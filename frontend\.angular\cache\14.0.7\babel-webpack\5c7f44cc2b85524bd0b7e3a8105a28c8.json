{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (++seen <= count) {\n        subscriber.next(value);\n\n        if (count <= seen) {\n          subscriber.complete();\n        }\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "take", "count", "source", "subscriber", "seen", "subscribe", "value", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/take.js"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n    return count <= 0\n        ?\n            () => EMPTY\n        : operate((source, subscriber) => {\n            let seen = 0;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (++seen <= count) {\n                    subscriber.next(value);\n                    if (count <= seen) {\n                        subscriber.complete();\n                    }\n                }\n            }));\n        });\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,qBAAtB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,IAAT,CAAcC,KAAd,EAAqB;EACxB,OAAOA,KAAK,IAAI,CAAT,GAEC,MAAMJ,KAFP,GAGDC,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,IAAI,GAAG,CAAX;IACAF,MAAM,CAACG,SAAP,CAAiBN,wBAAwB,CAACI,UAAD,EAAcG,KAAD,IAAW;MAC7D,IAAI,EAAEF,IAAF,IAAUH,KAAd,EAAqB;QACjBE,UAAU,CAACI,IAAX,CAAgBD,KAAhB;;QACA,IAAIL,KAAK,IAAIG,IAAb,EAAmB;UACfD,UAAU,CAACK,QAAX;QACH;MACJ;IACJ,CAPwC,CAAzC;EAQH,CAVQ,CAHb;AAcH"}, "metadata": {}, "sourceType": "module"}