{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\n\nclass InputTextarea {\n  constructor(el, ngModel, control, cd) {\n    this.el = el;\n    this.ngModel = ngModel;\n    this.control = control;\n    this.cd = cd;\n    this.onResize = new EventEmitter();\n  }\n\n  ngOnInit() {\n    if (this.ngModel) {\n      this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n        this.updateState();\n      });\n    }\n\n    if (this.control) {\n      this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n        this.updateState();\n      });\n    }\n  }\n\n  ngAfterViewInit() {\n    if (this.autoResize) this.resize();\n    this.updateFilledState();\n    this.cd.detectChanges();\n  }\n\n  onInput(e) {\n    this.updateState();\n  }\n\n  updateFilledState() {\n    this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n  }\n\n  onFocus(e) {\n    if (this.autoResize) {\n      this.resize(e);\n    }\n  }\n\n  onBlur(e) {\n    if (this.autoResize) {\n      this.resize(e);\n    }\n  }\n\n  resize(event) {\n    this.el.nativeElement.style.height = 'auto';\n    this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n\n    if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n      this.el.nativeElement.style.overflowY = \"scroll\";\n      this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n    } else {\n      this.el.nativeElement.style.overflow = \"hidden\";\n    }\n\n    this.onResize.emit(event || {});\n  }\n\n  updateState() {\n    this.updateFilledState();\n\n    if (this.autoResize) {\n      this.resize();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.ngModelSubscription) {\n      this.ngModelSubscription.unsubscribe();\n    }\n\n    if (this.ngControlSubscription) {\n      this.ngControlSubscription.unsubscribe();\n    }\n  }\n\n}\n\nInputTextarea.ɵfac = function InputTextarea_Factory(t) {\n  return new (t || InputTextarea)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgModel, 8), i0.ɵɵdirectiveInject(i1.NgControl, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nInputTextarea.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: InputTextarea,\n  selectors: [[\"\", \"pInputTextarea\", \"\"]],\n  hostAttrs: [1, \"p-inputtextarea\", \"p-inputtext\", \"p-component\", \"p-element\"],\n  hostVars: 4,\n  hostBindings: function InputTextarea_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"input\", function InputTextarea_input_HostBindingHandler($event) {\n        return ctx.onInput($event);\n      })(\"focus\", function InputTextarea_focus_HostBindingHandler($event) {\n        return ctx.onFocus($event);\n      })(\"blur\", function InputTextarea_blur_HostBindingHandler($event) {\n        return ctx.onBlur($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-inputtextarea-resizable\", ctx.autoResize);\n    }\n  },\n  inputs: {\n    autoResize: \"autoResize\"\n  },\n  outputs: {\n    onResize: \"onResize\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextarea, [{\n    type: Directive,\n    args: [{\n      selector: '[pInputTextarea]',\n      host: {\n        'class': 'p-inputtextarea p-inputtext p-component p-element',\n        '[class.p-filled]': 'filled',\n        '[class.p-inputtextarea-resizable]': 'autoResize'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.NgModel,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i1.NgControl,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    autoResize: [{\n      type: Input\n    }],\n    onResize: [{\n      type: Output\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur', ['$event']]\n    }]\n  });\n})();\n\nclass InputTextareaModule {}\n\nInputTextareaModule.ɵfac = function InputTextareaModule_Factory(t) {\n  return new (t || InputTextareaModule)();\n};\n\nInputTextareaModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: InputTextareaModule\n});\nInputTextareaModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextareaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [InputTextarea],\n      declarations: [InputTextarea]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { InputTextarea, InputTextareaModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Directive", "Optional", "Input", "Output", "HostListener", "NgModule", "CommonModule", "i1", "InputTextarea", "constructor", "el", "ngModel", "control", "cd", "onResize", "ngOnInit", "ngModelSubscription", "valueChanges", "subscribe", "updateState", "ngControlSubscription", "ngAfterViewInit", "autoResize", "resize", "updateFilledState", "detectChanges", "onInput", "e", "filled", "nativeElement", "value", "length", "onFocus", "onBlur", "event", "style", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "emit", "ngOnDestroy", "unsubscribe", "ɵfac", "ElementRef", "NgModel", "NgControl", "ChangeDetectorRef", "ɵdir", "type", "args", "selector", "host", "decorators", "InputTextareaModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-inputtextarea.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\n\nclass InputTextarea {\n    constructor(el, ngModel, control, cd) {\n        this.el = el;\n        this.ngModel = ngModel;\n        this.control = control;\n        this.cd = cd;\n        this.onResize = new EventEmitter();\n    }\n    ngOnInit() {\n        if (this.ngModel) {\n            this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n        if (this.control) {\n            this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n    }\n    ngAfterViewInit() {\n        if (this.autoResize)\n            this.resize();\n        this.updateFilledState();\n        this.cd.detectChanges();\n    }\n    onInput(e) {\n        this.updateState();\n    }\n    updateFilledState() {\n        this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    onFocus(e) {\n        if (this.autoResize) {\n            this.resize(e);\n        }\n    }\n    onBlur(e) {\n        if (this.autoResize) {\n            this.resize(e);\n        }\n    }\n    resize(event) {\n        this.el.nativeElement.style.height = 'auto';\n        this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n        if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n            this.el.nativeElement.style.overflowY = \"scroll\";\n            this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n        }\n        else {\n            this.el.nativeElement.style.overflow = \"hidden\";\n        }\n        this.onResize.emit(event || {});\n    }\n    updateState() {\n        this.updateFilledState();\n        if (this.autoResize) {\n            this.resize();\n        }\n    }\n    ngOnDestroy() {\n        if (this.ngModelSubscription) {\n            this.ngModelSubscription.unsubscribe();\n        }\n        if (this.ngControlSubscription) {\n            this.ngControlSubscription.unsubscribe();\n        }\n    }\n}\nInputTextarea.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextarea, deps: [{ token: i0.ElementRef }, { token: i1.NgModel, optional: true }, { token: i1.NgControl, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\nInputTextarea.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: InputTextarea, selector: \"[pInputTextarea]\", inputs: { autoResize: \"autoResize\" }, outputs: { onResize: \"onResize\" }, host: { listeners: { \"input\": \"onInput($event)\", \"focus\": \"onFocus($event)\", \"blur\": \"onBlur($event)\" }, properties: { \"class.p-filled\": \"filled\", \"class.p-inputtextarea-resizable\": \"autoResize\" }, classAttribute: \"p-inputtextarea p-inputtext p-component p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextarea, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pInputTextarea]',\n                    host: {\n                        'class': 'p-inputtextarea p-inputtext p-component p-element',\n                        '[class.p-filled]': 'filled',\n                        '[class.p-inputtextarea-resizable]': 'autoResize'\n                    }\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i1.NgModel, decorators: [{\n                        type: Optional\n                    }] }, { type: i1.NgControl, decorators: [{\n                        type: Optional\n                    }] }, { type: i0.ChangeDetectorRef }];\n    }, propDecorators: { autoResize: [{\n                type: Input\n            }], onResize: [{\n                type: Output\n            }], onInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }], onFocus: [{\n                type: HostListener,\n                args: ['focus', ['$event']]\n            }], onBlur: [{\n                type: HostListener,\n                args: ['blur', ['$event']]\n            }] } });\nclass InputTextareaModule {\n}\nInputTextareaModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextareaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nInputTextareaModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextareaModule, declarations: [InputTextarea], imports: [CommonModule], exports: [InputTextarea] });\nInputTextareaModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextareaModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputTextareaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [InputTextarea],\n                    declarations: [InputTextarea]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,QAAlC,EAA4CC,KAA5C,EAAmDC,MAAnD,EAA2DC,YAA3D,EAAyEC,QAAzE,QAAyF,eAAzF;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;;AAEA,MAAMC,aAAN,CAAoB;EAChBC,WAAW,CAACC,EAAD,EAAKC,OAAL,EAAcC,OAAd,EAAuBC,EAAvB,EAA2B;IAClC,KAAKH,EAAL,GAAUA,EAAV;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgB,IAAIf,YAAJ,EAAhB;EACH;;EACDgB,QAAQ,GAAG;IACP,IAAI,KAAKJ,OAAT,EAAkB;MACd,KAAKK,mBAAL,GAA2B,KAAKL,OAAL,CAAaM,YAAb,CAA0BC,SAA1B,CAAoC,MAAM;QACjE,KAAKC,WAAL;MACH,CAF0B,CAA3B;IAGH;;IACD,IAAI,KAAKP,OAAT,EAAkB;MACd,KAAKQ,qBAAL,GAA6B,KAAKR,OAAL,CAAaK,YAAb,CAA0BC,SAA1B,CAAoC,MAAM;QACnE,KAAKC,WAAL;MACH,CAF4B,CAA7B;IAGH;EACJ;;EACDE,eAAe,GAAG;IACd,IAAI,KAAKC,UAAT,EACI,KAAKC,MAAL;IACJ,KAAKC,iBAAL;IACA,KAAKX,EAAL,CAAQY,aAAR;EACH;;EACDC,OAAO,CAACC,CAAD,EAAI;IACP,KAAKR,WAAL;EACH;;EACDK,iBAAiB,GAAG;IAChB,KAAKI,MAAL,GAAc,KAAKlB,EAAL,CAAQmB,aAAR,CAAsBC,KAAtB,IAA+B,KAAKpB,EAAL,CAAQmB,aAAR,CAAsBC,KAAtB,CAA4BC,MAAzE;EACH;;EACDC,OAAO,CAACL,CAAD,EAAI;IACP,IAAI,KAAKL,UAAT,EAAqB;MACjB,KAAKC,MAAL,CAAYI,CAAZ;IACH;EACJ;;EACDM,MAAM,CAACN,CAAD,EAAI;IACN,IAAI,KAAKL,UAAT,EAAqB;MACjB,KAAKC,MAAL,CAAYI,CAAZ;IACH;EACJ;;EACDJ,MAAM,CAACW,KAAD,EAAQ;IACV,KAAKxB,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BC,MAA5B,GAAqC,MAArC;IACA,KAAK1B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BC,MAA5B,GAAqC,KAAK1B,EAAL,CAAQmB,aAAR,CAAsBQ,YAAtB,GAAqC,IAA1E;;IACA,IAAIC,UAAU,CAAC,KAAK5B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BC,MAA7B,CAAV,IAAkDE,UAAU,CAAC,KAAK5B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BI,SAA7B,CAAhE,EAAyG;MACrG,KAAK7B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BK,SAA5B,GAAwC,QAAxC;MACA,KAAK9B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BC,MAA5B,GAAqC,KAAK1B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BI,SAAjE;IACH,CAHD,MAIK;MACD,KAAK7B,EAAL,CAAQmB,aAAR,CAAsBM,KAAtB,CAA4BM,QAA5B,GAAuC,QAAvC;IACH;;IACD,KAAK3B,QAAL,CAAc4B,IAAd,CAAmBR,KAAK,IAAI,EAA5B;EACH;;EACDf,WAAW,GAAG;IACV,KAAKK,iBAAL;;IACA,IAAI,KAAKF,UAAT,EAAqB;MACjB,KAAKC,MAAL;IACH;EACJ;;EACDoB,WAAW,GAAG;IACV,IAAI,KAAK3B,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyB4B,WAAzB;IACH;;IACD,IAAI,KAAKxB,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL,CAA2BwB,WAA3B;IACH;EACJ;;AAnEe;;AAqEpBpC,aAAa,CAACqC,IAAd;EAAA,iBAA0GrC,aAA1G,EAAgGV,EAAhG,mBAAyIA,EAAE,CAACgD,UAA5I,GAAgGhD,EAAhG,mBAAmKS,EAAE,CAACwC,OAAtK,MAAgGjD,EAAhG,mBAA0MS,EAAE,CAACyC,SAA7M,MAAgGlD,EAAhG,mBAAmPA,EAAE,CAACmD,iBAAtP;AAAA;;AACAzC,aAAa,CAAC0C,IAAd,kBADgGpD,EAChG;EAAA,MAA8FU,aAA9F;EAAA;EAAA;EAAA;EAAA;IAAA;MADgGV,EAChG;QAAA,OAA8F,mBAA9F;MAAA;QAAA,OAA8F,mBAA9F;MAAA;QAAA,OAA8F,kBAA9F;MAAA;IAAA;;IAAA;MADgGA,EAChG;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAFgGA,EAEhG,mBAA2FU,aAA3F,EAAsH,CAAC;IAC3G2C,IAAI,EAAEnD,SADqG;IAE3GoD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBADX;MAECC,IAAI,EAAE;QACF,SAAS,mDADP;QAEF,oBAAoB,QAFlB;QAGF,qCAAqC;MAHnC;IAFP,CAAD;EAFqG,CAAD,CAAtH,EAU4B,YAAY;IAChC,OAAO,CAAC;MAAEH,IAAI,EAAErD,EAAE,CAACgD;IAAX,CAAD,EAA0B;MAAEK,IAAI,EAAE5C,EAAE,CAACwC,OAAX;MAAoBQ,UAAU,EAAE,CAAC;QAClDJ,IAAI,EAAElD;MAD4C,CAAD;IAAhC,CAA1B,EAEW;MAAEkD,IAAI,EAAE5C,EAAE,CAACyC,SAAX;MAAsBO,UAAU,EAAE,CAAC;QACrCJ,IAAI,EAAElD;MAD+B,CAAD;IAAlC,CAFX,EAIW;MAAEkD,IAAI,EAAErD,EAAE,CAACmD;IAAX,CAJX,CAAP;EAKH,CAhBL,EAgBuB;IAAE3B,UAAU,EAAE,CAAC;MACtB6B,IAAI,EAAEjD;IADgB,CAAD,CAAd;IAEPY,QAAQ,EAAE,CAAC;MACXqC,IAAI,EAAEhD;IADK,CAAD,CAFH;IAIPuB,OAAO,EAAE,CAAC;MACVyB,IAAI,EAAE/C,YADI;MAEVgD,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;IAFI,CAAD,CAJF;IAOPpB,OAAO,EAAE,CAAC;MACVmB,IAAI,EAAE/C,YADI;MAEVgD,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;IAFI,CAAD,CAPF;IAUPnB,MAAM,EAAE,CAAC;MACTkB,IAAI,EAAE/C,YADG;MAETgD,IAAI,EAAE,CAAC,MAAD,EAAS,CAAC,QAAD,CAAT;IAFG,CAAD;EAVD,CAhBvB;AAAA;;AA8BA,MAAMI,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACX,IAApB;EAAA,iBAAgHW,mBAAhH;AAAA;;AACAA,mBAAmB,CAACC,IAApB,kBAnCgG3D,EAmChG;EAAA,MAAiH0D;AAAjH;AACAA,mBAAmB,CAACE,IAApB,kBApCgG5D,EAoChG;EAAA,UAAgJQ,YAAhJ;AAAA;;AACA;EAAA,mDArCgGR,EAqChG,mBAA2F0D,mBAA3F,EAA4H,CAAC;IACjHL,IAAI,EAAE9C,QAD2G;IAEjH+C,IAAI,EAAE,CAAC;MACCO,OAAO,EAAE,CAACrD,YAAD,CADV;MAECsD,OAAO,EAAE,CAACpD,aAAD,CAFV;MAGCqD,YAAY,EAAE,CAACrD,aAAD;IAHf,CAAD;EAF2G,CAAD,CAA5H;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,aAAT,EAAwBgD,mBAAxB"}, "metadata": {}, "sourceType": "module"}