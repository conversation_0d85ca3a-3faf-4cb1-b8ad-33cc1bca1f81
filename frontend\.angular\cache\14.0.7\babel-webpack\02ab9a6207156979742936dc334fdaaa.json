{"ast": null, "code": "import { EMPTY } from './empty';\nimport { onErrorResumeNext as onErrorResumeNextWith } from '../operators/onErrorResumeNext';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nexport function onErrorResumeNext(...sources) {\n  return onErrorResumeNextWith(argsOrArgArray(sources))(EMPTY);\n}", "map": {"version": 3, "names": ["EMPTY", "onErrorResumeNext", "onErrorResumeNextWith", "argsOrArgArray", "sources"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/onErrorResumeNext.js"], "sourcesContent": ["import { EMPTY } from './empty';\nimport { onErrorResumeNext as onErrorResumeNextWith } from '../operators/onErrorResumeNext';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nexport function onErrorResumeNext(...sources) {\n    return onErrorResumeNextWith(argsOrArgArray(sources))(EMPTY);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,SAAtB;AACA,SAASC,iBAAiB,IAAIC,qBAA9B,QAA2D,gCAA3D;AACA,SAASC,cAAT,QAA+B,wBAA/B;AACA,OAAO,SAASF,iBAAT,CAA2B,GAAGG,OAA9B,EAAuC;EAC1C,OAAOF,qBAAqB,CAACC,cAAc,CAACC,OAAD,CAAf,CAArB,CAA+CJ,KAA/C,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}