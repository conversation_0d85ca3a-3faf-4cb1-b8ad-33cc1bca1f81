{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/documents.service\";\nimport * as i2 from \"../../../../services/formation.service\";\nimport * as i3 from \"primeng/api\";\nexport class DocumentsComponent {\n  constructor(documentsService, formationService, messageService, confirmationService) {\n    this.documentsService = documentsService;\n    this.formationService = formationService;\n    this.messageService = messageService;\n    this.confirmationService = confirmationService;\n    this.documents = [];\n    this.formations = [];\n    this.loading = false;\n    this.uploadDialog = false; // Upload form\n\n    this.selectedFormationId = null;\n    this.selectedFile = null;\n    this.customFileName = '';\n    this.uploading = false;\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this.loadDocuments();\n      yield _this.loadFormations();\n    })();\n  }\n\n  loadDocuments() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      _this2.loading = true;\n\n      try {\n        _this2.documents = yield lastValueFrom(_this2.documentsService.getDocuments());\n      } catch (error) {\n        console.error('Error loading documents:', error);\n\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load documents',\n          life: 3000\n        });\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  loadFormations() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this3.formations = yield _this3.formationService.getFormations();\n      } catch (error) {\n        console.error('Error loading formations:', error);\n      }\n    })();\n  }\n\n  openUploadDialog() {\n    this.uploadDialog = true;\n    this.resetUploadForm();\n  }\n\n  hideUploadDialog() {\n    this.uploadDialog = false;\n    this.resetUploadForm();\n  }\n\n  resetUploadForm() {\n    this.selectedFormationId = null;\n    this.selectedFile = null;\n    this.customFileName = '';\n    this.uploading = false;\n  }\n\n  onFileSelect(event) {\n    const file = event.files[0];\n\n    if (file) {\n      this.selectedFile = file;\n\n      if (!this.customFileName) {\n        this.customFileName = file.name;\n      }\n    }\n  }\n\n  uploadDocument() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      if (!_this4.selectedFile || !_this4.selectedFormationId) {\n        _this4.messageService.add({\n          severity: 'warn',\n          summary: 'Warning',\n          detail: 'Please select a file and formation',\n          life: 3000\n        });\n\n        return;\n      }\n\n      _this4.uploading = true;\n\n      try {\n        const upload = {\n          formation_id: _this4.selectedFormationId,\n          file: _this4.selectedFile,\n          name: _this4.customFileName || _this4.selectedFile.name\n        };\n        yield lastValueFrom(_this4.documentsService.uploadDocument(upload));\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Document uploaded successfully',\n          life: 3000\n        });\n\n        _this4.hideUploadDialog();\n\n        yield _this4.loadDocuments();\n      } catch (error) {\n        console.error('Error uploading document:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to upload document',\n          life: 3000\n        });\n      } finally {\n        _this4.uploading = false;\n      }\n    })();\n  }\n\n  deleteDocument(document) {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      _this5.confirmationService.confirm({\n        message: `Are you sure you want to delete \"${document.name}\"?`,\n        header: 'Confirm Delete',\n        icon: 'pi pi-exclamation-triangle',\n        accept: function () {\n          var _ref = _asyncToGenerator(function* () {\n            try {\n              yield lastValueFrom(_this5.documentsService.deleteDocument(document.id));\n\n              _this5.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'Document deleted successfully',\n                life: 3000\n              });\n\n              yield _this5.loadDocuments();\n            } catch (error) {\n              console.error('Error deleting document:', error);\n\n              _this5.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to delete document',\n                life: 3000\n              });\n            }\n          });\n\n          return function accept() {\n            return _ref.apply(this, arguments);\n          };\n        }()\n      });\n    })();\n  }\n\n  downloadDocument(document) {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this6.documentsService.downloadDocument(document.id)); // Créer URL temporaire et déclencher téléchargement\n\n        const url = window.URL.createObjectURL(blob);\n        const link = window.document.createElement('a');\n        link.href = url;\n        link.download = document.name;\n        link.click();\n        window.URL.revokeObjectURL(url);\n      } catch (error) {\n        console.error('Error downloading document:', error);\n\n        _this6.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to download document',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  getFormationName(formationId) {\n    const formation = this.formations.find(f => f.id === formationId);\n    return formation ? formation.name : 'Unknown';\n  }\n\n}\n\nDocumentsComponent.ɵfac = function DocumentsComponent_Factory(t) {\n  return new (t || DocumentsComponent)(i0.ɵɵdirectiveInject(i1.DocumentsService), i0.ɵɵdirectiveInject(i2.FormationService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i3.ConfirmationService));\n};\n\nDocumentsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DocumentsComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService])],\n  decls: 2,\n  vars: 0,\n  template: function DocumentsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p\");\n      i0.ɵɵtext(1, \"documents works!\");\n      i0.ɵɵelementEnd();\n    }\n  },\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,cAAT,EAAyBC,mBAAzB,QAAoD,aAApD;AACA,SAASC,aAAT,QAA8B,MAA9B;;;;;AAQA,OAAM,MAAOC,kBAAP,CAAyB;EAY7BC,YACUC,gBADV,EAEUC,gBAFV,EAGUC,cAHV,EAIUC,mBAJV,EAIkD;IAHxC;IACA;IACA;IACA;IAfV,iBAAwB,EAAxB;IACA,kBAAoB,EAApB;IACA,eAAmB,KAAnB;IACA,oBAAwB,KAAxB,CAYkD,CAVlD;;IACA,2BAAqC,IAArC;IACA,oBAA4B,IAA5B;IACA,sBAAyB,EAAzB;IACA,iBAAqB,KAArB;EAOI;;EAEEC,QAAQ;IAAA;;IAAA;MACZ,MAAM,KAAI,CAACC,aAAL,EAAN;MACA,MAAM,KAAI,CAACC,cAAL,EAAN;IAFY;EAGb;;EAEKD,aAAa;IAAA;;IAAA;MACjB,MAAI,CAACE,OAAL,GAAe,IAAf;;MACA,IAAI;QACF,MAAI,CAACC,SAAL,SAAuBX,aAAa,CAAC,MAAI,CAACG,gBAAL,CAAsBS,YAAtB,EAAD,CAApC;MACD,CAFD,CAEE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;;QACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,0BAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,SAUU;QACR,MAAI,CAACT,OAAL,GAAe,KAAf;MACD;IAdgB;EAelB;;EAEKD,cAAc;IAAA;;IAAA;MAClB,IAAI;QACF,MAAI,CAACW,UAAL,SAAwB,MAAI,CAAChB,gBAAL,CAAsBiB,aAAtB,EAAxB;MACD,CAFD,CAEE,OAAOR,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;MACD;IALiB;EAMnB;;EAEDS,gBAAgB;IACd,KAAKC,YAAL,GAAoB,IAApB;IACA,KAAKC,eAAL;EACD;;EAEDC,gBAAgB;IACd,KAAKF,YAAL,GAAoB,KAApB;IACA,KAAKC,eAAL;EACD;;EAEDA,eAAe;IACb,KAAKE,mBAAL,GAA2B,IAA3B;IACA,KAAKC,YAAL,GAAoB,IAApB;IACA,KAAKC,cAAL,GAAsB,EAAtB;IACA,KAAKC,SAAL,GAAiB,KAAjB;EACD;;EAEDC,YAAY,CAACC,KAAD,EAAW;IACrB,MAAMC,IAAI,GAAGD,KAAK,CAACE,KAAN,CAAY,CAAZ,CAAb;;IACA,IAAID,IAAJ,EAAU;MACR,KAAKL,YAAL,GAAoBK,IAApB;;MACA,IAAI,CAAC,KAAKJ,cAAV,EAA0B;QACxB,KAAKA,cAAL,GAAsBI,IAAI,CAACE,IAA3B;MACD;IACF;EACF;;EAEKC,cAAc;IAAA;;IAAA;MAClB,IAAI,CAAC,MAAI,CAACR,YAAN,IAAsB,CAAC,MAAI,CAACD,mBAAhC,EAAqD;QACnD,MAAI,CAACrB,cAAL,CAAoBU,GAApB,CAAwB;UACtBC,QAAQ,EAAE,MADY;UAEtBC,OAAO,EAAE,SAFa;UAGtBC,MAAM,EAAE,oCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;;QAMA;MACD;;MAED,MAAI,CAACU,SAAL,GAAiB,IAAjB;;MACA,IAAI;QACF,MAAMO,MAAM,GAAmB;UAC7BC,YAAY,EAAE,MAAI,CAACX,mBADU;UAE7BM,IAAI,EAAE,MAAI,CAACL,YAFkB;UAG7BO,IAAI,EAAE,MAAI,CAACN,cAAL,IAAuB,MAAI,CAACD,YAAL,CAAkBO;QAHlB,CAA/B;QAMA,MAAMlC,aAAa,CAAC,MAAI,CAACG,gBAAL,CAAsBgC,cAAtB,CAAqCC,MAArC,CAAD,CAAnB;;QAEA,MAAI,CAAC/B,cAAL,CAAoBU,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,SAFa;UAGtBC,MAAM,EAAE,gCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;;QAOA,MAAI,CAACM,gBAAL;;QACA,MAAM,MAAI,CAACjB,aAAL,EAAN;MAED,CAnBD,CAmBE,OAAOK,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;;QACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,2BAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CA3BD,SA2BU;QACR,MAAI,CAACU,SAAL,GAAiB,KAAjB;MACD;IAzCiB;EA0CnB;;EAEKS,cAAc,CAACC,QAAD,EAAmB;IAAA;;IAAA;MACrC,MAAI,CAACjC,mBAAL,CAAyBkC,OAAzB,CAAiC;QAC/BC,OAAO,EAAE,oCAAoCF,QAAQ,CAACL,IAAI,IAD3B;QAE/BQ,MAAM,EAAE,gBAFuB;QAG/BC,IAAI,EAAE,4BAHyB;QAI/BC,MAAM;UAAA,6BAAE,aAAW;YACjB,IAAI;cACF,MAAM5C,aAAa,CAAC,MAAI,CAACG,gBAAL,CAAsBmC,cAAtB,CAAqCC,QAAQ,CAACM,EAA9C,CAAD,CAAnB;;cAEA,MAAI,CAACxC,cAAL,CAAoBU,GAApB,CAAwB;gBACtBC,QAAQ,EAAE,SADY;gBAEtBC,OAAO,EAAE,SAFa;gBAGtBC,MAAM,EAAE,+BAHc;gBAItBC,IAAI,EAAE;cAJgB,CAAxB;;cAOA,MAAM,MAAI,CAACX,aAAL,EAAN;YAED,CAZD,CAYE,OAAOK,KAAP,EAAc;cACdC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;;cACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;gBACtBC,QAAQ,EAAE,OADY;gBAEtBC,OAAO,EAAE,OAFa;gBAGtBC,MAAM,EAAE,2BAHc;gBAItBC,IAAI,EAAE;cAJgB,CAAxB;YAMD;UACF,CAtBK;;UAAA,gBAANyB,MAAM;YAAA;UAAA;QAAA;MAJyB,CAAjC;IADqC;EA6BtC;;EAEKE,gBAAgB,CAACP,QAAD,EAAmB;IAAA;;IAAA;MACvC,IAAI;QACF,MAAMQ,IAAI,SAAS/C,aAAa,CAAC,MAAI,CAACG,gBAAL,CAAsB2C,gBAAtB,CAAuCP,QAAQ,CAACM,EAAhD,CAAD,CAAhC,CADE,CAGF;;QACA,MAAMG,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BJ,IAA3B,CAAZ;QACA,MAAMK,IAAI,GAAGH,MAAM,CAACV,QAAP,CAAgBc,aAAhB,CAA8B,GAA9B,CAAb;QACAD,IAAI,CAACE,IAAL,GAAYN,GAAZ;QACAI,IAAI,CAACG,QAAL,GAAgBhB,QAAQ,CAACL,IAAzB;QACAkB,IAAI,CAACI,KAAL;QACAP,MAAM,CAACC,GAAP,CAAWO,eAAX,CAA2BT,GAA3B;MAED,CAXD,CAWE,OAAOnC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,6BAAd,EAA6CA,KAA7C;;QACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,6BAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD;IApBsC;EAqBxC;;EAEDuC,cAAc,CAACC,KAAD,EAAc;IAC1B,IAAIA,KAAK,KAAK,CAAd,EAAiB,OAAO,SAAP;IACjB,MAAMC,CAAC,GAAG,IAAV;IACA,MAAMC,KAAK,GAAG,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsB,IAAtB,CAAd;IACA,MAAMC,CAAC,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,GAAL,CAASN,KAAT,IAAkBI,IAAI,CAACE,GAAL,CAASL,CAAT,CAA7B,CAAV;IACA,OAAOM,UAAU,CAAC,CAACP,KAAK,GAAGI,IAAI,CAACI,GAAL,CAASP,CAAT,EAAYE,CAAZ,CAAT,EAAyBM,OAAzB,CAAiC,CAAjC,CAAD,CAAV,GAAkD,GAAlD,GAAwDP,KAAK,CAACC,CAAD,CAApE;EACD;;EAEDO,gBAAgB,CAACC,WAAD,EAAoB;IAClC,MAAMC,SAAS,GAAG,KAAKnD,UAAL,CAAgBoD,IAAhB,CAAqBC,CAAC,IAAIA,CAAC,CAAC5B,EAAF,KAASyB,WAAnC,CAAlB;IACA,OAAOC,SAAS,GAAGA,SAAS,CAACrC,IAAb,GAAoB,SAApC;EACD;;AAzL4B;;;mBAAlBjC,oBAAkByE;AAAA;;;QAAlBzE;EAAkB0E;EAAAC,iCAFlB,CAAC9E,cAAD,EAAiBC,mBAAjB,CAEkB;EAFmB8E;EAAAC;EAAAC;IAAA;MCRlDL;MAAGA;MAAgBA", "names": ["MessageService", "ConfirmationService", "lastValueFrom", "DocumentsComponent", "constructor", "documentsService", "formationService", "messageService", "confirmationService", "ngOnInit", "loadDocuments", "loadFormations", "loading", "documents", "getDocuments", "error", "console", "add", "severity", "summary", "detail", "life", "formations", "getFormations", "openUploadDialog", "uploadDialog", "resetUploadForm", "hideUploadDialog", "selectedFormationId", "selectedFile", "customFileName", "uploading", "onFileSelect", "event", "file", "files", "name", "uploadDocument", "upload", "formation_id", "deleteDocument", "document", "confirm", "message", "header", "icon", "accept", "id", "downloadDocument", "blob", "url", "window", "URL", "createObjectURL", "link", "createElement", "href", "download", "click", "revokeObjectURL", "formatFileSize", "bytes", "k", "sizes", "i", "Math", "floor", "log", "parseFloat", "pow", "toFixed", "getFormationName", "formationId", "formation", "find", "f", "i0", "selectors", "features", "decls", "vars", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\documents\\documents.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\documents\\documents.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport { DocumentsService, Document, DocumentUpload } from '../../../../services/documents.service';\nimport { FormationService } from '../../../../services/formation.service';\n\n@Component({\n  templateUrl: './documents.component.html',\n  providers: [MessageService, ConfirmationService]\n})\nexport class DocumentsComponent implements OnInit {\n  documents: Document[] = [];\n  formations: any[] = [];\n  loading: boolean = false;\n  uploadDialog: boolean = false;\n  \n  // Upload form\n  selectedFormationId: number | null = null;\n  selectedFile: File | null = null;\n  customFileName: string = '';\n  uploading: boolean = false;\n\n  constructor(\n    private documentsService: DocumentsService,\n    private formationService: FormationService,\n    private messageService: MessageService,\n    private confirmationService: ConfirmationService\n  ) {}\n\n  async ngOnInit() {\n    await this.loadDocuments();\n    await this.loadFormations();\n  }\n\n  async loadDocuments() {\n    this.loading = true;\n    try {\n      this.documents = await lastValueFrom(this.documentsService.getDocuments());\n    } catch (error) {\n      console.error('Error loading documents:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to load documents',\n        life: 3000\n      });\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  async loadFormations() {\n    try {\n      this.formations = await this.formationService.getFormations();\n    } catch (error) {\n      console.error('Error loading formations:', error);\n    }\n  }\n\n  openUploadDialog() {\n    this.uploadDialog = true;\n    this.resetUploadForm();\n  }\n\n  hideUploadDialog() {\n    this.uploadDialog = false;\n    this.resetUploadForm();\n  }\n\n  resetUploadForm() {\n    this.selectedFormationId = null;\n    this.selectedFile = null;\n    this.customFileName = '';\n    this.uploading = false;\n  }\n\n  onFileSelect(event: any) {\n    const file = event.files[0];\n    if (file) {\n      this.selectedFile = file;\n      if (!this.customFileName) {\n        this.customFileName = file.name;\n      }\n    }\n  }\n\n  async uploadDocument() {\n    if (!this.selectedFile || !this.selectedFormationId) {\n      this.messageService.add({\n        severity: 'warn',\n        summary: 'Warning',\n        detail: 'Please select a file and formation',\n        life: 3000\n      });\n      return;\n    }\n\n    this.uploading = true;\n    try {\n      const upload: DocumentUpload = {\n        formation_id: this.selectedFormationId,\n        file: this.selectedFile,\n        name: this.customFileName || this.selectedFile.name\n      };\n\n      await lastValueFrom(this.documentsService.uploadDocument(upload));\n      \n      this.messageService.add({\n        severity: 'success',\n        summary: 'Success',\n        detail: 'Document uploaded successfully',\n        life: 3000\n      });\n      \n      this.hideUploadDialog();\n      await this.loadDocuments();\n      \n    } catch (error) {\n      console.error('Error uploading document:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to upload document',\n        life: 3000\n      });\n    } finally {\n      this.uploading = false;\n    }\n  }\n\n  async deleteDocument(document: Document) {\n    this.confirmationService.confirm({\n      message: `Are you sure you want to delete \"${document.name}\"?`,\n      header: 'Confirm Delete',\n      icon: 'pi pi-exclamation-triangle',\n      accept: async () => {\n        try {\n          await lastValueFrom(this.documentsService.deleteDocument(document.id));\n          \n          this.messageService.add({\n            severity: 'success',\n            summary: 'Success',\n            detail: 'Document deleted successfully',\n            life: 3000\n          });\n          \n          await this.loadDocuments();\n          \n        } catch (error) {\n          console.error('Error deleting document:', error);\n          this.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to delete document',\n            life: 3000\n          });\n        }\n      }\n    });\n  }\n\n  async downloadDocument(document: Document) {\n    try {\n      const blob = await lastValueFrom(this.documentsService.downloadDocument(document.id));\n      \n      // Créer URL temporaire et déclencher téléchargement\n      const url = window.URL.createObjectURL(blob);\n      const link = window.document.createElement('a');\n      link.href = url;\n      link.download = document.name;\n      link.click();\n      window.URL.revokeObjectURL(url);\n      \n    } catch (error) {\n      console.error('Error downloading document:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to download document',\n        life: 3000\n      });\n    }\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  getFormationName(formationId: number): string {\n    const formation = this.formations.find(f => f.id === formationId);\n    return formation ? formation.name : 'Unknown';\n  }\n}", "<p>documents works!</p>\n"]}, "metadata": {}, "sourceType": "module"}