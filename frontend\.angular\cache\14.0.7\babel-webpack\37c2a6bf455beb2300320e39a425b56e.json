{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/tabview\";\nimport * as i4 from \"primeng/inputtext\";\nimport * as i5 from \"primeng/inputtextarea\";\nimport * as i6 from \"primeng/inputnumber\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/checkbox\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/toast\";\nimport * as i11 from \"primeng/confirmdialog\";\nexport class SettingsComponent {\n  constructor(messageService, confirmationService) {\n    this.messageService = messageService;\n    this.confirmationService = confirmationService; // Settings objects\n\n    this.systemSettings = {\n      siteName: 'Formation Management System',\n      siteDescription: 'Complete training management solution',\n      adminEmail: '<EMAIL>',\n      defaultLanguage: 'fr',\n      timezone: 'Europe/Paris',\n      dateFormat: 'DD/MM/YYYY',\n      timeFormat: '24h',\n      maxFileSize: 10,\n      allowedFileTypes: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],\n      emailNotifications: true,\n      smsNotifications: false,\n      autoBackup: true,\n      backupFrequency: 'daily',\n      maintenanceMode: false,\n      debugMode: false\n    };\n    this.notificationSettings = {\n      formationReminder: true,\n      attendanceAlert: true,\n      reportGeneration: true,\n      systemUpdates: true,\n      reminderDays: 3,\n      emailTemplate: 'default'\n    };\n    this.securitySettings = {\n      passwordMinLength: 8,\n      passwordRequireUppercase: true,\n      passwordRequireNumbers: true,\n      passwordRequireSymbols: false,\n      sessionTimeout: 30,\n      maxLoginAttempts: 5,\n      lockoutDuration: 15,\n      twoFactorAuth: false\n    }; // Options\n\n    this.languageOptions = [{\n      label: 'Français',\n      value: 'fr'\n    }, {\n      label: 'English',\n      value: 'en'\n    }, {\n      label: 'العربية',\n      value: 'ar'\n    }];\n    this.timezoneOptions = [{\n      label: 'Europe/Paris',\n      value: 'Europe/Paris'\n    }, {\n      label: 'UTC',\n      value: 'UTC'\n    }, {\n      label: 'America/New_York',\n      value: 'America/New_York'\n    }];\n    this.dateFormatOptions = [{\n      label: 'DD/MM/YYYY',\n      value: 'DD/MM/YYYY'\n    }, {\n      label: 'MM/DD/YYYY',\n      value: 'MM/DD/YYYY'\n    }, {\n      label: 'YYYY-MM-DD',\n      value: 'YYYY-MM-DD'\n    }];\n    this.timeFormatOptions = [{\n      label: '24 Hours',\n      value: '24h'\n    }, {\n      label: '12 Hours (AM/PM)',\n      value: '12h'\n    }];\n    this.backupFrequencyOptions = [{\n      label: 'Daily',\n      value: 'daily'\n    }, {\n      label: 'Weekly',\n      value: 'weekly'\n    }, {\n      label: 'Monthly',\n      value: 'monthly'\n    }];\n    this.emailTemplateOptions = [{\n      label: 'Default Template',\n      value: 'default'\n    }, {\n      label: 'Professional Template',\n      value: 'professional'\n    }, {\n      label: 'Modern Template',\n      value: 'modern'\n    }]; // Loading states\n\n    this.savingSystem = false;\n    this.savingNotifications = false;\n    this.savingSecurity = false; // Active tab\n\n    this.activeTab = 0;\n  }\n\n  ngOnInit() {\n    this.loadSettings();\n  }\n\n  loadSettings() {\n    // In a real application, load settings from backend\n    // For now, we use default values\n    console.log('Settings loaded');\n  }\n\n  saveSystemSettings() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.savingSystem = true;\n\n      try {\n        // In a real application, save to backend\n        yield new Promise(resolve => setTimeout(resolve, 1000));\n\n        _this.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'System settings saved successfully',\n          life: 3000\n        });\n      } catch (error) {\n        _this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to save system settings',\n          life: 3000\n        });\n      } finally {\n        _this.savingSystem = false;\n      }\n    })();\n  }\n\n  saveNotificationSettings() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      _this2.savingNotifications = true;\n\n      try {\n        // In a real application, save to backend\n        yield new Promise(resolve => setTimeout(resolve, 1000));\n\n        _this2.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Notification settings saved successfully',\n          life: 3000\n        });\n      } catch (error) {\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to save notification settings',\n          life: 3000\n        });\n      } finally {\n        _this2.savingNotifications = false;\n      }\n    })();\n  }\n\n  saveSecuritySettings() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      _this3.savingSecurity = true;\n\n      try {\n        // In a real application, save to backend\n        yield new Promise(resolve => setTimeout(resolve, 1000));\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Security settings saved successfully',\n          life: 3000\n        });\n      } catch (error) {\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to save security settings',\n          life: 3000\n        });\n      } finally {\n        _this3.savingSecurity = false;\n      }\n    })();\n  }\n\n  resetSystemSettings() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to reset system settings to default values?',\n      header: 'Confirm Reset',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        // Reset to default values\n        this.systemSettings = {\n          siteName: 'Formation Management System',\n          siteDescription: 'Complete training management solution',\n          adminEmail: '<EMAIL>',\n          defaultLanguage: 'fr',\n          timezone: 'Europe/Paris',\n          dateFormat: 'DD/MM/YYYY',\n          timeFormat: '24h',\n          maxFileSize: 10,\n          allowedFileTypes: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],\n          emailNotifications: true,\n          smsNotifications: false,\n          autoBackup: true,\n          backupFrequency: 'daily',\n          maintenanceMode: false,\n          debugMode: false\n        };\n        this.messageService.add({\n          severity: 'info',\n          summary: 'Reset',\n          detail: 'System settings reset to default values',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  testEmailNotification() {\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Test Email',\n      detail: 'Test email notification sent',\n      life: 3000\n    });\n  }\n\n  performBackup() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to perform a manual backup?',\n      header: 'Confirm Backup',\n      icon: 'pi pi-question-circle',\n      accept: () => {\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Backup',\n          detail: 'Manual backup initiated successfully',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  clearCache() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to clear the system cache?',\n      header: 'Confirm Cache Clear',\n      icon: 'pi pi-question-circle',\n      accept: () => {\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Cache',\n          detail: 'System cache cleared successfully',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  exportSettings() {\n    const settings = {\n      system: this.systemSettings,\n      notifications: this.notificationSettings,\n      security: this.securitySettings\n    };\n    const blob = new Blob([JSON.stringify(settings, null, 2)], {\n      type: 'application/json'\n    });\n    const url = window.URL.createObjectURL(blob);\n    const link = window.document.createElement('a');\n    link.href = url;\n    link.download = 'system-settings.json';\n    link.click();\n    window.URL.revokeObjectURL(url);\n    this.messageService.add({\n      severity: 'success',\n      summary: 'Export',\n      detail: 'Settings exported successfully',\n      life: 3000\n    });\n  }\n\n}\n\nSettingsComponent.ɵfac = function SettingsComponent_Factory(t) {\n  return new (t || SettingsComponent)(i0.ɵɵdirectiveInject(i1.MessageService), i0.ɵɵdirectiveInject(i1.ConfirmationService));\n};\n\nSettingsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: SettingsComponent,\n  selectors: [[\"app-settings\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService])],\n  decls: 199,\n  vars: 56,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [3, \"activeIndex\", \"activeIndexChange\"], [\"header\", \"System\", \"leftIcon\", \"pi pi-cog\"], [1, \"col-12\", \"md:col-6\"], [1, \"field\"], [\"for\", \"siteName\"], [\"id\", \"siteName\", \"type\", \"text\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"adminEmail\"], [\"id\", \"adminEmail\", \"type\", \"email\", \"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"siteDescription\"], [\"id\", \"siteDescription\", \"pInputTextarea\", \"\", \"rows\", \"3\", 1, \"w-full\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"defaultLanguage\"], [\"id\", \"defaultLanguage\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"timezone\"], [\"id\", \"timezone\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"dateFormat\"], [\"id\", \"dateFormat\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"timeFormat\"], [\"id\", \"timeFormat\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [\"for\", \"maxFileSize\"], [\"id\", \"maxFileSize\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"for\", \"backupFrequency\"], [\"id\", \"backupFrequency\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [1, \"grid\", \"mt-4\"], [1, \"field-checkbox\"], [\"id\", \"emailNotifications\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"emailNotifications\"], [\"id\", \"smsNotifications\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"smsNotifications\"], [\"id\", \"autoBackup\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"autoBackup\"], [\"id\", \"maintenanceMode\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"maintenanceMode\"], [1, \"flex\", \"gap-2\", \"mt-4\"], [\"label\", \"Save Settings\", \"icon\", \"pi pi-save\", 3, \"loading\", \"onClick\"], [\"label\", \"Reset to Default\", \"icon\", \"pi pi-refresh\", 1, \"p-button-secondary\", 3, \"onClick\"], [\"label\", \"Export Settings\", \"icon\", \"pi pi-download\", 1, \"p-button-info\", 3, \"onClick\"], [\"header\", \"Notifications\", \"leftIcon\", \"pi pi-bell\"], [\"id\", \"formationReminder\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"formationReminder\"], [\"id\", \"attendanceAlert\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"attendanceAlert\"], [\"id\", \"reportGeneration\", 3, \"ngModel\", \"binary\", \"ngModelChange\"], [\"for\", \"reportGeneration\"], [\"id\", \"systemUpdates\", \"binary\", \"true\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"systemUpdates\"], [\"for\", \"reminderDays\"], [\"id\", \"reminderDays\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"for\", \"emailTemplate\"], [\"id\", \"emailTemplate\", \"optionLabel\", \"label\", \"optionValue\", \"value\", 1, \"w-full\", 3, \"ngModel\", \"options\", \"ngModelChange\"], [\"label\", \"Save Notifications\", \"icon\", \"pi pi-save\", 3, \"loading\", \"onClick\"], [\"label\", \"Test Email\", \"icon\", \"pi pi-send\", 1, \"p-button-info\", 3, \"onClick\"], [\"header\", \"Security\", \"leftIcon\", \"pi pi-shield\"], [\"for\", \"passwordMinLength\"], [\"id\", \"passwordMinLength\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"for\", \"sessionTimeout\"], [\"id\", \"sessionTimeout\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"for\", \"maxLoginAttempts\"], [\"id\", \"maxLoginAttempts\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"for\", \"lockoutDuration\"], [\"id\", \"lockoutDuration\", 1, \"w-full\", 3, \"ngModel\", \"min\", \"max\", \"ngModelChange\"], [\"id\", \"passwordRequireUppercase\", \"binary\", \"true\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"passwordRequireUppercase\"], [\"id\", \"passwordRequireNumbers\", \"binary\", \"true\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"passwordRequireNumbers\"], [\"id\", \"passwordRequireSymbols\", \"binary\", \"true\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"passwordRequireSymbols\"], [\"id\", \"twoFactorAuth\", \"binary\", \"true\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"twoFactorAuth\"], [\"label\", \"Save Security\", \"icon\", \"pi pi-save\", 3, \"loading\", \"onClick\"], [\"header\", \"Maintenance\", \"leftIcon\", \"pi pi-wrench\"], [1, \"col-12\", \"md:col-4\"], [1, \"card\", \"text-center\"], [1, \"pi\", \"pi-database\", \"text-4xl\", \"text-blue-500\", \"mb-3\"], [1, \"text-600\"], [\"label\", \"Backup Now\", \"icon\", \"pi pi-download\", 1, \"p-button-info\", 3, \"onClick\"], [1, \"pi\", \"pi-refresh\", \"text-4xl\", \"text-green-500\", \"mb-3\"], [\"label\", \"Clear Cache\", \"icon\", \"pi pi-trash\", 1, \"p-button-success\", 3, \"onClick\"], [1, \"pi\", \"pi-cog\", \"text-4xl\", \"text-orange-500\", \"mb-3\"], [\"label\", \"View Info\", \"icon\", \"pi pi-info-circle\", \"disabled\", \"true\", 1, \"p-button-warning\"]],\n  template: function SettingsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n      i0.ɵɵtext(4, \"System Settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(5, \"p-toast\")(6, \"p-confirmDialog\");\n      i0.ɵɵelementStart(7, \"p-tabView\", 3);\n      i0.ɵɵlistener(\"activeIndexChange\", function SettingsComponent_Template_p_tabView_activeIndexChange_7_listener($event) {\n        return ctx.activeTab = $event;\n      });\n      i0.ɵɵelementStart(8, \"p-tabPanel\", 4)(9, \"div\", 2)(10, \"h6\");\n      i0.ɵɵtext(11, \"General System Settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(12, \"div\", 0)(13, \"div\", 5)(14, \"div\", 6)(15, \"label\", 7);\n      i0.ɵɵtext(16, \"Site Name\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(17, \"input\", 8);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_input_ngModelChange_17_listener($event) {\n        return ctx.systemSettings.siteName = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 6)(20, \"label\", 9);\n      i0.ɵɵtext(21, \"Admin Email\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(22, \"input\", 10);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_input_ngModelChange_22_listener($event) {\n        return ctx.systemSettings.adminEmail = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(23, \"div\", 1)(24, \"div\", 6)(25, \"label\", 11);\n      i0.ɵɵtext(26, \"Site Description\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"textarea\", 12);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_textarea_ngModelChange_27_listener($event) {\n        return ctx.systemSettings.siteDescription = $event;\n      });\n      i0.ɵɵtext(28, \"                                    \");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(29, \"div\", 5)(30, \"div\", 6)(31, \"label\", 13);\n      i0.ɵɵtext(32, \"Default Language\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(33, \"p-dropdown\", 14);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_33_listener($event) {\n        return ctx.systemSettings.defaultLanguage = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(34, \"div\", 5)(35, \"div\", 6)(36, \"label\", 15);\n      i0.ɵɵtext(37, \"Timezone\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(38, \"p-dropdown\", 16);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_38_listener($event) {\n        return ctx.systemSettings.timezone = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(39, \"div\", 5)(40, \"div\", 6)(41, \"label\", 17);\n      i0.ɵɵtext(42, \"Date Format\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(43, \"p-dropdown\", 18);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_43_listener($event) {\n        return ctx.systemSettings.dateFormat = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(44, \"div\", 5)(45, \"div\", 6)(46, \"label\", 19);\n      i0.ɵɵtext(47, \"Time Format\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(48, \"p-dropdown\", 20);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_48_listener($event) {\n        return ctx.systemSettings.timeFormat = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(49, \"div\", 5)(50, \"div\", 6)(51, \"label\", 21);\n      i0.ɵɵtext(52, \"Max File Size (MB)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"p-inputNumber\", 22);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_53_listener($event) {\n        return ctx.systemSettings.maxFileSize = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(54, \"div\", 5)(55, \"div\", 6)(56, \"label\", 23);\n      i0.ɵɵtext(57, \"Backup Frequency\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(58, \"p-dropdown\", 24);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_58_listener($event) {\n        return ctx.systemSettings.backupFrequency = $event;\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(59, \"div\", 25)(60, \"div\", 5)(61, \"div\", 26)(62, \"p-checkbox\", 27);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_62_listener($event) {\n        return ctx.systemSettings.emailNotifications = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(63, \"label\", 28);\n      i0.ɵɵtext(64, \"Enable Email Notifications\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(65, \"div\", 5)(66, \"div\", 26)(67, \"p-checkbox\", 29);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_67_listener($event) {\n        return ctx.systemSettings.smsNotifications = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"label\", 30);\n      i0.ɵɵtext(69, \"Enable SMS Notifications\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(70, \"div\", 5)(71, \"div\", 26)(72, \"p-checkbox\", 31);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_72_listener($event) {\n        return ctx.systemSettings.autoBackup = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(73, \"label\", 32);\n      i0.ɵɵtext(74, \"Enable Auto Backup\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(75, \"div\", 5)(76, \"div\", 26)(77, \"p-checkbox\", 33);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_77_listener($event) {\n        return ctx.systemSettings.maintenanceMode = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(78, \"label\", 34);\n      i0.ɵɵtext(79, \"Maintenance Mode\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(80, \"div\", 35)(81, \"p-button\", 36);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_81_listener() {\n        return ctx.saveSystemSettings();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"p-button\", 37);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_82_listener() {\n        return ctx.resetSystemSettings();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(83, \"p-button\", 38);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_83_listener() {\n        return ctx.exportSettings();\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(84, \"p-tabPanel\", 39)(85, \"div\", 2)(86, \"h6\");\n      i0.ɵɵtext(87, \"Notification Settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"div\", 0)(89, \"div\", 5)(90, \"div\", 26)(91, \"p-checkbox\", 40);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_91_listener($event) {\n        return ctx.notificationSettings.formationReminder = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(92, \"label\", 41);\n      i0.ɵɵtext(93, \"Formation Reminders\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(94, \"div\", 5)(95, \"div\", 26)(96, \"p-checkbox\", 42);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_96_listener($event) {\n        return ctx.notificationSettings.attendanceAlert = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(97, \"label\", 43);\n      i0.ɵɵtext(98, \"Attendance Alerts\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(99, \"div\", 5)(100, \"div\", 26)(101, \"p-checkbox\", 44);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_101_listener($event) {\n        return ctx.notificationSettings.reportGeneration = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(102, \"label\", 45);\n      i0.ɵɵtext(103, \"Report Generation\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(104, \"div\", 5)(105, \"div\", 26)(106, \"p-checkbox\", 46);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_106_listener($event) {\n        return ctx.notificationSettings.systemUpdates = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(107, \"label\", 47);\n      i0.ɵɵtext(108, \"System Updates\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(109, \"div\", 5)(110, \"div\", 6)(111, \"label\", 48);\n      i0.ɵɵtext(112, \"Reminder Days Before\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(113, \"p-inputNumber\", 49);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_113_listener($event) {\n        return ctx.notificationSettings.reminderDays = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(114, \"div\", 5)(115, \"div\", 6)(116, \"label\", 50);\n      i0.ɵɵtext(117, \"Email Template\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(118, \"p-dropdown\", 51);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_dropdown_ngModelChange_118_listener($event) {\n        return ctx.notificationSettings.emailTemplate = $event;\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(119, \"div\", 35)(120, \"p-button\", 52);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_120_listener() {\n        return ctx.saveNotificationSettings();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(121, \"p-button\", 53);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_121_listener() {\n        return ctx.testEmailNotification();\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(122, \"p-tabPanel\", 54)(123, \"div\", 2)(124, \"h6\");\n      i0.ɵɵtext(125, \"Security Settings\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(126, \"div\", 0)(127, \"div\", 5)(128, \"div\", 6)(129, \"label\", 55);\n      i0.ɵɵtext(130, \"Password Min Length\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(131, \"p-inputNumber\", 56);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_131_listener($event) {\n        return ctx.securitySettings.passwordMinLength = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(132, \"div\", 5)(133, \"div\", 6)(134, \"label\", 57);\n      i0.ɵɵtext(135, \"Session Timeout (minutes)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(136, \"p-inputNumber\", 58);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_136_listener($event) {\n        return ctx.securitySettings.sessionTimeout = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(137, \"div\", 5)(138, \"div\", 6)(139, \"label\", 59);\n      i0.ɵɵtext(140, \"Max Login Attempts\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(141, \"p-inputNumber\", 60);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_141_listener($event) {\n        return ctx.securitySettings.maxLoginAttempts = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(142, \"div\", 5)(143, \"div\", 6)(144, \"label\", 61);\n      i0.ɵɵtext(145, \"Lockout Duration (minutes)\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(146, \"p-inputNumber\", 62);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_inputNumber_ngModelChange_146_listener($event) {\n        return ctx.securitySettings.lockoutDuration = $event;\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(147, \"div\", 25)(148, \"div\", 5)(149, \"div\", 26)(150, \"p-checkbox\", 63);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_150_listener($event) {\n        return ctx.securitySettings.passwordRequireUppercase = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(151, \"label\", 64);\n      i0.ɵɵtext(152, \"Require Uppercase\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(153, \"div\", 5)(154, \"div\", 26)(155, \"p-checkbox\", 65);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_155_listener($event) {\n        return ctx.securitySettings.passwordRequireNumbers = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(156, \"label\", 66);\n      i0.ɵɵtext(157, \"Require Numbers\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(158, \"div\", 5)(159, \"div\", 26)(160, \"p-checkbox\", 67);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_160_listener($event) {\n        return ctx.securitySettings.passwordRequireSymbols = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(161, \"label\", 68);\n      i0.ɵɵtext(162, \"Require Symbols\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(163, \"div\", 5)(164, \"div\", 26)(165, \"p-checkbox\", 69);\n      i0.ɵɵlistener(\"ngModelChange\", function SettingsComponent_Template_p_checkbox_ngModelChange_165_listener($event) {\n        return ctx.securitySettings.twoFactorAuth = $event;\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(166, \"label\", 70);\n      i0.ɵɵtext(167, \"Two-Factor Authentication\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(168, \"div\", 35)(169, \"p-button\", 71);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_169_listener() {\n        return ctx.saveSecuritySettings();\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(170, \"p-tabPanel\", 72)(171, \"div\", 2)(172, \"h6\");\n      i0.ɵɵtext(173, \"System Maintenance\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(174, \"div\", 0)(175, \"div\", 73)(176, \"div\", 74);\n      i0.ɵɵelement(177, \"i\", 75);\n      i0.ɵɵelementStart(178, \"h6\");\n      i0.ɵɵtext(179, \"Database Backup\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(180, \"p\", 76);\n      i0.ɵɵtext(181, \"Perform manual database backup\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(182, \"p-button\", 77);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_182_listener() {\n        return ctx.performBackup();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(183, \"div\", 73)(184, \"div\", 74);\n      i0.ɵɵelement(185, \"i\", 78);\n      i0.ɵɵelementStart(186, \"h6\");\n      i0.ɵɵtext(187, \"Clear Cache\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(188, \"p\", 76);\n      i0.ɵɵtext(189, \"Clear system cache and temporary files\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(190, \"p-button\", 79);\n      i0.ɵɵlistener(\"onClick\", function SettingsComponent_Template_p_button_onClick_190_listener() {\n        return ctx.clearCache();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(191, \"div\", 73)(192, \"div\", 74);\n      i0.ɵɵelement(193, \"i\", 80);\n      i0.ɵɵelementStart(194, \"h6\");\n      i0.ɵɵtext(195, \"System Info\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(196, \"p\", 76);\n      i0.ɵɵtext(197, \"View system information and logs\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(198, \"p-button\", 81);\n      i0.ɵɵelementEnd()()()()()()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"activeIndex\", ctx.activeTab);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.siteName);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.adminEmail);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.siteDescription);\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.defaultLanguage)(\"options\", ctx.languageOptions);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.timezone)(\"options\", ctx.timezoneOptions);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.dateFormat)(\"options\", ctx.dateFormatOptions);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.timeFormat)(\"options\", ctx.timeFormatOptions);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.maxFileSize)(\"min\", 1)(\"max\", 100);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.backupFrequency)(\"options\", ctx.backupFrequencyOptions);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.emailNotifications)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.smsNotifications)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.autoBackup)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.systemSettings.maintenanceMode)(\"binary\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"loading\", ctx.savingSystem);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.formationReminder)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.attendanceAlert)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.reportGeneration)(\"binary\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.systemUpdates);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.reminderDays)(\"min\", 1)(\"max\", 30);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.notificationSettings.emailTemplate)(\"options\", ctx.emailTemplateOptions);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"loading\", ctx.savingNotifications);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.passwordMinLength)(\"min\", 6)(\"max\", 20);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.sessionTimeout)(\"min\", 5)(\"max\", 120);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.maxLoginAttempts)(\"min\", 3)(\"max\", 10);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.lockoutDuration)(\"min\", 5)(\"max\", 60);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.passwordRequireUppercase);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.passwordRequireNumbers);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.passwordRequireSymbols);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.securitySettings.twoFactorAuth);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"loading\", ctx.savingSecurity);\n    }\n  },\n  dependencies: [i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.TabView, i3.TabPanel, i4.InputText, i5.InputTextarea, i6.InputNumber, i7.Dropdown, i8.Checkbox, i9.Button, i10.Toast, i11.ConfirmDialog],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJzZXR0aW5ncy5jb21wb25lbnQuc2NzcyJ9 */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,cAAT,EAAyBC,mBAAzB,QAAoD,aAApD;;;;;;;;;;;;;AA8CA,OAAM,MAAOC,iBAAP,CAAwB;EAqF5BC,YACUC,cADV,EAEUC,mBAFV,EAEkD;IADxC;IACA,+CAAwC,CArFlD;;IACA,sBAAiC;MAC/BC,QAAQ,EAAE,6BADqB;MAE/BC,eAAe,EAAE,uCAFc;MAG/BC,UAAU,EAAE,mBAHmB;MAI/BC,eAAe,EAAE,IAJc;MAK/BC,QAAQ,EAAE,cALqB;MAM/BC,UAAU,EAAE,YANmB;MAO/BC,UAAU,EAAE,KAPmB;MAQ/BC,WAAW,EAAE,EARkB;MAS/BC,gBAAgB,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,MAAf,EAAuB,KAAvB,EAA8B,MAA9B,CATa;MAU/BC,kBAAkB,EAAE,IAVW;MAW/BC,gBAAgB,EAAE,KAXa;MAY/BC,UAAU,EAAE,IAZmB;MAa/BC,eAAe,EAAE,OAbc;MAc/BC,eAAe,EAAE,KAdc;MAe/BC,SAAS,EAAE;IAfoB,CAAjC;IAkBA,4BAA6C;MAC3CC,iBAAiB,EAAE,IADwB;MAE3CC,eAAe,EAAE,IAF0B;MAG3CC,gBAAgB,EAAE,IAHyB;MAI3CC,aAAa,EAAE,IAJ4B;MAK3CC,YAAY,EAAE,CAL6B;MAM3CC,aAAa,EAAE;IAN4B,CAA7C;IASA,wBAAqC;MACnCC,iBAAiB,EAAE,CADgB;MAEnCC,wBAAwB,EAAE,IAFS;MAGnCC,sBAAsB,EAAE,IAHW;MAInCC,sBAAsB,EAAE,KAJW;MAKnCC,cAAc,EAAE,EALmB;MAMnCC,gBAAgB,EAAE,CANiB;MAOnCC,eAAe,EAAE,EAPkB;MAQnCC,aAAa,EAAE;IARoB,CAArC,CAyDkD,CA9ClD;;IACA,uBAAkB,CAChB;MAAEC,KAAK,EAAE,UAAT;MAAqBC,KAAK,EAAE;IAA5B,CADgB,EAEhB;MAAED,KAAK,EAAE,SAAT;MAAoBC,KAAK,EAAE;IAA3B,CAFgB,EAGhB;MAAED,KAAK,EAAE,SAAT;MAAoBC,KAAK,EAAE;IAA3B,CAHgB,CAAlB;IAMA,uBAAkB,CAChB;MAAED,KAAK,EAAE,cAAT;MAAyBC,KAAK,EAAE;IAAhC,CADgB,EAEhB;MAAED,KAAK,EAAE,KAAT;MAAgBC,KAAK,EAAE;IAAvB,CAFgB,EAGhB;MAAED,KAAK,EAAE,kBAAT;MAA6BC,KAAK,EAAE;IAApC,CAHgB,CAAlB;IAMA,yBAAoB,CAClB;MAAED,KAAK,EAAE,YAAT;MAAuBC,KAAK,EAAE;IAA9B,CADkB,EAElB;MAAED,KAAK,EAAE,YAAT;MAAuBC,KAAK,EAAE;IAA9B,CAFkB,EAGlB;MAAED,KAAK,EAAE,YAAT;MAAuBC,KAAK,EAAE;IAA9B,CAHkB,CAApB;IAMA,yBAAoB,CAClB;MAAED,KAAK,EAAE,UAAT;MAAqBC,KAAK,EAAE;IAA5B,CADkB,EAElB;MAAED,KAAK,EAAE,kBAAT;MAA6BC,KAAK,EAAE;IAApC,CAFkB,CAApB;IAKA,8BAAyB,CACvB;MAAED,KAAK,EAAE,OAAT;MAAkBC,KAAK,EAAE;IAAzB,CADuB,EAEvB;MAAED,KAAK,EAAE,QAAT;MAAmBC,KAAK,EAAE;IAA1B,CAFuB,EAGvB;MAAED,KAAK,EAAE,SAAT;MAAoBC,KAAK,EAAE;IAA3B,CAHuB,CAAzB;IAMA,4BAAuB,CACrB;MAAED,KAAK,EAAE,kBAAT;MAA6BC,KAAK,EAAE;IAApC,CADqB,EAErB;MAAED,KAAK,EAAE,uBAAT;MAAkCC,KAAK,EAAE;IAAzC,CAFqB,EAGrB;MAAED,KAAK,EAAE,iBAAT;MAA4BC,KAAK,EAAE;IAAnC,CAHqB,CAAvB,CAgBkD,CAVlD;;IACA,oBAAwB,KAAxB;IACA,2BAA+B,KAA/B;IACA,sBAA0B,KAA1B,CAOkD,CALlD;;IACA,iBAAoB,CAApB;EAKK;;EAELC,QAAQ;IACN,KAAKC,YAAL;EACD;;EAEDA,YAAY;IACV;IACA;IACAC,OAAO,CAACC,GAAR,CAAY,iBAAZ;EACD;;EAEKC,kBAAkB;IAAA;;IAAA;MACtB,KAAI,CAACC,YAAL,GAAoB,IAApB;;MACA,IAAI;QACF;QACA,MAAM,IAAIC,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAU,IAAV,CAAjC,CAAN;;QAEA,KAAI,CAACxC,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,SAFa;UAGtBC,MAAM,EAAE,oCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,CAUE,OAAOC,KAAP,EAAc;QACd,KAAI,CAAC/C,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,gCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAjBD,SAiBU;QACR,KAAI,CAACR,YAAL,GAAoB,KAApB;MACD;IArBqB;EAsBvB;;EAEKU,wBAAwB;IAAA;;IAAA;MAC5B,MAAI,CAACC,mBAAL,GAA2B,IAA3B;;MACA,IAAI;QACF;QACA,MAAM,IAAIV,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAU,IAAV,CAAjC,CAAN;;QAEA,MAAI,CAACxC,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,SAFa;UAGtBC,MAAM,EAAE,0CAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,CAUE,OAAOC,KAAP,EAAc;QACd,MAAI,CAAC/C,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,sCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAjBD,SAiBU;QACR,MAAI,CAACG,mBAAL,GAA2B,KAA3B;MACD;IArB2B;EAsB7B;;EAEKC,oBAAoB;IAAA;;IAAA;MACxB,MAAI,CAACC,cAAL,GAAsB,IAAtB;;MACA,IAAI;QACF;QACA,MAAM,IAAIZ,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAU,IAAV,CAAjC,CAAN;;QAEA,MAAI,CAACxC,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,SAFa;UAGtBC,MAAM,EAAE,sCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,CAUE,OAAOC,KAAP,EAAc;QACd,MAAI,CAAC/C,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,kCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAjBD,SAiBU;QACR,MAAI,CAACK,cAAL,GAAsB,KAAtB;MACD;IArBuB;EAsBzB;;EAEDC,mBAAmB;IACjB,KAAKnD,mBAAL,CAAyBoD,OAAzB,CAAiC;MAC/BC,OAAO,EAAE,mEADsB;MAE/BC,MAAM,EAAE,eAFuB;MAG/BC,IAAI,EAAE,4BAHyB;MAI/BC,MAAM,EAAE,MAAK;QACX;QACA,KAAKC,cAAL,GAAsB;UACpBxD,QAAQ,EAAE,6BADU;UAEpBC,eAAe,EAAE,uCAFG;UAGpBC,UAAU,EAAE,mBAHQ;UAIpBC,eAAe,EAAE,IAJG;UAKpBC,QAAQ,EAAE,cALU;UAMpBC,UAAU,EAAE,YANQ;UAOpBC,UAAU,EAAE,KAPQ;UAQpBC,WAAW,EAAE,EARO;UASpBC,gBAAgB,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,MAAf,EAAuB,KAAvB,EAA8B,MAA9B,CATE;UAUpBC,kBAAkB,EAAE,IAVA;UAWpBC,gBAAgB,EAAE,KAXE;UAYpBC,UAAU,EAAE,IAZQ;UAapBC,eAAe,EAAE,OAbG;UAcpBC,eAAe,EAAE,KAdG;UAepBC,SAAS,EAAE;QAfS,CAAtB;QAkBA,KAAKhB,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,MADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,yCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD;IA9B8B,CAAjC;EAgCD;;EAEDa,qBAAqB;IACnB,KAAK3D,cAAL,CAAoB0C,GAApB,CAAwB;MACtBC,QAAQ,EAAE,MADY;MAEtBC,OAAO,EAAE,YAFa;MAGtBC,MAAM,EAAE,8BAHc;MAItBC,IAAI,EAAE;IAJgB,CAAxB;EAMD;;EAEDc,aAAa;IACX,KAAK3D,mBAAL,CAAyBoD,OAAzB,CAAiC;MAC/BC,OAAO,EAAE,mDADsB;MAE/BC,MAAM,EAAE,gBAFuB;MAG/BC,IAAI,EAAE,uBAHyB;MAI/BC,MAAM,EAAE,MAAK;QACX,KAAKzD,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,QAFa;UAGtBC,MAAM,EAAE,sCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD;IAX8B,CAAjC;EAaD;;EAEDe,UAAU;IACR,KAAK5D,mBAAL,CAAyBoD,OAAzB,CAAiC;MAC/BC,OAAO,EAAE,kDADsB;MAE/BC,MAAM,EAAE,qBAFuB;MAG/BC,IAAI,EAAE,uBAHyB;MAI/BC,MAAM,EAAE,MAAK;QACX,KAAKzD,cAAL,CAAoB0C,GAApB,CAAwB;UACtBC,QAAQ,EAAE,SADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,mCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD;IAX8B,CAAjC;EAaD;;EAEDgB,cAAc;IACZ,MAAMC,QAAQ,GAAG;MACfC,MAAM,EAAE,KAAKN,cADE;MAEfO,aAAa,EAAE,KAAKC,oBAFL;MAGfC,QAAQ,EAAE,KAAKC;IAHA,CAAjB;IAMA,MAAMC,IAAI,GAAG,IAAIC,IAAJ,CAAS,CAACC,IAAI,CAACC,SAAL,CAAeT,QAAf,EAAyB,IAAzB,EAA+B,CAA/B,CAAD,CAAT,EAA8C;MAAEU,IAAI,EAAE;IAAR,CAA9C,CAAb;IACA,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BR,IAA3B,CAAZ;IACA,MAAMS,IAAI,GAAGH,MAAM,CAACI,QAAP,CAAgBC,aAAhB,CAA8B,GAA9B,CAAb;IACAF,IAAI,CAACG,IAAL,GAAYP,GAAZ;IACAI,IAAI,CAACI,QAAL,GAAgB,sBAAhB;IACAJ,IAAI,CAACK,KAAL;IACAR,MAAM,CAACC,GAAP,CAAWQ,eAAX,CAA2BV,GAA3B;IAEA,KAAK1E,cAAL,CAAoB0C,GAApB,CAAwB;MACtBC,QAAQ,EAAE,SADY;MAEtBC,OAAO,EAAE,QAFa;MAGtBC,MAAM,EAAE,gCAHc;MAItBC,IAAI,EAAE;IAJgB,CAAxB;EAMD;;AA7Q2B;;;mBAAjBhD,mBAAiBuF;AAAA;;;QAAjBvF;EAAiBwF;EAAAC,iCAFjB,CAAC3F,cAAD,EAAiBC,mBAAjB,CAEiB;EAFoB2F;EAAAC;EAAAC;EAAAC;IAAA;MC7ClDN,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,IAAlB;MAGgBA;MAAeA;MACnBA,2BAAmB,CAAnB,EAAmB,iBAAnB;MAGAA;MAAWA;QAAA;MAAA;MAGPA,sCAAiD,CAAjD,EAAiD,KAAjD,EAAiD,CAAjD,EAAiD,EAAjD,EAAiD,IAAjD;MAEYA;MAAuBA;MAE3BA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,OAAlB,EAAkB,CAAlB;MAGkCA;MAASA;MAC/BA;MAIIA;QAAA;MAAA;MAJJA;MAQRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,CAA7B;MAEgCA;MAAWA;MACnCA;MAIIA;QAAA;MAAA;MAJJA;MAQRA,gCAAoB,EAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;MAEqCA;MAAgBA;MAC7CA;MAGIA;QAAA;MAAA;MAGJA;MAAAA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEqCA;MAAgBA;MAC7CA;MAEIA;QAAA;MAAA;MAKJA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAE8BA;MAAQA;MAC9BA;MAEIA;QAAA;MAAA;MAKJA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEgCA;MAAWA;MACnCA;MAEIA;QAAA;MAAA;MAKJA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEgCA;MAAWA;MACnCA;MAEIA;QAAA;MAAA;MAKJA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEiCA;MAAkBA;MAC3CA;MAEIA;QAAA;MAAA;MAIJA;MAGRA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEqCA;MAAgBA;MAC7CA;MAEIA;QAAA;MAAA;MAKJA;MAKZA,iCAAuB,EAAvB,EAAuB,KAAvB,EAAuB,CAAvB,EAAuB,EAAvB,EAAuB,KAAvB,EAAuB,EAAvB,EAAuB,EAAvB,EAAuB,YAAvB,EAAuB,EAAvB;MAKgBA;QAAA;MAAA;MAEJA;MACAA;MAAgCA;MAA0BA;MAGlEA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA8BA;MAAwBA;MAG9DA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAAwBA;MAAkBA;MAGlDA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA6BA;MAAgBA;MAKzDA,iCAA6B,EAA7B,EAA6B,UAA7B,EAA6B,EAA7B;MAKQA;QAAA,OAAWO,wBAAX;MAA+B,CAA/B;MACJP;MACAA;MAIIA;QAAA,OAAWO,yBAAX;MAAgC,CAAhC;MACJP;MACAA;MAIIA;QAAA,OAAWO,oBAAX;MAA2B,CAA3B;MACJP;MAMZA,wCAAyD,EAAzD,EAAyD,KAAzD,EAAyD,CAAzD,EAAyD,EAAzD,EAAyD,IAAzD;MAEYA;MAAqBA;MAEzBA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,YAAlB,EAAkB,EAAlB;MAKgBA;QAAA;MAAA;MAEJA;MACAA;MAA+BA;MAAmBA;MAG1DA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA6BA;MAAiBA;MAGtDA,gCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA8BA;MAAiBA;MAGvDA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA2BA;MAAcA;MAGjDA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,GAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEkCA;MAAoBA;MAC9CA;MAEIA;QAAA;MAAA;MAIJA;MAGRA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,GAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEmCA;MAAcA;MACzCA;MAEIA;QAAA;MAAA;MAKJA;MAKZA,kCAA6B,GAA7B,EAA6B,UAA7B,EAA6B,EAA7B;MAKQA;QAAA,OAAWO,8BAAX;MAAqC,CAArC;MACJP;MACAA;MAIIA;QAAA,OAAWO,2BAAX;MAAkC,CAAlC;MACJP;MAMZA,yCAAsD,GAAtD,EAAsD,KAAtD,EAAsD,CAAtD,EAAsD,GAAtD,EAAsD,IAAtD;MAEYA;MAAiBA;MAErBA,iCAAkB,GAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,GAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,GAAlB,EAAkB,OAAlB,EAAkB,EAAlB;MAG2CA;MAAmBA;MAClDA;MAEIA;QAAA;MAAA;MAIJA;MAGRA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,GAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEoCA;MAAyBA;MACrDA;MAEIA;QAAA;MAAA;MAIJA;MAGRA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,GAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEsCA;MAAkBA;MAChDA;MAEIA;QAAA;MAAA;MAIJA;MAGRA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,GAA7B,EAA6B,OAA7B,EAA6B,EAA7B;MAEqCA;MAA0BA;MACvDA;MAEIA;QAAA;MAAA;MAIJA;MAKZA,kCAAuB,GAAvB,EAAuB,KAAvB,EAAuB,CAAvB,EAAuB,GAAvB,EAAuB,KAAvB,EAAuB,EAAvB,EAAuB,GAAvB,EAAuB,YAAvB,EAAuB,EAAvB;MAKgBA;QAAA;MAAA;MAEJA;MACAA;MAAsCA;MAAiBA;MAG/DA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAAoCA;MAAeA;MAG3DA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAAoCA;MAAeA;MAG3DA,iCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,YAA7B,EAA6B,EAA7B;MAIYA;QAAA;MAAA;MAEJA;MACAA;MAA2BA;MAAyBA;MAKhEA,kCAA6B,GAA7B,EAA6B,UAA7B,EAA6B,EAA7B;MAKQA;QAAA,OAAWO,0BAAX;MAAiC,CAAjC;MACJP;MAMZA,yCAAyD,GAAzD,EAAyD,KAAzD,EAAyD,CAAzD,EAAyD,GAAzD,EAAyD,IAAzD;MAEYA;MAAkBA;MAEtBA,iCAAkB,GAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,GAAlB,EAAkB,KAAlB,EAAkB,EAAlB;MAGYA;MACAA;MAAIA;MAAeA;MACnBA;MAAoBA;MAA8BA;MAClDA;MAIIA;QAAA,OAAWO,mBAAX;MAA0B,CAA1B;MACJP;MAGRA,kCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B;MAEQA;MACAA;MAAIA;MAAWA;MACfA;MAAoBA;MAAsCA;MAC1DA;MAIIA;QAAA,OAAWO,gBAAX;MAAuB,CAAvB;MACJP;MAGRA,kCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B;MAEQA;MACAA;MAAIA;MAAWA;MACfA;MAAoBA;MAAgCA;MACpDA;MAMJA;;;;MA9aTA;MAAAA;MAeiBA;MAAAA;MAWAA;MAAAA;MAUAA;MAAAA;MAWAA;MAAAA,6DAA4C,SAA5C,EAA4CO,mBAA5C;MAaAP;MAAAA,sDAAqC,SAArC,EAAqCO,mBAArC;MAaAP;MAAAA,wDAAuC,SAAvC,EAAuCO,qBAAvC;MAaAP;MAAAA,wDAAuC,SAAvC,EAAuCO,qBAAvC;MAaAP;MAAAA,yDAAwC,KAAxC,EAAwC,CAAxC,EAAwC,KAAxC,EAAwC,GAAxC;MAYAA;MAAAA,6DAA4C,SAA5C,EAA4CO,0BAA5C;MAeAP;MAAAA,gEAA+C,QAA/C,EAA+C,IAA/C;MAUAA;MAAAA,8DAA6C,QAA7C,EAA6C,IAA7C;MAUAA;MAAAA,wDAAuC,QAAvC,EAAuC,IAAvC;MAUAA;MAAAA,6DAA4C,QAA5C,EAA4C,IAA5C;MAYRA;MAAAA;MA6BQA;MAAAA,qEAAoD,QAApD,EAAoD,IAApD;MAUAA;MAAAA,mEAAkD,QAAlD,EAAkD,IAAlD;MAUAA;MAAAA,oEAAmD,QAAnD,EAAmD,IAAnD;MAUAA;MAAAA;MAWAA;MAAAA,gEAA+C,KAA/C,EAA+C,CAA/C,EAA+C,KAA/C,EAA+C,EAA/C;MAYAA;MAAAA,iEAAgD,SAAhD,EAAgDO,wBAAhD;MAcRP;MAAAA;MAwBQA;MAAAA,iEAAgD,KAAhD,EAAgD,CAAhD,EAAgD,KAAhD,EAAgD,EAAhD;MAYAA;MAAAA,8DAA6C,KAA7C,EAA6C,CAA7C,EAA6C,KAA7C,EAA6C,GAA7C;MAYAA;MAAAA,gEAA+C,KAA/C,EAA+C,CAA/C,EAA+C,KAA/C,EAA+C,EAA/C;MAYAA;MAAAA,+DAA8C,KAA9C,EAA8C,CAA9C,EAA8C,KAA9C,EAA8C,EAA9C;MAcAA;MAAAA;MAUAA;MAAAA;MAUAA;MAAAA;MAUAA;MAAAA;MAYRA;MAAAA", "names": ["MessageService", "ConfirmationService", "SettingsComponent", "constructor", "messageService", "confirmationService", "siteName", "siteDescription", "adminEmail", "defaultLanguage", "timezone", "dateFormat", "timeFormat", "maxFileSize", "allowedFileTypes", "emailNotifications", "smsNotifications", "autoBackup", "backupFrequency", "maintenanceMode", "debugMode", "formationReminder", "attendanceAlert", "reportGeneration", "systemUpdates", "reminderDays", "emailTemplate", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "passwordRequireUppercase", "passwordRequireNumbers", "passwordRequireSymbols", "sessionTimeout", "maxLogin<PERSON><PERSON><PERSON>s", "lockoutDuration", "twoFactorAuth", "label", "value", "ngOnInit", "loadSettings", "console", "log", "saveSystemSettings", "savingSystem", "Promise", "resolve", "setTimeout", "add", "severity", "summary", "detail", "life", "error", "saveNotificationSettings", "savingNotifications", "saveSecuritySettings", "savingSecurity", "resetSystemSettings", "confirm", "message", "header", "icon", "accept", "systemSettings", "testEmailNotification", "performBackup", "clearCache", "exportSettings", "settings", "system", "notifications", "notificationSettings", "security", "securitySettings", "blob", "Blob", "JSON", "stringify", "type", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "i0", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\settings\\settings.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\settings\\settings.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { MessageService, ConfirmationService } from 'primeng/api';\n\ninterface SystemSettings {\n  siteName: string;\n  siteDescription: string;\n  adminEmail: string;\n  defaultLanguage: string;\n  timezone: string;\n  dateFormat: string;\n  timeFormat: string;\n  maxFileSize: number;\n  allowedFileTypes: string[];\n  emailNotifications: boolean;\n  smsNotifications: boolean;\n  autoBackup: boolean;\n  backupFrequency: string;\n  maintenanceMode: boolean;\n  debugMode: boolean;\n}\n\ninterface NotificationSettings {\n  formationReminder: boolean;\n  attendanceAlert: boolean;\n  reportGeneration: boolean;\n  systemUpdates: boolean;\n  reminderDays: number;\n  emailTemplate: string;\n}\n\ninterface SecuritySettings {\n  passwordMinLength: number;\n  passwordRequireUppercase: boolean;\n  passwordRequireNumbers: boolean;\n  passwordRequireSymbols: boolean;\n  sessionTimeout: number;\n  maxLoginAttempts: number;\n  lockoutDuration: number;\n  twoFactorAuth: boolean;\n}\n\n@Component({\n  selector: 'app-settings',\n  templateUrl: './settings.component.html',\n  styleUrls: ['./settings.component.scss'],\n  providers: [MessageService, ConfirmationService]\n})\nexport class SettingsComponent implements OnInit {\n\n  // Settings objects\n  systemSettings: SystemSettings = {\n    siteName: 'Formation Management System',\n    siteDescription: 'Complete training management solution',\n    adminEmail: '<EMAIL>',\n    defaultLanguage: 'fr',\n    timezone: 'Europe/Paris',\n    dateFormat: 'DD/MM/YYYY',\n    timeFormat: '24h',\n    maxFileSize: 10,\n    allowedFileTypes: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],\n    emailNotifications: true,\n    smsNotifications: false,\n    autoBackup: true,\n    backupFrequency: 'daily',\n    maintenanceMode: false,\n    debugMode: false\n  };\n\n  notificationSettings: NotificationSettings = {\n    formationReminder: true,\n    attendanceAlert: true,\n    reportGeneration: true,\n    systemUpdates: true,\n    reminderDays: 3,\n    emailTemplate: 'default'\n  };\n\n  securitySettings: SecuritySettings = {\n    passwordMinLength: 8,\n    passwordRequireUppercase: true,\n    passwordRequireNumbers: true,\n    passwordRequireSymbols: false,\n    sessionTimeout: 30,\n    maxLoginAttempts: 5,\n    lockoutDuration: 15,\n    twoFactorAuth: false\n  };\n\n  // Options\n  languageOptions = [\n    { label: 'Français', value: 'fr' },\n    { label: 'English', value: 'en' },\n    { label: 'العربية', value: 'ar' }\n  ];\n\n  timezoneOptions = [\n    { label: 'Europe/Paris', value: 'Europe/Paris' },\n    { label: 'UTC', value: 'UTC' },\n    { label: 'America/New_York', value: 'America/New_York' }\n  ];\n\n  dateFormatOptions = [\n    { label: 'DD/MM/YYYY', value: 'DD/MM/YYYY' },\n    { label: 'MM/DD/YYYY', value: 'MM/DD/YYYY' },\n    { label: 'YYYY-MM-DD', value: 'YYYY-MM-DD' }\n  ];\n\n  timeFormatOptions = [\n    { label: '24 Hours', value: '24h' },\n    { label: '12 Hours (AM/PM)', value: '12h' }\n  ];\n\n  backupFrequencyOptions = [\n    { label: 'Daily', value: 'daily' },\n    { label: 'Weekly', value: 'weekly' },\n    { label: 'Monthly', value: 'monthly' }\n  ];\n\n  emailTemplateOptions = [\n    { label: 'Default Template', value: 'default' },\n    { label: 'Professional Template', value: 'professional' },\n    { label: 'Modern Template', value: 'modern' }\n  ];\n\n  // Loading states\n  savingSystem: boolean = false;\n  savingNotifications: boolean = false;\n  savingSecurity: boolean = false;\n\n  // Active tab\n  activeTab: number = 0;\n\n  constructor(\n    private messageService: MessageService,\n    private confirmationService: ConfirmationService\n  ) { }\n\n  ngOnInit(): void {\n    this.loadSettings();\n  }\n\n  loadSettings() {\n    // In a real application, load settings from backend\n    // For now, we use default values\n    console.log('Settings loaded');\n  }\n\n  async saveSystemSettings() {\n    this.savingSystem = true;\n    try {\n      // In a real application, save to backend\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      this.messageService.add({\n        severity: 'success',\n        summary: 'Success',\n        detail: 'System settings saved successfully',\n        life: 3000\n      });\n    } catch (error) {\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to save system settings',\n        life: 3000\n      });\n    } finally {\n      this.savingSystem = false;\n    }\n  }\n\n  async saveNotificationSettings() {\n    this.savingNotifications = true;\n    try {\n      // In a real application, save to backend\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      this.messageService.add({\n        severity: 'success',\n        summary: 'Success',\n        detail: 'Notification settings saved successfully',\n        life: 3000\n      });\n    } catch (error) {\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to save notification settings',\n        life: 3000\n      });\n    } finally {\n      this.savingNotifications = false;\n    }\n  }\n\n  async saveSecuritySettings() {\n    this.savingSecurity = true;\n    try {\n      // In a real application, save to backend\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      this.messageService.add({\n        severity: 'success',\n        summary: 'Success',\n        detail: 'Security settings saved successfully',\n        life: 3000\n      });\n    } catch (error) {\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to save security settings',\n        life: 3000\n      });\n    } finally {\n      this.savingSecurity = false;\n    }\n  }\n\n  resetSystemSettings() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to reset system settings to default values?',\n      header: 'Confirm Reset',\n      icon: 'pi pi-exclamation-triangle',\n      accept: () => {\n        // Reset to default values\n        this.systemSettings = {\n          siteName: 'Formation Management System',\n          siteDescription: 'Complete training management solution',\n          adminEmail: '<EMAIL>',\n          defaultLanguage: 'fr',\n          timezone: 'Europe/Paris',\n          dateFormat: 'DD/MM/YYYY',\n          timeFormat: '24h',\n          maxFileSize: 10,\n          allowedFileTypes: ['pdf', 'doc', 'docx', 'ppt', 'pptx'],\n          emailNotifications: true,\n          smsNotifications: false,\n          autoBackup: true,\n          backupFrequency: 'daily',\n          maintenanceMode: false,\n          debugMode: false\n        };\n\n        this.messageService.add({\n          severity: 'info',\n          summary: 'Reset',\n          detail: 'System settings reset to default values',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  testEmailNotification() {\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Test Email',\n      detail: 'Test email notification sent',\n      life: 3000\n    });\n  }\n\n  performBackup() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to perform a manual backup?',\n      header: 'Confirm Backup',\n      icon: 'pi pi-question-circle',\n      accept: () => {\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Backup',\n          detail: 'Manual backup initiated successfully',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  clearCache() {\n    this.confirmationService.confirm({\n      message: 'Are you sure you want to clear the system cache?',\n      header: 'Confirm Cache Clear',\n      icon: 'pi pi-question-circle',\n      accept: () => {\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Cache',\n          detail: 'System cache cleared successfully',\n          life: 3000\n        });\n      }\n    });\n  }\n\n  exportSettings() {\n    const settings = {\n      system: this.systemSettings,\n      notifications: this.notificationSettings,\n      security: this.securitySettings\n    };\n\n    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' });\n    const url = window.URL.createObjectURL(blob);\n    const link = window.document.createElement('a');\n    link.href = url;\n    link.download = 'system-settings.json';\n    link.click();\n    window.URL.revokeObjectURL(url);\n\n    this.messageService.add({\n      severity: 'success',\n      summary: 'Export',\n      detail: 'Settings exported successfully',\n      life: 3000\n    });\n  }\n}\n", "<div class=\"grid\">\n    <div class=\"col-12\">\n        <div class=\"card\">\n            <h5>System Settings</h5>\n            <p-toast></p-toast>\n            <p-confirmDialog></p-confirmDialog>\n\n            <p-tabView [(activeIndex)]=\"activeTab\">\n\n                <!-- System Settings Tab -->\n                <p-tabPanel header=\"System\" leftIcon=\"pi pi-cog\">\n                    <div class=\"card\">\n                        <h6>General System Settings</h6>\n\n                        <div class=\"grid\">\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"siteName\">Site Name</label>\n                                    <input\n                                        id=\"siteName\"\n                                        type=\"text\"\n                                        pInputText\n                                        [(ngModel)]=\"systemSettings.siteName\"\n                                        class=\"w-full\">\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"adminEmail\">Admin Email</label>\n                                    <input\n                                        id=\"adminEmail\"\n                                        type=\"email\"\n                                        pInputText\n                                        [(ngModel)]=\"systemSettings.adminEmail\"\n                                        class=\"w-full\">\n                                </div>\n                            </div>\n                            <div class=\"col-12\">\n                                <div class=\"field\">\n                                    <label for=\"siteDescription\">Site Description</label>\n                                    <textarea\n                                        id=\"siteDescription\"\n                                        pInputTextarea\n                                        [(ngModel)]=\"systemSettings.siteDescription\"\n                                        rows=\"3\"\n                                        class=\"w-full\">\n                                    </textarea>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"defaultLanguage\">Default Language</label>\n                                    <p-dropdown\n                                        id=\"defaultLanguage\"\n                                        [(ngModel)]=\"systemSettings.defaultLanguage\"\n                                        [options]=\"languageOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"timezone\">Timezone</label>\n                                    <p-dropdown\n                                        id=\"timezone\"\n                                        [(ngModel)]=\"systemSettings.timezone\"\n                                        [options]=\"timezoneOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"dateFormat\">Date Format</label>\n                                    <p-dropdown\n                                        id=\"dateFormat\"\n                                        [(ngModel)]=\"systemSettings.dateFormat\"\n                                        [options]=\"dateFormatOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"timeFormat\">Time Format</label>\n                                    <p-dropdown\n                                        id=\"timeFormat\"\n                                        [(ngModel)]=\"systemSettings.timeFormat\"\n                                        [options]=\"timeFormatOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"maxFileSize\">Max File Size (MB)</label>\n                                    <p-inputNumber\n                                        id=\"maxFileSize\"\n                                        [(ngModel)]=\"systemSettings.maxFileSize\"\n                                        [min]=\"1\"\n                                        [max]=\"100\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"backupFrequency\">Backup Frequency</label>\n                                    <p-dropdown\n                                        id=\"backupFrequency\"\n                                        [(ngModel)]=\"systemSettings.backupFrequency\"\n                                        [options]=\"backupFrequencyOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"grid mt-4\">\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"emailNotifications\"\n                                        [(ngModel)]=\"systemSettings.emailNotifications\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"emailNotifications\">Enable Email Notifications</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"smsNotifications\"\n                                        [(ngModel)]=\"systemSettings.smsNotifications\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"smsNotifications\">Enable SMS Notifications</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"autoBackup\"\n                                        [(ngModel)]=\"systemSettings.autoBackup\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"autoBackup\">Enable Auto Backup</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"maintenanceMode\"\n                                        [(ngModel)]=\"systemSettings.maintenanceMode\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"maintenanceMode\">Maintenance Mode</label>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"flex gap-2 mt-4\">\n                            <p-button\n                                label=\"Save Settings\"\n                                icon=\"pi pi-save\"\n                                [loading]=\"savingSystem\"\n                                (onClick)=\"saveSystemSettings()\">\n                            </p-button>\n                            <p-button\n                                label=\"Reset to Default\"\n                                icon=\"pi pi-refresh\"\n                                class=\"p-button-secondary\"\n                                (onClick)=\"resetSystemSettings()\">\n                            </p-button>\n                            <p-button\n                                label=\"Export Settings\"\n                                icon=\"pi pi-download\"\n                                class=\"p-button-info\"\n                                (onClick)=\"exportSettings()\">\n                            </p-button>\n                        </div>\n                    </div>\n                </p-tabPanel>\n\n                <!-- Notifications Tab -->\n                <p-tabPanel header=\"Notifications\" leftIcon=\"pi pi-bell\">\n                    <div class=\"card\">\n                        <h6>Notification Settings</h6>\n\n                        <div class=\"grid\">\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"formationReminder\"\n                                        [(ngModel)]=\"notificationSettings.formationReminder\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"formationReminder\">Formation Reminders</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"attendanceAlert\"\n                                        [(ngModel)]=\"notificationSettings.attendanceAlert\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"attendanceAlert\">Attendance Alerts</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"reportGeneration\"\n                                        [(ngModel)]=\"notificationSettings.reportGeneration\"\n                                        [binary]=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"reportGeneration\">Report Generation</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"systemUpdates\"\n                                        [(ngModel)]=\"notificationSettings.systemUpdates\"\n                                        binary=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"systemUpdates\">System Updates</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"reminderDays\">Reminder Days Before</label>\n                                    <p-inputNumber\n                                        id=\"reminderDays\"\n                                        [(ngModel)]=\"notificationSettings.reminderDays\"\n                                        [min]=\"1\"\n                                        [max]=\"30\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"emailTemplate\">Email Template</label>\n                                    <p-dropdown\n                                        id=\"emailTemplate\"\n                                        [(ngModel)]=\"notificationSettings.emailTemplate\"\n                                        [options]=\"emailTemplateOptions\"\n                                        optionLabel=\"label\"\n                                        optionValue=\"value\"\n                                        class=\"w-full\">\n                                    </p-dropdown>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"flex gap-2 mt-4\">\n                            <p-button\n                                label=\"Save Notifications\"\n                                icon=\"pi pi-save\"\n                                [loading]=\"savingNotifications\"\n                                (onClick)=\"saveNotificationSettings()\">\n                            </p-button>\n                            <p-button\n                                label=\"Test Email\"\n                                icon=\"pi pi-send\"\n                                class=\"p-button-info\"\n                                (onClick)=\"testEmailNotification()\">\n                            </p-button>\n                        </div>\n                    </div>\n                </p-tabPanel>\n\n                <!-- Security Tab -->\n                <p-tabPanel header=\"Security\" leftIcon=\"pi pi-shield\">\n                    <div class=\"card\">\n                        <h6>Security Settings</h6>\n\n                        <div class=\"grid\">\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"passwordMinLength\">Password Min Length</label>\n                                    <p-inputNumber\n                                        id=\"passwordMinLength\"\n                                        [(ngModel)]=\"securitySettings.passwordMinLength\"\n                                        [min]=\"6\"\n                                        [max]=\"20\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"sessionTimeout\">Session Timeout (minutes)</label>\n                                    <p-inputNumber\n                                        id=\"sessionTimeout\"\n                                        [(ngModel)]=\"securitySettings.sessionTimeout\"\n                                        [min]=\"5\"\n                                        [max]=\"120\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"maxLoginAttempts\">Max Login Attempts</label>\n                                    <p-inputNumber\n                                        id=\"maxLoginAttempts\"\n                                        [(ngModel)]=\"securitySettings.maxLoginAttempts\"\n                                        [min]=\"3\"\n                                        [max]=\"10\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field\">\n                                    <label for=\"lockoutDuration\">Lockout Duration (minutes)</label>\n                                    <p-inputNumber\n                                        id=\"lockoutDuration\"\n                                        [(ngModel)]=\"securitySettings.lockoutDuration\"\n                                        [min]=\"5\"\n                                        [max]=\"60\"\n                                        class=\"w-full\">\n                                    </p-inputNumber>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"grid mt-4\">\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"passwordRequireUppercase\"\n                                        [(ngModel)]=\"securitySettings.passwordRequireUppercase\"\n                                        binary=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"passwordRequireUppercase\">Require Uppercase</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"passwordRequireNumbers\"\n                                        [(ngModel)]=\"securitySettings.passwordRequireNumbers\"\n                                        binary=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"passwordRequireNumbers\">Require Numbers</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"passwordRequireSymbols\"\n                                        [(ngModel)]=\"securitySettings.passwordRequireSymbols\"\n                                        binary=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"passwordRequireSymbols\">Require Symbols</label>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-6\">\n                                <div class=\"field-checkbox\">\n                                    <p-checkbox\n                                        id=\"twoFactorAuth\"\n                                        [(ngModel)]=\"securitySettings.twoFactorAuth\"\n                                        binary=\"true\">\n                                    </p-checkbox>\n                                    <label for=\"twoFactorAuth\">Two-Factor Authentication</label>\n                                </div>\n                            </div>\n                        </div>\n\n                        <div class=\"flex gap-2 mt-4\">\n                            <p-button\n                                label=\"Save Security\"\n                                icon=\"pi pi-save\"\n                                [loading]=\"savingSecurity\"\n                                (onClick)=\"saveSecuritySettings()\">\n                            </p-button>\n                        </div>\n                    </div>\n                </p-tabPanel>\n\n                <!-- Maintenance Tab -->\n                <p-tabPanel header=\"Maintenance\" leftIcon=\"pi pi-wrench\">\n                    <div class=\"card\">\n                        <h6>System Maintenance</h6>\n\n                        <div class=\"grid\">\n                            <div class=\"col-12 md:col-4\">\n                                <div class=\"card text-center\">\n                                    <i class=\"pi pi-database text-4xl text-blue-500 mb-3\"></i>\n                                    <h6>Database Backup</h6>\n                                    <p class=\"text-600\">Perform manual database backup</p>\n                                    <p-button\n                                        label=\"Backup Now\"\n                                        icon=\"pi pi-download\"\n                                        class=\"p-button-info\"\n                                        (onClick)=\"performBackup()\">\n                                    </p-button>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-4\">\n                                <div class=\"card text-center\">\n                                    <i class=\"pi pi-refresh text-4xl text-green-500 mb-3\"></i>\n                                    <h6>Clear Cache</h6>\n                                    <p class=\"text-600\">Clear system cache and temporary files</p>\n                                    <p-button\n                                        label=\"Clear Cache\"\n                                        icon=\"pi pi-trash\"\n                                        class=\"p-button-success\"\n                                        (onClick)=\"clearCache()\">\n                                    </p-button>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-4\">\n                                <div class=\"card text-center\">\n                                    <i class=\"pi pi-cog text-4xl text-orange-500 mb-3\"></i>\n                                    <h6>System Info</h6>\n                                    <p class=\"text-600\">View system information and logs</p>\n                                    <p-button\n                                        label=\"View Info\"\n                                        icon=\"pi pi-info-circle\"\n                                        class=\"p-button-warning\"\n                                        disabled=\"true\">\n                                    </p-button>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </p-tabPanel>\n\n            </p-tabView>\n        </div>\n    </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}