{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function takeUntil(notifier) {\n  return operate((source, subscriber) => {\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => subscriber.complete(), noop));\n    !subscriber.closed && source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "noop", "takeUntil", "notifier", "source", "subscriber", "subscribe", "complete", "closed"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/takeUntil.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { noop } from '../util/noop';\nexport function takeUntil(notifier) {\n    return operate((source, subscriber) => {\n        innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, () => subscriber.complete(), noop));\n        !subscriber.closed && source.subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6B;EAChC,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnCL,SAAS,CAACG,QAAD,CAAT,CAAoBG,SAApB,CAA8BP,wBAAwB,CAACM,UAAD,EAAa,MAAMA,UAAU,CAACE,QAAX,EAAnB,EAA0CN,IAA1C,CAAtD;IACA,CAACI,UAAU,CAACG,MAAZ,IAAsBJ,MAAM,CAACE,SAAP,CAAiBD,UAAjB,CAAtB;EACH,CAHa,CAAd;AAIH"}, "metadata": {}, "sourceType": "module"}