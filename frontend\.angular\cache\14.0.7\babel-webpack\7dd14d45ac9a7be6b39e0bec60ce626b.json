{"ast": null, "code": "const {\n  isArray\n} = Array;\nexport function argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}", "map": {"version": 3, "names": ["isArray", "Array", "argsOrArgArray", "args", "length"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/argsOrArgArray.js"], "sourcesContent": ["const { isArray } = Array;\nexport function argsOrArgArray(args) {\n    return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}\n"], "mappings": "AAAA,MAAM;EAAEA;AAAF,IAAcC,KAApB;AACA,OAAO,SAASC,cAAT,CAAwBC,IAAxB,EAA8B;EACjC,OAAOA,IAAI,CAACC,MAAL,KAAgB,CAAhB,IAAqBJ,OAAO,CAACG,IAAI,CAAC,CAAD,CAAL,CAA5B,GAAwCA,IAAI,CAAC,CAAD,CAA5C,GAAkDA,IAAzD;AACH"}, "metadata": {}, "sourceType": "module"}