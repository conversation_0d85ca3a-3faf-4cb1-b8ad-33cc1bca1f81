{"ast": null, "code": "import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n  return pipe(toArray(), mergeMap(sources => joinFn(sources)), project ? mapOneOrManyArgs(project) : identity);\n}", "map": {"version": 3, "names": ["identity", "mapOneOrManyArgs", "pipe", "mergeMap", "toArray", "joinAllInternals", "joinFn", "project", "sources"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/joinAllInternals.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n    return pipe(toArray(), mergeMap((sources) => joinFn(sources)), project ? mapOneOrManyArgs(project) : identity);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,kBAAzB;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,OAAO,SAASC,gBAAT,CAA0BC,MAA1B,EAAkCC,OAAlC,EAA2C;EAC9C,OAAOL,IAAI,CAACE,OAAO,EAAR,EAAYD,QAAQ,CAAEK,OAAD,IAAaF,MAAM,CAACE,OAAD,CAApB,CAApB,EAAoDD,OAAO,GAAGN,gBAAgB,CAACM,OAAD,CAAnB,GAA+BP,QAA1F,CAAX;AACH"}, "metadata": {}, "sourceType": "module"}