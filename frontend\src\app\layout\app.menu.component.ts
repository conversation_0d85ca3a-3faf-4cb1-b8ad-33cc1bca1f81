import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { LayoutService } from './service/app.layout.service';


@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent implements OnInit {
    model: any[] = [];

    constructor(public layoutService: LayoutService) { }

    ngOnInit() {
        this.model = [
            {
                label: 'Home',
                items: [
                    { label: 'Dashboard', icon: 'pi pi-fw pi-home', routerLink: ['/'] }
                ]
            },
            {
                label: 'About',
                items: [
                    { label: 'Input', icon: 'pi pi-fw pi-check-square', routerLink: ['/uikit/input'] },
                    { label: 'List', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/list'] },
                    { label: 'Tree', icon: 'pi pi-fw pi-share-alt', routerLink: ['/uikit/tree'] },
                    { label: 'Discussion Panel', icon: 'pi pi-fw pi-comments', routerLink: ['/uikit/panel'] },

                    {
                        label: 'User Management', icon: 'pi pi-fw pi-pencil',
                        items: [
                            { label: 'Trainers', icon: 'pi pi-fw pi-user-edit', routerLink: ['/uikit/crud/trainers'] },
                            { label: 'Employees', icon: 'pi pi-fw pi-users', routerLink: ['/uikit/crud/employees'] },
                            { label: 'Teams', icon: 'pi pi-fw pi-sitemap', routerLink: ['/uikit/crud/teams'] }
                        ]
                    },
                    {
                        label: 'Training Management', icon: 'pi pi-fw pi-briefcase',
                        items: [
                            { label: 'Add Training', icon: 'pi pi-fw pi-id-card', routerLink: ['/uikit/formlayout'] },
                            { label: 'List Trainings', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/listtrain'] }
                        ]
                    },
                    {
                        label: 'Admin Panel', icon: 'pi pi-fw pi-cog',
                        items: [
                            { label: 'Documents', icon: 'pi pi-fw pi-file', routerLink: ['/uikit/documents'] },
                            { label: 'Statistics', icon: 'pi pi-fw pi-chart-line', routerLink: ['/uikit/statistics'] },
                            { label: 'Charts', icon: 'pi pi-fw pi-chart-bar', routerLink: ['/uikit/charts'] },

                            { label: 'Reports', icon: 'pi pi-fw pi-file-pdf', routerLink: ['/uikit/reports'] },
                            { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/uikit/settings'] }
                        ]
                    }
                ]
            },
           
           
            {
                label: 'Account',
                icon: 'pi pi-fw pi-user',
                routerLink: ['/pages'],
                items: [
                    {
                        label: 'Profile',
                        icon: 'pi pi-fw pi-id-card',
                        routerLink: ['/pages/profile']
                    },
                    {
                        label: 'Settings',
                        icon: 'pi pi-fw pi-cog',
                        routerLink: ['/pages/settings']
                    },
                    {
                        label: 'Help',
                        icon: 'pi pi-fw pi-question',
                        routerLink: ['/pages/help']
                    },
                    {
                      
                                label: 'Logout',
                                icon: 'pi pi-fw pi-sign-out',
                                routerLink: ['/auth/logout']
                    }
                           
                           
                        ]
                    },
                    
                   
                ]
            }
      
           
      
    }

