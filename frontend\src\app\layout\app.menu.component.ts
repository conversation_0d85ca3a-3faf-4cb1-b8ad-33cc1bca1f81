import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { LayoutService } from './service/app.layout.service';


@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent implements OnInit {
    model: any[] = [];

    constructor(public layoutService: LayoutService) { }

    ngOnInit() {
        this.model = [
            // ===== ADMIN MENU =====
            {
                label: 'HOME',
                items: [
                    { label: 'Dashboard', icon: 'pi pi-fw pi-home', routerLink: ['/'] }
                ]
            },
            {
                label: 'MANAGEMENT',
                items: [
                    {
                        label: 'User Management', icon: 'pi pi-fw pi-users',
                        items: [
                            { label: 'Employees', icon: 'pi pi-fw pi-user', routerLink: ['/uikit/crud/employees'] },
                            { label: 'Trainers', icon: 'pi pi-fw pi-user-edit', routerLink: ['/uikit/crud/trainers'] },
                            { label: 'Teams', icon: 'pi pi-fw pi-sitemap', routerLink: ['/uikit/crud/teams'] }
                        ]
                    },
                    {
                        label: 'Training Management', icon: 'pi pi-fw pi-briefcase',
                        items: [
                            { label: 'Create Training', icon: 'pi pi-fw pi-plus', routerLink: ['/uikit/formlayout'] },
                            { label: 'All Trainings', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/listtrain'] }
                        ]
                    }
                ]
            },
            {
                label: 'ANALYTICS',
                items: [
                    { label: 'Statistics', icon: 'pi pi-fw pi-chart-line', routerLink: ['/uikit/statistics'] },
                    { label: 'Charts', icon: 'pi pi-fw pi-chart-bar', routerLink: ['/uikit/charts'] }
                ]
            },
            {
                label: 'SYSTEM',
                items: [
                    { label: 'Discussion Panel', icon: 'pi pi-fw pi-comments', routerLink: ['/uikit/panel'] },
                    { label: 'Documents', icon: 'pi pi-fw pi-file', routerLink: ['/uikit/documents'] },
                    { label: 'Reports', icon: 'pi pi-fw pi-file-pdf', routerLink: ['/uikit/reports'] },
                    { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/uikit/settings'] }
                ]
            },
            {
                label: 'ACCOUNT',
                items: [
                    { label: 'Profile', icon: 'pi pi-fw pi-id-card', routerLink: ['/pages/profile'] },
                    { label: 'Settings', icon: 'pi pi-fw pi-cog', routerLink: ['/pages/settings'] },
                    { label: 'Help', icon: 'pi pi-fw pi-question', routerLink: ['/pages/help'] },
                    { label: 'Logout', icon: 'pi pi-fw pi-sign-out', routerLink: ['/auth/logout'] }
                ]
            }

            /* ===== PRESERVED CODE FOR FUTURE TRAINER/EMPLOYEE MODULES =====

            // FOR TRAINER MODULE:
            {
                label: 'TRAINER TOOLS',
                items: [
                    { label: 'My Trainings', icon: 'pi pi-fw pi-briefcase', routerLink: ['/trainer/trainings'] },
                    { label: 'Attendance', icon: 'pi pi-fw pi-check-square', routerLink: ['/trainer/attendance'] },
                    { label: 'Materials', icon: 'pi pi-fw pi-file', routerLink: ['/trainer/materials'] }
                ]
            },

            // FOR EMPLOYEE MODULE:
            {
                label: 'MY LEARNING',
                items: [
                    { label: 'My Trainings', icon: 'pi pi-fw pi-calendar', routerLink: ['/employee/trainings'] },
                    { label: 'Progress', icon: 'pi pi-fw pi-chart-line', routerLink: ['/employee/progress'] },
                    { label: 'Certificates', icon: 'pi pi-fw pi-award', routerLink: ['/employee/certificates'] }
                ]
            },

            // UTILITY COMPONENTS (can be reused):
            { label: 'Input', icon: 'pi pi-fw pi-check-square', routerLink: ['/uikit/input'] },
            { label: 'List', icon: 'pi pi-fw pi-list', routerLink: ['/uikit/list'] },
            { label: 'Tree', icon: 'pi pi-fw pi-share-alt', routerLink: ['/uikit/tree'] }

            ===== END PRESERVED CODE ===== */
        ];
    }
}