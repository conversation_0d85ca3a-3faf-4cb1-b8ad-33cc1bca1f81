{"ast": null, "code": "export const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined))();\nexport function errorNotification(error) {\n  return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n  return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n  return {\n    kind,\n    value,\n    error\n  };\n}", "map": {"version": 3, "names": ["COMPLETE_NOTIFICATION", "createNotification", "undefined", "errorNotification", "error", "nextNotification", "value", "kind"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/NotificationFactories.js"], "sourcesContent": ["export const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined))();\nexport function errorNotification(error) {\n    return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n    return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n    return {\n        kind,\n        value,\n        error,\n    };\n}\n"], "mappings": "AAAA,OAAO,MAAMA,qBAAqB,GAAG,CAAC,MAAMC,kBAAkB,CAAC,GAAD,EAAMC,SAAN,EAAiBA,SAAjB,CAAzB,GAA9B;AACP,OAAO,SAASC,iBAAT,CAA2BC,KAA3B,EAAkC;EACrC,OAAOH,kBAAkB,CAAC,GAAD,EAAMC,SAAN,EAAiBE,KAAjB,CAAzB;AACH;AACD,OAAO,SAASC,gBAAT,CAA0BC,KAA1B,EAAiC;EACpC,OAAOL,kBAAkB,CAAC,GAAD,EAAMK,KAAN,EAAaJ,SAAb,CAAzB;AACH;AACD,OAAO,SAASD,kBAAT,CAA4BM,IAA5B,EAAkCD,KAAlC,EAAyCF,KAAzC,EAAgD;EACnD,OAAO;IACHG,IADG;IAEHD,KAFG;IAGHF;EAHG,CAAP;AAKH"}, "metadata": {}, "sourceType": "module"}