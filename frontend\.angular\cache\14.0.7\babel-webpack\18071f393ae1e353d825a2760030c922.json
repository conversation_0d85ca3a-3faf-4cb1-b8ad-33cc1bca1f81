{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/formation.service\";\nimport * as i2 from \"../../../../services/team.service\";\nimport * as i3 from \"../../../service/user.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/dropdown\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/inputtextarea\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/table\";\nimport * as i12 from \"primeng/ripple\";\nimport * as i13 from \"primeng/toast\";\nimport * as i14 from \"primeng/toolbar\";\nimport * as i15 from \"primeng/dialog\";\n\nfunction FormLayoutComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.openNew());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deleteSelectedFormations());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.selectedFormations || !ctx_r0.selectedFormations.length);\n  }\n}\n\nfunction FormLayoutComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h5\", 21);\n    i0.ɵɵtext(2, \"Manage Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵlistener(\"input\", function FormLayoutComponent_ng_template_8_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n\n      const _r1 = i0.ɵɵreference(7);\n\n      return i0.ɵɵresetView(ctx_r13.onGlobalFilter(_r1, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 25);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 26);\n    i0.ɵɵtext(4, \"ID \");\n    i0.ɵɵelement(5, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 28);\n    i0.ɵɵtext(7, \"Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 30);\n    i0.ɵɵtext(10, \"Date \");\n    i0.ɵɵelement(11, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 32);\n    i0.ɵɵtext(13, \"Duration \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Trainer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(19, \"th\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\")(17, \"div\", 35)(18, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_10_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const formation_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editFormation(formation_r15));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_10_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const formation_r15 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.deleteFormation(formation_r15));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const formation_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", formation_r15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formation_r15.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formation_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 7, formation_r15.date, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", formation_r15.duree, \"h\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getTeamName(formation_r15.equipe_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getTrainerName(formation_r15.formateur_id));\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_small_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 55);\n    i0.ɵɵtext(1, \"Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_small_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 55);\n    i0.ɵɵtext(1, \"Date is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_small_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 55);\n    i0.ɵɵtext(1, \"Duration is required and must be greater than 0.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_small_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 55);\n    i0.ɵɵtext(1, \"Team is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n\n  if (rf & 2) {\n    const trainer_r25 = ctx.$implicit;\n    i0.ɵɵtextInterpolate2(\" \", trainer_r25.first_name, \" \", trainer_r25.last_name, \" \");\n  }\n}\n\nfunction FormLayoutComponent_ng_template_12_small_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 55);\n    i0.ɵɵtext(1, \"Trainer is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"ng-invalid ng-dirty\": a0\n  };\n};\n\nfunction FormLayoutComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"label\", 40);\n    i0.ɵɵtext(3, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.formation.name = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, FormLayoutComponent_ng_template_12_small_5_Template, 2, 0, \"small\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39)(7, \"label\", 43);\n    i0.ɵɵtext(8, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"input\", 44);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_input_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.formation.date = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, FormLayoutComponent_ng_template_12_small_10_Template, 2, 0, \"small\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 38)(12, \"div\", 39)(13, \"label\", 45);\n    i0.ɵɵtext(14, \"Duration (hours)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"input\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_input_ngModelChange_15_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.formation.duree = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, FormLayoutComponent_ng_template_12_small_16_Template, 2, 0, \"small\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"label\", 47);\n    i0.ɵɵtext(19, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p-dropdown\", 48);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_p_dropdown_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.formation.equipe_id = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, FormLayoutComponent_ng_template_12_small_21_Template, 2, 0, \"small\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 49)(23, \"label\", 50);\n    i0.ɵɵtext(24, \"Trainer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p-dropdown\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_p_dropdown_ngModelChange_25_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.formation.formateur_id = $event);\n    });\n    i0.ɵɵtemplate(26, FormLayoutComponent_ng_template_12_ng_template_26_Template, 1, 2, \"ng-template\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, FormLayoutComponent_ng_template_12_small_27_Template, 2, 0, \"small\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 49)(29, \"label\", 53);\n    i0.ɵɵtext(30, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"textarea\", 54);\n    i0.ɵɵlistener(\"ngModelChange\", function FormLayoutComponent_ng_template_12_Template_textarea_ngModelChange_31_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.formation.description = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.formation.name)(\"ngClass\", i0.ɵɵpureFunction1(20, _c0, ctx_r5.submitted && !ctx_r5.formation.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.formation.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.formation.date)(\"ngClass\", i0.ɵɵpureFunction1(22, _c0, ctx_r5.submitted && !ctx_r5.formation.date));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.formation.date);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.formation.duree)(\"ngClass\", i0.ɵɵpureFunction1(24, _c0, ctx_r5.submitted && (!ctx_r5.formation.duree || ctx_r5.formation.duree <= 0)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && (!ctx_r5.formation.duree || ctx_r5.formation.duree <= 0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r5.teams)(\"ngModel\", ctx_r5.formation.equipe_id)(\"required\", true)(\"ngClass\", i0.ɵɵpureFunction1(26, _c0, ctx_r5.submitted && !ctx_r5.formation.equipe_id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.formation.equipe_id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r5.trainers)(\"ngModel\", ctx_r5.formation.formateur_id)(\"required\", true)(\"ngClass\", i0.ɵɵpureFunction1(28, _c0, ctx_r5.submitted && !ctx_r5.formation.formateur_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.formation.formateur_id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.formation.description);\n  }\n}\n\nfunction FormLayoutComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.hideDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.saveFormation());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Are you sure you want to delete \");\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"?\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.formation.name);\n  }\n}\n\nfunction FormLayoutComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.deleteFormationDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_18_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.confirmDelete());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction FormLayoutComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.deleteFormationsDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function FormLayoutComponent_ng_template_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.confirmDeleteSelected());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return [\"name\", \"description\"];\n};\n\nconst _c2 = function () {\n  return [10, 20, 30];\n};\n\nconst _c3 = function () {\n  return {\n    width: \"600px\"\n  };\n};\n\nconst _c4 = function () {\n  return {\n    width: \"450px\"\n  };\n};\n\nexport class FormLayoutComponent {\n  constructor(formationService, teamService, userService, messageService) {\n    this.formationService = formationService;\n    this.teamService = teamService;\n    this.userService = userService;\n    this.messageService = messageService;\n    this.formationDialog = false;\n    this.deleteFormationDialog = false;\n    this.deleteFormationsDialog = false;\n    this.formations = [];\n    this.formation = {};\n    this.selectedFormations = [];\n    this.submitted = false;\n    this.cols = [];\n    this.rowsPerPageOptions = [5, 10, 20];\n    this.teams = [];\n    this.trainers = [];\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // Load formations\n        _this.formations = yield _this.formationService.getFormations(); // Load teams\n\n        _this.teams = yield lastValueFrom(_this.teamService.getTeams()); // Load trainers\n\n        const trainersData = yield _this.userService.getUsersByRole('formateur');\n        _this.trainers = trainersData.map(trainer => Object.assign(Object.assign({}, trainer), {\n          name: `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`\n        }));\n      } catch (error) {\n        console.error('Error loading data:', error);\n\n        _this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load data',\n          life: 3000\n        });\n      }\n\n      _this.cols = [{\n        field: 'id',\n        header: 'ID'\n      }, {\n        field: 'name',\n        header: 'Name'\n      }, {\n        field: 'date',\n        header: 'Date'\n      }, {\n        field: 'duree',\n        header: 'Duration (h)'\n      }, {\n        field: 'equipe',\n        header: 'Team'\n      }, {\n        field: 'formateur',\n        header: 'Trainer'\n      }];\n    })();\n  }\n\n  openNew() {\n    this.formation = {\n      name: '',\n      description: '',\n      date: '',\n      duree: 0,\n      equipe_id: 0,\n      formateur_id: 0\n    };\n    this.submitted = false;\n    this.formationDialog = true;\n  }\n\n  onGlobalFilter(table, event) {\n    const input = event.target;\n    table.filterGlobal(input.value, 'contains');\n  }\n\n  editFormation(formation) {\n    this.formation = Object.assign({}, formation);\n    this.formationDialog = true;\n  }\n\n  deleteFormation(formation) {\n    this.formation = formation;\n    this.deleteFormationDialog = true;\n  }\n\n  deleteSelectedFormations() {\n    this.deleteFormationsDialog = true;\n  }\n\n  hideDialog() {\n    this.formationDialog = false;\n    this.submitted = false;\n  }\n\n  saveFormation() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      _this2.submitted = true;\n\n      if (((_a = _this2.formation.name) === null || _a === void 0 ? void 0 : _a.trim()) && _this2.formation.date && _this2.formation.duree > 0 && _this2.formation.equipe_id && _this2.formation.formateur_id) {\n        try {\n          if (_this2.formation.id) {\n            // Update existing formation\n            const updatedFormation = yield lastValueFrom(_this2.formationService.updateFormation(_this2.formation.id, _this2.formation));\n\n            const index = _this2.findIndexById(_this2.formation.id);\n\n            _this2.formations[index] = updatedFormation;\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Formation Updated',\n              life: 3000\n            });\n          } else {\n            // Create new formation\n            const newFormation = yield lastValueFrom(_this2.formationService.createFormation(_this2.formation));\n\n            _this2.formations.push(newFormation);\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Formation Created',\n              life: 3000\n            });\n          }\n\n          _this2.formationDialog = false;\n          _this2.formation = {};\n        } catch (error) {\n          console.error('Error saving formation:', error);\n\n          _this2.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to save formation',\n            life: 3000\n          });\n        }\n      }\n    })();\n  }\n\n  confirmDelete() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield lastValueFrom(_this3.formationService.deleteFormation(_this3.formation.id));\n        _this3.formations = _this3.formations.filter(val => val.id !== _this3.formation.id);\n        _this3.deleteFormationDialog = false;\n        _this3.formation = {};\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Formation Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting formation:', error);\n\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete formation',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  confirmDeleteSelected() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        for (const formation of _this4.selectedFormations) {\n          yield lastValueFrom(_this4.formationService.deleteFormation(formation.id));\n        }\n\n        _this4.formations = _this4.formations.filter(val => !_this4.selectedFormations.includes(val));\n        _this4.deleteFormationsDialog = false;\n        _this4.selectedFormations = [];\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Formations Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting formations:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete formations',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  findIndexById(id) {\n    return this.formations.findIndex(formation => formation.id === id);\n  }\n\n  getTeamName(teamId) {\n    const team = this.teams.find(t => t.id === teamId);\n    return team ? team.name : 'Unknown';\n  }\n\n  getTrainerName(trainerId) {\n    const trainer = this.trainers.find(t => t.id === trainerId);\n    if (!trainer) return 'Unknown';\n    return `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`;\n  }\n\n}\n\nFormLayoutComponent.ɵfac = function FormLayoutComponent_Factory(t) {\n  return new (t || FormLayoutComponent)(i0.ɵɵdirectiveInject(i1.FormationService), i0.ɵɵdirectiveInject(i2.TeamService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.MessageService));\n};\n\nFormLayoutComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: FormLayoutComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 25,\n  vars: 27,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\", \"px-6\", \"py-6\"], [\"styleClass\", \"mb-4\"], [\"pTemplate\", \"left\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} formations\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt\", \"\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"header\", \"Formation Details\", 1, \"p-fluid\", 3, \"visible\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [\"header\", \"Confirm\", 3, \"visible\", \"modal\", \"visibleChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-exclamation-triangle\", \"mr-3\", 2, \"font-size\", \"2rem\"], [4, \"ngIf\"], [1, \"my-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"New\", \"icon\", \"pi pi-plus\", 1, \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Delete\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", 3, \"disabled\", \"click\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"md:align-items-center\"], [1, \"m-0\"], [1, \"block\", \"mt-2\", \"md:mt-0\", \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"id\"], [\"field\", \"id\"], [\"pSortableColumn\", \"name\"], [\"field\", \"name\"], [\"pSortableColumn\", \"date\"], [\"field\", \"date\"], [\"pSortableColumn\", \"duree\"], [\"field\", \"duree\"], [3, \"value\"], [1, \"flex\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-pencil\", 1, \"p-button-rounded\", \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-trash\", 1, \"p-button-rounded\", \"p-button-warning\", 3, \"click\"], [1, \"formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col-6\"], [\"for\", \"name\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"name\", \"required\", \"\", \"autofocus\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"class\", \"ng-dirty ng-invalid\", 4, \"ngIf\"], [\"for\", \"date\"], [\"type\", \"date\", \"pInputText\", \"\", \"id\", \"date\", \"required\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"for\", \"duree\"], [\"type\", \"number\", \"pInputText\", \"\", \"id\", \"duree\", \"required\", \"\", \"min\", \"1\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"for\", \"team\"], [\"id\", \"team\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select a team\", 3, \"options\", \"ngModel\", \"required\", \"ngClass\", \"ngModelChange\"], [1, \"field\"], [\"for\", \"trainer\"], [\"id\", \"trainer\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select a trainer\", 3, \"options\", \"ngModel\", \"required\", \"ngClass\", \"ngModelChange\"], [\"pTemplate\", \"item\"], [\"for\", \"description\"], [\"pInputTextarea\", \"\", \"id\", \"description\", \"rows\", \"3\", \"placeholder\", \"Optional description\", 3, \"ngModel\", \"ngModelChange\"], [1, \"ng-dirty\", \"ng-invalid\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Save\", \"icon\", \"pi pi-check\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-times\", \"label\", \"No\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-check\", \"label\", \"Yes\", 1, \"p-button-text\", 3, \"click\"]],\n  template: function FormLayoutComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n      i0.ɵɵelement(3, \"p-toast\");\n      i0.ɵɵelementStart(4, \"p-toolbar\", 3);\n      i0.ɵɵtemplate(5, FormLayoutComponent_ng_template_5_Template, 3, 1, \"ng-template\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"p-table\", 5, 6);\n      i0.ɵɵlistener(\"selectionChange\", function FormLayoutComponent_Template_p_table_selectionChange_6_listener($event) {\n        return ctx.selectedFormations = $event;\n      });\n      i0.ɵɵtemplate(8, FormLayoutComponent_ng_template_8_Template, 6, 0, \"ng-template\", 7);\n      i0.ɵɵtemplate(9, FormLayoutComponent_ng_template_9_Template, 20, 0, \"ng-template\", 8);\n      i0.ɵɵtemplate(10, FormLayoutComponent_ng_template_10_Template, 20, 10, \"ng-template\", 9);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(11, \"p-dialog\", 10);\n      i0.ɵɵlistener(\"visibleChange\", function FormLayoutComponent_Template_p_dialog_visibleChange_11_listener($event) {\n        return ctx.formationDialog = $event;\n      });\n      i0.ɵɵtemplate(12, FormLayoutComponent_ng_template_12_Template, 32, 30, \"ng-template\", 11);\n      i0.ɵɵtemplate(13, FormLayoutComponent_ng_template_13_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function FormLayoutComponent_Template_p_dialog_visibleChange_14_listener($event) {\n        return ctx.deleteFormationDialog = $event;\n      });\n      i0.ɵɵelementStart(15, \"div\", 14);\n      i0.ɵɵelement(16, \"i\", 15);\n      i0.ɵɵtemplate(17, FormLayoutComponent_span_17_Template, 5, 1, \"span\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(18, FormLayoutComponent_ng_template_18_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function FormLayoutComponent_Template_p_dialog_visibleChange_19_listener($event) {\n        return ctx.deleteFormationsDialog = $event;\n      });\n      i0.ɵɵelementStart(20, \"div\", 14);\n      i0.ɵɵelement(21, \"i\", 15);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Are you sure you want to delete selected formations?\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(24, FormLayoutComponent_ng_template_24_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"value\", ctx.formations)(\"columns\", ctx.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(22, _c1))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c2))(\"showCurrentPageReport\", true)(\"selection\", ctx.selectedFormations)(\"rowHover\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.formationDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(25, _c4));\n      i0.ɵɵproperty(\"visible\", ctx.deleteFormationDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.formation);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c4));\n      i0.ɵɵproperty(\"visible\", ctx.deleteFormationsDialog)(\"modal\", true);\n    }\n  },\n  dependencies: [i5.NgClass, i5.NgIf, i6.Dropdown, i4.PrimeTemplate, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.RequiredValidator, i7.MinValidator, i7.NgModel, i8.InputText, i9.InputTextarea, i10.ButtonDirective, i11.Table, i11.SortableColumn, i11.SortIcon, i11.TableCheckbox, i11.TableHeaderCheckbox, i12.Ripple, i13.Toast, i14.Toolbar, i15.Dialog, i5.DatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAOA,SAASA,cAAT,QAA+B,aAA/B;AAEA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;;;;;;;;;;ICFoBC,gCAAkB,CAAlB,EAAkB,QAAlB,EAAkB,EAAlB;IACwFA;MAAAA;MAAA;MAAA,OAASA,iCAAT;IAAkB,CAAlB;IAAoBA;IACxGA;IAAkFA;MAAAA;MAAA;MAAA,OAASA,kDAAT;IAAmC,CAAnC;IAAoGA;;;;;IAA/DA;IAAAA;;;;;;;;IAW3HA,gCAA2F,CAA3F,EAA2F,IAA3F,EAA2F,EAA3F;IACoBA;IAAiBA;IACjCA;IACIA;IACAA;IAA8BA;MAAAA;MAAA;;MAAA;;MAAA,OAASA,mDAAT;IAAmC,CAAnC;IAA9BA;;;;;;IAMRA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IAEQA;IACJA;IACAA;IAAyBA;IAAGA;IAAoCA;IAChEA;IAA2BA;IAAKA;IAAsCA;IACtEA;IAA2BA;IAAKA;IAAsCA;IACtEA;IAA4BA;IAASA;IAAuCA;IAC5EA;IAAIA;IAAIA;IACRA;IAAIA;IAAOA;IACXA;IACJA;;;;;;;;IAIAA,2BAAI,CAAJ,EAAI,IAAJ;IAEQA;IACJA;IACAA;IAAIA;IAAgBA;IACpBA;IAAIA;IAAkBA;IACtBA;IAAIA;;IAAiCA;IACrCA;IAAIA;IAAoBA;IACxBA;IAAIA;IAAoCA;IACxCA;IAAIA;IAA0CA;IAC9CA,4BAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,EAAJ,EAAI,QAAJ,EAAI,EAAJ;IAEmGA;MAAA;MAAA;MAAA;MAAA,OAASA,oDAAT;IAAiC,CAAjC;IAAmCA;IAC9HA;IAAqFA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAAT;IAAmC,CAAnC;IAAqCA;;;;;;IAX7GA;IAAAA;IAEjBA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;;;IAsBZA;IAAwEA;IAAiBA;;;;;;IAMzFA;IAAwEA;IAAiBA;;;;;;IAQzFA;IAAmGA;IAAgDA;;;;;;IAOnJA;IAA6EA;IAAiBA;;;;;;IAS1FA;;;;;IAAAA;;;;;;IAGRA;IAAgFA;IAAoBA;;;;;;;;;;;;;;IAtCxGA,gCAA2B,CAA3B,EAA2B,KAA3B,EAA2B,EAA3B,EAA2B,CAA3B,EAA2B,OAA3B,EAA2B,EAA3B;IAE0BA;IAAIA;IACtBA;IAAwCA;MAAAA;MAAA;MAAA,OAAaA,+CAAb;IAAmC,CAAnC;IAAxCA;IAEAA;IACJA;IACAA,gCAAmC,CAAnC,EAAmC,OAAnC,EAAmC,EAAnC;IACsBA;IAAIA;IACtBA;IAAwCA;MAAAA;MAAA;MAAA,OAAaA,+CAAb;IAAmC,CAAnC;IAAxCA;IAEAA;IACJA;IAEJA,iCAA2B,EAA3B,EAA2B,KAA3B,EAA2B,EAA3B,EAA2B,EAA3B,EAA2B,OAA3B,EAA2B,EAA3B;IAE2BA;IAAgBA;IACnCA;IAA2CA;MAAAA;MAAA;MAAA,OAAaA,gDAAb;IAAoC,CAApC;IAA3CA;IAEAA;IACJA;IACAA,iCAAmC,EAAnC,EAAmC,OAAnC,EAAmC,EAAnC;IACsBA;IAAIA;IACtBA;IAAwCA;MAAAA;MAAA;MAAA,OAAaA,oDAAb;IAClD,CADkD;IAE2CA;IACnFA;IACJA;IAEJA,iCAAmB,EAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IACyBA;IAAOA;IAC5BA;IAA8CA;MAAAA;MAAA;MAAA,OAAaA,uDAAb;IACpD,CADoD;IAG1CA;IAGJA;IACAA;IACJA;IACAA,iCAAmB,EAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IAC6BA;IAAWA;IACpCA;IAA0CA;MAAAA;MAAA;MAAA,OAAaA,sDAAb;IAA0C,CAA1C;IACEA;;;;;IAxCAA;IAAAA,gDAA4B,SAA5B,EAA4BA,uEAA5B;IAEJA;IAAAA;IAIIA;IAAAA,gDAA4B,SAA5B,EAA4BA,uEAA5B;IAEJA;IAAAA;IAMOA;IAAAA,iDAA6B,SAA7B,EAA6BA,yGAA7B;IAEPA;IAAAA;IAIdA;IAAAA,uCAAiB,SAAjB,EAAiBC,0BAAjB,EAAiB,UAAjB,EAAiB,IAAjB,EAAiB,SAAjB,EAAiBD,4EAAjB;IAGcA;IAAAA;IAKfA;IAAAA,0CAAoB,SAApB,EAAoBC,6BAApB,EAAoB,UAApB,EAAoB,IAApB,EAAoB,SAApB,EAAoBD,+EAApB;IAOWA;IAAAA;IAIMA;IAAAA;;;;;;;;IAK9CA;IAAgFA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IAAuBA;IACvGA;IAA8EA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAA0BA;;;;;;IAQxGA;IAAwBA;IAAgCA;IAAGA;IAAkBA;IAAIA;IAACA;;;;;IAAvBA;IAAAA;;;;;;;;IAG3DA;IAA4EA;MAAAA;MAAA;MAAA,sDAAiC,KAAjC;IAAsC,CAAtC;IAAwCA;IACpHA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAA0BA;;;;;;;;IAWvGA;IAA4EA;MAAAA;MAAA;MAAA,uDAAkC,KAAlC;IAAuC,CAAvC;IAAyCA;IACrHA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAAkCA;;;;;;;;;;;;;;;;;;;;;;;;ADhIvH,OAAM,MAAOE,mBAAP,CAA0B;EAa5BC,YACYC,gBADZ,EAEYC,WAFZ,EAGYC,WAHZ,EAIYC,cAJZ,EAI0C;IAH9B;IACA;IACA;IACA;IAhBZ,uBAA2B,KAA3B;IACA,6BAAiC,KAAjC;IACA,8BAAkC,KAAlC;IACA,kBAA0B,EAA1B;IACA,iBAAuB,EAAvB;IACA,0BAAkC,EAAlC;IACA,iBAAqB,KAArB;IACA,YAAc,EAAd;IACA,0BAAqB,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,CAArB;IACA,aAAgB,EAAhB;IACA,gBAAmB,EAAnB;EAOI;;EAEEC,QAAQ;IAAA;;IAAA;MACV,IAAI;QACA;QACA,KAAI,CAACC,UAAL,SAAwB,KAAI,CAACL,gBAAL,CAAsBM,aAAtB,EAAxB,CAFA,CAIA;;QACA,KAAI,CAACC,KAAL,SAAmBZ,aAAa,CAAC,KAAI,CAACM,WAAL,CAAiBO,QAAjB,EAAD,CAAhC,CALA,CAOA;;QACA,MAAMC,YAAY,SAAS,KAAI,CAACP,WAAL,CAAiBQ,cAAjB,CAAgC,WAAhC,CAA3B;QACA,KAAI,CAACC,QAAL,GAAgBF,YAAY,CAACG,GAAb,CAAiBC,OAAO,IAAIC,gCACrCD,OADqC,GAC9B;UACVE,IAAI,EAAE,GAAGF,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,UAAR,GAAqB,OAAOL,OAAO,CAACK,UAAf,GAA4B,GAAjD,GAAuD,EAAE;QADlG,CAD8B,CAA5B,CAAhB;MAIH,CAbD,CAaE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,qBAAd,EAAqCA,KAArC;;QACA,KAAI,CAAChB,cAAL,CAAoBkB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,qBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;;MAED,KAAI,CAACC,IAAL,GAAY,CACR;QAAEC,KAAK,EAAE,IAAT;QAAeC,MAAM,EAAE;MAAvB,CADQ,EAER;QAAED,KAAK,EAAE,MAAT;QAAiBC,MAAM,EAAE;MAAzB,CAFQ,EAGR;QAAED,KAAK,EAAE,MAAT;QAAiBC,MAAM,EAAE;MAAzB,CAHQ,EAIR;QAAED,KAAK,EAAE,OAAT;QAAkBC,MAAM,EAAE;MAA1B,CAJQ,EAKR;QAAED,KAAK,EAAE,QAAT;QAAmBC,MAAM,EAAE;MAA3B,CALQ,EAMR;QAAED,KAAK,EAAE,WAAT;QAAsBC,MAAM,EAAE;MAA9B,CANQ,CAAZ;IAxBU;EAgCb;;EAEDC,OAAO;IACH,KAAKC,SAAL,GAAiB;MACbf,IAAI,EAAE,EADO;MAEbgB,WAAW,EAAE,EAFA;MAGbC,IAAI,EAAE,EAHO;MAIbC,KAAK,EAAE,CAJM;MAKbC,SAAS,EAAE,CALE;MAMbC,YAAY,EAAE;IAND,CAAjB;IAQA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,eAAL,GAAuB,IAAvB;EACH;;EAEDC,cAAc,CAACC,KAAD,EAAeC,KAAf,EAA2B;IACrC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAApB;IACAH,KAAK,CAACI,YAAN,CAAmBF,KAAK,CAACG,KAAzB,EAAgC,UAAhC;EACH;;EAEDC,aAAa,CAACf,SAAD,EAAqB;IAC9B,KAAKA,SAAL,GAAchB,kBAAQgB,SAAR,CAAd;IACA,KAAKO,eAAL,GAAuB,IAAvB;EACH;;EAEDS,eAAe,CAAChB,SAAD,EAAqB;IAChC,KAAKA,SAAL,GAAiBA,SAAjB;IACA,KAAKiB,qBAAL,GAA6B,IAA7B;EACH;;EAEDC,wBAAwB;IACpB,KAAKC,sBAAL,GAA8B,IAA9B;EACH;;EAEDC,UAAU;IACN,KAAKb,eAAL,GAAuB,KAAvB;IACA,KAAKD,SAAL,GAAiB,KAAjB;EACH;;EAEKe,aAAa;IAAA;;IAAA;;;MACf,MAAI,CAACf,SAAL,GAAiB,IAAjB;;MAEA,IAAI,aAAI,CAACN,SAAL,CAAef,IAAf,MAAmB,IAAnB,IAAmBqC,aAAnB,GAAmB,MAAnB,GAAmBA,GAAEC,IAAF,EAAnB,KAA+B,MAAI,CAACvB,SAAL,CAAeE,IAA9C,IAAsD,MAAI,CAACF,SAAL,CAAeG,KAAf,GAAuB,CAA7E,IACA,MAAI,CAACH,SAAL,CAAeI,SADf,IAC4B,MAAI,CAACJ,SAAL,CAAeK,YAD/C,EAC6D;QACzD,IAAI;UACA,IAAI,MAAI,CAACL,SAAL,CAAewB,EAAnB,EAAuB;YACnB;YACA,MAAMC,gBAAgB,SAAS5D,aAAa,CACxC,MAAI,CAACK,gBAAL,CAAsBwD,eAAtB,CAAsC,MAAI,CAAC1B,SAAL,CAAewB,EAArD,EAAyD,MAAI,CAACxB,SAA9D,CADwC,CAA5C;;YAGA,MAAM2B,KAAK,GAAG,MAAI,CAACC,aAAL,CAAmB,MAAI,CAAC5B,SAAL,CAAewB,EAAlC,CAAd;;YACA,MAAI,CAACjD,UAAL,CAAgBoD,KAAhB,IAAyBF,gBAAzB;;YACA,MAAI,CAACpD,cAAL,CAAoBkB,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,mBAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH,CAbD,MAaO;YACH;YACA,MAAMkC,YAAY,SAAShE,aAAa,CACpC,MAAI,CAACK,gBAAL,CAAsB4D,eAAtB,CAAsC,MAAI,CAAC9B,SAA3C,CADoC,CAAxC;;YAGA,MAAI,CAACzB,UAAL,CAAgBwD,IAAhB,CAAqBF,YAArB;;YACA,MAAI,CAACxD,cAAL,CAAoBkB,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,mBAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH;;UAED,MAAI,CAACY,eAAL,GAAuB,KAAvB;UACA,MAAI,CAACP,SAAL,GAAiB,EAAjB;QACH,CA9BD,CA8BE,OAAOX,KAAP,EAAc;UACZC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;;UACA,MAAI,CAAChB,cAAL,CAAoBkB,GAApB,CAAwB;YACpBC,QAAQ,EAAE,OADU;YAEpBC,OAAO,EAAE,OAFW;YAGpBC,MAAM,EAAE,0BAHY;YAIpBC,IAAI,EAAE;UAJc,CAAxB;QAMH;MACJ;IA5Cc;EA6ClB;;EAEKqC,aAAa;IAAA;;IAAA;MACf,IAAI;QACA,MAAMnE,aAAa,CAAC,MAAI,CAACK,gBAAL,CAAsB8C,eAAtB,CAAsC,MAAI,CAAChB,SAAL,CAAewB,EAArD,CAAD,CAAnB;QACA,MAAI,CAACjD,UAAL,GAAkB,MAAI,CAACA,UAAL,CAAgB0D,MAAhB,CAAuBC,GAAG,IAAIA,GAAG,CAACV,EAAJ,KAAW,MAAI,CAACxB,SAAL,CAAewB,EAAxD,CAAlB;QACA,MAAI,CAACP,qBAAL,GAA6B,KAA7B;QACA,MAAI,CAACjB,SAAL,GAAiB,EAAjB;;QACA,MAAI,CAAC3B,cAAL,CAAoBkB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,mBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAXD,CAWE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;;QACA,MAAI,CAAChB,cAAL,CAAoBkB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,4BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IApBc;EAqBlB;;EAEKwC,qBAAqB;IAAA;;IAAA;MACvB,IAAI;QACA,KAAK,MAAMnC,SAAX,IAAwB,MAAI,CAACoC,kBAA7B,EAAiD;UAC7C,MAAMvE,aAAa,CAAC,MAAI,CAACK,gBAAL,CAAsB8C,eAAtB,CAAsChB,SAAS,CAACwB,EAAhD,CAAD,CAAnB;QACH;;QACD,MAAI,CAACjD,UAAL,GAAkB,MAAI,CAACA,UAAL,CAAgB0D,MAAhB,CAAuBC,GAAG,IAAI,CAAC,MAAI,CAACE,kBAAL,CAAwBC,QAAxB,CAAiCH,GAAjC,CAA/B,CAAlB;QACA,MAAI,CAACf,sBAAL,GAA8B,KAA9B;QACA,MAAI,CAACiB,kBAAL,GAA0B,EAA1B;;QACA,MAAI,CAAC/D,cAAL,CAAoBkB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,oBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAbD,CAaE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;;QACA,MAAI,CAAChB,cAAL,CAAoBkB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,6BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAtBsB;EAuB1B;;EAEDiC,aAAa,CAACJ,EAAD,EAAW;IACpB,OAAO,KAAKjD,UAAL,CAAgB+D,SAAhB,CAA0BtC,SAAS,IAAIA,SAAS,CAACwB,EAAV,KAAiBA,EAAxD,CAAP;EACH;;EAEDe,WAAW,CAACC,MAAD,EAAe;IACtB,MAAMC,IAAI,GAAG,KAAKhE,KAAL,CAAWiE,IAAX,CAAgBC,CAAC,IAAIA,CAAC,CAACnB,EAAF,KAASgB,MAA9B,CAAb;IACA,OAAOC,IAAI,GAAGA,IAAI,CAACxD,IAAR,GAAe,SAA1B;EACH;;EAED2D,cAAc,CAACC,SAAD,EAAkB;IAC5B,MAAM9D,OAAO,GAAG,KAAKF,QAAL,CAAc6D,IAAd,CAAmBC,CAAC,IAAIA,CAAC,CAACnB,EAAF,KAASqB,SAAjC,CAAhB;IACA,IAAI,CAAC9D,OAAL,EAAc,OAAO,SAAP;IACd,OAAO,GAAGA,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,UAAR,GAAqB,OAAOL,OAAO,CAACK,UAAf,GAA4B,GAAjD,GAAuD,EAAE,EAA7G;EACH;;AAvM2B;;;mBAAnBpB,qBAAmBF;AAAA;;;QAAnBE;EAAmB8E;EAAAC,iCAFjB,CAACnF,cAAD,CAEiB;EAFDoF;EAAAC;EAAAC;EAAAC;IAAA;MCZ/BrF,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB;MAGYA;MACAA;MACIA;MAMJA;MAEAA;MAGSA;QAAA;MAAA;MAELA;MAUAA;MAeAA;MAmBJA;MAMZA;MAAUA;QAAA;MAAA;MACNA;MA+CAA;MAIJA;MAGAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MACJA;MACAA;MAIJA;MAGAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MAAMA;MAAoDA;MAE9DA;MAIJA;;;;MAnIyBA;MAAAA,uCAAoB,SAApB,EAAoBsF,QAApB,EAAoB,MAApB,EAAoB,EAApB,EAAoB,oBAApB,EAAoBtF,2BAApB,EAAoB,WAApB,EAAoB,IAApB,EAAoB,oBAApB,EAAoBA,2BAApB,EAAoB,uBAApB,EAAoB,IAApB,EAAoB,WAApB,EAAoBsF,sBAApB,EAAoB,UAApB,EAAoB,IAApB;MAuDetF;MAAAA;MAA9BA,8CAA6B,OAA7B,EAA6B,IAA7B;MAuDoEA;MAAAA;MAApEA,oDAAmC,OAAnC,EAAmC,IAAnC;MAGKA;MAAAA;MASgEA;MAAAA;MAArEA,qDAAoC,OAApC,EAAoC,IAApC", "names": ["MessageService", "lastValueFrom", "i0", "ctx_r5", "FormLayoutComponent", "constructor", "formationService", "teamService", "userService", "messageService", "ngOnInit", "formations", "getFormations", "teams", "getTeams", "trainersData", "getUsersByRole", "trainers", "map", "trainer", "Object", "name", "first_name", "last_name", "specialite", "error", "console", "add", "severity", "summary", "detail", "life", "cols", "field", "header", "openNew", "formation", "description", "date", "duree", "equipe_id", "formateur_id", "submitted", "formationDialog", "onGlobalFilter", "table", "event", "input", "target", "filterGlobal", "value", "editFormation", "deleteFormation", "deleteFormationDialog", "deleteSelectedFormations", "deleteFormationsDialog", "hideDialog", "saveFormation", "_a", "trim", "id", "updatedFormation", "updateFormation", "index", "findIndexById", "newFormation", "createFormation", "push", "confirmDelete", "filter", "val", "confirmDeleteSelected", "selectedFormations", "includes", "findIndex", "getTeamName", "teamId", "team", "find", "t", "getTrainerName", "trainerId", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\training\\formlayout.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\training\\formlayout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Formation } from '../../../../models/formation.model';\nimport { FormationService } from '../../../../services/formation.service';\nimport { TeamService } from '../../../../services/team.service';\nimport { UserService } from '../../../service/user.service';\nimport { Team } from '../../../../models/team.model';\nimport { User } from '../../../../models/user.model';\nimport { MessageService } from 'primeng/api';\nimport { Table } from 'primeng/table';\nimport { lastValueFrom } from 'rxjs';\n\n@Component({\n    templateUrl: './formlayout.component.html',\n    providers: [MessageService]\n})\nexport class FormLayoutComponent implements OnInit {\n    formationDialog: boolean = false;\n    deleteFormationDialog: boolean = false;\n    deleteFormationsDialog: boolean = false;\n    formations: Formation[] = [];\n    formation: Formation = {} as Formation;\n    selectedFormations: Formation[] = [];\n    submitted: boolean = false;\n    cols: any[] = [];\n    rowsPerPageOptions = [5, 10, 20];\n    teams: Team[] = [];\n    trainers: User[] = [];\n\n    constructor(\n        private formationService: FormationService,\n        private teamService: TeamService,\n        private userService: UserService,\n        private messageService: MessageService\n    ) {}\n\n    async ngOnInit() {\n        try {\n            // Load formations\n            this.formations = await this.formationService.getFormations();\n\n            // Load teams\n            this.teams = await lastValueFrom(this.teamService.getTeams());\n\n            // Load trainers\n            const trainersData = await this.userService.getUsersByRole('formateur');\n            this.trainers = trainersData.map(trainer => ({\n                ...trainer,\n                name: `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`\n            }));\n        } catch (error) {\n            console.error('Error loading data:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to load data',\n                life: 3000\n            });\n        }\n\n        this.cols = [\n            { field: 'id', header: 'ID' },\n            { field: 'name', header: 'Name' },\n            { field: 'date', header: 'Date' },\n            { field: 'duree', header: 'Duration (h)' },\n            { field: 'equipe', header: 'Team' },\n            { field: 'formateur', header: 'Trainer' }\n        ];\n    }\n\n    openNew() {\n        this.formation = {\n            name: '',\n            description: '',\n            date: '',\n            duree: 0,\n            equipe_id: 0,\n            formateur_id: 0\n        } as Formation;\n        this.submitted = false;\n        this.formationDialog = true;\n    }\n\n    onGlobalFilter(table: Table, event: Event) {\n        const input = event.target as HTMLInputElement;\n        table.filterGlobal(input.value, 'contains');\n    }\n\n    editFormation(formation: Formation) {\n        this.formation = { ...formation };\n        this.formationDialog = true;\n    }\n\n    deleteFormation(formation: Formation) {\n        this.formation = formation;\n        this.deleteFormationDialog = true;\n    }\n\n    deleteSelectedFormations() {\n        this.deleteFormationsDialog = true;\n    }\n\n    hideDialog() {\n        this.formationDialog = false;\n        this.submitted = false;\n    }\n\n    async saveFormation() {\n        this.submitted = true;\n\n        if (this.formation.name?.trim() && this.formation.date && this.formation.duree > 0 &&\n            this.formation.equipe_id && this.formation.formateur_id) {\n            try {\n                if (this.formation.id) {\n                    // Update existing formation\n                    const updatedFormation = await lastValueFrom(\n                        this.formationService.updateFormation(this.formation.id, this.formation)\n                    );\n                    const index = this.findIndexById(this.formation.id);\n                    this.formations[index] = updatedFormation;\n                    this.messageService.add({\n                        severity: 'success',\n                        summary: 'Successful',\n                        detail: 'Formation Updated',\n                        life: 3000\n                    });\n                } else {\n                    // Create new formation\n                    const newFormation = await lastValueFrom(\n                        this.formationService.createFormation(this.formation)\n                    );\n                    this.formations.push(newFormation);\n                    this.messageService.add({\n                        severity: 'success',\n                        summary: 'Successful',\n                        detail: 'Formation Created',\n                        life: 3000\n                    });\n                }\n\n                this.formationDialog = false;\n                this.formation = {} as Formation;\n            } catch (error) {\n                console.error('Error saving formation:', error);\n                this.messageService.add({\n                    severity: 'error',\n                    summary: 'Error',\n                    detail: 'Failed to save formation',\n                    life: 3000\n                });\n            }\n        }\n    }\n\n    async confirmDelete() {\n        try {\n            await lastValueFrom(this.formationService.deleteFormation(this.formation.id));\n            this.formations = this.formations.filter(val => val.id !== this.formation.id);\n            this.deleteFormationDialog = false;\n            this.formation = {} as Formation;\n            this.messageService.add({\n                severity: 'success',\n                summary: 'Successful',\n                detail: 'Formation Deleted',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error deleting formation:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to delete formation',\n                life: 3000\n            });\n        }\n    }\n\n    async confirmDeleteSelected() {\n        try {\n            for (const formation of this.selectedFormations) {\n                await lastValueFrom(this.formationService.deleteFormation(formation.id));\n            }\n            this.formations = this.formations.filter(val => !this.selectedFormations.includes(val));\n            this.deleteFormationsDialog = false;\n            this.selectedFormations = [];\n            this.messageService.add({\n                severity: 'success',\n                summary: 'Successful',\n                detail: 'Formations Deleted',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error deleting formations:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to delete formations',\n                life: 3000\n            });\n        }\n    }\n\n    findIndexById(id: number): number {\n        return this.formations.findIndex(formation => formation.id === id);\n    }\n\n    getTeamName(teamId: number): string {\n        const team = this.teams.find(t => t.id === teamId);\n        return team ? team.name : 'Unknown';\n    }\n\n    getTrainerName(trainerId: number): string {\n        const trainer = this.trainers.find(t => t.id === trainerId);\n        if (!trainer) return 'Unknown';\n        return `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`;\n    }\n}\n", "\n<div class=\"grid\">\n    <div class=\"col-12\">\n        <div class=\"card px-6 py-6\">\n            <p-toast></p-toast>\n            <p-toolbar styleClass=\"mb-4\">\n                <ng-template pTemplate=\"left\">\n                    <div class=\"my-2\">\n                        <button pButton pRipple label=\"New\" icon=\"pi pi-plus\" class=\"p-button-success mr-2\" (click)=\"openNew()\"></button>\n                        <button pButton pRipple label=\"Delete\" icon=\"pi pi-trash\" class=\"p-button-danger\" (click)=\"deleteSelectedFormations()\" [disabled]=\"!selectedFormations || !selectedFormations.length\"></button>\n                    </div>\n                </ng-template>\n            </p-toolbar>\n\n            <p-table #dt [value]=\"formations\" [columns]=\"cols\" responsiveLayout=\"scroll\" [rows]=\"10\"\n                     [globalFilterFields]=\"['name','description']\" [paginator]=\"true\" [rowsPerPageOptions]=\"[10,20,30]\"\n                     [showCurrentPageReport]=\"true\" currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} formations\"\n                     [(selection)]=\"selectedFormations\" selectionMode=\"multiple\" [rowHover]=\"true\" dataKey=\"id\">\n\n                <ng-template pTemplate=\"caption\">\n                    <div class=\"flex flex-column md:flex-row md:justify-content-between md:align-items-center\">\n                        <h5 class=\"m-0\">Manage Formations</h5>\n                        <span class=\"block mt-2 md:mt-0 p-input-icon-left\">\n                            <i class=\"pi pi-search\"></i>\n                            <input pInputText type=\"text\" (input)=\"onGlobalFilter(dt, $event)\" placeholder=\"Search...\" class=\"w-full sm:w-auto\"/>\n                        </span>\n                    </div>\n                </ng-template>\n\n                <ng-template pTemplate=\"header\">\n                    <tr>\n                        <th style=\"width: 3rem\">\n                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\n                        </th>\n                        <th pSortableColumn=\"id\">ID <p-sortIcon field=\"id\"></p-sortIcon></th>\n                        <th pSortableColumn=\"name\">Name <p-sortIcon field=\"name\"></p-sortIcon></th>\n                        <th pSortableColumn=\"date\">Date <p-sortIcon field=\"date\"></p-sortIcon></th>\n                        <th pSortableColumn=\"duree\">Duration <p-sortIcon field=\"duree\"></p-sortIcon></th>\n                        <th>Team</th>\n                        <th>Trainer</th>\n                        <th></th>\n                    </tr>\n                </ng-template>\n\n                <ng-template pTemplate=\"body\" let-formation>\n                    <tr>\n                        <td>\n                            <p-tableCheckbox [value]=\"formation\"></p-tableCheckbox>\n                        </td>\n                        <td>{{formation.id}}</td>\n                        <td>{{formation.name}}</td>\n                        <td>{{formation.date | date:'short'}}</td>\n                        <td>{{formation.duree}}h</td>\n                        <td>{{getTeamName(formation.equipe_id)}}</td>\n                        <td>{{getTrainerName(formation.formateur_id)}}</td>\n                        <td>\n                            <div class=\"flex\">\n                                <button pButton pRipple icon=\"pi pi-pencil\" class=\"p-button-rounded p-button-success mr-2\" (click)=\"editFormation(formation)\"></button>\n                                <button pButton pRipple icon=\"pi pi-trash\" class=\"p-button-rounded p-button-warning\" (click)=\"deleteFormation(formation)\"></button>\n                            </div>\n                        </td>\n                    </tr>\n                </ng-template>\n            </p-table>\n        </div>\n    </div>\n</div>\n\n<!-- Formation Dialog -->\n<p-dialog [(visible)]=\"formationDialog\" [style]=\"{width: '600px'}\" header=\"Formation Details\" [modal]=\"true\" class=\"p-fluid\">\n    <ng-template pTemplate=\"content\">\n        <div class=\"formgrid grid\">\n            <div class=\"field col-12 md:col-6\">\n                <label for=\"name\">Name</label>\n                <input type=\"text\" pInputText id=\"name\" [(ngModel)]=\"formation.name\" required autofocus\n                       [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !formation.name}\"/>\n                <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !formation.name\">Name is required.</small>\n            </div>\n            <div class=\"field col-12 md:col-6\">\n                <label for=\"date\">Date</label>\n                <input type=\"date\" pInputText id=\"date\" [(ngModel)]=\"formation.date\" required\n                       [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !formation.date}\"/>\n                <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !formation.date\">Date is required.</small>\n            </div>\n        </div>\n        <div class=\"formgrid grid\">\n            <div class=\"field col-12 md:col-6\">\n                <label for=\"duree\">Duration (hours)</label>\n                <input type=\"number\" pInputText id=\"duree\" [(ngModel)]=\"formation.duree\" required min=\"1\"\n                       [ngClass]=\"{'ng-invalid ng-dirty' : submitted && (!formation.duree || formation.duree <= 0)}\"/>\n                <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && (!formation.duree || formation.duree <= 0)\">Duration is required and must be greater than 0.</small>\n            </div>\n            <div class=\"field col-12 md:col-6\">\n                <label for=\"team\">Team</label>\n                <p-dropdown id=\"team\" [options]=\"teams\" [(ngModel)]=\"formation.equipe_id\"\n                           optionLabel=\"name\" optionValue=\"id\" placeholder=\"Select a team\" [required]=\"true\"\n                           [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !formation.equipe_id}\"></p-dropdown>\n                <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !formation.equipe_id\">Team is required.</small>\n            </div>\n        </div>\n        <div class=\"field\">\n            <label for=\"trainer\">Trainer</label>\n            <p-dropdown id=\"trainer\" [options]=\"trainers\" [(ngModel)]=\"formation.formateur_id\"\n                       optionLabel=\"name\" optionValue=\"id\" placeholder=\"Select a trainer\" [required]=\"true\"\n                       [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !formation.formateur_id}\">\n                <ng-template let-trainer pTemplate=\"item\">\n                    {{trainer.first_name}} {{trainer.last_name}}\n                </ng-template>\n            </p-dropdown>\n            <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !formation.formateur_id\">Trainer is required.</small>\n        </div>\n        <div class=\"field\">\n            <label for=\"description\">Description</label>\n            <textarea pInputTextarea id=\"description\" [(ngModel)]=\"formation.description\" rows=\"3\"\n                     placeholder=\"Optional description\"></textarea>\n        </div>\n    </ng-template>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple label=\"Cancel\" icon=\"pi pi-times\" class=\"p-button-text\" (click)=\"hideDialog()\"></button>\n        <button pButton pRipple label=\"Save\" icon=\"pi pi-check\" class=\"p-button-text\" (click)=\"saveFormation()\"></button>\n    </ng-template>\n</p-dialog>\n\n<!-- Delete Formation Dialog -->\n<p-dialog [(visible)]=\"deleteFormationDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\n    <div class=\"flex align-items-center justify-content-center\">\n        <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\n        <span *ngIf=\"formation\">Are you sure you want to delete <b>{{formation.name}}</b>?</span>\n    </div>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteFormationDialog = false\"></button>\n        <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDelete()\"></button>\n    </ng-template>\n</p-dialog>\n\n<!-- Delete Formations Dialog -->\n<p-dialog [(visible)]=\"deleteFormationsDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\n    <div class=\"flex align-items-center justify-content-center\">\n        <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\n        <span>Are you sure you want to delete selected formations?</span>\n    </div>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteFormationsDialog = false\"></button>\n        <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDeleteSelected()\"></button>\n    </ng-template>\n</p-dialog>\n"]}, "metadata": {}, "sourceType": "module"}