{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate } from 'primeng/api';\nimport { ZIndexUtils } from 'primeng/utils';\n\nfunction Sidebar_div_0_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function Sidebar_div_0_button_3_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.close($event));\n    })(\"keydown.enter\", function Sidebar_div_0_button_3_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.close($event));\n    });\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaCloseLabel);\n  }\n}\n\nfunction Sidebar_div_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c0 = function (a1, a2, a3, a4, a5, a6) {\n  return {\n    \"p-sidebar\": true,\n    \"p-sidebar-active\": a1,\n    \"p-sidebar-left\": a2,\n    \"p-sidebar-right\": a3,\n    \"p-sidebar-top\": a4,\n    \"p-sidebar-bottom\": a5,\n    \"p-sidebar-full\": a6\n  };\n};\n\nconst _c1 = function (a0, a1) {\n  return {\n    transform: a0,\n    transition: a1\n  };\n};\n\nconst _c2 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction Sidebar_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"@panelState.start\", function Sidebar_div_0_Template_div_animation_panelState_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onAnimationStart($event));\n    })(\"@panelState.done\", function Sidebar_div_0_Template_div_animation_panelState_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 3);\n    i0.ɵɵtemplate(3, Sidebar_div_0_button_3_Template, 2, 1, \"button\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 5);\n    i0.ɵɵprojection(5);\n    i0.ɵɵtemplate(6, Sidebar_div_0_ng_container_6_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction6(8, _c0, ctx_r0.visible, ctx_r0.position === \"left\" && !ctx_r0.fullScreen, ctx_r0.position === \"right\" && !ctx_r0.fullScreen, ctx_r0.position === \"top\" && !ctx_r0.fullScreen, ctx_r0.position === \"bottom\" && !ctx_r0.fullScreen, ctx_r0.fullScreen))(\"@panelState\", i0.ɵɵpureFunction1(18, _c2, i0.ɵɵpureFunction2(15, _c1, ctx_r0.transformOptions, ctx_r0.transitionOptions)))(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵattribute(\"aria-modal\", ctx_r0.modal);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseIcon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n  }\n}\n\nconst _c3 = [\"*\"];\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}')]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n\nclass Sidebar {\n  constructor(el, renderer, cd, config) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.blockScroll = false;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.modal = true;\n    this.dismissible = true;\n    this.showCloseIcon = true;\n    this.closeOnEscape = true;\n    this.transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.visibleChange = new EventEmitter();\n    this._position = \"left\";\n    this._fullScreen = false;\n    this.transformOptions = \"translate3d(-100%, 0px, 0px)\";\n  }\n\n  ngAfterViewInit() {\n    this.initialized = true;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  get visible() {\n    return this._visible;\n  }\n\n  set visible(val) {\n    this._visible = val;\n  }\n\n  get position() {\n    return this._position;\n  }\n\n  set position(value) {\n    this._position = value;\n\n    switch (value) {\n      case 'left':\n        this.transformOptions = \"translate3d(-100%, 0px, 0px)\";\n        break;\n\n      case 'right':\n        this.transformOptions = \"translate3d(100%, 0px, 0px)\";\n        break;\n\n      case 'bottom':\n        this.transformOptions = \"translate3d(0px, 100%, 0px)\";\n        break;\n\n      case 'top':\n        this.transformOptions = \"translate3d(0px, -100%, 0px)\";\n        break;\n    }\n  }\n\n  get fullScreen() {\n    return this._fullScreen;\n  }\n\n  set fullScreen(value) {\n    this._fullScreen = value;\n    if (value) this.transformOptions = \"none\";\n  }\n\n  show() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n    }\n\n    if (this.modal) {\n      this.enableModality();\n    }\n\n    this.onShow.emit({});\n    this.visibleChange.emit(true);\n  }\n\n  hide() {\n    this.onHide.emit({});\n\n    if (this.modal) {\n      this.disableModality();\n    }\n  }\n\n  close(event) {\n    this.hide();\n    this.visibleChange.emit(false);\n    event.preventDefault();\n  }\n\n  enableModality() {\n    if (!this.mask) {\n      this.mask = document.createElement('div');\n      this.mask.style.zIndex = String(parseInt(this.container.style.zIndex) - 1);\n      DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n\n      if (this.dismissible) {\n        this.maskClickListener = this.renderer.listen(this.mask, 'click', event => {\n          if (this.dismissible) {\n            this.close(event);\n          }\n        });\n      }\n\n      document.body.appendChild(this.mask);\n\n      if (this.blockScroll) {\n        DomHandler.addClass(document.body, 'p-overflow-hidden');\n      }\n    }\n  }\n\n  disableModality() {\n    if (this.mask) {\n      DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n      this.animationEndListener = this.destroyModal.bind(this);\n      this.mask.addEventListener('animationend', this.animationEndListener);\n    }\n  }\n\n  destroyModal() {\n    this.unbindMaskClickListener();\n\n    if (this.mask) {\n      document.body.removeChild(this.mask);\n    }\n\n    if (this.blockScroll) {\n      DomHandler.removeClass(document.body, 'p-overflow-hidden');\n    }\n\n    this.unbindAnimationEndListener();\n    this.mask = null;\n  }\n\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.container = event.element;\n        this.appendContainer();\n        this.show();\n\n        if (this.closeOnEscape) {\n          this.bindDocumentEscapeListener();\n        }\n\n        break;\n    }\n  }\n\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.hide();\n        ZIndexUtils.clear(this.container);\n        this.unbindGlobalListeners();\n        break;\n    }\n  }\n\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n\n  bindDocumentEscapeListener() {\n    const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n    this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n      if (event.which == 27) {\n        if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n          this.close(event);\n        }\n      }\n    });\n  }\n\n  unbindDocumentEscapeListener() {\n    if (this.documentEscapeListener) {\n      this.documentEscapeListener();\n      this.documentEscapeListener = null;\n    }\n  }\n\n  unbindMaskClickListener() {\n    if (this.maskClickListener) {\n      this.maskClickListener();\n      this.maskClickListener = null;\n    }\n  }\n\n  unbindGlobalListeners() {\n    this.unbindMaskClickListener();\n    this.unbindDocumentEscapeListener();\n  }\n\n  unbindAnimationEndListener() {\n    if (this.animationEndListener && this.mask) {\n      this.mask.removeEventListener('animationend', this.animationEndListener);\n      this.animationEndListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    this.initialized = false;\n\n    if (this.visible && this.modal) {\n      this.destroyModal();\n    }\n\n    if (this.appendTo && this.container) {\n      this.el.nativeElement.appendChild(this.container);\n    }\n\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.container = null;\n    this.unbindGlobalListeners();\n    this.unbindAnimationEndListener();\n  }\n\n}\n\nSidebar.ɵfac = function Sidebar_Factory(t) {\n  return new (t || Sidebar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n};\n\nSidebar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Sidebar,\n  selectors: [[\"p-sidebar\"]],\n  contentQueries: function Sidebar_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    appendTo: \"appendTo\",\n    blockScroll: \"blockScroll\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    ariaCloseLabel: \"ariaCloseLabel\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    modal: \"modal\",\n    dismissible: \"dismissible\",\n    showCloseIcon: \"showCloseIcon\",\n    closeOnEscape: \"closeOnEscape\",\n    transitionOptions: \"transitionOptions\",\n    visible: \"visible\",\n    position: \"position\",\n    fullScreen: \"fullScreen\"\n  },\n  outputs: {\n    onShow: \"onShow\",\n    onHide: \"onHide\",\n    visibleChange: \"visibleChange\"\n  },\n  ngContentSelectors: _c3,\n  decls: 1,\n  vars: 1,\n  consts: [[\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"role\", \"complementary\", 3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [1, \"p-sidebar-header\"], [\"type\", \"button\", \"class\", \"p-sidebar-close p-sidebar-icon p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-sidebar-content\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-sidebar-close\", \"p-sidebar-icon\", \"p-link\", 3, \"click\", \"keydown.enter\"], [1, \"p-sidebar-close-icon\", \"pi\", \"pi-times\"]],\n  template: function Sidebar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, Sidebar_div_0_Template, 7, 20, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.visible);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple],\n  styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto}.p-sidebar-header{display:flex;align-items:center;justify-content:flex-end}.p-sidebar-icon{display:flex;align-items:center;justify-content:center}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Sidebar, [{\n    type: Component,\n    args: [{\n      selector: 'p-sidebar',\n      template: `\n        <div #container [ngClass]=\"{'p-sidebar':true, 'p-sidebar-active': visible,\n            'p-sidebar-left': (position === 'left' && !fullScreen), 'p-sidebar-right': (position === 'right' && !fullScreen),\n            'p-sidebar-top': (position === 'top' && !fullScreen), 'p-sidebar-bottom': (position === 'bottom' && !fullScreen),\n            'p-sidebar-full': fullScreen}\"  *ngIf=\"visible\" [@panelState]=\"{value: 'visible', params: {transform: transformOptions, transition: transitionOptions}}\" (@panelState.start)=\"onAnimationStart($event)\" (@panelState.done)=\"onAnimationEnd($event)\" [ngStyle]=\"style\" [class]=\"styleClass\"  role=\"complementary\" [attr.aria-modal]=\"modal\">\n            <div class=\"p-sidebar-header\">\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\"  *ngIf=\"showCloseIcon\" pRipple>\n                    <span class=\"p-sidebar-close-icon pi pi-times\"></span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('panelState', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto}.p-sidebar-header{display:flex;align-items:center;justify-content:flex-end}.p-sidebar-icon{display:flex;align-items:center;justify-content:center}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.PrimeNGConfig\n    }];\n  }, {\n    appendTo: [{\n      type: Input\n    }],\n    blockScroll: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    ariaCloseLabel: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    modal: [{\n      type: Input\n    }],\n    dismissible: [{\n      type: Input\n    }],\n    showCloseIcon: [{\n      type: Input\n    }],\n    closeOnEscape: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    visible: [{\n      type: Input\n    }],\n    position: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }]\n  });\n})();\n\nclass SidebarModule {}\n\nSidebarModule.ɵfac = function SidebarModule_Factory(t) {\n  return new (t || SidebarModule)();\n};\n\nSidebarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: SidebarModule\n});\nSidebarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RippleModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SidebarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule],\n      exports: [Sidebar],\n      declarations: [Sidebar]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Sidebar, SidebarModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChildren", "Output", "NgModule", "animation", "style", "animate", "trigger", "transition", "useAnimation", "i2", "CommonModule", "i3", "RippleModule", "<PERSON><PERSON><PERSON><PERSON>", "i1", "PrimeTemplate", "ZIndexUtils", "showAnimation", "transform", "opacity", "hideAnimation", "Sidebar", "constructor", "el", "renderer", "cd", "config", "blockScroll", "autoZIndex", "baseZIndex", "modal", "dismissible", "showCloseIcon", "closeOnEscape", "transitionOptions", "onShow", "onHide", "visibleChange", "_position", "_fullScreen", "transformOptions", "ngAfterViewInit", "initialized", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "contentTemplate", "template", "visible", "_visible", "val", "position", "value", "fullScreen", "show", "set", "container", "zIndex", "enableModality", "emit", "hide", "disableModality", "close", "event", "preventDefault", "mask", "document", "createElement", "String", "parseInt", "addMultipleClasses", "maskClickListener", "listen", "body", "append<PERSON><PERSON><PERSON>", "addClass", "animationEndListener", "destroyModal", "bind", "addEventListener", "unbindMaskClickListener", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "unbindAnimationEndListener", "onAnimationStart", "toState", "element", "append<PERSON><PERSON><PERSON>", "bindDocumentEscapeListener", "onAnimationEnd", "clear", "unbindGlobalListeners", "appendTo", "documentTarget", "nativeElement", "ownerDocument", "documentEscapeListener", "which", "get", "unbindDocumentEscapeListener", "removeEventListener", "ngOnDestroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "PrimeNGConfig", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "ariaCloseLabel", "SidebarModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-sidebar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, Output, NgModule } from '@angular/core';\nimport { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { DomHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate } from 'primeng/api';\nimport { ZIndexUtils } from 'primeng/utils';\n\nconst showAnimation = animation([\n    style({ transform: '{{transform}}', opacity: 0 }),\n    animate('{{transition}}')\n]);\nconst hideAnimation = animation([\n    animate('{{transition}}', style({ transform: '{{transform}}', opacity: 0 }))\n]);\nclass Sidebar {\n    constructor(el, renderer, cd, config) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.blockScroll = false;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.modal = true;\n        this.dismissible = true;\n        this.showCloseIcon = true;\n        this.closeOnEscape = true;\n        this.transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.visibleChange = new EventEmitter();\n        this._position = \"left\";\n        this._fullScreen = false;\n        this.transformOptions = \"translate3d(-100%, 0px, 0px)\";\n    }\n    ngAfterViewInit() {\n        this.initialized = true;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    get visible() {\n        return this._visible;\n    }\n    set visible(val) {\n        this._visible = val;\n    }\n    get position() {\n        return this._position;\n    }\n    ;\n    set position(value) {\n        this._position = value;\n        switch (value) {\n            case 'left':\n                this.transformOptions = \"translate3d(-100%, 0px, 0px)\";\n                break;\n            case 'right':\n                this.transformOptions = \"translate3d(100%, 0px, 0px)\";\n                break;\n            case 'bottom':\n                this.transformOptions = \"translate3d(0px, 100%, 0px)\";\n                break;\n            case 'top':\n                this.transformOptions = \"translate3d(0px, -100%, 0px)\";\n                break;\n        }\n    }\n    get fullScreen() {\n        return this._fullScreen;\n    }\n    set fullScreen(value) {\n        this._fullScreen = value;\n        if (value)\n            this.transformOptions = \"none\";\n    }\n    show() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('modal', this.container, this.baseZIndex || this.config.zIndex.modal);\n        }\n        if (this.modal) {\n            this.enableModality();\n        }\n        this.onShow.emit({});\n        this.visibleChange.emit(true);\n    }\n    hide() {\n        this.onHide.emit({});\n        if (this.modal) {\n            this.disableModality();\n        }\n    }\n    close(event) {\n        this.hide();\n        this.visibleChange.emit(false);\n        event.preventDefault();\n    }\n    enableModality() {\n        if (!this.mask) {\n            this.mask = document.createElement('div');\n            this.mask.style.zIndex = String(parseInt(this.container.style.zIndex) - 1);\n            DomHandler.addMultipleClasses(this.mask, 'p-component-overlay p-sidebar-mask p-component-overlay p-component-overlay-enter');\n            if (this.dismissible) {\n                this.maskClickListener = this.renderer.listen(this.mask, 'click', (event) => {\n                    if (this.dismissible) {\n                        this.close(event);\n                    }\n                });\n            }\n            document.body.appendChild(this.mask);\n            if (this.blockScroll) {\n                DomHandler.addClass(document.body, 'p-overflow-hidden');\n            }\n        }\n    }\n    disableModality() {\n        if (this.mask) {\n            DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n            this.animationEndListener = this.destroyModal.bind(this);\n            this.mask.addEventListener('animationend', this.animationEndListener);\n        }\n    }\n    destroyModal() {\n        this.unbindMaskClickListener();\n        if (this.mask) {\n            document.body.removeChild(this.mask);\n        }\n        if (this.blockScroll) {\n            DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        }\n        this.unbindAnimationEndListener();\n        this.mask = null;\n    }\n    onAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.container = event.element;\n                this.appendContainer();\n                this.show();\n                if (this.closeOnEscape) {\n                    this.bindDocumentEscapeListener();\n                }\n                break;\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.hide();\n                ZIndexUtils.clear(this.container);\n                this.unbindGlobalListeners();\n                break;\n        }\n    }\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    bindDocumentEscapeListener() {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n            if (event.which == 27) {\n                if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container)) {\n                    this.close(event);\n                }\n            }\n        });\n    }\n    unbindDocumentEscapeListener() {\n        if (this.documentEscapeListener) {\n            this.documentEscapeListener();\n            this.documentEscapeListener = null;\n        }\n    }\n    unbindMaskClickListener() {\n        if (this.maskClickListener) {\n            this.maskClickListener();\n            this.maskClickListener = null;\n        }\n    }\n    unbindGlobalListeners() {\n        this.unbindMaskClickListener();\n        this.unbindDocumentEscapeListener();\n    }\n    unbindAnimationEndListener() {\n        if (this.animationEndListener && this.mask) {\n            this.mask.removeEventListener('animationend', this.animationEndListener);\n            this.animationEndListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.initialized = false;\n        if (this.visible && this.modal) {\n            this.destroyModal();\n        }\n        if (this.appendTo && this.container) {\n            this.el.nativeElement.appendChild(this.container);\n        }\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.container = null;\n        this.unbindGlobalListeners();\n        this.unbindAnimationEndListener();\n    }\n}\nSidebar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Sidebar, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\nSidebar.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Sidebar, selector: \"p-sidebar\", inputs: { appendTo: \"appendTo\", blockScroll: \"blockScroll\", style: \"style\", styleClass: \"styleClass\", ariaCloseLabel: \"ariaCloseLabel\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", modal: \"modal\", dismissible: \"dismissible\", showCloseIcon: \"showCloseIcon\", closeOnEscape: \"closeOnEscape\", transitionOptions: \"transitionOptions\", visible: \"visible\", position: \"position\", fullScreen: \"fullScreen\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\", visibleChange: \"visibleChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div #container [ngClass]=\"{'p-sidebar':true, 'p-sidebar-active': visible,\n            'p-sidebar-left': (position === 'left' && !fullScreen), 'p-sidebar-right': (position === 'right' && !fullScreen),\n            'p-sidebar-top': (position === 'top' && !fullScreen), 'p-sidebar-bottom': (position === 'bottom' && !fullScreen),\n            'p-sidebar-full': fullScreen}\"  *ngIf=\"visible\" [@panelState]=\"{value: 'visible', params: {transform: transformOptions, transition: transitionOptions}}\" (@panelState.start)=\"onAnimationStart($event)\" (@panelState.done)=\"onAnimationEnd($event)\" [ngStyle]=\"style\" [class]=\"styleClass\"  role=\"complementary\" [attr.aria-modal]=\"modal\">\n            <div class=\"p-sidebar-header\">\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\"  *ngIf=\"showCloseIcon\" pRipple>\n                    <span class=\"p-sidebar-close-icon pi pi-times\"></span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto}.p-sidebar-header{display:flex;align-items:center;justify-content:flex-end}.p-sidebar-icon{display:flex;align-items:center;justify-content:center}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }], animations: [\n        trigger('panelState', [\n            transition('void => visible', [\n                useAnimation(showAnimation)\n            ]),\n            transition('visible => void', [\n                useAnimation(hideAnimation)\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Sidebar, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-sidebar', template: `\n        <div #container [ngClass]=\"{'p-sidebar':true, 'p-sidebar-active': visible,\n            'p-sidebar-left': (position === 'left' && !fullScreen), 'p-sidebar-right': (position === 'right' && !fullScreen),\n            'p-sidebar-top': (position === 'top' && !fullScreen), 'p-sidebar-bottom': (position === 'bottom' && !fullScreen),\n            'p-sidebar-full': fullScreen}\"  *ngIf=\"visible\" [@panelState]=\"{value: 'visible', params: {transform: transformOptions, transition: transitionOptions}}\" (@panelState.start)=\"onAnimationStart($event)\" (@panelState.done)=\"onAnimationEnd($event)\" [ngStyle]=\"style\" [class]=\"styleClass\"  role=\"complementary\" [attr.aria-modal]=\"modal\">\n            <div class=\"p-sidebar-header\">\n                <button type=\"button\" class=\"p-sidebar-close p-sidebar-icon p-link\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\" [attr.aria-label]=\"ariaCloseLabel\"  *ngIf=\"showCloseIcon\" pRipple>\n                    <span class=\"p-sidebar-close-icon pi pi-times\"></span>\n                </button>\n            </div>\n            <div class=\"p-sidebar-content\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('panelState', [\n                            transition('void => visible', [\n                                useAnimation(showAnimation)\n                            ]),\n                            transition('visible => void', [\n                                useAnimation(hideAnimation)\n                            ])\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-sidebar{position:fixed;transition:transform .3s;display:flex;flex-direction:column}.p-sidebar-content{position:relative;overflow-y:auto}.p-sidebar-header{display:flex;align-items:center;justify-content:flex-end}.p-sidebar-icon{display:flex;align-items:center;justify-content:center}.p-sidebar-left{top:0;left:0;width:20rem;height:100%}.p-sidebar-right{top:0;right:0;width:20rem;height:100%}.p-sidebar-top{top:0;left:0;width:100%;height:10rem}.p-sidebar-bottom{bottom:0;left:0;width:100%;height:10rem}.p-sidebar-full{width:100%;height:100%;top:0;left:0;transition:none}.p-sidebar-left.p-sidebar-sm,.p-sidebar-right.p-sidebar-sm{width:20rem}.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-md{width:40rem}.p-sidebar-left.p-sidebar-lg,.p-sidebar-right.p-sidebar-lg{width:60rem}.p-sidebar-top.p-sidebar-sm,.p-sidebar-bottom.p-sidebar-sm{height:10rem}.p-sidebar-top.p-sidebar-md,.p-sidebar-bottom.p-sidebar-md{height:20rem}.p-sidebar-top.p-sidebar-lg,.p-sidebar-bottom.p-sidebar-lg{height:30rem}@media screen and (max-width: 64em){.p-sidebar-left.p-sidebar-lg,.p-sidebar-left.p-sidebar-md,.p-sidebar-right.p-sidebar-lg,.p-sidebar-right.p-sidebar-md{width:20rem}}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }]; }, propDecorators: { appendTo: [{\n                type: Input\n            }], blockScroll: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], ariaCloseLabel: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], modal: [{\n                type: Input\n            }], dismissible: [{\n                type: Input\n            }], showCloseIcon: [{\n                type: Input\n            }], closeOnEscape: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], visibleChange: [{\n                type: Output\n            }], visible: [{\n                type: Input\n            }], position: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }] } });\nclass SidebarModule {\n}\nSidebarModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SidebarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nSidebarModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: SidebarModule, declarations: [Sidebar], imports: [CommonModule, RippleModule], exports: [Sidebar] });\nSidebarModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SidebarModule, imports: [CommonModule, RippleModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SidebarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RippleModule],\n                    exports: [Sidebar],\n                    declarations: [Sidebar]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Sidebar, SidebarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,eAArF,EAAsGC,MAAtG,EAA8GC,QAA9G,QAA8H,eAA9H;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,OAA3B,EAAoCC,OAApC,EAA6CC,UAA7C,EAAyDC,YAAzD,QAA6E,qBAA7E;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,SAASC,WAAT,QAA4B,eAA5B;;;;gBAsN0FtB,E;;IAAAA,EAO1E,+B;IAP0EA,EAON;MAPMA,EAON;MAAA,eAPMA,EAON;MAAA,OAPMA,EAOG,kCAAT;IAAA;MAPMA,EAON;MAAA,eAPMA,EAON;MAAA,OAPMA,EAOmC,kCAAzC;IAAA,E;IAPMA,EAQtE,wB;IARsEA,EAS1E,e;;;;mBAT0EA,E;IAAAA,EAOkD,iD;;;;;;IAPlDA,EAa1E,sB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAb0EA,E;;IAAAA,EAElF,+B;IAFkFA,EAK2E;MAL3EA,EAK2E;MAAA,eAL3EA,EAK2E;MAAA,OAL3EA,EAKgG,6CAArB;IAAA;MAL3EA,EAK2E;MAAA,eAL3EA,EAK2E;MAAA,OAL3EA,EAK8I,2CAAnE;IAAA,E;IAL3EA,EAM9E,4B;IAN8EA,EAO1E,kE;IAP0EA,EAU9E,e;IAV8EA,EAW9E,4B;IAX8EA,EAY1E,gB;IAZ0EA,EAa1E,8E;IAb0EA,EAc9E,iB;;;;mBAd8EA,E;IAAAA,EAKwL,8B;IALxLA,EAElE,uBAFkEA,EAElE,sRAFkEA,EAElE,0BAFkEA,EAElE,uG;IAFkEA,EAKmO,wC;IALnOA,EAOuF,a;IAPvFA,EAOuF,yC;IAPvFA,EAa3D,a;IAb2DA,EAa3D,uD;;;;;AAjO/B,MAAMuB,aAAa,GAAGd,SAAS,CAAC,CAC5BC,KAAK,CAAC;EAAEc,SAAS,EAAE,eAAb;EAA8BC,OAAO,EAAE;AAAvC,CAAD,CADuB,EAE5Bd,OAAO,CAAC,gBAAD,CAFqB,CAAD,CAA/B;AAIA,MAAMe,aAAa,GAAGjB,SAAS,CAAC,CAC5BE,OAAO,CAAC,gBAAD,EAAmBD,KAAK,CAAC;EAAEc,SAAS,EAAE,eAAb;EAA8BC,OAAO,EAAE;AAAvC,CAAD,CAAxB,CADqB,CAAD,CAA/B;;AAGA,MAAME,OAAN,CAAc;EACVC,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,MAAnB,EAA2B;IAClC,KAAKH,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,KAAL,GAAa,IAAb;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,iBAAL,GAAyB,kCAAzB;IACA,KAAKC,MAAL,GAAc,IAAIxC,YAAJ,EAAd;IACA,KAAKyC,MAAL,GAAc,IAAIzC,YAAJ,EAAd;IACA,KAAK0C,aAAL,GAAqB,IAAI1C,YAAJ,EAArB;IACA,KAAK2C,SAAL,GAAiB,MAAjB;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA,KAAKC,gBAAL,GAAwB,8BAAxB;EACH;;EACDC,eAAe,GAAG;IACd,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;;QACJ;UACI,KAAKD,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;MANR;IAQH,CATD;EAUH;;EACU,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKC,QAAZ;EACH;;EACU,IAAPD,OAAO,CAACE,GAAD,EAAM;IACb,KAAKD,QAAL,GAAgBC,GAAhB;EACH;;EACW,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKf,SAAZ;EACH;;EAEW,IAARe,QAAQ,CAACC,KAAD,EAAQ;IAChB,KAAKhB,SAAL,GAAiBgB,KAAjB;;IACA,QAAQA,KAAR;MACI,KAAK,MAAL;QACI,KAAKd,gBAAL,GAAwB,8BAAxB;QACA;;MACJ,KAAK,OAAL;QACI,KAAKA,gBAAL,GAAwB,6BAAxB;QACA;;MACJ,KAAK,QAAL;QACI,KAAKA,gBAAL,GAAwB,6BAAxB;QACA;;MACJ,KAAK,KAAL;QACI,KAAKA,gBAAL,GAAwB,8BAAxB;QACA;IAZR;EAcH;;EACa,IAAVe,UAAU,GAAG;IACb,OAAO,KAAKhB,WAAZ;EACH;;EACa,IAAVgB,UAAU,CAACD,KAAD,EAAQ;IAClB,KAAKf,WAAL,GAAmBe,KAAnB;IACA,IAAIA,KAAJ,EACI,KAAKd,gBAAL,GAAwB,MAAxB;EACP;;EACDgB,IAAI,GAAG;IACH,IAAI,KAAK5B,UAAT,EAAqB;MACjBZ,WAAW,CAACyC,GAAZ,CAAgB,OAAhB,EAAyB,KAAKC,SAA9B,EAAyC,KAAK7B,UAAL,IAAmB,KAAKH,MAAL,CAAYiC,MAAZ,CAAmB7B,KAA/E;IACH;;IACD,IAAI,KAAKA,KAAT,EAAgB;MACZ,KAAK8B,cAAL;IACH;;IACD,KAAKzB,MAAL,CAAY0B,IAAZ,CAAiB,EAAjB;IACA,KAAKxB,aAAL,CAAmBwB,IAAnB,CAAwB,IAAxB;EACH;;EACDC,IAAI,GAAG;IACH,KAAK1B,MAAL,CAAYyB,IAAZ,CAAiB,EAAjB;;IACA,IAAI,KAAK/B,KAAT,EAAgB;MACZ,KAAKiC,eAAL;IACH;EACJ;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKH,IAAL;IACA,KAAKzB,aAAL,CAAmBwB,IAAnB,CAAwB,KAAxB;IACAI,KAAK,CAACC,cAAN;EACH;;EACDN,cAAc,GAAG;IACb,IAAI,CAAC,KAAKO,IAAV,EAAgB;MACZ,KAAKA,IAAL,GAAYC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAZ;MACA,KAAKF,IAAL,CAAU/D,KAAV,CAAgBuD,MAAhB,GAAyBW,MAAM,CAACC,QAAQ,CAAC,KAAKb,SAAL,CAAetD,KAAf,CAAqBuD,MAAtB,CAAR,GAAwC,CAAzC,CAA/B;MACA9C,UAAU,CAAC2D,kBAAX,CAA8B,KAAKL,IAAnC,EAAyC,kFAAzC;;MACA,IAAI,KAAKpC,WAAT,EAAsB;QAClB,KAAK0C,iBAAL,GAAyB,KAAKjD,QAAL,CAAckD,MAAd,CAAqB,KAAKP,IAA1B,EAAgC,OAAhC,EAA0CF,KAAD,IAAW;UACzE,IAAI,KAAKlC,WAAT,EAAsB;YAClB,KAAKiC,KAAL,CAAWC,KAAX;UACH;QACJ,CAJwB,CAAzB;MAKH;;MACDG,QAAQ,CAACO,IAAT,CAAcC,WAAd,CAA0B,KAAKT,IAA/B;;MACA,IAAI,KAAKxC,WAAT,EAAsB;QAClBd,UAAU,CAACgE,QAAX,CAAoBT,QAAQ,CAACO,IAA7B,EAAmC,mBAAnC;MACH;IACJ;EACJ;;EACDZ,eAAe,GAAG;IACd,IAAI,KAAKI,IAAT,EAAe;MACXtD,UAAU,CAACgE,QAAX,CAAoB,KAAKV,IAAzB,EAA+B,2BAA/B;MACA,KAAKW,oBAAL,GAA4B,KAAKC,YAAL,CAAkBC,IAAlB,CAAuB,IAAvB,CAA5B;MACA,KAAKb,IAAL,CAAUc,gBAAV,CAA2B,cAA3B,EAA2C,KAAKH,oBAAhD;IACH;EACJ;;EACDC,YAAY,GAAG;IACX,KAAKG,uBAAL;;IACA,IAAI,KAAKf,IAAT,EAAe;MACXC,QAAQ,CAACO,IAAT,CAAcQ,WAAd,CAA0B,KAAKhB,IAA/B;IACH;;IACD,IAAI,KAAKxC,WAAT,EAAsB;MAClBd,UAAU,CAACuE,WAAX,CAAuBhB,QAAQ,CAACO,IAAhC,EAAsC,mBAAtC;IACH;;IACD,KAAKU,0BAAL;IACA,KAAKlB,IAAL,GAAY,IAAZ;EACH;;EACDmB,gBAAgB,CAACrB,KAAD,EAAQ;IACpB,QAAQA,KAAK,CAACsB,OAAd;MACI,KAAK,SAAL;QACI,KAAK7B,SAAL,GAAiBO,KAAK,CAACuB,OAAvB;QACA,KAAKC,eAAL;QACA,KAAKjC,IAAL;;QACA,IAAI,KAAKvB,aAAT,EAAwB;UACpB,KAAKyD,0BAAL;QACH;;QACD;IARR;EAUH;;EACDC,cAAc,CAAC1B,KAAD,EAAQ;IAClB,QAAQA,KAAK,CAACsB,OAAd;MACI,KAAK,MAAL;QACI,KAAKzB,IAAL;QACA9C,WAAW,CAAC4E,KAAZ,CAAkB,KAAKlC,SAAvB;QACA,KAAKmC,qBAAL;QACA;IALR;EAOH;;EACDJ,eAAe,GAAG;IACd,IAAI,KAAKK,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACI1B,QAAQ,CAACO,IAAT,CAAcC,WAAd,CAA0B,KAAKlB,SAA/B,EADJ,KAGI7C,UAAU,CAAC+D,WAAX,CAAuB,KAAKlB,SAA5B,EAAuC,KAAKoC,QAA5C;IACP;EACJ;;EACDJ,0BAA0B,GAAG;IACzB,MAAMK,cAAc,GAAG,KAAKxE,EAAL,GAAU,KAAKA,EAAL,CAAQyE,aAAR,CAAsBC,aAAhC,GAAgD,UAAvE;IACA,KAAKC,sBAAL,GAA8B,KAAK1E,QAAL,CAAckD,MAAd,CAAqBqB,cAArB,EAAqC,SAArC,EAAiD9B,KAAD,IAAW;MACrF,IAAIA,KAAK,CAACkC,KAAN,IAAe,EAAnB,EAAuB;QACnB,IAAI5B,QAAQ,CAAC,KAAKb,SAAL,CAAetD,KAAf,CAAqBuD,MAAtB,CAAR,KAA0C3C,WAAW,CAACoF,GAAZ,CAAgB,KAAK1C,SAArB,CAA9C,EAA+E;UAC3E,KAAKM,KAAL,CAAWC,KAAX;QACH;MACJ;IACJ,CAN6B,CAA9B;EAOH;;EACDoC,4BAA4B,GAAG;IAC3B,IAAI,KAAKH,sBAAT,EAAiC;MAC7B,KAAKA,sBAAL;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDhB,uBAAuB,GAAG;IACtB,IAAI,KAAKT,iBAAT,EAA4B;MACxB,KAAKA,iBAAL;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;EACJ;;EACDoB,qBAAqB,GAAG;IACpB,KAAKX,uBAAL;IACA,KAAKmB,4BAAL;EACH;;EACDhB,0BAA0B,GAAG;IACzB,IAAI,KAAKP,oBAAL,IAA6B,KAAKX,IAAtC,EAA4C;MACxC,KAAKA,IAAL,CAAUmC,mBAAV,CAA8B,cAA9B,EAA8C,KAAKxB,oBAAnD;MACA,KAAKA,oBAAL,GAA4B,IAA5B;IACH;EACJ;;EACDyB,WAAW,GAAG;IACV,KAAK7D,WAAL,GAAmB,KAAnB;;IACA,IAAI,KAAKQ,OAAL,IAAgB,KAAKpB,KAAzB,EAAgC;MAC5B,KAAKiD,YAAL;IACH;;IACD,IAAI,KAAKe,QAAL,IAAiB,KAAKpC,SAA1B,EAAqC;MACjC,KAAKnC,EAAL,CAAQyE,aAAR,CAAsBpB,WAAtB,CAAkC,KAAKlB,SAAvC;IACH;;IACD,IAAI,KAAKA,SAAL,IAAkB,KAAK9B,UAA3B,EAAuC;MACnCZ,WAAW,CAAC4E,KAAZ,CAAkB,KAAKlC,SAAvB;IACH;;IACD,KAAKA,SAAL,GAAiB,IAAjB;IACA,KAAKmC,qBAAL;IACA,KAAKR,0BAAL;EACH;;AA3MS;;AA6MdhE,OAAO,CAACmF,IAAR;EAAA,iBAAoGnF,OAApG,EAA0F3B,EAA1F,mBAA6HA,EAAE,CAAC+G,UAAhI,GAA0F/G,EAA1F,mBAAuJA,EAAE,CAACgH,SAA1J,GAA0FhH,EAA1F,mBAAgLA,EAAE,CAACiH,iBAAnL,GAA0FjH,EAA1F,mBAAiNoB,EAAE,CAAC8F,aAApN;AAAA;;AACAvF,OAAO,CAACwF,IAAR,kBAD0FnH,EAC1F;EAAA,MAAwF2B,OAAxF;EAAA;EAAA;IAAA;MAD0F3B,EAC1F,0BAAwrBqB,aAAxrB;IAAA;;IAAA;MAAA;;MAD0FrB,EAC1F,qBAD0FA,EAC1F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD0FA,EAC1F;MAD0FA,EAElF,uDADR;IAAA;;IAAA;MAD0FA,EAK7C,gCAJ7C;IAAA;EAAA;EAAA,eAeguCe,EAAE,CAACqG,OAfnuC,EAe8zCrG,EAAE,CAACsG,IAfj0C,EAek6CtG,EAAE,CAACuG,gBAfr6C,EAeykDvG,EAAE,CAACwG,OAf5kD,EAe8pDtG,EAAE,CAACuG,MAfjqD;EAAA;EAAA;EAAA;IAAA,WAe+sD,CACvsD5G,OAAO,CAAC,YAAD,EAAe,CAClBC,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,YAAY,CAACS,aAAD,CADc,CAApB,CADQ,EAIlBV,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,YAAY,CAACY,aAAD,CADc,CAApB,CAJQ,CAAf,CADgsD;EAf/sD;EAAA;AAAA;;AAyBA;EAAA,mDA1B0F1B,EA0B1F,mBAA2F2B,OAA3F,EAAgH,CAAC;IACrG8F,IAAI,EAAEvH,SAD+F;IAErGwH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAZ;MAAyBpE,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAfmB;MAeZqE,UAAU,EAAE,CACKhH,OAAO,CAAC,YAAD,EAAe,CAClBC,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,YAAY,CAACS,aAAD,CADc,CAApB,CADQ,EAIlBV,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,YAAY,CAACY,aAAD,CADc,CAApB,CAJQ,CAAf,CADZ,CAfA;MAwBImG,eAAe,EAAE1H,uBAAuB,CAAC2H,MAxB7C;MAwBqDC,aAAa,EAAE3H,iBAAiB,CAAC4H,IAxBtF;MAwB4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CAxBlG;MA0BIC,MAAM,EAAE,CAAC,mpCAAD;IA1BZ,CAAD;EAF+F,CAAD,CAAhH,EA6B4B,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEzH,EAAE,CAAC+G;IAAX,CAAD,EAA0B;MAAEU,IAAI,EAAEzH,EAAE,CAACgH;IAAX,CAA1B,EAAkD;MAAES,IAAI,EAAEzH,EAAE,CAACiH;IAAX,CAAlD,EAAkF;MAAEQ,IAAI,EAAErG,EAAE,CAAC8F;IAAX,CAAlF,CAAP;EAAuH,CA7BjK,EA6BmL;IAAEd,QAAQ,EAAE,CAAC;MAChLqB,IAAI,EAAEpH;IAD0K,CAAD,CAAZ;IAEnK4B,WAAW,EAAE,CAAC;MACdwF,IAAI,EAAEpH;IADQ,CAAD,CAFsJ;IAInKK,KAAK,EAAE,CAAC;MACR+G,IAAI,EAAEpH;IADE,CAAD,CAJ4J;IAMnK8H,UAAU,EAAE,CAAC;MACbV,IAAI,EAAEpH;IADO,CAAD,CANuJ;IAQnK+H,cAAc,EAAE,CAAC;MACjBX,IAAI,EAAEpH;IADW,CAAD,CARmJ;IAUnK6B,UAAU,EAAE,CAAC;MACbuF,IAAI,EAAEpH;IADO,CAAD,CAVuJ;IAYnK8B,UAAU,EAAE,CAAC;MACbsF,IAAI,EAAEpH;IADO,CAAD,CAZuJ;IAcnK+B,KAAK,EAAE,CAAC;MACRqF,IAAI,EAAEpH;IADE,CAAD,CAd4J;IAgBnKgC,WAAW,EAAE,CAAC;MACdoF,IAAI,EAAEpH;IADQ,CAAD,CAhBsJ;IAkBnKiC,aAAa,EAAE,CAAC;MAChBmF,IAAI,EAAEpH;IADU,CAAD,CAlBoJ;IAoBnKkC,aAAa,EAAE,CAAC;MAChBkF,IAAI,EAAEpH;IADU,CAAD,CApBoJ;IAsBnKmC,iBAAiB,EAAE,CAAC;MACpBiF,IAAI,EAAEpH;IADc,CAAD,CAtBgJ;IAwBnK6C,SAAS,EAAE,CAAC;MACZuE,IAAI,EAAEnH,eADM;MAEZoH,IAAI,EAAE,CAACrG,aAAD;IAFM,CAAD,CAxBwJ;IA2BnKoB,MAAM,EAAE,CAAC;MACTgF,IAAI,EAAElH;IADG,CAAD,CA3B2J;IA6BnKmC,MAAM,EAAE,CAAC;MACT+E,IAAI,EAAElH;IADG,CAAD,CA7B2J;IA+BnKoC,aAAa,EAAE,CAAC;MAChB8E,IAAI,EAAElH;IADU,CAAD,CA/BoJ;IAiCnKiD,OAAO,EAAE,CAAC;MACViE,IAAI,EAAEpH;IADI,CAAD,CAjC0J;IAmCnKsD,QAAQ,EAAE,CAAC;MACX8D,IAAI,EAAEpH;IADK,CAAD,CAnCyJ;IAqCnKwD,UAAU,EAAE,CAAC;MACb4D,IAAI,EAAEpH;IADO,CAAD;EArCuJ,CA7BnL;AAAA;;AAqEA,MAAMgI,aAAN,CAAoB;;AAEpBA,aAAa,CAACvB,IAAd;EAAA,iBAA0GuB,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAlG0FtI,EAkG1F;EAAA,MAA2GqI;AAA3G;AACAA,aAAa,CAACE,IAAd,kBAnG0FvI,EAmG1F;EAAA,UAAoIgB,YAApI,EAAkJE,YAAlJ;AAAA;;AACA;EAAA,mDApG0FlB,EAoG1F,mBAA2FqI,aAA3F,EAAsH,CAAC;IAC3GZ,IAAI,EAAEjH,QADqG;IAE3GkH,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAACxH,YAAD,EAAeE,YAAf,CADV;MAECuH,OAAO,EAAE,CAAC9G,OAAD,CAFV;MAGC+G,YAAY,EAAE,CAAC/G,OAAD;IAHf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,OAAT,EAAkB0G,aAAlB"}, "metadata": {}, "sourceType": "module"}