{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = asyncScheduler) {\n  return audit(() => timer(duration, scheduler));\n}", "map": {"version": 3, "names": ["asyncScheduler", "audit", "timer", "auditTime", "duration", "scheduler"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/auditTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { audit } from './audit';\nimport { timer } from '../observable/timer';\nexport function auditTime(duration, scheduler = asyncScheduler) {\n    return audit(() => timer(duration, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,OAAO,SAASC,SAAT,CAAmBC,QAAnB,EAA6BC,SAAS,GAAGL,cAAzC,EAAyD;EAC5D,OAAOC,KAAK,CAAC,MAAMC,KAAK,CAACE,QAAD,EAAWC,SAAX,CAAZ,CAAZ;AACH"}, "metadata": {}, "sourceType": "module"}