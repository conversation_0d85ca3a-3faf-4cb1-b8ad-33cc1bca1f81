{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class KeycloakAuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:8080/api';\n    this.accessTokenKey = 'keycloak_access_token';\n    this.refreshTokenKey = 'keycloak_refresh_token';\n    this.userKey = 'keycloak_user';\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(this.hasValidToken());\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.keycloakConfig = null;\n    /**\r\n     * Handle HTTP errors\r\n     */\n\n    this.handleError = error => {\n      console.error('Keycloak Auth Service Error:', error);\n\n      if (error.status === 401) {\n        this.clearSession();\n      }\n\n      return throwError(error);\n    }; // Initialize will be called by AppInitService\n\n  }\n  /**\r\n   * Initialize authentication service\r\n   */\n\n\n  initializeAuth() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this.loadKeycloakConfig();\n\n      _this.checkExistingAuth();\n    })();\n  }\n  /**\r\n   * Load Keycloak configuration from backend\r\n   */\n\n\n  loadKeycloakConfig() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // Use fetch to avoid circular dependency with HttpClient interceptor\n        const response = yield fetch(`${_this2.apiUrl}/auth/config`);\n        const data = yield response.json();\n\n        if (data.success) {\n          _this2.keycloakConfig = data.data;\n        }\n      } catch (error) {\n        console.error('Failed to load Keycloak config:', error);\n      }\n    })();\n  }\n  /**\r\n   * Check if user is already authenticated on app start\r\n   */\n\n\n  checkExistingAuth() {\n    const token = this.getAccessToken();\n    const userStr = localStorage.getItem('keycloak_user');\n\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr);\n        this.currentUserSubject.next(user);\n      } catch (error) {\n        console.error('Failed to parse stored user:', error);\n        this.clearStoredTokens();\n      }\n    }\n  }\n  /**\r\n   * Clear stored tokens and user data\r\n   */\n\n\n  clearStoredTokens() {\n    localStorage.removeItem(this.accessTokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n    localStorage.removeItem('keycloak_user');\n    this.currentUserSubject.next(null);\n  }\n  /**\r\n   * Login with username and password\r\n   */\n\n\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      if (response.success) {\n        this.setSession(response.data);\n      }\n    }), catchError(this.handleError));\n  }\n  /**\r\n   * Refresh access token\r\n   */\n\n\n  refreshToken() {\n    const refreshToken = this.getRefreshToken();\n\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n\n    return this.http.post(`${this.apiUrl}/auth/refresh`, {\n      refresh_token: refreshToken\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.setTokens(response.data.access_token, response.data.refresh_token);\n      }\n    }), catchError(error => {\n      this.clearSession();\n      return throwError(error);\n    }));\n  }\n  /**\r\n   * Logout user\r\n   */\n\n\n  logout() {\n    const refreshToken = this.getRefreshToken();\n    const logoutRequest = refreshToken ? this.http.post(`${this.apiUrl}/auth/logout`, {\n      refresh_token: refreshToken\n    }) : new Observable(observer => observer.next({}));\n    return logoutRequest.pipe(tap(() => this.clearSession()), catchError(() => {\n      this.clearSession();\n      return throwError('Logout failed');\n    }));\n  }\n  /**\r\n   * Get current user info from server\r\n   */\n\n\n  getCurrentUser() {\n    return this.http.get(`${this.apiUrl}/auth/me`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.updateUser(response.data.user);\n      }\n    }), catchError(this.handleError));\n  }\n  /**\r\n   * Validate current token\r\n   */\n\n\n  validateToken() {\n    return this.http.post(`${this.apiUrl}/auth/validate`, {}, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.updateUser(response.data.user);\n      }\n    }), catchError(error => {\n      this.clearSession();\n      return throwError(error);\n    }));\n  }\n  /**\r\n   * Check if user is authenticated\r\n   */\n\n\n  isAuthenticated() {\n    return this.hasValidToken();\n  }\n  /**\r\n   * Get current user\r\n   */\n\n\n  getCurrentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  /**\r\n   * Check if user has specific role\r\n   */\n\n\n  hasRole(role) {\n    const user = this.getCurrentUserValue();\n    return user ? user.roles.includes(role) : false;\n  }\n  /**\r\n   * Check if user has any of the specified roles\r\n   */\n\n\n  hasAnyRole(roles) {\n    const user = this.getCurrentUserValue();\n    return user ? roles.some(role => user.roles.includes(role)) : false;\n  }\n  /**\r\n   * Check if user is admin\r\n   */\n\n\n  isAdmin() {\n    return this.hasRole('admin');\n  }\n  /**\r\n   * Check if user is trainer\r\n   */\n\n\n  isTrainer() {\n    return this.hasRole('trainer');\n  }\n  /**\r\n   * Check if user is employee\r\n   */\n\n\n  isEmployee() {\n    return this.hasRole('employee');\n  }\n  /**\r\n   * Get access token\r\n   */\n\n\n  getAccessToken() {\n    return localStorage.getItem(this.accessTokenKey);\n  }\n  /**\r\n   * Get refresh token\r\n   */\n\n\n  getRefreshToken() {\n    return localStorage.getItem(this.refreshTokenKey);\n  }\n  /**\r\n   * Get Keycloak configuration\r\n   */\n\n\n  getKeycloakConfig() {\n    return this.keycloakConfig;\n  }\n  /**\r\n   * Set session data\r\n   */\n\n\n  setSession(authData) {\n    localStorage.setItem(this.accessTokenKey, authData.access_token);\n    localStorage.setItem(this.refreshTokenKey, authData.refresh_token);\n    localStorage.setItem(this.userKey, JSON.stringify(authData.user));\n    this.currentUserSubject.next(authData.user);\n    this.isAuthenticatedSubject.next(true);\n  }\n  /**\r\n   * Set tokens only\r\n   */\n\n\n  setTokens(accessToken, refreshToken) {\n    localStorage.setItem(this.accessTokenKey, accessToken);\n    localStorage.setItem(this.refreshTokenKey, refreshToken);\n  }\n  /**\r\n   * Update user data\r\n   */\n\n\n  updateUser(user) {\n    localStorage.setItem(this.userKey, JSON.stringify(user));\n    this.currentUserSubject.next(user);\n  }\n  /**\r\n   * Clear session\r\n   */\n\n\n  clearSession() {\n    localStorage.removeItem(this.accessTokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n    localStorage.removeItem(this.userKey);\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n  /**\r\n   * Get user from storage\r\n   */\n\n\n  getUserFromStorage() {\n    const userStr = localStorage.getItem(this.userKey);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  /**\r\n   * Check if token exists\r\n   */\n\n\n  hasValidToken() {\n    const token = this.getAccessToken();\n    return !!token;\n  }\n  /**\r\n   * Get authorization headers\r\n   */\n\n\n  getAuthHeaders() {\n    const token = this.getAccessToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  /**\r\n   * Redirect user based on role after login\r\n   */\n\n\n  redirectAfterLogin() {\n    const user = this.getCurrentUserValue();\n    if (!user) return; // Check if there's an attempted URL to redirect to\n\n    const attemptedUrl = localStorage.getItem('attempted_url');\n\n    if (attemptedUrl) {\n      localStorage.removeItem('attempted_url');\n      this.router.navigateByUrl(attemptedUrl);\n      return;\n    } // Default role-based redirection\n\n\n    if (user.roles.includes('admin')) {\n      this.router.navigate(['/']);\n    } else if (user.roles.includes('trainer')) {\n      this.router.navigate(['/trainer/dashboard']);\n    } else if (user.roles.includes('employee')) {\n      this.router.navigate(['/employee/dashboard']);\n    } else {\n      this.router.navigate(['/']);\n    }\n  }\n\n}\n\nKeycloakAuthService.ɵfac = function KeycloakAuthService_Factory(t) {\n  return new (t || KeycloakAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n};\n\nKeycloakAuthService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: KeycloakAuthService,\n  factory: KeycloakAuthService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAAqBA,WAArB,QAAwC,sBAAxC;AACA,SAASC,eAAT,EAA0BC,UAA1B,EAAsCC,UAAtC,QAAwD,MAAxD;AACA,SAAcC,UAAd,EAA0BC,GAA1B,QAAqC,gBAArC;;;;AA8CA,OAAM,MAAOC,mBAAP,CAA0B;EAc5BC,YACYC,IADZ,EAEYC,MAFZ,EAE0B;IADd;IACA;IAfJ,cAAS,2BAAT;IACA,sBAAiB,uBAAjB;IACA,uBAAkB,wBAAlB;IACA,eAAU,eAAV;IAEA,0BAAqB,IAAIR,eAAJ,CAAyC,KAAKS,kBAAL,EAAzC,CAArB;IACD,oBAAe,KAAKC,kBAAL,CAAwBC,YAAxB,EAAf;IAEC,8BAAyB,IAAIX,eAAJ,CAA6B,KAAKY,aAAL,EAA7B,CAAzB;IACD,wBAAmB,KAAKC,sBAAL,CAA4BF,YAA5B,EAAnB;IAEC,sBAAwC,IAAxC;IAqSR;;;;IAGQ,mBAAeG,KAAD,IAAe;MACjCC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;;MAEA,IAAIA,KAAK,CAACE,MAAN,KAAiB,GAArB,EAA0B;QACtB,KAAKC,YAAL;MACH;;MAED,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CARO,CApSkB,CAEtB;;EACH;EAED;;;;;EAGMI,cAAc;IAAA;;IAAA;MAChB,MAAM,KAAI,CAACC,kBAAL,EAAN;;MACA,KAAI,CAACC,iBAAL;IAFgB;EAGnB;EAED;;;;;EAGcD,kBAAkB;IAAA;;IAAA;MAC5B,IAAI;QACA;QACA,MAAME,QAAQ,SAASC,KAAK,CAAC,GAAG,MAAI,CAACC,MAAM,cAAf,CAA5B;QACA,MAAMC,IAAI,SAASH,QAAQ,CAACI,IAAT,EAAnB;;QACA,IAAID,IAAI,CAACE,OAAT,EAAkB;UACd,MAAI,CAACC,cAAL,GAAsBH,IAAI,CAACA,IAA3B;QACH;MACJ,CAPD,CAOE,OAAOV,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;MACH;IAV2B;EAW/B;EAED;;;;;EAGQM,iBAAiB;IACrB,MAAMQ,KAAK,GAAG,KAAKC,cAAL,EAAd;IACA,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAb,CAAqB,eAArB,CAAhB;;IAEA,IAAIJ,KAAK,IAAIE,OAAb,EAAsB;MAClB,IAAI;QACA,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAL,CAAWL,OAAX,CAAb;QACA,KAAKpB,kBAAL,CAAwB0B,IAAxB,CAA6BH,IAA7B;MACH,CAHD,CAGE,OAAOnB,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;QACA,KAAKuB,iBAAL;MACH;IACJ;EACJ;EAED;;;;;EAGQA,iBAAiB;IACrBN,YAAY,CAACO,UAAb,CAAwB,KAAKC,cAA7B;IACAR,YAAY,CAACO,UAAb,CAAwB,KAAKE,eAA7B;IACAT,YAAY,CAACO,UAAb,CAAwB,eAAxB;IACA,KAAK5B,kBAAL,CAAwB0B,IAAxB,CAA6B,IAA7B;EACH;EAED;;;;;EAGAK,KAAK,CAACC,WAAD,EAA0B;IAC3B,OAAO,KAAKnC,IAAL,CAAUoC,IAAV,CAA8B,GAAG,KAAKpB,MAAM,aAA5C,EAA2DmB,WAA3D,EACFE,IADE,CAECxC,GAAG,CAACiB,QAAQ,IAAG;MACX,IAAIA,QAAQ,CAACK,OAAb,EAAsB;QAClB,KAAKmB,UAAL,CAAgBxB,QAAQ,CAACG,IAAzB;MACH;IACJ,CAJE,CAFJ,EAOCrB,UAAU,CAAC,KAAK2C,WAAN,CAPX,CAAP;EASH;EAED;;;;;EAGAC,YAAY;IACR,MAAMA,YAAY,GAAG,KAAKC,eAAL,EAArB;;IAEA,IAAI,CAACD,YAAL,EAAmB;MACf,OAAO7C,UAAU,CAAC,4BAAD,CAAjB;IACH;;IAED,OAAO,KAAKK,IAAL,CAAUoC,IAAV,CAAe,GAAG,KAAKpB,MAAM,eAA7B,EAA8C;MACjD0B,aAAa,EAAEF;IADkC,CAA9C,EAEJH,IAFI,CAGHxC,GAAG,CAAEiB,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACK,OAAb,EAAsB;QAClB,KAAKwB,SAAL,CAAe7B,QAAQ,CAACG,IAAT,CAAc2B,YAA7B,EAA2C9B,QAAQ,CAACG,IAAT,CAAcyB,aAAzD;MACH;IACJ,CAJE,CAHA,EAQH9C,UAAU,CAACW,KAAK,IAAG;MACf,KAAKG,YAAL;MACA,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CAHS,CARP,CAAP;EAaH;EAED;;;;;EAGAsC,MAAM;IACF,MAAML,YAAY,GAAG,KAAKC,eAAL,EAArB;IAEA,MAAMK,aAAa,GAAGN,YAAY,GAC9B,KAAKxC,IAAL,CAAUoC,IAAV,CAAe,GAAG,KAAKpB,MAAM,cAA7B,EAA6C;MAAE0B,aAAa,EAAEF;IAAjB,CAA7C,CAD8B,GAE9B,IAAI9C,UAAJ,CAAeqD,QAAQ,IAAIA,QAAQ,CAAClB,IAAT,CAAc,EAAd,CAA3B,CAFJ;IAIA,OAAOiB,aAAa,CAACT,IAAd,CACHxC,GAAG,CAAC,MAAM,KAAKa,YAAL,EAAP,CADA,EAEHd,UAAU,CAAC,MAAK;MACZ,KAAKc,YAAL;MACA,OAAOf,UAAU,CAAC,eAAD,CAAjB;IACH,CAHS,CAFP,CAAP;EAOH;EAED;;;;;EAGAqD,cAAc;IACV,OAAO,KAAKhD,IAAL,CAAUiD,GAAV,CAAc,GAAG,KAAKjC,MAAM,UAA5B,EAAwC;MAC3CkC,OAAO,EAAE,KAAKC,cAAL;IADkC,CAAxC,EAEJd,IAFI,CAGHxC,GAAG,CAAEiB,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACK,OAAb,EAAsB;QAClB,KAAKiC,UAAL,CAAgBtC,QAAQ,CAACG,IAAT,CAAcS,IAA9B;MACH;IACJ,CAJE,CAHA,EAQH9B,UAAU,CAAC,KAAK2C,WAAN,CARP,CAAP;EAUH;EAED;;;;;EAGAc,aAAa;IACT,OAAO,KAAKrD,IAAL,CAAUoC,IAAV,CAAe,GAAG,KAAKpB,MAAM,gBAA7B,EAA+C,EAA/C,EAAmD;MACtDkC,OAAO,EAAE,KAAKC,cAAL;IAD6C,CAAnD,EAEJd,IAFI,CAGHxC,GAAG,CAAEiB,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACK,OAAb,EAAsB;QAClB,KAAKiC,UAAL,CAAgBtC,QAAQ,CAACG,IAAT,CAAcS,IAA9B;MACH;IACJ,CAJE,CAHA,EAQH9B,UAAU,CAACW,KAAK,IAAG;MACf,KAAKG,YAAL;MACA,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CAHS,CARP,CAAP;EAaH;EAED;;;;;EAGA+C,eAAe;IACX,OAAO,KAAKjD,aAAL,EAAP;EACH;EAED;;;;;EAGAkD,mBAAmB;IACf,OAAO,KAAKpD,kBAAL,CAAwBqD,KAA/B;EACH;EAED;;;;;EAGAC,OAAO,CAACC,IAAD,EAAa;IAChB,MAAMhC,IAAI,GAAG,KAAK6B,mBAAL,EAAb;IACA,OAAO7B,IAAI,GAAGA,IAAI,CAACiC,KAAL,CAAWC,QAAX,CAAoBF,IAApB,CAAH,GAA+B,KAA1C;EACH;EAED;;;;;EAGAG,UAAU,CAACF,KAAD,EAAgB;IACtB,MAAMjC,IAAI,GAAG,KAAK6B,mBAAL,EAAb;IACA,OAAO7B,IAAI,GAAGiC,KAAK,CAACG,IAAN,CAAWJ,IAAI,IAAIhC,IAAI,CAACiC,KAAL,CAAWC,QAAX,CAAoBF,IAApB,CAAnB,CAAH,GAAmD,KAA9D;EACH;EAED;;;;;EAGAK,OAAO;IACH,OAAO,KAAKN,OAAL,CAAa,OAAb,CAAP;EACH;EAED;;;;;EAGAO,SAAS;IACL,OAAO,KAAKP,OAAL,CAAa,SAAb,CAAP;EACH;EAED;;;;;EAGAQ,UAAU;IACN,OAAO,KAAKR,OAAL,CAAa,UAAb,CAAP;EACH;EAED;;;;;EAGAnC,cAAc;IACV,OAAOE,YAAY,CAACC,OAAb,CAAqB,KAAKO,cAA1B,CAAP;EACH;EAED;;;;;EAGAS,eAAe;IACX,OAAOjB,YAAY,CAACC,OAAb,CAAqB,KAAKQ,eAA1B,CAAP;EACH;EAED;;;;;EAGAiC,iBAAiB;IACb,OAAO,KAAK9C,cAAZ;EACH;EAED;;;;;EAGQkB,UAAU,CAAC6B,QAAD,EAAc;IAC5B3C,YAAY,CAAC4C,OAAb,CAAqB,KAAKpC,cAA1B,EAA0CmC,QAAQ,CAACvB,YAAnD;IACApB,YAAY,CAAC4C,OAAb,CAAqB,KAAKnC,eAA1B,EAA2CkC,QAAQ,CAACzB,aAApD;IACAlB,YAAY,CAAC4C,OAAb,CAAqB,KAAKC,OAA1B,EAAmC1C,IAAI,CAAC2C,SAAL,CAAeH,QAAQ,CAACzC,IAAxB,CAAnC;IACA,KAAKvB,kBAAL,CAAwB0B,IAAxB,CAA6BsC,QAAQ,CAACzC,IAAtC;IACA,KAAKpB,sBAAL,CAA4BuB,IAA5B,CAAiC,IAAjC;EACH;EAED;;;;;EAGQc,SAAS,CAAC4B,WAAD,EAAsB/B,YAAtB,EAA0C;IACvDhB,YAAY,CAAC4C,OAAb,CAAqB,KAAKpC,cAA1B,EAA0CuC,WAA1C;IACA/C,YAAY,CAAC4C,OAAb,CAAqB,KAAKnC,eAA1B,EAA2CO,YAA3C;EACH;EAED;;;;;EAGQY,UAAU,CAAC1B,IAAD,EAAmB;IACjCF,YAAY,CAAC4C,OAAb,CAAqB,KAAKC,OAA1B,EAAmC1C,IAAI,CAAC2C,SAAL,CAAe5C,IAAf,CAAnC;IACA,KAAKvB,kBAAL,CAAwB0B,IAAxB,CAA6BH,IAA7B;EACH;EAED;;;;;EAGQhB,YAAY;IAChBc,YAAY,CAACO,UAAb,CAAwB,KAAKC,cAA7B;IACAR,YAAY,CAACO,UAAb,CAAwB,KAAKE,eAA7B;IACAT,YAAY,CAACO,UAAb,CAAwB,KAAKsC,OAA7B;IACA,KAAKlE,kBAAL,CAAwB0B,IAAxB,CAA6B,IAA7B;IACA,KAAKvB,sBAAL,CAA4BuB,IAA5B,CAAiC,KAAjC;IACA,KAAK5B,MAAL,CAAYuE,QAAZ,CAAqB,CAAC,aAAD,CAArB;EACH;EAED;;;;;EAGQtE,kBAAkB;IACtB,MAAMqB,OAAO,GAAGC,YAAY,CAACC,OAAb,CAAqB,KAAK4C,OAA1B,CAAhB;IACA,OAAO9C,OAAO,GAAGI,IAAI,CAACC,KAAL,CAAWL,OAAX,CAAH,GAAyB,IAAvC;EACH;EAED;;;;;EAGQlB,aAAa;IACjB,MAAMgB,KAAK,GAAG,KAAKC,cAAL,EAAd;IACA,OAAO,CAAC,CAACD,KAAT;EACH;EAED;;;;;EAGQ8B,cAAc;IAClB,MAAM9B,KAAK,GAAG,KAAKC,cAAL,EAAd;IACA,OAAO,IAAI9B,WAAJ,CAAgB;MACnB,iBAAiB,UAAU6B,KAAK,EADb;MAEnB,gBAAgB;IAFG,CAAhB,CAAP;EAIH;EAeD;;;;;EAGAoD,kBAAkB;IACd,MAAM/C,IAAI,GAAG,KAAK6B,mBAAL,EAAb;IACA,IAAI,CAAC7B,IAAL,EAAW,OAFG,CAId;;IACA,MAAMgD,YAAY,GAAGlD,YAAY,CAACC,OAAb,CAAqB,eAArB,CAArB;;IACA,IAAIiD,YAAJ,EAAkB;MACdlD,YAAY,CAACO,UAAb,CAAwB,eAAxB;MACA,KAAK9B,MAAL,CAAY0E,aAAZ,CAA0BD,YAA1B;MACA;IACH,CAVa,CAYd;;;IACA,IAAIhD,IAAI,CAACiC,KAAL,CAAWC,QAAX,CAAoB,OAApB,CAAJ,EAAkC;MAC9B,KAAK3D,MAAL,CAAYuE,QAAZ,CAAqB,CAAC,GAAD,CAArB;IACH,CAFD,MAEO,IAAI9C,IAAI,CAACiC,KAAL,CAAWC,QAAX,CAAoB,SAApB,CAAJ,EAAoC;MACvC,KAAK3D,MAAL,CAAYuE,QAAZ,CAAqB,CAAC,oBAAD,CAArB;IACH,CAFM,MAEA,IAAI9C,IAAI,CAACiC,KAAL,CAAWC,QAAX,CAAoB,UAApB,CAAJ,EAAqC;MACxC,KAAK3D,MAAL,CAAYuE,QAAZ,CAAqB,CAAC,qBAAD,CAArB;IACH,CAFM,MAEA;MACH,KAAKvE,MAAL,CAAYuE,QAAZ,CAAqB,CAAC,GAAD,CAArB;IACH;EACJ;;AAvV2B;;;mBAAnB1E,qBAAmB8E;AAAA;;;SAAnB9E;EAAmB+E,SAAnB/E,mBAAmB;EAAAgF,YAFhB", "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "catchError", "tap", "KeycloakAuthService", "constructor", "http", "router", "getUserFromStorage", "currentUserSubject", "asObservable", "hasValidToken", "isAuthenticatedSubject", "error", "console", "status", "clearSession", "initializeAuth", "loadKeycloakConfig", "checkExistingAuth", "response", "fetch", "apiUrl", "data", "json", "success", "keycloakConfig", "token", "getAccessToken", "userStr", "localStorage", "getItem", "user", "JSON", "parse", "next", "clearStoredTokens", "removeItem", "accessTokenKey", "refreshT<PERSON><PERSON><PERSON>", "login", "credentials", "post", "pipe", "setSession", "handleError", "refreshToken", "getRefreshToken", "refresh_token", "setTokens", "access_token", "logout", "logoutRequest", "observer", "getCurrentUser", "get", "headers", "getAuthHeaders", "updateUser", "validateToken", "isAuthenticated", "getCurrentUserValue", "value", "hasRole", "role", "roles", "includes", "hasAnyRole", "some", "isAdmin", "isTrainer", "isEmployee", "getKeycloakConfig", "authData", "setItem", "<PERSON><PERSON><PERSON>", "stringify", "accessToken", "navigate", "redirectAfterLogin", "attemptedUrl", "navigateByUrl", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\services\\keycloak-auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface KeycloakUser {\n    id: string;\n    username: string;\n    email: string;\n    name: string;\n    first_name: string;\n    last_name: string;\n    roles: string[];\n    team?: string;\n    phone?: string;\n    specialite?: string;\n}\n\nexport interface LoginRequest {\n    username: string;\n    password: string;\n}\n\nexport interface LoginResponse {\n    success: boolean;\n    message: string;\n    data: {\n        access_token: string;\n        refresh_token: string;\n        expires_in: number;\n        token_type: string;\n        user: KeycloakUser;\n    };\n}\n\nexport interface KeycloakConfig {\n    keycloak_url: string;\n    realm: string;\n    client_id: string;\n    auth_url: string;\n    token_url: string;\n    userinfo_url: string;\n    logout_url: string;\n}\n\n@Injectable({\n    providedIn: 'root'\n})\nexport class KeycloakAuthService {\n    private apiUrl = 'http://localhost:8080/api';\n    private accessTokenKey = 'keycloak_access_token';\n    private refreshTokenKey = 'keycloak_refresh_token';\n    private userKey = 'keycloak_user';\n\n    private currentUserSubject = new BehaviorSubject<KeycloakUser | null>(this.getUserFromStorage());\n    public currentUser$ = this.currentUserSubject.asObservable();\n\n    private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());\n    public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n    private keycloakConfig: KeycloakConfig | null = null;\n\n    constructor(\n        private http: HttpClient,\n        private router: Router\n    ) {\n        // Initialize will be called by AppInitService\n    }\n\n    /**\n     * Initialize authentication service\n     */\n    async initializeAuth(): Promise<void> {\n        await this.loadKeycloakConfig();\n        this.checkExistingAuth();\n    }\n\n    /**\n     * Load Keycloak configuration from backend\n     */\n    private async loadKeycloakConfig(): Promise<void> {\n        try {\n            // Use fetch to avoid circular dependency with HttpClient interceptor\n            const response = await fetch(`${this.apiUrl}/auth/config`);\n            const data = await response.json();\n            if (data.success) {\n                this.keycloakConfig = data.data;\n            }\n        } catch (error) {\n            console.error('Failed to load Keycloak config:', error);\n        }\n    }\n\n    /**\n     * Check if user is already authenticated on app start\n     */\n    private checkExistingAuth(): void {\n        const token = this.getAccessToken();\n        const userStr = localStorage.getItem('keycloak_user');\n\n        if (token && userStr) {\n            try {\n                const user = JSON.parse(userStr);\n                this.currentUserSubject.next(user);\n            } catch (error) {\n                console.error('Failed to parse stored user:', error);\n                this.clearStoredTokens();\n            }\n        }\n    }\n\n    /**\n     * Clear stored tokens and user data\n     */\n    private clearStoredTokens(): void {\n        localStorage.removeItem(this.accessTokenKey);\n        localStorage.removeItem(this.refreshTokenKey);\n        localStorage.removeItem('keycloak_user');\n        this.currentUserSubject.next(null);\n    }\n\n    /**\n     * Login with username and password\n     */\n    login(credentials: LoginRequest): Observable<LoginResponse> {\n        return this.http.post<LoginResponse>(`${this.apiUrl}/auth/login`, credentials)\n            .pipe(\n                tap(response => {\n                    if (response.success) {\n                        this.setSession(response.data);\n                    }\n                }),\n                catchError(this.handleError)\n            );\n    }\n\n    /**\n     * Refresh access token\n     */\n    refreshToken(): Observable<any> {\n        const refreshToken = this.getRefreshToken();\n        \n        if (!refreshToken) {\n            return throwError('No refresh token available');\n        }\n\n        return this.http.post(`${this.apiUrl}/auth/refresh`, {\n            refresh_token: refreshToken\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.setTokens(response.data.access_token, response.data.refresh_token);\n                }\n            }),\n            catchError(error => {\n                this.clearSession();\n                return throwError(error);\n            })\n        );\n    }\n\n    /**\n     * Logout user\n     */\n    logout(): Observable<any> {\n        const refreshToken = this.getRefreshToken();\n        \n        const logoutRequest = refreshToken ? \n            this.http.post(`${this.apiUrl}/auth/logout`, { refresh_token: refreshToken }) :\n            new Observable(observer => observer.next({}));\n\n        return logoutRequest.pipe(\n            tap(() => this.clearSession()),\n            catchError(() => {\n                this.clearSession();\n                return throwError('Logout failed');\n            })\n        );\n    }\n\n    /**\n     * Get current user info from server\n     */\n    getCurrentUser(): Observable<any> {\n        return this.http.get(`${this.apiUrl}/auth/me`, {\n            headers: this.getAuthHeaders()\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.updateUser(response.data.user);\n                }\n            }),\n            catchError(this.handleError)\n        );\n    }\n\n    /**\n     * Validate current token\n     */\n    validateToken(): Observable<any> {\n        return this.http.post(`${this.apiUrl}/auth/validate`, {}, {\n            headers: this.getAuthHeaders()\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.updateUser(response.data.user);\n                }\n            }),\n            catchError(error => {\n                this.clearSession();\n                return throwError(error);\n            })\n        );\n    }\n\n    /**\n     * Check if user is authenticated\n     */\n    isAuthenticated(): boolean {\n        return this.hasValidToken();\n    }\n\n    /**\n     * Get current user\n     */\n    getCurrentUserValue(): KeycloakUser | null {\n        return this.currentUserSubject.value;\n    }\n\n    /**\n     * Check if user has specific role\n     */\n    hasRole(role: string): boolean {\n        const user = this.getCurrentUserValue();\n        return user ? user.roles.includes(role) : false;\n    }\n\n    /**\n     * Check if user has any of the specified roles\n     */\n    hasAnyRole(roles: string[]): boolean {\n        const user = this.getCurrentUserValue();\n        return user ? roles.some(role => user.roles.includes(role)) : false;\n    }\n\n    /**\n     * Check if user is admin\n     */\n    isAdmin(): boolean {\n        return this.hasRole('admin');\n    }\n\n    /**\n     * Check if user is trainer\n     */\n    isTrainer(): boolean {\n        return this.hasRole('trainer');\n    }\n\n    /**\n     * Check if user is employee\n     */\n    isEmployee(): boolean {\n        return this.hasRole('employee');\n    }\n\n    /**\n     * Get access token\n     */\n    getAccessToken(): string | null {\n        return localStorage.getItem(this.accessTokenKey);\n    }\n\n    /**\n     * Get refresh token\n     */\n    getRefreshToken(): string | null {\n        return localStorage.getItem(this.refreshTokenKey);\n    }\n\n    /**\n     * Get Keycloak configuration\n     */\n    getKeycloakConfig(): KeycloakConfig | null {\n        return this.keycloakConfig;\n    }\n\n    /**\n     * Set session data\n     */\n    private setSession(authData: any): void {\n        localStorage.setItem(this.accessTokenKey, authData.access_token);\n        localStorage.setItem(this.refreshTokenKey, authData.refresh_token);\n        localStorage.setItem(this.userKey, JSON.stringify(authData.user));\n        this.currentUserSubject.next(authData.user);\n        this.isAuthenticatedSubject.next(true);\n    }\n\n    /**\n     * Set tokens only\n     */\n    private setTokens(accessToken: string, refreshToken: string): void {\n        localStorage.setItem(this.accessTokenKey, accessToken);\n        localStorage.setItem(this.refreshTokenKey, refreshToken);\n    }\n\n    /**\n     * Update user data\n     */\n    private updateUser(user: KeycloakUser): void {\n        localStorage.setItem(this.userKey, JSON.stringify(user));\n        this.currentUserSubject.next(user);\n    }\n\n    /**\n     * Clear session\n     */\n    private clearSession(): void {\n        localStorage.removeItem(this.accessTokenKey);\n        localStorage.removeItem(this.refreshTokenKey);\n        localStorage.removeItem(this.userKey);\n        this.currentUserSubject.next(null);\n        this.isAuthenticatedSubject.next(false);\n        this.router.navigate(['/auth/login']);\n    }\n\n    /**\n     * Get user from storage\n     */\n    private getUserFromStorage(): KeycloakUser | null {\n        const userStr = localStorage.getItem(this.userKey);\n        return userStr ? JSON.parse(userStr) : null;\n    }\n\n    /**\n     * Check if token exists\n     */\n    private hasValidToken(): boolean {\n        const token = this.getAccessToken();\n        return !!token;\n    }\n\n    /**\n     * Get authorization headers\n     */\n    private getAuthHeaders(): HttpHeaders {\n        const token = this.getAccessToken();\n        return new HttpHeaders({\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n        });\n    }\n\n    /**\n     * Handle HTTP errors\n     */\n    private handleError = (error: any) => {\n        console.error('Keycloak Auth Service Error:', error);\n        \n        if (error.status === 401) {\n            this.clearSession();\n        }\n        \n        return throwError(error);\n    };\n\n    /**\n     * Redirect user based on role after login\n     */\n    redirectAfterLogin(): void {\n        const user = this.getCurrentUserValue();\n        if (!user) return;\n\n        // Check if there's an attempted URL to redirect to\n        const attemptedUrl = localStorage.getItem('attempted_url');\n        if (attemptedUrl) {\n            localStorage.removeItem('attempted_url');\n            this.router.navigateByUrl(attemptedUrl);\n            return;\n        }\n\n        // Default role-based redirection\n        if (user.roles.includes('admin')) {\n            this.router.navigate(['/']);\n        } else if (user.roles.includes('trainer')) {\n            this.router.navigate(['/trainer/dashboard']);\n        } else if (user.roles.includes('employee')) {\n            this.router.navigate(['/employee/dashboard']);\n        } else {\n            this.router.navigate(['/']);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}