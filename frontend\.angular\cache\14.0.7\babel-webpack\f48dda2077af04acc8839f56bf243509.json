{"ast": null, "code": "import { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip(...sources) {\n  return operate((source, subscriber) => {\n    zipStatic(source, ...sources).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["zip", "zipStatic", "operate", "sources", "source", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/zip.js"], "sourcesContent": ["import { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip(...sources) {\n    return operate((source, subscriber) => {\n        zipStatic(source, ...sources).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAG,IAAIC,SAAhB,QAAiC,mBAAjC;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,OAAO,SAASF,GAAT,CAAa,GAAGG,OAAhB,EAAyB;EAC5B,OAAOD,OAAO,CAAC,CAACE,MAAD,EAASC,UAAT,KAAwB;IACnCJ,SAAS,CAACG,MAAD,EAAS,GAAGD,OAAZ,CAAT,CAA8BG,SAA9B,CAAwCD,UAAxC;EACH,CAFa,CAAd;AAGH"}, "metadata": {}, "sourceType": "module"}