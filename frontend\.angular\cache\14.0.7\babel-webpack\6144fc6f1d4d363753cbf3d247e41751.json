{"ast": null, "code": "import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, notification => observeNotification(notification, subscriber)));\n  });\n}", "map": {"version": 3, "names": ["observeNotification", "operate", "createOperatorSubscriber", "dematerialize", "source", "subscriber", "subscribe", "notification"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/dematerialize.js"], "sourcesContent": ["import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, (notification) => observeNotification(notification, subscriber)));\n    });\n}\n"], "mappings": "AAAA,SAASA,mBAAT,QAAoC,iBAApC;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,aAAT,GAAyB;EAC5B,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnCD,MAAM,CAACE,SAAP,CAAiBJ,wBAAwB,CAACG,UAAD,EAAcE,YAAD,IAAkBP,mBAAmB,CAACO,YAAD,EAAeF,UAAf,CAAlD,CAAzC;EACH,CAFa,CAAd;AAGH"}, "metadata": {}, "sourceType": "module"}