{"ast": null, "code": "import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n\n  return new Observable(subscriber => {\n    executeSchedule(subscriber, scheduler, () => {\n      const iterator = input[Symbol.asyncIterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        iterator.next().then(result => {\n          if (result.done) {\n            subscriber.complete();\n          } else {\n            subscriber.next(result.value);\n          }\n        });\n      }, 0, true);\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "executeSchedule", "scheduleAsyncIterable", "input", "scheduler", "Error", "subscriber", "iterator", "Symbol", "asyncIterator", "next", "then", "result", "done", "complete", "value"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleAsyncIterable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n    if (!input) {\n        throw new Error('Iterable cannot be null');\n    }\n    return new Observable((subscriber) => {\n        executeSchedule(subscriber, scheduler, () => {\n            const iterator = input[Symbol.asyncIterator]();\n            executeSchedule(subscriber, scheduler, () => {\n                iterator.next().then((result) => {\n                    if (result.done) {\n                        subscriber.complete();\n                    }\n                    else {\n                        subscriber.next(result.value);\n                    }\n                });\n            }, 0, true);\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,qBAAT,CAA+BC,KAA/B,EAAsCC,SAAtC,EAAiD;EACpD,IAAI,CAACD,KAAL,EAAY;IACR,MAAM,IAAIE,KAAJ,CAAU,yBAAV,CAAN;EACH;;EACD,OAAO,IAAIL,UAAJ,CAAgBM,UAAD,IAAgB;IAClCL,eAAe,CAACK,UAAD,EAAaF,SAAb,EAAwB,MAAM;MACzC,MAAMG,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACC,aAAR,CAAL,EAAjB;MACAR,eAAe,CAACK,UAAD,EAAaF,SAAb,EAAwB,MAAM;QACzCG,QAAQ,CAACG,IAAT,GAAgBC,IAAhB,CAAsBC,MAAD,IAAY;UAC7B,IAAIA,MAAM,CAACC,IAAX,EAAiB;YACbP,UAAU,CAACQ,QAAX;UACH,CAFD,MAGK;YACDR,UAAU,CAACI,IAAX,CAAgBE,MAAM,CAACG,KAAvB;UACH;QACJ,CAPD;MAQH,CATc,EASZ,CATY,EAST,IATS,CAAf;IAUH,CAZc,CAAf;EAaH,CAdM,CAAP;AAeH"}, "metadata": {}, "sourceType": "module"}