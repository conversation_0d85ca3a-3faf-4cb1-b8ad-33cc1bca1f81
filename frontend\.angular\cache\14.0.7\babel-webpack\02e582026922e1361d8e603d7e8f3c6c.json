{"ast": null, "code": "import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n  return new Observable(subscriber => {\n    let i = 0;\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n      } else {\n        subscriber.next(input[i++]);\n\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "scheduleArray", "input", "scheduler", "subscriber", "i", "schedule", "length", "complete", "next", "closed"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleArray.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n    return new Observable((subscriber) => {\n        let i = 0;\n        return scheduler.schedule(function () {\n            if (i === input.length) {\n                subscriber.complete();\n            }\n            else {\n                subscriber.next(input[i++]);\n                if (!subscriber.closed) {\n                    this.schedule();\n                }\n            }\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,aAAT,CAAuBC,KAAvB,EAA8BC,SAA9B,EAAyC;EAC5C,OAAO,IAAIH,UAAJ,CAAgBI,UAAD,IAAgB;IAClC,IAAIC,CAAC,GAAG,CAAR;IACA,OAAOF,SAAS,CAACG,QAAV,CAAmB,YAAY;MAClC,IAAID,CAAC,KAAKH,KAAK,CAACK,MAAhB,EAAwB;QACpBH,UAAU,CAACI,QAAX;MACH,CAFD,MAGK;QACDJ,UAAU,CAACK,IAAX,CAAgBP,KAAK,CAACG,CAAC,EAAF,CAArB;;QACA,IAAI,CAACD,UAAU,CAACM,MAAhB,EAAwB;UACpB,KAAKJ,QAAL;QACH;MACJ;IACJ,CAVM,CAAP;EAWH,CAbM,CAAP;AAcH"}, "metadata": {}, "sourceType": "module"}