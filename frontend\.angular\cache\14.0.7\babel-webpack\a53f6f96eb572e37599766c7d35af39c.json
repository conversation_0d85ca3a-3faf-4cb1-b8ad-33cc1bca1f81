{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function hasLift(source) {\n  return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n  return source => {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}", "map": {"version": 3, "names": ["isFunction", "hasLift", "source", "lift", "operate", "init", "liftedSource", "err", "error", "TypeError"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/lift.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function hasLift(source) {\n    return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n    return (source) => {\n        if (hasLift(source)) {\n            return source.lift(function (liftedSource) {\n                try {\n                    return init(liftedSource, this);\n                }\n                catch (err) {\n                    this.error(err);\n                }\n            });\n        }\n        throw new TypeError('Unable to lift unknown Observable type');\n    };\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;EAC5B,OAAOF,UAAU,CAACE,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACC,IAAxD,CAAjB;AACH;AACD,OAAO,SAASC,OAAT,CAAiBC,IAAjB,EAAuB;EAC1B,OAAQH,MAAD,IAAY;IACf,IAAID,OAAO,CAACC,MAAD,CAAX,EAAqB;MACjB,OAAOA,MAAM,CAACC,IAAP,CAAY,UAAUG,YAAV,EAAwB;QACvC,IAAI;UACA,OAAOD,IAAI,CAACC,YAAD,EAAe,IAAf,CAAX;QACH,CAFD,CAGA,OAAOC,GAAP,EAAY;UACR,KAAKC,KAAL,CAAWD,GAAX;QACH;MACJ,CAPM,CAAP;IAQH;;IACD,MAAM,IAAIE,SAAJ,CAAc,wCAAd,CAAN;EACH,CAZD;AAaH"}, "metadata": {}, "sourceType": "module"}