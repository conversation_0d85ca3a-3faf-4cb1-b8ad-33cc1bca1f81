{"ast": null, "code": "import { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nexport class AsyncAction extends Action {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.pending = false;\n  }\n\n  schedule(state, delay = 0) {\n    var _a;\n\n    if (this.closed) {\n      return this;\n    }\n\n    this.state = state;\n    const id = this.id;\n    const scheduler = this.scheduler;\n\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n\n    this.pending = true;\n    this.delay = delay;\n    this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  }\n\n  requestAsyncId(scheduler, _id, delay = 0) {\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  }\n\n  recycleAsyncId(_scheduler, id, delay = 0) {\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n\n    return undefined;\n  }\n\n  execute(state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n\n    this.pending = false;\n\n    const error = this._execute(state, delay);\n\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  }\n\n  _execute(state, _delay) {\n    let errored = false;\n    let errorValue;\n\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      const {\n        id,\n        scheduler\n      } = this;\n      const {\n        actions\n      } = scheduler;\n      this.work = this.state = this.scheduler = null;\n      this.pending = false;\n      arrRemove(actions, this);\n\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n\n      this.delay = null;\n      super.unsubscribe();\n    }\n  }\n\n}", "map": {"version": 3, "names": ["Action", "intervalProvider", "arr<PERSON><PERSON><PERSON>", "AsyncAction", "constructor", "scheduler", "work", "pending", "schedule", "state", "delay", "_a", "closed", "id", "recycleAsyncId", "requestAsyncId", "_id", "setInterval", "flush", "bind", "_scheduler", "clearInterval", "undefined", "execute", "Error", "error", "_execute", "_delay", "errored", "errorValue", "e", "unsubscribe", "actions"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/AsyncAction.js"], "sourcesContent": ["import { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nexport class AsyncAction extends Action {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.pending = false;\n    }\n    schedule(state, delay = 0) {\n        var _a;\n        if (this.closed) {\n            return this;\n        }\n        this.state = state;\n        const id = this.id;\n        const scheduler = this.scheduler;\n        if (id != null) {\n            this.id = this.recycleAsyncId(scheduler, id, delay);\n        }\n        this.pending = true;\n        this.delay = delay;\n        this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n        return this;\n    }\n    requestAsyncId(scheduler, _id, delay = 0) {\n        return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n    }\n    recycleAsyncId(_scheduler, id, delay = 0) {\n        if (delay != null && this.delay === delay && this.pending === false) {\n            return id;\n        }\n        if (id != null) {\n            intervalProvider.clearInterval(id);\n        }\n        return undefined;\n    }\n    execute(state, delay) {\n        if (this.closed) {\n            return new Error('executing a cancelled action');\n        }\n        this.pending = false;\n        const error = this._execute(state, delay);\n        if (error) {\n            return error;\n        }\n        else if (this.pending === false && this.id != null) {\n            this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n        }\n    }\n    _execute(state, _delay) {\n        let errored = false;\n        let errorValue;\n        try {\n            this.work(state);\n        }\n        catch (e) {\n            errored = true;\n            errorValue = e ? e : new Error('Scheduled action threw falsy error');\n        }\n        if (errored) {\n            this.unsubscribe();\n            return errorValue;\n        }\n    }\n    unsubscribe() {\n        if (!this.closed) {\n            const { id, scheduler } = this;\n            const { actions } = scheduler;\n            this.work = this.state = this.scheduler = null;\n            this.pending = false;\n            arrRemove(actions, this);\n            if (id != null) {\n                this.id = this.recycleAsyncId(scheduler, id, null);\n            }\n            this.delay = null;\n            super.unsubscribe();\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,OAAO,MAAMC,WAAN,SAA0BH,MAA1B,CAAiC;EACpCI,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,OAAL,GAAe,KAAf;EACH;;EACDC,QAAQ,CAACC,KAAD,EAAQC,KAAK,GAAG,CAAhB,EAAmB;IACvB,IAAIC,EAAJ;;IACA,IAAI,KAAKC,MAAT,EAAiB;MACb,OAAO,IAAP;IACH;;IACD,KAAKH,KAAL,GAAaA,KAAb;IACA,MAAMI,EAAE,GAAG,KAAKA,EAAhB;IACA,MAAMR,SAAS,GAAG,KAAKA,SAAvB;;IACA,IAAIQ,EAAE,IAAI,IAAV,EAAgB;MACZ,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoBT,SAApB,EAA+BQ,EAA/B,EAAmCH,KAAnC,CAAV;IACH;;IACD,KAAKH,OAAL,GAAe,IAAf;IACA,KAAKG,KAAL,GAAaA,KAAb;IACA,KAAKG,EAAL,GAAU,CAACF,EAAE,GAAG,KAAKE,EAAX,MAAmB,IAAnB,IAA2BF,EAAE,KAAK,KAAK,CAAvC,GAA2CA,EAA3C,GAAgD,KAAKI,cAAL,CAAoBV,SAApB,EAA+B,KAAKQ,EAApC,EAAwCH,KAAxC,CAA1D;IACA,OAAO,IAAP;EACH;;EACDK,cAAc,CAACV,SAAD,EAAYW,GAAZ,EAAiBN,KAAK,GAAG,CAAzB,EAA4B;IACtC,OAAOT,gBAAgB,CAACgB,WAAjB,CAA6BZ,SAAS,CAACa,KAAV,CAAgBC,IAAhB,CAAqBd,SAArB,EAAgC,IAAhC,CAA7B,EAAoEK,KAApE,CAAP;EACH;;EACDI,cAAc,CAACM,UAAD,EAAaP,EAAb,EAAiBH,KAAK,GAAG,CAAzB,EAA4B;IACtC,IAAIA,KAAK,IAAI,IAAT,IAAiB,KAAKA,KAAL,KAAeA,KAAhC,IAAyC,KAAKH,OAAL,KAAiB,KAA9D,EAAqE;MACjE,OAAOM,EAAP;IACH;;IACD,IAAIA,EAAE,IAAI,IAAV,EAAgB;MACZZ,gBAAgB,CAACoB,aAAjB,CAA+BR,EAA/B;IACH;;IACD,OAAOS,SAAP;EACH;;EACDC,OAAO,CAACd,KAAD,EAAQC,KAAR,EAAe;IAClB,IAAI,KAAKE,MAAT,EAAiB;MACb,OAAO,IAAIY,KAAJ,CAAU,8BAAV,CAAP;IACH;;IACD,KAAKjB,OAAL,GAAe,KAAf;;IACA,MAAMkB,KAAK,GAAG,KAAKC,QAAL,CAAcjB,KAAd,EAAqBC,KAArB,CAAd;;IACA,IAAIe,KAAJ,EAAW;MACP,OAAOA,KAAP;IACH,CAFD,MAGK,IAAI,KAAKlB,OAAL,KAAiB,KAAjB,IAA0B,KAAKM,EAAL,IAAW,IAAzC,EAA+C;MAChD,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoB,KAAKT,SAAzB,EAAoC,KAAKQ,EAAzC,EAA6C,IAA7C,CAAV;IACH;EACJ;;EACDa,QAAQ,CAACjB,KAAD,EAAQkB,MAAR,EAAgB;IACpB,IAAIC,OAAO,GAAG,KAAd;IACA,IAAIC,UAAJ;;IACA,IAAI;MACA,KAAKvB,IAAL,CAAUG,KAAV;IACH,CAFD,CAGA,OAAOqB,CAAP,EAAU;MACNF,OAAO,GAAG,IAAV;MACAC,UAAU,GAAGC,CAAC,GAAGA,CAAH,GAAO,IAAIN,KAAJ,CAAU,oCAAV,CAArB;IACH;;IACD,IAAII,OAAJ,EAAa;MACT,KAAKG,WAAL;MACA,OAAOF,UAAP;IACH;EACJ;;EACDE,WAAW,GAAG;IACV,IAAI,CAAC,KAAKnB,MAAV,EAAkB;MACd,MAAM;QAAEC,EAAF;QAAMR;MAAN,IAAoB,IAA1B;MACA,MAAM;QAAE2B;MAAF,IAAc3B,SAApB;MACA,KAAKC,IAAL,GAAY,KAAKG,KAAL,GAAa,KAAKJ,SAAL,GAAiB,IAA1C;MACA,KAAKE,OAAL,GAAe,KAAf;MACAL,SAAS,CAAC8B,OAAD,EAAU,IAAV,CAAT;;MACA,IAAInB,EAAE,IAAI,IAAV,EAAgB;QACZ,KAAKA,EAAL,GAAU,KAAKC,cAAL,CAAoBT,SAApB,EAA+BQ,EAA/B,EAAmC,IAAnC,CAAV;MACH;;MACD,KAAKH,KAAL,GAAa,IAAb;MACA,MAAMqB,WAAN;IACH;EACJ;;AA5EmC"}, "metadata": {}, "sourceType": "module"}