{"ast": null, "code": "import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n  constructor(initialTeardown) {\n    this.initialTeardown = initialTeardown;\n    this.closed = false;\n    this._parentage = null;\n    this._finalizers = null;\n  }\n\n  unsubscribe() {\n    let errors;\n\n    if (!this.closed) {\n      this.closed = true;\n      const {\n        _parentage\n      } = this;\n\n      if (_parentage) {\n        this._parentage = null;\n\n        if (Array.isArray(_parentage)) {\n          for (const parent of _parentage) {\n            parent.remove(this);\n          }\n        } else {\n          _parentage.remove(this);\n        }\n      }\n\n      const {\n        initialTeardown: initialFinalizer\n      } = this;\n\n      if (isFunction(initialFinalizer)) {\n        try {\n          initialFinalizer();\n        } catch (e) {\n          errors = e instanceof UnsubscriptionError ? e.errors : [e];\n        }\n      }\n\n      const {\n        _finalizers\n      } = this;\n\n      if (_finalizers) {\n        this._finalizers = null;\n\n        for (const finalizer of _finalizers) {\n          try {\n            execFinalizer(finalizer);\n          } catch (err) {\n            errors = errors !== null && errors !== void 0 ? errors : [];\n\n            if (err instanceof UnsubscriptionError) {\n              errors = [...errors, ...err.errors];\n            } else {\n              errors.push(err);\n            }\n          }\n        }\n      }\n\n      if (errors) {\n        throw new UnsubscriptionError(errors);\n      }\n    }\n  }\n\n  add(teardown) {\n    var _a;\n\n    if (teardown && teardown !== this) {\n      if (this.closed) {\n        execFinalizer(teardown);\n      } else {\n        if (teardown instanceof Subscription) {\n          if (teardown.closed || teardown._hasParent(this)) {\n            return;\n          }\n\n          teardown._addParent(this);\n        }\n\n        (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n      }\n    }\n  }\n\n  _hasParent(parent) {\n    const {\n      _parentage\n    } = this;\n    return _parentage === parent || Array.isArray(_parentage) && _parentage.includes(parent);\n  }\n\n  _addParent(parent) {\n    const {\n      _parentage\n    } = this;\n    this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n  }\n\n  _removeParent(parent) {\n    const {\n      _parentage\n    } = this;\n\n    if (_parentage === parent) {\n      this._parentage = null;\n    } else if (Array.isArray(_parentage)) {\n      arrRemove(_parentage, parent);\n    }\n  }\n\n  remove(teardown) {\n    const {\n      _finalizers\n    } = this;\n    _finalizers && arrRemove(_finalizers, teardown);\n\n    if (teardown instanceof Subscription) {\n      teardown._removeParent(this);\n    }\n  }\n\n}\n\nSubscription.EMPTY = (() => {\n  const empty = new Subscription();\n  empty.closed = true;\n  return empty;\n})();\n\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n  return value instanceof Subscription || value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe);\n}\n\nfunction execFinalizer(finalizer) {\n  if (isFunction(finalizer)) {\n    finalizer();\n  } else {\n    finalizer.unsubscribe();\n  }\n}", "map": {"version": 3, "names": ["isFunction", "UnsubscriptionError", "arr<PERSON><PERSON><PERSON>", "Subscription", "constructor", "initialTeardown", "closed", "_parentage", "_finalizers", "unsubscribe", "errors", "Array", "isArray", "parent", "remove", "initialFinalizer", "e", "finalizer", "execFinalizer", "err", "push", "add", "teardown", "_a", "_hasParent", "_addParent", "includes", "_removeParent", "EMPTY", "empty", "EMPTY_SUBSCRIPTION", "isSubscription", "value"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/Subscription.js"], "sourcesContent": ["import { isFunction } from './util/isFunction';\nimport { UnsubscriptionError } from './util/UnsubscriptionError';\nimport { arrRemove } from './util/arrRemove';\nexport class Subscription {\n    constructor(initialTeardown) {\n        this.initialTeardown = initialTeardown;\n        this.closed = false;\n        this._parentage = null;\n        this._finalizers = null;\n    }\n    unsubscribe() {\n        let errors;\n        if (!this.closed) {\n            this.closed = true;\n            const { _parentage } = this;\n            if (_parentage) {\n                this._parentage = null;\n                if (Array.isArray(_parentage)) {\n                    for (const parent of _parentage) {\n                        parent.remove(this);\n                    }\n                }\n                else {\n                    _parentage.remove(this);\n                }\n            }\n            const { initialTeardown: initialFinalizer } = this;\n            if (isFunction(initialFinalizer)) {\n                try {\n                    initialFinalizer();\n                }\n                catch (e) {\n                    errors = e instanceof UnsubscriptionError ? e.errors : [e];\n                }\n            }\n            const { _finalizers } = this;\n            if (_finalizers) {\n                this._finalizers = null;\n                for (const finalizer of _finalizers) {\n                    try {\n                        execFinalizer(finalizer);\n                    }\n                    catch (err) {\n                        errors = errors !== null && errors !== void 0 ? errors : [];\n                        if (err instanceof UnsubscriptionError) {\n                            errors = [...errors, ...err.errors];\n                        }\n                        else {\n                            errors.push(err);\n                        }\n                    }\n                }\n            }\n            if (errors) {\n                throw new UnsubscriptionError(errors);\n            }\n        }\n    }\n    add(teardown) {\n        var _a;\n        if (teardown && teardown !== this) {\n            if (this.closed) {\n                execFinalizer(teardown);\n            }\n            else {\n                if (teardown instanceof Subscription) {\n                    if (teardown.closed || teardown._hasParent(this)) {\n                        return;\n                    }\n                    teardown._addParent(this);\n                }\n                (this._finalizers = (_a = this._finalizers) !== null && _a !== void 0 ? _a : []).push(teardown);\n            }\n        }\n    }\n    _hasParent(parent) {\n        const { _parentage } = this;\n        return _parentage === parent || (Array.isArray(_parentage) && _parentage.includes(parent));\n    }\n    _addParent(parent) {\n        const { _parentage } = this;\n        this._parentage = Array.isArray(_parentage) ? (_parentage.push(parent), _parentage) : _parentage ? [_parentage, parent] : parent;\n    }\n    _removeParent(parent) {\n        const { _parentage } = this;\n        if (_parentage === parent) {\n            this._parentage = null;\n        }\n        else if (Array.isArray(_parentage)) {\n            arrRemove(_parentage, parent);\n        }\n    }\n    remove(teardown) {\n        const { _finalizers } = this;\n        _finalizers && arrRemove(_finalizers, teardown);\n        if (teardown instanceof Subscription) {\n            teardown._removeParent(this);\n        }\n    }\n}\nSubscription.EMPTY = (() => {\n    const empty = new Subscription();\n    empty.closed = true;\n    return empty;\n})();\nexport const EMPTY_SUBSCRIPTION = Subscription.EMPTY;\nexport function isSubscription(value) {\n    return (value instanceof Subscription ||\n        (value && 'closed' in value && isFunction(value.remove) && isFunction(value.add) && isFunction(value.unsubscribe)));\n}\nfunction execFinalizer(finalizer) {\n    if (isFunction(finalizer)) {\n        finalizer();\n    }\n    else {\n        finalizer.unsubscribe();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,SAASC,mBAAT,QAAoC,4BAApC;AACA,SAASC,SAAT,QAA0B,kBAA1B;AACA,OAAO,MAAMC,YAAN,CAAmB;EACtBC,WAAW,CAACC,eAAD,EAAkB;IACzB,KAAKA,eAAL,GAAuBA,eAAvB;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,WAAW,GAAG;IACV,IAAIC,MAAJ;;IACA,IAAI,CAAC,KAAKJ,MAAV,EAAkB;MACd,KAAKA,MAAL,GAAc,IAAd;MACA,MAAM;QAAEC;MAAF,IAAiB,IAAvB;;MACA,IAAIA,UAAJ,EAAgB;QACZ,KAAKA,UAAL,GAAkB,IAAlB;;QACA,IAAII,KAAK,CAACC,OAAN,CAAcL,UAAd,CAAJ,EAA+B;UAC3B,KAAK,MAAMM,MAAX,IAAqBN,UAArB,EAAiC;YAC7BM,MAAM,CAACC,MAAP,CAAc,IAAd;UACH;QACJ,CAJD,MAKK;UACDP,UAAU,CAACO,MAAX,CAAkB,IAAlB;QACH;MACJ;;MACD,MAAM;QAAET,eAAe,EAAEU;MAAnB,IAAwC,IAA9C;;MACA,IAAIf,UAAU,CAACe,gBAAD,CAAd,EAAkC;QAC9B,IAAI;UACAA,gBAAgB;QACnB,CAFD,CAGA,OAAOC,CAAP,EAAU;UACNN,MAAM,GAAGM,CAAC,YAAYf,mBAAb,GAAmCe,CAAC,CAACN,MAArC,GAA8C,CAACM,CAAD,CAAvD;QACH;MACJ;;MACD,MAAM;QAAER;MAAF,IAAkB,IAAxB;;MACA,IAAIA,WAAJ,EAAiB;QACb,KAAKA,WAAL,GAAmB,IAAnB;;QACA,KAAK,MAAMS,SAAX,IAAwBT,WAAxB,EAAqC;UACjC,IAAI;YACAU,aAAa,CAACD,SAAD,CAAb;UACH,CAFD,CAGA,OAAOE,GAAP,EAAY;YACRT,MAAM,GAAGA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuCA,MAAvC,GAAgD,EAAzD;;YACA,IAAIS,GAAG,YAAYlB,mBAAnB,EAAwC;cACpCS,MAAM,GAAG,CAAC,GAAGA,MAAJ,EAAY,GAAGS,GAAG,CAACT,MAAnB,CAAT;YACH,CAFD,MAGK;cACDA,MAAM,CAACU,IAAP,CAAYD,GAAZ;YACH;UACJ;QACJ;MACJ;;MACD,IAAIT,MAAJ,EAAY;QACR,MAAM,IAAIT,mBAAJ,CAAwBS,MAAxB,CAAN;MACH;IACJ;EACJ;;EACDW,GAAG,CAACC,QAAD,EAAW;IACV,IAAIC,EAAJ;;IACA,IAAID,QAAQ,IAAIA,QAAQ,KAAK,IAA7B,EAAmC;MAC/B,IAAI,KAAKhB,MAAT,EAAiB;QACbY,aAAa,CAACI,QAAD,CAAb;MACH,CAFD,MAGK;QACD,IAAIA,QAAQ,YAAYnB,YAAxB,EAAsC;UAClC,IAAImB,QAAQ,CAAChB,MAAT,IAAmBgB,QAAQ,CAACE,UAAT,CAAoB,IAApB,CAAvB,EAAkD;YAC9C;UACH;;UACDF,QAAQ,CAACG,UAAT,CAAoB,IAApB;QACH;;QACD,CAAC,KAAKjB,WAAL,GAAmB,CAACe,EAAE,GAAG,KAAKf,WAAX,MAA4B,IAA5B,IAAoCe,EAAE,KAAK,KAAK,CAAhD,GAAoDA,EAApD,GAAyD,EAA7E,EAAiFH,IAAjF,CAAsFE,QAAtF;MACH;IACJ;EACJ;;EACDE,UAAU,CAACX,MAAD,EAAS;IACf,MAAM;MAAEN;IAAF,IAAiB,IAAvB;IACA,OAAOA,UAAU,KAAKM,MAAf,IAA0BF,KAAK,CAACC,OAAN,CAAcL,UAAd,KAA6BA,UAAU,CAACmB,QAAX,CAAoBb,MAApB,CAA9D;EACH;;EACDY,UAAU,CAACZ,MAAD,EAAS;IACf,MAAM;MAAEN;IAAF,IAAiB,IAAvB;IACA,KAAKA,UAAL,GAAkBI,KAAK,CAACC,OAAN,CAAcL,UAAd,KAA6BA,UAAU,CAACa,IAAX,CAAgBP,MAAhB,GAAyBN,UAAtD,IAAoEA,UAAU,GAAG,CAACA,UAAD,EAAaM,MAAb,CAAH,GAA0BA,MAA1H;EACH;;EACDc,aAAa,CAACd,MAAD,EAAS;IAClB,MAAM;MAAEN;IAAF,IAAiB,IAAvB;;IACA,IAAIA,UAAU,KAAKM,MAAnB,EAA2B;MACvB,KAAKN,UAAL,GAAkB,IAAlB;IACH,CAFD,MAGK,IAAII,KAAK,CAACC,OAAN,CAAcL,UAAd,CAAJ,EAA+B;MAChCL,SAAS,CAACK,UAAD,EAAaM,MAAb,CAAT;IACH;EACJ;;EACDC,MAAM,CAACQ,QAAD,EAAW;IACb,MAAM;MAAEd;IAAF,IAAkB,IAAxB;IACAA,WAAW,IAAIN,SAAS,CAACM,WAAD,EAAcc,QAAd,CAAxB;;IACA,IAAIA,QAAQ,YAAYnB,YAAxB,EAAsC;MAClCmB,QAAQ,CAACK,aAAT,CAAuB,IAAvB;IACH;EACJ;;AA/FqB;;AAiG1BxB,YAAY,CAACyB,KAAb,GAAqB,CAAC,MAAM;EACxB,MAAMC,KAAK,GAAG,IAAI1B,YAAJ,EAAd;EACA0B,KAAK,CAACvB,MAAN,GAAe,IAAf;EACA,OAAOuB,KAAP;AACH,CAJoB,GAArB;;AAKA,OAAO,MAAMC,kBAAkB,GAAG3B,YAAY,CAACyB,KAAxC;AACP,OAAO,SAASG,cAAT,CAAwBC,KAAxB,EAA+B;EAClC,OAAQA,KAAK,YAAY7B,YAAjB,IACH6B,KAAK,IAAI,YAAYA,KAArB,IAA8BhC,UAAU,CAACgC,KAAK,CAAClB,MAAP,CAAxC,IAA0Dd,UAAU,CAACgC,KAAK,CAACX,GAAP,CAApE,IAAmFrB,UAAU,CAACgC,KAAK,CAACvB,WAAP,CADlG;AAEH;;AACD,SAASS,aAAT,CAAuBD,SAAvB,EAAkC;EAC9B,IAAIjB,UAAU,CAACiB,SAAD,CAAd,EAA2B;IACvBA,SAAS;EACZ,CAFD,MAGK;IACDA,SAAS,CAACR,WAAV;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}