{"ast": null, "code": "import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Infinity) {\n  if (isFunction(resultSelector)) {\n    return mergeMap(() => innerObservable, resultSelector, concurrent);\n  }\n\n  if (typeof resultSelector === 'number') {\n    concurrent = resultSelector;\n  }\n\n  return mergeMap(() => innerObservable, concurrent);\n}", "map": {"version": 3, "names": ["mergeMap", "isFunction", "mergeMapTo", "innerObservable", "resultSelector", "concurrent", "Infinity"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/mergeMapTo.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nimport { isFunction } from '../util/isFunction';\nexport function mergeMapTo(innerObservable, resultSelector, concurrent = Infinity) {\n    if (isFunction(resultSelector)) {\n        return mergeMap(() => innerObservable, resultSelector, concurrent);\n    }\n    if (typeof resultSelector === 'number') {\n        concurrent = resultSelector;\n    }\n    return mergeMap(() => innerObservable, concurrent);\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,UAAT,CAAoBC,eAApB,EAAqCC,cAArC,EAAqDC,UAAU,GAAGC,QAAlE,EAA4E;EAC/E,IAAIL,UAAU,CAACG,cAAD,CAAd,EAAgC;IAC5B,OAAOJ,QAAQ,CAAC,MAAMG,eAAP,EAAwBC,cAAxB,EAAwCC,UAAxC,CAAf;EACH;;EACD,IAAI,OAAOD,cAAP,KAA0B,QAA9B,EAAwC;IACpCC,UAAU,GAAGD,cAAb;EACH;;EACD,OAAOJ,QAAQ,CAAC,MAAMG,eAAP,EAAwBE,UAAxB,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}