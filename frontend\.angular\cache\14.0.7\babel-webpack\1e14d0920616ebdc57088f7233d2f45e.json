{"ast": null, "code": "import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n  var _a, _b;\n\n  const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  const maxBufferSize = otherArgs[1] || Infinity;\n  return operate((source, subscriber) => {\n    let bufferRecords = [];\n    let restartOnEmit = false;\n\n    const emit = record => {\n      const {\n        buffer,\n        subs\n      } = record;\n      subs.unsubscribe();\n      arrRemove(bufferRecords, record);\n      subscriber.next(buffer);\n      restartOnEmit && startBuffer();\n    };\n\n    const startBuffer = () => {\n      if (bufferRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const buffer = [];\n        const record = {\n          buffer,\n          subs\n        };\n        bufferRecords.push(record);\n        executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n      }\n    };\n\n    if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n    } else {\n      restartOnEmit = true;\n    }\n\n    startBuffer();\n    const bufferTimeSubscriber = createOperatorSubscriber(subscriber, value => {\n      const recordsCopy = bufferRecords.slice();\n\n      for (const record of recordsCopy) {\n        const {\n          buffer\n        } = record;\n        buffer.push(value);\n        maxBufferSize <= buffer.length && emit(record);\n      }\n    }, () => {\n      while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n        subscriber.next(bufferRecords.shift().buffer);\n      }\n\n      bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n      subscriber.complete();\n      subscriber.unsubscribe();\n    }, undefined, () => bufferRecords = null);\n    source.subscribe(bufferTimeSubscriber);\n  });\n}", "map": {"version": 3, "names": ["Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "asyncScheduler", "popScheduler", "executeSchedule", "bufferTime", "bufferTimeSpan", "otherArgs", "_a", "_b", "scheduler", "bufferCreationInterval", "maxBufferSize", "Infinity", "source", "subscriber", "bufferRecords", "restartOnEmit", "emit", "record", "buffer", "subs", "unsubscribe", "next", "startBuffer", "add", "push", "bufferTimeSubscriber", "value", "recordsCopy", "slice", "length", "shift", "complete", "undefined", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/bufferTime.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { asyncScheduler } from '../scheduler/async';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function bufferTime(bufferTimeSpan, ...otherArgs) {\n    var _a, _b;\n    const scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n    const bufferCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n    const maxBufferSize = otherArgs[1] || Infinity;\n    return operate((source, subscriber) => {\n        let bufferRecords = [];\n        let restartOnEmit = false;\n        const emit = (record) => {\n            const { buffer, subs } = record;\n            subs.unsubscribe();\n            arrRemove(bufferRecords, record);\n            subscriber.next(buffer);\n            restartOnEmit && startBuffer();\n        };\n        const startBuffer = () => {\n            if (bufferRecords) {\n                const subs = new Subscription();\n                subscriber.add(subs);\n                const buffer = [];\n                const record = {\n                    buffer,\n                    subs,\n                };\n                bufferRecords.push(record);\n                executeSchedule(subs, scheduler, () => emit(record), bufferTimeSpan);\n            }\n        };\n        if (bufferCreationInterval !== null && bufferCreationInterval >= 0) {\n            executeSchedule(subscriber, scheduler, startBuffer, bufferCreationInterval, true);\n        }\n        else {\n            restartOnEmit = true;\n        }\n        startBuffer();\n        const bufferTimeSubscriber = createOperatorSubscriber(subscriber, (value) => {\n            const recordsCopy = bufferRecords.slice();\n            for (const record of recordsCopy) {\n                const { buffer } = record;\n                buffer.push(value);\n                maxBufferSize <= buffer.length && emit(record);\n            }\n        }, () => {\n            while (bufferRecords === null || bufferRecords === void 0 ? void 0 : bufferRecords.length) {\n                subscriber.next(bufferRecords.shift().buffer);\n            }\n            bufferTimeSubscriber === null || bufferTimeSubscriber === void 0 ? void 0 : bufferTimeSubscriber.unsubscribe();\n            subscriber.complete();\n            subscriber.unsubscribe();\n        }, undefined, () => (bufferRecords = null));\n        source.subscribe(bufferTimeSubscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,UAAT,CAAoBC,cAApB,EAAoC,GAAGC,SAAvC,EAAkD;EACrD,IAAIC,EAAJ,EAAQC,EAAR;;EACA,MAAMC,SAAS,GAAG,CAACF,EAAE,GAAGL,YAAY,CAACI,SAAD,CAAlB,MAAmC,IAAnC,IAA2CC,EAAE,KAAK,KAAK,CAAvD,GAA2DA,EAA3D,GAAgEN,cAAlF;EACA,MAAMS,sBAAsB,GAAG,CAACF,EAAE,GAAGF,SAAS,CAAC,CAAD,CAAf,MAAwB,IAAxB,IAAgCE,EAAE,KAAK,KAAK,CAA5C,GAAgDA,EAAhD,GAAqD,IAApF;EACA,MAAMG,aAAa,GAAGL,SAAS,CAAC,CAAD,CAAT,IAAgBM,QAAtC;EACA,OAAOd,OAAO,CAAC,CAACe,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,aAAa,GAAG,EAApB;IACA,IAAIC,aAAa,GAAG,KAApB;;IACA,MAAMC,IAAI,GAAIC,MAAD,IAAY;MACrB,MAAM;QAAEC,MAAF;QAAUC;MAAV,IAAmBF,MAAzB;MACAE,IAAI,CAACC,WAAL;MACArB,SAAS,CAACe,aAAD,EAAgBG,MAAhB,CAAT;MACAJ,UAAU,CAACQ,IAAX,CAAgBH,MAAhB;MACAH,aAAa,IAAIO,WAAW,EAA5B;IACH,CAND;;IAOA,MAAMA,WAAW,GAAG,MAAM;MACtB,IAAIR,aAAJ,EAAmB;QACf,MAAMK,IAAI,GAAG,IAAIvB,YAAJ,EAAb;QACAiB,UAAU,CAACU,GAAX,CAAeJ,IAAf;QACA,MAAMD,MAAM,GAAG,EAAf;QACA,MAAMD,MAAM,GAAG;UACXC,MADW;UAEXC;QAFW,CAAf;QAIAL,aAAa,CAACU,IAAd,CAAmBP,MAAnB;QACAf,eAAe,CAACiB,IAAD,EAAOX,SAAP,EAAkB,MAAMQ,IAAI,CAACC,MAAD,CAA5B,EAAsCb,cAAtC,CAAf;MACH;IACJ,CAZD;;IAaA,IAAIK,sBAAsB,KAAK,IAA3B,IAAmCA,sBAAsB,IAAI,CAAjE,EAAoE;MAChEP,eAAe,CAACW,UAAD,EAAaL,SAAb,EAAwBc,WAAxB,EAAqCb,sBAArC,EAA6D,IAA7D,CAAf;IACH,CAFD,MAGK;MACDM,aAAa,GAAG,IAAhB;IACH;;IACDO,WAAW;IACX,MAAMG,oBAAoB,GAAG3B,wBAAwB,CAACe,UAAD,EAAca,KAAD,IAAW;MACzE,MAAMC,WAAW,GAAGb,aAAa,CAACc,KAAd,EAApB;;MACA,KAAK,MAAMX,MAAX,IAAqBU,WAArB,EAAkC;QAC9B,MAAM;UAAET;QAAF,IAAaD,MAAnB;QACAC,MAAM,CAACM,IAAP,CAAYE,KAAZ;QACAhB,aAAa,IAAIQ,MAAM,CAACW,MAAxB,IAAkCb,IAAI,CAACC,MAAD,CAAtC;MACH;IACJ,CAPoD,EAOlD,MAAM;MACL,OAAOH,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACe,MAAnF,EAA2F;QACvFhB,UAAU,CAACQ,IAAX,CAAgBP,aAAa,CAACgB,KAAd,GAAsBZ,MAAtC;MACH;;MACDO,oBAAoB,KAAK,IAAzB,IAAiCA,oBAAoB,KAAK,KAAK,CAA/D,GAAmE,KAAK,CAAxE,GAA4EA,oBAAoB,CAACL,WAArB,EAA5E;MACAP,UAAU,CAACkB,QAAX;MACAlB,UAAU,CAACO,WAAX;IACH,CAdoD,EAclDY,SAdkD,EAcvC,MAAOlB,aAAa,GAAG,IAdgB,CAArD;IAeAF,MAAM,CAACqB,SAAP,CAAiBR,oBAAjB;EACH,CA9Ca,CAAd;AA+CH"}, "metadata": {}, "sourceType": "module"}