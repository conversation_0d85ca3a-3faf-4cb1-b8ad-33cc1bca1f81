{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nconst _c0 = [\"input\"];\n\nfunction InputNumber_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 6);\n    i0.ɵɵlistener(\"click\", function InputNumber_i_3_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return {\n    \"p-inputnumber-button p-inputnumber-button-up\": true\n  };\n};\n\nconst _c2 = function () {\n  return {\n    \"p-inputnumber-button p-inputnumber-button-down\": true\n  };\n};\n\nfunction InputNumber_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 7)(1, \"button\", 8);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onUpButtonKeyUp());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 8);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_span_4_Template_button_mousedown_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_span_4_Template_button_mouseup_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_span_4_Template_button_mouseleave_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_span_4_Template_button_keydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_span_4_Template_button_keyup_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onDownButtonKeyUp());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r2.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(10, _c1))(\"icon\", ctx_r2.incrementButtonIcon)(\"disabled\", ctx_r2.disabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r2.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(11, _c2))(\"icon\", ctx_r2.decrementButtonIcon)(\"disabled\", ctx_r2.disabled);\n  }\n}\n\nfunction InputNumber_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_5_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onUpButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_5_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onUpButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_5_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onUpButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_5_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onUpButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_5_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onUpButtonKeyUp());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.incrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(5, _c1))(\"icon\", ctx_r3.incrementButtonIcon)(\"disabled\", ctx_r3.disabled);\n  }\n}\n\nfunction InputNumber_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"mousedown\", function InputNumber_button_6_Template_button_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.onDownButtonMouseDown($event));\n    })(\"mouseup\", function InputNumber_button_6_Template_button_mouseup_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onDownButtonMouseUp());\n    })(\"mouseleave\", function InputNumber_button_6_Template_button_mouseleave_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.onDownButtonMouseLeave());\n    })(\"keydown\", function InputNumber_button_6_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onDownButtonKeyDown($event));\n    })(\"keyup\", function InputNumber_button_6_Template_button_keyup_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.onDownButtonKeyUp());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r4.decrementButtonClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(5, _c2))(\"icon\", ctx_r4.decrementButtonIcon)(\"disabled\", ctx_r4.disabled);\n  }\n}\n\nconst _c3 = function (a1, a2, a3) {\n  return {\n    \"p-inputnumber p-component\": true,\n    \"p-inputnumber-buttons-stacked\": a1,\n    \"p-inputnumber-buttons-horizontal\": a2,\n    \"p-inputnumber-buttons-vertical\": a3\n  };\n};\n\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputNumber),\n  multi: true\n};\n\nclass InputNumber {\n  constructor(el, cd) {\n    this.el = el;\n    this.cd = cd;\n    this.showButtons = false;\n    this.format = true;\n    this.buttonLayout = \"stacked\";\n    this.incrementButtonIcon = 'pi pi-angle-up';\n    this.decrementButtonIcon = 'pi pi-angle-down';\n    this.readonly = false;\n    this.step = 1;\n    this.allowEmpty = true;\n    this.mode = \"decimal\";\n    this.useGrouping = true;\n    this.showClear = false;\n    this.onInput = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.onKeyDown = new EventEmitter();\n    this.onClear = new EventEmitter();\n\n    this.onModelChange = () => {};\n\n    this.onModelTouched = () => {};\n\n    this.groupChar = '';\n    this.prefixChar = '';\n    this.suffixChar = '';\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(disabled) {\n    if (disabled) this.focused = false;\n    this._disabled = disabled;\n    if (this.timer) this.clearTimer();\n  }\n\n  ngOnChanges(simpleChange) {\n    const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n\n    if (props.some(p => !!simpleChange[p])) {\n      this.updateConstructParser();\n    }\n  }\n\n  ngOnInit() {\n    this.constructParser();\n    this.initialized = true;\n  }\n\n  getOptions() {\n    return {\n      localeMatcher: this.localeMatcher,\n      style: this.mode,\n      currency: this.currency,\n      currencyDisplay: this.currencyDisplay,\n      useGrouping: this.useGrouping,\n      minimumFractionDigits: this.minFractionDigits,\n      maximumFractionDigits: this.maxFractionDigits\n    };\n  }\n\n  constructParser() {\n    this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n    const numerals = [...new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    }).format(9876543210)].reverse();\n    const index = new Map(numerals.map((d, i) => [d, i]));\n    this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n    this._group = this.getGroupingExpression();\n    this._minusSign = this.getMinusSignExpression();\n    this._currency = this.getCurrencyExpression();\n    this._decimal = this.getDecimalExpression();\n    this._suffix = this.getSuffixExpression();\n    this._prefix = this.getPrefixExpression();\n\n    this._index = d => index.get(d);\n  }\n\n  updateConstructParser() {\n    if (this.initialized) {\n      this.constructParser();\n    }\n  }\n\n  escapeRegExp(text) {\n    return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n  }\n\n  getDecimalExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, Object.assign(Object.assign({}, this.getOptions()), {\n      useGrouping: false\n    }));\n    return new RegExp(`[${formatter.format(1.1).replace(this._currency, '').trim().replace(this._numeral, '')}]`, 'g');\n  }\n\n  getGroupingExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: true\n    });\n    this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n    return new RegExp(`[${this.groupChar}]`, 'g');\n  }\n\n  getMinusSignExpression() {\n    const formatter = new Intl.NumberFormat(this.locale, {\n      useGrouping: false\n    });\n    return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n  }\n\n  getCurrencyExpression() {\n    if (this.currency) {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: 'currency',\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n    }\n\n    return new RegExp(`[]`, 'g');\n  }\n\n  getPrefixExpression() {\n    if (this.prefix) {\n      this.prefixChar = this.prefix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay\n      });\n      this.prefixChar = formatter.format(1).split('1')[0];\n    }\n\n    return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n  }\n\n  getSuffixExpression() {\n    if (this.suffix) {\n      this.suffixChar = this.suffix;\n    } else {\n      const formatter = new Intl.NumberFormat(this.locale, {\n        style: this.mode,\n        currency: this.currency,\n        currencyDisplay: this.currencyDisplay,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n      });\n      this.suffixChar = formatter.format(1).split('1')[1];\n    }\n\n    return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n  }\n\n  formatValue(value) {\n    if (value != null) {\n      if (value === '-') {\n        // Minus sign\n        return value;\n      }\n\n      if (this.format) {\n        let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n        let formattedValue = formatter.format(value);\n\n        if (this.prefix) {\n          formattedValue = this.prefix + formattedValue;\n        }\n\n        if (this.suffix) {\n          formattedValue = formattedValue + this.suffix;\n        }\n\n        return formattedValue;\n      }\n\n      return value.toString();\n    }\n\n    return '';\n  }\n\n  parseValue(text) {\n    let filteredText = text.replace(this._suffix, '').replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '').replace(this._group, '').replace(this._minusSign, '-').replace(this._decimal, '.').replace(this._numeral, this._index);\n\n    if (filteredText) {\n      if (filteredText === '-') // Minus sign\n        return filteredText;\n      let parsedValue = +filteredText;\n      return isNaN(parsedValue) ? null : parsedValue;\n    }\n\n    return null;\n  }\n\n  repeat(event, interval, dir) {\n    if (this.readonly) {\n      return;\n    }\n\n    let i = interval || 500;\n    this.clearTimer();\n    this.timer = setTimeout(() => {\n      this.repeat(event, 40, dir);\n    }, i);\n    this.spin(event, dir);\n  }\n\n  spin(event, dir) {\n    let step = this.step * dir;\n    let currentValue = this.parseValue(this.input.nativeElement.value) || 0;\n    let newValue = this.validateValue(currentValue + step);\n\n    if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n      return;\n    }\n\n    this.updateInput(newValue, null, 'spin', null);\n    this.updateModel(event, newValue);\n    this.handleOnInput(event, currentValue, newValue);\n  }\n\n  clear() {\n    this.value = null;\n    this.onModelChange(this.value);\n    this.onClear.emit();\n  }\n\n  onUpButtonMouseDown(event) {\n    this.input.nativeElement.focus();\n    this.repeat(event, null, 1);\n    event.preventDefault();\n  }\n\n  onUpButtonMouseUp() {\n    this.clearTimer();\n  }\n\n  onUpButtonMouseLeave() {\n    this.clearTimer();\n  }\n\n  onUpButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, 1);\n    }\n  }\n\n  onUpButtonKeyUp() {\n    this.clearTimer();\n  }\n\n  onDownButtonMouseDown(event) {\n    this.input.nativeElement.focus();\n    this.repeat(event, null, -1);\n    event.preventDefault();\n  }\n\n  onDownButtonMouseUp() {\n    this.clearTimer();\n  }\n\n  onDownButtonMouseLeave() {\n    this.clearTimer();\n  }\n\n  onDownButtonKeyUp() {\n    this.clearTimer();\n  }\n\n  onDownButtonKeyDown(event) {\n    if (event.keyCode === 32 || event.keyCode === 13) {\n      this.repeat(event, null, -1);\n    }\n  }\n\n  onUserInput(event) {\n    if (this.readonly) {\n      return;\n    }\n\n    if (this.isSpecialChar) {\n      event.target.value = this.lastValue;\n    }\n\n    this.isSpecialChar = false;\n  }\n\n  onInputKeyDown(event) {\n    if (this.readonly) {\n      return;\n    }\n\n    this.lastValue = event.target.value;\n\n    if (event.shiftKey || event.altKey) {\n      this.isSpecialChar = true;\n      return;\n    }\n\n    let selectionStart = event.target.selectionStart;\n    let selectionEnd = event.target.selectionEnd;\n    let inputValue = event.target.value;\n    let newValueStr = null;\n\n    if (event.altKey) {\n      event.preventDefault();\n    }\n\n    switch (event.which) {\n      //up\n      case 38:\n        this.spin(event, 1);\n        event.preventDefault();\n        break;\n      //down\n\n      case 40:\n        this.spin(event, -1);\n        event.preventDefault();\n        break;\n      //left\n\n      case 37:\n        if (!this.isNumeralChar(inputValue.charAt(selectionStart - 1))) {\n          event.preventDefault();\n        }\n\n        break;\n      //right\n\n      case 39:\n        if (!this.isNumeralChar(inputValue.charAt(selectionStart))) {\n          event.preventDefault();\n        }\n\n        break;\n      //enter\n\n      case 13:\n        newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        this.input.nativeElement.value = this.formatValue(newValueStr);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n        this.updateModel(event, newValueStr);\n        break;\n      //backspace\n\n      case 8:\n        {\n          event.preventDefault();\n\n          if (selectionStart === selectionEnd) {\n            const deleteChar = inputValue.charAt(selectionStart - 1);\n            const {\n              decimalCharIndex,\n              decimalCharIndexWithoutPrefix\n            } = this.getDecimalCharIndexes(inputValue);\n\n            if (this.isNumeralChar(deleteChar)) {\n              const decimalLength = this.getDecimalLength(inputValue);\n\n              if (this._group.test(deleteChar)) {\n                this._group.lastIndex = 0;\n                newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n              } else if (this._decimal.test(deleteChar)) {\n                this._decimal.lastIndex = 0;\n\n                if (decimalLength) {\n                  this.input.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                } else {\n                  newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                }\n              } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n              } else if (decimalCharIndexWithoutPrefix === 1) {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n              }\n            }\n\n            this.updateValue(event, newValueStr, null, 'delete-single');\n          } else {\n            newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n            this.updateValue(event, newValueStr, null, 'delete-range');\n          }\n\n          break;\n        }\n      // del\n\n      case 46:\n        event.preventDefault();\n\n        if (selectionStart === selectionEnd) {\n          const deleteChar = inputValue.charAt(selectionStart);\n          const {\n            decimalCharIndex,\n            decimalCharIndexWithoutPrefix\n          } = this.getDecimalCharIndexes(inputValue);\n\n          if (this.isNumeralChar(deleteChar)) {\n            const decimalLength = this.getDecimalLength(inputValue);\n\n            if (this._group.test(deleteChar)) {\n              this._group.lastIndex = 0;\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n            } else if (this._decimal.test(deleteChar)) {\n              this._decimal.lastIndex = 0;\n\n              if (decimalLength) {\n                this.input.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n              } else {\n                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n              }\n            } else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n              const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n              newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n            } else if (decimalCharIndexWithoutPrefix === 1) {\n              newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n              newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n            } else {\n              newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n            }\n          }\n\n          this.updateValue(event, newValueStr, null, 'delete-back-single');\n        } else {\n          newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n          this.updateValue(event, newValueStr, null, 'delete-range');\n        }\n\n        break;\n\n      default:\n        break;\n    }\n\n    this.onKeyDown.emit(event);\n  }\n\n  onInputKeyPress(event) {\n    if (this.readonly) {\n      return;\n    }\n\n    event.preventDefault();\n    let code = event.which || event.keyCode;\n    let char = String.fromCharCode(code);\n    const isDecimalSign = this.isDecimalSign(char);\n    const isMinusSign = this.isMinusSign(char);\n\n    if (48 <= code && code <= 57 || isMinusSign || isDecimalSign) {\n      this.insert(event, char, {\n        isDecimalSign,\n        isMinusSign\n      });\n    }\n  }\n\n  onPaste(event) {\n    if (!this.disabled && !this.readonly) {\n      event.preventDefault();\n      let data = (event.clipboardData || window['clipboardData']).getData('Text');\n\n      if (data) {\n        let filteredData = this.parseValue(data);\n\n        if (filteredData != null) {\n          this.insert(event, filteredData.toString());\n        }\n      }\n    }\n  }\n\n  allowMinusSign() {\n    return this.min == null || this.min < 0;\n  }\n\n  isMinusSign(char) {\n    if (this._minusSign.test(char) || char === '-') {\n      this._minusSign.lastIndex = 0;\n      return true;\n    }\n\n    return false;\n  }\n\n  isDecimalSign(char) {\n    if (this._decimal.test(char)) {\n      this._decimal.lastIndex = 0;\n      return true;\n    }\n\n    return false;\n  }\n\n  isDecimalMode() {\n    return this.mode === 'decimal';\n  }\n\n  getDecimalCharIndexes(val) {\n    let decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const filteredVal = val.replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '');\n    const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      decimalCharIndexWithoutPrefix\n    };\n  }\n\n  getCharIndexes(val) {\n    const decimalCharIndex = val.search(this._decimal);\n    this._decimal.lastIndex = 0;\n    const minusCharIndex = val.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n    const suffixCharIndex = val.search(this._suffix);\n    this._suffix.lastIndex = 0;\n    const currencyCharIndex = val.search(this._currency);\n    this._currency.lastIndex = 0;\n    return {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    };\n  }\n\n  insert(event, text, sign = {\n    isDecimalSign: false,\n    isMinusSign: false\n  }) {\n    const minusCharIndexOnText = text.search(this._minusSign);\n    this._minusSign.lastIndex = 0;\n\n    if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n      return;\n    }\n\n    let selectionStart = this.input.nativeElement.selectionStart;\n    let selectionEnd = this.input.nativeElement.selectionEnd;\n    let inputValue = this.input.nativeElement.value.trim();\n    const {\n      decimalCharIndex,\n      minusCharIndex,\n      suffixCharIndex,\n      currencyCharIndex\n    } = this.getCharIndexes(inputValue);\n    let newValueStr;\n\n    if (sign.isMinusSign) {\n      if (selectionStart === 0) {\n        newValueStr = inputValue;\n\n        if (minusCharIndex === -1 || selectionEnd !== 0) {\n          newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n        }\n\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else if (sign.isDecimalSign) {\n      if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n        this.updateValue(event, inputValue, text, 'insert');\n      } else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      } else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, 'insert');\n      }\n    } else {\n      const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n      const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n\n      if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n        if (selectionStart + text.length - (decimalCharIndex + 1) <= maxFractionDigits) {\n          const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length;\n          newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n          this.updateValue(event, newValueStr, text, operation);\n        }\n      } else {\n        newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n        this.updateValue(event, newValueStr, text, operation);\n      }\n    }\n  }\n\n  insertText(value, text, start, end) {\n    let textSplit = text === '.' ? text : text.split('.');\n\n    if (textSplit.length === 2) {\n      const decimalCharIndex = value.slice(start, end).search(this._decimal);\n      this._decimal.lastIndex = 0;\n      return decimalCharIndex > 0 ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : value || this.formatValue(text);\n    } else if (end - start === value.length) {\n      return this.formatValue(text);\n    } else if (start === 0) {\n      return text + value.slice(end);\n    } else if (end === value.length) {\n      return value.slice(0, start) + text;\n    } else {\n      return value.slice(0, start) + text + value.slice(end);\n    }\n  }\n\n  deleteRange(value, start, end) {\n    let newValueStr;\n    if (end - start === value.length) newValueStr = '';else if (start === 0) newValueStr = value.slice(end);else if (end === value.length) newValueStr = value.slice(0, start);else newValueStr = value.slice(0, start) + value.slice(end);\n    return newValueStr;\n  }\n\n  initCursor() {\n    let selectionStart = this.input.nativeElement.selectionStart;\n    let inputValue = this.input.nativeElement.value;\n    let valueLength = inputValue.length;\n    let index = null; // remove prefix\n\n    let prefixLength = (this.prefixChar || '').length;\n    inputValue = inputValue.replace(this._prefix, '');\n    selectionStart = selectionStart - prefixLength;\n    let char = inputValue.charAt(selectionStart);\n\n    if (this.isNumeralChar(char)) {\n      return selectionStart + prefixLength;\n    } //left\n\n\n    let i = selectionStart - 1;\n\n    while (i >= 0) {\n      char = inputValue.charAt(i);\n\n      if (this.isNumeralChar(char)) {\n        index = i + prefixLength;\n        break;\n      } else {\n        i--;\n      }\n    }\n\n    if (index !== null) {\n      this.input.nativeElement.setSelectionRange(index + 1, index + 1);\n    } else {\n      i = selectionStart;\n\n      while (i < valueLength) {\n        char = inputValue.charAt(i);\n\n        if (this.isNumeralChar(char)) {\n          index = i + prefixLength;\n          break;\n        } else {\n          i++;\n        }\n      }\n\n      if (index !== null) {\n        this.input.nativeElement.setSelectionRange(index, index);\n      }\n    }\n\n    return index || 0;\n  }\n\n  onInputClick() {\n    if (!this.readonly) {\n      this.initCursor();\n    }\n  }\n\n  isNumeralChar(char) {\n    if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n      this.resetRegex();\n      return true;\n    }\n\n    return false;\n  }\n\n  resetRegex() {\n    this._numeral.lastIndex = 0;\n    this._decimal.lastIndex = 0;\n    this._group.lastIndex = 0;\n    this._minusSign.lastIndex = 0;\n  }\n\n  updateValue(event, valueStr, insertedValueStr, operation) {\n    let currentValue = this.input.nativeElement.value;\n    let newValue = null;\n\n    if (valueStr != null) {\n      newValue = this.parseValue(valueStr);\n      newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n      this.updateInput(newValue, insertedValueStr, operation, valueStr);\n      this.handleOnInput(event, currentValue, newValue);\n    }\n  }\n\n  handleOnInput(event, currentValue, newValue) {\n    if (this.isValueChanged(currentValue, newValue)) {\n      this.onInput.emit({\n        originalEvent: event,\n        value: newValue\n      });\n    }\n  }\n\n  isValueChanged(currentValue, newValue) {\n    if (newValue === null && currentValue !== null) {\n      return true;\n    }\n\n    if (newValue != null) {\n      let parsedCurrentValue = typeof currentValue === 'string' ? this.parseValue(currentValue) : currentValue;\n      return newValue !== parsedCurrentValue;\n    }\n\n    return false;\n  }\n\n  validateValue(value) {\n    if (value === '-' || value == null) {\n      return null;\n    }\n\n    if (this.min != null && value < this.min) {\n      return this.min;\n    }\n\n    if (this.max != null && value > this.max) {\n      return this.max;\n    }\n\n    return value;\n  }\n\n  updateInput(value, insertedValueStr, operation, valueStr) {\n    insertedValueStr = insertedValueStr || '';\n    let inputValue = this.input.nativeElement.value;\n    let newValue = this.formatValue(value);\n    let currentLength = inputValue.length;\n\n    if (newValue !== valueStr) {\n      newValue = this.concatValues(newValue, valueStr);\n    }\n\n    if (currentLength === 0) {\n      this.input.nativeElement.value = newValue;\n      this.input.nativeElement.setSelectionRange(0, 0);\n      const index = this.initCursor();\n      const selectionEnd = index + insertedValueStr.length;\n      this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n    } else {\n      let selectionStart = this.input.nativeElement.selectionStart;\n      let selectionEnd = this.input.nativeElement.selectionEnd;\n\n      if (this.maxlength && this.maxlength < newValue.length) {\n        return;\n      }\n\n      this.input.nativeElement.value = newValue;\n      let newLength = newValue.length;\n\n      if (operation === 'range-insert') {\n        const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n        const startValueStr = startValue !== null ? startValue.toString() : '';\n        const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n        const sRegex = new RegExp(startExpr, 'g');\n        sRegex.test(newValue);\n        const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n        const tRegex = new RegExp(tExpr, 'g');\n        tRegex.test(newValue.slice(sRegex.lastIndex));\n        selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (newLength === currentLength) {\n        if (operation === 'insert' || operation === 'delete-back-single') this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);else if (operation === 'delete-single') this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);else if (operation === 'delete-range' || operation === 'spin') this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (operation === 'delete-back-single') {\n        let prevChar = inputValue.charAt(selectionEnd - 1);\n        let nextChar = inputValue.charAt(selectionEnd);\n        let diff = currentLength - newLength;\n\n        let isGroupChar = this._group.test(nextChar);\n\n        if (isGroupChar && diff === 1) {\n          selectionEnd += 1;\n        } else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n          selectionEnd += -1 * diff + 1;\n        }\n\n        this._group.lastIndex = 0;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else if (inputValue === '-' && operation === 'insert') {\n        this.input.nativeElement.setSelectionRange(0, 0);\n        const index = this.initCursor();\n        const selectionEnd = index + insertedValueStr.length + 1;\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      } else {\n        selectionEnd = selectionEnd + (newLength - currentLength);\n        this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n      }\n    }\n\n    this.input.nativeElement.setAttribute('aria-valuenow', value);\n  }\n\n  concatValues(val1, val2) {\n    if (val1 && val2) {\n      let decimalCharIndex = val2.search(this._decimal);\n      this._decimal.lastIndex = 0;\n      return decimalCharIndex !== -1 ? val1.split(this._decimal)[0] + val2.slice(decimalCharIndex) : val1;\n    }\n\n    return val1;\n  }\n\n  getDecimalLength(value) {\n    if (value) {\n      const valueSplit = value.split(this._decimal);\n\n      if (valueSplit.length === 2) {\n        return valueSplit[1].replace(this._suffix, '').trim().replace(/\\s/g, '').replace(this._currency, '').length;\n      }\n    }\n\n    return 0;\n  }\n\n  onInputFocus(event) {\n    this.focused = true;\n    this.onFocus.emit(event);\n  }\n\n  onInputBlur(event) {\n    this.focused = false;\n    let newValue = this.validateValue(this.parseValue(this.input.nativeElement.value));\n    this.input.nativeElement.value = this.formatValue(newValue);\n    this.input.nativeElement.setAttribute('aria-valuenow', newValue);\n    this.updateModel(event, newValue);\n    this.onBlur.emit(event);\n  }\n\n  formattedValue() {\n    const val = !this.value && !this.allowEmpty ? 0 : this.value;\n    return this.formatValue(val);\n  }\n\n  updateModel(event, value) {\n    if (this.value !== value) {\n      this.value = value;\n      this.onModelChange(value);\n    }\n\n    this.onModelTouched();\n  }\n\n  writeValue(value) {\n    this.value = value;\n    this.cd.markForCheck();\n  }\n\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n\n  get filled() {\n    return this.value != null && this.value.toString().length > 0;\n  }\n\n  clearTimer() {\n    if (this.timer) {\n      clearInterval(this.timer);\n    }\n  }\n\n  getFormatter() {\n    return this.numberFormat;\n  }\n\n}\n\nInputNumber.ɵfac = function InputNumber_Factory(t) {\n  return new (t || InputNumber)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nInputNumber.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: InputNumber,\n  selectors: [[\"p-inputNumber\"]],\n  viewQuery: function InputNumber_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n  hostVars: 6,\n  hostBindings: function InputNumber_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused)(\"p-inputnumber-clearable\", ctx.showClear && ctx.buttonLayout != \"vertical\");\n    }\n  },\n  inputs: {\n    showButtons: \"showButtons\",\n    format: \"format\",\n    buttonLayout: \"buttonLayout\",\n    inputId: \"inputId\",\n    styleClass: \"styleClass\",\n    style: \"style\",\n    placeholder: \"placeholder\",\n    size: \"size\",\n    maxlength: \"maxlength\",\n    tabindex: \"tabindex\",\n    title: \"title\",\n    ariaLabel: \"ariaLabel\",\n    ariaRequired: \"ariaRequired\",\n    name: \"name\",\n    required: \"required\",\n    autocomplete: \"autocomplete\",\n    min: \"min\",\n    max: \"max\",\n    incrementButtonClass: \"incrementButtonClass\",\n    decrementButtonClass: \"decrementButtonClass\",\n    incrementButtonIcon: \"incrementButtonIcon\",\n    decrementButtonIcon: \"decrementButtonIcon\",\n    readonly: \"readonly\",\n    step: \"step\",\n    allowEmpty: \"allowEmpty\",\n    locale: \"locale\",\n    localeMatcher: \"localeMatcher\",\n    mode: \"mode\",\n    currency: \"currency\",\n    currencyDisplay: \"currencyDisplay\",\n    useGrouping: \"useGrouping\",\n    minFractionDigits: \"minFractionDigits\",\n    maxFractionDigits: \"maxFractionDigits\",\n    prefix: \"prefix\",\n    suffix: \"suffix\",\n    inputStyle: \"inputStyle\",\n    inputStyleClass: \"inputStyleClass\",\n    showClear: \"showClear\",\n    disabled: \"disabled\"\n  },\n  outputs: {\n    onInput: \"onInput\",\n    onFocus: \"onFocus\",\n    onBlur: \"onBlur\",\n    onKeyDown: \"onKeyDown\",\n    onClear: \"onClear\"\n  },\n  features: [i0.ɵɵProvidersFeature([INPUTNUMBER_VALUE_ACCESSOR]), i0.ɵɵNgOnChangesFeature],\n  decls: 7,\n  vars: 32,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"pInputText\", \"\", \"inputmode\", \"decimal\", 3, \"ngClass\", \"ngStyle\", \"value\", \"disabled\", \"readonly\", \"input\", \"keydown\", \"keypress\", \"paste\", \"click\", \"focus\", \"blur\"], [\"input\", \"\"], [\"class\", \"p-inputnumber-clear-icon pi pi-times\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-inputnumber-button-group\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"ngClass\", \"class\", \"icon\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\", 4, \"ngIf\"], [1, \"p-inputnumber-clear-icon\", \"pi\", \"pi-times\", 3, \"click\"], [1, \"p-inputnumber-button-group\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"ngClass\", \"icon\", \"disabled\", \"mousedown\", \"mouseup\", \"mouseleave\", \"keydown\", \"keyup\"]],\n  template: function InputNumber_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"span\", 0)(1, \"input\", 1, 2);\n      i0.ɵɵlistener(\"input\", function InputNumber_Template_input_input_1_listener($event) {\n        return ctx.onUserInput($event);\n      })(\"keydown\", function InputNumber_Template_input_keydown_1_listener($event) {\n        return ctx.onInputKeyDown($event);\n      })(\"keypress\", function InputNumber_Template_input_keypress_1_listener($event) {\n        return ctx.onInputKeyPress($event);\n      })(\"paste\", function InputNumber_Template_input_paste_1_listener($event) {\n        return ctx.onPaste($event);\n      })(\"click\", function InputNumber_Template_input_click_1_listener() {\n        return ctx.onInputClick();\n      })(\"focus\", function InputNumber_Template_input_focus_1_listener($event) {\n        return ctx.onInputFocus($event);\n      })(\"blur\", function InputNumber_Template_input_blur_1_listener($event) {\n        return ctx.onInputBlur($event);\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(3, InputNumber_i_3_Template, 1, 0, \"i\", 3);\n      i0.ɵɵtemplate(4, InputNumber_span_4_Template, 3, 12, \"span\", 4);\n      i0.ɵɵtemplate(5, InputNumber_button_5_Template, 1, 6, \"button\", 5);\n      i0.ɵɵtemplate(6, InputNumber_button_6_Template, 1, 6, \"button\", 5);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(28, _c3, ctx.showButtons && ctx.buttonLayout === \"stacked\", ctx.showButtons && ctx.buttonLayout === \"horizontal\", ctx.showButtons && ctx.buttonLayout === \"vertical\"))(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMap(ctx.inputStyleClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-inputnumber-input\")(\"ngStyle\", ctx.inputStyle)(\"value\", ctx.formattedValue())(\"disabled\", ctx.disabled)(\"readonly\", ctx.readonly);\n      i0.ɵɵattribute(\"placeholder\", ctx.placeholder)(\"title\", ctx.title)(\"id\", ctx.inputId)(\"size\", ctx.size)(\"name\", ctx.name)(\"autocomplete\", ctx.autocomplete)(\"maxlength\", ctx.maxlength)(\"tabindex\", ctx.tabindex)(\"aria-label\", ctx.ariaLabel)(\"aria-required\", ctx.ariaRequired)(\"required\", ctx.required)(\"min\", ctx.min)(\"max\", ctx.max);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.buttonLayout != \"vertical\" && ctx.showClear && ctx.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout === \"stacked\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showButtons && ctx.buttonLayout !== \"stacked\");\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, i2.InputText, i3.ButtonDirective],\n  styles: [\"p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumber, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputNumber',\n      template: `\n        <span [ngClass]=\"{'p-inputnumber p-component': true,'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal', 'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'}\"\n                [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input #input [ngClass]=\"'p-inputnumber-input'\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" pInputText [value]=\"formattedValue()\" [attr.placeholder]=\"placeholder\" [attr.title]=\"title\" [attr.id]=\"inputId\"\n                [attr.size]=\"size\" [attr.name]=\"name\" [attr.autocomplete]=\"autocomplete\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-required]=\"ariaRequired\" [disabled]=\"disabled\" [attr.required]=\"required\" [attr.min]=\"min\" [attr.max]=\"max\" [readonly]=\"readonly\" inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\" (keydown)=\"onInputKeyDown($event)\" (keypress)=\"onInputKeyPress($event)\" (paste)=\"onPaste($event)\" (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i *ngIf=\"buttonLayout != 'vertical' && showClear && value\" class=\"p-inputnumber-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\">\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n            </span>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [INPUTNUMBER_VALUE_ACCESSOR],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused',\n        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n      },\n      styles: [\"p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    showButtons: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    buttonLayout: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    maxlength: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    title: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaRequired: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    autocomplete: [{\n      type: Input\n    }],\n    min: [{\n      type: Input\n    }],\n    max: [{\n      type: Input\n    }],\n    incrementButtonClass: [{\n      type: Input\n    }],\n    decrementButtonClass: [{\n      type: Input\n    }],\n    incrementButtonIcon: [{\n      type: Input\n    }],\n    decrementButtonIcon: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    allowEmpty: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    localeMatcher: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    currency: [{\n      type: Input\n    }],\n    currencyDisplay: [{\n      type: Input\n    }],\n    useGrouping: [{\n      type: Input\n    }],\n    minFractionDigits: [{\n      type: Input\n    }],\n    maxFractionDigits: [{\n      type: Input\n    }],\n    prefix: [{\n      type: Input\n    }],\n    suffix: [{\n      type: Input\n    }],\n    inputStyle: [{\n      type: Input\n    }],\n    inputStyleClass: [{\n      type: Input\n    }],\n    showClear: [{\n      type: Input\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    onInput: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onKeyDown: [{\n      type: Output\n    }],\n    onClear: [{\n      type: Output\n    }],\n    disabled: [{\n      type: Input\n    }]\n  });\n})();\n\nclass InputNumberModule {}\n\nInputNumberModule.ɵfac = function InputNumberModule_Factory(t) {\n  return new (t || InputNumberModule)();\n};\n\nInputNumberModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: InputNumberModule\n});\nInputNumberModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, InputTextModule, ButtonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputNumberModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, InputTextModule, ButtonModule],\n      exports: [InputNumber],\n      declarations: [InputNumber]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "Output", "NgModule", "i1", "CommonModule", "i2", "InputTextModule", "i3", "ButtonModule", "NG_VALUE_ACCESSOR", "INPUTNUMBER_VALUE_ACCESSOR", "provide", "useExisting", "InputNumber", "multi", "constructor", "el", "cd", "showButtons", "format", "buttonLayout", "incrementButtonIcon", "decrementButtonIcon", "readonly", "step", "allowEmpty", "mode", "useGrouping", "showClear", "onInput", "onFocus", "onBlur", "onKeyDown", "onClear", "onModelChange", "onModelTouched", "groupChar", "prefixChar", "suffixChar", "disabled", "_disabled", "focused", "timer", "clearTimer", "ngOnChanges", "simpleChange", "props", "some", "p", "updateConstructParser", "ngOnInit", "<PERSON><PERSON><PERSON><PERSON>", "initialized", "getOptions", "localeMatcher", "style", "currency", "currencyDisplay", "minimumFractionDigits", "minFractionDigits", "maximumFractionDigits", "maxFractionDigits", "numberFormat", "Intl", "NumberFormat", "locale", "numerals", "reverse", "index", "Map", "map", "d", "i", "_numeral", "RegExp", "join", "_group", "getGroupingExpression", "_minusSign", "getMinusSignExpression", "_currency", "getCurrencyExpression", "_decimal", "getDecimalExpression", "_suffix", "getSuffixExpression", "_prefix", "getPrefixExpression", "_index", "get", "escapeRegExp", "text", "replace", "formatter", "Object", "assign", "trim", "char<PERSON>t", "prefix", "split", "suffix", "formatValue", "value", "formattedValue", "toString", "parseValue", "filteredText", "parsedValue", "isNaN", "repeat", "event", "interval", "dir", "setTimeout", "spin", "currentValue", "input", "nativeElement", "newValue", "validate<PERSON><PERSON>ue", "maxlength", "length", "updateInput", "updateModel", "handleOnInput", "clear", "emit", "onUpButtonMouseDown", "focus", "preventDefault", "onUpButtonMouseUp", "onUpButtonMouseLeave", "onUpButtonKeyDown", "keyCode", "onUpButtonKeyUp", "onDownButtonMouseDown", "onDownButtonMouseUp", "onDownButtonMouseLeave", "onDownButtonKeyUp", "onDownButtonKeyDown", "onUserInput", "isSpecialChar", "target", "lastValue", "onInputKeyDown", "shift<PERSON>ey", "altKey", "selectionStart", "selectionEnd", "inputValue", "newValueStr", "which", "isNumeralChar", "setAttribute", "deleteChar", "decimalCharIndex", "decimalCharIndexWithoutPrefix", "getDecimalCharIndexes", "decimalLength", "getDecimalLength", "test", "lastIndex", "slice", "setSelectionRange", "insertedText", "isDecimalMode", "updateValue", "deleteRange", "onInputKeyPress", "code", "char", "String", "fromCharCode", "isDecimalSign", "isMinusSign", "insert", "onPaste", "data", "clipboardData", "window", "getData", "filteredData", "allowMinusSign", "min", "val", "search", "filteredVal", "getCharIndexes", "minusCharIndex", "suffixCharIndex", "currencyCharIndex", "sign", "minusCharIndexOnText", "insertText", "resolvedOptions", "operation", "charIndex", "start", "end", "textSplit", "initCursor", "valueLength", "prefixLength", "onInputClick", "resetRegex", "valueStr", "insertedValueStr", "isValueChanged", "originalEvent", "parsedCurrentValue", "max", "<PERSON><PERSON><PERSON><PERSON>", "concat<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "startValue", "startValueStr", "startExpr", "sRegex", "tExpr", "tRegex", "prevChar", "nextChar", "diff", "isGroupChar", "val1", "val2", "valueSplit", "onInputFocus", "onInputBlur", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "filled", "clearInterval", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵfac", "ElementRef", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "InputText", "ButtonDirective", "type", "args", "selector", "template", "changeDetection", "OnPush", "providers", "encapsulation", "None", "host", "styles", "inputId", "styleClass", "placeholder", "size", "tabindex", "title", "aria<PERSON><PERSON><PERSON>", "ariaRequired", "name", "required", "autocomplete", "incrementButtonClass", "decrementButtonClass", "inputStyle", "inputStyleClass", "InputNumberModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-inputnumber.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from 'primeng/inputtext';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\n\nconst INPUTNUMBER_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputNumber),\n    multi: true\n};\nclass InputNumber {\n    constructor(el, cd) {\n        this.el = el;\n        this.cd = cd;\n        this.showButtons = false;\n        this.format = true;\n        this.buttonLayout = \"stacked\";\n        this.incrementButtonIcon = 'pi pi-angle-up';\n        this.decrementButtonIcon = 'pi pi-angle-down';\n        this.readonly = false;\n        this.step = 1;\n        this.allowEmpty = true;\n        this.mode = \"decimal\";\n        this.useGrouping = true;\n        this.showClear = false;\n        this.onInput = new EventEmitter();\n        this.onFocus = new EventEmitter();\n        this.onBlur = new EventEmitter();\n        this.onKeyDown = new EventEmitter();\n        this.onClear = new EventEmitter();\n        this.onModelChange = () => { };\n        this.onModelTouched = () => { };\n        this.groupChar = '';\n        this.prefixChar = '';\n        this.suffixChar = '';\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(disabled) {\n        if (disabled)\n            this.focused = false;\n        this._disabled = disabled;\n        if (this.timer)\n            this.clearTimer();\n    }\n    ngOnChanges(simpleChange) {\n        const props = ['locale', 'localeMatcher', 'mode', 'currency', 'currencyDisplay', 'useGrouping', 'minFractionDigits', 'maxFractionDigits', 'prefix', 'suffix'];\n        if (props.some(p => !!simpleChange[p])) {\n            this.updateConstructParser();\n        }\n    }\n    ngOnInit() {\n        this.constructParser();\n        this.initialized = true;\n    }\n    getOptions() {\n        return {\n            localeMatcher: this.localeMatcher,\n            style: this.mode,\n            currency: this.currency,\n            currencyDisplay: this.currencyDisplay,\n            useGrouping: this.useGrouping,\n            minimumFractionDigits: this.minFractionDigits,\n            maximumFractionDigits: this.maxFractionDigits\n        };\n    }\n    constructParser() {\n        this.numberFormat = new Intl.NumberFormat(this.locale, this.getOptions());\n        const numerals = [...new Intl.NumberFormat(this.locale, { useGrouping: false }).format(9876543210)].reverse();\n        const index = new Map(numerals.map((d, i) => [d, i]));\n        this._numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n        this._group = this.getGroupingExpression();\n        this._minusSign = this.getMinusSignExpression();\n        this._currency = this.getCurrencyExpression();\n        this._decimal = this.getDecimalExpression();\n        this._suffix = this.getSuffixExpression();\n        this._prefix = this.getPrefixExpression();\n        this._index = d => index.get(d);\n    }\n    updateConstructParser() {\n        if (this.initialized) {\n            this.constructParser();\n        }\n    }\n    escapeRegExp(text) {\n        return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n    }\n    getDecimalExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, Object.assign(Object.assign({}, this.getOptions()), { useGrouping: false }));\n        return new RegExp(`[${formatter.format(1.1).replace(this._currency, '').trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getGroupingExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: true });\n        this.groupChar = formatter.format(1000000).trim().replace(this._numeral, '').charAt(0);\n        return new RegExp(`[${this.groupChar}]`, 'g');\n    }\n    getMinusSignExpression() {\n        const formatter = new Intl.NumberFormat(this.locale, { useGrouping: false });\n        return new RegExp(`[${formatter.format(-1).trim().replace(this._numeral, '')}]`, 'g');\n    }\n    getCurrencyExpression() {\n        if (this.currency) {\n            const formatter = new Intl.NumberFormat(this.locale, { style: 'currency', currency: this.currency, currencyDisplay: this.currencyDisplay,\n                minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            return new RegExp(`[${formatter.format(1).replace(/\\s/g, '').replace(this._numeral, '').replace(this._group, '')}]`, 'g');\n        }\n        return new RegExp(`[]`, 'g');\n    }\n    getPrefixExpression() {\n        if (this.prefix) {\n            this.prefixChar = this.prefix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay });\n            this.prefixChar = formatter.format(1).split('1')[0];\n        }\n        return new RegExp(`${this.escapeRegExp(this.prefixChar || '')}`, 'g');\n    }\n    getSuffixExpression() {\n        if (this.suffix) {\n            this.suffixChar = this.suffix;\n        }\n        else {\n            const formatter = new Intl.NumberFormat(this.locale, { style: this.mode, currency: this.currency, currencyDisplay: this.currencyDisplay,\n                minimumFractionDigits: 0, maximumFractionDigits: 0 });\n            this.suffixChar = formatter.format(1).split('1')[1];\n        }\n        return new RegExp(`${this.escapeRegExp(this.suffixChar || '')}`, 'g');\n    }\n    formatValue(value) {\n        if (value != null) {\n            if (value === '-') { // Minus sign\n                return value;\n            }\n            if (this.format) {\n                let formatter = new Intl.NumberFormat(this.locale, this.getOptions());\n                let formattedValue = formatter.format(value);\n                if (this.prefix) {\n                    formattedValue = this.prefix + formattedValue;\n                }\n                if (this.suffix) {\n                    formattedValue = formattedValue + this.suffix;\n                }\n                return formattedValue;\n            }\n            return value.toString();\n        }\n        return '';\n    }\n    parseValue(text) {\n        let filteredText = text\n            .replace(this._suffix, '')\n            .replace(this._prefix, '')\n            .trim()\n            .replace(/\\s/g, '')\n            .replace(this._currency, '')\n            .replace(this._group, '')\n            .replace(this._minusSign, '-')\n            .replace(this._decimal, '.')\n            .replace(this._numeral, this._index);\n        if (filteredText) {\n            if (filteredText === '-') // Minus sign\n                return filteredText;\n            let parsedValue = +filteredText;\n            return isNaN(parsedValue) ? null : parsedValue;\n        }\n        return null;\n    }\n    repeat(event, interval, dir) {\n        if (this.readonly) {\n            return;\n        }\n        let i = interval || 500;\n        this.clearTimer();\n        this.timer = setTimeout(() => {\n            this.repeat(event, 40, dir);\n        }, i);\n        this.spin(event, dir);\n    }\n    spin(event, dir) {\n        let step = this.step * dir;\n        let currentValue = this.parseValue(this.input.nativeElement.value) || 0;\n        let newValue = this.validateValue(currentValue + step);\n        if (this.maxlength && this.maxlength < this.formatValue(newValue).length) {\n            return;\n        }\n        this.updateInput(newValue, null, 'spin', null);\n        this.updateModel(event, newValue);\n        this.handleOnInput(event, currentValue, newValue);\n    }\n    clear() {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n    onUpButtonMouseDown(event) {\n        this.input.nativeElement.focus();\n        this.repeat(event, null, 1);\n        event.preventDefault();\n    }\n    onUpButtonMouseUp() {\n        this.clearTimer();\n    }\n    onUpButtonMouseLeave() {\n        this.clearTimer();\n    }\n    onUpButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, 1);\n        }\n    }\n    onUpButtonKeyUp() {\n        this.clearTimer();\n    }\n    onDownButtonMouseDown(event) {\n        this.input.nativeElement.focus();\n        this.repeat(event, null, -1);\n        event.preventDefault();\n    }\n    onDownButtonMouseUp() {\n        this.clearTimer();\n    }\n    onDownButtonMouseLeave() {\n        this.clearTimer();\n    }\n    onDownButtonKeyUp() {\n        this.clearTimer();\n    }\n    onDownButtonKeyDown(event) {\n        if (event.keyCode === 32 || event.keyCode === 13) {\n            this.repeat(event, null, -1);\n        }\n    }\n    onUserInput(event) {\n        if (this.readonly) {\n            return;\n        }\n        if (this.isSpecialChar) {\n            event.target.value = this.lastValue;\n        }\n        this.isSpecialChar = false;\n    }\n    onInputKeyDown(event) {\n        if (this.readonly) {\n            return;\n        }\n        this.lastValue = event.target.value;\n        if (event.shiftKey || event.altKey) {\n            this.isSpecialChar = true;\n            return;\n        }\n        let selectionStart = event.target.selectionStart;\n        let selectionEnd = event.target.selectionEnd;\n        let inputValue = event.target.value;\n        let newValueStr = null;\n        if (event.altKey) {\n            event.preventDefault();\n        }\n        switch (event.which) {\n            //up\n            case 38:\n                this.spin(event, 1);\n                event.preventDefault();\n                break;\n            //down\n            case 40:\n                this.spin(event, -1);\n                event.preventDefault();\n                break;\n            //left\n            case 37:\n                if (!this.isNumeralChar(inputValue.charAt(selectionStart - 1))) {\n                    event.preventDefault();\n                }\n                break;\n            //right\n            case 39:\n                if (!this.isNumeralChar(inputValue.charAt(selectionStart))) {\n                    event.preventDefault();\n                }\n                break;\n            //enter\n            case 13:\n                newValueStr = this.validateValue(this.parseValue(this.input.nativeElement.value));\n                this.input.nativeElement.value = this.formatValue(newValueStr);\n                this.input.nativeElement.setAttribute('aria-valuenow', newValueStr);\n                this.updateModel(event, newValueStr);\n                break;\n            //backspace\n            case 8: {\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    const deleteChar = inputValue.charAt(selectionStart - 1);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart - 2) + inputValue.slice(selectionStart - 1);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input.nativeElement.setSelectionRange(selectionStart - 1, selectionStart - 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + insertedText + inputValue.slice(selectionStart);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + '0' + inputValue.slice(selectionStart);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart - 1) + inputValue.slice(selectionStart);\n                        }\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            }\n            // del\n            case 46:\n                event.preventDefault();\n                if (selectionStart === selectionEnd) {\n                    const deleteChar = inputValue.charAt(selectionStart);\n                    const { decimalCharIndex, decimalCharIndexWithoutPrefix } = this.getDecimalCharIndexes(inputValue);\n                    if (this.isNumeralChar(deleteChar)) {\n                        const decimalLength = this.getDecimalLength(inputValue);\n                        if (this._group.test(deleteChar)) {\n                            this._group.lastIndex = 0;\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 2);\n                        }\n                        else if (this._decimal.test(deleteChar)) {\n                            this._decimal.lastIndex = 0;\n                            if (decimalLength) {\n                                this.input.nativeElement.setSelectionRange(selectionStart + 1, selectionStart + 1);\n                            }\n                            else {\n                                newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                            }\n                        }\n                        else if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                            const insertedText = this.isDecimalMode() && (this.minFractionDigits || 0) < decimalLength ? '' : '0';\n                            newValueStr = inputValue.slice(0, selectionStart) + insertedText + inputValue.slice(selectionStart + 1);\n                        }\n                        else if (decimalCharIndexWithoutPrefix === 1) {\n                            newValueStr = inputValue.slice(0, selectionStart) + '0' + inputValue.slice(selectionStart + 1);\n                            newValueStr = this.parseValue(newValueStr) > 0 ? newValueStr : '';\n                        }\n                        else {\n                            newValueStr = inputValue.slice(0, selectionStart) + inputValue.slice(selectionStart + 1);\n                        }\n                    }\n                    this.updateValue(event, newValueStr, null, 'delete-back-single');\n                }\n                else {\n                    newValueStr = this.deleteRange(inputValue, selectionStart, selectionEnd);\n                    this.updateValue(event, newValueStr, null, 'delete-range');\n                }\n                break;\n            default:\n                break;\n        }\n        this.onKeyDown.emit(event);\n    }\n    onInputKeyPress(event) {\n        if (this.readonly) {\n            return;\n        }\n        event.preventDefault();\n        let code = event.which || event.keyCode;\n        let char = String.fromCharCode(code);\n        const isDecimalSign = this.isDecimalSign(char);\n        const isMinusSign = this.isMinusSign(char);\n        if ((48 <= code && code <= 57) || isMinusSign || isDecimalSign) {\n            this.insert(event, char, { isDecimalSign, isMinusSign });\n        }\n    }\n    onPaste(event) {\n        if (!this.disabled && !this.readonly) {\n            event.preventDefault();\n            let data = (event.clipboardData || window['clipboardData']).getData('Text');\n            if (data) {\n                let filteredData = this.parseValue(data);\n                if (filteredData != null) {\n                    this.insert(event, filteredData.toString());\n                }\n            }\n        }\n    }\n    allowMinusSign() {\n        return this.min == null || this.min < 0;\n    }\n    isMinusSign(char) {\n        if (this._minusSign.test(char) || char === '-') {\n            this._minusSign.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalSign(char) {\n        if (this._decimal.test(char)) {\n            this._decimal.lastIndex = 0;\n            return true;\n        }\n        return false;\n    }\n    isDecimalMode() {\n        return this.mode === 'decimal';\n    }\n    getDecimalCharIndexes(val) {\n        let decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const filteredVal = val.replace(this._prefix, '').trim().replace(/\\s/g, '').replace(this._currency, '');\n        const decimalCharIndexWithoutPrefix = filteredVal.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        return { decimalCharIndex, decimalCharIndexWithoutPrefix };\n    }\n    getCharIndexes(val) {\n        const decimalCharIndex = val.search(this._decimal);\n        this._decimal.lastIndex = 0;\n        const minusCharIndex = val.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        const suffixCharIndex = val.search(this._suffix);\n        this._suffix.lastIndex = 0;\n        const currencyCharIndex = val.search(this._currency);\n        this._currency.lastIndex = 0;\n        return { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex };\n    }\n    insert(event, text, sign = { isDecimalSign: false, isMinusSign: false }) {\n        const minusCharIndexOnText = text.search(this._minusSign);\n        this._minusSign.lastIndex = 0;\n        if (!this.allowMinusSign() && minusCharIndexOnText !== -1) {\n            return;\n        }\n        let selectionStart = this.input.nativeElement.selectionStart;\n        let selectionEnd = this.input.nativeElement.selectionEnd;\n        let inputValue = this.input.nativeElement.value.trim();\n        const { decimalCharIndex, minusCharIndex, suffixCharIndex, currencyCharIndex } = this.getCharIndexes(inputValue);\n        let newValueStr;\n        if (sign.isMinusSign) {\n            if (selectionStart === 0) {\n                newValueStr = inputValue;\n                if (minusCharIndex === -1 || selectionEnd !== 0) {\n                    newValueStr = this.insertText(inputValue, text, 0, selectionEnd);\n                }\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else if (sign.isDecimalSign) {\n            if (decimalCharIndex > 0 && selectionStart === decimalCharIndex) {\n                this.updateValue(event, inputValue, text, 'insert');\n            }\n            else if (decimalCharIndex > selectionStart && decimalCharIndex < selectionEnd) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n            else if (decimalCharIndex === -1 && this.maxFractionDigits) {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, 'insert');\n            }\n        }\n        else {\n            const maxFractionDigits = this.numberFormat.resolvedOptions().maximumFractionDigits;\n            const operation = selectionStart !== selectionEnd ? 'range-insert' : 'insert';\n            if (decimalCharIndex > 0 && selectionStart > decimalCharIndex) {\n                if ((selectionStart + text.length - (decimalCharIndex + 1)) <= maxFractionDigits) {\n                    const charIndex = currencyCharIndex >= selectionStart ? currencyCharIndex - 1 : (suffixCharIndex >= selectionStart ? suffixCharIndex : inputValue.length);\n                    newValueStr = inputValue.slice(0, selectionStart) + text + inputValue.slice(selectionStart + text.length, charIndex) + inputValue.slice(charIndex);\n                    this.updateValue(event, newValueStr, text, operation);\n                }\n            }\n            else {\n                newValueStr = this.insertText(inputValue, text, selectionStart, selectionEnd);\n                this.updateValue(event, newValueStr, text, operation);\n            }\n        }\n    }\n    insertText(value, text, start, end) {\n        let textSplit = text === '.' ? text : text.split('.');\n        if (textSplit.length === 2) {\n            const decimalCharIndex = value.slice(start, end).search(this._decimal);\n            this._decimal.lastIndex = 0;\n            return (decimalCharIndex > 0) ? value.slice(0, start) + this.formatValue(text) + value.slice(end) : (value || this.formatValue(text));\n        }\n        else if ((end - start) === value.length) {\n            return this.formatValue(text);\n        }\n        else if (start === 0) {\n            return text + value.slice(end);\n        }\n        else if (end === value.length) {\n            return value.slice(0, start) + text;\n        }\n        else {\n            return value.slice(0, start) + text + value.slice(end);\n        }\n    }\n    deleteRange(value, start, end) {\n        let newValueStr;\n        if ((end - start) === value.length)\n            newValueStr = '';\n        else if (start === 0)\n            newValueStr = value.slice(end);\n        else if (end === value.length)\n            newValueStr = value.slice(0, start);\n        else\n            newValueStr = value.slice(0, start) + value.slice(end);\n        return newValueStr;\n    }\n    initCursor() {\n        let selectionStart = this.input.nativeElement.selectionStart;\n        let inputValue = this.input.nativeElement.value;\n        let valueLength = inputValue.length;\n        let index = null;\n        // remove prefix\n        let prefixLength = (this.prefixChar || '').length;\n        inputValue = inputValue.replace(this._prefix, '');\n        selectionStart = selectionStart - prefixLength;\n        let char = inputValue.charAt(selectionStart);\n        if (this.isNumeralChar(char)) {\n            return selectionStart + prefixLength;\n        }\n        //left\n        let i = selectionStart - 1;\n        while (i >= 0) {\n            char = inputValue.charAt(i);\n            if (this.isNumeralChar(char)) {\n                index = i + prefixLength;\n                break;\n            }\n            else {\n                i--;\n            }\n        }\n        if (index !== null) {\n            this.input.nativeElement.setSelectionRange(index + 1, index + 1);\n        }\n        else {\n            i = selectionStart;\n            while (i < valueLength) {\n                char = inputValue.charAt(i);\n                if (this.isNumeralChar(char)) {\n                    index = i + prefixLength;\n                    break;\n                }\n                else {\n                    i++;\n                }\n            }\n            if (index !== null) {\n                this.input.nativeElement.setSelectionRange(index, index);\n            }\n        }\n        return index || 0;\n    }\n    onInputClick() {\n        if (!this.readonly) {\n            this.initCursor();\n        }\n    }\n    isNumeralChar(char) {\n        if (char.length === 1 && (this._numeral.test(char) || this._decimal.test(char) || this._group.test(char) || this._minusSign.test(char))) {\n            this.resetRegex();\n            return true;\n        }\n        return false;\n    }\n    resetRegex() {\n        this._numeral.lastIndex = 0;\n        this._decimal.lastIndex = 0;\n        this._group.lastIndex = 0;\n        this._minusSign.lastIndex = 0;\n    }\n    updateValue(event, valueStr, insertedValueStr, operation) {\n        let currentValue = this.input.nativeElement.value;\n        let newValue = null;\n        if (valueStr != null) {\n            newValue = this.parseValue(valueStr);\n            newValue = !newValue && !this.allowEmpty ? 0 : newValue;\n            this.updateInput(newValue, insertedValueStr, operation, valueStr);\n            this.handleOnInput(event, currentValue, newValue);\n        }\n    }\n    handleOnInput(event, currentValue, newValue) {\n        if (this.isValueChanged(currentValue, newValue)) {\n            this.onInput.emit({ originalEvent: event, value: newValue });\n        }\n    }\n    isValueChanged(currentValue, newValue) {\n        if (newValue === null && currentValue !== null) {\n            return true;\n        }\n        if (newValue != null) {\n            let parsedCurrentValue = (typeof currentValue === 'string') ? this.parseValue(currentValue) : currentValue;\n            return newValue !== parsedCurrentValue;\n        }\n        return false;\n    }\n    validateValue(value) {\n        if (value === '-' || value == null) {\n            return null;\n        }\n        if (this.min != null && value < this.min) {\n            return this.min;\n        }\n        if (this.max != null && value > this.max) {\n            return this.max;\n        }\n        return value;\n    }\n    updateInput(value, insertedValueStr, operation, valueStr) {\n        insertedValueStr = insertedValueStr || '';\n        let inputValue = this.input.nativeElement.value;\n        let newValue = this.formatValue(value);\n        let currentLength = inputValue.length;\n        if (newValue !== valueStr) {\n            newValue = this.concatValues(newValue, valueStr);\n        }\n        if (currentLength === 0) {\n            this.input.nativeElement.value = newValue;\n            this.input.nativeElement.setSelectionRange(0, 0);\n            const index = this.initCursor();\n            const selectionEnd = index + insertedValueStr.length;\n            this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n        }\n        else {\n            let selectionStart = this.input.nativeElement.selectionStart;\n            let selectionEnd = this.input.nativeElement.selectionEnd;\n            if (this.maxlength && this.maxlength < newValue.length) {\n                return;\n            }\n            this.input.nativeElement.value = newValue;\n            let newLength = newValue.length;\n            if (operation === 'range-insert') {\n                const startValue = this.parseValue((inputValue || '').slice(0, selectionStart));\n                const startValueStr = startValue !== null ? startValue.toString() : '';\n                const startExpr = startValueStr.split('').join(`(${this.groupChar})?`);\n                const sRegex = new RegExp(startExpr, 'g');\n                sRegex.test(newValue);\n                const tExpr = insertedValueStr.split('').join(`(${this.groupChar})?`);\n                const tRegex = new RegExp(tExpr, 'g');\n                tRegex.test(newValue.slice(sRegex.lastIndex));\n                selectionEnd = sRegex.lastIndex + tRegex.lastIndex;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (newLength === currentLength) {\n                if (operation === 'insert' || operation === 'delete-back-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd + 1, selectionEnd + 1);\n                else if (operation === 'delete-single')\n                    this.input.nativeElement.setSelectionRange(selectionEnd - 1, selectionEnd - 1);\n                else if (operation === 'delete-range' || operation === 'spin')\n                    this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (operation === 'delete-back-single') {\n                let prevChar = inputValue.charAt(selectionEnd - 1);\n                let nextChar = inputValue.charAt(selectionEnd);\n                let diff = currentLength - newLength;\n                let isGroupChar = this._group.test(nextChar);\n                if (isGroupChar && diff === 1) {\n                    selectionEnd += 1;\n                }\n                else if (!isGroupChar && this.isNumeralChar(prevChar)) {\n                    selectionEnd += (-1 * diff) + 1;\n                }\n                this._group.lastIndex = 0;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else if (inputValue === '-' && operation === 'insert') {\n                this.input.nativeElement.setSelectionRange(0, 0);\n                const index = this.initCursor();\n                const selectionEnd = index + insertedValueStr.length + 1;\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n            else {\n                selectionEnd = selectionEnd + (newLength - currentLength);\n                this.input.nativeElement.setSelectionRange(selectionEnd, selectionEnd);\n            }\n        }\n        this.input.nativeElement.setAttribute('aria-valuenow', value);\n    }\n    concatValues(val1, val2) {\n        if (val1 && val2) {\n            let decimalCharIndex = val2.search(this._decimal);\n            this._decimal.lastIndex = 0;\n            return decimalCharIndex !== -1 ? (val1.split(this._decimal)[0] + val2.slice(decimalCharIndex)) : val1;\n        }\n        return val1;\n    }\n    getDecimalLength(value) {\n        if (value) {\n            const valueSplit = value.split(this._decimal);\n            if (valueSplit.length === 2) {\n                return valueSplit[1].replace(this._suffix, '')\n                    .trim()\n                    .replace(/\\s/g, '')\n                    .replace(this._currency, '').length;\n            }\n        }\n        return 0;\n    }\n    onInputFocus(event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n        this.focused = false;\n        let newValue = this.validateValue(this.parseValue(this.input.nativeElement.value));\n        this.input.nativeElement.value = this.formatValue(newValue);\n        this.input.nativeElement.setAttribute('aria-valuenow', newValue);\n        this.updateModel(event, newValue);\n        this.onBlur.emit(event);\n    }\n    formattedValue() {\n        const val = !this.value && !this.allowEmpty ? 0 : this.value;\n        return this.formatValue(val);\n    }\n    updateModel(event, value) {\n        if (this.value !== value) {\n            this.value = value;\n            this.onModelChange(value);\n        }\n        this.onModelTouched();\n    }\n    writeValue(value) {\n        this.value = value;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    get filled() {\n        return (this.value != null && this.value.toString().length > 0);\n    }\n    clearTimer() {\n        if (this.timer) {\n            clearInterval(this.timer);\n        }\n    }\n    getFormatter() {\n        return this.numberFormat;\n    }\n}\nInputNumber.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumber, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nInputNumber.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: InputNumber, selector: \"p-inputNumber\", inputs: { showButtons: \"showButtons\", format: \"format\", buttonLayout: \"buttonLayout\", inputId: \"inputId\", styleClass: \"styleClass\", style: \"style\", placeholder: \"placeholder\", size: \"size\", maxlength: \"maxlength\", tabindex: \"tabindex\", title: \"title\", ariaLabel: \"ariaLabel\", ariaRequired: \"ariaRequired\", name: \"name\", required: \"required\", autocomplete: \"autocomplete\", min: \"min\", max: \"max\", incrementButtonClass: \"incrementButtonClass\", decrementButtonClass: \"decrementButtonClass\", incrementButtonIcon: \"incrementButtonIcon\", decrementButtonIcon: \"decrementButtonIcon\", readonly: \"readonly\", step: \"step\", allowEmpty: \"allowEmpty\", locale: \"locale\", localeMatcher: \"localeMatcher\", mode: \"mode\", currency: \"currency\", currencyDisplay: \"currencyDisplay\", useGrouping: \"useGrouping\", minFractionDigits: \"minFractionDigits\", maxFractionDigits: \"maxFractionDigits\", prefix: \"prefix\", suffix: \"suffix\", inputStyle: \"inputStyle\", inputStyleClass: \"inputStyleClass\", showClear: \"showClear\", disabled: \"disabled\" }, outputs: { onInput: \"onInput\", onFocus: \"onFocus\", onBlur: \"onBlur\", onKeyDown: \"onKeyDown\", onClear: \"onClear\" }, host: { properties: { \"class.p-inputwrapper-filled\": \"filled\", \"class.p-inputwrapper-focus\": \"focused\", \"class.p-inputnumber-clearable\": \"showClear && buttonLayout != \\\"vertical\\\"\" }, classAttribute: \"p-element p-inputwrapper\" }, providers: [INPUTNUMBER_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <span [ngClass]=\"{'p-inputnumber p-component': true,'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal', 'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'}\"\n                [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input #input [ngClass]=\"'p-inputnumber-input'\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" pInputText [value]=\"formattedValue()\" [attr.placeholder]=\"placeholder\" [attr.title]=\"title\" [attr.id]=\"inputId\"\n                [attr.size]=\"size\" [attr.name]=\"name\" [attr.autocomplete]=\"autocomplete\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-required]=\"ariaRequired\" [disabled]=\"disabled\" [attr.required]=\"required\" [attr.min]=\"min\" [attr.max]=\"max\" [readonly]=\"readonly\" inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\" (keydown)=\"onInputKeyDown($event)\" (keypress)=\"onInputKeyPress($event)\" (paste)=\"onPaste($event)\" (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i *ngIf=\"buttonLayout != 'vertical' && showClear && value\" class=\"p-inputnumber-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\">\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n            </span>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n        </span>\n    `, isInline: true, styles: [\"p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.InputText, selector: \"[pInputText]\" }, { kind: \"directive\", type: i3.ButtonDirective, selector: \"[pButton]\", inputs: [\"iconPos\", \"loadingIcon\", \"label\", \"icon\", \"loading\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumber, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-inputNumber', template: `\n        <span [ngClass]=\"{'p-inputnumber p-component': true,'p-inputnumber-buttons-stacked': this.showButtons && this.buttonLayout === 'stacked',\n                'p-inputnumber-buttons-horizontal': this.showButtons && this.buttonLayout === 'horizontal', 'p-inputnumber-buttons-vertical': this.showButtons && this.buttonLayout === 'vertical'}\"\n                [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input #input [ngClass]=\"'p-inputnumber-input'\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" pInputText [value]=\"formattedValue()\" [attr.placeholder]=\"placeholder\" [attr.title]=\"title\" [attr.id]=\"inputId\"\n                [attr.size]=\"size\" [attr.name]=\"name\" [attr.autocomplete]=\"autocomplete\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [attr.aria-label]=\"ariaLabel\"\n                [attr.aria-required]=\"ariaRequired\" [disabled]=\"disabled\" [attr.required]=\"required\" [attr.min]=\"min\" [attr.max]=\"max\" [readonly]=\"readonly\" inputmode=\"decimal\"\n                (input)=\"onUserInput($event)\" (keydown)=\"onInputKeyDown($event)\" (keypress)=\"onInputKeyPress($event)\" (paste)=\"onPaste($event)\" (click)=\"onInputClick()\"\n                (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i *ngIf=\"buttonLayout != 'vertical' && showClear && value\" class=\"p-inputnumber-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <span class=\"p-inputnumber-button-group\" *ngIf=\"showButtons && buttonLayout === 'stacked'\">\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n                <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" [disabled]=\"disabled\"\n                    (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n            </span>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-up': true}\" [class]=\"incrementButtonClass\" [icon]=\"incrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onUpButtonMouseDown($event)\" (mouseup)=\"onUpButtonMouseUp()\" (mouseleave)=\"onUpButtonMouseLeave()\" (keydown)=\"onUpButtonKeyDown($event)\" (keyup)=\"onUpButtonKeyUp()\"></button>\n            <button type=\"button\" pButton [ngClass]=\"{'p-inputnumber-button p-inputnumber-button-down': true}\" [class]=\"decrementButtonClass\" [icon]=\"decrementButtonIcon\" *ngIf=\"showButtons && buttonLayout !== 'stacked'\" [disabled]=\"disabled\"\n                (mousedown)=\"this.onDownButtonMouseDown($event)\" (mouseup)=\"onDownButtonMouseUp()\" (mouseleave)=\"onDownButtonMouseLeave()\" (keydown)=\"onDownButtonKeyDown($event)\" (keyup)=\"onDownButtonKeyUp()\"></button>\n        </span>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, providers: [INPUTNUMBER_VALUE_ACCESSOR], encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element p-inputwrapper',\n                        '[class.p-inputwrapper-filled]': 'filled',\n                        '[class.p-inputwrapper-focus]': 'focused',\n                        '[class.p-inputnumber-clearable]': 'showClear && buttonLayout != \"vertical\"'\n                    }, styles: [\"p-inputnumber,.p-inputnumber{display:inline-flex}.p-inputnumber-button{display:flex;align-items:center;justify-content:center;flex:0 0 auto}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button .p-button-label,.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button .p-button-label{display:none}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-up{border-top-left-radius:0;border-bottom-left-radius:0;border-bottom-right-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-input{border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-stacked .p-button.p-inputnumber-button-down{border-top-left-radius:0;border-top-right-radius:0;border-bottom-left-radius:0;padding:0}.p-inputnumber-buttons-stacked .p-inputnumber-button-group{display:flex;flex-direction:column}.p-inputnumber-buttons-stacked .p-inputnumber-button-group .p-button.p-inputnumber-button{flex:1 1 auto}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-up{order:3;border-top-left-radius:0;border-bottom-left-radius:0}.p-inputnumber-buttons-horizontal .p-inputnumber-input{order:2;border-radius:0}.p-inputnumber-buttons-horizontal .p-button.p-inputnumber-button-down{order:1;border-top-right-radius:0;border-bottom-right-radius:0}.p-inputnumber-buttons-vertical{flex-direction:column}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-up{order:1;border-bottom-left-radius:0;border-bottom-right-radius:0;width:100%}.p-inputnumber-buttons-vertical .p-inputnumber-input{order:2;border-radius:0;text-align:center}.p-inputnumber-buttons-vertical .p-button.p-inputnumber-button-down{order:3;border-top-left-radius:0;border-top-right-radius:0;width:100%}.p-inputnumber-input{flex:1 1 auto}.p-fluid p-inputnumber,.p-fluid .p-inputnumber{width:100%}.p-fluid .p-inputnumber .p-inputnumber-input{width:1%}.p-fluid .p-inputnumber-buttons-vertical .p-inputnumber-input{width:100%}.p-inputnumber-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-inputnumber-clearable{position:relative}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { showButtons: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], buttonLayout: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], maxlength: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], title: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaRequired: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], autocomplete: [{\n                type: Input\n            }], min: [{\n                type: Input\n            }], max: [{\n                type: Input\n            }], incrementButtonClass: [{\n                type: Input\n            }], decrementButtonClass: [{\n                type: Input\n            }], incrementButtonIcon: [{\n                type: Input\n            }], decrementButtonIcon: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], allowEmpty: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], localeMatcher: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], currency: [{\n                type: Input\n            }], currencyDisplay: [{\n                type: Input\n            }], useGrouping: [{\n                type: Input\n            }], minFractionDigits: [{\n                type: Input\n            }], maxFractionDigits: [{\n                type: Input\n            }], prefix: [{\n                type: Input\n            }], suffix: [{\n                type: Input\n            }], inputStyle: [{\n                type: Input\n            }], inputStyleClass: [{\n                type: Input\n            }], showClear: [{\n                type: Input\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }], onInput: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onKeyDown: [{\n                type: Output\n            }], onClear: [{\n                type: Output\n            }], disabled: [{\n                type: Input\n            }] } });\nclass InputNumberModule {\n}\nInputNumberModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumberModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nInputNumberModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumberModule, declarations: [InputNumber], imports: [CommonModule, InputTextModule, ButtonModule], exports: [InputNumber] });\nInputNumberModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumberModule, imports: [CommonModule, InputTextModule, ButtonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: InputNumberModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, InputTextModule, ButtonModule],\n                    exports: [InputNumber],\n                    declarations: [InputNumber]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTNUMBER_VALUE_ACCESSOR, InputNumber, InputNumberModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,KAA1F,EAAiGC,SAAjG,EAA4GC,MAA5G,EAAoHC,QAApH,QAAoI,eAApI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,iBAAT,QAAkC,gBAAlC;;;;;gBAqvB8FhB,E;;IAAAA,EAUlF,0B;IAVkFA,EAUuB;MAVvBA,EAUuB;MAAA,eAVvBA,EAUuB;MAAA,OAVvBA,EAUgC,4BAAT;IAAA,E;IAVvBA,EAUyC,e;;;;;;;;;;;;;;;;;;gBAVzCA,E;;IAAAA,EAWlF,6C;IAXkFA,EAa1E;MAb0EA,EAa1E;MAAA,eAb0EA,EAa1E;MAAA,OAb0EA,EAa7D,gDAAb;IAAA;MAb0EA,EAa1E;MAAA,eAb0EA,EAa1E;MAAA,OAb0EA,EAahB,wCAA1D;IAAA;MAb0EA,EAa1E;MAAA,gBAb0EA,EAa1E;MAAA,OAb0EA,EAamB,4CAA7F;IAAA;MAb0EA,EAa1E;MAAA,gBAb0EA,EAa1E;MAAA,OAb0EA,EAasD,+CAAhI;IAAA;MAb0EA,EAa1E;MAAA,gBAb0EA,EAa1E;MAAA,OAb0EA,EAa0F,uCAApK;IAAA,E;IAb0EA,EAa6G,e;IAb7GA,EAc9E,+B;IAd8EA,EAe1E;MAf0EA,EAe1E;MAAA,gBAf0EA,EAe1E;MAAA,OAf0EA,EAe7D,mDAAb;IAAA;MAf0EA,EAe1E;MAAA,gBAf0EA,EAe1E;MAAA,OAf0EA,EAed,2CAA5D;IAAA;MAf0EA,EAe1E;MAAA,gBAf0EA,EAe1E;MAAA,OAf0EA,EAeuB,8CAAjG;IAAA;MAf0EA,EAe1E;MAAA,gBAf0EA,EAe1E;MAAA,OAf0EA,EAe4D,iDAAtI;IAAA;MAf0EA,EAe1E;MAAA,gBAf0EA,EAe1E;MAAA,OAf0EA,EAekG,yCAA5K;IAAA,E;IAf0EA,EAeuH,iB;;;;mBAfvHA,E;IAAAA,EAYmB,a;IAZnBA,EAYmB,wC;IAZnBA,EAYhD,uBAZgDA,EAYhD,2F;IAZgDA,EAcqB,a;IAdrBA,EAcqB,wC;IAdrBA,EAchD,uBAdgDA,EAchD,2F;;;;;;iBAdgDA,E;;IAAAA,EAiBlF,+B;IAjBkFA,EAkB9E;MAlB8EA,EAkB9E;MAAA,gBAlB8EA,EAkB9E;MAAA,OAlB8EA,EAkBjE,iDAAb;IAAA;MAlB8EA,EAkB9E;MAAA,gBAlB8EA,EAkB9E;MAAA,OAlB8EA,EAkBpB,yCAA1D;IAAA;MAlB8EA,EAkB9E;MAAA,gBAlB8EA,EAkB9E;MAAA,OAlB8EA,EAkBe,4CAA7F;IAAA;MAlB8EA,EAkB9E;MAAA,gBAlB8EA,EAkB9E;MAAA,OAlB8EA,EAkBkD,+CAAhI;IAAA;MAlB8EA,EAkB9E;MAAA,gBAlB8EA,EAkB9E;MAAA,OAlB8EA,EAkBsF,uCAApK;IAAA,E;IAlB8EA,EAkByG,e;;;;mBAlBzGA,E;IAAAA,EAiBe,wC;IAjBfA,EAiBpD,uBAjBoDA,EAiBpD,0F;;;;;;iBAjBoDA,E;;IAAAA,EAmBlF,+B;IAnBkFA,EAoB9E;MApB8EA,EAoB9E;MAAA,gBApB8EA,EAoB9E;MAAA,OApB8EA,EAoBjE,mDAAb;IAAA;MApB8EA,EAoB9E;MAAA,gBApB8EA,EAoB9E;MAAA,OApB8EA,EAoBlB,2CAA5D;IAAA;MApB8EA,EAoB9E;MAAA,gBApB8EA,EAoB9E;MAAA,OApB8EA,EAoBmB,8CAAjG;IAAA;MApB8EA,EAoB9E;MAAA,gBApB8EA,EAoB9E;MAAA,OApB8EA,EAoBwD,iDAAtI;IAAA;MApB8EA,EAoB9E;MAAA,gBApB8EA,EAoB9E;MAAA,OApB8EA,EAoB8F,yCAA5K;IAAA,E;IApB8EA,EAoBmH,e;;;;mBApBnHA,E;IAAAA,EAmBiB,wC;IAnBjBA,EAmBpD,uBAnBoDA,EAmBpD,0F;;;;;;;;;;;;;AAtwB1C,MAAMiB,0BAA0B,GAAG;EAC/BC,OAAO,EAAEF,iBADsB;EAE/BG,WAAW,EAAElB,UAAU,CAAC,MAAMmB,WAAP,CAFQ;EAG/BC,KAAK,EAAE;AAHwB,CAAnC;;AAKA,MAAMD,WAAN,CAAkB;EACdE,WAAW,CAACC,EAAD,EAAKC,EAAL,EAAS;IAChB,KAAKD,EAAL,GAAUA,EAAV;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKC,YAAL,GAAoB,SAApB;IACA,KAAKC,mBAAL,GAA2B,gBAA3B;IACA,KAAKC,mBAAL,GAA2B,kBAA3B;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,IAAL,GAAY,CAAZ;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,IAAL,GAAY,SAAZ;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,OAAL,GAAe,IAAIlC,YAAJ,EAAf;IACA,KAAKmC,OAAL,GAAe,IAAInC,YAAJ,EAAf;IACA,KAAKoC,MAAL,GAAc,IAAIpC,YAAJ,EAAd;IACA,KAAKqC,SAAL,GAAiB,IAAIrC,YAAJ,EAAjB;IACA,KAAKsC,OAAL,GAAe,IAAItC,YAAJ,EAAf;;IACA,KAAKuC,aAAL,GAAqB,MAAM,CAAG,CAA9B;;IACA,KAAKC,cAAL,GAAsB,MAAM,CAAG,CAA/B;;IACA,KAAKC,SAAL,GAAiB,EAAjB;IACA,KAAKC,UAAL,GAAkB,EAAlB;IACA,KAAKC,UAAL,GAAkB,EAAlB;EACH;;EACW,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACA,QAAD,EAAW;IACnB,IAAIA,QAAJ,EACI,KAAKE,OAAL,GAAe,KAAf;IACJ,KAAKD,SAAL,GAAiBD,QAAjB;IACA,IAAI,KAAKG,KAAT,EACI,KAAKC,UAAL;EACP;;EACDC,WAAW,CAACC,YAAD,EAAe;IACtB,MAAMC,KAAK,GAAG,CAAC,QAAD,EAAW,eAAX,EAA4B,MAA5B,EAAoC,UAApC,EAAgD,iBAAhD,EAAmE,aAAnE,EAAkF,mBAAlF,EAAuG,mBAAvG,EAA4H,QAA5H,EAAsI,QAAtI,CAAd;;IACA,IAAIA,KAAK,CAACC,IAAN,CAAWC,CAAC,IAAI,CAAC,CAACH,YAAY,CAACG,CAAD,CAA9B,CAAJ,EAAwC;MACpC,KAAKC,qBAAL;IACH;EACJ;;EACDC,QAAQ,GAAG;IACP,KAAKC,eAAL;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,UAAU,GAAG;IACT,OAAO;MACHC,aAAa,EAAE,KAAKA,aADjB;MAEHC,KAAK,EAAE,KAAK7B,IAFT;MAGH8B,QAAQ,EAAE,KAAKA,QAHZ;MAIHC,eAAe,EAAE,KAAKA,eAJnB;MAKH9B,WAAW,EAAE,KAAKA,WALf;MAMH+B,qBAAqB,EAAE,KAAKC,iBANzB;MAOHC,qBAAqB,EAAE,KAAKC;IAPzB,CAAP;EASH;;EACDV,eAAe,GAAG;IACd,KAAKW,YAAL,GAAoB,IAAIC,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC,KAAKZ,UAAL,EAAnC,CAApB;IACA,MAAMa,QAAQ,GAAG,CAAC,GAAG,IAAIH,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;MAAEtC,WAAW,EAAE;IAAf,CAAnC,EAA2DR,MAA3D,CAAkE,UAAlE,CAAJ,EAAmFgD,OAAnF,EAAjB;IACA,MAAMC,KAAK,GAAG,IAAIC,GAAJ,CAAQH,QAAQ,CAACI,GAAT,CAAa,CAACC,CAAD,EAAIC,CAAJ,KAAU,CAACD,CAAD,EAAIC,CAAJ,CAAvB,CAAR,CAAd;IACA,KAAKC,QAAL,GAAgB,IAAIC,MAAJ,CAAY,IAAGR,QAAQ,CAACS,IAAT,CAAc,EAAd,CAAkB,GAAjC,EAAqC,GAArC,CAAhB;IACA,KAAKC,MAAL,GAAc,KAAKC,qBAAL,EAAd;IACA,KAAKC,UAAL,GAAkB,KAAKC,sBAAL,EAAlB;IACA,KAAKC,SAAL,GAAiB,KAAKC,qBAAL,EAAjB;IACA,KAAKC,QAAL,GAAgB,KAAKC,oBAAL,EAAhB;IACA,KAAKC,OAAL,GAAe,KAAKC,mBAAL,EAAf;IACA,KAAKC,OAAL,GAAe,KAAKC,mBAAL,EAAf;;IACA,KAAKC,MAAL,GAAcjB,CAAC,IAAIH,KAAK,CAACqB,GAAN,CAAUlB,CAAV,CAAnB;EACH;;EACDtB,qBAAqB,GAAG;IACpB,IAAI,KAAKG,WAAT,EAAsB;MAClB,KAAKD,eAAL;IACH;EACJ;;EACDuC,YAAY,CAACC,IAAD,EAAO;IACf,OAAOA,IAAI,CAACC,OAAL,CAAa,0BAAb,EAAyC,MAAzC,CAAP;EACH;;EACDT,oBAAoB,GAAG;IACnB,MAAMU,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC6B,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAK1C,UAAL,EAAlB,CAAd,EAAoD;MAAE1B,WAAW,EAAE;IAAf,CAApD,CAAnC,CAAlB;IACA,OAAO,IAAI+C,MAAJ,CAAY,IAAGmB,SAAS,CAAC1E,MAAV,CAAiB,GAAjB,EAAsByE,OAAtB,CAA8B,KAAKZ,SAAnC,EAA8C,EAA9C,EAAkDgB,IAAlD,GAAyDJ,OAAzD,CAAiE,KAAKnB,QAAtE,EAAgF,EAAhF,CAAoF,GAAnG,EAAuG,GAAvG,CAAP;EACH;;EACDI,qBAAqB,GAAG;IACpB,MAAMgB,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;MAAEtC,WAAW,EAAE;IAAf,CAAnC,CAAlB;IACA,KAAKS,SAAL,GAAiByD,SAAS,CAAC1E,MAAV,CAAiB,OAAjB,EAA0B6E,IAA1B,GAAiCJ,OAAjC,CAAyC,KAAKnB,QAA9C,EAAwD,EAAxD,EAA4DwB,MAA5D,CAAmE,CAAnE,CAAjB;IACA,OAAO,IAAIvB,MAAJ,CAAY,IAAG,KAAKtC,SAAU,GAA9B,EAAkC,GAAlC,CAAP;EACH;;EACD2C,sBAAsB,GAAG;IACrB,MAAMc,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;MAAEtC,WAAW,EAAE;IAAf,CAAnC,CAAlB;IACA,OAAO,IAAI+C,MAAJ,CAAY,IAAGmB,SAAS,CAAC1E,MAAV,CAAiB,CAAC,CAAlB,EAAqB6E,IAArB,GAA4BJ,OAA5B,CAAoC,KAAKnB,QAAzC,EAAmD,EAAnD,CAAuD,GAAtE,EAA0E,GAA1E,CAAP;EACH;;EACDQ,qBAAqB,GAAG;IACpB,IAAI,KAAKzB,QAAT,EAAmB;MACf,MAAMqC,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;QAAEV,KAAK,EAAE,UAAT;QAAqBC,QAAQ,EAAE,KAAKA,QAApC;QAA8CC,eAAe,EAAE,KAAKA,eAApE;QACjDC,qBAAqB,EAAE,CAD0B;QACvBE,qBAAqB,EAAE;MADA,CAAnC,CAAlB;MAEA,OAAO,IAAIc,MAAJ,CAAY,IAAGmB,SAAS,CAAC1E,MAAV,CAAiB,CAAjB,EAAoByE,OAApB,CAA4B,KAA5B,EAAmC,EAAnC,EAAuCA,OAAvC,CAA+C,KAAKnB,QAApD,EAA8D,EAA9D,EAAkEmB,OAAlE,CAA0E,KAAKhB,MAA/E,EAAuF,EAAvF,CAA2F,GAA1G,EAA8G,GAA9G,CAAP;IACH;;IACD,OAAO,IAAIF,MAAJ,CAAY,IAAZ,EAAiB,GAAjB,CAAP;EACH;;EACDa,mBAAmB,GAAG;IAClB,IAAI,KAAKW,MAAT,EAAiB;MACb,KAAK7D,UAAL,GAAkB,KAAK6D,MAAvB;IACH,CAFD,MAGK;MACD,MAAML,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;QAAEV,KAAK,EAAE,KAAK7B,IAAd;QAAoB8B,QAAQ,EAAE,KAAKA,QAAnC;QAA6CC,eAAe,EAAE,KAAKA;MAAnE,CAAnC,CAAlB;MACA,KAAKpB,UAAL,GAAkBwD,SAAS,CAAC1E,MAAV,CAAiB,CAAjB,EAAoBgF,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAlB;IACH;;IACD,OAAO,IAAIzB,MAAJ,CAAY,GAAE,KAAKgB,YAAL,CAAkB,KAAKrD,UAAL,IAAmB,EAArC,CAAyC,EAAvD,EAA0D,GAA1D,CAAP;EACH;;EACDgD,mBAAmB,GAAG;IAClB,IAAI,KAAKe,MAAT,EAAiB;MACb,KAAK9D,UAAL,GAAkB,KAAK8D,MAAvB;IACH,CAFD,MAGK;MACD,MAAMP,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC;QAAEV,KAAK,EAAE,KAAK7B,IAAd;QAAoB8B,QAAQ,EAAE,KAAKA,QAAnC;QAA6CC,eAAe,EAAE,KAAKA,eAAnE;QACjDC,qBAAqB,EAAE,CAD0B;QACvBE,qBAAqB,EAAE;MADA,CAAnC,CAAlB;MAEA,KAAKtB,UAAL,GAAkBuD,SAAS,CAAC1E,MAAV,CAAiB,CAAjB,EAAoBgF,KAApB,CAA0B,GAA1B,EAA+B,CAA/B,CAAlB;IACH;;IACD,OAAO,IAAIzB,MAAJ,CAAY,GAAE,KAAKgB,YAAL,CAAkB,KAAKpD,UAAL,IAAmB,EAArC,CAAyC,EAAvD,EAA0D,GAA1D,CAAP;EACH;;EACD+D,WAAW,CAACC,KAAD,EAAQ;IACf,IAAIA,KAAK,IAAI,IAAb,EAAmB;MACf,IAAIA,KAAK,KAAK,GAAd,EAAmB;QAAE;QACjB,OAAOA,KAAP;MACH;;MACD,IAAI,KAAKnF,MAAT,EAAiB;QACb,IAAI0E,SAAS,GAAG,IAAI9B,IAAI,CAACC,YAAT,CAAsB,KAAKC,MAA3B,EAAmC,KAAKZ,UAAL,EAAnC,CAAhB;QACA,IAAIkD,cAAc,GAAGV,SAAS,CAAC1E,MAAV,CAAiBmF,KAAjB,CAArB;;QACA,IAAI,KAAKJ,MAAT,EAAiB;UACbK,cAAc,GAAG,KAAKL,MAAL,GAAcK,cAA/B;QACH;;QACD,IAAI,KAAKH,MAAT,EAAiB;UACbG,cAAc,GAAGA,cAAc,GAAG,KAAKH,MAAvC;QACH;;QACD,OAAOG,cAAP;MACH;;MACD,OAAOD,KAAK,CAACE,QAAN,EAAP;IACH;;IACD,OAAO,EAAP;EACH;;EACDC,UAAU,CAACd,IAAD,EAAO;IACb,IAAIe,YAAY,GAAGf,IAAI,CAClBC,OADc,CACN,KAAKR,OADC,EACQ,EADR,EAEdQ,OAFc,CAEN,KAAKN,OAFC,EAEQ,EAFR,EAGdU,IAHc,GAIdJ,OAJc,CAIN,KAJM,EAIC,EAJD,EAKdA,OALc,CAKN,KAAKZ,SALC,EAKU,EALV,EAMdY,OANc,CAMN,KAAKhB,MANC,EAMO,EANP,EAOdgB,OAPc,CAON,KAAKd,UAPC,EAOW,GAPX,EAQdc,OARc,CAQN,KAAKV,QARC,EAQS,GART,EASdU,OATc,CASN,KAAKnB,QATC,EASS,KAAKe,MATd,CAAnB;;IAUA,IAAIkB,YAAJ,EAAkB;MACd,IAAIA,YAAY,KAAK,GAArB,EAA0B;QACtB,OAAOA,YAAP;MACJ,IAAIC,WAAW,GAAG,CAACD,YAAnB;MACA,OAAOE,KAAK,CAACD,WAAD,CAAL,GAAqB,IAArB,GAA4BA,WAAnC;IACH;;IACD,OAAO,IAAP;EACH;;EACDE,MAAM,CAACC,KAAD,EAAQC,QAAR,EAAkBC,GAAlB,EAAuB;IACzB,IAAI,KAAKzF,QAAT,EAAmB;MACf;IACH;;IACD,IAAIiD,CAAC,GAAGuC,QAAQ,IAAI,GAApB;IACA,KAAKpE,UAAL;IACA,KAAKD,KAAL,GAAauE,UAAU,CAAC,MAAM;MAC1B,KAAKJ,MAAL,CAAYC,KAAZ,EAAmB,EAAnB,EAAuBE,GAAvB;IACH,CAFsB,EAEpBxC,CAFoB,CAAvB;IAGA,KAAK0C,IAAL,CAAUJ,KAAV,EAAiBE,GAAjB;EACH;;EACDE,IAAI,CAACJ,KAAD,EAAQE,GAAR,EAAa;IACb,IAAIxF,IAAI,GAAG,KAAKA,IAAL,GAAYwF,GAAvB;IACA,IAAIG,YAAY,GAAG,KAAKV,UAAL,CAAgB,KAAKW,KAAL,CAAWC,aAAX,CAAyBf,KAAzC,KAAmD,CAAtE;IACA,IAAIgB,QAAQ,GAAG,KAAKC,aAAL,CAAmBJ,YAAY,GAAG3F,IAAlC,CAAf;;IACA,IAAI,KAAKgG,SAAL,IAAkB,KAAKA,SAAL,GAAiB,KAAKnB,WAAL,CAAiBiB,QAAjB,EAA2BG,MAAlE,EAA0E;MACtE;IACH;;IACD,KAAKC,WAAL,CAAiBJ,QAAjB,EAA2B,IAA3B,EAAiC,MAAjC,EAAyC,IAAzC;IACA,KAAKK,WAAL,CAAiBb,KAAjB,EAAwBQ,QAAxB;IACA,KAAKM,aAAL,CAAmBd,KAAnB,EAA0BK,YAA1B,EAAwCG,QAAxC;EACH;;EACDO,KAAK,GAAG;IACJ,KAAKvB,KAAL,GAAa,IAAb;IACA,KAAKpE,aAAL,CAAmB,KAAKoE,KAAxB;IACA,KAAKrE,OAAL,CAAa6F,IAAb;EACH;;EACDC,mBAAmB,CAACjB,KAAD,EAAQ;IACvB,KAAKM,KAAL,CAAWC,aAAX,CAAyBW,KAAzB;IACA,KAAKnB,MAAL,CAAYC,KAAZ,EAAmB,IAAnB,EAAyB,CAAzB;IACAA,KAAK,CAACmB,cAAN;EACH;;EACDC,iBAAiB,GAAG;IAChB,KAAKvF,UAAL;EACH;;EACDwF,oBAAoB,GAAG;IACnB,KAAKxF,UAAL;EACH;;EACDyF,iBAAiB,CAACtB,KAAD,EAAQ;IACrB,IAAIA,KAAK,CAACuB,OAAN,KAAkB,EAAlB,IAAwBvB,KAAK,CAACuB,OAAN,KAAkB,EAA9C,EAAkD;MAC9C,KAAKxB,MAAL,CAAYC,KAAZ,EAAmB,IAAnB,EAAyB,CAAzB;IACH;EACJ;;EACDwB,eAAe,GAAG;IACd,KAAK3F,UAAL;EACH;;EACD4F,qBAAqB,CAACzB,KAAD,EAAQ;IACzB,KAAKM,KAAL,CAAWC,aAAX,CAAyBW,KAAzB;IACA,KAAKnB,MAAL,CAAYC,KAAZ,EAAmB,IAAnB,EAAyB,CAAC,CAA1B;IACAA,KAAK,CAACmB,cAAN;EACH;;EACDO,mBAAmB,GAAG;IAClB,KAAK7F,UAAL;EACH;;EACD8F,sBAAsB,GAAG;IACrB,KAAK9F,UAAL;EACH;;EACD+F,iBAAiB,GAAG;IAChB,KAAK/F,UAAL;EACH;;EACDgG,mBAAmB,CAAC7B,KAAD,EAAQ;IACvB,IAAIA,KAAK,CAACuB,OAAN,KAAkB,EAAlB,IAAwBvB,KAAK,CAACuB,OAAN,KAAkB,EAA9C,EAAkD;MAC9C,KAAKxB,MAAL,CAAYC,KAAZ,EAAmB,IAAnB,EAAyB,CAAC,CAA1B;IACH;EACJ;;EACD8B,WAAW,CAAC9B,KAAD,EAAQ;IACf,IAAI,KAAKvF,QAAT,EAAmB;MACf;IACH;;IACD,IAAI,KAAKsH,aAAT,EAAwB;MACpB/B,KAAK,CAACgC,MAAN,CAAaxC,KAAb,GAAqB,KAAKyC,SAA1B;IACH;;IACD,KAAKF,aAAL,GAAqB,KAArB;EACH;;EACDG,cAAc,CAAClC,KAAD,EAAQ;IAClB,IAAI,KAAKvF,QAAT,EAAmB;MACf;IACH;;IACD,KAAKwH,SAAL,GAAiBjC,KAAK,CAACgC,MAAN,CAAaxC,KAA9B;;IACA,IAAIQ,KAAK,CAACmC,QAAN,IAAkBnC,KAAK,CAACoC,MAA5B,EAAoC;MAChC,KAAKL,aAAL,GAAqB,IAArB;MACA;IACH;;IACD,IAAIM,cAAc,GAAGrC,KAAK,CAACgC,MAAN,CAAaK,cAAlC;IACA,IAAIC,YAAY,GAAGtC,KAAK,CAACgC,MAAN,CAAaM,YAAhC;IACA,IAAIC,UAAU,GAAGvC,KAAK,CAACgC,MAAN,CAAaxC,KAA9B;IACA,IAAIgD,WAAW,GAAG,IAAlB;;IACA,IAAIxC,KAAK,CAACoC,MAAV,EAAkB;MACdpC,KAAK,CAACmB,cAAN;IACH;;IACD,QAAQnB,KAAK,CAACyC,KAAd;MACI;MACA,KAAK,EAAL;QACI,KAAKrC,IAAL,CAAUJ,KAAV,EAAiB,CAAjB;QACAA,KAAK,CAACmB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,KAAKf,IAAL,CAAUJ,KAAV,EAAiB,CAAC,CAAlB;QACAA,KAAK,CAACmB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,CAAC,KAAKuB,aAAL,CAAmBH,UAAU,CAACpD,MAAX,CAAkBkD,cAAc,GAAG,CAAnC,CAAnB,CAAL,EAAgE;UAC5DrC,KAAK,CAACmB,cAAN;QACH;;QACD;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,CAAC,KAAKuB,aAAL,CAAmBH,UAAU,CAACpD,MAAX,CAAkBkD,cAAlB,CAAnB,CAAL,EAA4D;UACxDrC,KAAK,CAACmB,cAAN;QACH;;QACD;MACJ;;MACA,KAAK,EAAL;QACIqB,WAAW,GAAG,KAAK/B,aAAL,CAAmB,KAAKd,UAAL,CAAgB,KAAKW,KAAL,CAAWC,aAAX,CAAyBf,KAAzC,CAAnB,CAAd;QACA,KAAKc,KAAL,CAAWC,aAAX,CAAyBf,KAAzB,GAAiC,KAAKD,WAAL,CAAiBiD,WAAjB,CAAjC;QACA,KAAKlC,KAAL,CAAWC,aAAX,CAAyBoC,YAAzB,CAAsC,eAAtC,EAAuDH,WAAvD;QACA,KAAK3B,WAAL,CAAiBb,KAAjB,EAAwBwC,WAAxB;QACA;MACJ;;MACA,KAAK,CAAL;QAAQ;UACJxC,KAAK,CAACmB,cAAN;;UACA,IAAIkB,cAAc,KAAKC,YAAvB,EAAqC;YACjC,MAAMM,UAAU,GAAGL,UAAU,CAACpD,MAAX,CAAkBkD,cAAc,GAAG,CAAnC,CAAnB;YACA,MAAM;cAAEQ,gBAAF;cAAoBC;YAApB,IAAsD,KAAKC,qBAAL,CAA2BR,UAA3B,CAA5D;;YACA,IAAI,KAAKG,aAAL,CAAmBE,UAAnB,CAAJ,EAAoC;cAChC,MAAMI,aAAa,GAAG,KAAKC,gBAAL,CAAsBV,UAAtB,CAAtB;;cACA,IAAI,KAAKzE,MAAL,CAAYoF,IAAZ,CAAiBN,UAAjB,CAAJ,EAAkC;gBAC9B,KAAK9E,MAAL,CAAYqF,SAAZ,GAAwB,CAAxB;gBACAX,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAAc,GAAG,CAArC,IAA0CE,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAAxD;cACH,CAHD,MAIK,IAAI,KAAKjE,QAAL,CAAc8E,IAAd,CAAmBN,UAAnB,CAAJ,EAAoC;gBACrC,KAAKxE,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;;gBACA,IAAIH,aAAJ,EAAmB;kBACf,KAAK1C,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2ChB,cAAc,GAAG,CAA5D,EAA+DA,cAAc,GAAG,CAAhF;gBACH,CAFD,MAGK;kBACDG,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAAc,GAAG,CAArC,IAA0CE,UAAU,CAACa,KAAX,CAAiBf,cAAjB,CAAxD;gBACH;cACJ,CARI,MASA,IAAIQ,gBAAgB,GAAG,CAAnB,IAAwBR,cAAc,GAAGQ,gBAA7C,EAA+D;gBAChE,MAAMS,YAAY,GAAG,KAAKC,aAAL,MAAwB,CAAC,KAAK1G,iBAAL,IAA0B,CAA3B,IAAgCmG,aAAxD,GAAwE,EAAxE,GAA6E,GAAlG;gBACAR,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAAc,GAAG,CAArC,IAA0CiB,YAA1C,GAAyDf,UAAU,CAACa,KAAX,CAAiBf,cAAjB,CAAvE;cACH,CAHI,MAIA,IAAIS,6BAA6B,KAAK,CAAtC,EAAyC;gBAC1CN,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAAc,GAAG,CAArC,IAA0C,GAA1C,GAAgDE,UAAU,CAACa,KAAX,CAAiBf,cAAjB,CAA9D;gBACAG,WAAW,GAAG,KAAK7C,UAAL,CAAgB6C,WAAhB,IAA+B,CAA/B,GAAmCA,WAAnC,GAAiD,EAA/D;cACH,CAHI,MAIA;gBACDA,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAAc,GAAG,CAArC,IAA0CE,UAAU,CAACa,KAAX,CAAiBf,cAAjB,CAAxD;cACH;YACJ;;YACD,KAAKmB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC,IAArC,EAA2C,eAA3C;UACH,CA/BD,MAgCK;YACDA,WAAW,GAAG,KAAKiB,WAAL,CAAiBlB,UAAjB,EAA6BF,cAA7B,EAA6CC,YAA7C,CAAd;YACA,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC,IAArC,EAA2C,cAA3C;UACH;;UACD;QACH;MACD;;MACA,KAAK,EAAL;QACIxC,KAAK,CAACmB,cAAN;;QACA,IAAIkB,cAAc,KAAKC,YAAvB,EAAqC;UACjC,MAAMM,UAAU,GAAGL,UAAU,CAACpD,MAAX,CAAkBkD,cAAlB,CAAnB;UACA,MAAM;YAAEQ,gBAAF;YAAoBC;UAApB,IAAsD,KAAKC,qBAAL,CAA2BR,UAA3B,CAA5D;;UACA,IAAI,KAAKG,aAAL,CAAmBE,UAAnB,CAAJ,EAAoC;YAChC,MAAMI,aAAa,GAAG,KAAKC,gBAAL,CAAsBV,UAAtB,CAAtB;;YACA,IAAI,KAAKzE,MAAL,CAAYoF,IAAZ,CAAiBN,UAAjB,CAAJ,EAAkC;cAC9B,KAAK9E,MAAL,CAAYqF,SAAZ,GAAwB,CAAxB;cACAX,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsCE,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAApD;YACH,CAHD,MAIK,IAAI,KAAKjE,QAAL,CAAc8E,IAAd,CAAmBN,UAAnB,CAAJ,EAAoC;cACrC,KAAKxE,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;;cACA,IAAIH,aAAJ,EAAmB;gBACf,KAAK1C,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2ChB,cAAc,GAAG,CAA5D,EAA+DA,cAAc,GAAG,CAAhF;cACH,CAFD,MAGK;gBACDG,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsCE,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAApD;cACH;YACJ,CARI,MASA,IAAIQ,gBAAgB,GAAG,CAAnB,IAAwBR,cAAc,GAAGQ,gBAA7C,EAA+D;cAChE,MAAMS,YAAY,GAAG,KAAKC,aAAL,MAAwB,CAAC,KAAK1G,iBAAL,IAA0B,CAA3B,IAAgCmG,aAAxD,GAAwE,EAAxE,GAA6E,GAAlG;cACAR,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsCiB,YAAtC,GAAqDf,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAAnE;YACH,CAHI,MAIA,IAAIS,6BAA6B,KAAK,CAAtC,EAAyC;cAC1CN,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsC,GAAtC,GAA4CE,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAA1D;cACAG,WAAW,GAAG,KAAK7C,UAAL,CAAgB6C,WAAhB,IAA+B,CAA/B,GAAmCA,WAAnC,GAAiD,EAA/D;YACH,CAHI,MAIA;cACDA,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsCE,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAG,CAAlC,CAApD;YACH;UACJ;;UACD,KAAKmB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC,IAArC,EAA2C,oBAA3C;QACH,CA/BD,MAgCK;UACDA,WAAW,GAAG,KAAKiB,WAAL,CAAiBlB,UAAjB,EAA6BF,cAA7B,EAA6CC,YAA7C,CAAd;UACA,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC,IAArC,EAA2C,cAA3C;QACH;;QACD;;MACJ;QACI;IAhHR;;IAkHA,KAAKtH,SAAL,CAAe8F,IAAf,CAAoBhB,KAApB;EACH;;EACD0D,eAAe,CAAC1D,KAAD,EAAQ;IACnB,IAAI,KAAKvF,QAAT,EAAmB;MACf;IACH;;IACDuF,KAAK,CAACmB,cAAN;IACA,IAAIwC,IAAI,GAAG3D,KAAK,CAACyC,KAAN,IAAezC,KAAK,CAACuB,OAAhC;IACA,IAAIqC,IAAI,GAAGC,MAAM,CAACC,YAAP,CAAoBH,IAApB,CAAX;IACA,MAAMI,aAAa,GAAG,KAAKA,aAAL,CAAmBH,IAAnB,CAAtB;IACA,MAAMI,WAAW,GAAG,KAAKA,WAAL,CAAiBJ,IAAjB,CAApB;;IACA,IAAK,MAAMD,IAAN,IAAcA,IAAI,IAAI,EAAvB,IAA8BK,WAA9B,IAA6CD,aAAjD,EAAgE;MAC5D,KAAKE,MAAL,CAAYjE,KAAZ,EAAmB4D,IAAnB,EAAyB;QAAEG,aAAF;QAAiBC;MAAjB,CAAzB;IACH;EACJ;;EACDE,OAAO,CAAClE,KAAD,EAAQ;IACX,IAAI,CAAC,KAAKvE,QAAN,IAAkB,CAAC,KAAKhB,QAA5B,EAAsC;MAClCuF,KAAK,CAACmB,cAAN;MACA,IAAIgD,IAAI,GAAG,CAACnE,KAAK,CAACoE,aAAN,IAAuBC,MAAM,CAAC,eAAD,CAA9B,EAAiDC,OAAjD,CAAyD,MAAzD,CAAX;;MACA,IAAIH,IAAJ,EAAU;QACN,IAAII,YAAY,GAAG,KAAK5E,UAAL,CAAgBwE,IAAhB,CAAnB;;QACA,IAAII,YAAY,IAAI,IAApB,EAA0B;UACtB,KAAKN,MAAL,CAAYjE,KAAZ,EAAmBuE,YAAY,CAAC7E,QAAb,EAAnB;QACH;MACJ;IACJ;EACJ;;EACD8E,cAAc,GAAG;IACb,OAAO,KAAKC,GAAL,IAAY,IAAZ,IAAoB,KAAKA,GAAL,GAAW,CAAtC;EACH;;EACDT,WAAW,CAACJ,IAAD,EAAO;IACd,IAAI,KAAK5F,UAAL,CAAgBkF,IAAhB,CAAqBU,IAArB,KAA8BA,IAAI,KAAK,GAA3C,EAAgD;MAC5C,KAAK5F,UAAL,CAAgBmF,SAAhB,GAA4B,CAA5B;MACA,OAAO,IAAP;IACH;;IACD,OAAO,KAAP;EACH;;EACDY,aAAa,CAACH,IAAD,EAAO;IAChB,IAAI,KAAKxF,QAAL,CAAc8E,IAAd,CAAmBU,IAAnB,CAAJ,EAA8B;MAC1B,KAAKxF,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;MACA,OAAO,IAAP;IACH;;IACD,OAAO,KAAP;EACH;;EACDI,aAAa,GAAG;IACZ,OAAO,KAAK3I,IAAL,KAAc,SAArB;EACH;;EACDmI,qBAAqB,CAAC2B,GAAD,EAAM;IACvB,IAAI7B,gBAAgB,GAAG6B,GAAG,CAACC,MAAJ,CAAW,KAAKvG,QAAhB,CAAvB;IACA,KAAKA,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;IACA,MAAMyB,WAAW,GAAGF,GAAG,CAAC5F,OAAJ,CAAY,KAAKN,OAAjB,EAA0B,EAA1B,EAA8BU,IAA9B,GAAqCJ,OAArC,CAA6C,KAA7C,EAAoD,EAApD,EAAwDA,OAAxD,CAAgE,KAAKZ,SAArE,EAAgF,EAAhF,CAApB;IACA,MAAM4E,6BAA6B,GAAG8B,WAAW,CAACD,MAAZ,CAAmB,KAAKvG,QAAxB,CAAtC;IACA,KAAKA,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;IACA,OAAO;MAAEN,gBAAF;MAAoBC;IAApB,CAAP;EACH;;EACD+B,cAAc,CAACH,GAAD,EAAM;IAChB,MAAM7B,gBAAgB,GAAG6B,GAAG,CAACC,MAAJ,CAAW,KAAKvG,QAAhB,CAAzB;IACA,KAAKA,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;IACA,MAAM2B,cAAc,GAAGJ,GAAG,CAACC,MAAJ,CAAW,KAAK3G,UAAhB,CAAvB;IACA,KAAKA,UAAL,CAAgBmF,SAAhB,GAA4B,CAA5B;IACA,MAAM4B,eAAe,GAAGL,GAAG,CAACC,MAAJ,CAAW,KAAKrG,OAAhB,CAAxB;IACA,KAAKA,OAAL,CAAa6E,SAAb,GAAyB,CAAzB;IACA,MAAM6B,iBAAiB,GAAGN,GAAG,CAACC,MAAJ,CAAW,KAAKzG,SAAhB,CAA1B;IACA,KAAKA,SAAL,CAAeiF,SAAf,GAA2B,CAA3B;IACA,OAAO;MAAEN,gBAAF;MAAoBiC,cAApB;MAAoCC,eAApC;MAAqDC;IAArD,CAAP;EACH;;EACDf,MAAM,CAACjE,KAAD,EAAQnB,IAAR,EAAcoG,IAAI,GAAG;IAAElB,aAAa,EAAE,KAAjB;IAAwBC,WAAW,EAAE;EAArC,CAArB,EAAmE;IACrE,MAAMkB,oBAAoB,GAAGrG,IAAI,CAAC8F,MAAL,CAAY,KAAK3G,UAAjB,CAA7B;IACA,KAAKA,UAAL,CAAgBmF,SAAhB,GAA4B,CAA5B;;IACA,IAAI,CAAC,KAAKqB,cAAL,EAAD,IAA0BU,oBAAoB,KAAK,CAAC,CAAxD,EAA2D;MACvD;IACH;;IACD,IAAI7C,cAAc,GAAG,KAAK/B,KAAL,CAAWC,aAAX,CAAyB8B,cAA9C;IACA,IAAIC,YAAY,GAAG,KAAKhC,KAAL,CAAWC,aAAX,CAAyB+B,YAA5C;IACA,IAAIC,UAAU,GAAG,KAAKjC,KAAL,CAAWC,aAAX,CAAyBf,KAAzB,CAA+BN,IAA/B,EAAjB;IACA,MAAM;MAAE2D,gBAAF;MAAoBiC,cAApB;MAAoCC,eAApC;MAAqDC;IAArD,IAA2E,KAAKH,cAAL,CAAoBtC,UAApB,CAAjF;IACA,IAAIC,WAAJ;;IACA,IAAIyC,IAAI,CAACjB,WAAT,EAAsB;MAClB,IAAI3B,cAAc,KAAK,CAAvB,EAA0B;QACtBG,WAAW,GAAGD,UAAd;;QACA,IAAIuC,cAAc,KAAK,CAAC,CAApB,IAAyBxC,YAAY,KAAK,CAA9C,EAAiD;UAC7CE,WAAW,GAAG,KAAK2C,UAAL,CAAgB5C,UAAhB,EAA4B1D,IAA5B,EAAkC,CAAlC,EAAqCyD,YAArC,CAAd;QACH;;QACD,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC3D,IAArC,EAA2C,QAA3C;MACH;IACJ,CARD,MASK,IAAIoG,IAAI,CAAClB,aAAT,EAAwB;MACzB,IAAIlB,gBAAgB,GAAG,CAAnB,IAAwBR,cAAc,KAAKQ,gBAA/C,EAAiE;QAC7D,KAAKW,WAAL,CAAiBxD,KAAjB,EAAwBuC,UAAxB,EAAoC1D,IAApC,EAA0C,QAA1C;MACH,CAFD,MAGK,IAAIgE,gBAAgB,GAAGR,cAAnB,IAAqCQ,gBAAgB,GAAGP,YAA5D,EAA0E;QAC3EE,WAAW,GAAG,KAAK2C,UAAL,CAAgB5C,UAAhB,EAA4B1D,IAA5B,EAAkCwD,cAAlC,EAAkDC,YAAlD,CAAd;QACA,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC3D,IAArC,EAA2C,QAA3C;MACH,CAHI,MAIA,IAAIgE,gBAAgB,KAAK,CAAC,CAAtB,IAA2B,KAAK9F,iBAApC,EAAuD;QACxDyF,WAAW,GAAG,KAAK2C,UAAL,CAAgB5C,UAAhB,EAA4B1D,IAA5B,EAAkCwD,cAAlC,EAAkDC,YAAlD,CAAd;QACA,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC3D,IAArC,EAA2C,QAA3C;MACH;IACJ,CAZI,MAaA;MACD,MAAM9B,iBAAiB,GAAG,KAAKC,YAAL,CAAkBoI,eAAlB,GAAoCtI,qBAA9D;MACA,MAAMuI,SAAS,GAAGhD,cAAc,KAAKC,YAAnB,GAAkC,cAAlC,GAAmD,QAArE;;MACA,IAAIO,gBAAgB,GAAG,CAAnB,IAAwBR,cAAc,GAAGQ,gBAA7C,EAA+D;QAC3D,IAAKR,cAAc,GAAGxD,IAAI,CAAC8B,MAAtB,IAAgCkC,gBAAgB,GAAG,CAAnD,CAAD,IAA2D9F,iBAA/D,EAAkF;UAC9E,MAAMuI,SAAS,GAAGN,iBAAiB,IAAI3C,cAArB,GAAsC2C,iBAAiB,GAAG,CAA1D,GAA+DD,eAAe,IAAI1C,cAAnB,GAAoC0C,eAApC,GAAsDxC,UAAU,CAAC5B,MAAlJ;UACA6B,WAAW,GAAGD,UAAU,CAACa,KAAX,CAAiB,CAAjB,EAAoBf,cAApB,IAAsCxD,IAAtC,GAA6C0D,UAAU,CAACa,KAAX,CAAiBf,cAAc,GAAGxD,IAAI,CAAC8B,MAAvC,EAA+C2E,SAA/C,CAA7C,GAAyG/C,UAAU,CAACa,KAAX,CAAiBkC,SAAjB,CAAvH;UACA,KAAK9B,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC3D,IAArC,EAA2CwG,SAA3C;QACH;MACJ,CAND,MAOK;QACD7C,WAAW,GAAG,KAAK2C,UAAL,CAAgB5C,UAAhB,EAA4B1D,IAA5B,EAAkCwD,cAAlC,EAAkDC,YAAlD,CAAd;QACA,KAAKkB,WAAL,CAAiBxD,KAAjB,EAAwBwC,WAAxB,EAAqC3D,IAArC,EAA2CwG,SAA3C;MACH;IACJ;EACJ;;EACDF,UAAU,CAAC3F,KAAD,EAAQX,IAAR,EAAc0G,KAAd,EAAqBC,GAArB,EAA0B;IAChC,IAAIC,SAAS,GAAG5G,IAAI,KAAK,GAAT,GAAeA,IAAf,GAAsBA,IAAI,CAACQ,KAAL,CAAW,GAAX,CAAtC;;IACA,IAAIoG,SAAS,CAAC9E,MAAV,KAAqB,CAAzB,EAA4B;MACxB,MAAMkC,gBAAgB,GAAGrD,KAAK,CAAC4D,KAAN,CAAYmC,KAAZ,EAAmBC,GAAnB,EAAwBb,MAAxB,CAA+B,KAAKvG,QAApC,CAAzB;MACA,KAAKA,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;MACA,OAAQN,gBAAgB,GAAG,CAApB,GAAyBrD,KAAK,CAAC4D,KAAN,CAAY,CAAZ,EAAemC,KAAf,IAAwB,KAAKhG,WAAL,CAAiBV,IAAjB,CAAxB,GAAiDW,KAAK,CAAC4D,KAAN,CAAYoC,GAAZ,CAA1E,GAA8FhG,KAAK,IAAI,KAAKD,WAAL,CAAiBV,IAAjB,CAA9G;IACH,CAJD,MAKK,IAAK2G,GAAG,GAAGD,KAAP,KAAkB/F,KAAK,CAACmB,MAA5B,EAAoC;MACrC,OAAO,KAAKpB,WAAL,CAAiBV,IAAjB,CAAP;IACH,CAFI,MAGA,IAAI0G,KAAK,KAAK,CAAd,EAAiB;MAClB,OAAO1G,IAAI,GAAGW,KAAK,CAAC4D,KAAN,CAAYoC,GAAZ,CAAd;IACH,CAFI,MAGA,IAAIA,GAAG,KAAKhG,KAAK,CAACmB,MAAlB,EAA0B;MAC3B,OAAOnB,KAAK,CAAC4D,KAAN,CAAY,CAAZ,EAAemC,KAAf,IAAwB1G,IAA/B;IACH,CAFI,MAGA;MACD,OAAOW,KAAK,CAAC4D,KAAN,CAAY,CAAZ,EAAemC,KAAf,IAAwB1G,IAAxB,GAA+BW,KAAK,CAAC4D,KAAN,CAAYoC,GAAZ,CAAtC;IACH;EACJ;;EACD/B,WAAW,CAACjE,KAAD,EAAQ+F,KAAR,EAAeC,GAAf,EAAoB;IAC3B,IAAIhD,WAAJ;IACA,IAAKgD,GAAG,GAAGD,KAAP,KAAkB/F,KAAK,CAACmB,MAA5B,EACI6B,WAAW,GAAG,EAAd,CADJ,KAEK,IAAI+C,KAAK,KAAK,CAAd,EACD/C,WAAW,GAAGhD,KAAK,CAAC4D,KAAN,CAAYoC,GAAZ,CAAd,CADC,KAEA,IAAIA,GAAG,KAAKhG,KAAK,CAACmB,MAAlB,EACD6B,WAAW,GAAGhD,KAAK,CAAC4D,KAAN,CAAY,CAAZ,EAAemC,KAAf,CAAd,CADC,KAGD/C,WAAW,GAAGhD,KAAK,CAAC4D,KAAN,CAAY,CAAZ,EAAemC,KAAf,IAAwB/F,KAAK,CAAC4D,KAAN,CAAYoC,GAAZ,CAAtC;IACJ,OAAOhD,WAAP;EACH;;EACDkD,UAAU,GAAG;IACT,IAAIrD,cAAc,GAAG,KAAK/B,KAAL,CAAWC,aAAX,CAAyB8B,cAA9C;IACA,IAAIE,UAAU,GAAG,KAAKjC,KAAL,CAAWC,aAAX,CAAyBf,KAA1C;IACA,IAAImG,WAAW,GAAGpD,UAAU,CAAC5B,MAA7B;IACA,IAAIrD,KAAK,GAAG,IAAZ,CAJS,CAKT;;IACA,IAAIsI,YAAY,GAAG,CAAC,KAAKrK,UAAL,IAAmB,EAApB,EAAwBoF,MAA3C;IACA4B,UAAU,GAAGA,UAAU,CAACzD,OAAX,CAAmB,KAAKN,OAAxB,EAAiC,EAAjC,CAAb;IACA6D,cAAc,GAAGA,cAAc,GAAGuD,YAAlC;IACA,IAAIhC,IAAI,GAAGrB,UAAU,CAACpD,MAAX,CAAkBkD,cAAlB,CAAX;;IACA,IAAI,KAAKK,aAAL,CAAmBkB,IAAnB,CAAJ,EAA8B;MAC1B,OAAOvB,cAAc,GAAGuD,YAAxB;IACH,CAZQ,CAaT;;;IACA,IAAIlI,CAAC,GAAG2E,cAAc,GAAG,CAAzB;;IACA,OAAO3E,CAAC,IAAI,CAAZ,EAAe;MACXkG,IAAI,GAAGrB,UAAU,CAACpD,MAAX,CAAkBzB,CAAlB,CAAP;;MACA,IAAI,KAAKgF,aAAL,CAAmBkB,IAAnB,CAAJ,EAA8B;QAC1BtG,KAAK,GAAGI,CAAC,GAAGkI,YAAZ;QACA;MACH,CAHD,MAIK;QACDlI,CAAC;MACJ;IACJ;;IACD,IAAIJ,KAAK,KAAK,IAAd,EAAoB;MAChB,KAAKgD,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2C/F,KAAK,GAAG,CAAnD,EAAsDA,KAAK,GAAG,CAA9D;IACH,CAFD,MAGK;MACDI,CAAC,GAAG2E,cAAJ;;MACA,OAAO3E,CAAC,GAAGiI,WAAX,EAAwB;QACpB/B,IAAI,GAAGrB,UAAU,CAACpD,MAAX,CAAkBzB,CAAlB,CAAP;;QACA,IAAI,KAAKgF,aAAL,CAAmBkB,IAAnB,CAAJ,EAA8B;UAC1BtG,KAAK,GAAGI,CAAC,GAAGkI,YAAZ;UACA;QACH,CAHD,MAIK;UACDlI,CAAC;QACJ;MACJ;;MACD,IAAIJ,KAAK,KAAK,IAAd,EAAoB;QAChB,KAAKgD,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2C/F,KAA3C,EAAkDA,KAAlD;MACH;IACJ;;IACD,OAAOA,KAAK,IAAI,CAAhB;EACH;;EACDuI,YAAY,GAAG;IACX,IAAI,CAAC,KAAKpL,QAAV,EAAoB;MAChB,KAAKiL,UAAL;IACH;EACJ;;EACDhD,aAAa,CAACkB,IAAD,EAAO;IAChB,IAAIA,IAAI,CAACjD,MAAL,KAAgB,CAAhB,KAAsB,KAAKhD,QAAL,CAAcuF,IAAd,CAAmBU,IAAnB,KAA4B,KAAKxF,QAAL,CAAc8E,IAAd,CAAmBU,IAAnB,CAA5B,IAAwD,KAAK9F,MAAL,CAAYoF,IAAZ,CAAiBU,IAAjB,CAAxD,IAAkF,KAAK5F,UAAL,CAAgBkF,IAAhB,CAAqBU,IAArB,CAAxG,CAAJ,EAAyI;MACrI,KAAKkC,UAAL;MACA,OAAO,IAAP;IACH;;IACD,OAAO,KAAP;EACH;;EACDA,UAAU,GAAG;IACT,KAAKnI,QAAL,CAAcwF,SAAd,GAA0B,CAA1B;IACA,KAAK/E,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;IACA,KAAKrF,MAAL,CAAYqF,SAAZ,GAAwB,CAAxB;IACA,KAAKnF,UAAL,CAAgBmF,SAAhB,GAA4B,CAA5B;EACH;;EACDK,WAAW,CAACxD,KAAD,EAAQ+F,QAAR,EAAkBC,gBAAlB,EAAoCX,SAApC,EAA+C;IACtD,IAAIhF,YAAY,GAAG,KAAKC,KAAL,CAAWC,aAAX,CAAyBf,KAA5C;IACA,IAAIgB,QAAQ,GAAG,IAAf;;IACA,IAAIuF,QAAQ,IAAI,IAAhB,EAAsB;MAClBvF,QAAQ,GAAG,KAAKb,UAAL,CAAgBoG,QAAhB,CAAX;MACAvF,QAAQ,GAAG,CAACA,QAAD,IAAa,CAAC,KAAK7F,UAAnB,GAAgC,CAAhC,GAAoC6F,QAA/C;MACA,KAAKI,WAAL,CAAiBJ,QAAjB,EAA2BwF,gBAA3B,EAA6CX,SAA7C,EAAwDU,QAAxD;MACA,KAAKjF,aAAL,CAAmBd,KAAnB,EAA0BK,YAA1B,EAAwCG,QAAxC;IACH;EACJ;;EACDM,aAAa,CAACd,KAAD,EAAQK,YAAR,EAAsBG,QAAtB,EAAgC;IACzC,IAAI,KAAKyF,cAAL,CAAoB5F,YAApB,EAAkCG,QAAlC,CAAJ,EAAiD;MAC7C,KAAKzF,OAAL,CAAaiG,IAAb,CAAkB;QAAEkF,aAAa,EAAElG,KAAjB;QAAwBR,KAAK,EAAEgB;MAA/B,CAAlB;IACH;EACJ;;EACDyF,cAAc,CAAC5F,YAAD,EAAeG,QAAf,EAAyB;IACnC,IAAIA,QAAQ,KAAK,IAAb,IAAqBH,YAAY,KAAK,IAA1C,EAAgD;MAC5C,OAAO,IAAP;IACH;;IACD,IAAIG,QAAQ,IAAI,IAAhB,EAAsB;MAClB,IAAI2F,kBAAkB,GAAI,OAAO9F,YAAP,KAAwB,QAAzB,GAAqC,KAAKV,UAAL,CAAgBU,YAAhB,CAArC,GAAqEA,YAA9F;MACA,OAAOG,QAAQ,KAAK2F,kBAApB;IACH;;IACD,OAAO,KAAP;EACH;;EACD1F,aAAa,CAACjB,KAAD,EAAQ;IACjB,IAAIA,KAAK,KAAK,GAAV,IAAiBA,KAAK,IAAI,IAA9B,EAAoC;MAChC,OAAO,IAAP;IACH;;IACD,IAAI,KAAKiF,GAAL,IAAY,IAAZ,IAAoBjF,KAAK,GAAG,KAAKiF,GAArC,EAA0C;MACtC,OAAO,KAAKA,GAAZ;IACH;;IACD,IAAI,KAAK2B,GAAL,IAAY,IAAZ,IAAoB5G,KAAK,GAAG,KAAK4G,GAArC,EAA0C;MACtC,OAAO,KAAKA,GAAZ;IACH;;IACD,OAAO5G,KAAP;EACH;;EACDoB,WAAW,CAACpB,KAAD,EAAQwG,gBAAR,EAA0BX,SAA1B,EAAqCU,QAArC,EAA+C;IACtDC,gBAAgB,GAAGA,gBAAgB,IAAI,EAAvC;IACA,IAAIzD,UAAU,GAAG,KAAKjC,KAAL,CAAWC,aAAX,CAAyBf,KAA1C;IACA,IAAIgB,QAAQ,GAAG,KAAKjB,WAAL,CAAiBC,KAAjB,CAAf;IACA,IAAI6G,aAAa,GAAG9D,UAAU,CAAC5B,MAA/B;;IACA,IAAIH,QAAQ,KAAKuF,QAAjB,EAA2B;MACvBvF,QAAQ,GAAG,KAAK8F,YAAL,CAAkB9F,QAAlB,EAA4BuF,QAA5B,CAAX;IACH;;IACD,IAAIM,aAAa,KAAK,CAAtB,EAAyB;MACrB,KAAK/F,KAAL,CAAWC,aAAX,CAAyBf,KAAzB,GAAiCgB,QAAjC;MACA,KAAKF,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2C,CAA3C,EAA8C,CAA9C;MACA,MAAM/F,KAAK,GAAG,KAAKoI,UAAL,EAAd;MACA,MAAMpD,YAAY,GAAGhF,KAAK,GAAG0I,gBAAgB,CAACrF,MAA9C;MACA,KAAKL,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;IACH,CAND,MAOK;MACD,IAAID,cAAc,GAAG,KAAK/B,KAAL,CAAWC,aAAX,CAAyB8B,cAA9C;MACA,IAAIC,YAAY,GAAG,KAAKhC,KAAL,CAAWC,aAAX,CAAyB+B,YAA5C;;MACA,IAAI,KAAK5B,SAAL,IAAkB,KAAKA,SAAL,GAAiBF,QAAQ,CAACG,MAAhD,EAAwD;QACpD;MACH;;MACD,KAAKL,KAAL,CAAWC,aAAX,CAAyBf,KAAzB,GAAiCgB,QAAjC;MACA,IAAI+F,SAAS,GAAG/F,QAAQ,CAACG,MAAzB;;MACA,IAAI0E,SAAS,KAAK,cAAlB,EAAkC;QAC9B,MAAMmB,UAAU,GAAG,KAAK7G,UAAL,CAAgB,CAAC4C,UAAU,IAAI,EAAf,EAAmBa,KAAnB,CAAyB,CAAzB,EAA4Bf,cAA5B,CAAhB,CAAnB;QACA,MAAMoE,aAAa,GAAGD,UAAU,KAAK,IAAf,GAAsBA,UAAU,CAAC9G,QAAX,EAAtB,GAA8C,EAApE;QACA,MAAMgH,SAAS,GAAGD,aAAa,CAACpH,KAAd,CAAoB,EAApB,EAAwBxB,IAAxB,CAA8B,IAAG,KAAKvC,SAAU,IAAhD,CAAlB;QACA,MAAMqL,MAAM,GAAG,IAAI/I,MAAJ,CAAW8I,SAAX,EAAsB,GAAtB,CAAf;QACAC,MAAM,CAACzD,IAAP,CAAY1C,QAAZ;QACA,MAAMoG,KAAK,GAAGZ,gBAAgB,CAAC3G,KAAjB,CAAuB,EAAvB,EAA2BxB,IAA3B,CAAiC,IAAG,KAAKvC,SAAU,IAAnD,CAAd;QACA,MAAMuL,MAAM,GAAG,IAAIjJ,MAAJ,CAAWgJ,KAAX,EAAkB,GAAlB,CAAf;QACAC,MAAM,CAAC3D,IAAP,CAAY1C,QAAQ,CAAC4C,KAAT,CAAeuD,MAAM,CAACxD,SAAtB,CAAZ;QACAb,YAAY,GAAGqE,MAAM,CAACxD,SAAP,GAAmB0D,MAAM,CAAC1D,SAAzC;QACA,KAAK7C,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;MACH,CAXD,MAYK,IAAIiE,SAAS,KAAKF,aAAlB,EAAiC;QAClC,IAAIhB,SAAS,KAAK,QAAd,IAA0BA,SAAS,KAAK,oBAA5C,EACI,KAAK/E,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAAY,GAAG,CAA1D,EAA6DA,YAAY,GAAG,CAA5E,EADJ,KAEK,IAAI+C,SAAS,KAAK,eAAlB,EACD,KAAK/E,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAAY,GAAG,CAA1D,EAA6DA,YAAY,GAAG,CAA5E,EADC,KAEA,IAAI+C,SAAS,KAAK,cAAd,IAAgCA,SAAS,KAAK,MAAlD,EACD,KAAK/E,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;MACP,CAPI,MAQA,IAAI+C,SAAS,KAAK,oBAAlB,EAAwC;QACzC,IAAIyB,QAAQ,GAAGvE,UAAU,CAACpD,MAAX,CAAkBmD,YAAY,GAAG,CAAjC,CAAf;QACA,IAAIyE,QAAQ,GAAGxE,UAAU,CAACpD,MAAX,CAAkBmD,YAAlB,CAAf;QACA,IAAI0E,IAAI,GAAGX,aAAa,GAAGE,SAA3B;;QACA,IAAIU,WAAW,GAAG,KAAKnJ,MAAL,CAAYoF,IAAZ,CAAiB6D,QAAjB,CAAlB;;QACA,IAAIE,WAAW,IAAID,IAAI,KAAK,CAA5B,EAA+B;UAC3B1E,YAAY,IAAI,CAAhB;QACH,CAFD,MAGK,IAAI,CAAC2E,WAAD,IAAgB,KAAKvE,aAAL,CAAmBoE,QAAnB,CAApB,EAAkD;UACnDxE,YAAY,IAAK,CAAC,CAAD,GAAK0E,IAAN,GAAc,CAA9B;QACH;;QACD,KAAKlJ,MAAL,CAAYqF,SAAZ,GAAwB,CAAxB;QACA,KAAK7C,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;MACH,CAbI,MAcA,IAAIC,UAAU,KAAK,GAAf,IAAsB8C,SAAS,KAAK,QAAxC,EAAkD;QACnD,KAAK/E,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2C,CAA3C,EAA8C,CAA9C;QACA,MAAM/F,KAAK,GAAG,KAAKoI,UAAL,EAAd;QACA,MAAMpD,YAAY,GAAGhF,KAAK,GAAG0I,gBAAgB,CAACrF,MAAzB,GAAkC,CAAvD;QACA,KAAKL,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;MACH,CALI,MAMA;QACDA,YAAY,GAAGA,YAAY,IAAIiE,SAAS,GAAGF,aAAhB,CAA3B;QACA,KAAK/F,KAAL,CAAWC,aAAX,CAAyB8C,iBAAzB,CAA2Cf,YAA3C,EAAyDA,YAAzD;MACH;IACJ;;IACD,KAAKhC,KAAL,CAAWC,aAAX,CAAyBoC,YAAzB,CAAsC,eAAtC,EAAuDnD,KAAvD;EACH;;EACD8G,YAAY,CAACY,IAAD,EAAOC,IAAP,EAAa;IACrB,IAAID,IAAI,IAAIC,IAAZ,EAAkB;MACd,IAAItE,gBAAgB,GAAGsE,IAAI,CAACxC,MAAL,CAAY,KAAKvG,QAAjB,CAAvB;MACA,KAAKA,QAAL,CAAc+E,SAAd,GAA0B,CAA1B;MACA,OAAON,gBAAgB,KAAK,CAAC,CAAtB,GAA2BqE,IAAI,CAAC7H,KAAL,CAAW,KAAKjB,QAAhB,EAA0B,CAA1B,IAA+B+I,IAAI,CAAC/D,KAAL,CAAWP,gBAAX,CAA1D,GAA0FqE,IAAjG;IACH;;IACD,OAAOA,IAAP;EACH;;EACDjE,gBAAgB,CAACzD,KAAD,EAAQ;IACpB,IAAIA,KAAJ,EAAW;MACP,MAAM4H,UAAU,GAAG5H,KAAK,CAACH,KAAN,CAAY,KAAKjB,QAAjB,CAAnB;;MACA,IAAIgJ,UAAU,CAACzG,MAAX,KAAsB,CAA1B,EAA6B;QACzB,OAAOyG,UAAU,CAAC,CAAD,CAAV,CAActI,OAAd,CAAsB,KAAKR,OAA3B,EAAoC,EAApC,EACFY,IADE,GAEFJ,OAFE,CAEM,KAFN,EAEa,EAFb,EAGFA,OAHE,CAGM,KAAKZ,SAHX,EAGsB,EAHtB,EAG0ByC,MAHjC;MAIH;IACJ;;IACD,OAAO,CAAP;EACH;;EACD0G,YAAY,CAACrH,KAAD,EAAQ;IAChB,KAAKrE,OAAL,GAAe,IAAf;IACA,KAAKX,OAAL,CAAagG,IAAb,CAAkBhB,KAAlB;EACH;;EACDsH,WAAW,CAACtH,KAAD,EAAQ;IACf,KAAKrE,OAAL,GAAe,KAAf;IACA,IAAI6E,QAAQ,GAAG,KAAKC,aAAL,CAAmB,KAAKd,UAAL,CAAgB,KAAKW,KAAL,CAAWC,aAAX,CAAyBf,KAAzC,CAAnB,CAAf;IACA,KAAKc,KAAL,CAAWC,aAAX,CAAyBf,KAAzB,GAAiC,KAAKD,WAAL,CAAiBiB,QAAjB,CAAjC;IACA,KAAKF,KAAL,CAAWC,aAAX,CAAyBoC,YAAzB,CAAsC,eAAtC,EAAuDnC,QAAvD;IACA,KAAKK,WAAL,CAAiBb,KAAjB,EAAwBQ,QAAxB;IACA,KAAKvF,MAAL,CAAY+F,IAAZ,CAAiBhB,KAAjB;EACH;;EACDP,cAAc,GAAG;IACb,MAAMiF,GAAG,GAAG,CAAC,KAAKlF,KAAN,IAAe,CAAC,KAAK7E,UAArB,GAAkC,CAAlC,GAAsC,KAAK6E,KAAvD;IACA,OAAO,KAAKD,WAAL,CAAiBmF,GAAjB,CAAP;EACH;;EACD7D,WAAW,CAACb,KAAD,EAAQR,KAAR,EAAe;IACtB,IAAI,KAAKA,KAAL,KAAeA,KAAnB,EAA0B;MACtB,KAAKA,KAAL,GAAaA,KAAb;MACA,KAAKpE,aAAL,CAAmBoE,KAAnB;IACH;;IACD,KAAKnE,cAAL;EACH;;EACDkM,UAAU,CAAC/H,KAAD,EAAQ;IACd,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKrF,EAAL,CAAQqN,YAAR;EACH;;EACDC,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAKtM,aAAL,GAAqBsM,EAArB;EACH;;EACDC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAKrM,cAAL,GAAsBqM,EAAtB;EACH;;EACDE,gBAAgB,CAAClD,GAAD,EAAM;IAClB,KAAKjJ,QAAL,GAAgBiJ,GAAhB;IACA,KAAKvK,EAAL,CAAQqN,YAAR;EACH;;EACS,IAANK,MAAM,GAAG;IACT,OAAQ,KAAKrI,KAAL,IAAc,IAAd,IAAsB,KAAKA,KAAL,CAAWE,QAAX,GAAsBiB,MAAtB,GAA+B,CAA7D;EACH;;EACD9E,UAAU,GAAG;IACT,IAAI,KAAKD,KAAT,EAAgB;MACZkM,aAAa,CAAC,KAAKlM,KAAN,CAAb;IACH;EACJ;;EACDmM,YAAY,GAAG;IACX,OAAO,KAAK/K,YAAZ;EACH;;AA5uBa;;AA8uBlBjD,WAAW,CAACiO,IAAZ;EAAA,iBAAwGjO,WAAxG,EAA8FpB,EAA9F,mBAAqIA,EAAE,CAACsP,UAAxI,GAA8FtP,EAA9F,mBAA+JA,EAAE,CAACuP,iBAAlK;AAAA;;AACAnO,WAAW,CAACoO,IAAZ,kBAD8FxP,EAC9F;EAAA,MAA4FoB,WAA5F;EAAA;EAAA;IAAA;MAD8FpB,EAC9F;IAAA;;IAAA;MAAA;;MAD8FA,EAC9F,qBAD8FA,EAC9F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD8FA,EAC9F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD8FA,EAC9F,oBAA89C,CAACiB,0BAAD,CAA99C,GAD8FjB,EAC9F;EAAA;EAAA;EAAA;EAAA;IAAA;MAD8FA,EAEtF,+CADR;MAD8FA,EAQ9E;QAAA,OAAS,uBAAT;MAAA;QAAA,OAAyC,0BAAzC;MAAA;QAAA,OAA6E,2BAA7E;MAAA;QAAA,OAA+G,mBAA/G;MAAA;QAAA,OAAyI,kBAAzI;MAAA;QAAA,OACS,wBADT;MAAA;QAAA,OACuC,uBADvC;MAAA,EAPhB;MAD8FA,EAKlF,eAJZ;MAD8FA,EAUlF,sDATZ;MAD8FA,EAWlF,6DAVZ;MAD8FA,EAiBlF,gEAhBZ;MAD8FA,EAmBlF,gEAlBZ;MAD8FA,EAqBtF,eApBR;IAAA;;IAAA;MAD8FA,EAI5D,2BAHlC;MAD8FA,EAEhF,uBAFgFA,EAEhF,6MADd;MAD8FA,EAKX,aAJnF;MAD8FA,EAKX,gCAJnF;MAD8FA,EAKpE,2JAJ1B;MAD8FA,EAKqD,yUAJnJ;MAD8FA,EAU9E,aAThB;MAD8FA,EAU9E,iFAThB;MAD8FA,EAWxC,aAVtD;MAD8FA,EAWxC,sEAVtD;MAD8FA,EAiB4E,aAhB1K;MAD8FA,EAiB4E,sEAhB1K;MAD8FA,EAmB8E,aAlB5K;MAD8FA,EAmB8E,sEAlB5K;IAAA;EAAA;EAAA,eAqBilEU,EAAE,CAAC+O,OArBplE,EAqB+qE/O,EAAE,CAACgP,IArBlrE,EAqBmxEhP,EAAE,CAACiP,OArBtxE,EAqBw2E/O,EAAE,CAACgP,SArB32E,EAqB66E9O,EAAE,CAAC+O,eArBh7E;EAAA;EAAA;EAAA;AAAA;;AAsBA;EAAA,mDAvB8F7P,EAuB9F,mBAA2FoB,WAA3F,EAAoH,CAAC;IACzG0O,IAAI,EAAE3P,SADmG;IAEzG4P,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6BC,QAAQ,EAAG;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KArBmB;MAqBZC,eAAe,EAAE9P,uBAAuB,CAAC+P,MArB7B;MAqBqCC,SAAS,EAAE,CAACnP,0BAAD,CArBhD;MAqB8EoP,aAAa,EAAEhQ,iBAAiB,CAACiQ,IArB/G;MAqBqHC,IAAI,EAAE;QACtH,SAAS,0BAD6G;QAEtH,iCAAiC,QAFqF;QAGtH,gCAAgC,SAHsF;QAItH,mCAAmC;MAJmF,CArB3H;MA0BIC,MAAM,EAAE,CAAC,ogEAAD;IA1BZ,CAAD;EAFmG,CAAD,CAApH,EA6B4B,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAE9P,EAAE,CAACsP;IAAX,CAAD,EAA0B;MAAEQ,IAAI,EAAE9P,EAAE,CAACuP;IAAX,CAA1B,CAAP;EAAmE,CA7B7G,EA6B+H;IAAE9N,WAAW,EAAE,CAAC;MAC/HqO,IAAI,EAAExP;IADyH,CAAD,CAAf;IAE/GoB,MAAM,EAAE,CAAC;MACToO,IAAI,EAAExP;IADG,CAAD,CAFuG;IAI/GqB,YAAY,EAAE,CAAC;MACfmO,IAAI,EAAExP;IADS,CAAD,CAJiG;IAM/GmQ,OAAO,EAAE,CAAC;MACVX,IAAI,EAAExP;IADI,CAAD,CANsG;IAQ/GoQ,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAExP;IADO,CAAD,CARmG;IAU/GwD,KAAK,EAAE,CAAC;MACRgM,IAAI,EAAExP;IADE,CAAD,CAVwG;IAY/GqQ,WAAW,EAAE,CAAC;MACdb,IAAI,EAAExP;IADQ,CAAD,CAZkG;IAc/GsQ,IAAI,EAAE,CAAC;MACPd,IAAI,EAAExP;IADC,CAAD,CAdyG;IAgB/GyH,SAAS,EAAE,CAAC;MACZ+H,IAAI,EAAExP;IADM,CAAD,CAhBoG;IAkB/GuQ,QAAQ,EAAE,CAAC;MACXf,IAAI,EAAExP;IADK,CAAD,CAlBqG;IAoB/GwQ,KAAK,EAAE,CAAC;MACRhB,IAAI,EAAExP;IADE,CAAD,CApBwG;IAsB/GyQ,SAAS,EAAE,CAAC;MACZjB,IAAI,EAAExP;IADM,CAAD,CAtBoG;IAwB/G0Q,YAAY,EAAE,CAAC;MACflB,IAAI,EAAExP;IADS,CAAD,CAxBiG;IA0B/G2Q,IAAI,EAAE,CAAC;MACPnB,IAAI,EAAExP;IADC,CAAD,CA1ByG;IA4B/G4Q,QAAQ,EAAE,CAAC;MACXpB,IAAI,EAAExP;IADK,CAAD,CA5BqG;IA8B/G6Q,YAAY,EAAE,CAAC;MACfrB,IAAI,EAAExP;IADS,CAAD,CA9BiG;IAgC/GwL,GAAG,EAAE,CAAC;MACNgE,IAAI,EAAExP;IADA,CAAD,CAhC0G;IAkC/GmN,GAAG,EAAE,CAAC;MACNqC,IAAI,EAAExP;IADA,CAAD,CAlC0G;IAoC/G8Q,oBAAoB,EAAE,CAAC;MACvBtB,IAAI,EAAExP;IADiB,CAAD,CApCyF;IAsC/G+Q,oBAAoB,EAAE,CAAC;MACvBvB,IAAI,EAAExP;IADiB,CAAD,CAtCyF;IAwC/GsB,mBAAmB,EAAE,CAAC;MACtBkO,IAAI,EAAExP;IADgB,CAAD,CAxC0F;IA0C/GuB,mBAAmB,EAAE,CAAC;MACtBiO,IAAI,EAAExP;IADgB,CAAD,CA1C0F;IA4C/GwB,QAAQ,EAAE,CAAC;MACXgO,IAAI,EAAExP;IADK,CAAD,CA5CqG;IA8C/GyB,IAAI,EAAE,CAAC;MACP+N,IAAI,EAAExP;IADC,CAAD,CA9CyG;IAgD/G0B,UAAU,EAAE,CAAC;MACb8N,IAAI,EAAExP;IADO,CAAD,CAhDmG;IAkD/GkE,MAAM,EAAE,CAAC;MACTsL,IAAI,EAAExP;IADG,CAAD,CAlDuG;IAoD/GuD,aAAa,EAAE,CAAC;MAChBiM,IAAI,EAAExP;IADU,CAAD,CApDgG;IAsD/G2B,IAAI,EAAE,CAAC;MACP6N,IAAI,EAAExP;IADC,CAAD,CAtDyG;IAwD/GyD,QAAQ,EAAE,CAAC;MACX+L,IAAI,EAAExP;IADK,CAAD,CAxDqG;IA0D/G0D,eAAe,EAAE,CAAC;MAClB8L,IAAI,EAAExP;IADY,CAAD,CA1D8F;IA4D/G4B,WAAW,EAAE,CAAC;MACd4N,IAAI,EAAExP;IADQ,CAAD,CA5DkG;IA8D/G4D,iBAAiB,EAAE,CAAC;MACpB4L,IAAI,EAAExP;IADc,CAAD,CA9D4F;IAgE/G8D,iBAAiB,EAAE,CAAC;MACpB0L,IAAI,EAAExP;IADc,CAAD,CAhE4F;IAkE/GmG,MAAM,EAAE,CAAC;MACTqJ,IAAI,EAAExP;IADG,CAAD,CAlEuG;IAoE/GqG,MAAM,EAAE,CAAC;MACTmJ,IAAI,EAAExP;IADG,CAAD,CApEuG;IAsE/GgR,UAAU,EAAE,CAAC;MACbxB,IAAI,EAAExP;IADO,CAAD,CAtEmG;IAwE/GiR,eAAe,EAAE,CAAC;MAClBzB,IAAI,EAAExP;IADY,CAAD,CAxE8F;IA0E/G6B,SAAS,EAAE,CAAC;MACZ2N,IAAI,EAAExP;IADM,CAAD,CA1EoG;IA4E/GqH,KAAK,EAAE,CAAC;MACRmI,IAAI,EAAEvP,SADE;MAERwP,IAAI,EAAE,CAAC,OAAD;IAFE,CAAD,CA5EwG;IA+E/G3N,OAAO,EAAE,CAAC;MACV0N,IAAI,EAAEtP;IADI,CAAD,CA/EsG;IAiF/G6B,OAAO,EAAE,CAAC;MACVyN,IAAI,EAAEtP;IADI,CAAD,CAjFsG;IAmF/G8B,MAAM,EAAE,CAAC;MACTwN,IAAI,EAAEtP;IADG,CAAD,CAnFuG;IAqF/G+B,SAAS,EAAE,CAAC;MACZuN,IAAI,EAAEtP;IADM,CAAD,CArFoG;IAuF/GgC,OAAO,EAAE,CAAC;MACVsN,IAAI,EAAEtP;IADI,CAAD,CAvFsG;IAyF/GsC,QAAQ,EAAE,CAAC;MACXgN,IAAI,EAAExP;IADK,CAAD;EAzFqG,CA7B/H;AAAA;;AAyHA,MAAMkR,iBAAN,CAAwB;;AAExBA,iBAAiB,CAACnC,IAAlB;EAAA,iBAA8GmC,iBAA9G;AAAA;;AACAA,iBAAiB,CAACC,IAAlB,kBAnJ8FzR,EAmJ9F;EAAA,MAA+GwR;AAA/G;AACAA,iBAAiB,CAACE,IAAlB,kBApJ8F1R,EAoJ9F;EAAA,UAA4IW,YAA5I,EAA0JE,eAA1J,EAA2KE,YAA3K;AAAA;;AACA;EAAA,mDArJ8Ff,EAqJ9F,mBAA2FwR,iBAA3F,EAA0H,CAAC;IAC/G1B,IAAI,EAAErP,QADyG;IAE/GsP,IAAI,EAAE,CAAC;MACC4B,OAAO,EAAE,CAAChR,YAAD,EAAeE,eAAf,EAAgCE,YAAhC,CADV;MAEC6Q,OAAO,EAAE,CAACxQ,WAAD,CAFV;MAGCyQ,YAAY,EAAE,CAACzQ,WAAD;IAHf,CAAD;EAFyG,CAAD,CAA1H;AAAA;AASA;AACA;AACA;;;AAEA,SAASH,0BAAT,EAAqCG,WAArC,EAAkDoQ,iBAAlD"}, "metadata": {}, "sourceType": "module"}