{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../service/product.service\";\nimport * as i2 from \"../../../services/statistics.service\";\nimport * as i3 from \"src/app/layout/service/app.layout.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/chart\";\nimport * as i6 from \"primeng/menu\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/api\";\nimport * as i9 from \"primeng/button\";\n\nfunction DashboardComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Image\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 67);\n    i0.ɵɵtext(4, \"Name \");\n    i0.ɵɵelement(5, \"p-sortIcon\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 69);\n    i0.ɵɵtext(7, \"Price \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 70);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"View\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction DashboardComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 71);\n    i0.ɵɵelement(2, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 73);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\", 74);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"currency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 75);\n    i0.ɵɵelement(9, \"button\", 76);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const product_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"assets/demo/images/product/\", product_r4.image, \"\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵpropertyInterpolate(\"alt\", product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 4, product_r4.price, \"USD\"));\n  }\n}\n\nconst _c0 = function () {\n  return {\n    width: \"2.5rem\",\n    height: \"2.5rem\"\n  };\n};\n\nconst _c1 = function () {\n  return {\n    height: \"8px\"\n  };\n};\n\nconst _c2 = function () {\n  return {\n    width: \"50%\"\n  };\n};\n\nconst _c3 = function () {\n  return {\n    width: \"16%\"\n  };\n};\n\nconst _c4 = function () {\n  return {\n    width: \"67%\"\n  };\n};\n\nconst _c5 = function () {\n  return {\n    width: \"35%\"\n  };\n};\n\nconst _c6 = function () {\n  return {\n    width: \"75%\"\n  };\n};\n\nconst _c7 = function () {\n  return {\n    width: \"40%\"\n  };\n};\n\nconst _c8 = \"linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)\";\n\nconst _c9 = function () {\n  return {\n    borderRadius: \"1rem\",\n    background: _c8\n  };\n};\n\nexport class DashboardComponent {\n  constructor(productService, statisticsService, layoutService) {\n    this.productService = productService;\n    this.statisticsService = statisticsService;\n    this.layoutService = layoutService; // Statistics data\n\n    this.dashboardStats = null;\n    this.monthlyFormations = [];\n    this.loading = true;\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initChart();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initChart();\n\n      _this.productService.getProductsSmall().then(data => _this.products = data);\n\n      _this.items = [{\n        label: 'Add New',\n        icon: 'pi pi-fw pi-plus'\n      }, {\n        label: 'Remove',\n        icon: 'pi pi-fw pi-minus'\n      }]; // Load statistics data\n\n      yield _this.loadDashboardData();\n    })();\n  }\n\n  loadDashboardData() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.loading = true; // Load dashboard stats\n\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats()); // Load monthly formations data\n\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations()); // Update chart with real data\n\n        _this2.updateFormationsChart();\n      } catch (error) {\n        console.error('Error loading dashboard data:', error);\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  initChart() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.chartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      datasets: [{\n        label: 'Formations',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate (%)',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4,\n        yAxisID: 'y1'\n      }]\n    };\n    this.chartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          type: 'linear',\n          display: true,\n          position: 'left',\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          ticks: {\n            color: textColorSecondary,\n            max: 100\n          },\n          grid: {\n            drawOnChartArea: false\n          }\n        }\n      }\n    };\n  }\n\n  updateFormationsChart() {\n    if (!this.monthlyFormations.length) return;\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const formationsData = new Array(12).fill(0);\n    const attendanceData = new Array(12).fill(0);\n    this.monthlyFormations.forEach(item => {\n      const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n      formationsData[monthIndex] = item.count;\n      attendanceData[monthIndex] = item.attendanceRate;\n    });\n    this.chartData = Object.assign(Object.assign({}, this.chartData), {\n      datasets: [Object.assign(Object.assign({}, this.chartData.datasets[0]), {\n        data: formationsData\n      }), Object.assign(Object.assign({}, this.chartData.datasets[1]), {\n        data: attendanceData\n      })]\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nDashboardComponent.ɵfac = function DashboardComponent_Factory(t) {\n  return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.ProductService), i0.ɵɵdirectiveInject(i2.StatisticsService), i0.ɵɵdirectiveInject(i3.LayoutService));\n};\n\nDashboardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DashboardComponent,\n  selectors: [[\"ng-component\"]],\n  decls: 200,\n  vars: 43,\n  consts: [[1, \"grid\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-3\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-shopping-cart\", \"text-blue-500\", \"text-xl\"], [1, \"text-green-500\", \"font-medium\"], [1, \"text-500\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-map-marker\", \"text-orange-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-cyan-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-inbox\", \"text-cyan-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-comment\", \"text-purple-500\", \"text-xl\"], [1, \"col-12\", \"xl:col-6\"], [1, \"card\"], [\"responsiveLayout\", \"scroll\", 3, \"value\", \"paginator\", \"rows\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-5\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-ellipsis-v\", 1, \"p-button-rounded\", \"p-button-text\", \"p-button-plain\", 3, \"click\"], [3, \"popup\", \"model\"], [\"menu\", \"\"], [1, \"list-none\", \"p-0\", \"m-0\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"md:justify-content-between\", \"mb-4\"], [1, \"text-900\", \"font-medium\", \"mr-2\", \"mb-1\", \"md:mb-0\"], [1, \"mt-1\", \"text-600\"], [1, \"mt-2\", \"md:mt-0\", \"flex\", \"align-items-center\"], [1, \"surface-300\", \"border-round\", \"overflow-hidden\", \"w-10rem\", \"lg:w-6rem\", 3, \"ngStyle\"], [1, \"bg-orange-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-orange-500\", \"ml-3\", \"font-medium\"], [1, \"mt-2\", \"md:mt-0\", \"ml-0\", \"md:ml-8\", \"flex\", \"align-items-center\"], [1, \"bg-cyan-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-cyan-500\", \"ml-3\", \"font-medium\"], [1, \"bg-pink-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-pink-500\", \"ml-3\", \"font-medium\"], [1, \"bg-green-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-green-500\", \"ml-3\", \"font-medium\"], [1, \"bg-purple-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-purple-500\", \"ml-3\", \"font-medium\"], [1, \"bg-teal-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-teal-500\", \"ml-3\", \"font-medium\"], [\"type\", \"line\", 3, \"data\", \"options\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-4\"], [1, \"block\", \"text-600\", \"font-medium\", \"mb-3\"], [1, \"p-0\", \"mx-0\", \"mt-0\", \"mb-4\", \"list-none\"], [1, \"flex\", \"align-items-center\", \"py-2\", \"border-bottom-1\", \"surface-border\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-circle\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi-dollar\", \"text-xl\", \"text-blue-500\"], [1, \"text-900\", \"line-height-3\"], [1, \"text-700\"], [1, \"text-blue-500\"], [1, \"flex\", \"align-items-center\", \"py-2\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-100\", \"border-circle\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi-download\", \"text-xl\", \"text-orange-500\"], [1, \"text-700\", \"line-height-3\"], [1, \"text-blue-500\", \"font-medium\"], [1, \"p-0\", \"m-0\", \"list-none\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-pink-100\", \"border-circle\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi-question\", \"text-xl\", \"text-pink-500\"], [1, \"px-4\", \"py-5\", \"shadow-2\", \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"justify-content-between\", \"mb-3\", 3, \"ngStyle\"], [1, \"text-blue-100\", \"font-medium\", \"text-xl\", \"mt-2\", \"mb-3\"], [1, \"text-white\", \"font-medium\", \"text-5xl\"], [1, \"mt-4\", \"mr-auto\", \"md:mt-0\", \"md:mr-0\"], [\"target\", \"_blank\", \"href\", \"https://www.primefaces.org/primeblocks-ng\", 1, \"p-button\", \"font-bold\", \"px-5\", \"py-3\", \"p-button-warning\", \"p-button-rounded\", \"p-button-raised\"], [\"pSortableColumn\", \"name\"], [\"field\", \"name\"], [\"pSortableColumn\", \"price\"], [\"field\", \"price\"], [2, \"width\", \"15%\", \"min-width\", \"5rem\"], [\"width\", \"50\", 1, \"shadow-4\", 3, \"src\", \"alt\"], [2, \"width\", \"35%\", \"min-width\", \"7rem\"], [2, \"width\", \"35%\", \"min-width\", \"8rem\"], [2, \"width\", \"15%\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-search\", 1, \"p-button\", \"p-component\", \"p-button-text\", \"p-button-icon-only\"]],\n  template: function DashboardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r5 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"span\", 4);\n      i0.ɵɵtext(6, \"Orders\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5);\n      i0.ɵɵtext(8, \"152\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelement(10, \"i\", 7);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"span\", 8);\n      i0.ɵɵtext(12, \"24 new \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(13, \"span\", 9);\n      i0.ɵɵtext(14, \"since last visit\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(15, \"div\", 1)(16, \"div\", 2)(17, \"div\", 3)(18, \"div\")(19, \"span\", 4);\n      i0.ɵɵtext(20, \"Revenue\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(21, \"div\", 5);\n      i0.ɵɵtext(22, \"$2.100\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(23, \"div\", 10);\n      i0.ɵɵelement(24, \"i\", 11);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(25, \"span\", 8);\n      i0.ɵɵtext(26, \"%52+ \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(27, \"span\", 9);\n      i0.ɵɵtext(28, \"since last week\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(29, \"div\", 1)(30, \"div\", 2)(31, \"div\", 3)(32, \"div\")(33, \"span\", 4);\n      i0.ɵɵtext(34, \"Customers\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(35, \"div\", 5);\n      i0.ɵɵtext(36, \"28441\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(37, \"div\", 12);\n      i0.ɵɵelement(38, \"i\", 13);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(39, \"span\", 8);\n      i0.ɵɵtext(40, \"520 \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"span\", 9);\n      i0.ɵɵtext(42, \"newly registered\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(43, \"div\", 1)(44, \"div\", 2)(45, \"div\", 3)(46, \"div\")(47, \"span\", 4);\n      i0.ɵɵtext(48, \"Comments\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(49, \"div\", 5);\n      i0.ɵɵtext(50, \"152 Unread\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(51, \"div\", 14);\n      i0.ɵɵelement(52, \"i\", 15);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(53, \"span\", 8);\n      i0.ɵɵtext(54, \"85 \");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(55, \"span\", 9);\n      i0.ɵɵtext(56, \"responded\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(57, \"div\", 16)(58, \"div\", 17)(59, \"h5\");\n      i0.ɵɵtext(60, \"Recent Sales\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(61, \"p-table\", 18);\n      i0.ɵɵtemplate(62, DashboardComponent_ng_template_62_Template, 11, 0, \"ng-template\", 19);\n      i0.ɵɵtemplate(63, DashboardComponent_ng_template_63_Template, 10, 7, \"ng-template\", 20);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(64, \"div\", 17)(65, \"div\", 21)(66, \"h5\");\n      i0.ɵɵtext(67, \"Best Selling Products\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(68, \"div\")(69, \"button\", 22);\n      i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_69_listener($event) {\n        i0.ɵɵrestoreView(_r5);\n\n        const _r2 = i0.ɵɵreference(71);\n\n        return i0.ɵɵresetView(_r2.toggle($event));\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(70, \"p-menu\", 23, 24);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(72, \"ul\", 25)(73, \"li\", 26)(74, \"div\")(75, \"span\", 27);\n      i0.ɵɵtext(76, \"Space T-Shirt\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(77, \"div\", 28);\n      i0.ɵɵtext(78, \"Clothing\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(79, \"div\", 29)(80, \"div\", 30);\n      i0.ɵɵelement(81, \"div\", 31);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(82, \"span\", 32);\n      i0.ɵɵtext(83, \"%50\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(84, \"li\", 26)(85, \"div\")(86, \"span\", 27);\n      i0.ɵɵtext(87, \"Portal Sticker\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(88, \"div\", 28);\n      i0.ɵɵtext(89, \"Accessories\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(90, \"div\", 33)(91, \"div\", 30);\n      i0.ɵɵelement(92, \"div\", 34);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(93, \"span\", 35);\n      i0.ɵɵtext(94, \"%16\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(95, \"li\", 26)(96, \"div\")(97, \"span\", 27);\n      i0.ɵɵtext(98, \"Supernova Sticker\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(99, \"div\", 28);\n      i0.ɵɵtext(100, \"Accessories\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(101, \"div\", 33)(102, \"div\", 30);\n      i0.ɵɵelement(103, \"div\", 36);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(104, \"span\", 37);\n      i0.ɵɵtext(105, \"%67\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(106, \"li\", 26)(107, \"div\")(108, \"span\", 27);\n      i0.ɵɵtext(109, \"Wonders Notebook\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(110, \"div\", 28);\n      i0.ɵɵtext(111, \"Office\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(112, \"div\", 33)(113, \"div\", 30);\n      i0.ɵɵelement(114, \"div\", 38);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(115, \"span\", 39);\n      i0.ɵɵtext(116, \"%35\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(117, \"li\", 26)(118, \"div\")(119, \"span\", 27);\n      i0.ɵɵtext(120, \"Mat Black Case\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(121, \"div\", 28);\n      i0.ɵɵtext(122, \"Accessories\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(123, \"div\", 33)(124, \"div\", 30);\n      i0.ɵɵelement(125, \"div\", 40);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(126, \"span\", 41);\n      i0.ɵɵtext(127, \"%75\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(128, \"li\", 26)(129, \"div\")(130, \"span\", 27);\n      i0.ɵɵtext(131, \"Robots T-Shirt\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(132, \"div\", 28);\n      i0.ɵɵtext(133, \"Clothing\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(134, \"div\", 33)(135, \"div\", 30);\n      i0.ɵɵelement(136, \"div\", 42);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(137, \"span\", 43);\n      i0.ɵɵtext(138, \"%40\");\n      i0.ɵɵelementEnd()()()()()();\n      i0.ɵɵelementStart(139, \"div\", 16)(140, \"div\", 17)(141, \"h5\");\n      i0.ɵɵtext(142, \"Sales Overview\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(143, \"p-chart\", 44);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(144, \"div\", 17)(145, \"div\", 45)(146, \"h5\");\n      i0.ɵɵtext(147, \"Notifications\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(148, \"div\")(149, \"button\", 22);\n      i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_149_listener($event) {\n        i0.ɵɵrestoreView(_r5);\n\n        const _r2 = i0.ɵɵreference(71);\n\n        return i0.ɵɵresetView(_r2.toggle($event));\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(150, \"p-menu\", 23, 24);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(152, \"span\", 46);\n      i0.ɵɵtext(153, \"TODAY\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(154, \"ul\", 47)(155, \"li\", 48)(156, \"div\", 49);\n      i0.ɵɵelement(157, \"i\", 50);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(158, \"span\", 51);\n      i0.ɵɵtext(159, \"Richard Jones \");\n      i0.ɵɵelementStart(160, \"span\", 52);\n      i0.ɵɵtext(161, \" has purchased a blue t-shirt for \");\n      i0.ɵɵelementStart(162, \"span\", 53);\n      i0.ɵɵtext(163, \"79$\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(164, \"li\", 54)(165, \"div\", 55);\n      i0.ɵɵelement(166, \"i\", 56);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(167, \"span\", 57);\n      i0.ɵɵtext(168, \"Your request for withdrawal of \");\n      i0.ɵɵelementStart(169, \"span\", 58);\n      i0.ɵɵtext(170, \"2500$\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtext(171, \" has been initiated.\");\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(172, \"span\", 46);\n      i0.ɵɵtext(173, \"YESTERDAY\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(174, \"ul\", 59)(175, \"li\", 48)(176, \"div\", 49);\n      i0.ɵɵelement(177, \"i\", 50);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(178, \"span\", 51);\n      i0.ɵɵtext(179, \"Keyser Wick \");\n      i0.ɵɵelementStart(180, \"span\", 52);\n      i0.ɵɵtext(181, \" has purchased a black jacket for \");\n      i0.ɵɵelementStart(182, \"span\", 53);\n      i0.ɵɵtext(183, \"59$\");\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(184, \"li\", 48)(185, \"div\", 60);\n      i0.ɵɵelement(186, \"i\", 61);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(187, \"span\", 51);\n      i0.ɵɵtext(188, \"Jane Davis\");\n      i0.ɵɵelementStart(189, \"span\", 52);\n      i0.ɵɵtext(190, \" has posted a new questions about your product.\");\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵelementStart(191, \"div\", 62)(192, \"div\")(193, \"div\", 63);\n      i0.ɵɵtext(194, \"TAKE THE NEXT STEP\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(195, \"div\", 64);\n      i0.ɵɵtext(196, \"Try PrimeBlocks\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(197, \"div\", 65)(198, \"a\", 66);\n      i0.ɵɵtext(199, \" Get Started \");\n      i0.ɵɵelementEnd()()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(26, _c0));\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(27, _c0));\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(28, _c0));\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(29, _c0));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"value\", ctx.products)(\"paginator\", true)(\"rows\", 5);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"popup\", true)(\"model\", ctx.items);\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(30, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(31, _c2));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(32, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(33, _c3));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(34, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(35, _c4));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(36, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(37, _c5));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(38, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(39, _c6));\n      i0.ɵɵadvance(10);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(40, _c1));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(41, _c7));\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"data\", ctx.chartData)(\"options\", ctx.chartOptions);\n      i0.ɵɵadvance(7);\n      i0.ɵɵproperty(\"popup\", true)(\"model\", ctx.items);\n      i0.ɵɵadvance(41);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(42, _c9));\n    }\n  },\n  dependencies: [i4.NgStyle, i5.UIChart, i6.Menu, i7.Table, i8.PrimeTemplate, i7.SortableColumn, i7.SortIcon, i9.ButtonDirective, i4.CurrencyPipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAKA,SAAuBA,aAAvB,QAA4C,MAA5C;;;;;;;;;;;;;;IC8DwBC,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAKA;IACTA;IAA2BA;IAAKA;IAAsCA;IACtEA;IAA4BA;IAAMA;IAAuCA;IACzEA;IAAIA;IAAIA;;;;;;IAIZA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IAEQA;IACJA;IACAA;IAAyCA;IAAgBA;IACzDA;IAAyCA;;IAAkCA;IAC3EA;IACIA;IACJA;;;;;IANSA;IAAAA;IAAoEA;IAEpCA;IAAAA;IACAA;IAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ADrErE,OAAM,MAAOC,kBAAP,CAAyB;EAiB3BC,YACYC,cADZ,EAEYC,iBAFZ,EAGWC,aAHX,EAGuC;IAF3B;IACA;IACD,mCAA4B,CARvC;;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC;IACA,eAAmB,IAAnB;IAOI,KAAKC,YAAL,GAAoB,KAAKD,aAAL,CAAmBE,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,SAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,SAAL;;MACA,KAAI,CAACN,cAAL,CAAoBQ,gBAApB,GAAuCC,IAAvC,CAA4CC,IAAI,IAAI,KAAI,CAACC,QAAL,GAAgBD,IAApE;;MAEA,KAAI,CAACE,KAAL,GAAa,CACT;QAAEC,KAAK,EAAE,SAAT;QAAoBC,IAAI,EAAE;MAA1B,CADS,EAET;QAAED,KAAK,EAAE,QAAT;QAAmBC,IAAI,EAAE;MAAzB,CAFS,CAAb,CAJU,CASV;;MACA,MAAM,KAAI,CAACC,iBAAL,EAAN;IAVU;EAWb;;EAEKA,iBAAiB;IAAA;;IAAA;MACnB,IAAI;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CADA,CAGA;;QACA,MAAI,CAACC,cAAL,SAA4BrB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBiB,iBAAvB,EAAD,CAAzC,CAJA,CAMA;;QACA,MAAI,CAACC,iBAAL,SAA+BvB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBmB,oBAAvB,EAAD,CAA5C,CAPA,CASA;;QACA,MAAI,CAACC,qBAAL;MAEH,CAZD,CAYE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;MACH,CAdD,SAcU;QACN,MAAI,CAACN,OAAL,GAAe,KAAf;MACH;IAjBkB;EAkBtB;;EAEDV,SAAS;IACL,MAAMkB,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB;IAEA,KAAKG,SAAL,GAAiB;MACbC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CADK;MAEbC,QAAQ,EAAE,CACN;QACIrB,KAAK,EAAE,YADX;QAEIH,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIyB,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMIS,OAAO,EAAE;MANb,CADM,EASN;QACIzB,KAAK,EAAE,qBADX;QAEIH,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIyB,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMIS,OAAO,EAAE,EANb;QAOIC,OAAO,EAAE;MAPb,CATM;IAFG,CAAjB;IAuBA,KAAKC,YAAL,GAAoB;MAChBC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJT,MAAM,EAAE;YACJU,KAAK,EAAEf;UADH;QADJ;MADH,CADO;MAQhBgB,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEb;UADJ,CADR;UAICiB,IAAI,EAAE;YACFJ,KAAK,EAAEZ,aADL;YAEFiB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCC,IAAI,EAAE,QADP;UAECC,OAAO,EAAE,IAFV;UAGCC,QAAQ,EAAE,MAHX;UAICN,KAAK,EAAE;YACHH,KAAK,EAAEb;UADJ,CAJR;UAOCiB,IAAI,EAAE;YACFJ,KAAK,EAAEZ,aADL;YAEFiB,UAAU,EAAE;UAFV;QAPP,CAVC;QAsBJK,EAAE,EAAE;UACAH,IAAI,EAAE,QADN;UAEAC,OAAO,EAAE,IAFT;UAGAC,QAAQ,EAAE,OAHV;UAIAN,KAAK,EAAE;YACHH,KAAK,EAAEb,kBADJ;YAEHwB,GAAG,EAAE;UAFF,CAJP;UAQAP,IAAI,EAAE;YACFQ,eAAe,EAAE;UADf;QARN;MAtBA;IARQ,CAApB;EA4CH;;EAEDlC,qBAAqB;IACjB,IAAI,CAAC,KAAKF,iBAAL,CAAuBqC,MAA5B,EAAoC;IAEpC,MAAMC,MAAM,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CAAf;IACA,MAAMC,cAAc,GAAG,IAAIC,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IACA,MAAMyB,cAAc,GAAG,IAAID,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IAEA,KAAKhB,iBAAL,CAAuB0C,OAAvB,CAA+BC,IAAI,IAAG;MAClC,MAAMC,UAAU,GAAG,IAAIC,IAAJ,CAASF,IAAI,CAACG,KAAL,GAAa,UAAtB,EAAkCC,QAAlC,EAAnB;MACAR,cAAc,CAACK,UAAD,CAAd,GAA6BD,IAAI,CAACK,KAAlC;MACAP,cAAc,CAACG,UAAD,CAAd,GAA6BD,IAAI,CAACM,cAAlC;IACH,CAJD;IAMA,KAAKpC,SAAL,GAAcqC,gCACP,KAAKrC,SADE,GACO;MACjBE,QAAQ,EAAE,iCAEC,KAAKF,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BxB,IAAI,EAAEgD;MADuB,EAF3B,kCAMC,KAAK1B,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BxB,IAAI,EAAEkD;MADuB,EAN3B;IADO,CADP,CAAd;EAaH;;EAEDU,WAAW;IACP,IAAI,KAAKnE,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBoE,WAAlB;IACH;EACJ;;AAvK0B;;;mBAAlBzE,oBAAkBD;AAAA;;;QAAlBC;EAAkB0E;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;;;MCX3B/E,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,MAAlB,EAAkB,CAAlB;MAKkEA;MAAMA;MACpDA;MAA0CA;MAAGA;MAEjDA;MACIA;MACJA;MAEJA;MAAyCA;MAAOA;MAChDA;MAAuBA;MAAgBA;MAG/CA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,CAAtC;MAI8DA;MAAOA;MACrDA;MAA0CA;MAAMA;MAEpDA;MACIA;MACJA;MAEJA;MAAyCA;MAAKA;MAC9CA;MAAuBA;MAAeA;MAG9CA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,CAAtC;MAI8DA;MAASA;MACvDA;MAA0CA;MAAKA;MAEnDA;MACIA;MACJA;MAEJA;MAAyCA;MAAKA;MAC9CA;MAAuBA;MAAgBA;MAG/CA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,CAAtC;MAI8DA;MAAQA;MACtDA;MAA0CA;MAAUA;MAExDA;MACIA;MACJA;MAEJA;MAAyCA;MAAGA;MAC5CA;MAAuBA;MAASA;MAIxCA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,IAA7B;MAEYA;MAAYA;MAChBA;MACIA;MAQAA;MAYJA;MAEJA,iCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,IAAlB;MAEYA;MAAqBA;MACzBA,6BAAK,EAAL,EAAK,QAAL,EAAK,EAAL;MACgHA;QAAAA;;QAAA;;QAAA,OAASA,kCAAT;MAA4B,CAA5B;MAA8BA;MAC1IA;MACJA;MAEJA,gCAA8B,EAA9B,EAA8B,IAA9B,EAA8B,EAA9B,EAA8B,EAA9B,EAA8B,KAA9B,EAA8B,EAA9B,EAA8B,MAA9B,EAA8B,EAA9B;MAGiEA;MAAaA;MAClEA;MAA2BA;MAAQA;MAEvCA,iCAAkD,EAAlD,EAAkD,KAAlD,EAAkD,EAAlD;MAEQA;MACJA;MACAA;MAA+CA;MAAGA;MAG1DA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;MAE6DA;MAAcA;MACnEA;MAA2BA;MAAWA;MAE1CA,iCAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;MAEQA;MACJA;MACAA;MAA6CA;MAAGA;MAGxDA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;MAE6DA;MAAiBA;MACtEA;MAA2BA;MAAWA;MAE1CA,kCAA+D,GAA/D,EAA+D,KAA/D,EAA+D,EAA/D;MAEQA;MACJA;MACAA;MAA6CA;MAAGA;MAGxDA,iCAA+F,GAA/F,EAA+F,KAA/F,EAA+F,GAA/F,EAA+F,MAA/F,EAA+F,EAA/F;MAE6DA;MAAgBA;MACrEA;MAA2BA;MAAMA;MAErCA,kCAA+D,GAA/D,EAA+D,KAA/D,EAA+D,EAA/D;MAEQA;MACJA;MACAA;MAA8CA;MAAGA;MAGzDA,iCAA+F,GAA/F,EAA+F,KAA/F,EAA+F,GAA/F,EAA+F,MAA/F,EAA+F,EAA/F;MAE6DA;MAAcA;MACnEA;MAA2BA;MAAWA;MAE1CA,kCAA+D,GAA/D,EAA+D,KAA/D,EAA+D,EAA/D;MAEQA;MACJA;MACAA;MAA+CA;MAAGA;MAG1DA,iCAA+F,GAA/F,EAA+F,KAA/F,EAA+F,GAA/F,EAA+F,MAA/F,EAA+F,EAA/F;MAE6DA;MAAcA;MACnEA;MAA2BA;MAAQA;MAEvCA,kCAA+D,GAA/D,EAA+D,KAA/D,EAA+D,EAA/D;MAEQA;MACJA;MACAA;MAA6CA;MAAGA;MAOpEA,kCAA6B,GAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,GAA7B,EAA6B,IAA7B;MAEYA;MAAcA;MAClBA;MACJA;MAEAA,kCAAkB,GAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,GAAlB,EAAkB,IAAlB;MAEYA;MAAaA;MACjBA,8BAAK,GAAL,EAAK,QAAL,EAAK,EAAL;MACgHA;QAAAA;;QAAA;;QAAA,OAASA,kCAAT;MAA4B,CAA5B;MAA8BA;MAC1IA;MACJA;MAGJA;MAA8CA;MAAKA;MACnDA,iCAAyC,GAAzC,EAAyC,IAAzC,EAAyC,EAAzC,EAAyC,GAAzC,EAAyC,KAAzC,EAAyC,EAAzC;MAGYA;MACJA;MACAA;MAAqCA;MACzCA;MAAwBA;MAAiCA;MAA4BA;MAAGA;MAGxFA,iCAAyC,GAAzC,EAAyC,KAAzC,EAAyC,EAAzC;MAEQA;MACJA;MACAA;MAAqCA;MAA+BA;MAAwCA;MAAKA;MAAQA;MAAmBA;MAIpJA;MAA8CA;MAASA;MACvDA,iCAA8B,GAA9B,EAA8B,IAA9B,EAA8B,EAA9B,EAA8B,GAA9B,EAA8B,KAA9B,EAA8B,EAA9B;MAGYA;MACJA;MACAA;MAAqCA;MACzCA;MAAwBA;MAAiCA;MAA4BA;MAAGA;MAGxFA,iCAAwE,GAAxE,EAAwE,KAAxE,EAAwE,EAAxE;MAEQA;MACJA;MACAA;MAAqCA;MAAUA;MAAwBA;MAA8CA;MAKjIA,kCAAoS,GAApS,EAAoS,KAApS,EAAoS,GAApS,EAAoS,KAApS,EAAoS,EAApS;MAE4DA;MAAkBA;MAC3EA;MAA6CA;MAAeA;MAEhEA,kCAA0C,GAA1C,EAA0C,GAA1C,EAA0C,EAA1C;MAEQA;MACJA;;;;MAjO0FA;MAAAA;MAeEA;MAAAA;MAeFA;MAAAA;MAeEA;MAAAA;MAYlFA;MAAAA,qCAAkB,WAAlB,EAAkB,IAAlB,EAAkB,MAAlB,EAAkB,CAAlB;MA4BaA;MAAAA,6BAAc,OAAd,EAAcgF,SAAd;MAU8DhF;MAAAA;MAClCA;MAAAA;MAWkCA;MAAAA;MACpCA;MAAAA;MAWoCA;MAAAA;MACpCA;MAAAA;MAWoCA;MAAAA;MACnCA;MAAAA;MAWmCA;MAAAA;MAClCA;MAAAA;MAWkCA;MAAAA;MACpCA;MAAAA;MAY3BA;MAAAA,qCAAkB,SAAlB,EAAkBgF,gBAAlB;MAQChF;MAAAA,6BAAc,OAAd,EAAcgF,SAAd;MAyCsFhF;MAAAA", "names": ["lastValueFrom", "i0", "DashboardComponent", "constructor", "productService", "statisticsService", "layoutService", "subscription", "configUpdate$", "subscribe", "initChart", "ngOnInit", "getProductsSmall", "then", "data", "products", "items", "label", "icon", "loadDashboardData", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "updateFormationsChart", "error", "console", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "chartData", "labels", "datasets", "fill", "backgroundColor", "borderColor", "tension", "yAxisID", "chartOptions", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "drawBorder", "y", "type", "display", "position", "y1", "max", "drawOnChartArea", "length", "months", "formationsData", "Array", "attendanceData", "for<PERSON>ach", "item", "monthIndex", "Date", "month", "getMonth", "count", "attendanceRate", "Object", "ngOnDestroy", "unsubscribe", "selectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { MenuItem } from 'primeng/api';\nimport { Product } from '../../api/product';\nimport { ProductService } from '../../service/product.service';\nimport { StatisticsService, DashboardStats, MonthlyFormations } from '../../../services/statistics.service';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\n\n@Component({\n    templateUrl: './dashboard.component.html',\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n\n    items!: MenuItem[];\n\n    products!: Product[];\n\n    chartData: any;\n\n    chartOptions: any;\n\n    subscription!: Subscription;\n\n    // Statistics data\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n    loading: boolean = true;\n\n    constructor(\n        private productService: ProductService,\n        private statisticsService: StatisticsService,\n        public layoutService: LayoutService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initChart();\n        });\n    }\n\n    async ngOnInit() {\n        this.initChart();\n        this.productService.getProductsSmall().then(data => this.products = data);\n\n        this.items = [\n            { label: 'Add New', icon: 'pi pi-fw pi-plus' },\n            { label: 'Remove', icon: 'pi pi-fw pi-minus' }\n        ];\n\n        // Load statistics data\n        await this.loadDashboardData();\n    }\n\n    async loadDashboardData() {\n        try {\n            this.loading = true;\n\n            // Load dashboard stats\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n\n            // Load monthly formations data\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n\n            // Update chart with real data\n            this.updateFormationsChart();\n\n        } catch (error) {\n            console.error('Error loading dashboard data:', error);\n        } finally {\n            this.loading = false;\n        }\n    }\n\n    initChart() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        this.chartData = {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate (%)',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4,\n                    yAxisID: 'y1'\n                }\n            ]\n        };\n\n        this.chartOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    type: 'linear',\n                    display: true,\n                    position: 'left',\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    ticks: {\n                        color: textColorSecondary,\n                        max: 100\n                    },\n                    grid: {\n                        drawOnChartArea: false,\n                    },\n                }\n            }\n        };\n    }\n\n    updateFormationsChart() {\n        if (!this.monthlyFormations.length) return;\n\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formationsData = new Array(12).fill(0);\n        const attendanceData = new Array(12).fill(0);\n\n        this.monthlyFormations.forEach(item => {\n            const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n            formationsData[monthIndex] = item.count;\n            attendanceData[monthIndex] = item.attendanceRate;\n        });\n\n        this.chartData = {\n            ...this.chartData,\n            datasets: [\n                {\n                    ...this.chartData.datasets[0],\n                    data: formationsData\n                },\n                {\n                    ...this.chartData.datasets[1],\n                    data: attendanceData\n                }\n            ]\n        };\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n", "    <div class=\"grid\">\n        <div class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Orders</span>\n                        <div class=\"text-900 font-medium text-xl\">152</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-shopping-cart text-blue-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">24 new </span>\n                <span class=\"text-500\">since last visit</span>\n            </div>\n        </div>\n        <div class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Revenue</span>\n                        <div class=\"text-900 font-medium text-xl\">$2.100</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-orange-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-map-marker text-orange-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">%52+ </span>\n                <span class=\"text-500\">since last week</span>\n            </div>\n        </div>\n        <div class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Customers</span>\n                        <div class=\"text-900 font-medium text-xl\">28441</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-cyan-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-inbox text-cyan-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">520  </span>\n                <span class=\"text-500\">newly registered</span>\n            </div>\n        </div>\n        <div class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Comments</span>\n                        <div class=\"text-900 font-medium text-xl\">152 Unread</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-purple-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-comment text-purple-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">85 </span>\n                <span class=\"text-500\">responded</span>\n            </div>\n        </div>\n\n        <div class=\"col-12 xl:col-6\">\n            <div class=\"card\">\n                <h5>Recent Sales</h5>\n                <p-table [value]=\"products\" [paginator]=\"true\" [rows]=\"5\" responsiveLayout=\"scroll\">\n                    <ng-template pTemplate=\"header\">\n                        <tr>\n                            <th>Image</th>\n                            <th pSortableColumn=\"name\">Name <p-sortIcon field=\"name\"></p-sortIcon></th>\n                            <th pSortableColumn=\"price\">Price <p-sortIcon field=\"price\"></p-sortIcon></th>\n                            <th>View</th>\n                        </tr>\n                    </ng-template>\n                    <ng-template pTemplate=\"body\" let-product>\n                        <tr>\n                            <td style=\"width: 15%; min-width: 5rem;\">\n                                <img src=\"assets/demo/images/product/{{product.image}}\" class=\"shadow-4\" alt=\"{{product.name}}\" width=\"50\">\n                            </td>\n                            <td style=\"width: 35%; min-width: 7rem;\">{{product.name}}</td>\n                            <td style=\"width: 35%; min-width: 8rem;\">{{product.price | currency:'USD'}}</td>\n                            <td style=\"width: 15%;\">\n                                <button pButton pRipple type=\"button\" icon=\"pi pi-search\" class=\"p-button p-component p-button-text p-button-icon-only\"></button>\n                            </td>\n                        </tr>\n                    </ng-template>\n                </p-table>\n            </div>\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center mb-5\">\n                    <h5>Best Selling Products</h5>\n                    <div>\n                        <button pButton type=\"button\" icon=\"pi pi-ellipsis-v\" class=\"p-button-rounded p-button-text p-button-plain\" (click)=\"menu.toggle($event)\"></button>\n                        <p-menu #menu [popup]=\"true\" [model]=\"items\"></p-menu>\n                    </div>\n                </div>\n                <ul class=\"list-none p-0 m-0\">\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Space T-Shirt</span>\n                            <div class=\"mt-1 text-600\">Clothing</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-orange-500 h-full\" [ngStyle]=\"{width: '50%'}\"></div>\n                            </div>\n                            <span class=\"text-orange-500 ml-3 font-medium\">%50</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Portal Sticker</span>\n                            <div class=\"mt-1 text-600\">Accessories</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-cyan-500 h-full\" [ngStyle]=\"{width: '16%'}\"></div>\n                            </div>\n                            <span class=\"text-cyan-500 ml-3 font-medium\">%16</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Supernova Sticker</span>\n                            <div class=\"mt-1 text-600\">Accessories</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-pink-500 h-full\" [ngStyle]=\"{width: '67%'}\"></div>\n                            </div>\n                            <span class=\"text-pink-500 ml-3 font-medium\">%67</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Wonders Notebook</span>\n                            <div class=\"mt-1 text-600\">Office</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-green-500 h-full\" [ngStyle]=\"{width: '35%'}\"></div>\n                            </div>\n                            <span class=\"text-green-500 ml-3 font-medium\">%35</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Mat Black Case</span>\n                            <div class=\"mt-1 text-600\">Accessories</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-purple-500 h-full\" [ngStyle]=\"{width: '75%'}\"></div>\n                            </div>\n                            <span class=\"text-purple-500 ml-3 font-medium\">%75</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Robots T-Shirt</span>\n                            <div class=\"mt-1 text-600\">Clothing</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-teal-500 h-full\" [ngStyle]=\"{width: '40%'}\"></div>\n                            </div>\n                            <span class=\"text-teal-500 ml-3 font-medium\">%40</span>\n                        </div>\n                    </li>\n                </ul>\n            </div>\n        </div>\n\n        <div class=\"col-12 xl:col-6\">\n            <div class=\"card\">\n                <h5>Sales Overview</h5>\n                <p-chart type=\"line\" [data]=\"chartData\" [options]=\"chartOptions\"></p-chart>\n            </div>\n\n            <div class=\"card\">\n                <div class=\"flex align-items-center justify-content-between mb-4\">\n                    <h5>Notifications</h5>\n                    <div>\n                        <button pButton type=\"button\" icon=\"pi pi-ellipsis-v\" class=\"p-button-rounded p-button-text p-button-plain\" (click)=\"menu.toggle($event)\"></button>\n                        <p-menu #menu [popup]=\"true\" [model]=\"items\"></p-menu>\n                    </div>\n                </div>\n\n                <span class=\"block text-600 font-medium mb-3\">TODAY</span>\n                <ul class=\"p-0 mx-0 mt-0 mb-4 list-none\">\n                    <li class=\"flex align-items-center py-2 border-bottom-1 surface-border\">\n                        <div class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\">\n                            <i class=\"pi pi-dollar text-xl text-blue-500\"></i>\n                        </div>\n                        <span class=\"text-900 line-height-3\">Richard Jones\n                    <span class=\"text-700\"> has purchased a blue t-shirt for <span class=\"text-blue-500\">79$</span></span>\n                </span>\n                    </li>\n                    <li class=\"flex align-items-center py-2\">\n                        <div class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-orange-100 border-circle mr-3 flex-shrink-0\">\n                            <i class=\"pi pi-download text-xl text-orange-500\"></i>\n                        </div>\n                        <span class=\"text-700 line-height-3\">Your request for withdrawal of <span class=\"text-blue-500 font-medium\">2500$</span> has been initiated.</span>\n                    </li>\n                </ul>\n\n                <span class=\"block text-600 font-medium mb-3\">YESTERDAY</span>\n                <ul class=\"p-0 m-0 list-none\">\n                    <li class=\"flex align-items-center py-2 border-bottom-1 surface-border\">\n                        <div class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-blue-100 border-circle mr-3 flex-shrink-0\">\n                            <i class=\"pi pi-dollar text-xl text-blue-500\"></i>\n                        </div>\n                        <span class=\"text-900 line-height-3\">Keyser Wick\n                    <span class=\"text-700\"> has purchased a black jacket for <span class=\"text-blue-500\">59$</span></span>\n                </span>\n                    </li>\n                    <li class=\"flex align-items-center py-2 border-bottom-1 surface-border\">\n                        <div class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-pink-100 border-circle mr-3 flex-shrink-0\">\n                            <i class=\"pi pi-question text-xl text-pink-500\"></i>\n                        </div>\n                        <span class=\"text-900 line-height-3\">Jane Davis<span class=\"text-700\"> has posted a new questions about your product.</span></span>\n                    </li>\n                </ul>\n            </div>\n\n            <div class=\"px-4 py-5 shadow-2 flex flex-column md:flex-row md:align-items-center justify-content-between mb-3\" [ngStyle]=\"{borderRadius: '1rem', background: 'linear-gradient(0deg, rgba(0, 123, 255, 0.5), rgba(0, 123, 255, 0.5)), linear-gradient(92.54deg, #1C80CF 47.88%, #FFFFFF 100.01%)'}\">\n           <div>\n               <div class=\"text-blue-100 font-medium text-xl mt-2 mb-3\">TAKE THE NEXT STEP</div>\n               <div class=\"text-white font-medium text-5xl\">Try PrimeBlocks</div>\n           </div>\n           <div class=\"mt-4 mr-auto md:mt-0 md:mr-0\">\n               <a target=\"_blank\" href=\"https://www.primefaces.org/primeblocks-ng\" class=\"p-button font-bold px-5 py-3 p-button-warning p-button-rounded p-button-raised\">\n                   Get Started\n               </a>\n           </div>\n        </div>\n    </div>\n</div>\n\n"]}, "metadata": {}, "sourceType": "module"}