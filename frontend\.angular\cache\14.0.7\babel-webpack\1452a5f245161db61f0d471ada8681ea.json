{"ast": null, "code": "import { ElementRef } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a data-bound value (typically a string) to a boolean. */\n\nfunction coerceBooleanProperty(value) {\n  return value != null && `${value}` !== 'false';\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n  return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\n\n\nfunction _isNumberValue(value) {\n  // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n  // and other non-number values as NaN, where Number just uses 0) but it considers the string\n  // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n  return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Coerces a value to a CSS pixel value. */\n\n\nfunction coerceCssPixelValue(value) {\n  if (value == null) {\n    return '';\n  }\n\n  return typeof value === 'string' ? value : `${value}px`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\n\n\nfunction coerceElement(elementOrRef) {\n  return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\n\n\nfunction coerceStringArray(value, separator = /\\s+/) {\n  const result = [];\n\n  if (value != null) {\n    const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n\n    for (const sourceValue of sourceValues) {\n      const trimmedString = `${sourceValue}`.trim();\n\n      if (trimmedString) {\n        result.push(trimmedString);\n      }\n    }\n  }\n\n  return result;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };", "map": {"version": 3, "names": ["ElementRef", "coerceBooleanProperty", "value", "coerceNumberProperty", "fallback<PERSON><PERSON><PERSON>", "_isNumberValue", "Number", "isNaN", "parseFloat", "coerce<PERSON><PERSON><PERSON>", "Array", "isArray", "coerceCssPixelValue", "coerceElement", "elementOrRef", "nativeElement", "coerce<PERSON><PERSON><PERSON><PERSON><PERSON>", "separator", "result", "sourceValues", "split", "sourceValue", "trimmedString", "trim", "push"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/coercion.mjs"], "sourcesContent": ["import { ElementRef } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Coerces a data-bound value (typically a string) to a boolean. */\nfunction coerceBooleanProperty(value) {\n    return value != null && `${value}` !== 'false';\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction coerceNumberProperty(value, fallbackValue = 0) {\n    return _isNumberValue(value) ? Number(value) : fallbackValue;\n}\n/**\n * Whether the provided value is considered a number.\n * @docs-private\n */\nfunction _isNumberValue(value) {\n    // parseFloat(value) handles most of the cases we're interested in (it treats null, empty string,\n    // and other non-number values as NaN, where Number just uses 0) but it considers the string\n    // '123hello' to be a valid number. Therefore we also check if Number(value) is NaN.\n    return !isNaN(parseFloat(value)) && !isNaN(Number(value));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Coerces a value to a CSS pixel value. */\nfunction coerceCssPixelValue(value) {\n    if (value == null) {\n        return '';\n    }\n    return typeof value === 'string' ? value : `${value}px`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Coerces an ElementRef or an Element into an element.\n * Useful for APIs that can accept either a ref or the native element itself.\n */\nfunction coerceElement(elementOrRef) {\n    return elementOrRef instanceof ElementRef ? elementOrRef.nativeElement : elementOrRef;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Coerces a value to an array of trimmed non-empty strings.\n * Any input that is not an array, `null` or `undefined` will be turned into a string\n * via `toString()` and subsequently split with the given separator.\n * `null` and `undefined` will result in an empty array.\n * This results in the following outcomes:\n * - `null` -&gt; `[]`\n * - `[null]` -&gt; `[\"null\"]`\n * - `[\"a\", \"b \", \" \"]` -&gt; `[\"a\", \"b\"]`\n * - `[1, [2, 3]]` -&gt; `[\"1\", \"2,3\"]`\n * - `[{ a: 0 }]` -&gt; `[\"[object Object]\"]`\n * - `{ a: 0 }` -&gt; `[\"[object\", \"Object]\"]`\n *\n * Useful for defining CSS classes or table columns.\n * @param value the value to coerce into an array of strings\n * @param separator split-separator if value isn't an array\n */\nfunction coerceStringArray(value, separator = /\\s+/) {\n    const result = [];\n    if (value != null) {\n        const sourceValues = Array.isArray(value) ? value : `${value}`.split(separator);\n        for (const sourceValue of sourceValues) {\n            const trimmedString = `${sourceValue}`.trim();\n            if (trimmedString) {\n                result.push(trimmedString);\n            }\n        }\n    }\n    return result;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport { _isNumberValue, coerceArray, coerceBooleanProperty, coerceCssPixelValue, coerceElement, coerceNumberProperty, coerceStringArray };\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,SAASC,qBAAT,CAA+BC,KAA/B,EAAsC;EAClC,OAAOA,KAAK,IAAI,IAAT,IAAkB,GAAEA,KAAM,EAAT,KAAe,OAAvC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,oBAAT,CAA8BD,KAA9B,EAAqCE,aAAa,GAAG,CAArD,EAAwD;EACpD,OAAOC,cAAc,CAACH,KAAD,CAAd,GAAwBI,MAAM,CAACJ,KAAD,CAA9B,GAAwCE,aAA/C;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASC,cAAT,CAAwBH,KAAxB,EAA+B;EAC3B;EACA;EACA;EACA,OAAO,CAACK,KAAK,CAACC,UAAU,CAACN,KAAD,CAAX,CAAN,IAA6B,CAACK,KAAK,CAACD,MAAM,CAACJ,KAAD,CAAP,CAA1C;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,WAAT,CAAqBP,KAArB,EAA4B;EACxB,OAAOQ,KAAK,CAACC,OAAN,CAAcT,KAAd,IAAuBA,KAAvB,GAA+B,CAACA,KAAD,CAAtC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASU,mBAAT,CAA6BV,KAA7B,EAAoC;EAChC,IAAIA,KAAK,IAAI,IAAb,EAAmB;IACf,OAAO,EAAP;EACH;;EACD,OAAO,OAAOA,KAAP,KAAiB,QAAjB,GAA4BA,KAA5B,GAAqC,GAAEA,KAAM,IAApD;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,SAASW,aAAT,CAAuBC,YAAvB,EAAqC;EACjC,OAAOA,YAAY,YAAYd,UAAxB,GAAqCc,YAAY,CAACC,aAAlD,GAAkED,YAAzE;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,iBAAT,CAA2Bd,KAA3B,EAAkCe,SAAS,GAAG,KAA9C,EAAqD;EACjD,MAAMC,MAAM,GAAG,EAAf;;EACA,IAAIhB,KAAK,IAAI,IAAb,EAAmB;IACf,MAAMiB,YAAY,GAAGT,KAAK,CAACC,OAAN,CAAcT,KAAd,IAAuBA,KAAvB,GAAgC,GAAEA,KAAM,EAAT,CAAWkB,KAAX,CAAiBH,SAAjB,CAApD;;IACA,KAAK,MAAMI,WAAX,IAA0BF,YAA1B,EAAwC;MACpC,MAAMG,aAAa,GAAI,GAAED,WAAY,EAAf,CAAiBE,IAAjB,EAAtB;;MACA,IAAID,aAAJ,EAAmB;QACfJ,MAAM,CAACM,IAAP,CAAYF,aAAZ;MACH;IACJ;EACJ;;EACD,OAAOJ,MAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASb,cAAT,EAAyBI,WAAzB,EAAsCR,qBAAtC,EAA6DW,mBAA7D,EAAkFC,aAAlF,EAAiGV,oBAAjG,EAAuHa,iBAAvH"}, "metadata": {}, "sourceType": "module"}