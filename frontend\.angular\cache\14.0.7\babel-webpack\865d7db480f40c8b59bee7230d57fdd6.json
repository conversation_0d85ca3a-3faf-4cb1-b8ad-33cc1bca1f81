{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery = null) {\n  startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n  return operate((source, subscriber) => {\n    let buffers = [];\n    let count = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      let toEmit = null;\n\n      if (count++ % startBufferEvery === 0) {\n        buffers.push([]);\n      }\n\n      for (const buffer of buffers) {\n        buffer.push(value);\n\n        if (bufferSize <= buffer.length) {\n          toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n          toEmit.push(buffer);\n        }\n      }\n\n      if (toEmit) {\n        for (const buffer of toEmit) {\n          arrRemove(buffers, buffer);\n          subscriber.next(buffer);\n        }\n      }\n    }, () => {\n      for (const buffer of buffers) {\n        subscriber.next(buffer);\n      }\n\n      subscriber.complete();\n    }, undefined, () => {\n      buffers = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "bufferCount", "bufferSize", "startBufferEvery", "source", "subscriber", "buffers", "count", "subscribe", "value", "toEmit", "push", "buffer", "length", "next", "complete", "undefined"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/bufferCount.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nexport function bufferCount(bufferSize, startBufferEvery = null) {\n    startBufferEvery = startBufferEvery !== null && startBufferEvery !== void 0 ? startBufferEvery : bufferSize;\n    return operate((source, subscriber) => {\n        let buffers = [];\n        let count = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            let toEmit = null;\n            if (count++ % startBufferEvery === 0) {\n                buffers.push([]);\n            }\n            for (const buffer of buffers) {\n                buffer.push(value);\n                if (bufferSize <= buffer.length) {\n                    toEmit = toEmit !== null && toEmit !== void 0 ? toEmit : [];\n                    toEmit.push(buffer);\n                }\n            }\n            if (toEmit) {\n                for (const buffer of toEmit) {\n                    arrRemove(buffers, buffer);\n                    subscriber.next(buffer);\n                }\n            }\n        }, () => {\n            for (const buffer of buffers) {\n                subscriber.next(buffer);\n            }\n            subscriber.complete();\n        }, undefined, () => {\n            buffers = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,OAAO,SAASC,WAAT,CAAqBC,UAArB,EAAiCC,gBAAgB,GAAG,IAApD,EAA0D;EAC7DA,gBAAgB,GAAGA,gBAAgB,KAAK,IAArB,IAA6BA,gBAAgB,KAAK,KAAK,CAAvD,GAA2DA,gBAA3D,GAA8ED,UAAjG;EACA,OAAOJ,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,OAAO,GAAG,EAAd;IACA,IAAIC,KAAK,GAAG,CAAZ;IACAH,MAAM,CAACI,SAAP,CAAiBT,wBAAwB,CAACM,UAAD,EAAcI,KAAD,IAAW;MAC7D,IAAIC,MAAM,GAAG,IAAb;;MACA,IAAIH,KAAK,KAAKJ,gBAAV,KAA+B,CAAnC,EAAsC;QAClCG,OAAO,CAACK,IAAR,CAAa,EAAb;MACH;;MACD,KAAK,MAAMC,MAAX,IAAqBN,OAArB,EAA8B;QAC1BM,MAAM,CAACD,IAAP,CAAYF,KAAZ;;QACA,IAAIP,UAAU,IAAIU,MAAM,CAACC,MAAzB,EAAiC;UAC7BH,MAAM,GAAGA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuCA,MAAvC,GAAgD,EAAzD;UACAA,MAAM,CAACC,IAAP,CAAYC,MAAZ;QACH;MACJ;;MACD,IAAIF,MAAJ,EAAY;QACR,KAAK,MAAME,MAAX,IAAqBF,MAArB,EAA6B;UACzBV,SAAS,CAACM,OAAD,EAAUM,MAAV,CAAT;UACAP,UAAU,CAACS,IAAX,CAAgBF,MAAhB;QACH;MACJ;IACJ,CAlBwC,EAkBtC,MAAM;MACL,KAAK,MAAMA,MAAX,IAAqBN,OAArB,EAA8B;QAC1BD,UAAU,CAACS,IAAX,CAAgBF,MAAhB;MACH;;MACDP,UAAU,CAACU,QAAX;IACH,CAvBwC,EAuBtCC,SAvBsC,EAuB3B,MAAM;MAChBV,OAAO,GAAG,IAAV;IACH,CAzBwC,CAAzC;EA0BH,CA7Ba,CAAd;AA8BH"}, "metadata": {}, "sourceType": "module"}