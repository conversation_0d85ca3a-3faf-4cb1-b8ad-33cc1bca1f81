{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, () => {\n      subscriber.next(false);\n      subscriber.complete();\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "isEmpty", "source", "subscriber", "subscribe", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/isEmpty.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function isEmpty() {\n    return operate((source, subscriber) => {\n        source.subscribe(createOperatorSubscriber(subscriber, () => {\n            subscriber.next(false);\n            subscriber.complete();\n        }, () => {\n            subscriber.next(true);\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,OAAT,GAAmB;EACtB,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnCD,MAAM,CAACE,SAAP,CAAiBJ,wBAAwB,CAACG,UAAD,EAAa,MAAM;MACxDA,UAAU,CAACE,IAAX,CAAgB,KAAhB;MACAF,UAAU,CAACG,QAAX;IACH,CAHwC,EAGtC,MAAM;MACLH,UAAU,CAACE,IAAX,CAAgB,IAAhB;MACAF,UAAU,CAACG,QAAX;IACH,CANwC,CAAzC;EAOH,CARa,CAAd;AASH"}, "metadata": {}, "sourceType": "module"}