{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, Input, ViewChild, Output, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i5 from 'primeng/api';\nconst _c0 = [\"sublist\"];\n\nconst _c1 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\n\nfunction TieredMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, child_r2.visible === false));\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.icon)(\"ngStyle\", child_r2.iconStyle);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.label);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r2.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.badge);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n}\n\nconst _c2 = function (a1) {\n  return {\n    \"p-menuitem-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction TieredMenuSub_ng_template_2_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"keydown\", function TieredMenuSub_ng_template_2_li_1_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onItemKeyDown($event, child_r2));\n    })(\"click\", function TieredMenuSub_ng_template_2_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onItemClick($event, child_r2));\n    })(\"mouseenter\", function TieredMenuSub_ng_template_2_li_1_a_2_Template_a_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onItemMouseEnter($event, child_r2));\n    });\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_a_2_span_1_Template, 1, 2, \"span\", 12);\n    i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_li_1_a_2_span_2_Template, 2, 1, \"span\", 13);\n    i0.ɵɵtemplate(3, TieredMenuSub_ng_template_2_li_1_a_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TieredMenuSub_ng_template_2_li_1_a_2_span_5_Template, 2, 2, \"span\", 15);\n    i0.ɵɵtemplate(6, TieredMenuSub_ng_template_2_li_1_a_2_span_6_Template, 1, 0, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(4);\n\n    const child_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", child_r2.target)(\"ngClass\", i0.ɵɵpureFunction1(14, _c2, child_r2.disabled));\n    i0.ɵɵattribute(\"href\", child_r2.url, i0.ɵɵsanitizeUrl)(\"data-automationid\", child_r2.automationId)(\"title\", child_r2.title)(\"id\", child_r2.id)(\"tabindex\", child_r2.disabled ? null : \"0\")(\"aria-haspopup\", ctx_r7.item.items != null)(\"aria-expanded\", ctx_r7.item === ctx_r7.activeItem);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.escape !== false)(\"ngIfElse\", _r12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r2.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.icon)(\"ngStyle\", child_r2.iconStyle);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.label);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r2.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.badge);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n}\n\nconst _c3 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction TieredMenuSub_ng_template_2_li_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 22);\n    i0.ɵɵlistener(\"keydown\", function TieredMenuSub_ng_template_2_li_1_a_3_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onItemKeyDown($event, child_r2));\n    })(\"click\", function TieredMenuSub_ng_template_2_li_1_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onItemClick($event, child_r2));\n    })(\"mouseenter\", function TieredMenuSub_ng_template_2_li_1_a_3_Template_a_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const child_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onItemMouseEnter($event, child_r2));\n    });\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_a_3_span_1_Template, 1, 2, \"span\", 12);\n    i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_li_1_a_3_span_2_Template, 2, 1, \"span\", 13);\n    i0.ɵɵtemplate(3, TieredMenuSub_ng_template_2_li_1_a_3_ng_template_3_Template, 1, 1, \"ng-template\", null, 23, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, TieredMenuSub_ng_template_2_li_1_a_3_span_5_Template, 2, 2, \"span\", 15);\n    i0.ɵɵtemplate(6, TieredMenuSub_ng_template_2_li_1_a_3_span_6_Template, 1, 0, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r30 = i0.ɵɵreference(4);\n\n    const child_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"routerLink\", child_r2.routerLink)(\"queryParams\", child_r2.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", child_r2.routerLinkActiveOptions || i0.ɵɵpureFunction0(21, _c3))(\"target\", child_r2.target)(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, child_r2.disabled))(\"fragment\", child_r2.fragment)(\"queryParamsHandling\", child_r2.queryParamsHandling)(\"preserveFragment\", child_r2.preserveFragment)(\"skipLocationChange\", child_r2.skipLocationChange)(\"replaceUrl\", child_r2.replaceUrl)(\"state\", child_r2.state);\n    i0.ɵɵattribute(\"data-automationid\", child_r2.automationId)(\"title\", child_r2.title)(\"id\", child_r2.id)(\"tabindex\", child_r2.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.escape !== false)(\"ngIfElse\", _r30);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r2.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-tieredMenuSub\", 24);\n    i0.ɵɵlistener(\"keydownItem\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_4_Template_p_tieredMenuSub_keydownItem_0_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r46.onChildItemKeyDown($event));\n    })(\"leafClick\", function TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_4_Template_p_tieredMenuSub_leafClick_0_listener() {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r48 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r48.onLeafClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"parentActive\", child_r2 === ctx_r9.activeItem)(\"item\", child_r2)(\"mobileActive\", ctx_r9.mobileActive)(\"autoDisplay\", ctx_r9.autoDisplay)(\"popup\", ctx_r9.popup);\n  }\n}\n\nconst _c4 = function (a1, a2) {\n  return {\n    \"p-menuitem\": true,\n    \"p-menuitem-active\": a1,\n    \"p-hidden\": a2\n  };\n};\n\nfunction TieredMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 6, 7);\n    i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_li_1_a_2_Template, 7, 16, \"a\", 8);\n    i0.ɵɵtemplate(3, TieredMenuSub_ng_template_2_li_1_a_3_Template, 7, 24, \"a\", 9);\n    i0.ɵɵtemplate(4, TieredMenuSub_ng_template_2_li_1_p_tieredMenuSub_4_Template, 1, 5, \"p-tieredMenuSub\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(child_r2.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c4, child_r2 === ctx_r4.activeItem, child_r2.visible === false))(\"ngStyle\", child_r2.style)(\"tooltipOptions\", child_r2.tooltipOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !child_r2.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction TieredMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TieredMenuSub_ng_template_2_li_0_Template, 1, 3, \"li\", 3);\n    i0.ɵɵtemplate(1, TieredMenuSub_ng_template_2_li_1_Template, 5, 11, \"li\", 4);\n  }\n\n  if (rf & 2) {\n    const child_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", child_r2.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !child_r2.separator);\n  }\n}\n\nconst _c5 = function (a0) {\n  return {\n    \"p-submenu-list\": a0\n  };\n};\n\nconst _c6 = function (a1) {\n  return {\n    \"p-tieredmenu p-component\": true,\n    \"p-tieredmenu-overlay\": a1\n  };\n};\n\nconst _c7 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c8 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction TieredMenu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function TieredMenu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function TieredMenu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"p-tieredMenuSub\", 2);\n    i0.ɵɵlistener(\"leafClick\", function TieredMenu_div_0_Template_p_tieredMenuSub_leafClick_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.onLeafClick());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c6, ctx_r0.popup))(\"ngStyle\", ctx_r0.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(17, _c8, i0.ɵɵpureFunction2(14, _c7, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)))(\"@.disabled\", ctx_r0.popup !== true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", ctx_r0.model)(\"parentActive\", ctx_r0.parentActive)(\"baseZIndex\", ctx_r0.baseZIndex)(\"autoZIndex\", ctx_r0.autoZIndex)(\"autoDisplay\", ctx_r0.autoDisplay)(\"popup\", ctx_r0.popup);\n  }\n}\n\nclass TieredMenuSub {\n  constructor(el, renderer, cd) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.leafClick = new EventEmitter();\n    this.keydownItem = new EventEmitter();\n    this.menuHoverActive = false;\n  }\n\n  get parentActive() {\n    return this._parentActive;\n  }\n\n  set parentActive(value) {\n    if (!this.root) {\n      this._parentActive = value;\n      if (!value) this.activeItem = null;else this.positionSubmenu();\n    }\n  }\n\n  onItemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (item.items) {\n      if (this.activeItem && item === this.activeItem) {\n        this.activeItem = null;\n        this.unbindDocumentClickListener();\n      } else {\n        this.activeItem = item;\n\n        if (this.root) {\n          this.bindDocumentClickListener();\n        }\n      }\n    }\n\n    if (!item.items) {\n      this.onLeafClick();\n    }\n  }\n\n  onItemMouseEnter(event, item) {\n    if (item.disabled || this.mobileActive) {\n      event.preventDefault();\n      return;\n    }\n\n    if (this.root) {\n      if (this.activeItem || this.autoDisplay || this.popup) {\n        this.activeItem = item;\n        this.bindDocumentClickListener();\n      }\n    } else {\n      this.activeItem = item;\n      this.bindDocumentClickListener();\n    }\n  }\n\n  onLeafClick() {\n    this.activeItem = null;\n\n    if (this.root) {\n      this.unbindDocumentClickListener();\n    }\n\n    this.leafClick.emit();\n  }\n\n  onItemKeyDown(event, item) {\n    let listItem = event.currentTarget.parentElement;\n\n    switch (event.key) {\n      case 'ArrowDown':\n        const nextItem = this.findNextItem(listItem);\n\n        if (nextItem) {\n          nextItem.children[0].focus();\n        }\n\n        event.preventDefault();\n        break;\n\n      case 'ArrowUp':\n        const prevItem = this.findPrevItem(listItem);\n\n        if (prevItem) {\n          prevItem.children[0].focus();\n        }\n\n        event.preventDefault();\n        break;\n\n      case 'ArrowRight':\n        if (item.items) {\n          this.activeItem = item;\n\n          if (this.root) {\n            this.bindDocumentClickListener();\n          }\n\n          setTimeout(() => {\n            listItem.children[1].children[0].children[0].children[0].focus();\n          }, 50);\n        }\n\n        event.preventDefault();\n        break;\n\n      case 'Enter':\n        if (!item.routerLink) {\n          this.onItemClick(event, item);\n        }\n\n        break;\n\n      default:\n        break;\n    }\n\n    this.keydownItem.emit({\n      originalEvent: event,\n      element: listItem\n    });\n  }\n\n  positionSubmenu() {\n    let sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n\n    if (sublist) {\n      const parentItem = sublist.parentElement.parentElement;\n      const containerOffset = DomHandler.getOffset(parentItem);\n      const viewport = DomHandler.getViewport();\n      const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n      const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n\n      if (parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n        DomHandler.addClass(sublist, 'p-submenu-list-flipped');\n      }\n    }\n  }\n\n  findNextItem(item) {\n    let nextItem = item.nextElementSibling;\n    if (nextItem) return DomHandler.hasClass(nextItem, 'p-disabled') || !DomHandler.hasClass(nextItem, 'p-menuitem') ? this.findNextItem(nextItem) : nextItem;else return null;\n  }\n\n  findPrevItem(item) {\n    let prevItem = item.previousElementSibling;\n    if (prevItem) return DomHandler.hasClass(prevItem, 'p-disabled') || !DomHandler.hasClass(prevItem, 'p-menuitem') ? this.findPrevItem(prevItem) : prevItem;else return null;\n  }\n\n  onChildItemKeyDown(event) {\n    if (event.originalEvent.key === 'ArrowLeft') {\n      this.activeItem = null;\n\n      if (this.root) {\n        this.unbindDocumentClickListener();\n      }\n\n      event.element.parentElement.parentElement.parentElement.children[0].focus();\n    }\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      this.documentClickListener = event => {\n        if (this.el && !this.el.nativeElement.contains(event.target)) {\n          this.activeItem = null;\n          this.cd.markForCheck();\n          this.unbindDocumentClickListener();\n        }\n      };\n\n      document.addEventListener('click', this.documentClickListener);\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      document.removeEventListener('click', this.documentClickListener);\n      this.documentClickListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    this.unbindDocumentClickListener();\n  }\n\n}\n\nTieredMenuSub.ɵfac = function TieredMenuSub_Factory(t) {\n  return new (t || TieredMenuSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTieredMenuSub.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TieredMenuSub,\n  selectors: [[\"p-tieredMenuSub\"]],\n  viewQuery: function TieredMenuSub_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sublistViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    item: \"item\",\n    root: \"root\",\n    autoDisplay: \"autoDisplay\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    mobileActive: \"mobileActive\",\n    popup: \"popup\",\n    parentActive: \"parentActive\"\n  },\n  outputs: {\n    leafClick: \"leafClick\",\n    keydownItem: \"keydownItem\"\n  },\n  decls: 3,\n  vars: 4,\n  consts: [[3, \"ngClass\"], [\"sublist\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menu-separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [1, \"p-menu-separator\", 3, \"ngClass\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"listItem\", \"\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", \"keydown\", \"click\", \"mouseenter\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\", \"mouseenter\", 4, \"ngIf\"], [3, \"parentActive\", \"item\", \"mobileActive\", \"autoDisplay\", \"popup\", \"keydownItem\", \"leafClick\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", \"keydown\", \"click\", \"mouseenter\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-icon pi pi-angle-right\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-submenu-icon\", \"pi\", \"pi-angle-right\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\", \"mouseenter\"], [\"htmlRouteLabel\", \"\"], [3, \"parentActive\", \"item\", \"mobileActive\", \"autoDisplay\", \"popup\", \"keydownItem\", \"leafClick\"]],\n  template: function TieredMenuSub_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ul\", 0, 1);\n      i0.ɵɵtemplate(2, TieredMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, !ctx.root));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.root ? ctx.item : ctx.item.items);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, TieredMenuSub],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenuSub',\n      template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list': !root}\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                         (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"  [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-tieredMenuSub (keydownItem)=\"onChildItemKeyDown($event)\" [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\" [popup]=\"popup\"></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    item: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    mobileActive: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    parentActive: [{\n      type: Input\n    }],\n    sublistViewChild: [{\n      type: ViewChild,\n      args: ['sublist']\n    }],\n    leafClick: [{\n      type: Output\n    }],\n    keydownItem: [{\n      type: Output\n    }]\n  });\n})();\n\nclass TieredMenu {\n  constructor(el, renderer, cd, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n  }\n\n  toggle(event) {\n    if (this.visible) this.hide();else this.show(event);\n    this.preventDocumentDefault = true;\n  }\n\n  show(event) {\n    this.target = event.currentTarget;\n    this.relativeAlign = event.relativeAlign;\n    this.visible = true;\n    this.parentActive = true;\n    this.preventDocumentDefault = true;\n    this.cd.markForCheck();\n  }\n\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n\n    this.preventDocumentDefault = true;\n  }\n\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n        }\n\n        break;\n\n      case 'void':\n        this.onOverlayHide();\n        break;\n    }\n  }\n\n  alignOverlay() {\n    if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n  }\n\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        ZIndexUtils.clear(event.element);\n        break;\n    }\n  }\n\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n\n  restoreOverlayAppend() {\n    if (this.container && this.appendTo) {\n      this.el.nativeElement.appendChild(this.container);\n    }\n  }\n\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n\n  hide() {\n    this.visible = false;\n    this.relativeAlign = false;\n    this.parentActive = false;\n    this.cd.markForCheck();\n  }\n\n  onWindowResize() {\n    if (this.visible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  onLeafClick() {\n    if (this.popup) {\n      this.hide();\n    }\n\n    this.unbindDocumentClickListener();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n        if (!this.preventDocumentDefault && this.popup) {\n          this.hide();\n        }\n\n        this.preventDocumentDefault = false;\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n        if (this.visible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.preventDocumentDefault = false;\n\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n  }\n\n}\n\nTieredMenu.ɵfac = function TieredMenu_Factory(t) {\n  return new (t || TieredMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i5.OverlayService));\n};\n\nTieredMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TieredMenu,\n  selectors: [[\"p-tieredMenu\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    popup: \"popup\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    appendTo: \"appendTo\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    autoDisplay: \"autoDisplay\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [\"root\", \"root\", 3, \"item\", \"parentActive\", \"baseZIndex\", \"autoZIndex\", \"autoDisplay\", \"popup\", \"leafClick\"]],\n  template: function TieredMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TieredMenu_div_0_Template, 2, 19, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, TieredMenuSub],\n  styles: [\".p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('overlayAnimation', [transition(':enter', [style({\n      opacity: 0,\n      transform: 'scaleY(0.8)'\n    }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-tieredMenu',\n      template: `\n        <div [ngClass]=\"{'p-tieredmenu p-component':true, 'p-tieredmenu-overlay':popup}\" [class]=\"styleClass\" [ngStyle]=\"style\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" *ngIf=\"!popup || visible\">\n            <p-tieredMenuSub [item]=\"model\" root=\"root\" [parentActive]=\"parentActive\" [baseZIndex]=\"baseZIndex\" [autoZIndex]=\"autoZIndex\" (leafClick)=\"onLeafClick()\"\n                [autoDisplay]=\"autoDisplay\" [popup]=\"popup\"></p-tieredMenuSub>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i5.PrimeNGConfig\n    }, {\n      type: i5.OverlayService\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TieredMenuModule {}\n\nTieredMenuModule.ɵfac = function TieredMenuModule_Factory(t) {\n  return new (t || TieredMenuModule)();\n};\n\nTieredMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TieredMenuModule\n});\nTieredMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TieredMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n      exports: [TieredMenu, RouterModule, TooltipModule],\n      declarations: [TieredMenu, TieredMenuSub]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ViewEncapsulation", "Input", "ViewChild", "Output", "ChangeDetectionStrategy", "NgModule", "i1", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "i2", "RouterModule", "i3", "RippleModule", "trigger", "transition", "style", "animate", "ZIndexUtils", "i4", "TooltipModule", "i5", "TieredMenuSub", "constructor", "el", "renderer", "cd", "autoZIndex", "baseZIndex", "leafClick", "keydownItem", "menuHoverActive", "parentActive", "_parentActive", "value", "root", "activeItem", "positionSubmenu", "onItemClick", "event", "item", "disabled", "preventDefault", "url", "routerLink", "command", "originalEvent", "items", "unbindDocumentClickListener", "bindDocumentClickListener", "onLeafClick", "onItemMouseEnter", "mobileActive", "autoDisplay", "popup", "emit", "onItemKeyDown", "listItem", "currentTarget", "parentElement", "key", "nextItem", "findNextItem", "children", "focus", "prevItem", "findPrevItem", "setTimeout", "element", "sublist", "sublist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "parentItem", "containerOffset", "getOffset", "viewport", "getViewport", "sublist<PERSON><PERSON><PERSON>", "offsetParent", "offsetWidth", "getHiddenElementOuterWidth", "itemOuterWidth", "getOuterWidth", "parseInt", "left", "width", "calculateScrollbarWidth", "addClass", "nextElement<PERSON><PERSON>ling", "hasClass", "previousElementSibling", "onChildItemKeyDown", "documentClickListener", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "document", "addEventListener", "removeEventListener", "ngOnDestroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "encapsulation", "None", "host", "TieredMenu", "config", "overlayService", "showTransitionOptions", "hideTransitionOptions", "toggle", "visible", "hide", "show", "preventDocumentDefault", "relativeAlign", "onOverlayClick", "add", "onOverlayAnimationStart", "toState", "container", "moveOnTop", "appendOverlay", "alignOverlay", "bindDocumentResizeListener", "bindScrollListener", "onOverlayHide", "relativePosition", "absolutePosition", "onOverlayAnimationEnd", "clear", "appendTo", "body", "append<PERSON><PERSON><PERSON>", "restoreOverlayAppend", "set", "zIndex", "menu", "onWindowResize", "isTouchDevice", "documentTarget", "ownerDocument", "listen", "documentResizeListener", "bind", "window", "unbindDocumentResizeListener", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "destroyed", "destroy", "PrimeNGConfig", "OverlayService", "opacity", "transform", "animations", "changeDetection", "OnPush", "styles", "model", "styleClass", "TieredMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-tieredmenu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, Input, ViewChild, Output, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i5 from 'primeng/api';\n\nclass TieredMenuSub {\n    constructor(el, renderer, cd) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.leafClick = new EventEmitter();\n        this.keydownItem = new EventEmitter();\n        this.menuHoverActive = false;\n    }\n    get parentActive() {\n        return this._parentActive;\n    }\n    set parentActive(value) {\n        if (!this.root) {\n            this._parentActive = value;\n            if (!value)\n                this.activeItem = null;\n            else\n                this.positionSubmenu();\n        }\n    }\n    onItemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (item.items) {\n            if (this.activeItem && item === this.activeItem) {\n                this.activeItem = null;\n                this.unbindDocumentClickListener();\n            }\n            else {\n                this.activeItem = item;\n                if (this.root) {\n                    this.bindDocumentClickListener();\n                }\n            }\n        }\n        if (!item.items) {\n            this.onLeafClick();\n        }\n    }\n    onItemMouseEnter(event, item) {\n        if (item.disabled || this.mobileActive) {\n            event.preventDefault();\n            return;\n        }\n        if (this.root) {\n            if (this.activeItem || this.autoDisplay || this.popup) {\n                this.activeItem = item;\n                this.bindDocumentClickListener();\n            }\n        }\n        else {\n            this.activeItem = item;\n            this.bindDocumentClickListener();\n        }\n    }\n    onLeafClick() {\n        this.activeItem = null;\n        if (this.root) {\n            this.unbindDocumentClickListener();\n        }\n        this.leafClick.emit();\n    }\n    onItemKeyDown(event, item) {\n        let listItem = event.currentTarget.parentElement;\n        switch (event.key) {\n            case 'ArrowDown':\n                const nextItem = this.findNextItem(listItem);\n                if (nextItem) {\n                    nextItem.children[0].focus();\n                }\n                event.preventDefault();\n                break;\n            case 'ArrowUp':\n                const prevItem = this.findPrevItem(listItem);\n                if (prevItem) {\n                    prevItem.children[0].focus();\n                }\n                event.preventDefault();\n                break;\n            case 'ArrowRight':\n                if (item.items) {\n                    this.activeItem = item;\n                    if (this.root) {\n                        this.bindDocumentClickListener();\n                    }\n                    setTimeout(() => {\n                        listItem.children[1].children[0].children[0].children[0].focus();\n                    }, 50);\n                }\n                event.preventDefault();\n                break;\n            case 'Enter':\n                if (!item.routerLink) {\n                    this.onItemClick(event, item);\n                }\n                break;\n            default:\n                break;\n        }\n        this.keydownItem.emit({\n            originalEvent: event,\n            element: listItem\n        });\n    }\n    positionSubmenu() {\n        let sublist = this.sublistViewChild && this.sublistViewChild.nativeElement;\n        if (sublist) {\n            const parentItem = sublist.parentElement.parentElement;\n            const containerOffset = DomHandler.getOffset(parentItem);\n            const viewport = DomHandler.getViewport();\n            const sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n            const itemOuterWidth = DomHandler.getOuterWidth(parentItem.children[0]);\n            if ((parseInt(containerOffset.left, 10) + itemOuterWidth + sublistWidth) > (viewport.width - DomHandler.calculateScrollbarWidth())) {\n                DomHandler.addClass(sublist, 'p-submenu-list-flipped');\n            }\n        }\n    }\n    findNextItem(item) {\n        let nextItem = item.nextElementSibling;\n        if (nextItem)\n            return DomHandler.hasClass(nextItem, 'p-disabled') || !DomHandler.hasClass(nextItem, 'p-menuitem') ? this.findNextItem(nextItem) : nextItem;\n        else\n            return null;\n    }\n    findPrevItem(item) {\n        let prevItem = item.previousElementSibling;\n        if (prevItem)\n            return DomHandler.hasClass(prevItem, 'p-disabled') || !DomHandler.hasClass(prevItem, 'p-menuitem') ? this.findPrevItem(prevItem) : prevItem;\n        else\n            return null;\n    }\n    onChildItemKeyDown(event) {\n        if (event.originalEvent.key === 'ArrowLeft') {\n            this.activeItem = null;\n            if (this.root) {\n                this.unbindDocumentClickListener();\n            }\n            event.element.parentElement.parentElement.parentElement.children[0].focus();\n        }\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = (event) => {\n                if (this.el && !this.el.nativeElement.contains(event.target)) {\n                    this.activeItem = null;\n                    this.cd.markForCheck();\n                    this.unbindDocumentClickListener();\n                }\n            };\n            document.addEventListener('click', this.documentClickListener);\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            document.removeEventListener('click', this.documentClickListener);\n            this.documentClickListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.unbindDocumentClickListener();\n    }\n}\nTieredMenuSub.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTieredMenuSub.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: { item: \"item\", root: \"root\", autoDisplay: \"autoDisplay\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", mobileActive: \"mobileActive\", popup: \"popup\", parentActive: \"parentActive\" }, outputs: { leafClick: \"leafClick\", keydownItem: \"keydownItem\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"sublistViewChild\", first: true, predicate: [\"sublist\"], descendants: true }], ngImport: i0, template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list': !root}\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                         (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"  [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-tieredMenuSub (keydownItem)=\"onChildItemKeyDown($event)\" [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\" [popup]=\"popup\"></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }, { kind: \"directive\", type: i4.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: [\"item\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"popup\", \"parentActive\"], outputs: [\"leafClick\", \"keydownItem\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-tieredMenuSub',\n                    template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list': !root}\">\n            <ng-template ngFor let-child [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\">\n                <li *ngIf=\"!child.separator\" #listItem [ngClass]=\"{'p-menuitem':true, 'p-menuitem-active': child === activeItem, 'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [attr.href]=\"child.url\" [attr.data-automationid]=\"child.automationId\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                         (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"\n                         [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         [attr.tabindex]=\"child.disabled ? null : '0'\" [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"item === activeItem\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event, child)\" [routerLink]=\"child.routerLink\" [attr.data-automationid]=\"child.automationId\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\"\n                        [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\" role=\"menuitem\"\n                        (click)=\"onItemClick($event, child)\" (mouseenter)=\"onItemMouseEnter($event,child)\"  [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\" pRipple>\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-tieredMenuSub (keydownItem)=\"onChildItemKeyDown($event)\" [parentActive]=\"child === activeItem\" [item]=\"child\" *ngIf=\"child.items\" [mobileActive]=\"mobileActive\" [autoDisplay]=\"autoDisplay\" (leafClick)=\"onLeafClick()\" [popup]=\"popup\"></p-tieredMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { item: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], mobileActive: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], parentActive: [{\n                type: Input\n            }], sublistViewChild: [{\n                type: ViewChild,\n                args: ['sublist']\n            }], leafClick: [{\n                type: Output\n            }], keydownItem: [{\n                type: Output\n            }] } });\nclass TieredMenu {\n    constructor(el, renderer, cd, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n    }\n    toggle(event) {\n        if (this.visible)\n            this.hide();\n        else\n            this.show(event);\n        this.preventDocumentDefault = true;\n    }\n    show(event) {\n        this.target = event.currentTarget;\n        this.relativeAlign = event.relativeAlign;\n        this.visible = true;\n        this.parentActive = true;\n        this.preventDocumentDefault = true;\n        this.cd.markForCheck();\n    }\n    onOverlayClick(event) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n        this.preventDocumentDefault = true;\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindDocumentClickListener();\n                    this.bindDocumentResizeListener();\n                    this.bindScrollListener();\n                }\n                break;\n            case 'void':\n                this.onOverlayHide();\n                break;\n        }\n    }\n    alignOverlay() {\n        if (this.relativeAlign)\n            DomHandler.relativePosition(this.container, this.target);\n        else\n            DomHandler.absolutePosition(this.container, this.target);\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.container && this.appendTo) {\n            this.el.nativeElement.appendChild(this.container);\n        }\n    }\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    hide() {\n        this.visible = false;\n        this.relativeAlign = false;\n        this.parentActive = false;\n        this.cd.markForCheck();\n    }\n    onWindowResize() {\n        if (this.visible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    onLeafClick() {\n        if (this.popup) {\n            this.hide();\n        }\n        this.unbindDocumentClickListener();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n                if (!this.preventDocumentDefault && this.popup) {\n                    this.hide();\n                }\n                this.preventDocumentDefault = false;\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                if (this.visible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    onOverlayHide() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.preventDocumentDefault = false;\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n    }\n}\nTieredMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenu, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }, { token: i5.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nTieredMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TieredMenu, selector: \"p-tieredMenu\", inputs: { model: \"model\", popup: \"popup\", style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", autoDisplay: \"autoDisplay\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"{'p-tieredmenu p-component':true, 'p-tieredmenu-overlay':popup}\" [class]=\"styleClass\" [ngStyle]=\"style\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" *ngIf=\"!popup || visible\">\n            <p-tieredMenuSub [item]=\"model\" root=\"root\" [parentActive]=\"parentActive\" [baseZIndex]=\"baseZIndex\" [autoZIndex]=\"autoZIndex\" (leafClick)=\"onLeafClick()\"\n                [autoDisplay]=\"autoDisplay\" [popup]=\"popup\"></p-tieredMenuSub>\n        </div>\n    `, isInline: true, styles: [\".p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: TieredMenuSub, selector: \"p-tieredMenuSub\", inputs: [\"item\", \"root\", \"autoDisplay\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"popup\", \"parentActive\"], outputs: [\"leafClick\", \"keydownItem\"] }], animations: [\n        trigger('overlayAnimation', [\n            transition(':enter', [\n                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition(':leave', [\n                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tieredMenu', template: `\n        <div [ngClass]=\"{'p-tieredmenu p-component':true, 'p-tieredmenu-overlay':popup}\" [class]=\"styleClass\" [ngStyle]=\"style\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\"\n            (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" *ngIf=\"!popup || visible\">\n            <p-tieredMenuSub [item]=\"model\" root=\"root\" [parentActive]=\"parentActive\" [baseZIndex]=\"baseZIndex\" [autoZIndex]=\"autoZIndex\" (leafClick)=\"onLeafClick()\"\n                [autoDisplay]=\"autoDisplay\" [popup]=\"popup\"></p-tieredMenuSub>\n        </div>\n    `, animations: [\n                        trigger('overlayAnimation', [\n                            transition(':enter', [\n                                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition(':leave', [\n                                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n                            ])\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-tieredmenu-overlay{position:absolute;top:0;left:0}.p-tieredmenu ul{margin:0;padding:0;list-style:none}.p-tieredmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-tieredmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-tieredmenu .p-menuitem-text{line-height:1}.p-tieredmenu .p-menuitem{position:relative}.p-tieredmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list{display:block;left:100%;top:0}.p-tieredmenu .p-menuitem-active>p-tieredmenusub>.p-submenu-list.p-submenu-list-flipped{left:-100%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }, { type: i5.OverlayService }]; }, propDecorators: { model: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }] } });\nclass TieredMenuModule {\n}\nTieredMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTieredMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuModule, declarations: [TieredMenu, TieredMenuSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule], exports: [TieredMenu, RouterModule, TooltipModule] });\nTieredMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TieredMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n                    exports: [TieredMenu, RouterModule, TooltipModule],\n                    declarations: [TieredMenu, TieredMenuSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TieredMenu, TieredMenuModule, TieredMenuSub };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,iBAAlC,EAAqDC,KAArD,EAA4DC,SAA5D,EAAuEC,MAAvE,EAA+EC,uBAA/E,EAAwGC,QAAxG,QAAwH,eAAxH;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;;;;;;;;;;;IAkLgGxB,EAIhF,sB;;;;qBAJgFA,E;IAAAA,EAI3B,uBAJ2BA,EAI3B,qD;;;;;;IAJ2BA,EAUxE,yB;;;;qBAVwEA,E;IAAAA,EAUvB,oE;;;;;;IAVuBA,EAWxE,8B;IAXwEA,EAWK,U;IAXLA,EAWoB,e;;;;qBAXpBA,E;IAAAA,EAWK,a;IAXLA,EAWK,kC;;;;;;IAXLA,EAYhD,yB;;;;qBAZgDA,E;IAAAA,EAYlB,yCAZkBA,EAYlB,gB;;;;;;IAZkBA,EAaxE,8B;IAbwEA,EAaa,U;IAbbA,EAa4B,e;;;;qBAb5BA,E;IAAAA,EAarB,gD;IAbqBA,EAaa,a;IAbbA,EAaa,kC;;;;;;IAbbA,EAcxE,yB;;;;;;;;;;;;;iBAdwEA,E;;IAAAA,EAM5E,2B;IAN4EA,EAM/C;MAN+CA,EAM/C;MAAA,iBAN+CA,EAM/C;MAAA,gBAN+CA,EAM/C;MAAA,OAN+CA,EAMpC,qDAAX;IAAA;MAN+CA,EAM/C;MAAA,iBAN+CA,EAM/C;MAAA,gBAN+CA,EAM/C;MAAA,OAN+CA,EAO9D,mDADe;IAAA;MAN+CA,EAM/C;MAAA,iBAN+CA,EAM/C;MAAA,gBAN+CA,EAM/C;MAAA,OAN+CA,EAOpB,wDAD3B;IAAA,E;IAN+CA,EAUxE,sF;IAVwEA,EAWxE,sF;IAXwEA,EAYxE,2GAZwEA,EAYxE,wB;IAZwEA,EAaxE,sF;IAbwEA,EAcxE,sF;IAdwEA,EAe5E,e;;;;iBAf4EA,E;;qBAAAA,E;mBAAAA,E;IAAAA,EAMgE,kDANhEA,EAMgE,6C;IANhEA,EAMN,mCANMA,EAMN,mP;IANMA,EAUzC,a;IAVyCA,EAUzC,kC;IAVyCA,EAWzC,a;IAXyCA,EAWzC,gE;IAXyCA,EAaxC,a;IAbwCA,EAaxC,mC;IAbwCA,EAcxB,a;IAdwBA,EAcxB,mC;;;;;;IAdwBA,EAoBxE,yB;;;;qBApBwEA,E;IAAAA,EAoBvB,oE;;;;;;IApBuBA,EAqBxE,8B;IArBwEA,EAqBU,U;IArBVA,EAqByB,e;;;;qBArBzBA,E;IAAAA,EAqBU,a;IArBVA,EAqBU,kC;;;;;;IArBVA,EAsB3C,yB;;;;qBAtB2CA,E;IAAAA,EAsBb,yCAtBaA,EAsBb,gB;;;;;;IAtBaA,EAuBxE,8B;IAvBwEA,EAuBa,U;IAvBbA,EAuB4B,e;;;;qBAvB5BA,E;IAAAA,EAuBrB,gD;IAvBqBA,EAuBa,a;IAvBbA,EAuBa,kC;;;;;;IAvBbA,EAwBxE,yB;;;;;;;;;;;;iBAxBwEA,E;;IAAAA,EAgB5E,2B;IAhB4EA,EAgBhD;MAhBgDA,EAgBhD;MAAA,iBAhBgDA,EAgBhD;MAAA,gBAhBgDA,EAgBhD;MAAA,OAhBgDA,EAgBrC,qDAAX;IAAA;MAhBgDA,EAgBhD;MAAA,iBAhBgDA,EAgBhD;MAAA,gBAhBgDA,EAgBhD;MAAA,OAhBgDA,EAkB/D,mDAFe;IAAA;MAhBgDA,EAgBhD;MAAA,iBAhBgDA,EAgBhD;MAAA,gBAhBgDA,EAgBhD;MAAA,OAhBgDA,EAkBrB,wDAF3B;IAAA,E;IAhBgDA,EAoBxE,sF;IApBwEA,EAqBxE,sF;IArBwEA,EAsBxE,2GAtBwEA,EAsBxE,wB;IAtBwEA,EAuBxE,sF;IAvBwEA,EAwBxE,sF;IAxBwEA,EAyB5E,e;;;;iBAzB4EA,E;;qBAAAA,E;IAAAA,EAgBP,iMAhBOA,EAgBP,iEAhBOA,EAgBP,+R;IAhBOA,EAgByB,gJ;IAhBzBA,EAoBzC,a;IApByCA,EAoBzC,kC;IApByCA,EAqBzC,a;IArByCA,EAqBzC,gE;IArByCA,EAuBxC,a;IAvBwCA,EAuBxC,mC;IAvBwCA,EAwBxB,a;IAxBwBA,EAwBxB,mC;;;;;;iBAxBwBA,E;;IAAAA,EA0B5E,yC;IA1B4EA,EA0B3D;MA1B2DA,EA0B3D;MAAA,gBA1B2DA,EA0B3D;MAAA,OA1B2DA,EA0B5C,gDAAf;IAAA;MA1B2DA,EA0B3D;MAAA,gBA1B2DA,EA0B3D;MAAA,OA1B2DA,EA0BgI,mCAA3L;IAAA,E;IA1B2DA,EA0B+J,e;;;;qBA1B/JA,E;mBAAAA,E;IAAAA,EA0BhB,4K;;;;;;;;;;;;;;IA1BgBA,EAKhF,8B;IALgFA,EAM5E,4E;IAN4EA,EAgB5E,4E;IAhB4EA,EA0B5E,wG;IA1B4EA,EA2BhF,e;;;;qBA3BgFA,E;mBAAAA,E;IAAAA,EAK+F,gC;IAL/FA,EAKzC,uBALyCA,EAKzC,2J;IALyCA,EAMxE,a;IANwEA,EAMxE,yC;IANwEA,EAgBxE,a;IAhBwEA,EAgBxE,wC;IAhBwEA,EA0BsC,a;IA1BtCA,EA0BsC,mC;;;;;;IA1BtCA,EAIhF,wE;IAJgFA,EAKhF,yE;;;;;IALgFA,EAI3E,uC;IAJ2EA,EAK3E,a;IAL2EA,EAK3E,wC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAL2EA,E;;IAAAA,EAuQxF,4B;IAvQwFA,EAuQgC;MAvQhCA,EAuQgC;MAAA,eAvQhCA,EAuQgC;MAAA,OAvQhCA,EAuQyC,2CAAT;IAAA;MAvQhCA,EAuQgC;MAAA,eAvQhCA,EAuQgC;MAAA,OAvQhCA,EAyQzD,oDAFyF;IAAA;MAvQhCA,EAuQgC;MAAA,eAvQhCA,EAuQgC;MAAA,OAvQhCA,EAyQE,kDAF8B;IAAA,E;IAvQhCA,EA0QpF,wC;IA1QoFA,EA0Q0C;MA1Q1CA,EA0Q0C;MAAA,eA1Q1CA,EA0Q0C;MAAA,OA1Q1CA,EA0QuD,kCAAb;IAAA,E;IA1Q1CA,EA2QpC,iB;;;;mBA3QoCA,E;IAAAA,EAuQP,8B;IAvQOA,EAuQnF,uBAvQmFA,EAuQnF,uFAvQmFA,EAuQnF,0BAvQmFA,EAuQnF,4H;IAvQmFA,EA0QnE,a;IA1QmEA,EA0QnE,kM;;;;AA1b7B,MAAMyB,aAAN,CAAoB;EAChBC,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmB;IAC1B,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,SAAL,GAAiB,IAAI/B,YAAJ,EAAjB;IACA,KAAKgC,WAAL,GAAmB,IAAIhC,YAAJ,EAAnB;IACA,KAAKiC,eAAL,GAAuB,KAAvB;EACH;;EACe,IAAZC,YAAY,GAAG;IACf,OAAO,KAAKC,aAAZ;EACH;;EACe,IAAZD,YAAY,CAACE,KAAD,EAAQ;IACpB,IAAI,CAAC,KAAKC,IAAV,EAAgB;MACZ,KAAKF,aAAL,GAAqBC,KAArB;MACA,IAAI,CAACA,KAAL,EACI,KAAKE,UAAL,GAAkB,IAAlB,CADJ,KAGI,KAAKC,eAAL;IACP;EACJ;;EACDC,WAAW,CAACC,KAAD,EAAQC,IAAR,EAAc;IACrB,IAAIA,IAAI,CAACC,QAAT,EAAmB;MACfF,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,CAACF,IAAI,CAACG,GAAN,IAAa,CAACH,IAAI,CAACI,UAAvB,EAAmC;MAC/BL,KAAK,CAACG,cAAN;IACH;;IACD,IAAIF,IAAI,CAACK,OAAT,EAAkB;MACdL,IAAI,CAACK,OAAL,CAAa;QACTC,aAAa,EAAEP,KADN;QAETC,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,IAAIA,IAAI,CAACO,KAAT,EAAgB;MACZ,IAAI,KAAKX,UAAL,IAAmBI,IAAI,KAAK,KAAKJ,UAArC,EAAiD;QAC7C,KAAKA,UAAL,GAAkB,IAAlB;QACA,KAAKY,2BAAL;MACH,CAHD,MAIK;QACD,KAAKZ,UAAL,GAAkBI,IAAlB;;QACA,IAAI,KAAKL,IAAT,EAAe;UACX,KAAKc,yBAAL;QACH;MACJ;IACJ;;IACD,IAAI,CAACT,IAAI,CAACO,KAAV,EAAiB;MACb,KAAKG,WAAL;IACH;EACJ;;EACDC,gBAAgB,CAACZ,KAAD,EAAQC,IAAR,EAAc;IAC1B,IAAIA,IAAI,CAACC,QAAL,IAAiB,KAAKW,YAA1B,EAAwC;MACpCb,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,KAAKP,IAAT,EAAe;MACX,IAAI,KAAKC,UAAL,IAAmB,KAAKiB,WAAxB,IAAuC,KAAKC,KAAhD,EAAuD;QACnD,KAAKlB,UAAL,GAAkBI,IAAlB;QACA,KAAKS,yBAAL;MACH;IACJ,CALD,MAMK;MACD,KAAKb,UAAL,GAAkBI,IAAlB;MACA,KAAKS,yBAAL;IACH;EACJ;;EACDC,WAAW,GAAG;IACV,KAAKd,UAAL,GAAkB,IAAlB;;IACA,IAAI,KAAKD,IAAT,EAAe;MACX,KAAKa,2BAAL;IACH;;IACD,KAAKnB,SAAL,CAAe0B,IAAf;EACH;;EACDC,aAAa,CAACjB,KAAD,EAAQC,IAAR,EAAc;IACvB,IAAIiB,QAAQ,GAAGlB,KAAK,CAACmB,aAAN,CAAoBC,aAAnC;;IACA,QAAQpB,KAAK,CAACqB,GAAd;MACI,KAAK,WAAL;QACI,MAAMC,QAAQ,GAAG,KAAKC,YAAL,CAAkBL,QAAlB,CAAjB;;QACA,IAAII,QAAJ,EAAc;UACVA,QAAQ,CAACE,QAAT,CAAkB,CAAlB,EAAqBC,KAArB;QACH;;QACDzB,KAAK,CAACG,cAAN;QACA;;MACJ,KAAK,SAAL;QACI,MAAMuB,QAAQ,GAAG,KAAKC,YAAL,CAAkBT,QAAlB,CAAjB;;QACA,IAAIQ,QAAJ,EAAc;UACVA,QAAQ,CAACF,QAAT,CAAkB,CAAlB,EAAqBC,KAArB;QACH;;QACDzB,KAAK,CAACG,cAAN;QACA;;MACJ,KAAK,YAAL;QACI,IAAIF,IAAI,CAACO,KAAT,EAAgB;UACZ,KAAKX,UAAL,GAAkBI,IAAlB;;UACA,IAAI,KAAKL,IAAT,EAAe;YACX,KAAKc,yBAAL;UACH;;UACDkB,UAAU,CAAC,MAAM;YACbV,QAAQ,CAACM,QAAT,CAAkB,CAAlB,EAAqBA,QAArB,CAA8B,CAA9B,EAAiCA,QAAjC,CAA0C,CAA1C,EAA6CA,QAA7C,CAAsD,CAAtD,EAAyDC,KAAzD;UACH,CAFS,EAEP,EAFO,CAAV;QAGH;;QACDzB,KAAK,CAACG,cAAN;QACA;;MACJ,KAAK,OAAL;QACI,IAAI,CAACF,IAAI,CAACI,UAAV,EAAsB;UAClB,KAAKN,WAAL,CAAiBC,KAAjB,EAAwBC,IAAxB;QACH;;QACD;;MACJ;QACI;IAjCR;;IAmCA,KAAKV,WAAL,CAAiByB,IAAjB,CAAsB;MAClBT,aAAa,EAAEP,KADG;MAElB6B,OAAO,EAAEX;IAFS,CAAtB;EAIH;;EACDpB,eAAe,GAAG;IACd,IAAIgC,OAAO,GAAG,KAAKC,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBC,aAA7D;;IACA,IAAIF,OAAJ,EAAa;MACT,MAAMG,UAAU,GAAGH,OAAO,CAACV,aAAR,CAAsBA,aAAzC;MACA,MAAMc,eAAe,GAAGjE,UAAU,CAACkE,SAAX,CAAqBF,UAArB,CAAxB;MACA,MAAMG,QAAQ,GAAGnE,UAAU,CAACoE,WAAX,EAAjB;MACA,MAAMC,YAAY,GAAGR,OAAO,CAACS,YAAR,GAAuBT,OAAO,CAACU,WAA/B,GAA6CvE,UAAU,CAACwE,0BAAX,CAAsCX,OAAtC,CAAlE;MACA,MAAMY,cAAc,GAAGzE,UAAU,CAAC0E,aAAX,CAAyBV,UAAU,CAACT,QAAX,CAAoB,CAApB,CAAzB,CAAvB;;MACA,IAAKoB,QAAQ,CAACV,eAAe,CAACW,IAAjB,EAAuB,EAAvB,CAAR,GAAqCH,cAArC,GAAsDJ,YAAvD,GAAwEF,QAAQ,CAACU,KAAT,GAAiB7E,UAAU,CAAC8E,uBAAX,EAA7F,EAAoI;QAChI9E,UAAU,CAAC+E,QAAX,CAAoBlB,OAApB,EAA6B,wBAA7B;MACH;IACJ;EACJ;;EACDP,YAAY,CAACtB,IAAD,EAAO;IACf,IAAIqB,QAAQ,GAAGrB,IAAI,CAACgD,kBAApB;IACA,IAAI3B,QAAJ,EACI,OAAOrD,UAAU,CAACiF,QAAX,CAAoB5B,QAApB,EAA8B,YAA9B,KAA+C,CAACrD,UAAU,CAACiF,QAAX,CAAoB5B,QAApB,EAA8B,YAA9B,CAAhD,GAA8F,KAAKC,YAAL,CAAkBD,QAAlB,CAA9F,GAA4HA,QAAnI,CADJ,KAGI,OAAO,IAAP;EACP;;EACDK,YAAY,CAAC1B,IAAD,EAAO;IACf,IAAIyB,QAAQ,GAAGzB,IAAI,CAACkD,sBAApB;IACA,IAAIzB,QAAJ,EACI,OAAOzD,UAAU,CAACiF,QAAX,CAAoBxB,QAApB,EAA8B,YAA9B,KAA+C,CAACzD,UAAU,CAACiF,QAAX,CAAoBxB,QAApB,EAA8B,YAA9B,CAAhD,GAA8F,KAAKC,YAAL,CAAkBD,QAAlB,CAA9F,GAA4HA,QAAnI,CADJ,KAGI,OAAO,IAAP;EACP;;EACD0B,kBAAkB,CAACpD,KAAD,EAAQ;IACtB,IAAIA,KAAK,CAACO,aAAN,CAAoBc,GAApB,KAA4B,WAAhC,EAA6C;MACzC,KAAKxB,UAAL,GAAkB,IAAlB;;MACA,IAAI,KAAKD,IAAT,EAAe;QACX,KAAKa,2BAAL;MACH;;MACDT,KAAK,CAAC6B,OAAN,CAAcT,aAAd,CAA4BA,aAA5B,CAA0CA,aAA1C,CAAwDI,QAAxD,CAAiE,CAAjE,EAAoEC,KAApE;IACH;EACJ;;EACDf,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAK2C,qBAAV,EAAiC;MAC7B,KAAKA,qBAAL,GAA8BrD,KAAD,IAAW;QACpC,IAAI,KAAKf,EAAL,IAAW,CAAC,KAAKA,EAAL,CAAQ+C,aAAR,CAAsBsB,QAAtB,CAA+BtD,KAAK,CAACuD,MAArC,CAAhB,EAA8D;UAC1D,KAAK1D,UAAL,GAAkB,IAAlB;UACA,KAAKV,EAAL,CAAQqE,YAAR;UACA,KAAK/C,2BAAL;QACH;MACJ,CAND;;MAOAgD,QAAQ,CAACC,gBAAT,CAA0B,OAA1B,EAAmC,KAAKL,qBAAxC;IACH;EACJ;;EACD5C,2BAA2B,GAAG;IAC1B,IAAI,KAAK4C,qBAAT,EAAgC;MAC5BI,QAAQ,CAACE,mBAAT,CAA6B,OAA7B,EAAsC,KAAKN,qBAA3C;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACDO,WAAW,GAAG;IACV,KAAKnD,2BAAL;EACH;;AA9Ke;;AAgLpB1B,aAAa,CAAC8E,IAAd;EAAA,iBAA0G9E,aAA1G,EAAgGzB,EAAhG,mBAAyIA,EAAE,CAACwG,UAA5I,GAAgGxG,EAAhG,mBAAmKA,EAAE,CAACyG,SAAtK,GAAgGzG,EAAhG,mBAA4LA,EAAE,CAAC0G,iBAA/L;AAAA;;AACAjF,aAAa,CAACkF,IAAd,kBADgG3G,EAChG;EAAA,MAA8FyB,aAA9F;EAAA;EAAA;IAAA;MADgGzB,EAChG;IAAA;;IAAA;MAAA;;MADgGA,EAChG,qBADgGA,EAChG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADgGA,EAExF,8BADR;MADgGA,EAGpF,4EAFZ;MADgGA,EA6BxF,eA5BR;IAAA;;IAAA;MADgGA,EAE3E,uBAF2EA,EAE3E,oCADrB;MADgGA,EAGvD,aAFzC;MADgGA,EAGvD,4DAFzC;IAAA;EAAA;EAAA,eA6BiES,EAAE,CAACmG,OA7BpE,EA6B+JnG,EAAE,CAACoG,OA7BlK,EA6B4RpG,EAAE,CAACqG,IA7B/R,EA6BgYrG,EAAE,CAACsG,OA7BnY,EA6BqdlG,EAAE,CAACmG,kBA7Bxd,EA6BwtBnG,EAAE,CAACoG,gBA7B3tB,EA6By7BlG,EAAE,CAACmG,MA7B57B,EA6Bw/B5F,EAAE,CAAC6F,OA7B3/B,EA6BmzC1F,aA7BnzC;EAAA;AAAA;;AA8BA;EAAA,mDA/BgGzB,EA+BhG,mBAA2FyB,aAA3F,EAAsH,CAAC;IAC3G2F,IAAI,EAAElH,SADqG;IAE3GmH,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA/BmB;MAgCCC,aAAa,EAAErH,iBAAiB,CAACsH,IAhClC;MAiCCC,IAAI,EAAE;QACF,SAAS;MADP;IAjCP,CAAD;EAFqG,CAAD,CAAtH,EAuC4B,YAAY;IAAE,OAAO,CAAC;MAAEN,IAAI,EAAEpH,EAAE,CAACwG;IAAX,CAAD,EAA0B;MAAEY,IAAI,EAAEpH,EAAE,CAACyG;IAAX,CAA1B,EAAkD;MAAEW,IAAI,EAAEpH,EAAE,CAAC0G;IAAX,CAAlD,CAAP;EAA2F,CAvCrI,EAuCuJ;IAAE/D,IAAI,EAAE,CAAC;MAChJyE,IAAI,EAAEhH;IAD0I,CAAD,CAAR;IAEvIkC,IAAI,EAAE,CAAC;MACP8E,IAAI,EAAEhH;IADC,CAAD,CAFiI;IAIvIoD,WAAW,EAAE,CAAC;MACd4D,IAAI,EAAEhH;IADQ,CAAD,CAJ0H;IAMvI0B,UAAU,EAAE,CAAC;MACbsF,IAAI,EAAEhH;IADO,CAAD,CAN2H;IAQvI2B,UAAU,EAAE,CAAC;MACbqF,IAAI,EAAEhH;IADO,CAAD,CAR2H;IAUvImD,YAAY,EAAE,CAAC;MACf6D,IAAI,EAAEhH;IADS,CAAD,CAVyH;IAYvIqD,KAAK,EAAE,CAAC;MACR2D,IAAI,EAAEhH;IADE,CAAD,CAZgI;IAcvI+B,YAAY,EAAE,CAAC;MACfiF,IAAI,EAAEhH;IADS,CAAD,CAdyH;IAgBvIqE,gBAAgB,EAAE,CAAC;MACnB2C,IAAI,EAAE/G,SADa;MAEnBgH,IAAI,EAAE,CAAC,SAAD;IAFa,CAAD,CAhBqH;IAmBvIrF,SAAS,EAAE,CAAC;MACZoF,IAAI,EAAE9G;IADM,CAAD,CAnB4H;IAqBvI2B,WAAW,EAAE,CAAC;MACdmF,IAAI,EAAE9G;IADQ,CAAD;EArB0H,CAvCvJ;AAAA;;AA+DA,MAAMqH,UAAN,CAAiB;EACbjG,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmB+F,MAAnB,EAA2BC,cAA3B,EAA2C;IAClD,KAAKlG,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAK+F,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAK/F,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAK+F,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;EACH;;EACDC,MAAM,CAACtF,KAAD,EAAQ;IACV,IAAI,KAAKuF,OAAT,EACI,KAAKC,IAAL,GADJ,KAGI,KAAKC,IAAL,CAAUzF,KAAV;IACJ,KAAK0F,sBAAL,GAA8B,IAA9B;EACH;;EACDD,IAAI,CAACzF,KAAD,EAAQ;IACR,KAAKuD,MAAL,GAAcvD,KAAK,CAACmB,aAApB;IACA,KAAKwE,aAAL,GAAqB3F,KAAK,CAAC2F,aAA3B;IACA,KAAKJ,OAAL,GAAe,IAAf;IACA,KAAK9F,YAAL,GAAoB,IAApB;IACA,KAAKiG,sBAAL,GAA8B,IAA9B;IACA,KAAKvG,EAAL,CAAQqE,YAAR;EACH;;EACDoC,cAAc,CAAC5F,KAAD,EAAQ;IAClB,IAAI,KAAKe,KAAT,EAAgB;MACZ,KAAKoE,cAAL,CAAoBU,GAApB,CAAwB;QACpBtF,aAAa,EAAEP,KADK;QAEpBuD,MAAM,EAAE,KAAKtE,EAAL,CAAQ+C;MAFI,CAAxB;IAIH;;IACD,KAAK0D,sBAAL,GAA8B,IAA9B;EACH;;EACDI,uBAAuB,CAAC9F,KAAD,EAAQ;IAC3B,QAAQA,KAAK,CAAC+F,OAAd;MACI,KAAK,SAAL;QACI,IAAI,KAAKhF,KAAT,EAAgB;UACZ,KAAKiF,SAAL,GAAiBhG,KAAK,CAAC6B,OAAvB;UACA,KAAKoE,SAAL;UACA,KAAKC,aAAL;UACA,KAAKC,YAAL;UACA,KAAKzF,yBAAL;UACA,KAAK0F,0BAAL;UACA,KAAKC,kBAAL;QACH;;QACD;;MACJ,KAAK,MAAL;QACI,KAAKC,aAAL;QACA;IAdR;EAgBH;;EACDH,YAAY,GAAG;IACX,IAAI,KAAKR,aAAT,EACI1H,UAAU,CAACsI,gBAAX,CAA4B,KAAKP,SAAjC,EAA4C,KAAKzC,MAAjD,EADJ,KAGItF,UAAU,CAACuI,gBAAX,CAA4B,KAAKR,SAAjC,EAA4C,KAAKzC,MAAjD;EACP;;EACDkD,qBAAqB,CAACzG,KAAD,EAAQ;IACzB,QAAQA,KAAK,CAAC+F,OAAd;MACI,KAAK,MAAL;QACIpH,WAAW,CAAC+H,KAAZ,CAAkB1G,KAAK,CAAC6B,OAAxB;QACA;IAHR;EAKH;;EACDqE,aAAa,GAAG;IACZ,IAAI,KAAKS,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACIlD,QAAQ,CAACmD,IAAT,CAAcC,WAAd,CAA0B,KAAKb,SAA/B,EADJ,KAGI/H,UAAU,CAAC4I,WAAX,CAAuB,KAAKb,SAA5B,EAAuC,KAAKW,QAA5C;IACP;EACJ;;EACDG,oBAAoB,GAAG;IACnB,IAAI,KAAKd,SAAL,IAAkB,KAAKW,QAA3B,EAAqC;MACjC,KAAK1H,EAAL,CAAQ+C,aAAR,CAAsB6E,WAAtB,CAAkC,KAAKb,SAAvC;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,IAAI,KAAK7G,UAAT,EAAqB;MACjBT,WAAW,CAACoI,GAAZ,CAAgB,MAAhB,EAAwB,KAAKf,SAA7B,EAAwC,KAAK3G,UAAL,GAAkB,KAAK6F,MAAL,CAAY8B,MAAZ,CAAmBC,IAA7E;IACH;EACJ;;EACDzB,IAAI,GAAG;IACH,KAAKD,OAAL,GAAe,KAAf;IACA,KAAKI,aAAL,GAAqB,KAArB;IACA,KAAKlG,YAAL,GAAoB,KAApB;IACA,KAAKN,EAAL,CAAQqE,YAAR;EACH;;EACD0D,cAAc,GAAG;IACb,IAAI,KAAK3B,OAAL,IAAgB,CAACtH,UAAU,CAACkJ,aAAX,EAArB,EAAiD;MAC7C,KAAK3B,IAAL;IACH;EACJ;;EACD7E,WAAW,GAAG;IACV,IAAI,KAAKI,KAAT,EAAgB;MACZ,KAAKyE,IAAL;IACH;;IACD,KAAK/E,2BAAL;EACH;;EACDC,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAK2C,qBAAV,EAAiC;MAC7B,MAAM+D,cAAc,GAAG,KAAKnI,EAAL,GAAU,KAAKA,EAAL,CAAQ+C,aAAR,CAAsBqF,aAAhC,GAAgD,UAAvE;MACA,KAAKhE,qBAAL,GAA6B,KAAKnE,QAAL,CAAcoI,MAAd,CAAqBF,cAArB,EAAqC,OAArC,EAA8C,MAAM;QAC7E,IAAI,CAAC,KAAK1B,sBAAN,IAAgC,KAAK3E,KAAzC,EAAgD;UAC5C,KAAKyE,IAAL;QACH;;QACD,KAAKE,sBAAL,GAA8B,KAA9B;MACH,CAL4B,CAA7B;IAMH;EACJ;;EACDjF,2BAA2B,GAAG;IAC1B,IAAI,KAAK4C,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACD+C,0BAA0B,GAAG;IACzB,KAAKmB,sBAAL,GAA8B,KAAKL,cAAL,CAAoBM,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAAC/D,gBAAP,CAAwB,QAAxB,EAAkC,KAAK6D,sBAAvC;EACH;;EACDG,4BAA4B,GAAG;IAC3B,IAAI,KAAKH,sBAAT,EAAiC;MAC7BE,MAAM,CAAC9D,mBAAP,CAA2B,QAA3B,EAAqC,KAAK4D,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDlB,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKsB,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAIzJ,6BAAJ,CAAkC,KAAKqF,MAAvC,EAA+C,MAAM;QACtE,IAAI,KAAKgC,OAAT,EAAkB;UACd,KAAKC,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKmC,aAAL,CAAmBtB,kBAAnB;EACH;;EACDuB,oBAAoB,GAAG;IACnB,IAAI,KAAKD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBC,oBAAnB;IACH;EACJ;;EACDtB,aAAa,GAAG;IACZ,KAAK7F,2BAAL;IACA,KAAKiH,4BAAL;IACA,KAAKE,oBAAL;IACA,KAAKlC,sBAAL,GAA8B,KAA9B;;IACA,IAAI,CAAC,KAAKvG,EAAL,CAAQ0I,SAAb,EAAwB;MACpB,KAAKtE,MAAL,GAAc,IAAd;IACH;EACJ;;EACDK,WAAW,GAAG;IACV,IAAI,KAAK7C,KAAT,EAAgB;MACZ,IAAI,KAAK4G,aAAT,EAAwB;QACpB,KAAKA,aAAL,CAAmBG,OAAnB;QACA,KAAKH,aAAL,GAAqB,IAArB;MACH;;MACD,IAAI,KAAK3B,SAAL,IAAkB,KAAK5G,UAA3B,EAAuC;QACnCT,WAAW,CAAC+H,KAAZ,CAAkB,KAAKV,SAAvB;MACH;;MACD,KAAKc,oBAAL;MACA,KAAKR,aAAL;IACH;EACJ;;AArKY;;AAuKjBrB,UAAU,CAACpB,IAAX;EAAA,iBAAuGoB,UAAvG,EArQgG3H,EAqQhG,mBAAmIA,EAAE,CAACwG,UAAtI,GArQgGxG,EAqQhG,mBAA6JA,EAAE,CAACyG,SAAhK,GArQgGzG,EAqQhG,mBAAsLA,EAAE,CAAC0G,iBAAzL,GArQgG1G,EAqQhG,mBAAuNwB,EAAE,CAACiJ,aAA1N,GArQgGzK,EAqQhG,mBAAoPwB,EAAE,CAACkJ,cAAvP;AAAA;;AACA/C,UAAU,CAAChB,IAAX,kBAtQgG3G,EAsQhG;EAAA,MAA2F2H,UAA3F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAtQgG3H,EAuQxF,0DADR;IAAA;;IAAA;MAtQgGA,EAyQkC,8CAHlI;IAAA;EAAA;EAAA,eAOgvBS,EAAE,CAACmG,OAPnvB,EAO80BnG,EAAE,CAACqG,IAPj1B,EAOk7BrG,EAAE,CAACsG,OAPr7B,EAOugCtF,aAPvgC;EAAA;EAAA;EAAA;IAAA,WAOutC,CAC/sCR,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;MAAEwJ,OAAO,EAAE,CAAX;MAAcC,SAAS,EAAE;IAAzB,CAAD,CADY,EAEjBxJ,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAEwJ,OAAO,EAAE;IAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADwsC;EAPvtC;EAAA;AAAA;;AAkBA;EAAA,mDAxRgG3K,EAwRhG,mBAA2F2H,UAA3F,EAAmH,CAAC;IACxGP,IAAI,EAAElH,SADkG;IAExGmH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAZ;MAA4BC,QAAQ,EAAG;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA,KAPmB;MAOZsD,UAAU,EAAE,CACK5J,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;QAAEwJ,OAAO,EAAE,CAAX;QAAcC,SAAS,EAAE;MAAzB,CAAD,CADY,EAEjBxJ,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAEwJ,OAAO,EAAE;MAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADZ,CAPA;MAiBIG,eAAe,EAAEvK,uBAAuB,CAACwK,MAjB7C;MAiBqDvD,aAAa,EAAErH,iBAAiB,CAACsH,IAjBtF;MAiB4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CAjBlG;MAmBIsD,MAAM,EAAE,CAAC,mqBAAD;IAnBZ,CAAD;EAFkG,CAAD,CAAnH,EAsB4B,YAAY;IAAE,OAAO,CAAC;MAAE5D,IAAI,EAAEpH,EAAE,CAACwG;IAAX,CAAD,EAA0B;MAAEY,IAAI,EAAEpH,EAAE,CAACyG;IAAX,CAA1B,EAAkD;MAAEW,IAAI,EAAEpH,EAAE,CAAC0G;IAAX,CAAlD,EAAkF;MAAEU,IAAI,EAAE5F,EAAE,CAACiJ;IAAX,CAAlF,EAA8G;MAAErD,IAAI,EAAE5F,EAAE,CAACkJ;IAAX,CAA9G,CAAP;EAAoJ,CAtB9L,EAsBgN;IAAEO,KAAK,EAAE,CAAC;MAC1M7D,IAAI,EAAEhH;IADoM,CAAD,CAAT;IAEhMqD,KAAK,EAAE,CAAC;MACR2D,IAAI,EAAEhH;IADE,CAAD,CAFyL;IAIhMe,KAAK,EAAE,CAAC;MACRiG,IAAI,EAAEhH;IADE,CAAD,CAJyL;IAMhM8K,UAAU,EAAE,CAAC;MACb9D,IAAI,EAAEhH;IADO,CAAD,CANoL;IAQhMiJ,QAAQ,EAAE,CAAC;MACXjC,IAAI,EAAEhH;IADK,CAAD,CARsL;IAUhM0B,UAAU,EAAE,CAAC;MACbsF,IAAI,EAAEhH;IADO,CAAD,CAVoL;IAYhM2B,UAAU,EAAE,CAAC;MACbqF,IAAI,EAAEhH;IADO,CAAD,CAZoL;IAchMoD,WAAW,EAAE,CAAC;MACd4D,IAAI,EAAEhH;IADQ,CAAD,CAdmL;IAgBhM0H,qBAAqB,EAAE,CAAC;MACxBV,IAAI,EAAEhH;IADkB,CAAD,CAhByK;IAkBhM2H,qBAAqB,EAAE,CAAC;MACxBX,IAAI,EAAEhH;IADkB,CAAD;EAlByK,CAtBhN;AAAA;;AA2CA,MAAM+K,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAAC5E,IAAjB;EAAA,iBAA6G4E,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBAtUgGpL,EAsUhG;EAAA,MAA8GmL;AAA9G;AACAA,gBAAgB,CAACE,IAAjB,kBAvUgGrL,EAuUhG;EAAA,UAA0IU,YAA1I,EAAwJI,YAAxJ,EAAsKE,YAAtK,EAAoLO,aAApL,EAAmMT,YAAnM,EAAiNS,aAAjN;AAAA;;AACA;EAAA,mDAxUgGvB,EAwUhG,mBAA2FmL,gBAA3F,EAAyH,CAAC;IAC9G/D,IAAI,EAAE5G,QADwG;IAE9G6G,IAAI,EAAE,CAAC;MACCiE,OAAO,EAAE,CAAC5K,YAAD,EAAeI,YAAf,EAA6BE,YAA7B,EAA2CO,aAA3C,CADV;MAECgK,OAAO,EAAE,CAAC5D,UAAD,EAAa7G,YAAb,EAA2BS,aAA3B,CAFV;MAGCiK,YAAY,EAAE,CAAC7D,UAAD,EAAalG,aAAb;IAHf,CAAD;EAFwG,CAAD,CAAzH;AAAA;AASA;AACA;AACA;;;AAEA,SAASkG,UAAT,EAAqBwD,gBAArB,EAAuC1J,aAAvC"}, "metadata": {}, "sourceType": "module"}