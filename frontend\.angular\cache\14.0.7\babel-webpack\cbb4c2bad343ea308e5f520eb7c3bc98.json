{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ObjectUtils } from 'primeng/utils';\nconst _c0 = [\"cb\"];\n\nconst _c1 = function (a1, a2, a3) {\n  return {\n    \"p-checkbox-label\": true,\n    \"p-checkbox-label-active\": a1,\n    \"p-disabled\": a2,\n    \"p-checkbox-label-focus\": a3\n  };\n};\n\nfunction Checkbox_label_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"label\", 7);\n    i0.ɵɵlistener(\"click\", function Checkbox_label_6_Template_label_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r2 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(3);\n\n      return i0.ɵɵresetView(ctx_r2.onClick($event, _r0, true));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.labelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(5, _c1, ctx_r1.checked(), ctx_r1.disabled, ctx_r1.focused));\n    i0.ɵɵattribute(\"for\", ctx_r1.inputId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.label);\n  }\n}\n\nconst _c2 = function (a1, a2, a3) {\n  return {\n    \"p-checkbox p-component\": true,\n    \"p-checkbox-checked\": a1,\n    \"p-checkbox-disabled\": a2,\n    \"p-checkbox-focused\": a3\n  };\n};\n\nconst _c3 = function (a0, a1, a2) {\n  return {\n    \"p-highlight\": a0,\n    \"p-disabled\": a1,\n    \"p-focus\": a2\n  };\n};\n\nconst CHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Checkbox),\n  multi: true\n};\n\nclass Checkbox {\n  constructor(cd) {\n    this.cd = cd;\n    this.checkboxIcon = 'pi pi-check';\n    this.trueValue = true;\n    this.falseValue = false;\n    this.onChange = new EventEmitter();\n\n    this.onModelChange = () => {};\n\n    this.onModelTouched = () => {};\n\n    this.focused = false;\n  }\n\n  onClick(event, checkbox, focus) {\n    event.preventDefault();\n\n    if (this.disabled || this.readonly) {\n      return;\n    }\n\n    this.updateModel(event);\n\n    if (focus) {\n      checkbox.focus();\n    }\n  }\n\n  updateModel(event) {\n    let newModelValue;\n\n    if (!this.binary) {\n      if (this.checked()) newModelValue = this.model.filter(val => !ObjectUtils.equals(val, this.value));else newModelValue = this.model ? [...this.model, this.value] : [this.value];\n      this.onModelChange(newModelValue);\n      this.model = newModelValue;\n\n      if (this.formControl) {\n        this.formControl.setValue(newModelValue);\n      }\n    } else {\n      newModelValue = this.checked() ? this.falseValue : this.trueValue;\n      this.model = newModelValue;\n      this.onModelChange(newModelValue);\n    }\n\n    this.onChange.emit({\n      checked: newModelValue,\n      originalEvent: event\n    });\n  }\n\n  handleChange(event) {\n    if (!this.readonly) {\n      this.updateModel(event);\n    }\n  }\n\n  onFocus() {\n    this.focused = true;\n  }\n\n  onBlur() {\n    this.focused = false;\n    this.onModelTouched();\n  }\n\n  focus() {\n    this.inputViewChild.nativeElement.focus();\n  }\n\n  writeValue(model) {\n    this.model = model;\n    this.cd.markForCheck();\n  }\n\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n\n  checked() {\n    return this.binary ? this.model === this.trueValue : ObjectUtils.contains(this.value, this.model);\n  }\n\n}\n\nCheckbox.ɵfac = function Checkbox_Factory(t) {\n  return new (t || Checkbox)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Checkbox,\n  selectors: [[\"p-checkbox\"]],\n  viewQuery: function Checkbox_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    value: \"value\",\n    name: \"name\",\n    disabled: \"disabled\",\n    binary: \"binary\",\n    label: \"label\",\n    ariaLabelledBy: \"ariaLabelledBy\",\n    ariaLabel: \"ariaLabel\",\n    tabindex: \"tabindex\",\n    inputId: \"inputId\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    labelStyleClass: \"labelStyleClass\",\n    formControl: \"formControl\",\n    checkboxIcon: \"checkboxIcon\",\n    readonly: \"readonly\",\n    required: \"required\",\n    trueValue: \"trueValue\",\n    falseValue: \"falseValue\"\n  },\n  outputs: {\n    onChange: \"onChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([CHECKBOX_VALUE_ACCESSOR])],\n  decls: 7,\n  vars: 26,\n  consts: [[3, \"ngStyle\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"readonly\", \"value\", \"checked\", \"disabled\", \"focus\", \"blur\", \"change\"], [\"cb\", \"\"], [1, \"p-checkbox-box\", 3, \"ngClass\", \"click\"], [1, \"p-checkbox-icon\", 3, \"ngClass\"], [3, \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"click\"]],\n  template: function Checkbox_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r4 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"input\", 2, 3);\n      i0.ɵɵlistener(\"focus\", function Checkbox_Template_input_focus_2_listener() {\n        return ctx.onFocus();\n      })(\"blur\", function Checkbox_Template_input_blur_2_listener() {\n        return ctx.onBlur();\n      })(\"change\", function Checkbox_Template_input_change_2_listener($event) {\n        return ctx.handleChange($event);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(4, \"div\", 4);\n      i0.ɵɵlistener(\"click\", function Checkbox_Template_div_click_4_listener($event) {\n        i0.ɵɵrestoreView(_r4);\n\n        const _r0 = i0.ɵɵreference(3);\n\n        return i0.ɵɵresetView(ctx.onClick($event, _r0, true));\n      });\n      i0.ɵɵelement(5, \"span\", 5);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(6, Checkbox_label_6_Template, 2, 9, \"label\", 6);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction3(18, _c2, ctx.checked(), ctx.disabled, ctx.focused));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"readonly\", ctx.readonly)(\"value\", ctx.value)(\"checked\", ctx.checked())(\"disabled\", ctx.disabled);\n      i0.ɵɵattribute(\"id\", ctx.inputId)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"aria-checked\", ctx.checked())(\"required\", ctx.required);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(22, _c3, ctx.checked(), ctx.disabled, ctx.focused));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", ctx.checked() ? ctx.checkboxIcon : null);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.label);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n  styles: [\".p-checkbox{display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;vertical-align:bottom;position:relative}.p-checkbox-disabled{cursor:default!important;pointer-events:none}.p-checkbox-box{display:flex;justify-content:center;align-items:center}p-checkbox{display:inline-flex;vertical-align:bottom;align-items:center}.p-checkbox-label{line-height:1}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Checkbox, [{\n    type: Component,\n    args: [{\n      selector: 'p-checkbox',\n      template: `\n        <div [ngStyle]=\"style\" [ngClass]=\"{'p-checkbox p-component': true, 'p-checkbox-checked': checked(), 'p-checkbox-disabled': disabled, 'p-checkbox-focused': focused}\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [attr.id]=\"inputId\" [attr.name]=\"name\" [readonly]=\"readonly\" [value]=\"value\" [checked]=\"checked()\" (focus)=\"onFocus()\" (blur)=\"onBlur()\"\n                (change)=\"handleChange($event)\" [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\" [attr.aria-checked]=\"checked()\" [attr.required]=\"required\">\n            </div>\n            <div class=\"p-checkbox-box\" (click)=\"onClick($event,cb,true)\"\n                        [ngClass]=\"{'p-highlight': checked(), 'p-disabled': disabled, 'p-focus': focused}\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"checked() ? checkboxIcon : null\"></span>\n            </div>\n        </div>\n        <label (click)=\"onClick($event,cb,true)\" [class]=\"labelStyleClass\"\n                [ngClass]=\"{'p-checkbox-label': true, 'p-checkbox-label-active':checked(), 'p-disabled':disabled, 'p-checkbox-label-focus':focused}\"\n                *ngIf=\"label\" [attr.for]=\"inputId\">{{label}}</label>\n    `,\n      providers: [CHECKBOX_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-checkbox{display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;vertical-align:bottom;position:relative}.p-checkbox-disabled{cursor:default!important;pointer-events:none}.p-checkbox-box{display:flex;justify-content:center;align-items:center}p-checkbox{display:inline-flex;vertical-align:bottom;align-items:center}.p-checkbox-label{line-height:1}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    binary: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    labelStyleClass: [{\n      type: Input\n    }],\n    formControl: [{\n      type: Input\n    }],\n    checkboxIcon: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    trueValue: [{\n      type: Input\n    }],\n    falseValue: [{\n      type: Input\n    }],\n    inputViewChild: [{\n      type: ViewChild,\n      args: ['cb']\n    }],\n    onChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass CheckboxModule {}\n\nCheckboxModule.ɵfac = function CheckboxModule_Factory(t) {\n  return new (t || CheckboxModule)();\n};\n\nCheckboxModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CheckboxModule\n});\nCheckboxModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Checkbox],\n      declarations: [Checkbox]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CHECKBOX_VALUE_ACCESSOR, Checkbox, CheckboxModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "Output", "NgModule", "i1", "CommonModule", "NG_VALUE_ACCESSOR", "ObjectUtils", "CHECKBOX_VALUE_ACCESSOR", "provide", "useExisting", "Checkbox", "multi", "constructor", "cd", "checkboxIcon", "trueValue", "falseValue", "onChange", "onModelChange", "onModelTouched", "focused", "onClick", "event", "checkbox", "focus", "preventDefault", "disabled", "readonly", "updateModel", "newModelValue", "binary", "checked", "model", "filter", "val", "equals", "value", "formControl", "setValue", "emit", "originalEvent", "handleChange", "onFocus", "onBlur", "inputViewChild", "nativeElement", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "contains", "ɵfac", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "type", "args", "selector", "template", "providers", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "name", "label", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "tabindex", "inputId", "style", "styleClass", "labelStyleClass", "required", "CheckboxModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-checkbox.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ObjectUtils } from 'primeng/utils';\n\nconst CHECKBOX_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Checkbox),\n    multi: true\n};\nclass Checkbox {\n    constructor(cd) {\n        this.cd = cd;\n        this.checkboxIcon = 'pi pi-check';\n        this.trueValue = true;\n        this.falseValue = false;\n        this.onChange = new EventEmitter();\n        this.onModelChange = () => { };\n        this.onModelTouched = () => { };\n        this.focused = false;\n    }\n    onClick(event, checkbox, focus) {\n        event.preventDefault();\n        if (this.disabled || this.readonly) {\n            return;\n        }\n        this.updateModel(event);\n        if (focus) {\n            checkbox.focus();\n        }\n    }\n    updateModel(event) {\n        let newModelValue;\n        if (!this.binary) {\n            if (this.checked())\n                newModelValue = this.model.filter(val => !ObjectUtils.equals(val, this.value));\n            else\n                newModelValue = this.model ? [...this.model, this.value] : [this.value];\n            this.onModelChange(newModelValue);\n            this.model = newModelValue;\n            if (this.formControl) {\n                this.formControl.setValue(newModelValue);\n            }\n        }\n        else {\n            newModelValue = this.checked() ? this.falseValue : this.trueValue;\n            this.model = newModelValue;\n            this.onModelChange(newModelValue);\n        }\n        this.onChange.emit({ checked: newModelValue, originalEvent: event });\n    }\n    handleChange(event) {\n        if (!this.readonly) {\n            this.updateModel(event);\n        }\n    }\n    onFocus() {\n        this.focused = true;\n    }\n    onBlur() {\n        this.focused = false;\n        this.onModelTouched();\n    }\n    focus() {\n        this.inputViewChild.nativeElement.focus();\n    }\n    writeValue(model) {\n        this.model = model;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    checked() {\n        return this.binary ? this.model === this.trueValue : ObjectUtils.contains(this.value, this.model);\n    }\n}\nCheckbox.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Checkbox, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nCheckbox.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Checkbox, selector: \"p-checkbox\", inputs: { value: \"value\", name: \"name\", disabled: \"disabled\", binary: \"binary\", label: \"label\", ariaLabelledBy: \"ariaLabelledBy\", ariaLabel: \"ariaLabel\", tabindex: \"tabindex\", inputId: \"inputId\", style: \"style\", styleClass: \"styleClass\", labelStyleClass: \"labelStyleClass\", formControl: \"formControl\", checkboxIcon: \"checkboxIcon\", readonly: \"readonly\", required: \"required\", trueValue: \"trueValue\", falseValue: \"falseValue\" }, outputs: { onChange: \"onChange\" }, host: { classAttribute: \"p-element\" }, providers: [CHECKBOX_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"inputViewChild\", first: true, predicate: [\"cb\"], descendants: true }], ngImport: i0, template: `\n        <div [ngStyle]=\"style\" [ngClass]=\"{'p-checkbox p-component': true, 'p-checkbox-checked': checked(), 'p-checkbox-disabled': disabled, 'p-checkbox-focused': focused}\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [attr.id]=\"inputId\" [attr.name]=\"name\" [readonly]=\"readonly\" [value]=\"value\" [checked]=\"checked()\" (focus)=\"onFocus()\" (blur)=\"onBlur()\"\n                (change)=\"handleChange($event)\" [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\" [attr.aria-checked]=\"checked()\" [attr.required]=\"required\">\n            </div>\n            <div class=\"p-checkbox-box\" (click)=\"onClick($event,cb,true)\"\n                        [ngClass]=\"{'p-highlight': checked(), 'p-disabled': disabled, 'p-focus': focused}\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"checked() ? checkboxIcon : null\"></span>\n            </div>\n        </div>\n        <label (click)=\"onClick($event,cb,true)\" [class]=\"labelStyleClass\"\n                [ngClass]=\"{'p-checkbox-label': true, 'p-checkbox-label-active':checked(), 'p-disabled':disabled, 'p-checkbox-label-focus':focused}\"\n                *ngIf=\"label\" [attr.for]=\"inputId\">{{label}}</label>\n    `, isInline: true, styles: [\".p-checkbox{display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;vertical-align:bottom;position:relative}.p-checkbox-disabled{cursor:default!important;pointer-events:none}.p-checkbox-box{display:flex;justify-content:center;align-items:center}p-checkbox{display:inline-flex;vertical-align:bottom;align-items:center}.p-checkbox-label{line-height:1}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Checkbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-checkbox', template: `\n        <div [ngStyle]=\"style\" [ngClass]=\"{'p-checkbox p-component': true, 'p-checkbox-checked': checked(), 'p-checkbox-disabled': disabled, 'p-checkbox-focused': focused}\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [attr.id]=\"inputId\" [attr.name]=\"name\" [readonly]=\"readonly\" [value]=\"value\" [checked]=\"checked()\" (focus)=\"onFocus()\" (blur)=\"onBlur()\"\n                (change)=\"handleChange($event)\" [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-label]=\"ariaLabel\" [attr.aria-checked]=\"checked()\" [attr.required]=\"required\">\n            </div>\n            <div class=\"p-checkbox-box\" (click)=\"onClick($event,cb,true)\"\n                        [ngClass]=\"{'p-highlight': checked(), 'p-disabled': disabled, 'p-focus': focused}\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"checked() ? checkboxIcon : null\"></span>\n            </div>\n        </div>\n        <label (click)=\"onClick($event,cb,true)\" [class]=\"labelStyleClass\"\n                [ngClass]=\"{'p-checkbox-label': true, 'p-checkbox-label-active':checked(), 'p-disabled':disabled, 'p-checkbox-label-focus':focused}\"\n                *ngIf=\"label\" [attr.for]=\"inputId\">{{label}}</label>\n    `, providers: [CHECKBOX_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-checkbox{display:inline-flex;cursor:pointer;-webkit-user-select:none;user-select:none;vertical-align:bottom;position:relative}.p-checkbox-disabled{cursor:default!important;pointer-events:none}.p-checkbox-box{display:flex;justify-content:center;align-items:center}p-checkbox{display:inline-flex;vertical-align:bottom;align-items:center}.p-checkbox-label{line-height:1}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; }, propDecorators: { value: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], binary: [{\n                type: Input\n            }], label: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], labelStyleClass: [{\n                type: Input\n            }], formControl: [{\n                type: Input\n            }], checkboxIcon: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], trueValue: [{\n                type: Input\n            }], falseValue: [{\n                type: Input\n            }], inputViewChild: [{\n                type: ViewChild,\n                args: ['cb']\n            }], onChange: [{\n                type: Output\n            }] } });\nclass CheckboxModule {\n}\nCheckboxModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCheckboxModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: CheckboxModule, declarations: [Checkbox], imports: [CommonModule], exports: [Checkbox] });\nCheckboxModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CheckboxModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Checkbox],\n                    declarations: [Checkbox]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CHECKBOX_VALUE_ACCESSOR, Checkbox, CheckboxModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,KAA1F,EAAiGC,SAAjG,EAA4GC,MAA5G,EAAoHC,QAApH,QAAoI,eAApI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,SAASC,WAAT,QAA4B,eAA5B;;;;;;;;;;;;;;gBAiF2Fb,E;;IAAAA,EAYnF,8B;IAZmFA,EAY5E;MAZ4EA,EAY5E;MAAA,eAZ4EA,EAY5E;;MAAA,YAZ4EA,EAY5E;;MAAA,OAZ4EA,EAYnE,yCAAkB,IAAlB,EAAT;IAAA,E;IAZ4EA,EAcxC,U;IAdwCA,EAc/B,e;;;;mBAd+BA,E;IAAAA,EAY1C,mC;IAZ0CA,EAa3E,uBAb2EA,EAa3E,4E;IAb2EA,EAc7D,mC;IAd6DA,EAcxC,a;IAdwCA,EAcxC,gC;;;;;;;;;;;;;;;;;;;;;AA7FnD,MAAMc,uBAAuB,GAAG;EAC5BC,OAAO,EAAEH,iBADmB;EAE5BI,WAAW,EAAEf,UAAU,CAAC,MAAMgB,QAAP,CAFK;EAG5BC,KAAK,EAAE;AAHqB,CAAhC;;AAKA,MAAMD,QAAN,CAAe;EACXE,WAAW,CAACC,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;IACA,KAAKC,YAAL,GAAoB,aAApB;IACA,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,UAAL,GAAkB,KAAlB;IACA,KAAKC,QAAL,GAAgB,IAAItB,YAAJ,EAAhB;;IACA,KAAKuB,aAAL,GAAqB,MAAM,CAAG,CAA9B;;IACA,KAAKC,cAAL,GAAsB,MAAM,CAAG,CAA/B;;IACA,KAAKC,OAAL,GAAe,KAAf;EACH;;EACDC,OAAO,CAACC,KAAD,EAAQC,QAAR,EAAkBC,KAAlB,EAAyB;IAC5BF,KAAK,CAACG,cAAN;;IACA,IAAI,KAAKC,QAAL,IAAiB,KAAKC,QAA1B,EAAoC;MAChC;IACH;;IACD,KAAKC,WAAL,CAAiBN,KAAjB;;IACA,IAAIE,KAAJ,EAAW;MACPD,QAAQ,CAACC,KAAT;IACH;EACJ;;EACDI,WAAW,CAACN,KAAD,EAAQ;IACf,IAAIO,aAAJ;;IACA,IAAI,CAAC,KAAKC,MAAV,EAAkB;MACd,IAAI,KAAKC,OAAL,EAAJ,EACIF,aAAa,GAAG,KAAKG,KAAL,CAAWC,MAAX,CAAkBC,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,MAAZ,CAAmBD,GAAnB,EAAwB,KAAKE,KAA7B,CAA1B,CAAhB,CADJ,KAGIP,aAAa,GAAG,KAAKG,KAAL,GAAa,CAAC,GAAG,KAAKA,KAAT,EAAgB,KAAKI,KAArB,CAAb,GAA2C,CAAC,KAAKA,KAAN,CAA3D;MACJ,KAAKlB,aAAL,CAAmBW,aAAnB;MACA,KAAKG,KAAL,GAAaH,aAAb;;MACA,IAAI,KAAKQ,WAAT,EAAsB;QAClB,KAAKA,WAAL,CAAiBC,QAAjB,CAA0BT,aAA1B;MACH;IACJ,CAVD,MAWK;MACDA,aAAa,GAAG,KAAKE,OAAL,KAAiB,KAAKf,UAAtB,GAAmC,KAAKD,SAAxD;MACA,KAAKiB,KAAL,GAAaH,aAAb;MACA,KAAKX,aAAL,CAAmBW,aAAnB;IACH;;IACD,KAAKZ,QAAL,CAAcsB,IAAd,CAAmB;MAAER,OAAO,EAAEF,aAAX;MAA0BW,aAAa,EAAElB;IAAzC,CAAnB;EACH;;EACDmB,YAAY,CAACnB,KAAD,EAAQ;IAChB,IAAI,CAAC,KAAKK,QAAV,EAAoB;MAChB,KAAKC,WAAL,CAAiBN,KAAjB;IACH;EACJ;;EACDoB,OAAO,GAAG;IACN,KAAKtB,OAAL,GAAe,IAAf;EACH;;EACDuB,MAAM,GAAG;IACL,KAAKvB,OAAL,GAAe,KAAf;IACA,KAAKD,cAAL;EACH;;EACDK,KAAK,GAAG;IACJ,KAAKoB,cAAL,CAAoBC,aAApB,CAAkCrB,KAAlC;EACH;;EACDsB,UAAU,CAACd,KAAD,EAAQ;IACd,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKnB,EAAL,CAAQkC,YAAR;EACH;;EACDC,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAK/B,aAAL,GAAqB+B,EAArB;EACH;;EACDC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAK9B,cAAL,GAAsB8B,EAAtB;EACH;;EACDE,gBAAgB,CAACjB,GAAD,EAAM;IAClB,KAAKR,QAAL,GAAgBQ,GAAhB;IACA,KAAKrB,EAAL,CAAQkC,YAAR;EACH;;EACDhB,OAAO,GAAG;IACN,OAAO,KAAKD,MAAL,GAAc,KAAKE,KAAL,KAAe,KAAKjB,SAAlC,GAA8CT,WAAW,CAAC8C,QAAZ,CAAqB,KAAKhB,KAA1B,EAAiC,KAAKJ,KAAtC,CAArD;EACH;;AAxEU;;AA0EftB,QAAQ,CAAC2C,IAAT;EAAA,iBAAqG3C,QAArG,EAA2FjB,EAA3F,mBAA+HA,EAAE,CAAC6D,iBAAlI;AAAA;;AACA5C,QAAQ,CAAC6C,IAAT,kBAD2F9D,EAC3F;EAAA,MAAyFiB,QAAzF;EAAA;EAAA;IAAA;MAD2FjB,EAC3F;IAAA;;IAAA;MAAA;;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAD2FA,EAC3F,oBAA4nB,CAACc,uBAAD,CAA5nB;EAAA;EAAA;EAAA;EAAA;IAAA;MAAA,YAD2Fd,EAC3F;;MAD2FA,EAEnF,2DADR;MAD2FA,EAImD;QAAA,OAAS,aAAT;MAAA;QAAA,OAA4B,YAA5B;MAAA;QAAA,OACpH,wBADoH;MAAA,EAH9I;MAD2FA,EAI3E,iBAHhB;MAD2FA,EAO/E,4BANZ;MAD2FA,EAOnD;QAPmDA,EAOnD;;QAAA,YAPmDA,EAOnD;;QAAA,OAPmDA,EAO1C,sCAAkB,IAAlB,EAAT;MAAA,EANxC;MAD2FA,EAS3E,wBARhB;MAD2FA,EAU/E,iBATZ;MAD2FA,EAYnF,2DAXR;IAAA;;IAAA;MAD2FA,EAEkF,2BAD7K;MAD2FA,EAE9E,6CAF8EA,EAE9E,oEADb;MAD2FA,EAIT,aAHlF;MAD2FA,EAIT,6GAHlF;MAD2FA,EAIhD,wMAH3C;MAD2FA,EAQnE,aAPxB;MAD2FA,EAQnE,uBARmEA,EAQnE,oEAPxB;MAD2FA,EAS7C,aAR9C;MAD2FA,EAS7C,+DAR9C;MAD2FA,EAc1E,aAbjB;MAD2FA,EAc1E,8BAbjB;IAAA;EAAA;EAAA,eAcmcU,EAAE,CAACqD,OAdtc,EAciiBrD,EAAE,CAACsD,IAdpiB,EAcqoBtD,EAAE,CAACuD,OAdxoB;EAAA;EAAA;EAAA;AAAA;;AAeA;EAAA,mDAhB2FjE,EAgB3F,mBAA2FiB,QAA3F,EAAiH,CAAC;IACtGiD,IAAI,EAAE/D,SADgG;IAEtGgE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BC,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAdmB;MAcZC,SAAS,EAAE,CAACxD,uBAAD,CAdC;MAc0ByD,eAAe,EAAEnE,uBAAuB,CAACoE,MAdnE;MAc2EC,aAAa,EAAEpE,iBAAiB,CAACqE,IAd5G;MAckHC,IAAI,EAAE;QACnH,SAAS;MAD0G,CAdxH;MAgBIC,MAAM,EAAE,CAAC,sXAAD;IAhBZ,CAAD;EAFgG,CAAD,CAAjH,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAElE,EAAE,CAAC6D;IAAX,CAAD,CAAP;EAA0C,CAnBpF,EAmBsG;IAAElB,KAAK,EAAE,CAAC;MAChGuB,IAAI,EAAE5D;IAD0F,CAAD,CAAT;IAEtFuE,IAAI,EAAE,CAAC;MACPX,IAAI,EAAE5D;IADC,CAAD,CAFgF;IAItF2B,QAAQ,EAAE,CAAC;MACXiC,IAAI,EAAE5D;IADK,CAAD,CAJ4E;IAMtF+B,MAAM,EAAE,CAAC;MACT6B,IAAI,EAAE5D;IADG,CAAD,CAN8E;IAQtFwE,KAAK,EAAE,CAAC;MACRZ,IAAI,EAAE5D;IADE,CAAD,CAR+E;IAUtFyE,cAAc,EAAE,CAAC;MACjBb,IAAI,EAAE5D;IADW,CAAD,CAVsE;IAYtF0E,SAAS,EAAE,CAAC;MACZd,IAAI,EAAE5D;IADM,CAAD,CAZ2E;IActF2E,QAAQ,EAAE,CAAC;MACXf,IAAI,EAAE5D;IADK,CAAD,CAd4E;IAgBtF4E,OAAO,EAAE,CAAC;MACVhB,IAAI,EAAE5D;IADI,CAAD,CAhB6E;IAkBtF6E,KAAK,EAAE,CAAC;MACRjB,IAAI,EAAE5D;IADE,CAAD,CAlB+E;IAoBtF8E,UAAU,EAAE,CAAC;MACblB,IAAI,EAAE5D;IADO,CAAD,CApB0E;IAsBtF+E,eAAe,EAAE,CAAC;MAClBnB,IAAI,EAAE5D;IADY,CAAD,CAtBqE;IAwBtFsC,WAAW,EAAE,CAAC;MACdsB,IAAI,EAAE5D;IADQ,CAAD,CAxByE;IA0BtFe,YAAY,EAAE,CAAC;MACf6C,IAAI,EAAE5D;IADS,CAAD,CA1BwE;IA4BtF4B,QAAQ,EAAE,CAAC;MACXgC,IAAI,EAAE5D;IADK,CAAD,CA5B4E;IA8BtFgF,QAAQ,EAAE,CAAC;MACXpB,IAAI,EAAE5D;IADK,CAAD,CA9B4E;IAgCtFgB,SAAS,EAAE,CAAC;MACZ4C,IAAI,EAAE5D;IADM,CAAD,CAhC2E;IAkCtFiB,UAAU,EAAE,CAAC;MACb2C,IAAI,EAAE5D;IADO,CAAD,CAlC0E;IAoCtF6C,cAAc,EAAE,CAAC;MACjBe,IAAI,EAAE3D,SADW;MAEjB4D,IAAI,EAAE,CAAC,IAAD;IAFW,CAAD,CApCsE;IAuCtF3C,QAAQ,EAAE,CAAC;MACX0C,IAAI,EAAE1D;IADK,CAAD;EAvC4E,CAnBtG;AAAA;;AA6DA,MAAM+E,cAAN,CAAqB;;AAErBA,cAAc,CAAC3B,IAAf;EAAA,iBAA2G2B,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAhF2FxF,EAgF3F;EAAA,MAA4GuF;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAjF2FzF,EAiF3F;EAAA,UAAsIW,YAAtI;AAAA;;AACA;EAAA,mDAlF2FX,EAkF3F,mBAA2FuF,cAA3F,EAAuH,CAAC;IAC5GrB,IAAI,EAAEzD,QADsG;IAE5G0D,IAAI,EAAE,CAAC;MACCuB,OAAO,EAAE,CAAC/E,YAAD,CADV;MAECgF,OAAO,EAAE,CAAC1E,QAAD,CAFV;MAGC2E,YAAY,EAAE,CAAC3E,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASH,uBAAT,EAAkCG,QAAlC,EAA4CsE,cAA5C"}, "metadata": {}, "sourceType": "module"}