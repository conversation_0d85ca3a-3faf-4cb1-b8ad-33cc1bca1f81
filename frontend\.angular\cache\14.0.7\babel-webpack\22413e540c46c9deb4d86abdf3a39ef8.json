{"ast": null, "code": "import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n  if (index < 0) {\n    throw new ArgumentOutOfRangeError();\n  }\n\n  const hasDefaultValue = arguments.length >= 2;\n  return source => source.pipe(filter((v, i) => i === index), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new ArgumentOutOfRangeError()));\n}", "map": {"version": 3, "names": ["ArgumentOutOfRangeError", "filter", "throwIfEmpty", "defaultIfEmpty", "take", "elementAt", "index", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/elementAt.js"], "sourcesContent": ["import { ArgumentOutOfRangeError } from '../util/ArgumentOutOfRangeError';\nimport { filter } from './filter';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { take } from './take';\nexport function elementAt(index, defaultValue) {\n    if (index < 0) {\n        throw new ArgumentOutOfRangeError();\n    }\n    const hasDefaultValue = arguments.length >= 2;\n    return (source) => source.pipe(filter((v, i) => i === index), take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(() => new ArgumentOutOfRangeError()));\n}\n"], "mappings": "AAAA,SAASA,uBAAT,QAAwC,iCAAxC;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,OAAO,SAASC,SAAT,CAAmBC,KAAnB,EAA0BC,YAA1B,EAAwC;EAC3C,IAAID,KAAK,GAAG,CAAZ,EAAe;IACX,MAAM,IAAIN,uBAAJ,EAAN;EACH;;EACD,MAAMQ,eAAe,GAAGC,SAAS,CAACC,MAAV,IAAoB,CAA5C;EACA,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYX,MAAM,CAAC,CAACY,CAAD,EAAIC,CAAJ,KAAUA,CAAC,KAAKR,KAAjB,CAAlB,EAA2CF,IAAI,CAAC,CAAD,CAA/C,EAAoDI,eAAe,GAAGL,cAAc,CAACI,YAAD,CAAjB,GAAkCL,YAAY,CAAC,MAAM,IAAIF,uBAAJ,EAAP,CAAjH,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}