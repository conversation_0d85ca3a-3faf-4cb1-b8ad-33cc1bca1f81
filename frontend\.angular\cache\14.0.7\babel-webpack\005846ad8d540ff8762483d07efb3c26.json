{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (!predicate.call(thisArg, value, index++, source)) {\n        subscriber.next(false);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(true);\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "every", "predicate", "thisArg", "source", "subscriber", "index", "subscribe", "value", "call", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/every.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function every(predicate, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            if (!predicate.call(thisArg, value, index++, source)) {\n                subscriber.next(false);\n                subscriber.complete();\n            }\n        }, () => {\n            subscriber.next(true);\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,KAAT,CAAeC,SAAf,EAA0BC,OAA1B,EAAmC;EACtC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAG,CAAZ;IACAF,MAAM,CAACG,SAAP,CAAiBP,wBAAwB,CAACK,UAAD,EAAcG,KAAD,IAAW;MAC7D,IAAI,CAACN,SAAS,CAACO,IAAV,CAAeN,OAAf,EAAwBK,KAAxB,EAA+BF,KAAK,EAApC,EAAwCF,MAAxC,CAAL,EAAsD;QAClDC,UAAU,CAACK,IAAX,CAAgB,KAAhB;QACAL,UAAU,CAACM,QAAX;MACH;IACJ,CALwC,EAKtC,MAAM;MACLN,UAAU,CAACK,IAAX,CAAgB,IAAhB;MACAL,UAAU,CAACM,QAAX;IACH,CARwC,CAAzC;EASH,CAXa,CAAd;AAYH"}, "metadata": {}, "sourceType": "module"}