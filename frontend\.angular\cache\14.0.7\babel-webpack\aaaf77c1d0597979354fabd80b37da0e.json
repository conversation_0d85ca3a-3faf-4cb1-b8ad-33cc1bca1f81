{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ObjectUnsubscribedError = createErrorClass(_super => function ObjectUnsubscribedErrorImpl() {\n  _super(this);\n\n  this.name = 'ObjectUnsubscribedError';\n  this.message = 'object unsubscribed';\n});", "map": {"version": 3, "names": ["createErrorClass", "ObjectUnsubscribedError", "_super", "ObjectUnsubscribedErrorImpl", "name", "message"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/ObjectUnsubscribedError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const ObjectUnsubscribedError = createErrorClass((_super) => function ObjectUnsubscribedErrorImpl() {\n    _super(this);\n    this.name = 'ObjectUnsubscribedError';\n    this.message = 'object unsubscribed';\n});\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAO,MAAMC,uBAAuB,GAAGD,gBAAgB,CAAEE,MAAD,IAAY,SAASC,2BAAT,GAAuC;EACvGD,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKE,IAAL,GAAY,yBAAZ;EACA,KAAKC,OAAL,GAAe,qBAAf;AACH,CAJsD,CAAhD"}, "metadata": {}, "sourceType": "module"}