{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Input, Output, ViewChild, ContentChildren, Directive, HostListener, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { DomHandler } from 'primeng/dom';\nimport * as i3 from 'primeng/paginator';\nimport { PaginatorModule } from 'primeng/paginator';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\nimport * as i5 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nconst _c0 = [\"container\"];\nconst _c1 = [\"resizeHelper\"];\nconst _c2 = [\"reorderIndicatorUp\"];\nconst _c3 = [\"reorderIndicatorDown\"];\nconst _c4 = [\"table\"];\nconst _c5 = [\"scrollableView\"];\nconst _c6 = [\"scrollableFrozenView\"];\n\nfunction TreeTable_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"p-treetable-loading-icon pi-spin \" + ctx_r1.loadingIcon);\n  }\n}\n\nfunction TreeTable_div_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTable_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, TreeTable_div_3_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.captionTemplate);\n  }\n}\n\nfunction TreeTable_p_paginator_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-paginator\", 16);\n    i0.ɵɵlistener(\"onPageChange\", function TreeTable_p_paginator_4_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r3.rows)(\"first\", ctx_r3.first)(\"totalRecords\", ctx_r3.totalRecords)(\"pageLinkSize\", ctx_r3.pageLinks)(\"alwaysShow\", ctx_r3.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r3.rowsPerPageOptions)(\"templateLeft\", ctx_r3.paginatorLeftTemplate)(\"templateRight\", ctx_r3.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r3.paginatorDropdownAppendTo)(\"currentPageReportTemplate\", ctx_r3.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r3.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r3.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r3.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r3.showJumpToPageDropdown)(\"showPageLinks\", ctx_r3.showPageLinks);\n  }\n}\n\nfunction TreeTable_div_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTable_div_5_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTable_div_5_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c7 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\n\nfunction TreeTable_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"table\", 18, 19);\n    i0.ɵɵtemplate(3, TreeTable_div_5_ng_container_3_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementStart(4, \"thead\", 21);\n    i0.ɵɵtemplate(5, TreeTable_div_5_ng_container_5_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"tbody\", 22);\n    i0.ɵɵelementStart(7, \"tfoot\", 23);\n    i0.ɵɵtemplate(8, TreeTable_div_5_ng_container_8_Template, 1, 0, \"ng-container\", 20);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.tableStyleClass)(\"ngStyle\", ctx_r4.tableStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c7, ctx_r4.columns));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c7, ctx_r4.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTreeTableBody\", ctx_r4.columns)(\"pTreeTableBodyTemplate\", ctx_r4.bodyTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c7, ctx_r4.columns));\n  }\n}\n\nconst _c8 = function (a0) {\n  return {\n    width: a0\n  };\n};\n\nfunction TreeTable_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 28, 29);\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ttScrollableView\", ctx_r18.frozenColumns)(\"frozen\", true)(\"ngStyle\", i0.ɵɵpureFunction1(4, _c8, ctx_r18.frozenWidth))(\"scrollHeight\", ctx_r18.scrollHeight);\n  }\n}\n\nconst _c9 = function (a0, a1) {\n  return {\n    left: a0,\n    width: a1\n  };\n};\n\nfunction TreeTable_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, TreeTable_div_6_div_1_Template, 2, 6, \"div\", 25);\n    i0.ɵɵelement(2, \"div\", 26, 27);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.frozenColumns || ctx_r5.frozenBodyTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ttScrollableView\", ctx_r5.columns)(\"frozen\", false)(\"scrollHeight\", ctx_r5.scrollHeight)(\"ngStyle\", i0.ɵɵpureFunction2(5, _c9, ctx_r5.frozenWidth, \"calc(100% - \" + ctx_r5.frozenWidth + \")\"));\n  }\n}\n\nfunction TreeTable_p_paginator_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-paginator\", 30);\n    i0.ɵɵlistener(\"onPageChange\", function TreeTable_p_paginator_7_Template_p_paginator_onPageChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onPageChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rows\", ctx_r6.rows)(\"first\", ctx_r6.first)(\"totalRecords\", ctx_r6.totalRecords)(\"pageLinkSize\", ctx_r6.pageLinks)(\"alwaysShow\", ctx_r6.alwaysShowPaginator)(\"rowsPerPageOptions\", ctx_r6.rowsPerPageOptions)(\"templateLeft\", ctx_r6.paginatorLeftTemplate)(\"templateRight\", ctx_r6.paginatorRightTemplate)(\"dropdownAppendTo\", ctx_r6.paginatorDropdownAppendTo)(\"currentPageReportTemplate\", ctx_r6.currentPageReportTemplate)(\"showFirstLastIcon\", ctx_r6.showFirstLastIcon)(\"dropdownItemTemplate\", ctx_r6.paginatorDropdownItemTemplate)(\"showCurrentPageReport\", ctx_r6.showCurrentPageReport)(\"showJumpToPageDropdown\", ctx_r6.showJumpToPageDropdown)(\"showPageLinks\", ctx_r6.showPageLinks);\n  }\n}\n\nfunction TreeTable_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTable_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, TreeTable_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.summaryTemplate);\n  }\n}\n\nfunction TreeTable_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 32, 33);\n  }\n}\n\nfunction TreeTable_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 34, 35);\n  }\n}\n\nfunction TreeTable_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36, 37);\n  }\n}\n\nconst _c10 = function (a1, a2, a3, a4, a5) {\n  return {\n    \"p-treetable p-component\": true,\n    \"p-treetable-hoverable-rows\": a1,\n    \"p-treetable-auto-layout\": a2,\n    \"p-treetable-resizable\": a3,\n    \"p-treetable-resizable-fit\": a4,\n    \"p-treetable-flex-scrollable\": a5\n  };\n};\n\nconst _c11 = [\"pTreeTableBody\", \"\"];\n\nfunction TTBody_ng_template_0_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c12 = function (a0, a1, a2, a3) {\n  return {\n    $implicit: a0,\n    node: a1,\n    rowData: a2,\n    columns: a3\n  };\n};\n\nfunction TTBody_ng_template_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTBody_ng_template_0_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const serializedNode_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction4(2, _c12, serializedNode_r2, serializedNode_r2.node, serializedNode_r2.node.data, ctx_r4.columns));\n  }\n}\n\nfunction TTBody_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTBody_ng_template_0_ng_container_0_Template, 2, 7, \"ng-container\", 1);\n  }\n\n  if (rf & 2) {\n    const serializedNode_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", serializedNode_r2.visible);\n  }\n}\n\nfunction TTBody_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c13 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    frozen: a1\n  };\n};\n\nfunction TTBody_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTBody_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.tt.emptyMessageTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c13, ctx_r1.columns, ctx_r1.frozen));\n  }\n}\n\nconst _c14 = [\"scrollHeader\"];\nconst _c15 = [\"scrollHeaderBox\"];\nconst _c16 = [\"scrollBody\"];\nconst _c17 = [\"scrollTable\"];\nconst _c18 = [\"loadingTable\"];\nconst _c19 = [\"scrollFooter\"];\nconst _c20 = [\"scrollFooterBox\"];\nconst _c21 = [\"scrollableAligner\"];\nconst _c22 = [\"scroller\"];\nconst _c23 = [\"ttScrollableView\", \"\"];\n\nfunction TTScrollableView_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TTScrollableView_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TTScrollableView_p_scroller_8_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c24 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    options: a1\n  };\n};\n\nfunction TTScrollableView_p_scroller_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTScrollableView_p_scroller_8_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n\n  if (rf & 2) {\n    const items_r12 = ctx.$implicit;\n    const scrollerOptions_r13 = ctx.options;\n    i0.ɵɵnextContext(2);\n\n    const _r6 = i0.ɵɵreference(11);\n\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c24, items_r12, scrollerOptions_r13));\n  }\n}\n\nfunction TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c25 = function (a0) {\n  return {\n    options: a0\n  };\n};\n\nfunction TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 5);\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r16 = ctx.options;\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r15.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c25, scrollerOptions_r16));\n  }\n}\n\nfunction TTScrollableView_p_scroller_8_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TTScrollableView_p_scroller_8_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nconst _c26 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nfunction TTScrollableView_p_scroller_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-scroller\", 11, 12);\n    i0.ɵɵlistener(\"onLazyLoad\", function TTScrollableView_p_scroller_8_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.tt.onLazyItemLoad($event));\n    });\n    i0.ɵɵtemplate(2, TTScrollableView_p_scroller_8_ng_template_2_Template, 1, 5, \"ng-template\", 13);\n    i0.ɵɵtemplate(3, TTScrollableView_p_scroller_8_ng_container_3_Template, 2, 0, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c26, ctx_r4.tt.scrollHeight !== \"flex\" ? ctx_r4.tt.scrollHeight : undefined));\n    i0.ɵɵproperty(\"items\", ctx_r4.tt.serializedValue)(\"scrollHeight\", ctx_r4.scrollHeight !== \"flex\" ? undefined : \"100%\")(\"itemSize\", ctx_r4.tt.virtualScrollItemSize || ctx_r4.tt._virtualRowHeight)(\"lazy\", ctx_r4.tt.lazy)(\"options\", ctx_r4.tt.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loaderTemplate);\n  }\n}\n\nfunction TTScrollableView_ng_container_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c27 = function (a0, a1) {\n  return {\n    \"max-height\": a0,\n    \"overflow-y\": a1\n  };\n};\n\nconst _c28 = function () {\n  return {};\n};\n\nfunction TTScrollableView_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15, 16);\n    i0.ɵɵtemplate(3, TTScrollableView_ng_container_9_ng_container_3_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n\n    const _r6 = i0.ɵɵreference(11);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(3, _c27, ctx_r5.tt.scrollHeight !== \"flex\" ? ctx_r5.scrollHeight : undefined, !ctx_r5.frozen && ctx_r5.tt.scrollHeight ? \"scroll\" : undefined));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(7, _c24, ctx_r5.serializedValue, i0.ɵɵpureFunction0(6, _c28)));\n  }\n}\n\nfunction TTScrollableView_ng_template_10_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TTScrollableView_ng_template_10_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21, 22);\n  }\n}\n\nfunction TTScrollableView_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\", 17, 18);\n    i0.ɵɵtemplate(2, TTScrollableView_ng_template_10_ng_container_2_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelement(3, \"tbody\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TTScrollableView_ng_template_10_div_4_Template, 2, 0, \"div\", 20);\n  }\n\n  if (rf & 2) {\n    const items_r22 = ctx.$implicit;\n    const scrollerOptions_r23 = ctx.options;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(scrollerOptions_r23.contentStyle);\n    i0.ɵɵclassMap(ctx_r7.tt.tableStyleClass);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r23.contentStyleClass)(\"ngStyle\", ctx_r7.tt.tableStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.frozen ? ctx_r7.tt.frozenColGroupTemplate || ctx_r7.tt.colGroupTemplate : ctx_r7.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c7, ctx_r7.columns));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"pTreeTableBody\", ctx_r7.columns)(\"pTreeTableBodyTemplate\", ctx_r7.frozen ? ctx_r7.tt.frozenBodyTemplate || ctx_r7.tt.bodyTemplate : ctx_r7.tt.bodyTemplate)(\"serializedNodes\", items_r22)(\"frozen\", ctx_r7.frozen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.frozen);\n  }\n}\n\nfunction TTScrollableView_div_12_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TTScrollableView_div_12_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TTScrollableView_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23, 24)(2, \"div\", 25, 26)(4, \"table\", 27);\n    i0.ɵɵtemplate(5, TTScrollableView_div_12_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementStart(6, \"tfoot\", 28);\n    i0.ɵɵtemplate(7, TTScrollableView_div_12_ng_container_7_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r8.tt.tableStyleClass)(\"ngStyle\", ctx_r8.tt.tableStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r8.frozen ? ctx_r8.tt.frozenColGroupTemplate || ctx_r8.tt.colGroupTemplate : ctx_r8.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c7, ctx_r8.columns));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r8.frozen ? ctx_r8.tt.frozenFooterTemplate || ctx_r8.tt.footerTemplate : ctx_r8.tt.footerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c7, ctx_r8.columns));\n  }\n}\n\nconst _c29 = function (a0, a1, a2) {\n  return {\n    \"pi-sort-amount-up-alt\": a0,\n    \"pi-sort-amount-down\": a1,\n    \"pi-sort-alt\": a2\n  };\n};\n\nconst _c30 = function (a0) {\n  return {\n    \"p-checkbox-focused\": a0\n  };\n};\n\nconst _c31 = function (a1, a2, a3, a4) {\n  return {\n    \"p-checkbox-box\": true,\n    \"p-highlight\": a1,\n    \"p-focus\": a2,\n    \"p-indeterminate\": a3,\n    \"p-disabled\": a4\n  };\n};\n\nconst _c32 = function (a0, a1) {\n  return {\n    \"pi-check\": a0,\n    \"pi-minus\": a1\n  };\n};\n\nconst _c33 = [\"box\"];\n\nconst _c34 = function (a1, a2, a3) {\n  return {\n    \"p-checkbox-box\": true,\n    \"p-highlight\": a1,\n    \"p-focus\": a2,\n    \"p-disabled\": a3\n  };\n};\n\nconst _c35 = function (a0) {\n  return {\n    \"pi pi-check\": a0\n  };\n};\n\nfunction TreeTableCellEditor_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTableCellEditor_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTableCellEditor_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.inputTemplate);\n  }\n}\n\nfunction TreeTableCellEditor_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction TreeTableCellEditor_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TreeTableCellEditor_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.outputTemplate);\n  }\n}\n\nclass TreeTableService {\n  constructor() {\n    this.sortSource = new Subject();\n    this.selectionSource = new Subject();\n    this.contextMenuSource = new Subject();\n    this.uiUpdateSource = new Subject();\n    this.totalRecordsSource = new Subject();\n    this.sortSource$ = this.sortSource.asObservable();\n    this.selectionSource$ = this.selectionSource.asObservable();\n    this.contextMenuSource$ = this.contextMenuSource.asObservable();\n    this.uiUpdateSource$ = this.uiUpdateSource.asObservable();\n    this.totalRecordsSource$ = this.totalRecordsSource.asObservable();\n  }\n\n  onSort(sortMeta) {\n    this.sortSource.next(sortMeta);\n  }\n\n  onSelectionChange() {\n    this.selectionSource.next(null);\n  }\n\n  onContextMenu(node) {\n    this.contextMenuSource.next(node);\n  }\n\n  onUIUpdate(value) {\n    this.uiUpdateSource.next(value);\n  }\n\n  onTotalRecordsChange(value) {\n    this.totalRecordsSource.next(value);\n  }\n\n}\n\nTreeTableService.ɵfac = function TreeTableService_Factory(t) {\n  return new (t || TreeTableService)();\n};\n\nTreeTableService.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TreeTableService,\n  factory: TreeTableService.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeTableService, [{\n    type: Injectable\n  }], null, null);\n})();\n\nclass TreeTable {\n  constructor(el, cd, zone, tableService, filterService) {\n    this.el = el;\n    this.cd = cd;\n    this.zone = zone;\n    this.tableService = tableService;\n    this.filterService = filterService;\n    this.lazy = false;\n    this.lazyLoadOnInit = true;\n    this.first = 0;\n    this.pageLinks = 5;\n    this.alwaysShowPaginator = true;\n    this.paginatorPosition = 'bottom';\n    this.currentPageReportTemplate = '{currentPage} of {totalPages}';\n    this.showFirstLastIcon = true;\n    this.showPageLinks = true;\n    this.defaultSortOrder = 1;\n    this.sortMode = 'single';\n    this.resetPageOnSort = true;\n    this.selectionChange = new EventEmitter();\n    this.contextMenuSelectionChange = new EventEmitter();\n    this.contextMenuSelectionMode = \"separate\";\n    this.compareSelectionBy = 'deepEquals';\n    this.loadingIcon = 'pi pi-spinner';\n    this.showLoader = true;\n    this.virtualScrollDelay = 150;\n    this.columnResizeMode = 'fit';\n\n    this.rowTrackBy = (index, item) => item;\n\n    this.filters = {};\n    this.filterDelay = 300;\n    this.filterMode = 'lenient';\n    this.onFilter = new EventEmitter();\n    this.onNodeExpand = new EventEmitter();\n    this.onNodeCollapse = new EventEmitter();\n    this.onPage = new EventEmitter();\n    this.onSort = new EventEmitter();\n    this.onLazyLoad = new EventEmitter();\n    this.sortFunction = new EventEmitter();\n    this.onColResize = new EventEmitter();\n    this.onColReorder = new EventEmitter();\n    this.onNodeSelect = new EventEmitter();\n    this.onNodeUnselect = new EventEmitter();\n    this.onContextMenuSelect = new EventEmitter();\n    this.onHeaderCheckboxToggle = new EventEmitter();\n    this.onEditInit = new EventEmitter();\n    this.onEditComplete = new EventEmitter();\n    this.onEditCancel = new EventEmitter();\n    /* @deprecated */\n\n    this._virtualRowHeight = 28;\n    this._value = [];\n    this._totalRecords = 0;\n    this._sortOrder = 1;\n    this.selectionKeys = {};\n  }\n\n  get virtualRowHeight() {\n    return this._virtualRowHeight;\n  }\n\n  set virtualRowHeight(val) {\n    this._virtualRowHeight = val;\n    console.warn(\"The virtualRowHeight property is deprecated, use virtualScrollItemSize property instead.\");\n  }\n\n  ngOnInit() {\n    if (this.lazy && this.lazyLoadOnInit && !this.virtualScroll) {\n      this.onLazyLoad.emit(this.createLazyLoadMetadata());\n    }\n\n    this.initialized = true;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'caption':\n          this.captionTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'body':\n          this.bodyTemplate = item.template;\n          break;\n\n        case 'loadingbody':\n          this.loadingBodyTemplate = item.template;\n          break;\n\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n\n        case 'summary':\n          this.summaryTemplate = item.template;\n          break;\n\n        case 'colgroup':\n          this.colGroupTemplate = item.template;\n          break;\n\n        case 'emptymessage':\n          this.emptyMessageTemplate = item.template;\n          break;\n\n        case 'paginatorleft':\n          this.paginatorLeftTemplate = item.template;\n          break;\n\n        case 'paginatorright':\n          this.paginatorRightTemplate = item.template;\n          break;\n\n        case 'paginatordropdownitem':\n          this.paginatorDropdownItemTemplate = item.template;\n          break;\n\n        case 'frozenheader':\n          this.frozenHeaderTemplate = item.template;\n          break;\n\n        case 'frozenbody':\n          this.frozenBodyTemplate = item.template;\n          break;\n\n        case 'frozenfooter':\n          this.frozenFooterTemplate = item.template;\n          break;\n\n        case 'frozencolgroup':\n          this.frozenColGroupTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  ngOnChanges(simpleChange) {\n    if (simpleChange.value) {\n      this._value = simpleChange.value.currentValue;\n\n      if (!this.lazy) {\n        this.totalRecords = this._value ? this._value.length : 0;\n        if (this.sortMode == 'single' && this.sortField) this.sortSingle();else if (this.sortMode == 'multiple' && this.multiSortMeta) this.sortMultiple();else if (this.hasFilter()) //sort already filters\n          this._filter();\n      }\n\n      this.updateSerializedValue();\n      this.tableService.onUIUpdate(this.value);\n    }\n\n    if (simpleChange.sortField) {\n      this._sortField = simpleChange.sortField.currentValue; //avoid triggering lazy load prior to lazy initialization at onInit\n\n      if (!this.lazy || this.initialized) {\n        if (this.sortMode === 'single') {\n          this.sortSingle();\n        }\n      }\n    }\n\n    if (simpleChange.sortOrder) {\n      this._sortOrder = simpleChange.sortOrder.currentValue; //avoid triggering lazy load prior to lazy initialization at onInit\n\n      if (!this.lazy || this.initialized) {\n        if (this.sortMode === 'single') {\n          this.sortSingle();\n        }\n      }\n    }\n\n    if (simpleChange.multiSortMeta) {\n      this._multiSortMeta = simpleChange.multiSortMeta.currentValue;\n\n      if (this.sortMode === 'multiple') {\n        this.sortMultiple();\n      }\n    }\n\n    if (simpleChange.selection) {\n      this._selection = simpleChange.selection.currentValue;\n\n      if (!this.preventSelectionSetterPropagation) {\n        this.updateSelectionKeys();\n        this.tableService.onSelectionChange();\n      }\n\n      this.preventSelectionSetterPropagation = false;\n    }\n  }\n\n  get value() {\n    return this._value;\n  }\n\n  set value(val) {\n    this._value = val;\n  }\n\n  updateSerializedValue() {\n    this.serializedValue = [];\n    if (this.paginator) this.serializePageNodes();else this.serializeNodes(null, this.filteredNodes || this.value, 0, true);\n  }\n\n  serializeNodes(parent, nodes, level, visible) {\n    if (nodes && nodes.length) {\n      for (let node of nodes) {\n        node.parent = parent;\n        const rowNode = {\n          node: node,\n          parent: parent,\n          level: level,\n          visible: visible && (parent ? parent.expanded : true)\n        };\n        this.serializedValue.push(rowNode);\n\n        if (rowNode.visible && node.expanded) {\n          this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n        }\n      }\n    }\n  }\n\n  serializePageNodes() {\n    let data = this.filteredNodes || this.value;\n    this.serializedValue = [];\n\n    if (data && data.length) {\n      const first = this.lazy ? 0 : this.first;\n\n      for (let i = first; i < first + this.rows; i++) {\n        let node = data[i];\n\n        if (node) {\n          this.serializedValue.push({\n            node: node,\n            parent: null,\n            level: 0,\n            visible: true\n          });\n          this.serializeNodes(node, node.children, 1, true);\n        }\n      }\n    }\n  }\n\n  get totalRecords() {\n    return this._totalRecords;\n  }\n\n  set totalRecords(val) {\n    this._totalRecords = val;\n    this.tableService.onTotalRecordsChange(this._totalRecords);\n  }\n\n  get sortField() {\n    return this._sortField;\n  }\n\n  set sortField(val) {\n    this._sortField = val;\n  }\n\n  get sortOrder() {\n    return this._sortOrder;\n  }\n\n  set sortOrder(val) {\n    this._sortOrder = val;\n  }\n\n  get multiSortMeta() {\n    return this._multiSortMeta;\n  }\n\n  set multiSortMeta(val) {\n    this._multiSortMeta = val;\n  }\n\n  get selection() {\n    return this._selection;\n  }\n\n  set selection(val) {\n    this._selection = val;\n  }\n\n  updateSelectionKeys() {\n    if (this.dataKey && this._selection) {\n      this.selectionKeys = {};\n\n      if (Array.isArray(this._selection)) {\n        for (let node of this._selection) {\n          this.selectionKeys[String(ObjectUtils.resolveFieldData(node.data, this.dataKey))] = 1;\n        }\n      } else {\n        this.selectionKeys[String(ObjectUtils.resolveFieldData(this._selection.data, this.dataKey))] = 1;\n      }\n    }\n  }\n\n  onPageChange(event) {\n    this.first = event.first;\n    this.rows = event.rows;\n    if (this.lazy) this.onLazyLoad.emit(this.createLazyLoadMetadata());else this.serializePageNodes();\n    this.onPage.emit({\n      first: this.first,\n      rows: this.rows\n    });\n    this.tableService.onUIUpdate(this.value);\n\n    if (this.scrollable) {\n      this.resetScrollTop();\n    }\n  }\n\n  sort(event) {\n    let originalEvent = event.originalEvent;\n\n    if (this.sortMode === 'single') {\n      this._sortOrder = this.sortField === event.field ? this.sortOrder * -1 : this.defaultSortOrder;\n      this._sortField = event.field;\n      this.sortSingle();\n\n      if (this.resetPageOnSort && this.scrollable) {\n        this.resetScrollTop();\n      }\n    }\n\n    if (this.sortMode === 'multiple') {\n      let metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n      let sortMeta = this.getSortMeta(event.field);\n\n      if (sortMeta) {\n        if (!metaKey) {\n          this._multiSortMeta = [{\n            field: event.field,\n            order: sortMeta.order * -1\n          }];\n\n          if (this.resetPageOnSort && this.scrollable) {\n            this.resetScrollTop();\n          }\n        } else {\n          sortMeta.order = sortMeta.order * -1;\n        }\n      } else {\n        if (!metaKey || !this.multiSortMeta) {\n          this._multiSortMeta = [];\n\n          if (this.resetPageOnSort && this.scrollable) {\n            this.resetScrollTop();\n          }\n        }\n\n        this.multiSortMeta.push({\n          field: event.field,\n          order: this.defaultSortOrder\n        });\n      }\n\n      this.sortMultiple();\n    }\n  }\n\n  sortSingle() {\n    if (this.sortField && this.sortOrder) {\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else if (this.value) {\n        this.sortNodes(this.value);\n\n        if (this.hasFilter()) {\n          this._filter();\n        }\n      }\n\n      let sortMeta = {\n        field: this.sortField,\n        order: this.sortOrder\n      };\n      this.onSort.emit(sortMeta);\n      this.tableService.onSort(sortMeta);\n      this.updateSerializedValue();\n    }\n  }\n\n  sortNodes(nodes) {\n    if (!nodes || nodes.length === 0) {\n      return;\n    }\n\n    if (this.customSort) {\n      this.sortFunction.emit({\n        data: nodes,\n        mode: this.sortMode,\n        field: this.sortField,\n        order: this.sortOrder\n      });\n    } else {\n      nodes.sort((node1, node2) => {\n        let value1 = ObjectUtils.resolveFieldData(node1.data, this.sortField);\n        let value2 = ObjectUtils.resolveFieldData(node2.data, this.sortField);\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return this.sortOrder * result;\n      });\n    }\n\n    for (let node of nodes) {\n      this.sortNodes(node.children);\n    }\n  }\n\n  sortMultiple() {\n    if (this.multiSortMeta) {\n      if (this.lazy) {\n        this.onLazyLoad.emit(this.createLazyLoadMetadata());\n      } else if (this.value) {\n        this.sortMultipleNodes(this.value);\n\n        if (this.hasFilter()) {\n          this._filter();\n        }\n      }\n\n      this.onSort.emit({\n        multisortmeta: this.multiSortMeta\n      });\n      this.updateSerializedValue();\n      this.tableService.onSort(this.multiSortMeta);\n    }\n  }\n\n  sortMultipleNodes(nodes) {\n    if (!nodes || nodes.length === 0) {\n      return;\n    }\n\n    if (this.customSort) {\n      this.sortFunction.emit({\n        data: this.value,\n        mode: this.sortMode,\n        multiSortMeta: this.multiSortMeta\n      });\n    } else {\n      nodes.sort((node1, node2) => {\n        return this.multisortField(node1, node2, this.multiSortMeta, 0);\n      });\n    }\n\n    for (let node of nodes) {\n      this.sortMultipleNodes(node.children);\n    }\n  }\n\n  multisortField(node1, node2, multiSortMeta, index) {\n    let value1 = ObjectUtils.resolveFieldData(node1.data, multiSortMeta[index].field);\n    let value2 = ObjectUtils.resolveFieldData(node2.data, multiSortMeta[index].field);\n    let result = null;\n    if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;\n\n    if (typeof value1 == 'string' || value1 instanceof String) {\n      if (value1.localeCompare && value1 != value2) {\n        return multiSortMeta[index].order * value1.localeCompare(value2, undefined, {\n          numeric: true\n        });\n      }\n    } else {\n      result = value1 < value2 ? -1 : 1;\n    }\n\n    if (value1 == value2) {\n      return multiSortMeta.length - 1 > index ? this.multisortField(node1, node2, multiSortMeta, index + 1) : 0;\n    }\n\n    return multiSortMeta[index].order * result;\n  }\n\n  getSortMeta(field) {\n    if (this.multiSortMeta && this.multiSortMeta.length) {\n      for (let i = 0; i < this.multiSortMeta.length; i++) {\n        if (this.multiSortMeta[i].field === field) {\n          return this.multiSortMeta[i];\n        }\n      }\n    }\n\n    return null;\n  }\n\n  isSorted(field) {\n    if (this.sortMode === 'single') {\n      return this.sortField && this.sortField === field;\n    } else if (this.sortMode === 'multiple') {\n      let sorted = false;\n\n      if (this.multiSortMeta) {\n        for (let i = 0; i < this.multiSortMeta.length; i++) {\n          if (this.multiSortMeta[i].field == field) {\n            sorted = true;\n            break;\n          }\n        }\n      }\n\n      return sorted;\n    }\n  }\n\n  createLazyLoadMetadata() {\n    return {\n      first: this.first,\n      rows: this.rows,\n      sortField: this.sortField,\n      sortOrder: this.sortOrder,\n      filters: this.filters,\n      globalFilter: this.filters && this.filters['global'] ? this.filters['global'].value : null,\n      multiSortMeta: this.multiSortMeta,\n      forceUpdate: () => this.cd.detectChanges()\n    };\n  }\n\n  onLazyItemLoad(event) {\n    this.onLazyLoad.emit(Object.assign(Object.assign(Object.assign({}, this.createLazyLoadMetadata()), event), {\n      rows: event.last - event.first\n    }));\n  }\n\n  resetScrollTop() {\n    if (this.virtualScroll) this.scrollToVirtualIndex(0);else this.scrollTo({\n      top: 0\n    });\n  }\n\n  scrollToVirtualIndex(index) {\n    if (this.scrollableViewChild) {\n      this.scrollableViewChild.scrollToVirtualIndex(index);\n    }\n\n    if (this.scrollableFrozenViewChild) {\n      this.scrollableFrozenViewChild.scrollToVirtualIndex(index);\n    }\n  }\n\n  scrollTo(options) {\n    if (this.scrollableViewChild) {\n      this.scrollableViewChild.scrollTo(options);\n    }\n\n    if (this.scrollableFrozenViewChild) {\n      this.scrollableFrozenViewChild.scrollTo(options);\n    }\n  }\n\n  isEmpty() {\n    let data = this.filteredNodes || this.value;\n    return data == null || data.length == 0;\n  }\n\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n\n  onColumnResizeBegin(event) {\n    let containerLeft = DomHandler.getOffset(this.containerViewChild.nativeElement).left;\n    this.lastResizerHelperX = event.pageX - containerLeft + this.containerViewChild.nativeElement.scrollLeft;\n    event.preventDefault();\n  }\n\n  onColumnResize(event) {\n    let containerLeft = DomHandler.getOffset(this.containerViewChild.nativeElement).left;\n    DomHandler.addClass(this.containerViewChild.nativeElement, 'p-unselectable-text');\n    this.resizeHelperViewChild.nativeElement.style.height = this.containerViewChild.nativeElement.offsetHeight + 'px';\n    this.resizeHelperViewChild.nativeElement.style.top = 0 + 'px';\n    this.resizeHelperViewChild.nativeElement.style.left = event.pageX - containerLeft + this.containerViewChild.nativeElement.scrollLeft + 'px';\n    this.resizeHelperViewChild.nativeElement.style.display = 'block';\n  }\n\n  onColumnResizeEnd(event, column) {\n    let delta = this.resizeHelperViewChild.nativeElement.offsetLeft - this.lastResizerHelperX;\n    let columnWidth = column.offsetWidth;\n    let newColumnWidth = columnWidth + delta;\n    let minWidth = column.style.minWidth || 15;\n\n    if (columnWidth + delta > parseInt(minWidth)) {\n      if (this.columnResizeMode === 'fit') {\n        let nextColumn = column.nextElementSibling;\n\n        while (!nextColumn.offsetParent) {\n          nextColumn = nextColumn.nextElementSibling;\n        }\n\n        if (nextColumn) {\n          let nextColumnWidth = nextColumn.offsetWidth - delta;\n          let nextColumnMinWidth = nextColumn.style.minWidth || 15;\n\n          if (newColumnWidth > 15 && nextColumnWidth > parseInt(nextColumnMinWidth)) {\n            if (this.scrollable) {\n              let scrollableView = this.findParentScrollableView(column);\n              let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n              let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n              let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n              let resizeColumnIndex = DomHandler.index(column);\n              this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n              this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n              this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n            } else {\n              column.style.width = newColumnWidth + 'px';\n\n              if (nextColumn) {\n                nextColumn.style.width = nextColumnWidth + 'px';\n              }\n            }\n          }\n        }\n      } else if (this.columnResizeMode === 'expand') {\n        if (this.scrollable) {\n          let scrollableView = this.findParentScrollableView(column);\n          let scrollableBody = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport');\n          let scrollableHeader = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-header');\n          let scrollableFooter = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-footer');\n          let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n          let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n          let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n          scrollableBodyTable.style.width = scrollableBodyTable.offsetWidth + delta + 'px';\n          scrollableHeaderTable.style.width = scrollableHeaderTable.offsetWidth + delta + 'px';\n\n          if (scrollableFooterTable) {\n            scrollableFooterTable.style.width = scrollableFooterTable.offsetWidth + delta + 'px';\n          }\n\n          let resizeColumnIndex = DomHandler.index(column);\n          const scrollableBodyTableWidth = column ? scrollableBodyTable.offsetWidth + delta : newColumnWidth;\n          const scrollableHeaderTableWidth = column ? scrollableHeaderTable.offsetWidth + delta : newColumnWidth;\n          const isContainerInViewport = this.containerViewChild.nativeElement.offsetWidth >= scrollableBodyTableWidth;\n\n          let setWidth = (container, table, width, isContainerInViewport) => {\n            if (container && table) {\n              container.style.width = isContainerInViewport ? width + DomHandler.calculateScrollbarWidth(scrollableBody) + 'px' : 'auto';\n              table.style.width = width + 'px';\n            }\n          };\n\n          setWidth(scrollableBody, scrollableBodyTable, scrollableBodyTableWidth, isContainerInViewport);\n          setWidth(scrollableHeader, scrollableHeaderTable, scrollableHeaderTableWidth, isContainerInViewport);\n          setWidth(scrollableFooter, scrollableFooterTable, scrollableHeaderTableWidth, isContainerInViewport);\n          this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, null);\n          this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, null);\n          this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, null);\n        } else {\n          this.tableViewChild.nativeElement.style.width = this.tableViewChild.nativeElement.offsetWidth + delta + 'px';\n          column.style.width = newColumnWidth + 'px';\n          let containerWidth = this.tableViewChild.nativeElement.style.width;\n          this.containerViewChild.nativeElement.style.width = containerWidth + 'px';\n        }\n      }\n\n      this.onColResize.emit({\n        element: column,\n        delta: delta\n      });\n    }\n\n    this.resizeHelperViewChild.nativeElement.style.display = 'none';\n    DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-unselectable-text');\n  }\n\n  findParentScrollableView(column) {\n    if (column) {\n      let parent = column.parentElement;\n\n      while (parent && !DomHandler.hasClass(parent, 'p-treetable-scrollable-view')) {\n        parent = parent.parentElement;\n      }\n\n      return parent;\n    } else {\n      return null;\n    }\n  }\n\n  resizeColGroup(table, resizeColumnIndex, newColumnWidth, nextColumnWidth) {\n    if (table) {\n      let colGroup = table.children[0].nodeName === 'COLGROUP' ? table.children[0] : null;\n\n      if (colGroup) {\n        let col = colGroup.children[resizeColumnIndex];\n        let nextCol = col.nextElementSibling;\n        col.style.width = newColumnWidth + 'px';\n\n        if (nextCol && nextColumnWidth) {\n          nextCol.style.width = nextColumnWidth + 'px';\n        }\n      } else {\n        throw \"Scrollable tables require a colgroup to support resizable columns\";\n      }\n    }\n  }\n\n  onColumnDragStart(event, columnElement) {\n    this.reorderIconWidth = DomHandler.getHiddenElementOuterWidth(this.reorderIndicatorUpViewChild.nativeElement);\n    this.reorderIconHeight = DomHandler.getHiddenElementOuterHeight(this.reorderIndicatorDownViewChild.nativeElement);\n    this.draggedColumn = columnElement;\n    event.dataTransfer.setData('text', 'b'); // For firefox\n  }\n\n  onColumnDragEnter(event, dropHeader) {\n    if (this.reorderableColumns && this.draggedColumn && dropHeader) {\n      event.preventDefault();\n      let containerOffset = DomHandler.getOffset(this.containerViewChild.nativeElement);\n      let dropHeaderOffset = DomHandler.getOffset(dropHeader);\n\n      if (this.draggedColumn != dropHeader) {\n        let targetLeft = dropHeaderOffset.left - containerOffset.left;\n        let targetTop = containerOffset.top - dropHeaderOffset.top;\n        let columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;\n        this.reorderIndicatorUpViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top - (this.reorderIconHeight - 1) + 'px';\n        this.reorderIndicatorDownViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + 'px';\n\n        if (event.pageX > columnCenter) {\n          this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n          this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2) + 'px';\n          this.dropPosition = 1;\n        } else {\n          this.reorderIndicatorUpViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n          this.reorderIndicatorDownViewChild.nativeElement.style.left = targetLeft - Math.ceil(this.reorderIconWidth / 2) + 'px';\n          this.dropPosition = -1;\n        }\n\n        this.reorderIndicatorUpViewChild.nativeElement.style.display = 'block';\n        this.reorderIndicatorDownViewChild.nativeElement.style.display = 'block';\n      } else {\n        event.dataTransfer.dropEffect = 'none';\n      }\n    }\n  }\n\n  onColumnDragLeave(event) {\n    if (this.reorderableColumns && this.draggedColumn) {\n      event.preventDefault();\n      this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n      this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n    }\n  }\n\n  onColumnDrop(event, dropColumn) {\n    event.preventDefault();\n\n    if (this.draggedColumn) {\n      let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'ttreorderablecolumn');\n      let dropIndex = DomHandler.indexWithinGroup(dropColumn, 'ttreorderablecolumn');\n      let allowDrop = dragIndex != dropIndex;\n\n      if (allowDrop && (dropIndex - dragIndex == 1 && this.dropPosition === -1 || dragIndex - dropIndex == 1 && this.dropPosition === 1)) {\n        allowDrop = false;\n      }\n\n      if (allowDrop && dropIndex < dragIndex && this.dropPosition === 1) {\n        dropIndex = dropIndex + 1;\n      }\n\n      if (allowDrop && dropIndex > dragIndex && this.dropPosition === -1) {\n        dropIndex = dropIndex - 1;\n      }\n\n      if (allowDrop) {\n        ObjectUtils.reorderArray(this.columns, dragIndex, dropIndex);\n        this.onColReorder.emit({\n          dragIndex: dragIndex,\n          dropIndex: dropIndex,\n          columns: this.columns\n        });\n      }\n\n      this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n      this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n      this.draggedColumn.draggable = false;\n      this.draggedColumn = null;\n      this.dropPosition = null;\n    }\n  }\n\n  handleRowClick(event) {\n    let targetNode = event.originalEvent.target.nodeName;\n\n    if (targetNode == 'INPUT' || targetNode == 'BUTTON' || targetNode == 'A' || DomHandler.hasClass(event.originalEvent.target, 'p-clickable')) {\n      return;\n    }\n\n    if (this.selectionMode) {\n      this.preventSelectionSetterPropagation = true;\n      let rowNode = event.rowNode;\n      let selected = this.isSelected(rowNode.node);\n      let metaSelection = this.rowTouched ? false : this.metaKeySelection;\n      let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowNode.node.data, this.dataKey)) : null;\n\n      if (metaSelection) {\n        let metaKey = event.originalEvent.metaKey || event.originalEvent.ctrlKey;\n\n        if (selected && metaKey) {\n          if (this.isSingleSelectionMode()) {\n            this._selection = null;\n            this.selectionKeys = {};\n            this.selectionChange.emit(null);\n          } else {\n            let selectionIndex = this.findIndexInSelection(rowNode.node);\n            this._selection = this.selection.filter((val, i) => i != selectionIndex);\n            this.selectionChange.emit(this.selection);\n\n            if (dataKeyValue) {\n              delete this.selectionKeys[dataKeyValue];\n            }\n          }\n\n          this.onNodeUnselect.emit({\n            originalEvent: event.originalEvent,\n            node: rowNode.node,\n            type: 'row'\n          });\n        } else {\n          if (this.isSingleSelectionMode()) {\n            this._selection = rowNode.node;\n            this.selectionChange.emit(rowNode.node);\n\n            if (dataKeyValue) {\n              this.selectionKeys = {};\n              this.selectionKeys[dataKeyValue] = 1;\n            }\n          } else if (this.isMultipleSelectionMode()) {\n            if (metaKey) {\n              this._selection = this.selection || [];\n            } else {\n              this._selection = [];\n              this.selectionKeys = {};\n            }\n\n            this._selection = [...this.selection, rowNode.node];\n            this.selectionChange.emit(this.selection);\n\n            if (dataKeyValue) {\n              this.selectionKeys[dataKeyValue] = 1;\n            }\n          }\n\n          this.onNodeSelect.emit({\n            originalEvent: event.originalEvent,\n            node: rowNode.node,\n            type: 'row',\n            index: event.rowIndex\n          });\n        }\n      } else {\n        if (this.selectionMode === 'single') {\n          if (selected) {\n            this._selection = null;\n            this.selectionKeys = {};\n            this.selectionChange.emit(this.selection);\n            this.onNodeUnselect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row'\n            });\n          } else {\n            this._selection = rowNode.node;\n            this.selectionChange.emit(this.selection);\n            this.onNodeSelect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row',\n              index: event.rowIndex\n            });\n\n            if (dataKeyValue) {\n              this.selectionKeys = {};\n              this.selectionKeys[dataKeyValue] = 1;\n            }\n          }\n        } else if (this.selectionMode === 'multiple') {\n          if (selected) {\n            let selectionIndex = this.findIndexInSelection(rowNode.node);\n            this._selection = this.selection.filter((val, i) => i != selectionIndex);\n            this.selectionChange.emit(this.selection);\n            this.onNodeUnselect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row'\n            });\n\n            if (dataKeyValue) {\n              delete this.selectionKeys[dataKeyValue];\n            }\n          } else {\n            this._selection = this.selection ? [...this.selection, rowNode.node] : [rowNode.node];\n            this.selectionChange.emit(this.selection);\n            this.onNodeSelect.emit({\n              originalEvent: event.originalEvent,\n              node: rowNode.node,\n              type: 'row',\n              index: event.rowIndex\n            });\n\n            if (dataKeyValue) {\n              this.selectionKeys[dataKeyValue] = 1;\n            }\n          }\n        }\n      }\n\n      this.tableService.onSelectionChange();\n    }\n\n    this.rowTouched = false;\n  }\n\n  handleRowTouchEnd(event) {\n    this.rowTouched = true;\n  }\n\n  handleRowRightClick(event) {\n    if (this.contextMenu) {\n      const node = event.rowNode.node;\n\n      if (this.contextMenuSelectionMode === 'separate') {\n        this.contextMenuSelection = node;\n        this.contextMenuSelectionChange.emit(node);\n        this.onContextMenuSelect.emit({\n          originalEvent: event.originalEvent,\n          node: node\n        });\n        this.contextMenu.show(event.originalEvent);\n        this.tableService.onContextMenu(node);\n      } else if (this.contextMenuSelectionMode === 'joint') {\n        this.preventSelectionSetterPropagation = true;\n        let selected = this.isSelected(node);\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n        if (!selected) {\n          if (this.isSingleSelectionMode()) {\n            this.selection = node;\n            this.selectionChange.emit(node);\n          } else if (this.isMultipleSelectionMode()) {\n            this.selection = [node];\n            this.selectionChange.emit(this.selection);\n          }\n\n          if (dataKeyValue) {\n            this.selectionKeys[dataKeyValue] = 1;\n          }\n        }\n\n        this.contextMenu.show(event.originalEvent);\n        this.onContextMenuSelect.emit({\n          originalEvent: event.originalEvent,\n          node: node\n        });\n      }\n    }\n  }\n\n  toggleNodeWithCheckbox(event) {\n    this.selection = this.selection || [];\n    this.preventSelectionSetterPropagation = true;\n    let node = event.rowNode.node;\n    let selected = this.isSelected(node);\n\n    if (selected) {\n      this.propagateSelectionDown(node, false);\n\n      if (event.rowNode.parent) {\n        this.propagateSelectionUp(node.parent, false);\n      }\n\n      this.selectionChange.emit(this.selection);\n      this.onNodeUnselect.emit({\n        originalEvent: event,\n        node: node\n      });\n    } else {\n      this.propagateSelectionDown(node, true);\n\n      if (event.rowNode.parent) {\n        this.propagateSelectionUp(node.parent, true);\n      }\n\n      this.selectionChange.emit(this.selection);\n      this.onNodeSelect.emit({\n        originalEvent: event,\n        node: node\n      });\n    }\n\n    this.tableService.onSelectionChange();\n  }\n\n  toggleNodesWithCheckbox(event, check) {\n    let data = this.filteredNodes || this.value;\n    this._selection = check && data ? data.slice() : [];\n\n    if (check) {\n      if (data && data.length) {\n        for (let node of data) {\n          this.propagateSelectionDown(node, true);\n        }\n      }\n    } else {\n      this._selection = [];\n      this.selectionKeys = {};\n    }\n\n    this.preventSelectionSetterPropagation = true;\n    this.selectionChange.emit(this._selection);\n    this.tableService.onSelectionChange();\n    this.onHeaderCheckboxToggle.emit({\n      originalEvent: event,\n      checked: check\n    });\n  }\n\n  propagateSelectionUp(node, select) {\n    if (node.children && node.children.length) {\n      let selectedChildCount = 0;\n      let childPartialSelected = false;\n      let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n      for (let child of node.children) {\n        if (this.isSelected(child)) selectedChildCount++;else if (child.partialSelected) childPartialSelected = true;\n      }\n\n      if (select && selectedChildCount == node.children.length) {\n        this._selection = [...(this.selection || []), node];\n        node.partialSelected = false;\n\n        if (dataKeyValue) {\n          this.selectionKeys[dataKeyValue] = 1;\n        }\n      } else {\n        if (!select) {\n          let index = this.findIndexInSelection(node);\n\n          if (index >= 0) {\n            this._selection = this.selection.filter((val, i) => i != index);\n\n            if (dataKeyValue) {\n              delete this.selectionKeys[dataKeyValue];\n            }\n          }\n        }\n\n        if (childPartialSelected || selectedChildCount > 0 && selectedChildCount != node.children.length) node.partialSelected = true;else node.partialSelected = false;\n      }\n    }\n\n    let parent = node.parent;\n\n    if (parent) {\n      this.propagateSelectionUp(parent, select);\n    }\n  }\n\n  propagateSelectionDown(node, select) {\n    let index = this.findIndexInSelection(node);\n    let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n\n    if (select && index == -1) {\n      this._selection = [...(this.selection || []), node];\n\n      if (dataKeyValue) {\n        this.selectionKeys[dataKeyValue] = 1;\n      }\n    } else if (!select && index > -1) {\n      this._selection = this.selection.filter((val, i) => i != index);\n\n      if (dataKeyValue) {\n        delete this.selectionKeys[dataKeyValue];\n      }\n    }\n\n    node.partialSelected = false;\n\n    if (node.children && node.children.length) {\n      for (let child of node.children) {\n        this.propagateSelectionDown(child, select);\n      }\n    }\n  }\n\n  isSelected(node) {\n    if (node && this.selection) {\n      if (this.dataKey) {\n        return this.selectionKeys[ObjectUtils.resolveFieldData(node.data, this.dataKey)] !== undefined;\n      } else {\n        if (this.selection instanceof Array) return this.findIndexInSelection(node) > -1;else return this.equals(node, this.selection);\n      }\n    }\n\n    return false;\n  }\n\n  findIndexInSelection(node) {\n    let index = -1;\n\n    if (this.selection && this.selection.length) {\n      for (let i = 0; i < this.selection.length; i++) {\n        if (this.equals(node, this.selection[i])) {\n          index = i;\n          break;\n        }\n      }\n    }\n\n    return index;\n  }\n\n  isSingleSelectionMode() {\n    return this.selectionMode === 'single';\n  }\n\n  isMultipleSelectionMode() {\n    return this.selectionMode === 'multiple';\n  }\n\n  equals(node1, node2) {\n    return this.compareSelectionBy === 'equals' ? node1 === node2 : ObjectUtils.equals(node1.data, node2.data, this.dataKey);\n  }\n\n  filter(value, field, matchMode) {\n    if (this.filterTimeout) {\n      clearTimeout(this.filterTimeout);\n    }\n\n    if (!this.isFilterBlank(value)) {\n      this.filters[field] = {\n        value: value,\n        matchMode: matchMode\n      };\n    } else if (this.filters[field]) {\n      delete this.filters[field];\n    }\n\n    this.filterTimeout = setTimeout(() => {\n      this._filter();\n\n      this.filterTimeout = null;\n    }, this.filterDelay);\n  }\n\n  filterGlobal(value, matchMode) {\n    this.filter(value, 'global', matchMode);\n  }\n\n  isFilterBlank(filter) {\n    if (filter !== null && filter !== undefined) {\n      if (typeof filter === 'string' && filter.trim().length == 0 || filter instanceof Array && filter.length == 0) return true;else return false;\n    }\n\n    return true;\n  }\n\n  _filter() {\n    if (this.lazy) {\n      this.onLazyLoad.emit(this.createLazyLoadMetadata());\n    } else {\n      if (!this.value) {\n        return;\n      }\n\n      if (!this.hasFilter()) {\n        this.filteredNodes = null;\n\n        if (this.paginator) {\n          this.totalRecords = this.value ? this.value.length : 0;\n        }\n      } else {\n        let globalFilterFieldsArray;\n\n        if (this.filters['global']) {\n          if (!this.columns && !this.globalFilterFields) throw new Error('Global filtering requires dynamic columns or globalFilterFields to be defined.');else globalFilterFieldsArray = this.globalFilterFields || this.columns;\n        }\n\n        this.filteredNodes = [];\n        const isStrictMode = this.filterMode === 'strict';\n        let isValueChanged = false;\n\n        for (let node of this.value) {\n          let copyNode = Object.assign({}, node);\n          let localMatch = true;\n          let globalMatch = false;\n          let paramsWithoutNode;\n\n          for (let prop in this.filters) {\n            if (this.filters.hasOwnProperty(prop) && prop !== 'global') {\n              let filterMeta = this.filters[prop];\n              let filterField = prop;\n              let filterValue = filterMeta.value;\n              let filterMatchMode = filterMeta.matchMode || 'startsWith';\n              let filterConstraint = this.filterService.filters[filterMatchMode];\n              paramsWithoutNode = {\n                filterField,\n                filterValue,\n                filterConstraint,\n                isStrictMode\n              };\n\n              if (isStrictMode && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode)) || !isStrictMode && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode))) {\n                localMatch = false;\n              }\n\n              if (!localMatch) {\n                break;\n              }\n            }\n          }\n\n          if (this.filters['global'] && !globalMatch && globalFilterFieldsArray) {\n            for (let j = 0; j < globalFilterFieldsArray.length; j++) {\n              let copyNodeForGlobal = Object.assign({}, copyNode);\n              let filterField = globalFilterFieldsArray[j].field || globalFilterFieldsArray[j];\n              let filterValue = this.filters['global'].value;\n              let filterConstraint = this.filterService.filters[this.filters['global'].matchMode];\n              paramsWithoutNode = {\n                filterField,\n                filterValue,\n                filterConstraint,\n                isStrictMode\n              };\n\n              if (isStrictMode && (this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode)) || !isStrictMode && (this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode))) {\n                globalMatch = true;\n                copyNode = copyNodeForGlobal;\n              }\n            }\n          }\n\n          let matches = localMatch;\n\n          if (this.filters['global']) {\n            matches = localMatch && globalMatch;\n          }\n\n          if (matches) {\n            this.filteredNodes.push(copyNode);\n          }\n\n          isValueChanged = isValueChanged || !localMatch || globalMatch || localMatch && this.filteredNodes.length > 0 || !globalMatch && this.filteredNodes.length === 0;\n        }\n\n        if (!isValueChanged) {\n          this.filteredNodes = null;\n        }\n\n        if (this.paginator) {\n          this.totalRecords = this.filteredNodes ? this.filteredNodes.length : this.value ? this.value.length : 0;\n        }\n      }\n    }\n\n    this.first = 0;\n    const filteredValue = this.filteredNodes || this.value;\n    this.onFilter.emit({\n      filters: this.filters,\n      filteredValue: filteredValue\n    });\n    this.tableService.onUIUpdate(filteredValue);\n    this.updateSerializedValue();\n\n    if (this.scrollable) {\n      this.resetScrollTop();\n    }\n  }\n\n  findFilteredNodes(node, paramsWithoutNode) {\n    if (node) {\n      let matched = false;\n\n      if (node.children) {\n        let childNodes = [...node.children];\n        node.children = [];\n\n        for (let childNode of childNodes) {\n          let copyChildNode = Object.assign({}, childNode);\n\n          if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n            matched = true;\n            node.children.push(copyChildNode);\n          }\n        }\n      }\n\n      if (matched) {\n        return true;\n      }\n    }\n  }\n\n  isFilterMatched(node, {\n    filterField,\n    filterValue,\n    filterConstraint,\n    isStrictMode\n  }) {\n    let matched = false;\n    let dataFieldValue = ObjectUtils.resolveFieldData(node.data, filterField);\n\n    if (filterConstraint(dataFieldValue, filterValue, this.filterLocale)) {\n      matched = true;\n    }\n\n    if (!matched || isStrictMode && !this.isNodeLeaf(node)) {\n      matched = this.findFilteredNodes(node, {\n        filterField,\n        filterValue,\n        filterConstraint,\n        isStrictMode\n      }) || matched;\n    }\n\n    return matched;\n  }\n\n  isNodeLeaf(node) {\n    return node.leaf === false ? false : !(node.children && node.children.length);\n  }\n\n  hasFilter() {\n    let empty = true;\n\n    for (let prop in this.filters) {\n      if (this.filters.hasOwnProperty(prop)) {\n        empty = false;\n        break;\n      }\n    }\n\n    return !empty;\n  }\n\n  reset() {\n    this._sortField = null;\n    this._sortOrder = 1;\n    this._multiSortMeta = null;\n    this.tableService.onSort(null);\n    this.filteredNodes = null;\n    this.filters = {};\n    this.first = 0;\n\n    if (this.lazy) {\n      this.onLazyLoad.emit(this.createLazyLoadMetadata());\n    } else {\n      this.totalRecords = this._value ? this._value.length : 0;\n    }\n  }\n\n  updateEditingCell(cell, data, field) {\n    this.editingCell = cell;\n    this.editingCellData = data;\n    this.editingCellField = field;\n    this.bindDocumentEditListener();\n  }\n\n  isEditingCellValid() {\n    return this.editingCell && DomHandler.find(this.editingCell, '.ng-invalid.ng-dirty').length === 0;\n  }\n\n  bindDocumentEditListener() {\n    if (!this.documentEditListener) {\n      this.documentEditListener = event => {\n        if (this.editingCell && !this.editingCellClick && this.isEditingCellValid()) {\n          DomHandler.removeClass(this.editingCell, 'p-cell-editing');\n          this.editingCell = null;\n          this.onEditComplete.emit({\n            field: this.editingCellField,\n            data: this.editingCellData\n          });\n          this.editingCellField = null;\n          this.editingCellData = null;\n          this.unbindDocumentEditListener();\n        }\n\n        this.editingCellClick = false;\n      };\n\n      document.addEventListener('click', this.documentEditListener);\n    }\n  }\n\n  unbindDocumentEditListener() {\n    if (this.documentEditListener) {\n      document.removeEventListener('click', this.documentEditListener);\n      this.documentEditListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    this.unbindDocumentEditListener();\n    this.editingCell = null;\n    this.editingCellField = null;\n    this.editingCellData = null;\n    this.initialized = null;\n  }\n\n}\n\nTreeTable.ɵfac = function TreeTable_Factory(t) {\n  return new (t || TreeTable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i1.FilterService));\n};\n\nTreeTable.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TreeTable,\n  selectors: [[\"p-treeTable\"]],\n  contentQueries: function TreeTable_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function TreeTable_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n      i0.ɵɵviewQuery(_c3, 5);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n      i0.ɵɵviewQuery(_c6, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.resizeHelperViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorUpViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.reorderIndicatorDownViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableFrozenViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    columns: \"columns\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    tableStyle: \"tableStyle\",\n    tableStyleClass: \"tableStyleClass\",\n    autoLayout: \"autoLayout\",\n    lazy: \"lazy\",\n    lazyLoadOnInit: \"lazyLoadOnInit\",\n    paginator: \"paginator\",\n    rows: \"rows\",\n    first: \"first\",\n    pageLinks: \"pageLinks\",\n    rowsPerPageOptions: \"rowsPerPageOptions\",\n    alwaysShowPaginator: \"alwaysShowPaginator\",\n    paginatorPosition: \"paginatorPosition\",\n    paginatorDropdownAppendTo: \"paginatorDropdownAppendTo\",\n    currentPageReportTemplate: \"currentPageReportTemplate\",\n    showCurrentPageReport: \"showCurrentPageReport\",\n    showJumpToPageDropdown: \"showJumpToPageDropdown\",\n    showFirstLastIcon: \"showFirstLastIcon\",\n    showPageLinks: \"showPageLinks\",\n    defaultSortOrder: \"defaultSortOrder\",\n    sortMode: \"sortMode\",\n    resetPageOnSort: \"resetPageOnSort\",\n    customSort: \"customSort\",\n    selectionMode: \"selectionMode\",\n    contextMenuSelection: \"contextMenuSelection\",\n    contextMenuSelectionMode: \"contextMenuSelectionMode\",\n    dataKey: \"dataKey\",\n    metaKeySelection: \"metaKeySelection\",\n    compareSelectionBy: \"compareSelectionBy\",\n    rowHover: \"rowHover\",\n    loading: \"loading\",\n    loadingIcon: \"loadingIcon\",\n    showLoader: \"showLoader\",\n    scrollable: \"scrollable\",\n    scrollHeight: \"scrollHeight\",\n    virtualScroll: \"virtualScroll\",\n    virtualScrollItemSize: \"virtualScrollItemSize\",\n    virtualScrollOptions: \"virtualScrollOptions\",\n    virtualScrollDelay: \"virtualScrollDelay\",\n    frozenWidth: \"frozenWidth\",\n    frozenColumns: \"frozenColumns\",\n    resizableColumns: \"resizableColumns\",\n    columnResizeMode: \"columnResizeMode\",\n    reorderableColumns: \"reorderableColumns\",\n    contextMenu: \"contextMenu\",\n    rowTrackBy: \"rowTrackBy\",\n    filters: \"filters\",\n    globalFilterFields: \"globalFilterFields\",\n    filterDelay: \"filterDelay\",\n    filterMode: \"filterMode\",\n    filterLocale: \"filterLocale\",\n    virtualRowHeight: \"virtualRowHeight\",\n    value: \"value\",\n    totalRecords: \"totalRecords\",\n    sortField: \"sortField\",\n    sortOrder: \"sortOrder\",\n    multiSortMeta: \"multiSortMeta\",\n    selection: \"selection\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\",\n    contextMenuSelectionChange: \"contextMenuSelectionChange\",\n    onFilter: \"onFilter\",\n    onNodeExpand: \"onNodeExpand\",\n    onNodeCollapse: \"onNodeCollapse\",\n    onPage: \"onPage\",\n    onSort: \"onSort\",\n    onLazyLoad: \"onLazyLoad\",\n    sortFunction: \"sortFunction\",\n    onColResize: \"onColResize\",\n    onColReorder: \"onColReorder\",\n    onNodeSelect: \"onNodeSelect\",\n    onNodeUnselect: \"onNodeUnselect\",\n    onContextMenuSelect: \"onContextMenuSelect\",\n    onHeaderCheckboxToggle: \"onHeaderCheckboxToggle\",\n    onEditInit: \"onEditInit\",\n    onEditComplete: \"onEditComplete\",\n    onEditCancel: \"onEditCancel\"\n  },\n  features: [i0.ɵɵProvidersFeature([TreeTableService]), i0.ɵɵNgOnChangesFeature],\n  decls: 12,\n  vars: 20,\n  consts: [[\"data-scrollselectors\", \".p-treetable-scrollable-body\", 3, \"ngStyle\", \"ngClass\"], [\"container\", \"\"], [\"class\", \"p-treetable-loading\", 4, \"ngIf\"], [\"class\", \"p-treetable-header\", 4, \"ngIf\"], [\"styleClass\", \"p-paginator-top\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"onPageChange\", 4, \"ngIf\"], [\"class\", \"p-treetable-wrapper\", 4, \"ngIf\"], [\"class\", \"p-treetable-scrollable-wrapper\", 4, \"ngIf\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"onPageChange\", 4, \"ngIf\"], [\"class\", \"p-treetable-footer\", 4, \"ngIf\"], [\"class\", \"p-column-resizer-helper\", \"style\", \"display:none\", 4, \"ngIf\"], [\"class\", \"pi pi-arrow-down p-treetable-reorder-indicator-up\", 4, \"ngIf\"], [\"class\", \"pi pi-arrow-up p-treetable-reorder-indicator-down\", 4, \"ngIf\"], [1, \"p-treetable-loading\"], [1, \"p-treetable-loading-overlay\", \"p-component-overlay\"], [1, \"p-treetable-header\"], [4, \"ngTemplateOutlet\"], [\"styleClass\", \"p-paginator-top\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"onPageChange\"], [1, \"p-treetable-wrapper\"], [3, \"ngClass\", \"ngStyle\"], [\"table\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-treetable-thead\"], [1, \"p-treetable-tbody\", 3, \"pTreeTableBody\", \"pTreeTableBodyTemplate\"], [1, \"p-treetable-tfoot\"], [1, \"p-treetable-scrollable-wrapper\"], [\"class\", \"p-treetable-scrollable-view p-treetable-frozen-view\", 3, \"ttScrollableView\", \"frozen\", \"ngStyle\", \"scrollHeight\", 4, \"ngIf\"], [1, \"p-treetable-scrollable-view\", 3, \"ttScrollableView\", \"frozen\", \"scrollHeight\", \"ngStyle\"], [\"scrollableView\", \"\"], [1, \"p-treetable-scrollable-view\", \"p-treetable-frozen-view\", 3, \"ttScrollableView\", \"frozen\", \"ngStyle\", \"scrollHeight\"], [\"scrollableFrozenView\", \"\"], [\"styleClass\", \"p-paginator-bottom\", 3, \"rows\", \"first\", \"totalRecords\", \"pageLinkSize\", \"alwaysShow\", \"rowsPerPageOptions\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"currentPageReportTemplate\", \"showFirstLastIcon\", \"dropdownItemTemplate\", \"showCurrentPageReport\", \"showJumpToPageDropdown\", \"showPageLinks\", \"onPageChange\"], [1, \"p-treetable-footer\"], [1, \"p-column-resizer-helper\", 2, \"display\", \"none\"], [\"resizeHelper\", \"\"], [1, \"pi\", \"pi-arrow-down\", \"p-treetable-reorder-indicator-up\"], [\"reorderIndicatorUp\", \"\"], [1, \"pi\", \"pi-arrow-up\", \"p-treetable-reorder-indicator-down\"], [\"reorderIndicatorDown\", \"\"]],\n  template: function TreeTable_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵtemplate(2, TreeTable_div_2_Template, 3, 2, \"div\", 2);\n      i0.ɵɵtemplate(3, TreeTable_div_3_Template, 2, 1, \"div\", 3);\n      i0.ɵɵtemplate(4, TreeTable_p_paginator_4_Template, 1, 15, \"p-paginator\", 4);\n      i0.ɵɵtemplate(5, TreeTable_div_5_Template, 9, 16, \"div\", 5);\n      i0.ɵɵtemplate(6, TreeTable_div_6_Template, 4, 8, \"div\", 6);\n      i0.ɵɵtemplate(7, TreeTable_p_paginator_7_Template, 1, 15, \"p-paginator\", 7);\n      i0.ɵɵtemplate(8, TreeTable_div_8_Template, 2, 1, \"div\", 8);\n      i0.ɵɵtemplate(9, TreeTable_div_9_Template, 2, 0, \"div\", 9);\n      i0.ɵɵtemplate(10, TreeTable_span_10_Template, 2, 0, \"span\", 10);\n      i0.ɵɵtemplate(11, TreeTable_span_11_Template, 2, 0, \"span\", 11);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction5(14, _c10, ctx.rowHover || ctx.selectionMode === \"single\" || ctx.selectionMode === \"multiple\", ctx.autoLayout, ctx.resizableColumns, ctx.resizableColumns && ctx.columnResizeMode === \"fit\", ctx.scrollable && ctx.scrollHeight === \"flex\"));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading && ctx.showLoader);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.captionTemplate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"top\" || ctx.paginatorPosition == \"both\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.scrollable);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.scrollable);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.paginator && (ctx.paginatorPosition === \"bottom\" || ctx.paginatorPosition == \"both\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.summaryTemplate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.resizableColumns);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.reorderableColumns);\n    }\n  },\n  dependencies: function () {\n    return [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Paginator, TTScrollableView, TTBody];\n  },\n  styles: [\".p-treetable{position:relative}.p-treetable table{border-collapse:collapse;width:100%;table-layout:fixed}.p-treetable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-treetable .p-sortable-column .p-column-title,.p-treetable .p-sortable-column .p-sortable-column-icon,.p-treetable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-treetable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-treetable-auto-layout>.p-treetable-wrapper{overflow-x:auto}.p-treetable-auto-layout>.p-treetable-wrapper>table{table-layout:auto}.p-treetable-hoverable-rows .p-treetable-tbody>tr{cursor:pointer}.p-treetable-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;vertical-align:middle;overflow:hidden;position:relative}p-treetabletoggler+p-treetablecheckbox .p-checkbox{vertical-align:middle}p-treetabletoggler+p-treetablecheckbox+span{vertical-align:middle}.p-treetable-scrollable-wrapper{position:relative}.p-treetable-scrollable-header,.p-treetable-scrollable-footer{overflow:hidden}.p-treetable-scrollable-body{overflow:auto;position:relative}.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td{border-top:0 none}.p-treetable-virtual-table{position:absolute}.p-treetable-frozen-view .p-treetable-scrollable-body{overflow:hidden}.p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child{border-right:0 none}.p-treetable-unfrozen-view{position:absolute;top:0}.p-treetable-flex-scrollable,.p-treetable-flex-scrollable .p-treetable-scrollable-wrapper,.p-treetable-flex-scrollable .p-treetable-scrollable-view{display:flex;flex-direction:column;flex:1;height:100%}.p-treetable-flex-scrollable .p-treetable-virtual-scrollable-body{flex:1}.p-treetable-resizable>.p-treetable-wrapper{overflow-x:auto}.p-treetable-resizable .p-treetable-thead>tr>th,.p-treetable-resizable .p-treetable-tfoot>tr>td,.p-treetable-resizable .p-treetable-tbody>tr>td{overflow:hidden}.p-treetable-resizable .p-resizable-column{background-clip:padding-box;position:relative}.p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer{display:none}.p-treetable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-treetable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-treetable .p-row-editor-init,.p-treetable .p-row-editor-save,.p-treetable .p-row-editor-cancel,.p-treetable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-treetable-reorder-indicator-up,.p-treetable-reorder-indicator-down{position:absolute;display:none}[ttReorderableColumn]{cursor:move}.p-treetable .p-treetable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-treetable .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}\\n\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeTable, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTable',\n      template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" data-scrollselectors=\".p-treetable-scrollable-body\"\n                [ngClass]=\"{'p-treetable p-component': true,\n                'p-treetable-hoverable-rows': (rowHover||(selectionMode === 'single' || selectionMode === 'multiple')),\n                'p-treetable-auto-layout': autoLayout,\n                'p-treetable-resizable': resizableColumns,\n                'p-treetable-resizable-fit': (resizableColumns && columnResizeMode === 'fit'),\n                'p-treetable-flex-scrollable': (scrollable && scrollHeight === 'flex')}\">\n            <div class=\"p-treetable-loading\" *ngIf=\"loading && showLoader\">\n                <div class=\"p-treetable-loading-overlay p-component-overlay\">\n                    <i [class]=\"'p-treetable-loading-icon pi-spin ' + loadingIcon\"></i>\n                </div>\n            </div>\n            <div *ngIf=\"captionTemplate\" class=\"p-treetable-header\">\n                <ng-container *ngTemplateOutlet=\"captionTemplate\"></ng-container>\n            </div>\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-top\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'top' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n\n            <div class=\"p-treetable-wrapper\" *ngIf=\"!scrollable\">\n                <table #table [ngClass]=\"tableStyleClass\" [ngStyle]=\"tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"headerTemplate; context: {$implicit: columns}\"></ng-container>\n                    </thead>\n                    <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"bodyTemplate\"></tbody>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n\n            <div class=\"p-treetable-scrollable-wrapper\" *ngIf=\"scrollable\">\n               <div class=\"p-treetable-scrollable-view p-treetable-frozen-view\" *ngIf=\"frozenColumns||frozenBodyTemplate\" #scrollableFrozenView [ttScrollableView]=\"frozenColumns\" [frozen]=\"true\" [ngStyle]=\"{width: frozenWidth}\" [scrollHeight]=\"scrollHeight\"></div>\n               <div class=\"p-treetable-scrollable-view\" #scrollableView [ttScrollableView]=\"columns\" [frozen]=\"false\" [scrollHeight]=\"scrollHeight\" [ngStyle]=\"{left: frozenWidth, width: 'calc(100% - '+frozenWidth+')'}\"></div>\n            </div>\n\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-bottom\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'bottom' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n            <div *ngIf=\"summaryTemplate\" class=\"p-treetable-footer\">\n                <ng-container *ngTemplateOutlet=\"summaryTemplate\"></ng-container>\n            </div>\n\n            <div #resizeHelper class=\"p-column-resizer-helper\" style=\"display:none\" *ngIf=\"resizableColumns\"></div>\n\n            <span #reorderIndicatorUp class=\"pi pi-arrow-down p-treetable-reorder-indicator-up\" *ngIf=\"reorderableColumns\"></span>\n            <span #reorderIndicatorDown class=\"pi pi-arrow-up p-treetable-reorder-indicator-down\" *ngIf=\"reorderableColumns\"></span>\n        </div>\n    `,\n      providers: [TreeTableService],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-treetable{position:relative}.p-treetable table{border-collapse:collapse;width:100%;table-layout:fixed}.p-treetable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-treetable .p-sortable-column .p-column-title,.p-treetable .p-sortable-column .p-sortable-column-icon,.p-treetable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-treetable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-treetable-auto-layout>.p-treetable-wrapper{overflow-x:auto}.p-treetable-auto-layout>.p-treetable-wrapper>table{table-layout:auto}.p-treetable-hoverable-rows .p-treetable-tbody>tr{cursor:pointer}.p-treetable-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;vertical-align:middle;overflow:hidden;position:relative}p-treetabletoggler+p-treetablecheckbox .p-checkbox{vertical-align:middle}p-treetabletoggler+p-treetablecheckbox+span{vertical-align:middle}.p-treetable-scrollable-wrapper{position:relative}.p-treetable-scrollable-header,.p-treetable-scrollable-footer{overflow:hidden}.p-treetable-scrollable-body{overflow:auto;position:relative}.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td{border-top:0 none}.p-treetable-virtual-table{position:absolute}.p-treetable-frozen-view .p-treetable-scrollable-body{overflow:hidden}.p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child{border-right:0 none}.p-treetable-unfrozen-view{position:absolute;top:0}.p-treetable-flex-scrollable,.p-treetable-flex-scrollable .p-treetable-scrollable-wrapper,.p-treetable-flex-scrollable .p-treetable-scrollable-view{display:flex;flex-direction:column;flex:1;height:100%}.p-treetable-flex-scrollable .p-treetable-virtual-scrollable-body{flex:1}.p-treetable-resizable>.p-treetable-wrapper{overflow-x:auto}.p-treetable-resizable .p-treetable-thead>tr>th,.p-treetable-resizable .p-treetable-tfoot>tr>td,.p-treetable-resizable .p-treetable-tbody>tr>td{overflow:hidden}.p-treetable-resizable .p-resizable-column{background-clip:padding-box;position:relative}.p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer{display:none}.p-treetable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-treetable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-treetable .p-row-editor-init,.p-treetable .p-row-editor-save,.p-treetable .p-row-editor-cancel,.p-treetable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-treetable-reorder-indicator-up,.p-treetable-reorder-indicator-down{position:absolute;display:none}[ttReorderableColumn]{cursor:move}.p-treetable .p-treetable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-treetable .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: TreeTableService\n    }, {\n      type: i1.FilterService\n    }];\n  }, {\n    columns: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tableStyle: [{\n      type: Input\n    }],\n    tableStyleClass: [{\n      type: Input\n    }],\n    autoLayout: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    lazyLoadOnInit: [{\n      type: Input\n    }],\n    paginator: [{\n      type: Input\n    }],\n    rows: [{\n      type: Input\n    }],\n    first: [{\n      type: Input\n    }],\n    pageLinks: [{\n      type: Input\n    }],\n    rowsPerPageOptions: [{\n      type: Input\n    }],\n    alwaysShowPaginator: [{\n      type: Input\n    }],\n    paginatorPosition: [{\n      type: Input\n    }],\n    paginatorDropdownAppendTo: [{\n      type: Input\n    }],\n    currentPageReportTemplate: [{\n      type: Input\n    }],\n    showCurrentPageReport: [{\n      type: Input\n    }],\n    showJumpToPageDropdown: [{\n      type: Input\n    }],\n    showFirstLastIcon: [{\n      type: Input\n    }],\n    showPageLinks: [{\n      type: Input\n    }],\n    defaultSortOrder: [{\n      type: Input\n    }],\n    sortMode: [{\n      type: Input\n    }],\n    resetPageOnSort: [{\n      type: Input\n    }],\n    customSort: [{\n      type: Input\n    }],\n    selectionMode: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    contextMenuSelection: [{\n      type: Input\n    }],\n    contextMenuSelectionChange: [{\n      type: Output\n    }],\n    contextMenuSelectionMode: [{\n      type: Input\n    }],\n    dataKey: [{\n      type: Input\n    }],\n    metaKeySelection: [{\n      type: Input\n    }],\n    compareSelectionBy: [{\n      type: Input\n    }],\n    rowHover: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    loadingIcon: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    scrollable: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    virtualScrollItemSize: [{\n      type: Input\n    }],\n    virtualScrollOptions: [{\n      type: Input\n    }],\n    virtualScrollDelay: [{\n      type: Input\n    }],\n    frozenWidth: [{\n      type: Input\n    }],\n    frozenColumns: [{\n      type: Input\n    }],\n    resizableColumns: [{\n      type: Input\n    }],\n    columnResizeMode: [{\n      type: Input\n    }],\n    reorderableColumns: [{\n      type: Input\n    }],\n    contextMenu: [{\n      type: Input\n    }],\n    rowTrackBy: [{\n      type: Input\n    }],\n    filters: [{\n      type: Input\n    }],\n    globalFilterFields: [{\n      type: Input\n    }],\n    filterDelay: [{\n      type: Input\n    }],\n    filterMode: [{\n      type: Input\n    }],\n    filterLocale: [{\n      type: Input\n    }],\n    onFilter: [{\n      type: Output\n    }],\n    onNodeExpand: [{\n      type: Output\n    }],\n    onNodeCollapse: [{\n      type: Output\n    }],\n    onPage: [{\n      type: Output\n    }],\n    onSort: [{\n      type: Output\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    sortFunction: [{\n      type: Output\n    }],\n    onColResize: [{\n      type: Output\n    }],\n    onColReorder: [{\n      type: Output\n    }],\n    onNodeSelect: [{\n      type: Output\n    }],\n    onNodeUnselect: [{\n      type: Output\n    }],\n    onContextMenuSelect: [{\n      type: Output\n    }],\n    onHeaderCheckboxToggle: [{\n      type: Output\n    }],\n    onEditInit: [{\n      type: Output\n    }],\n    onEditComplete: [{\n      type: Output\n    }],\n    onEditCancel: [{\n      type: Output\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    resizeHelperViewChild: [{\n      type: ViewChild,\n      args: ['resizeHelper']\n    }],\n    reorderIndicatorUpViewChild: [{\n      type: ViewChild,\n      args: ['reorderIndicatorUp']\n    }],\n    reorderIndicatorDownViewChild: [{\n      type: ViewChild,\n      args: ['reorderIndicatorDown']\n    }],\n    tableViewChild: [{\n      type: ViewChild,\n      args: ['table']\n    }],\n    scrollableViewChild: [{\n      type: ViewChild,\n      args: ['scrollableView']\n    }],\n    scrollableFrozenViewChild: [{\n      type: ViewChild,\n      args: ['scrollableFrozenView']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    virtualRowHeight: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    totalRecords: [{\n      type: Input\n    }],\n    sortField: [{\n      type: Input\n    }],\n    sortOrder: [{\n      type: Input\n    }],\n    multiSortMeta: [{\n      type: Input\n    }],\n    selection: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TTBody {\n  constructor(tt, treeTableService, cd) {\n    this.tt = tt;\n    this.treeTableService = treeTableService;\n    this.cd = cd;\n    this.subscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n      if (this.tt.virtualScroll) {\n        this.cd.detectChanges();\n      }\n    });\n  }\n\n  getScrollerOption(option, options) {\n    if (this.tt.virtualScroll) {\n      options = options || this.scrollerOptions;\n      return options ? options[option] : null;\n    }\n\n    return null;\n  }\n\n  getRowIndex(rowIndex) {\n    const getItemOptions = this.getScrollerOption('getItemOptions');\n    return getItemOptions ? getItemOptions(rowIndex).index : rowIndex;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTBody.ɵfac = function TTBody_Factory(t) {\n  return new (t || TTBody)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTTBody.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TTBody,\n  selectors: [[\"\", \"pTreeTableBody\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    columns: [\"pTreeTableBody\", \"columns\"],\n    template: [\"pTreeTableBodyTemplate\", \"template\"],\n    frozen: \"frozen\",\n    serializedNodes: \"serializedNodes\",\n    scrollerOptions: \"scrollerOptions\"\n  },\n  attrs: _c11,\n  decls: 2,\n  vars: 3,\n  consts: [[\"ngFor\", \"\", 3, \"ngForOf\", \"ngForTrackBy\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function TTBody_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TTBody_ng_template_0_Template, 1, 1, \"ng-template\", 0);\n      i0.ɵɵtemplate(1, TTBody_ng_container_1_Template, 2, 5, \"ng-container\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngForOf\", ctx.serializedNodes || ctx.tt.serializedValue)(\"ngForTrackBy\", ctx.tt.rowTrackBy);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.tt.isEmpty());\n    }\n  },\n  dependencies: [i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTBody, [{\n    type: Component,\n    args: [{\n      selector: '[pTreeTableBody]',\n      template: `\n        <ng-template ngFor let-serializedNode let-rowIndex=\"index\" [ngForOf]=\"serializedNodes||tt.serializedValue\" [ngForTrackBy]=\"tt.rowTrackBy\">\n            <ng-container *ngIf=\"serializedNode.visible\">\n                <ng-container *ngTemplateOutlet=\"template; context: {$implicit: serializedNode, node: serializedNode.node, rowData: serializedNode.node.data, columns: columns}\"></ng-container>\n            </ng-container>\n        </ng-template>\n        <ng-container *ngIf=\"tt.isEmpty()\">\n            <ng-container *ngTemplateOutlet=\"tt.emptyMessageTemplate; context: {$implicit: columns, frozen: frozen}\"></ng-container>\n        </ng-container>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    columns: [{\n      type: Input,\n      args: [\"pTreeTableBody\"]\n    }],\n    template: [{\n      type: Input,\n      args: [\"pTreeTableBodyTemplate\"]\n    }],\n    frozen: [{\n      type: Input\n    }],\n    serializedNodes: [{\n      type: Input\n    }],\n    scrollerOptions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TTScrollableView {\n  constructor(tt, el, zone) {\n    this.tt = tt;\n    this.el = el;\n    this.zone = zone;\n  }\n\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n\n    if (val != null && (val.includes('%') || val.includes('calc'))) {\n      console.log('Percentage scroll height calculation is removed in favor of the more performant CSS based flex mode, use scrollHeight=\"flex\" instead.');\n    }\n  }\n\n  ngAfterViewInit() {\n    if (!this.frozen) {\n      if (this.tt.frozenColumns || this.tt.frozenBodyTemplate) {\n        DomHandler.addClass(this.el.nativeElement, 'p-treetable-unfrozen-view');\n      }\n\n      let frozenView = this.el.nativeElement.previousElementSibling;\n\n      if (frozenView) {\n        if (this.tt.virtualScroll) this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-scroller-viewport');else this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-treetable-scrollable-body');\n      }\n\n      let scrollBarWidth = DomHandler.calculateScrollbarWidth();\n      this.scrollHeaderBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n\n      if (this.scrollFooterBoxViewChild && this.scrollFooterBoxViewChild.nativeElement) {\n        this.scrollFooterBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n      }\n    } else {\n      if (this.scrollableAlignerViewChild && this.scrollableAlignerViewChild.nativeElement) {\n        this.scrollableAlignerViewChild.nativeElement.style.height = DomHandler.calculateScrollbarHeight() + 'px';\n      }\n    }\n\n    this.bindEvents();\n  }\n\n  bindEvents() {\n    this.zone.runOutsideAngular(() => {\n      if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n        this.headerScrollListener = this.onHeaderScroll.bind(this);\n        this.scrollHeaderBoxViewChild.nativeElement.addEventListener('scroll', this.headerScrollListener);\n      }\n\n      if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n        this.footerScrollListener = this.onFooterScroll.bind(this);\n        this.scrollFooterViewChild.nativeElement.addEventListener('scroll', this.footerScrollListener);\n      }\n\n      if (!this.frozen) {\n        this.bodyScrollListener = this.onBodyScroll.bind(this);\n        if (this.tt.virtualScroll) this.scroller.getElementRef().nativeElement.addEventListener('scroll', this.bodyScrollListener);else this.scrollBodyViewChild.nativeElement.addEventListener('scroll', this.bodyScrollListener);\n      }\n    });\n  }\n\n  unbindEvents() {\n    if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n      this.scrollHeaderBoxViewChild.nativeElement.removeEventListener('scroll', this.headerScrollListener);\n    }\n\n    if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n      this.scrollFooterViewChild.nativeElement.removeEventListener('scroll', this.footerScrollListener);\n    }\n\n    if (this.scrollBodyViewChild && this.scrollBodyViewChild.nativeElement) {\n      this.scrollBodyViewChild.nativeElement.removeEventListener('scroll', this.bodyScrollListener);\n    }\n\n    if (this.scroller && this.scroller.getElementRef()) {\n      this.scroller.getElementRef().nativeElement.removeEventListener('scroll', this.bodyScrollListener);\n    }\n  }\n\n  onHeaderScroll() {\n    const scrollLeft = this.scrollHeaderViewChild.nativeElement.scrollLeft;\n    this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n\n    if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n      this.scrollFooterViewChild.nativeElement.scrollLeft = scrollLeft;\n    }\n\n    this.preventBodyScrollPropagation = true;\n  }\n\n  onFooterScroll() {\n    const scrollLeft = this.scrollFooterViewChild.nativeElement.scrollLeft;\n    this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n\n    if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n      this.scrollHeaderViewChild.nativeElement.scrollLeft = scrollLeft;\n    }\n\n    this.preventBodyScrollPropagation = true;\n  }\n\n  onBodyScroll(event) {\n    if (this.preventBodyScrollPropagation) {\n      this.preventBodyScrollPropagation = false;\n      return;\n    }\n\n    if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n      this.scrollHeaderBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n    }\n\n    if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n      this.scrollFooterBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n    }\n\n    if (this.frozenSiblingBody) {\n      this.frozenSiblingBody.scrollTop = event.target.scrollTop;\n    }\n  }\n\n  scrollToVirtualIndex(index) {\n    if (this.scroller) {\n      this.scroller.scrollToIndex(index);\n    }\n  }\n\n  scrollTo(options) {\n    if (this.scroller) {\n      this.scroller.scrollTo(options);\n    } else {\n      if (this.scrollBodyViewChild.nativeElement.scrollTo) {\n        this.scrollBodyViewChild.nativeElement.scrollTo(options);\n      } else {\n        this.scrollBodyViewChild.nativeElement.scrollLeft = options.left;\n        this.scrollBodyViewChild.nativeElement.scrollTop = options.top;\n      }\n    }\n  }\n\n  ngOnDestroy() {\n    this.unbindEvents();\n    this.frozenSiblingBody = null;\n  }\n\n}\n\nTTScrollableView.ɵfac = function TTScrollableView_Factory(t) {\n  return new (t || TTScrollableView)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nTTScrollableView.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TTScrollableView,\n  selectors: [[\"\", \"ttScrollableView\", \"\"]],\n  viewQuery: function TTScrollableView_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c14, 5);\n      i0.ɵɵviewQuery(_c15, 5);\n      i0.ɵɵviewQuery(_c16, 5);\n      i0.ɵɵviewQuery(_c17, 5);\n      i0.ɵɵviewQuery(_c18, 5);\n      i0.ɵɵviewQuery(_c19, 5);\n      i0.ɵɵviewQuery(_c20, 5);\n      i0.ɵɵviewQuery(_c21, 5);\n      i0.ɵɵviewQuery(_c22, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollHeaderViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollHeaderBoxViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollBodyViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollTableViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollLoadingTableViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollFooterViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollFooterBoxViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollableAlignerViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    columns: [\"ttScrollableView\", \"columns\"],\n    frozen: \"frozen\",\n    scrollHeight: \"scrollHeight\"\n  },\n  attrs: _c23,\n  decls: 13,\n  vars: 13,\n  consts: [[1, \"p-treetable-scrollable-header\"], [\"scrollHeader\", \"\"], [1, \"p-treetable-scrollable-header-box\"], [\"scrollHeaderBox\", \"\"], [1, \"p-treetable-scrollable-header-table\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-treetable-thead\"], [\"styleClass\", \"p-treetable-scrollable-body\", 3, \"items\", \"style\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"buildInItems\", \"\"], [\"class\", \"p-treetable-scrollable-footer\", 4, \"ngIf\"], [\"styleClass\", \"p-treetable-scrollable-body\", 3, \"items\", \"scrollHeight\", \"itemSize\", \"lazy\", \"options\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [1, \"p-treetable-scrollable-body\", 3, \"ngStyle\"], [\"scrollBody\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"scrollTable\", \"\"], [1, \"p-treetable-tbody\", 3, \"pTreeTableBody\", \"pTreeTableBodyTemplate\", \"serializedNodes\", \"frozen\"], [\"style\", \"background-color:transparent\", 4, \"ngIf\"], [2, \"background-color\", \"transparent\"], [\"scrollableAligner\", \"\"], [1, \"p-treetable-scrollable-footer\"], [\"scrollFooter\", \"\"], [1, \"p-treetable-scrollable-footer-box\"], [\"scrollFooterBox\", \"\"], [1, \"p-treetable-scrollable-footer-table\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-treetable-tfoot\"]],\n  template: function TTScrollableView_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1)(2, \"div\", 2, 3)(4, \"table\", 4);\n      i0.ɵɵtemplate(5, TTScrollableView_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n      i0.ɵɵelementStart(6, \"thead\", 6);\n      i0.ɵɵtemplate(7, TTScrollableView_ng_container_7_Template, 1, 0, \"ng-container\", 5);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵtemplate(8, TTScrollableView_p_scroller_8_Template, 4, 10, \"p-scroller\", 7);\n      i0.ɵɵtemplate(9, TTScrollableView_ng_container_9_Template, 4, 10, \"ng-container\", 8);\n      i0.ɵɵtemplate(10, TTScrollableView_ng_template_10_Template, 5, 15, \"ng-template\", null, 9, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(12, TTScrollableView_div_12_Template, 8, 10, \"div\", 10);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"ngClass\", ctx.tt.tableStyleClass)(\"ngStyle\", ctx.tt.tableStyle);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.frozen ? ctx.tt.frozenColGroupTemplate || ctx.tt.colGroupTemplate : ctx.tt.colGroupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(9, _c7, ctx.columns));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.frozen ? ctx.tt.frozenHeaderTemplate || ctx.tt.headerTemplate : ctx.tt.headerTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(11, _c7, ctx.columns));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.tt.virtualScroll);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.tt.virtualScroll);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.tt.footerTemplate);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i1.PrimeTemplate, i4.Scroller, TTBody],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTScrollableView, [{\n    type: Component,\n    args: [{\n      selector: '[ttScrollableView]',\n      template: `\n        <div #scrollHeader class=\"p-treetable-scrollable-header\">\n            <div #scrollHeaderBox class=\"p-treetable-scrollable-header-box\">\n                <table class=\"p-treetable-scrollable-header-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenHeaderTemplate||tt.headerTemplate : tt.headerTemplate; context {$implicit: columns}\"></ng-container>\n                    </thead>\n                </table>\n            </div>\n        </div>\n\n        <p-scroller *ngIf=\"tt.virtualScroll\" #scroller [items]=\"tt.serializedValue\" styleClass=\"p-treetable-scrollable-body\" [style]=\"{'height': tt.scrollHeight !== 'flex' ? tt.scrollHeight : undefined}\" [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\" [itemSize]=\"tt.virtualScrollItemSize||tt._virtualRowHeight\"\n            [lazy]=\"tt.lazy\" (onLazyLoad)=\"tt.onLazyItemLoad($event)\" [options]=\"tt.virtualScrollOptions\">\n            <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n            </ng-template>\n            <ng-container *ngIf=\"loaderTemplate\">\n                <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                    <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                </ng-template>\n            </ng-container>\n        </p-scroller>\n        <ng-container *ngIf=\"!tt.virtualScroll\">\n            <div #scrollBody class=\"p-treetable-scrollable-body\" [ngStyle]=\"{'max-height': tt.scrollHeight !== 'flex' ? scrollHeight : undefined, 'overflow-y': !frozen && tt.scrollHeight ? 'scroll' : undefined}\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: serializedValue, options: {}}\"></ng-container>\n            </div>\n        </ng-container>\n\n        <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n            <table #scrollTable [class]=\"tt.tableStyleClass\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"tt.tableStyle\" [style]=\"scrollerOptions.contentStyle\">\n                <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"frozen ? tt.frozenBodyTemplate||tt.bodyTemplate : tt.bodyTemplate\" [serializedNodes]=\"items\" [frozen]=\"frozen\"></tbody>\n            </table>\n            <div #scrollableAligner style=\"background-color:transparent\" *ngIf=\"frozen\"></div>\n        </ng-template>\n\n        <div #scrollFooter *ngIf=\"tt.footerTemplate\" class=\"p-treetable-scrollable-footer\">\n            <div #scrollFooterBox class=\"p-treetable-scrollable-footer-box\">\n                <table class=\"p-treetable-scrollable-footer-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenFooterTemplate||tt.footerTemplate : tt.footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    columns: [{\n      type: Input,\n      args: [\"ttScrollableView\"]\n    }],\n    frozen: [{\n      type: Input\n    }],\n    scrollHeaderViewChild: [{\n      type: ViewChild,\n      args: ['scrollHeader']\n    }],\n    scrollHeaderBoxViewChild: [{\n      type: ViewChild,\n      args: ['scrollHeaderBox']\n    }],\n    scrollBodyViewChild: [{\n      type: ViewChild,\n      args: ['scrollBody']\n    }],\n    scrollTableViewChild: [{\n      type: ViewChild,\n      args: ['scrollTable']\n    }],\n    scrollLoadingTableViewChild: [{\n      type: ViewChild,\n      args: ['loadingTable']\n    }],\n    scrollFooterViewChild: [{\n      type: ViewChild,\n      args: ['scrollFooter']\n    }],\n    scrollFooterBoxViewChild: [{\n      type: ViewChild,\n      args: ['scrollFooterBox']\n    }],\n    scrollableAlignerViewChild: [{\n      type: ViewChild,\n      args: ['scrollableAligner']\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: ['scroller']\n    }],\n    scrollHeight: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TTSortableColumn {\n  constructor(tt) {\n    this.tt = tt;\n\n    if (this.isEnabled()) {\n      this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n        this.updateSortState();\n      });\n    }\n  }\n\n  ngOnInit() {\n    if (this.isEnabled()) {\n      this.updateSortState();\n    }\n  }\n\n  updateSortState() {\n    this.sorted = this.tt.isSorted(this.field);\n  }\n\n  onClick(event) {\n    if (this.isEnabled()) {\n      this.updateSortState();\n      this.tt.sort({\n        originalEvent: event,\n        field: this.field\n      });\n      DomHandler.clearSelection();\n    }\n  }\n\n  onEnterKey(event) {\n    this.onClick(event);\n  }\n\n  isEnabled() {\n    return this.ttSortableColumnDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTSortableColumn.ɵfac = function TTSortableColumn_Factory(t) {\n  return new (t || TTSortableColumn)(i0.ɵɵdirectiveInject(TreeTable));\n};\n\nTTSortableColumn.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTSortableColumn,\n  selectors: [[\"\", \"ttSortableColumn\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 6,\n  hostBindings: function TTSortableColumn_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function TTSortableColumn_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      })(\"keydown.enter\", function TTSortableColumn_keydown_enter_HostBindingHandler($event) {\n        return ctx.onEnterKey($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? \"0\" : null)(\"role\", \"columnheader\");\n      i0.ɵɵclassProp(\"p-sortable-column\", ctx.isEnabled())(\"p-highlight\", ctx.sorted);\n    }\n  },\n  inputs: {\n    field: [\"ttSortableColumn\", \"field\"],\n    ttSortableColumnDisabled: \"ttSortableColumnDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTSortableColumn, [{\n    type: Directive,\n    args: [{\n      selector: '[ttSortableColumn]',\n      host: {\n        'class': 'p-element',\n        '[class.p-sortable-column]': 'isEnabled()',\n        '[class.p-highlight]': 'sorted',\n        '[attr.tabindex]': 'isEnabled() ? \"0\" : null',\n        '[attr.role]': '\"columnheader\"'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }];\n  }, {\n    field: [{\n      type: Input,\n      args: [\"ttSortableColumn\"]\n    }],\n    ttSortableColumnDisabled: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onEnterKey: [{\n      type: HostListener,\n      args: ['keydown.enter', ['$event']]\n    }]\n  });\n})();\n\nclass TTSortIcon {\n  constructor(tt, cd) {\n    this.tt = tt;\n    this.cd = cd;\n    this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n      this.updateSortState();\n      this.cd.markForCheck();\n    });\n  }\n\n  ngOnInit() {\n    this.updateSortState();\n  }\n\n  onClick(event) {\n    event.preventDefault();\n  }\n\n  updateSortState() {\n    if (this.tt.sortMode === 'single') {\n      this.sortOrder = this.tt.isSorted(this.field) ? this.tt.sortOrder : 0;\n    } else if (this.tt.sortMode === 'multiple') {\n      let sortMeta = this.tt.getSortMeta(this.field);\n      this.sortOrder = sortMeta ? sortMeta.order : 0;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTSortIcon.ɵfac = function TTSortIcon_Factory(t) {\n  return new (t || TTSortIcon)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTTSortIcon.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TTSortIcon,\n  selectors: [[\"p-treeTableSortIcon\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    field: \"field\",\n    ariaLabelDesc: \"ariaLabelDesc\",\n    ariaLabelAsc: \"ariaLabelAsc\"\n  },\n  decls: 1,\n  vars: 5,\n  consts: [[1, \"p-sortable-column-icon\", \"pi\", \"pi-fw\", 3, \"ngClass\"]],\n  template: function TTSortIcon_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"i\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(1, _c29, ctx.sortOrder === 1, ctx.sortOrder === -1, ctx.sortOrder === 0));\n    }\n  },\n  dependencies: [i2.NgClass],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTSortIcon, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTableSortIcon',\n      template: `\n        <i class=\"p-sortable-column-icon pi pi-fw\" [ngClass]=\"{'pi-sort-amount-up-alt': sortOrder === 1, 'pi-sort-amount-down': sortOrder === -1, 'pi-sort-alt': sortOrder === 0}\"></i>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    field: [{\n      type: Input\n    }],\n    ariaLabelDesc: [{\n      type: Input\n    }],\n    ariaLabelAsc: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TTResizableColumn {\n  constructor(tt, el, zone) {\n    this.tt = tt;\n    this.el = el;\n    this.zone = zone;\n  }\n\n  ngAfterViewInit() {\n    if (this.isEnabled()) {\n      DomHandler.addClass(this.el.nativeElement, 'p-resizable-column');\n      this.resizer = document.createElement('span');\n      this.resizer.className = 'p-column-resizer';\n      this.el.nativeElement.appendChild(this.resizer);\n      this.zone.runOutsideAngular(() => {\n        this.resizerMouseDownListener = this.onMouseDown.bind(this);\n        this.resizer.addEventListener('mousedown', this.resizerMouseDownListener);\n      });\n    }\n  }\n\n  bindDocumentEvents() {\n    this.zone.runOutsideAngular(() => {\n      this.documentMouseMoveListener = this.onDocumentMouseMove.bind(this);\n      document.addEventListener('mousemove', this.documentMouseMoveListener);\n      this.documentMouseUpListener = this.onDocumentMouseUp.bind(this);\n      document.addEventListener('mouseup', this.documentMouseUpListener);\n    });\n  }\n\n  unbindDocumentEvents() {\n    if (this.documentMouseMoveListener) {\n      document.removeEventListener('mousemove', this.documentMouseMoveListener);\n      this.documentMouseMoveListener = null;\n    }\n\n    if (this.documentMouseUpListener) {\n      document.removeEventListener('mouseup', this.documentMouseUpListener);\n      this.documentMouseUpListener = null;\n    }\n  }\n\n  onMouseDown(event) {\n    this.tt.onColumnResizeBegin(event);\n    this.bindDocumentEvents();\n  }\n\n  onDocumentMouseMove(event) {\n    this.tt.onColumnResize(event);\n  }\n\n  onDocumentMouseUp(event) {\n    this.tt.onColumnResizeEnd(event, this.el.nativeElement);\n    this.unbindDocumentEvents();\n  }\n\n  isEnabled() {\n    return this.ttResizableColumnDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    if (this.resizerMouseDownListener) {\n      this.resizer.removeEventListener('mousedown', this.resizerMouseDownListener);\n    }\n\n    this.unbindDocumentEvents();\n  }\n\n}\n\nTTResizableColumn.ɵfac = function TTResizableColumn_Factory(t) {\n  return new (t || TTResizableColumn)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nTTResizableColumn.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTResizableColumn,\n  selectors: [[\"\", \"ttResizableColumn\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    ttResizableColumnDisabled: \"ttResizableColumnDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTResizableColumn, [{\n    type: Directive,\n    args: [{\n      selector: '[ttResizableColumn]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    ttResizableColumnDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TTReorderableColumn {\n  constructor(tt, el, zone) {\n    this.tt = tt;\n    this.el = el;\n    this.zone = zone;\n  }\n\n  ngAfterViewInit() {\n    if (this.isEnabled()) {\n      this.bindEvents();\n    }\n  }\n\n  bindEvents() {\n    this.zone.runOutsideAngular(() => {\n      this.mouseDownListener = this.onMouseDown.bind(this);\n      this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n      this.dragStartListener = this.onDragStart.bind(this);\n      this.el.nativeElement.addEventListener('dragstart', this.dragStartListener);\n      this.dragOverListener = this.onDragEnter.bind(this);\n      this.el.nativeElement.addEventListener('dragover', this.dragOverListener);\n      this.dragEnterListener = this.onDragEnter.bind(this);\n      this.el.nativeElement.addEventListener('dragenter', this.dragEnterListener);\n      this.dragLeaveListener = this.onDragLeave.bind(this);\n      this.el.nativeElement.addEventListener('dragleave', this.dragLeaveListener);\n    });\n  }\n\n  unbindEvents() {\n    if (this.mouseDownListener) {\n      document.removeEventListener('mousedown', this.mouseDownListener);\n      this.mouseDownListener = null;\n    }\n\n    if (this.dragOverListener) {\n      document.removeEventListener('dragover', this.dragOverListener);\n      this.dragOverListener = null;\n    }\n\n    if (this.dragEnterListener) {\n      document.removeEventListener('dragenter', this.dragEnterListener);\n      this.dragEnterListener = null;\n    }\n\n    if (this.dragEnterListener) {\n      document.removeEventListener('dragenter', this.dragEnterListener);\n      this.dragEnterListener = null;\n    }\n\n    if (this.dragLeaveListener) {\n      document.removeEventListener('dragleave', this.dragLeaveListener);\n      this.dragLeaveListener = null;\n    }\n  }\n\n  onMouseDown(event) {\n    if (event.target.nodeName === 'INPUT' || event.target.nodeName === 'TEXTAREA' || DomHandler.hasClass(event.target, 'p-column-resizer')) this.el.nativeElement.draggable = false;else this.el.nativeElement.draggable = true;\n  }\n\n  onDragStart(event) {\n    this.tt.onColumnDragStart(event, this.el.nativeElement);\n  }\n\n  onDragOver(event) {\n    event.preventDefault();\n  }\n\n  onDragEnter(event) {\n    this.tt.onColumnDragEnter(event, this.el.nativeElement);\n  }\n\n  onDragLeave(event) {\n    this.tt.onColumnDragLeave(event);\n  }\n\n  onDrop(event) {\n    if (this.isEnabled()) {\n      this.tt.onColumnDrop(event, this.el.nativeElement);\n    }\n  }\n\n  isEnabled() {\n    return this.ttReorderableColumnDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    this.unbindEvents();\n  }\n\n}\n\nTTReorderableColumn.ɵfac = function TTReorderableColumn_Factory(t) {\n  return new (t || TTReorderableColumn)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nTTReorderableColumn.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTReorderableColumn,\n  selectors: [[\"\", \"ttReorderableColumn\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostBindings: function TTReorderableColumn_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"drop\", function TTReorderableColumn_drop_HostBindingHandler($event) {\n        return ctx.onDrop($event);\n      });\n    }\n  },\n  inputs: {\n    ttReorderableColumnDisabled: \"ttReorderableColumnDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTReorderableColumn, [{\n    type: Directive,\n    args: [{\n      selector: '[ttReorderableColumn]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    ttReorderableColumnDisabled: [{\n      type: Input\n    }],\n    onDrop: [{\n      type: HostListener,\n      args: ['drop', ['$event']]\n    }]\n  });\n})();\n\nclass TTSelectableRow {\n  constructor(tt, tableService) {\n    this.tt = tt;\n    this.tableService = tableService;\n\n    if (this.isEnabled()) {\n      this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n        this.selected = this.tt.isSelected(this.rowNode.node);\n      });\n    }\n  }\n\n  ngOnInit() {\n    if (this.isEnabled()) {\n      this.selected = this.tt.isSelected(this.rowNode.node);\n    }\n  }\n\n  onClick(event) {\n    if (this.isEnabled()) {\n      this.tt.handleRowClick({\n        originalEvent: event,\n        rowNode: this.rowNode\n      });\n    }\n  }\n\n  onEnterKey(event) {\n    if (event.which === 13) {\n      this.onClick(event);\n    }\n  }\n\n  onTouchEnd(event) {\n    if (this.isEnabled()) {\n      this.tt.handleRowTouchEnd(event);\n    }\n  }\n\n  isEnabled() {\n    return this.ttSelectableRowDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTSelectableRow.ɵfac = function TTSelectableRow_Factory(t) {\n  return new (t || TTSelectableRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService));\n};\n\nTTSelectableRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTSelectableRow,\n  selectors: [[\"\", \"ttSelectableRow\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 2,\n  hostBindings: function TTSelectableRow_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function TTSelectableRow_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      })(\"keydown\", function TTSelectableRow_keydown_HostBindingHandler($event) {\n        return ctx.onEnterKey($event);\n      })(\"touchend\", function TTSelectableRow_touchend_HostBindingHandler($event) {\n        return ctx.onTouchEnd($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-highlight\", ctx.selected);\n    }\n  },\n  inputs: {\n    rowNode: [\"ttSelectableRow\", \"rowNode\"],\n    ttSelectableRowDisabled: \"ttSelectableRowDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTSelectableRow, [{\n    type: Directive,\n    args: [{\n      selector: '[ttSelectableRow]',\n      host: {\n        'class': 'p-element',\n        '[class.p-highlight]': 'selected'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }];\n  }, {\n    rowNode: [{\n      type: Input,\n      args: [\"ttSelectableRow\"]\n    }],\n    ttSelectableRowDisabled: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onEnterKey: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }],\n    onTouchEnd: [{\n      type: HostListener,\n      args: ['touchend', ['$event']]\n    }]\n  });\n})();\n\nclass TTSelectableRowDblClick {\n  constructor(tt, tableService) {\n    this.tt = tt;\n    this.tableService = tableService;\n\n    if (this.isEnabled()) {\n      this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n        this.selected = this.tt.isSelected(this.rowNode.node);\n      });\n    }\n  }\n\n  ngOnInit() {\n    if (this.isEnabled()) {\n      this.selected = this.tt.isSelected(this.rowNode.node);\n    }\n  }\n\n  onClick(event) {\n    if (this.isEnabled()) {\n      this.tt.handleRowClick({\n        originalEvent: event,\n        rowNode: this.rowNode\n      });\n    }\n  }\n\n  isEnabled() {\n    return this.ttSelectableRowDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTSelectableRowDblClick.ɵfac = function TTSelectableRowDblClick_Factory(t) {\n  return new (t || TTSelectableRowDblClick)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService));\n};\n\nTTSelectableRowDblClick.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTSelectableRowDblClick,\n  selectors: [[\"\", \"ttSelectableRowDblClick\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 2,\n  hostBindings: function TTSelectableRowDblClick_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"dblclick\", function TTSelectableRowDblClick_dblclick_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-highlight\", ctx.selected);\n    }\n  },\n  inputs: {\n    rowNode: [\"ttSelectableRowDblClick\", \"rowNode\"],\n    ttSelectableRowDisabled: \"ttSelectableRowDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTSelectableRowDblClick, [{\n    type: Directive,\n    args: [{\n      selector: '[ttSelectableRowDblClick]',\n      host: {\n        'class': 'p-element',\n        '[class.p-highlight]': 'selected'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }];\n  }, {\n    rowNode: [{\n      type: Input,\n      args: [\"ttSelectableRowDblClick\"]\n    }],\n    ttSelectableRowDisabled: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['dblclick', ['$event']]\n    }]\n  });\n})();\n\nclass TTContextMenuRow {\n  constructor(tt, tableService, el) {\n    this.tt = tt;\n    this.tableService = tableService;\n    this.el = el;\n\n    if (this.isEnabled()) {\n      this.subscription = this.tt.tableService.contextMenuSource$.subscribe(node => {\n        this.selected = this.tt.equals(this.rowNode.node, node);\n      });\n    }\n  }\n\n  onContextMenu(event) {\n    if (this.isEnabled()) {\n      this.tt.handleRowRightClick({\n        originalEvent: event,\n        rowNode: this.rowNode\n      });\n      this.el.nativeElement.focus();\n      event.preventDefault();\n    }\n  }\n\n  isEnabled() {\n    return this.ttContextMenuRowDisabled !== true;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTContextMenuRow.ɵfac = function TTContextMenuRow_Factory(t) {\n  return new (t || TTContextMenuRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nTTContextMenuRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTContextMenuRow,\n  selectors: [[\"\", \"ttContextMenuRow\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 3,\n  hostBindings: function TTContextMenuRow_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"contextmenu\", function TTContextMenuRow_contextmenu_HostBindingHandler($event) {\n        return ctx.onContextMenu($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", ctx.isEnabled() ? 0 : undefined);\n      i0.ɵɵclassProp(\"p-highlight-contextmenu\", ctx.selected);\n    }\n  },\n  inputs: {\n    rowNode: [\"ttContextMenuRow\", \"rowNode\"],\n    ttContextMenuRowDisabled: \"ttContextMenuRowDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTContextMenuRow, [{\n    type: Directive,\n    args: [{\n      selector: '[ttContextMenuRow]',\n      host: {\n        'class': 'p-element',\n        '[class.p-highlight-contextmenu]': 'selected',\n        '[attr.tabindex]': 'isEnabled() ? 0 : undefined'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    rowNode: [{\n      type: Input,\n      args: [\"ttContextMenuRow\"]\n    }],\n    ttContextMenuRowDisabled: [{\n      type: Input\n    }],\n    onContextMenu: [{\n      type: HostListener,\n      args: ['contextmenu', ['$event']]\n    }]\n  });\n})();\n\nclass TTCheckbox {\n  constructor(tt, tableService, cd) {\n    this.tt = tt;\n    this.tableService = tableService;\n    this.cd = cd;\n    this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n      this.checked = this.tt.isSelected(this.rowNode.node);\n      this.cd.markForCheck();\n    });\n  }\n\n  ngOnInit() {\n    this.checked = this.tt.isSelected(this.rowNode.node);\n  }\n\n  onClick(event) {\n    if (!this.disabled) {\n      this.tt.toggleNodeWithCheckbox({\n        originalEvent: event,\n        rowNode: this.rowNode\n      });\n    }\n\n    DomHandler.clearSelection();\n  }\n\n  onFocus() {\n    this.focused = true;\n  }\n\n  onBlur() {\n    this.focused = false;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nTTCheckbox.ɵfac = function TTCheckbox_Factory(t) {\n  return new (t || TTCheckbox)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTTCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TTCheckbox,\n  selectors: [[\"p-treeTableCheckbox\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    disabled: \"disabled\",\n    rowNode: [\"value\", \"rowNode\"]\n  },\n  decls: 6,\n  vars: 15,\n  consts: [[1, \"p-checkbox\", \"p-component\", 3, \"ngClass\", \"click\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"checked\", \"focus\", \"blur\"], [\"role\", \"checkbox\", 3, \"ngClass\"], [\"box\", \"\"], [1, \"p-checkbox-icon\", \"pi\", 3, \"ngClass\"]],\n  template: function TTCheckbox_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function TTCheckbox_Template_div_click_0_listener($event) {\n        return ctx.onClick($event);\n      });\n      i0.ɵɵelementStart(1, \"div\", 1)(2, \"input\", 2);\n      i0.ɵɵlistener(\"focus\", function TTCheckbox_Template_input_focus_2_listener() {\n        return ctx.onFocus();\n      })(\"blur\", function TTCheckbox_Template_input_blur_2_listener() {\n        return ctx.onBlur();\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(3, \"div\", 3, 4);\n      i0.ɵɵelement(5, \"span\", 5);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c30, ctx.focused));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"checked\", ctx.checked);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(7, _c31, ctx.checked, ctx.focused, ctx.rowNode.node.partialSelected, ctx.disabled));\n      i0.ɵɵattribute(\"aria-checked\", ctx.checked);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c32, ctx.checked, ctx.rowNode.node.partialSelected));\n    }\n  },\n  dependencies: [i2.NgClass],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTableCheckbox',\n      template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event)\">\n            <div class=\"p-hidden-accessible\">\n                <input type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-indeterminate': rowNode.node.partialSelected, 'p-disabled':disabled}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':checked, 'pi-minus': rowNode.node.partialSelected}\"></span>\n            </div>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    disabled: [{\n      type: Input\n    }],\n    rowNode: [{\n      type: Input,\n      args: [\"value\"]\n    }]\n  });\n})();\n\nclass TTHeaderCheckbox {\n  constructor(tt, tableService, cd) {\n    this.tt = tt;\n    this.tableService = tableService;\n    this.cd = cd;\n    this.valueChangeSubscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n      this.checked = this.updateCheckedState();\n    });\n    this.selectionChangeSubscription = this.tt.tableService.selectionSource$.subscribe(() => {\n      this.checked = this.updateCheckedState();\n    });\n  }\n\n  ngOnInit() {\n    this.checked = this.updateCheckedState();\n  }\n\n  onClick(event, checked) {\n    if (this.tt.value && this.tt.value.length > 0) {\n      this.tt.toggleNodesWithCheckbox(event, !checked);\n    }\n\n    DomHandler.clearSelection();\n  }\n\n  onFocus() {\n    this.focused = true;\n  }\n\n  onBlur() {\n    this.focused = false;\n  }\n\n  ngOnDestroy() {\n    if (this.selectionChangeSubscription) {\n      this.selectionChangeSubscription.unsubscribe();\n    }\n\n    if (this.valueChangeSubscription) {\n      this.valueChangeSubscription.unsubscribe();\n    }\n  }\n\n  updateCheckedState() {\n    this.cd.markForCheck();\n    let checked;\n    const data = this.tt.filteredNodes || this.tt.value;\n\n    if (data) {\n      for (let node of data) {\n        if (this.tt.isSelected(node)) {\n          checked = true;\n        } else {\n          checked = false;\n          break;\n        }\n      }\n    } else {\n      checked = false;\n    }\n\n    return checked;\n  }\n\n}\n\nTTHeaderCheckbox.ɵfac = function TTHeaderCheckbox_Factory(t) {\n  return new (t || TTHeaderCheckbox)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TreeTableService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nTTHeaderCheckbox.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TTHeaderCheckbox,\n  selectors: [[\"p-treeTableHeaderCheckbox\"]],\n  viewQuery: function TTHeaderCheckbox_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c33, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.boxViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  decls: 7,\n  vars: 14,\n  consts: [[1, \"p-checkbox\", \"p-component\", 3, \"ngClass\", \"click\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", 3, \"checked\", \"disabled\", \"focus\", \"blur\"], [\"cb\", \"\"], [\"role\", \"checkbox\", 3, \"ngClass\"], [\"box\", \"\"], [1, \"p-checkbox-icon\", 3, \"ngClass\"]],\n  template: function TTHeaderCheckbox_Template(rf, ctx) {\n    if (rf & 1) {\n      const _r2 = i0.ɵɵgetCurrentView();\n\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵlistener(\"click\", function TTHeaderCheckbox_Template_div_click_0_listener($event) {\n        i0.ɵɵrestoreView(_r2);\n\n        const _r0 = i0.ɵɵreference(3);\n\n        return i0.ɵɵresetView(ctx.onClick($event, _r0.checked));\n      });\n      i0.ɵɵelementStart(1, \"div\", 1)(2, \"input\", 2, 3);\n      i0.ɵɵlistener(\"focus\", function TTHeaderCheckbox_Template_input_focus_2_listener() {\n        return ctx.onFocus();\n      })(\"blur\", function TTHeaderCheckbox_Template_input_blur_2_listener() {\n        return ctx.onBlur();\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(4, \"div\", 4, 5);\n      i0.ɵɵelement(6, \"span\", 6);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c30, ctx.focused));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", !ctx.tt.value || ctx.tt.value.length === 0);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(8, _c34, ctx.checked, ctx.focused, !ctx.tt.value || ctx.tt.value.length === 0));\n      i0.ɵɵattribute(\"aria-checked\", ctx.checked);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c35, ctx.checked));\n    }\n  },\n  dependencies: [i2.NgClass],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTHeaderCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTableHeaderCheckbox',\n      template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event, cb.checked)\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\" [disabled]=\"!tt.value||tt.value.length === 0\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-disabled': (!tt.value || tt.value.length === 0)}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"{'pi pi-check':checked}\"></span>\n            </div>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TreeTableService\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    boxViewChild: [{\n      type: ViewChild,\n      args: ['box']\n    }]\n  });\n})();\n\nclass TTEditableColumn {\n  constructor(tt, el, zone) {\n    this.tt = tt;\n    this.el = el;\n    this.zone = zone;\n  }\n\n  ngAfterViewInit() {\n    if (this.isEnabled()) {\n      DomHandler.addClass(this.el.nativeElement, 'p-editable-column');\n    }\n  }\n\n  onClick(event) {\n    if (this.isEnabled()) {\n      this.tt.editingCellClick = true;\n\n      if (this.tt.editingCell) {\n        if (this.tt.editingCell !== this.el.nativeElement) {\n          if (!this.tt.isEditingCellValid()) {\n            return;\n          }\n\n          DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n          this.openCell();\n        }\n      } else {\n        this.openCell();\n      }\n    }\n  }\n\n  openCell() {\n    this.tt.updateEditingCell(this.el.nativeElement, this.data, this.field);\n    DomHandler.addClass(this.el.nativeElement, 'p-cell-editing');\n    this.tt.onEditInit.emit({\n      field: this.field,\n      data: this.data\n    });\n    this.tt.editingCellClick = true;\n    this.zone.runOutsideAngular(() => {\n      setTimeout(() => {\n        let focusable = DomHandler.findSingle(this.el.nativeElement, 'input, textarea');\n\n        if (focusable) {\n          focusable.focus();\n        }\n      }, 50);\n    });\n  }\n\n  closeEditingCell() {\n    DomHandler.removeClass(this.tt.editingCell, 'p-checkbox-icon');\n    this.tt.editingCell = null;\n    this.tt.unbindDocumentEditListener();\n  }\n\n  onKeyDown(event) {\n    if (this.isEnabled()) {\n      //enter\n      if (event.keyCode == 13) {\n        if (this.tt.isEditingCellValid()) {\n          DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n          this.closeEditingCell();\n          this.tt.onEditComplete.emit({\n            field: this.field,\n            data: this.data\n          });\n        }\n\n        event.preventDefault();\n      } //escape\n      else if (event.keyCode == 27) {\n        if (this.tt.isEditingCellValid()) {\n          DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n          this.closeEditingCell();\n          this.tt.onEditCancel.emit({\n            field: this.field,\n            data: this.data\n          });\n        }\n\n        event.preventDefault();\n      } //tab\n      else if (event.keyCode == 9) {\n        this.tt.onEditComplete.emit({\n          field: this.field,\n          data: this.data\n        });\n        if (event.shiftKey) this.moveToPreviousCell(event);else this.moveToNextCell(event);\n      }\n    }\n  }\n\n  findCell(element) {\n    if (element) {\n      let cell = element;\n\n      while (cell && !DomHandler.hasClass(cell, 'p-cell-editing')) {\n        cell = cell.parentElement;\n      }\n\n      return cell;\n    } else {\n      return null;\n    }\n  }\n\n  moveToPreviousCell(event) {\n    let currentCell = this.findCell(event.target);\n    let row = currentCell.parentElement;\n    let targetCell = this.findPreviousEditableColumn(currentCell);\n\n    if (targetCell) {\n      DomHandler.invokeElementMethod(targetCell, 'click');\n      event.preventDefault();\n    }\n  }\n\n  moveToNextCell(event) {\n    let currentCell = this.findCell(event.target);\n    let row = currentCell.parentElement;\n    let targetCell = this.findNextEditableColumn(currentCell);\n\n    if (targetCell) {\n      DomHandler.invokeElementMethod(targetCell, 'click');\n      event.preventDefault();\n    }\n  }\n\n  findPreviousEditableColumn(cell) {\n    let prevCell = cell.previousElementSibling;\n\n    if (!prevCell) {\n      let previousRow = cell.parentElement ? cell.parentElement.previousElementSibling : null;\n\n      if (previousRow) {\n        prevCell = previousRow.lastElementChild;\n      }\n    }\n\n    if (prevCell) {\n      if (DomHandler.hasClass(prevCell, 'p-editable-column')) return prevCell;else return this.findPreviousEditableColumn(prevCell);\n    } else {\n      return null;\n    }\n  }\n\n  findNextEditableColumn(cell) {\n    let nextCell = cell.nextElementSibling;\n\n    if (!nextCell) {\n      let nextRow = cell.parentElement ? cell.parentElement.nextElementSibling : null;\n\n      if (nextRow) {\n        nextCell = nextRow.firstElementChild;\n      }\n    }\n\n    if (nextCell) {\n      if (DomHandler.hasClass(nextCell, 'p-editable-column')) return nextCell;else return this.findNextEditableColumn(nextCell);\n    } else {\n      return null;\n    }\n  }\n\n  isEnabled() {\n    return this.ttEditableColumnDisabled !== true;\n  }\n\n}\n\nTTEditableColumn.ɵfac = function TTEditableColumn_Factory(t) {\n  return new (t || TTEditableColumn)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nTTEditableColumn.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTEditableColumn,\n  selectors: [[\"\", \"ttEditableColumn\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostBindings: function TTEditableColumn_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"click\", function TTEditableColumn_click_HostBindingHandler($event) {\n        return ctx.onClick($event);\n      })(\"keydown\", function TTEditableColumn_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n  },\n  inputs: {\n    data: [\"ttEditableColumn\", \"data\"],\n    field: [\"ttEditableColumnField\", \"field\"],\n    ttEditableColumnDisabled: \"ttEditableColumnDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTEditableColumn, [{\n    type: Directive,\n    args: [{\n      selector: '[ttEditableColumn]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    data: [{\n      type: Input,\n      args: [\"ttEditableColumn\"]\n    }],\n    field: [{\n      type: Input,\n      args: [\"ttEditableColumnField\"]\n    }],\n    ttEditableColumnDisabled: [{\n      type: Input\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\n\nclass TreeTableCellEditor {\n  constructor(tt, editableColumn) {\n    this.tt = tt;\n    this.editableColumn = editableColumn;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'input':\n          this.inputTemplate = item.template;\n          break;\n\n        case 'output':\n          this.outputTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n}\n\nTreeTableCellEditor.ɵfac = function TreeTableCellEditor_Factory(t) {\n  return new (t || TreeTableCellEditor)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(TTEditableColumn));\n};\n\nTreeTableCellEditor.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TreeTableCellEditor,\n  selectors: [[\"p-treeTableCellEditor\"]],\n  contentQueries: function TreeTableCellEditor_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  decls: 2,\n  vars: 2,\n  consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\"]],\n  template: function TreeTableCellEditor_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, TreeTableCellEditor_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, TreeTableCellEditor_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.tt.editingCell === ctx.editableColumn.el.nativeElement);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.tt.editingCell || ctx.tt.editingCell !== ctx.editableColumn.el.nativeElement);\n    }\n  },\n  dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeTableCellEditor, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTableCellEditor',\n      template: `\n        <ng-container *ngIf=\"tt.editingCell === editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"inputTemplate\"></ng-container>\n        </ng-container>\n        <ng-container *ngIf=\"!tt.editingCell || tt.editingCell !== editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"outputTemplate\"></ng-container>\n        </ng-container>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: TTEditableColumn\n    }];\n  }, {\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass TTRow {\n  constructor(tt, el, zone) {\n    this.tt = tt;\n    this.el = el;\n    this.zone = zone;\n  }\n\n  onKeyDown(event) {\n    switch (event.which) {\n      //down arrow\n      case 40:\n        let nextRow = this.el.nativeElement.nextElementSibling;\n\n        if (nextRow) {\n          nextRow.focus();\n        }\n\n        event.preventDefault();\n        break;\n      //down arrow\n\n      case 38:\n        let prevRow = this.el.nativeElement.previousElementSibling;\n\n        if (prevRow) {\n          prevRow.focus();\n        }\n\n        event.preventDefault();\n        break;\n      //left arrow\n\n      case 37:\n        if (this.rowNode.node.expanded) {\n          this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n          this.rowNode.node.expanded = false;\n          this.tt.onNodeCollapse.emit({\n            originalEvent: event,\n            node: this.rowNode.node\n          });\n          this.tt.updateSerializedValue();\n          this.tt.tableService.onUIUpdate(this.tt.value);\n          this.restoreFocus();\n        }\n\n        break;\n      //right arrow\n\n      case 39:\n        if (!this.rowNode.node.expanded) {\n          this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n          this.rowNode.node.expanded = true;\n          this.tt.onNodeExpand.emit({\n            originalEvent: event,\n            node: this.rowNode.node\n          });\n          this.tt.updateSerializedValue();\n          this.tt.tableService.onUIUpdate(this.tt.value);\n          this.restoreFocus();\n        }\n\n        break;\n    }\n  }\n\n  restoreFocus() {\n    this.zone.runOutsideAngular(() => {\n      setTimeout(() => {\n        let row = DomHandler.findSingle(this.tt.containerViewChild.nativeElement, '.p-treetable-tbody').children[this.tt.toggleRowIndex];\n\n        if (row) {\n          row.focus();\n        }\n      }, 25);\n    });\n  }\n\n}\n\nTTRow.ɵfac = function TTRow_Factory(t) {\n  return new (t || TTRow)(i0.ɵɵdirectiveInject(TreeTable), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nTTRow.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: TTRow,\n  selectors: [[\"\", \"ttRow\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 1,\n  hostBindings: function TTRow_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown\", function TTRow_keydown_HostBindingHandler($event) {\n        return ctx.onKeyDown($event);\n      });\n    }\n\n    if (rf & 2) {\n      i0.ɵɵattribute(\"tabindex\", \"0\");\n    }\n  },\n  inputs: {\n    rowNode: [\"ttRow\", \"rowNode\"]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TTRow, [{\n    type: Directive,\n    args: [{\n      selector: '[ttRow]',\n      host: {\n        'class': 'p-element',\n        '[attr.tabindex]': '\"0\"'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    rowNode: [{\n      type: Input,\n      args: ['ttRow']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\n\nclass TreeTableToggler {\n  constructor(tt) {\n    this.tt = tt;\n  }\n\n  onClick(event) {\n    this.rowNode.node.expanded = !this.rowNode.node.expanded;\n\n    if (this.rowNode.node.expanded) {\n      this.tt.onNodeExpand.emit({\n        originalEvent: event,\n        node: this.rowNode.node\n      });\n    } else {\n      this.tt.onNodeCollapse.emit({\n        originalEvent: event,\n        node: this.rowNode.node\n      });\n    }\n\n    this.tt.updateSerializedValue();\n    this.tt.tableService.onUIUpdate(this.tt.value);\n    event.preventDefault();\n  }\n\n}\n\nTreeTableToggler.ɵfac = function TreeTableToggler_Factory(t) {\n  return new (t || TreeTableToggler)(i0.ɵɵdirectiveInject(TreeTable));\n};\n\nTreeTableToggler.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: TreeTableToggler,\n  selectors: [[\"p-treeTableToggler\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    rowNode: \"rowNode\"\n  },\n  decls: 2,\n  vars: 5,\n  consts: [[\"type\", \"button\", \"tabindex\", \"-1\", \"pRipple\", \"\", 1, \"p-treetable-toggler\", \"p-link\", 3, \"click\"], [3, \"ngClass\"]],\n  template: function TreeTableToggler_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"button\", 0);\n      i0.ɵɵlistener(\"click\", function TreeTableToggler_Template_button_click_0_listener($event) {\n        return ctx.onClick($event);\n      });\n      i0.ɵɵelement(1, \"i\", 1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"visibility\", ctx.rowNode.node.leaf === false || ctx.rowNode.node.children && ctx.rowNode.node.children.length ? \"visible\" : \"hidden\")(\"margin-left\", ctx.rowNode.level * 16 + \"px\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", ctx.rowNode.node.expanded ? \"pi pi-fw pi-chevron-down\" : \"pi pi-fw pi-chevron-right\");\n    }\n  },\n  dependencies: [i2.NgClass, i5.Ripple],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeTableToggler, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeTableToggler',\n      template: `\n        <button type=\"button\" class=\"p-treetable-toggler p-link\" (click)=\"onClick($event)\" tabindex=\"-1\" pRipple\n            [style.visibility]=\"rowNode.node.leaf === false || (rowNode.node.children && rowNode.node.children.length) ? 'visible' : 'hidden'\" [style.marginLeft]=\"rowNode.level * 16 + 'px'\">\n            <i [ngClass]=\"rowNode.node.expanded ? 'pi pi-fw pi-chevron-down' : 'pi pi-fw pi-chevron-right'\"></i>\n        </button>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: TreeTable\n    }];\n  }, {\n    rowNode: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TreeTableModule {}\n\nTreeTableModule.ɵfac = function TreeTableModule_Factory(t) {\n  return new (t || TreeTableModule)();\n};\n\nTreeTableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TreeTableModule\n});\nTreeTableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule],\n      exports: [TreeTable, SharedModule, TreeTableToggler, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor, ScrollerModule],\n      declarations: [TreeTable, TreeTableToggler, TTScrollableView, TTBody, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { TTBody, TTCheckbox, TTContextMenuRow, TTEditableColumn, TTHeaderCheckbox, TTReorderableColumn, TTResizableColumn, TTRow, TTScrollableView, TTSelectableRow, TTSelectableRowDblClick, TTSortIcon, TTSortableColumn, TreeTable, TreeTableCellEditor, TreeTableModule, TreeTableService, TreeTableToggler };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Component", "ViewEncapsulation", "Input", "Output", "ViewChild", "ContentChildren", "Directive", "HostListener", "ChangeDetectionStrategy", "NgModule", "i2", "CommonModule", "Subject", "<PERSON><PERSON><PERSON><PERSON>", "i3", "PaginatorModule", "i1", "PrimeTemplate", "SharedModule", "ObjectUtils", "i5", "RippleModule", "i4", "ScrollerModule", "TreeTableService", "constructor", "sortSource", "selectionSource", "contextMenuSource", "uiUpdateSource", "totalRecordsSource", "sortSource$", "asObservable", "selectionSource$", "contextMenuSource$", "uiUpdateSource$", "totalRecordsSource$", "onSort", "sortMeta", "next", "onSelectionChange", "onContextMenu", "node", "onUIUpdate", "value", "onTotalRecordsChange", "ɵfac", "ɵprov", "type", "TreeTable", "el", "cd", "zone", "tableService", "filterService", "lazy", "lazyLoadOnInit", "first", "pageLinks", "alwaysShowPaginator", "paginatorPosition", "currentPageReportTemplate", "showFirstLastIcon", "showPageLinks", "defaultSortOrder", "sortMode", "resetPageOnSort", "selectionChange", "contextMenuSelectionChange", "contextMenuSelectionMode", "compareSelectionBy", "loadingIcon", "<PERSON><PERSON><PERSON><PERSON>", "virtualScrollDelay", "columnResizeMode", "rowTrackBy", "index", "item", "filters", "filterDelay", "filterMode", "onFilter", "onNodeExpand", "onNodeCollapse", "onPage", "onLazyLoad", "sortFunction", "onColResize", "onColReorder", "onNodeSelect", "onNodeUnselect", "onContextMenuSelect", "onHeaderCheckboxToggle", "onEditInit", "onEditComplete", "onEditCancel", "_virtualRowHeight", "_value", "_totalRecords", "_sortOrder", "<PERSON><PERSON><PERSON><PERSON>", "virtualRowHeight", "val", "console", "warn", "ngOnInit", "virtualScroll", "emit", "createLazyLoadMetadata", "initialized", "ngAfterContentInit", "templates", "for<PERSON>ach", "getType", "captionTemplate", "template", "headerTemplate", "bodyTemplate", "loadingBodyTemplate", "footerTemplate", "summaryTemplate", "colGroupTemplate", "emptyMessageTemplate", "paginatorLeftTemplate", "paginatorRightTemplate", "paginatorDropdownItemTemplate", "frozenHeaderTemplate", "frozenBodyTemplate", "frozenFooterTemplate", "frozenColGroupTemplate", "ngOnChanges", "simpleChange", "currentValue", "totalRecords", "length", "sortField", "sortSingle", "multiSortMeta", "sortMultiple", "<PERSON><PERSON><PERSON>er", "_filter", "updateSerializedValue", "_sortField", "sortOrder", "_multiSortMeta", "selection", "_selection", "preventSelectionSetterPropagation", "updateSelectionKeys", "serializedValue", "paginator", "serializePageNodes", "serializeNodes", "filteredNodes", "parent", "nodes", "level", "visible", "rowNode", "expanded", "push", "children", "data", "i", "rows", "dataKey", "Array", "isArray", "String", "resolveFieldData", "onPageChange", "event", "scrollable", "resetScrollTop", "sort", "originalEvent", "field", "metaKey", "ctrl<PERSON>ey", "getSortMeta", "order", "sortNodes", "customSort", "mode", "node1", "node2", "value1", "value2", "result", "localeCompare", "undefined", "numeric", "sortMultipleNodes", "multisortmeta", "multisortField", "isSorted", "sorted", "globalFilter", "forceUpdate", "detectChanges", "onLazyItemLoad", "Object", "assign", "last", "scrollToVirtualIndex", "scrollTo", "top", "scrollableViewChild", "scrollableFrozenViewChild", "options", "isEmpty", "getBlockableElement", "nativeElement", "onColumnResizeBegin", "containerLeft", "getOffset", "containerViewChild", "left", "lastResizerHelperX", "pageX", "scrollLeft", "preventDefault", "onColumnResize", "addClass", "resizeHelperViewChild", "style", "height", "offsetHeight", "display", "onColumnResizeEnd", "column", "delta", "offsetLeft", "columnWidth", "offsetWidth", "newColumnWidth", "min<PERSON><PERSON><PERSON>", "parseInt", "nextColumn", "nextElement<PERSON><PERSON>ling", "offsetParent", "nextColumnWidth", "nextColumnMin<PERSON>idth", "scrollableView", "findParentScrollableView", "scrollableBodyTable", "findSingle", "scrollableHeaderTable", "scrollableFooterTable", "resizeColumnIndex", "resizeColGroup", "width", "scrollableBody", "scrollableHeader", "scrollableFooter", "scrollableBodyTableWidth", "scrollableHeaderTableWidth", "isContainerInViewport", "<PERSON><PERSON><PERSON><PERSON>", "container", "table", "calculateScrollbarWidth", "table<PERSON>iew<PERSON><PERSON><PERSON>", "containerWidth", "element", "removeClass", "parentElement", "hasClass", "colGroup", "nodeName", "col", "nextCol", "onColumnDragStart", "columnElement", "reorderIconWidth", "getHiddenElementOuterWidth", "reorderIndicatorUpViewChild", "reorderIconHeight", "getHiddenElementOuterHeight", "reorderIndicatorDownViewChild", "draggedColumn", "dataTransfer", "setData", "onColumnDragEnter", "dropHeader", "reorderableColumns", "containerOffset", "dropHeaderOffset", "targetLeft", "targetTop", "columnCenter", "Math", "ceil", "dropPosition", "dropEffect", "onColumnDragLeave", "onColumnDrop", "dropColumn", "dragIndex", "indexWithinGroup", "dropIndex", "allowDrop", "reorderArray", "columns", "draggable", "handleRowClick", "targetNode", "target", "selectionMode", "selected", "isSelected", "metaSelection", "rowTouched", "metaKeySelection", "dataKeyValue", "isSingleSelectionMode", "selectionIndex", "findIndexInSelection", "filter", "isMultipleSelectionMode", "rowIndex", "handleRowTouchEnd", "handleRowRightClick", "contextMenu", "contextMenuSelection", "show", "toggleNodeWithCheckbox", "propagateSelectionDown", "propagateSelectionUp", "toggleNodesWithCheckbox", "check", "slice", "checked", "select", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "childPartialSelected", "child", "partialSelected", "equals", "matchMode", "filterTimeout", "clearTimeout", "isFilterBlank", "setTimeout", "filterGlobal", "trim", "globalFilterFieldsArray", "globalFilterFields", "Error", "isStrictMode", "isValueChanged", "copyNode", "localMatch", "globalMatch", "paramsWithoutNode", "prop", "hasOwnProperty", "filterMeta", "filterField", "filterValue", "filterMatchMode", "filterConstraint", "findFilteredNodes", "isFilterMatched", "j", "copyNodeForGlobal", "matches", "filteredValue", "matched", "childNodes", "childNode", "copyChildNode", "dataFieldValue", "filterLocale", "isNodeLeaf", "leaf", "empty", "reset", "updateEditingCell", "cell", "editingCell", "editingCellData", "editingCellField", "bindDocumentEditListener", "isEditingCellValid", "find", "documentEditListener", "editingCellClick", "unbindDocumentEditListener", "document", "addEventListener", "removeEventListener", "ngOnDestroy", "ElementRef", "ChangeDetectorRef", "NgZone", "FilterService", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "Paginator", "TTScrollableView", "TTBody", "args", "selector", "providers", "encapsulation", "None", "host", "styles", "styleClass", "tableStyle", "tableStyleClass", "autoLayout", "rowsPerPageOptions", "paginatorDropdownAppendTo", "showCurrentPageReport", "showJumpToPageDropdown", "rowHover", "loading", "scrollHeight", "virtualScrollItemSize", "virtualScrollOptions", "frozenWidth", "frozenColumns", "resizableColumns", "tt", "treeTableService", "subscription", "subscribe", "getScrollerOption", "option", "scrollerOptions", "getRowIndex", "getItemOptions", "unsubscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frozen", "serializedNodes", "_scrollHeight", "includes", "log", "ngAfterViewInit", "frozenView", "previousElementSibling", "frozenSiblingBody", "scrollBarWidth", "scrollHeaderBoxViewChild", "paddingRight", "scrollFooterBoxViewChild", "scrollableAlignerViewChild", "calculateScrollbarHeight", "bindEvents", "runOutsideAngular", "scrollHeaderViewChild", "headerScrollListener", "onHeaderScroll", "bind", "scrollFooter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "footerScrollListener", "onFooterScroll", "bodyScrollListener", "onBodyScroll", "scroller", "getElementRef", "scrollBodyViewChild", "unbindEvents", "preventBodyScrollPropagation", "marginLeft", "scrollTop", "scrollToIndex", "<PERSON><PERSON><PERSON>", "scrollTableViewChild", "scrollLoadingTableViewChild", "TTSortableColumn", "isEnabled", "updateSortState", "onClick", "clearSelection", "onEnterKey", "ttSortableColumnDisabled", "ɵdir", "TTSortIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeDetection", "OnPush", "ariaLabelDesc", "ariaLabelAsc", "TTResizableColumn", "resizer", "createElement", "className", "append<PERSON><PERSON><PERSON>", "resizerMouseDownListener", "onMouseDown", "bindDocumentEvents", "documentMouseMoveListener", "onDocumentMouseMove", "documentMouseUpListener", "onDocumentMouseUp", "unbindDocumentEvents", "ttResizableColumnDisabled", "TTReorderableColumn", "mouseDownListener", "dragStartListener", "onDragStart", "dragOverListener", "onDragEnter", "dragEnterListener", "dragLeaveListener", "onDragLeave", "onDragOver", "onDrop", "ttReorderableColumnDisabled", "TTSelectableRow", "which", "onTouchEnd", "ttSelectableRowDisabled", "TTSelectableRowDblClick", "TTContextMenuRow", "focus", "ttContextMenuRowDisabled", "TTCheckbox", "disabled", "onFocus", "focused", "onBlur", "TTHeaderCheckbox", "valueChangeSubscription", "updateCheckedState", "selectionChangeSubscription", "boxViewChild", "TTEditableColumn", "openCell", "focusable", "closeEditingCell", "onKeyDown", "keyCode", "shift<PERSON>ey", "moveToPreviousCell", "moveToNextCell", "find<PERSON>ell", "currentCell", "row", "targetCell", "findPreviousEditableColumn", "invokeElementMethod", "findNextEditableColumn", "prevCell", "previousRow", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nextCell", "nextRow", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "ttEditableColumnDisabled", "TreeTableCellEditor", "editableColumn", "inputTemplate", "outputTemplate", "TTRow", "prevRow", "toggleRowIndex", "restoreFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "TreeTableModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-treetable.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Input, Output, ViewChild, ContentChildren, Directive, HostListener, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { DomHandler } from 'primeng/dom';\nimport * as i3 from 'primeng/paginator';\nimport { PaginatorModule } from 'primeng/paginator';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\nimport * as i5 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\n\nclass TreeTableService {\n    constructor() {\n        this.sortSource = new Subject();\n        this.selectionSource = new Subject();\n        this.contextMenuSource = new Subject();\n        this.uiUpdateSource = new Subject();\n        this.totalRecordsSource = new Subject();\n        this.sortSource$ = this.sortSource.asObservable();\n        this.selectionSource$ = this.selectionSource.asObservable();\n        this.contextMenuSource$ = this.contextMenuSource.asObservable();\n        this.uiUpdateSource$ = this.uiUpdateSource.asObservable();\n        this.totalRecordsSource$ = this.totalRecordsSource.asObservable();\n    }\n    onSort(sortMeta) {\n        this.sortSource.next(sortMeta);\n    }\n    onSelectionChange() {\n        this.selectionSource.next(null);\n    }\n    onContextMenu(node) {\n        this.contextMenuSource.next(node);\n    }\n    onUIUpdate(value) {\n        this.uiUpdateSource.next(value);\n    }\n    onTotalRecordsChange(value) {\n        this.totalRecordsSource.next(value);\n    }\n}\nTreeTableService.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTreeTableService.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableService });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableService, decorators: [{\n            type: Injectable\n        }] });\nclass TreeTable {\n    constructor(el, cd, zone, tableService, filterService) {\n        this.el = el;\n        this.cd = cd;\n        this.zone = zone;\n        this.tableService = tableService;\n        this.filterService = filterService;\n        this.lazy = false;\n        this.lazyLoadOnInit = true;\n        this.first = 0;\n        this.pageLinks = 5;\n        this.alwaysShowPaginator = true;\n        this.paginatorPosition = 'bottom';\n        this.currentPageReportTemplate = '{currentPage} of {totalPages}';\n        this.showFirstLastIcon = true;\n        this.showPageLinks = true;\n        this.defaultSortOrder = 1;\n        this.sortMode = 'single';\n        this.resetPageOnSort = true;\n        this.selectionChange = new EventEmitter();\n        this.contextMenuSelectionChange = new EventEmitter();\n        this.contextMenuSelectionMode = \"separate\";\n        this.compareSelectionBy = 'deepEquals';\n        this.loadingIcon = 'pi pi-spinner';\n        this.showLoader = true;\n        this.virtualScrollDelay = 150;\n        this.columnResizeMode = 'fit';\n        this.rowTrackBy = (index, item) => item;\n        this.filters = {};\n        this.filterDelay = 300;\n        this.filterMode = 'lenient';\n        this.onFilter = new EventEmitter();\n        this.onNodeExpand = new EventEmitter();\n        this.onNodeCollapse = new EventEmitter();\n        this.onPage = new EventEmitter();\n        this.onSort = new EventEmitter();\n        this.onLazyLoad = new EventEmitter();\n        this.sortFunction = new EventEmitter();\n        this.onColResize = new EventEmitter();\n        this.onColReorder = new EventEmitter();\n        this.onNodeSelect = new EventEmitter();\n        this.onNodeUnselect = new EventEmitter();\n        this.onContextMenuSelect = new EventEmitter();\n        this.onHeaderCheckboxToggle = new EventEmitter();\n        this.onEditInit = new EventEmitter();\n        this.onEditComplete = new EventEmitter();\n        this.onEditCancel = new EventEmitter();\n        /* @deprecated */\n        this._virtualRowHeight = 28;\n        this._value = [];\n        this._totalRecords = 0;\n        this._sortOrder = 1;\n        this.selectionKeys = {};\n    }\n    get virtualRowHeight() {\n        return this._virtualRowHeight;\n    }\n    set virtualRowHeight(val) {\n        this._virtualRowHeight = val;\n        console.warn(\"The virtualRowHeight property is deprecated, use virtualScrollItemSize property instead.\");\n    }\n    ngOnInit() {\n        if (this.lazy && this.lazyLoadOnInit && !this.virtualScroll) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n        this.initialized = true;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'caption':\n                    this.captionTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'body':\n                    this.bodyTemplate = item.template;\n                    break;\n                case 'loadingbody':\n                    this.loadingBodyTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'summary':\n                    this.summaryTemplate = item.template;\n                    break;\n                case 'colgroup':\n                    this.colGroupTemplate = item.template;\n                    break;\n                case 'emptymessage':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n                case 'paginatorleft':\n                    this.paginatorLeftTemplate = item.template;\n                    break;\n                case 'paginatorright':\n                    this.paginatorRightTemplate = item.template;\n                    break;\n                case 'paginatordropdownitem':\n                    this.paginatorDropdownItemTemplate = item.template;\n                    break;\n                case 'frozenheader':\n                    this.frozenHeaderTemplate = item.template;\n                    break;\n                case 'frozenbody':\n                    this.frozenBodyTemplate = item.template;\n                    break;\n                case 'frozenfooter':\n                    this.frozenFooterTemplate = item.template;\n                    break;\n                case 'frozencolgroup':\n                    this.frozenColGroupTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.value) {\n            this._value = simpleChange.value.currentValue;\n            if (!this.lazy) {\n                this.totalRecords = (this._value ? this._value.length : 0);\n                if (this.sortMode == 'single' && this.sortField)\n                    this.sortSingle();\n                else if (this.sortMode == 'multiple' && this.multiSortMeta)\n                    this.sortMultiple();\n                else if (this.hasFilter()) //sort already filters\n                    this._filter();\n            }\n            this.updateSerializedValue();\n            this.tableService.onUIUpdate(this.value);\n        }\n        if (simpleChange.sortField) {\n            this._sortField = simpleChange.sortField.currentValue;\n            //avoid triggering lazy load prior to lazy initialization at onInit\n            if (!this.lazy || this.initialized) {\n                if (this.sortMode === 'single') {\n                    this.sortSingle();\n                }\n            }\n        }\n        if (simpleChange.sortOrder) {\n            this._sortOrder = simpleChange.sortOrder.currentValue;\n            //avoid triggering lazy load prior to lazy initialization at onInit\n            if (!this.lazy || this.initialized) {\n                if (this.sortMode === 'single') {\n                    this.sortSingle();\n                }\n            }\n        }\n        if (simpleChange.multiSortMeta) {\n            this._multiSortMeta = simpleChange.multiSortMeta.currentValue;\n            if (this.sortMode === 'multiple') {\n                this.sortMultiple();\n            }\n        }\n        if (simpleChange.selection) {\n            this._selection = simpleChange.selection.currentValue;\n            if (!this.preventSelectionSetterPropagation) {\n                this.updateSelectionKeys();\n                this.tableService.onSelectionChange();\n            }\n            this.preventSelectionSetterPropagation = false;\n        }\n    }\n    get value() {\n        return this._value;\n    }\n    set value(val) {\n        this._value = val;\n    }\n    updateSerializedValue() {\n        this.serializedValue = [];\n        if (this.paginator)\n            this.serializePageNodes();\n        else\n            this.serializeNodes(null, this.filteredNodes || this.value, 0, true);\n    }\n    serializeNodes(parent, nodes, level, visible) {\n        if (nodes && nodes.length) {\n            for (let node of nodes) {\n                node.parent = parent;\n                const rowNode = {\n                    node: node,\n                    parent: parent,\n                    level: level,\n                    visible: visible && (parent ? parent.expanded : true)\n                };\n                this.serializedValue.push(rowNode);\n                if (rowNode.visible && node.expanded) {\n                    this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n                }\n            }\n        }\n    }\n    serializePageNodes() {\n        let data = this.filteredNodes || this.value;\n        this.serializedValue = [];\n        if (data && data.length) {\n            const first = this.lazy ? 0 : this.first;\n            for (let i = first; i < (first + this.rows); i++) {\n                let node = data[i];\n                if (node) {\n                    this.serializedValue.push({\n                        node: node,\n                        parent: null,\n                        level: 0,\n                        visible: true\n                    });\n                    this.serializeNodes(node, node.children, 1, true);\n                }\n            }\n        }\n    }\n    get totalRecords() {\n        return this._totalRecords;\n    }\n    set totalRecords(val) {\n        this._totalRecords = val;\n        this.tableService.onTotalRecordsChange(this._totalRecords);\n    }\n    get sortField() {\n        return this._sortField;\n    }\n    set sortField(val) {\n        this._sortField = val;\n    }\n    get sortOrder() {\n        return this._sortOrder;\n    }\n    set sortOrder(val) {\n        this._sortOrder = val;\n    }\n    get multiSortMeta() {\n        return this._multiSortMeta;\n    }\n    set multiSortMeta(val) {\n        this._multiSortMeta = val;\n    }\n    get selection() {\n        return this._selection;\n    }\n    set selection(val) {\n        this._selection = val;\n    }\n    updateSelectionKeys() {\n        if (this.dataKey && this._selection) {\n            this.selectionKeys = {};\n            if (Array.isArray(this._selection)) {\n                for (let node of this._selection) {\n                    this.selectionKeys[String(ObjectUtils.resolveFieldData(node.data, this.dataKey))] = 1;\n                }\n            }\n            else {\n                this.selectionKeys[String(ObjectUtils.resolveFieldData(this._selection.data, this.dataKey))] = 1;\n            }\n        }\n    }\n    onPageChange(event) {\n        this.first = event.first;\n        this.rows = event.rows;\n        if (this.lazy)\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        else\n            this.serializePageNodes();\n        this.onPage.emit({\n            first: this.first,\n            rows: this.rows\n        });\n        this.tableService.onUIUpdate(this.value);\n        if (this.scrollable) {\n            this.resetScrollTop();\n        }\n    }\n    sort(event) {\n        let originalEvent = event.originalEvent;\n        if (this.sortMode === 'single') {\n            this._sortOrder = (this.sortField === event.field) ? this.sortOrder * -1 : this.defaultSortOrder;\n            this._sortField = event.field;\n            this.sortSingle();\n            if (this.resetPageOnSort && this.scrollable) {\n                this.resetScrollTop();\n            }\n        }\n        if (this.sortMode === 'multiple') {\n            let metaKey = originalEvent.metaKey || originalEvent.ctrlKey;\n            let sortMeta = this.getSortMeta(event.field);\n            if (sortMeta) {\n                if (!metaKey) {\n                    this._multiSortMeta = [{ field: event.field, order: sortMeta.order * -1 }];\n                    if (this.resetPageOnSort && this.scrollable) {\n                        this.resetScrollTop();\n                    }\n                }\n                else {\n                    sortMeta.order = sortMeta.order * -1;\n                }\n            }\n            else {\n                if (!metaKey || !this.multiSortMeta) {\n                    this._multiSortMeta = [];\n                    if (this.resetPageOnSort && this.scrollable) {\n                        this.resetScrollTop();\n                    }\n                }\n                this.multiSortMeta.push({ field: event.field, order: this.defaultSortOrder });\n            }\n            this.sortMultiple();\n        }\n    }\n    sortSingle() {\n        if (this.sortField && this.sortOrder) {\n            if (this.lazy) {\n                this.onLazyLoad.emit(this.createLazyLoadMetadata());\n            }\n            else if (this.value) {\n                this.sortNodes(this.value);\n                if (this.hasFilter()) {\n                    this._filter();\n                }\n            }\n            let sortMeta = {\n                field: this.sortField,\n                order: this.sortOrder\n            };\n            this.onSort.emit(sortMeta);\n            this.tableService.onSort(sortMeta);\n            this.updateSerializedValue();\n        }\n    }\n    sortNodes(nodes) {\n        if (!nodes || nodes.length === 0) {\n            return;\n        }\n        if (this.customSort) {\n            this.sortFunction.emit({\n                data: nodes,\n                mode: this.sortMode,\n                field: this.sortField,\n                order: this.sortOrder\n            });\n        }\n        else {\n            nodes.sort((node1, node2) => {\n                let value1 = ObjectUtils.resolveFieldData(node1.data, this.sortField);\n                let value2 = ObjectUtils.resolveFieldData(node2.data, this.sortField);\n                let result = null;\n                if (value1 == null && value2 != null)\n                    result = -1;\n                else if (value1 != null && value2 == null)\n                    result = 1;\n                else if (value1 == null && value2 == null)\n                    result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string')\n                    result = value1.localeCompare(value2, undefined, { numeric: true });\n                else\n                    result = (value1 < value2) ? -1 : (value1 > value2) ? 1 : 0;\n                return (this.sortOrder * result);\n            });\n        }\n        for (let node of nodes) {\n            this.sortNodes(node.children);\n        }\n    }\n    sortMultiple() {\n        if (this.multiSortMeta) {\n            if (this.lazy) {\n                this.onLazyLoad.emit(this.createLazyLoadMetadata());\n            }\n            else if (this.value) {\n                this.sortMultipleNodes(this.value);\n                if (this.hasFilter()) {\n                    this._filter();\n                }\n            }\n            this.onSort.emit({\n                multisortmeta: this.multiSortMeta\n            });\n            this.updateSerializedValue();\n            this.tableService.onSort(this.multiSortMeta);\n        }\n    }\n    sortMultipleNodes(nodes) {\n        if (!nodes || nodes.length === 0) {\n            return;\n        }\n        if (this.customSort) {\n            this.sortFunction.emit({\n                data: this.value,\n                mode: this.sortMode,\n                multiSortMeta: this.multiSortMeta\n            });\n        }\n        else {\n            nodes.sort((node1, node2) => {\n                return this.multisortField(node1, node2, this.multiSortMeta, 0);\n            });\n        }\n        for (let node of nodes) {\n            this.sortMultipleNodes(node.children);\n        }\n    }\n    multisortField(node1, node2, multiSortMeta, index) {\n        let value1 = ObjectUtils.resolveFieldData(node1.data, multiSortMeta[index].field);\n        let value2 = ObjectUtils.resolveFieldData(node2.data, multiSortMeta[index].field);\n        let result = null;\n        if (value1 == null && value2 != null)\n            result = -1;\n        else if (value1 != null && value2 == null)\n            result = 1;\n        else if (value1 == null && value2 == null)\n            result = 0;\n        if (typeof value1 == 'string' || value1 instanceof String) {\n            if (value1.localeCompare && (value1 != value2)) {\n                return (multiSortMeta[index].order * value1.localeCompare(value2, undefined, { numeric: true }));\n            }\n        }\n        else {\n            result = (value1 < value2) ? -1 : 1;\n        }\n        if (value1 == value2) {\n            return (multiSortMeta.length - 1) > (index) ? (this.multisortField(node1, node2, multiSortMeta, index + 1)) : 0;\n        }\n        return (multiSortMeta[index].order * result);\n    }\n    getSortMeta(field) {\n        if (this.multiSortMeta && this.multiSortMeta.length) {\n            for (let i = 0; i < this.multiSortMeta.length; i++) {\n                if (this.multiSortMeta[i].field === field) {\n                    return this.multiSortMeta[i];\n                }\n            }\n        }\n        return null;\n    }\n    isSorted(field) {\n        if (this.sortMode === 'single') {\n            return (this.sortField && this.sortField === field);\n        }\n        else if (this.sortMode === 'multiple') {\n            let sorted = false;\n            if (this.multiSortMeta) {\n                for (let i = 0; i < this.multiSortMeta.length; i++) {\n                    if (this.multiSortMeta[i].field == field) {\n                        sorted = true;\n                        break;\n                    }\n                }\n            }\n            return sorted;\n        }\n    }\n    createLazyLoadMetadata() {\n        return {\n            first: this.first,\n            rows: this.rows,\n            sortField: this.sortField,\n            sortOrder: this.sortOrder,\n            filters: this.filters,\n            globalFilter: this.filters && this.filters['global'] ? this.filters['global'].value : null,\n            multiSortMeta: this.multiSortMeta,\n            forceUpdate: () => this.cd.detectChanges()\n        };\n    }\n    onLazyItemLoad(event) {\n        this.onLazyLoad.emit(Object.assign(Object.assign(Object.assign({}, this.createLazyLoadMetadata()), event), { rows: event.last - event.first }));\n    }\n    resetScrollTop() {\n        if (this.virtualScroll)\n            this.scrollToVirtualIndex(0);\n        else\n            this.scrollTo({ top: 0 });\n    }\n    scrollToVirtualIndex(index) {\n        if (this.scrollableViewChild) {\n            this.scrollableViewChild.scrollToVirtualIndex(index);\n        }\n        if (this.scrollableFrozenViewChild) {\n            this.scrollableFrozenViewChild.scrollToVirtualIndex(index);\n        }\n    }\n    scrollTo(options) {\n        if (this.scrollableViewChild) {\n            this.scrollableViewChild.scrollTo(options);\n        }\n        if (this.scrollableFrozenViewChild) {\n            this.scrollableFrozenViewChild.scrollTo(options);\n        }\n    }\n    isEmpty() {\n        let data = this.filteredNodes || this.value;\n        return data == null || data.length == 0;\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    onColumnResizeBegin(event) {\n        let containerLeft = DomHandler.getOffset(this.containerViewChild.nativeElement).left;\n        this.lastResizerHelperX = (event.pageX - containerLeft + this.containerViewChild.nativeElement.scrollLeft);\n        event.preventDefault();\n    }\n    onColumnResize(event) {\n        let containerLeft = DomHandler.getOffset(this.containerViewChild.nativeElement).left;\n        DomHandler.addClass(this.containerViewChild.nativeElement, 'p-unselectable-text');\n        this.resizeHelperViewChild.nativeElement.style.height = this.containerViewChild.nativeElement.offsetHeight + 'px';\n        this.resizeHelperViewChild.nativeElement.style.top = 0 + 'px';\n        this.resizeHelperViewChild.nativeElement.style.left = (event.pageX - containerLeft + this.containerViewChild.nativeElement.scrollLeft) + 'px';\n        this.resizeHelperViewChild.nativeElement.style.display = 'block';\n    }\n    onColumnResizeEnd(event, column) {\n        let delta = this.resizeHelperViewChild.nativeElement.offsetLeft - this.lastResizerHelperX;\n        let columnWidth = column.offsetWidth;\n        let newColumnWidth = columnWidth + delta;\n        let minWidth = column.style.minWidth || 15;\n        if (columnWidth + delta > parseInt(minWidth)) {\n            if (this.columnResizeMode === 'fit') {\n                let nextColumn = column.nextElementSibling;\n                while (!nextColumn.offsetParent) {\n                    nextColumn = nextColumn.nextElementSibling;\n                }\n                if (nextColumn) {\n                    let nextColumnWidth = nextColumn.offsetWidth - delta;\n                    let nextColumnMinWidth = nextColumn.style.minWidth || 15;\n                    if (newColumnWidth > 15 && nextColumnWidth > parseInt(nextColumnMinWidth)) {\n                        if (this.scrollable) {\n                            let scrollableView = this.findParentScrollableView(column);\n                            let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n                            let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n                            let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n                            let resizeColumnIndex = DomHandler.index(column);\n                            this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                            this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                            this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, nextColumnWidth);\n                        }\n                        else {\n                            column.style.width = newColumnWidth + 'px';\n                            if (nextColumn) {\n                                nextColumn.style.width = nextColumnWidth + 'px';\n                            }\n                        }\n                    }\n                }\n            }\n            else if (this.columnResizeMode === 'expand') {\n                if (this.scrollable) {\n                    let scrollableView = this.findParentScrollableView(column);\n                    let scrollableBody = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport');\n                    let scrollableHeader = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-header');\n                    let scrollableFooter = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-footer');\n                    let scrollableBodyTable = DomHandler.findSingle(scrollableView, '.p-treetable-scrollable-body table') || DomHandler.findSingle(scrollableView, '.p-scroller-viewport table');\n                    let scrollableHeaderTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-header-table');\n                    let scrollableFooterTable = DomHandler.findSingle(scrollableView, 'table.p-treetable-scrollable-footer-table');\n                    scrollableBodyTable.style.width = scrollableBodyTable.offsetWidth + delta + 'px';\n                    scrollableHeaderTable.style.width = scrollableHeaderTable.offsetWidth + delta + 'px';\n                    if (scrollableFooterTable) {\n                        scrollableFooterTable.style.width = scrollableFooterTable.offsetWidth + delta + 'px';\n                    }\n                    let resizeColumnIndex = DomHandler.index(column);\n                    const scrollableBodyTableWidth = column ? scrollableBodyTable.offsetWidth + delta : newColumnWidth;\n                    const scrollableHeaderTableWidth = column ? scrollableHeaderTable.offsetWidth + delta : newColumnWidth;\n                    const isContainerInViewport = this.containerViewChild.nativeElement.offsetWidth >= scrollableBodyTableWidth;\n                    let setWidth = (container, table, width, isContainerInViewport) => {\n                        if (container && table) {\n                            container.style.width = isContainerInViewport ? width + DomHandler.calculateScrollbarWidth(scrollableBody) + 'px' : 'auto';\n                            table.style.width = width + 'px';\n                        }\n                    };\n                    setWidth(scrollableBody, scrollableBodyTable, scrollableBodyTableWidth, isContainerInViewport);\n                    setWidth(scrollableHeader, scrollableHeaderTable, scrollableHeaderTableWidth, isContainerInViewport);\n                    setWidth(scrollableFooter, scrollableFooterTable, scrollableHeaderTableWidth, isContainerInViewport);\n                    this.resizeColGroup(scrollableHeaderTable, resizeColumnIndex, newColumnWidth, null);\n                    this.resizeColGroup(scrollableBodyTable, resizeColumnIndex, newColumnWidth, null);\n                    this.resizeColGroup(scrollableFooterTable, resizeColumnIndex, newColumnWidth, null);\n                }\n                else {\n                    this.tableViewChild.nativeElement.style.width = this.tableViewChild.nativeElement.offsetWidth + delta + 'px';\n                    column.style.width = newColumnWidth + 'px';\n                    let containerWidth = this.tableViewChild.nativeElement.style.width;\n                    this.containerViewChild.nativeElement.style.width = containerWidth + 'px';\n                }\n            }\n            this.onColResize.emit({\n                element: column,\n                delta: delta\n            });\n        }\n        this.resizeHelperViewChild.nativeElement.style.display = 'none';\n        DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-unselectable-text');\n    }\n    findParentScrollableView(column) {\n        if (column) {\n            let parent = column.parentElement;\n            while (parent && !DomHandler.hasClass(parent, 'p-treetable-scrollable-view')) {\n                parent = parent.parentElement;\n            }\n            return parent;\n        }\n        else {\n            return null;\n        }\n    }\n    resizeColGroup(table, resizeColumnIndex, newColumnWidth, nextColumnWidth) {\n        if (table) {\n            let colGroup = table.children[0].nodeName === 'COLGROUP' ? table.children[0] : null;\n            if (colGroup) {\n                let col = colGroup.children[resizeColumnIndex];\n                let nextCol = col.nextElementSibling;\n                col.style.width = newColumnWidth + 'px';\n                if (nextCol && nextColumnWidth) {\n                    nextCol.style.width = nextColumnWidth + 'px';\n                }\n            }\n            else {\n                throw \"Scrollable tables require a colgroup to support resizable columns\";\n            }\n        }\n    }\n    onColumnDragStart(event, columnElement) {\n        this.reorderIconWidth = DomHandler.getHiddenElementOuterWidth(this.reorderIndicatorUpViewChild.nativeElement);\n        this.reorderIconHeight = DomHandler.getHiddenElementOuterHeight(this.reorderIndicatorDownViewChild.nativeElement);\n        this.draggedColumn = columnElement;\n        event.dataTransfer.setData('text', 'b'); // For firefox\n    }\n    onColumnDragEnter(event, dropHeader) {\n        if (this.reorderableColumns && this.draggedColumn && dropHeader) {\n            event.preventDefault();\n            let containerOffset = DomHandler.getOffset(this.containerViewChild.nativeElement);\n            let dropHeaderOffset = DomHandler.getOffset(dropHeader);\n            if (this.draggedColumn != dropHeader) {\n                let targetLeft = dropHeaderOffset.left - containerOffset.left;\n                let targetTop = containerOffset.top - dropHeaderOffset.top;\n                let columnCenter = dropHeaderOffset.left + dropHeader.offsetWidth / 2;\n                this.reorderIndicatorUpViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top - (this.reorderIconHeight - 1) + 'px';\n                this.reorderIndicatorDownViewChild.nativeElement.style.top = dropHeaderOffset.top - containerOffset.top + dropHeader.offsetHeight + 'px';\n                if (event.pageX > columnCenter) {\n                    this.reorderIndicatorUpViewChild.nativeElement.style.left = (targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2)) + 'px';\n                    this.reorderIndicatorDownViewChild.nativeElement.style.left = (targetLeft + dropHeader.offsetWidth - Math.ceil(this.reorderIconWidth / 2)) + 'px';\n                    this.dropPosition = 1;\n                }\n                else {\n                    this.reorderIndicatorUpViewChild.nativeElement.style.left = (targetLeft - Math.ceil(this.reorderIconWidth / 2)) + 'px';\n                    this.reorderIndicatorDownViewChild.nativeElement.style.left = (targetLeft - Math.ceil(this.reorderIconWidth / 2)) + 'px';\n                    this.dropPosition = -1;\n                }\n                this.reorderIndicatorUpViewChild.nativeElement.style.display = 'block';\n                this.reorderIndicatorDownViewChild.nativeElement.style.display = 'block';\n            }\n            else {\n                event.dataTransfer.dropEffect = 'none';\n            }\n        }\n    }\n    onColumnDragLeave(event) {\n        if (this.reorderableColumns && this.draggedColumn) {\n            event.preventDefault();\n            this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n            this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n        }\n    }\n    onColumnDrop(event, dropColumn) {\n        event.preventDefault();\n        if (this.draggedColumn) {\n            let dragIndex = DomHandler.indexWithinGroup(this.draggedColumn, 'ttreorderablecolumn');\n            let dropIndex = DomHandler.indexWithinGroup(dropColumn, 'ttreorderablecolumn');\n            let allowDrop = (dragIndex != dropIndex);\n            if (allowDrop && ((dropIndex - dragIndex == 1 && this.dropPosition === -1) || (dragIndex - dropIndex == 1 && this.dropPosition === 1))) {\n                allowDrop = false;\n            }\n            if (allowDrop && ((dropIndex < dragIndex && this.dropPosition === 1))) {\n                dropIndex = dropIndex + 1;\n            }\n            if (allowDrop && ((dropIndex > dragIndex && this.dropPosition === -1))) {\n                dropIndex = dropIndex - 1;\n            }\n            if (allowDrop) {\n                ObjectUtils.reorderArray(this.columns, dragIndex, dropIndex);\n                this.onColReorder.emit({\n                    dragIndex: dragIndex,\n                    dropIndex: dropIndex,\n                    columns: this.columns\n                });\n            }\n            this.reorderIndicatorUpViewChild.nativeElement.style.display = 'none';\n            this.reorderIndicatorDownViewChild.nativeElement.style.display = 'none';\n            this.draggedColumn.draggable = false;\n            this.draggedColumn = null;\n            this.dropPosition = null;\n        }\n    }\n    handleRowClick(event) {\n        let targetNode = event.originalEvent.target.nodeName;\n        if (targetNode == 'INPUT' || targetNode == 'BUTTON' || targetNode == 'A' || (DomHandler.hasClass(event.originalEvent.target, 'p-clickable'))) {\n            return;\n        }\n        if (this.selectionMode) {\n            this.preventSelectionSetterPropagation = true;\n            let rowNode = event.rowNode;\n            let selected = this.isSelected(rowNode.node);\n            let metaSelection = this.rowTouched ? false : this.metaKeySelection;\n            let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(rowNode.node.data, this.dataKey)) : null;\n            if (metaSelection) {\n                let metaKey = event.originalEvent.metaKey || event.originalEvent.ctrlKey;\n                if (selected && metaKey) {\n                    if (this.isSingleSelectionMode()) {\n                        this._selection = null;\n                        this.selectionKeys = {};\n                        this.selectionChange.emit(null);\n                    }\n                    else {\n                        let selectionIndex = this.findIndexInSelection(rowNode.node);\n                        this._selection = this.selection.filter((val, i) => i != selectionIndex);\n                        this.selectionChange.emit(this.selection);\n                        if (dataKeyValue) {\n                            delete this.selectionKeys[dataKeyValue];\n                        }\n                    }\n                    this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row' });\n                }\n                else {\n                    if (this.isSingleSelectionMode()) {\n                        this._selection = rowNode.node;\n                        this.selectionChange.emit(rowNode.node);\n                        if (dataKeyValue) {\n                            this.selectionKeys = {};\n                            this.selectionKeys[dataKeyValue] = 1;\n                        }\n                    }\n                    else if (this.isMultipleSelectionMode()) {\n                        if (metaKey) {\n                            this._selection = this.selection || [];\n                        }\n                        else {\n                            this._selection = [];\n                            this.selectionKeys = {};\n                        }\n                        this._selection = [...this.selection, rowNode.node];\n                        this.selectionChange.emit(this.selection);\n                        if (dataKeyValue) {\n                            this.selectionKeys[dataKeyValue] = 1;\n                        }\n                    }\n                    this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: event.rowIndex });\n                }\n            }\n            else {\n                if (this.selectionMode === 'single') {\n                    if (selected) {\n                        this._selection = null;\n                        this.selectionKeys = {};\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row' });\n                    }\n                    else {\n                        this._selection = rowNode.node;\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: event.rowIndex });\n                        if (dataKeyValue) {\n                            this.selectionKeys = {};\n                            this.selectionKeys[dataKeyValue] = 1;\n                        }\n                    }\n                }\n                else if (this.selectionMode === 'multiple') {\n                    if (selected) {\n                        let selectionIndex = this.findIndexInSelection(rowNode.node);\n                        this._selection = this.selection.filter((val, i) => i != selectionIndex);\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeUnselect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row' });\n                        if (dataKeyValue) {\n                            delete this.selectionKeys[dataKeyValue];\n                        }\n                    }\n                    else {\n                        this._selection = this.selection ? [...this.selection, rowNode.node] : [rowNode.node];\n                        this.selectionChange.emit(this.selection);\n                        this.onNodeSelect.emit({ originalEvent: event.originalEvent, node: rowNode.node, type: 'row', index: event.rowIndex });\n                        if (dataKeyValue) {\n                            this.selectionKeys[dataKeyValue] = 1;\n                        }\n                    }\n                }\n            }\n            this.tableService.onSelectionChange();\n        }\n        this.rowTouched = false;\n    }\n    handleRowTouchEnd(event) {\n        this.rowTouched = true;\n    }\n    handleRowRightClick(event) {\n        if (this.contextMenu) {\n            const node = event.rowNode.node;\n            if (this.contextMenuSelectionMode === 'separate') {\n                this.contextMenuSelection = node;\n                this.contextMenuSelectionChange.emit(node);\n                this.onContextMenuSelect.emit({ originalEvent: event.originalEvent, node: node });\n                this.contextMenu.show(event.originalEvent);\n                this.tableService.onContextMenu(node);\n            }\n            else if (this.contextMenuSelectionMode === 'joint') {\n                this.preventSelectionSetterPropagation = true;\n                let selected = this.isSelected(node);\n                let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n                if (!selected) {\n                    if (this.isSingleSelectionMode()) {\n                        this.selection = node;\n                        this.selectionChange.emit(node);\n                    }\n                    else if (this.isMultipleSelectionMode()) {\n                        this.selection = [node];\n                        this.selectionChange.emit(this.selection);\n                    }\n                    if (dataKeyValue) {\n                        this.selectionKeys[dataKeyValue] = 1;\n                    }\n                }\n                this.contextMenu.show(event.originalEvent);\n                this.onContextMenuSelect.emit({ originalEvent: event.originalEvent, node: node });\n            }\n        }\n    }\n    toggleNodeWithCheckbox(event) {\n        this.selection = this.selection || [];\n        this.preventSelectionSetterPropagation = true;\n        let node = event.rowNode.node;\n        let selected = this.isSelected(node);\n        if (selected) {\n            this.propagateSelectionDown(node, false);\n            if (event.rowNode.parent) {\n                this.propagateSelectionUp(node.parent, false);\n            }\n            this.selectionChange.emit(this.selection);\n            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n        }\n        else {\n            this.propagateSelectionDown(node, true);\n            if (event.rowNode.parent) {\n                this.propagateSelectionUp(node.parent, true);\n            }\n            this.selectionChange.emit(this.selection);\n            this.onNodeSelect.emit({ originalEvent: event, node: node });\n        }\n        this.tableService.onSelectionChange();\n    }\n    toggleNodesWithCheckbox(event, check) {\n        let data = this.filteredNodes || this.value;\n        this._selection = check && data ? data.slice() : [];\n        if (check) {\n            if (data && data.length) {\n                for (let node of data) {\n                    this.propagateSelectionDown(node, true);\n                }\n            }\n        }\n        else {\n            this._selection = [];\n            this.selectionKeys = {};\n        }\n        this.preventSelectionSetterPropagation = true;\n        this.selectionChange.emit(this._selection);\n        this.tableService.onSelectionChange();\n        this.onHeaderCheckboxToggle.emit({ originalEvent: event, checked: check });\n    }\n    propagateSelectionUp(node, select) {\n        if (node.children && node.children.length) {\n            let selectedChildCount = 0;\n            let childPartialSelected = false;\n            let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n            for (let child of node.children) {\n                if (this.isSelected(child))\n                    selectedChildCount++;\n                else if (child.partialSelected)\n                    childPartialSelected = true;\n            }\n            if (select && selectedChildCount == node.children.length) {\n                this._selection = [...this.selection || [], node];\n                node.partialSelected = false;\n                if (dataKeyValue) {\n                    this.selectionKeys[dataKeyValue] = 1;\n                }\n            }\n            else {\n                if (!select) {\n                    let index = this.findIndexInSelection(node);\n                    if (index >= 0) {\n                        this._selection = this.selection.filter((val, i) => i != index);\n                        if (dataKeyValue) {\n                            delete this.selectionKeys[dataKeyValue];\n                        }\n                    }\n                }\n                if (childPartialSelected || selectedChildCount > 0 && selectedChildCount != node.children.length)\n                    node.partialSelected = true;\n                else\n                    node.partialSelected = false;\n            }\n        }\n        let parent = node.parent;\n        if (parent) {\n            this.propagateSelectionUp(parent, select);\n        }\n    }\n    propagateSelectionDown(node, select) {\n        let index = this.findIndexInSelection(node);\n        let dataKeyValue = this.dataKey ? String(ObjectUtils.resolveFieldData(node.data, this.dataKey)) : null;\n        if (select && index == -1) {\n            this._selection = [...this.selection || [], node];\n            if (dataKeyValue) {\n                this.selectionKeys[dataKeyValue] = 1;\n            }\n        }\n        else if (!select && index > -1) {\n            this._selection = this.selection.filter((val, i) => i != index);\n            if (dataKeyValue) {\n                delete this.selectionKeys[dataKeyValue];\n            }\n        }\n        node.partialSelected = false;\n        if (node.children && node.children.length) {\n            for (let child of node.children) {\n                this.propagateSelectionDown(child, select);\n            }\n        }\n    }\n    isSelected(node) {\n        if (node && this.selection) {\n            if (this.dataKey) {\n                return this.selectionKeys[ObjectUtils.resolveFieldData(node.data, this.dataKey)] !== undefined;\n            }\n            else {\n                if (this.selection instanceof Array)\n                    return this.findIndexInSelection(node) > -1;\n                else\n                    return this.equals(node, this.selection);\n            }\n        }\n        return false;\n    }\n    findIndexInSelection(node) {\n        let index = -1;\n        if (this.selection && this.selection.length) {\n            for (let i = 0; i < this.selection.length; i++) {\n                if (this.equals(node, this.selection[i])) {\n                    index = i;\n                    break;\n                }\n            }\n        }\n        return index;\n    }\n    isSingleSelectionMode() {\n        return this.selectionMode === 'single';\n    }\n    isMultipleSelectionMode() {\n        return this.selectionMode === 'multiple';\n    }\n    equals(node1, node2) {\n        return this.compareSelectionBy === 'equals' ? (node1 === node2) : ObjectUtils.equals(node1.data, node2.data, this.dataKey);\n    }\n    filter(value, field, matchMode) {\n        if (this.filterTimeout) {\n            clearTimeout(this.filterTimeout);\n        }\n        if (!this.isFilterBlank(value)) {\n            this.filters[field] = { value: value, matchMode: matchMode };\n        }\n        else if (this.filters[field]) {\n            delete this.filters[field];\n        }\n        this.filterTimeout = setTimeout(() => {\n            this._filter();\n            this.filterTimeout = null;\n        }, this.filterDelay);\n    }\n    filterGlobal(value, matchMode) {\n        this.filter(value, 'global', matchMode);\n    }\n    isFilterBlank(filter) {\n        if (filter !== null && filter !== undefined) {\n            if ((typeof filter === 'string' && filter.trim().length == 0) || (filter instanceof Array && filter.length == 0))\n                return true;\n            else\n                return false;\n        }\n        return true;\n    }\n    _filter() {\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n        else {\n            if (!this.value) {\n                return;\n            }\n            if (!this.hasFilter()) {\n                this.filteredNodes = null;\n                if (this.paginator) {\n                    this.totalRecords = this.value ? this.value.length : 0;\n                }\n            }\n            else {\n                let globalFilterFieldsArray;\n                if (this.filters['global']) {\n                    if (!this.columns && !this.globalFilterFields)\n                        throw new Error('Global filtering requires dynamic columns or globalFilterFields to be defined.');\n                    else\n                        globalFilterFieldsArray = this.globalFilterFields || this.columns;\n                }\n                this.filteredNodes = [];\n                const isStrictMode = this.filterMode === 'strict';\n                let isValueChanged = false;\n                for (let node of this.value) {\n                    let copyNode = Object.assign({}, node);\n                    let localMatch = true;\n                    let globalMatch = false;\n                    let paramsWithoutNode;\n                    for (let prop in this.filters) {\n                        if (this.filters.hasOwnProperty(prop) && prop !== 'global') {\n                            let filterMeta = this.filters[prop];\n                            let filterField = prop;\n                            let filterValue = filterMeta.value;\n                            let filterMatchMode = filterMeta.matchMode || 'startsWith';\n                            let filterConstraint = this.filterService.filters[filterMatchMode];\n                            paramsWithoutNode = { filterField, filterValue, filterConstraint, isStrictMode };\n                            if ((isStrictMode && !(this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                                (!isStrictMode && !(this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))) {\n                                localMatch = false;\n                            }\n                            if (!localMatch) {\n                                break;\n                            }\n                        }\n                    }\n                    if (this.filters['global'] && !globalMatch && globalFilterFieldsArray) {\n                        for (let j = 0; j < globalFilterFieldsArray.length; j++) {\n                            let copyNodeForGlobal = Object.assign({}, copyNode);\n                            let filterField = globalFilterFieldsArray[j].field || globalFilterFieldsArray[j];\n                            let filterValue = this.filters['global'].value;\n                            let filterConstraint = this.filterService.filters[this.filters['global'].matchMode];\n                            paramsWithoutNode = { filterField, filterValue, filterConstraint, isStrictMode };\n                            if ((isStrictMode && (this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode) || this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode))) ||\n                                (!isStrictMode && (this.isFilterMatched(copyNodeForGlobal, paramsWithoutNode) || this.findFilteredNodes(copyNodeForGlobal, paramsWithoutNode)))) {\n                                globalMatch = true;\n                                copyNode = copyNodeForGlobal;\n                            }\n                        }\n                    }\n                    let matches = localMatch;\n                    if (this.filters['global']) {\n                        matches = localMatch && globalMatch;\n                    }\n                    if (matches) {\n                        this.filteredNodes.push(copyNode);\n                    }\n                    isValueChanged = isValueChanged || !localMatch || globalMatch || (localMatch && this.filteredNodes.length > 0) || (!globalMatch && this.filteredNodes.length === 0);\n                }\n                if (!isValueChanged) {\n                    this.filteredNodes = null;\n                }\n                if (this.paginator) {\n                    this.totalRecords = this.filteredNodes ? this.filteredNodes.length : this.value ? this.value.length : 0;\n                }\n            }\n        }\n        this.first = 0;\n        const filteredValue = this.filteredNodes || this.value;\n        this.onFilter.emit({\n            filters: this.filters,\n            filteredValue: filteredValue\n        });\n        this.tableService.onUIUpdate(filteredValue);\n        this.updateSerializedValue();\n        if (this.scrollable) {\n            this.resetScrollTop();\n        }\n    }\n    findFilteredNodes(node, paramsWithoutNode) {\n        if (node) {\n            let matched = false;\n            if (node.children) {\n                let childNodes = [...node.children];\n                node.children = [];\n                for (let childNode of childNodes) {\n                    let copyChildNode = Object.assign({}, childNode);\n                    if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                        matched = true;\n                        node.children.push(copyChildNode);\n                    }\n                }\n            }\n            if (matched) {\n                return true;\n            }\n        }\n    }\n    isFilterMatched(node, { filterField, filterValue, filterConstraint, isStrictMode }) {\n        let matched = false;\n        let dataFieldValue = ObjectUtils.resolveFieldData(node.data, filterField);\n        if (filterConstraint(dataFieldValue, filterValue, this.filterLocale)) {\n            matched = true;\n        }\n        if (!matched || (isStrictMode && !this.isNodeLeaf(node))) {\n            matched = this.findFilteredNodes(node, { filterField, filterValue, filterConstraint, isStrictMode }) || matched;\n        }\n        return matched;\n    }\n    isNodeLeaf(node) {\n        return node.leaf === false ? false : !(node.children && node.children.length);\n    }\n    hasFilter() {\n        let empty = true;\n        for (let prop in this.filters) {\n            if (this.filters.hasOwnProperty(prop)) {\n                empty = false;\n                break;\n            }\n        }\n        return !empty;\n    }\n    reset() {\n        this._sortField = null;\n        this._sortOrder = 1;\n        this._multiSortMeta = null;\n        this.tableService.onSort(null);\n        this.filteredNodes = null;\n        this.filters = {};\n        this.first = 0;\n        if (this.lazy) {\n            this.onLazyLoad.emit(this.createLazyLoadMetadata());\n        }\n        else {\n            this.totalRecords = (this._value ? this._value.length : 0);\n        }\n    }\n    updateEditingCell(cell, data, field) {\n        this.editingCell = cell;\n        this.editingCellData = data;\n        this.editingCellField = field;\n        this.bindDocumentEditListener();\n    }\n    isEditingCellValid() {\n        return (this.editingCell && DomHandler.find(this.editingCell, '.ng-invalid.ng-dirty').length === 0);\n    }\n    bindDocumentEditListener() {\n        if (!this.documentEditListener) {\n            this.documentEditListener = (event) => {\n                if (this.editingCell && !this.editingCellClick && this.isEditingCellValid()) {\n                    DomHandler.removeClass(this.editingCell, 'p-cell-editing');\n                    this.editingCell = null;\n                    this.onEditComplete.emit({ field: this.editingCellField, data: this.editingCellData });\n                    this.editingCellField = null;\n                    this.editingCellData = null;\n                    this.unbindDocumentEditListener();\n                }\n                this.editingCellClick = false;\n            };\n            document.addEventListener('click', this.documentEditListener);\n        }\n    }\n    unbindDocumentEditListener() {\n        if (this.documentEditListener) {\n            document.removeEventListener('click', this.documentEditListener);\n            this.documentEditListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.unbindDocumentEditListener();\n        this.editingCell = null;\n        this.editingCellField = null;\n        this.editingCellData = null;\n        this.initialized = null;\n    }\n}\nTreeTable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTable, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: TreeTableService }, { token: i1.FilterService }], target: i0.ɵɵFactoryTarget.Component });\nTreeTable.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TreeTable, selector: \"p-treeTable\", inputs: { columns: \"columns\", style: \"style\", styleClass: \"styleClass\", tableStyle: \"tableStyle\", tableStyleClass: \"tableStyleClass\", autoLayout: \"autoLayout\", lazy: \"lazy\", lazyLoadOnInit: \"lazyLoadOnInit\", paginator: \"paginator\", rows: \"rows\", first: \"first\", pageLinks: \"pageLinks\", rowsPerPageOptions: \"rowsPerPageOptions\", alwaysShowPaginator: \"alwaysShowPaginator\", paginatorPosition: \"paginatorPosition\", paginatorDropdownAppendTo: \"paginatorDropdownAppendTo\", currentPageReportTemplate: \"currentPageReportTemplate\", showCurrentPageReport: \"showCurrentPageReport\", showJumpToPageDropdown: \"showJumpToPageDropdown\", showFirstLastIcon: \"showFirstLastIcon\", showPageLinks: \"showPageLinks\", defaultSortOrder: \"defaultSortOrder\", sortMode: \"sortMode\", resetPageOnSort: \"resetPageOnSort\", customSort: \"customSort\", selectionMode: \"selectionMode\", contextMenuSelection: \"contextMenuSelection\", contextMenuSelectionMode: \"contextMenuSelectionMode\", dataKey: \"dataKey\", metaKeySelection: \"metaKeySelection\", compareSelectionBy: \"compareSelectionBy\", rowHover: \"rowHover\", loading: \"loading\", loadingIcon: \"loadingIcon\", showLoader: \"showLoader\", scrollable: \"scrollable\", scrollHeight: \"scrollHeight\", virtualScroll: \"virtualScroll\", virtualScrollItemSize: \"virtualScrollItemSize\", virtualScrollOptions: \"virtualScrollOptions\", virtualScrollDelay: \"virtualScrollDelay\", frozenWidth: \"frozenWidth\", frozenColumns: \"frozenColumns\", resizableColumns: \"resizableColumns\", columnResizeMode: \"columnResizeMode\", reorderableColumns: \"reorderableColumns\", contextMenu: \"contextMenu\", rowTrackBy: \"rowTrackBy\", filters: \"filters\", globalFilterFields: \"globalFilterFields\", filterDelay: \"filterDelay\", filterMode: \"filterMode\", filterLocale: \"filterLocale\", virtualRowHeight: \"virtualRowHeight\", value: \"value\", totalRecords: \"totalRecords\", sortField: \"sortField\", sortOrder: \"sortOrder\", multiSortMeta: \"multiSortMeta\", selection: \"selection\" }, outputs: { selectionChange: \"selectionChange\", contextMenuSelectionChange: \"contextMenuSelectionChange\", onFilter: \"onFilter\", onNodeExpand: \"onNodeExpand\", onNodeCollapse: \"onNodeCollapse\", onPage: \"onPage\", onSort: \"onSort\", onLazyLoad: \"onLazyLoad\", sortFunction: \"sortFunction\", onColResize: \"onColResize\", onColReorder: \"onColReorder\", onNodeSelect: \"onNodeSelect\", onNodeUnselect: \"onNodeUnselect\", onContextMenuSelect: \"onContextMenuSelect\", onHeaderCheckboxToggle: \"onHeaderCheckboxToggle\", onEditInit: \"onEditInit\", onEditComplete: \"onEditComplete\", onEditCancel: \"onEditCancel\" }, host: { classAttribute: \"p-element\" }, providers: [TreeTableService], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }, { propertyName: \"resizeHelperViewChild\", first: true, predicate: [\"resizeHelper\"], descendants: true }, { propertyName: \"reorderIndicatorUpViewChild\", first: true, predicate: [\"reorderIndicatorUp\"], descendants: true }, { propertyName: \"reorderIndicatorDownViewChild\", first: true, predicate: [\"reorderIndicatorDown\"], descendants: true }, { propertyName: \"tableViewChild\", first: true, predicate: [\"table\"], descendants: true }, { propertyName: \"scrollableViewChild\", first: true, predicate: [\"scrollableView\"], descendants: true }, { propertyName: \"scrollableFrozenViewChild\", first: true, predicate: [\"scrollableFrozenView\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" data-scrollselectors=\".p-treetable-scrollable-body\"\n                [ngClass]=\"{'p-treetable p-component': true,\n                'p-treetable-hoverable-rows': (rowHover||(selectionMode === 'single' || selectionMode === 'multiple')),\n                'p-treetable-auto-layout': autoLayout,\n                'p-treetable-resizable': resizableColumns,\n                'p-treetable-resizable-fit': (resizableColumns && columnResizeMode === 'fit'),\n                'p-treetable-flex-scrollable': (scrollable && scrollHeight === 'flex')}\">\n            <div class=\"p-treetable-loading\" *ngIf=\"loading && showLoader\">\n                <div class=\"p-treetable-loading-overlay p-component-overlay\">\n                    <i [class]=\"'p-treetable-loading-icon pi-spin ' + loadingIcon\"></i>\n                </div>\n            </div>\n            <div *ngIf=\"captionTemplate\" class=\"p-treetable-header\">\n                <ng-container *ngTemplateOutlet=\"captionTemplate\"></ng-container>\n            </div>\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-top\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'top' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n\n            <div class=\"p-treetable-wrapper\" *ngIf=\"!scrollable\">\n                <table #table [ngClass]=\"tableStyleClass\" [ngStyle]=\"tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"headerTemplate; context: {$implicit: columns}\"></ng-container>\n                    </thead>\n                    <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"bodyTemplate\"></tbody>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n\n            <div class=\"p-treetable-scrollable-wrapper\" *ngIf=\"scrollable\">\n               <div class=\"p-treetable-scrollable-view p-treetable-frozen-view\" *ngIf=\"frozenColumns||frozenBodyTemplate\" #scrollableFrozenView [ttScrollableView]=\"frozenColumns\" [frozen]=\"true\" [ngStyle]=\"{width: frozenWidth}\" [scrollHeight]=\"scrollHeight\"></div>\n               <div class=\"p-treetable-scrollable-view\" #scrollableView [ttScrollableView]=\"columns\" [frozen]=\"false\" [scrollHeight]=\"scrollHeight\" [ngStyle]=\"{left: frozenWidth, width: 'calc(100% - '+frozenWidth+')'}\"></div>\n            </div>\n\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-bottom\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'bottom' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n            <div *ngIf=\"summaryTemplate\" class=\"p-treetable-footer\">\n                <ng-container *ngTemplateOutlet=\"summaryTemplate\"></ng-container>\n            </div>\n\n            <div #resizeHelper class=\"p-column-resizer-helper\" style=\"display:none\" *ngIf=\"resizableColumns\"></div>\n\n            <span #reorderIndicatorUp class=\"pi pi-arrow-down p-treetable-reorder-indicator-up\" *ngIf=\"reorderableColumns\"></span>\n            <span #reorderIndicatorDown class=\"pi pi-arrow-up p-treetable-reorder-indicator-down\" *ngIf=\"reorderableColumns\"></span>\n        </div>\n    `, isInline: true, styles: [\".p-treetable{position:relative}.p-treetable table{border-collapse:collapse;width:100%;table-layout:fixed}.p-treetable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-treetable .p-sortable-column .p-column-title,.p-treetable .p-sortable-column .p-sortable-column-icon,.p-treetable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-treetable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-treetable-auto-layout>.p-treetable-wrapper{overflow-x:auto}.p-treetable-auto-layout>.p-treetable-wrapper>table{table-layout:auto}.p-treetable-hoverable-rows .p-treetable-tbody>tr{cursor:pointer}.p-treetable-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;vertical-align:middle;overflow:hidden;position:relative}p-treetabletoggler+p-treetablecheckbox .p-checkbox{vertical-align:middle}p-treetabletoggler+p-treetablecheckbox+span{vertical-align:middle}.p-treetable-scrollable-wrapper{position:relative}.p-treetable-scrollable-header,.p-treetable-scrollable-footer{overflow:hidden}.p-treetable-scrollable-body{overflow:auto;position:relative}.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td{border-top:0 none}.p-treetable-virtual-table{position:absolute}.p-treetable-frozen-view .p-treetable-scrollable-body{overflow:hidden}.p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child{border-right:0 none}.p-treetable-unfrozen-view{position:absolute;top:0}.p-treetable-flex-scrollable,.p-treetable-flex-scrollable .p-treetable-scrollable-wrapper,.p-treetable-flex-scrollable .p-treetable-scrollable-view{display:flex;flex-direction:column;flex:1;height:100%}.p-treetable-flex-scrollable .p-treetable-virtual-scrollable-body{flex:1}.p-treetable-resizable>.p-treetable-wrapper{overflow-x:auto}.p-treetable-resizable .p-treetable-thead>tr>th,.p-treetable-resizable .p-treetable-tfoot>tr>td,.p-treetable-resizable .p-treetable-tbody>tr>td{overflow:hidden}.p-treetable-resizable .p-resizable-column{background-clip:padding-box;position:relative}.p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer{display:none}.p-treetable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-treetable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-treetable .p-row-editor-init,.p-treetable .p-row-editor-save,.p-treetable .p-row-editor-cancel,.p-treetable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-treetable-reorder-indicator-up,.p-treetable-reorder-indicator-down{position:absolute;display:none}[ttReorderableColumn]{cursor:move}.p-treetable .p-treetable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-treetable .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i2.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgTemplateOutlet; }), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(function () { return i3.Paginator; }), selector: \"p-paginator\", inputs: [\"pageLinkSize\", \"style\", \"styleClass\", \"alwaysShow\", \"templateLeft\", \"templateRight\", \"dropdownAppendTo\", \"dropdownScrollHeight\", \"currentPageReportTemplate\", \"showCurrentPageReport\", \"showFirstLastIcon\", \"totalRecords\", \"rows\", \"rowsPerPageOptions\", \"showJumpToPageDropdown\", \"showJumpToPageInput\", \"showPageLinks\", \"dropdownItemTemplate\", \"first\"], outputs: [\"onPageChange\"] }, { kind: \"component\", type: i0.forwardRef(function () { return TTScrollableView; }), selector: \"[ttScrollableView]\", inputs: [\"ttScrollableView\", \"frozen\", \"scrollHeight\"] }, { kind: \"component\", type: i0.forwardRef(function () { return TTBody; }), selector: \"[pTreeTableBody]\", inputs: [\"pTreeTableBody\", \"pTreeTableBodyTemplate\", \"frozen\", \"serializedNodes\", \"scrollerOptions\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTable, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-treeTable', template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" data-scrollselectors=\".p-treetable-scrollable-body\"\n                [ngClass]=\"{'p-treetable p-component': true,\n                'p-treetable-hoverable-rows': (rowHover||(selectionMode === 'single' || selectionMode === 'multiple')),\n                'p-treetable-auto-layout': autoLayout,\n                'p-treetable-resizable': resizableColumns,\n                'p-treetable-resizable-fit': (resizableColumns && columnResizeMode === 'fit'),\n                'p-treetable-flex-scrollable': (scrollable && scrollHeight === 'flex')}\">\n            <div class=\"p-treetable-loading\" *ngIf=\"loading && showLoader\">\n                <div class=\"p-treetable-loading-overlay p-component-overlay\">\n                    <i [class]=\"'p-treetable-loading-icon pi-spin ' + loadingIcon\"></i>\n                </div>\n            </div>\n            <div *ngIf=\"captionTemplate\" class=\"p-treetable-header\">\n                <ng-container *ngTemplateOutlet=\"captionTemplate\"></ng-container>\n            </div>\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-top\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'top' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n\n            <div class=\"p-treetable-wrapper\" *ngIf=\"!scrollable\">\n                <table #table [ngClass]=\"tableStyleClass\" [ngStyle]=\"tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"headerTemplate; context: {$implicit: columns}\"></ng-container>\n                    </thead>\n                    <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"bodyTemplate\"></tbody>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n\n            <div class=\"p-treetable-scrollable-wrapper\" *ngIf=\"scrollable\">\n               <div class=\"p-treetable-scrollable-view p-treetable-frozen-view\" *ngIf=\"frozenColumns||frozenBodyTemplate\" #scrollableFrozenView [ttScrollableView]=\"frozenColumns\" [frozen]=\"true\" [ngStyle]=\"{width: frozenWidth}\" [scrollHeight]=\"scrollHeight\"></div>\n               <div class=\"p-treetable-scrollable-view\" #scrollableView [ttScrollableView]=\"columns\" [frozen]=\"false\" [scrollHeight]=\"scrollHeight\" [ngStyle]=\"{left: frozenWidth, width: 'calc(100% - '+frozenWidth+')'}\"></div>\n            </div>\n\n            <p-paginator [rows]=\"rows\" [first]=\"first\" [totalRecords]=\"totalRecords\" [pageLinkSize]=\"pageLinks\" styleClass=\"p-paginator-bottom\" [alwaysShow]=\"alwaysShowPaginator\"\n                (onPageChange)=\"onPageChange($event)\" [rowsPerPageOptions]=\"rowsPerPageOptions\" *ngIf=\"paginator && (paginatorPosition === 'bottom' || paginatorPosition =='both')\"\n                [templateLeft]=\"paginatorLeftTemplate\" [templateRight]=\"paginatorRightTemplate\" [dropdownAppendTo]=\"paginatorDropdownAppendTo\"\n                [currentPageReportTemplate]=\"currentPageReportTemplate\" [showFirstLastIcon]=\"showFirstLastIcon\" [dropdownItemTemplate]=\"paginatorDropdownItemTemplate\" [showCurrentPageReport]=\"showCurrentPageReport\" [showJumpToPageDropdown]=\"showJumpToPageDropdown\" [showPageLinks]=\"showPageLinks\"></p-paginator>\n            <div *ngIf=\"summaryTemplate\" class=\"p-treetable-footer\">\n                <ng-container *ngTemplateOutlet=\"summaryTemplate\"></ng-container>\n            </div>\n\n            <div #resizeHelper class=\"p-column-resizer-helper\" style=\"display:none\" *ngIf=\"resizableColumns\"></div>\n\n            <span #reorderIndicatorUp class=\"pi pi-arrow-down p-treetable-reorder-indicator-up\" *ngIf=\"reorderableColumns\"></span>\n            <span #reorderIndicatorDown class=\"pi pi-arrow-up p-treetable-reorder-indicator-down\" *ngIf=\"reorderableColumns\"></span>\n        </div>\n    `, providers: [TreeTableService], encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-treetable{position:relative}.p-treetable table{border-collapse:collapse;width:100%;table-layout:fixed}.p-treetable .p-sortable-column{cursor:pointer;-webkit-user-select:none;user-select:none}.p-treetable .p-sortable-column .p-column-title,.p-treetable .p-sortable-column .p-sortable-column-icon,.p-treetable .p-sortable-column .p-sortable-column-badge{vertical-align:middle}.p-treetable .p-sortable-column .p-sortable-column-badge{display:inline-flex;align-items:center;justify-content:center}.p-treetable-auto-layout>.p-treetable-wrapper{overflow-x:auto}.p-treetable-auto-layout>.p-treetable-wrapper>table{table-layout:auto}.p-treetable-hoverable-rows .p-treetable-tbody>tr{cursor:pointer}.p-treetable-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;vertical-align:middle;overflow:hidden;position:relative}p-treetabletoggler+p-treetablecheckbox .p-checkbox{vertical-align:middle}p-treetabletoggler+p-treetablecheckbox+span{vertical-align:middle}.p-treetable-scrollable-wrapper{position:relative}.p-treetable-scrollable-header,.p-treetable-scrollable-footer{overflow:hidden}.p-treetable-scrollable-body{overflow:auto;position:relative}.p-treetable-scrollable-body>table>.p-treetable-tbody>tr:first-child>td{border-top:0 none}.p-treetable-virtual-table{position:absolute}.p-treetable-frozen-view .p-treetable-scrollable-body{overflow:hidden}.p-treetable-frozen-view>.p-treetable-scrollable-body>table>.p-treetable-tbody>tr>td:last-child{border-right:0 none}.p-treetable-unfrozen-view{position:absolute;top:0}.p-treetable-flex-scrollable,.p-treetable-flex-scrollable .p-treetable-scrollable-wrapper,.p-treetable-flex-scrollable .p-treetable-scrollable-view{display:flex;flex-direction:column;flex:1;height:100%}.p-treetable-flex-scrollable .p-treetable-virtual-scrollable-body{flex:1}.p-treetable-resizable>.p-treetable-wrapper{overflow-x:auto}.p-treetable-resizable .p-treetable-thead>tr>th,.p-treetable-resizable .p-treetable-tfoot>tr>td,.p-treetable-resizable .p-treetable-tbody>tr>td{overflow:hidden}.p-treetable-resizable .p-resizable-column{background-clip:padding-box;position:relative}.p-treetable-resizable-fit .p-resizable-column:last-child .p-column-resizer{display:none}.p-treetable .p-column-resizer{display:block;position:absolute!important;top:0;right:0;margin:0;width:.5rem;height:100%;padding:0;cursor:col-resize;border:1px solid transparent}.p-treetable .p-column-resizer-helper{width:1px;position:absolute;z-index:10;display:none}.p-treetable .p-row-editor-init,.p-treetable .p-row-editor-save,.p-treetable .p-row-editor-cancel,.p-treetable .p-row-toggler{display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-treetable-reorder-indicator-up,.p-treetable-reorder-indicator-down{position:absolute;display:none}[ttReorderableColumn]{cursor:move}.p-treetable .p-treetable-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-treetable .p-scroller-loading{transform:none!important;min-height:0;position:sticky;top:0;left:0}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: TreeTableService }, { type: i1.FilterService }]; }, propDecorators: { columns: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tableStyle: [{\n                type: Input\n            }], tableStyleClass: [{\n                type: Input\n            }], autoLayout: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], lazyLoadOnInit: [{\n                type: Input\n            }], paginator: [{\n                type: Input\n            }], rows: [{\n                type: Input\n            }], first: [{\n                type: Input\n            }], pageLinks: [{\n                type: Input\n            }], rowsPerPageOptions: [{\n                type: Input\n            }], alwaysShowPaginator: [{\n                type: Input\n            }], paginatorPosition: [{\n                type: Input\n            }], paginatorDropdownAppendTo: [{\n                type: Input\n            }], currentPageReportTemplate: [{\n                type: Input\n            }], showCurrentPageReport: [{\n                type: Input\n            }], showJumpToPageDropdown: [{\n                type: Input\n            }], showFirstLastIcon: [{\n                type: Input\n            }], showPageLinks: [{\n                type: Input\n            }], defaultSortOrder: [{\n                type: Input\n            }], sortMode: [{\n                type: Input\n            }], resetPageOnSort: [{\n                type: Input\n            }], customSort: [{\n                type: Input\n            }], selectionMode: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], contextMenuSelection: [{\n                type: Input\n            }], contextMenuSelectionChange: [{\n                type: Output\n            }], contextMenuSelectionMode: [{\n                type: Input\n            }], dataKey: [{\n                type: Input\n            }], metaKeySelection: [{\n                type: Input\n            }], compareSelectionBy: [{\n                type: Input\n            }], rowHover: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], loadingIcon: [{\n                type: Input\n            }], showLoader: [{\n                type: Input\n            }], scrollable: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], virtualScrollItemSize: [{\n                type: Input\n            }], virtualScrollOptions: [{\n                type: Input\n            }], virtualScrollDelay: [{\n                type: Input\n            }], frozenWidth: [{\n                type: Input\n            }], frozenColumns: [{\n                type: Input\n            }], resizableColumns: [{\n                type: Input\n            }], columnResizeMode: [{\n                type: Input\n            }], reorderableColumns: [{\n                type: Input\n            }], contextMenu: [{\n                type: Input\n            }], rowTrackBy: [{\n                type: Input\n            }], filters: [{\n                type: Input\n            }], globalFilterFields: [{\n                type: Input\n            }], filterDelay: [{\n                type: Input\n            }], filterMode: [{\n                type: Input\n            }], filterLocale: [{\n                type: Input\n            }], onFilter: [{\n                type: Output\n            }], onNodeExpand: [{\n                type: Output\n            }], onNodeCollapse: [{\n                type: Output\n            }], onPage: [{\n                type: Output\n            }], onSort: [{\n                type: Output\n            }], onLazyLoad: [{\n                type: Output\n            }], sortFunction: [{\n                type: Output\n            }], onColResize: [{\n                type: Output\n            }], onColReorder: [{\n                type: Output\n            }], onNodeSelect: [{\n                type: Output\n            }], onNodeUnselect: [{\n                type: Output\n            }], onContextMenuSelect: [{\n                type: Output\n            }], onHeaderCheckboxToggle: [{\n                type: Output\n            }], onEditInit: [{\n                type: Output\n            }], onEditComplete: [{\n                type: Output\n            }], onEditCancel: [{\n                type: Output\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }], resizeHelperViewChild: [{\n                type: ViewChild,\n                args: ['resizeHelper']\n            }], reorderIndicatorUpViewChild: [{\n                type: ViewChild,\n                args: ['reorderIndicatorUp']\n            }], reorderIndicatorDownViewChild: [{\n                type: ViewChild,\n                args: ['reorderIndicatorDown']\n            }], tableViewChild: [{\n                type: ViewChild,\n                args: ['table']\n            }], scrollableViewChild: [{\n                type: ViewChild,\n                args: ['scrollableView']\n            }], scrollableFrozenViewChild: [{\n                type: ViewChild,\n                args: ['scrollableFrozenView']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], virtualRowHeight: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], totalRecords: [{\n                type: Input\n            }], sortField: [{\n                type: Input\n            }], sortOrder: [{\n                type: Input\n            }], multiSortMeta: [{\n                type: Input\n            }], selection: [{\n                type: Input\n            }] } });\nclass TTBody {\n    constructor(tt, treeTableService, cd) {\n        this.tt = tt;\n        this.treeTableService = treeTableService;\n        this.cd = cd;\n        this.subscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n            if (this.tt.virtualScroll) {\n                this.cd.detectChanges();\n            }\n        });\n    }\n    getScrollerOption(option, options) {\n        if (this.tt.virtualScroll) {\n            options = options || this.scrollerOptions;\n            return options ? options[option] : null;\n        }\n        return null;\n    }\n    getRowIndex(rowIndex) {\n        const getItemOptions = this.getScrollerOption('getItemOptions');\n        return getItemOptions ? getItemOptions(rowIndex).index : rowIndex;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTBody.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTBody, deps: [{ token: TreeTable }, { token: TreeTableService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTTBody.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTBody, selector: \"[pTreeTableBody]\", inputs: { columns: [\"pTreeTableBody\", \"columns\"], template: [\"pTreeTableBodyTemplate\", \"template\"], frozen: \"frozen\", serializedNodes: \"serializedNodes\", scrollerOptions: \"scrollerOptions\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <ng-template ngFor let-serializedNode let-rowIndex=\"index\" [ngForOf]=\"serializedNodes||tt.serializedValue\" [ngForTrackBy]=\"tt.rowTrackBy\">\n            <ng-container *ngIf=\"serializedNode.visible\">\n                <ng-container *ngTemplateOutlet=\"template; context: {$implicit: serializedNode, node: serializedNode.node, rowData: serializedNode.node.data, columns: columns}\"></ng-container>\n            </ng-container>\n        </ng-template>\n        <ng-container *ngIf=\"tt.isEmpty()\">\n            <ng-container *ngTemplateOutlet=\"tt.emptyMessageTemplate; context: {$implicit: columns, frozen: frozen}\"></ng-container>\n        </ng-container>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTBody, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[pTreeTableBody]',\n                    template: `\n        <ng-template ngFor let-serializedNode let-rowIndex=\"index\" [ngForOf]=\"serializedNodes||tt.serializedValue\" [ngForTrackBy]=\"tt.rowTrackBy\">\n            <ng-container *ngIf=\"serializedNode.visible\">\n                <ng-container *ngTemplateOutlet=\"template; context: {$implicit: serializedNode, node: serializedNode.node, rowData: serializedNode.node.data, columns: columns}\"></ng-container>\n            </ng-container>\n        </ng-template>\n        <ng-container *ngIf=\"tt.isEmpty()\">\n            <ng-container *ngTemplateOutlet=\"tt.emptyMessageTemplate; context: {$implicit: columns, frozen: frozen}\"></ng-container>\n        </ng-container>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { columns: [{\n                type: Input,\n                args: [\"pTreeTableBody\"]\n            }], template: [{\n                type: Input,\n                args: [\"pTreeTableBodyTemplate\"]\n            }], frozen: [{\n                type: Input\n            }], serializedNodes: [{\n                type: Input\n            }], scrollerOptions: [{\n                type: Input\n            }] } });\nclass TTScrollableView {\n    constructor(tt, el, zone) {\n        this.tt = tt;\n        this.el = el;\n        this.zone = zone;\n    }\n    get scrollHeight() {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n        this._scrollHeight = val;\n        if (val != null && (val.includes('%') || val.includes('calc'))) {\n            console.log('Percentage scroll height calculation is removed in favor of the more performant CSS based flex mode, use scrollHeight=\"flex\" instead.');\n        }\n    }\n    ngAfterViewInit() {\n        if (!this.frozen) {\n            if (this.tt.frozenColumns || this.tt.frozenBodyTemplate) {\n                DomHandler.addClass(this.el.nativeElement, 'p-treetable-unfrozen-view');\n            }\n            let frozenView = this.el.nativeElement.previousElementSibling;\n            if (frozenView) {\n                if (this.tt.virtualScroll)\n                    this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-scroller-viewport');\n                else\n                    this.frozenSiblingBody = DomHandler.findSingle(frozenView, '.p-treetable-scrollable-body');\n            }\n            let scrollBarWidth = DomHandler.calculateScrollbarWidth();\n            this.scrollHeaderBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n            if (this.scrollFooterBoxViewChild && this.scrollFooterBoxViewChild.nativeElement) {\n                this.scrollFooterBoxViewChild.nativeElement.style.paddingRight = scrollBarWidth + 'px';\n            }\n        }\n        else {\n            if (this.scrollableAlignerViewChild && this.scrollableAlignerViewChild.nativeElement) {\n                this.scrollableAlignerViewChild.nativeElement.style.height = DomHandler.calculateScrollbarHeight() + 'px';\n            }\n        }\n        this.bindEvents();\n    }\n    bindEvents() {\n        this.zone.runOutsideAngular(() => {\n            if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n                this.headerScrollListener = this.onHeaderScroll.bind(this);\n                this.scrollHeaderBoxViewChild.nativeElement.addEventListener('scroll', this.headerScrollListener);\n            }\n            if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n                this.footerScrollListener = this.onFooterScroll.bind(this);\n                this.scrollFooterViewChild.nativeElement.addEventListener('scroll', this.footerScrollListener);\n            }\n            if (!this.frozen) {\n                this.bodyScrollListener = this.onBodyScroll.bind(this);\n                if (this.tt.virtualScroll)\n                    this.scroller.getElementRef().nativeElement.addEventListener('scroll', this.bodyScrollListener);\n                else\n                    this.scrollBodyViewChild.nativeElement.addEventListener('scroll', this.bodyScrollListener);\n            }\n        });\n    }\n    unbindEvents() {\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            this.scrollHeaderBoxViewChild.nativeElement.removeEventListener('scroll', this.headerScrollListener);\n        }\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            this.scrollFooterViewChild.nativeElement.removeEventListener('scroll', this.footerScrollListener);\n        }\n        if (this.scrollBodyViewChild && this.scrollBodyViewChild.nativeElement) {\n            this.scrollBodyViewChild.nativeElement.removeEventListener('scroll', this.bodyScrollListener);\n        }\n        if (this.scroller && this.scroller.getElementRef()) {\n            this.scroller.getElementRef().nativeElement.removeEventListener('scroll', this.bodyScrollListener);\n        }\n    }\n    onHeaderScroll() {\n        const scrollLeft = this.scrollHeaderViewChild.nativeElement.scrollLeft;\n        this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            this.scrollFooterViewChild.nativeElement.scrollLeft = scrollLeft;\n        }\n        this.preventBodyScrollPropagation = true;\n    }\n    onFooterScroll() {\n        const scrollLeft = this.scrollFooterViewChild.nativeElement.scrollLeft;\n        this.scrollBodyViewChild.nativeElement.scrollLeft = scrollLeft;\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            this.scrollHeaderViewChild.nativeElement.scrollLeft = scrollLeft;\n        }\n        this.preventBodyScrollPropagation = true;\n    }\n    onBodyScroll(event) {\n        if (this.preventBodyScrollPropagation) {\n            this.preventBodyScrollPropagation = false;\n            return;\n        }\n        if (this.scrollHeaderViewChild && this.scrollHeaderViewChild.nativeElement) {\n            this.scrollHeaderBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n        }\n        if (this.scrollFooterViewChild && this.scrollFooterViewChild.nativeElement) {\n            this.scrollFooterBoxViewChild.nativeElement.style.marginLeft = -1 * event.target.scrollLeft + 'px';\n        }\n        if (this.frozenSiblingBody) {\n            this.frozenSiblingBody.scrollTop = event.target.scrollTop;\n        }\n    }\n    scrollToVirtualIndex(index) {\n        if (this.scroller) {\n            this.scroller.scrollToIndex(index);\n        }\n    }\n    scrollTo(options) {\n        if (this.scroller) {\n            this.scroller.scrollTo(options);\n        }\n        else {\n            if (this.scrollBodyViewChild.nativeElement.scrollTo) {\n                this.scrollBodyViewChild.nativeElement.scrollTo(options);\n            }\n            else {\n                this.scrollBodyViewChild.nativeElement.scrollLeft = options.left;\n                this.scrollBodyViewChild.nativeElement.scrollTop = options.top;\n            }\n        }\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n        this.frozenSiblingBody = null;\n    }\n}\nTTScrollableView.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTScrollableView, deps: [{ token: TreeTable }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nTTScrollableView.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTScrollableView, selector: \"[ttScrollableView]\", inputs: { columns: [\"ttScrollableView\", \"columns\"], frozen: \"frozen\", scrollHeight: \"scrollHeight\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"scrollHeaderViewChild\", first: true, predicate: [\"scrollHeader\"], descendants: true }, { propertyName: \"scrollHeaderBoxViewChild\", first: true, predicate: [\"scrollHeaderBox\"], descendants: true }, { propertyName: \"scrollBodyViewChild\", first: true, predicate: [\"scrollBody\"], descendants: true }, { propertyName: \"scrollTableViewChild\", first: true, predicate: [\"scrollTable\"], descendants: true }, { propertyName: \"scrollLoadingTableViewChild\", first: true, predicate: [\"loadingTable\"], descendants: true }, { propertyName: \"scrollFooterViewChild\", first: true, predicate: [\"scrollFooter\"], descendants: true }, { propertyName: \"scrollFooterBoxViewChild\", first: true, predicate: [\"scrollFooterBox\"], descendants: true }, { propertyName: \"scrollableAlignerViewChild\", first: true, predicate: [\"scrollableAligner\"], descendants: true }, { propertyName: \"scroller\", first: true, predicate: [\"scroller\"], descendants: true }], ngImport: i0, template: `\n        <div #scrollHeader class=\"p-treetable-scrollable-header\">\n            <div #scrollHeaderBox class=\"p-treetable-scrollable-header-box\">\n                <table class=\"p-treetable-scrollable-header-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenHeaderTemplate||tt.headerTemplate : tt.headerTemplate; context {$implicit: columns}\"></ng-container>\n                    </thead>\n                </table>\n            </div>\n        </div>\n\n        <p-scroller *ngIf=\"tt.virtualScroll\" #scroller [items]=\"tt.serializedValue\" styleClass=\"p-treetable-scrollable-body\" [style]=\"{'height': tt.scrollHeight !== 'flex' ? tt.scrollHeight : undefined}\" [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\" [itemSize]=\"tt.virtualScrollItemSize||tt._virtualRowHeight\"\n            [lazy]=\"tt.lazy\" (onLazyLoad)=\"tt.onLazyItemLoad($event)\" [options]=\"tt.virtualScrollOptions\">\n            <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n            </ng-template>\n            <ng-container *ngIf=\"loaderTemplate\">\n                <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                    <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                </ng-template>\n            </ng-container>\n        </p-scroller>\n        <ng-container *ngIf=\"!tt.virtualScroll\">\n            <div #scrollBody class=\"p-treetable-scrollable-body\" [ngStyle]=\"{'max-height': tt.scrollHeight !== 'flex' ? scrollHeight : undefined, 'overflow-y': !frozen && tt.scrollHeight ? 'scroll' : undefined}\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: serializedValue, options: {}}\"></ng-container>\n            </div>\n        </ng-container>\n\n        <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n            <table #scrollTable [class]=\"tt.tableStyleClass\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"tt.tableStyle\" [style]=\"scrollerOptions.contentStyle\">\n                <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"frozen ? tt.frozenBodyTemplate||tt.bodyTemplate : tt.bodyTemplate\" [serializedNodes]=\"items\" [frozen]=\"frozen\"></tbody>\n            </table>\n            <div #scrollableAligner style=\"background-color:transparent\" *ngIf=\"frozen\"></div>\n        </ng-template>\n\n        <div #scrollFooter *ngIf=\"tt.footerTemplate\" class=\"p-treetable-scrollable-footer\">\n            <div #scrollFooterBox class=\"p-treetable-scrollable-footer-box\">\n                <table class=\"p-treetable-scrollable-footer-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenFooterTemplate||tt.footerTemplate : tt.footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i1.PrimeTemplate, selector: \"[pTemplate]\", inputs: [\"type\", \"pTemplate\"] }, { kind: \"component\", type: i4.Scroller, selector: \"p-scroller\", inputs: [\"id\", \"style\", \"styleClass\", \"tabindex\", \"items\", \"itemSize\", \"scrollHeight\", \"scrollWidth\", \"orientation\", \"delay\", \"resizeDelay\", \"lazy\", \"disabled\", \"loaderDisabled\", \"columns\", \"showSpacer\", \"showLoader\", \"numToleratedItems\", \"loading\", \"autoSize\", \"trackBy\", \"options\"], outputs: [\"onLazyLoad\", \"onScroll\", \"onScrollIndexChange\"] }, { kind: \"component\", type: TTBody, selector: \"[pTreeTableBody]\", inputs: [\"pTreeTableBody\", \"pTreeTableBodyTemplate\", \"frozen\", \"serializedNodes\", \"scrollerOptions\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTScrollableView, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[ttScrollableView]',\n                    template: `\n        <div #scrollHeader class=\"p-treetable-scrollable-header\">\n            <div #scrollHeaderBox class=\"p-treetable-scrollable-header-box\">\n                <table class=\"p-treetable-scrollable-header-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <thead class=\"p-treetable-thead\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenHeaderTemplate||tt.headerTemplate : tt.headerTemplate; context {$implicit: columns}\"></ng-container>\n                    </thead>\n                </table>\n            </div>\n        </div>\n\n        <p-scroller *ngIf=\"tt.virtualScroll\" #scroller [items]=\"tt.serializedValue\" styleClass=\"p-treetable-scrollable-body\" [style]=\"{'height': tt.scrollHeight !== 'flex' ? tt.scrollHeight : undefined}\" [scrollHeight]=\"scrollHeight !== 'flex' ? undefined : '100%'\" [itemSize]=\"tt.virtualScrollItemSize||tt._virtualRowHeight\"\n            [lazy]=\"tt.lazy\" (onLazyLoad)=\"tt.onLazyItemLoad($event)\" [options]=\"tt.virtualScrollOptions\">\n            <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n            </ng-template>\n            <ng-container *ngIf=\"loaderTemplate\">\n                <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                    <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                </ng-template>\n            </ng-container>\n        </p-scroller>\n        <ng-container *ngIf=\"!tt.virtualScroll\">\n            <div #scrollBody class=\"p-treetable-scrollable-body\" [ngStyle]=\"{'max-height': tt.scrollHeight !== 'flex' ? scrollHeight : undefined, 'overflow-y': !frozen && tt.scrollHeight ? 'scroll' : undefined}\">\n                <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: serializedValue, options: {}}\"></ng-container>\n            </div>\n        </ng-container>\n\n        <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n            <table #scrollTable [class]=\"tt.tableStyleClass\" [ngClass]=\"scrollerOptions.contentStyleClass\" [ngStyle]=\"tt.tableStyle\" [style]=\"scrollerOptions.contentStyle\">\n                <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                <tbody class=\"p-treetable-tbody\" [pTreeTableBody]=\"columns\" [pTreeTableBodyTemplate]=\"frozen ? tt.frozenBodyTemplate||tt.bodyTemplate : tt.bodyTemplate\" [serializedNodes]=\"items\" [frozen]=\"frozen\"></tbody>\n            </table>\n            <div #scrollableAligner style=\"background-color:transparent\" *ngIf=\"frozen\"></div>\n        </ng-template>\n\n        <div #scrollFooter *ngIf=\"tt.footerTemplate\" class=\"p-treetable-scrollable-footer\">\n            <div #scrollFooterBox class=\"p-treetable-scrollable-footer-box\">\n                <table class=\"p-treetable-scrollable-footer-table\" [ngClass]=\"tt.tableStyleClass\" [ngStyle]=\"tt.tableStyle\">\n                    <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenColGroupTemplate||tt.colGroupTemplate : tt.colGroupTemplate; context {$implicit: columns}\"></ng-container>\n                    <tfoot class=\"p-treetable-tfoot\">\n                        <ng-container *ngTemplateOutlet=\"frozen ? tt.frozenFooterTemplate||tt.footerTemplate : tt.footerTemplate; context {$implicit: columns}\"></ng-container>\n                    </tfoot>\n                </table>\n            </div>\n        </div>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { columns: [{\n                type: Input,\n                args: [\"ttScrollableView\"]\n            }], frozen: [{\n                type: Input\n            }], scrollHeaderViewChild: [{\n                type: ViewChild,\n                args: ['scrollHeader']\n            }], scrollHeaderBoxViewChild: [{\n                type: ViewChild,\n                args: ['scrollHeaderBox']\n            }], scrollBodyViewChild: [{\n                type: ViewChild,\n                args: ['scrollBody']\n            }], scrollTableViewChild: [{\n                type: ViewChild,\n                args: ['scrollTable']\n            }], scrollLoadingTableViewChild: [{\n                type: ViewChild,\n                args: ['loadingTable']\n            }], scrollFooterViewChild: [{\n                type: ViewChild,\n                args: ['scrollFooter']\n            }], scrollFooterBoxViewChild: [{\n                type: ViewChild,\n                args: ['scrollFooterBox']\n            }], scrollableAlignerViewChild: [{\n                type: ViewChild,\n                args: ['scrollableAligner']\n            }], scroller: [{\n                type: ViewChild,\n                args: ['scroller']\n            }], scrollHeight: [{\n                type: Input\n            }] } });\nclass TTSortableColumn {\n    constructor(tt) {\n        this.tt = tt;\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n                this.updateSortState();\n            });\n        }\n    }\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.updateSortState();\n        }\n    }\n    updateSortState() {\n        this.sorted = this.tt.isSorted(this.field);\n    }\n    onClick(event) {\n        if (this.isEnabled()) {\n            this.updateSortState();\n            this.tt.sort({\n                originalEvent: event,\n                field: this.field\n            });\n            DomHandler.clearSelection();\n        }\n    }\n    onEnterKey(event) {\n        this.onClick(event);\n    }\n    isEnabled() {\n        return this.ttSortableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTSortableColumn.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSortableColumn, deps: [{ token: TreeTable }], target: i0.ɵɵFactoryTarget.Directive });\nTTSortableColumn.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTSortableColumn, selector: \"[ttSortableColumn]\", inputs: { field: [\"ttSortableColumn\", \"field\"], ttSortableColumnDisabled: \"ttSortableColumnDisabled\" }, host: { listeners: { \"click\": \"onClick($event)\", \"keydown.enter\": \"onEnterKey($event)\" }, properties: { \"class.p-sortable-column\": \"isEnabled()\", \"class.p-highlight\": \"sorted\", \"attr.tabindex\": \"isEnabled() ? \\\"0\\\" : null\", \"attr.role\": \"\\\"columnheader\\\"\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSortableColumn, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttSortableColumn]',\n                    host: {\n                        'class': 'p-element',\n                        '[class.p-sortable-column]': 'isEnabled()',\n                        '[class.p-highlight]': 'sorted',\n                        '[attr.tabindex]': 'isEnabled() ? \"0\" : null',\n                        '[attr.role]': '\"columnheader\"'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }]; }, propDecorators: { field: [{\n                type: Input,\n                args: [\"ttSortableColumn\"]\n            }], ttSortableColumnDisabled: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }], onEnterKey: [{\n                type: HostListener,\n                args: ['keydown.enter', ['$event']]\n            }] } });\nclass TTSortIcon {\n    constructor(tt, cd) {\n        this.tt = tt;\n        this.cd = cd;\n        this.subscription = this.tt.tableService.sortSource$.subscribe(sortMeta => {\n            this.updateSortState();\n            this.cd.markForCheck();\n        });\n    }\n    ngOnInit() {\n        this.updateSortState();\n    }\n    onClick(event) {\n        event.preventDefault();\n    }\n    updateSortState() {\n        if (this.tt.sortMode === 'single') {\n            this.sortOrder = this.tt.isSorted(this.field) ? this.tt.sortOrder : 0;\n        }\n        else if (this.tt.sortMode === 'multiple') {\n            let sortMeta = this.tt.getSortMeta(this.field);\n            this.sortOrder = sortMeta ? sortMeta.order : 0;\n        }\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTSortIcon.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSortIcon, deps: [{ token: TreeTable }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTTSortIcon.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTSortIcon, selector: \"p-treeTableSortIcon\", inputs: { field: \"field\", ariaLabelDesc: \"ariaLabelDesc\", ariaLabelAsc: \"ariaLabelAsc\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <i class=\"p-sortable-column-icon pi pi-fw\" [ngClass]=\"{'pi-sort-amount-up-alt': sortOrder === 1, 'pi-sort-amount-down': sortOrder === -1, 'pi-sort-alt': sortOrder === 0}\"></i>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSortIcon, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeTableSortIcon',\n                    template: `\n        <i class=\"p-sortable-column-icon pi pi-fw\" [ngClass]=\"{'pi-sort-amount-up-alt': sortOrder === 1, 'pi-sort-amount-down': sortOrder === -1, 'pi-sort-alt': sortOrder === 0}\"></i>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { field: [{\n                type: Input\n            }], ariaLabelDesc: [{\n                type: Input\n            }], ariaLabelAsc: [{\n                type: Input\n            }] } });\nclass TTResizableColumn {\n    constructor(tt, el, zone) {\n        this.tt = tt;\n        this.el = el;\n        this.zone = zone;\n    }\n    ngAfterViewInit() {\n        if (this.isEnabled()) {\n            DomHandler.addClass(this.el.nativeElement, 'p-resizable-column');\n            this.resizer = document.createElement('span');\n            this.resizer.className = 'p-column-resizer';\n            this.el.nativeElement.appendChild(this.resizer);\n            this.zone.runOutsideAngular(() => {\n                this.resizerMouseDownListener = this.onMouseDown.bind(this);\n                this.resizer.addEventListener('mousedown', this.resizerMouseDownListener);\n            });\n        }\n    }\n    bindDocumentEvents() {\n        this.zone.runOutsideAngular(() => {\n            this.documentMouseMoveListener = this.onDocumentMouseMove.bind(this);\n            document.addEventListener('mousemove', this.documentMouseMoveListener);\n            this.documentMouseUpListener = this.onDocumentMouseUp.bind(this);\n            document.addEventListener('mouseup', this.documentMouseUpListener);\n        });\n    }\n    unbindDocumentEvents() {\n        if (this.documentMouseMoveListener) {\n            document.removeEventListener('mousemove', this.documentMouseMoveListener);\n            this.documentMouseMoveListener = null;\n        }\n        if (this.documentMouseUpListener) {\n            document.removeEventListener('mouseup', this.documentMouseUpListener);\n            this.documentMouseUpListener = null;\n        }\n    }\n    onMouseDown(event) {\n        this.tt.onColumnResizeBegin(event);\n        this.bindDocumentEvents();\n    }\n    onDocumentMouseMove(event) {\n        this.tt.onColumnResize(event);\n    }\n    onDocumentMouseUp(event) {\n        this.tt.onColumnResizeEnd(event, this.el.nativeElement);\n        this.unbindDocumentEvents();\n    }\n    isEnabled() {\n        return this.ttResizableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n        if (this.resizerMouseDownListener) {\n            this.resizer.removeEventListener('mousedown', this.resizerMouseDownListener);\n        }\n        this.unbindDocumentEvents();\n    }\n}\nTTResizableColumn.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTResizableColumn, deps: [{ token: TreeTable }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nTTResizableColumn.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTResizableColumn, selector: \"[ttResizableColumn]\", inputs: { ttResizableColumnDisabled: \"ttResizableColumnDisabled\" }, host: { classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTResizableColumn, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttResizableColumn]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { ttResizableColumnDisabled: [{\n                type: Input\n            }] } });\nclass TTReorderableColumn {\n    constructor(tt, el, zone) {\n        this.tt = tt;\n        this.el = el;\n        this.zone = zone;\n    }\n    ngAfterViewInit() {\n        if (this.isEnabled()) {\n            this.bindEvents();\n        }\n    }\n    bindEvents() {\n        this.zone.runOutsideAngular(() => {\n            this.mouseDownListener = this.onMouseDown.bind(this);\n            this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n            this.dragStartListener = this.onDragStart.bind(this);\n            this.el.nativeElement.addEventListener('dragstart', this.dragStartListener);\n            this.dragOverListener = this.onDragEnter.bind(this);\n            this.el.nativeElement.addEventListener('dragover', this.dragOverListener);\n            this.dragEnterListener = this.onDragEnter.bind(this);\n            this.el.nativeElement.addEventListener('dragenter', this.dragEnterListener);\n            this.dragLeaveListener = this.onDragLeave.bind(this);\n            this.el.nativeElement.addEventListener('dragleave', this.dragLeaveListener);\n        });\n    }\n    unbindEvents() {\n        if (this.mouseDownListener) {\n            document.removeEventListener('mousedown', this.mouseDownListener);\n            this.mouseDownListener = null;\n        }\n        if (this.dragOverListener) {\n            document.removeEventListener('dragover', this.dragOverListener);\n            this.dragOverListener = null;\n        }\n        if (this.dragEnterListener) {\n            document.removeEventListener('dragenter', this.dragEnterListener);\n            this.dragEnterListener = null;\n        }\n        if (this.dragEnterListener) {\n            document.removeEventListener('dragenter', this.dragEnterListener);\n            this.dragEnterListener = null;\n        }\n        if (this.dragLeaveListener) {\n            document.removeEventListener('dragleave', this.dragLeaveListener);\n            this.dragLeaveListener = null;\n        }\n    }\n    onMouseDown(event) {\n        if (event.target.nodeName === 'INPUT' || event.target.nodeName === 'TEXTAREA' || DomHandler.hasClass(event.target, 'p-column-resizer'))\n            this.el.nativeElement.draggable = false;\n        else\n            this.el.nativeElement.draggable = true;\n    }\n    onDragStart(event) {\n        this.tt.onColumnDragStart(event, this.el.nativeElement);\n    }\n    onDragOver(event) {\n        event.preventDefault();\n    }\n    onDragEnter(event) {\n        this.tt.onColumnDragEnter(event, this.el.nativeElement);\n    }\n    onDragLeave(event) {\n        this.tt.onColumnDragLeave(event);\n    }\n    onDrop(event) {\n        if (this.isEnabled()) {\n            this.tt.onColumnDrop(event, this.el.nativeElement);\n        }\n    }\n    isEnabled() {\n        return this.ttReorderableColumnDisabled !== true;\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n    }\n}\nTTReorderableColumn.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTReorderableColumn, deps: [{ token: TreeTable }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nTTReorderableColumn.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTReorderableColumn, selector: \"[ttReorderableColumn]\", inputs: { ttReorderableColumnDisabled: \"ttReorderableColumnDisabled\" }, host: { listeners: { \"drop\": \"onDrop($event)\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTReorderableColumn, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttReorderableColumn]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { ttReorderableColumnDisabled: [{\n                type: Input\n            }], onDrop: [{\n                type: HostListener,\n                args: ['drop', ['$event']]\n            }] } });\nclass TTSelectableRow {\n    constructor(tt, tableService) {\n        this.tt = tt;\n        this.tableService = tableService;\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n                this.selected = this.tt.isSelected(this.rowNode.node);\n            });\n        }\n    }\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.selected = this.tt.isSelected(this.rowNode.node);\n        }\n    }\n    onClick(event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        }\n    }\n    onEnterKey(event) {\n        if (event.which === 13) {\n            this.onClick(event);\n        }\n    }\n    onTouchEnd(event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowTouchEnd(event);\n        }\n    }\n    isEnabled() {\n        return this.ttSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTSelectableRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSelectableRow, deps: [{ token: TreeTable }, { token: TreeTableService }], target: i0.ɵɵFactoryTarget.Directive });\nTTSelectableRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTSelectableRow, selector: \"[ttSelectableRow]\", inputs: { rowNode: [\"ttSelectableRow\", \"rowNode\"], ttSelectableRowDisabled: \"ttSelectableRowDisabled\" }, host: { listeners: { \"click\": \"onClick($event)\", \"keydown\": \"onEnterKey($event)\", \"touchend\": \"onTouchEnd($event)\" }, properties: { \"class.p-highlight\": \"selected\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSelectableRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttSelectableRow]',\n                    host: {\n                        'class': 'p-element',\n                        '[class.p-highlight]': 'selected'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }]; }, propDecorators: { rowNode: [{\n                type: Input,\n                args: [\"ttSelectableRow\"]\n            }], ttSelectableRowDisabled: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }], onEnterKey: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }], onTouchEnd: [{\n                type: HostListener,\n                args: ['touchend', ['$event']]\n            }] } });\nclass TTSelectableRowDblClick {\n    constructor(tt, tableService) {\n        this.tt = tt;\n        this.tableService = tableService;\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n                this.selected = this.tt.isSelected(this.rowNode.node);\n            });\n        }\n    }\n    ngOnInit() {\n        if (this.isEnabled()) {\n            this.selected = this.tt.isSelected(this.rowNode.node);\n        }\n    }\n    onClick(event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        }\n    }\n    isEnabled() {\n        return this.ttSelectableRowDisabled !== true;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTSelectableRowDblClick.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSelectableRowDblClick, deps: [{ token: TreeTable }, { token: TreeTableService }], target: i0.ɵɵFactoryTarget.Directive });\nTTSelectableRowDblClick.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTSelectableRowDblClick, selector: \"[ttSelectableRowDblClick]\", inputs: { rowNode: [\"ttSelectableRowDblClick\", \"rowNode\"], ttSelectableRowDisabled: \"ttSelectableRowDisabled\" }, host: { listeners: { \"dblclick\": \"onClick($event)\" }, properties: { \"class.p-highlight\": \"selected\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTSelectableRowDblClick, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttSelectableRowDblClick]',\n                    host: {\n                        'class': 'p-element',\n                        '[class.p-highlight]': 'selected'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }]; }, propDecorators: { rowNode: [{\n                type: Input,\n                args: [\"ttSelectableRowDblClick\"]\n            }], ttSelectableRowDisabled: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['dblclick', ['$event']]\n            }] } });\nclass TTContextMenuRow {\n    constructor(tt, tableService, el) {\n        this.tt = tt;\n        this.tableService = tableService;\n        this.el = el;\n        if (this.isEnabled()) {\n            this.subscription = this.tt.tableService.contextMenuSource$.subscribe((node) => {\n                this.selected = this.tt.equals(this.rowNode.node, node);\n            });\n        }\n    }\n    onContextMenu(event) {\n        if (this.isEnabled()) {\n            this.tt.handleRowRightClick({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n            this.el.nativeElement.focus();\n            event.preventDefault();\n        }\n    }\n    isEnabled() {\n        return this.ttContextMenuRowDisabled !== true;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTContextMenuRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTContextMenuRow, deps: [{ token: TreeTable }, { token: TreeTableService }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nTTContextMenuRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTContextMenuRow, selector: \"[ttContextMenuRow]\", inputs: { rowNode: [\"ttContextMenuRow\", \"rowNode\"], ttContextMenuRowDisabled: \"ttContextMenuRowDisabled\" }, host: { listeners: { \"contextmenu\": \"onContextMenu($event)\" }, properties: { \"class.p-highlight-contextmenu\": \"selected\", \"attr.tabindex\": \"isEnabled() ? 0 : undefined\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTContextMenuRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttContextMenuRow]',\n                    host: {\n                        'class': 'p-element',\n                        '[class.p-highlight-contextmenu]': 'selected',\n                        '[attr.tabindex]': 'isEnabled() ? 0 : undefined'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }, { type: i0.ElementRef }]; }, propDecorators: { rowNode: [{\n                type: Input,\n                args: [\"ttContextMenuRow\"]\n            }], ttContextMenuRowDisabled: [{\n                type: Input\n            }], onContextMenu: [{\n                type: HostListener,\n                args: ['contextmenu', ['$event']]\n            }] } });\nclass TTCheckbox {\n    constructor(tt, tableService, cd) {\n        this.tt = tt;\n        this.tableService = tableService;\n        this.cd = cd;\n        this.subscription = this.tt.tableService.selectionSource$.subscribe(() => {\n            this.checked = this.tt.isSelected(this.rowNode.node);\n            this.cd.markForCheck();\n        });\n    }\n    ngOnInit() {\n        this.checked = this.tt.isSelected(this.rowNode.node);\n    }\n    onClick(event) {\n        if (!this.disabled) {\n            this.tt.toggleNodeWithCheckbox({\n                originalEvent: event,\n                rowNode: this.rowNode\n            });\n        }\n        DomHandler.clearSelection();\n    }\n    onFocus() {\n        this.focused = true;\n    }\n    onBlur() {\n        this.focused = false;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nTTCheckbox.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTCheckbox, deps: [{ token: TreeTable }, { token: TreeTableService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTTCheckbox.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTCheckbox, selector: \"p-treeTableCheckbox\", inputs: { disabled: \"disabled\", rowNode: [\"value\", \"rowNode\"] }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event)\">\n            <div class=\"p-hidden-accessible\">\n                <input type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-indeterminate': rowNode.node.partialSelected, 'p-disabled':disabled}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':checked, 'pi-minus': rowNode.node.partialSelected}\"></span>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTCheckbox, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeTableCheckbox',\n                    template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event)\">\n            <div class=\"p-hidden-accessible\">\n                <input type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-indeterminate': rowNode.node.partialSelected, 'p-disabled':disabled}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':checked, 'pi-minus': rowNode.node.partialSelected}\"></span>\n            </div>\n        </div>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { disabled: [{\n                type: Input\n            }], rowNode: [{\n                type: Input,\n                args: [\"value\"]\n            }] } });\nclass TTHeaderCheckbox {\n    constructor(tt, tableService, cd) {\n        this.tt = tt;\n        this.tableService = tableService;\n        this.cd = cd;\n        this.valueChangeSubscription = this.tt.tableService.uiUpdateSource$.subscribe(() => {\n            this.checked = this.updateCheckedState();\n        });\n        this.selectionChangeSubscription = this.tt.tableService.selectionSource$.subscribe(() => {\n            this.checked = this.updateCheckedState();\n        });\n    }\n    ngOnInit() {\n        this.checked = this.updateCheckedState();\n    }\n    onClick(event, checked) {\n        if (this.tt.value && this.tt.value.length > 0) {\n            this.tt.toggleNodesWithCheckbox(event, !checked);\n        }\n        DomHandler.clearSelection();\n    }\n    onFocus() {\n        this.focused = true;\n    }\n    onBlur() {\n        this.focused = false;\n    }\n    ngOnDestroy() {\n        if (this.selectionChangeSubscription) {\n            this.selectionChangeSubscription.unsubscribe();\n        }\n        if (this.valueChangeSubscription) {\n            this.valueChangeSubscription.unsubscribe();\n        }\n    }\n    updateCheckedState() {\n        this.cd.markForCheck();\n        let checked;\n        const data = this.tt.filteredNodes || this.tt.value;\n        if (data) {\n            for (let node of data) {\n                if (this.tt.isSelected(node)) {\n                    checked = true;\n                }\n                else {\n                    checked = false;\n                    break;\n                }\n            }\n        }\n        else {\n            checked = false;\n        }\n        return checked;\n    }\n}\nTTHeaderCheckbox.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTHeaderCheckbox, deps: [{ token: TreeTable }, { token: TreeTableService }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nTTHeaderCheckbox.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTHeaderCheckbox, selector: \"p-treeTableHeaderCheckbox\", host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"boxViewChild\", first: true, predicate: [\"box\"], descendants: true }], ngImport: i0, template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event, cb.checked)\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\" [disabled]=\"!tt.value||tt.value.length === 0\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-disabled': (!tt.value || tt.value.length === 0)}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"{'pi pi-check':checked}\"></span>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTHeaderCheckbox, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeTableHeaderCheckbox',\n                    template: `\n        <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-focused':focused}\" (click)=\"onClick($event, cb.checked)\">\n            <div class=\"p-hidden-accessible\">\n                <input #cb type=\"checkbox\" [checked]=\"checked\" (focus)=\"onFocus()\" (blur)=\"onBlur()\" [disabled]=\"!tt.value||tt.value.length === 0\">\n            </div>\n            <div #box [ngClass]=\"{'p-checkbox-box':true,\n                'p-highlight':checked, 'p-focus':focused, 'p-disabled': (!tt.value || tt.value.length === 0)}\"  role=\"checkbox\" [attr.aria-checked]=\"checked\">\n                <span class=\"p-checkbox-icon\" [ngClass]=\"{'pi pi-check':checked}\"></span>\n            </div>\n        </div>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TreeTableService }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { boxViewChild: [{\n                type: ViewChild,\n                args: ['box']\n            }] } });\nclass TTEditableColumn {\n    constructor(tt, el, zone) {\n        this.tt = tt;\n        this.el = el;\n        this.zone = zone;\n    }\n    ngAfterViewInit() {\n        if (this.isEnabled()) {\n            DomHandler.addClass(this.el.nativeElement, 'p-editable-column');\n        }\n    }\n    onClick(event) {\n        if (this.isEnabled()) {\n            this.tt.editingCellClick = true;\n            if (this.tt.editingCell) {\n                if (this.tt.editingCell !== this.el.nativeElement) {\n                    if (!this.tt.isEditingCellValid()) {\n                        return;\n                    }\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.openCell();\n                }\n            }\n            else {\n                this.openCell();\n            }\n        }\n    }\n    openCell() {\n        this.tt.updateEditingCell(this.el.nativeElement, this.data, this.field);\n        DomHandler.addClass(this.el.nativeElement, 'p-cell-editing');\n        this.tt.onEditInit.emit({ field: this.field, data: this.data });\n        this.tt.editingCellClick = true;\n        this.zone.runOutsideAngular(() => {\n            setTimeout(() => {\n                let focusable = DomHandler.findSingle(this.el.nativeElement, 'input, textarea');\n                if (focusable) {\n                    focusable.focus();\n                }\n            }, 50);\n        });\n    }\n    closeEditingCell() {\n        DomHandler.removeClass(this.tt.editingCell, 'p-checkbox-icon');\n        this.tt.editingCell = null;\n        this.tt.unbindDocumentEditListener();\n    }\n    onKeyDown(event) {\n        if (this.isEnabled()) {\n            //enter\n            if (event.keyCode == 13) {\n                if (this.tt.isEditingCellValid()) {\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.closeEditingCell();\n                    this.tt.onEditComplete.emit({ field: this.field, data: this.data });\n                }\n                event.preventDefault();\n            }\n            //escape\n            else if (event.keyCode == 27) {\n                if (this.tt.isEditingCellValid()) {\n                    DomHandler.removeClass(this.tt.editingCell, 'p-cell-editing');\n                    this.closeEditingCell();\n                    this.tt.onEditCancel.emit({ field: this.field, data: this.data });\n                }\n                event.preventDefault();\n            }\n            //tab\n            else if (event.keyCode == 9) {\n                this.tt.onEditComplete.emit({ field: this.field, data: this.data });\n                if (event.shiftKey)\n                    this.moveToPreviousCell(event);\n                else\n                    this.moveToNextCell(event);\n            }\n        }\n    }\n    findCell(element) {\n        if (element) {\n            let cell = element;\n            while (cell && !DomHandler.hasClass(cell, 'p-cell-editing')) {\n                cell = cell.parentElement;\n            }\n            return cell;\n        }\n        else {\n            return null;\n        }\n    }\n    moveToPreviousCell(event) {\n        let currentCell = this.findCell(event.target);\n        let row = currentCell.parentElement;\n        let targetCell = this.findPreviousEditableColumn(currentCell);\n        if (targetCell) {\n            DomHandler.invokeElementMethod(targetCell, 'click');\n            event.preventDefault();\n        }\n    }\n    moveToNextCell(event) {\n        let currentCell = this.findCell(event.target);\n        let row = currentCell.parentElement;\n        let targetCell = this.findNextEditableColumn(currentCell);\n        if (targetCell) {\n            DomHandler.invokeElementMethod(targetCell, 'click');\n            event.preventDefault();\n        }\n    }\n    findPreviousEditableColumn(cell) {\n        let prevCell = cell.previousElementSibling;\n        if (!prevCell) {\n            let previousRow = cell.parentElement ? cell.parentElement.previousElementSibling : null;\n            if (previousRow) {\n                prevCell = previousRow.lastElementChild;\n            }\n        }\n        if (prevCell) {\n            if (DomHandler.hasClass(prevCell, 'p-editable-column'))\n                return prevCell;\n            else\n                return this.findPreviousEditableColumn(prevCell);\n        }\n        else {\n            return null;\n        }\n    }\n    findNextEditableColumn(cell) {\n        let nextCell = cell.nextElementSibling;\n        if (!nextCell) {\n            let nextRow = cell.parentElement ? cell.parentElement.nextElementSibling : null;\n            if (nextRow) {\n                nextCell = nextRow.firstElementChild;\n            }\n        }\n        if (nextCell) {\n            if (DomHandler.hasClass(nextCell, 'p-editable-column'))\n                return nextCell;\n            else\n                return this.findNextEditableColumn(nextCell);\n        }\n        else {\n            return null;\n        }\n    }\n    isEnabled() {\n        return this.ttEditableColumnDisabled !== true;\n    }\n}\nTTEditableColumn.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTEditableColumn, deps: [{ token: TreeTable }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nTTEditableColumn.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTEditableColumn, selector: \"[ttEditableColumn]\", inputs: { data: [\"ttEditableColumn\", \"data\"], field: [\"ttEditableColumnField\", \"field\"], ttEditableColumnDisabled: \"ttEditableColumnDisabled\" }, host: { listeners: { \"click\": \"onClick($event)\", \"keydown\": \"onKeyDown($event)\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTEditableColumn, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttEditableColumn]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { data: [{\n                type: Input,\n                args: [\"ttEditableColumn\"]\n            }], field: [{\n                type: Input,\n                args: [\"ttEditableColumnField\"]\n            }], ttEditableColumnDisabled: [{\n                type: Input\n            }], onClick: [{\n                type: HostListener,\n                args: ['click', ['$event']]\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\nclass TreeTableCellEditor {\n    constructor(tt, editableColumn) {\n        this.tt = tt;\n        this.editableColumn = editableColumn;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'input':\n                    this.inputTemplate = item.template;\n                    break;\n                case 'output':\n                    this.outputTemplate = item.template;\n                    break;\n            }\n        });\n    }\n}\nTreeTableCellEditor.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableCellEditor, deps: [{ token: TreeTable }, { token: TTEditableColumn }], target: i0.ɵɵFactoryTarget.Component });\nTreeTableCellEditor.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TreeTableCellEditor, selector: \"p-treeTableCellEditor\", host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <ng-container *ngIf=\"tt.editingCell === editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"inputTemplate\"></ng-container>\n        </ng-container>\n        <ng-container *ngIf=\"!tt.editingCell || tt.editingCell !== editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"outputTemplate\"></ng-container>\n        </ng-container>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableCellEditor, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeTableCellEditor',\n                    template: `\n        <ng-container *ngIf=\"tt.editingCell === editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"inputTemplate\"></ng-container>\n        </ng-container>\n        <ng-container *ngIf=\"!tt.editingCell || tt.editingCell !== editableColumn.el.nativeElement\">\n            <ng-container *ngTemplateOutlet=\"outputTemplate\"></ng-container>\n        </ng-container>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: TTEditableColumn }]; }, propDecorators: { templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass TTRow {\n    constructor(tt, el, zone) {\n        this.tt = tt;\n        this.el = el;\n        this.zone = zone;\n    }\n    onKeyDown(event) {\n        switch (event.which) {\n            //down arrow\n            case 40:\n                let nextRow = this.el.nativeElement.nextElementSibling;\n                if (nextRow) {\n                    nextRow.focus();\n                }\n                event.preventDefault();\n                break;\n            //down arrow\n            case 38:\n                let prevRow = this.el.nativeElement.previousElementSibling;\n                if (prevRow) {\n                    prevRow.focus();\n                }\n                event.preventDefault();\n                break;\n            //left arrow\n            case 37:\n                if (this.rowNode.node.expanded) {\n                    this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n                    this.rowNode.node.expanded = false;\n                    this.tt.onNodeCollapse.emit({\n                        originalEvent: event,\n                        node: this.rowNode.node\n                    });\n                    this.tt.updateSerializedValue();\n                    this.tt.tableService.onUIUpdate(this.tt.value);\n                    this.restoreFocus();\n                }\n                break;\n            //right arrow\n            case 39:\n                if (!this.rowNode.node.expanded) {\n                    this.tt.toggleRowIndex = DomHandler.index(this.el.nativeElement);\n                    this.rowNode.node.expanded = true;\n                    this.tt.onNodeExpand.emit({\n                        originalEvent: event,\n                        node: this.rowNode.node\n                    });\n                    this.tt.updateSerializedValue();\n                    this.tt.tableService.onUIUpdate(this.tt.value);\n                    this.restoreFocus();\n                }\n                break;\n        }\n    }\n    restoreFocus() {\n        this.zone.runOutsideAngular(() => {\n            setTimeout(() => {\n                let row = DomHandler.findSingle(this.tt.containerViewChild.nativeElement, '.p-treetable-tbody').children[this.tt.toggleRowIndex];\n                if (row) {\n                    row.focus();\n                }\n            }, 25);\n        });\n    }\n}\nTTRow.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTRow, deps: [{ token: TreeTable }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nTTRow.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TTRow, selector: \"[ttRow]\", inputs: { rowNode: [\"ttRow\", \"rowNode\"] }, host: { listeners: { \"keydown\": \"onKeyDown($event)\" }, properties: { \"attr.tabindex\": \"\\\"0\\\"\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TTRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[ttRow]',\n                    host: {\n                        'class': 'p-element',\n                        '[attr.tabindex]': '\"0\"'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { rowNode: [{\n                type: Input,\n                args: ['ttRow']\n            }], onKeyDown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\nclass TreeTableToggler {\n    constructor(tt) {\n        this.tt = tt;\n    }\n    onClick(event) {\n        this.rowNode.node.expanded = !this.rowNode.node.expanded;\n        if (this.rowNode.node.expanded) {\n            this.tt.onNodeExpand.emit({\n                originalEvent: event,\n                node: this.rowNode.node\n            });\n        }\n        else {\n            this.tt.onNodeCollapse.emit({\n                originalEvent: event,\n                node: this.rowNode.node\n            });\n        }\n        this.tt.updateSerializedValue();\n        this.tt.tableService.onUIUpdate(this.tt.value);\n        event.preventDefault();\n    }\n}\nTreeTableToggler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableToggler, deps: [{ token: TreeTable }], target: i0.ɵɵFactoryTarget.Component });\nTreeTableToggler.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: TreeTableToggler, selector: \"p-treeTableToggler\", inputs: { rowNode: \"rowNode\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <button type=\"button\" class=\"p-treetable-toggler p-link\" (click)=\"onClick($event)\" tabindex=\"-1\" pRipple\n            [style.visibility]=\"rowNode.node.leaf === false || (rowNode.node.children && rowNode.node.children.length) ? 'visible' : 'hidden'\" [style.marginLeft]=\"rowNode.level * 16 + 'px'\">\n            <i [ngClass]=\"rowNode.node.expanded ? 'pi pi-fw pi-chevron-down' : 'pi pi-fw pi-chevron-right'\"></i>\n        </button>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i5.Ripple, selector: \"[pRipple]\" }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableToggler, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeTableToggler',\n                    template: `\n        <button type=\"button\" class=\"p-treetable-toggler p-link\" (click)=\"onClick($event)\" tabindex=\"-1\" pRipple\n            [style.visibility]=\"rowNode.node.leaf === false || (rowNode.node.children && rowNode.node.children.length) ? 'visible' : 'hidden'\" [style.marginLeft]=\"rowNode.level * 16 + 'px'\">\n            <i [ngClass]=\"rowNode.node.expanded ? 'pi pi-fw pi-chevron-down' : 'pi pi-fw pi-chevron-right'\"></i>\n        </button>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: TreeTable }]; }, propDecorators: { rowNode: [{\n                type: Input\n            }] } });\nclass TreeTableModule {\n}\nTreeTableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTreeTableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableModule, declarations: [TreeTable, TreeTableToggler, TTScrollableView, TTBody, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor], imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule], exports: [TreeTable, SharedModule, TreeTableToggler, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor, ScrollerModule] });\nTreeTableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableModule, imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, PaginatorModule, RippleModule, ScrollerModule],\n                    exports: [TreeTable, SharedModule, TreeTableToggler, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor, ScrollerModule],\n                    declarations: [TreeTable, TreeTableToggler, TTScrollableView, TTBody, TTSortableColumn, TTSortIcon, TTResizableColumn, TTRow, TTReorderableColumn, TTSelectableRow, TTSelectableRowDblClick, TTContextMenuRow, TTCheckbox, TTHeaderCheckbox, TTEditableColumn, TreeTableCellEditor]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TTBody, TTCheckbox, TTContextMenuRow, TTEditableColumn, TTHeaderCheckbox, TTReorderableColumn, TTResizableColumn, TTRow, TTScrollableView, TTSelectableRow, TTSelectableRowDblClick, TTSortIcon, TTSortableColumn, TreeTable, TreeTableCellEditor, TreeTableModule, TreeTableService, TreeTableToggler };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,iBAA9C,EAAiEC,KAAjE,EAAwEC,MAAxE,EAAgFC,SAAhF,EAA2FC,eAA3F,EAA4GC,SAA5G,EAAuHC,YAAvH,EAAqIC,uBAArI,EAA8JC,QAA9J,QAA8K,eAA9K;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,aAA5C;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,kBAApB;AACA,SAASC,cAAT,QAA+B,kBAA/B;;;;;;;;;;;IA+BmG1B,EAoqCvF,2C;IApqCuFA,EAsqC/E,kB;IAtqC+EA,EAuqCnF,iB;;;;mBAvqCmFA,E;IAAAA,EAsqC5E,a;IAtqC4EA,EAsqC5E,qE;;;;;;IAtqC4EA,EA0qCnF,sB;;;;;;IA1qCmFA,EAyqCvF,6B;IAzqCuFA,EA0qCnF,iF;IA1qCmFA,EA2qCvF,e;;;;mBA3qCuFA,E;IAAAA,EA0qCpE,a;IA1qCoEA,EA0qCpE,uD;;;;;;iBA1qCoEA,E;;IAAAA,EA4qCvF,qC;IA5qCuFA,EA6qCnF;MA7qCmFA,EA6qCnF;MAAA,gBA7qCmFA,EA6qCnF;MAAA,OA7qCmFA,EA6qCnE,0CAAhB;IAAA,E;IA7qCmFA,EA+qCsM,e;;;;mBA/qCtMA,E;IAAAA,EA4qC1E,grB;;;;;;IA5qC0EA,EAmrC/E,sB;;;;;;IAnrC+EA,EAqrC3E,sB;;;;;;IArrC2EA,EAyrC3E,sB;;;;;;;;;;;;IAzrC2EA,EAirCvF,iD;IAjrCuFA,EAmrC/E,iF;IAnrC+EA,EAorC/E,+B;IAprC+EA,EAqrC3E,iF;IArrC2EA,EAsrC/E,e;IAtrC+EA,EAurC/E,0B;IAvrC+EA,EAwrC/E,+B;IAxrC+EA,EAyrC3E,iF;IAzrC2EA,EA0rC/E,mB;;;;mBA1rC+EA,E;IAAAA,EAkrCrE,a;IAlrCqEA,EAkrCrE,4E;IAlrCqEA,EAmrChE,a;IAnrCgEA,EAmrChE,oFAnrCgEA,EAmrChE,0C;IAnrCgEA,EAqrC5D,a;IArrC4DA,EAqrC5D,kFArrC4DA,EAqrC5D,0C;IArrC4DA,EAurC9C,a;IAvrC8CA,EAurC9C,4F;IAvrC8CA,EAyrC5D,a;IAzrC4DA,EAyrC5D,kFAzrC4DA,EAyrC5D,0C;;;;;;;;;;;;IAzrC4DA,EA+rCpF,4B;;;;oBA/rCoFA,E;IAAAA,EA+rC6C,kFA/rC7CA,EA+rC6C,oF;;;;;;;;;;;;;IA/rC7CA,EA8rCvF,6B;IA9rCuFA,EA+rCpF,+D;IA/rCoFA,EAgsCpF,4B;IAhsCoFA,EAisCvF,e;;;;mBAjsCuFA,E;IAAAA,EA+rClB,a;IA/rCkBA,EA+rClB,sE;IA/rCkBA,EAgsC3B,a;IAhsC2BA,EAgsC3B,iHAhsC2BA,EAgsC3B,wF;;;;;;iBAhsC2BA,E;;IAAAA,EAmsCvF,qC;IAnsCuFA,EAosCnF;MApsCmFA,EAosCnF;MAAA,gBApsCmFA,EAosCnF;MAAA,OApsCmFA,EAosCnE,0CAAhB;IAAA,E;IApsCmFA,EAssCsM,e;;;;mBAtsCtMA,E;IAAAA,EAmsC1E,grB;;;;;;IAnsC0EA,EAwsCnF,sB;;;;;;IAxsCmFA,EAusCvF,6B;IAvsCuFA,EAwsCnF,iF;IAxsCmFA,EAysCvF,e;;;;mBAzsCuFA,E;IAAAA,EAwsCpE,a;IAxsCoEA,EAwsCpE,uD;;;;;;IAxsCoEA,EA2sCvF,4B;;;;;;IA3sCuFA,EA6sCvF,6B;;;;;;IA7sCuFA,EA8sCvF,6B;;;;;;;;;;;;;;;;;;;IA9sCuFA,EA+9CnF,sB;;;;;;;;;;;;;;;IA/9CmFA,EA89CvF,2B;IA99CuFA,EA+9CnF,oG;IA/9CmFA,EAg+CvF,wB;;;;8BAh+CuFA,E;mBAAAA,E;IAAAA,EA+9CpE,a;IA/9CoEA,EA+9CpE,4EA/9CoEA,EA+9CpE,kH;;;;;;IA/9CoEA,EA89CvF,qF;;;;;IA99CuFA,EA89CxE,8C;;;;;;IA99CwEA,EAm+CvF,sB;;;;;;;;;;;;;IAn+CuFA,EAk+C3F,2B;IAl+C2FA,EAm+CvF,sF;IAn+CuFA,EAo+C3F,wB;;;;mBAp+C2FA,E;IAAAA,EAm+CxE,a;IAn+CwEA,EAm+CxE,2FAn+CwEA,EAm+CxE,yD;;;;;;;;;;;;;;;;;IAn+CwEA,EA2oD/E,sB;;;;;;IA3oD+EA,EA6oD3E,sB;;;;;;IA7oD2EA,EAspDnF,sB;;;;;;;;;;;;;IAtpDmFA,EAspDnF,4G;;;;;;IAtpDmFA,E;;gBAAAA,E;;IAAAA,EAspDpE,gEAtpDoEA,EAspDpE,0D;;;;;;IAtpDoEA,EA0pD/E,sB;;;;;;;;;;;;IA1pD+EA,EA0pD/E,2H;;;;;oBA1pD+EA,E;IAAAA,EA0pDhE,mFA1pDgEA,EA0pDhE,+C;;;;;;IA1pDgEA,EAwpDvF,2B;IAxpDuFA,EAypDnF,4G;IAzpDmFA,EA4pDvF,wB;;;;;;;;;;;;iBA5pDuFA,E;;IAAAA,EAmpD3F,wC;IAnpD2FA,EAopDtE;MAppDsEA,EAopDtE;MAAA,gBAppDsEA,EAopDtE;MAAA,OAppDsEA,EAopDxD,+CAAd;IAAA,E;IAppDsEA,EAqpDvF,6F;IArpDuFA,EAwpDvF,8F;IAxpDuFA,EA6pD3F,e;;;;mBA7pD2FA,E;IAAAA,EAmpD0B,YAnpD1BA,EAmpD0B,kG;IAnpD1BA,EAmpD5C,mQ;IAnpD4CA,EAwpDxE,a;IAxpDwEA,EAwpDxE,0C;;;;;;IAxpDwEA,EAgqDnF,sB;;;;;;;;;;;;;;;;;IAhqDmFA,EA8pD3F,2B;IA9pD2FA,EA+pDvF,iC;IA/pDuFA,EAgqDnF,gG;IAhqDmFA,EAiqDvF,e;IAjqDuFA,EAkqD3F,wB;;;;mBAlqD2FA,E;;gBAAAA,E;;IAAAA,EA+pDlC,a;IA/pDkCA,EA+pDlC,uBA/pDkCA,EA+pDlC,gK;IA/pDkCA,EAgqDpE,a;IAhqDoEA,EAgqDpE,gEAhqDoEA,EAgqDpE,kDAhqDoEA,EAgqDpE,2B;;;;;;IAhqDoEA,EAsqDnF,sB;;;;;;IAtqDmFA,EAyqDvF,4B;;;;;;IAzqDuFA,EAqqDvF,mC;IArqDuFA,EAsqDnF,gG;IAtqDmFA,EAuqDnF,0B;IAvqDmFA,EAwqDvF,e;IAxqDuFA,EAyqDvF,+E;;;;;;mBAzqDuFA,E;IAAAA,EAqqDkC,6C;IArqDlCA,EAqqDnE,sC;IArqDmEA,EAqqDtC,8F;IArqDsCA,EAsqDpE,a;IAtqDoEA,EAsqDpE,wKAtqDoEA,EAsqDpE,0C;IAtqDoEA,EAuqDlD,a;IAvqDkDA,EAuqDlD,+N;IAvqDkDA,EAyqDzB,a;IAzqDyBA,EAyqDzB,kC;;;;;;IAzqDyBA,EA+qD/E,sB;;;;;;IA/qD+EA,EAirD3E,sB;;;;;;IAjrD2EA,EA4qD3F,mE;IA5qD2FA,EA+qD/E,wF;IA/qD+EA,EAgrD/E,+B;IAhrD+EA,EAirD3E,wF;IAjrD2EA,EAkrD/E,qB;;;;mBAlrD+EA,E;IAAAA,EA8qDhC,a;IA9qDgCA,EA8qDhC,kF;IA9qDgCA,EA+qDhE,a;IA/qDgEA,EA+qDhE,wKA/qDgEA,EA+qDhE,yC;IA/qDgEA,EAirD5D,a;IAjrD4DA,EAirD5D,kKAjrD4DA,EAirD5D,yC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAjrD4DA,EA+jFvF,sB;;;;;;IA/jFuFA,EA8jF3F,2B;IA9jF2FA,EA+jFvF,mG;IA/jFuFA,EAgkF3F,wB;;;;mBAhkF2FA,E;IAAAA,EA+jFxE,a;IA/jFwEA,EA+jFxE,qD;;;;;;IA/jFwEA,EAkkFvF,sB;;;;;;IAlkFuFA,EAikF3F,2B;IAjkF2FA,EAkkFvF,mG;IAlkFuFA,EAmkF3F,wB;;;;mBAnkF2FA,E;IAAAA,EAkkFxE,a;IAlkFwEA,EAkkFxE,sD;;;;AA/lF3B,MAAM2B,gBAAN,CAAuB;EACnBC,WAAW,GAAG;IACV,KAAKC,UAAL,GAAkB,IAAId,OAAJ,EAAlB;IACA,KAAKe,eAAL,GAAuB,IAAIf,OAAJ,EAAvB;IACA,KAAKgB,iBAAL,GAAyB,IAAIhB,OAAJ,EAAzB;IACA,KAAKiB,cAAL,GAAsB,IAAIjB,OAAJ,EAAtB;IACA,KAAKkB,kBAAL,GAA0B,IAAIlB,OAAJ,EAA1B;IACA,KAAKmB,WAAL,GAAmB,KAAKL,UAAL,CAAgBM,YAAhB,EAAnB;IACA,KAAKC,gBAAL,GAAwB,KAAKN,eAAL,CAAqBK,YAArB,EAAxB;IACA,KAAKE,kBAAL,GAA0B,KAAKN,iBAAL,CAAuBI,YAAvB,EAA1B;IACA,KAAKG,eAAL,GAAuB,KAAKN,cAAL,CAAoBG,YAApB,EAAvB;IACA,KAAKI,mBAAL,GAA2B,KAAKN,kBAAL,CAAwBE,YAAxB,EAA3B;EACH;;EACDK,MAAM,CAACC,QAAD,EAAW;IACb,KAAKZ,UAAL,CAAgBa,IAAhB,CAAqBD,QAArB;EACH;;EACDE,iBAAiB,GAAG;IAChB,KAAKb,eAAL,CAAqBY,IAArB,CAA0B,IAA1B;EACH;;EACDE,aAAa,CAACC,IAAD,EAAO;IAChB,KAAKd,iBAAL,CAAuBW,IAAvB,CAA4BG,IAA5B;EACH;;EACDC,UAAU,CAACC,KAAD,EAAQ;IACd,KAAKf,cAAL,CAAoBU,IAApB,CAAyBK,KAAzB;EACH;;EACDC,oBAAoB,CAACD,KAAD,EAAQ;IACxB,KAAKd,kBAAL,CAAwBS,IAAxB,CAA6BK,KAA7B;EACH;;AA3BkB;;AA6BvBpB,gBAAgB,CAACsB,IAAjB;EAAA,iBAA6GtB,gBAA7G;AAAA;;AACAA,gBAAgB,CAACuB,KAAjB,kBADmGlD,EACnG;EAAA,OAAiH2B,gBAAjH;EAAA,SAAiHA,gBAAjH;AAAA;;AACA;EAAA,mDAFmG3B,EAEnG,mBAA2F2B,gBAA3F,EAAyH,CAAC;IAC9GwB,IAAI,EAAElD;EADwG,CAAD,CAAzH;AAAA;;AAGA,MAAMmD,SAAN,CAAgB;EACZxB,WAAW,CAACyB,EAAD,EAAKC,EAAL,EAASC,IAAT,EAAeC,YAAf,EAA6BC,aAA7B,EAA4C;IACnD,KAAKJ,EAAL,GAAUA,EAAV;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,YAAL,GAAoBA,YAApB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,IAAL,GAAY,KAAZ;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,mBAAL,GAA2B,IAA3B;IACA,KAAKC,iBAAL,GAAyB,QAAzB;IACA,KAAKC,yBAAL,GAAiC,+BAAjC;IACA,KAAKC,iBAAL,GAAyB,IAAzB;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,gBAAL,GAAwB,CAAxB;IACA,KAAKC,QAAL,GAAgB,QAAhB;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA,KAAKC,eAAL,GAAuB,IAAIpE,YAAJ,EAAvB;IACA,KAAKqE,0BAAL,GAAkC,IAAIrE,YAAJ,EAAlC;IACA,KAAKsE,wBAAL,GAAgC,UAAhC;IACA,KAAKC,kBAAL,GAA0B,YAA1B;IACA,KAAKC,WAAL,GAAmB,eAAnB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,kBAAL,GAA0B,GAA1B;IACA,KAAKC,gBAAL,GAAwB,KAAxB;;IACA,KAAKC,UAAL,GAAkB,CAACC,KAAD,EAAQC,IAAR,KAAiBA,IAAnC;;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,WAAL,GAAmB,GAAnB;IACA,KAAKC,UAAL,GAAkB,SAAlB;IACA,KAAKC,QAAL,GAAgB,IAAIlF,YAAJ,EAAhB;IACA,KAAKmF,YAAL,GAAoB,IAAInF,YAAJ,EAApB;IACA,KAAKoF,cAAL,GAAsB,IAAIpF,YAAJ,EAAtB;IACA,KAAKqF,MAAL,GAAc,IAAIrF,YAAJ,EAAd;IACA,KAAKsC,MAAL,GAAc,IAAItC,YAAJ,EAAd;IACA,KAAKsF,UAAL,GAAkB,IAAItF,YAAJ,EAAlB;IACA,KAAKuF,YAAL,GAAoB,IAAIvF,YAAJ,EAApB;IACA,KAAKwF,WAAL,GAAmB,IAAIxF,YAAJ,EAAnB;IACA,KAAKyF,YAAL,GAAoB,IAAIzF,YAAJ,EAApB;IACA,KAAK0F,YAAL,GAAoB,IAAI1F,YAAJ,EAApB;IACA,KAAK2F,cAAL,GAAsB,IAAI3F,YAAJ,EAAtB;IACA,KAAK4F,mBAAL,GAA2B,IAAI5F,YAAJ,EAA3B;IACA,KAAK6F,sBAAL,GAA8B,IAAI7F,YAAJ,EAA9B;IACA,KAAK8F,UAAL,GAAkB,IAAI9F,YAAJ,EAAlB;IACA,KAAK+F,cAAL,GAAsB,IAAI/F,YAAJ,EAAtB;IACA,KAAKgG,YAAL,GAAoB,IAAIhG,YAAJ,EAApB;IACA;;IACA,KAAKiG,iBAAL,GAAyB,EAAzB;IACA,KAAKC,MAAL,GAAc,EAAd;IACA,KAAKC,aAAL,GAAqB,CAArB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,aAAL,GAAqB,EAArB;EACH;;EACmB,IAAhBC,gBAAgB,GAAG;IACnB,OAAO,KAAKL,iBAAZ;EACH;;EACmB,IAAhBK,gBAAgB,CAACC,GAAD,EAAM;IACtB,KAAKN,iBAAL,GAAyBM,GAAzB;IACAC,OAAO,CAACC,IAAR,CAAa,0FAAb;EACH;;EACDC,QAAQ,GAAG;IACP,IAAI,KAAKlD,IAAL,IAAa,KAAKC,cAAlB,IAAoC,CAAC,KAAKkD,aAA9C,EAA6D;MACzD,KAAKrB,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB;IACH;;IACD,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBnC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACoC,OAAL,EAAR;QACI,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBrC,IAAI,CAACsC,QAA5B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKC,cAAL,GAAsBvC,IAAI,CAACsC,QAA3B;UACA;;QACJ,KAAK,MAAL;UACI,KAAKE,YAAL,GAAoBxC,IAAI,CAACsC,QAAzB;UACA;;QACJ,KAAK,aAAL;UACI,KAAKG,mBAAL,GAA2BzC,IAAI,CAACsC,QAAhC;UACA;;QACJ,KAAK,QAAL;UACI,KAAKI,cAAL,GAAsB1C,IAAI,CAACsC,QAA3B;UACA;;QACJ,KAAK,SAAL;UACI,KAAKK,eAAL,GAAuB3C,IAAI,CAACsC,QAA5B;UACA;;QACJ,KAAK,UAAL;UACI,KAAKM,gBAAL,GAAwB5C,IAAI,CAACsC,QAA7B;UACA;;QACJ,KAAK,cAAL;UACI,KAAKO,oBAAL,GAA4B7C,IAAI,CAACsC,QAAjC;UACA;;QACJ,KAAK,eAAL;UACI,KAAKQ,qBAAL,GAA6B9C,IAAI,CAACsC,QAAlC;UACA;;QACJ,KAAK,gBAAL;UACI,KAAKS,sBAAL,GAA8B/C,IAAI,CAACsC,QAAnC;UACA;;QACJ,KAAK,uBAAL;UACI,KAAKU,6BAAL,GAAqChD,IAAI,CAACsC,QAA1C;UACA;;QACJ,KAAK,cAAL;UACI,KAAKW,oBAAL,GAA4BjD,IAAI,CAACsC,QAAjC;UACA;;QACJ,KAAK,YAAL;UACI,KAAKY,kBAAL,GAA0BlD,IAAI,CAACsC,QAA/B;UACA;;QACJ,KAAK,cAAL;UACI,KAAKa,oBAAL,GAA4BnD,IAAI,CAACsC,QAAjC;UACA;;QACJ,KAAK,gBAAL;UACI,KAAKc,sBAAL,GAA8BpD,IAAI,CAACsC,QAAnC;UACA;MA7CR;IA+CH,CAhDD;EAiDH;;EACDe,WAAW,CAACC,YAAD,EAAe;IACtB,IAAIA,YAAY,CAACvF,KAAjB,EAAwB;MACpB,KAAKqD,MAAL,GAAckC,YAAY,CAACvF,KAAb,CAAmBwF,YAAjC;;MACA,IAAI,CAAC,KAAK7E,IAAV,EAAgB;QACZ,KAAK8E,YAAL,GAAqB,KAAKpC,MAAL,GAAc,KAAKA,MAAL,CAAYqC,MAA1B,GAAmC,CAAxD;QACA,IAAI,KAAKrE,QAAL,IAAiB,QAAjB,IAA6B,KAAKsE,SAAtC,EACI,KAAKC,UAAL,GADJ,KAEK,IAAI,KAAKvE,QAAL,IAAiB,UAAjB,IAA+B,KAAKwE,aAAxC,EACD,KAAKC,YAAL,GADC,KAEA,IAAI,KAAKC,SAAL,EAAJ,EAAsB;UACvB,KAAKC,OAAL;MACP;;MACD,KAAKC,qBAAL;MACA,KAAKxF,YAAL,CAAkBV,UAAlB,CAA6B,KAAKC,KAAlC;IACH;;IACD,IAAIuF,YAAY,CAACI,SAAjB,EAA4B;MACxB,KAAKO,UAAL,GAAkBX,YAAY,CAACI,SAAb,CAAuBH,YAAzC,CADwB,CAExB;;MACA,IAAI,CAAC,KAAK7E,IAAN,IAAc,KAAKsD,WAAvB,EAAoC;QAChC,IAAI,KAAK5C,QAAL,KAAkB,QAAtB,EAAgC;UAC5B,KAAKuE,UAAL;QACH;MACJ;IACJ;;IACD,IAAIL,YAAY,CAACY,SAAjB,EAA4B;MACxB,KAAK5C,UAAL,GAAkBgC,YAAY,CAACY,SAAb,CAAuBX,YAAzC,CADwB,CAExB;;MACA,IAAI,CAAC,KAAK7E,IAAN,IAAc,KAAKsD,WAAvB,EAAoC;QAChC,IAAI,KAAK5C,QAAL,KAAkB,QAAtB,EAAgC;UAC5B,KAAKuE,UAAL;QACH;MACJ;IACJ;;IACD,IAAIL,YAAY,CAACM,aAAjB,EAAgC;MAC5B,KAAKO,cAAL,GAAsBb,YAAY,CAACM,aAAb,CAA2BL,YAAjD;;MACA,IAAI,KAAKnE,QAAL,KAAkB,UAAtB,EAAkC;QAC9B,KAAKyE,YAAL;MACH;IACJ;;IACD,IAAIP,YAAY,CAACc,SAAjB,EAA4B;MACxB,KAAKC,UAAL,GAAkBf,YAAY,CAACc,SAAb,CAAuBb,YAAzC;;MACA,IAAI,CAAC,KAAKe,iCAAV,EAA6C;QACzC,KAAKC,mBAAL;QACA,KAAK/F,YAAL,CAAkBb,iBAAlB;MACH;;MACD,KAAK2G,iCAAL,GAAyC,KAAzC;IACH;EACJ;;EACQ,IAALvG,KAAK,GAAG;IACR,OAAO,KAAKqD,MAAZ;EACH;;EACQ,IAALrD,KAAK,CAAC0D,GAAD,EAAM;IACX,KAAKL,MAAL,GAAcK,GAAd;EACH;;EACDuC,qBAAqB,GAAG;IACpB,KAAKQ,eAAL,GAAuB,EAAvB;IACA,IAAI,KAAKC,SAAT,EACI,KAAKC,kBAAL,GADJ,KAGI,KAAKC,cAAL,CAAoB,IAApB,EAA0B,KAAKC,aAAL,IAAsB,KAAK7G,KAArD,EAA4D,CAA5D,EAA+D,IAA/D;EACP;;EACD4G,cAAc,CAACE,MAAD,EAASC,KAAT,EAAgBC,KAAhB,EAAuBC,OAAvB,EAAgC;IAC1C,IAAIF,KAAK,IAAIA,KAAK,CAACrB,MAAnB,EAA2B;MACvB,KAAK,IAAI5F,IAAT,IAAiBiH,KAAjB,EAAwB;QACpBjH,IAAI,CAACgH,MAAL,GAAcA,MAAd;QACA,MAAMI,OAAO,GAAG;UACZpH,IAAI,EAAEA,IADM;UAEZgH,MAAM,EAAEA,MAFI;UAGZE,KAAK,EAAEA,KAHK;UAIZC,OAAO,EAAEA,OAAO,KAAKH,MAAM,GAAGA,MAAM,CAACK,QAAV,GAAqB,IAAhC;QAJJ,CAAhB;QAMA,KAAKV,eAAL,CAAqBW,IAArB,CAA0BF,OAA1B;;QACA,IAAIA,OAAO,CAACD,OAAR,IAAmBnH,IAAI,CAACqH,QAA5B,EAAsC;UAClC,KAAKP,cAAL,CAAoB9G,IAApB,EAA0BA,IAAI,CAACuH,QAA/B,EAAyCL,KAAK,GAAG,CAAjD,EAAoDE,OAAO,CAACD,OAA5D;QACH;MACJ;IACJ;EACJ;;EACDN,kBAAkB,GAAG;IACjB,IAAIW,IAAI,GAAG,KAAKT,aAAL,IAAsB,KAAK7G,KAAtC;IACA,KAAKyG,eAAL,GAAuB,EAAvB;;IACA,IAAIa,IAAI,IAAIA,IAAI,CAAC5B,MAAjB,EAAyB;MACrB,MAAM7E,KAAK,GAAG,KAAKF,IAAL,GAAY,CAAZ,GAAgB,KAAKE,KAAnC;;MACA,KAAK,IAAI0G,CAAC,GAAG1G,KAAb,EAAoB0G,CAAC,GAAI1G,KAAK,GAAG,KAAK2G,IAAtC,EAA6CD,CAAC,EAA9C,EAAkD;QAC9C,IAAIzH,IAAI,GAAGwH,IAAI,CAACC,CAAD,CAAf;;QACA,IAAIzH,IAAJ,EAAU;UACN,KAAK2G,eAAL,CAAqBW,IAArB,CAA0B;YACtBtH,IAAI,EAAEA,IADgB;YAEtBgH,MAAM,EAAE,IAFc;YAGtBE,KAAK,EAAE,CAHe;YAItBC,OAAO,EAAE;UAJa,CAA1B;UAMA,KAAKL,cAAL,CAAoB9G,IAApB,EAA0BA,IAAI,CAACuH,QAA/B,EAAyC,CAAzC,EAA4C,IAA5C;QACH;MACJ;IACJ;EACJ;;EACe,IAAZ5B,YAAY,GAAG;IACf,OAAO,KAAKnC,aAAZ;EACH;;EACe,IAAZmC,YAAY,CAAC/B,GAAD,EAAM;IAClB,KAAKJ,aAAL,GAAqBI,GAArB;IACA,KAAKjD,YAAL,CAAkBR,oBAAlB,CAAuC,KAAKqD,aAA5C;EACH;;EACY,IAATqC,SAAS,GAAG;IACZ,OAAO,KAAKO,UAAZ;EACH;;EACY,IAATP,SAAS,CAACjC,GAAD,EAAM;IACf,KAAKwC,UAAL,GAAkBxC,GAAlB;EACH;;EACY,IAATyC,SAAS,GAAG;IACZ,OAAO,KAAK5C,UAAZ;EACH;;EACY,IAAT4C,SAAS,CAACzC,GAAD,EAAM;IACf,KAAKH,UAAL,GAAkBG,GAAlB;EACH;;EACgB,IAAbmC,aAAa,GAAG;IAChB,OAAO,KAAKO,cAAZ;EACH;;EACgB,IAAbP,aAAa,CAACnC,GAAD,EAAM;IACnB,KAAK0C,cAAL,GAAsB1C,GAAtB;EACH;;EACY,IAAT2C,SAAS,GAAG;IACZ,OAAO,KAAKC,UAAZ;EACH;;EACY,IAATD,SAAS,CAAC3C,GAAD,EAAM;IACf,KAAK4C,UAAL,GAAkB5C,GAAlB;EACH;;EACD8C,mBAAmB,GAAG;IAClB,IAAI,KAAKiB,OAAL,IAAgB,KAAKnB,UAAzB,EAAqC;MACjC,KAAK9C,aAAL,GAAqB,EAArB;;MACA,IAAIkE,KAAK,CAACC,OAAN,CAAc,KAAKrB,UAAnB,CAAJ,EAAoC;QAChC,KAAK,IAAIxG,IAAT,IAAiB,KAAKwG,UAAtB,EAAkC;UAC9B,KAAK9C,aAAL,CAAmBoE,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwC,KAAKG,OAA7C,CAAD,CAAzB,IAAoF,CAApF;QACH;MACJ,CAJD,MAKK;QACD,KAAKjE,aAAL,CAAmBoE,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6B,KAAKvB,UAAL,CAAgBgB,IAA7C,EAAmD,KAAKG,OAAxD,CAAD,CAAzB,IAA+F,CAA/F;MACH;IACJ;EACJ;;EACDK,YAAY,CAACC,KAAD,EAAQ;IAChB,KAAKlH,KAAL,GAAakH,KAAK,CAAClH,KAAnB;IACA,KAAK2G,IAAL,GAAYO,KAAK,CAACP,IAAlB;IACA,IAAI,KAAK7G,IAAT,EACI,KAAK8B,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB,EADJ,KAGI,KAAK2C,kBAAL;IACJ,KAAKnE,MAAL,CAAYuB,IAAZ,CAAiB;MACblD,KAAK,EAAE,KAAKA,KADC;MAEb2G,IAAI,EAAE,KAAKA;IAFE,CAAjB;IAIA,KAAK/G,YAAL,CAAkBV,UAAlB,CAA6B,KAAKC,KAAlC;;IACA,IAAI,KAAKgI,UAAT,EAAqB;MACjB,KAAKC,cAAL;IACH;EACJ;;EACDC,IAAI,CAACH,KAAD,EAAQ;IACR,IAAII,aAAa,GAAGJ,KAAK,CAACI,aAA1B;;IACA,IAAI,KAAK9G,QAAL,KAAkB,QAAtB,EAAgC;MAC5B,KAAKkC,UAAL,GAAmB,KAAKoC,SAAL,KAAmBoC,KAAK,CAACK,KAA1B,GAAmC,KAAKjC,SAAL,GAAiB,CAAC,CAArD,GAAyD,KAAK/E,gBAAhF;MACA,KAAK8E,UAAL,GAAkB6B,KAAK,CAACK,KAAxB;MACA,KAAKxC,UAAL;;MACA,IAAI,KAAKtE,eAAL,IAAwB,KAAK0G,UAAjC,EAA6C;QACzC,KAAKC,cAAL;MACH;IACJ;;IACD,IAAI,KAAK5G,QAAL,KAAkB,UAAtB,EAAkC;MAC9B,IAAIgH,OAAO,GAAGF,aAAa,CAACE,OAAd,IAAyBF,aAAa,CAACG,OAArD;MACA,IAAI5I,QAAQ,GAAG,KAAK6I,WAAL,CAAiBR,KAAK,CAACK,KAAvB,CAAf;;MACA,IAAI1I,QAAJ,EAAc;QACV,IAAI,CAAC2I,OAAL,EAAc;UACV,KAAKjC,cAAL,GAAsB,CAAC;YAAEgC,KAAK,EAAEL,KAAK,CAACK,KAAf;YAAsBI,KAAK,EAAE9I,QAAQ,CAAC8I,KAAT,GAAiB,CAAC;UAA/C,CAAD,CAAtB;;UACA,IAAI,KAAKlH,eAAL,IAAwB,KAAK0G,UAAjC,EAA6C;YACzC,KAAKC,cAAL;UACH;QACJ,CALD,MAMK;UACDvI,QAAQ,CAAC8I,KAAT,GAAiB9I,QAAQ,CAAC8I,KAAT,GAAiB,CAAC,CAAnC;QACH;MACJ,CAVD,MAWK;QACD,IAAI,CAACH,OAAD,IAAY,CAAC,KAAKxC,aAAtB,EAAqC;UACjC,KAAKO,cAAL,GAAsB,EAAtB;;UACA,IAAI,KAAK9E,eAAL,IAAwB,KAAK0G,UAAjC,EAA6C;YACzC,KAAKC,cAAL;UACH;QACJ;;QACD,KAAKpC,aAAL,CAAmBuB,IAAnB,CAAwB;UAAEgB,KAAK,EAAEL,KAAK,CAACK,KAAf;UAAsBI,KAAK,EAAE,KAAKpH;QAAlC,CAAxB;MACH;;MACD,KAAK0E,YAAL;IACH;EACJ;;EACDF,UAAU,GAAG;IACT,IAAI,KAAKD,SAAL,IAAkB,KAAKQ,SAA3B,EAAsC;MAClC,IAAI,KAAKxF,IAAT,EAAe;QACX,KAAK8B,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB;MACH,CAFD,MAGK,IAAI,KAAKhE,KAAT,EAAgB;QACjB,KAAKyI,SAAL,CAAe,KAAKzI,KAApB;;QACA,IAAI,KAAK+F,SAAL,EAAJ,EAAsB;UAClB,KAAKC,OAAL;QACH;MACJ;;MACD,IAAItG,QAAQ,GAAG;QACX0I,KAAK,EAAE,KAAKzC,SADD;QAEX6C,KAAK,EAAE,KAAKrC;MAFD,CAAf;MAIA,KAAK1G,MAAL,CAAYsE,IAAZ,CAAiBrE,QAAjB;MACA,KAAKe,YAAL,CAAkBhB,MAAlB,CAAyBC,QAAzB;MACA,KAAKuG,qBAAL;IACH;EACJ;;EACDwC,SAAS,CAAC1B,KAAD,EAAQ;IACb,IAAI,CAACA,KAAD,IAAUA,KAAK,CAACrB,MAAN,KAAiB,CAA/B,EAAkC;MAC9B;IACH;;IACD,IAAI,KAAKgD,UAAT,EAAqB;MACjB,KAAKhG,YAAL,CAAkBqB,IAAlB,CAAuB;QACnBuD,IAAI,EAAEP,KADa;QAEnB4B,IAAI,EAAE,KAAKtH,QAFQ;QAGnB+G,KAAK,EAAE,KAAKzC,SAHO;QAInB6C,KAAK,EAAE,KAAKrC;MAJO,CAAvB;IAMH,CAPD,MAQK;MACDY,KAAK,CAACmB,IAAN,CAAW,CAACU,KAAD,EAAQC,KAAR,KAAkB;QACzB,IAAIC,MAAM,GAAGvK,WAAW,CAACsJ,gBAAZ,CAA6Be,KAAK,CAACtB,IAAnC,EAAyC,KAAK3B,SAA9C,CAAb;QACA,IAAIoD,MAAM,GAAGxK,WAAW,CAACsJ,gBAAZ,CAA6BgB,KAAK,CAACvB,IAAnC,EAAyC,KAAK3B,SAA9C,CAAb;QACA,IAAIqD,MAAM,GAAG,IAAb;QACA,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACIC,MAAM,GAAG,CAAC,CAAV,CADJ,KAEK,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAI,OAAOF,MAAP,KAAkB,QAAlB,IAA8B,OAAOC,MAAP,KAAkB,QAApD,EACDC,MAAM,GAAGF,MAAM,CAACG,aAAP,CAAqBF,MAArB,EAA6BG,SAA7B,EAAwC;UAAEC,OAAO,EAAE;QAAX,CAAxC,CAAT,CADC,KAGDH,MAAM,GAAIF,MAAM,GAAGC,MAAV,GAAoB,CAAC,CAArB,GAA0BD,MAAM,GAAGC,MAAV,GAAoB,CAApB,GAAwB,CAA1D;QACJ,OAAQ,KAAK5C,SAAL,GAAiB6C,MAAzB;MACH,CAfD;IAgBH;;IACD,KAAK,IAAIlJ,IAAT,IAAiBiH,KAAjB,EAAwB;MACpB,KAAK0B,SAAL,CAAe3I,IAAI,CAACuH,QAApB;IACH;EACJ;;EACDvB,YAAY,GAAG;IACX,IAAI,KAAKD,aAAT,EAAwB;MACpB,IAAI,KAAKlF,IAAT,EAAe;QACX,KAAK8B,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB;MACH,CAFD,MAGK,IAAI,KAAKhE,KAAT,EAAgB;QACjB,KAAKoJ,iBAAL,CAAuB,KAAKpJ,KAA5B;;QACA,IAAI,KAAK+F,SAAL,EAAJ,EAAsB;UAClB,KAAKC,OAAL;QACH;MACJ;;MACD,KAAKvG,MAAL,CAAYsE,IAAZ,CAAiB;QACbsF,aAAa,EAAE,KAAKxD;MADP,CAAjB;MAGA,KAAKI,qBAAL;MACA,KAAKxF,YAAL,CAAkBhB,MAAlB,CAAyB,KAAKoG,aAA9B;IACH;EACJ;;EACDuD,iBAAiB,CAACrC,KAAD,EAAQ;IACrB,IAAI,CAACA,KAAD,IAAUA,KAAK,CAACrB,MAAN,KAAiB,CAA/B,EAAkC;MAC9B;IACH;;IACD,IAAI,KAAKgD,UAAT,EAAqB;MACjB,KAAKhG,YAAL,CAAkBqB,IAAlB,CAAuB;QACnBuD,IAAI,EAAE,KAAKtH,KADQ;QAEnB2I,IAAI,EAAE,KAAKtH,QAFQ;QAGnBwE,aAAa,EAAE,KAAKA;MAHD,CAAvB;IAKH,CAND,MAOK;MACDkB,KAAK,CAACmB,IAAN,CAAW,CAACU,KAAD,EAAQC,KAAR,KAAkB;QACzB,OAAO,KAAKS,cAAL,CAAoBV,KAApB,EAA2BC,KAA3B,EAAkC,KAAKhD,aAAvC,EAAsD,CAAtD,CAAP;MACH,CAFD;IAGH;;IACD,KAAK,IAAI/F,IAAT,IAAiBiH,KAAjB,EAAwB;MACpB,KAAKqC,iBAAL,CAAuBtJ,IAAI,CAACuH,QAA5B;IACH;EACJ;;EACDiC,cAAc,CAACV,KAAD,EAAQC,KAAR,EAAehD,aAAf,EAA8B7D,KAA9B,EAAqC;IAC/C,IAAI8G,MAAM,GAAGvK,WAAW,CAACsJ,gBAAZ,CAA6Be,KAAK,CAACtB,IAAnC,EAAyCzB,aAAa,CAAC7D,KAAD,CAAb,CAAqBoG,KAA9D,CAAb;IACA,IAAIW,MAAM,GAAGxK,WAAW,CAACsJ,gBAAZ,CAA6BgB,KAAK,CAACvB,IAAnC,EAAyCzB,aAAa,CAAC7D,KAAD,CAAb,CAAqBoG,KAA9D,CAAb;IACA,IAAIY,MAAM,GAAG,IAAb;IACA,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACIC,MAAM,GAAG,CAAC,CAAV,CADJ,KAEK,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAIF,MAAM,IAAI,IAAV,IAAkBC,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT;;IACJ,IAAI,OAAOF,MAAP,IAAiB,QAAjB,IAA6BA,MAAM,YAAYlB,MAAnD,EAA2D;MACvD,IAAIkB,MAAM,CAACG,aAAP,IAAyBH,MAAM,IAAIC,MAAvC,EAAgD;QAC5C,OAAQlD,aAAa,CAAC7D,KAAD,CAAb,CAAqBwG,KAArB,GAA6BM,MAAM,CAACG,aAAP,CAAqBF,MAArB,EAA6BG,SAA7B,EAAwC;UAAEC,OAAO,EAAE;QAAX,CAAxC,CAArC;MACH;IACJ,CAJD,MAKK;MACDH,MAAM,GAAIF,MAAM,GAAGC,MAAV,GAAoB,CAAC,CAArB,GAAyB,CAAlC;IACH;;IACD,IAAID,MAAM,IAAIC,MAAd,EAAsB;MAClB,OAAQlD,aAAa,CAACH,MAAd,GAAuB,CAAxB,GAA8B1D,KAA9B,GAAwC,KAAKsH,cAAL,CAAoBV,KAApB,EAA2BC,KAA3B,EAAkChD,aAAlC,EAAiD7D,KAAK,GAAG,CAAzD,CAAxC,GAAuG,CAA9G;IACH;;IACD,OAAQ6D,aAAa,CAAC7D,KAAD,CAAb,CAAqBwG,KAArB,GAA6BQ,MAArC;EACH;;EACDT,WAAW,CAACH,KAAD,EAAQ;IACf,IAAI,KAAKvC,aAAL,IAAsB,KAAKA,aAAL,CAAmBH,MAA7C,EAAqD;MACjD,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1B,aAAL,CAAmBH,MAAvC,EAA+C6B,CAAC,EAAhD,EAAoD;QAChD,IAAI,KAAK1B,aAAL,CAAmB0B,CAAnB,EAAsBa,KAAtB,KAAgCA,KAApC,EAA2C;UACvC,OAAO,KAAKvC,aAAL,CAAmB0B,CAAnB,CAAP;QACH;MACJ;IACJ;;IACD,OAAO,IAAP;EACH;;EACDgC,QAAQ,CAACnB,KAAD,EAAQ;IACZ,IAAI,KAAK/G,QAAL,KAAkB,QAAtB,EAAgC;MAC5B,OAAQ,KAAKsE,SAAL,IAAkB,KAAKA,SAAL,KAAmByC,KAA7C;IACH,CAFD,MAGK,IAAI,KAAK/G,QAAL,KAAkB,UAAtB,EAAkC;MACnC,IAAImI,MAAM,GAAG,KAAb;;MACA,IAAI,KAAK3D,aAAT,EAAwB;QACpB,KAAK,IAAI0B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1B,aAAL,CAAmBH,MAAvC,EAA+C6B,CAAC,EAAhD,EAAoD;UAChD,IAAI,KAAK1B,aAAL,CAAmB0B,CAAnB,EAAsBa,KAAtB,IAA+BA,KAAnC,EAA0C;YACtCoB,MAAM,GAAG,IAAT;YACA;UACH;QACJ;MACJ;;MACD,OAAOA,MAAP;IACH;EACJ;;EACDxF,sBAAsB,GAAG;IACrB,OAAO;MACHnD,KAAK,EAAE,KAAKA,KADT;MAEH2G,IAAI,EAAE,KAAKA,IAFR;MAGH7B,SAAS,EAAE,KAAKA,SAHb;MAIHQ,SAAS,EAAE,KAAKA,SAJb;MAKHjE,OAAO,EAAE,KAAKA,OALX;MAMHuH,YAAY,EAAE,KAAKvH,OAAL,IAAgB,KAAKA,OAAL,CAAa,QAAb,CAAhB,GAAyC,KAAKA,OAAL,CAAa,QAAb,EAAuBlC,KAAhE,GAAwE,IANnF;MAOH6F,aAAa,EAAE,KAAKA,aAPjB;MAQH6D,WAAW,EAAE,MAAM,KAAKnJ,EAAL,CAAQoJ,aAAR;IARhB,CAAP;EAUH;;EACDC,cAAc,CAAC7B,KAAD,EAAQ;IAClB,KAAKtF,UAAL,CAAgBsB,IAAhB,CAAqB8F,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAK9F,sBAAL,EAAlB,CAAd,EAAgE+D,KAAhE,CAAd,EAAsF;MAAEP,IAAI,EAAEO,KAAK,CAACgC,IAAN,GAAahC,KAAK,CAAClH;IAA3B,CAAtF,CAArB;EACH;;EACDoH,cAAc,GAAG;IACb,IAAI,KAAKnE,aAAT,EACI,KAAKkG,oBAAL,CAA0B,CAA1B,EADJ,KAGI,KAAKC,QAAL,CAAc;MAAEC,GAAG,EAAE;IAAP,CAAd;EACP;;EACDF,oBAAoB,CAAChI,KAAD,EAAQ;IACxB,IAAI,KAAKmI,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBH,oBAAzB,CAA8ChI,KAA9C;IACH;;IACD,IAAI,KAAKoI,yBAAT,EAAoC;MAChC,KAAKA,yBAAL,CAA+BJ,oBAA/B,CAAoDhI,KAApD;IACH;EACJ;;EACDiI,QAAQ,CAACI,OAAD,EAAU;IACd,IAAI,KAAKF,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBF,QAAzB,CAAkCI,OAAlC;IACH;;IACD,IAAI,KAAKD,yBAAT,EAAoC;MAChC,KAAKA,yBAAL,CAA+BH,QAA/B,CAAwCI,OAAxC;IACH;EACJ;;EACDC,OAAO,GAAG;IACN,IAAIhD,IAAI,GAAG,KAAKT,aAAL,IAAsB,KAAK7G,KAAtC;IACA,OAAOsH,IAAI,IAAI,IAAR,IAAgBA,IAAI,CAAC5B,MAAL,IAAe,CAAtC;EACH;;EACD6E,mBAAmB,GAAG;IAClB,OAAO,KAAKjK,EAAL,CAAQkK,aAAR,CAAsBnD,QAAtB,CAA+B,CAA/B,CAAP;EACH;;EACDoD,mBAAmB,CAAC1C,KAAD,EAAQ;IACvB,IAAI2C,aAAa,GAAGzM,UAAU,CAAC0M,SAAX,CAAqB,KAAKC,kBAAL,CAAwBJ,aAA7C,EAA4DK,IAAhF;IACA,KAAKC,kBAAL,GAA2B/C,KAAK,CAACgD,KAAN,GAAcL,aAAd,GAA8B,KAAKE,kBAAL,CAAwBJ,aAAxB,CAAsCQ,UAA/F;IACAjD,KAAK,CAACkD,cAAN;EACH;;EACDC,cAAc,CAACnD,KAAD,EAAQ;IAClB,IAAI2C,aAAa,GAAGzM,UAAU,CAAC0M,SAAX,CAAqB,KAAKC,kBAAL,CAAwBJ,aAA7C,EAA4DK,IAAhF;IACA5M,UAAU,CAACkN,QAAX,CAAoB,KAAKP,kBAAL,CAAwBJ,aAA5C,EAA2D,qBAA3D;IACA,KAAKY,qBAAL,CAA2BZ,aAA3B,CAAyCa,KAAzC,CAA+CC,MAA/C,GAAwD,KAAKV,kBAAL,CAAwBJ,aAAxB,CAAsCe,YAAtC,GAAqD,IAA7G;IACA,KAAKH,qBAAL,CAA2BZ,aAA3B,CAAyCa,KAAzC,CAA+CnB,GAA/C,GAAqD,IAAI,IAAzD;IACA,KAAKkB,qBAAL,CAA2BZ,aAA3B,CAAyCa,KAAzC,CAA+CR,IAA/C,GAAuD9C,KAAK,CAACgD,KAAN,GAAcL,aAAd,GAA8B,KAAKE,kBAAL,CAAwBJ,aAAxB,CAAsCQ,UAArE,GAAmF,IAAzI;IACA,KAAKI,qBAAL,CAA2BZ,aAA3B,CAAyCa,KAAzC,CAA+CG,OAA/C,GAAyD,OAAzD;EACH;;EACDC,iBAAiB,CAAC1D,KAAD,EAAQ2D,MAAR,EAAgB;IAC7B,IAAIC,KAAK,GAAG,KAAKP,qBAAL,CAA2BZ,aAA3B,CAAyCoB,UAAzC,GAAsD,KAAKd,kBAAvE;IACA,IAAIe,WAAW,GAAGH,MAAM,CAACI,WAAzB;IACA,IAAIC,cAAc,GAAGF,WAAW,GAAGF,KAAnC;IACA,IAAIK,QAAQ,GAAGN,MAAM,CAACL,KAAP,CAAaW,QAAb,IAAyB,EAAxC;;IACA,IAAIH,WAAW,GAAGF,KAAd,GAAsBM,QAAQ,CAACD,QAAD,CAAlC,EAA8C;MAC1C,IAAI,KAAKlK,gBAAL,KAA0B,KAA9B,EAAqC;QACjC,IAAIoK,UAAU,GAAGR,MAAM,CAACS,kBAAxB;;QACA,OAAO,CAACD,UAAU,CAACE,YAAnB,EAAiC;UAC7BF,UAAU,GAAGA,UAAU,CAACC,kBAAxB;QACH;;QACD,IAAID,UAAJ,EAAgB;UACZ,IAAIG,eAAe,GAAGH,UAAU,CAACJ,WAAX,GAAyBH,KAA/C;UACA,IAAIW,kBAAkB,GAAGJ,UAAU,CAACb,KAAX,CAAiBW,QAAjB,IAA6B,EAAtD;;UACA,IAAID,cAAc,GAAG,EAAjB,IAAuBM,eAAe,GAAGJ,QAAQ,CAACK,kBAAD,CAArD,EAA2E;YACvE,IAAI,KAAKtE,UAAT,EAAqB;cACjB,IAAIuE,cAAc,GAAG,KAAKC,wBAAL,CAA8Bd,MAA9B,CAArB;cACA,IAAIe,mBAAmB,GAAGxO,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,oCAAtC,KAA+EtO,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,4BAAtC,CAAzG;cACA,IAAII,qBAAqB,GAAG1O,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,2CAAtC,CAA5B;cACA,IAAIK,qBAAqB,GAAG3O,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,2CAAtC,CAA5B;cACA,IAAIM,iBAAiB,GAAG5O,UAAU,CAAC+D,KAAX,CAAiB0J,MAAjB,CAAxB;cACA,KAAKoB,cAAL,CAAoBH,qBAApB,EAA2CE,iBAA3C,EAA8Dd,cAA9D,EAA8EM,eAA9E;cACA,KAAKS,cAAL,CAAoBL,mBAApB,EAAyCI,iBAAzC,EAA4Dd,cAA5D,EAA4EM,eAA5E;cACA,KAAKS,cAAL,CAAoBF,qBAApB,EAA2CC,iBAA3C,EAA8Dd,cAA9D,EAA8EM,eAA9E;YACH,CATD,MAUK;cACDX,MAAM,CAACL,KAAP,CAAa0B,KAAb,GAAqBhB,cAAc,GAAG,IAAtC;;cACA,IAAIG,UAAJ,EAAgB;gBACZA,UAAU,CAACb,KAAX,CAAiB0B,KAAjB,GAAyBV,eAAe,GAAG,IAA3C;cACH;YACJ;UACJ;QACJ;MACJ,CA3BD,MA4BK,IAAI,KAAKvK,gBAAL,KAA0B,QAA9B,EAAwC;QACzC,IAAI,KAAKkG,UAAT,EAAqB;UACjB,IAAIuE,cAAc,GAAG,KAAKC,wBAAL,CAA8Bd,MAA9B,CAArB;UACA,IAAIsB,cAAc,GAAG/O,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,8BAAtC,KAAyEtO,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,sBAAtC,CAA9F;UACA,IAAIU,gBAAgB,GAAGhP,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,gCAAtC,CAAvB;UACA,IAAIW,gBAAgB,GAAGjP,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,gCAAtC,CAAvB;UACA,IAAIE,mBAAmB,GAAGxO,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,oCAAtC,KAA+EtO,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,4BAAtC,CAAzG;UACA,IAAII,qBAAqB,GAAG1O,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,2CAAtC,CAA5B;UACA,IAAIK,qBAAqB,GAAG3O,UAAU,CAACyO,UAAX,CAAsBH,cAAtB,EAAsC,2CAAtC,CAA5B;UACAE,mBAAmB,CAACpB,KAApB,CAA0B0B,KAA1B,GAAkCN,mBAAmB,CAACX,WAApB,GAAkCH,KAAlC,GAA0C,IAA5E;UACAgB,qBAAqB,CAACtB,KAAtB,CAA4B0B,KAA5B,GAAoCJ,qBAAqB,CAACb,WAAtB,GAAoCH,KAApC,GAA4C,IAAhF;;UACA,IAAIiB,qBAAJ,EAA2B;YACvBA,qBAAqB,CAACvB,KAAtB,CAA4B0B,KAA5B,GAAoCH,qBAAqB,CAACd,WAAtB,GAAoCH,KAApC,GAA4C,IAAhF;UACH;;UACD,IAAIkB,iBAAiB,GAAG5O,UAAU,CAAC+D,KAAX,CAAiB0J,MAAjB,CAAxB;UACA,MAAMyB,wBAAwB,GAAGzB,MAAM,GAAGe,mBAAmB,CAACX,WAApB,GAAkCH,KAArC,GAA6CI,cAApF;UACA,MAAMqB,0BAA0B,GAAG1B,MAAM,GAAGiB,qBAAqB,CAACb,WAAtB,GAAoCH,KAAvC,GAA+CI,cAAxF;UACA,MAAMsB,qBAAqB,GAAG,KAAKzC,kBAAL,CAAwBJ,aAAxB,CAAsCsB,WAAtC,IAAqDqB,wBAAnF;;UACA,IAAIG,QAAQ,GAAG,CAACC,SAAD,EAAYC,KAAZ,EAAmBT,KAAnB,EAA0BM,qBAA1B,KAAoD;YAC/D,IAAIE,SAAS,IAAIC,KAAjB,EAAwB;cACpBD,SAAS,CAAClC,KAAV,CAAgB0B,KAAhB,GAAwBM,qBAAqB,GAAGN,KAAK,GAAG9O,UAAU,CAACwP,uBAAX,CAAmCT,cAAnC,CAAR,GAA6D,IAAhE,GAAuE,MAApH;cACAQ,KAAK,CAACnC,KAAN,CAAY0B,KAAZ,GAAoBA,KAAK,GAAG,IAA5B;YACH;UACJ,CALD;;UAMAO,QAAQ,CAACN,cAAD,EAAiBP,mBAAjB,EAAsCU,wBAAtC,EAAgEE,qBAAhE,CAAR;UACAC,QAAQ,CAACL,gBAAD,EAAmBN,qBAAnB,EAA0CS,0BAA1C,EAAsEC,qBAAtE,CAAR;UACAC,QAAQ,CAACJ,gBAAD,EAAmBN,qBAAnB,EAA0CQ,0BAA1C,EAAsEC,qBAAtE,CAAR;UACA,KAAKP,cAAL,CAAoBH,qBAApB,EAA2CE,iBAA3C,EAA8Dd,cAA9D,EAA8E,IAA9E;UACA,KAAKe,cAAL,CAAoBL,mBAApB,EAAyCI,iBAAzC,EAA4Dd,cAA5D,EAA4E,IAA5E;UACA,KAAKe,cAAL,CAAoBF,qBAApB,EAA2CC,iBAA3C,EAA8Dd,cAA9D,EAA8E,IAA9E;QACH,CA7BD,MA8BK;UACD,KAAK2B,cAAL,CAAoBlD,aAApB,CAAkCa,KAAlC,CAAwC0B,KAAxC,GAAgD,KAAKW,cAAL,CAAoBlD,aAApB,CAAkCsB,WAAlC,GAAgDH,KAAhD,GAAwD,IAAxG;UACAD,MAAM,CAACL,KAAP,CAAa0B,KAAb,GAAqBhB,cAAc,GAAG,IAAtC;UACA,IAAI4B,cAAc,GAAG,KAAKD,cAAL,CAAoBlD,aAApB,CAAkCa,KAAlC,CAAwC0B,KAA7D;UACA,KAAKnC,kBAAL,CAAwBJ,aAAxB,CAAsCa,KAAtC,CAA4C0B,KAA5C,GAAoDY,cAAc,GAAG,IAArE;QACH;MACJ;;MACD,KAAKhL,WAAL,CAAiBoB,IAAjB,CAAsB;QAClB6J,OAAO,EAAElC,MADS;QAElBC,KAAK,EAAEA;MAFW,CAAtB;IAIH;;IACD,KAAKP,qBAAL,CAA2BZ,aAA3B,CAAyCa,KAAzC,CAA+CG,OAA/C,GAAyD,MAAzD;IACAvN,UAAU,CAAC4P,WAAX,CAAuB,KAAKjD,kBAAL,CAAwBJ,aAA/C,EAA8D,qBAA9D;EACH;;EACDgC,wBAAwB,CAACd,MAAD,EAAS;IAC7B,IAAIA,MAAJ,EAAY;MACR,IAAI5E,MAAM,GAAG4E,MAAM,CAACoC,aAApB;;MACA,OAAOhH,MAAM,IAAI,CAAC7I,UAAU,CAAC8P,QAAX,CAAoBjH,MAApB,EAA4B,6BAA5B,CAAlB,EAA8E;QAC1EA,MAAM,GAAGA,MAAM,CAACgH,aAAhB;MACH;;MACD,OAAOhH,MAAP;IACH,CAND,MAOK;MACD,OAAO,IAAP;IACH;EACJ;;EACDgG,cAAc,CAACU,KAAD,EAAQX,iBAAR,EAA2Bd,cAA3B,EAA2CM,eAA3C,EAA4D;IACtE,IAAImB,KAAJ,EAAW;MACP,IAAIQ,QAAQ,GAAGR,KAAK,CAACnG,QAAN,CAAe,CAAf,EAAkB4G,QAAlB,KAA+B,UAA/B,GAA4CT,KAAK,CAACnG,QAAN,CAAe,CAAf,CAA5C,GAAgE,IAA/E;;MACA,IAAI2G,QAAJ,EAAc;QACV,IAAIE,GAAG,GAAGF,QAAQ,CAAC3G,QAAT,CAAkBwF,iBAAlB,CAAV;QACA,IAAIsB,OAAO,GAAGD,GAAG,CAAC/B,kBAAlB;QACA+B,GAAG,CAAC7C,KAAJ,CAAU0B,KAAV,GAAkBhB,cAAc,GAAG,IAAnC;;QACA,IAAIoC,OAAO,IAAI9B,eAAf,EAAgC;UAC5B8B,OAAO,CAAC9C,KAAR,CAAc0B,KAAd,GAAsBV,eAAe,GAAG,IAAxC;QACH;MACJ,CAPD,MAQK;QACD,MAAM,mEAAN;MACH;IACJ;EACJ;;EACD+B,iBAAiB,CAACrG,KAAD,EAAQsG,aAAR,EAAuB;IACpC,KAAKC,gBAAL,GAAwBrQ,UAAU,CAACsQ,0BAAX,CAAsC,KAAKC,2BAAL,CAAiChE,aAAvE,CAAxB;IACA,KAAKiE,iBAAL,GAAyBxQ,UAAU,CAACyQ,2BAAX,CAAuC,KAAKC,6BAAL,CAAmCnE,aAA1E,CAAzB;IACA,KAAKoE,aAAL,GAAqBP,aAArB;IACAtG,KAAK,CAAC8G,YAAN,CAAmBC,OAAnB,CAA2B,MAA3B,EAAmC,GAAnC,EAJoC,CAIK;EAC5C;;EACDC,iBAAiB,CAAChH,KAAD,EAAQiH,UAAR,EAAoB;IACjC,IAAI,KAAKC,kBAAL,IAA2B,KAAKL,aAAhC,IAAiDI,UAArD,EAAiE;MAC7DjH,KAAK,CAACkD,cAAN;MACA,IAAIiE,eAAe,GAAGjR,UAAU,CAAC0M,SAAX,CAAqB,KAAKC,kBAAL,CAAwBJ,aAA7C,CAAtB;MACA,IAAI2E,gBAAgB,GAAGlR,UAAU,CAAC0M,SAAX,CAAqBqE,UAArB,CAAvB;;MACA,IAAI,KAAKJ,aAAL,IAAsBI,UAA1B,EAAsC;QAClC,IAAII,UAAU,GAAGD,gBAAgB,CAACtE,IAAjB,GAAwBqE,eAAe,CAACrE,IAAzD;QACA,IAAIwE,SAAS,GAAGH,eAAe,CAAChF,GAAhB,GAAsBiF,gBAAgB,CAACjF,GAAvD;QACA,IAAIoF,YAAY,GAAGH,gBAAgB,CAACtE,IAAjB,GAAwBmE,UAAU,CAAClD,WAAX,GAAyB,CAApE;QACA,KAAK0C,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDnB,GAArD,GAA2DiF,gBAAgB,CAACjF,GAAjB,GAAuBgF,eAAe,CAAChF,GAAvC,IAA8C,KAAKuE,iBAAL,GAAyB,CAAvE,IAA4E,IAAvI;QACA,KAAKE,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDnB,GAAvD,GAA6DiF,gBAAgB,CAACjF,GAAjB,GAAuBgF,eAAe,CAAChF,GAAvC,GAA6C8E,UAAU,CAACzD,YAAxD,GAAuE,IAApI;;QACA,IAAIxD,KAAK,CAACgD,KAAN,GAAcuE,YAAlB,EAAgC;UAC5B,KAAKd,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDR,IAArD,GAA6DuE,UAAU,GAAGJ,UAAU,CAAClD,WAAxB,GAAsCyD,IAAI,CAACC,IAAL,CAAU,KAAKlB,gBAAL,GAAwB,CAAlC,CAAvC,GAA+E,IAA3I;UACA,KAAKK,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDR,IAAvD,GAA+DuE,UAAU,GAAGJ,UAAU,CAAClD,WAAxB,GAAsCyD,IAAI,CAACC,IAAL,CAAU,KAAKlB,gBAAL,GAAwB,CAAlC,CAAvC,GAA+E,IAA7I;UACA,KAAKmB,YAAL,GAAoB,CAApB;QACH,CAJD,MAKK;UACD,KAAKjB,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDR,IAArD,GAA6DuE,UAAU,GAAGG,IAAI,CAACC,IAAL,CAAU,KAAKlB,gBAAL,GAAwB,CAAlC,CAAd,GAAsD,IAAlH;UACA,KAAKK,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDR,IAAvD,GAA+DuE,UAAU,GAAGG,IAAI,CAACC,IAAL,CAAU,KAAKlB,gBAAL,GAAwB,CAAlC,CAAd,GAAsD,IAApH;UACA,KAAKmB,YAAL,GAAoB,CAAC,CAArB;QACH;;QACD,KAAKjB,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDG,OAArD,GAA+D,OAA/D;QACA,KAAKmD,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDG,OAAvD,GAAiE,OAAjE;MACH,CAlBD,MAmBK;QACDzD,KAAK,CAAC8G,YAAN,CAAmBa,UAAnB,GAAgC,MAAhC;MACH;IACJ;EACJ;;EACDC,iBAAiB,CAAC5H,KAAD,EAAQ;IACrB,IAAI,KAAKkH,kBAAL,IAA2B,KAAKL,aAApC,EAAmD;MAC/C7G,KAAK,CAACkD,cAAN;MACA,KAAKuD,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDG,OAArD,GAA+D,MAA/D;MACA,KAAKmD,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDG,OAAvD,GAAiE,MAAjE;IACH;EACJ;;EACDoE,YAAY,CAAC7H,KAAD,EAAQ8H,UAAR,EAAoB;IAC5B9H,KAAK,CAACkD,cAAN;;IACA,IAAI,KAAK2D,aAAT,EAAwB;MACpB,IAAIkB,SAAS,GAAG7R,UAAU,CAAC8R,gBAAX,CAA4B,KAAKnB,aAAjC,EAAgD,qBAAhD,CAAhB;MACA,IAAIoB,SAAS,GAAG/R,UAAU,CAAC8R,gBAAX,CAA4BF,UAA5B,EAAwC,qBAAxC,CAAhB;MACA,IAAII,SAAS,GAAIH,SAAS,IAAIE,SAA9B;;MACA,IAAIC,SAAS,KAAMD,SAAS,GAAGF,SAAZ,IAAyB,CAAzB,IAA8B,KAAKL,YAAL,KAAsB,CAAC,CAAtD,IAA6DK,SAAS,GAAGE,SAAZ,IAAyB,CAAzB,IAA8B,KAAKP,YAAL,KAAsB,CAAtH,CAAb,EAAwI;QACpIQ,SAAS,GAAG,KAAZ;MACH;;MACD,IAAIA,SAAS,IAAMD,SAAS,GAAGF,SAAZ,IAAyB,KAAKL,YAAL,KAAsB,CAAlE,EAAuE;QACnEO,SAAS,GAAGA,SAAS,GAAG,CAAxB;MACH;;MACD,IAAIC,SAAS,IAAMD,SAAS,GAAGF,SAAZ,IAAyB,KAAKL,YAAL,KAAsB,CAAC,CAAnE,EAAwE;QACpEO,SAAS,GAAGA,SAAS,GAAG,CAAxB;MACH;;MACD,IAAIC,SAAJ,EAAe;QACX1R,WAAW,CAAC2R,YAAZ,CAAyB,KAAKC,OAA9B,EAAuCL,SAAvC,EAAkDE,SAAlD;QACA,KAAKpN,YAAL,CAAkBmB,IAAlB,CAAuB;UACnB+L,SAAS,EAAEA,SADQ;UAEnBE,SAAS,EAAEA,SAFQ;UAGnBG,OAAO,EAAE,KAAKA;QAHK,CAAvB;MAKH;;MACD,KAAK3B,2BAAL,CAAiChE,aAAjC,CAA+Ca,KAA/C,CAAqDG,OAArD,GAA+D,MAA/D;MACA,KAAKmD,6BAAL,CAAmCnE,aAAnC,CAAiDa,KAAjD,CAAuDG,OAAvD,GAAiE,MAAjE;MACA,KAAKoD,aAAL,CAAmBwB,SAAnB,GAA+B,KAA/B;MACA,KAAKxB,aAAL,GAAqB,IAArB;MACA,KAAKa,YAAL,GAAoB,IAApB;IACH;EACJ;;EACDY,cAAc,CAACtI,KAAD,EAAQ;IAClB,IAAIuI,UAAU,GAAGvI,KAAK,CAACI,aAAN,CAAoBoI,MAApB,CAA2BtC,QAA5C;;IACA,IAAIqC,UAAU,IAAI,OAAd,IAAyBA,UAAU,IAAI,QAAvC,IAAmDA,UAAU,IAAI,GAAjE,IAAyErS,UAAU,CAAC8P,QAAX,CAAoBhG,KAAK,CAACI,aAAN,CAAoBoI,MAAxC,EAAgD,aAAhD,CAA7E,EAA8I;MAC1I;IACH;;IACD,IAAI,KAAKC,aAAT,EAAwB;MACpB,KAAKjK,iCAAL,GAAyC,IAAzC;MACA,IAAIW,OAAO,GAAGa,KAAK,CAACb,OAApB;MACA,IAAIuJ,QAAQ,GAAG,KAAKC,UAAL,CAAgBxJ,OAAO,CAACpH,IAAxB,CAAf;MACA,IAAI6Q,aAAa,GAAG,KAAKC,UAAL,GAAkB,KAAlB,GAA0B,KAAKC,gBAAnD;MACA,IAAIC,YAAY,GAAG,KAAKrJ,OAAL,GAAeG,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6BX,OAAO,CAACpH,IAAR,CAAawH,IAA1C,EAAgD,KAAKG,OAArD,CAAD,CAArB,GAAuF,IAA1G;;MACA,IAAIkJ,aAAJ,EAAmB;QACf,IAAItI,OAAO,GAAGN,KAAK,CAACI,aAAN,CAAoBE,OAApB,IAA+BN,KAAK,CAACI,aAAN,CAAoBG,OAAjE;;QACA,IAAImI,QAAQ,IAAIpI,OAAhB,EAAyB;UACrB,IAAI,KAAK0I,qBAAL,EAAJ,EAAkC;YAC9B,KAAKzK,UAAL,GAAkB,IAAlB;YACA,KAAK9C,aAAL,GAAqB,EAArB;YACA,KAAKjC,eAAL,CAAqBwC,IAArB,CAA0B,IAA1B;UACH,CAJD,MAKK;YACD,IAAIiN,cAAc,GAAG,KAAKC,oBAAL,CAA0B/J,OAAO,CAACpH,IAAlC,CAArB;YACA,KAAKwG,UAAL,GAAkB,KAAKD,SAAL,CAAe6K,MAAf,CAAsB,CAACxN,GAAD,EAAM6D,CAAN,KAAYA,CAAC,IAAIyJ,cAAvC,CAAlB;YACA,KAAKzP,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;;YACA,IAAIyK,YAAJ,EAAkB;cACd,OAAO,KAAKtN,aAAL,CAAmBsN,YAAnB,CAAP;YACH;UACJ;;UACD,KAAKhO,cAAL,CAAoBiB,IAApB,CAAyB;YAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;YAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;YAA0DM,IAAI,EAAE;UAAhE,CAAzB;QACH,CAfD,MAgBK;UACD,IAAI,KAAK2Q,qBAAL,EAAJ,EAAkC;YAC9B,KAAKzK,UAAL,GAAkBY,OAAO,CAACpH,IAA1B;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0BmD,OAAO,CAACpH,IAAlC;;YACA,IAAIgR,YAAJ,EAAkB;cACd,KAAKtN,aAAL,GAAqB,EAArB;cACA,KAAKA,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;YACH;UACJ,CAPD,MAQK,IAAI,KAAKK,uBAAL,EAAJ,EAAoC;YACrC,IAAI9I,OAAJ,EAAa;cACT,KAAK/B,UAAL,GAAkB,KAAKD,SAAL,IAAkB,EAApC;YACH,CAFD,MAGK;cACD,KAAKC,UAAL,GAAkB,EAAlB;cACA,KAAK9C,aAAL,GAAqB,EAArB;YACH;;YACD,KAAK8C,UAAL,GAAkB,CAAC,GAAG,KAAKD,SAAT,EAAoBa,OAAO,CAACpH,IAA5B,CAAlB;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;;YACA,IAAIyK,YAAJ,EAAkB;cACd,KAAKtN,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;YACH;UACJ;;UACD,KAAKjO,YAAL,CAAkBkB,IAAlB,CAAuB;YAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;YAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;YAA0DM,IAAI,EAAE,KAAhE;YAAuE4B,KAAK,EAAE+F,KAAK,CAACqJ;UAApF,CAAvB;QACH;MACJ,CA3CD,MA4CK;QACD,IAAI,KAAKZ,aAAL,KAAuB,QAA3B,EAAqC;UACjC,IAAIC,QAAJ,EAAc;YACV,KAAKnK,UAAL,GAAkB,IAAlB;YACA,KAAK9C,aAAL,GAAqB,EAArB;YACA,KAAKjC,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;YACA,KAAKvD,cAAL,CAAoBiB,IAApB,CAAyB;cAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;cAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;cAA0DM,IAAI,EAAE;YAAhE,CAAzB;UACH,CALD,MAMK;YACD,KAAKkG,UAAL,GAAkBY,OAAO,CAACpH,IAA1B;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;YACA,KAAKxD,YAAL,CAAkBkB,IAAlB,CAAuB;cAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;cAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;cAA0DM,IAAI,EAAE,KAAhE;cAAuE4B,KAAK,EAAE+F,KAAK,CAACqJ;YAApF,CAAvB;;YACA,IAAIN,YAAJ,EAAkB;cACd,KAAKtN,aAAL,GAAqB,EAArB;cACA,KAAKA,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;YACH;UACJ;QACJ,CAhBD,MAiBK,IAAI,KAAKN,aAAL,KAAuB,UAA3B,EAAuC;UACxC,IAAIC,QAAJ,EAAc;YACV,IAAIO,cAAc,GAAG,KAAKC,oBAAL,CAA0B/J,OAAO,CAACpH,IAAlC,CAArB;YACA,KAAKwG,UAAL,GAAkB,KAAKD,SAAL,CAAe6K,MAAf,CAAsB,CAACxN,GAAD,EAAM6D,CAAN,KAAYA,CAAC,IAAIyJ,cAAvC,CAAlB;YACA,KAAKzP,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;YACA,KAAKvD,cAAL,CAAoBiB,IAApB,CAAyB;cAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;cAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;cAA0DM,IAAI,EAAE;YAAhE,CAAzB;;YACA,IAAI0Q,YAAJ,EAAkB;cACd,OAAO,KAAKtN,aAAL,CAAmBsN,YAAnB,CAAP;YACH;UACJ,CARD,MASK;YACD,KAAKxK,UAAL,GAAkB,KAAKD,SAAL,GAAiB,CAAC,GAAG,KAAKA,SAAT,EAAoBa,OAAO,CAACpH,IAA5B,CAAjB,GAAqD,CAACoH,OAAO,CAACpH,IAAT,CAAvE;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;YACA,KAAKxD,YAAL,CAAkBkB,IAAlB,CAAuB;cAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;cAAsCrI,IAAI,EAAEoH,OAAO,CAACpH,IAApD;cAA0DM,IAAI,EAAE,KAAhE;cAAuE4B,KAAK,EAAE+F,KAAK,CAACqJ;YAApF,CAAvB;;YACA,IAAIN,YAAJ,EAAkB;cACd,KAAKtN,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;YACH;UACJ;QACJ;MACJ;;MACD,KAAKrQ,YAAL,CAAkBb,iBAAlB;IACH;;IACD,KAAKgR,UAAL,GAAkB,KAAlB;EACH;;EACDS,iBAAiB,CAACtJ,KAAD,EAAQ;IACrB,KAAK6I,UAAL,GAAkB,IAAlB;EACH;;EACDU,mBAAmB,CAACvJ,KAAD,EAAQ;IACvB,IAAI,KAAKwJ,WAAT,EAAsB;MAClB,MAAMzR,IAAI,GAAGiI,KAAK,CAACb,OAAN,CAAcpH,IAA3B;;MACA,IAAI,KAAK2B,wBAAL,KAAkC,UAAtC,EAAkD;QAC9C,KAAK+P,oBAAL,GAA4B1R,IAA5B;QACA,KAAK0B,0BAAL,CAAgCuC,IAAhC,CAAqCjE,IAArC;QACA,KAAKiD,mBAAL,CAAyBgB,IAAzB,CAA8B;UAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;UAAsCrI,IAAI,EAAEA;QAA5C,CAA9B;QACA,KAAKyR,WAAL,CAAiBE,IAAjB,CAAsB1J,KAAK,CAACI,aAA5B;QACA,KAAK1H,YAAL,CAAkBZ,aAAlB,CAAgCC,IAAhC;MACH,CAND,MAOK,IAAI,KAAK2B,wBAAL,KAAkC,OAAtC,EAA+C;QAChD,KAAK8E,iCAAL,GAAyC,IAAzC;QACA,IAAIkK,QAAQ,GAAG,KAAKC,UAAL,CAAgB5Q,IAAhB,CAAf;QACA,IAAIgR,YAAY,GAAG,KAAKrJ,OAAL,GAAeG,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwC,KAAKG,OAA7C,CAAD,CAArB,GAA+E,IAAlG;;QACA,IAAI,CAACgJ,QAAL,EAAe;UACX,IAAI,KAAKM,qBAAL,EAAJ,EAAkC;YAC9B,KAAK1K,SAAL,GAAiBvG,IAAjB;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0BjE,IAA1B;UACH,CAHD,MAIK,IAAI,KAAKqR,uBAAL,EAAJ,EAAoC;YACrC,KAAK9K,SAAL,GAAiB,CAACvG,IAAD,CAAjB;YACA,KAAKyB,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;UACH;;UACD,IAAIyK,YAAJ,EAAkB;YACd,KAAKtN,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;UACH;QACJ;;QACD,KAAKS,WAAL,CAAiBE,IAAjB,CAAsB1J,KAAK,CAACI,aAA5B;QACA,KAAKpF,mBAAL,CAAyBgB,IAAzB,CAA8B;UAAEoE,aAAa,EAAEJ,KAAK,CAACI,aAAvB;UAAsCrI,IAAI,EAAEA;QAA5C,CAA9B;MACH;IACJ;EACJ;;EACD4R,sBAAsB,CAAC3J,KAAD,EAAQ;IAC1B,KAAK1B,SAAL,GAAiB,KAAKA,SAAL,IAAkB,EAAnC;IACA,KAAKE,iCAAL,GAAyC,IAAzC;IACA,IAAIzG,IAAI,GAAGiI,KAAK,CAACb,OAAN,CAAcpH,IAAzB;IACA,IAAI2Q,QAAQ,GAAG,KAAKC,UAAL,CAAgB5Q,IAAhB,CAAf;;IACA,IAAI2Q,QAAJ,EAAc;MACV,KAAKkB,sBAAL,CAA4B7R,IAA5B,EAAkC,KAAlC;;MACA,IAAIiI,KAAK,CAACb,OAAN,CAAcJ,MAAlB,EAA0B;QACtB,KAAK8K,oBAAL,CAA0B9R,IAAI,CAACgH,MAA/B,EAAuC,KAAvC;MACH;;MACD,KAAKvF,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;MACA,KAAKvD,cAAL,CAAoBiB,IAApB,CAAyB;QAAEoE,aAAa,EAAEJ,KAAjB;QAAwBjI,IAAI,EAAEA;MAA9B,CAAzB;IACH,CAPD,MAQK;MACD,KAAK6R,sBAAL,CAA4B7R,IAA5B,EAAkC,IAAlC;;MACA,IAAIiI,KAAK,CAACb,OAAN,CAAcJ,MAAlB,EAA0B;QACtB,KAAK8K,oBAAL,CAA0B9R,IAAI,CAACgH,MAA/B,EAAuC,IAAvC;MACH;;MACD,KAAKvF,eAAL,CAAqBwC,IAArB,CAA0B,KAAKsC,SAA/B;MACA,KAAKxD,YAAL,CAAkBkB,IAAlB,CAAuB;QAAEoE,aAAa,EAAEJ,KAAjB;QAAwBjI,IAAI,EAAEA;MAA9B,CAAvB;IACH;;IACD,KAAKW,YAAL,CAAkBb,iBAAlB;EACH;;EACDiS,uBAAuB,CAAC9J,KAAD,EAAQ+J,KAAR,EAAe;IAClC,IAAIxK,IAAI,GAAG,KAAKT,aAAL,IAAsB,KAAK7G,KAAtC;IACA,KAAKsG,UAAL,GAAkBwL,KAAK,IAAIxK,IAAT,GAAgBA,IAAI,CAACyK,KAAL,EAAhB,GAA+B,EAAjD;;IACA,IAAID,KAAJ,EAAW;MACP,IAAIxK,IAAI,IAAIA,IAAI,CAAC5B,MAAjB,EAAyB;QACrB,KAAK,IAAI5F,IAAT,IAAiBwH,IAAjB,EAAuB;UACnB,KAAKqK,sBAAL,CAA4B7R,IAA5B,EAAkC,IAAlC;QACH;MACJ;IACJ,CAND,MAOK;MACD,KAAKwG,UAAL,GAAkB,EAAlB;MACA,KAAK9C,aAAL,GAAqB,EAArB;IACH;;IACD,KAAK+C,iCAAL,GAAyC,IAAzC;IACA,KAAKhF,eAAL,CAAqBwC,IAArB,CAA0B,KAAKuC,UAA/B;IACA,KAAK7F,YAAL,CAAkBb,iBAAlB;IACA,KAAKoD,sBAAL,CAA4Be,IAA5B,CAAiC;MAAEoE,aAAa,EAAEJ,KAAjB;MAAwBiK,OAAO,EAAEF;IAAjC,CAAjC;EACH;;EACDF,oBAAoB,CAAC9R,IAAD,EAAOmS,MAAP,EAAe;IAC/B,IAAInS,IAAI,CAACuH,QAAL,IAAiBvH,IAAI,CAACuH,QAAL,CAAc3B,MAAnC,EAA2C;MACvC,IAAIwM,kBAAkB,GAAG,CAAzB;MACA,IAAIC,oBAAoB,GAAG,KAA3B;MACA,IAAIrB,YAAY,GAAG,KAAKrJ,OAAL,GAAeG,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwC,KAAKG,OAA7C,CAAD,CAArB,GAA+E,IAAlG;;MACA,KAAK,IAAI2K,KAAT,IAAkBtS,IAAI,CAACuH,QAAvB,EAAiC;QAC7B,IAAI,KAAKqJ,UAAL,CAAgB0B,KAAhB,CAAJ,EACIF,kBAAkB,GADtB,KAEK,IAAIE,KAAK,CAACC,eAAV,EACDF,oBAAoB,GAAG,IAAvB;MACP;;MACD,IAAIF,MAAM,IAAIC,kBAAkB,IAAIpS,IAAI,CAACuH,QAAL,CAAc3B,MAAlD,EAA0D;QACtD,KAAKY,UAAL,GAAkB,CAAC,IAAG,KAAKD,SAAL,IAAkB,EAArB,CAAD,EAA0BvG,IAA1B,CAAlB;QACAA,IAAI,CAACuS,eAAL,GAAuB,KAAvB;;QACA,IAAIvB,YAAJ,EAAkB;UACd,KAAKtN,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;QACH;MACJ,CAND,MAOK;QACD,IAAI,CAACmB,MAAL,EAAa;UACT,IAAIjQ,KAAK,GAAG,KAAKiP,oBAAL,CAA0BnR,IAA1B,CAAZ;;UACA,IAAIkC,KAAK,IAAI,CAAb,EAAgB;YACZ,KAAKsE,UAAL,GAAkB,KAAKD,SAAL,CAAe6K,MAAf,CAAsB,CAACxN,GAAD,EAAM6D,CAAN,KAAYA,CAAC,IAAIvF,KAAvC,CAAlB;;YACA,IAAI8O,YAAJ,EAAkB;cACd,OAAO,KAAKtN,aAAL,CAAmBsN,YAAnB,CAAP;YACH;UACJ;QACJ;;QACD,IAAIqB,oBAAoB,IAAID,kBAAkB,GAAG,CAArB,IAA0BA,kBAAkB,IAAIpS,IAAI,CAACuH,QAAL,CAAc3B,MAA1F,EACI5F,IAAI,CAACuS,eAAL,GAAuB,IAAvB,CADJ,KAGIvS,IAAI,CAACuS,eAAL,GAAuB,KAAvB;MACP;IACJ;;IACD,IAAIvL,MAAM,GAAGhH,IAAI,CAACgH,MAAlB;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAK8K,oBAAL,CAA0B9K,MAA1B,EAAkCmL,MAAlC;IACH;EACJ;;EACDN,sBAAsB,CAAC7R,IAAD,EAAOmS,MAAP,EAAe;IACjC,IAAIjQ,KAAK,GAAG,KAAKiP,oBAAL,CAA0BnR,IAA1B,CAAZ;IACA,IAAIgR,YAAY,GAAG,KAAKrJ,OAAL,GAAeG,MAAM,CAACrJ,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwC,KAAKG,OAA7C,CAAD,CAArB,GAA+E,IAAlG;;IACA,IAAIwK,MAAM,IAAIjQ,KAAK,IAAI,CAAC,CAAxB,EAA2B;MACvB,KAAKsE,UAAL,GAAkB,CAAC,IAAG,KAAKD,SAAL,IAAkB,EAArB,CAAD,EAA0BvG,IAA1B,CAAlB;;MACA,IAAIgR,YAAJ,EAAkB;QACd,KAAKtN,aAAL,CAAmBsN,YAAnB,IAAmC,CAAnC;MACH;IACJ,CALD,MAMK,IAAI,CAACmB,MAAD,IAAWjQ,KAAK,GAAG,CAAC,CAAxB,EAA2B;MAC5B,KAAKsE,UAAL,GAAkB,KAAKD,SAAL,CAAe6K,MAAf,CAAsB,CAACxN,GAAD,EAAM6D,CAAN,KAAYA,CAAC,IAAIvF,KAAvC,CAAlB;;MACA,IAAI8O,YAAJ,EAAkB;QACd,OAAO,KAAKtN,aAAL,CAAmBsN,YAAnB,CAAP;MACH;IACJ;;IACDhR,IAAI,CAACuS,eAAL,GAAuB,KAAvB;;IACA,IAAIvS,IAAI,CAACuH,QAAL,IAAiBvH,IAAI,CAACuH,QAAL,CAAc3B,MAAnC,EAA2C;MACvC,KAAK,IAAI0M,KAAT,IAAkBtS,IAAI,CAACuH,QAAvB,EAAiC;QAC7B,KAAKsK,sBAAL,CAA4BS,KAA5B,EAAmCH,MAAnC;MACH;IACJ;EACJ;;EACDvB,UAAU,CAAC5Q,IAAD,EAAO;IACb,IAAIA,IAAI,IAAI,KAAKuG,SAAjB,EAA4B;MACxB,IAAI,KAAKoB,OAAT,EAAkB;QACd,OAAO,KAAKjE,aAAL,CAAmBjF,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwC,KAAKG,OAA7C,CAAnB,MAA8EyB,SAArF;MACH,CAFD,MAGK;QACD,IAAI,KAAK7C,SAAL,YAA0BqB,KAA9B,EACI,OAAO,KAAKuJ,oBAAL,CAA0BnR,IAA1B,IAAkC,CAAC,CAA1C,CADJ,KAGI,OAAO,KAAKwS,MAAL,CAAYxS,IAAZ,EAAkB,KAAKuG,SAAvB,CAAP;MACP;IACJ;;IACD,OAAO,KAAP;EACH;;EACD4K,oBAAoB,CAACnR,IAAD,EAAO;IACvB,IAAIkC,KAAK,GAAG,CAAC,CAAb;;IACA,IAAI,KAAKqE,SAAL,IAAkB,KAAKA,SAAL,CAAeX,MAArC,EAA6C;MACzC,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKlB,SAAL,CAAeX,MAAnC,EAA2C6B,CAAC,EAA5C,EAAgD;QAC5C,IAAI,KAAK+K,MAAL,CAAYxS,IAAZ,EAAkB,KAAKuG,SAAL,CAAekB,CAAf,CAAlB,CAAJ,EAA0C;UACtCvF,KAAK,GAAGuF,CAAR;UACA;QACH;MACJ;IACJ;;IACD,OAAOvF,KAAP;EACH;;EACD+O,qBAAqB,GAAG;IACpB,OAAO,KAAKP,aAAL,KAAuB,QAA9B;EACH;;EACDW,uBAAuB,GAAG;IACtB,OAAO,KAAKX,aAAL,KAAuB,UAA9B;EACH;;EACD8B,MAAM,CAAC1J,KAAD,EAAQC,KAAR,EAAe;IACjB,OAAO,KAAKnH,kBAAL,KAA4B,QAA5B,GAAwCkH,KAAK,KAAKC,KAAlD,GAA2DtK,WAAW,CAAC+T,MAAZ,CAAmB1J,KAAK,CAACtB,IAAzB,EAA+BuB,KAAK,CAACvB,IAArC,EAA2C,KAAKG,OAAhD,CAAlE;EACH;;EACDyJ,MAAM,CAAClR,KAAD,EAAQoI,KAAR,EAAemK,SAAf,EAA0B;IAC5B,IAAI,KAAKC,aAAT,EAAwB;MACpBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;IACH;;IACD,IAAI,CAAC,KAAKE,aAAL,CAAmB1S,KAAnB,CAAL,EAAgC;MAC5B,KAAKkC,OAAL,CAAakG,KAAb,IAAsB;QAAEpI,KAAK,EAAEA,KAAT;QAAgBuS,SAAS,EAAEA;MAA3B,CAAtB;IACH,CAFD,MAGK,IAAI,KAAKrQ,OAAL,CAAakG,KAAb,CAAJ,EAAyB;MAC1B,OAAO,KAAKlG,OAAL,CAAakG,KAAb,CAAP;IACH;;IACD,KAAKoK,aAAL,GAAqBG,UAAU,CAAC,MAAM;MAClC,KAAK3M,OAAL;;MACA,KAAKwM,aAAL,GAAqB,IAArB;IACH,CAH8B,EAG5B,KAAKrQ,WAHuB,CAA/B;EAIH;;EACDyQ,YAAY,CAAC5S,KAAD,EAAQuS,SAAR,EAAmB;IAC3B,KAAKrB,MAAL,CAAYlR,KAAZ,EAAmB,QAAnB,EAA6BuS,SAA7B;EACH;;EACDG,aAAa,CAACxB,MAAD,EAAS;IAClB,IAAIA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAKhI,SAAlC,EAA6C;MACzC,IAAK,OAAOgI,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,CAAC2B,IAAP,GAAcnN,MAAd,IAAwB,CAAvD,IAA8DwL,MAAM,YAAYxJ,KAAlB,IAA2BwJ,MAAM,CAACxL,MAAP,IAAiB,CAA9G,EACI,OAAO,IAAP,CADJ,KAGI,OAAO,KAAP;IACP;;IACD,OAAO,IAAP;EACH;;EACDM,OAAO,GAAG;IACN,IAAI,KAAKrF,IAAT,EAAe;MACX,KAAK8B,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB;IACH,CAFD,MAGK;MACD,IAAI,CAAC,KAAKhE,KAAV,EAAiB;QACb;MACH;;MACD,IAAI,CAAC,KAAK+F,SAAL,EAAL,EAAuB;QACnB,KAAKc,aAAL,GAAqB,IAArB;;QACA,IAAI,KAAKH,SAAT,EAAoB;UAChB,KAAKjB,YAAL,GAAoB,KAAKzF,KAAL,GAAa,KAAKA,KAAL,CAAW0F,MAAxB,GAAiC,CAArD;QACH;MACJ,CALD,MAMK;QACD,IAAIoN,uBAAJ;;QACA,IAAI,KAAK5Q,OAAL,CAAa,QAAb,CAAJ,EAA4B;UACxB,IAAI,CAAC,KAAKiO,OAAN,IAAiB,CAAC,KAAK4C,kBAA3B,EACI,MAAM,IAAIC,KAAJ,CAAU,gFAAV,CAAN,CADJ,KAGIF,uBAAuB,GAAG,KAAKC,kBAAL,IAA2B,KAAK5C,OAA1D;QACP;;QACD,KAAKtJ,aAAL,GAAqB,EAArB;QACA,MAAMoM,YAAY,GAAG,KAAK7Q,UAAL,KAAoB,QAAzC;QACA,IAAI8Q,cAAc,GAAG,KAArB;;QACA,KAAK,IAAIpT,IAAT,IAAiB,KAAKE,KAAtB,EAA6B;UACzB,IAAImT,QAAQ,GAAGtJ,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBhK,IAAlB,CAAf;UACA,IAAIsT,UAAU,GAAG,IAAjB;UACA,IAAIC,WAAW,GAAG,KAAlB;UACA,IAAIC,iBAAJ;;UACA,KAAK,IAAIC,IAAT,IAAiB,KAAKrR,OAAtB,EAA+B;YAC3B,IAAI,KAAKA,OAAL,CAAasR,cAAb,CAA4BD,IAA5B,KAAqCA,IAAI,KAAK,QAAlD,EAA4D;cACxD,IAAIE,UAAU,GAAG,KAAKvR,OAAL,CAAaqR,IAAb,CAAjB;cACA,IAAIG,WAAW,GAAGH,IAAlB;cACA,IAAII,WAAW,GAAGF,UAAU,CAACzT,KAA7B;cACA,IAAI4T,eAAe,GAAGH,UAAU,CAAClB,SAAX,IAAwB,YAA9C;cACA,IAAIsB,gBAAgB,GAAG,KAAKnT,aAAL,CAAmBwB,OAAnB,CAA2B0R,eAA3B,CAAvB;cACAN,iBAAiB,GAAG;gBAAEI,WAAF;gBAAeC,WAAf;gBAA4BE,gBAA5B;gBAA8CZ;cAA9C,CAApB;;cACA,IAAKA,YAAY,IAAI,EAAE,KAAKa,iBAAL,CAAuBX,QAAvB,EAAiCG,iBAAjC,KAAuD,KAAKS,eAAL,CAAqBZ,QAArB,EAA+BG,iBAA/B,CAAzD,CAAjB,IACC,CAACL,YAAD,IAAiB,EAAE,KAAKc,eAAL,CAAqBZ,QAArB,EAA+BG,iBAA/B,KAAqD,KAAKQ,iBAAL,CAAuBX,QAAvB,EAAiCG,iBAAjC,CAAvD,CADtB,EACoI;gBAChIF,UAAU,GAAG,KAAb;cACH;;cACD,IAAI,CAACA,UAAL,EAAiB;gBACb;cACH;YACJ;UACJ;;UACD,IAAI,KAAKlR,OAAL,CAAa,QAAb,KAA0B,CAACmR,WAA3B,IAA0CP,uBAA9C,EAAuE;YACnE,KAAK,IAAIkB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlB,uBAAuB,CAACpN,MAA5C,EAAoDsO,CAAC,EAArD,EAAyD;cACrD,IAAIC,iBAAiB,GAAGpK,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBqJ,QAAlB,CAAxB;cACA,IAAIO,WAAW,GAAGZ,uBAAuB,CAACkB,CAAD,CAAvB,CAA2B5L,KAA3B,IAAoC0K,uBAAuB,CAACkB,CAAD,CAA7E;cACA,IAAIL,WAAW,GAAG,KAAKzR,OAAL,CAAa,QAAb,EAAuBlC,KAAzC;cACA,IAAI6T,gBAAgB,GAAG,KAAKnT,aAAL,CAAmBwB,OAAnB,CAA2B,KAAKA,OAAL,CAAa,QAAb,EAAuBqQ,SAAlD,CAAvB;cACAe,iBAAiB,GAAG;gBAAEI,WAAF;gBAAeC,WAAf;gBAA4BE,gBAA5B;gBAA8CZ;cAA9C,CAApB;;cACA,IAAKA,YAAY,KAAK,KAAKa,iBAAL,CAAuBG,iBAAvB,EAA0CX,iBAA1C,KAAgE,KAAKS,eAAL,CAAqBE,iBAArB,EAAwCX,iBAAxC,CAArE,CAAb,IACC,CAACL,YAAD,KAAkB,KAAKc,eAAL,CAAqBE,iBAArB,EAAwCX,iBAAxC,KAA8D,KAAKQ,iBAAL,CAAuBG,iBAAvB,EAA0CX,iBAA1C,CAAhF,CADL,EACqJ;gBACjJD,WAAW,GAAG,IAAd;gBACAF,QAAQ,GAAGc,iBAAX;cACH;YACJ;UACJ;;UACD,IAAIC,OAAO,GAAGd,UAAd;;UACA,IAAI,KAAKlR,OAAL,CAAa,QAAb,CAAJ,EAA4B;YACxBgS,OAAO,GAAGd,UAAU,IAAIC,WAAxB;UACH;;UACD,IAAIa,OAAJ,EAAa;YACT,KAAKrN,aAAL,CAAmBO,IAAnB,CAAwB+L,QAAxB;UACH;;UACDD,cAAc,GAAGA,cAAc,IAAI,CAACE,UAAnB,IAAiCC,WAAjC,IAAiDD,UAAU,IAAI,KAAKvM,aAAL,CAAmBnB,MAAnB,GAA4B,CAA3F,IAAkG,CAAC2N,WAAD,IAAgB,KAAKxM,aAAL,CAAmBnB,MAAnB,KAA8B,CAAjK;QACH;;QACD,IAAI,CAACwN,cAAL,EAAqB;UACjB,KAAKrM,aAAL,GAAqB,IAArB;QACH;;QACD,IAAI,KAAKH,SAAT,EAAoB;UAChB,KAAKjB,YAAL,GAAoB,KAAKoB,aAAL,GAAqB,KAAKA,aAAL,CAAmBnB,MAAxC,GAAiD,KAAK1F,KAAL,GAAa,KAAKA,KAAL,CAAW0F,MAAxB,GAAiC,CAAtG;QACH;MACJ;IACJ;;IACD,KAAK7E,KAAL,GAAa,CAAb;IACA,MAAMsT,aAAa,GAAG,KAAKtN,aAAL,IAAsB,KAAK7G,KAAjD;IACA,KAAKqC,QAAL,CAAc0B,IAAd,CAAmB;MACf7B,OAAO,EAAE,KAAKA,OADC;MAEfiS,aAAa,EAAEA;IAFA,CAAnB;IAIA,KAAK1T,YAAL,CAAkBV,UAAlB,CAA6BoU,aAA7B;IACA,KAAKlO,qBAAL;;IACA,IAAI,KAAK+B,UAAT,EAAqB;MACjB,KAAKC,cAAL;IACH;EACJ;;EACD6L,iBAAiB,CAAChU,IAAD,EAAOwT,iBAAP,EAA0B;IACvC,IAAIxT,IAAJ,EAAU;MACN,IAAIsU,OAAO,GAAG,KAAd;;MACA,IAAItU,IAAI,CAACuH,QAAT,EAAmB;QACf,IAAIgN,UAAU,GAAG,CAAC,GAAGvU,IAAI,CAACuH,QAAT,CAAjB;QACAvH,IAAI,CAACuH,QAAL,GAAgB,EAAhB;;QACA,KAAK,IAAIiN,SAAT,IAAsBD,UAAtB,EAAkC;UAC9B,IAAIE,aAAa,GAAG1K,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBwK,SAAlB,CAApB;;UACA,IAAI,KAAKP,eAAL,CAAqBQ,aAArB,EAAoCjB,iBAApC,CAAJ,EAA4D;YACxDc,OAAO,GAAG,IAAV;YACAtU,IAAI,CAACuH,QAAL,CAAcD,IAAd,CAAmBmN,aAAnB;UACH;QACJ;MACJ;;MACD,IAAIH,OAAJ,EAAa;QACT,OAAO,IAAP;MACH;IACJ;EACJ;;EACDL,eAAe,CAACjU,IAAD,EAAO;IAAE4T,WAAF;IAAeC,WAAf;IAA4BE,gBAA5B;IAA8CZ;EAA9C,CAAP,EAAqE;IAChF,IAAImB,OAAO,GAAG,KAAd;IACA,IAAII,cAAc,GAAGjW,WAAW,CAACsJ,gBAAZ,CAA6B/H,IAAI,CAACwH,IAAlC,EAAwCoM,WAAxC,CAArB;;IACA,IAAIG,gBAAgB,CAACW,cAAD,EAAiBb,WAAjB,EAA8B,KAAKc,YAAnC,CAApB,EAAsE;MAClEL,OAAO,GAAG,IAAV;IACH;;IACD,IAAI,CAACA,OAAD,IAAanB,YAAY,IAAI,CAAC,KAAKyB,UAAL,CAAgB5U,IAAhB,CAAlC,EAA0D;MACtDsU,OAAO,GAAG,KAAKN,iBAAL,CAAuBhU,IAAvB,EAA6B;QAAE4T,WAAF;QAAeC,WAAf;QAA4BE,gBAA5B;QAA8CZ;MAA9C,CAA7B,KAA8FmB,OAAxG;IACH;;IACD,OAAOA,OAAP;EACH;;EACDM,UAAU,CAAC5U,IAAD,EAAO;IACb,OAAOA,IAAI,CAAC6U,IAAL,KAAc,KAAd,GAAsB,KAAtB,GAA8B,EAAE7U,IAAI,CAACuH,QAAL,IAAiBvH,IAAI,CAACuH,QAAL,CAAc3B,MAAjC,CAArC;EACH;;EACDK,SAAS,GAAG;IACR,IAAI6O,KAAK,GAAG,IAAZ;;IACA,KAAK,IAAIrB,IAAT,IAAiB,KAAKrR,OAAtB,EAA+B;MAC3B,IAAI,KAAKA,OAAL,CAAasR,cAAb,CAA4BD,IAA5B,CAAJ,EAAuC;QACnCqB,KAAK,GAAG,KAAR;QACA;MACH;IACJ;;IACD,OAAO,CAACA,KAAR;EACH;;EACDC,KAAK,GAAG;IACJ,KAAK3O,UAAL,GAAkB,IAAlB;IACA,KAAK3C,UAAL,GAAkB,CAAlB;IACA,KAAK6C,cAAL,GAAsB,IAAtB;IACA,KAAK3F,YAAL,CAAkBhB,MAAlB,CAAyB,IAAzB;IACA,KAAKoH,aAAL,GAAqB,IAArB;IACA,KAAK3E,OAAL,GAAe,EAAf;IACA,KAAKrB,KAAL,GAAa,CAAb;;IACA,IAAI,KAAKF,IAAT,EAAe;MACX,KAAK8B,UAAL,CAAgBsB,IAAhB,CAAqB,KAAKC,sBAAL,EAArB;IACH,CAFD,MAGK;MACD,KAAKyB,YAAL,GAAqB,KAAKpC,MAAL,GAAc,KAAKA,MAAL,CAAYqC,MAA1B,GAAmC,CAAxD;IACH;EACJ;;EACDoP,iBAAiB,CAACC,IAAD,EAAOzN,IAAP,EAAac,KAAb,EAAoB;IACjC,KAAK4M,WAAL,GAAmBD,IAAnB;IACA,KAAKE,eAAL,GAAuB3N,IAAvB;IACA,KAAK4N,gBAAL,GAAwB9M,KAAxB;IACA,KAAK+M,wBAAL;EACH;;EACDC,kBAAkB,GAAG;IACjB,OAAQ,KAAKJ,WAAL,IAAoB/W,UAAU,CAACoX,IAAX,CAAgB,KAAKL,WAArB,EAAkC,sBAAlC,EAA0DtP,MAA1D,KAAqE,CAAjG;EACH;;EACDyP,wBAAwB,GAAG;IACvB,IAAI,CAAC,KAAKG,oBAAV,EAAgC;MAC5B,KAAKA,oBAAL,GAA6BvN,KAAD,IAAW;QACnC,IAAI,KAAKiN,WAAL,IAAoB,CAAC,KAAKO,gBAA1B,IAA8C,KAAKH,kBAAL,EAAlD,EAA6E;UACzEnX,UAAU,CAAC4P,WAAX,CAAuB,KAAKmH,WAA5B,EAAyC,gBAAzC;UACA,KAAKA,WAAL,GAAmB,IAAnB;UACA,KAAK9R,cAAL,CAAoBa,IAApB,CAAyB;YAAEqE,KAAK,EAAE,KAAK8M,gBAAd;YAAgC5N,IAAI,EAAE,KAAK2N;UAA3C,CAAzB;UACA,KAAKC,gBAAL,GAAwB,IAAxB;UACA,KAAKD,eAAL,GAAuB,IAAvB;UACA,KAAKO,0BAAL;QACH;;QACD,KAAKD,gBAAL,GAAwB,KAAxB;MACH,CAVD;;MAWAE,QAAQ,CAACC,gBAAT,CAA0B,OAA1B,EAAmC,KAAKJ,oBAAxC;IACH;EACJ;;EACDE,0BAA0B,GAAG;IACzB,IAAI,KAAKF,oBAAT,EAA+B;MAC3BG,QAAQ,CAACE,mBAAT,CAA6B,OAA7B,EAAsC,KAAKL,oBAA3C;MACA,KAAKA,oBAAL,GAA4B,IAA5B;IACH;EACJ;;EACDM,WAAW,GAAG;IACV,KAAKJ,0BAAL;IACA,KAAKR,WAAL,GAAmB,IAAnB;IACA,KAAKE,gBAAL,GAAwB,IAAxB;IACA,KAAKD,eAAL,GAAuB,IAAvB;IACA,KAAKhR,WAAL,GAAmB,IAAnB;EACH;;AAppCW;;AAspChB5D,SAAS,CAACH,IAAV;EAAA,iBAAsGG,SAAtG,EA3pCmGpD,EA2pCnG,mBAAiIA,EAAE,CAAC4Y,UAApI,GA3pCmG5Y,EA2pCnG,mBAA2JA,EAAE,CAAC6Y,iBAA9J,GA3pCmG7Y,EA2pCnG,mBAA4LA,EAAE,CAAC8Y,MAA/L,GA3pCmG9Y,EA2pCnG,mBAAkN2B,gBAAlN,GA3pCmG3B,EA2pCnG,mBAA+OmB,EAAE,CAAC4X,aAAlP;AAAA;;AACA3V,SAAS,CAAC4V,IAAV,kBA5pCmGhZ,EA4pCnG;EAAA,MAA0FoD,SAA1F;EAAA;EAAA;IAAA;MA5pCmGpD,EA4pCnG,0BAAqtFoB,aAArtF;IAAA;;IAAA;MAAA;;MA5pCmGpB,EA4pCnG,qBA5pCmGA,EA4pCnG;IAAA;EAAA;EAAA;IAAA;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG;IAAA;;IAAA;MAAA;;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;MA5pCmGA,EA4pCnG,qBA5pCmGA,EA4pCnG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WA5pCmGA,EA4pCnG,oBAA+oF,CAAC2B,gBAAD,CAA/oF,GA5pCmG3B,EA4pCnG;EAAA;EAAA;EAAA;EAAA;IAAA;MA5pCmGA,EA6pC3F,+BADR;MA5pCmGA,EAoqCvF,wDARZ;MA5pCmGA,EAyqCvF,wDAbZ;MA5pCmGA,EA4qCvF,yEAhBZ;MA5pCmGA,EAirCvF,yDArBZ;MA5pCmGA,EA8rCvF,wDAlCZ;MA5pCmGA,EAmsCvF,yEAvCZ;MA5pCmGA,EAusCvF,wDA3CZ;MA5pCmGA,EA2sCvF,wDA/CZ;MA5pCmGA,EA6sCvF,6DAjDZ;MA5pCmGA,EA8sCvF,6DAlDZ;MA5pCmGA,EA+sC3F,eAnDR;IAAA;;IAAA;MA5pCmGA,EA6pCzD,2BAD1C;MA5pCmGA,EA6pC3E,6CA7pC2EA,EA6pC3E,4PADxB;MA5pCmGA,EAoqCrD,aAR9C;MA5pCmGA,EAoqCrD,kDAR9C;MA5pCmGA,EAyqCjF,aAblB;MA5pCmGA,EAyqCjF,wCAblB;MA5pCmGA,EA6qCF,aAjBjG;MA5pCmGA,EA6qCF,0GAjBjG;MA5pCmGA,EAirCrD,aArB9C;MA5pCmGA,EAirCrD,oCArB9C;MA5pCmGA,EA8rC1C,aAlCzD;MA5pCmGA,EA8rC1C,mCAlCzD;MA5pCmGA,EAosCF,aAxCjG;MA5pCmGA,EAosCF,6GAxCjG;MA5pCmGA,EAusCjF,aA3ClB;MA5pCmGA,EAusCjF,wCA3ClB;MA5pCmGA,EA2sCd,aA/CrF;MA5pCmGA,EA2sCd,yCA/CrF;MA5pCmGA,EA6sCF,aAjDjG;MA5pCmGA,EA6sCF,2CAjDjG;MA5pCmGA,EA8sCA,aAlDnG;MA5pCmGA,EA8sCA,2CAlDnG;IAAA;EAAA;EAAA;IAAA,QAoD2pGa,EAAE,CAACoY,OApD9pG,EAoDgyGpY,EAAE,CAACqY,IApDnyG,EAoD26GrY,EAAE,CAACsY,gBApD96G,EAoDynHtY,EAAE,CAACuY,OApD5nH,EAoDqvHnY,EAAE,CAACoY,SApDxvH,EAoDmuIC,gBApDnuI,EAoDi5IC,MApDj5I;EAAA;EAAA;EAAA;AAAA;;AAqDA;EAAA,mDAjtCmGvZ,EAitCnG,mBAA2FoD,SAA3F,EAAkH,CAAC;IACvGD,IAAI,EAAEhD,SADiG;IAEvGqZ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAZ;MAA2BnS,QAAQ,EAAG;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KApDmB;MAoDZoS,SAAS,EAAE,CAAC/X,gBAAD,CApDC;MAoDmBgY,aAAa,EAAEvZ,iBAAiB,CAACwZ,IApDpD;MAoD0DC,IAAI,EAAE;QAC3D,SAAS;MADkD,CApDhE;MAsDIC,MAAM,EAAE,CAAC,2iGAAD;IAtDZ,CAAD;EAFiG,CAAD,CAAlH,EAyD4B,YAAY;IAAE,OAAO,CAAC;MAAE3W,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAD,EAA0B;MAAEzV,IAAI,EAAEnD,EAAE,CAAC6Y;IAAX,CAA1B,EAA0D;MAAE1V,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA1D,EAA+E;MAAE3V,IAAI,EAAExB;IAAR,CAA/E,EAA2G;MAAEwB,IAAI,EAAEhC,EAAE,CAAC4X;IAAX,CAA3G,CAAP;EAAgJ,CAzD1L,EAyD4M;IAAE7F,OAAO,EAAE,CAAC;MACxM/P,IAAI,EAAE9C;IADkM,CAAD,CAAX;IAE5L+N,KAAK,EAAE,CAAC;MACRjL,IAAI,EAAE9C;IADE,CAAD,CAFqL;IAI5L0Z,UAAU,EAAE,CAAC;MACb5W,IAAI,EAAE9C;IADO,CAAD,CAJgL;IAM5L2Z,UAAU,EAAE,CAAC;MACb7W,IAAI,EAAE9C;IADO,CAAD,CANgL;IAQ5L4Z,eAAe,EAAE,CAAC;MAClB9W,IAAI,EAAE9C;IADY,CAAD,CAR2K;IAU5L6Z,UAAU,EAAE,CAAC;MACb/W,IAAI,EAAE9C;IADO,CAAD,CAVgL;IAY5LqD,IAAI,EAAE,CAAC;MACPP,IAAI,EAAE9C;IADC,CAAD,CAZsL;IAc5LsD,cAAc,EAAE,CAAC;MACjBR,IAAI,EAAE9C;IADW,CAAD,CAd4K;IAgB5LoJ,SAAS,EAAE,CAAC;MACZtG,IAAI,EAAE9C;IADM,CAAD,CAhBiL;IAkB5LkK,IAAI,EAAE,CAAC;MACPpH,IAAI,EAAE9C;IADC,CAAD,CAlBsL;IAoB5LuD,KAAK,EAAE,CAAC;MACRT,IAAI,EAAE9C;IADE,CAAD,CApBqL;IAsB5LwD,SAAS,EAAE,CAAC;MACZV,IAAI,EAAE9C;IADM,CAAD,CAtBiL;IAwB5L8Z,kBAAkB,EAAE,CAAC;MACrBhX,IAAI,EAAE9C;IADe,CAAD,CAxBwK;IA0B5LyD,mBAAmB,EAAE,CAAC;MACtBX,IAAI,EAAE9C;IADgB,CAAD,CA1BuK;IA4B5L0D,iBAAiB,EAAE,CAAC;MACpBZ,IAAI,EAAE9C;IADc,CAAD,CA5ByK;IA8B5L+Z,yBAAyB,EAAE,CAAC;MAC5BjX,IAAI,EAAE9C;IADsB,CAAD,CA9BiK;IAgC5L2D,yBAAyB,EAAE,CAAC;MAC5Bb,IAAI,EAAE9C;IADsB,CAAD,CAhCiK;IAkC5Lga,qBAAqB,EAAE,CAAC;MACxBlX,IAAI,EAAE9C;IADkB,CAAD,CAlCqK;IAoC5Lia,sBAAsB,EAAE,CAAC;MACzBnX,IAAI,EAAE9C;IADmB,CAAD,CApCoK;IAsC5L4D,iBAAiB,EAAE,CAAC;MACpBd,IAAI,EAAE9C;IADc,CAAD,CAtCyK;IAwC5L6D,aAAa,EAAE,CAAC;MAChBf,IAAI,EAAE9C;IADU,CAAD,CAxC6K;IA0C5L8D,gBAAgB,EAAE,CAAC;MACnBhB,IAAI,EAAE9C;IADa,CAAD,CA1C0K;IA4C5L+D,QAAQ,EAAE,CAAC;MACXjB,IAAI,EAAE9C;IADK,CAAD,CA5CkL;IA8C5LgE,eAAe,EAAE,CAAC;MAClBlB,IAAI,EAAE9C;IADY,CAAD,CA9C2K;IAgD5LoL,UAAU,EAAE,CAAC;MACbtI,IAAI,EAAE9C;IADO,CAAD,CAhDgL;IAkD5LkT,aAAa,EAAE,CAAC;MAChBpQ,IAAI,EAAE9C;IADU,CAAD,CAlD6K;IAoD5LiE,eAAe,EAAE,CAAC;MAClBnB,IAAI,EAAE7C;IADY,CAAD,CApD2K;IAsD5LiU,oBAAoB,EAAE,CAAC;MACvBpR,IAAI,EAAE9C;IADiB,CAAD,CAtDsK;IAwD5LkE,0BAA0B,EAAE,CAAC;MAC7BpB,IAAI,EAAE7C;IADuB,CAAD,CAxDgK;IA0D5LkE,wBAAwB,EAAE,CAAC;MAC3BrB,IAAI,EAAE9C;IADqB,CAAD,CA1DkK;IA4D5LmK,OAAO,EAAE,CAAC;MACVrH,IAAI,EAAE9C;IADI,CAAD,CA5DmL;IA8D5LuT,gBAAgB,EAAE,CAAC;MACnBzQ,IAAI,EAAE9C;IADa,CAAD,CA9D0K;IAgE5LoE,kBAAkB,EAAE,CAAC;MACrBtB,IAAI,EAAE9C;IADe,CAAD,CAhEwK;IAkE5Lka,QAAQ,EAAE,CAAC;MACXpX,IAAI,EAAE9C;IADK,CAAD,CAlEkL;IAoE5Lma,OAAO,EAAE,CAAC;MACVrX,IAAI,EAAE9C;IADI,CAAD,CApEmL;IAsE5LqE,WAAW,EAAE,CAAC;MACdvB,IAAI,EAAE9C;IADQ,CAAD,CAtE+K;IAwE5LsE,UAAU,EAAE,CAAC;MACbxB,IAAI,EAAE9C;IADO,CAAD,CAxEgL;IA0E5L0K,UAAU,EAAE,CAAC;MACb5H,IAAI,EAAE9C;IADO,CAAD,CA1EgL;IA4E5Loa,YAAY,EAAE,CAAC;MACftX,IAAI,EAAE9C;IADS,CAAD,CA5E8K;IA8E5LwG,aAAa,EAAE,CAAC;MAChB1D,IAAI,EAAE9C;IADU,CAAD,CA9E6K;IAgF5Lqa,qBAAqB,EAAE,CAAC;MACxBvX,IAAI,EAAE9C;IADkB,CAAD,CAhFqK;IAkF5Lsa,oBAAoB,EAAE,CAAC;MACvBxX,IAAI,EAAE9C;IADiB,CAAD,CAlFsK;IAoF5LuE,kBAAkB,EAAE,CAAC;MACrBzB,IAAI,EAAE9C;IADe,CAAD,CApFwK;IAsF5Lua,WAAW,EAAE,CAAC;MACdzX,IAAI,EAAE9C;IADQ,CAAD,CAtF+K;IAwF5Lwa,aAAa,EAAE,CAAC;MAChB1X,IAAI,EAAE9C;IADU,CAAD,CAxF6K;IA0F5Lya,gBAAgB,EAAE,CAAC;MACnB3X,IAAI,EAAE9C;IADa,CAAD,CA1F0K;IA4F5LwE,gBAAgB,EAAE,CAAC;MACnB1B,IAAI,EAAE9C;IADa,CAAD,CA5F0K;IA8F5L2R,kBAAkB,EAAE,CAAC;MACrB7O,IAAI,EAAE9C;IADe,CAAD,CA9FwK;IAgG5LiU,WAAW,EAAE,CAAC;MACdnR,IAAI,EAAE9C;IADQ,CAAD,CAhG+K;IAkG5LyE,UAAU,EAAE,CAAC;MACb3B,IAAI,EAAE9C;IADO,CAAD,CAlGgL;IAoG5L4E,OAAO,EAAE,CAAC;MACV9B,IAAI,EAAE9C;IADI,CAAD,CApGmL;IAsG5LyV,kBAAkB,EAAE,CAAC;MACrB3S,IAAI,EAAE9C;IADe,CAAD,CAtGwK;IAwG5L6E,WAAW,EAAE,CAAC;MACd/B,IAAI,EAAE9C;IADQ,CAAD,CAxG+K;IA0G5L8E,UAAU,EAAE,CAAC;MACbhC,IAAI,EAAE9C;IADO,CAAD,CA1GgL;IA4G5LmX,YAAY,EAAE,CAAC;MACfrU,IAAI,EAAE9C;IADS,CAAD,CA5G8K;IA8G5L+E,QAAQ,EAAE,CAAC;MACXjC,IAAI,EAAE7C;IADK,CAAD,CA9GkL;IAgH5L+E,YAAY,EAAE,CAAC;MACflC,IAAI,EAAE7C;IADS,CAAD,CAhH8K;IAkH5LgF,cAAc,EAAE,CAAC;MACjBnC,IAAI,EAAE7C;IADW,CAAD,CAlH4K;IAoH5LiF,MAAM,EAAE,CAAC;MACTpC,IAAI,EAAE7C;IADG,CAAD,CApHoL;IAsH5LkC,MAAM,EAAE,CAAC;MACTW,IAAI,EAAE7C;IADG,CAAD,CAtHoL;IAwH5LkF,UAAU,EAAE,CAAC;MACbrC,IAAI,EAAE7C;IADO,CAAD,CAxHgL;IA0H5LmF,YAAY,EAAE,CAAC;MACftC,IAAI,EAAE7C;IADS,CAAD,CA1H8K;IA4H5LoF,WAAW,EAAE,CAAC;MACdvC,IAAI,EAAE7C;IADQ,CAAD,CA5H+K;IA8H5LqF,YAAY,EAAE,CAAC;MACfxC,IAAI,EAAE7C;IADS,CAAD,CA9H8K;IAgI5LsF,YAAY,EAAE,CAAC;MACfzC,IAAI,EAAE7C;IADS,CAAD,CAhI8K;IAkI5LuF,cAAc,EAAE,CAAC;MACjB1C,IAAI,EAAE7C;IADW,CAAD,CAlI4K;IAoI5LwF,mBAAmB,EAAE,CAAC;MACtB3C,IAAI,EAAE7C;IADgB,CAAD,CApIuK;IAsI5LyF,sBAAsB,EAAE,CAAC;MACzB5C,IAAI,EAAE7C;IADmB,CAAD,CAtIoK;IAwI5L0F,UAAU,EAAE,CAAC;MACb7C,IAAI,EAAE7C;IADO,CAAD,CAxIgL;IA0I5L2F,cAAc,EAAE,CAAC;MACjB9C,IAAI,EAAE7C;IADW,CAAD,CA1I4K;IA4I5L4F,YAAY,EAAE,CAAC;MACf/C,IAAI,EAAE7C;IADS,CAAD,CA5I8K;IA8I5LqN,kBAAkB,EAAE,CAAC;MACrBxK,IAAI,EAAE5C,SADe;MAErBiZ,IAAI,EAAE,CAAC,WAAD;IAFe,CAAD,CA9IwK;IAiJ5LrL,qBAAqB,EAAE,CAAC;MACxBhL,IAAI,EAAE5C,SADkB;MAExBiZ,IAAI,EAAE,CAAC,cAAD;IAFkB,CAAD,CAjJqK;IAoJ5LjI,2BAA2B,EAAE,CAAC;MAC9BpO,IAAI,EAAE5C,SADwB;MAE9BiZ,IAAI,EAAE,CAAC,oBAAD;IAFwB,CAAD,CApJ+J;IAuJ5L9H,6BAA6B,EAAE,CAAC;MAChCvO,IAAI,EAAE5C,SAD0B;MAEhCiZ,IAAI,EAAE,CAAC,sBAAD;IAF0B,CAAD,CAvJ6J;IA0J5L/I,cAAc,EAAE,CAAC;MACjBtN,IAAI,EAAE5C,SADW;MAEjBiZ,IAAI,EAAE,CAAC,OAAD;IAFW,CAAD,CA1J4K;IA6J5LtM,mBAAmB,EAAE,CAAC;MACtB/J,IAAI,EAAE5C,SADgB;MAEtBiZ,IAAI,EAAE,CAAC,gBAAD;IAFgB,CAAD,CA7JuK;IAgK5LrM,yBAAyB,EAAE,CAAC;MAC5BhK,IAAI,EAAE5C,SADsB;MAE5BiZ,IAAI,EAAE,CAAC,sBAAD;IAFsB,CAAD,CAhKiK;IAmK5LtS,SAAS,EAAE,CAAC;MACZ/D,IAAI,EAAE3C,eADM;MAEZgZ,IAAI,EAAE,CAACpY,aAAD;IAFM,CAAD,CAnKiL;IAsK5LoF,gBAAgB,EAAE,CAAC;MACnBrD,IAAI,EAAE9C;IADa,CAAD,CAtK0K;IAwK5L0C,KAAK,EAAE,CAAC;MACRI,IAAI,EAAE9C;IADE,CAAD,CAxKqL;IA0K5LmI,YAAY,EAAE,CAAC;MACfrF,IAAI,EAAE9C;IADS,CAAD,CA1K8K;IA4K5LqI,SAAS,EAAE,CAAC;MACZvF,IAAI,EAAE9C;IADM,CAAD,CA5KiL;IA8K5L6I,SAAS,EAAE,CAAC;MACZ/F,IAAI,EAAE9C;IADM,CAAD,CA9KiL;IAgL5LuI,aAAa,EAAE,CAAC;MAChBzF,IAAI,EAAE9C;IADU,CAAD,CAhL6K;IAkL5L+I,SAAS,EAAE,CAAC;MACZjG,IAAI,EAAE9C;IADM,CAAD;EAlLiL,CAzD5M;AAAA;;AA8OA,MAAMkZ,MAAN,CAAa;EACT3X,WAAW,CAACmZ,EAAD,EAAKC,gBAAL,EAAuB1X,EAAvB,EAA2B;IAClC,KAAKyX,EAAL,GAAUA,EAAV;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAK2X,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBlB,eAArB,CAAqC4Y,SAArC,CAA+C,MAAM;MACrE,IAAI,KAAKH,EAAL,CAAQlU,aAAZ,EAA2B;QACvB,KAAKvD,EAAL,CAAQoJ,aAAR;MACH;IACJ,CAJmB,CAApB;EAKH;;EACDyO,iBAAiB,CAACC,MAAD,EAAShO,OAAT,EAAkB;IAC/B,IAAI,KAAK2N,EAAL,CAAQlU,aAAZ,EAA2B;MACvBuG,OAAO,GAAGA,OAAO,IAAI,KAAKiO,eAA1B;MACA,OAAOjO,OAAO,GAAGA,OAAO,CAACgO,MAAD,CAAV,GAAqB,IAAnC;IACH;;IACD,OAAO,IAAP;EACH;;EACDE,WAAW,CAACnH,QAAD,EAAW;IAClB,MAAMoH,cAAc,GAAG,KAAKJ,iBAAL,CAAuB,gBAAvB,CAAvB;IACA,OAAOI,cAAc,GAAGA,cAAc,CAACpH,QAAD,CAAd,CAAyBpP,KAA5B,GAAoCoP,QAAzD;EACH;;EACDwE,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AA1BQ;;AA4BbjC,MAAM,CAACtW,IAAP;EAAA,iBAAmGsW,MAAnG,EA39CmGvZ,EA29CnG,mBAA2HoD,SAA3H,GA39CmGpD,EA29CnG,mBAAiJ2B,gBAAjJ,GA39CmG3B,EA29CnG,mBAA8KA,EAAE,CAAC6Y,iBAAjL;AAAA;;AACAU,MAAM,CAACP,IAAP,kBA59CmGhZ,EA49CnG;EAAA,MAAuFuZ,MAAvF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA59CmGvZ,EA69C3F,qEADR;MA59CmGA,EAk+C3F,uEANR;IAAA;;IAAA;MA59CmGA,EA69ChC,wGADnE;MA59CmGA,EAk+C5E,aANvB;MA59CmGA,EAk+C5E,qCANvB;IAAA;EAAA;EAAA,eASiEa,EAAE,CAAC4a,OATpE,EAS8L5a,EAAE,CAACqY,IATjM,EASkSrY,EAAE,CAACsY,gBATrS;EAAA;AAAA;;AAUA;EAAA,mDAt+CmGnZ,EAs+CnG,mBAA2FuZ,MAA3F,EAA+G,CAAC;IACpGpW,IAAI,EAAEhD,SAD8F;IAEpGqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAXmB;MAYCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IAZlC;MAaCC,IAAI,EAAE;QACF,SAAS;MADP;IAbP,CAAD;EAF8F,CAAD,CAA/G,EAmB4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,EAAkD;MAAEwB,IAAI,EAAEnD,EAAE,CAAC6Y;IAAX,CAAlD,CAAP;EAA2F,CAnBrI,EAmBuJ;IAAE3F,OAAO,EAAE,CAAC;MACnJ/P,IAAI,EAAE9C,KAD6I;MAEnJmZ,IAAI,EAAE,CAAC,gBAAD;IAF6I,CAAD,CAAX;IAGvIlS,QAAQ,EAAE,CAAC;MACXnE,IAAI,EAAE9C,KADK;MAEXmZ,IAAI,EAAE,CAAC,wBAAD;IAFK,CAAD,CAH6H;IAMvIkC,MAAM,EAAE,CAAC;MACTvY,IAAI,EAAE9C;IADG,CAAD,CAN+H;IAQvIsb,eAAe,EAAE,CAAC;MAClBxY,IAAI,EAAE9C;IADY,CAAD,CARsH;IAUvIgb,eAAe,EAAE,CAAC;MAClBlY,IAAI,EAAE9C;IADY,CAAD;EAVsH,CAnBvJ;AAAA;;AAgCA,MAAMiZ,gBAAN,CAAuB;EACnB1X,WAAW,CAACmZ,EAAD,EAAK1X,EAAL,EAASE,IAAT,EAAe;IACtB,KAAKwX,EAAL,GAAUA,EAAV;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAKE,IAAL,GAAYA,IAAZ;EACH;;EACe,IAAZkX,YAAY,GAAG;IACf,OAAO,KAAKmB,aAAZ;EACH;;EACe,IAAZnB,YAAY,CAAChU,GAAD,EAAM;IAClB,KAAKmV,aAAL,GAAqBnV,GAArB;;IACA,IAAIA,GAAG,IAAI,IAAP,KAAgBA,GAAG,CAACoV,QAAJ,CAAa,GAAb,KAAqBpV,GAAG,CAACoV,QAAJ,CAAa,MAAb,CAArC,CAAJ,EAAgE;MAC5DnV,OAAO,CAACoV,GAAR,CAAY,uIAAZ;IACH;EACJ;;EACDC,eAAe,GAAG;IACd,IAAI,CAAC,KAAKL,MAAV,EAAkB;MACd,IAAI,KAAKX,EAAL,CAAQF,aAAR,IAAyB,KAAKE,EAAL,CAAQ7S,kBAArC,EAAyD;QACrDlH,UAAU,CAACkN,QAAX,CAAoB,KAAK7K,EAAL,CAAQkK,aAA5B,EAA2C,2BAA3C;MACH;;MACD,IAAIyO,UAAU,GAAG,KAAK3Y,EAAL,CAAQkK,aAAR,CAAsB0O,sBAAvC;;MACA,IAAID,UAAJ,EAAgB;QACZ,IAAI,KAAKjB,EAAL,CAAQlU,aAAZ,EACI,KAAKqV,iBAAL,GAAyBlb,UAAU,CAACyO,UAAX,CAAsBuM,UAAtB,EAAkC,sBAAlC,CAAzB,CADJ,KAGI,KAAKE,iBAAL,GAAyBlb,UAAU,CAACyO,UAAX,CAAsBuM,UAAtB,EAAkC,8BAAlC,CAAzB;MACP;;MACD,IAAIG,cAAc,GAAGnb,UAAU,CAACwP,uBAAX,EAArB;MACA,KAAK4L,wBAAL,CAA8B7O,aAA9B,CAA4Ca,KAA5C,CAAkDiO,YAAlD,GAAiEF,cAAc,GAAG,IAAlF;;MACA,IAAI,KAAKG,wBAAL,IAAiC,KAAKA,wBAAL,CAA8B/O,aAAnE,EAAkF;QAC9E,KAAK+O,wBAAL,CAA8B/O,aAA9B,CAA4Ca,KAA5C,CAAkDiO,YAAlD,GAAiEF,cAAc,GAAG,IAAlF;MACH;IACJ,CAhBD,MAiBK;MACD,IAAI,KAAKI,0BAAL,IAAmC,KAAKA,0BAAL,CAAgChP,aAAvE,EAAsF;QAClF,KAAKgP,0BAAL,CAAgChP,aAAhC,CAA8Ca,KAA9C,CAAoDC,MAApD,GAA6DrN,UAAU,CAACwb,wBAAX,KAAwC,IAArG;MACH;IACJ;;IACD,KAAKC,UAAL;EACH;;EACDA,UAAU,GAAG;IACT,KAAKlZ,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;MAC9B,IAAI,KAAKC,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BpP,aAA7D,EAA4E;QACxE,KAAKqP,oBAAL,GAA4B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA5B;QACA,KAAKV,wBAAL,CAA8B7O,aAA9B,CAA4CkL,gBAA5C,CAA6D,QAA7D,EAAuE,KAAKmE,oBAA5E;MACH;;MACD,IAAI,KAAKG,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BxP,aAA7D,EAA4E;QACxE,KAAKyP,oBAAL,GAA4B,KAAKC,cAAL,CAAoBH,IAApB,CAAyB,IAAzB,CAA5B;QACA,KAAKC,qBAAL,CAA2BxP,aAA3B,CAAyCkL,gBAAzC,CAA0D,QAA1D,EAAoE,KAAKuE,oBAAzE;MACH;;MACD,IAAI,CAAC,KAAKtB,MAAV,EAAkB;QACd,KAAKwB,kBAAL,GAA0B,KAAKC,YAAL,CAAkBL,IAAlB,CAAuB,IAAvB,CAA1B;QACA,IAAI,KAAK/B,EAAL,CAAQlU,aAAZ,EACI,KAAKuW,QAAL,CAAcC,aAAd,GAA8B9P,aAA9B,CAA4CkL,gBAA5C,CAA6D,QAA7D,EAAuE,KAAKyE,kBAA5E,EADJ,KAGI,KAAKI,mBAAL,CAAyB/P,aAAzB,CAAuCkL,gBAAvC,CAAwD,QAAxD,EAAkE,KAAKyE,kBAAvE;MACP;IACJ,CAhBD;EAiBH;;EACDK,YAAY,GAAG;IACX,IAAI,KAAKZ,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BpP,aAA7D,EAA4E;MACxE,KAAK6O,wBAAL,CAA8B7O,aAA9B,CAA4CmL,mBAA5C,CAAgE,QAAhE,EAA0E,KAAKkE,oBAA/E;IACH;;IACD,IAAI,KAAKG,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BxP,aAA7D,EAA4E;MACxE,KAAKwP,qBAAL,CAA2BxP,aAA3B,CAAyCmL,mBAAzC,CAA6D,QAA7D,EAAuE,KAAKsE,oBAA5E;IACH;;IACD,IAAI,KAAKM,mBAAL,IAA4B,KAAKA,mBAAL,CAAyB/P,aAAzD,EAAwE;MACpE,KAAK+P,mBAAL,CAAyB/P,aAAzB,CAAuCmL,mBAAvC,CAA2D,QAA3D,EAAqE,KAAKwE,kBAA1E;IACH;;IACD,IAAI,KAAKE,QAAL,IAAiB,KAAKA,QAAL,CAAcC,aAAd,EAArB,EAAoD;MAChD,KAAKD,QAAL,CAAcC,aAAd,GAA8B9P,aAA9B,CAA4CmL,mBAA5C,CAAgE,QAAhE,EAA0E,KAAKwE,kBAA/E;IACH;EACJ;;EACDL,cAAc,GAAG;IACb,MAAM9O,UAAU,GAAG,KAAK4O,qBAAL,CAA2BpP,aAA3B,CAAyCQ,UAA5D;IACA,KAAKuP,mBAAL,CAAyB/P,aAAzB,CAAuCQ,UAAvC,GAAoDA,UAApD;;IACA,IAAI,KAAKgP,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BxP,aAA7D,EAA4E;MACxE,KAAKwP,qBAAL,CAA2BxP,aAA3B,CAAyCQ,UAAzC,GAAsDA,UAAtD;IACH;;IACD,KAAKyP,4BAAL,GAAoC,IAApC;EACH;;EACDP,cAAc,GAAG;IACb,MAAMlP,UAAU,GAAG,KAAKgP,qBAAL,CAA2BxP,aAA3B,CAAyCQ,UAA5D;IACA,KAAKuP,mBAAL,CAAyB/P,aAAzB,CAAuCQ,UAAvC,GAAoDA,UAApD;;IACA,IAAI,KAAK4O,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BpP,aAA7D,EAA4E;MACxE,KAAKoP,qBAAL,CAA2BpP,aAA3B,CAAyCQ,UAAzC,GAAsDA,UAAtD;IACH;;IACD,KAAKyP,4BAAL,GAAoC,IAApC;EACH;;EACDL,YAAY,CAACrS,KAAD,EAAQ;IAChB,IAAI,KAAK0S,4BAAT,EAAuC;MACnC,KAAKA,4BAAL,GAAoC,KAApC;MACA;IACH;;IACD,IAAI,KAAKb,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BpP,aAA7D,EAA4E;MACxE,KAAK6O,wBAAL,CAA8B7O,aAA9B,CAA4Ca,KAA5C,CAAkDqP,UAAlD,GAA+D,CAAC,CAAD,GAAK3S,KAAK,CAACwI,MAAN,CAAavF,UAAlB,GAA+B,IAA9F;IACH;;IACD,IAAI,KAAKgP,qBAAL,IAA8B,KAAKA,qBAAL,CAA2BxP,aAA7D,EAA4E;MACxE,KAAK+O,wBAAL,CAA8B/O,aAA9B,CAA4Ca,KAA5C,CAAkDqP,UAAlD,GAA+D,CAAC,CAAD,GAAK3S,KAAK,CAACwI,MAAN,CAAavF,UAAlB,GAA+B,IAA9F;IACH;;IACD,IAAI,KAAKmO,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBwB,SAAvB,GAAmC5S,KAAK,CAACwI,MAAN,CAAaoK,SAAhD;IACH;EACJ;;EACD3Q,oBAAoB,CAAChI,KAAD,EAAQ;IACxB,IAAI,KAAKqY,QAAT,EAAmB;MACf,KAAKA,QAAL,CAAcO,aAAd,CAA4B5Y,KAA5B;IACH;EACJ;;EACDiI,QAAQ,CAACI,OAAD,EAAU;IACd,IAAI,KAAKgQ,QAAT,EAAmB;MACf,KAAKA,QAAL,CAAcpQ,QAAd,CAAuBI,OAAvB;IACH,CAFD,MAGK;MACD,IAAI,KAAKkQ,mBAAL,CAAyB/P,aAAzB,CAAuCP,QAA3C,EAAqD;QACjD,KAAKsQ,mBAAL,CAAyB/P,aAAzB,CAAuCP,QAAvC,CAAgDI,OAAhD;MACH,CAFD,MAGK;QACD,KAAKkQ,mBAAL,CAAyB/P,aAAzB,CAAuCQ,UAAvC,GAAoDX,OAAO,CAACQ,IAA5D;QACA,KAAK0P,mBAAL,CAAyB/P,aAAzB,CAAuCmQ,SAAvC,GAAmDtQ,OAAO,CAACH,GAA3D;MACH;IACJ;EACJ;;EACD0L,WAAW,GAAG;IACV,KAAK4E,YAAL;IACA,KAAKrB,iBAAL,GAAyB,IAAzB;EACH;;AA9HkB;;AAgIvB5C,gBAAgB,CAACrW,IAAjB;EAAA,iBAA6GqW,gBAA7G,EAtoDmGtZ,EAsoDnG,mBAA+IoD,SAA/I,GAtoDmGpD,EAsoDnG,mBAAqKA,EAAE,CAAC4Y,UAAxK,GAtoDmG5Y,EAsoDnG,mBAA+LA,EAAE,CAAC8Y,MAAlM;AAAA;;AACAQ,gBAAgB,CAACN,IAAjB,kBAvoDmGhZ,EAuoDnG;EAAA,MAAiGsZ,gBAAjG;EAAA;EAAA;IAAA;MAvoDmGtZ,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG;IAAA;;IAAA;MAAA;;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;MAvoDmGA,EAuoDnG,qBAvoDmGA,EAuoDnG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAvoDmGA,EAwoD3F,8DADR;MAvoDmGA,EA2oD/E,iFAJpB;MAvoDmGA,EA4oD/E,8BALpB;MAvoDmGA,EA6oD3E,iFANxB;MAvoDmGA,EA8oD/E,qBAPpB;MAvoDmGA,EAmpD3F,8EAZR;MAvoDmGA,EA8pD3F,kFAvBR;MAvoDmGA,EAoqD3F,yFApqD2FA,EAoqD3F,wBA7BR;MAvoDmGA,EA4qD3F,mEArCR;IAAA;;IAAA;MAvoDmGA,EA0oDhC,aAHnE;MAvoDmGA,EA0oDhC,4EAHnE;MAvoDmGA,EA2oDhE,aAJnC;MAvoDmGA,EA2oDhE,4JA3oDgEA,EA2oDhE,sCAJnC;MAvoDmGA,EA6oD5D,aANvC;MAvoDmGA,EA6oD5D,sJA7oD4DA,EA6oD5D,uCANvC;MAvoDmGA,EAmpD9E,aAZrB;MAvoDmGA,EAmpD9E,yCAZrB;MAvoDmGA,EA8pD5E,aAvBvB;MAvoDmGA,EA8pD5E,0CAvBvB;MAvoDmGA,EA4qDvE,aArC5B;MAvoDmGA,EA4qDvE,0CArC5B;IAAA;EAAA;EAAA,eA+CiEa,EAAE,CAACoY,OA/CpE,EA+C+JpY,EAAE,CAACqY,IA/ClK,EA+CmQrY,EAAE,CAACsY,gBA/CtQ,EA+C0atY,EAAE,CAACuY,OA/C7a,EA+C+fjY,EAAE,CAACC,aA/ClgB,EA+CsmBK,EAAE,CAACmc,QA/CzmB,EA+CigCrE,MA/CjgC;EAAA;AAAA;;AAgDA;EAAA,mDAvrDmGvZ,EAurDnG,mBAA2FsZ,gBAA3F,EAAyH,CAAC;IAC9GnW,IAAI,EAAEhD,SADwG;IAE9GqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAjDmB;MAkDCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IAlDlC;MAmDCC,IAAI,EAAE;QACF,SAAS;MADP;IAnDP,CAAD;EAFwG,CAAD,CAAzH,EAyD4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAtB,EAA+C;MAAEzV,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA/C,CAAP;EAA6E,CAzDvH,EAyDyI;IAAE5F,OAAO,EAAE,CAAC;MACrI/P,IAAI,EAAE9C,KAD+H;MAErImZ,IAAI,EAAE,CAAC,kBAAD;IAF+H,CAAD,CAAX;IAGzHkC,MAAM,EAAE,CAAC;MACTvY,IAAI,EAAE9C;IADG,CAAD,CAHiH;IAKzHsc,qBAAqB,EAAE,CAAC;MACxBxZ,IAAI,EAAE5C,SADkB;MAExBiZ,IAAI,EAAE,CAAC,cAAD;IAFkB,CAAD,CALkG;IAQzH4C,wBAAwB,EAAE,CAAC;MAC3BjZ,IAAI,EAAE5C,SADqB;MAE3BiZ,IAAI,EAAE,CAAC,iBAAD;IAFqB,CAAD,CAR+F;IAWzH8D,mBAAmB,EAAE,CAAC;MACtBna,IAAI,EAAE5C,SADgB;MAEtBiZ,IAAI,EAAE,CAAC,YAAD;IAFgB,CAAD,CAXoG;IAczHqE,oBAAoB,EAAE,CAAC;MACvB1a,IAAI,EAAE5C,SADiB;MAEvBiZ,IAAI,EAAE,CAAC,aAAD;IAFiB,CAAD,CAdmG;IAiBzHsE,2BAA2B,EAAE,CAAC;MAC9B3a,IAAI,EAAE5C,SADwB;MAE9BiZ,IAAI,EAAE,CAAC,cAAD;IAFwB,CAAD,CAjB4F;IAoBzHuD,qBAAqB,EAAE,CAAC;MACxB5Z,IAAI,EAAE5C,SADkB;MAExBiZ,IAAI,EAAE,CAAC,cAAD;IAFkB,CAAD,CApBkG;IAuBzH8C,wBAAwB,EAAE,CAAC;MAC3BnZ,IAAI,EAAE5C,SADqB;MAE3BiZ,IAAI,EAAE,CAAC,iBAAD;IAFqB,CAAD,CAvB+F;IA0BzH+C,0BAA0B,EAAE,CAAC;MAC7BpZ,IAAI,EAAE5C,SADuB;MAE7BiZ,IAAI,EAAE,CAAC,mBAAD;IAFuB,CAAD,CA1B6F;IA6BzH4D,QAAQ,EAAE,CAAC;MACXja,IAAI,EAAE5C,SADK;MAEXiZ,IAAI,EAAE,CAAC,UAAD;IAFK,CAAD,CA7B+G;IAgCzHiB,YAAY,EAAE,CAAC;MACftX,IAAI,EAAE9C;IADS,CAAD;EAhC2G,CAzDzI;AAAA;;AA4FA,MAAM0d,gBAAN,CAAuB;EACnBnc,WAAW,CAACmZ,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;;IACA,IAAI,KAAKiD,SAAL,EAAJ,EAAsB;MAClB,KAAK/C,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBtB,WAArB,CAAiCgZ,SAAjC,CAA2CzY,QAAQ,IAAI;QACvE,KAAKwb,eAAL;MACH,CAFmB,CAApB;IAGH;EACJ;;EACDrX,QAAQ,GAAG;IACP,IAAI,KAAKoX,SAAL,EAAJ,EAAsB;MAClB,KAAKC,eAAL;IACH;EACJ;;EACDA,eAAe,GAAG;IACd,KAAK1R,MAAL,GAAc,KAAKwO,EAAL,CAAQzO,QAAR,CAAiB,KAAKnB,KAAtB,CAAd;EACH;;EACD+S,OAAO,CAACpT,KAAD,EAAQ;IACX,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKC,eAAL;MACA,KAAKlD,EAAL,CAAQ9P,IAAR,CAAa;QACTC,aAAa,EAAEJ,KADN;QAETK,KAAK,EAAE,KAAKA;MAFH,CAAb;MAIAnK,UAAU,CAACmd,cAAX;IACH;EACJ;;EACDC,UAAU,CAACtT,KAAD,EAAQ;IACd,KAAKoT,OAAL,CAAapT,KAAb;EACH;;EACDkT,SAAS,GAAG;IACR,OAAO,KAAKK,wBAAL,KAAkC,IAAzC;EACH;;EACD1F,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AArCkB;;AAuCvBuC,gBAAgB,CAAC9a,IAAjB;EAAA,iBAA6G8a,gBAA7G,EA1zDmG/d,EA0zDnG,mBAA+IoD,SAA/I;AAAA;;AACA2a,gBAAgB,CAACO,IAAjB,kBA3zDmGte,EA2zDnG;EAAA,MAAiG+d,gBAAjG;EAAA;EAAA;EAAA;EAAA;IAAA;MA3zDmG/d,EA2zDnG;QAAA,OAAiG,mBAAjG;MAAA;QAAA,OAAiG,sBAAjG;MAAA;IAAA;;IAAA;MA3zDmGA,EA2zDnG;MA3zDmGA,EA2zDnG;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA5zDmGA,EA4zDnG,mBAA2F+d,gBAA3F,EAAyH,CAAC;IAC9G5a,IAAI,EAAE1C,SADwG;IAE9G+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECI,IAAI,EAAE;QACF,SAAS,WADP;QAEF,6BAA6B,aAF3B;QAGF,uBAAuB,QAHrB;QAIF,mBAAmB,0BAJjB;QAKF,eAAe;MALb;IAFP,CAAD;EAFwG,CAAD,CAAzH,EAY4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,CAAP;EAA+B,CAZzE,EAY2F;IAAE+H,KAAK,EAAE,CAAC;MACrFhI,IAAI,EAAE9C,KAD+E;MAErFmZ,IAAI,EAAE,CAAC,kBAAD;IAF+E,CAAD,CAAT;IAG3E6E,wBAAwB,EAAE,CAAC;MAC3Blb,IAAI,EAAE9C;IADqB,CAAD,CAHiD;IAK3E6d,OAAO,EAAE,CAAC;MACV/a,IAAI,EAAEzC,YADI;MAEV8Y,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;IAFI,CAAD,CALkE;IAQ3E4E,UAAU,EAAE,CAAC;MACbjb,IAAI,EAAEzC,YADO;MAEb8Y,IAAI,EAAE,CAAC,eAAD,EAAkB,CAAC,QAAD,CAAlB;IAFO,CAAD;EAR+D,CAZ3F;AAAA;;AAwBA,MAAM+E,UAAN,CAAiB;EACb3c,WAAW,CAACmZ,EAAD,EAAKzX,EAAL,EAAS;IAChB,KAAKyX,EAAL,GAAUA,EAAV;IACA,KAAKzX,EAAL,GAAUA,EAAV;IACA,KAAK2X,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBtB,WAArB,CAAiCgZ,SAAjC,CAA2CzY,QAAQ,IAAI;MACvE,KAAKwb,eAAL;MACA,KAAK3a,EAAL,CAAQkb,YAAR;IACH,CAHmB,CAApB;EAIH;;EACD5X,QAAQ,GAAG;IACP,KAAKqX,eAAL;EACH;;EACDC,OAAO,CAACpT,KAAD,EAAQ;IACXA,KAAK,CAACkD,cAAN;EACH;;EACDiQ,eAAe,GAAG;IACd,IAAI,KAAKlD,EAAL,CAAQ3W,QAAR,KAAqB,QAAzB,EAAmC;MAC/B,KAAK8E,SAAL,GAAiB,KAAK6R,EAAL,CAAQzO,QAAR,CAAiB,KAAKnB,KAAtB,IAA+B,KAAK4P,EAAL,CAAQ7R,SAAvC,GAAmD,CAApE;IACH,CAFD,MAGK,IAAI,KAAK6R,EAAL,CAAQ3W,QAAR,KAAqB,UAAzB,EAAqC;MACtC,IAAI3B,QAAQ,GAAG,KAAKsY,EAAL,CAAQzP,WAAR,CAAoB,KAAKH,KAAzB,CAAf;MACA,KAAKjC,SAAL,GAAiBzG,QAAQ,GAAGA,QAAQ,CAAC8I,KAAZ,GAAoB,CAA7C;IACH;EACJ;;EACDoN,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AA5BY;;AA8BjB+C,UAAU,CAACtb,IAAX;EAAA,iBAAuGsb,UAAvG,EAl3DmGve,EAk3DnG,mBAAmIoD,SAAnI,GAl3DmGpD,EAk3DnG,mBAAyJA,EAAE,CAAC6Y,iBAA5J;AAAA;;AACA0F,UAAU,CAACvF,IAAX,kBAn3DmGhZ,EAm3DnG;EAAA,MAA2Fue,UAA3F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAn3DmGve,EAo3D3F,qBADR;IAAA;;IAAA;MAn3DmGA,EAo3DhD,uBAp3DgDA,EAo3DhD,0FADnD;IAAA;EAAA;EAAA,eAEiEa,EAAE,CAACoY,OAFpE;EAAA;EAAA;AAAA;;AAGA;EAAA,mDAt3DmGjZ,EAs3DnG,mBAA2Fue,UAA3F,EAAmH,CAAC;IACxGpb,IAAI,EAAEhD,SADkG;IAExGqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA,KAJmB;MAKCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IALlC;MAMC6E,eAAe,EAAE9d,uBAAuB,CAAC+d,MAN1C;MAOC7E,IAAI,EAAE;QACF,SAAS;MADP;IAPP,CAAD;EAFkG,CAAD,CAAnH,EAa4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC6Y;IAAX,CAAtB,CAAP;EAA+D,CAbzG,EAa2H;IAAE1N,KAAK,EAAE,CAAC;MACrHhI,IAAI,EAAE9C;IAD+G,CAAD,CAAT;IAE3Gse,aAAa,EAAE,CAAC;MAChBxb,IAAI,EAAE9C;IADU,CAAD,CAF4F;IAI3Gue,YAAY,EAAE,CAAC;MACfzb,IAAI,EAAE9C;IADS,CAAD;EAJ6F,CAb3H;AAAA;;AAoBA,MAAMwe,iBAAN,CAAwB;EACpBjd,WAAW,CAACmZ,EAAD,EAAK1X,EAAL,EAASE,IAAT,EAAe;IACtB,KAAKwX,EAAL,GAAUA,EAAV;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAKE,IAAL,GAAYA,IAAZ;EACH;;EACDwY,eAAe,GAAG;IACd,IAAI,KAAKiC,SAAL,EAAJ,EAAsB;MAClBhd,UAAU,CAACkN,QAAX,CAAoB,KAAK7K,EAAL,CAAQkK,aAA5B,EAA2C,oBAA3C;MACA,KAAKuR,OAAL,GAAetG,QAAQ,CAACuG,aAAT,CAAuB,MAAvB,CAAf;MACA,KAAKD,OAAL,CAAaE,SAAb,GAAyB,kBAAzB;MACA,KAAK3b,EAAL,CAAQkK,aAAR,CAAsB0R,WAAtB,CAAkC,KAAKH,OAAvC;MACA,KAAKvb,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;QAC9B,KAAKwC,wBAAL,GAAgC,KAAKC,WAAL,CAAiBrC,IAAjB,CAAsB,IAAtB,CAAhC;QACA,KAAKgC,OAAL,CAAarG,gBAAb,CAA8B,WAA9B,EAA2C,KAAKyG,wBAAhD;MACH,CAHD;IAIH;EACJ;;EACDE,kBAAkB,GAAG;IACjB,KAAK7b,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;MAC9B,KAAK2C,yBAAL,GAAiC,KAAKC,mBAAL,CAAyBxC,IAAzB,CAA8B,IAA9B,CAAjC;MACAtE,QAAQ,CAACC,gBAAT,CAA0B,WAA1B,EAAuC,KAAK4G,yBAA5C;MACA,KAAKE,uBAAL,GAA+B,KAAKC,iBAAL,CAAuB1C,IAAvB,CAA4B,IAA5B,CAA/B;MACAtE,QAAQ,CAACC,gBAAT,CAA0B,SAA1B,EAAqC,KAAK8G,uBAA1C;IACH,CALD;EAMH;;EACDE,oBAAoB,GAAG;IACnB,IAAI,KAAKJ,yBAAT,EAAoC;MAChC7G,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0C,KAAK2G,yBAA/C;MACA,KAAKA,yBAAL,GAAiC,IAAjC;IACH;;IACD,IAAI,KAAKE,uBAAT,EAAkC;MAC9B/G,QAAQ,CAACE,mBAAT,CAA6B,SAA7B,EAAwC,KAAK6G,uBAA7C;MACA,KAAKA,uBAAL,GAA+B,IAA/B;IACH;EACJ;;EACDJ,WAAW,CAACrU,KAAD,EAAQ;IACf,KAAKiQ,EAAL,CAAQvN,mBAAR,CAA4B1C,KAA5B;IACA,KAAKsU,kBAAL;EACH;;EACDE,mBAAmB,CAACxU,KAAD,EAAQ;IACvB,KAAKiQ,EAAL,CAAQ9M,cAAR,CAAuBnD,KAAvB;EACH;;EACD0U,iBAAiB,CAAC1U,KAAD,EAAQ;IACrB,KAAKiQ,EAAL,CAAQvM,iBAAR,CAA0B1D,KAA1B,EAAiC,KAAKzH,EAAL,CAAQkK,aAAzC;IACA,KAAKkS,oBAAL;EACH;;EACDzB,SAAS,GAAG;IACR,OAAO,KAAK0B,yBAAL,KAAmC,IAA1C;EACH;;EACD/G,WAAW,GAAG;IACV,IAAI,KAAKuG,wBAAT,EAAmC;MAC/B,KAAKJ,OAAL,CAAapG,mBAAb,CAAiC,WAAjC,EAA8C,KAAKwG,wBAAnD;IACH;;IACD,KAAKO,oBAAL;EACH;;AAvDmB;;AAyDxBZ,iBAAiB,CAAC5b,IAAlB;EAAA,iBAA8G4b,iBAA9G,EAn8DmG7e,EAm8DnG,mBAAiJoD,SAAjJ,GAn8DmGpD,EAm8DnG,mBAAuKA,EAAE,CAAC4Y,UAA1K,GAn8DmG5Y,EAm8DnG,mBAAiMA,EAAE,CAAC8Y,MAApM;AAAA;;AACA+F,iBAAiB,CAACP,IAAlB,kBAp8DmGte,EAo8DnG;EAAA,MAAkG6e,iBAAlG;EAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAr8DmG7e,EAq8DnG,mBAA2F6e,iBAA3F,EAA0H,CAAC;IAC/G1b,IAAI,EAAE1C,SADyG;IAE/G+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBADX;MAECI,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAFyG,CAAD,CAA1H,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAtB,EAA+C;MAAEzV,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA/C,CAAP;EAA6E,CARvH,EAQyI;IAAE4G,yBAAyB,EAAE,CAAC;MACvJvc,IAAI,EAAE9C;IADiJ,CAAD;EAA7B,CARzI;AAAA;;AAWA,MAAMsf,mBAAN,CAA0B;EACtB/d,WAAW,CAACmZ,EAAD,EAAK1X,EAAL,EAASE,IAAT,EAAe;IACtB,KAAKwX,EAAL,GAAUA,EAAV;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAKE,IAAL,GAAYA,IAAZ;EACH;;EACDwY,eAAe,GAAG;IACd,IAAI,KAAKiC,SAAL,EAAJ,EAAsB;MAClB,KAAKvB,UAAL;IACH;EACJ;;EACDA,UAAU,GAAG;IACT,KAAKlZ,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;MAC9B,KAAKkD,iBAAL,GAAyB,KAAKT,WAAL,CAAiBrC,IAAjB,CAAsB,IAAtB,CAAzB;MACA,KAAKzZ,EAAL,CAAQkK,aAAR,CAAsBkL,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKmH,iBAAzD;MACA,KAAKC,iBAAL,GAAyB,KAAKC,WAAL,CAAiBhD,IAAjB,CAAsB,IAAtB,CAAzB;MACA,KAAKzZ,EAAL,CAAQkK,aAAR,CAAsBkL,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKoH,iBAAzD;MACA,KAAKE,gBAAL,GAAwB,KAAKC,WAAL,CAAiBlD,IAAjB,CAAsB,IAAtB,CAAxB;MACA,KAAKzZ,EAAL,CAAQkK,aAAR,CAAsBkL,gBAAtB,CAAuC,UAAvC,EAAmD,KAAKsH,gBAAxD;MACA,KAAKE,iBAAL,GAAyB,KAAKD,WAAL,CAAiBlD,IAAjB,CAAsB,IAAtB,CAAzB;MACA,KAAKzZ,EAAL,CAAQkK,aAAR,CAAsBkL,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKwH,iBAAzD;MACA,KAAKC,iBAAL,GAAyB,KAAKC,WAAL,CAAiBrD,IAAjB,CAAsB,IAAtB,CAAzB;MACA,KAAKzZ,EAAL,CAAQkK,aAAR,CAAsBkL,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKyH,iBAAzD;IACH,CAXD;EAYH;;EACD3C,YAAY,GAAG;IACX,IAAI,KAAKqC,iBAAT,EAA4B;MACxBpH,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0C,KAAKkH,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;;IACD,IAAI,KAAKG,gBAAT,EAA2B;MACvBvH,QAAQ,CAACE,mBAAT,CAA6B,UAA7B,EAAyC,KAAKqH,gBAA9C;MACA,KAAKA,gBAAL,GAAwB,IAAxB;IACH;;IACD,IAAI,KAAKE,iBAAT,EAA4B;MACxBzH,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0C,KAAKuH,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;;IACD,IAAI,KAAKA,iBAAT,EAA4B;MACxBzH,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0C,KAAKuH,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;;IACD,IAAI,KAAKC,iBAAT,EAA4B;MACxB1H,QAAQ,CAACE,mBAAT,CAA6B,WAA7B,EAA0C,KAAKwH,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;EACJ;;EACDf,WAAW,CAACrU,KAAD,EAAQ;IACf,IAAIA,KAAK,CAACwI,MAAN,CAAatC,QAAb,KAA0B,OAA1B,IAAqClG,KAAK,CAACwI,MAAN,CAAatC,QAAb,KAA0B,UAA/D,IAA6EhQ,UAAU,CAAC8P,QAAX,CAAoBhG,KAAK,CAACwI,MAA1B,EAAkC,kBAAlC,CAAjF,EACI,KAAKjQ,EAAL,CAAQkK,aAAR,CAAsB4F,SAAtB,GAAkC,KAAlC,CADJ,KAGI,KAAK9P,EAAL,CAAQkK,aAAR,CAAsB4F,SAAtB,GAAkC,IAAlC;EACP;;EACD2M,WAAW,CAAChV,KAAD,EAAQ;IACf,KAAKiQ,EAAL,CAAQ5J,iBAAR,CAA0BrG,KAA1B,EAAiC,KAAKzH,EAAL,CAAQkK,aAAzC;EACH;;EACD6S,UAAU,CAACtV,KAAD,EAAQ;IACdA,KAAK,CAACkD,cAAN;EACH;;EACDgS,WAAW,CAAClV,KAAD,EAAQ;IACf,KAAKiQ,EAAL,CAAQjJ,iBAAR,CAA0BhH,KAA1B,EAAiC,KAAKzH,EAAL,CAAQkK,aAAzC;EACH;;EACD4S,WAAW,CAACrV,KAAD,EAAQ;IACf,KAAKiQ,EAAL,CAAQrI,iBAAR,CAA0B5H,KAA1B;EACH;;EACDuV,MAAM,CAACvV,KAAD,EAAQ;IACV,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQpI,YAAR,CAAqB7H,KAArB,EAA4B,KAAKzH,EAAL,CAAQkK,aAApC;IACH;EACJ;;EACDyQ,SAAS,GAAG;IACR,OAAO,KAAKsC,2BAAL,KAAqC,IAA5C;EACH;;EACD3H,WAAW,GAAG;IACV,KAAK4E,YAAL;EACH;;AA3EqB;;AA6E1BoC,mBAAmB,CAAC1c,IAApB;EAAA,iBAAgH0c,mBAAhH,EA7hEmG3f,EA6hEnG,mBAAqJoD,SAArJ,GA7hEmGpD,EA6hEnG,mBAA2KA,EAAE,CAAC4Y,UAA9K,GA7hEmG5Y,EA6hEnG,mBAAqMA,EAAE,CAAC8Y,MAAxM;AAAA;;AACA6G,mBAAmB,CAACrB,IAApB,kBA9hEmGte,EA8hEnG;EAAA,MAAoG2f,mBAApG;EAAA;EAAA;EAAA;IAAA;MA9hEmG3f,EA8hEnG;QAAA,OAAoG,kBAApG;MAAA;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA/hEmGA,EA+hEnG,mBAA2F2f,mBAA3F,EAA4H,CAAC;IACjHxc,IAAI,EAAE1C,SAD2G;IAEjH+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBADX;MAECI,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAF2G,CAAD,CAA5H,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAtB,EAA+C;MAAEzV,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA/C,CAAP;EAA6E,CARvH,EAQyI;IAAEwH,2BAA2B,EAAE,CAAC;MACzJnd,IAAI,EAAE9C;IADmJ,CAAD,CAA/B;IAEzHggB,MAAM,EAAE,CAAC;MACTld,IAAI,EAAEzC,YADG;MAET8Y,IAAI,EAAE,CAAC,MAAD,EAAS,CAAC,QAAD,CAAT;IAFG,CAAD;EAFiH,CARzI;AAAA;;AAcA,MAAM+G,eAAN,CAAsB;EAClB3e,WAAW,CAACmZ,EAAD,EAAKvX,YAAL,EAAmB;IAC1B,KAAKuX,EAAL,GAAUA,EAAV;IACA,KAAKvX,YAAL,GAAoBA,YAApB;;IACA,IAAI,KAAKwa,SAAL,EAAJ,EAAsB;MAClB,KAAK/C,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBpB,gBAArB,CAAsC8Y,SAAtC,CAAgD,MAAM;QACtE,KAAK1H,QAAL,GAAgB,KAAKuH,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAhB;MACH,CAFmB,CAApB;IAGH;EACJ;;EACD+D,QAAQ,GAAG;IACP,IAAI,KAAKoX,SAAL,EAAJ,EAAsB;MAClB,KAAKxK,QAAL,GAAgB,KAAKuH,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAhB;IACH;EACJ;;EACDqb,OAAO,CAACpT,KAAD,EAAQ;IACX,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQ3H,cAAR,CAAuB;QACnBlI,aAAa,EAAEJ,KADI;QAEnBb,OAAO,EAAE,KAAKA;MAFK,CAAvB;IAIH;EACJ;;EACDmU,UAAU,CAACtT,KAAD,EAAQ;IACd,IAAIA,KAAK,CAAC0V,KAAN,KAAgB,EAApB,EAAwB;MACpB,KAAKtC,OAAL,CAAapT,KAAb;IACH;EACJ;;EACD2V,UAAU,CAAC3V,KAAD,EAAQ;IACd,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQ3G,iBAAR,CAA0BtJ,KAA1B;IACH;EACJ;;EACDkT,SAAS,GAAG;IACR,OAAO,KAAK0C,uBAAL,KAAiC,IAAxC;EACH;;EACD/H,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AAxCiB;;AA0CtB+E,eAAe,CAACtd,IAAhB;EAAA,iBAA4Gsd,eAA5G,EAvlEmGvgB,EAulEnG,mBAA6IoD,SAA7I,GAvlEmGpD,EAulEnG,mBAAmK2B,gBAAnK;AAAA;;AACA4e,eAAe,CAACjC,IAAhB,kBAxlEmGte,EAwlEnG;EAAA,MAAgGugB,eAAhG;EAAA;EAAA;EAAA;EAAA;IAAA;MAxlEmGvgB,EAwlEnG;QAAA,OAAgG,mBAAhG;MAAA;QAAA,OAAgG,sBAAhG;MAAA;QAAA,OAAgG,sBAAhG;MAAA;IAAA;;IAAA;MAxlEmGA,EAwlEnG;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAzlEmGA,EAylEnG,mBAA2FugB,eAA3F,EAAwH,CAAC;IAC7Gpd,IAAI,EAAE1C,SADuG;IAE7G+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBADX;MAECI,IAAI,EAAE;QACF,SAAS,WADP;QAEF,uBAAuB;MAFrB;IAFP,CAAD;EAFuG,CAAD,CAAxH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,CAAP;EAA2D,CATrG,EASuH;IAAEsI,OAAO,EAAE,CAAC;MACnH9G,IAAI,EAAE9C,KAD6G;MAEnHmZ,IAAI,EAAE,CAAC,iBAAD;IAF6G,CAAD,CAAX;IAGvGkH,uBAAuB,EAAE,CAAC;MAC1Bvd,IAAI,EAAE9C;IADoB,CAAD,CAH8E;IAKvG6d,OAAO,EAAE,CAAC;MACV/a,IAAI,EAAEzC,YADI;MAEV8Y,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;IAFI,CAAD,CAL8F;IAQvG4E,UAAU,EAAE,CAAC;MACbjb,IAAI,EAAEzC,YADO;MAEb8Y,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;IAFO,CAAD,CAR2F;IAWvGiH,UAAU,EAAE,CAAC;MACbtd,IAAI,EAAEzC,YADO;MAEb8Y,IAAI,EAAE,CAAC,UAAD,EAAa,CAAC,QAAD,CAAb;IAFO,CAAD;EAX2F,CATvH;AAAA;;AAwBA,MAAMmH,uBAAN,CAA8B;EAC1B/e,WAAW,CAACmZ,EAAD,EAAKvX,YAAL,EAAmB;IAC1B,KAAKuX,EAAL,GAAUA,EAAV;IACA,KAAKvX,YAAL,GAAoBA,YAApB;;IACA,IAAI,KAAKwa,SAAL,EAAJ,EAAsB;MAClB,KAAK/C,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBpB,gBAArB,CAAsC8Y,SAAtC,CAAgD,MAAM;QACtE,KAAK1H,QAAL,GAAgB,KAAKuH,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAhB;MACH,CAFmB,CAApB;IAGH;EACJ;;EACD+D,QAAQ,GAAG;IACP,IAAI,KAAKoX,SAAL,EAAJ,EAAsB;MAClB,KAAKxK,QAAL,GAAgB,KAAKuH,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAhB;IACH;EACJ;;EACDqb,OAAO,CAACpT,KAAD,EAAQ;IACX,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQ3H,cAAR,CAAuB;QACnBlI,aAAa,EAAEJ,KADI;QAEnBb,OAAO,EAAE,KAAKA;MAFK,CAAvB;IAIH;EACJ;;EACD+T,SAAS,GAAG;IACR,OAAO,KAAK0C,uBAAL,KAAiC,IAAxC;EACH;;EACD/H,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AA9ByB;;AAgC9BmF,uBAAuB,CAAC1d,IAAxB;EAAA,iBAAoH0d,uBAApH,EAjpEmG3gB,EAipEnG,mBAA6JoD,SAA7J,GAjpEmGpD,EAipEnG,mBAAmL2B,gBAAnL;AAAA;;AACAgf,uBAAuB,CAACrC,IAAxB,kBAlpEmGte,EAkpEnG;EAAA,MAAwG2gB,uBAAxG;EAAA;EAAA;EAAA;EAAA;IAAA;MAlpEmG3gB,EAkpEnG;QAAA,OAAwG,mBAAxG;MAAA;IAAA;;IAAA;MAlpEmGA,EAkpEnG;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAnpEmGA,EAmpEnG,mBAA2F2gB,uBAA3F,EAAgI,CAAC;IACrHxd,IAAI,EAAE1C,SAD+G;IAErH+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BADX;MAECI,IAAI,EAAE;QACF,SAAS,WADP;QAEF,uBAAuB;MAFrB;IAFP,CAAD;EAF+G,CAAD,CAAhI,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,CAAP;EAA2D,CATrG,EASuH;IAAEsI,OAAO,EAAE,CAAC;MACnH9G,IAAI,EAAE9C,KAD6G;MAEnHmZ,IAAI,EAAE,CAAC,yBAAD;IAF6G,CAAD,CAAX;IAGvGkH,uBAAuB,EAAE,CAAC;MAC1Bvd,IAAI,EAAE9C;IADoB,CAAD,CAH8E;IAKvG6d,OAAO,EAAE,CAAC;MACV/a,IAAI,EAAEzC,YADI;MAEV8Y,IAAI,EAAE,CAAC,UAAD,EAAa,CAAC,QAAD,CAAb;IAFI,CAAD;EAL8F,CATvH;AAAA;;AAkBA,MAAMoH,gBAAN,CAAuB;EACnBhf,WAAW,CAACmZ,EAAD,EAAKvX,YAAL,EAAmBH,EAAnB,EAAuB;IAC9B,KAAK0X,EAAL,GAAUA,EAAV;IACA,KAAKvX,YAAL,GAAoBA,YAApB;IACA,KAAKH,EAAL,GAAUA,EAAV;;IACA,IAAI,KAAK2a,SAAL,EAAJ,EAAsB;MAClB,KAAK/C,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBnB,kBAArB,CAAwC6Y,SAAxC,CAAmDrY,IAAD,IAAU;QAC5E,KAAK2Q,QAAL,GAAgB,KAAKuH,EAAL,CAAQ1F,MAAR,CAAe,KAAKpL,OAAL,CAAapH,IAA5B,EAAkCA,IAAlC,CAAhB;MACH,CAFmB,CAApB;IAGH;EACJ;;EACDD,aAAa,CAACkI,KAAD,EAAQ;IACjB,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQ1G,mBAAR,CAA4B;QACxBnJ,aAAa,EAAEJ,KADS;QAExBb,OAAO,EAAE,KAAKA;MAFU,CAA5B;MAIA,KAAK5G,EAAL,CAAQkK,aAAR,CAAsBsT,KAAtB;MACA/V,KAAK,CAACkD,cAAN;IACH;EACJ;;EACDgQ,SAAS,GAAG;IACR,OAAO,KAAK8C,wBAAL,KAAkC,IAAzC;EACH;;EACDnI,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AA5BkB;;AA8BvBoF,gBAAgB,CAAC3d,IAAjB;EAAA,iBAA6G2d,gBAA7G,EAnsEmG5gB,EAmsEnG,mBAA+IoD,SAA/I,GAnsEmGpD,EAmsEnG,mBAAqK2B,gBAArK,GAnsEmG3B,EAmsEnG,mBAAkMA,EAAE,CAAC4Y,UAArM;AAAA;;AACAgI,gBAAgB,CAACtC,IAAjB,kBApsEmGte,EAosEnG;EAAA,MAAiG4gB,gBAAjG;EAAA;EAAA;EAAA;EAAA;IAAA;MApsEmG5gB,EAosEnG;QAAA,OAAiG,yBAAjG;MAAA;IAAA;;IAAA;MApsEmGA,EAosEnG;MApsEmGA,EAosEnG;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDArsEmGA,EAqsEnG,mBAA2F4gB,gBAA3F,EAAyH,CAAC;IAC9Gzd,IAAI,EAAE1C,SADwG;IAE9G+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECI,IAAI,EAAE;QACF,SAAS,WADP;QAEF,mCAAmC,UAFjC;QAGF,mBAAmB;MAHjB;IAFP,CAAD;EAFwG,CAAD,CAAzH,EAU4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,EAAkD;MAAEwB,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAlD,CAAP;EAAoF,CAV9H,EAUgJ;IAAE3O,OAAO,EAAE,CAAC;MAC5I9G,IAAI,EAAE9C,KADsI;MAE5ImZ,IAAI,EAAE,CAAC,kBAAD;IAFsI,CAAD,CAAX;IAGhIsH,wBAAwB,EAAE,CAAC;MAC3B3d,IAAI,EAAE9C;IADqB,CAAD,CAHsG;IAKhIuC,aAAa,EAAE,CAAC;MAChBO,IAAI,EAAEzC,YADU;MAEhB8Y,IAAI,EAAE,CAAC,aAAD,EAAgB,CAAC,QAAD,CAAhB;IAFU,CAAD;EALiH,CAVhJ;AAAA;;AAmBA,MAAMuH,UAAN,CAAiB;EACbnf,WAAW,CAACmZ,EAAD,EAAKvX,YAAL,EAAmBF,EAAnB,EAAuB;IAC9B,KAAKyX,EAAL,GAAUA,EAAV;IACA,KAAKvX,YAAL,GAAoBA,YAApB;IACA,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAK2X,YAAL,GAAoB,KAAKF,EAAL,CAAQvX,YAAR,CAAqBpB,gBAArB,CAAsC8Y,SAAtC,CAAgD,MAAM;MACtE,KAAKnG,OAAL,GAAe,KAAKgG,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAf;MACA,KAAKS,EAAL,CAAQkb,YAAR;IACH,CAHmB,CAApB;EAIH;;EACD5X,QAAQ,GAAG;IACP,KAAKmO,OAAL,GAAe,KAAKgG,EAAL,CAAQtH,UAAR,CAAmB,KAAKxJ,OAAL,CAAapH,IAAhC,CAAf;EACH;;EACDqb,OAAO,CAACpT,KAAD,EAAQ;IACX,IAAI,CAAC,KAAKkW,QAAV,EAAoB;MAChB,KAAKjG,EAAL,CAAQtG,sBAAR,CAA+B;QAC3BvJ,aAAa,EAAEJ,KADY;QAE3Bb,OAAO,EAAE,KAAKA;MAFa,CAA/B;IAIH;;IACDjJ,UAAU,CAACmd,cAAX;EACH;;EACD8C,OAAO,GAAG;IACN,KAAKC,OAAL,GAAe,IAAf;EACH;;EACDC,MAAM,GAAG;IACL,KAAKD,OAAL,GAAe,KAAf;EACH;;EACDvI,WAAW,GAAG;IACV,IAAI,KAAKsC,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBO,WAAlB;IACH;EACJ;;AAhCY;;AAkCjBuF,UAAU,CAAC9d,IAAX;EAAA,iBAAuG8d,UAAvG,EA1vEmG/gB,EA0vEnG,mBAAmIoD,SAAnI,GA1vEmGpD,EA0vEnG,mBAAyJ2B,gBAAzJ,GA1vEmG3B,EA0vEnG,mBAAsLA,EAAE,CAAC6Y,iBAAzL;AAAA;;AACAkI,UAAU,CAAC/H,IAAX,kBA3vEmGhZ,EA2vEnG;EAAA,MAA2F+gB,UAA3F;EAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA3vEmG/gB,EA4vE3F,4BADR;MA3vEmGA,EA4vEZ;QAAA,OAAS,mBAAT;MAAA,EADvF;MA3vEmGA,EA6vEvF,2CAFZ;MA3vEmGA,EA8vExC;QAAA,OAAS,aAAT;MAAA;QAAA,OAA4B,YAA5B;MAAA,EAH3D;MA3vEmGA,EA8vEnF,iBAHhB;MA3vEmGA,EAgwEvF,+BALZ;MA3vEmGA,EAkwEnF,wBAPhB;MA3vEmGA,EAmwEvF,iBARZ;IAAA;;IAAA;MA3vEmGA,EA4vEvD,uBA5vEuDA,EA4vEvD,uCAD5C;MA3vEmGA,EA8vE5D,aAHvC;MA3vEmGA,EA8vE5D,mCAHvC;MA3vEmGA,EAgwE7E,aALtB;MA3vEmGA,EAgwE7E,uBAhwE6EA,EAgwE7E,oGALtB;MA3vEmGA,EAiwEiD,yCANpJ;MA3vEmGA,EAkwElD,aAPjD;MA3vEmGA,EAkwElD,uBAlwEkDA,EAkwElD,0EAPjD;IAAA;EAAA;EAAA,eAUiEa,EAAE,CAACoY,OAVpE;EAAA;EAAA;AAAA;;AAWA;EAAA,mDAtwEmGjZ,EAswEnG,mBAA2F+gB,UAA3F,EAAmH,CAAC;IACxG5d,IAAI,EAAEhD,SADkG;IAExGqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAZmB;MAaCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IAblC;MAcC6E,eAAe,EAAE9d,uBAAuB,CAAC+d,MAd1C;MAeC7E,IAAI,EAAE;QACF,SAAS;MADP;IAfP,CAAD;EAFkG,CAAD,CAAnH,EAqB4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,EAAkD;MAAEwB,IAAI,EAAEnD,EAAE,CAAC6Y;IAAX,CAAlD,CAAP;EAA2F,CArBrI,EAqBuJ;IAAEmI,QAAQ,EAAE,CAAC;MACpJ7d,IAAI,EAAE9C;IAD8I,CAAD,CAAZ;IAEvI4J,OAAO,EAAE,CAAC;MACV9G,IAAI,EAAE9C,KADI;MAEVmZ,IAAI,EAAE,CAAC,OAAD;IAFI,CAAD;EAF8H,CArBvJ;AAAA;;AA2BA,MAAM4H,gBAAN,CAAuB;EACnBxf,WAAW,CAACmZ,EAAD,EAAKvX,YAAL,EAAmBF,EAAnB,EAAuB;IAC9B,KAAKyX,EAAL,GAAUA,EAAV;IACA,KAAKvX,YAAL,GAAoBA,YAApB;IACA,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAK+d,uBAAL,GAA+B,KAAKtG,EAAL,CAAQvX,YAAR,CAAqBlB,eAArB,CAAqC4Y,SAArC,CAA+C,MAAM;MAChF,KAAKnG,OAAL,GAAe,KAAKuM,kBAAL,EAAf;IACH,CAF8B,CAA/B;IAGA,KAAKC,2BAAL,GAAmC,KAAKxG,EAAL,CAAQvX,YAAR,CAAqBpB,gBAArB,CAAsC8Y,SAAtC,CAAgD,MAAM;MACrF,KAAKnG,OAAL,GAAe,KAAKuM,kBAAL,EAAf;IACH,CAFkC,CAAnC;EAGH;;EACD1a,QAAQ,GAAG;IACP,KAAKmO,OAAL,GAAe,KAAKuM,kBAAL,EAAf;EACH;;EACDpD,OAAO,CAACpT,KAAD,EAAQiK,OAAR,EAAiB;IACpB,IAAI,KAAKgG,EAAL,CAAQhY,KAAR,IAAiB,KAAKgY,EAAL,CAAQhY,KAAR,CAAc0F,MAAd,GAAuB,CAA5C,EAA+C;MAC3C,KAAKsS,EAAL,CAAQnG,uBAAR,CAAgC9J,KAAhC,EAAuC,CAACiK,OAAxC;IACH;;IACD/T,UAAU,CAACmd,cAAX;EACH;;EACD8C,OAAO,GAAG;IACN,KAAKC,OAAL,GAAe,IAAf;EACH;;EACDC,MAAM,GAAG;IACL,KAAKD,OAAL,GAAe,KAAf;EACH;;EACDvI,WAAW,GAAG;IACV,IAAI,KAAK4I,2BAAT,EAAsC;MAClC,KAAKA,2BAAL,CAAiC/F,WAAjC;IACH;;IACD,IAAI,KAAK6F,uBAAT,EAAkC;MAC9B,KAAKA,uBAAL,CAA6B7F,WAA7B;IACH;EACJ;;EACD8F,kBAAkB,GAAG;IACjB,KAAKhe,EAAL,CAAQkb,YAAR;IACA,IAAIzJ,OAAJ;IACA,MAAM1K,IAAI,GAAG,KAAK0Q,EAAL,CAAQnR,aAAR,IAAyB,KAAKmR,EAAL,CAAQhY,KAA9C;;IACA,IAAIsH,IAAJ,EAAU;MACN,KAAK,IAAIxH,IAAT,IAAiBwH,IAAjB,EAAuB;QACnB,IAAI,KAAK0Q,EAAL,CAAQtH,UAAR,CAAmB5Q,IAAnB,CAAJ,EAA8B;UAC1BkS,OAAO,GAAG,IAAV;QACH,CAFD,MAGK;UACDA,OAAO,GAAG,KAAV;UACA;QACH;MACJ;IACJ,CAVD,MAWK;MACDA,OAAO,GAAG,KAAV;IACH;;IACD,OAAOA,OAAP;EACH;;AAtDkB;;AAwDvBqM,gBAAgB,CAACne,IAAjB;EAAA,iBAA6Gme,gBAA7G,EAz1EmGphB,EAy1EnG,mBAA+IoD,SAA/I,GAz1EmGpD,EAy1EnG,mBAAqK2B,gBAArK,GAz1EmG3B,EAy1EnG,mBAAkMA,EAAE,CAAC6Y,iBAArM;AAAA;;AACAuI,gBAAgB,CAACpI,IAAjB,kBA11EmGhZ,EA01EnG;EAAA,MAAiGohB,gBAAjG;EAAA;EAAA;IAAA;MA11EmGphB,EA01EnG;IAAA;;IAAA;MAAA;;MA11EmGA,EA01EnG,qBA11EmGA,EA01EnG;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAAA,YA11EmGA,EA01EnG;;MA11EmGA,EA21E3F,4BADR;MA11EmGA,EA21EZ;QA31EYA,EA21EZ;;QAAA,YA31EYA,EA21EZ;;QAAA,OA31EYA,EA21EH,8CAAT;MAAA,EADvF;MA11EmGA,EA41EvF,8CAFZ;MA11EmGA,EA61EpC;QAAA,OAAS,aAAT;MAAA;QAAA,OAA4B,YAA5B;MAAA,EAH/D;MA11EmGA,EA61EnF,iBAHhB;MA11EmGA,EA+1EvF,+BALZ;MA11EmGA,EAi2EnF,wBAPhB;MA11EmGA,EAk2EvF,iBARZ;IAAA;;IAAA;MA11EmGA,EA21EvD,uBA31EuDA,EA21EvD,uCAD5C;MA11EmGA,EA61ExD,aAH3C;MA11EmGA,EA61ExD,2FAH3C;MA11EmGA,EA+1E7E,aALtB;MA11EmGA,EA+1E7E,uBA/1E6EA,EA+1E7E,gGALtB;MA11EmGA,EAg2E6B,yCANhI;MA11EmGA,EAi2ErD,aAP9C;MA11EmGA,EAi2ErD,uBAj2EqDA,EAi2ErD,wCAP9C;IAAA;EAAA;EAAA,eAUiEa,EAAE,CAACoY,OAVpE;EAAA;EAAA;AAAA;;AAWA;EAAA,mDAr2EmGjZ,EAq2EnG,mBAA2FohB,gBAA3F,EAAyH,CAAC;IAC9Gje,IAAI,EAAEhD,SADwG;IAE9GqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAZmB;MAaCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IAblC;MAcC6E,eAAe,EAAE9d,uBAAuB,CAAC+d,MAd1C;MAeC7E,IAAI,EAAE;QACF,SAAS;MADP;IAfP,CAAD;EAFwG,CAAD,CAAzH,EAqB4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAExB;IAAR,CAAtB,EAAkD;MAAEwB,IAAI,EAAEnD,EAAE,CAAC6Y;IAAX,CAAlD,CAAP;EAA2F,CArBrI,EAqBuJ;IAAE2I,YAAY,EAAE,CAAC;MACxJre,IAAI,EAAE5C,SADkJ;MAExJiZ,IAAI,EAAE,CAAC,KAAD;IAFkJ,CAAD;EAAhB,CArBvJ;AAAA;;AAyBA,MAAMiI,gBAAN,CAAuB;EACnB7f,WAAW,CAACmZ,EAAD,EAAK1X,EAAL,EAASE,IAAT,EAAe;IACtB,KAAKwX,EAAL,GAAUA,EAAV;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAKE,IAAL,GAAYA,IAAZ;EACH;;EACDwY,eAAe,GAAG;IACd,IAAI,KAAKiC,SAAL,EAAJ,EAAsB;MAClBhd,UAAU,CAACkN,QAAX,CAAoB,KAAK7K,EAAL,CAAQkK,aAA5B,EAA2C,mBAA3C;IACH;EACJ;;EACD2Q,OAAO,CAACpT,KAAD,EAAQ;IACX,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB,KAAKjD,EAAL,CAAQzC,gBAAR,GAA2B,IAA3B;;MACA,IAAI,KAAKyC,EAAL,CAAQhD,WAAZ,EAAyB;QACrB,IAAI,KAAKgD,EAAL,CAAQhD,WAAR,KAAwB,KAAK1U,EAAL,CAAQkK,aAApC,EAAmD;UAC/C,IAAI,CAAC,KAAKwN,EAAL,CAAQ5C,kBAAR,EAAL,EAAmC;YAC/B;UACH;;UACDnX,UAAU,CAAC4P,WAAX,CAAuB,KAAKmK,EAAL,CAAQhD,WAA/B,EAA4C,gBAA5C;UACA,KAAK2J,QAAL;QACH;MACJ,CARD,MASK;QACD,KAAKA,QAAL;MACH;IACJ;EACJ;;EACDA,QAAQ,GAAG;IACP,KAAK3G,EAAL,CAAQlD,iBAAR,CAA0B,KAAKxU,EAAL,CAAQkK,aAAlC,EAAiD,KAAKlD,IAAtD,EAA4D,KAAKc,KAAjE;IACAnK,UAAU,CAACkN,QAAX,CAAoB,KAAK7K,EAAL,CAAQkK,aAA5B,EAA2C,gBAA3C;IACA,KAAKwN,EAAL,CAAQ/U,UAAR,CAAmBc,IAAnB,CAAwB;MAAEqE,KAAK,EAAE,KAAKA,KAAd;MAAqBd,IAAI,EAAE,KAAKA;IAAhC,CAAxB;IACA,KAAK0Q,EAAL,CAAQzC,gBAAR,GAA2B,IAA3B;IACA,KAAK/U,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;MAC9BhH,UAAU,CAAC,MAAM;QACb,IAAIiM,SAAS,GAAG3gB,UAAU,CAACyO,UAAX,CAAsB,KAAKpM,EAAL,CAAQkK,aAA9B,EAA6C,iBAA7C,CAAhB;;QACA,IAAIoU,SAAJ,EAAe;UACXA,SAAS,CAACd,KAAV;QACH;MACJ,CALS,EAKP,EALO,CAAV;IAMH,CAPD;EAQH;;EACDe,gBAAgB,GAAG;IACf5gB,UAAU,CAAC4P,WAAX,CAAuB,KAAKmK,EAAL,CAAQhD,WAA/B,EAA4C,iBAA5C;IACA,KAAKgD,EAAL,CAAQhD,WAAR,GAAsB,IAAtB;IACA,KAAKgD,EAAL,CAAQxC,0BAAR;EACH;;EACDsJ,SAAS,CAAC/W,KAAD,EAAQ;IACb,IAAI,KAAKkT,SAAL,EAAJ,EAAsB;MAClB;MACA,IAAIlT,KAAK,CAACgX,OAAN,IAAiB,EAArB,EAAyB;QACrB,IAAI,KAAK/G,EAAL,CAAQ5C,kBAAR,EAAJ,EAAkC;UAC9BnX,UAAU,CAAC4P,WAAX,CAAuB,KAAKmK,EAAL,CAAQhD,WAA/B,EAA4C,gBAA5C;UACA,KAAK6J,gBAAL;UACA,KAAK7G,EAAL,CAAQ9U,cAAR,CAAuBa,IAAvB,CAA4B;YAAEqE,KAAK,EAAE,KAAKA,KAAd;YAAqBd,IAAI,EAAE,KAAKA;UAAhC,CAA5B;QACH;;QACDS,KAAK,CAACkD,cAAN;MACH,CAPD,CAQA;MARA,KASK,IAAIlD,KAAK,CAACgX,OAAN,IAAiB,EAArB,EAAyB;QAC1B,IAAI,KAAK/G,EAAL,CAAQ5C,kBAAR,EAAJ,EAAkC;UAC9BnX,UAAU,CAAC4P,WAAX,CAAuB,KAAKmK,EAAL,CAAQhD,WAA/B,EAA4C,gBAA5C;UACA,KAAK6J,gBAAL;UACA,KAAK7G,EAAL,CAAQ7U,YAAR,CAAqBY,IAArB,CAA0B;YAAEqE,KAAK,EAAE,KAAKA,KAAd;YAAqBd,IAAI,EAAE,KAAKA;UAAhC,CAA1B;QACH;;QACDS,KAAK,CAACkD,cAAN;MACH,CAPI,CAQL;MARK,KASA,IAAIlD,KAAK,CAACgX,OAAN,IAAiB,CAArB,EAAwB;QACzB,KAAK/G,EAAL,CAAQ9U,cAAR,CAAuBa,IAAvB,CAA4B;UAAEqE,KAAK,EAAE,KAAKA,KAAd;UAAqBd,IAAI,EAAE,KAAKA;QAAhC,CAA5B;QACA,IAAIS,KAAK,CAACiX,QAAV,EACI,KAAKC,kBAAL,CAAwBlX,KAAxB,EADJ,KAGI,KAAKmX,cAAL,CAAoBnX,KAApB;MACP;IACJ;EACJ;;EACDoX,QAAQ,CAACvR,OAAD,EAAU;IACd,IAAIA,OAAJ,EAAa;MACT,IAAImH,IAAI,GAAGnH,OAAX;;MACA,OAAOmH,IAAI,IAAI,CAAC9W,UAAU,CAAC8P,QAAX,CAAoBgH,IAApB,EAA0B,gBAA1B,CAAhB,EAA6D;QACzDA,IAAI,GAAGA,IAAI,CAACjH,aAAZ;MACH;;MACD,OAAOiH,IAAP;IACH,CAND,MAOK;MACD,OAAO,IAAP;IACH;EACJ;;EACDkK,kBAAkB,CAAClX,KAAD,EAAQ;IACtB,IAAIqX,WAAW,GAAG,KAAKD,QAAL,CAAcpX,KAAK,CAACwI,MAApB,CAAlB;IACA,IAAI8O,GAAG,GAAGD,WAAW,CAACtR,aAAtB;IACA,IAAIwR,UAAU,GAAG,KAAKC,0BAAL,CAAgCH,WAAhC,CAAjB;;IACA,IAAIE,UAAJ,EAAgB;MACZrhB,UAAU,CAACuhB,mBAAX,CAA+BF,UAA/B,EAA2C,OAA3C;MACAvX,KAAK,CAACkD,cAAN;IACH;EACJ;;EACDiU,cAAc,CAACnX,KAAD,EAAQ;IAClB,IAAIqX,WAAW,GAAG,KAAKD,QAAL,CAAcpX,KAAK,CAACwI,MAApB,CAAlB;IACA,IAAI8O,GAAG,GAAGD,WAAW,CAACtR,aAAtB;IACA,IAAIwR,UAAU,GAAG,KAAKG,sBAAL,CAA4BL,WAA5B,CAAjB;;IACA,IAAIE,UAAJ,EAAgB;MACZrhB,UAAU,CAACuhB,mBAAX,CAA+BF,UAA/B,EAA2C,OAA3C;MACAvX,KAAK,CAACkD,cAAN;IACH;EACJ;;EACDsU,0BAA0B,CAACxK,IAAD,EAAO;IAC7B,IAAI2K,QAAQ,GAAG3K,IAAI,CAACmE,sBAApB;;IACA,IAAI,CAACwG,QAAL,EAAe;MACX,IAAIC,WAAW,GAAG5K,IAAI,CAACjH,aAAL,GAAqBiH,IAAI,CAACjH,aAAL,CAAmBoL,sBAAxC,GAAiE,IAAnF;;MACA,IAAIyG,WAAJ,EAAiB;QACbD,QAAQ,GAAGC,WAAW,CAACC,gBAAvB;MACH;IACJ;;IACD,IAAIF,QAAJ,EAAc;MACV,IAAIzhB,UAAU,CAAC8P,QAAX,CAAoB2R,QAApB,EAA8B,mBAA9B,CAAJ,EACI,OAAOA,QAAP,CADJ,KAGI,OAAO,KAAKH,0BAAL,CAAgCG,QAAhC,CAAP;IACP,CALD,MAMK;MACD,OAAO,IAAP;IACH;EACJ;;EACDD,sBAAsB,CAAC1K,IAAD,EAAO;IACzB,IAAI8K,QAAQ,GAAG9K,IAAI,CAAC5I,kBAApB;;IACA,IAAI,CAAC0T,QAAL,EAAe;MACX,IAAIC,OAAO,GAAG/K,IAAI,CAACjH,aAAL,GAAqBiH,IAAI,CAACjH,aAAL,CAAmB3B,kBAAxC,GAA6D,IAA3E;;MACA,IAAI2T,OAAJ,EAAa;QACTD,QAAQ,GAAGC,OAAO,CAACC,iBAAnB;MACH;IACJ;;IACD,IAAIF,QAAJ,EAAc;MACV,IAAI5hB,UAAU,CAAC8P,QAAX,CAAoB8R,QAApB,EAA8B,mBAA9B,CAAJ,EACI,OAAOA,QAAP,CADJ,KAGI,OAAO,KAAKJ,sBAAL,CAA4BI,QAA5B,CAAP;IACP,CALD,MAMK;MACD,OAAO,IAAP;IACH;EACJ;;EACD5E,SAAS,GAAG;IACR,OAAO,KAAK+E,wBAAL,KAAkC,IAAzC;EACH;;AAjJkB;;AAmJvBtB,gBAAgB,CAACxe,IAAjB;EAAA,iBAA6Gwe,gBAA7G,EAjhFmGzhB,EAihFnG,mBAA+IoD,SAA/I,GAjhFmGpD,EAihFnG,mBAAqKA,EAAE,CAAC4Y,UAAxK,GAjhFmG5Y,EAihFnG,mBAA+LA,EAAE,CAAC8Y,MAAlM;AAAA;;AACA2I,gBAAgB,CAACnD,IAAjB,kBAlhFmGte,EAkhFnG;EAAA,MAAiGyhB,gBAAjG;EAAA;EAAA;EAAA;IAAA;MAlhFmGzhB,EAkhFnG;QAAA,OAAiG,mBAAjG;MAAA;QAAA,OAAiG,qBAAjG;MAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAnhFmGA,EAmhFnG,mBAA2FyhB,gBAA3F,EAAyH,CAAC;IAC9Gte,IAAI,EAAE1C,SADwG;IAE9G+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECI,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAFwG,CAAD,CAAzH,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAtB,EAA+C;MAAEzV,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA/C,CAAP;EAA6E,CARvH,EAQyI;IAAEzO,IAAI,EAAE,CAAC;MAClIlH,IAAI,EAAE9C,KAD4H;MAElImZ,IAAI,EAAE,CAAC,kBAAD;IAF4H,CAAD,CAAR;IAGzHrO,KAAK,EAAE,CAAC;MACRhI,IAAI,EAAE9C,KADE;MAERmZ,IAAI,EAAE,CAAC,uBAAD;IAFE,CAAD,CAHkH;IAMzHuJ,wBAAwB,EAAE,CAAC;MAC3B5f,IAAI,EAAE9C;IADqB,CAAD,CAN+F;IAQzH6d,OAAO,EAAE,CAAC;MACV/a,IAAI,EAAEzC,YADI;MAEV8Y,IAAI,EAAE,CAAC,OAAD,EAAU,CAAC,QAAD,CAAV;IAFI,CAAD,CARgH;IAWzHqI,SAAS,EAAE,CAAC;MACZ1e,IAAI,EAAEzC,YADM;MAEZ8Y,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;IAFM,CAAD;EAX8G,CARzI;AAAA;;AAuBA,MAAMwJ,mBAAN,CAA0B;EACtBphB,WAAW,CAACmZ,EAAD,EAAKkI,cAAL,EAAqB;IAC5B,KAAKlI,EAAL,GAAUA,EAAV;IACA,KAAKkI,cAAL,GAAsBA,cAAtB;EACH;;EACDhc,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBnC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACoC,OAAL,EAAR;QACI,KAAK,OAAL;UACI,KAAK8b,aAAL,GAAqBle,IAAI,CAACsC,QAA1B;UACA;;QACJ,KAAK,QAAL;UACI,KAAK6b,cAAL,GAAsBne,IAAI,CAACsC,QAA3B;UACA;MANR;IAQH,CATD;EAUH;;AAhBqB;;AAkB1B0b,mBAAmB,CAAC/f,IAApB;EAAA,iBAAgH+f,mBAAhH,EA5jFmGhjB,EA4jFnG,mBAAqJoD,SAArJ,GA5jFmGpD,EA4jFnG,mBAA2KyhB,gBAA3K;AAAA;;AACAuB,mBAAmB,CAAChK,IAApB,kBA7jFmGhZ,EA6jFnG;EAAA,MAAoGgjB,mBAApG;EAAA;EAAA;IAAA;MA7jFmGhjB,EA6jFnG,0BAAqPoB,aAArP;IAAA;;IAAA;MAAA;;MA7jFmGpB,EA6jFnG,qBA7jFmGA,EA6jFnG;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA7jFmGA,EA8jF3F,oFADR;MA7jFmGA,EAikF3F,oFAJR;IAAA;;IAAA;MA7jFmGA,EA8jF5E,+EADvB;MA7jFmGA,EAikF5E,aAJvB;MA7jFmGA,EAikF5E,sGAJvB;IAAA;EAAA;EAAA,eAOiEa,EAAE,CAACqY,IAPpE,EAOqKrY,EAAE,CAACsY,gBAPxK;EAAA;AAAA;;AAQA;EAAA,mDArkFmGnZ,EAqkFnG,mBAA2FgjB,mBAA3F,EAA4H,CAAC;IACjH7f,IAAI,EAAEhD,SAD2G;IAEjHqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,KATmB;MAUCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IAVlC;MAWCC,IAAI,EAAE;QACF,SAAS;MADP;IAXP,CAAD;EAF2G,CAAD,CAA5H,EAiB4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEse;IAAR,CAAtB,CAAP;EAA2D,CAjBrG,EAiBuH;IAAEva,SAAS,EAAE,CAAC;MACrH/D,IAAI,EAAE3C,eAD+G;MAErHgZ,IAAI,EAAE,CAACpY,aAAD;IAF+G,CAAD;EAAb,CAjBvH;AAAA;;AAqBA,MAAMgiB,KAAN,CAAY;EACRxhB,WAAW,CAACmZ,EAAD,EAAK1X,EAAL,EAASE,IAAT,EAAe;IACtB,KAAKwX,EAAL,GAAUA,EAAV;IACA,KAAK1X,EAAL,GAAUA,EAAV;IACA,KAAKE,IAAL,GAAYA,IAAZ;EACH;;EACDse,SAAS,CAAC/W,KAAD,EAAQ;IACb,QAAQA,KAAK,CAAC0V,KAAd;MACI;MACA,KAAK,EAAL;QACI,IAAIqC,OAAO,GAAG,KAAKxf,EAAL,CAAQkK,aAAR,CAAsB2B,kBAApC;;QACA,IAAI2T,OAAJ,EAAa;UACTA,OAAO,CAAChC,KAAR;QACH;;QACD/V,KAAK,CAACkD,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAIqV,OAAO,GAAG,KAAKhgB,EAAL,CAAQkK,aAAR,CAAsB0O,sBAApC;;QACA,IAAIoH,OAAJ,EAAa;UACTA,OAAO,CAACxC,KAAR;QACH;;QACD/V,KAAK,CAACkD,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,KAAK/D,OAAL,CAAapH,IAAb,CAAkBqH,QAAtB,EAAgC;UAC5B,KAAK6Q,EAAL,CAAQuI,cAAR,GAAyBtiB,UAAU,CAAC+D,KAAX,CAAiB,KAAK1B,EAAL,CAAQkK,aAAzB,CAAzB;UACA,KAAKtD,OAAL,CAAapH,IAAb,CAAkBqH,QAAlB,GAA6B,KAA7B;UACA,KAAK6Q,EAAL,CAAQzV,cAAR,CAAuBwB,IAAvB,CAA4B;YACxBoE,aAAa,EAAEJ,KADS;YAExBjI,IAAI,EAAE,KAAKoH,OAAL,CAAapH;UAFK,CAA5B;UAIA,KAAKkY,EAAL,CAAQ/R,qBAAR;UACA,KAAK+R,EAAL,CAAQvX,YAAR,CAAqBV,UAArB,CAAgC,KAAKiY,EAAL,CAAQhY,KAAxC;UACA,KAAKwgB,YAAL;QACH;;QACD;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,CAAC,KAAKtZ,OAAL,CAAapH,IAAb,CAAkBqH,QAAvB,EAAiC;UAC7B,KAAK6Q,EAAL,CAAQuI,cAAR,GAAyBtiB,UAAU,CAAC+D,KAAX,CAAiB,KAAK1B,EAAL,CAAQkK,aAAzB,CAAzB;UACA,KAAKtD,OAAL,CAAapH,IAAb,CAAkBqH,QAAlB,GAA6B,IAA7B;UACA,KAAK6Q,EAAL,CAAQ1V,YAAR,CAAqByB,IAArB,CAA0B;YACtBoE,aAAa,EAAEJ,KADO;YAEtBjI,IAAI,EAAE,KAAKoH,OAAL,CAAapH;UAFG,CAA1B;UAIA,KAAKkY,EAAL,CAAQ/R,qBAAR;UACA,KAAK+R,EAAL,CAAQvX,YAAR,CAAqBV,UAArB,CAAgC,KAAKiY,EAAL,CAAQhY,KAAxC;UACA,KAAKwgB,YAAL;QACH;;QACD;IA5CR;EA8CH;;EACDA,YAAY,GAAG;IACX,KAAKhgB,IAAL,CAAUmZ,iBAAV,CAA4B,MAAM;MAC9BhH,UAAU,CAAC,MAAM;QACb,IAAI0M,GAAG,GAAGphB,UAAU,CAACyO,UAAX,CAAsB,KAAKsL,EAAL,CAAQpN,kBAAR,CAA2BJ,aAAjD,EAAgE,oBAAhE,EAAsFnD,QAAtF,CAA+F,KAAK2Q,EAAL,CAAQuI,cAAvG,CAAV;;QACA,IAAIlB,GAAJ,EAAS;UACLA,GAAG,CAACvB,KAAJ;QACH;MACJ,CALS,EAKP,EALO,CAAV;IAMH,CAPD;EAQH;;AA/DO;;AAiEZuC,KAAK,CAACngB,IAAN;EAAA,iBAAkGmgB,KAAlG,EA3pFmGpjB,EA2pFnG,mBAAyHoD,SAAzH,GA3pFmGpD,EA2pFnG,mBAA+IA,EAAE,CAAC4Y,UAAlJ,GA3pFmG5Y,EA2pFnG,mBAAyKA,EAAE,CAAC8Y,MAA5K;AAAA;;AACAsK,KAAK,CAAC9E,IAAN,kBA5pFmGte,EA4pFnG;EAAA,MAAsFojB,KAAtF;EAAA;EAAA;EAAA;EAAA;IAAA;MA5pFmGpjB,EA4pFnG;QAAA,OAAsF,qBAAtF;MAAA;IAAA;;IAAA;MA5pFmGA,EA4pFnG;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDA7pFmGA,EA6pFnG,mBAA2FojB,KAA3F,EAA8G,CAAC;IACnGjgB,IAAI,EAAE1C,SAD6F;IAEnG+Y,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SADX;MAECI,IAAI,EAAE;QACF,SAAS,WADP;QAEF,mBAAmB;MAFjB;IAFP,CAAD;EAF6F,CAAD,CAA9G,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,EAAsB;MAAED,IAAI,EAAEnD,EAAE,CAAC4Y;IAAX,CAAtB,EAA+C;MAAEzV,IAAI,EAAEnD,EAAE,CAAC8Y;IAAX,CAA/C,CAAP;EAA6E,CATvH,EASyI;IAAE7O,OAAO,EAAE,CAAC;MACrI9G,IAAI,EAAE9C,KAD+H;MAErImZ,IAAI,EAAE,CAAC,OAAD;IAF+H,CAAD,CAAX;IAGzHqI,SAAS,EAAE,CAAC;MACZ1e,IAAI,EAAEzC,YADM;MAEZ8Y,IAAI,EAAE,CAAC,SAAD,EAAY,CAAC,QAAD,CAAZ;IAFM,CAAD;EAH8G,CATzI;AAAA;;AAgBA,MAAMgK,gBAAN,CAAuB;EACnB5hB,WAAW,CAACmZ,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;EACH;;EACDmD,OAAO,CAACpT,KAAD,EAAQ;IACX,KAAKb,OAAL,CAAapH,IAAb,CAAkBqH,QAAlB,GAA6B,CAAC,KAAKD,OAAL,CAAapH,IAAb,CAAkBqH,QAAhD;;IACA,IAAI,KAAKD,OAAL,CAAapH,IAAb,CAAkBqH,QAAtB,EAAgC;MAC5B,KAAK6Q,EAAL,CAAQ1V,YAAR,CAAqByB,IAArB,CAA0B;QACtBoE,aAAa,EAAEJ,KADO;QAEtBjI,IAAI,EAAE,KAAKoH,OAAL,CAAapH;MAFG,CAA1B;IAIH,CALD,MAMK;MACD,KAAKkY,EAAL,CAAQzV,cAAR,CAAuBwB,IAAvB,CAA4B;QACxBoE,aAAa,EAAEJ,KADS;QAExBjI,IAAI,EAAE,KAAKoH,OAAL,CAAapH;MAFK,CAA5B;IAIH;;IACD,KAAKkY,EAAL,CAAQ/R,qBAAR;IACA,KAAK+R,EAAL,CAAQvX,YAAR,CAAqBV,UAArB,CAAgC,KAAKiY,EAAL,CAAQhY,KAAxC;IACA+H,KAAK,CAACkD,cAAN;EACH;;AArBkB;;AAuBvBwV,gBAAgB,CAACvgB,IAAjB;EAAA,iBAA6GugB,gBAA7G,EApsFmGxjB,EAosFnG,mBAA+IoD,SAA/I;AAAA;;AACAogB,gBAAgB,CAACxK,IAAjB,kBArsFmGhZ,EAqsFnG;EAAA,MAAiGwjB,gBAAjG;EAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MArsFmGxjB,EAssF3F,+BADR;MArsFmGA,EAssFlC;QAAA,OAAS,mBAAT;MAAA,EADjE;MArsFmGA,EAwsFvF,qBAHZ;MArsFmGA,EAysF3F,eAJR;IAAA;;IAAA;MArsFmGA,EAusFvF,iMAFZ;MArsFmGA,EAwsFpF,aAHf;MArsFmGA,EAwsFpF,4GAHf;IAAA;EAAA;EAAA,eAKiEa,EAAE,CAACoY,OALpE,EAK+J1X,EAAE,CAACkiB,MALlK;EAAA;AAAA;;AAMA;EAAA,mDA3sFmGzjB,EA2sFnG,mBAA2FwjB,gBAA3F,EAAyH,CAAC;IAC9GrgB,IAAI,EAAEhD,SADwG;IAE9GqZ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECnS,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA,KAPmB;MAQCqS,aAAa,EAAEvZ,iBAAiB,CAACwZ,IARlC;MASCC,IAAI,EAAE;QACF,SAAS;MADP;IATP,CAAD;EAFwG,CAAD,CAAzH,EAe4B,YAAY;IAAE,OAAO,CAAC;MAAE1W,IAAI,EAAEC;IAAR,CAAD,CAAP;EAA+B,CAfzE,EAe2F;IAAE6G,OAAO,EAAE,CAAC;MACvF9G,IAAI,EAAE9C;IADiF,CAAD;EAAX,CAf3F;AAAA;;AAkBA,MAAMqjB,eAAN,CAAsB;;AAEtBA,eAAe,CAACzgB,IAAhB;EAAA,iBAA4GygB,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAhuFmG3jB,EAguFnG;EAAA,MAA6G0jB;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBAjuFmG5jB,EAiuFnG;EAAA,UAAwIc,YAAxI,EAAsJI,eAAtJ,EAAuKM,YAAvK,EAAqLE,cAArL,EAAqML,YAArM,EAAmNK,cAAnN;AAAA;;AACA;EAAA,mDAluFmG1B,EAkuFnG,mBAA2F0jB,eAA3F,EAAwH,CAAC;IAC7GvgB,IAAI,EAAEvC,QADuG;IAE7G4Y,IAAI,EAAE,CAAC;MACCqK,OAAO,EAAE,CAAC/iB,YAAD,EAAeI,eAAf,EAAgCM,YAAhC,EAA8CE,cAA9C,CADV;MAECoiB,OAAO,EAAE,CAAC1gB,SAAD,EAAY/B,YAAZ,EAA0BmiB,gBAA1B,EAA4CzF,gBAA5C,EAA8DQ,UAA9D,EAA0EM,iBAA1E,EAA6FuE,KAA7F,EAAoGzD,mBAApG,EAAyHY,eAAzH,EAA0II,uBAA1I,EAAmKC,gBAAnK,EAAqLG,UAArL,EAAiMK,gBAAjM,EAAmNK,gBAAnN,EAAqOuB,mBAArO,EAA0PthB,cAA1P,CAFV;MAGCqiB,YAAY,EAAE,CAAC3gB,SAAD,EAAYogB,gBAAZ,EAA8BlK,gBAA9B,EAAgDC,MAAhD,EAAwDwE,gBAAxD,EAA0EQ,UAA1E,EAAsFM,iBAAtF,EAAyGuE,KAAzG,EAAgHzD,mBAAhH,EAAqIY,eAArI,EAAsJI,uBAAtJ,EAA+KC,gBAA/K,EAAiMG,UAAjM,EAA6MK,gBAA7M,EAA+NK,gBAA/N,EAAiPuB,mBAAjP;IAHf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;;;AAEA,SAASzJ,MAAT,EAAiBwH,UAAjB,EAA6BH,gBAA7B,EAA+Ca,gBAA/C,EAAiEL,gBAAjE,EAAmFzB,mBAAnF,EAAwGd,iBAAxG,EAA2HuE,KAA3H,EAAkI9J,gBAAlI,EAAoJiH,eAApJ,EAAqKI,uBAArK,EAA8LpC,UAA9L,EAA0MR,gBAA1M,EAA4N3a,SAA5N,EAAuO4f,mBAAvO,EAA4PU,eAA5P,EAA6Q/hB,gBAA7Q,EAA+R6hB,gBAA/R"}, "metadata": {}, "sourceType": "module"}