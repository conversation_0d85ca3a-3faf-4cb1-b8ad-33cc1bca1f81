<!DOCTYPE html>
<html>
<head>
    <title>Debug Frontend Issues</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; border-radius: 3px; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { padding: 8px; width: 200px; border: 1px solid #ccc; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔍 Debug Frontend Issues</h1>
    
    <div class="section">
        <h3>1. Test Backend Connection</h3>
        <button onclick="testBackendConnection()">Test Connection</button>
        <div id="connectionResult"></div>
    </div>

    <div class="section">
        <h3>2. Test Create Trainer (Simulate Frontend)</h3>
        <div class="form-group">
            <label>First Name:</label>
            <input type="text" id="firstName" value="John" />
        </div>
        <div class="form-group">
            <label>Last Name:</label>
            <input type="text" id="lastName" value="Doe" />
        </div>
        <div class="form-group">
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" />
        </div>
        <div class="form-group">
            <label>Phone:</label>
            <input type="text" id="phone" value="1234567890" />
        </div>
        <div class="form-group">
            <label>Speciality:</label>
            <input type="text" id="specialite" value="Web Development" />
        </div>
        <button onclick="testCreateTrainer()">Create Trainer</button>
        <div id="createResult"></div>
    </div>

    <div class="section">
        <h3>3. Test Get Trainers</h3>
        <button onclick="testGetTrainers()">Get All Trainers</button>
        <div id="getResult"></div>
    </div>

    <div class="section">
        <h3>4. Network & CORS Check</h3>
        <button onclick="testCORS()">Test CORS</button>
        <div id="corsResult"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000/api';

        async function testBackendConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div>Testing connection...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/users`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Backend connection successful</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Backend responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
            }
        }

        async function testCreateTrainer() {
            const resultDiv = document.getElementById('createResult');
            resultDiv.innerHTML = '<div>Creating trainer...</div>';
            
            const trainerData = {
                first_name: document.getElementById('firstName').value,
                last_name: document.getElementById('lastName').value,
                email: document.getElementById('email').value,
                password: 'defaultPassword123',
                role: 'formateur',
                phone: document.getElementById('phone').value || undefined,
                specialite: document.getElementById('specialite').value || undefined
            };

            console.log('Sending trainer data:', trainerData);

            try {
                const response = await fetch(`${baseUrl}/users`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(trainerData)
                });
                
                const responseData = await response.json();
                console.log('Response:', responseData);
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Trainer created successfully!</div>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Failed to create trainer (Status: ${response.status})</div>
                        <pre>${JSON.stringify(responseData, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        async function testGetTrainers() {
            const resultDiv = document.getElementById('getResult');
            resultDiv.innerHTML = '<div>Fetching trainers...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/users?role=formateur`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Trainers fetched successfully (${data.length} found)</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Failed to fetch trainers (Status: ${response.status})</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }

        async function testCORS() {
            const resultDiv = document.getElementById('corsResult');
            resultDiv.innerHTML = '<div>Testing CORS...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/users`, {
                    method: 'OPTIONS'
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };
                
                resultDiv.innerHTML = `
                    <div class="success">✅ CORS headers:</div>
                    <pre>${JSON.stringify(corsHeaders, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ CORS test failed: ${error.message}</div>`;
            }
        }

        // Auto-run connection test
        window.onload = function() {
            testBackendConnection();
        };
    </script>
</body>
</html>
