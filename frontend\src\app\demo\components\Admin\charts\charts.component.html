<div class="grid" *ngIf="!loading; else loadingTemplate">
    <!-- Dashboard Stats Cards -->
    <div class="col-12" *ngIf="dashboardStats">
        <div class="grid">
            <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Total Formations</span>
                            <div class="text-900 font-medium text-xl">{{dashboardStats.totalFormations}}</div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-blue-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-book text-blue-500 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Total Employees</span>
                            <div class="text-900 font-medium text-xl">{{dashboardStats.totalEmployees}}</div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-orange-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-users text-orange-500 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Total Teams</span>
                            <div class="text-900 font-medium text-xl">{{dashboardStats.totalTeams}}</div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-cyan-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-sitemap text-cyan-500 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 md:col-6 lg:col-3">
                <div class="card mb-0">
                    <div class="flex justify-content-between mb-3">
                        <div>
                            <span class="block text-500 font-medium mb-3">Attendance Rate</span>
                            <div class="text-900 font-medium text-xl">{{dashboardStats.globalAttendanceRate}}%</div>
                        </div>
                        <div class="flex align-items-center justify-content-center bg-purple-100 border-round" style="width:2.5rem;height:2.5rem">
                            <i class="pi pi-chart-line text-purple-500 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 1 -->
    <div class="col-12 lg:col-6">
        <div class="card">
            <h5>Formation Attendance Trends</h5>
            <p-chart type="line" [data]="lineData" [options]="lineOptions"></p-chart>
        </div>
    </div>

    <div class="col-12 lg:col-6">
        <div class="card">
            <h5>Team Performance</h5>
            <p-chart type="bar" [data]="barData" [options]="barOptions"></p-chart>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="col-12 lg:col-6">
        <div class="card flex flex-column align-items-center">
            <h5 class="text-left w-full">Formation Status Distribution</h5>
            <p-chart type="pie" [data]="pieData" [options]="pieOptions" [style]="{'width': '70%'}"></p-chart>
        </div>
    </div>

    <div class="col-12 lg:col-6">
        <div class="card flex flex-column align-items-center">
            <h5 class="text-left w-full">Monthly Formations</h5>
            <p-chart type="doughnut" [data]="polarData" [options]="polarOptions" [style]="{'width': '70%'}"></p-chart>
        </div>
    </div>

    <!-- Charts Row 3 -->
    <div class="col-12">
        <div class="card">
            <h5>Team Speciality Analysis</h5>
            <p-chart type="radar" [data]="radarData" [options]="radarOptions"></p-chart>
        </div>
    </div>
</div>

<ng-template #loadingTemplate>
    <div class="col-12">
        <div class="card">
            <div class="flex align-items-center justify-content-center" style="height: 400px;">
                <p-progressSpinner></p-progressSpinner>
                <span class="ml-3">Loading charts...</span>
            </div>
        </div>
    </div>
</ng-template>
