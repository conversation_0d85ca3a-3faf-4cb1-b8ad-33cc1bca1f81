{"realm": "formation", "enabled": true, "displayName": "Formation Management System", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Formation Management</span></div>", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"name": "admin", "description": "Administrator role with full access", "composite": false, "clientRole": false, "containerId": "formation"}, {"name": "trainer", "description": "Trainer role for managing formations", "composite": false, "clientRole": false, "containerId": "formation"}, {"name": "employee", "description": "Employee role for participating in formations", "composite": false, "clientRole": false, "containerId": "formation"}]}, "groups": [{"name": "Administrators", "path": "/Administrators", "realmRoles": ["admin"]}, {"name": "Trainers", "path": "/Trainers", "realmRoles": ["trainer"]}, {"name": "Employees", "path": "/Employees", "realmRoles": ["employee"]}], "users": [{"username": "admin", "enabled": true, "emailVerified": true, "firstName": "System", "lastName": "Administrator", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "admin123", "temporary": false}], "realmRoles": ["admin"], "groups": ["/Administrators"], "attributes": {"team": ["Administration"], "phone": ["+1234567890"]}}, {"username": "trainer", "enabled": true, "emailVerified": true, "firstName": "<PERSON>", "lastName": "Trainer", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "trainer123", "temporary": false}], "realmRoles": ["trainer"], "groups": ["/Trainers"], "attributes": {"team": ["Development"], "phone": ["+1234567891"], "specialite": ["Angular & React"], "room": ["Room A1"]}}, {"username": "employee", "enabled": true, "emailVerified": true, "firstName": "<PERSON>", "lastName": "Employee", "email": "<EMAIL>", "credentials": [{"type": "password", "value": "employee123", "temporary": false}], "realmRoles": ["employee"], "groups": ["/Employees"], "attributes": {"team": ["Development"], "phone": ["+1234567892"]}}], "clients": [{"clientId": "formation-frontend", "name": "Formation Frontend Application", "description": "Angular frontend application for formation management", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "formation-frontend-secret", "redirectUris": ["http://localhost:4200/*", "http://localhost:4200/auth/callback"], "webOrigins": ["http://localhost:4200"], "protocol": "openid-connect", "publicClient": true, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "authorizationServicesEnabled": false, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"name": "role-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "role", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String", "multivalued": "true"}}, {"name": "team-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "team", "access.token.claim": "true", "claim.name": "team", "jsonType.label": "String"}}, {"name": "phone-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "phone", "access.token.claim": "true", "claim.name": "phone", "jsonType.label": "String"}}, {"name": "specialite-mapper", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"user.attribute": "specialite", "access.token.claim": "true", "claim.name": "specialite", "jsonType.label": "String"}}]}, {"clientId": "formation-backend", "name": "Formation Backend API", "description": "Laravel backend API for formation management", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "formation-backend-secret", "bearerOnly": true, "protocol": "openid-connect", "publicClient": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "fullScopeAllowed": true}], "clientScopes": [{"name": "formation-scope", "description": "Formation management scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true"}, "protocolMappers": [{"name": "formation-audience", "protocol": "openid-connect", "protocolMapper": "oidc-audience-mapper", "config": {"included.client.audience": "formation-backend", "access.token.claim": "true"}}]}]}