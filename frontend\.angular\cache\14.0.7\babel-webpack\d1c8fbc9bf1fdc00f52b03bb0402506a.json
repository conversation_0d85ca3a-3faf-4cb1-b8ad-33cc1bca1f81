{"ast": null, "code": "export function executeSchedule(parentSubscription, scheduler, work, delay = 0, repeat = false) {\n  const scheduleSubscription = scheduler.schedule(function () {\n    work();\n\n    if (repeat) {\n      parentSubscription.add(this.schedule(null, delay));\n    } else {\n      this.unsubscribe();\n    }\n  }, delay);\n  parentSubscription.add(scheduleSubscription);\n\n  if (!repeat) {\n    return scheduleSubscription;\n  }\n}", "map": {"version": 3, "names": ["executeSchedule", "parentSubscription", "scheduler", "work", "delay", "repeat", "scheduleSubscription", "schedule", "add", "unsubscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/executeSchedule.js"], "sourcesContent": ["export function executeSchedule(parentSubscription, scheduler, work, delay = 0, repeat = false) {\n    const scheduleSubscription = scheduler.schedule(function () {\n        work();\n        if (repeat) {\n            parentSubscription.add(this.schedule(null, delay));\n        }\n        else {\n            this.unsubscribe();\n        }\n    }, delay);\n    parentSubscription.add(scheduleSubscription);\n    if (!repeat) {\n        return scheduleSubscription;\n    }\n}\n"], "mappings": "AAAA,OAAO,SAASA,eAAT,CAAyBC,kBAAzB,EAA6CC,SAA7C,EAAwDC,IAAxD,EAA8DC,KAAK,GAAG,CAAtE,EAAyEC,MAAM,GAAG,KAAlF,EAAyF;EAC5F,MAAMC,oBAAoB,GAAGJ,SAAS,CAACK,QAAV,CAAmB,YAAY;IACxDJ,IAAI;;IACJ,IAAIE,MAAJ,EAAY;MACRJ,kBAAkB,CAACO,GAAnB,CAAuB,KAAKD,QAAL,CAAc,IAAd,EAAoBH,KAApB,CAAvB;IACH,CAFD,MAGK;MACD,KAAKK,WAAL;IACH;EACJ,CAR4B,EAQ1BL,KAR0B,CAA7B;EASAH,kBAAkB,CAACO,GAAnB,CAAuBF,oBAAvB;;EACA,IAAI,CAACD,MAAL,EAAa;IACT,OAAOC,oBAAP;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}