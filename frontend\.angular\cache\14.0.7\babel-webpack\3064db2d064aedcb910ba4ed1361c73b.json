{"ast": null, "code": "import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** The injection token used to specify the virtual scrolling strategy. */\n\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\n\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n\n\n  attach(viewport) {\n    this._viewport = viewport;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n\n\n  detach() {\n    this._scrolledIndexChange.complete();\n\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n\n\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n\n\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n\n\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n\n\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n\n\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n\n    const renderedRange = this._viewport.getRenderedRange();\n\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n\n    const viewportSize = this._viewport.getViewportSize();\n\n    const dataLength = this._viewport.getDataLength();\n\n    let scrollOffset = this._viewport.measureScrollOffset(); // Prevent NaN as result when dividing by zero.\n\n\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0; // If user scrolls to the bottom of the list and data changes to a smaller list\n\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems)); // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n\n    this._viewport.setRenderedRange(newRange);\n\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\n\n\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\n\n\nclass CdkFixedSizeVirtualScroll {\n  constructor() {\n    this._itemSize = 20;\n    this._minBufferPx = 100;\n    this._maxBufferPx = 200;\n    /** The scroll strategy used by this directive. */\n\n    this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n  /** The size of the items in the list (in pixels). */\n\n\n  get itemSize() {\n    return this._itemSize;\n  }\n\n  set itemSize(value) {\n    this._itemSize = coerceNumberProperty(value);\n  }\n  /**\n   * The minimum amount of buffer rendered beyond the viewport (in pixels).\n   * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n   */\n\n\n  get minBufferPx() {\n    return this._minBufferPx;\n  }\n\n  set minBufferPx(value) {\n    this._minBufferPx = coerceNumberProperty(value);\n  }\n  /**\n   * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n   */\n\n\n  get maxBufferPx() {\n    return this._maxBufferPx;\n  }\n\n  set maxBufferPx(value) {\n    this._maxBufferPx = coerceNumberProperty(value);\n  }\n\n  ngOnChanges() {\n    this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n  }\n\n}\n\nCdkFixedSizeVirtualScroll.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n  return new (t || CdkFixedSizeVirtualScroll)();\n};\n\nCdkFixedSizeVirtualScroll.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkFixedSizeVirtualScroll,\n  selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n  inputs: {\n    itemSize: \"itemSize\",\n    minBufferPx: \"minBufferPx\",\n    maxBufferPx: \"maxBufferPx\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: VIRTUAL_SCROLL_STRATEGY,\n    useFactory: _fixedSizeVirtualScrollStrategyFactory,\n    deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkFixedSizeVirtualScroll, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport[itemSize]',\n      providers: [{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]\n    }]\n  }], null, {\n    itemSize: [{\n      type: Input\n    }],\n    minBufferPx: [{\n      type: Input\n    }],\n    maxBufferPx: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Time in ms to throttle the scrolling events by default. */\n\n\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\n\nclass ScrollDispatcher {\n  constructor(_ngZone, _platform, document) {\n    this._ngZone = _ngZone;\n    this._platform = _platform;\n    /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n\n    this._scrolled = new Subject();\n    /** Keeps track of the global `scroll` and `resize` subscriptions. */\n\n    this._globalSubscription = null;\n    /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n\n    this._scrolledCount = 0;\n    /**\n     * Map of all the scrollable references that are registered with the service and their\n     * scroll event subscriptions.\n     */\n\n    this.scrollContainers = new Map();\n    this._document = document;\n  }\n  /**\n   * Registers a scrollable instance with the service and listens for its scrolled events. When the\n   * scrollable is scrolled, the service emits the event to its scrolled observable.\n   * @param scrollable Scrollable instance to be registered.\n   */\n\n\n  register(scrollable) {\n    if (!this.scrollContainers.has(scrollable)) {\n      this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n    }\n  }\n  /**\n   * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n   * @param scrollable Scrollable instance to be deregistered.\n   */\n\n\n  deregister(scrollable) {\n    const scrollableReference = this.scrollContainers.get(scrollable);\n\n    if (scrollableReference) {\n      scrollableReference.unsubscribe();\n      this.scrollContainers.delete(scrollable);\n    }\n  }\n  /**\n   * Returns an observable that emits an event whenever any of the registered Scrollable\n   * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n   * to override the default \"throttle\" time.\n   *\n   * **Note:** in order to avoid hitting change detection for every scroll event,\n   * all of the events emitted from this stream will be run outside the Angular zone.\n   * If you need to update any data bindings as a result of a scroll event, you have\n   * to run the callback using `NgZone.run`.\n   */\n\n\n  scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n    if (!this._platform.isBrowser) {\n      return of();\n    }\n\n    return new Observable(observer => {\n      if (!this._globalSubscription) {\n        this._addGlobalListener();\n      } // In the case of a 0ms delay, use an observable without auditTime\n      // since it does add a perceptible delay in processing overhead.\n\n\n      const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n      this._scrolledCount++;\n      return () => {\n        subscription.unsubscribe();\n        this._scrolledCount--;\n\n        if (!this._scrolledCount) {\n          this._removeGlobalListener();\n        }\n      };\n    });\n  }\n\n  ngOnDestroy() {\n    this._removeGlobalListener();\n\n    this.scrollContainers.forEach((_, container) => this.deregister(container));\n\n    this._scrolled.complete();\n  }\n  /**\n   * Returns an observable that emits whenever any of the\n   * scrollable ancestors of an element are scrolled.\n   * @param elementOrElementRef Element whose ancestors to listen for.\n   * @param auditTimeInMs Time to throttle the scroll events.\n   */\n\n\n  ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n    const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n    return this.scrolled(auditTimeInMs).pipe(filter(target => {\n      return !target || ancestors.indexOf(target) > -1;\n    }));\n  }\n  /** Returns all registered Scrollables that contain the provided element. */\n\n\n  getAncestorScrollContainers(elementOrElementRef) {\n    const scrollingContainers = [];\n    this.scrollContainers.forEach((_subscription, scrollable) => {\n      if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n        scrollingContainers.push(scrollable);\n      }\n    });\n    return scrollingContainers;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Returns true if the element is contained within the provided Scrollable. */\n\n\n  _scrollableContainsElement(scrollable, elementOrElementRef) {\n    let element = coerceElement(elementOrElementRef);\n    let scrollableElement = scrollable.getElementRef().nativeElement; // Traverse through the element parents until we reach null, checking if any of the elements\n    // are the scrollable's element.\n\n    do {\n      if (element == scrollableElement) {\n        return true;\n      }\n    } while (element = element.parentElement);\n\n    return false;\n  }\n  /** Sets up the global scroll listeners. */\n\n\n  _addGlobalListener() {\n    this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n      const window = this._getWindow();\n\n      return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n    });\n  }\n  /** Cleans up the global scroll listener. */\n\n\n  _removeGlobalListener() {\n    if (this._globalSubscription) {\n      this._globalSubscription.unsubscribe();\n\n      this._globalSubscription = null;\n    }\n  }\n\n}\n\nScrollDispatcher.ɵfac = function ScrollDispatcher_Factory(t) {\n  return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n};\n\nScrollDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ScrollDispatcher,\n  factory: ScrollDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: i1.Platform\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\n\n\nclass CdkScrollable {\n  constructor(elementRef, scrollDispatcher, ngZone, dir) {\n    this.elementRef = elementRef;\n    this.scrollDispatcher = scrollDispatcher;\n    this.ngZone = ngZone;\n    this.dir = dir;\n    this._destroyed = new Subject();\n    this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n  }\n\n  ngOnInit() {\n    this.scrollDispatcher.register(this);\n  }\n\n  ngOnDestroy() {\n    this.scrollDispatcher.deregister(this);\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Returns observable that emits when a scroll event is fired on the host element. */\n\n\n  elementScrolled() {\n    return this._elementScrolled;\n  }\n  /** Gets the ElementRef for the viewport. */\n\n\n  getElementRef() {\n    return this.elementRef;\n  }\n  /**\n   * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n   * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param options specified the offsets to scroll to.\n   */\n\n\n  scrollTo(options) {\n    const el = this.elementRef.nativeElement;\n    const isRtl = this.dir && this.dir.value == 'rtl'; // Rewrite start & end offsets as right or left offsets.\n\n    if (options.left == null) {\n      options.left = isRtl ? options.end : options.start;\n    }\n\n    if (options.right == null) {\n      options.right = isRtl ? options.start : options.end;\n    } // Rewrite the bottom offset as a top offset.\n\n\n    if (options.bottom != null) {\n      options.top = el.scrollHeight - el.clientHeight - options.bottom;\n    } // Rewrite the right offset as a left offset.\n\n\n    if (isRtl && getRtlScrollAxisType() != 0\n    /* RtlScrollAxisType.NORMAL */\n    ) {\n      if (options.left != null) {\n        options.right = el.scrollWidth - el.clientWidth - options.left;\n      }\n\n      if (getRtlScrollAxisType() == 2\n      /* RtlScrollAxisType.INVERTED */\n      ) {\n        options.left = options.right;\n      } else if (getRtlScrollAxisType() == 1\n      /* RtlScrollAxisType.NEGATED */\n      ) {\n        options.left = options.right ? -options.right : options.right;\n      }\n    } else {\n      if (options.right != null) {\n        options.left = el.scrollWidth - el.clientWidth - options.right;\n      }\n    }\n\n    this._applyScrollToOptions(options);\n  }\n\n  _applyScrollToOptions(options) {\n    const el = this.elementRef.nativeElement;\n\n    if (supportsScrollBehavior()) {\n      el.scrollTo(options);\n    } else {\n      if (options.top != null) {\n        el.scrollTop = options.top;\n      }\n\n      if (options.left != null) {\n        el.scrollLeft = options.left;\n      }\n    }\n  }\n  /**\n   * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n   * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n   * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n   * left and right always refer to the left and right side of the scrolling container irrespective\n   * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n   * in an RTL context.\n   * @param from The edge to measure from.\n   */\n\n\n  measureScrollOffset(from) {\n    const LEFT = 'left';\n    const RIGHT = 'right';\n    const el = this.elementRef.nativeElement;\n\n    if (from == 'top') {\n      return el.scrollTop;\n    }\n\n    if (from == 'bottom') {\n      return el.scrollHeight - el.clientHeight - el.scrollTop;\n    } // Rewrite start & end as left or right offsets.\n\n\n    const isRtl = this.dir && this.dir.value == 'rtl';\n\n    if (from == 'start') {\n      from = isRtl ? RIGHT : LEFT;\n    } else if (from == 'end') {\n      from = isRtl ? LEFT : RIGHT;\n    }\n\n    if (isRtl && getRtlScrollAxisType() == 2\n    /* RtlScrollAxisType.INVERTED */\n    ) {\n      // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      } else {\n        return el.scrollLeft;\n      }\n    } else if (isRtl && getRtlScrollAxisType() == 1\n    /* RtlScrollAxisType.NEGATED */\n    ) {\n      // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n      // 0 when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft + el.scrollWidth - el.clientWidth;\n      } else {\n        return -el.scrollLeft;\n      }\n    } else {\n      // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n      // (scrollWidth - clientWidth) when scrolled all the way right.\n      if (from == LEFT) {\n        return el.scrollLeft;\n      } else {\n        return el.scrollWidth - el.clientWidth - el.scrollLeft;\n      }\n    }\n  }\n\n}\n\nCdkScrollable.ɵfac = function CdkScrollable_Factory(t) {\n  return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n};\n\nCdkScrollable.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkScrollable,\n  selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollable, [{\n    type: Directive,\n    args: [{\n      selector: '[cdk-scrollable], [cdkScrollable]'\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n/** Time in ms to throttle the resize events by default. */\n\n\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\n\nclass ViewportRuler {\n  constructor(_platform, ngZone, document) {\n    this._platform = _platform;\n    /** Stream of viewport change events. */\n\n    this._change = new Subject();\n    /** Event listener that will be used to handle the viewport change events. */\n\n    this._changeListener = event => {\n      this._change.next(event);\n    };\n\n    this._document = document;\n    ngZone.runOutsideAngular(() => {\n      if (_platform.isBrowser) {\n        const window = this._getWindow(); // Note that bind the events ourselves, rather than going through something like RxJS's\n        // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n\n\n        window.addEventListener('resize', this._changeListener);\n        window.addEventListener('orientationchange', this._changeListener);\n      } // Clear the cached position so that the viewport is re-measured next time it is required.\n      // We don't need to keep track of the subscription, because it is completed on destroy.\n\n\n      this.change().subscribe(() => this._viewportSize = null);\n    });\n  }\n\n  ngOnDestroy() {\n    if (this._platform.isBrowser) {\n      const window = this._getWindow();\n\n      window.removeEventListener('resize', this._changeListener);\n      window.removeEventListener('orientationchange', this._changeListener);\n    }\n\n    this._change.complete();\n  }\n  /** Returns the viewport's width and height. */\n\n\n  getViewportSize() {\n    if (!this._viewportSize) {\n      this._updateViewportSize();\n    }\n\n    const output = {\n      width: this._viewportSize.width,\n      height: this._viewportSize.height\n    }; // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n\n    if (!this._platform.isBrowser) {\n      this._viewportSize = null;\n    }\n\n    return output;\n  }\n  /** Gets a ClientRect for the viewport's bounds. */\n\n\n  getViewportRect() {\n    // Use the document element's bounding rect rather than the window scroll properties\n    // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n    // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n    // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n    // can disagree when the page is pinch-zoomed (on devices that support touch).\n    // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n    // We use the documentElement instead of the body because, by default (without a css reset)\n    // browsers typically give the document body an 8px margin, which is not included in\n    // getBoundingClientRect().\n    const scrollPosition = this.getViewportScrollPosition();\n    const {\n      width,\n      height\n    } = this.getViewportSize();\n    return {\n      top: scrollPosition.top,\n      left: scrollPosition.left,\n      bottom: scrollPosition.top + height,\n      right: scrollPosition.left + width,\n      height,\n      width\n    };\n  }\n  /** Gets the (top, left) scroll position of the viewport. */\n\n\n  getViewportScrollPosition() {\n    // While we can get a reference to the fake document\n    // during SSR, it doesn't have getBoundingClientRect.\n    if (!this._platform.isBrowser) {\n      return {\n        top: 0,\n        left: 0\n      };\n    } // The top-left-corner of the viewport is determined by the scroll position of the document\n    // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n    // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n    // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n    // `document.documentElement` works consistently, where the `top` and `left` values will\n    // equal negative the scroll position.\n\n\n    const document = this._document;\n\n    const window = this._getWindow();\n\n    const documentElement = document.documentElement;\n    const documentRect = documentElement.getBoundingClientRect();\n    const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n    const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n    return {\n      top,\n      left\n    };\n  }\n  /**\n   * Returns a stream that emits whenever the size of the viewport changes.\n   * This stream emits outside of the Angular zone.\n   * @param throttleTime Time in milliseconds to throttle the stream.\n   */\n\n\n  change(throttleTime = DEFAULT_RESIZE_TIME) {\n    return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n\n\n  _getWindow() {\n    return this._document.defaultView || window;\n  }\n  /** Updates the cached viewport size. */\n\n\n  _updateViewportSize() {\n    const window = this._getWindow();\n\n    this._viewportSize = this._platform.isBrowser ? {\n      width: window.innerWidth,\n      height: window.innerHeight\n    } : {\n      width: 0,\n      height: 0\n    };\n  }\n\n}\n\nViewportRuler.ɵfac = function ViewportRuler_Factory(t) {\n  return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n};\n\nViewportRuler.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ViewportRuler,\n  factory: ViewportRuler.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ViewportRuler, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i1.Platform\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/** Checks if the given ranges are equal. */\n\n\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\n\n\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\n\nclass CdkVirtualScrollViewport extends CdkScrollable {\n  constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler) {\n    super(elementRef, scrollDispatcher, ngZone, dir);\n    this.elementRef = elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollStrategy = _scrollStrategy;\n    /** Emits when the viewport is detached from a CdkVirtualForOf. */\n\n    this._detachedSubject = new Subject();\n    /** Emits when the rendered range changes. */\n\n    this._renderedRangeSubject = new Subject();\n    this._orientation = 'vertical';\n    this._appendOnly = false; // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n    // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n    // depending on how the strategy calculates the scrolled index, it may come at a cost to\n    // performance.\n\n    /** Emits when the index of the first element visible in the viewport changes. */\n\n    this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n    /** A stream that emits whenever the rendered range changes. */\n\n    this.renderedRangeStream = this._renderedRangeSubject;\n    /**\n     * The total size of all content (in pixels), including content that is not currently rendered.\n     */\n\n    this._totalContentSize = 0;\n    /** A string representing the `style.width` property value to be used for the spacer element. */\n\n    this._totalContentWidth = '';\n    /** A string representing the `style.height` property value to be used for the spacer element. */\n\n    this._totalContentHeight = '';\n    /** The currently rendered range of indices. */\n\n    this._renderedRange = {\n      start: 0,\n      end: 0\n    };\n    /** The length of the data bound to this viewport (in number of items). */\n\n    this._dataLength = 0;\n    /** The size of the viewport (in pixels). */\n\n    this._viewportSize = 0;\n    /** The last rendered content offset that was set. */\n\n    this._renderedContentOffset = 0;\n    /**\n     * Whether the last rendered content offset was to the end of the content (and therefore needs to\n     * be rewritten as an offset to the start of the content).\n     */\n\n    this._renderedContentOffsetNeedsRewrite = false;\n    /** Whether there is a pending change detection cycle. */\n\n    this._isChangeDetectionPending = false;\n    /** A list of functions to run after the next change detection cycle. */\n\n    this._runAfterChangeDetection = [];\n    /** Subscription to changes in the viewport size. */\n\n    this._viewportChanges = Subscription.EMPTY;\n\n    if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n    }\n\n    this._viewportChanges = viewportRuler.change().subscribe(() => {\n      this.checkViewportSize();\n    });\n  }\n  /** The direction the viewport scrolls. */\n\n\n  get orientation() {\n    return this._orientation;\n  }\n\n  set orientation(orientation) {\n    if (this._orientation !== orientation) {\n      this._orientation = orientation;\n\n      this._calculateSpacerSize();\n    }\n  }\n  /**\n   * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n   * will be removed.\n   */\n\n\n  get appendOnly() {\n    return this._appendOnly;\n  }\n\n  set appendOnly(value) {\n    this._appendOnly = coerceBooleanProperty(value);\n  }\n\n  ngOnInit() {\n    super.ngOnInit(); // It's still too early to measure the viewport at this point. Deferring with a promise allows\n    // the Viewport to be rendered with the correct size before we measure. We run this outside the\n    // zone to avoid causing more change detection cycles. We handle the change detection loop\n    // ourselves instead.\n\n    this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n      this._measureViewportSize();\n\n      this._scrollStrategy.attach(this);\n\n      this.elementScrolled().pipe( // Start off with a fake scroll event so we properly detect our initial position.\n      startWith(null), // Collect multiple events into one until the next animation frame. This way if\n      // there are multiple scroll events in the same frame we only need to recheck\n      // our layout once.\n      auditTime(0, SCROLL_SCHEDULER)).subscribe(() => this._scrollStrategy.onContentScrolled());\n\n      this._markChangeDetectionNeeded();\n    }));\n  }\n\n  ngOnDestroy() {\n    this.detach();\n\n    this._scrollStrategy.detach(); // Complete all subjects\n\n\n    this._renderedRangeSubject.complete();\n\n    this._detachedSubject.complete();\n\n    this._viewportChanges.unsubscribe();\n\n    super.ngOnDestroy();\n  }\n  /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n\n\n  attach(forOf) {\n    if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CdkVirtualScrollViewport is already attached.');\n    } // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n    // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n    // change detection loop ourselves.\n\n\n    this.ngZone.runOutsideAngular(() => {\n      this._forOf = forOf;\n\n      this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n        const newLength = data.length;\n\n        if (newLength !== this._dataLength) {\n          this._dataLength = newLength;\n\n          this._scrollStrategy.onDataLengthChanged();\n        }\n\n        this._doChangeDetection();\n      });\n    });\n  }\n  /** Detaches the current `CdkVirtualForOf`. */\n\n\n  detach() {\n    this._forOf = null;\n\n    this._detachedSubject.next();\n  }\n  /** Gets the length of the data bound to this viewport (in number of items). */\n\n\n  getDataLength() {\n    return this._dataLength;\n  }\n  /** Gets the size of the viewport (in pixels). */\n\n\n  getViewportSize() {\n    return this._viewportSize;\n  } // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n  // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n  // setting it to something else, but its error prone and should probably be split into\n  // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n\n  /** Get the current rendered range of items. */\n\n\n  getRenderedRange() {\n    return this._renderedRange;\n  }\n  /**\n   * Sets the total size of all content (in pixels), including content that is not currently\n   * rendered.\n   */\n\n\n  setTotalContentSize(size) {\n    if (this._totalContentSize !== size) {\n      this._totalContentSize = size;\n\n      this._calculateSpacerSize();\n\n      this._markChangeDetectionNeeded();\n    }\n  }\n  /** Sets the currently rendered range of indices. */\n\n\n  setRenderedRange(range) {\n    if (!rangesEqual(this._renderedRange, range)) {\n      if (this.appendOnly) {\n        range = {\n          start: 0,\n          end: Math.max(this._renderedRange.end, range.end)\n        };\n      }\n\n      this._renderedRangeSubject.next(this._renderedRange = range);\n\n      this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n    }\n  }\n  /**\n   * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n   */\n\n\n  getOffsetToRenderedContentStart() {\n    return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n  }\n  /**\n   * Sets the offset from the start of the viewport to either the start or end of the rendered data\n   * (in pixels).\n   */\n\n\n  setRenderedContentOffset(offset, to = 'to-start') {\n    // In appendOnly, we always start from the top\n    offset = this.appendOnly && to === 'to-start' ? 0 : offset; // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n    // in the negative direction.\n\n    const isRtl = this.dir && this.dir.value == 'rtl';\n    const isHorizontal = this.orientation == 'horizontal';\n    const axis = isHorizontal ? 'X' : 'Y';\n    const axisDirection = isHorizontal && isRtl ? -1 : 1;\n    let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n    this._renderedContentOffset = offset;\n\n    if (to === 'to-end') {\n      transform += ` translate${axis}(-100%)`; // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n      // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n      // expand upward).\n\n      this._renderedContentOffsetNeedsRewrite = true;\n    }\n\n    if (this._renderedContentTransform != transform) {\n      // We know this value is safe because we parse `offset` with `Number()` before passing it\n      // into the string.\n      this._renderedContentTransform = transform;\n\n      this._markChangeDetectionNeeded(() => {\n        if (this._renderedContentOffsetNeedsRewrite) {\n          this._renderedContentOffset -= this.measureRenderedContentSize();\n          this._renderedContentOffsetNeedsRewrite = false;\n          this.setRenderedContentOffset(this._renderedContentOffset);\n        } else {\n          this._scrollStrategy.onRenderedOffsetChanged();\n        }\n      });\n    }\n  }\n  /**\n   * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n   * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n   * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n   * @param offset The offset to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n\n\n  scrollToOffset(offset, behavior = 'auto') {\n    const options = {\n      behavior\n    };\n\n    if (this.orientation === 'horizontal') {\n      options.start = offset;\n    } else {\n      options.top = offset;\n    }\n\n    this.scrollTo(options);\n  }\n  /**\n   * Scrolls to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n   */\n\n\n  scrollToIndex(index, behavior = 'auto') {\n    this._scrollStrategy.scrollToIndex(index, behavior);\n  }\n  /**\n   * Gets the current scroll offset from the start of the viewport (in pixels).\n   * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n   *     in horizontal mode.\n   */\n\n\n  measureScrollOffset(from) {\n    return from ? super.measureScrollOffset(from) : super.measureScrollOffset(this.orientation === 'horizontal' ? 'start' : 'top');\n  }\n  /** Measure the combined size of all of the rendered items. */\n\n\n  measureRenderedContentSize() {\n    const contentEl = this._contentWrapper.nativeElement;\n    return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n  }\n  /**\n   * Measure the total combined size of the given range. Throws if the range includes items that are\n   * not rendered.\n   */\n\n\n  measureRangeSize(range) {\n    if (!this._forOf) {\n      return 0;\n    }\n\n    return this._forOf.measureRangeSize(range, this.orientation);\n  }\n  /** Update the viewport dimensions and re-render. */\n\n\n  checkViewportSize() {\n    // TODO: Cleanup later when add logic for handling content resize\n    this._measureViewportSize();\n\n    this._scrollStrategy.onDataLengthChanged();\n  }\n  /** Measure the viewport size. */\n\n\n  _measureViewportSize() {\n    const viewportEl = this.elementRef.nativeElement;\n    this._viewportSize = this.orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n  }\n  /** Queue up change detection to run. */\n\n\n  _markChangeDetectionNeeded(runAfter) {\n    if (runAfter) {\n      this._runAfterChangeDetection.push(runAfter);\n    } // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n    // properties sequentially we only have to run `_doChangeDetection` once at the end.\n\n\n    if (!this._isChangeDetectionPending) {\n      this._isChangeDetectionPending = true;\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._doChangeDetection();\n      }));\n    }\n  }\n  /** Run change detection. */\n\n\n  _doChangeDetection() {\n    this._isChangeDetectionPending = false; // Apply the content transform. The transform can't be set via an Angular binding because\n    // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n    // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n    // the `Number` function first to coerce it to a numeric value.\n\n    this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform; // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n    // from the root, since the repeated items are content projected in. Calling `detectChanges`\n    // instead does not properly check the projected content.\n\n    this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n    const runAfterChangeDetection = this._runAfterChangeDetection;\n    this._runAfterChangeDetection = [];\n\n    for (const fn of runAfterChangeDetection) {\n      fn();\n    }\n  }\n  /** Calculates the `style.width` and `style.height` for the spacer element. */\n\n\n  _calculateSpacerSize() {\n    this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n    this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n  }\n\n}\n\nCdkVirtualScrollViewport.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n  return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler));\n};\n\nCdkVirtualScrollViewport.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: CdkVirtualScrollViewport,\n  selectors: [[\"cdk-virtual-scroll-viewport\"]],\n  viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 7);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n    }\n  },\n  hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n  hostVars: 4,\n  hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n    }\n  },\n  inputs: {\n    orientation: \"orientation\",\n    appendOnly: \"appendOnly\"\n  },\n  outputs: {\n    scrolledIndexChange: \"scrolledIndexChange\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CdkScrollable,\n    useExisting: CdkVirtualScrollViewport\n  }]), i0.ɵɵInheritDefinitionFeature],\n  ngContentSelectors: _c1,\n  decls: 4,\n  vars: 4,\n  consts: [[1, \"cdk-virtual-scroll-content-wrapper\"], [\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-spacer\"]],\n  template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵprojection(2);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(3, \"div\", 2);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n    }\n  },\n  styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualScrollViewport, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-virtual-scroll-viewport',\n      host: {\n        'class': 'cdk-virtual-scroll-viewport',\n        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: CdkScrollable,\n        useExisting: CdkVirtualScrollViewport\n      }],\n      template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\",\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [VIRTUAL_SCROLL_STRATEGY]\n      }]\n    }, {\n      type: i2.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: ScrollDispatcher\n    }, {\n      type: ViewportRuler\n    }];\n  }, {\n    orientation: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    scrolledIndexChange: [{\n      type: Output\n    }],\n    _contentWrapper: [{\n      type: ViewChild,\n      args: ['contentWrapper', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\n\n\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n\n  const rect = el.getBoundingClientRect();\n\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\n\n\nclass CdkVirtualForOf {\n  constructor(\n  /** The view container to add items to. */\n  _viewContainerRef,\n  /** The template to use when stamping out new items. */\n  _template,\n  /** The set of available differs. */\n  _differs,\n  /** The strategy used to render items in the virtual scroll viewport. */\n  _viewRepeater,\n  /** The virtual scrolling viewport that these items are being rendered in. */\n  _viewport, ngZone) {\n    this._viewContainerRef = _viewContainerRef;\n    this._template = _template;\n    this._differs = _differs;\n    this._viewRepeater = _viewRepeater;\n    this._viewport = _viewport;\n    /** Emits when the rendered view of the data changes. */\n\n    this.viewChange = new Subject();\n    /** Subject that emits when a new DataSource instance is given. */\n\n    this._dataSourceChanges = new Subject();\n    /** Emits whenever the data in the current DataSource changes. */\n\n    this.dataStream = this._dataSourceChanges.pipe( // Start off with null `DataSource`.\n    startWith(null), // Bundle up the previous and current data sources so we can work with both.\n    pairwise(), // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n    // new one, passing back a stream of data changes which we run through `switchMap` to give\n    // us a data stream that emits the latest data from whatever the current `DataSource` is.\n    switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), // Replay the last emitted data when someone subscribes.\n    shareReplay(1));\n    /** The differ used to calculate changes to the data. */\n\n    this._differ = null;\n    /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n\n    this._needsUpdate = false;\n    this._destroyed = new Subject();\n    this.dataStream.subscribe(data => {\n      this._data = data;\n\n      this._onRenderedDataChange();\n    });\n\n    this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n      this._renderedRange = range;\n\n      if (this.viewChange.observers.length) {\n        ngZone.run(() => this.viewChange.next(this._renderedRange));\n      }\n\n      this._onRenderedDataChange();\n    });\n\n    this._viewport.attach(this);\n  }\n  /** The DataSource to display. */\n\n\n  get cdkVirtualForOf() {\n    return this._cdkVirtualForOf;\n  }\n\n  set cdkVirtualForOf(value) {\n    this._cdkVirtualForOf = value;\n\n    if (isDataSource(value)) {\n      this._dataSourceChanges.next(value);\n    } else {\n      // If value is an an NgIterable, convert it to an array.\n      this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n    }\n  }\n  /**\n   * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n   * the item and produces a value to be used as the item's identity when tracking changes.\n   */\n\n\n  get cdkVirtualForTrackBy() {\n    return this._cdkVirtualForTrackBy;\n  }\n\n  set cdkVirtualForTrackBy(fn) {\n    this._needsUpdate = true;\n    this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n  }\n  /** The template used to stamp out new elements. */\n\n\n  set cdkVirtualForTemplate(value) {\n    if (value) {\n      this._needsUpdate = true;\n      this._template = value;\n    }\n  }\n  /**\n   * The size of the cache used to store templates that are not being used for re-use later.\n   * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n   */\n\n\n  get cdkVirtualForTemplateCacheSize() {\n    return this._viewRepeater.viewCacheSize;\n  }\n\n  set cdkVirtualForTemplateCacheSize(size) {\n    this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n  }\n  /**\n   * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n   * in the specified range. Throws an error if the range includes items that are not currently\n   * rendered.\n   */\n\n\n  measureRangeSize(range, orientation) {\n    if (range.start >= range.end) {\n      return 0;\n    }\n\n    if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Error: attempted to measure an item that isn't rendered.`);\n    } // The index into the list of rendered views for the first item in the range.\n\n\n    const renderedStartIndex = range.start - this._renderedRange.start; // The length of the range we're measuring.\n\n    const rangeLen = range.end - range.start; // Loop over all the views, find the first and land node and compute the size by subtracting\n    // the top of the first node from the bottom of the last one.\n\n    let firstNode;\n    let lastNode; // Find the first node by starting from the beginning and going forwards.\n\n    for (let i = 0; i < rangeLen; i++) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n      if (view && view.rootNodes.length) {\n        firstNode = lastNode = view.rootNodes[0];\n        break;\n      }\n    } // Find the last node by starting from the end and going backwards.\n\n\n    for (let i = rangeLen - 1; i > -1; i--) {\n      const view = this._viewContainerRef.get(i + renderedStartIndex);\n\n      if (view && view.rootNodes.length) {\n        lastNode = view.rootNodes[view.rootNodes.length - 1];\n        break;\n      }\n    }\n\n    return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n  }\n\n  ngDoCheck() {\n    if (this._differ && this._needsUpdate) {\n      // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n      // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n      // changing (need to do this diff).\n      const changes = this._differ.diff(this._renderedItems);\n\n      if (!changes) {\n        this._updateContext();\n      } else {\n        this._applyChanges(changes);\n      }\n\n      this._needsUpdate = false;\n    }\n  }\n\n  ngOnDestroy() {\n    this._viewport.detach();\n\n    this._dataSourceChanges.next(undefined);\n\n    this._dataSourceChanges.complete();\n\n    this.viewChange.complete();\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n\n    this._viewRepeater.detach();\n  }\n  /** React to scroll state changes in the viewport. */\n\n\n  _onRenderedDataChange() {\n    if (!this._renderedRange) {\n      return;\n    }\n\n    this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n\n    if (!this._differ) {\n      // Use a wrapper function for the `trackBy` so any new values are\n      // picked up automatically without having to recreate the differ.\n      this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n        return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n      });\n    }\n\n    this._needsUpdate = true;\n  }\n  /** Swap out one `DataSource` for another. */\n\n\n  _changeDataSource(oldDs, newDs) {\n    if (oldDs) {\n      oldDs.disconnect(this);\n    }\n\n    this._needsUpdate = true;\n    return newDs ? newDs.connect(this) : of();\n  }\n  /** Update the `CdkVirtualForOfContext` for all views. */\n\n\n  _updateContext() {\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n\n      this._updateComputedContextProperties(view.context);\n\n      view.detectChanges();\n    }\n  }\n  /** Apply changes to the DOM. */\n\n\n  _applyChanges(changes) {\n    this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item); // Update $implicit for any items that had an identity change.\n\n\n    changes.forEachIdentityChange(record => {\n      const view = this._viewContainerRef.get(record.currentIndex);\n\n      view.context.$implicit = record.item;\n    }); // Update the context variables on all items.\n\n    const count = this._data.length;\n    let i = this._viewContainerRef.length;\n\n    while (i--) {\n      const view = this._viewContainerRef.get(i);\n\n      view.context.index = this._renderedRange.start + i;\n      view.context.count = count;\n\n      this._updateComputedContextProperties(view.context);\n    }\n  }\n  /** Update the computed properties on the `CdkVirtualForOfContext`. */\n\n\n  _updateComputedContextProperties(context) {\n    context.first = context.index === 0;\n    context.last = context.index === context.count - 1;\n    context.even = context.index % 2 === 0;\n    context.odd = !context.even;\n  }\n\n  _getEmbeddedViewArgs(record, index) {\n    // Note that it's important that we insert the item directly at the proper index,\n    // rather than inserting it and the moving it in place, because if there's a directive\n    // on the same node that injects the `ViewContainerRef`, Angular will insert another\n    // comment node which can throw off the move when it's being repeated for all items.\n    return {\n      templateRef: this._template,\n      context: {\n        $implicit: record.item,\n        // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n        // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n        cdkVirtualForOf: this._cdkVirtualForOf,\n        index: -1,\n        count: -1,\n        first: false,\n        last: false,\n        odd: false,\n        even: false\n      },\n      index\n    };\n  }\n\n}\n\nCdkVirtualForOf.ɵfac = function CdkVirtualForOf_Factory(t) {\n  return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkVirtualForOf.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkVirtualForOf,\n  selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n  inputs: {\n    cdkVirtualForOf: \"cdkVirtualForOf\",\n    cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n    cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n    cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: _VIEW_REPEATER_STRATEGY,\n    useClass: _RecycleViewRepeaterStrategy\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkVirtualForOf, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkVirtualFor][cdkVirtualForOf]',\n      providers: [{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ViewContainerRef\n    }, {\n      type: i0.TemplateRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i2$1._RecycleViewRepeaterStrategy,\n      decorators: [{\n        type: Inject,\n        args: [_VIEW_REPEATER_STRATEGY]\n      }]\n    }, {\n      type: CdkVirtualScrollViewport,\n      decorators: [{\n        type: SkipSelf\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    cdkVirtualForOf: [{\n      type: Input\n    }],\n    cdkVirtualForTrackBy: [{\n      type: Input\n    }],\n    cdkVirtualForTemplate: [{\n      type: Input\n    }],\n    cdkVirtualForTemplateCacheSize: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass CdkScrollableModule {}\n\nCdkScrollableModule.ɵfac = function CdkScrollableModule_Factory(t) {\n  return new (t || CdkScrollableModule)();\n};\n\nCdkScrollableModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CdkScrollableModule\n});\nCdkScrollableModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkScrollableModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkScrollable],\n      declarations: [CdkScrollable]\n    }]\n  }], null, null);\n})();\n/**\n * @docs-primary-export\n */\n\n\nclass ScrollingModule {}\n\nScrollingModule.ɵfac = function ScrollingModule_Factory(t) {\n  return new (t || ScrollingModule)();\n};\n\nScrollingModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ScrollingModule\n});\nScrollingModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollingModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkScrollableModule],\n      exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport],\n      declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["coerceNumberProperty", "coerceElement", "coerceBooleanProperty", "i0", "InjectionToken", "forwardRef", "Directive", "Input", "Injectable", "Optional", "Inject", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "SkipSelf", "NgModule", "Subject", "of", "Observable", "fromEvent", "animationFrameScheduler", "asapScheduler", "Subscription", "isObservable", "distinctUntilChanged", "auditTime", "filter", "takeUntil", "startWith", "pairwise", "switchMap", "shareReplay", "DOCUMENT", "i1", "getRtlScrollAxisType", "supportsScrollBehavior", "i2", "BidiModule", "i2$1", "isDataSource", "ArrayDataSource", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "ɵfac", "ɵdir", "provide", "useFactory", "deps", "type", "args", "selector", "providers", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "document", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "_document", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_addGlobalListener", "subscription", "_removeGlobalListener", "ngOnDestroy", "for<PERSON>ach", "_", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "_getWindow", "defaultView", "window", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "runOutsideAngular", "NgZone", "Platform", "ɵprov", "providedIn", "undefined", "decorators", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "_destroyed", "_elementScrolled", "ngOnInit", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "ElementRef", "Directionality", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "event", "addEventListener", "change", "_viewportSize", "removeEventListener", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "_orientation", "_appendOnly", "Promise", "resolve", "then", "run", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "checkViewportSize", "orientation", "_calculateSpacerSize", "appendOnly", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "viewportEl", "runAfter", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "fn", "ChangeDetectorRef", "ɵcmp", "useExisting", "host", "encapsulation", "None", "changeDetection", "OnPush", "template", "styles", "static", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "_viewContainerRef", "_template", "_differs", "_view<PERSON><PERSON><PERSON>er", "viewChange", "_dataSourceChanges", "prev", "cur", "_changeDataSource", "_differ", "_needsUpdate", "_data", "_onRenderedDataChange", "observers", "cdkVirtualForOf", "_cdkVirtualForOf", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "item", "cdkVirtualForTemplate", "cdkVirtualForTemplateCacheSize", "viewCacheSize", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "i", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "first", "last", "even", "odd", "templateRef", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkScrollableModule", "ɵmod", "ɵinj", "exports", "declarations", "ScrollingModule", "imports"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/scrolling.mjs"], "sourcesContent": ["import { coerceNumberProperty, coerceElement, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, supportsScrollBehavior } from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n}\nCdkFixedSizeVirtualScroll.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkFixedSizeVirtualScroll.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkFixedSizeVirtualScroll, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n        {\n            provide: VIRTUAL_SCROLL_STRATEGY,\n            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n        },\n    ], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n}\nScrollDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nScrollDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () {\n        return [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != 0 /* RtlScrollAxisType.NORMAL */) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == 2 /* RtlScrollAxisType.INVERTED */) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == 1 /* RtlScrollAxisType.NEGATED */) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n}\nCdkScrollable.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkScrollable.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkScrollable, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                        type: Optional\n                    }] }];\n    } });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a ClientRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n}\nViewportRuler.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nViewportRuler.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ViewportRuler, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () {\n        return [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkScrollable {\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        this._appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n    }\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    /**\n     * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n     * will be removed.\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(value) {\n        this._appendOnly = coerceBooleanProperty(value);\n    }\n    ngOnInit() {\n        super.ngOnInit();\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the viewport (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        return from\n            ? super.measureScrollOffset(from)\n            : super.measureScrollOffset(this.orientation === 'horizontal' ? 'start' : 'top');\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        const viewportEl = this.elementRef.nativeElement;\n        this._viewportSize =\n            this.orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n}\nCdkVirtualScrollViewport.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }], target: i0.ɵɵFactoryTarget.Component });\nCdkVirtualScrollViewport.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkVirtualScrollViewport, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: \"appendOnly\" }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n        {\n            provide: CdkScrollable,\n            useExisting: CdkVirtualScrollViewport,\n        },\n    ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useExisting: CdkVirtualScrollViewport,\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0}[dir=rtl] .cdk-virtual-scroll-spacer{right:0;left:auto;transform-origin:100% 0}\"] }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [VIRTUAL_SCROLL_STRATEGY]\n                    }] }, { type: i2.Directionality, decorators: [{\n                        type: Optional\n                    }] }, { type: ScrollDispatcher }, { type: ViewportRuler }];\n    }, propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n}\nCdkVirtualForOf.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkVirtualForOf.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkVirtualForOf, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                        type: Inject,\n                        args: [_VIEW_REPEATER_STRATEGY]\n                    }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                        type: SkipSelf\n                    }] }, { type: i0.NgZone }];\n    }, propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass CdkScrollableModule {\n}\nCdkScrollableModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCdkScrollableModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollableModule, declarations: [CdkScrollable], exports: [CdkScrollable] });\nCdkScrollableModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollableModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    declarations: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n}\nScrollingModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nScrollingModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollingModule, declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport], imports: [BidiModule, CdkScrollableModule], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n        CdkVirtualForOf,\n        CdkVirtualScrollViewport] });\nScrollingModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [BidiModule, CdkScrollableModule],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                    ],\n                    declarations: [CdkFixedSizeVirtualScroll, CdkVirtualForOf, CdkVirtualScrollViewport],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,SAASA,oBAAT,EAA+BC,aAA/B,EAA8CC,qBAA9C,QAA2E,uBAA3E;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,UAAzB,EAAqCC,SAArC,EAAgDC,KAAhD,EAAuDC,UAAvD,EAAmEC,QAAnE,EAA6EC,MAA7E,EAAqFC,SAArF,EAAgGC,iBAAhG,EAAmHC,uBAAnH,EAA4IC,MAA5I,EAAoJC,SAApJ,EAA+JC,QAA/J,EAAyKC,QAAzK,QAAyL,eAAzL;AACA,SAASC,OAAT,EAAkBC,EAAlB,EAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,uBAA7C,EAAsEC,aAAtE,EAAqFC,YAArF,EAAmGC,YAAnG,QAAuH,MAAvH;AACA,SAASC,oBAAT,EAA+BC,SAA/B,EAA0CC,MAA1C,EAAkDC,SAAlD,EAA6DC,SAA7D,EAAwEC,QAAxE,EAAkFC,SAAlF,EAA6FC,WAA7F,QAAgH,gBAAhH;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,OAAO,KAAKC,EAAZ,MAAoB,uBAApB;AACA,SAASC,oBAAT,EAA+BC,sBAA/B,QAA6D,uBAA7D;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,KAAKC,IAAZ,MAAsB,0BAAtB;AACA,SAASC,YAAT,EAAuBC,eAAvB,EAAwCC,uBAAxC,EAAiEC,4BAAjE,QAAqG,0BAArG;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;;AACA,MAAMC,uBAAuB,GAAG,IAAIzC,cAAJ,CAAmB,yBAAnB,CAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;AACA,MAAM0C,8BAAN,CAAqC;EACjC;AACJ;AACA;AACA;AACA;EACIC,WAAW,CAACC,QAAD,EAAWC,WAAX,EAAwBC,WAAxB,EAAqC;IAC5C,KAAKC,oBAAL,GAA4B,IAAIjC,OAAJ,EAA5B;IACA;;IACA,KAAKkC,mBAAL,GAA2B,KAAKD,oBAAL,CAA0BE,IAA1B,CAA+B3B,oBAAoB,EAAnD,CAA3B;IACA;;IACA,KAAK4B,SAAL,GAAiB,IAAjB;IACA,KAAKC,SAAL,GAAiBP,QAAjB;IACA,KAAKQ,YAAL,GAAoBP,WAApB;IACA,KAAKQ,YAAL,GAAoBP,WAApB;EACH;EACD;AACJ;AACA;AACA;;;EACIQ,MAAM,CAACC,QAAD,EAAW;IACb,KAAKL,SAAL,GAAiBK,QAAjB;;IACA,KAAKC,uBAAL;;IACA,KAAKC,oBAAL;EACH;EACD;;;EACAC,MAAM,GAAG;IACL,KAAKX,oBAAL,CAA0BY,QAA1B;;IACA,KAAKT,SAAL,GAAiB,IAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIU,uBAAuB,CAAChB,QAAD,EAAWC,WAAX,EAAwBC,WAAxB,EAAqC;IACxD,IAAIA,WAAW,GAAGD,WAAd,KAA8B,OAAOgB,SAAP,KAAqB,WAArB,IAAoCA,SAAlE,CAAJ,EAAkF;MAC9E,MAAMC,KAAK,CAAC,8EAAD,CAAX;IACH;;IACD,KAAKX,SAAL,GAAiBP,QAAjB;IACA,KAAKQ,YAAL,GAAoBP,WAApB;IACA,KAAKQ,YAAL,GAAoBP,WAApB;;IACA,KAAKU,uBAAL;;IACA,KAAKC,oBAAL;EACH;EACD;;;EACAM,iBAAiB,GAAG;IAChB,KAAKN,oBAAL;EACH;EACD;;;EACAO,mBAAmB,GAAG;IAClB,KAAKR,uBAAL;;IACA,KAAKC,oBAAL;EACH;EACD;;;EACAQ,iBAAiB,GAAG;IAChB;EACH;EACD;;;EACAC,uBAAuB,GAAG;IACtB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,aAAa,CAACC,KAAD,EAAQC,QAAR,EAAkB;IAC3B,IAAI,KAAKnB,SAAT,EAAoB;MAChB,KAAKA,SAAL,CAAeoB,cAAf,CAA8BF,KAAK,GAAG,KAAKjB,SAA3C,EAAsDkB,QAAtD;IACH;EACJ;EACD;;;EACAb,uBAAuB,GAAG;IACtB,IAAI,CAAC,KAAKN,SAAV,EAAqB;MACjB;IACH;;IACD,KAAKA,SAAL,CAAeqB,mBAAf,CAAmC,KAAKrB,SAAL,CAAesB,aAAf,KAAiC,KAAKrB,SAAzE;EACH;EACD;;;EACAM,oBAAoB,GAAG;IACnB,IAAI,CAAC,KAAKP,SAAV,EAAqB;MACjB;IACH;;IACD,MAAMuB,aAAa,GAAG,KAAKvB,SAAL,CAAewB,gBAAf,EAAtB;;IACA,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAvB;MAA8BC,GAAG,EAAEJ,aAAa,CAACI;IAAjD,CAAjB;;IACA,MAAMC,YAAY,GAAG,KAAK5B,SAAL,CAAe6B,eAAf,EAArB;;IACA,MAAMC,UAAU,GAAG,KAAK9B,SAAL,CAAesB,aAAf,EAAnB;;IACA,IAAIS,YAAY,GAAG,KAAK/B,SAAL,CAAegC,mBAAf,EAAnB,CARmB,CASnB;;;IACA,IAAIC,iBAAiB,GAAG,KAAKhC,SAAL,GAAiB,CAAjB,GAAqB8B,YAAY,GAAG,KAAK9B,SAAzC,GAAqD,CAA7E,CAVmB,CAWnB;;IACA,IAAIwB,QAAQ,CAACE,GAAT,GAAeG,UAAnB,EAA+B;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAL,CAAUR,YAAY,GAAG,KAAK3B,SAA9B,CAAxB;MACA,MAAMoC,eAAe,GAAGF,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACI,GAAL,CAASN,iBAAT,EAA4BH,UAAU,GAAGI,eAAzC,CAAZ,CAAxB,CAH2B,CAI3B;MACA;;MACA,IAAID,iBAAiB,IAAII,eAAzB,EAA0C;QACtCJ,iBAAiB,GAAGI,eAApB;QACAN,YAAY,GAAGM,eAAe,GAAG,KAAKpC,SAAtC;QACAwB,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACK,KAAL,CAAWP,iBAAX,CAAjB;MACH;;MACDR,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBL,QAAQ,CAACC,KAAT,GAAiBQ,eAAtC,CAAZ,CAAf;IACH;;IACD,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAT,GAAiB,KAAKzB,SAAzD;;IACA,IAAIwC,WAAW,GAAG,KAAKvC,YAAnB,IAAmCuB,QAAQ,CAACC,KAAT,IAAkB,CAAzD,EAA4D;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAL,CAAU,CAAC,KAAKjC,YAAL,GAAoBsC,WAArB,IAAoC,KAAKxC,SAAnD,CAApB;MACAwB,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYb,QAAQ,CAACC,KAAT,GAAiBgB,WAA7B,CAAjB;MACAjB,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBK,IAAI,CAACC,IAAL,CAAUH,iBAAiB,GAAG,CAACL,YAAY,GAAG,KAAK1B,YAArB,IAAqC,KAAKD,SAAxE,CAArB,CAAf;IACH,CAJD,MAKK;MACD,MAAM0C,SAAS,GAAGlB,QAAQ,CAACE,GAAT,GAAe,KAAK1B,SAApB,IAAiC8B,YAAY,GAAGH,YAAhD,CAAlB;;MACA,IAAIe,SAAS,GAAG,KAAKzC,YAAjB,IAAiCuB,QAAQ,CAACE,GAAT,IAAgBG,UAArD,EAAiE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAL,CAAU,CAAC,KAAKjC,YAAL,GAAoBwC,SAArB,IAAkC,KAAK1C,SAAjD,CAAlB;;QACA,IAAI2C,SAAS,GAAG,CAAhB,EAAmB;UACfnB,QAAQ,CAACE,GAAT,GAAeQ,IAAI,CAACI,GAAL,CAAST,UAAT,EAAqBL,QAAQ,CAACE,GAAT,GAAeiB,SAApC,CAAf;UACAnB,QAAQ,CAACC,KAAT,GAAiBS,IAAI,CAACG,GAAL,CAAS,CAAT,EAAYH,IAAI,CAACK,KAAL,CAAWP,iBAAiB,GAAG,KAAK/B,YAAL,GAAoB,KAAKD,SAAxD,CAAZ,CAAjB;QACH;MACJ;IACJ;;IACD,KAAKD,SAAL,CAAe6C,gBAAf,CAAgCpB,QAAhC;;IACA,KAAKzB,SAAL,CAAe8C,wBAAf,CAAwC,KAAK7C,SAAL,GAAiBwB,QAAQ,CAACC,KAAlE;;IACA,KAAK7B,oBAAL,CAA0BkD,IAA1B,CAA+BZ,IAAI,CAACK,KAAL,CAAWP,iBAAX,CAA/B;EACH;;AA7HgC;AA+HrC;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASe,sCAAT,CAAgDC,YAAhD,EAA8D;EAC1D,OAAOA,YAAY,CAACC,eAApB;AACH;AACD;;;AACA,MAAMC,yBAAN,CAAgC;EAC5B1D,WAAW,GAAG;IACV,KAAKQ,SAAL,GAAiB,EAAjB;IACA,KAAKC,YAAL,GAAoB,GAApB;IACA,KAAKC,YAAL,GAAoB,GAApB;IACA;;IACA,KAAK+C,eAAL,GAAuB,IAAI1D,8BAAJ,CAAmC,KAAKE,QAAxC,EAAkD,KAAKC,WAAvD,EAAoE,KAAKC,WAAzE,CAAvB;EACH;EACD;;;EACY,IAARF,QAAQ,GAAG;IACX,OAAO,KAAKO,SAAZ;EACH;;EACW,IAARP,QAAQ,CAAC0D,KAAD,EAAQ;IAChB,KAAKnD,SAAL,GAAiBvD,oBAAoB,CAAC0G,KAAD,CAArC;EACH;EACD;AACJ;AACA;AACA;;;EACmB,IAAXzD,WAAW,GAAG;IACd,OAAO,KAAKO,YAAZ;EACH;;EACc,IAAXP,WAAW,CAACyD,KAAD,EAAQ;IACnB,KAAKlD,YAAL,GAAoBxD,oBAAoB,CAAC0G,KAAD,CAAxC;EACH;EACD;AACJ;AACA;;;EACmB,IAAXxD,WAAW,GAAG;IACd,OAAO,KAAKO,YAAZ;EACH;;EACc,IAAXP,WAAW,CAACwD,KAAD,EAAQ;IACnB,KAAKjD,YAAL,GAAoBzD,oBAAoB,CAAC0G,KAAD,CAAxC;EACH;;EACDC,WAAW,GAAG;IACV,KAAKH,eAAL,CAAqBxC,uBAArB,CAA6C,KAAKhB,QAAlD,EAA4D,KAAKC,WAAjE,EAA8E,KAAKC,WAAnF;EACH;;AApC2B;;AAsChCuD,yBAAyB,CAACG,IAA1B;EAAA,iBAAsHH,yBAAtH;AAAA;;AACAA,yBAAyB,CAACI,IAA1B,kBAD4G1G,EAC5G;EAAA,MAA0GsG,yBAA1G;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD4GtG,EAC5G,oBAA6R,CACrR;IACI2G,OAAO,EAAEjE,uBADb;IAEIkE,UAAU,EAAET,sCAFhB;IAGIU,IAAI,EAAE,CAAC3G,UAAU,CAAC,MAAMoG,yBAAP,CAAX;EAHV,CADqR,CAA7R,GAD4GtG,EAC5G;AAAA;;AAOA;EAAA,mDAR4GA,EAQ5G,mBAA2FsG,yBAA3F,EAAkI,CAAC;IACvHQ,IAAI,EAAE3G,SADiH;IAEvH4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCADX;MAECC,SAAS,EAAE,CACP;QACIN,OAAO,EAAEjE,uBADb;QAEIkE,UAAU,EAAET,sCAFhB;QAGIU,IAAI,EAAE,CAAC3G,UAAU,CAAC,MAAMoG,yBAAP,CAAX;MAHV,CADO;IAFZ,CAAD;EAFiH,CAAD,CAAlI,QAY4B;IAAEzD,QAAQ,EAAE,CAAC;MACzBiE,IAAI,EAAE1G;IADmB,CAAD,CAAZ;IAEZ0C,WAAW,EAAE,CAAC;MACdgE,IAAI,EAAE1G;IADQ,CAAD,CAFD;IAIZ2C,WAAW,EAAE,CAAC;MACd+D,IAAI,EAAE1G;IADQ,CAAD;EAJD,CAZ5B;AAAA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM8G,mBAAmB,GAAG,EAA5B;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAN,CAAuB;EACnBvE,WAAW,CAACwE,OAAD,EAAUC,SAAV,EAAqBC,QAArB,EAA+B;IACtC,KAAKF,OAAL,GAAeA,OAAf;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA;;IACA,KAAKE,SAAL,GAAiB,IAAIxG,OAAJ,EAAjB;IACA;;IACA,KAAKyG,mBAAL,GAA2B,IAA3B;IACA;;IACA,KAAKC,cAAL,GAAsB,CAAtB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,gBAAL,GAAwB,IAAIC,GAAJ,EAAxB;IACA,KAAKC,SAAL,GAAiBN,QAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIO,QAAQ,CAACC,UAAD,EAAa;IACjB,IAAI,CAAC,KAAKJ,gBAAL,CAAsBK,GAAtB,CAA0BD,UAA1B,CAAL,EAA4C;MACxC,KAAKJ,gBAAL,CAAsBM,GAAtB,CAA0BF,UAA1B,EAAsCA,UAAU,CAACG,eAAX,GAA6BC,SAA7B,CAAuC,MAAM,KAAKX,SAAL,CAAerB,IAAf,CAAoB4B,UAApB,CAA7C,CAAtC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIK,UAAU,CAACL,UAAD,EAAa;IACnB,MAAMM,mBAAmB,GAAG,KAAKV,gBAAL,CAAsBW,GAAtB,CAA0BP,UAA1B,CAA5B;;IACA,IAAIM,mBAAJ,EAAyB;MACrBA,mBAAmB,CAACE,WAApB;MACA,KAAKZ,gBAAL,CAAsBa,MAAtB,CAA6BT,UAA7B;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIU,QAAQ,CAACC,aAAa,GAAGvB,mBAAjB,EAAsC;IAC1C,IAAI,CAAC,KAAKG,SAAL,CAAeqB,SAApB,EAA+B;MAC3B,OAAO1H,EAAE,EAAT;IACH;;IACD,OAAO,IAAIC,UAAJ,CAAgB0H,QAAD,IAAc;MAChC,IAAI,CAAC,KAAKnB,mBAAV,EAA+B;QAC3B,KAAKoB,kBAAL;MACH,CAH+B,CAIhC;MACA;;;MACA,MAAMC,YAAY,GAAGJ,aAAa,GAAG,CAAhB,GACf,KAAKlB,SAAL,CAAerE,IAAf,CAAoB1B,SAAS,CAACiH,aAAD,CAA7B,EAA8CP,SAA9C,CAAwDS,QAAxD,CADe,GAEf,KAAKpB,SAAL,CAAeW,SAAf,CAAyBS,QAAzB,CAFN;MAGA,KAAKlB,cAAL;MACA,OAAO,MAAM;QACToB,YAAY,CAACP,WAAb;QACA,KAAKb,cAAL;;QACA,IAAI,CAAC,KAAKA,cAAV,EAA0B;UACtB,KAAKqB,qBAAL;QACH;MACJ,CAND;IAOH,CAjBM,CAAP;EAkBH;;EACDC,WAAW,GAAG;IACV,KAAKD,qBAAL;;IACA,KAAKpB,gBAAL,CAAsBsB,OAAtB,CAA8B,CAACC,CAAD,EAAIC,SAAJ,KAAkB,KAAKf,UAAL,CAAgBe,SAAhB,CAAhD;;IACA,KAAK3B,SAAL,CAAe3D,QAAf;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIuF,gBAAgB,CAACC,mBAAD,EAAsBX,aAAtB,EAAqC;IACjD,MAAMY,SAAS,GAAG,KAAKC,2BAAL,CAAiCF,mBAAjC,CAAlB;IACA,OAAO,KAAKZ,QAAL,CAAcC,aAAd,EAA6BvF,IAA7B,CAAkCzB,MAAM,CAAC8H,MAAM,IAAI;MACtD,OAAO,CAACA,MAAD,IAAWF,SAAS,CAACG,OAAV,CAAkBD,MAAlB,IAA4B,CAAC,CAA/C;IACH,CAF8C,CAAxC,CAAP;EAGH;EACD;;;EACAD,2BAA2B,CAACF,mBAAD,EAAsB;IAC7C,MAAMK,mBAAmB,GAAG,EAA5B;IACA,KAAK/B,gBAAL,CAAsBsB,OAAtB,CAA8B,CAACU,aAAD,EAAgB5B,UAAhB,KAA+B;MACzD,IAAI,KAAK6B,0BAAL,CAAgC7B,UAAhC,EAA4CsB,mBAA5C,CAAJ,EAAsE;QAClEK,mBAAmB,CAACG,IAApB,CAAyB9B,UAAzB;MACH;IACJ,CAJD;IAKA,OAAO2B,mBAAP;EACH;EACD;;;EACAI,UAAU,GAAG;IACT,OAAO,KAAKjC,SAAL,CAAekC,WAAf,IAA8BC,MAArC;EACH;EACD;;;EACAJ,0BAA0B,CAAC7B,UAAD,EAAasB,mBAAb,EAAkC;IACxD,IAAIY,OAAO,GAAGlK,aAAa,CAACsJ,mBAAD,CAA3B;IACA,IAAIa,iBAAiB,GAAGnC,UAAU,CAACoC,aAAX,GAA2BC,aAAnD,CAFwD,CAGxD;IACA;;IACA,GAAG;MACC,IAAIH,OAAO,IAAIC,iBAAf,EAAkC;QAC9B,OAAO,IAAP;MACH;IACJ,CAJD,QAIUD,OAAO,GAAGA,OAAO,CAACI,aAJ5B;;IAKA,OAAO,KAAP;EACH;EACD;;;EACAxB,kBAAkB,GAAG;IACjB,KAAKpB,mBAAL,GAA2B,KAAKJ,OAAL,CAAaiD,iBAAb,CAA+B,MAAM;MAC5D,MAAMN,MAAM,GAAG,KAAKF,UAAL,EAAf;;MACA,OAAO3I,SAAS,CAAC6I,MAAM,CAACzC,QAAR,EAAkB,QAAlB,CAAT,CAAqCY,SAArC,CAA+C,MAAM,KAAKX,SAAL,CAAerB,IAAf,EAArD,CAAP;IACH,CAH0B,CAA3B;EAIH;EACD;;;EACA4C,qBAAqB,GAAG;IACpB,IAAI,KAAKtB,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBc,WAAzB;;MACA,KAAKd,mBAAL,GAA2B,IAA3B;IACH;EACJ;;AAhIkB;;AAkIvBL,gBAAgB,CAACV,IAAjB;EAAA,iBAA6GU,gBAA7G,EA3K4GnH,EA2K5G,UAA+IA,EAAE,CAACsK,MAAlJ,GA3K4GtK,EA2K5G,UAAqKgC,EAAE,CAACuI,QAAxK,GA3K4GvK,EA2K5G,UAA6L+B,QAA7L;AAAA;;AACAoF,gBAAgB,CAACqD,KAAjB,kBA5K4GxK,EA4K5G;EAAA,OAAiHmH,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDA7K4GnH,EA6K5G,mBAA2FmH,gBAA3F,EAAyH,CAAC;IAC9GL,IAAI,EAAEzG,UADwG;IAE9G0G,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAd,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAE3D,IAAI,EAAE9G,EAAE,CAACsK;IAAX,CAAD,EAAsB;MAAExD,IAAI,EAAE9E,EAAE,CAACuI;IAAX,CAAtB,EAA6C;MAAEzD,IAAI,EAAE4D,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACpE7D,IAAI,EAAExG;MAD8D,CAAD,EAEpE;QACCwG,IAAI,EAAEvG,MADP;QAECwG,IAAI,EAAE,CAAChF,QAAD;MAFP,CAFoE;IAA/B,CAA7C,CAAP;EAMH,CAVL;AAAA;AAYA;AACA;AACA;AACA;AACA;;;AACA,MAAM6I,aAAN,CAAoB;EAChBhI,WAAW,CAACiI,UAAD,EAAaC,gBAAb,EAA+BC,MAA/B,EAAuCC,GAAvC,EAA4C;IACnD,KAAKH,UAAL,GAAkBA,UAAlB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,GAAL,GAAWA,GAAX;IACA,KAAKC,UAAL,GAAkB,IAAIlK,OAAJ,EAAlB;IACA,KAAKmK,gBAAL,GAAwB,IAAIjK,UAAJ,CAAgB0H,QAAD,IAAc,KAAKoC,MAAL,CAAYV,iBAAZ,CAA8B,MAAMnJ,SAAS,CAAC,KAAK2J,UAAL,CAAgBV,aAAjB,EAAgC,QAAhC,CAAT,CACpFjH,IADoF,CAC/ExB,SAAS,CAAC,KAAKuJ,UAAN,CADsE,EAEpF/C,SAFoF,CAE1ES,QAF0E,CAApC,CAA7B,CAAxB;EAGH;;EACDwC,QAAQ,GAAG;IACP,KAAKL,gBAAL,CAAsBjD,QAAtB,CAA+B,IAA/B;EACH;;EACDkB,WAAW,GAAG;IACV,KAAK+B,gBAAL,CAAsB3C,UAAtB,CAAiC,IAAjC;;IACA,KAAK8C,UAAL,CAAgB/E,IAAhB;;IACA,KAAK+E,UAAL,CAAgBrH,QAAhB;EACH;EACD;;;EACAqE,eAAe,GAAG;IACd,OAAO,KAAKiD,gBAAZ;EACH;EACD;;;EACAhB,aAAa,GAAG;IACZ,OAAO,KAAKW,UAAZ;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIO,QAAQ,CAACC,OAAD,EAAU;IACd,MAAMC,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;IACA,MAAMoB,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C,CAFc,CAGd;;IACA,IAAI8E,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;MACtBH,OAAO,CAACG,IAAR,GAAeD,KAAK,GAAGF,OAAO,CAACvG,GAAX,GAAiBuG,OAAO,CAACxG,KAA7C;IACH;;IACD,IAAIwG,OAAO,CAACI,KAAR,IAAiB,IAArB,EAA2B;MACvBJ,OAAO,CAACI,KAAR,GAAgBF,KAAK,GAAGF,OAAO,CAACxG,KAAX,GAAmBwG,OAAO,CAACvG,GAAhD;IACH,CATa,CAUd;;;IACA,IAAIuG,OAAO,CAACK,MAAR,IAAkB,IAAtB,EAA4B;MACxBL,OAAO,CAACM,GAAR,GACIL,EAAE,CAACM,YAAH,GAAkBN,EAAE,CAACO,YAArB,GAAoCR,OAAO,CAACK,MADhD;IAEH,CAda,CAed;;;IACA,IAAIH,KAAK,IAAItJ,oBAAoB,MAAM;IAAE;IAAzC,EAAyE;MACrE,IAAIoJ,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;QACtBH,OAAO,CAACI,KAAR,GACIH,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCV,OAAO,CAACG,IAD9C;MAEH;;MACD,IAAIvJ,oBAAoB,MAAM;MAAE;MAAhC,EAAkE;QAC9DoJ,OAAO,CAACG,IAAR,GAAeH,OAAO,CAACI,KAAvB;MACH,CAFD,MAGK,IAAIxJ,oBAAoB,MAAM;MAAE;MAAhC,EAAiE;QAClEoJ,OAAO,CAACG,IAAR,GAAeH,OAAO,CAACI,KAAR,GAAgB,CAACJ,OAAO,CAACI,KAAzB,GAAiCJ,OAAO,CAACI,KAAxD;MACH;IACJ,CAXD,MAYK;MACD,IAAIJ,OAAO,CAACI,KAAR,IAAiB,IAArB,EAA2B;QACvBJ,OAAO,CAACG,IAAR,GACIF,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCV,OAAO,CAACI,KAD9C;MAEH;IACJ;;IACD,KAAKO,qBAAL,CAA2BX,OAA3B;EACH;;EACDW,qBAAqB,CAACX,OAAD,EAAU;IAC3B,MAAMC,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;;IACA,IAAIjI,sBAAsB,EAA1B,EAA8B;MAC1BoJ,EAAE,CAACF,QAAH,CAAYC,OAAZ;IACH,CAFD,MAGK;MACD,IAAIA,OAAO,CAACM,GAAR,IAAe,IAAnB,EAAyB;QACrBL,EAAE,CAACW,SAAH,GAAeZ,OAAO,CAACM,GAAvB;MACH;;MACD,IAAIN,OAAO,CAACG,IAAR,IAAgB,IAApB,EAA0B;QACtBF,EAAE,CAACY,UAAH,GAAgBb,OAAO,CAACG,IAAxB;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIrG,mBAAmB,CAACgH,IAAD,EAAO;IACtB,MAAMC,IAAI,GAAG,MAAb;IACA,MAAMC,KAAK,GAAG,OAAd;IACA,MAAMf,EAAE,GAAG,KAAKT,UAAL,CAAgBV,aAA3B;;IACA,IAAIgC,IAAI,IAAI,KAAZ,EAAmB;MACf,OAAOb,EAAE,CAACW,SAAV;IACH;;IACD,IAAIE,IAAI,IAAI,QAAZ,EAAsB;MAClB,OAAOb,EAAE,CAACM,YAAH,GAAkBN,EAAE,CAACO,YAArB,GAAoCP,EAAE,CAACW,SAA9C;IACH,CATqB,CAUtB;;;IACA,MAAMV,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C;;IACA,IAAI4F,IAAI,IAAI,OAAZ,EAAqB;MACjBA,IAAI,GAAGZ,KAAK,GAAGc,KAAH,GAAWD,IAAvB;IACH,CAFD,MAGK,IAAID,IAAI,IAAI,KAAZ,EAAmB;MACpBA,IAAI,GAAGZ,KAAK,GAAGa,IAAH,GAAUC,KAAtB;IACH;;IACD,IAAId,KAAK,IAAItJ,oBAAoB,MAAM;IAAE;IAAzC,EAA2E;MACvE;MACA;MACA,IAAIkK,IAAI,IAAIC,IAAZ,EAAkB;QACd,OAAOd,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCT,EAAE,CAACY,UAA5C;MACH,CAFD,MAGK;QACD,OAAOZ,EAAE,CAACY,UAAV;MACH;IACJ,CATD,MAUK,IAAIX,KAAK,IAAItJ,oBAAoB,MAAM;IAAE;IAAzC,EAA0E;MAC3E;MACA;MACA,IAAIkK,IAAI,IAAIC,IAAZ,EAAkB;QACd,OAAOd,EAAE,CAACY,UAAH,GAAgBZ,EAAE,CAACQ,WAAnB,GAAiCR,EAAE,CAACS,WAA3C;MACH,CAFD,MAGK;QACD,OAAO,CAACT,EAAE,CAACY,UAAX;MACH;IACJ,CATI,MAUA;MACD;MACA;MACA,IAAIC,IAAI,IAAIC,IAAZ,EAAkB;QACd,OAAOd,EAAE,CAACY,UAAV;MACH,CAFD,MAGK;QACD,OAAOZ,EAAE,CAACQ,WAAH,GAAiBR,EAAE,CAACS,WAApB,GAAkCT,EAAE,CAACY,UAA5C;MACH;IACJ;EACJ;;AA9Ie;;AAgJpBtB,aAAa,CAACnE,IAAd;EAAA,iBAA0GmE,aAA1G,EA9U4G5K,EA8U5G,mBAAyIA,EAAE,CAACsM,UAA5I,GA9U4GtM,EA8U5G,mBAAmKmH,gBAAnK,GA9U4GnH,EA8U5G,mBAAgMA,EAAE,CAACsK,MAAnM,GA9U4GtK,EA8U5G,mBAAsNmC,EAAE,CAACoK,cAAzN;AAAA;;AACA3B,aAAa,CAAClE,IAAd,kBA/U4G1G,EA+U5G;EAAA,MAA8F4K,aAA9F;EAAA;AAAA;;AACA;EAAA,mDAhV4G5K,EAgV5G,mBAA2F4K,aAA3F,EAAsH,CAAC;IAC3G9D,IAAI,EAAE3G,SADqG;IAE3G4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;IADX,CAAD;EAFqG,CAAD,CAAtH,EAK4B,YAAY;IAChC,OAAO,CAAC;MAAEF,IAAI,EAAE9G,EAAE,CAACsM;IAAX,CAAD,EAA0B;MAAExF,IAAI,EAAEK;IAAR,CAA1B,EAAsD;MAAEL,IAAI,EAAE9G,EAAE,CAACsK;IAAX,CAAtD,EAA2E;MAAExD,IAAI,EAAE3E,EAAE,CAACoK,cAAX;MAA2B5B,UAAU,EAAE,CAAC;QAC1G7D,IAAI,EAAExG;MADoG,CAAD;IAAvC,CAA3E,CAAP;EAGH,CATL;AAAA;AAWA;;;AACA,MAAMkM,mBAAmB,GAAG,EAA5B;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAN,CAAoB;EAChB7J,WAAW,CAACyE,SAAD,EAAY0D,MAAZ,EAAoBzD,QAApB,EAA8B;IACrC,KAAKD,SAAL,GAAiBA,SAAjB;IACA;;IACA,KAAKqF,OAAL,GAAe,IAAI3L,OAAJ,EAAf;IACA;;IACA,KAAK4L,eAAL,GAAwBC,KAAD,IAAW;MAC9B,KAAKF,OAAL,CAAaxG,IAAb,CAAkB0G,KAAlB;IACH,CAFD;;IAGA,KAAKhF,SAAL,GAAiBN,QAAjB;IACAyD,MAAM,CAACV,iBAAP,CAAyB,MAAM;MAC3B,IAAIhD,SAAS,CAACqB,SAAd,EAAyB;QACrB,MAAMqB,MAAM,GAAG,KAAKF,UAAL,EAAf,CADqB,CAErB;QACA;;;QACAE,MAAM,CAAC8C,gBAAP,CAAwB,QAAxB,EAAkC,KAAKF,eAAvC;QACA5C,MAAM,CAAC8C,gBAAP,CAAwB,mBAAxB,EAA6C,KAAKF,eAAlD;MACH,CAP0B,CAQ3B;MACA;;;MACA,KAAKG,MAAL,GAAc5E,SAAd,CAAwB,MAAO,KAAK6E,aAAL,GAAqB,IAApD;IACH,CAXD;EAYH;;EACDhE,WAAW,GAAG;IACV,IAAI,KAAK1B,SAAL,CAAeqB,SAAnB,EAA8B;MAC1B,MAAMqB,MAAM,GAAG,KAAKF,UAAL,EAAf;;MACAE,MAAM,CAACiD,mBAAP,CAA2B,QAA3B,EAAqC,KAAKL,eAA1C;MACA5C,MAAM,CAACiD,mBAAP,CAA2B,mBAA3B,EAAgD,KAAKL,eAArD;IACH;;IACD,KAAKD,OAAL,CAAa9I,QAAb;EACH;EACD;;;EACAoB,eAAe,GAAG;IACd,IAAI,CAAC,KAAK+H,aAAV,EAAyB;MACrB,KAAKE,mBAAL;IACH;;IACD,MAAMC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAKJ,aAAL,CAAmBI,KAA5B;MAAmCC,MAAM,EAAE,KAAKL,aAAL,CAAmBK;IAA9D,CAAf,CAJc,CAKd;;IACA,IAAI,CAAC,KAAK/F,SAAL,CAAeqB,SAApB,EAA+B;MAC3B,KAAKqE,aAAL,GAAqB,IAArB;IACH;;IACD,OAAOG,MAAP;EACH;EACD;;;EACAG,eAAe,GAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMC,cAAc,GAAG,KAAKC,yBAAL,EAAvB;IACA,MAAM;MAAEJ,KAAF;MAASC;IAAT,IAAoB,KAAKpI,eAAL,EAA1B;IACA,OAAO;MACH2G,GAAG,EAAE2B,cAAc,CAAC3B,GADjB;MAEHH,IAAI,EAAE8B,cAAc,CAAC9B,IAFlB;MAGHE,MAAM,EAAE4B,cAAc,CAAC3B,GAAf,GAAqByB,MAH1B;MAIH3B,KAAK,EAAE6B,cAAc,CAAC9B,IAAf,GAAsB2B,KAJ1B;MAKHC,MALG;MAMHD;IANG,CAAP;EAQH;EACD;;;EACAI,yBAAyB,GAAG;IACxB;IACA;IACA,IAAI,CAAC,KAAKlG,SAAL,CAAeqB,SAApB,EAA+B;MAC3B,OAAO;QAAEiD,GAAG,EAAE,CAAP;QAAUH,IAAI,EAAE;MAAhB,CAAP;IACH,CALuB,CAMxB;IACA;IACA;IACA;IACA;IACA;;;IACA,MAAMlE,QAAQ,GAAG,KAAKM,SAAtB;;IACA,MAAMmC,MAAM,GAAG,KAAKF,UAAL,EAAf;;IACA,MAAM2D,eAAe,GAAGlG,QAAQ,CAACkG,eAAjC;IACA,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAhB,EAArB;IACA,MAAM/B,GAAG,GAAG,CAAC8B,YAAY,CAAC9B,GAAd,IACRrE,QAAQ,CAACqG,IAAT,CAAc1B,SADN,IAERlC,MAAM,CAAC6D,OAFC,IAGRJ,eAAe,CAACvB,SAHR,IAIR,CAJJ;IAKA,MAAMT,IAAI,GAAG,CAACiC,YAAY,CAACjC,IAAd,IACTlE,QAAQ,CAACqG,IAAT,CAAczB,UADL,IAETnC,MAAM,CAAC8D,OAFE,IAGTL,eAAe,CAACtB,UAHP,IAIT,CAJJ;IAKA,OAAO;MAAEP,GAAF;MAAOH;IAAP,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIsB,MAAM,CAACgB,YAAY,GAAGtB,mBAAhB,EAAqC;IACvC,OAAOsB,YAAY,GAAG,CAAf,GAAmB,KAAKpB,OAAL,CAAaxJ,IAAb,CAAkB1B,SAAS,CAACsM,YAAD,CAA3B,CAAnB,GAAgE,KAAKpB,OAA5E;EACH;EACD;;;EACA7C,UAAU,GAAG;IACT,OAAO,KAAKjC,SAAL,CAAekC,WAAf,IAA8BC,MAArC;EACH;EACD;;;EACAkD,mBAAmB,GAAG;IAClB,MAAMlD,MAAM,GAAG,KAAKF,UAAL,EAAf;;IACA,KAAKkD,aAAL,GAAqB,KAAK1F,SAAL,CAAeqB,SAAf,GACf;MAAEyE,KAAK,EAAEpD,MAAM,CAACgE,UAAhB;MAA4BX,MAAM,EAAErD,MAAM,CAACiE;IAA3C,CADe,GAEf;MAAEb,KAAK,EAAE,CAAT;MAAYC,MAAM,EAAE;IAApB,CAFN;EAGH;;AAhHe;;AAkHpBX,aAAa,CAAChG,IAAd;EAAA,iBAA0GgG,aAA1G,EAnd4GzM,EAmd5G,UAAyIgC,EAAE,CAACuI,QAA5I,GAnd4GvK,EAmd5G,UAAiKA,EAAE,CAACsK,MAApK,GAnd4GtK,EAmd5G,UAAuL+B,QAAvL;AAAA;;AACA0K,aAAa,CAACjC,KAAd,kBApd4GxK,EAod5G;EAAA,OAA8GyM,aAA9G;EAAA,SAA8GA,aAA9G;EAAA,YAAyI;AAAzI;;AACA;EAAA,mDArd4GzM,EAqd5G,mBAA2FyM,aAA3F,EAAsH,CAAC;IAC3G3F,IAAI,EAAEzG,UADqG;IAE3G0G,IAAI,EAAE,CAAC;MAAE0D,UAAU,EAAE;IAAd,CAAD;EAFqG,CAAD,CAAtH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAE3D,IAAI,EAAE9E,EAAE,CAACuI;IAAX,CAAD,EAAwB;MAAEzD,IAAI,EAAE9G,EAAE,CAACsK;IAAX,CAAxB,EAA6C;MAAExD,IAAI,EAAE4D,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACpE7D,IAAI,EAAExG;MAD8D,CAAD,EAEpE;QACCwG,IAAI,EAAEvG,MADP;QAECwG,IAAI,EAAE,CAAChF,QAAD;MAFP,CAFoE;IAA/B,CAA7C,CAAP;EAMH,CAVL;AAAA;AAYA;;;AACA,SAASkM,WAAT,CAAqBC,EAArB,EAAyBC,EAAzB,EAA6B;EACzB,OAAOD,EAAE,CAACrJ,KAAH,IAAYsJ,EAAE,CAACtJ,KAAf,IAAwBqJ,EAAE,CAACpJ,GAAH,IAAUqJ,EAAE,CAACrJ,GAA5C;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,MAAMsJ,gBAAgB,GAAG,OAAOC,qBAAP,KAAiC,WAAjC,GAA+ClN,uBAA/C,GAAyEC,aAAlG;AACA;;AACA,MAAMkN,wBAAN,SAAuC1D,aAAvC,CAAqD;EACjDhI,WAAW,CAACiI,UAAD,EAAa0D,kBAAb,EAAiCxD,MAAjC,EAAyC1E,eAAzC,EAA0D2E,GAA1D,EAA+DF,gBAA/D,EAAiF0D,aAAjF,EAAgG;IACvG,MAAM3D,UAAN,EAAkBC,gBAAlB,EAAoCC,MAApC,EAA4CC,GAA5C;IACA,KAAKH,UAAL,GAAkBA,UAAlB;IACA,KAAK0D,kBAAL,GAA0BA,kBAA1B;IACA,KAAKlI,eAAL,GAAuBA,eAAvB;IACA;;IACA,KAAKoI,gBAAL,GAAwB,IAAI1N,OAAJ,EAAxB;IACA;;IACA,KAAK2N,qBAAL,GAA6B,IAAI3N,OAAJ,EAA7B;IACA,KAAK4N,YAAL,GAAoB,UAApB;IACA,KAAKC,WAAL,GAAmB,KAAnB,CAVuG,CAWvG;IACA;IACA;IACA;;IACA;;IACA,KAAK3L,mBAAL,GAA2B,IAAIhC,UAAJ,CAAgB0H,QAAD,IAAc,KAAKtC,eAAL,CAAqBpD,mBAArB,CAAyCiF,SAAzC,CAAmD7D,KAAK,IAAIwK,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM,KAAKhE,MAAL,CAAYiE,GAAZ,CAAgB,MAAMrG,QAAQ,CAACzC,IAAT,CAAc7B,KAAd,CAAtB,CAA7B,CAA5D,CAA7B,CAA3B;IACA;;IACA,KAAK4K,mBAAL,GAA2B,KAAKP,qBAAhC;IACA;AACR;AACA;;IACQ,KAAKQ,iBAAL,GAAyB,CAAzB;IACA;;IACA,KAAKC,kBAAL,GAA0B,EAA1B;IACA;;IACA,KAAKC,mBAAL,GAA2B,EAA3B;IACA;;IACA,KAAKC,cAAL,GAAsB;MAAExK,KAAK,EAAE,CAAT;MAAYC,GAAG,EAAE;IAAjB,CAAtB;IACA;;IACA,KAAKwK,WAAL,GAAmB,CAAnB;IACA;;IACA,KAAKvC,aAAL,GAAqB,CAArB;IACA;;IACA,KAAKwC,sBAAL,GAA8B,CAA9B;IACA;AACR;AACA;AACA;;IACQ,KAAKC,kCAAL,GAA0C,KAA1C;IACA;;IACA,KAAKC,yBAAL,GAAiC,KAAjC;IACA;;IACA,KAAKC,wBAAL,GAAgC,EAAhC;IACA;;IACA,KAAKC,gBAAL,GAAwBtO,YAAY,CAACuO,KAArC;;IACA,IAAI,CAACvJ,eAAD,KAAqB,OAAOvC,SAAP,KAAqB,WAArB,IAAoCA,SAAzD,CAAJ,EAAyE;MACrE,MAAMC,KAAK,CAAC,gFAAD,CAAX;IACH;;IACD,KAAK4L,gBAAL,GAAwBnB,aAAa,CAAC1B,MAAd,GAAuB5E,SAAvB,CAAiC,MAAM;MAC3D,KAAK2H,iBAAL;IACH,CAFuB,CAAxB;EAGH;EACD;;;EACe,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKnB,YAAZ;EACH;;EACc,IAAXmB,WAAW,CAACA,WAAD,EAAc;IACzB,IAAI,KAAKnB,YAAL,KAAsBmB,WAA1B,EAAuC;MACnC,KAAKnB,YAAL,GAAoBmB,WAApB;;MACA,KAAKC,oBAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACkB,IAAVC,UAAU,GAAG;IACb,OAAO,KAAKpB,WAAZ;EACH;;EACa,IAAVoB,UAAU,CAACzJ,KAAD,EAAQ;IAClB,KAAKqI,WAAL,GAAmB7O,qBAAqB,CAACwG,KAAD,CAAxC;EACH;;EACD4E,QAAQ,GAAG;IACP,MAAMA,QAAN,GADO,CAEP;IACA;IACA;IACA;;IACA,KAAKJ,MAAL,CAAYV,iBAAZ,CAA8B,MAAMwE,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;MAC7D,KAAKkB,oBAAL;;MACA,KAAK5J,eAAL,CAAqB9C,MAArB,CAA4B,IAA5B;;MACA,KAAK0E,eAAL,GACK/E,IADL,EAEA;MACAvB,SAAS,CAAC,IAAD,CAHT,EAIA;MACA;MACA;MACAH,SAAS,CAAC,CAAD,EAAI4M,gBAAJ,CAPT,EAQKlG,SARL,CAQe,MAAM,KAAK7B,eAAL,CAAqBrC,iBAArB,EARrB;;MASA,KAAKkM,0BAAL;IACH,CAbmC,CAApC;EAcH;;EACDnH,WAAW,GAAG;IACV,KAAKpF,MAAL;;IACA,KAAK0C,eAAL,CAAqB1C,MAArB,GAFU,CAGV;;;IACA,KAAK+K,qBAAL,CAA2B9K,QAA3B;;IACA,KAAK6K,gBAAL,CAAsB7K,QAAtB;;IACA,KAAK+L,gBAAL,CAAsBrH,WAAtB;;IACA,MAAMS,WAAN;EACH;EACD;;;EACAxF,MAAM,CAAC4M,KAAD,EAAQ;IACV,IAAI,KAAKC,MAAL,KAAgB,OAAOtM,SAAP,KAAqB,WAArB,IAAoCA,SAApD,CAAJ,EAAoE;MAChE,MAAMC,KAAK,CAAC,+CAAD,CAAX;IACH,CAHS,CAIV;IACA;IACA;;;IACA,KAAKgH,MAAL,CAAYV,iBAAZ,CAA8B,MAAM;MAChC,KAAK+F,MAAL,GAAcD,KAAd;;MACA,KAAKC,MAAL,CAAYC,UAAZ,CAAuBnN,IAAvB,CAA4BxB,SAAS,CAAC,KAAK+M,gBAAN,CAArC,EAA8DvG,SAA9D,CAAwEoI,IAAI,IAAI;QAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAvB;;QACA,IAAID,SAAS,KAAK,KAAKjB,WAAvB,EAAoC;UAChC,KAAKA,WAAL,GAAmBiB,SAAnB;;UACA,KAAKlK,eAAL,CAAqBpC,mBAArB;QACH;;QACD,KAAKwM,kBAAL;MACH,CAPD;IAQH,CAVD;EAWH;EACD;;;EACA9M,MAAM,GAAG;IACL,KAAKyM,MAAL,GAAc,IAAd;;IACA,KAAK3B,gBAAL,CAAsBvI,IAAtB;EACH;EACD;;;EACAzB,aAAa,GAAG;IACZ,OAAO,KAAK6K,WAAZ;EACH;EACD;;;EACAtK,eAAe,GAAG;IACd,OAAO,KAAK+H,aAAZ;EACH,CAxIgD,CAyIjD;EACA;EACA;EACA;;EACA;;;EACApI,gBAAgB,GAAG;IACf,OAAO,KAAK0K,cAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACI7K,mBAAmB,CAACkM,IAAD,EAAO;IACtB,IAAI,KAAKxB,iBAAL,KAA2BwB,IAA/B,EAAqC;MACjC,KAAKxB,iBAAL,GAAyBwB,IAAzB;;MACA,KAAKX,oBAAL;;MACA,KAAKG,0BAAL;IACH;EACJ;EACD;;;EACAlK,gBAAgB,CAAC2K,KAAD,EAAQ;IACpB,IAAI,CAAC1C,WAAW,CAAC,KAAKoB,cAAN,EAAsBsB,KAAtB,CAAhB,EAA8C;MAC1C,IAAI,KAAKX,UAAT,EAAqB;QACjBW,KAAK,GAAG;UAAE9L,KAAK,EAAE,CAAT;UAAYC,GAAG,EAAEQ,IAAI,CAACG,GAAL,CAAS,KAAK4J,cAAL,CAAoBvK,GAA7B,EAAkC6L,KAAK,CAAC7L,GAAxC;QAAjB,CAAR;MACH;;MACD,KAAK4J,qBAAL,CAA2BxI,IAA3B,CAAiC,KAAKmJ,cAAL,GAAsBsB,KAAvD;;MACA,KAAKT,0BAAL,CAAgC,MAAM,KAAK7J,eAAL,CAAqBnC,iBAArB,EAAtC;IACH;EACJ;EACD;AACJ;AACA;;;EACI0M,+BAA+B,GAAG;IAC9B,OAAO,KAAKpB,kCAAL,GAA0C,IAA1C,GAAiD,KAAKD,sBAA7D;EACH;EACD;AACJ;AACA;AACA;;;EACItJ,wBAAwB,CAAC4K,MAAD,EAASC,EAAE,GAAG,UAAd,EAA0B;IAC9C;IACAD,MAAM,GAAG,KAAKb,UAAL,IAAmBc,EAAE,KAAK,UAA1B,GAAuC,CAAvC,GAA2CD,MAApD,CAF8C,CAG9C;IACA;;IACA,MAAMtF,KAAK,GAAG,KAAKP,GAAL,IAAY,KAAKA,GAAL,CAASzE,KAAT,IAAkB,KAA5C;IACA,MAAMwK,YAAY,GAAG,KAAKjB,WAAL,IAAoB,YAAzC;IACA,MAAMkB,IAAI,GAAGD,YAAY,GAAG,GAAH,GAAS,GAAlC;IACA,MAAME,aAAa,GAAGF,YAAY,IAAIxF,KAAhB,GAAwB,CAAC,CAAzB,GAA6B,CAAnD;IACA,IAAI2F,SAAS,GAAI,YAAWF,IAAK,IAAGG,MAAM,CAACF,aAAa,GAAGJ,MAAjB,CAAyB,KAAnE;IACA,KAAKtB,sBAAL,GAA8BsB,MAA9B;;IACA,IAAIC,EAAE,KAAK,QAAX,EAAqB;MACjBI,SAAS,IAAK,aAAYF,IAAK,SAA/B,CADiB,CAEjB;MACA;MACA;;MACA,KAAKxB,kCAAL,GAA0C,IAA1C;IACH;;IACD,IAAI,KAAK4B,yBAAL,IAAkCF,SAAtC,EAAiD;MAC7C;MACA;MACA,KAAKE,yBAAL,GAAiCF,SAAjC;;MACA,KAAKhB,0BAAL,CAAgC,MAAM;QAClC,IAAI,KAAKV,kCAAT,EAA6C;UACzC,KAAKD,sBAAL,IAA+B,KAAK8B,0BAAL,EAA/B;UACA,KAAK7B,kCAAL,GAA0C,KAA1C;UACA,KAAKvJ,wBAAL,CAA8B,KAAKsJ,sBAAnC;QACH,CAJD,MAKK;UACD,KAAKlJ,eAAL,CAAqBlC,uBAArB;QACH;MACJ,CATD;IAUH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACII,cAAc,CAACsM,MAAD,EAASvM,QAAQ,GAAG,MAApB,EAA4B;IACtC,MAAM+G,OAAO,GAAG;MAAE/G;IAAF,CAAhB;;IACA,IAAI,KAAKwL,WAAL,KAAqB,YAAzB,EAAuC;MACnCzE,OAAO,CAACxG,KAAR,GAAgBgM,MAAhB;IACH,CAFD,MAGK;MACDxF,OAAO,CAACM,GAAR,GAAckF,MAAd;IACH;;IACD,KAAKzF,QAAL,CAAcC,OAAd;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIjH,aAAa,CAACC,KAAD,EAAQC,QAAQ,GAAG,MAAnB,EAA2B;IACpC,KAAK+B,eAAL,CAAqBjC,aAArB,CAAmCC,KAAnC,EAA0CC,QAA1C;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIa,mBAAmB,CAACgH,IAAD,EAAO;IACtB,OAAOA,IAAI,GACL,MAAMhH,mBAAN,CAA0BgH,IAA1B,CADK,GAEL,MAAMhH,mBAAN,CAA0B,KAAK2K,WAAL,KAAqB,YAArB,GAAoC,OAApC,GAA8C,KAAxE,CAFN;EAGH;EACD;;;EACAuB,0BAA0B,GAAG;IACzB,MAAMC,SAAS,GAAG,KAAKC,eAAL,CAAqBpH,aAAvC;IACA,OAAO,KAAK2F,WAAL,KAAqB,YAArB,GAAoCwB,SAAS,CAACE,WAA9C,GAA4DF,SAAS,CAACG,YAA7E;EACH;EACD;AACJ;AACA;AACA;;;EACIC,gBAAgB,CAACf,KAAD,EAAQ;IACpB,IAAI,CAAC,KAAKP,MAAV,EAAkB;MACd,OAAO,CAAP;IACH;;IACD,OAAO,KAAKA,MAAL,CAAYsB,gBAAZ,CAA6Bf,KAA7B,EAAoC,KAAKb,WAAzC,CAAP;EACH;EACD;;;EACAD,iBAAiB,GAAG;IAChB;IACA,KAAKI,oBAAL;;IACA,KAAK5J,eAAL,CAAqBpC,mBAArB;EACH;EACD;;;EACAgM,oBAAoB,GAAG;IACnB,MAAM0B,UAAU,GAAG,KAAK9G,UAAL,CAAgBV,aAAnC;IACA,KAAK4C,aAAL,GACI,KAAK+C,WAAL,KAAqB,YAArB,GAAoC6B,UAAU,CAAC5F,WAA/C,GAA6D4F,UAAU,CAAC9F,YAD5E;EAEH;EACD;;;EACAqE,0BAA0B,CAAC0B,QAAD,EAAW;IACjC,IAAIA,QAAJ,EAAc;MACV,KAAKlC,wBAAL,CAA8B9F,IAA9B,CAAmCgI,QAAnC;IACH,CAHgC,CAIjC;IACA;;;IACA,IAAI,CAAC,KAAKnC,yBAAV,EAAqC;MACjC,KAAKA,yBAAL,GAAiC,IAAjC;MACA,KAAK1E,MAAL,CAAYV,iBAAZ,CAA8B,MAAMwE,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;QAC7D,KAAK0B,kBAAL;MACH,CAFmC,CAApC;IAGH;EACJ;EACD;;;EACAA,kBAAkB,GAAG;IACjB,KAAKhB,yBAAL,GAAiC,KAAjC,CADiB,CAEjB;IACA;IACA;IACA;;IACA,KAAK8B,eAAL,CAAqBpH,aAArB,CAAmC0H,KAAnC,CAAyCX,SAAzC,GAAqD,KAAKE,yBAA1D,CANiB,CAOjB;IACA;IACA;;IACA,KAAKrG,MAAL,CAAYiE,GAAZ,CAAgB,MAAM,KAAKT,kBAAL,CAAwBuD,YAAxB,EAAtB;IACA,MAAMC,uBAAuB,GAAG,KAAKrC,wBAArC;IACA,KAAKA,wBAAL,GAAgC,EAAhC;;IACA,KAAK,MAAMsC,EAAX,IAAiBD,uBAAjB,EAA0C;MACtCC,EAAE;IACL;EACJ;EACD;;;EACAjC,oBAAoB,GAAG;IACnB,KAAKX,mBAAL,GACI,KAAKU,WAAL,KAAqB,YAArB,GAAoC,EAApC,GAA0C,GAAE,KAAKZ,iBAAkB,IADvE;IAEA,KAAKC,kBAAL,GACI,KAAKW,WAAL,KAAqB,YAArB,GAAqC,GAAE,KAAKZ,iBAAkB,IAA9D,GAAoE,EADxE;EAEH;;AAtTgD;;AAwTrDZ,wBAAwB,CAAC7H,IAAzB;EAAA,iBAAqH6H,wBAArH,EApyB4GtO,EAoyB5G,mBAA+JA,EAAE,CAACsM,UAAlK,GApyB4GtM,EAoyB5G,mBAAyLA,EAAE,CAACiS,iBAA5L,GApyB4GjS,EAoyB5G,mBAA0NA,EAAE,CAACsK,MAA7N,GApyB4GtK,EAoyB5G,mBAAgP0C,uBAAhP,MApyB4G1C,EAoyB5G,mBAAoSmC,EAAE,CAACoK,cAAvS,MApyB4GvM,EAoyB5G,mBAAkVmH,gBAAlV,GApyB4GnH,EAoyB5G,mBAA+WyM,aAA/W;AAAA;;AACA6B,wBAAwB,CAAC4D,IAAzB,kBAryB4GlS,EAqyB5G;EAAA,MAAyGsO,wBAAzG;EAAA;EAAA;IAAA;MAryB4GtO,EAqyB5G;IAAA;;IAAA;MAAA;;MAryB4GA,EAqyB5G,qBAryB4GA,EAqyB5G;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAryB4GA,EAqyB5G;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAryB4GA,EAqyB5G,oBAAmiB,CAC3hB;IACI2G,OAAO,EAAEiE,aADb;IAEIuH,WAAW,EAAE7D;EAFjB,CAD2hB,CAAniB,GAryB4GtO,EAqyB5G;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAryB4GA,EAqyB5G;MAryB4GA,EA0yBkM,+BAL9S;MAryB4GA,EA0yBwQ,gBALpX;MAryB4GA,EA0yBmS,eAL/Y;MAryB4GA,EA0yB6d,uBALzkB;IAAA;;IAAA;MAryB4GA,EA0yB4gB,aALxnB;MAryB4GA,EA0yB4gB,gFALxnB;IAAA;EAAA;EAAA;EAAA;EAAA;AAAA;;AAMA;EAAA,mDA3yB4GA,EA2yB5G,mBAA2FsO,wBAA3F,EAAiI,CAAC;IACtHxH,IAAI,EAAEtG,SADgH;IAEtHuG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,6BAAZ;MAA2CoL,IAAI,EAAE;QAC5C,SAAS,6BADmC;QAE5C,qDAAqD,8BAFT;QAG5C,mDAAmD;MAHP,CAAjD;MAIIC,aAAa,EAAE5R,iBAAiB,CAAC6R,IAJrC;MAI2CC,eAAe,EAAE7R,uBAAuB,CAAC8R,MAJpF;MAI4FvL,SAAS,EAAE,CAClG;QACIN,OAAO,EAAEiE,aADb;QAEIuH,WAAW,EAAE7D;MAFjB,CADkG,CAJvG;MASImE,QAAQ,EAAE,shBATd;MASsiBC,MAAM,EAAE,CAAC,2sDAAD;IAT9iB,CAAD;EAFgH,CAAD,CAAjI,EAY4B,YAAY;IAChC,OAAO,CAAC;MAAE5L,IAAI,EAAE9G,EAAE,CAACsM;IAAX,CAAD,EAA0B;MAAExF,IAAI,EAAE9G,EAAE,CAACiS;IAAX,CAA1B,EAA0D;MAAEnL,IAAI,EAAE9G,EAAE,CAACsK;IAAX,CAA1D,EAA+E;MAAExD,IAAI,EAAE4D,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACtG7D,IAAI,EAAExG;MADgG,CAAD,EAEtG;QACCwG,IAAI,EAAEvG,MADP;QAECwG,IAAI,EAAE,CAACrE,uBAAD;MAFP,CAFsG;IAA/B,CAA/E,EAKW;MAAEoE,IAAI,EAAE3E,EAAE,CAACoK,cAAX;MAA2B5B,UAAU,EAAE,CAAC;QAC1C7D,IAAI,EAAExG;MADoC,CAAD;IAAvC,CALX,EAOW;MAAEwG,IAAI,EAAEK;IAAR,CAPX,EAOuC;MAAEL,IAAI,EAAE2F;IAAR,CAPvC,CAAP;EAQH,CArBL,EAqBuB;IAAEqD,WAAW,EAAE,CAAC;MACvBhJ,IAAI,EAAE1G;IADiB,CAAD,CAAf;IAEP4P,UAAU,EAAE,CAAC;MACblJ,IAAI,EAAE1G;IADO,CAAD,CAFL;IAIP6C,mBAAmB,EAAE,CAAC;MACtB6D,IAAI,EAAEnG;IADgB,CAAD,CAJd;IAMP4Q,eAAe,EAAE,CAAC;MAClBzK,IAAI,EAAElG,SADY;MAElBmG,IAAI,EAAE,CAAC,gBAAD,EAAmB;QAAE4L,MAAM,EAAE;MAAV,CAAnB;IAFY,CAAD;EANV,CArBvB;AAAA;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,SAAT,CAAmB9C,WAAnB,EAAgC+C,SAAhC,EAA2CC,IAA3C,EAAiD;EAC7C,MAAMxH,EAAE,GAAGwH,IAAX;;EACA,IAAI,CAACxH,EAAE,CAACoC,qBAAR,EAA+B;IAC3B,OAAO,CAAP;EACH;;EACD,MAAMqF,IAAI,GAAGzH,EAAE,CAACoC,qBAAH,EAAb;;EACA,IAAIoC,WAAW,KAAK,YAApB,EAAkC;IAC9B,OAAO+C,SAAS,KAAK,OAAd,GAAwBE,IAAI,CAACvH,IAA7B,GAAoCuH,IAAI,CAACtH,KAAhD;EACH;;EACD,OAAOoH,SAAS,KAAK,OAAd,GAAwBE,IAAI,CAACpH,GAA7B,GAAmCoH,IAAI,CAACrH,MAA/C;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMsH,eAAN,CAAsB;EAClBpQ,WAAW;EACX;EACAqQ,iBAFW;EAGX;EACAC,SAJW;EAKX;EACAC,QANW;EAOX;EACAC,aARW;EASX;EACAjQ,SAVW,EAUA4H,MAVA,EAUQ;IACf,KAAKkI,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,SAAL,GAAiBA,SAAjB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKjQ,SAAL,GAAiBA,SAAjB;IACA;;IACA,KAAKkQ,UAAL,GAAkB,IAAItS,OAAJ,EAAlB;IACA;;IACA,KAAKuS,kBAAL,GAA0B,IAAIvS,OAAJ,EAA1B;IACA;;IACA,KAAKsP,UAAL,GAAkB,KAAKiD,kBAAL,CAAwBpQ,IAAxB,EAClB;IACAvB,SAAS,CAAC,IAAD,CAFS,EAGlB;IACAC,QAAQ,EAJU,EAKlB;IACA;IACA;IACAC,SAAS,CAAC,CAAC,CAAC0R,IAAD,EAAOC,GAAP,CAAD,KAAiB,KAAKC,iBAAL,CAAuBF,IAAvB,EAA6BC,GAA7B,CAAlB,CARS,EASlB;IACA1R,WAAW,CAAC,CAAD,CAVO,CAAlB;IAWA;;IACA,KAAK4R,OAAL,GAAe,IAAf;IACA;;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA,KAAK1I,UAAL,GAAkB,IAAIlK,OAAJ,EAAlB;IACA,KAAKsP,UAAL,CAAgBnI,SAAhB,CAA0BoI,IAAI,IAAI;MAC9B,KAAKsD,KAAL,GAAatD,IAAb;;MACA,KAAKuD,qBAAL;IACH,CAHD;;IAIA,KAAK1Q,SAAL,CAAe8L,mBAAf,CAAmC/L,IAAnC,CAAwCxB,SAAS,CAAC,KAAKuJ,UAAN,CAAjD,EAAoE/C,SAApE,CAA8EyI,KAAK,IAAI;MACnF,KAAKtB,cAAL,GAAsBsB,KAAtB;;MACA,IAAI,KAAK0C,UAAL,CAAgBS,SAAhB,CAA0BtD,MAA9B,EAAsC;QAClCzF,MAAM,CAACiE,GAAP,CAAW,MAAM,KAAKqE,UAAL,CAAgBnN,IAAhB,CAAqB,KAAKmJ,cAA1B,CAAjB;MACH;;MACD,KAAKwE,qBAAL;IACH,CAND;;IAOA,KAAK1Q,SAAL,CAAeI,MAAf,CAAsB,IAAtB;EACH;EACD;;;EACmB,IAAfwQ,eAAe,GAAG;IAClB,OAAO,KAAKC,gBAAZ;EACH;;EACkB,IAAfD,eAAe,CAACxN,KAAD,EAAQ;IACvB,KAAKyN,gBAAL,GAAwBzN,KAAxB;;IACA,IAAIjE,YAAY,CAACiE,KAAD,CAAhB,EAAyB;MACrB,KAAK+M,kBAAL,CAAwBpN,IAAxB,CAA6BK,KAA7B;IACH,CAFD,MAGK;MACD;MACA,KAAK+M,kBAAL,CAAwBpN,IAAxB,CAA6B,IAAI3D,eAAJ,CAAoBjB,YAAY,CAACiF,KAAD,CAAZ,GAAsBA,KAAtB,GAA8B0N,KAAK,CAAC9H,IAAN,CAAW5F,KAAK,IAAI,EAApB,CAAlD,CAA7B;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EAC4B,IAApB2N,oBAAoB,GAAG;IACvB,OAAO,KAAKC,qBAAZ;EACH;;EACuB,IAApBD,oBAAoB,CAAClC,EAAD,EAAK;IACzB,KAAK2B,YAAL,GAAoB,IAApB;IACA,KAAKQ,qBAAL,GAA6BnC,EAAE,GACzB,CAAC3N,KAAD,EAAQ+P,IAAR,KAAiBpC,EAAE,CAAC3N,KAAK,IAAI,KAAKgL,cAAL,GAAsB,KAAKA,cAAL,CAAoBxK,KAA1C,GAAkD,CAAtD,CAAN,EAAgEuP,IAAhE,CADM,GAEzB1J,SAFN;EAGH;EACD;;;EACyB,IAArB2J,qBAAqB,CAAC9N,KAAD,EAAQ;IAC7B,IAAIA,KAAJ,EAAW;MACP,KAAKoN,YAAL,GAAoB,IAApB;MACA,KAAKT,SAAL,GAAiB3M,KAAjB;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACsC,IAA9B+N,8BAA8B,GAAG;IACjC,OAAO,KAAKlB,aAAL,CAAmBmB,aAA1B;EACH;;EACiC,IAA9BD,8BAA8B,CAAC5D,IAAD,EAAO;IACrC,KAAK0C,aAAL,CAAmBmB,aAAnB,GAAmC1U,oBAAoB,CAAC6Q,IAAD,CAAvD;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIgB,gBAAgB,CAACf,KAAD,EAAQb,WAAR,EAAqB;IACjC,IAAIa,KAAK,CAAC9L,KAAN,IAAe8L,KAAK,CAAC7L,GAAzB,EAA8B;MAC1B,OAAO,CAAP;IACH;;IACD,IAAI,CAAC6L,KAAK,CAAC9L,KAAN,GAAc,KAAKwK,cAAL,CAAoBxK,KAAlC,IAA2C8L,KAAK,CAAC7L,GAAN,GAAY,KAAKuK,cAAL,CAAoBvK,GAA5E,MACC,OAAOhB,SAAP,KAAqB,WAArB,IAAoCA,SADrC,CAAJ,EACqD;MACjD,MAAMC,KAAK,CAAE,0DAAF,CAAX;IACH,CAPgC,CAQjC;;;IACA,MAAMyQ,kBAAkB,GAAG7D,KAAK,CAAC9L,KAAN,GAAc,KAAKwK,cAAL,CAAoBxK,KAA7D,CATiC,CAUjC;;IACA,MAAM4P,QAAQ,GAAG9D,KAAK,CAAC7L,GAAN,GAAY6L,KAAK,CAAC9L,KAAnC,CAXiC,CAYjC;IACA;;IACA,IAAI6P,SAAJ;IACA,IAAIC,QAAJ,CAfiC,CAgBjC;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,QAApB,EAA8BG,CAAC,EAA/B,EAAmC;MAC/B,MAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAAC,GAAGJ,kBAA/B,CAAb;;MACA,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAL,CAAetE,MAA3B,EAAmC;QAC/BkE,SAAS,GAAGC,QAAQ,GAAGE,IAAI,CAACC,SAAL,CAAe,CAAf,CAAvB;QACA;MACH;IACJ,CAvBgC,CAwBjC;;;IACA,KAAK,IAAIF,CAAC,GAAGH,QAAQ,GAAG,CAAxB,EAA2BG,CAAC,GAAG,CAAC,CAAhC,EAAmCA,CAAC,EAApC,EAAwC;MACpC,MAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAAC,GAAGJ,kBAA/B,CAAb;;MACA,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAL,CAAetE,MAA3B,EAAmC;QAC/BmE,QAAQ,GAAGE,IAAI,CAACC,SAAL,CAAeD,IAAI,CAACC,SAAL,CAAetE,MAAf,GAAwB,CAAvC,CAAX;QACA;MACH;IACJ;;IACD,OAAOkE,SAAS,IAAIC,QAAb,GACD/B,SAAS,CAAC9C,WAAD,EAAc,KAAd,EAAqB6E,QAArB,CAAT,GAA0C/B,SAAS,CAAC9C,WAAD,EAAc,OAAd,EAAuB4E,SAAvB,CADlD,GAED,CAFN;EAGH;;EACDK,SAAS,GAAG;IACR,IAAI,KAAKrB,OAAL,IAAgB,KAAKC,YAAzB,EAAuC;MACnC;MACA;MACA;MACA,MAAMqB,OAAO,GAAG,KAAKtB,OAAL,CAAauB,IAAb,CAAkB,KAAKC,cAAvB,CAAhB;;MACA,IAAI,CAACF,OAAL,EAAc;QACV,KAAKG,cAAL;MACH,CAFD,MAGK;QACD,KAAKC,aAAL,CAAmBJ,OAAnB;MACH;;MACD,KAAKrB,YAAL,GAAoB,KAApB;IACH;EACJ;;EACD5K,WAAW,GAAG;IACV,KAAK5F,SAAL,CAAeQ,MAAf;;IACA,KAAK2P,kBAAL,CAAwBpN,IAAxB,CAA6BwE,SAA7B;;IACA,KAAK4I,kBAAL,CAAwB1P,QAAxB;;IACA,KAAKyP,UAAL,CAAgBzP,QAAhB;;IACA,KAAKqH,UAAL,CAAgB/E,IAAhB;;IACA,KAAK+E,UAAL,CAAgBrH,QAAhB;;IACA,KAAKwP,aAAL,CAAmBzP,MAAnB;EACH;EACD;;;EACAkQ,qBAAqB,GAAG;IACpB,IAAI,CAAC,KAAKxE,cAAV,EAA0B;MACtB;IACH;;IACD,KAAK6F,cAAL,GAAsB,KAAKtB,KAAL,CAAWyB,KAAX,CAAiB,KAAKhG,cAAL,CAAoBxK,KAArC,EAA4C,KAAKwK,cAAL,CAAoBvK,GAAhE,CAAtB;;IACA,IAAI,CAAC,KAAK4O,OAAV,EAAmB;MACf;MACA;MACA,KAAKA,OAAL,GAAe,KAAKP,QAAL,CAAcmC,IAAd,CAAmB,KAAKJ,cAAxB,EAAwCK,MAAxC,CAA+C,CAAClR,KAAD,EAAQ+P,IAAR,KAAiB;QAC3E,OAAO,KAAKF,oBAAL,GAA4B,KAAKA,oBAAL,CAA0B7P,KAA1B,EAAiC+P,IAAjC,CAA5B,GAAqEA,IAA5E;MACH,CAFc,CAAf;IAGH;;IACD,KAAKT,YAAL,GAAoB,IAApB;EACH;EACD;;;EACAF,iBAAiB,CAAC+B,KAAD,EAAQC,KAAR,EAAe;IAC5B,IAAID,KAAJ,EAAW;MACPA,KAAK,CAACE,UAAN,CAAiB,IAAjB;IACH;;IACD,KAAK/B,YAAL,GAAoB,IAApB;IACA,OAAO8B,KAAK,GAAGA,KAAK,CAACE,OAAN,CAAc,IAAd,CAAH,GAAyB3U,EAAE,EAAvC;EACH;EACD;;;EACAmU,cAAc,GAAG;IACb,MAAMS,KAAK,GAAG,KAAKhC,KAAL,CAAWpD,MAAzB;IACA,IAAIoE,CAAC,GAAG,KAAK3B,iBAAL,CAAuBzC,MAA/B;;IACA,OAAOoE,CAAC,EAAR,EAAY;MACR,MAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAA3B,CAAb;;MACAC,IAAI,CAACgB,OAAL,CAAaxR,KAAb,GAAqB,KAAKgL,cAAL,CAAoBxK,KAApB,GAA4B+P,CAAjD;MACAC,IAAI,CAACgB,OAAL,CAAaD,KAAb,GAAqBA,KAArB;;MACA,KAAKE,gCAAL,CAAsCjB,IAAI,CAACgB,OAA3C;;MACAhB,IAAI,CAACkB,aAAL;IACH;EACJ;EACD;;;EACAX,aAAa,CAACJ,OAAD,EAAU;IACnB,KAAK5B,aAAL,CAAmB4C,YAAnB,CAAgChB,OAAhC,EAAyC,KAAK/B,iBAA9C,EAAiE,CAACgD,MAAD,EAASC,sBAAT,EAAiCC,YAAjC,KAAkD,KAAKC,oBAAL,CAA0BH,MAA1B,EAAkCE,YAAlC,CAAnH,EAAoKF,MAAM,IAAIA,MAAM,CAAC7B,IAArL,EADmB,CAEnB;;;IACAY,OAAO,CAACqB,qBAAR,CAA+BJ,MAAD,IAAY;MACtC,MAAMpB,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2B4N,MAAM,CAACE,YAAlC,CAAb;;MACAtB,IAAI,CAACgB,OAAL,CAAaS,SAAb,GAAyBL,MAAM,CAAC7B,IAAhC;IACH,CAHD,EAHmB,CAOnB;;IACA,MAAMwB,KAAK,GAAG,KAAKhC,KAAL,CAAWpD,MAAzB;IACA,IAAIoE,CAAC,GAAG,KAAK3B,iBAAL,CAAuBzC,MAA/B;;IACA,OAAOoE,CAAC,EAAR,EAAY;MACR,MAAMC,IAAI,GAAG,KAAK5B,iBAAL,CAAuB5K,GAAvB,CAA2BuM,CAA3B,CAAb;;MACAC,IAAI,CAACgB,OAAL,CAAaxR,KAAb,GAAqB,KAAKgL,cAAL,CAAoBxK,KAApB,GAA4B+P,CAAjD;MACAC,IAAI,CAACgB,OAAL,CAAaD,KAAb,GAAqBA,KAArB;;MACA,KAAKE,gCAAL,CAAsCjB,IAAI,CAACgB,OAA3C;IACH;EACJ;EACD;;;EACAC,gCAAgC,CAACD,OAAD,EAAU;IACtCA,OAAO,CAACU,KAAR,GAAgBV,OAAO,CAACxR,KAAR,KAAkB,CAAlC;IACAwR,OAAO,CAACW,IAAR,GAAeX,OAAO,CAACxR,KAAR,KAAkBwR,OAAO,CAACD,KAAR,GAAgB,CAAjD;IACAC,OAAO,CAACY,IAAR,GAAeZ,OAAO,CAACxR,KAAR,GAAgB,CAAhB,KAAsB,CAArC;IACAwR,OAAO,CAACa,GAAR,GAAc,CAACb,OAAO,CAACY,IAAvB;EACH;;EACDL,oBAAoB,CAACH,MAAD,EAAS5R,KAAT,EAAgB;IAChC;IACA;IACA;IACA;IACA,OAAO;MACHsS,WAAW,EAAE,KAAKzD,SADf;MAEH2C,OAAO,EAAE;QACLS,SAAS,EAAEL,MAAM,CAAC7B,IADb;QAEL;QACA;QACAL,eAAe,EAAE,KAAKC,gBAJjB;QAKL3P,KAAK,EAAE,CAAC,CALH;QAMLuR,KAAK,EAAE,CAAC,CANH;QAOLW,KAAK,EAAE,KAPF;QAQLC,IAAI,EAAE,KARD;QASLE,GAAG,EAAE,KATA;QAULD,IAAI,EAAE;MAVD,CAFN;MAcHpS;IAdG,CAAP;EAgBH;;AAjPiB;;AAmPtB2O,eAAe,CAACvM,IAAhB;EAAA,iBAA4GuM,eAA5G,EArlC4GhT,EAqlC5G,mBAA6IA,EAAE,CAAC4W,gBAAhJ,GArlC4G5W,EAqlC5G,mBAA6KA,EAAE,CAAC6W,WAAhL,GArlC4G7W,EAqlC5G,mBAAwMA,EAAE,CAAC8W,eAA3M,GArlC4G9W,EAqlC5G,mBAAuOwC,uBAAvO,GArlC4GxC,EAqlC5G,mBAA2QsO,wBAA3Q,MArlC4GtO,EAqlC5G,mBAAgUA,EAAE,CAACsK,MAAnU;AAAA;;AACA0I,eAAe,CAACtM,IAAhB,kBAtlC4G1G,EAslC5G;EAAA,MAAgGgT,eAAhG;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAtlC4GhT,EAslC5G,oBAA0X,CAAC;IAAE2G,OAAO,EAAEnE,uBAAX;IAAoCuU,QAAQ,EAAEtU;EAA9C,CAAD,CAA1X;AAAA;;AACA;EAAA,mDAvlC4GzC,EAulC5G,mBAA2FgT,eAA3F,EAAwH,CAAC;IAC7GlM,IAAI,EAAE3G,SADuG;IAE7G4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCADX;MAECC,SAAS,EAAE,CAAC;QAAEN,OAAO,EAAEnE,uBAAX;QAAoCuU,QAAQ,EAAEtU;MAA9C,CAAD;IAFZ,CAAD;EAFuG,CAAD,CAAxH,EAM4B,YAAY;IAChC,OAAO,CAAC;MAAEqE,IAAI,EAAE9G,EAAE,CAAC4W;IAAX,CAAD,EAAgC;MAAE9P,IAAI,EAAE9G,EAAE,CAAC6W;IAAX,CAAhC,EAA0D;MAAE/P,IAAI,EAAE9G,EAAE,CAAC8W;IAAX,CAA1D,EAAwF;MAAEhQ,IAAI,EAAEzE,IAAI,CAACI,4BAAb;MAA2CkI,UAAU,EAAE,CAAC;QACvI7D,IAAI,EAAEvG,MADiI;QAEvIwG,IAAI,EAAE,CAACvE,uBAAD;MAFiI,CAAD;IAAvD,CAAxF,EAGW;MAAEsE,IAAI,EAAEwH,wBAAR;MAAkC3D,UAAU,EAAE,CAAC;QACjD7D,IAAI,EAAEjG;MAD2C,CAAD;IAA9C,CAHX,EAKW;MAAEiG,IAAI,EAAE9G,EAAE,CAACsK;IAAX,CALX,CAAP;EAMH,CAbL,EAauB;IAAEyJ,eAAe,EAAE,CAAC;MAC3BjN,IAAI,EAAE1G;IADqB,CAAD,CAAnB;IAEP8T,oBAAoB,EAAE,CAAC;MACvBpN,IAAI,EAAE1G;IADiB,CAAD,CAFf;IAIPiU,qBAAqB,EAAE,CAAC;MACxBvN,IAAI,EAAE1G;IADkB,CAAD,CAJhB;IAMPkU,8BAA8B,EAAE,CAAC;MACjCxN,IAAI,EAAE1G;IAD2B,CAAD;EANzB,CAbvB;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4W,mBAAN,CAA0B;;AAE1BA,mBAAmB,CAACvQ,IAApB;EAAA,iBAAgHuQ,mBAAhH;AAAA;;AACAA,mBAAmB,CAACC,IAApB,kBAxnC4GjX,EAwnC5G;EAAA,MAAiHgX;AAAjH;AACAA,mBAAmB,CAACE,IAApB,kBAznC4GlX,EAynC5G;;AACA;EAAA,mDA1nC4GA,EA0nC5G,mBAA2FgX,mBAA3F,EAA4H,CAAC;IACjHlQ,IAAI,EAAEhG,QAD2G;IAEjHiG,IAAI,EAAE,CAAC;MACCoQ,OAAO,EAAE,CAACvM,aAAD,CADV;MAECwM,YAAY,EAAE,CAACxM,aAAD;IAFf,CAAD;EAF2G,CAAD,CAA5H;AAAA;AAOA;AACA;AACA;;;AACA,MAAMyM,eAAN,CAAsB;;AAEtBA,eAAe,CAAC5Q,IAAhB;EAAA,iBAA4G4Q,eAA5G;AAAA;;AACAA,eAAe,CAACJ,IAAhB,kBAvoC4GjX,EAuoC5G;EAAA,MAA6GqX;AAA7G;AAGAA,eAAe,CAACH,IAAhB,kBA1oC4GlX,EA0oC5G;EAAA,UAAwIoC,UAAxI,EAAoJ4U,mBAApJ,EAAyK5U,UAAzK,EAAqL4U,mBAArL;AAAA;;AACA;EAAA,mDA3oC4GhX,EA2oC5G,mBAA2FqX,eAA3F,EAAwH,CAAC;IAC7GvQ,IAAI,EAAEhG,QADuG;IAE7GiG,IAAI,EAAE,CAAC;MACCuQ,OAAO,EAAE,CAAClV,UAAD,EAAa4U,mBAAb,CADV;MAECG,OAAO,EAAE,CACL/U,UADK,EAEL4U,mBAFK,EAGL1Q,yBAHK,EAIL0M,eAJK,EAKL1E,wBALK,CAFV;MASC8I,YAAY,EAAE,CAAC9Q,yBAAD,EAA4B0M,eAA5B,EAA6C1E,wBAA7C;IATf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAShI,yBAAT,EAAoCsE,aAApC,EAAmDoM,mBAAnD,EAAwEhE,eAAxE,EAAyF1E,wBAAzF,EAAmH9B,mBAAnH,EAAwItF,mBAAxI,EAA6JvE,8BAA7J,EAA6LwE,gBAA7L,EAA+MkQ,eAA/M,EAAgO3U,uBAAhO,EAAyP+J,aAAzP,EAAwQtG,sCAAxQ"}, "metadata": {}, "sourceType": "module"}