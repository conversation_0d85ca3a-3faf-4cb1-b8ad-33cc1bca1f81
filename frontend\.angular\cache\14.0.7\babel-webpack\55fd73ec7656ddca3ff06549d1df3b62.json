{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nexport class QueueAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  schedule(state, delay = 0) {\n    if (delay > 0) {\n      return super.schedule(state, delay);\n    }\n\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  }\n\n  execute(state, delay) {\n    return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.flush(this);\n    return 0;\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "QueueAction", "constructor", "scheduler", "work", "schedule", "state", "delay", "flush", "execute", "closed", "_execute", "requestAsyncId", "id"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/QueueAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nexport class QueueAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    schedule(state, delay = 0) {\n        if (delay > 0) {\n            return super.schedule(state, delay);\n        }\n        this.delay = delay;\n        this.state = state;\n        this.scheduler.flush(this);\n        return this;\n    }\n    execute(state, delay) {\n        return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.flush(this);\n        return 0;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,OAAO,MAAMC,WAAN,SAA0BD,WAA1B,CAAsC;EACzCE,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;EACH;;EACDC,QAAQ,CAACC,KAAD,EAAQC,KAAK,GAAG,CAAhB,EAAmB;IACvB,IAAIA,KAAK,GAAG,CAAZ,EAAe;MACX,OAAO,MAAMF,QAAN,CAAeC,KAAf,EAAsBC,KAAtB,CAAP;IACH;;IACD,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKH,SAAL,CAAeK,KAAf,CAAqB,IAArB;IACA,OAAO,IAAP;EACH;;EACDC,OAAO,CAACH,KAAD,EAAQC,KAAR,EAAe;IAClB,OAAOA,KAAK,GAAG,CAAR,IAAa,KAAKG,MAAlB,GAA2B,MAAMD,OAAN,CAAcH,KAAd,EAAqBC,KAArB,CAA3B,GAAyD,KAAKI,QAAL,CAAcL,KAAd,EAAqBC,KAArB,CAAhE;EACH;;EACDK,cAAc,CAACT,SAAD,EAAYU,EAAZ,EAAgBN,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAKA,KAAK,IAAI,IAAT,IAAiBA,KAAK,GAAG,CAA1B,IAAiCA,KAAK,IAAI,IAAT,IAAiB,KAAKA,KAAL,GAAa,CAAnE,EAAuE;MACnE,OAAO,MAAMK,cAAN,CAAqBT,SAArB,EAAgCU,EAAhC,EAAoCN,KAApC,CAAP;IACH;;IACDJ,SAAS,CAACK,KAAV,CAAgB,IAAhB;IACA,OAAO,CAAP;EACH;;AAxBwC"}, "metadata": {}, "sourceType": "module"}