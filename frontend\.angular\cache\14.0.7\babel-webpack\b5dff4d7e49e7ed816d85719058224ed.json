{"ast": null, "code": "import { reduce } from './reduce';\nexport function count(predicate) {\n  return reduce((total, value, i) => !predicate || predicate(value, i) ? total + 1 : total, 0);\n}", "map": {"version": 3, "names": ["reduce", "count", "predicate", "total", "value", "i"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/count.js"], "sourcesContent": ["import { reduce } from './reduce';\nexport function count(predicate) {\n    return reduce((total, value, i) => (!predicate || predicate(value, i) ? total + 1 : total), 0);\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,SAASC,KAAT,CAAeC,SAAf,EAA0B;EAC7B,OAAOF,MAAM,CAAC,CAACG,KAAD,EAAQC,KAAR,EAAeC,CAAf,KAAsB,CAACH,SAAD,IAAcA,SAAS,CAACE,KAAD,EAAQC,CAAR,CAAvB,GAAoCF,KAAK,GAAG,CAA5C,GAAgDA,KAAvE,EAA+E,CAA/E,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}