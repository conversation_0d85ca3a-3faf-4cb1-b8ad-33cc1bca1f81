version: '3.8'

services:
  keycloak-db:
    image: postgres:15
    container_name: keycloak-postgres
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloak123
    volumes:
      - keycloak_postgres_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - keycloak-network

  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    container_name: keycloak-server
    environment:
      KC_DB: postgres
      KC_DB_URL: *******************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak123
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8090
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_LOG_LEVEL: info
      KC_METRICS_ENABLED: true
      KC_HEALTH_ENABLED: true
      KEYCLOAK_ADMIN: admin
      K<PERSON>CLOAK_ADMIN_PASSWORD: admin123
    command: start-dev
    ports:
      - "8090:8080"
    depends_on:
      - keycloak-db
    networks:
      - keycloak-network
    volumes:
      - ./keycloak/themes:/opt/keycloak/themes
      - ./keycloak/imports:/opt/keycloak/data/import

volumes:
  keycloak_postgres_data:

networks:
  keycloak-network:
    driver: bridge
