{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n  return new Observable(subscriber => {\n    innerFrom(observableFactory()).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "defer", "observableFactory", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/defer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nexport function defer(observableFactory) {\n    return new Observable((subscriber) => {\n        innerFrom(observableFactory()).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,KAAT,CAAeC,iBAAf,EAAkC;EACrC,OAAO,IAAIH,UAAJ,CAAgBI,UAAD,IAAgB;IAClCH,SAAS,CAACE,iBAAiB,EAAlB,CAAT,CAA+BE,SAA/B,CAAyCD,UAAzC;EACH,CAFM,CAAP;AAGH"}, "metadata": {}, "sourceType": "module"}