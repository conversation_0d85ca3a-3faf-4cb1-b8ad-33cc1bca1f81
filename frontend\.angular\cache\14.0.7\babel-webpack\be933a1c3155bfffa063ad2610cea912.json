{"ast": null, "code": "import { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\nexport function exhaustAll() {\n  return exhaustMap(identity);\n}", "map": {"version": 3, "names": ["exhaustMap", "identity", "exhaustAll"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/exhaustAll.js"], "sourcesContent": ["import { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\nexport function exhaustAll() {\n    return exhaustMap(identity);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,OAAO,SAASC,UAAT,GAAsB;EACzB,OAAOF,UAAU,CAACC,QAAD,CAAjB;AACH"}, "metadata": {}, "sourceType": "module"}