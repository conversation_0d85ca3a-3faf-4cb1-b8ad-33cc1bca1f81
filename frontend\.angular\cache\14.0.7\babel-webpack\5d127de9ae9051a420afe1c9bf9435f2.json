{"ast": null, "code": "import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nexport { log, setLogLevel };", "map": {"version": 3, "names": ["logger", "name", "defaultLevel", "setLogLevel", "level", "configure<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "log", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/webpack-dev-server/client/utils/log.js"], "sourcesContent": ["import logger from \"../modules/logger/index.js\";\nvar name = \"webpack-dev-server\"; // default level is set on the client side, so it does not need\n// to be set by the CLI or API\n\nvar defaultLevel = \"info\"; // options new options, merge with old options\n\n/**\n * @param {false | true | \"none\" | \"error\" | \"warn\" | \"info\" | \"log\" | \"verbose\"} level\n * @returns {void}\n */\n\nfunction setLogLevel(level) {\n  logger.configureDefaultLogger({\n    level: level\n  });\n}\n\nsetLogLevel(defaultLevel);\nvar log = logger.getLogger(name);\nexport { log, setLogLevel };"], "mappings": "AAAA,OAAOA,MAAP,MAAmB,4BAAnB;AACA,IAAIC,IAAI,GAAG,oBAAX,C,CAAiC;AACjC;;AAEA,IAAIC,YAAY,GAAG,MAAnB,C,CAA2B;;AAE3B;AACA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqBC,KAArB,EAA4B;EAC1BJ,MAAM,CAACK,sBAAP,CAA8B;IAC5BD,KAAK,EAAEA;EADqB,CAA9B;AAGD;;AAEDD,WAAW,CAACD,YAAD,CAAX;AACA,IAAII,GAAG,GAAGN,MAAM,CAACO,SAAP,CAAiBN,IAAjB,CAAV;AACA,SAASK,GAAT,EAAcH,WAAd"}, "metadata": {}, "sourceType": "module"}