{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { getFullName } from '../../../../../models/user.model';\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../service/user.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"primeng/table\";\nimport * as i6 from \"primeng/toolbar\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"primeng/toast\";\nimport * as i10 from \"primeng/inputtext\";\n\nfunction TrainersComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.openNew());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deleteSelectedUsers());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.selectedUsers || !ctx_r0.selectedUsers.length);\n  }\n}\n\nfunction TrainersComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h5\", 21);\n    i0.ɵɵtext(2, \"Manage Trainers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵlistener(\"input\", function TrainersComponent_ng_template_8_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n\n      const _r1 = i0.ɵɵreference(7);\n\n      return i0.ɵɵresetView(ctx_r13.onGlobalFilter(_r1, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction TrainersComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 25);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 26);\n    i0.ɵɵtext(4, \"ID \");\n    i0.ɵɵelement(5, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 28);\n    i0.ɵɵtext(7, \"Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 30);\n    i0.ɵɵtext(10, \"Email \");\n    i0.ɵɵelement(11, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 32);\n    i0.ɵɵtext(13, \"Phone \");\n    i0.ɵɵelement(14, \"p-sortIcon\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 34);\n    i0.ɵɵtext(16, \"Speciality \");\n    i0.ɵɵelement(17, \"p-sortIcon\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"th\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TrainersComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"div\", 37)(15, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_10_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editUser(user_r15));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_10_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const user_r15 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.deleteUser(user_r15));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", user_r15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.phone || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r15.specialite || \"N/A\");\n  }\n}\n\nfunction TrainersComponent_ng_template_12_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 52);\n    i0.ɵɵtext(1, \"First name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TrainersComponent_ng_template_12_small_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 52);\n    i0.ɵɵtext(1, \"Last name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TrainersComponent_ng_template_12_small_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 52);\n    i0.ɵɵtext(1, \"Email is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"ng-invalid ng-dirty\": a0\n  };\n};\n\nfunction TrainersComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"label\", 41);\n    i0.ɵɵtext(2, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 42);\n    i0.ɵɵlistener(\"ngModelChange\", function TrainersComponent_ng_template_12_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.user.first_name = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TrainersComponent_ng_template_12_small_4_Template, 2, 0, \"small\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 40)(6, \"label\", 44);\n    i0.ɵɵtext(7, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 45);\n    i0.ɵɵlistener(\"ngModelChange\", function TrainersComponent_ng_template_12_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.user.last_name = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, TrainersComponent_ng_template_12_small_9_Template, 2, 0, \"small\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"label\", 46);\n    i0.ɵɵtext(12, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"input\", 47);\n    i0.ɵɵlistener(\"ngModelChange\", function TrainersComponent_ng_template_12_Template_input_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.user.email = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, TrainersComponent_ng_template_12_small_14_Template, 2, 0, \"small\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 40)(16, \"label\", 48);\n    i0.ɵɵtext(17, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 49);\n    i0.ɵɵlistener(\"ngModelChange\", function TrainersComponent_ng_template_12_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.user.phone = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 40)(20, \"label\", 50);\n    i0.ɵɵtext(21, \"Speciality\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 51);\n    i0.ɵɵlistener(\"ngModelChange\", function TrainersComponent_ng_template_12_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.user.specialite = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.user.first_name)(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx_r5.submitted && !ctx_r5.user.first_name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.user.first_name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.user.last_name)(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, ctx_r5.submitted && !ctx_r5.user.last_name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.user.last_name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.user.email)(\"ngClass\", i0.ɵɵpureFunction1(15, _c0, ctx_r5.submitted && !ctx_r5.user.email));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.user.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.user.phone);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.user.specialite);\n  }\n}\n\nfunction TrainersComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.hideDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.saveUser());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TrainersComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Are you sure you want to delete \");\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"?\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.user.name);\n  }\n}\n\nfunction TrainersComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.deleteUserDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_18_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.confirmDelete());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TrainersComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.deleteUsersDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function TrainersComponent_ng_template_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.confirmDeleteSelected());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return [\"name\", \"email\", \"phone\", \"specialite\"];\n};\n\nconst _c2 = function () {\n  return [10, 20, 30];\n};\n\nconst _c3 = function () {\n  return {\n    width: \"450px\"\n  };\n};\n\nexport class TrainersComponent {\n  constructor(userService, messageService) {\n    this.userService = userService;\n    this.messageService = messageService;\n    this.userDialog = false;\n    this.deleteUserDialog = false;\n    this.deleteUsersDialog = false;\n    this.users = [];\n    this.user = {};\n    this.selectedUsers = [];\n    this.submitted = false;\n    this.cols = [];\n    this.rowsPerPageOptions = [5, 10, 20];\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // Load trainers\n        const users = yield _this.userService.getUsersByRole('formateur');\n        _this.users = users.map(user => Object.assign(Object.assign({}, user), {\n          name: getFullName(user)\n        }));\n      } catch (error) {\n        console.error('Error loading trainers:', error);\n\n        _this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load trainers',\n          life: 3000\n        });\n      }\n\n      _this.cols = [{\n        field: 'id',\n        header: 'ID'\n      }, {\n        field: 'name',\n        header: 'Name'\n      }, {\n        field: 'email',\n        header: 'Email'\n      }, {\n        field: 'phone',\n        header: 'Phone'\n      }, {\n        field: 'specialite',\n        header: 'Speciality'\n      }];\n    })();\n  }\n\n  openNew() {\n    this.user = {\n      role: 'formateur',\n      first_name: '',\n      last_name: '',\n      email: '',\n      specialite: ''\n    };\n    this.submitted = false;\n    this.userDialog = true;\n  } // Table global filter\n\n\n  onGlobalFilter(table, event) {\n    const input = event.target;\n    table.filterGlobal(input.value, 'contains');\n  } // CRUD methods\n\n\n  editUser(user) {\n    this.user = Object.assign({}, user);\n    this.userDialog = true;\n  }\n\n  deleteUser(user) {\n    this.user = user;\n    this.deleteUserDialog = true;\n  }\n\n  deleteSelectedUsers() {\n    this.deleteUsersDialog = true;\n  }\n\n  hideDialog() {\n    this.userDialog = false;\n    this.submitted = false;\n  }\n\n  saveUser() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n\n      _this2.submitted = true;\n\n      if (((_a = _this2.user.first_name) === null || _a === void 0 ? void 0 : _a.trim()) && ((_b = _this2.user.last_name) === null || _b === void 0 ? void 0 : _b.trim()) && ((_c = _this2.user.email) === null || _c === void 0 ? void 0 : _c.trim())) {\n        try {\n          console.log('Saving user data:', _this2.user); // Debug log\n\n          if (_this2.user.id) {\n            // Update existing trainer\n            console.log('Updating trainer with ID:', _this2.user.id);\n            const updatedUser = yield lastValueFrom(_this2.userService.updateUser(_this2.user.id, _this2.user));\n\n            const index = _this2.findIndexById(_this2.user.id);\n\n            _this2.users[index] = Object.assign(Object.assign({}, updatedUser), {\n              name: getFullName(updatedUser)\n            });\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Trainer Updated',\n              life: 3000\n            });\n          } else {\n            // Create new trainer\n            const userData = {\n              first_name: _this2.user.first_name,\n              last_name: _this2.user.last_name,\n              email: _this2.user.email,\n              password: 'defaultPassword123',\n              role: 'formateur',\n              phone: _this2.user.phone || null,\n              specialite: _this2.user.specialite || null,\n              team_id: _this2.user.team_id || null\n            };\n            console.log('Creating trainer with data:', userData);\n            const newUser = yield lastValueFrom(_this2.userService.createUser(userData));\n\n            _this2.users.push(Object.assign(Object.assign({}, newUser), {\n              name: getFullName(newUser)\n            }));\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Trainer Created',\n              life: 3000\n            });\n          }\n\n          _this2.userDialog = false;\n          _this2.user = {};\n        } catch (error) {\n          console.error('Error saving trainer:', error);\n          console.error('Error details:', error); // Show more detailed error message\n\n          let errorMessage = 'Failed to save trainer';\n\n          if (error && typeof error === 'object' && 'error' in error) {\n            const errorObj = error;\n\n            if (errorObj.error && errorObj.error.message) {\n              errorMessage = errorObj.error.message;\n            } else if (errorObj.message) {\n              errorMessage = errorObj.message;\n            }\n          }\n\n          _this2.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: errorMessage,\n            life: 5000\n          });\n        }\n      } else {\n        _this2.messageService.add({\n          severity: 'warn',\n          summary: 'Validation Error',\n          detail: 'Please fill in all required fields (First Name, Last Name, Email)',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  confirmDelete() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield lastValueFrom(_this3.userService.deleteUser(_this3.user.id));\n        _this3.users = _this3.users.filter(val => val.id !== _this3.user.id);\n        _this3.deleteUserDialog = false;\n        _this3.user = {};\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Trainer Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting trainer:', error);\n\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete trainer',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  confirmDeleteSelected() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        for (const user of _this4.selectedUsers) {\n          yield lastValueFrom(_this4.userService.deleteUser(user.id));\n        }\n\n        _this4.users = _this4.users.filter(val => !_this4.selectedUsers.includes(val));\n        _this4.deleteUsersDialog = false;\n        _this4.selectedUsers = [];\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Trainers Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting trainers:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete trainers',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  findIndexById(id) {\n    return this.users.findIndex(u => u.id === id);\n  }\n\n}\n\nTrainersComponent.ɵfac = function TrainersComponent_Factory(t) {\n  return new (t || TrainersComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.MessageService));\n};\n\nTrainersComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TrainersComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 25,\n  vars: 27,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\", \"px-6\", \"py-6\"], [\"styleClass\", \"mb-4\"], [\"pTemplate\", \"left\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} trainers\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt\", \"\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"header\", \"Trainer Details\", 1, \"p-fluid\", 3, \"visible\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [\"header\", \"Confirm\", 3, \"visible\", \"modal\", \"visibleChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-exclamation-triangle\", \"mr-3\", 2, \"font-size\", \"2rem\"], [4, \"ngIf\"], [1, \"my-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"New\", \"icon\", \"pi pi-plus\", 1, \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Delete\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", 3, \"disabled\", \"click\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"md:align-items-center\"], [1, \"m-0\"], [1, \"block\", \"mt-2\", \"md:mt-0\", \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"id\"], [\"field\", \"id\"], [\"pSortableColumn\", \"name\"], [\"field\", \"name\"], [\"pSortableColumn\", \"email\"], [\"field\", \"email\"], [\"pSortableColumn\", \"phone\"], [\"field\", \"phone\"], [\"pSortableColumn\", \"specialite\"], [\"field\", \"specialite\"], [3, \"value\"], [1, \"flex\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-pencil\", 1, \"p-button-rounded\", \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-trash\", 1, \"p-button-rounded\", \"p-button-warning\", 3, \"click\"], [1, \"field\"], [\"for\", \"first_name\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"first_name\", \"required\", \"\", \"autofocus\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"class\", \"ng-dirty ng-invalid\", 4, \"ngIf\"], [\"for\", \"last_name\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"last_name\", \"required\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"for\", \"email\"], [\"type\", \"email\", \"pInputText\", \"\", \"id\", \"email\", \"required\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"for\", \"phone\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"phone\", \"placeholder\", \"Optional\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"specialite\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"specialite\", \"placeholder\", \"e.g., Web Development, UI/UX Design, Cybersecurity\", 3, \"ngModel\", \"ngModelChange\"], [1, \"ng-dirty\", \"ng-invalid\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Save\", \"icon\", \"pi pi-check\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-times\", \"label\", \"No\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-check\", \"label\", \"Yes\", 1, \"p-button-text\", 3, \"click\"]],\n  template: function TrainersComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n      i0.ɵɵelement(3, \"p-toast\");\n      i0.ɵɵelementStart(4, \"p-toolbar\", 3);\n      i0.ɵɵtemplate(5, TrainersComponent_ng_template_5_Template, 3, 1, \"ng-template\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"p-table\", 5, 6);\n      i0.ɵɵlistener(\"selectionChange\", function TrainersComponent_Template_p_table_selectionChange_6_listener($event) {\n        return ctx.selectedUsers = $event;\n      });\n      i0.ɵɵtemplate(8, TrainersComponent_ng_template_8_Template, 6, 0, \"ng-template\", 7);\n      i0.ɵɵtemplate(9, TrainersComponent_ng_template_9_Template, 19, 0, \"ng-template\", 8);\n      i0.ɵɵtemplate(10, TrainersComponent_ng_template_10_Template, 17, 6, \"ng-template\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(11, \"p-dialog\", 10);\n      i0.ɵɵlistener(\"visibleChange\", function TrainersComponent_Template_p_dialog_visibleChange_11_listener($event) {\n        return ctx.userDialog = $event;\n      });\n      i0.ɵɵtemplate(12, TrainersComponent_ng_template_12_Template, 23, 17, \"ng-template\", 11);\n      i0.ɵɵtemplate(13, TrainersComponent_ng_template_13_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function TrainersComponent_Template_p_dialog_visibleChange_14_listener($event) {\n        return ctx.deleteUserDialog = $event;\n      });\n      i0.ɵɵelementStart(15, \"div\", 14);\n      i0.ɵɵelement(16, \"i\", 15);\n      i0.ɵɵtemplate(17, TrainersComponent_span_17_Template, 5, 1, \"span\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(18, TrainersComponent_ng_template_18_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function TrainersComponent_Template_p_dialog_visibleChange_19_listener($event) {\n        return ctx.deleteUsersDialog = $event;\n      });\n      i0.ɵɵelementStart(20, \"div\", 14);\n      i0.ɵɵelement(21, \"i\", 15);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Are you sure you want to delete selected trainers?\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(24, TrainersComponent_ng_template_24_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"value\", ctx.users)(\"columns\", ctx.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(22, _c1))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c2))(\"showCurrentPageReport\", true)(\"selection\", ctx.selectedUsers)(\"rowHover\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.userDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(25, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.deleteUserDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.user);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.deleteUsersDialog)(\"modal\", true);\n    }\n  },\n  dependencies: [i3.NgClass, i3.NgIf, i4.DefaultValueAccessor, i4.NgControlStatus, i4.RequiredValidator, i4.NgModel, i5.Table, i2.PrimeTemplate, i5.SortableColumn, i5.SortIcon, i5.TableCheckbox, i5.TableHeaderCheckbox, i6.Toolbar, i7.ButtonDirective, i8.Dialog, i9.Toast, i10.InputText],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AACA,SAAeA,WAAf,QAAkC,kCAAlC;AACA,SAASC,cAAT,QAA+B,aAA/B;AAGA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;;;;;ICCoBC,gCAAkB,CAAlB,EAAkB,QAAlB,EAAkB,EAAlB;IACwFA;MAAAA;MAAA;MAAA,OAASA,iCAAT;IAAkB,CAAlB;IAAoBA;IACxGA;IAAkFA;MAAAA;MAAA;MAAA,OAASA,6CAAT;IAA8B,CAA9B;IAAqFA;;;;;IAArDA;IAAAA;;;;;;;;IAMtHA,gCAA2F,CAA3F,EAA2F,IAA3F,EAA2F,EAA3F;IACoBA;IAAeA;IAC/BA;IACIA;IACAA;IAA8BA;MAAAA;MAAA;;MAAA;;MAAA,OAASA,mDAAT;IAAmC,CAAnC;IAA9BA;;;;;;IAKRA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IAEQA;IACJA;IACAA;IAAyBA;IAAGA;IAAoCA;IAChEA;IAA2BA;IAAKA;IAAsCA;IACtEA;IAA4BA;IAAMA;IAAuCA;IACzEA;IAA4BA;IAAMA;IAAuCA;IACzEA;IAAiCA;IAAWA;IAA4CA;IACxFA;IACJA;;;;;;;;IAGAA,2BAAI,CAAJ,EAAI,IAAJ;IAEQA;IACJA;IACAA;IAAIA;IAAWA;IACfA;IAAIA;IAAaA;IACjBA;IAAIA;IAAcA;IAClBA;IAAIA;IAAuBA;IAC3BA;IAAIA;IAA4BA;IAChCA,4BAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,EAAJ,EAAI,QAAJ,EAAI,EAAJ;IAEmGA;MAAA;MAAA;MAAA;MAAA,OAASA,0CAAT;IAAuB,CAAvB;IAAyBA;IACpHA;IAAqFA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAT;IAAyB,CAAzB;IAA2BA;;;;;IAVnGA;IAAAA;IAEjBA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;;;IAeJA;IAAyEA;IAAuBA;;;;;;IAKhGA;IAAwEA;IAAsBA;;;;;;IAK9FA;IAAoEA;IAAkBA;;;;;;;;;;;;;;IAb1FA,gCAAmB,CAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IAC4BA;IAAUA;IAClCA;IAA8CA;MAAAA;MAAA;MAAA,OAAaA,gDAAb;IAAoC,CAApC;IAA9CA;IACAA;IACJA;IACAA,gCAAmB,CAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IAC2BA;IAASA;IAChCA;IAA6CA;MAAAA;MAAA;MAAA,OAAaA,+CAAb;IAAmC,CAAnC;IAA7CA;IACAA;IACJA;IACAA,iCAAmB,EAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IACuBA;IAAKA;IACxBA;IAA0CA;MAAAA;MAAA;MAAA,OAAaA,2CAAb;IAA+B,CAA/B;IAA1CA;IACAA;IACJA;IACAA,iCAAmB,EAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IACuBA;IAAKA;IACxBA;IAAyCA;MAAAA;MAAA;MAAA,OAAaA,2CAAb;IAA+B,CAA/B;IAAzCA;IAEJA,iCAAmB,EAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IAC4BA;IAAUA;IAClCA;IAA8CA;MAAAA;MAAA;MAAA,OAAaA,gDAAb;IAAoC,CAApC;IAA9CA;;;;;IAnB8CA;IAAAA,iDAA6B,SAA7B,EAA6BA,wEAA7B;IACVA;IAAAA;IAISA;IAAAA,gDAA4B,SAA5B,EAA4BA,uEAA5B;IACTA;IAAAA;IAIMA;IAAAA,4CAAwB,SAAxB,EAAwBA,mEAAxB;IACNA;IAAAA;IAIKA;IAAAA;IAIKA;IAAAA;;;;;;;;IAIlDA;IAAgFA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IAAuBA;IACvGA;IAA8EA;MAAAA;MAAA;MAAA,OAASA,kCAAT;IAAmB,CAAnB;IAAqBA;;;;;;IAOnGA;IAAmBA;IAAgCA;IAAGA;IAAaA;IAAIA;IAACA;;;;;IAAlBA;IAAAA;;;;;;;;IAGtDA;IAA4EA;MAAAA;MAAA;MAAA,iDAA4B,KAA5B;IAAiC,CAAjC;IAAmCA;IAC/GA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAA0BA;;;;;;;;IAUvGA;IAA4EA;MAAAA;MAAA;MAAA,kDAA6B,KAA7B;IAAkC,CAAlC;IAAoCA;IAChHA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAAkCA;;;;;;;;;;;;;;;;;;AD7FnI,OAAM,MAAOC,iBAAP,CAAwB;EAU1BC,YAAoBC,WAApB,EAAsDC,cAAtD,EAAoF;IAAhE;IAAkC;IATtD,kBAAsB,KAAtB;IACA,wBAA4B,KAA5B;IACA,yBAA6B,KAA7B;IACA,aAAgB,EAAhB;IACA,YAAa,EAAb;IACA,qBAAwB,EAAxB;IACA,iBAAqB,KAArB;IACA,YAAc,EAAd;IACA,0BAAqB,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,CAArB;EACyF;;EAEnFC,QAAQ;IAAA;;IAAA;MACV,IAAI;QACA;QACA,MAAMC,KAAK,SAAS,KAAI,CAACH,WAAL,CAAiBI,cAAjB,CAAgC,WAAhC,CAApB;QACA,KAAI,CAACD,KAAL,GAAaA,KAAK,CAACE,GAAN,CAAUC,IAAI,IAAIC,gCACxBD,IADwB,GACpB;UACPE,IAAI,EAAEd,WAAW,CAACY,IAAD;QADV,CADoB,CAAlB,CAAb;MAIH,CAPD,CAOE,OAAOG,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;;QACA,KAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,yBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;;MAED,KAAI,CAACC,IAAL,GAAY,CACR;QAAEC,KAAK,EAAE,IAAT;QAAeC,MAAM,EAAE;MAAvB,CADQ,EAER;QAAED,KAAK,EAAE,MAAT;QAAiBC,MAAM,EAAE;MAAzB,CAFQ,EAGR;QAAED,KAAK,EAAE,OAAT;QAAkBC,MAAM,EAAE;MAA1B,CAHQ,EAIR;QAAED,KAAK,EAAE,OAAT;QAAkBC,MAAM,EAAE;MAA1B,CAJQ,EAKR;QAAED,KAAK,EAAE,YAAT;QAAuBC,MAAM,EAAE;MAA/B,CALQ,CAAZ;IAlBU;EAyBb;;EACDC,OAAO;IACH,KAAKb,IAAL,GAAY;MACRc,IAAI,EAAE,WADE;MAERC,UAAU,EAAE,EAFJ;MAGRC,SAAS,EAAE,EAHH;MAIRC,KAAK,EAAE,EAJC;MAKRC,UAAU,EAAE;IALJ,CAAZ;IAOA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,UAAL,GAAkB,IAAlB;EACH,CAhDyB,CAiD1B;;;EACAC,cAAc,CAACC,KAAD,EAAeC,KAAf,EAA2B;IACrC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAApB;IACAH,KAAK,CAACI,YAAN,CAAmBF,KAAK,CAACG,KAAzB,EAAgC,UAAhC;EACH,CArDyB,CAuD1B;;;EACAC,QAAQ,CAAC5B,IAAD,EAAW;IACf,KAAKA,IAAL,GAASC,kBAAQD,IAAR,CAAT;IACA,KAAKoB,UAAL,GAAkB,IAAlB;EACH;;EAEDS,UAAU,CAAC7B,IAAD,EAAW;IACjB,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAK8B,gBAAL,GAAwB,IAAxB;EACH;;EAEDC,mBAAmB;IACf,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;EAEDC,UAAU;IACN,KAAKb,UAAL,GAAkB,KAAlB;IACA,KAAKD,SAAL,GAAiB,KAAjB;EACH;;EAEKe,QAAQ;IAAA;;IAAA;;;MACV,MAAI,CAACf,SAAL,GAAiB,IAAjB;;MAEA,IAAI,aAAI,CAACnB,IAAL,CAAUe,UAAV,MAAoB,IAApB,IAAoBoB,aAApB,GAAoB,MAApB,GAAoBA,GAAEC,IAAF,EAApB,MAAgC,YAAI,CAACpC,IAAL,CAAUgB,SAAV,MAAmB,IAAnB,IAAmBqB,aAAnB,GAAmB,MAAnB,GAAmBA,GAAED,IAAF,EAAnD,MAA+D,YAAI,CAACpC,IAAL,CAAUiB,KAAV,MAAe,IAAf,IAAeqB,aAAf,GAAe,MAAf,GAAeA,GAAEF,IAAF,EAA9E,CAAJ,EAA4F;QACxF,IAAI;UACAhC,OAAO,CAACmC,GAAR,CAAY,mBAAZ,EAAiC,MAAI,CAACvC,IAAtC,EADA,CAC6C;;UAE7C,IAAI,MAAI,CAACA,IAAL,CAAUwC,EAAd,EAAkB;YACd;YACApC,OAAO,CAACmC,GAAR,CAAY,2BAAZ,EAAyC,MAAI,CAACvC,IAAL,CAAUwC,EAAnD;YACA,MAAMC,WAAW,SAASnD,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBgD,UAAjB,CAA4B,MAAI,CAAC1C,IAAL,CAAUwC,EAAtC,EAA0C,MAAI,CAACxC,IAA/C,CAAD,CAAvC;;YACA,MAAM2C,KAAK,GAAG,MAAI,CAACC,aAAL,CAAmB,MAAI,CAAC5C,IAAL,CAAUwC,EAA7B,CAAd;;YACA,MAAI,CAAC3C,KAAL,CAAW8C,KAAX,IAAiB1C,gCACVwC,WADU,GACC;cACdvC,IAAI,EAAEd,WAAW,CAACqD,WAAD;YADH,CADD,CAAjB;;YAIA,MAAI,CAAC9C,cAAL,CAAoBU,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,iBAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH,CAfD,MAeO;YACH;YACA,MAAMoC,QAAQ,GAAG;cACb9B,UAAU,EAAE,MAAI,CAACf,IAAL,CAAUe,UADT;cAEbC,SAAS,EAAE,MAAI,CAAChB,IAAL,CAAUgB,SAFR;cAGbC,KAAK,EAAE,MAAI,CAACjB,IAAL,CAAUiB,KAHJ;cAIb6B,QAAQ,EAAE,oBAJG;cAKbhC,IAAI,EAAE,WALO;cAMbiC,KAAK,EAAE,MAAI,CAAC/C,IAAL,CAAU+C,KAAV,IAAmB,IANb;cAOb7B,UAAU,EAAE,MAAI,CAAClB,IAAL,CAAUkB,UAAV,IAAwB,IAPvB;cAQb8B,OAAO,EAAE,MAAI,CAAChD,IAAL,CAAUgD,OAAV,IAAqB;YARjB,CAAjB;YAUA5C,OAAO,CAACmC,GAAR,CAAY,6BAAZ,EAA2CM,QAA3C;YAEA,MAAMI,OAAO,SAAS3D,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBwD,UAAjB,CAA4BL,QAA5B,CAAD,CAAnC;;YACA,MAAI,CAAChD,KAAL,CAAWsD,IAAX,CAAelD,gCACRgD,OADQ,GACD;cACV/C,IAAI,EAAEd,WAAW,CAAC6D,OAAD;YADP,CADC,CAAf;;YAIA,MAAI,CAACtD,cAAL,CAAoBU,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,iBAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH;;UAED,MAAI,CAACW,UAAL,GAAkB,KAAlB;UACA,MAAI,CAACpB,IAAL,GAAY,EAAZ;QACH,CA/CD,CA+CE,OAAOG,KAAP,EAAc;UACZC,OAAO,CAACD,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;UACAC,OAAO,CAACD,KAAR,CAAc,gBAAd,EAAgCA,KAAhC,EAFY,CAIZ;;UACA,IAAIiD,YAAY,GAAG,wBAAnB;;UACA,IAAIjD,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA1B,IAAsC,WAAWA,KAArD,EAA4D;YACxD,MAAMkD,QAAQ,GAAGlD,KAAjB;;YACA,IAAIkD,QAAQ,CAAClD,KAAT,IAAkBkD,QAAQ,CAAClD,KAAT,CAAemD,OAArC,EAA8C;cAC1CF,YAAY,GAAGC,QAAQ,CAAClD,KAAT,CAAemD,OAA9B;YACH,CAFD,MAEO,IAAID,QAAQ,CAACC,OAAb,EAAsB;cACzBF,YAAY,GAAGC,QAAQ,CAACC,OAAxB;YACH;UACJ;;UAED,MAAI,CAAC3D,cAAL,CAAoBU,GAApB,CAAwB;YACpBC,QAAQ,EAAE,OADU;YAEpBC,OAAO,EAAE,OAFW;YAGpBC,MAAM,EAAE4C,YAHY;YAIpB3C,IAAI,EAAE;UAJc,CAAxB;QAMH;MACJ,CAtED,MAsEO;QACH,MAAI,CAACd,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,MADU;UAEpBC,OAAO,EAAE,kBAFW;UAGpBC,MAAM,EAAE,mEAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAhFS;EAiFb;;EAEK8C,aAAa;IAAA;;IAAA;MACf,IAAI;QACA,MAAMjE,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBmC,UAAjB,CAA4B,MAAI,CAAC7B,IAAL,CAAUwC,EAAtC,CAAD,CAAnB;QACA,MAAI,CAAC3C,KAAL,GAAa,MAAI,CAACA,KAAL,CAAW2D,MAAX,CAAkBC,GAAG,IAAIA,GAAG,CAACjB,EAAJ,KAAW,MAAI,CAACxC,IAAL,CAAUwC,EAA9C,CAAb;QACA,MAAI,CAACV,gBAAL,GAAwB,KAAxB;QACA,MAAI,CAAC9B,IAAL,GAAY,EAAZ;;QACA,MAAI,CAACL,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,iBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAXD,CAWE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,yBAAd,EAAyCA,KAAzC;;QACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,0BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IApBc;EAqBlB;;EAEKiD,qBAAqB;IAAA;;IAAA;MACvB,IAAI;QACA,KAAK,MAAM1D,IAAX,IAAmB,MAAI,CAAC2D,aAAxB,EAAuC;UACnC,MAAMrE,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBmC,UAAjB,CAA4B7B,IAAI,CAACwC,EAAjC,CAAD,CAAnB;QACH;;QACD,MAAI,CAAC3C,KAAL,GAAa,MAAI,CAACA,KAAL,CAAW2D,MAAX,CAAkBC,GAAG,IAAI,CAAC,MAAI,CAACE,aAAL,CAAmBC,QAAnB,CAA4BH,GAA5B,CAA1B,CAAb;QACA,MAAI,CAACzB,iBAAL,GAAyB,KAAzB;QACA,MAAI,CAAC2B,aAAL,GAAqB,EAArB;;QACA,MAAI,CAAChE,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,kBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAbD,CAaE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,0BAAd,EAA0CA,KAA1C;;QACA,MAAI,CAACR,cAAL,CAAoBU,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAtBsB;EAuB1B;;EAEDmC,aAAa,CAACJ,EAAD,EAAW;IACpB,OAAO,KAAK3C,KAAL,CAAWgE,SAAX,CAAqBC,CAAC,IAAIA,CAAC,CAACtB,EAAF,KAASA,EAAnC,CAAP;EACH;;AAhNyB;;;mBAAjBhD,mBAAiBD;AAAA;;;QAAjBC;EAAiBuE;EAAAC,iCAFf,CAAC3E,cAAD,CAEe;EAFC4E;EAAAC;EAAAC;EAAAC;IAAA;MCT/B7E,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB;MAGYA;MACAA;MACIA;MAMJA;MACAA;MAAsTA;QAAA;MAAA;MAClTA;MASAA;MAaAA;MAkBJA;MACAA;MAAUA;QAAA;MAAA;MACNA;MAyBAA;MAIJA;MAEAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MACJA;MACAA;MAIJA;MAEAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MAAMA;MAAkDA;MAE5DA;MAIJA;;;;MA9FaA;MAAAA,kCAAe,SAAf,EAAe8E,QAAf,EAAe,MAAf,EAAe,EAAf,EAAe,oBAAf,EAAe9E,2BAAf,EAAe,WAAf,EAAe,IAAf,EAAe,oBAAf,EAAeA,2BAAf,EAAe,uBAAf,EAAe,IAAf,EAAe,WAAf,EAAe8E,iBAAf,EAAe,UAAf,EAAe,IAAf;MA0CsB9E;MAAAA;MAAzBA,yCAAwB,OAAxB,EAAwB,IAAxB;MAgC+DA;MAAAA;MAA/DA,+CAA8B,OAA9B,EAA8B,IAA9B;MAGKA;MAAAA;MAQ2DA;MAAAA;MAAhEA,gDAA+B,OAA/B,EAA+B,IAA/B", "names": ["getFullName", "MessageService", "lastValueFrom", "i0", "TrainersComponent", "constructor", "userService", "messageService", "ngOnInit", "users", "getUsersByRole", "map", "user", "Object", "name", "error", "console", "add", "severity", "summary", "detail", "life", "cols", "field", "header", "openNew", "role", "first_name", "last_name", "email", "specialite", "submitted", "userDialog", "onGlobalFilter", "table", "event", "input", "target", "filterGlobal", "value", "editUser", "deleteUser", "deleteUserDialog", "deleteSelectedUsers", "deleteUsersDialog", "hideDialog", "saveUser", "_a", "trim", "_b", "_c", "log", "id", "updatedUser", "updateUser", "index", "findIndexById", "userData", "password", "phone", "team_id", "newUser", "createUser", "push", "errorMessage", "errorObj", "message", "confirmDelete", "filter", "val", "confirmDeleteSelected", "selectedUsers", "includes", "findIndex", "u", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\crud\\trainers\\trainers.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\crud\\trainers\\trainers.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { User, getFullName } from '../../../../../models/user.model';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Table } from 'primeng/table';\r\nimport { UserService } from '../../../../service/user.service';\r\nimport { lastValueFrom } from 'rxjs';\r\n\r\n@Component({\r\n    templateUrl: './trainers.component.html',\r\n    providers: [MessageService]\r\n})\r\nexport class TrainersComponent implements OnInit {\r\n    userDialog: boolean = false;\r\n    deleteUserDialog: boolean = false;\r\n    deleteUsersDialog: boolean = false;\r\n    users: User[] = [];\r\n    user: User = {} as User;\r\n    selectedUsers: User[] = [];\r\n    submitted: boolean = false;\r\n    cols: any[] = [];\r\n    rowsPerPageOptions = [5, 10, 20];\r\n    constructor(private userService: UserService, private messageService: MessageService) { }\r\n\r\n    async ngOnInit() {\r\n        try {\r\n            // Load trainers\r\n            const users = await this.userService.getUsersByRole('formateur');\r\n            this.users = users.map(user => ({\r\n                ...user,\r\n                name: getFullName(user)\r\n            }));\r\n        } catch (error) {\r\n            console.error('Error loading trainers:', error);\r\n            this.messageService.add({\r\n                severity: 'error',\r\n                summary: 'Error',\r\n                detail: 'Failed to load trainers',\r\n                life: 3000\r\n            });\r\n        }\r\n\r\n        this.cols = [\r\n            { field: 'id', header: 'ID' },\r\n            { field: 'name', header: 'Name' },\r\n            { field: 'email', header: 'Email' },\r\n            { field: 'phone', header: 'Phone' },\r\n            { field: 'specialite', header: 'Speciality' }\r\n        ];\r\n    }\r\n    openNew() {\r\n        this.user = {\r\n            role: 'formateur',\r\n            first_name: '',\r\n            last_name: '',\r\n            email: '',\r\n            specialite: ''\r\n        } as User;\r\n        this.submitted = false;\r\n        this.userDialog = true;\r\n    }\r\n    // Table global filter\r\n    onGlobalFilter(table: Table, event: Event) {\r\n        const input = event.target as HTMLInputElement;\r\n        table.filterGlobal(input.value, 'contains');\r\n    }\r\n\r\n    // CRUD methods\r\n    editUser(user: User) {\r\n        this.user = { ...user };\r\n        this.userDialog = true;\r\n    }\r\n\r\n    deleteUser(user: User) {\r\n        this.user = user;\r\n        this.deleteUserDialog = true;\r\n    }\r\n\r\n    deleteSelectedUsers() {\r\n        this.deleteUsersDialog = true;\r\n    }\r\n\r\n    hideDialog() {\r\n        this.userDialog = false;\r\n        this.submitted = false;\r\n    }\r\n\r\n    async saveUser() {\r\n        this.submitted = true;\r\n\r\n        if (this.user.first_name?.trim() && this.user.last_name?.trim() && this.user.email?.trim()) {\r\n            try {\r\n                console.log('Saving user data:', this.user); // Debug log\r\n\r\n                if (this.user.id) {\r\n                    // Update existing trainer\r\n                    console.log('Updating trainer with ID:', this.user.id);\r\n                    const updatedUser = await lastValueFrom(this.userService.updateUser(this.user.id, this.user));\r\n                    const index = this.findIndexById(this.user.id);\r\n                    this.users[index] = {\r\n                        ...updatedUser,\r\n                        name: getFullName(updatedUser)\r\n                    };\r\n                    this.messageService.add({\r\n                        severity: 'success',\r\n                        summary: 'Successful',\r\n                        detail: 'Trainer Updated',\r\n                        life: 3000\r\n                    });\r\n                } else {\r\n                    // Create new trainer\r\n                    const userData = {\r\n                        first_name: this.user.first_name,\r\n                        last_name: this.user.last_name,\r\n                        email: this.user.email,\r\n                        password: 'defaultPassword123',\r\n                        role: 'formateur',\r\n                        phone: this.user.phone || null,\r\n                        specialite: this.user.specialite || null,\r\n                        team_id: this.user.team_id || null\r\n                    };\r\n                    console.log('Creating trainer with data:', userData);\r\n\r\n                    const newUser = await lastValueFrom(this.userService.createUser(userData));\r\n                    this.users.push({\r\n                        ...newUser,\r\n                        name: getFullName(newUser)\r\n                    });\r\n                    this.messageService.add({\r\n                        severity: 'success',\r\n                        summary: 'Successful',\r\n                        detail: 'Trainer Created',\r\n                        life: 3000\r\n                    });\r\n                }\r\n\r\n                this.userDialog = false;\r\n                this.user = {} as User;\r\n            } catch (error) {\r\n                console.error('Error saving trainer:', error);\r\n                console.error('Error details:', error);\r\n\r\n                // Show more detailed error message\r\n                let errorMessage = 'Failed to save trainer';\r\n                if (error && typeof error === 'object' && 'error' in error) {\r\n                    const errorObj = error as any;\r\n                    if (errorObj.error && errorObj.error.message) {\r\n                        errorMessage = errorObj.error.message;\r\n                    } else if (errorObj.message) {\r\n                        errorMessage = errorObj.message;\r\n                    }\r\n                }\r\n\r\n                this.messageService.add({\r\n                    severity: 'error',\r\n                    summary: 'Error',\r\n                    detail: errorMessage,\r\n                    life: 5000\r\n                });\r\n            }\r\n        } else {\r\n            this.messageService.add({\r\n                severity: 'warn',\r\n                summary: 'Validation Error',\r\n                detail: 'Please fill in all required fields (First Name, Last Name, Email)',\r\n                life: 3000\r\n            });\r\n        }\r\n    }\r\n\r\n    async confirmDelete() {\r\n        try {\r\n            await lastValueFrom(this.userService.deleteUser(this.user.id));\r\n            this.users = this.users.filter(val => val.id !== this.user.id);\r\n            this.deleteUserDialog = false;\r\n            this.user = {} as User;\r\n            this.messageService.add({\r\n                severity: 'success',\r\n                summary: 'Successful',\r\n                detail: 'Trainer Deleted',\r\n                life: 3000\r\n            });\r\n        } catch (error) {\r\n            console.error('Error deleting trainer:', error);\r\n            this.messageService.add({\r\n                severity: 'error',\r\n                summary: 'Error',\r\n                detail: 'Failed to delete trainer',\r\n                life: 3000\r\n            });\r\n        }\r\n    }\r\n\r\n    async confirmDeleteSelected() {\r\n        try {\r\n            for (const user of this.selectedUsers) {\r\n                await lastValueFrom(this.userService.deleteUser(user.id));\r\n            }\r\n            this.users = this.users.filter(val => !this.selectedUsers.includes(val));\r\n            this.deleteUsersDialog = false;\r\n            this.selectedUsers = [];\r\n            this.messageService.add({\r\n                severity: 'success',\r\n                summary: 'Successful',\r\n                detail: 'Trainers Deleted',\r\n                life: 3000\r\n            });\r\n        } catch (error) {\r\n            console.error('Error deleting trainers:', error);\r\n            this.messageService.add({\r\n                severity: 'error',\r\n                summary: 'Error',\r\n                detail: 'Failed to delete trainers',\r\n                life: 3000\r\n            });\r\n        }\r\n    }\r\n\r\n    findIndexById(id: number): number {\r\n        return this.users.findIndex(u => u.id === id);\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <div class=\"col-12\">\r\n        <div class=\"card px-6 py-6\">\r\n            <p-toast></p-toast>\r\n            <p-toolbar styleClass=\"mb-4\">\r\n                <ng-template pTemplate=\"left\">\r\n                    <div class=\"my-2\">\r\n                        <button pButton pRipple label=\"New\" icon=\"pi pi-plus\" class=\"p-button-success mr-2\" (click)=\"openNew()\"></button>\r\n                        <button pButton pRipple label=\"Delete\" icon=\"pi pi-trash\" class=\"p-button-danger\" (click)=\"deleteSelectedUsers()\" [disabled]=\"!selectedUsers || !selectedUsers.length\"></button>\r\n                    </div>\r\n                </ng-template>\r\n            </p-toolbar>\r\n            <p-table #dt [value]=\"users\" [columns]=\"cols\" responsiveLayout=\"scroll\" [rows]=\"10\" [globalFilterFields]=\"['name','email','phone','specialite']\" [paginator]=\"true\" [rowsPerPageOptions]=\"[10,20,30]\" [showCurrentPageReport]=\"true\" currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} trainers\" [(selection)]=\"selectedUsers\" selectionMode=\"multiple\" [rowHover]=\"true\" dataKey=\"id\">\r\n                <ng-template pTemplate=\"caption\">\r\n                    <div class=\"flex flex-column md:flex-row md:justify-content-between md:align-items-center\">\r\n                        <h5 class=\"m-0\">Manage Trainers</h5>\r\n                        <span class=\"block mt-2 md:mt-0 p-input-icon-left\">\r\n                            <i class=\"pi pi-search\"></i>\r\n                            <input pInputText type=\"text\" (input)=\"onGlobalFilter(dt, $event)\" placeholder=\"Search...\"  class=\"w-full sm:w-auto\"/>\r\n                        </span>\r\n                    </div>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"header\">\r\n                    <tr>\r\n                        <th style=\"width: 3rem\">\r\n                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\r\n                        </th>\r\n                        <th pSortableColumn=\"id\">ID <p-sortIcon field=\"id\"></p-sortIcon></th>\r\n                        <th pSortableColumn=\"name\">Name <p-sortIcon field=\"name\"></p-sortIcon></th>\r\n                        <th pSortableColumn=\"email\">Email <p-sortIcon field=\"email\"></p-sortIcon></th>\r\n                        <th pSortableColumn=\"phone\">Phone <p-sortIcon field=\"phone\"></p-sortIcon></th>\r\n                        <th pSortableColumn=\"specialite\">Speciality <p-sortIcon field=\"specialite\"></p-sortIcon></th>\r\n                        <th></th>\r\n                    </tr>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"body\" let-user>\r\n                    <tr>\r\n                        <td>\r\n                            <p-tableCheckbox [value]=\"user\"></p-tableCheckbox>\r\n                        </td>\r\n                        <td>{{user.id}}</td>\r\n                        <td>{{user.name}}</td>\r\n                        <td>{{user.email}}</td>\r\n                        <td>{{user.phone || 'N/A'}}</td>\r\n                        <td>{{user.specialite || 'N/A'}}</td>\r\n                        <td>\r\n                            <div class=\"flex\">\r\n                                <button pButton pRipple icon=\"pi pi-pencil\" class=\"p-button-rounded p-button-success mr-2\" (click)=\"editUser(user)\"></button>\r\n                                <button pButton pRipple icon=\"pi pi-trash\" class=\"p-button-rounded p-button-warning\" (click)=\"deleteUser(user)\"></button>\r\n                            </div>\r\n                        </td>\r\n                    </tr>\r\n                </ng-template>\r\n            </p-table>\r\n            <p-dialog [(visible)]=\"userDialog\" [style]=\"{width: '450px'}\" header=\"Trainer Details\" [modal]=\"true\" class=\"p-fluid\">\r\n                <ng-template pTemplate=\"content\">\r\n                    <div class=\"field\">\r\n                        <label for=\"first_name\">First Name</label>\r\n                        <input type=\"text\" pInputText id=\"first_name\" [(ngModel)]=\"user.first_name\" required autofocus [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !user.first_name}\"/>\r\n                        <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !user.first_name\">First name is required.</small>\r\n                    </div>\r\n                    <div class=\"field\">\r\n                        <label for=\"last_name\">Last Name</label>\r\n                        <input type=\"text\" pInputText id=\"last_name\" [(ngModel)]=\"user.last_name\" required [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !user.last_name}\"/>\r\n                        <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !user.last_name\">Last name is required.</small>\r\n                    </div>\r\n                    <div class=\"field\">\r\n                        <label for=\"email\">Email</label>\r\n                        <input type=\"email\" pInputText id=\"email\" [(ngModel)]=\"user.email\" required [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !user.email}\"/>\r\n                        <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !user.email\">Email is required.</small>\r\n                    </div>\r\n                    <div class=\"field\">\r\n                        <label for=\"phone\">Phone</label>\r\n                        <input type=\"text\" pInputText id=\"phone\" [(ngModel)]=\"user.phone\" placeholder=\"Optional\"/>\r\n                    </div>\r\n                    <div class=\"field\">\r\n                        <label for=\"specialite\">Speciality</label>\r\n                        <input type=\"text\" pInputText id=\"specialite\" [(ngModel)]=\"user.specialite\" placeholder=\"e.g., Web Development, UI/UX Design, Cybersecurity\"/>\r\n                    </div>\r\n                </ng-template>\r\n                <ng-template pTemplate=\"footer\">\r\n                    <button pButton pRipple label=\"Cancel\" icon=\"pi pi-times\" class=\"p-button-text\" (click)=\"hideDialog()\"></button>\r\n                    <button pButton pRipple label=\"Save\" icon=\"pi pi-check\" class=\"p-button-text\" (click)=\"saveUser()\"></button>\r\n                </ng-template>\r\n            </p-dialog>\r\n\r\n            <p-dialog [(visible)]=\"deleteUserDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\r\n                    <span *ngIf=\"user\">Are you sure you want to delete <b>{{user.name}}</b>?</span>\r\n                </div>\r\n                <ng-template pTemplate=\"footer\">\r\n                    <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteUserDialog = false\"></button>\r\n                    <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDelete()\"></button>\r\n                </ng-template>\r\n            </p-dialog>\r\n\r\n            <p-dialog [(visible)]=\"deleteUsersDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\r\n                    <span>Are you sure you want to delete selected trainers?</span>\r\n                </div>\r\n                <ng-template pTemplate=\"footer\">\r\n                    <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteUsersDialog = false\"></button>\r\n                    <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDeleteSelected()\"></button>\r\n                </ng-template>\r\n            </p-dialog>\r\n        </div>\r\n    </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module"}