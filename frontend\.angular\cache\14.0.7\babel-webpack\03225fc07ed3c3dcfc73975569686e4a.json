{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ZIndexUtils } from 'primeng/utils';\n\nfunction OverlayPanel_div_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction OverlayPanel_div_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.onCloseClick($event));\n    })(\"keydown.enter\", function OverlayPanel_div_0_button_4_Template_button_keydown_enter_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.hide());\n    });\n    i0.ɵɵelement(1, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaCloseLabel);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c1 = function (a0, a1) {\n  return {\n    value: a0,\n    params: a1\n  };\n};\n\nfunction OverlayPanel_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onOverlayClick($event));\n    })(\"@animation.start\", function OverlayPanel_div_0_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onAnimationStart($event));\n    })(\"@animation.done\", function OverlayPanel_div_0_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function OverlayPanel_div_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onContentClick());\n    })(\"mousedown\", function OverlayPanel_div_0_Template_div_mousedown_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onContentClick());\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, OverlayPanel_div_0_ng_container_3_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, OverlayPanel_div_0_button_4_Template, 2, 1, \"button\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-overlaypanel p-component\")(\"ngStyle\", ctx_r0.style)(\"@animation\", i0.ɵɵpureFunction2(10, _c1, ctx_r0.overlayVisible ? \"open\" : \"close\", i0.ɵɵpureFunction2(7, _c0, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseIcon);\n  }\n}\n\nconst _c2 = [\"*\"];\n\nclass OverlayPanel {\n  constructor(el, renderer, cd, zone, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.dismissable = true;\n    this.appendTo = 'body';\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.focusOnShow = true;\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.overlayVisible = false;\n    this.render = false;\n    this.isOverlayAnimationInProgress = false;\n    this.selfClick = false;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n\n      this.cd.markForCheck();\n    });\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener && this.dismissable) {\n      this.zone.runOutsideAngular(() => {\n        let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, event => {\n          if (!this.container.contains(event.target) && this.target !== event.target && !this.target.contains(event.target) && !this.selfClick) {\n            this.zone.run(() => {\n              this.hide();\n            });\n          }\n\n          this.selfClick = false;\n          this.cd.markForCheck();\n        });\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n      this.selfClick = false;\n    }\n  }\n\n  toggle(event, target) {\n    if (this.isOverlayAnimationInProgress) {\n      return;\n    }\n\n    if (this.overlayVisible) {\n      if (this.hasTargetChanged(event, target)) {\n        this.destroyCallback = () => {\n          this.show(null, target || event.currentTarget || event.target);\n        };\n      }\n\n      this.hide();\n    } else {\n      this.show(event, target);\n    }\n  }\n\n  show(event, target) {\n    if (this.isOverlayAnimationInProgress) {\n      return;\n    }\n\n    this.target = target || event.currentTarget || event.target;\n    this.overlayVisible = true;\n    this.render = true;\n    this.cd.markForCheck();\n  }\n\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n    this.selfClick = true;\n  }\n\n  onContentClick() {\n    this.selfClick = true;\n  }\n\n  hasTargetChanged(event, target) {\n    return this.target != null && this.target !== (target || event.currentTarget || event.target);\n  }\n\n  appendContainer() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n\n  restoreAppend() {\n    if (this.container && this.appendTo) {\n      this.el.nativeElement.appendChild(this.container);\n    }\n  }\n\n  align() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('overlay', this.container, this.baseZIndex + this.config.zIndex.overlay);\n    }\n\n    DomHandler.absolutePosition(this.container, this.target);\n    const containerOffset = DomHandler.getOffset(this.container);\n    const targetOffset = DomHandler.getOffset(this.target);\n    let arrowLeft = 0;\n\n    if (containerOffset.left < targetOffset.left) {\n      arrowLeft = targetOffset.left - containerOffset.left;\n    }\n\n    this.container.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n\n    if (containerOffset.top < targetOffset.top) {\n      DomHandler.addClass(this.container, 'p-overlaypanel-flipped');\n    }\n  }\n\n  onAnimationStart(event) {\n    if (event.toState === 'open') {\n      this.container = event.element;\n      this.onShow.emit(null);\n      this.appendContainer();\n      this.align();\n      this.bindDocumentClickListener();\n      this.bindDocumentResizeListener();\n      this.bindScrollListener();\n\n      if (this.focusOnShow) {\n        this.focus();\n      }\n\n      this.overlayEventListener = e => {\n        if (this.container && this.container.contains(e.target)) {\n          this.selfClick = true;\n        }\n      };\n\n      this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n    }\n\n    this.isOverlayAnimationInProgress = true;\n  }\n\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.destroyCallback) {\n          this.destroyCallback();\n          this.destroyCallback = null;\n        }\n\n        if (this.overlaySubscription) {\n          this.overlaySubscription.unsubscribe();\n        }\n\n        break;\n\n      case 'close':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(this.container);\n        }\n\n        if (this.overlaySubscription) {\n          this.overlaySubscription.unsubscribe();\n        }\n\n        this.onContainerDestroy();\n        this.onHide.emit({});\n        this.render = false;\n        break;\n    }\n\n    this.isOverlayAnimationInProgress = false;\n  }\n\n  focus() {\n    let focusable = DomHandler.findSingle(this.container, '[autofocus]');\n\n    if (focusable) {\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => focusable.focus(), 5);\n      });\n    }\n  }\n\n  hide() {\n    if (this.isOverlayAnimationInProgress) {\n      return;\n    }\n\n    this.overlayVisible = false;\n    this.cd.markForCheck();\n  }\n\n  onCloseClick(event) {\n    this.hide();\n    event.preventDefault();\n  }\n\n  onWindowResize(event) {\n    if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n        if (this.overlayVisible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  onContainerDestroy() {\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n  }\n\n  ngOnDestroy() {\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n\n    if (this.container && this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n\n    this.destroyCallback = null;\n\n    if (this.container) {\n      this.restoreAppend();\n      this.onContainerDestroy();\n    }\n\n    if (this.overlaySubscription) {\n      this.overlaySubscription.unsubscribe();\n    }\n  }\n\n}\n\nOverlayPanel.ɵfac = function OverlayPanel_Factory(t) {\n  return new (t || OverlayPanel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n};\n\nOverlayPanel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: OverlayPanel,\n  selectors: [[\"p-overlayPanel\"]],\n  contentQueries: function OverlayPanel_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    dismissable: \"dismissable\",\n    showCloseIcon: \"showCloseIcon\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    appendTo: \"appendTo\",\n    autoZIndex: \"autoZIndex\",\n    ariaCloseLabel: \"ariaCloseLabel\",\n    baseZIndex: \"baseZIndex\",\n    focusOnShow: \"focusOnShow\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\"\n  },\n  outputs: {\n    onShow: \"onShow\",\n    onHide: \"onHide\"\n  },\n  ngContentSelectors: _c2,\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [1, \"p-overlaypanel-content\", 3, \"click\", \"mousedown\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"class\", \"p-overlaypanel-close p-link\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-overlaypanel-close\", \"p-link\", 3, \"click\", \"keydown.enter\"], [1, \"p-overlaypanel-close-icon\", \"pi\", \"pi-times\"]],\n  template: function OverlayPanel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, OverlayPanel_div_0_Template, 5, 13, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.render);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple],\n  styles: [\".p-overlaypanel{position:absolute;margin-top:10px;top:0;left:0}.p-overlaypanel-flipped{margin-top:0;margin-bottom:10px}.p-overlaypanel-close{display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-overlaypanel:after,.p-overlaypanel:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-overlaypanel:after{border-width:8px;margin-left:-8px}.p-overlaypanel:before{border-width:10px;margin-left:-10px}.p-overlaypanel-shifted:after,.p-overlaypanel-shifted:before{left:auto;right:1.25em;margin-left:auto}.p-overlaypanel-flipped:after,.p-overlaypanel-flipped:before{bottom:auto;top:100%}.p-overlaypanel.p-overlaypanel-flipped:after{border-bottom-color:transparent}.p-overlaypanel.p-overlaypanel-flipped:before{border-bottom-color:transparent}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('animation', [state('void', style({\n      transform: 'scaleY(0.8)',\n      opacity: 0\n    })), state('close', style({\n      opacity: 0\n    })), state('open', style({\n      transform: 'translateY(0)',\n      opacity: 1\n    })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => close', animate('{{hideTransitionParams}}'))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPanel, [{\n    type: Component,\n    args: [{\n      selector: 'p-overlayPanel',\n      template: `\n        <div *ngIf=\"render\" [ngClass]=\"'p-overlaypanel p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: (overlayVisible ? 'open': 'close'), params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n                (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div class=\"p-overlaypanel-content\" (click)=\"onContentClick()\" (mousedown)=\"onContentClick()\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <button *ngIf=\"showCloseIcon\" type=\"button\" class=\"p-overlaypanel-close p-link\" (click)=\"onCloseClick($event)\" (keydown.enter)=\"hide()\" [attr.aria-label]=\"ariaCloseLabel\" pRipple>\n                <span class=\"p-overlaypanel-close-icon pi pi-times\"></span>\n            </button>\n        </div>\n    `,\n      animations: [trigger('animation', [state('void', style({\n        transform: 'scaleY(0.8)',\n        opacity: 0\n      })), state('close', style({\n        opacity: 0\n      })), state('open', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => close', animate('{{hideTransitionParams}}'))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-overlaypanel{position:absolute;margin-top:10px;top:0;left:0}.p-overlaypanel-flipped{margin-top:0;margin-bottom:10px}.p-overlaypanel-close{display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-overlaypanel:after,.p-overlaypanel:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-overlaypanel:after{border-width:8px;margin-left:-8px}.p-overlaypanel:before{border-width:10px;margin-left:-10px}.p-overlaypanel-shifted:after,.p-overlaypanel-shifted:before{left:auto;right:1.25em;margin-left:auto}.p-overlaypanel-flipped:after,.p-overlaypanel-flipped:before{bottom:auto;top:100%}.p-overlaypanel.p-overlaypanel-flipped:after{border-bottom-color:transparent}.p-overlaypanel.p-overlaypanel-flipped:before{border-bottom-color:transparent}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.PrimeNGConfig\n    }, {\n      type: i1.OverlayService\n    }];\n  }, {\n    dismissable: [{\n      type: Input\n    }],\n    showCloseIcon: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    ariaCloseLabel: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    focusOnShow: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass OverlayPanelModule {}\n\nOverlayPanelModule.ɵfac = function OverlayPanelModule_Factory(t) {\n  return new (t || OverlayPanelModule)();\n};\n\nOverlayPanelModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: OverlayPanelModule\n});\nOverlayPanelModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RippleModule, SharedModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayPanelModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule, SharedModule],\n      exports: [OverlayPanel, SharedModule],\n      declarations: [OverlayPanel]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { OverlayPanel, OverlayPanelModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ContentChildren", "NgModule", "i2", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "i1", "PrimeTemplate", "SharedModule", "i3", "RippleModule", "trigger", "state", "style", "transition", "animate", "ZIndexUtils", "OverlayPanel", "constructor", "el", "renderer", "cd", "zone", "config", "overlayService", "dismissable", "appendTo", "autoZIndex", "baseZIndex", "focusOnShow", "showTransitionOptions", "hideTransitionOptions", "onShow", "onHide", "overlayVisible", "render", "isOverlayAnimationInProgress", "selfClick", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "contentTemplate", "template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bindDocumentClickListener", "documentClickListener", "runOutsideAngular", "documentEvent", "isIOS", "documentTarget", "nativeElement", "ownerDocument", "listen", "event", "container", "contains", "target", "run", "hide", "unbindDocumentClickListener", "toggle", "hasTargetChanged", "destroyCallback", "show", "currentTarget", "onOverlayClick", "add", "originalEvent", "onContentClick", "append<PERSON><PERSON><PERSON>", "document", "body", "append<PERSON><PERSON><PERSON>", "restoreAppend", "align", "set", "zIndex", "overlay", "absolutePosition", "containerOffset", "getOffset", "targetOffset", "arrowLeft", "left", "setProperty", "top", "addClass", "onAnimationStart", "toState", "element", "emit", "bindDocumentResizeListener", "bindScrollListener", "focus", "overlayEventListener", "e", "overlaySubscription", "clickObservable", "subscribe", "onAnimationEnd", "unsubscribe", "clear", "onContainerDestroy", "focusable", "findSingle", "setTimeout", "onCloseClick", "preventDefault", "onWindowResize", "isTouchDevice", "documentResizeListener", "bind", "window", "addEventListener", "unbindDocumentResizeListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "destroyed", "ngOnDestroy", "destroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "NgZone", "PrimeNGConfig", "OverlayService", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "transform", "opacity", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "showCloseIcon", "styleClass", "ariaCloseLabel", "OverlayPanelModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-overlaypanel.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { ZIndexUtils } from 'primeng/utils';\n\nclass OverlayPanel {\n    constructor(el, renderer, cd, zone, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.dismissable = true;\n        this.appendTo = 'body';\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.focusOnShow = true;\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.overlayVisible = false;\n        this.render = false;\n        this.isOverlayAnimationInProgress = false;\n        this.selfClick = false;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n            this.cd.markForCheck();\n        });\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener && this.dismissable) {\n            this.zone.runOutsideAngular(() => {\n                let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n                const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n                this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, (event) => {\n                    if (!this.container.contains(event.target) && this.target !== event.target && !this.target.contains(event.target) && !this.selfClick) {\n                        this.zone.run(() => {\n                            this.hide();\n                        });\n                    }\n                    this.selfClick = false;\n                    this.cd.markForCheck();\n                });\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n            this.selfClick = false;\n        }\n    }\n    toggle(event, target) {\n        if (this.isOverlayAnimationInProgress) {\n            return;\n        }\n        if (this.overlayVisible) {\n            if (this.hasTargetChanged(event, target)) {\n                this.destroyCallback = () => {\n                    this.show(null, (target || event.currentTarget || event.target));\n                };\n            }\n            this.hide();\n        }\n        else {\n            this.show(event, target);\n        }\n    }\n    show(event, target) {\n        if (this.isOverlayAnimationInProgress) {\n            return;\n        }\n        this.target = target || event.currentTarget || event.target;\n        this.overlayVisible = true;\n        this.render = true;\n        this.cd.markForCheck();\n    }\n    onOverlayClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n        this.selfClick = true;\n    }\n    onContentClick() {\n        this.selfClick = true;\n    }\n    hasTargetChanged(event, target) {\n        return this.target != null && this.target !== (target || event.currentTarget || event.target);\n    }\n    appendContainer() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    restoreAppend() {\n        if (this.container && this.appendTo) {\n            this.el.nativeElement.appendChild(this.container);\n        }\n    }\n    align() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('overlay', this.container, this.baseZIndex + this.config.zIndex.overlay);\n        }\n        DomHandler.absolutePosition(this.container, this.target);\n        const containerOffset = DomHandler.getOffset(this.container);\n        const targetOffset = DomHandler.getOffset(this.target);\n        let arrowLeft = 0;\n        if (containerOffset.left < targetOffset.left) {\n            arrowLeft = targetOffset.left - containerOffset.left;\n        }\n        this.container.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n        if (containerOffset.top < targetOffset.top) {\n            DomHandler.addClass(this.container, 'p-overlaypanel-flipped');\n        }\n    }\n    onAnimationStart(event) {\n        if (event.toState === 'open') {\n            this.container = event.element;\n            this.onShow.emit(null);\n            this.appendContainer();\n            this.align();\n            this.bindDocumentClickListener();\n            this.bindDocumentResizeListener();\n            this.bindScrollListener();\n            if (this.focusOnShow) {\n                this.focus();\n            }\n            this.overlayEventListener = (e) => {\n                if (this.container && this.container.contains(e.target)) {\n                    this.selfClick = true;\n                }\n            };\n            this.overlaySubscription = this.overlayService.clickObservable.subscribe(this.overlayEventListener);\n        }\n        this.isOverlayAnimationInProgress = true;\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                if (this.destroyCallback) {\n                    this.destroyCallback();\n                    this.destroyCallback = null;\n                }\n                if (this.overlaySubscription) {\n                    this.overlaySubscription.unsubscribe();\n                }\n                break;\n            case 'close':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(this.container);\n                }\n                if (this.overlaySubscription) {\n                    this.overlaySubscription.unsubscribe();\n                }\n                this.onContainerDestroy();\n                this.onHide.emit({});\n                this.render = false;\n                break;\n        }\n        this.isOverlayAnimationInProgress = false;\n    }\n    focus() {\n        let focusable = DomHandler.findSingle(this.container, '[autofocus]');\n        if (focusable) {\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => focusable.focus(), 5);\n            });\n        }\n    }\n    hide() {\n        if (this.isOverlayAnimationInProgress) {\n            return;\n        }\n        this.overlayVisible = false;\n        this.cd.markForCheck();\n    }\n    onCloseClick(event) {\n        this.hide();\n        event.preventDefault();\n    }\n    onWindowResize(event) {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                if (this.overlayVisible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    onContainerDestroy() {\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n    }\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        if (this.container && this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n        this.destroyCallback = null;\n        if (this.container) {\n            this.restoreAppend();\n            this.onContainerDestroy();\n        }\n        if (this.overlaySubscription) {\n            this.overlaySubscription.unsubscribe();\n        }\n    }\n}\nOverlayPanel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanel, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nOverlayPanel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: OverlayPanel, selector: \"p-overlayPanel\", inputs: { dismissable: \"dismissable\", showCloseIcon: \"showCloseIcon\", style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", ariaCloseLabel: \"ariaCloseLabel\", baseZIndex: \"baseZIndex\", focusOnShow: \"focusOnShow\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div *ngIf=\"render\" [ngClass]=\"'p-overlaypanel p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: (overlayVisible ? 'open': 'close'), params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n                (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div class=\"p-overlaypanel-content\" (click)=\"onContentClick()\" (mousedown)=\"onContentClick()\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <button *ngIf=\"showCloseIcon\" type=\"button\" class=\"p-overlaypanel-close p-link\" (click)=\"onCloseClick($event)\" (keydown.enter)=\"hide()\" [attr.aria-label]=\"ariaCloseLabel\" pRipple>\n                <span class=\"p-overlaypanel-close-icon pi pi-times\"></span>\n            </button>\n        </div>\n    `, isInline: true, styles: [\".p-overlaypanel{position:absolute;margin-top:10px;top:0;left:0}.p-overlaypanel-flipped{margin-top:0;margin-bottom:10px}.p-overlaypanel-close{display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-overlaypanel:after,.p-overlaypanel:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-overlaypanel:after{border-width:8px;margin-left:-8px}.p-overlaypanel:before{border-width:10px;margin-left:-10px}.p-overlaypanel-shifted:after,.p-overlaypanel-shifted:before{left:auto;right:1.25em;margin-left:auto}.p-overlaypanel-flipped:after,.p-overlaypanel-flipped:before{bottom:auto;top:100%}.p-overlaypanel.p-overlaypanel-flipped:after{border-bottom-color:transparent}.p-overlaypanel.p-overlaypanel-flipped:before{border-bottom-color:transparent}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }], animations: [\n        trigger('animation', [\n            state('void', style({\n                transform: 'scaleY(0.8)',\n                opacity: 0\n            })),\n            state('close', style({\n                opacity: 0\n            })),\n            state('open', style({\n                transform: 'translateY(0)',\n                opacity: 1\n            })),\n            transition('void => open', animate('{{showTransitionParams}}')),\n            transition('open => close', animate('{{hideTransitionParams}}')),\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-overlayPanel', template: `\n        <div *ngIf=\"render\" [ngClass]=\"'p-overlaypanel p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: (overlayVisible ? 'open': 'close'), params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n                (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div class=\"p-overlaypanel-content\" (click)=\"onContentClick()\" (mousedown)=\"onContentClick()\">\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n            </div>\n            <button *ngIf=\"showCloseIcon\" type=\"button\" class=\"p-overlaypanel-close p-link\" (click)=\"onCloseClick($event)\" (keydown.enter)=\"hide()\" [attr.aria-label]=\"ariaCloseLabel\" pRipple>\n                <span class=\"p-overlaypanel-close-icon pi pi-times\"></span>\n            </button>\n        </div>\n    `, animations: [\n                        trigger('animation', [\n                            state('void', style({\n                                transform: 'scaleY(0.8)',\n                                opacity: 0\n                            })),\n                            state('close', style({\n                                opacity: 0\n                            })),\n                            state('open', style({\n                                transform: 'translateY(0)',\n                                opacity: 1\n                            })),\n                            transition('void => open', animate('{{showTransitionParams}}')),\n                            transition('open => close', animate('{{hideTransitionParams}}')),\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-overlaypanel{position:absolute;margin-top:10px;top:0;left:0}.p-overlaypanel-flipped{margin-top:0;margin-bottom:10px}.p-overlaypanel-close{display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-overlaypanel:after,.p-overlaypanel:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-overlaypanel:after{border-width:8px;margin-left:-8px}.p-overlaypanel:before{border-width:10px;margin-left:-10px}.p-overlaypanel-shifted:after,.p-overlaypanel-shifted:before{left:auto;right:1.25em;margin-left:auto}.p-overlaypanel-flipped:after,.p-overlaypanel-flipped:before{bottom:auto;top:100%}.p-overlaypanel.p-overlaypanel-flipped:after{border-bottom-color:transparent}.p-overlaypanel.p-overlaypanel-flipped:before{border-bottom-color:transparent}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }]; }, propDecorators: { dismissable: [{\n                type: Input\n            }], showCloseIcon: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], ariaCloseLabel: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], focusOnShow: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass OverlayPanelModule {\n}\nOverlayPanelModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanelModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nOverlayPanelModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanelModule, declarations: [OverlayPanel], imports: [CommonModule, RippleModule, SharedModule], exports: [OverlayPanel, SharedModule] });\nOverlayPanelModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanelModule, imports: [CommonModule, RippleModule, SharedModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: OverlayPanelModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RippleModule, SharedModule],\n                    exports: [OverlayPanel, SharedModule],\n                    declarations: [OverlayPanel]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OverlayPanel, OverlayPanelModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,eAA7F,EAA8GC,QAA9G,QAA8H,eAA9H;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,aAA5C;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,WAAT,QAA4B,eAA5B;;;;IA4P+FvB,EAO/E,sB;;;;;;gBAP+EA,E;;IAAAA,EASnF,+B;IATmFA,EASH;MATGA,EASH;MAAA,eATGA,EASH;MAAA,OATGA,EASM,yCAAT;IAAA;MATGA,EASH;MAAA,eATGA,EASH;MAAA,OATGA,EAS6C,2BAAhD;IAAA,E;IATGA,EAU/E,wB;IAV+EA,EAWnF,e;;;;mBAXmFA,E;IAAAA,EASqD,iD;;;;;;;;;;;;;;;;;;;;gBATrDA,E;;IAAAA,EAEvF,4B;IAFuFA,EAEa;MAFbA,EAEa;MAAA,eAFbA,EAEa;MAAA,OAFbA,EAEsB,2CAAT;IAAA;MAFbA,EAEa;MAAA,eAFbA,EAEa;MAAA,OAFbA,EAI3D,6CAFwE;IAAA;MAFbA,EAEa;MAAA,eAFbA,EAEa;MAAA,OAFbA,EAId,2CAF2B;IAAA,E;IAFbA,EAKnF,4B;IALmFA,EAK/C;MAL+CA,EAK/C;MAAA,gBAL+CA,EAK/C;MAAA,OAL+CA,EAKtC,sCAAT;IAAA;MAL+CA,EAK/C;MAAA,gBAL+CA,EAK/C;MAAA,OAL+CA,EAKP,sCAAxC;IAAA,E;IAL+CA,EAM/E,gB;IAN+EA,EAO/E,mF;IAP+EA,EAQnF,e;IARmFA,EASnF,uE;IATmFA,EAYvF,e;;;;mBAZuFA,E;IAAAA,EAER,8B;IAFQA,EAEnE,4FAFmEA,EAEnE,oEAFmEA,EAEnE,sF;IAFmEA,EAOhE,a;IAPgEA,EAOhE,uD;IAPgEA,EAS1E,a;IAT0EA,EAS1E,yC;;;;;;AAnQrB,MAAMwB,YAAN,CAAmB;EACfC,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,IAAnB,EAAyBC,MAAzB,EAAiCC,cAAjC,EAAiD;IACxD,KAAKL,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,QAAL,GAAgB,MAAhB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,MAAL,GAAc,IAAItC,YAAJ,EAAd;IACA,KAAKuC,MAAL,GAAc,IAAIvC,YAAJ,EAAd;IACA,KAAKwC,cAAL,GAAsB,KAAtB;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,4BAAL,GAAoC,KAApC;IACA,KAAKC,SAAL,GAAiB,KAAjB;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;;QACJ;UACI,KAAKD,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;MANR;;MAQA,KAAKvB,EAAL,CAAQwB,YAAR;IACH,CAVD;EAWH;;EACDC,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAKC,qBAAN,IAA+B,KAAKtB,WAAxC,EAAqD;MACjD,KAAKH,IAAL,CAAU0B,iBAAV,CAA4B,MAAM;QAC9B,IAAIC,aAAa,GAAG7C,UAAU,CAAC8C,KAAX,KAAqB,YAArB,GAAoC,OAAxD;QACA,MAAMC,cAAc,GAAG,KAAKhC,EAAL,GAAU,KAAKA,EAAL,CAAQiC,aAAR,CAAsBC,aAAhC,GAAgD,UAAvE;QACA,KAAKN,qBAAL,GAA6B,KAAK3B,QAAL,CAAckC,MAAd,CAAqBH,cAArB,EAAqCF,aAArC,EAAqDM,KAAD,IAAW;UACxF,IAAI,CAAC,KAAKC,SAAL,CAAeC,QAAf,CAAwBF,KAAK,CAACG,MAA9B,CAAD,IAA0C,KAAKA,MAAL,KAAgBH,KAAK,CAACG,MAAhE,IAA0E,CAAC,KAAKA,MAAL,CAAYD,QAAZ,CAAqBF,KAAK,CAACG,MAA3B,CAA3E,IAAiH,CAAC,KAAKrB,SAA3H,EAAsI;YAClI,KAAKf,IAAL,CAAUqC,GAAV,CAAc,MAAM;cAChB,KAAKC,IAAL;YACH,CAFD;UAGH;;UACD,KAAKvB,SAAL,GAAiB,KAAjB;UACA,KAAKhB,EAAL,CAAQwB,YAAR;QACH,CAR4B,CAA7B;MASH,CAZD;IAaH;EACJ;;EACDgB,2BAA2B,GAAG;IAC1B,IAAI,KAAKd,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;MACA,KAAKV,SAAL,GAAiB,KAAjB;IACH;EACJ;;EACDyB,MAAM,CAACP,KAAD,EAAQG,MAAR,EAAgB;IAClB,IAAI,KAAKtB,4BAAT,EAAuC;MACnC;IACH;;IACD,IAAI,KAAKF,cAAT,EAAyB;MACrB,IAAI,KAAK6B,gBAAL,CAAsBR,KAAtB,EAA6BG,MAA7B,CAAJ,EAA0C;QACtC,KAAKM,eAAL,GAAuB,MAAM;UACzB,KAAKC,IAAL,CAAU,IAAV,EAAiBP,MAAM,IAAIH,KAAK,CAACW,aAAhB,IAAiCX,KAAK,CAACG,MAAxD;QACH,CAFD;MAGH;;MACD,KAAKE,IAAL;IACH,CAPD,MAQK;MACD,KAAKK,IAAL,CAAUV,KAAV,EAAiBG,MAAjB;IACH;EACJ;;EACDO,IAAI,CAACV,KAAD,EAAQG,MAAR,EAAgB;IAChB,IAAI,KAAKtB,4BAAT,EAAuC;MACnC;IACH;;IACD,KAAKsB,MAAL,GAAcA,MAAM,IAAIH,KAAK,CAACW,aAAhB,IAAiCX,KAAK,CAACG,MAArD;IACA,KAAKxB,cAAL,GAAsB,IAAtB;IACA,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKd,EAAL,CAAQwB,YAAR;EACH;;EACDsB,cAAc,CAACZ,KAAD,EAAQ;IAClB,KAAK/B,cAAL,CAAoB4C,GAApB,CAAwB;MACpBC,aAAa,EAAEd,KADK;MAEpBG,MAAM,EAAE,KAAKvC,EAAL,CAAQiC;IAFI,CAAxB;IAIA,KAAKf,SAAL,GAAiB,IAAjB;EACH;;EACDiC,cAAc,GAAG;IACb,KAAKjC,SAAL,GAAiB,IAAjB;EACH;;EACD0B,gBAAgB,CAACR,KAAD,EAAQG,MAAR,EAAgB;IAC5B,OAAO,KAAKA,MAAL,IAAe,IAAf,IAAuB,KAAKA,MAAL,MAAiBA,MAAM,IAAIH,KAAK,CAACW,aAAhB,IAAiCX,KAAK,CAACG,MAAxD,CAA9B;EACH;;EACDa,eAAe,GAAG;IACd,IAAI,KAAK7C,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACI8C,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKlB,SAA/B,EADJ,KAGIpD,UAAU,CAACsE,WAAX,CAAuB,KAAKlB,SAA5B,EAAuC,KAAK9B,QAA5C;IACP;EACJ;;EACDiD,aAAa,GAAG;IACZ,IAAI,KAAKnB,SAAL,IAAkB,KAAK9B,QAA3B,EAAqC;MACjC,KAAKP,EAAL,CAAQiC,aAAR,CAAsBsB,WAAtB,CAAkC,KAAKlB,SAAvC;IACH;EACJ;;EACDoB,KAAK,GAAG;IACJ,IAAI,KAAKjD,UAAT,EAAqB;MACjBX,WAAW,CAAC6D,GAAZ,CAAgB,SAAhB,EAA2B,KAAKrB,SAAhC,EAA2C,KAAK5B,UAAL,GAAkB,KAAKL,MAAL,CAAYuD,MAAZ,CAAmBC,OAAhF;IACH;;IACD3E,UAAU,CAAC4E,gBAAX,CAA4B,KAAKxB,SAAjC,EAA4C,KAAKE,MAAjD;IACA,MAAMuB,eAAe,GAAG7E,UAAU,CAAC8E,SAAX,CAAqB,KAAK1B,SAA1B,CAAxB;IACA,MAAM2B,YAAY,GAAG/E,UAAU,CAAC8E,SAAX,CAAqB,KAAKxB,MAA1B,CAArB;IACA,IAAI0B,SAAS,GAAG,CAAhB;;IACA,IAAIH,eAAe,CAACI,IAAhB,GAAuBF,YAAY,CAACE,IAAxC,EAA8C;MAC1CD,SAAS,GAAGD,YAAY,CAACE,IAAb,GAAoBJ,eAAe,CAACI,IAAhD;IACH;;IACD,KAAK7B,SAAL,CAAe3C,KAAf,CAAqByE,WAArB,CAAiC,oBAAjC,EAAwD,GAAEF,SAAU,IAApE;;IACA,IAAIH,eAAe,CAACM,GAAhB,GAAsBJ,YAAY,CAACI,GAAvC,EAA4C;MACxCnF,UAAU,CAACoF,QAAX,CAAoB,KAAKhC,SAAzB,EAAoC,wBAApC;IACH;EACJ;;EACDiC,gBAAgB,CAAClC,KAAD,EAAQ;IACpB,IAAIA,KAAK,CAACmC,OAAN,KAAkB,MAAtB,EAA8B;MAC1B,KAAKlC,SAAL,GAAiBD,KAAK,CAACoC,OAAvB;MACA,KAAK3D,MAAL,CAAY4D,IAAZ,CAAiB,IAAjB;MACA,KAAKrB,eAAL;MACA,KAAKK,KAAL;MACA,KAAK9B,yBAAL;MACA,KAAK+C,0BAAL;MACA,KAAKC,kBAAL;;MACA,IAAI,KAAKjE,WAAT,EAAsB;QAClB,KAAKkE,KAAL;MACH;;MACD,KAAKC,oBAAL,GAA6BC,CAAD,IAAO;QAC/B,IAAI,KAAKzC,SAAL,IAAkB,KAAKA,SAAL,CAAeC,QAAf,CAAwBwC,CAAC,CAACvC,MAA1B,CAAtB,EAAyD;UACrD,KAAKrB,SAAL,GAAiB,IAAjB;QACH;MACJ,CAJD;;MAKA,KAAK6D,mBAAL,GAA2B,KAAK1E,cAAL,CAAoB2E,eAApB,CAAoCC,SAApC,CAA8C,KAAKJ,oBAAnD,CAA3B;IACH;;IACD,KAAK5D,4BAAL,GAAoC,IAApC;EACH;;EACDiE,cAAc,CAAC9C,KAAD,EAAQ;IAClB,QAAQA,KAAK,CAACmC,OAAd;MACI,KAAK,MAAL;QACI,IAAI,KAAK1B,eAAT,EAA0B;UACtB,KAAKA,eAAL;UACA,KAAKA,eAAL,GAAuB,IAAvB;QACH;;QACD,IAAI,KAAKkC,mBAAT,EAA8B;UAC1B,KAAKA,mBAAL,CAAyBI,WAAzB;QACH;;QACD;;MACJ,KAAK,OAAL;QACI,IAAI,KAAK3E,UAAT,EAAqB;UACjBX,WAAW,CAACuF,KAAZ,CAAkB,KAAK/C,SAAvB;QACH;;QACD,IAAI,KAAK0C,mBAAT,EAA8B;UAC1B,KAAKA,mBAAL,CAAyBI,WAAzB;QACH;;QACD,KAAKE,kBAAL;QACA,KAAKvE,MAAL,CAAY2D,IAAZ,CAAiB,EAAjB;QACA,KAAKzD,MAAL,GAAc,KAAd;QACA;IApBR;;IAsBA,KAAKC,4BAAL,GAAoC,KAApC;EACH;;EACD2D,KAAK,GAAG;IACJ,IAAIU,SAAS,GAAGrG,UAAU,CAACsG,UAAX,CAAsB,KAAKlD,SAA3B,EAAsC,aAAtC,CAAhB;;IACA,IAAIiD,SAAJ,EAAe;MACX,KAAKnF,IAAL,CAAU0B,iBAAV,CAA4B,MAAM;QAC9B2D,UAAU,CAAC,MAAMF,SAAS,CAACV,KAAV,EAAP,EAA0B,CAA1B,CAAV;MACH,CAFD;IAGH;EACJ;;EACDnC,IAAI,GAAG;IACH,IAAI,KAAKxB,4BAAT,EAAuC;MACnC;IACH;;IACD,KAAKF,cAAL,GAAsB,KAAtB;IACA,KAAKb,EAAL,CAAQwB,YAAR;EACH;;EACD+D,YAAY,CAACrD,KAAD,EAAQ;IAChB,KAAKK,IAAL;IACAL,KAAK,CAACsD,cAAN;EACH;;EACDC,cAAc,CAACvD,KAAD,EAAQ;IAClB,IAAI,KAAKrB,cAAL,IAAuB,CAAC9B,UAAU,CAAC2G,aAAX,EAA5B,EAAwD;MACpD,KAAKnD,IAAL;IACH;EACJ;;EACDiC,0BAA0B,GAAG;IACzB,KAAKmB,sBAAL,GAA8B,KAAKF,cAAL,CAAoBG,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKH,sBAAvC;EACH;;EACDI,4BAA4B,GAAG;IAC3B,IAAI,KAAKJ,sBAAT,EAAiC;MAC7BE,MAAM,CAACG,mBAAP,CAA2B,QAA3B,EAAqC,KAAKL,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDlB,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKwB,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAIjH,6BAAJ,CAAkC,KAAKqD,MAAvC,EAA+C,MAAM;QACtE,IAAI,KAAKxB,cAAT,EAAyB;UACrB,KAAK0B,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAK0D,aAAL,CAAmBxB,kBAAnB;EACH;;EACDyB,oBAAoB,GAAG;IACnB,IAAI,KAAKD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBC,oBAAnB;IACH;EACJ;;EACDf,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKnF,EAAL,CAAQmG,SAAb,EAAwB;MACpB,KAAK9D,MAAL,GAAc,IAAd;IACH;;IACD,KAAKG,2BAAL;IACA,KAAKuD,4BAAL;IACA,KAAKG,oBAAL;EACH;;EACDE,WAAW,GAAG;IACV,IAAI,KAAKH,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBI,OAAnB;MACA,KAAKJ,aAAL,GAAqB,IAArB;IACH;;IACD,IAAI,KAAK9D,SAAL,IAAkB,KAAK7B,UAA3B,EAAuC;MACnCX,WAAW,CAACuF,KAAZ,CAAkB,KAAK/C,SAAvB;IACH;;IACD,IAAI,CAAC,KAAKnC,EAAL,CAAQmG,SAAb,EAAwB;MACpB,KAAK9D,MAAL,GAAc,IAAd;IACH;;IACD,KAAKM,eAAL,GAAuB,IAAvB;;IACA,IAAI,KAAKR,SAAT,EAAoB;MAChB,KAAKmB,aAAL;MACA,KAAK6B,kBAAL;IACH;;IACD,IAAI,KAAKN,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBI,WAAzB;IACH;EACJ;;AAxPc;;AA0PnBrF,YAAY,CAAC0G,IAAb;EAAA,iBAAyG1G,YAAzG,EAA+FxB,EAA/F,mBAAuIA,EAAE,CAACmI,UAA1I,GAA+FnI,EAA/F,mBAAiKA,EAAE,CAACoI,SAApK,GAA+FpI,EAA/F,mBAA0LA,EAAE,CAACqI,iBAA7L,GAA+FrI,EAA/F,mBAA2NA,EAAE,CAACsI,MAA9N,GAA+FtI,EAA/F,mBAAiPa,EAAE,CAAC0H,aAApP,GAA+FvI,EAA/F,mBAA8Qa,EAAE,CAAC2H,cAAjR;AAAA;;AACAhH,YAAY,CAACiH,IAAb,kBAD+FzI,EAC/F;EAAA,MAA6FwB,YAA7F;EAAA;EAAA;IAAA;MAD+FxB,EAC/F,0BAA2mBc,aAA3mB;IAAA;;IAAA;MAAA;;MAD+Fd,EAC/F,qBAD+FA,EAC/F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FA,EAC/F;MAD+FA,EAEvF,4DADR;IAAA;;IAAA;MAD+FA,EAEjF,+BADd;IAAA;EAAA;EAAA,eAY06BS,EAAE,CAACiI,OAZ76B,EAYwgCjI,EAAE,CAACkI,IAZ3gC,EAY4mClI,EAAE,CAACmI,gBAZ/mC,EAYmxCnI,EAAE,CAACoI,OAZtxC,EAYw2C7H,EAAE,CAAC8H,MAZ32C;EAAA;EAAA;EAAA;IAAA,WAYy5C,CACj5C5H,OAAO,CAAC,WAAD,EAAc,CACjBC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;MAChB2H,SAAS,EAAE,aADK;MAEhBC,OAAO,EAAE;IAFO,CAAD,CAAd,CADY,EAKjB7H,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;MACjB4H,OAAO,EAAE;IADQ,CAAD,CAAf,CALY,EAQjB7H,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;MAChB2H,SAAS,EAAE,eADK;MAEhBC,OAAO,EAAE;IAFO,CAAD,CAAd,CARY,EAYjB3H,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CAZO,EAajBD,UAAU,CAAC,eAAD,EAAkBC,OAAO,CAAC,0BAAD,CAAzB,CAbO,CAAd,CAD04C;EAZz5C;EAAA;AAAA;;AA6BA;EAAA,mDA9B+FtB,EA8B/F,mBAA2FwB,YAA3F,EAAqH,CAAC;IAC1GyH,IAAI,EAAE/I,SADoG;IAE1GgJ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAZ;MAA8BhG,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAZmB;MAYZiG,UAAU,EAAE,CACKlI,OAAO,CAAC,WAAD,EAAc,CACjBC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;QAChB2H,SAAS,EAAE,aADK;QAEhBC,OAAO,EAAE;MAFO,CAAD,CAAd,CADY,EAKjB7H,KAAK,CAAC,OAAD,EAAUC,KAAK,CAAC;QACjB4H,OAAO,EAAE;MADQ,CAAD,CAAf,CALY,EAQjB7H,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;QAChB2H,SAAS,EAAE,eADK;QAEhBC,OAAO,EAAE;MAFO,CAAD,CAAd,CARY,EAYjB3H,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CAZO,EAajBD,UAAU,CAAC,eAAD,EAAkBC,OAAO,CAAC,0BAAD,CAAzB,CAbO,CAAd,CADZ,CAZA;MA4BI+H,eAAe,EAAElJ,uBAAuB,CAACmJ,MA5B7C;MA4BqDC,aAAa,EAAEnJ,iBAAiB,CAACoJ,IA5BtF;MA4B4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CA5BlG;MA8BIC,MAAM,EAAE,CAAC,61BAAD;IA9BZ,CAAD;EAFoG,CAAD,CAArH,EAiC4B,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEjJ,EAAE,CAACmI;IAAX,CAAD,EAA0B;MAAEc,IAAI,EAAEjJ,EAAE,CAACoI;IAAX,CAA1B,EAAkD;MAAEa,IAAI,EAAEjJ,EAAE,CAACqI;IAAX,CAAlD,EAAkF;MAAEY,IAAI,EAAEjJ,EAAE,CAACsI;IAAX,CAAlF,EAAuG;MAAEW,IAAI,EAAEpI,EAAE,CAAC0H;IAAX,CAAvG,EAAmI;MAAEU,IAAI,EAAEpI,EAAE,CAAC2H;IAAX,CAAnI,CAAP;EAAyK,CAjCnN,EAiCqO;IAAExG,WAAW,EAAE,CAAC;MACrOiH,IAAI,EAAE5I;IAD+N,CAAD,CAAf;IAErNsJ,aAAa,EAAE,CAAC;MAChBV,IAAI,EAAE5I;IADU,CAAD,CAFsM;IAIrNe,KAAK,EAAE,CAAC;MACR6H,IAAI,EAAE5I;IADE,CAAD,CAJ8M;IAMrNuJ,UAAU,EAAE,CAAC;MACbX,IAAI,EAAE5I;IADO,CAAD,CANyM;IAQrN4B,QAAQ,EAAE,CAAC;MACXgH,IAAI,EAAE5I;IADK,CAAD,CAR2M;IAUrN6B,UAAU,EAAE,CAAC;MACb+G,IAAI,EAAE5I;IADO,CAAD,CAVyM;IAYrNwJ,cAAc,EAAE,CAAC;MACjBZ,IAAI,EAAE5I;IADW,CAAD,CAZqM;IAcrN8B,UAAU,EAAE,CAAC;MACb8G,IAAI,EAAE5I;IADO,CAAD,CAdyM;IAgBrN+B,WAAW,EAAE,CAAC;MACd6G,IAAI,EAAE5I;IADQ,CAAD,CAhBwM;IAkBrNgC,qBAAqB,EAAE,CAAC;MACxB4G,IAAI,EAAE5I;IADkB,CAAD,CAlB8L;IAoBrNiC,qBAAqB,EAAE,CAAC;MACxB2G,IAAI,EAAE5I;IADkB,CAAD,CApB8L;IAsBrNkC,MAAM,EAAE,CAAC;MACT0G,IAAI,EAAE3I;IADG,CAAD,CAtB6M;IAwBrNkC,MAAM,EAAE,CAAC;MACTyG,IAAI,EAAE3I;IADG,CAAD,CAxB6M;IA0BrNwC,SAAS,EAAE,CAAC;MACZmG,IAAI,EAAE1I,eADM;MAEZ2I,IAAI,EAAE,CAACpI,aAAD;IAFM,CAAD;EA1B0M,CAjCrO;AAAA;;AA+DA,MAAMgJ,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAAC5B,IAAnB;EAAA,iBAA+G4B,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBAhG+F/J,EAgG/F;EAAA,MAAgH8J;AAAhH;AACAA,kBAAkB,CAACE,IAAnB,kBAjG+FhK,EAiG/F;EAAA,UAA8IU,YAA9I,EAA4JO,YAA5J,EAA0KF,YAA1K,EAAwLA,YAAxL;AAAA;;AACA;EAAA,mDAlG+Ff,EAkG/F,mBAA2F8J,kBAA3F,EAA2H,CAAC;IAChHb,IAAI,EAAEzI,QAD0G;IAEhH0I,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAACvJ,YAAD,EAAeO,YAAf,EAA6BF,YAA7B,CADV;MAECmJ,OAAO,EAAE,CAAC1I,YAAD,EAAeT,YAAf,CAFV;MAGCoJ,YAAY,EAAE,CAAC3I,YAAD;IAHf,CAAD;EAF0G,CAAD,CAA3H;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,YAAT,EAAuBsI,kBAAvB"}, "metadata": {}, "sourceType": "module"}