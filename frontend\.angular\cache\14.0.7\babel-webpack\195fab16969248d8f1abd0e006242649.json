{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const windows = [];\n\n    const handleError = err => {\n      while (0 < windows.length) {\n        windows.shift().error(err);\n      }\n\n      subscriber.error(err);\n    };\n\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, openValue => {\n      const window = new Subject();\n      windows.push(window);\n      const closingSubscription = new Subscription();\n\n      const closeWindow = () => {\n        arrRemove(windows, window);\n        window.complete();\n        closingSubscription.unsubscribe();\n      };\n\n      let closingNotifier;\n\n      try {\n        closingNotifier = innerFrom(closingSelector(openValue));\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n\n      subscriber.next(window.asObservable());\n      closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const windowsCopy = windows.slice();\n\n      for (const window of windowsCopy) {\n        window.next(value);\n      }\n    }, () => {\n      while (0 < windows.length) {\n        windows.shift().complete();\n      }\n\n      subscriber.complete();\n    }, handleError, () => {\n      while (0 < windows.length) {\n        windows.shift().unsubscribe();\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "windowToggle", "openings", "closingSelector", "source", "subscriber", "windows", "handleError", "err", "length", "shift", "error", "subscribe", "openValue", "window", "push", "closingSubscription", "closeWindow", "complete", "unsubscribe", "closingNotifier", "next", "asObservable", "add", "value", "windowsCopy", "slice"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/windowToggle.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n    return operate((source, subscriber) => {\n        const windows = [];\n        const handleError = (err) => {\n            while (0 < windows.length) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        };\n        innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, (openValue) => {\n            const window = new Subject();\n            windows.push(window);\n            const closingSubscription = new Subscription();\n            const closeWindow = () => {\n                arrRemove(windows, window);\n                window.complete();\n                closingSubscription.unsubscribe();\n            };\n            let closingNotifier;\n            try {\n                closingNotifier = innerFrom(closingSelector(openValue));\n            }\n            catch (err) {\n                handleError(err);\n                return;\n            }\n            subscriber.next(window.asObservable());\n            closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n        }, noop));\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const windowsCopy = windows.slice();\n            for (const window of windowsCopy) {\n                window.next(value);\n            }\n        }, () => {\n            while (0 < windows.length) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, handleError, () => {\n            while (0 < windows.length) {\n                windows.shift().unsubscribe();\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,OAAO,SAASC,YAAT,CAAsBC,QAAtB,EAAgCC,eAAhC,EAAiD;EACpD,OAAOP,OAAO,CAAC,CAACQ,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,OAAO,GAAG,EAAhB;;IACA,MAAMC,WAAW,GAAIC,GAAD,IAAS;MACzB,OAAO,IAAIF,OAAO,CAACG,MAAnB,EAA2B;QACvBH,OAAO,CAACI,KAAR,GAAgBC,KAAhB,CAAsBH,GAAtB;MACH;;MACDH,UAAU,CAACM,KAAX,CAAiBH,GAAjB;IACH,CALD;;IAMAX,SAAS,CAACK,QAAD,CAAT,CAAoBU,SAApB,CAA8Bd,wBAAwB,CAACO,UAAD,EAAcQ,SAAD,IAAe;MAC9E,MAAMC,MAAM,GAAG,IAAIpB,OAAJ,EAAf;MACAY,OAAO,CAACS,IAAR,CAAaD,MAAb;MACA,MAAME,mBAAmB,GAAG,IAAIrB,YAAJ,EAA5B;;MACA,MAAMsB,WAAW,GAAG,MAAM;QACtBjB,SAAS,CAACM,OAAD,EAAUQ,MAAV,CAAT;QACAA,MAAM,CAACI,QAAP;QACAF,mBAAmB,CAACG,WAApB;MACH,CAJD;;MAKA,IAAIC,eAAJ;;MACA,IAAI;QACAA,eAAe,GAAGvB,SAAS,CAACM,eAAe,CAACU,SAAD,CAAhB,CAA3B;MACH,CAFD,CAGA,OAAOL,GAAP,EAAY;QACRD,WAAW,CAACC,GAAD,CAAX;QACA;MACH;;MACDH,UAAU,CAACgB,IAAX,CAAgBP,MAAM,CAACQ,YAAP,EAAhB;MACAN,mBAAmB,CAACO,GAApB,CAAwBH,eAAe,CAACR,SAAhB,CAA0Bd,wBAAwB,CAACO,UAAD,EAAaY,WAAb,EAA0BlB,IAA1B,EAAgCQ,WAAhC,CAAlD,CAAxB;IACH,CAnBqD,EAmBnDR,IAnBmD,CAAtD;IAoBAK,MAAM,CAACQ,SAAP,CAAiBd,wBAAwB,CAACO,UAAD,EAAcmB,KAAD,IAAW;MAC7D,MAAMC,WAAW,GAAGnB,OAAO,CAACoB,KAAR,EAApB;;MACA,KAAK,MAAMZ,MAAX,IAAqBW,WAArB,EAAkC;QAC9BX,MAAM,CAACO,IAAP,CAAYG,KAAZ;MACH;IACJ,CALwC,EAKtC,MAAM;MACL,OAAO,IAAIlB,OAAO,CAACG,MAAnB,EAA2B;QACvBH,OAAO,CAACI,KAAR,GAAgBQ,QAAhB;MACH;;MACDb,UAAU,CAACa,QAAX;IACH,CAVwC,EAUtCX,WAVsC,EAUzB,MAAM;MAClB,OAAO,IAAID,OAAO,CAACG,MAAnB,EAA2B;QACvBH,OAAO,CAACI,KAAR,GAAgBS,WAAhB;MACH;IACJ,CAdwC,CAAzC;EAeH,CA3Ca,CAAd;AA4CH"}, "metadata": {}, "sourceType": "module"}