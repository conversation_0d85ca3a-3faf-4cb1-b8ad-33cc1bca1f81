{"ast": null, "code": "'use strict';\n\nmodule.exports = ansiHTML; // Reference to https://github.com/sindresorhus/ansi-regex\n\nvar _regANSI = /(?:(?:\\u001b\\[)|\\u009b)(?:(?:[0-9]{1,3})?(?:(?:;[0-9]{0,3})*)?[A-M|f-m])|\\u001b[A-M]/;\nvar _defColors = {\n  reset: ['fff', '000'],\n  // [FOREGROUD_COLOR, BACKGROUND_COLOR]\n  black: '000',\n  red: 'ff0000',\n  green: '209805',\n  yellow: 'e8bf03',\n  blue: '0000ff',\n  magenta: 'ff00ff',\n  cyan: '00ffee',\n  lightgrey: 'f0f0f0',\n  darkgrey: '888'\n};\nvar _styles = {\n  30: 'black',\n  31: 'red',\n  32: 'green',\n  33: 'yellow',\n  34: 'blue',\n  35: 'magenta',\n  36: 'cyan',\n  37: 'lightgrey'\n};\nvar _openTags = {\n  '1': 'font-weight:bold',\n  // bold\n  '2': 'opacity:0.5',\n  // dim\n  '3': '<i>',\n  // italic\n  '4': '<u>',\n  // underscore\n  '8': 'display:none',\n  // hidden\n  '9': '<del>' // delete\n\n};\nvar _closeTags = {\n  '23': '</i>',\n  // reset italic\n  '24': '</u>',\n  // reset underscore\n  '29': '</del>' // reset delete\n\n};\n[0, 21, 22, 27, 28, 39, 49].forEach(function (n) {\n  _closeTags[n] = '</span>';\n});\n/**\n * Converts text with ANSI color codes to HTML markup.\n * @param {String} text\n * @returns {*}\n */\n\nfunction ansiHTML(text) {\n  // Returns the text if the string has no ANSI escape code.\n  if (!_regANSI.test(text)) {\n    return text;\n  } // Cache opened sequence.\n\n\n  var ansiCodes = []; // Replace with markup.\n\n  var ret = text.replace(/\\033\\[(\\d+)m/g, function (match, seq) {\n    var ot = _openTags[seq];\n\n    if (ot) {\n      // If current sequence has been opened, close it.\n      if (!!~ansiCodes.indexOf(seq)) {\n        // eslint-disable-line no-extra-boolean-cast\n        ansiCodes.pop();\n        return '</span>';\n      } // Open tag.\n\n\n      ansiCodes.push(seq);\n      return ot[0] === '<' ? ot : '<span style=\"' + ot + ';\">';\n    }\n\n    var ct = _closeTags[seq];\n\n    if (ct) {\n      // Pop sequence\n      ansiCodes.pop();\n      return ct;\n    }\n\n    return '';\n  }); // Make sure tags are closed.\n\n  var l = ansiCodes.length;\n  l > 0 && (ret += Array(l + 1).join('</span>'));\n  return ret;\n}\n/**\n * Customize colors.\n * @param {Object} colors reference to _defColors\n */\n\n\nansiHTML.setColors = function (colors) {\n  if (typeof colors !== 'object') {\n    throw new Error('`colors` parameter must be an Object.');\n  }\n\n  var _finalColors = {};\n\n  for (var key in _defColors) {\n    var hex = colors.hasOwnProperty(key) ? colors[key] : null;\n\n    if (!hex) {\n      _finalColors[key] = _defColors[key];\n      continue;\n    }\n\n    if ('reset' === key) {\n      if (typeof hex === 'string') {\n        hex = [hex];\n      }\n\n      if (!Array.isArray(hex) || hex.length === 0 || hex.some(function (h) {\n        return typeof h !== 'string';\n      })) {\n        throw new Error('The value of `' + key + '` property must be an Array and each item could only be a hex string, e.g.: FF0000');\n      }\n\n      var defHexColor = _defColors[key];\n\n      if (!hex[0]) {\n        hex[0] = defHexColor[0];\n      }\n\n      if (hex.length === 1 || !hex[1]) {\n        hex = [hex[0]];\n        hex.push(defHexColor[1]);\n      }\n\n      hex = hex.slice(0, 2);\n    } else if (typeof hex !== 'string') {\n      throw new Error('The value of `' + key + '` property must be a hex string, e.g.: FF0000');\n    }\n\n    _finalColors[key] = hex;\n  }\n\n  _setTags(_finalColors);\n};\n/**\n * Reset colors.\n */\n\n\nansiHTML.reset = function () {\n  _setTags(_defColors);\n};\n/**\n * Expose tags, including open and close.\n * @type {Object}\n */\n\n\nansiHTML.tags = {};\n\nif (Object.defineProperty) {\n  Object.defineProperty(ansiHTML.tags, 'open', {\n    get: function () {\n      return _openTags;\n    }\n  });\n  Object.defineProperty(ansiHTML.tags, 'close', {\n    get: function () {\n      return _closeTags;\n    }\n  });\n} else {\n  ansiHTML.tags.open = _openTags;\n  ansiHTML.tags.close = _closeTags;\n}\n\nfunction _setTags(colors) {\n  // reset all\n  _openTags['0'] = 'font-weight:normal;opacity:1;color:#' + colors.reset[0] + ';background:#' + colors.reset[1]; // inverse\n\n  _openTags['7'] = 'color:#' + colors.reset[1] + ';background:#' + colors.reset[0]; // dark grey\n\n  _openTags['90'] = 'color:#' + colors.darkgrey;\n\n  for (var code in _styles) {\n    var color = _styles[code];\n    var oriColor = colors[color] || '000';\n    _openTags[code] = 'color:#' + oriColor;\n    code = parseInt(code);\n    _openTags[(code + 10).toString()] = 'background:#' + oriColor;\n  }\n}\n\nansiHTML.reset();", "map": {"version": 3, "names": ["module", "exports", "ansiHTML", "_regANSI", "_defColors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "_styles", "_openTags", "_closeTags", "for<PERSON>ach", "n", "text", "test", "ansiCodes", "ret", "replace", "match", "seq", "ot", "indexOf", "pop", "push", "ct", "l", "length", "Array", "join", "setColors", "colors", "Error", "_finalColors", "key", "hex", "hasOwnProperty", "isArray", "some", "h", "defHexColor", "slice", "_setTags", "tags", "Object", "defineProperty", "get", "open", "close", "code", "color", "oriColor", "parseInt", "toString"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/ansi-html-community/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = ansiHTML\n\n// Reference to https://github.com/sindresorhus/ansi-regex\nvar _regANSI = /(?:(?:\\u001b\\[)|\\u009b)(?:(?:[0-9]{1,3})?(?:(?:;[0-9]{0,3})*)?[A-M|f-m])|\\u001b[A-M]/\n\nvar _defColors = {\n  reset: ['fff', '000'], // [FOREGROUD_COLOR, BACKGROUND_COLOR]\n  black: '000',\n  red: 'ff0000',\n  green: '209805',\n  yellow: 'e8bf03',\n  blue: '0000ff',\n  magenta: 'ff00ff',\n  cyan: '00ffee',\n  lightgrey: 'f0f0f0',\n  darkgrey: '888'\n}\nvar _styles = {\n  30: 'black',\n  31: 'red',\n  32: 'green',\n  33: 'yellow',\n  34: 'blue',\n  35: 'magenta',\n  36: 'cyan',\n  37: 'lightgrey'\n}\nvar _openTags = {\n  '1': 'font-weight:bold', // bold\n  '2': 'opacity:0.5', // dim\n  '3': '<i>', // italic\n  '4': '<u>', // underscore\n  '8': 'display:none', // hidden\n  '9': '<del>' // delete\n}\nvar _closeTags = {\n  '23': '</i>', // reset italic\n  '24': '</u>', // reset underscore\n  '29': '</del>' // reset delete\n}\n\n;[0, 21, 22, 27, 28, 39, 49].forEach(function (n) {\n  _closeTags[n] = '</span>'\n})\n\n/**\n * Converts text with ANSI color codes to HTML markup.\n * @param {String} text\n * @returns {*}\n */\nfunction ansiHTML (text) {\n  // Returns the text if the string has no ANSI escape code.\n  if (!_regANSI.test(text)) {\n    return text\n  }\n\n  // Cache opened sequence.\n  var ansiCodes = []\n  // Replace with markup.\n  var ret = text.replace(/\\033\\[(\\d+)m/g, function (match, seq) {\n    var ot = _openTags[seq]\n    if (ot) {\n      // If current sequence has been opened, close it.\n      if (!!~ansiCodes.indexOf(seq)) { // eslint-disable-line no-extra-boolean-cast\n        ansiCodes.pop()\n        return '</span>'\n      }\n      // Open tag.\n      ansiCodes.push(seq)\n      return ot[0] === '<' ? ot : '<span style=\"' + ot + ';\">'\n    }\n\n    var ct = _closeTags[seq]\n    if (ct) {\n      // Pop sequence\n      ansiCodes.pop()\n      return ct\n    }\n    return ''\n  })\n\n  // Make sure tags are closed.\n  var l = ansiCodes.length\n  ;(l > 0) && (ret += Array(l + 1).join('</span>'))\n\n  return ret\n}\n\n/**\n * Customize colors.\n * @param {Object} colors reference to _defColors\n */\nansiHTML.setColors = function (colors) {\n  if (typeof colors !== 'object') {\n    throw new Error('`colors` parameter must be an Object.')\n  }\n\n  var _finalColors = {}\n  for (var key in _defColors) {\n    var hex = colors.hasOwnProperty(key) ? colors[key] : null\n    if (!hex) {\n      _finalColors[key] = _defColors[key]\n      continue\n    }\n    if ('reset' === key) {\n      if (typeof hex === 'string') {\n        hex = [hex]\n      }\n      if (!Array.isArray(hex) || hex.length === 0 || hex.some(function (h) {\n        return typeof h !== 'string'\n      })) {\n        throw new Error('The value of `' + key + '` property must be an Array and each item could only be a hex string, e.g.: FF0000')\n      }\n      var defHexColor = _defColors[key]\n      if (!hex[0]) {\n        hex[0] = defHexColor[0]\n      }\n      if (hex.length === 1 || !hex[1]) {\n        hex = [hex[0]]\n        hex.push(defHexColor[1])\n      }\n\n      hex = hex.slice(0, 2)\n    } else if (typeof hex !== 'string') {\n      throw new Error('The value of `' + key + '` property must be a hex string, e.g.: FF0000')\n    }\n    _finalColors[key] = hex\n  }\n  _setTags(_finalColors)\n}\n\n/**\n * Reset colors.\n */\nansiHTML.reset = function () {\n  _setTags(_defColors)\n}\n\n/**\n * Expose tags, including open and close.\n * @type {Object}\n */\nansiHTML.tags = {}\n\nif (Object.defineProperty) {\n  Object.defineProperty(ansiHTML.tags, 'open', {\n    get: function () { return _openTags }\n  })\n  Object.defineProperty(ansiHTML.tags, 'close', {\n    get: function () { return _closeTags }\n  })\n} else {\n  ansiHTML.tags.open = _openTags\n  ansiHTML.tags.close = _closeTags\n}\n\nfunction _setTags (colors) {\n  // reset all\n  _openTags['0'] = 'font-weight:normal;opacity:1;color:#' + colors.reset[0] + ';background:#' + colors.reset[1]\n  // inverse\n  _openTags['7'] = 'color:#' + colors.reset[1] + ';background:#' + colors.reset[0]\n  // dark grey\n  _openTags['90'] = 'color:#' + colors.darkgrey\n\n  for (var code in _styles) {\n    var color = _styles[code]\n    var oriColor = colors[color] || '000'\n    _openTags[code] = 'color:#' + oriColor\n    code = parseInt(code)\n    _openTags[(code + 10).toString()] = 'background:#' + oriColor\n  }\n}\n\nansiHTML.reset()\n"], "mappings": "AAAA;;AAEAA,MAAM,CAACC,OAAP,GAAiBC,QAAjB,C,CAEA;;AACA,IAAIC,QAAQ,GAAG,sFAAf;AAEA,IAAIC,UAAU,GAAG;EACfC,KAAK,EAAE,CAAC,KAAD,EAAQ,KAAR,CADQ;EACQ;EACvBC,KAAK,EAAE,KAFQ;EAGfC,GAAG,EAAE,QAHU;EAIfC,KAAK,EAAE,QAJQ;EAKfC,MAAM,EAAE,QALO;EAMfC,IAAI,EAAE,QANS;EAOfC,OAAO,EAAE,QAPM;EAQfC,IAAI,EAAE,QARS;EASfC,SAAS,EAAE,QATI;EAUfC,QAAQ,EAAE;AAVK,CAAjB;AAYA,IAAIC,OAAO,GAAG;EACZ,IAAI,OADQ;EAEZ,IAAI,KAFQ;EAGZ,IAAI,OAHQ;EAIZ,IAAI,QAJQ;EAKZ,IAAI,MALQ;EAMZ,IAAI,SANQ;EAOZ,IAAI,MAPQ;EAQZ,IAAI;AARQ,CAAd;AAUA,IAAIC,SAAS,GAAG;EACd,KAAK,kBADS;EACW;EACzB,KAAK,aAFS;EAEM;EACpB,KAAK,KAHS;EAGF;EACZ,KAAK,KAJS;EAIF;EACZ,KAAK,cALS;EAKO;EACrB,KAAK,OANS,CAMD;;AANC,CAAhB;AAQA,IAAIC,UAAU,GAAG;EACf,MAAM,MADS;EACD;EACd,MAAM,MAFS;EAED;EACd,MAAM,QAHS,CAGA;;AAHA,CAAjB;AAMC,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,EAAY,EAAZ,EAAgB,EAAhB,EAAoB,EAApB,EAAwB,EAAxB,EAA4BC,OAA5B,CAAoC,UAAUC,CAAV,EAAa;EAChDF,UAAU,CAACE,CAAD,CAAV,GAAgB,SAAhB;AACD,CAFA;AAID;AACA;AACA;AACA;AACA;;AACA,SAASjB,QAAT,CAAmBkB,IAAnB,EAAyB;EACvB;EACA,IAAI,CAACjB,QAAQ,CAACkB,IAAT,CAAcD,IAAd,CAAL,EAA0B;IACxB,OAAOA,IAAP;EACD,CAJsB,CAMvB;;;EACA,IAAIE,SAAS,GAAG,EAAhB,CAPuB,CAQvB;;EACA,IAAIC,GAAG,GAAGH,IAAI,CAACI,OAAL,CAAa,eAAb,EAA8B,UAAUC,KAAV,EAAiBC,GAAjB,EAAsB;IAC5D,IAAIC,EAAE,GAAGX,SAAS,CAACU,GAAD,CAAlB;;IACA,IAAIC,EAAJ,EAAQ;MACN;MACA,IAAI,CAAC,CAAC,CAACL,SAAS,CAACM,OAAV,CAAkBF,GAAlB,CAAP,EAA+B;QAAE;QAC/BJ,SAAS,CAACO,GAAV;QACA,OAAO,SAAP;MACD,CALK,CAMN;;;MACAP,SAAS,CAACQ,IAAV,CAAeJ,GAAf;MACA,OAAOC,EAAE,CAAC,CAAD,CAAF,KAAU,GAAV,GAAgBA,EAAhB,GAAqB,kBAAkBA,EAAlB,GAAuB,KAAnD;IACD;;IAED,IAAII,EAAE,GAAGd,UAAU,CAACS,GAAD,CAAnB;;IACA,IAAIK,EAAJ,EAAQ;MACN;MACAT,SAAS,CAACO,GAAV;MACA,OAAOE,EAAP;IACD;;IACD,OAAO,EAAP;EACD,CApBS,CAAV,CATuB,CA+BvB;;EACA,IAAIC,CAAC,GAAGV,SAAS,CAACW,MAAlB;EACED,CAAC,GAAG,CAAL,KAAYT,GAAG,IAAIW,KAAK,CAACF,CAAC,GAAG,CAAL,CAAL,CAAaG,IAAb,CAAkB,SAAlB,CAAnB;EAED,OAAOZ,GAAP;AACD;AAED;AACA;AACA;AACA;;;AACArB,QAAQ,CAACkC,SAAT,GAAqB,UAAUC,MAAV,EAAkB;EACrC,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;IAC9B,MAAM,IAAIC,KAAJ,CAAU,uCAAV,CAAN;EACD;;EAED,IAAIC,YAAY,GAAG,EAAnB;;EACA,KAAK,IAAIC,GAAT,IAAgBpC,UAAhB,EAA4B;IAC1B,IAAIqC,GAAG,GAAGJ,MAAM,CAACK,cAAP,CAAsBF,GAAtB,IAA6BH,MAAM,CAACG,GAAD,CAAnC,GAA2C,IAArD;;IACA,IAAI,CAACC,GAAL,EAAU;MACRF,YAAY,CAACC,GAAD,CAAZ,GAAoBpC,UAAU,CAACoC,GAAD,CAA9B;MACA;IACD;;IACD,IAAI,YAAYA,GAAhB,EAAqB;MACnB,IAAI,OAAOC,GAAP,KAAe,QAAnB,EAA6B;QAC3BA,GAAG,GAAG,CAACA,GAAD,CAAN;MACD;;MACD,IAAI,CAACP,KAAK,CAACS,OAAN,CAAcF,GAAd,CAAD,IAAuBA,GAAG,CAACR,MAAJ,KAAe,CAAtC,IAA2CQ,GAAG,CAACG,IAAJ,CAAS,UAAUC,CAAV,EAAa;QACnE,OAAO,OAAOA,CAAP,KAAa,QAApB;MACD,CAF8C,CAA/C,EAEI;QACF,MAAM,IAAIP,KAAJ,CAAU,mBAAmBE,GAAnB,GAAyB,oFAAnC,CAAN;MACD;;MACD,IAAIM,WAAW,GAAG1C,UAAU,CAACoC,GAAD,CAA5B;;MACA,IAAI,CAACC,GAAG,CAAC,CAAD,CAAR,EAAa;QACXA,GAAG,CAAC,CAAD,CAAH,GAASK,WAAW,CAAC,CAAD,CAApB;MACD;;MACD,IAAIL,GAAG,CAACR,MAAJ,KAAe,CAAf,IAAoB,CAACQ,GAAG,CAAC,CAAD,CAA5B,EAAiC;QAC/BA,GAAG,GAAG,CAACA,GAAG,CAAC,CAAD,CAAJ,CAAN;QACAA,GAAG,CAACX,IAAJ,CAASgB,WAAW,CAAC,CAAD,CAApB;MACD;;MAEDL,GAAG,GAAGA,GAAG,CAACM,KAAJ,CAAU,CAAV,EAAa,CAAb,CAAN;IACD,CAnBD,MAmBO,IAAI,OAAON,GAAP,KAAe,QAAnB,EAA6B;MAClC,MAAM,IAAIH,KAAJ,CAAU,mBAAmBE,GAAnB,GAAyB,+CAAnC,CAAN;IACD;;IACDD,YAAY,CAACC,GAAD,CAAZ,GAAoBC,GAApB;EACD;;EACDO,QAAQ,CAACT,YAAD,CAAR;AACD,CArCD;AAuCA;AACA;AACA;;;AACArC,QAAQ,CAACG,KAAT,GAAiB,YAAY;EAC3B2C,QAAQ,CAAC5C,UAAD,CAAR;AACD,CAFD;AAIA;AACA;AACA;AACA;;;AACAF,QAAQ,CAAC+C,IAAT,GAAgB,EAAhB;;AAEA,IAAIC,MAAM,CAACC,cAAX,EAA2B;EACzBD,MAAM,CAACC,cAAP,CAAsBjD,QAAQ,CAAC+C,IAA/B,EAAqC,MAArC,EAA6C;IAC3CG,GAAG,EAAE,YAAY;MAAE,OAAOpC,SAAP;IAAkB;EADM,CAA7C;EAGAkC,MAAM,CAACC,cAAP,CAAsBjD,QAAQ,CAAC+C,IAA/B,EAAqC,OAArC,EAA8C;IAC5CG,GAAG,EAAE,YAAY;MAAE,OAAOnC,UAAP;IAAmB;EADM,CAA9C;AAGD,CAPD,MAOO;EACLf,QAAQ,CAAC+C,IAAT,CAAcI,IAAd,GAAqBrC,SAArB;EACAd,QAAQ,CAAC+C,IAAT,CAAcK,KAAd,GAAsBrC,UAAtB;AACD;;AAED,SAAS+B,QAAT,CAAmBX,MAAnB,EAA2B;EACzB;EACArB,SAAS,CAAC,GAAD,CAAT,GAAiB,yCAAyCqB,MAAM,CAAChC,KAAP,CAAa,CAAb,CAAzC,GAA2D,eAA3D,GAA6EgC,MAAM,CAAChC,KAAP,CAAa,CAAb,CAA9F,CAFyB,CAGzB;;EACAW,SAAS,CAAC,GAAD,CAAT,GAAiB,YAAYqB,MAAM,CAAChC,KAAP,CAAa,CAAb,CAAZ,GAA8B,eAA9B,GAAgDgC,MAAM,CAAChC,KAAP,CAAa,CAAb,CAAjE,CAJyB,CAKzB;;EACAW,SAAS,CAAC,IAAD,CAAT,GAAkB,YAAYqB,MAAM,CAACvB,QAArC;;EAEA,KAAK,IAAIyC,IAAT,IAAiBxC,OAAjB,EAA0B;IACxB,IAAIyC,KAAK,GAAGzC,OAAO,CAACwC,IAAD,CAAnB;IACA,IAAIE,QAAQ,GAAGpB,MAAM,CAACmB,KAAD,CAAN,IAAiB,KAAhC;IACAxC,SAAS,CAACuC,IAAD,CAAT,GAAkB,YAAYE,QAA9B;IACAF,IAAI,GAAGG,QAAQ,CAACH,IAAD,CAAf;IACAvC,SAAS,CAAC,CAACuC,IAAI,GAAG,EAAR,EAAYI,QAAZ,EAAD,CAAT,GAAoC,iBAAiBF,QAArD;EACD;AACF;;AAEDvD,QAAQ,CAACG,KAAT"}, "metadata": {}, "sourceType": "script"}