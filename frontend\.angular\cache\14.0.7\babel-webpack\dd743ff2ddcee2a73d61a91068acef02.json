{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction Chip_div_0_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 6);\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.image, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction Chip_div_0_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r6.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-chip-icon\");\n  }\n}\n\nfunction Chip_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chip_div_0_ng_template_3_span_0_Template, 1, 3, \"span\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.icon);\n  }\n}\n\nfunction Chip_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r4.label);\n  }\n}\n\nfunction Chip_div_0_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵlistener(\"click\", function Chip_div_0_span_6_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.close($event));\n    })(\"keydown.enter\", function Chip_div_0_span_6_Template_span_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.close($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r5.removeIcon);\n    i0.ɵɵproperty(\"ngClass\", \"pi-chip-remove-icon\");\n  }\n}\n\nfunction Chip_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Chip_div_0_img_2_Template, 1, 1, \"img\", 2);\n    i0.ɵɵtemplate(3, Chip_div_0_ng_template_3_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, Chip_div_0_div_5_Template, 2, 1, \"div\", 4);\n    i0.ɵɵtemplate(6, Chip_div_0_span_6_Template, 1, 3, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(4);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.containerClass())(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.image)(\"ngIfElse\", _r2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.label);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.removable);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nclass Chip {\n  constructor() {\n    this.removeIcon = \"pi pi-times-circle\";\n    this.onRemove = new EventEmitter();\n    this.visible = true;\n  }\n\n  containerClass() {\n    return {\n      'p-chip p-component': true,\n      'p-chip-image': this.image != null\n    };\n  }\n\n  close(event) {\n    this.visible = false;\n    this.onRemove.emit(event);\n  }\n\n}\n\nChip.ɵfac = function Chip_Factory(t) {\n  return new (t || Chip)();\n};\n\nChip.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Chip,\n  selectors: [[\"p-chip\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    label: \"label\",\n    icon: \"icon\",\n    image: \"image\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    removable: \"removable\",\n    removeIcon: \"removeIcon\"\n  },\n  outputs: {\n    onRemove: \"onRemove\"\n  },\n  ngContentSelectors: _c0,\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [\"iconTemplate\", \"\"], [\"class\", \"p-chip-text\", 4, \"ngIf\"], [\"tabindex\", \"0\", 3, \"class\", \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [3, \"src\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-chip-text\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"keydown.enter\"]],\n  template: function Chip_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, Chip_div_0_Template, 7, 8, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.visible);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n  styles: [\".p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi{line-height:1.5}.pi-chip-remove-icon{line-height:1.5;cursor:pointer}.p-chip img{border-radius:50%}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Chip, [{\n    type: Component,\n    args: [{\n      selector: 'p-chip',\n      template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image;else iconTemplate\">\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\">{{label}}</div>\n            <span *ngIf=\"removable\" tabindex=\"0\" [class]=\"removeIcon\" [ngClass]=\"'pi-chip-remove-icon'\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\"></span>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi{line-height:1.5}.pi-chip-remove-icon{line-height:1.5;cursor:pointer}.p-chip img{border-radius:50%}\\n\"]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    image: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input\n    }],\n    removeIcon: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Output\n    }]\n  });\n})();\n\nclass ChipModule {}\n\nChipModule.ɵfac = function ChipModule_Factory(t) {\n  return new (t || ChipModule)();\n};\n\nChipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ChipModule\n});\nChipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Chip],\n      declarations: [Chip]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Chip, ChipModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "i1", "CommonModule", "Chip", "constructor", "removeIcon", "onRemove", "visible", "containerClass", "image", "close", "event", "emit", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "label", "icon", "style", "styleClass", "removable", "ChipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-chip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass Chip {\n    constructor() {\n        this.removeIcon = \"pi pi-times-circle\";\n        this.onRemove = new EventEmitter();\n        this.visible = true;\n    }\n    containerClass() {\n        return {\n            'p-chip p-component': true,\n            'p-chip-image': this.image != null\n        };\n    }\n    close(event) {\n        this.visible = false;\n        this.onRemove.emit(event);\n    }\n}\nChip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Chip, deps: [], target: i0.ɵɵFactoryTarget.Component });\nChip.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Chip, selector: \"p-chip\", inputs: { label: \"label\", icon: \"icon\", image: \"image\", style: \"style\", styleClass: \"styleClass\", removable: \"removable\", removeIcon: \"removeIcon\" }, outputs: { onRemove: \"onRemove\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image;else iconTemplate\">\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\">{{label}}</div>\n            <span *ngIf=\"removable\" tabindex=\"0\" [class]=\"removeIcon\" [ngClass]=\"'pi-chip-remove-icon'\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\"></span>\n        </div>\n    `, isInline: true, styles: [\".p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi{line-height:1.5}.pi-chip-remove-icon{line-height:1.5;cursor:pointer}.p-chip img{border-radius:50%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Chip, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-chip', template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image;else iconTemplate\">\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\">{{label}}</div>\n            <span *ngIf=\"removable\" tabindex=\"0\" [class]=\"removeIcon\" [ngClass]=\"'pi-chip-remove-icon'\" (click)=\"close($event)\" (keydown.enter)=\"close($event)\"></span>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi{line-height:1.5}.pi-chip-remove-icon{line-height:1.5;cursor:pointer}.p-chip img{border-radius:50%}\\n\"] }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], image: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], removable: [{\n                type: Input\n            }], removeIcon: [{\n                type: Input\n            }], onRemove: [{\n                type: Output\n            }] } });\nclass ChipModule {\n}\nChipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nChipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ChipModule, declarations: [Chip], imports: [CommonModule], exports: [Chip] });\nChipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChipModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Chip],\n                    declarations: [Chip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Chip, ChipModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;IAmBuFT,EAI3E,uB;;;;mBAJ2EA,E;IAAAA,EAItE,iCAJsEA,EAItE,e;;;;;;IAJsEA,EAKhD,wB;;;;mBALgDA,E;IAAAA,EAK7B,wB;IAL6BA,EAKd,qC;;;;;;IALcA,EAKhD,yE;;;;mBALgDA,E;IAAAA,EAKzC,gC;;;;;;IALyCA,EAM3E,4B;IAN2EA,EAMpC,U;IANoCA,EAM3B,e;;;;mBAN2BA,E;IAAAA,EAMpC,a;IANoCA,EAMpC,gC;;;;;;gBANoCA,E;;IAAAA,EAO3E,8B;IAP2EA,EAOiB;MAPjBA,EAOiB;MAAA,eAPjBA,EAOiB;MAAA,OAPjBA,EAO0B,kCAAT;IAAA;MAPjBA,EAOiB;MAAA,eAPjBA,EAOiB;MAAA,OAPjBA,EAO0D,kCAAzC;IAAA,E;IAPjBA,EAOyE,e;;;;mBAPzEA,E;IAAAA,EAOtC,8B;IAPsCA,EAOjB,6C;;;;;;IAPiBA,EAE/E,4B;IAF+EA,EAG3E,gB;IAH2EA,EAI3E,yD;IAJ2EA,EAK3E,gFAL2EA,EAK3E,wB;IAL2EA,EAM3E,yD;IAN2EA,EAO3E,2D;IAP2EA,EAQ/E,e;;;;gBAR+EA,E;;mBAAAA,E;IAAAA,EAE7C,8B;IAF6CA,EAE1E,wE;IAF0EA,EAIvD,a;IAJuDA,EAIvD,kD;IAJuDA,EAMjD,a;IANiDA,EAMjD,iC;IANiDA,EAOpE,a;IAPoEA,EAOpE,qC;;;;;;AAxBnB,MAAMU,IAAN,CAAW;EACPC,WAAW,GAAG;IACV,KAAKC,UAAL,GAAkB,oBAAlB;IACA,KAAKC,QAAL,GAAgB,IAAIZ,YAAJ,EAAhB;IACA,KAAKa,OAAL,GAAe,IAAf;EACH;;EACDC,cAAc,GAAG;IACb,OAAO;MACH,sBAAsB,IADnB;MAEH,gBAAgB,KAAKC,KAAL,IAAc;IAF3B,CAAP;EAIH;;EACDC,KAAK,CAACC,KAAD,EAAQ;IACT,KAAKJ,OAAL,GAAe,KAAf;IACA,KAAKD,QAAL,CAAcM,IAAd,CAAmBD,KAAnB;EACH;;AAfM;;AAiBXR,IAAI,CAACU,IAAL;EAAA,iBAAiGV,IAAjG;AAAA;;AACAA,IAAI,CAACW,IAAL,kBADuFrB,EACvF;EAAA,MAAqFU,IAArF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADuFV,EACvF;MADuFA,EAE/E,mDADR;IAAA;;IAAA;MADuFA,EAEL,gCADlF;IAAA;EAAA;EAAA,eAQ+PQ,EAAE,CAACc,OARlQ,EAQ6Vd,EAAE,CAACe,IARhW,EAQicf,EAAE,CAACgB,OARpc;EAAA;EAAA;EAAA;AAAA;;AASA;EAAA,mDAVuFxB,EAUvF,mBAA2FU,IAA3F,EAA6G,CAAC;IAClGe,IAAI,EAAEvB,SAD4F;IAElGwB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAZ;MAAsBC,QAAQ,EAAG;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KARmB;MAQZC,eAAe,EAAE1B,uBAAuB,CAAC2B,MAR7B;MAQqCC,aAAa,EAAE3B,iBAAiB,CAAC4B,IARtE;MAQ4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CARlF;MAUIC,MAAM,EAAE,CAAC,kLAAD;IAVZ,CAAD;EAF4F,CAAD,CAA7G,QAa4B;IAAEC,KAAK,EAAE,CAAC;MACtBV,IAAI,EAAEpB;IADgB,CAAD,CAAT;IAEZ+B,IAAI,EAAE,CAAC;MACPX,IAAI,EAAEpB;IADC,CAAD,CAFM;IAIZW,KAAK,EAAE,CAAC;MACRS,IAAI,EAAEpB;IADE,CAAD,CAJK;IAMZgC,KAAK,EAAE,CAAC;MACRZ,IAAI,EAAEpB;IADE,CAAD,CANK;IAQZiC,UAAU,EAAE,CAAC;MACbb,IAAI,EAAEpB;IADO,CAAD,CARA;IAUZkC,SAAS,EAAE,CAAC;MACZd,IAAI,EAAEpB;IADM,CAAD,CAVC;IAYZO,UAAU,EAAE,CAAC;MACba,IAAI,EAAEpB;IADO,CAAD,CAZA;IAcZQ,QAAQ,EAAE,CAAC;MACXY,IAAI,EAAEnB;IADK,CAAD;EAdE,CAb5B;AAAA;;AA8BA,MAAMkC,UAAN,CAAiB;;AAEjBA,UAAU,CAACpB,IAAX;EAAA,iBAAuGoB,UAAvG;AAAA;;AACAA,UAAU,CAACC,IAAX,kBA3CuFzC,EA2CvF;EAAA,MAAwGwC;AAAxG;AACAA,UAAU,CAACE,IAAX,kBA5CuF1C,EA4CvF;EAAA,UAA8HS,YAA9H;AAAA;;AACA;EAAA,mDA7CuFT,EA6CvF,mBAA2FwC,UAA3F,EAAmH,CAAC;IACxGf,IAAI,EAAElB,QADkG;IAExGmB,IAAI,EAAE,CAAC;MACCiB,OAAO,EAAE,CAAClC,YAAD,CADV;MAECmC,OAAO,EAAE,CAAClC,IAAD,CAFV;MAGCmC,YAAY,EAAE,CAACnC,IAAD;IAHf,CAAD;EAFkG,CAAD,CAAnH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,IAAT,EAAe8B,UAAf"}, "metadata": {}, "sourceType": "module"}