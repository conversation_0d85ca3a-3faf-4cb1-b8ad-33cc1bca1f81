{"ast": null, "code": "import { observable as Symbol_observable } from '../symbol/observable';\nimport { isFunction } from './isFunction';\nexport function isInteropObservable(input) {\n  return isFunction(input[Symbol_observable]);\n}", "map": {"version": 3, "names": ["observable", "Symbol_observable", "isFunction", "isInteropObservable", "input"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isInteropObservable.js"], "sourcesContent": ["import { observable as Symbol_observable } from '../symbol/observable';\nimport { isFunction } from './isFunction';\nexport function isInteropObservable(input) {\n    return isFunction(input[Symbol_observable]);\n}\n"], "mappings": "AAAA,SAASA,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,mBAAT,CAA6BC,KAA7B,EAAoC;EACvC,OAAOF,UAAU,CAACE,KAAK,CAACH,iBAAD,CAAN,CAAjB;AACH"}, "metadata": {}, "sourceType": "module"}