{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, Input, Output, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i5 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\n\nfunction DropdownItem_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.label || \"empty\");\n  }\n}\n\nfunction DropdownItem_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nconst _c1 = function (a1, a2) {\n  return {\n    \"p-dropdown-item\": true,\n    \"p-highlight\": a1,\n    \"p-disabled\": a2\n  };\n};\n\nconst _c2 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\n\nconst _c3 = [\"container\"];\nconst _c4 = [\"filter\"];\nconst _c5 = [\"in\"];\nconst _c6 = [\"editableInput\"];\nconst _c7 = [\"items\"];\nconst _c8 = [\"scroller\"];\n\nfunction Dropdown_span_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.label || \"empty\");\n  }\n}\n\nfunction Dropdown_span_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c9 = function (a1) {\n  return {\n    \"p-dropdown-label p-inputtext\": true,\n    \"p-dropdown-label-empty\": a1\n  };\n};\n\nfunction Dropdown_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, Dropdown_span_5_ng_container_1_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵtemplate(2, Dropdown_span_5_ng_container_2_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c9, ctx_r2.label == null || ctx_r2.label.length === 0))(\"pTooltip\", ctx_r2.tooltip)(\"tooltipPosition\", ctx_r2.tooltipPosition)(\"positionStyle\", ctx_r2.tooltipPositionStyle)(\"tooltipStyleClass\", ctx_r2.tooltipStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r2.labelId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedItemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.selectedItemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(11, _c2, ctx_r2.selectedOption));\n  }\n}\n\nconst _c10 = function (a1) {\n  return {\n    \"p-dropdown-label p-inputtext p-placeholder\": true,\n    \"p-dropdown-label-empty\": a1\n  };\n};\n\nfunction Dropdown_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c10, ctx_r3.placeholder == null || ctx_r3.placeholder.length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.placeholder || \"empty\");\n  }\n}\n\nfunction Dropdown_input_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 16, 17);\n    i0.ɵɵlistener(\"click\", function Dropdown_input_7_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onEditableInputClick());\n    })(\"input\", function Dropdown_input_7_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onEditableInputChange($event));\n    })(\"focus\", function Dropdown_input_7_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onEditableInputFocus($event));\n    })(\"blur\", function Dropdown_input_7_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onInputBlur($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.disabled);\n    i0.ɵɵattribute(\"maxlength\", ctx_r4.maxlength)(\"placeholder\", ctx_r4.placeholder)(\"aria-expanded\", ctx_r4.overlayVisible);\n  }\n}\n\nfunction Dropdown_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 18);\n    i0.ɵɵlistener(\"click\", function Dropdown_i_8_Template_i_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.clear($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction Dropdown_div_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Dropdown_div_11_div_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c11 = function (a0) {\n  return {\n    options: a0\n  };\n};\n\nfunction Dropdown_div_11_div_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_div_11_div_2_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r24.filterTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c11, ctx_r24.filterOptions));\n  }\n}\n\nfunction Dropdown_div_11_div_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"input\", 28, 29);\n    i0.ɵɵlistener(\"keydown.enter\", function Dropdown_div_11_div_2_ng_template_2_Template_input_keydown_enter_1_listener($event) {\n      return $event.preventDefault();\n    })(\"keydown\", function Dropdown_div_11_div_2_ng_template_2_Template_input_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r30 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r30.onKeydown($event, false));\n    })(\"input\", function Dropdown_div_11_div_2_ng_template_2_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r32 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r32.onFilterInputChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 30);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r26 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", ctx_r26.filterValue || \"\");\n    i0.ɵɵattribute(\"placeholder\", ctx_r26.filterPlaceholder)(\"aria-label\", ctx_r26.ariaFilterLabel)(\"aria-activedescendant\", ctx_r26.overlayVisible ? \"p-highlighted-option\" : ctx_r26.labelId);\n  }\n}\n\nfunction Dropdown_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function Dropdown_div_11_div_2_Template_div_click_0_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵtemplate(1, Dropdown_div_11_div_2_ng_container_1_Template, 2, 4, \"ng-container\", 25);\n    i0.ɵɵtemplate(2, Dropdown_div_11_div_2_ng_template_2_Template, 4, 4, \"ng-template\", null, 26, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r25 = i0.ɵɵreference(3);\n\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r18.filterTemplate)(\"ngIfElse\", _r25);\n  }\n}\n\nfunction Dropdown_div_11_p_scroller_4_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c12 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    options: a1\n  };\n};\n\nfunction Dropdown_div_11_p_scroller_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_div_11_p_scroller_4_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 14);\n  }\n\n  if (rf & 2) {\n    const items_r37 = ctx.$implicit;\n    const scrollerOptions_r38 = ctx.options;\n    i0.ɵɵnextContext(2);\n\n    const _r21 = i0.ɵɵreference(7);\n\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r21)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c12, items_r37, scrollerOptions_r38));\n  }\n}\n\nfunction Dropdown_div_11_p_scroller_4_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Dropdown_div_11_p_scroller_4_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_div_11_p_scroller_4_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 14);\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r41 = ctx.options;\n    const ctx_r40 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r40.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c11, scrollerOptions_r41));\n  }\n}\n\nfunction Dropdown_div_11_p_scroller_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_div_11_p_scroller_4_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction Dropdown_div_11_p_scroller_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-scroller\", 31, 32);\n    i0.ɵɵlistener(\"onLazyLoad\", function Dropdown_div_11_p_scroller_4_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r43.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, Dropdown_div_11_p_scroller_4_ng_template_2_Template, 1, 5, \"ng-template\", 33);\n    i0.ɵɵtemplate(3, Dropdown_div_11_p_scroller_4_ng_container_3_Template, 2, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c0, ctx_r19.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r19.optionsToDisplay)(\"itemSize\", ctx_r19.virtualScrollItemSize || ctx_r19._itemSize)(\"autoSize\", true)(\"lazy\", ctx_r19.lazy)(\"options\", ctx_r19.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r19.loaderTemplate);\n  }\n}\n\nfunction Dropdown_div_11_ng_container_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c13 = function () {\n  return {};\n};\n\nfunction Dropdown_div_11_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_container_5_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n\n    const _r21 = i0.ɵɵreference(7);\n\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r21)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c12, ctx_r20.optionsToDisplay, i0.ɵɵpureFunction0(2, _c13)));\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const optgroup_r56 = i0.ɵɵnextContext().$implicit;\n    const ctx_r57 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r57.getOptionGroupLabel(optgroup_r56) || \"empty\");\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c14 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    selectedOption: a1\n  };\n};\n\nfunction Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 40);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_span_1_Template, 2, 1, \"span\", 13);\n    i0.ɵɵtemplate(2, Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_ng_container_2_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_ng_container_3_Template, 1, 0, \"ng-container\", 14);\n  }\n\n  if (rf & 2) {\n    const optgroup_r56 = ctx.$implicit;\n    const scrollerOptions_r47 = i0.ɵɵnextContext(2).options;\n\n    const _r51 = i0.ɵɵreference(5);\n\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c0, scrollerOptions_r47.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r55.groupTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r55.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c2, optgroup_r56));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r51)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(10, _c14, ctx_r55.getOptionGroupChildren(optgroup_r56), ctx_r55.selectedOption));\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_template_6_ng_container_2_ng_template_1_Template, 4, 13, \"ng-template\", 39);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const items_r46 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", items_r46);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_template_6_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const items_r46 = i0.ɵɵnextContext().$implicit;\n\n    const _r51 = i0.ɵɵreference(5);\n\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r51)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c14, items_r46, ctx_r50.selectedOption));\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_template_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-dropdownItem\", 41);\n    i0.ɵɵlistener(\"onClick\", function Dropdown_div_11_ng_template_6_ng_template_4_ng_template_0_Template_p_dropdownItem_onClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r70 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r70.onItemClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r68 = ctx.$implicit;\n    const selectedOption_r66 = i0.ɵɵnextContext().selectedOption;\n    const ctx_r67 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"option\", option_r68)(\"selected\", selectedOption_r66 == option_r68)(\"label\", ctx_r67.getOptionLabel(option_r68))(\"disabled\", ctx_r67.isOptionDisabled(option_r68))(\"template\", ctx_r67.itemTemplate);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Dropdown_div_11_ng_template_6_ng_template_4_ng_template_0_Template, 1, 5, \"ng-template\", 39);\n  }\n\n  if (rf & 2) {\n    const options_r65 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngForOf\", options_r65);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r73 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r73.emptyFilterMessageLabel, \" \");\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 43);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 42);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_template_6_li_6_ng_container_1_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵtemplate(2, Dropdown_div_11_ng_template_6_li_6_ng_container_2_Template, 2, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r47 = i0.ɵɵnextContext().options;\n    const ctx_r53 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r47.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r53.emptyFilterTemplate && !ctx_r53.emptyTemplate)(\"ngIfElse\", ctx_r53.emptyFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r53.emptyFilterTemplate || ctx_r53.emptyTemplate);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r77 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r77.emptyMessageLabel, \" \");\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_7_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 44);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 42);\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_template_6_li_7_ng_container_1_Template, 2, 1, \"ng-container\", 25);\n    i0.ɵɵtemplate(2, Dropdown_div_11_ng_template_6_li_7_ng_container_2_Template, 2, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r47 = i0.ɵɵnextContext().options;\n    const ctx_r54 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c0, scrollerOptions_r47.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r54.emptyTemplate)(\"ngIfElse\", ctx_r54.empty);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r54.emptyTemplate);\n  }\n}\n\nfunction Dropdown_div_11_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 35, 36);\n    i0.ɵɵtemplate(2, Dropdown_div_11_ng_template_6_ng_container_2_Template, 2, 1, \"ng-container\", 13);\n    i0.ɵɵtemplate(3, Dropdown_div_11_ng_template_6_ng_container_3_Template, 2, 5, \"ng-container\", 13);\n    i0.ɵɵtemplate(4, Dropdown_div_11_ng_template_6_ng_template_4_Template, 1, 1, \"ng-template\", null, 37, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, Dropdown_div_11_ng_template_6_li_6_Template, 3, 6, \"li\", 38);\n    i0.ɵɵtemplate(7, Dropdown_div_11_ng_template_6_li_7_Template, 3, 6, \"li\", 38);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r47 = ctx.options;\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(scrollerOptions_r47.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r47.contentStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r22.listId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.group);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r22.group);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r22.filterValue && ctx_r22.isEmpty());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r22.filterValue && ctx_r22.isEmpty());\n  }\n}\n\nfunction Dropdown_div_11_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c15 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c16 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction Dropdown_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"click\", function Dropdown_div_11_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Dropdown_div_11_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r83 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r83.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Dropdown_div_11_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r84 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r84.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(1, Dropdown_div_11_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵtemplate(2, Dropdown_div_11_div_2_Template, 4, 2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtemplate(4, Dropdown_div_11_p_scroller_4_Template, 4, 10, \"p-scroller\", 22);\n    i0.ɵɵtemplate(5, Dropdown_div_11_ng_container_5_Template, 2, 6, \"ng-container\", 13);\n    i0.ɵɵtemplate(6, Dropdown_div_11_ng_template_6_Template, 8, 8, \"ng-template\", null, 23, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, Dropdown_div_11_ng_container_8_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r6.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-dropdown-panel p-component\")(\"@overlayAnimation\", i0.ɵɵpureFunction1(15, _c16, i0.ɵɵpureFunction2(12, _c15, ctx_r6.showTransitionOptions, ctx_r6.hideTransitionOptions)))(\"ngStyle\", ctx_r6.panelStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.filter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"max-height\", ctx_r6.virtualScroll ? \"auto\" : ctx_r6.scrollHeight || \"auto\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.virtualScroll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.virtualScroll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r6.footerTemplate);\n  }\n}\n\nconst _c17 = function (a1, a2, a3, a4) {\n  return {\n    \"p-dropdown p-component\": true,\n    \"p-disabled\": a1,\n    \"p-dropdown-open\": a2,\n    \"p-focus\": a3,\n    \"p-dropdown-clearable\": a4\n  };\n};\n\nconst DROPDOWN_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Dropdown),\n  multi: true\n};\n\nclass DropdownItem {\n  constructor() {\n    this.onClick = new EventEmitter();\n  }\n\n  onOptionClick(event) {\n    this.onClick.emit({\n      originalEvent: event,\n      option: this.option\n    });\n  }\n\n}\n\nDropdownItem.ɵfac = function DropdownItem_Factory(t) {\n  return new (t || DropdownItem)();\n};\n\nDropdownItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: DropdownItem,\n  selectors: [[\"p-dropdownItem\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    option: \"option\",\n    selected: \"selected\",\n    label: \"label\",\n    disabled: \"disabled\",\n    visible: \"visible\",\n    itemSize: \"itemSize\",\n    template: \"template\"\n  },\n  outputs: {\n    onClick: \"onClick\"\n  },\n  decls: 3,\n  vars: 15,\n  consts: [[\"role\", \"option\", \"pRipple\", \"\", 3, \"ngStyle\", \"id\", \"ngClass\", \"click\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function DropdownItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"li\", 0);\n      i0.ɵɵlistener(\"click\", function DropdownItem_Template_li_click_0_listener($event) {\n        return ctx.onOptionClick($event);\n      });\n      i0.ɵɵtemplate(1, DropdownItem_span_1_Template, 2, 1, \"span\", 1);\n      i0.ɵɵtemplate(2, DropdownItem_ng_container_2_Template, 1, 0, \"ng-container\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(8, _c0, ctx.itemSize + \"px\"))(\"id\", ctx.selected ? \"p-highlighted-option\" : \"\")(\"ngClass\", i0.ɵɵpureFunction2(10, _c1, ctx.selected, ctx.disabled));\n      i0.ɵɵattribute(\"aria-label\", ctx.label)(\"aria-selected\", ctx.selected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.template);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.template)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c2, ctx.option));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DropdownItem, [{\n    type: Component,\n    args: [{\n      selector: 'p-dropdownItem',\n      template: `\n        <li (click)=\"onOptionClick($event)\" role=\"option\" pRipple\n            [attr.aria-label]=\"label\" [attr.aria-selected]=\"selected\"\n            [ngStyle]=\"{'height': itemSize + 'px'}\" [id]=\"selected ? 'p-highlighted-option':''\"\n            [ngClass]=\"{'p-dropdown-item':true, 'p-highlight': selected, 'p-disabled': disabled}\">\n            <span *ngIf=\"!template\">{{label||'empty'}}</span>\n            <ng-container *ngTemplateOutlet=\"template; context: {$implicit: option}\"></ng-container>\n        </li>\n    `,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], null, {\n    option: [{\n      type: Input\n    }],\n    selected: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    template: [{\n      type: Input\n    }],\n    onClick: [{\n      type: Output\n    }]\n  });\n})();\n\nclass Dropdown {\n  constructor(el, renderer, cd, zone, filterService, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n    this.filterService = filterService;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.scrollHeight = '200px';\n    this.resetFilterOnHide = false;\n    this.dropdownIcon = 'pi pi-chevron-down';\n    this.optionGroupChildren = \"items\";\n    this.autoDisplayFirst = true;\n    this.emptyFilterMessage = '';\n    this.emptyMessage = '';\n    this.lazy = false;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.filterMatchMode = \"contains\";\n    this.tooltip = '';\n    this.tooltipPosition = 'right';\n    this.tooltipPositionStyle = 'absolute';\n    this.autofocusFilter = true;\n    this.onChange = new EventEmitter();\n    this.onFilter = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.onClick = new EventEmitter();\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.onClear = new EventEmitter();\n    this.onLazyLoad = new EventEmitter();\n\n    this.onModelChange = () => {};\n\n    this.onModelTouched = () => {};\n\n    this.id = UniqueComponentId();\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(_disabled) {\n    if (_disabled) {\n      this.focused = false;\n      if (this.overlayVisible) this.hide();\n    }\n\n    this._disabled = _disabled;\n\n    if (!this.cd.destroyed) {\n      this.cd.detectChanges();\n    }\n  }\n\n  get itemSize() {\n    return this._itemSize;\n  }\n\n  set itemSize(val) {\n    this._itemSize = val;\n    console.warn(\"The itemSize property is deprecated, use virtualScrollItemSize property instead.\");\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n\n        case 'selectedItem':\n          this.selectedItemTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'filter':\n          this.filterTemplate = item.template;\n          break;\n\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n\n        case 'emptyfilter':\n          this.emptyFilterTemplate = item.template;\n          break;\n\n        case 'empty':\n          this.emptyTemplate = item.template;\n          break;\n\n        case 'group':\n          this.groupTemplate = item.template;\n          break;\n\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  ngOnInit() {\n    this.optionsToDisplay = this.options;\n    this.updateSelectedOption(null);\n    this.labelId = this.id + '_label';\n    this.listId = this.id + '_list';\n\n    if (this.filterBy) {\n      this.filterOptions = {\n        filter: value => this.onFilterInputChange(value),\n        reset: () => this.resetFilter()\n      };\n    }\n  }\n\n  get options() {\n    return this._options;\n  }\n\n  set options(val) {\n    this._options = val;\n    this.optionsToDisplay = this._options;\n    this.updateSelectedOption(this.value);\n    this.selectedOption = this.findOption(this.value, this.optionsToDisplay);\n\n    if (!this.selectedOption && ObjectUtils.isNotEmpty(this.value)) {\n      this.value = null;\n      this.onModelChange(this.value);\n    }\n\n    this.optionsChanged = true;\n\n    if (this._filterValue && this._filterValue.length) {\n      this.activateFilter();\n    }\n  }\n\n  get filterValue() {\n    return this._filterValue;\n  }\n\n  set filterValue(val) {\n    this._filterValue = val;\n    this.activateFilter();\n  }\n\n  ngAfterViewInit() {\n    if (this.editable) {\n      this.updateEditableLabel();\n    }\n  }\n\n  get label() {\n    return this.selectedOption ? this.getOptionLabel(this.selectedOption) : null;\n  }\n\n  get emptyMessageLabel() {\n    return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n  }\n\n  get emptyFilterMessageLabel() {\n    return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n  }\n\n  get filled() {\n    return this.value || this.value != null || this.value != undefined;\n  }\n\n  updateEditableLabel() {\n    if (this.editableInputViewChild && this.editableInputViewChild.nativeElement) {\n      this.editableInputViewChild.nativeElement.value = this.selectedOption ? this.getOptionLabel(this.selectedOption) : this.value || '';\n    }\n  }\n\n  getOptionLabel(option) {\n    return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : option && option.label !== undefined ? option.label : option;\n  }\n\n  getOptionValue(option) {\n    return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : !this.optionLabel && option && option.value !== undefined ? option.value : option;\n  }\n\n  isOptionDisabled(option) {\n    return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : option && option.disabled !== undefined ? option.disabled : false;\n  }\n\n  getOptionGroupLabel(optionGroup) {\n    return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup;\n  }\n\n  getOptionGroupChildren(optionGroup) {\n    return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n  }\n\n  onItemClick(event) {\n    const option = event.option;\n\n    if (!this.isOptionDisabled(option)) {\n      this.selectItem(event.originalEvent, option);\n      this.accessibleViewChild.nativeElement.focus({\n        preventScroll: true\n      });\n    }\n\n    setTimeout(() => {\n      this.hide();\n    }, 150);\n  }\n\n  selectItem(event, option) {\n    if (this.selectedOption != option) {\n      this.selectedOption = option;\n      this.value = this.getOptionValue(option);\n      this.onModelChange(this.value);\n      this.updateEditableLabel();\n      this.onChange.emit({\n        originalEvent: event,\n        value: this.value\n      });\n    }\n  }\n\n  ngAfterViewChecked() {\n    if (this.optionsChanged && this.overlayVisible) {\n      this.optionsChanged = false;\n      this.zone.runOutsideAngular(() => {\n        setTimeout(() => {\n          this.alignOverlay();\n        }, 1);\n      });\n    }\n\n    if (this.selectedOptionUpdated && this.itemsWrapper) {\n      let selectedItem = DomHandler.findSingle(this.overlay, 'li.p-highlight');\n\n      if (selectedItem) {\n        DomHandler.scrollInView(this.itemsWrapper, DomHandler.findSingle(this.overlay, 'li.p-highlight'));\n      }\n\n      this.selectedOptionUpdated = false;\n    }\n  }\n\n  writeValue(value) {\n    if (this.filter) {\n      this.resetFilter();\n    }\n\n    this.value = value;\n    this.updateSelectedOption(value);\n    this.updateEditableLabel();\n    this.cd.markForCheck();\n  }\n\n  resetFilter() {\n    this._filterValue = null;\n\n    if (this.filterViewChild && this.filterViewChild.nativeElement) {\n      this.filterViewChild.nativeElement.value = '';\n    }\n\n    this.optionsToDisplay = this.options;\n  }\n\n  updateSelectedOption(val) {\n    this.selectedOption = this.findOption(val, this.optionsToDisplay);\n\n    if (this.autoDisplayFirst && !this.placeholder && !this.selectedOption && this.optionsToDisplay && this.optionsToDisplay.length && !this.editable) {\n      if (this.group) {\n        this.selectedOption = this.optionsToDisplay[0].items[0];\n      } else {\n        this.selectedOption = this.optionsToDisplay[0];\n      }\n\n      this.value = this.getOptionValue(this.selectedOption);\n      this.onModelChange(this.value);\n    }\n\n    this.selectedOptionUpdated = true;\n  }\n\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n\n  onMouseclick(event) {\n    if (this.disabled || this.readonly || this.isInputClick(event)) {\n      return;\n    }\n\n    this.onClick.emit(event);\n    this.accessibleViewChild.nativeElement.focus({\n      preventScroll: true\n    });\n    if (this.overlayVisible) this.hide();else this.show();\n    this.cd.detectChanges();\n  }\n\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n  }\n\n  isInputClick(event) {\n    return DomHandler.hasClass(event.target, 'p-dropdown-clear-icon') || event.target.isSameNode(this.accessibleViewChild.nativeElement) || this.editableInputViewChild && event.target.isSameNode(this.editableInputViewChild.nativeElement);\n  }\n\n  isOutsideClicked(event) {\n    return !(this.el.nativeElement.isSameNode(event.target) || this.el.nativeElement.contains(event.target) || this.overlay && this.overlay.contains(event.target));\n  }\n\n  isEmpty() {\n    return !this.optionsToDisplay || this.optionsToDisplay && this.optionsToDisplay.length === 0;\n  }\n\n  onEditableInputClick() {\n    this.bindDocumentClickListener();\n  }\n\n  onEditableInputFocus(event) {\n    this.focused = true;\n    this.hide();\n    this.onFocus.emit(event);\n  }\n\n  onEditableInputChange(event) {\n    this.value = event.target.value;\n    this.updateSelectedOption(this.value);\n    this.onModelChange(this.value);\n    this.onChange.emit({\n      originalEvent: event,\n      value: this.value\n    });\n  }\n\n  show() {\n    this.overlayVisible = true;\n    this.preventDocumentDefault = true;\n    this.cd.markForCheck();\n  }\n\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.overlay = event.element;\n        this.itemsWrapper = DomHandler.findSingle(this.overlay, this.virtualScroll ? '.p-scroller' : '.p-dropdown-items-wrapper');\n        this.virtualScroll && this.scroller.setContentEl(this.itemsViewChild.nativeElement);\n        this.appendOverlay();\n\n        if (this.autoZIndex) {\n          ZIndexUtils.set('overlay', this.overlay, this.baseZIndex + this.config.zIndex.overlay);\n        }\n\n        this.alignOverlay();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n\n        if (this.options && this.options.length) {\n          if (this.virtualScroll) {\n            const selectedIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n\n            if (selectedIndex !== -1) {\n              this.scroller.scrollToIndex(selectedIndex);\n            }\n          } else {\n            let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-dropdown-item.p-highlight');\n\n            if (selectedListItem) {\n              selectedListItem.scrollIntoView({\n                block: 'nearest',\n                inline: 'center'\n              });\n            }\n          }\n        }\n\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n          this.preventModelTouched = true;\n\n          if (this.autofocusFilter) {\n            this.filterViewChild.nativeElement.focus();\n          }\n        }\n\n        this.onShow.emit(event);\n        break;\n\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit(event);\n        break;\n    }\n  }\n\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        ZIndexUtils.clear(event.element);\n        break;\n    }\n  }\n\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.overlay);else DomHandler.appendChild(this.overlay, this.appendTo);\n\n      if (!this.overlay.style.minWidth) {\n        this.overlay.style.minWidth = DomHandler.getWidth(this.containerViewChild.nativeElement) + 'px';\n      }\n    }\n  }\n\n  restoreOverlayAppend() {\n    if (this.overlay && this.appendTo) {\n      this.el.nativeElement.appendChild(this.overlay);\n    }\n  }\n\n  hide() {\n    this.overlayVisible = false;\n\n    if (this.filter && this.resetFilterOnHide) {\n      this.resetFilter();\n    }\n\n    this.cd.markForCheck();\n  }\n\n  alignOverlay() {\n    if (this.overlay) {\n      if (this.appendTo) DomHandler.absolutePosition(this.overlay, this.containerViewChild.nativeElement);else DomHandler.relativePosition(this.overlay, this.containerViewChild.nativeElement);\n    }\n  }\n\n  onInputFocus(event) {\n    this.focused = true;\n    this.onFocus.emit(event);\n  }\n\n  onInputBlur(event) {\n    this.focused = false;\n    this.onBlur.emit(event);\n\n    if (!this.preventModelTouched) {\n      this.onModelTouched();\n    }\n\n    this.preventModelTouched = false;\n  }\n\n  findPrevEnabledOption(index) {\n    let prevEnabledOption;\n\n    if (this.optionsToDisplay && this.optionsToDisplay.length) {\n      for (let i = index - 1; 0 <= i; i--) {\n        let option = this.optionsToDisplay[i];\n\n        if (this.isOptionDisabled(option)) {\n          continue;\n        } else {\n          prevEnabledOption = option;\n          break;\n        }\n      }\n\n      if (!prevEnabledOption) {\n        for (let i = this.optionsToDisplay.length - 1; i >= index; i--) {\n          let option = this.optionsToDisplay[i];\n\n          if (this.isOptionDisabled(option)) {\n            continue;\n          } else {\n            prevEnabledOption = option;\n            break;\n          }\n        }\n      }\n    }\n\n    return prevEnabledOption;\n  }\n\n  findNextEnabledOption(index) {\n    let nextEnabledOption;\n\n    if (this.optionsToDisplay && this.optionsToDisplay.length) {\n      for (let i = index + 1; i < this.optionsToDisplay.length; i++) {\n        let option = this.optionsToDisplay[i];\n\n        if (this.isOptionDisabled(option)) {\n          continue;\n        } else {\n          nextEnabledOption = option;\n          break;\n        }\n      }\n\n      if (!nextEnabledOption) {\n        for (let i = 0; i < index; i++) {\n          let option = this.optionsToDisplay[i];\n\n          if (this.isOptionDisabled(option)) {\n            continue;\n          } else {\n            nextEnabledOption = option;\n            break;\n          }\n        }\n      }\n    }\n\n    return nextEnabledOption;\n  }\n\n  onKeydown(event, search) {\n    if (this.readonly || !this.optionsToDisplay || this.optionsToDisplay.length === null) {\n      return;\n    }\n\n    switch (event.which) {\n      //down\n      case 40:\n        if (!this.overlayVisible && event.altKey) {\n          this.show();\n        } else {\n          if (this.group) {\n            let selectedItemIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n\n            if (selectedItemIndex !== -1) {\n              let nextItemIndex = selectedItemIndex.itemIndex + 1;\n\n              if (nextItemIndex < this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex]).length) {\n                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex])[nextItemIndex]);\n                this.selectedOptionUpdated = true;\n              } else if (this.optionsToDisplay[selectedItemIndex.groupIndex + 1]) {\n                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex + 1])[0]);\n                this.selectedOptionUpdated = true;\n              }\n            } else {\n              if (this.optionsToDisplay && this.optionsToDisplay.length > 0) {\n                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[0])[0]);\n              }\n            }\n          } else {\n            let selectedItemIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n            let nextEnabledOption = this.findNextEnabledOption(selectedItemIndex);\n\n            if (nextEnabledOption) {\n              this.selectItem(event, nextEnabledOption);\n              this.selectedOptionUpdated = true;\n            }\n          }\n        }\n\n        event.preventDefault();\n        break;\n      //up\n\n      case 38:\n        if (this.group) {\n          let selectedItemIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n\n          if (selectedItemIndex !== -1) {\n            let prevItemIndex = selectedItemIndex.itemIndex - 1;\n\n            if (prevItemIndex >= 0) {\n              this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex])[prevItemIndex]);\n              this.selectedOptionUpdated = true;\n            } else if (prevItemIndex < 0) {\n              let prevGroup = this.optionsToDisplay[selectedItemIndex.groupIndex - 1];\n\n              if (prevGroup) {\n                this.selectItem(event, this.getOptionGroupChildren(prevGroup)[this.getOptionGroupChildren(prevGroup).length - 1]);\n                this.selectedOptionUpdated = true;\n              }\n            }\n          }\n        } else {\n          let selectedItemIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n          let prevEnabledOption = this.findPrevEnabledOption(selectedItemIndex);\n\n          if (prevEnabledOption) {\n            this.selectItem(event, prevEnabledOption);\n            this.selectedOptionUpdated = true;\n          }\n        }\n\n        event.preventDefault();\n        break;\n      //space\n\n      case 32:\n        if (search) {\n          if (!this.overlayVisible) {\n            this.show();\n          } else {\n            this.hide();\n          }\n\n          event.preventDefault();\n        }\n\n        break;\n      //enter\n\n      case 13:\n        if (this.overlayVisible && (!this.filter || this.optionsToDisplay && this.optionsToDisplay.length > 0)) {\n          this.hide();\n        } else if (!this.overlayVisible) {\n          this.show();\n        }\n\n        event.preventDefault();\n        break;\n      //escape and tab\n\n      case 27:\n      case 9:\n        this.hide();\n        break;\n      //search item based on keyboard input\n\n      default:\n        if (search && !event.metaKey) {\n          this.search(event);\n        }\n\n        break;\n    }\n  }\n\n  search(event) {\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n\n    const char = event.key;\n    this.previousSearchChar = this.currentSearchChar;\n    this.currentSearchChar = char;\n    if (this.previousSearchChar === this.currentSearchChar) this.searchValue = this.currentSearchChar;else this.searchValue = this.searchValue ? this.searchValue + char : char;\n    let newOption;\n\n    if (this.group) {\n      let searchIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : {\n        groupIndex: 0,\n        itemIndex: 0\n      };\n      newOption = this.searchOptionWithinGroup(searchIndex);\n    } else {\n      let searchIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n      newOption = this.searchOption(++searchIndex);\n    }\n\n    if (newOption && !this.isOptionDisabled(newOption)) {\n      this.selectItem(event, newOption);\n      this.selectedOptionUpdated = true;\n    }\n\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = null;\n    }, 250);\n  }\n\n  searchOption(index) {\n    let option;\n\n    if (this.searchValue) {\n      option = this.searchOptionInRange(index, this.optionsToDisplay.length);\n\n      if (!option) {\n        option = this.searchOptionInRange(0, index);\n      }\n    }\n\n    return option;\n  }\n\n  searchOptionInRange(start, end) {\n    for (let i = start; i < end; i++) {\n      let opt = this.optionsToDisplay[i];\n\n      if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n        return opt;\n      }\n    }\n\n    return null;\n  }\n\n  searchOptionWithinGroup(index) {\n    let option;\n\n    if (this.searchValue) {\n      for (let i = index.groupIndex; i < this.optionsToDisplay.length; i++) {\n        for (let j = index.groupIndex === i ? index.itemIndex + 1 : 0; j < this.getOptionGroupChildren(this.optionsToDisplay[i]).length; j++) {\n          let opt = this.getOptionGroupChildren(this.optionsToDisplay[i])[j];\n\n          if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n            return opt;\n          }\n        }\n      }\n\n      if (!option) {\n        for (let i = 0; i <= index.groupIndex; i++) {\n          for (let j = 0; j < (index.groupIndex === i ? index.itemIndex : this.getOptionGroupChildren(this.optionsToDisplay[i]).length); j++) {\n            let opt = this.getOptionGroupChildren(this.optionsToDisplay[i])[j];\n\n            if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n              return opt;\n            }\n          }\n        }\n      }\n    }\n\n    return null;\n  }\n\n  findOptionIndex(val, opts) {\n    let index = -1;\n\n    if (opts) {\n      for (let i = 0; i < opts.length; i++) {\n        if (val == null && this.getOptionValue(opts[i]) == null || ObjectUtils.equals(val, this.getOptionValue(opts[i]), this.dataKey)) {\n          index = i;\n          break;\n        }\n      }\n    }\n\n    return index;\n  }\n\n  findOptionGroupIndex(val, opts) {\n    let groupIndex, itemIndex;\n\n    if (opts) {\n      for (let i = 0; i < opts.length; i++) {\n        groupIndex = i;\n        itemIndex = this.findOptionIndex(val, this.getOptionGroupChildren(opts[i]));\n\n        if (itemIndex !== -1) {\n          break;\n        }\n      }\n    }\n\n    if (itemIndex !== -1) {\n      return {\n        groupIndex: groupIndex,\n        itemIndex: itemIndex\n      };\n    } else {\n      return -1;\n    }\n  }\n\n  findOption(val, opts, inGroup) {\n    if (this.group && !inGroup) {\n      let opt;\n\n      if (opts && opts.length) {\n        for (let optgroup of opts) {\n          opt = this.findOption(val, this.getOptionGroupChildren(optgroup), true);\n\n          if (opt) {\n            break;\n          }\n        }\n      }\n\n      return opt;\n    } else {\n      let index = this.findOptionIndex(val, opts);\n      return index != -1 ? opts[index] : null;\n    }\n  }\n\n  onFilterInputChange(event) {\n    let inputValue = event.target.value;\n\n    if (inputValue && inputValue.length) {\n      this._filterValue = inputValue;\n      this.activateFilter();\n    } else {\n      this._filterValue = null;\n      this.optionsToDisplay = this.options;\n    }\n\n    this.virtualScroll && this.scroller.scrollToIndex(0);\n    this.optionsChanged = true;\n    this.onFilter.emit({\n      originalEvent: event,\n      filter: this._filterValue\n    });\n  }\n\n  activateFilter() {\n    let searchFields = (this.filterBy || this.optionLabel || 'label').split(',');\n\n    if (this.options && this.options.length) {\n      if (this.group) {\n        let filteredGroups = [];\n\n        for (let optgroup of this.options) {\n          let filteredSubOptions = this.filterService.filter(this.getOptionGroupChildren(optgroup), searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n\n          if (filteredSubOptions && filteredSubOptions.length) {\n            filteredGroups.push(Object.assign(Object.assign({}, optgroup), {\n              [this.optionGroupChildren]: filteredSubOptions\n            }));\n          }\n        }\n\n        this.optionsToDisplay = filteredGroups;\n      } else {\n        this.optionsToDisplay = this.filterService.filter(this.options, searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n      }\n\n      this.optionsChanged = true;\n    }\n  }\n\n  applyFocus() {\n    if (this.editable) DomHandler.findSingle(this.el.nativeElement, '.p-dropdown-label.p-inputtext').focus();else DomHandler.findSingle(this.el.nativeElement, 'input[readonly]').focus();\n  }\n\n  focus() {\n    this.applyFocus();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n        if (!this.preventDocumentDefault && this.isOutsideClicked(event)) {\n          this.hide();\n          this.unbindDocumentClickListener();\n        }\n\n        this.preventDocumentDefault = false;\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  onWindowResize() {\n    if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild.nativeElement, event => {\n        if (this.overlayVisible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  clear(event) {\n    this.value = null;\n    this.onModelChange(this.value);\n    this.onChange.emit({\n      originalEvent: event,\n      value: this.value\n    });\n    this.updateSelectedOption(this.value);\n    this.updateEditableLabel();\n    this.onClear.emit(event);\n  }\n\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.overlay = null;\n    this.itemsWrapper = null;\n    this.onModelTouched();\n  }\n\n  ngOnDestroy() {\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n\n    if (this.overlay) {\n      ZIndexUtils.clear(this.overlay);\n    }\n\n    this.restoreOverlayAppend();\n    this.onOverlayHide();\n  }\n\n}\n\nDropdown.ɵfac = function Dropdown_Factory(t) {\n  return new (t || Dropdown)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i3.FilterService), i0.ɵɵdirectiveInject(i3.PrimeNGConfig), i0.ɵɵdirectiveInject(i3.OverlayService));\n};\n\nDropdown.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Dropdown,\n  selectors: [[\"p-dropdown\"]],\n  contentQueries: function Dropdown_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Dropdown_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c3, 5);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n      i0.ɵɵviewQuery(_c6, 5);\n      i0.ɵɵviewQuery(_c7, 5);\n      i0.ɵɵviewQuery(_c8, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.accessibleViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editableInputViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n  hostVars: 4,\n  hostBindings: function Dropdown_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focused || ctx.overlayVisible);\n    }\n  },\n  inputs: {\n    scrollHeight: \"scrollHeight\",\n    filter: \"filter\",\n    name: \"name\",\n    style: \"style\",\n    panelStyle: \"panelStyle\",\n    styleClass: \"styleClass\",\n    panelStyleClass: \"panelStyleClass\",\n    readonly: \"readonly\",\n    required: \"required\",\n    editable: \"editable\",\n    appendTo: \"appendTo\",\n    tabindex: \"tabindex\",\n    placeholder: \"placeholder\",\n    filterPlaceholder: \"filterPlaceholder\",\n    filterLocale: \"filterLocale\",\n    inputId: \"inputId\",\n    selectId: \"selectId\",\n    dataKey: \"dataKey\",\n    filterBy: \"filterBy\",\n    autofocus: \"autofocus\",\n    resetFilterOnHide: \"resetFilterOnHide\",\n    dropdownIcon: \"dropdownIcon\",\n    optionLabel: \"optionLabel\",\n    optionValue: \"optionValue\",\n    optionDisabled: \"optionDisabled\",\n    optionGroupLabel: \"optionGroupLabel\",\n    optionGroupChildren: \"optionGroupChildren\",\n    autoDisplayFirst: \"autoDisplayFirst\",\n    group: \"group\",\n    showClear: \"showClear\",\n    emptyFilterMessage: \"emptyFilterMessage\",\n    emptyMessage: \"emptyMessage\",\n    lazy: \"lazy\",\n    virtualScroll: \"virtualScroll\",\n    virtualScrollItemSize: \"virtualScrollItemSize\",\n    virtualScrollOptions: \"virtualScrollOptions\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\",\n    ariaFilterLabel: \"ariaFilterLabel\",\n    ariaLabel: \"ariaLabel\",\n    ariaLabelledBy: \"ariaLabelledBy\",\n    filterMatchMode: \"filterMatchMode\",\n    maxlength: \"maxlength\",\n    tooltip: \"tooltip\",\n    tooltipPosition: \"tooltipPosition\",\n    tooltipPositionStyle: \"tooltipPositionStyle\",\n    tooltipStyleClass: \"tooltipStyleClass\",\n    autofocusFilter: \"autofocusFilter\",\n    disabled: \"disabled\",\n    itemSize: \"itemSize\",\n    options: \"options\",\n    filterValue: \"filterValue\"\n  },\n  outputs: {\n    onChange: \"onChange\",\n    onFilter: \"onFilter\",\n    onFocus: \"onFocus\",\n    onBlur: \"onBlur\",\n    onClick: \"onClick\",\n    onShow: \"onShow\",\n    onHide: \"onHide\",\n    onClear: \"onClear\",\n    onLazyLoad: \"onLazyLoad\"\n  },\n  features: [i0.ɵɵProvidersFeature([DROPDOWN_VALUE_ACCESSOR])],\n  decls: 12,\n  vars: 25,\n  consts: [[3, \"ngClass\", \"ngStyle\", \"click\"], [\"container\", \"\"], [1, \"p-hidden-accessible\"], [\"type\", \"text\", \"readonly\", \"\", \"aria-haspopup\", \"listbox\", \"aria-haspopup\", \"listbox\", \"role\", \"combobox\", 3, \"disabled\", \"focus\", \"blur\", \"keydown\"], [\"in\", \"\"], [3, \"ngClass\", \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [\"type\", \"text\", \"class\", \"p-dropdown-label p-inputtext\", \"aria-haspopup\", \"listbox\", 3, \"disabled\", \"click\", \"input\", \"focus\", \"blur\", 4, \"ngIf\"], [\"class\", \"p-dropdown-clear-icon pi pi-times\", 3, \"click\", 4, \"ngIf\"], [\"role\", \"button\", \"aria-label\", \"dropdown trigger\", \"aria-haspopup\", \"listbox\", 1, \"p-dropdown-trigger\"], [1, \"p-dropdown-trigger-icon\", 3, \"ngClass\"], [3, \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"pTooltip\", \"tooltipPosition\", \"positionStyle\", \"tooltipStyleClass\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\"], [\"type\", \"text\", \"aria-haspopup\", \"listbox\", 1, \"p-dropdown-label\", \"p-inputtext\", 3, \"disabled\", \"click\", \"input\", \"focus\", \"blur\"], [\"editableInput\", \"\"], [1, \"p-dropdown-clear-icon\", \"pi\", \"pi-times\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-dropdown-header\", 3, \"click\", 4, \"ngIf\"], [1, \"p-dropdown-items-wrapper\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [\"buildInItems\", \"\"], [1, \"p-dropdown-header\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"builtInFilterElement\", \"\"], [1, \"p-dropdown-filter-container\"], [\"type\", \"text\", \"autocomplete\", \"off\", 1, \"p-dropdown-filter\", \"p-inputtext\", \"p-component\", 3, \"value\", \"keydown.enter\", \"keydown\", \"input\"], [\"filter\", \"\"], [1, \"p-dropdown-filter-icon\", \"pi\", \"pi-search\"], [3, \"items\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", 1, \"p-dropdown-items\", 3, \"ngClass\"], [\"items\", \"\"], [\"itemslist\", \"\"], [\"class\", \"p-dropdown-empty-message\", 3, \"ngStyle\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"p-dropdown-item-group\", 3, \"ngStyle\"], [3, \"option\", \"selected\", \"label\", \"disabled\", \"template\", \"onClick\"], [1, \"p-dropdown-empty-message\", 3, \"ngStyle\"], [\"emptyFilter\", \"\"], [\"empty\", \"\"]],\n  template: function Dropdown_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵlistener(\"click\", function Dropdown_Template_div_click_0_listener($event) {\n        return ctx.onMouseclick($event);\n      });\n      i0.ɵɵelementStart(2, \"div\", 2)(3, \"input\", 3, 4);\n      i0.ɵɵlistener(\"focus\", function Dropdown_Template_input_focus_3_listener($event) {\n        return ctx.onInputFocus($event);\n      })(\"blur\", function Dropdown_Template_input_blur_3_listener($event) {\n        return ctx.onInputBlur($event);\n      })(\"keydown\", function Dropdown_Template_input_keydown_3_listener($event) {\n        return ctx.onKeydown($event, true);\n      });\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(5, Dropdown_span_5_Template, 3, 13, \"span\", 5);\n      i0.ɵɵtemplate(6, Dropdown_span_6_Template, 2, 4, \"span\", 6);\n      i0.ɵɵtemplate(7, Dropdown_input_7_Template, 2, 4, \"input\", 7);\n      i0.ɵɵtemplate(8, Dropdown_i_8_Template, 1, 0, \"i\", 8);\n      i0.ɵɵelementStart(9, \"div\", 9);\n      i0.ɵɵelement(10, \"span\", 10);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(11, Dropdown_div_11_Template, 9, 17, \"div\", 11);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(20, _c17, ctx.disabled, ctx.overlayVisible, ctx.focused, ctx.showClear && !ctx.disabled))(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"disabled\", ctx.disabled);\n      i0.ɵɵattribute(\"id\", ctx.inputId)(\"placeholder\", ctx.placeholder)(\"aria-label\", ctx.ariaLabel)(\"aria-expanded\", false)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"tabindex\", ctx.tabindex)(\"autofocus\", ctx.autofocus)(\"aria-activedescendant\", ctx.overlayVisible ? ctx.labelId : null);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.editable && ctx.label != null);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.editable && ctx.label == null);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.editable);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.value != null && ctx.showClear && !ctx.disabled);\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"aria-expanded\", ctx.overlayVisible);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", ctx.dropdownIcon);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.overlayVisible);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i3.PrimeTemplate, i4.Tooltip, i5.Scroller, DropdownItem],\n  styles: [\".p-dropdown{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-dropdown-clear-icon{position:absolute;top:50%;margin-top:-.5rem}.p-dropdown-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-dropdown-label{display:block;white-space:nowrap;overflow:hidden;flex:1 1 auto;width:1%;text-overflow:ellipsis;cursor:pointer}.p-dropdown-label-empty{overflow:hidden;visibility:hidden}input.p-dropdown-label{cursor:default}.p-dropdown .p-dropdown-panel{min-width:100%}.p-dropdown-panel{position:absolute;top:0;left:0}.p-dropdown-items-wrapper{overflow:auto}.p-dropdown-item{cursor:pointer;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-dropdown-items{margin:0;padding:0;list-style-type:none}.p-dropdown-filter{width:100%}.p-dropdown-filter-container{position:relative}.p-dropdown-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-fluid .p-dropdown{display:flex}.p-fluid .p-dropdown .p-dropdown-label{width:1%}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('overlayAnimation', [transition(':enter', [style({\n      opacity: 0,\n      transform: 'scaleY(0.8)'\n    }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dropdown, [{\n    type: Component,\n    args: [{\n      selector: 'p-dropdown',\n      template: `\n         <div #container [ngClass]=\"{'p-dropdown p-component':true,\n            'p-disabled':disabled, 'p-dropdown-open':overlayVisible, 'p-focus':focused, 'p-dropdown-clearable': showClear && !disabled}\"\n            (click)=\"onMouseclick($event)\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #in [attr.id]=\"inputId\" type=\"text\" readonly (focus)=\"onInputFocus($event)\" aria-haspopup=\"listbox\" [attr.placeholder]=\"placeholder\"\n                    aria-haspopup=\"listbox\" [attr.aria-label]=\"ariaLabel\" [attr.aria-expanded]=\"false\" [attr.aria-labelledby]=\"ariaLabelledBy\" (blur)=\"onInputBlur($event)\" (keydown)=\"onKeydown($event, true)\"\n                    [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.autofocus]=\"autofocus\" [attr.aria-activedescendant]=\"overlayVisible ? labelId : null\" role=\"combobox\">\n            </div>\n            <span [attr.id]=\"labelId\" [ngClass]=\"{'p-dropdown-label p-inputtext':true,'p-dropdown-label-empty':(label == null || label.length === 0)}\" *ngIf=\"!editable && (label != null)\" [pTooltip]=\"tooltip\" [tooltipPosition]=\"tooltipPosition\" [positionStyle]=\"tooltipPositionStyle\" [tooltipStyleClass]=\"tooltipStyleClass\">\n                <ng-container *ngIf=\"!selectedItemTemplate\">{{label||'empty'}}</ng-container>\n                <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: selectedOption}\"></ng-container>\n            </span>\n            <span [ngClass]=\"{'p-dropdown-label p-inputtext p-placeholder':true,'p-dropdown-label-empty': (placeholder == null || placeholder.length === 0)}\" *ngIf=\"!editable && (label == null)\">{{placeholder||'empty'}}</span>\n            <input #editableInput type=\"text\" [attr.maxlength]=\"maxlength\" class=\"p-dropdown-label p-inputtext\" *ngIf=\"editable\" [disabled]=\"disabled\" [attr.placeholder]=\"placeholder\"\n                aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\" (click)=\"onEditableInputClick()\" (input)=\"onEditableInputChange($event)\" (focus)=\"onEditableInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i class=\"p-dropdown-clear-icon pi pi-times\" (click)=\"clear($event)\" *ngIf=\"value != null && showClear && !disabled\"></i>\n            <div class=\"p-dropdown-trigger\" role=\"button\" aria-label=\"dropdown trigger\" aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\">\n                <span class=\"p-dropdown-trigger-icon\" [ngClass]=\"dropdownIcon\"></span>\n            </div>\n            <div *ngIf=\"overlayVisible\" [ngClass]=\"'p-dropdown-panel p-component'\" (click)=\"onOverlayClick($event)\" [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <div class=\"p-dropdown-header\" *ngIf=\"filter\" (click)=\"$event.stopPropagation()\">\n                    <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                        <ng-container *ngTemplateOutlet=\"filterTemplate; context: {options: filterOptions}\"></ng-container>\n                    </ng-container>\n                    <ng-template #builtInFilterElement>\n                        <div class=\"p-dropdown-filter-container\">\n                            <input #filter type=\"text\" autocomplete=\"off\" [value]=\"filterValue||''\" class=\"p-dropdown-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                            (keydown.enter)=\"$event.preventDefault()\" (keydown)=\"onKeydown($event, false)\" (input)=\"onFilterInputChange($event)\" [attr.aria-label]=\"ariaFilterLabel\" [attr.aria-activedescendant]=\"overlayVisible ? 'p-highlighted-option' : labelId\">\n                            <span class=\"p-dropdown-filter-icon pi pi-search\"></span>\n                        </div>\n                    </ng-template>\n                </div>\n                <div class=\"p-dropdown-items-wrapper\" [style.max-height]=\"virtualScroll ? 'auto' : (scrollHeight||'auto')\">\n                    <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"optionsToDisplay\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                        [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                        <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                        <ng-container *ngIf=\"loaderTemplate\">\n                            <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                    </p-scroller>\n                    <ng-container *ngIf=\"!virtualScroll\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: optionsToDisplay, options: {}}\"></ng-container>\n                    </ng-container>\n\n                    <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                        <ul #items [attr.id]=\"listId\" class=\"p-dropdown-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"listbox\">\n                            <ng-container *ngIf=\"group\">\n                                <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                    <li class=\"p-dropdown-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                        <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                        <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                    </li>\n                                    <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup), selectedOption: selectedOption}\"></ng-container>\n                                </ng-template>\n                            </ng-container>\n                            <ng-container *ngIf=\"!group\">\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items, selectedOption: selectedOption}\"></ng-container>\n                            </ng-container>\n                            <ng-template #itemslist let-options let-selectedOption=\"selectedOption\">\n                                <ng-template ngFor let-option let-i=\"index\" [ngForOf]=\"options\">\n                                    <p-dropdownItem [option]=\"option\" [selected]=\"selectedOption == option\" [label]=\"getOptionLabel(option)\" [disabled]=\"isOptionDisabled(option)\"\n                                                    (onClick)=\"onItemClick($event)\"\n                                                    [template]=\"itemTemplate\"></p-dropdownItem>\n                                </ng-template>\n                            </ng-template>\n                            <li *ngIf=\"filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                    {{emptyFilterMessageLabel}}\n                                </ng-container>\n                                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                            </li>\n                            <li *ngIf=\"!filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ul>\n                    </ng-template>\n                </div>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      host: {\n        'class': 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': 'focused || overlayVisible'\n      },\n      providers: [DROPDOWN_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      styles: [\".p-dropdown{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-dropdown-clear-icon{position:absolute;top:50%;margin-top:-.5rem}.p-dropdown-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-dropdown-label{display:block;white-space:nowrap;overflow:hidden;flex:1 1 auto;width:1%;text-overflow:ellipsis;cursor:pointer}.p-dropdown-label-empty{overflow:hidden;visibility:hidden}input.p-dropdown-label{cursor:default}.p-dropdown .p-dropdown-panel{min-width:100%}.p-dropdown-panel{position:absolute;top:0;left:0}.p-dropdown-items-wrapper{overflow:auto}.p-dropdown-item{cursor:pointer;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-dropdown-items{margin:0;padding:0;list-style-type:none}.p-dropdown-filter{width:100%}.p-dropdown-filter-container{position:relative}.p-dropdown-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-fluid .p-dropdown{display:flex}.p-fluid .p-dropdown .p-dropdown-label{width:1%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i3.FilterService\n    }, {\n      type: i3.PrimeNGConfig\n    }, {\n      type: i3.OverlayService\n    }];\n  }, {\n    scrollHeight: [{\n      type: Input\n    }],\n    filter: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    panelStyle: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    panelStyleClass: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    filterPlaceholder: [{\n      type: Input\n    }],\n    filterLocale: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    selectId: [{\n      type: Input\n    }],\n    dataKey: [{\n      type: Input\n    }],\n    filterBy: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input\n    }],\n    resetFilterOnHide: [{\n      type: Input\n    }],\n    dropdownIcon: [{\n      type: Input\n    }],\n    optionLabel: [{\n      type: Input\n    }],\n    optionValue: [{\n      type: Input\n    }],\n    optionDisabled: [{\n      type: Input\n    }],\n    optionGroupLabel: [{\n      type: Input\n    }],\n    optionGroupChildren: [{\n      type: Input\n    }],\n    autoDisplayFirst: [{\n      type: Input\n    }],\n    group: [{\n      type: Input\n    }],\n    showClear: [{\n      type: Input\n    }],\n    emptyFilterMessage: [{\n      type: Input\n    }],\n    emptyMessage: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    virtualScrollItemSize: [{\n      type: Input\n    }],\n    virtualScrollOptions: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    ariaFilterLabel: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    filterMatchMode: [{\n      type: Input\n    }],\n    maxlength: [{\n      type: Input\n    }],\n    tooltip: [{\n      type: Input\n    }],\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipPositionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    autofocusFilter: [{\n      type: Input\n    }],\n    onChange: [{\n      type: Output\n    }],\n    onFilter: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onClick: [{\n      type: Output\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onClear: [{\n      type: Output\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    filterViewChild: [{\n      type: ViewChild,\n      args: ['filter']\n    }],\n    accessibleViewChild: [{\n      type: ViewChild,\n      args: ['in']\n    }],\n    editableInputViewChild: [{\n      type: ViewChild,\n      args: ['editableInput']\n    }],\n    itemsViewChild: [{\n      type: ViewChild,\n      args: ['items']\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: ['scroller']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    disabled: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    filterValue: [{\n      type: Input\n    }]\n  });\n})();\n\nclass DropdownModule {}\n\nDropdownModule.ɵfac = function DropdownModule_Factory(t) {\n  return new (t || DropdownModule)();\n};\n\nDropdownModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DropdownModule\n});\nDropdownModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DropdownModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, TooltipModule, RippleModule, ScrollerModule],\n      exports: [Dropdown, SharedModule, ScrollerModule],\n      declarations: [Dropdown, DropdownItem]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { DROPDOWN_VALUE_ACCESSOR, Dropdown, DropdownItem, DropdownModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "Component", "Input", "Output", "ChangeDetectionStrategy", "ViewEncapsulation", "ViewChild", "ContentChildren", "NgModule", "trigger", "transition", "style", "animate", "i1", "CommonModule", "i3", "Translation<PERSON>eys", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "UniqueComponentId", "ObjectUtils", "ZIndexUtils", "NG_VALUE_ACCESSOR", "i4", "TooltipModule", "i5", "ScrollerModule", "i2", "RippleModule", "DROPDOWN_VALUE_ACCESSOR", "provide", "useExisting", "Dropdown", "multi", "DropdownItem", "constructor", "onClick", "onOptionClick", "event", "emit", "originalEvent", "option", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "host", "selected", "label", "disabled", "visible", "itemSize", "el", "renderer", "cd", "zone", "filterService", "config", "overlayService", "scrollHeight", "resetFilterOnHide", "dropdownIcon", "optionGroupChildren", "autoDisplayFirst", "emptyFilterMessage", "emptyMessage", "lazy", "autoZIndex", "baseZIndex", "showTransitionOptions", "hideTransitionOptions", "filterMatchMode", "tooltip", "tooltipPosition", "tooltipPositionStyle", "autofocusFilter", "onChange", "onFilter", "onFocus", "onBlur", "onShow", "onHide", "onClear", "onLazyLoad", "onModelChange", "onModelTouched", "id", "_disabled", "focused", "overlayVisible", "hide", "destroyed", "detectChanges", "_itemSize", "val", "console", "warn", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "itemTemplate", "selectedItemTemplate", "headerTemplate", "filterTemplate", "footerTemplate", "emptyFilterTemplate", "emptyTemplate", "groupTemplate", "loaderTemplate", "ngOnInit", "optionsToDisplay", "options", "updateSelectedOption", "labelId", "listId", "filterBy", "filterOptions", "filter", "value", "onFilterInputChange", "reset", "resetFilter", "_options", "selectedOption", "findOption", "isNotEmpty", "optionsChanged", "_filterValue", "length", "activateFilter", "filterValue", "ngAfterViewInit", "editable", "updateEditableLabel", "getOptionLabel", "emptyMessageLabel", "getTranslation", "EMPTY_MESSAGE", "emptyFilterMessageLabel", "EMPTY_FILTER_MESSAGE", "filled", "undefined", "editableInputViewChild", "nativeElement", "optionLabel", "resolveFieldData", "getOptionValue", "optionValue", "isOptionDisabled", "optionDisabled", "getOptionGroupLabel", "optionGroup", "optionGroupLabel", "getOptionGroupChildren", "items", "onItemClick", "selectItem", "accessibleViewChild", "focus", "preventScroll", "setTimeout", "ngAfterViewChecked", "runOutsideAngular", "alignOverlay", "selectedOptionUpdated", "itemsWrapper", "selectedItem", "findSingle", "overlay", "scrollInView", "writeValue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filterView<PERSON>hild", "placeholder", "group", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "onMouseclick", "readonly", "isInputClick", "show", "onOverlayClick", "add", "target", "hasClass", "isSameNode", "isOutsideClicked", "contains", "isEmpty", "onEditableInputClick", "bindDocumentClickListener", "onEditableInputFocus", "onEditableInputChange", "preventDocumentDefault", "onOverlayAnimationStart", "toState", "element", "virtualScroll", "scroller", "setContentEl", "itemsViewChild", "appendOverlay", "set", "zIndex", "bindDocumentResizeListener", "bindScrollListener", "selectedIndex", "findOptionIndex", "scrollToIndex", "selectedListItem", "scrollIntoView", "block", "inline", "preventModelTouched", "onOverlayHide", "onOverlayAnimationEnd", "clear", "appendTo", "document", "body", "append<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "getWidth", "containerViewChild", "restoreOverlayAppend", "absolutePosition", "relativePosition", "onInputFocus", "onInputBlur", "findPrevEnabledOption", "index", "prevEnabledOption", "i", "findNextEnabledOption", "nextEnabledOption", "onKeydown", "search", "which", "altKey", "selectedItemIndex", "findOptionGroupIndex", "nextItemIndex", "itemIndex", "groupIndex", "preventDefault", "prevItemIndex", "prevGroup", "metaKey", "searchTimeout", "clearTimeout", "char", "key", "previousSearchChar", "currentSearchChar", "searchValue", "newOption", "searchIndex", "searchOptionWithinGroup", "searchOption", "searchOptionInRange", "start", "end", "opt", "toLocaleLowerCase", "filterLocale", "startsWith", "j", "opts", "equals", "dataKey", "inGroup", "optgroup", "inputValue", "searchFields", "split", "filteredGroups", "filteredSubOptions", "push", "Object", "assign", "applyFocus", "documentClickListener", "documentTarget", "ownerDocument", "listen", "unbindDocumentClickListener", "documentResizeListener", "onWindowResize", "bind", "window", "addEventListener", "unbindDocumentResizeListener", "removeEventListener", "isTouchDevice", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "ngOnDestroy", "destroy", "ElementRef", "Renderer2", "ChangeDetectorRef", "NgZone", "FilterService", "PrimeNGConfig", "OverlayService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "opacity", "transform", "animations", "providers", "changeDetection", "OnPush", "encapsulation", "None", "styles", "name", "panelStyle", "styleClass", "panelStyleClass", "required", "tabindex", "filterPlaceholder", "inputId", "selectId", "autofocus", "showClear", "virtualScrollItemSize", "virtualScrollOptions", "ariaFilter<PERSON><PERSON>l", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "maxlength", "tooltipStyleClass", "DropdownModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-dropdown.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, Input, Output, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i5 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\n\nconst DROPDOWN_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Dropdown),\n    multi: true\n};\nclass DropdownItem {\n    constructor() {\n        this.onClick = new EventEmitter();\n    }\n    onOptionClick(event) {\n        this.onClick.emit({\n            originalEvent: event,\n            option: this.option\n        });\n    }\n}\nDropdownItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownItem, deps: [], target: i0.ɵɵFactoryTarget.Component });\nDropdownItem.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: DropdownItem, selector: \"p-dropdownItem\", inputs: { option: \"option\", selected: \"selected\", label: \"label\", disabled: \"disabled\", visible: \"visible\", itemSize: \"itemSize\", template: \"template\" }, outputs: { onClick: \"onClick\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <li (click)=\"onOptionClick($event)\" role=\"option\" pRipple\n            [attr.aria-label]=\"label\" [attr.aria-selected]=\"selected\"\n            [ngStyle]=\"{'height': itemSize + 'px'}\" [id]=\"selected ? 'p-highlighted-option':''\"\n            [ngClass]=\"{'p-dropdown-item':true, 'p-highlight': selected, 'p-disabled': disabled}\">\n            <span *ngIf=\"!template\">{{label||'empty'}}</span>\n            <ng-container *ngTemplateOutlet=\"template; context: {$implicit: option}\"></ng-container>\n        </li>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.Ripple, selector: \"[pRipple]\" }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownItem, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-dropdownItem',\n                    template: `\n        <li (click)=\"onOptionClick($event)\" role=\"option\" pRipple\n            [attr.aria-label]=\"label\" [attr.aria-selected]=\"selected\"\n            [ngStyle]=\"{'height': itemSize + 'px'}\" [id]=\"selected ? 'p-highlighted-option':''\"\n            [ngClass]=\"{'p-dropdown-item':true, 'p-highlight': selected, 'p-disabled': disabled}\">\n            <span *ngIf=\"!template\">{{label||'empty'}}</span>\n            <ng-container *ngTemplateOutlet=\"template; context: {$implicit: option}\"></ng-container>\n        </li>\n    `,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], propDecorators: { option: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], label: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], template: [{\n                type: Input\n            }], onClick: [{\n                type: Output\n            }] } });\nclass Dropdown {\n    constructor(el, renderer, cd, zone, filterService, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n        this.filterService = filterService;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.scrollHeight = '200px';\n        this.resetFilterOnHide = false;\n        this.dropdownIcon = 'pi pi-chevron-down';\n        this.optionGroupChildren = \"items\";\n        this.autoDisplayFirst = true;\n        this.emptyFilterMessage = '';\n        this.emptyMessage = '';\n        this.lazy = false;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.filterMatchMode = \"contains\";\n        this.tooltip = '';\n        this.tooltipPosition = 'right';\n        this.tooltipPositionStyle = 'absolute';\n        this.autofocusFilter = true;\n        this.onChange = new EventEmitter();\n        this.onFilter = new EventEmitter();\n        this.onFocus = new EventEmitter();\n        this.onBlur = new EventEmitter();\n        this.onClick = new EventEmitter();\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.onClear = new EventEmitter();\n        this.onLazyLoad = new EventEmitter();\n        this.onModelChange = () => { };\n        this.onModelTouched = () => { };\n        this.id = UniqueComponentId();\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(_disabled) {\n        if (_disabled) {\n            this.focused = false;\n            if (this.overlayVisible)\n                this.hide();\n        }\n        this._disabled = _disabled;\n        if (!this.cd.destroyed) {\n            this.cd.detectChanges();\n        }\n    }\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(val) {\n        this._itemSize = val;\n        console.warn(\"The itemSize property is deprecated, use virtualScrollItemSize property instead.\");\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'selectedItem':\n                    this.selectedItemTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'filter':\n                    this.filterTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'emptyfilter':\n                    this.emptyFilterTemplate = item.template;\n                    break;\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnInit() {\n        this.optionsToDisplay = this.options;\n        this.updateSelectedOption(null);\n        this.labelId = this.id + '_label';\n        this.listId = this.id + '_list';\n        if (this.filterBy) {\n            this.filterOptions = {\n                filter: (value) => this.onFilterInputChange(value),\n                reset: () => this.resetFilter()\n            };\n        }\n    }\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        this.optionsToDisplay = this._options;\n        this.updateSelectedOption(this.value);\n        this.selectedOption = this.findOption(this.value, this.optionsToDisplay);\n        if (!this.selectedOption && ObjectUtils.isNotEmpty(this.value)) {\n            this.value = null;\n            this.onModelChange(this.value);\n        }\n        this.optionsChanged = true;\n        if (this._filterValue && this._filterValue.length) {\n            this.activateFilter();\n        }\n    }\n    get filterValue() {\n        return this._filterValue;\n    }\n    set filterValue(val) {\n        this._filterValue = val;\n        this.activateFilter();\n    }\n    ngAfterViewInit() {\n        if (this.editable) {\n            this.updateEditableLabel();\n        }\n    }\n    get label() {\n        return this.selectedOption ? this.getOptionLabel(this.selectedOption) : null;\n    }\n    get emptyMessageLabel() {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n    get emptyFilterMessageLabel() {\n        return this.emptyFilterMessage || this.config.getTranslation(TranslationKeys.EMPTY_FILTER_MESSAGE);\n    }\n    get filled() {\n        return this.value || this.value != null || this.value != undefined;\n    }\n    updateEditableLabel() {\n        if (this.editableInputViewChild && this.editableInputViewChild.nativeElement) {\n            this.editableInputViewChild.nativeElement.value = (this.selectedOption ? this.getOptionLabel(this.selectedOption) : this.value || '');\n        }\n    }\n    getOptionLabel(option) {\n        return this.optionLabel ? ObjectUtils.resolveFieldData(option, this.optionLabel) : (option && option.label !== undefined ? option.label : option);\n    }\n    getOptionValue(option) {\n        return this.optionValue ? ObjectUtils.resolveFieldData(option, this.optionValue) : (!this.optionLabel && (option && option.value !== undefined) ? option.value : option);\n    }\n    isOptionDisabled(option) {\n        return this.optionDisabled ? ObjectUtils.resolveFieldData(option, this.optionDisabled) : (option && option.disabled !== undefined ? option.disabled : false);\n    }\n    getOptionGroupLabel(optionGroup) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : (optionGroup && optionGroup.label !== undefined ? optionGroup.label : optionGroup);\n    }\n    getOptionGroupChildren(optionGroup) {\n        return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    onItemClick(event) {\n        const option = event.option;\n        if (!this.isOptionDisabled(option)) {\n            this.selectItem(event.originalEvent, option);\n            this.accessibleViewChild.nativeElement.focus({ preventScroll: true });\n        }\n        setTimeout(() => {\n            this.hide();\n        }, 150);\n    }\n    selectItem(event, option) {\n        if (this.selectedOption != option) {\n            this.selectedOption = option;\n            this.value = this.getOptionValue(option);\n            this.onModelChange(this.value);\n            this.updateEditableLabel();\n            this.onChange.emit({\n                originalEvent: event,\n                value: this.value\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        if (this.optionsChanged && this.overlayVisible) {\n            this.optionsChanged = false;\n            this.zone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    this.alignOverlay();\n                }, 1);\n            });\n        }\n        if (this.selectedOptionUpdated && this.itemsWrapper) {\n            let selectedItem = DomHandler.findSingle(this.overlay, 'li.p-highlight');\n            if (selectedItem) {\n                DomHandler.scrollInView(this.itemsWrapper, DomHandler.findSingle(this.overlay, 'li.p-highlight'));\n            }\n            this.selectedOptionUpdated = false;\n        }\n    }\n    writeValue(value) {\n        if (this.filter) {\n            this.resetFilter();\n        }\n        this.value = value;\n        this.updateSelectedOption(value);\n        this.updateEditableLabel();\n        this.cd.markForCheck();\n    }\n    resetFilter() {\n        this._filterValue = null;\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n            this.filterViewChild.nativeElement.value = '';\n        }\n        this.optionsToDisplay = this.options;\n    }\n    updateSelectedOption(val) {\n        this.selectedOption = this.findOption(val, this.optionsToDisplay);\n        if (this.autoDisplayFirst && !this.placeholder && !this.selectedOption && this.optionsToDisplay && this.optionsToDisplay.length && !this.editable) {\n            if (this.group) {\n                this.selectedOption = this.optionsToDisplay[0].items[0];\n            }\n            else {\n                this.selectedOption = this.optionsToDisplay[0];\n            }\n            this.value = this.getOptionValue(this.selectedOption);\n            this.onModelChange(this.value);\n        }\n        this.selectedOptionUpdated = true;\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    onMouseclick(event) {\n        if (this.disabled || this.readonly || this.isInputClick(event)) {\n            return;\n        }\n        this.onClick.emit(event);\n        this.accessibleViewChild.nativeElement.focus({ preventScroll: true });\n        if (this.overlayVisible)\n            this.hide();\n        else\n            this.show();\n        this.cd.detectChanges();\n    }\n    onOverlayClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n    }\n    isInputClick(event) {\n        return DomHandler.hasClass(event.target, 'p-dropdown-clear-icon') ||\n            event.target.isSameNode(this.accessibleViewChild.nativeElement) ||\n            (this.editableInputViewChild && event.target.isSameNode(this.editableInputViewChild.nativeElement));\n    }\n    isOutsideClicked(event) {\n        return !(this.el.nativeElement.isSameNode(event.target) || this.el.nativeElement.contains(event.target) || (this.overlay && this.overlay.contains(event.target)));\n    }\n    isEmpty() {\n        return !this.optionsToDisplay || (this.optionsToDisplay && this.optionsToDisplay.length === 0);\n    }\n    onEditableInputClick() {\n        this.bindDocumentClickListener();\n    }\n    onEditableInputFocus(event) {\n        this.focused = true;\n        this.hide();\n        this.onFocus.emit(event);\n    }\n    onEditableInputChange(event) {\n        this.value = event.target.value;\n        this.updateSelectedOption(this.value);\n        this.onModelChange(this.value);\n        this.onChange.emit({\n            originalEvent: event,\n            value: this.value\n        });\n    }\n    show() {\n        this.overlayVisible = true;\n        this.preventDocumentDefault = true;\n        this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.overlay = event.element;\n                this.itemsWrapper = DomHandler.findSingle(this.overlay, this.virtualScroll ? '.p-scroller' : '.p-dropdown-items-wrapper');\n                this.virtualScroll && this.scroller.setContentEl(this.itemsViewChild.nativeElement);\n                this.appendOverlay();\n                if (this.autoZIndex) {\n                    ZIndexUtils.set('overlay', this.overlay, this.baseZIndex + this.config.zIndex.overlay);\n                }\n                this.alignOverlay();\n                this.bindDocumentClickListener();\n                this.bindDocumentResizeListener();\n                this.bindScrollListener();\n                if (this.options && this.options.length) {\n                    if (this.virtualScroll) {\n                        const selectedIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n                        if (selectedIndex !== -1) {\n                            this.scroller.scrollToIndex(selectedIndex);\n                        }\n                    }\n                    else {\n                        let selectedListItem = DomHandler.findSingle(this.itemsWrapper, '.p-dropdown-item.p-highlight');\n                        if (selectedListItem) {\n                            selectedListItem.scrollIntoView({ block: 'nearest', inline: 'center' });\n                        }\n                    }\n                }\n                if (this.filterViewChild && this.filterViewChild.nativeElement) {\n                    this.preventModelTouched = true;\n                    if (this.autofocusFilter) {\n                        this.filterViewChild.nativeElement.focus();\n                    }\n                }\n                this.onShow.emit(event);\n                break;\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit(event);\n                break;\n        }\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                ZIndexUtils.clear(event.element);\n                break;\n        }\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.overlay);\n            else\n                DomHandler.appendChild(this.overlay, this.appendTo);\n            if (!this.overlay.style.minWidth) {\n                this.overlay.style.minWidth = DomHandler.getWidth(this.containerViewChild.nativeElement) + 'px';\n            }\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.overlay && this.appendTo) {\n            this.el.nativeElement.appendChild(this.overlay);\n        }\n    }\n    hide() {\n        this.overlayVisible = false;\n        if (this.filter && this.resetFilterOnHide) {\n            this.resetFilter();\n        }\n        this.cd.markForCheck();\n    }\n    alignOverlay() {\n        if (this.overlay) {\n            if (this.appendTo)\n                DomHandler.absolutePosition(this.overlay, this.containerViewChild.nativeElement);\n            else\n                DomHandler.relativePosition(this.overlay, this.containerViewChild.nativeElement);\n        }\n    }\n    onInputFocus(event) {\n        this.focused = true;\n        this.onFocus.emit(event);\n    }\n    onInputBlur(event) {\n        this.focused = false;\n        this.onBlur.emit(event);\n        if (!this.preventModelTouched) {\n            this.onModelTouched();\n        }\n        this.preventModelTouched = false;\n    }\n    findPrevEnabledOption(index) {\n        let prevEnabledOption;\n        if (this.optionsToDisplay && this.optionsToDisplay.length) {\n            for (let i = (index - 1); 0 <= i; i--) {\n                let option = this.optionsToDisplay[i];\n                if (this.isOptionDisabled(option)) {\n                    continue;\n                }\n                else {\n                    prevEnabledOption = option;\n                    break;\n                }\n            }\n            if (!prevEnabledOption) {\n                for (let i = this.optionsToDisplay.length - 1; i >= index; i--) {\n                    let option = this.optionsToDisplay[i];\n                    if (this.isOptionDisabled(option)) {\n                        continue;\n                    }\n                    else {\n                        prevEnabledOption = option;\n                        break;\n                    }\n                }\n            }\n        }\n        return prevEnabledOption;\n    }\n    findNextEnabledOption(index) {\n        let nextEnabledOption;\n        if (this.optionsToDisplay && this.optionsToDisplay.length) {\n            for (let i = (index + 1); i < this.optionsToDisplay.length; i++) {\n                let option = this.optionsToDisplay[i];\n                if (this.isOptionDisabled(option)) {\n                    continue;\n                }\n                else {\n                    nextEnabledOption = option;\n                    break;\n                }\n            }\n            if (!nextEnabledOption) {\n                for (let i = 0; i < index; i++) {\n                    let option = this.optionsToDisplay[i];\n                    if (this.isOptionDisabled(option)) {\n                        continue;\n                    }\n                    else {\n                        nextEnabledOption = option;\n                        break;\n                    }\n                }\n            }\n        }\n        return nextEnabledOption;\n    }\n    onKeydown(event, search) {\n        if (this.readonly || !this.optionsToDisplay || this.optionsToDisplay.length === null) {\n            return;\n        }\n        switch (event.which) {\n            //down\n            case 40:\n                if (!this.overlayVisible && event.altKey) {\n                    this.show();\n                }\n                else {\n                    if (this.group) {\n                        let selectedItemIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n                        if (selectedItemIndex !== -1) {\n                            let nextItemIndex = selectedItemIndex.itemIndex + 1;\n                            if (nextItemIndex < (this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex]).length)) {\n                                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex])[nextItemIndex]);\n                                this.selectedOptionUpdated = true;\n                            }\n                            else if (this.optionsToDisplay[selectedItemIndex.groupIndex + 1]) {\n                                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex + 1])[0]);\n                                this.selectedOptionUpdated = true;\n                            }\n                        }\n                        else {\n                            if (this.optionsToDisplay && this.optionsToDisplay.length > 0) {\n                                this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[0])[0]);\n                            }\n                        }\n                    }\n                    else {\n                        let selectedItemIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n                        let nextEnabledOption = this.findNextEnabledOption(selectedItemIndex);\n                        if (nextEnabledOption) {\n                            this.selectItem(event, nextEnabledOption);\n                            this.selectedOptionUpdated = true;\n                        }\n                    }\n                }\n                event.preventDefault();\n                break;\n            //up\n            case 38:\n                if (this.group) {\n                    let selectedItemIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n                    if (selectedItemIndex !== -1) {\n                        let prevItemIndex = selectedItemIndex.itemIndex - 1;\n                        if (prevItemIndex >= 0) {\n                            this.selectItem(event, this.getOptionGroupChildren(this.optionsToDisplay[selectedItemIndex.groupIndex])[prevItemIndex]);\n                            this.selectedOptionUpdated = true;\n                        }\n                        else if (prevItemIndex < 0) {\n                            let prevGroup = this.optionsToDisplay[selectedItemIndex.groupIndex - 1];\n                            if (prevGroup) {\n                                this.selectItem(event, this.getOptionGroupChildren(prevGroup)[this.getOptionGroupChildren(prevGroup).length - 1]);\n                                this.selectedOptionUpdated = true;\n                            }\n                        }\n                    }\n                }\n                else {\n                    let selectedItemIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n                    let prevEnabledOption = this.findPrevEnabledOption(selectedItemIndex);\n                    if (prevEnabledOption) {\n                        this.selectItem(event, prevEnabledOption);\n                        this.selectedOptionUpdated = true;\n                    }\n                }\n                event.preventDefault();\n                break;\n            //space\n            case 32:\n                if (search) {\n                    if (!this.overlayVisible) {\n                        this.show();\n                    }\n                    else {\n                        this.hide();\n                    }\n                    event.preventDefault();\n                }\n                break;\n            //enter\n            case 13:\n                if (this.overlayVisible && (!this.filter || (this.optionsToDisplay && this.optionsToDisplay.length > 0))) {\n                    this.hide();\n                }\n                else if (!this.overlayVisible) {\n                    this.show();\n                }\n                event.preventDefault();\n                break;\n            //escape and tab\n            case 27:\n            case 9:\n                this.hide();\n                break;\n            //search item based on keyboard input\n            default:\n                if (search && !event.metaKey) {\n                    this.search(event);\n                }\n                break;\n        }\n    }\n    search(event) {\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n        const char = event.key;\n        this.previousSearchChar = this.currentSearchChar;\n        this.currentSearchChar = char;\n        if (this.previousSearchChar === this.currentSearchChar)\n            this.searchValue = this.currentSearchChar;\n        else\n            this.searchValue = this.searchValue ? this.searchValue + char : char;\n        let newOption;\n        if (this.group) {\n            let searchIndex = this.selectedOption ? this.findOptionGroupIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : { groupIndex: 0, itemIndex: 0 };\n            newOption = this.searchOptionWithinGroup(searchIndex);\n        }\n        else {\n            let searchIndex = this.selectedOption ? this.findOptionIndex(this.getOptionValue(this.selectedOption), this.optionsToDisplay) : -1;\n            newOption = this.searchOption(++searchIndex);\n        }\n        if (newOption && !this.isOptionDisabled(newOption)) {\n            this.selectItem(event, newOption);\n            this.selectedOptionUpdated = true;\n        }\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = null;\n        }, 250);\n    }\n    searchOption(index) {\n        let option;\n        if (this.searchValue) {\n            option = this.searchOptionInRange(index, this.optionsToDisplay.length);\n            if (!option) {\n                option = this.searchOptionInRange(0, index);\n            }\n        }\n        return option;\n    }\n    searchOptionInRange(start, end) {\n        for (let i = start; i < end; i++) {\n            let opt = this.optionsToDisplay[i];\n            if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n                return opt;\n            }\n        }\n        return null;\n    }\n    searchOptionWithinGroup(index) {\n        let option;\n        if (this.searchValue) {\n            for (let i = index.groupIndex; i < this.optionsToDisplay.length; i++) {\n                for (let j = (index.groupIndex === i) ? (index.itemIndex + 1) : 0; j < this.getOptionGroupChildren(this.optionsToDisplay[i]).length; j++) {\n                    let opt = this.getOptionGroupChildren(this.optionsToDisplay[i])[j];\n                    if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n                        return opt;\n                    }\n                }\n            }\n            if (!option) {\n                for (let i = 0; i <= index.groupIndex; i++) {\n                    for (let j = 0; j < ((index.groupIndex === i) ? index.itemIndex : this.getOptionGroupChildren(this.optionsToDisplay[i]).length); j++) {\n                        let opt = this.getOptionGroupChildren(this.optionsToDisplay[i])[j];\n                        if (this.getOptionLabel(opt).toLocaleLowerCase(this.filterLocale).startsWith(this.searchValue.toLocaleLowerCase(this.filterLocale)) && !this.isOptionDisabled(opt)) {\n                            return opt;\n                        }\n                    }\n                }\n            }\n        }\n        return null;\n    }\n    findOptionIndex(val, opts) {\n        let index = -1;\n        if (opts) {\n            for (let i = 0; i < opts.length; i++) {\n                if ((val == null && this.getOptionValue(opts[i]) == null) || ObjectUtils.equals(val, this.getOptionValue(opts[i]), this.dataKey)) {\n                    index = i;\n                    break;\n                }\n            }\n        }\n        return index;\n    }\n    findOptionGroupIndex(val, opts) {\n        let groupIndex, itemIndex;\n        if (opts) {\n            for (let i = 0; i < opts.length; i++) {\n                groupIndex = i;\n                itemIndex = this.findOptionIndex(val, this.getOptionGroupChildren(opts[i]));\n                if (itemIndex !== -1) {\n                    break;\n                }\n            }\n        }\n        if (itemIndex !== -1) {\n            return { groupIndex: groupIndex, itemIndex: itemIndex };\n        }\n        else {\n            return -1;\n        }\n    }\n    findOption(val, opts, inGroup) {\n        if (this.group && !inGroup) {\n            let opt;\n            if (opts && opts.length) {\n                for (let optgroup of opts) {\n                    opt = this.findOption(val, this.getOptionGroupChildren(optgroup), true);\n                    if (opt) {\n                        break;\n                    }\n                }\n            }\n            return opt;\n        }\n        else {\n            let index = this.findOptionIndex(val, opts);\n            return (index != -1) ? opts[index] : null;\n        }\n    }\n    onFilterInputChange(event) {\n        let inputValue = event.target.value;\n        if (inputValue && inputValue.length) {\n            this._filterValue = inputValue;\n            this.activateFilter();\n        }\n        else {\n            this._filterValue = null;\n            this.optionsToDisplay = this.options;\n        }\n        this.virtualScroll && this.scroller.scrollToIndex(0);\n        this.optionsChanged = true;\n        this.onFilter.emit({ originalEvent: event, filter: this._filterValue });\n    }\n    activateFilter() {\n        let searchFields = (this.filterBy || this.optionLabel || 'label').split(',');\n        if (this.options && this.options.length) {\n            if (this.group) {\n                let filteredGroups = [];\n                for (let optgroup of this.options) {\n                    let filteredSubOptions = this.filterService.filter(this.getOptionGroupChildren(optgroup), searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n                    if (filteredSubOptions && filteredSubOptions.length) {\n                        filteredGroups.push(Object.assign(Object.assign({}, optgroup), { [this.optionGroupChildren]: filteredSubOptions }));\n                    }\n                }\n                this.optionsToDisplay = filteredGroups;\n            }\n            else {\n                this.optionsToDisplay = this.filterService.filter(this.options, searchFields, this.filterValue, this.filterMatchMode, this.filterLocale);\n            }\n            this.optionsChanged = true;\n        }\n    }\n    applyFocus() {\n        if (this.editable)\n            DomHandler.findSingle(this.el.nativeElement, '.p-dropdown-label.p-inputtext').focus();\n        else\n            DomHandler.findSingle(this.el.nativeElement, 'input[readonly]').focus();\n    }\n    focus() {\n        this.applyFocus();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', (event) => {\n                if (!this.preventDocumentDefault && this.isOutsideClicked(event)) {\n                    this.hide();\n                    this.unbindDocumentClickListener();\n                }\n                this.preventDocumentDefault = false;\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild.nativeElement, (event) => {\n                if (this.overlayVisible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    clear(event) {\n        this.value = null;\n        this.onModelChange(this.value);\n        this.onChange.emit({\n            originalEvent: event,\n            value: this.value\n        });\n        this.updateSelectedOption(this.value);\n        this.updateEditableLabel();\n        this.onClear.emit(event);\n    }\n    onOverlayHide() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.overlay = null;\n        this.itemsWrapper = null;\n        this.onModelTouched();\n    }\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        if (this.overlay) {\n            ZIndexUtils.clear(this.overlay);\n        }\n        this.restoreOverlayAppend();\n        this.onOverlayHide();\n    }\n}\nDropdown.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Dropdown, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i3.FilterService }, { token: i3.PrimeNGConfig }, { token: i3.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nDropdown.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Dropdown, selector: \"p-dropdown\", inputs: { scrollHeight: \"scrollHeight\", filter: \"filter\", name: \"name\", style: \"style\", panelStyle: \"panelStyle\", styleClass: \"styleClass\", panelStyleClass: \"panelStyleClass\", readonly: \"readonly\", required: \"required\", editable: \"editable\", appendTo: \"appendTo\", tabindex: \"tabindex\", placeholder: \"placeholder\", filterPlaceholder: \"filterPlaceholder\", filterLocale: \"filterLocale\", inputId: \"inputId\", selectId: \"selectId\", dataKey: \"dataKey\", filterBy: \"filterBy\", autofocus: \"autofocus\", resetFilterOnHide: \"resetFilterOnHide\", dropdownIcon: \"dropdownIcon\", optionLabel: \"optionLabel\", optionValue: \"optionValue\", optionDisabled: \"optionDisabled\", optionGroupLabel: \"optionGroupLabel\", optionGroupChildren: \"optionGroupChildren\", autoDisplayFirst: \"autoDisplayFirst\", group: \"group\", showClear: \"showClear\", emptyFilterMessage: \"emptyFilterMessage\", emptyMessage: \"emptyMessage\", lazy: \"lazy\", virtualScroll: \"virtualScroll\", virtualScrollItemSize: \"virtualScrollItemSize\", virtualScrollOptions: \"virtualScrollOptions\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", ariaFilterLabel: \"ariaFilterLabel\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", filterMatchMode: \"filterMatchMode\", maxlength: \"maxlength\", tooltip: \"tooltip\", tooltipPosition: \"tooltipPosition\", tooltipPositionStyle: \"tooltipPositionStyle\", tooltipStyleClass: \"tooltipStyleClass\", autofocusFilter: \"autofocusFilter\", disabled: \"disabled\", itemSize: \"itemSize\", options: \"options\", filterValue: \"filterValue\" }, outputs: { onChange: \"onChange\", onFilter: \"onFilter\", onFocus: \"onFocus\", onBlur: \"onBlur\", onClick: \"onClick\", onShow: \"onShow\", onHide: \"onHide\", onClear: \"onClear\", onLazyLoad: \"onLazyLoad\" }, host: { properties: { \"class.p-inputwrapper-filled\": \"filled\", \"class.p-inputwrapper-focus\": \"focused || overlayVisible\" }, classAttribute: \"p-element p-inputwrapper\" }, providers: [DROPDOWN_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }, { propertyName: \"filterViewChild\", first: true, predicate: [\"filter\"], descendants: true }, { propertyName: \"accessibleViewChild\", first: true, predicate: [\"in\"], descendants: true }, { propertyName: \"editableInputViewChild\", first: true, predicate: [\"editableInput\"], descendants: true }, { propertyName: \"itemsViewChild\", first: true, predicate: [\"items\"], descendants: true }, { propertyName: \"scroller\", first: true, predicate: [\"scroller\"], descendants: true }], ngImport: i0, template: `\n         <div #container [ngClass]=\"{'p-dropdown p-component':true,\n            'p-disabled':disabled, 'p-dropdown-open':overlayVisible, 'p-focus':focused, 'p-dropdown-clearable': showClear && !disabled}\"\n            (click)=\"onMouseclick($event)\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #in [attr.id]=\"inputId\" type=\"text\" readonly (focus)=\"onInputFocus($event)\" aria-haspopup=\"listbox\" [attr.placeholder]=\"placeholder\"\n                    aria-haspopup=\"listbox\" [attr.aria-label]=\"ariaLabel\" [attr.aria-expanded]=\"false\" [attr.aria-labelledby]=\"ariaLabelledBy\" (blur)=\"onInputBlur($event)\" (keydown)=\"onKeydown($event, true)\"\n                    [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.autofocus]=\"autofocus\" [attr.aria-activedescendant]=\"overlayVisible ? labelId : null\" role=\"combobox\">\n            </div>\n            <span [attr.id]=\"labelId\" [ngClass]=\"{'p-dropdown-label p-inputtext':true,'p-dropdown-label-empty':(label == null || label.length === 0)}\" *ngIf=\"!editable && (label != null)\" [pTooltip]=\"tooltip\" [tooltipPosition]=\"tooltipPosition\" [positionStyle]=\"tooltipPositionStyle\" [tooltipStyleClass]=\"tooltipStyleClass\">\n                <ng-container *ngIf=\"!selectedItemTemplate\">{{label||'empty'}}</ng-container>\n                <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: selectedOption}\"></ng-container>\n            </span>\n            <span [ngClass]=\"{'p-dropdown-label p-inputtext p-placeholder':true,'p-dropdown-label-empty': (placeholder == null || placeholder.length === 0)}\" *ngIf=\"!editable && (label == null)\">{{placeholder||'empty'}}</span>\n            <input #editableInput type=\"text\" [attr.maxlength]=\"maxlength\" class=\"p-dropdown-label p-inputtext\" *ngIf=\"editable\" [disabled]=\"disabled\" [attr.placeholder]=\"placeholder\"\n                aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\" (click)=\"onEditableInputClick()\" (input)=\"onEditableInputChange($event)\" (focus)=\"onEditableInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i class=\"p-dropdown-clear-icon pi pi-times\" (click)=\"clear($event)\" *ngIf=\"value != null && showClear && !disabled\"></i>\n            <div class=\"p-dropdown-trigger\" role=\"button\" aria-label=\"dropdown trigger\" aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\">\n                <span class=\"p-dropdown-trigger-icon\" [ngClass]=\"dropdownIcon\"></span>\n            </div>\n            <div *ngIf=\"overlayVisible\" [ngClass]=\"'p-dropdown-panel p-component'\" (click)=\"onOverlayClick($event)\" [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <div class=\"p-dropdown-header\" *ngIf=\"filter\" (click)=\"$event.stopPropagation()\">\n                    <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                        <ng-container *ngTemplateOutlet=\"filterTemplate; context: {options: filterOptions}\"></ng-container>\n                    </ng-container>\n                    <ng-template #builtInFilterElement>\n                        <div class=\"p-dropdown-filter-container\">\n                            <input #filter type=\"text\" autocomplete=\"off\" [value]=\"filterValue||''\" class=\"p-dropdown-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                            (keydown.enter)=\"$event.preventDefault()\" (keydown)=\"onKeydown($event, false)\" (input)=\"onFilterInputChange($event)\" [attr.aria-label]=\"ariaFilterLabel\" [attr.aria-activedescendant]=\"overlayVisible ? 'p-highlighted-option' : labelId\">\n                            <span class=\"p-dropdown-filter-icon pi pi-search\"></span>\n                        </div>\n                    </ng-template>\n                </div>\n                <div class=\"p-dropdown-items-wrapper\" [style.max-height]=\"virtualScroll ? 'auto' : (scrollHeight||'auto')\">\n                    <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"optionsToDisplay\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                        [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                        <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                        <ng-container *ngIf=\"loaderTemplate\">\n                            <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                    </p-scroller>\n                    <ng-container *ngIf=\"!virtualScroll\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: optionsToDisplay, options: {}}\"></ng-container>\n                    </ng-container>\n\n                    <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                        <ul #items [attr.id]=\"listId\" class=\"p-dropdown-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"listbox\">\n                            <ng-container *ngIf=\"group\">\n                                <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                    <li class=\"p-dropdown-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                        <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                        <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                    </li>\n                                    <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup), selectedOption: selectedOption}\"></ng-container>\n                                </ng-template>\n                            </ng-container>\n                            <ng-container *ngIf=\"!group\">\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items, selectedOption: selectedOption}\"></ng-container>\n                            </ng-container>\n                            <ng-template #itemslist let-options let-selectedOption=\"selectedOption\">\n                                <ng-template ngFor let-option let-i=\"index\" [ngForOf]=\"options\">\n                                    <p-dropdownItem [option]=\"option\" [selected]=\"selectedOption == option\" [label]=\"getOptionLabel(option)\" [disabled]=\"isOptionDisabled(option)\"\n                                                    (onClick)=\"onItemClick($event)\"\n                                                    [template]=\"itemTemplate\"></p-dropdownItem>\n                                </ng-template>\n                            </ng-template>\n                            <li *ngIf=\"filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                    {{emptyFilterMessageLabel}}\n                                </ng-container>\n                                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                            </li>\n                            <li *ngIf=\"!filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ul>\n                    </ng-template>\n                </div>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-dropdown{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-dropdown-clear-icon{position:absolute;top:50%;margin-top:-.5rem}.p-dropdown-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-dropdown-label{display:block;white-space:nowrap;overflow:hidden;flex:1 1 auto;width:1%;text-overflow:ellipsis;cursor:pointer}.p-dropdown-label-empty{overflow:hidden;visibility:hidden}input.p-dropdown-label{cursor:default}.p-dropdown .p-dropdown-panel{min-width:100%}.p-dropdown-panel{position:absolute;top:0;left:0}.p-dropdown-items-wrapper{overflow:auto}.p-dropdown-item{cursor:pointer;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-dropdown-items{margin:0;padding:0;list-style-type:none}.p-dropdown-filter{width:100%}.p-dropdown-filter-container{position:relative}.p-dropdown-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-fluid .p-dropdown{display:flex}.p-fluid .p-dropdown .p-dropdown-label{width:1%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.PrimeTemplate, selector: \"[pTemplate]\", inputs: [\"type\", \"pTemplate\"] }, { kind: \"directive\", type: i4.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i5.Scroller, selector: \"p-scroller\", inputs: [\"id\", \"style\", \"styleClass\", \"tabindex\", \"items\", \"itemSize\", \"scrollHeight\", \"scrollWidth\", \"orientation\", \"delay\", \"resizeDelay\", \"lazy\", \"disabled\", \"loaderDisabled\", \"columns\", \"showSpacer\", \"showLoader\", \"numToleratedItems\", \"loading\", \"autoSize\", \"trackBy\", \"options\"], outputs: [\"onLazyLoad\", \"onScroll\", \"onScrollIndexChange\"] }, { kind: \"component\", type: DropdownItem, selector: \"p-dropdownItem\", inputs: [\"option\", \"selected\", \"label\", \"disabled\", \"visible\", \"itemSize\", \"template\"], outputs: [\"onClick\"] }], animations: [\n        trigger('overlayAnimation', [\n            transition(':enter', [\n                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition(':leave', [\n                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Dropdown, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-dropdown', template: `\n         <div #container [ngClass]=\"{'p-dropdown p-component':true,\n            'p-disabled':disabled, 'p-dropdown-open':overlayVisible, 'p-focus':focused, 'p-dropdown-clearable': showClear && !disabled}\"\n            (click)=\"onMouseclick($event)\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <div class=\"p-hidden-accessible\">\n                <input #in [attr.id]=\"inputId\" type=\"text\" readonly (focus)=\"onInputFocus($event)\" aria-haspopup=\"listbox\" [attr.placeholder]=\"placeholder\"\n                    aria-haspopup=\"listbox\" [attr.aria-label]=\"ariaLabel\" [attr.aria-expanded]=\"false\" [attr.aria-labelledby]=\"ariaLabelledBy\" (blur)=\"onInputBlur($event)\" (keydown)=\"onKeydown($event, true)\"\n                    [disabled]=\"disabled\" [attr.tabindex]=\"tabindex\" [attr.autofocus]=\"autofocus\" [attr.aria-activedescendant]=\"overlayVisible ? labelId : null\" role=\"combobox\">\n            </div>\n            <span [attr.id]=\"labelId\" [ngClass]=\"{'p-dropdown-label p-inputtext':true,'p-dropdown-label-empty':(label == null || label.length === 0)}\" *ngIf=\"!editable && (label != null)\" [pTooltip]=\"tooltip\" [tooltipPosition]=\"tooltipPosition\" [positionStyle]=\"tooltipPositionStyle\" [tooltipStyleClass]=\"tooltipStyleClass\">\n                <ng-container *ngIf=\"!selectedItemTemplate\">{{label||'empty'}}</ng-container>\n                <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: selectedOption}\"></ng-container>\n            </span>\n            <span [ngClass]=\"{'p-dropdown-label p-inputtext p-placeholder':true,'p-dropdown-label-empty': (placeholder == null || placeholder.length === 0)}\" *ngIf=\"!editable && (label == null)\">{{placeholder||'empty'}}</span>\n            <input #editableInput type=\"text\" [attr.maxlength]=\"maxlength\" class=\"p-dropdown-label p-inputtext\" *ngIf=\"editable\" [disabled]=\"disabled\" [attr.placeholder]=\"placeholder\"\n                aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\" (click)=\"onEditableInputClick()\" (input)=\"onEditableInputChange($event)\" (focus)=\"onEditableInputFocus($event)\" (blur)=\"onInputBlur($event)\">\n            <i class=\"p-dropdown-clear-icon pi pi-times\" (click)=\"clear($event)\" *ngIf=\"value != null && showClear && !disabled\"></i>\n            <div class=\"p-dropdown-trigger\" role=\"button\" aria-label=\"dropdown trigger\" aria-haspopup=\"listbox\" [attr.aria-expanded]=\"overlayVisible\">\n                <span class=\"p-dropdown-trigger-icon\" [ngClass]=\"dropdownIcon\"></span>\n            </div>\n            <div *ngIf=\"overlayVisible\" [ngClass]=\"'p-dropdown-panel p-component'\" (click)=\"onOverlayClick($event)\" [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <div class=\"p-dropdown-header\" *ngIf=\"filter\" (click)=\"$event.stopPropagation()\">\n                    <ng-container *ngIf=\"filterTemplate; else builtInFilterElement\">\n                        <ng-container *ngTemplateOutlet=\"filterTemplate; context: {options: filterOptions}\"></ng-container>\n                    </ng-container>\n                    <ng-template #builtInFilterElement>\n                        <div class=\"p-dropdown-filter-container\">\n                            <input #filter type=\"text\" autocomplete=\"off\" [value]=\"filterValue||''\" class=\"p-dropdown-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                            (keydown.enter)=\"$event.preventDefault()\" (keydown)=\"onKeydown($event, false)\" (input)=\"onFilterInputChange($event)\" [attr.aria-label]=\"ariaFilterLabel\" [attr.aria-activedescendant]=\"overlayVisible ? 'p-highlighted-option' : labelId\">\n                            <span class=\"p-dropdown-filter-icon pi pi-search\"></span>\n                        </div>\n                    </ng-template>\n                </div>\n                <div class=\"p-dropdown-items-wrapper\" [style.max-height]=\"virtualScroll ? 'auto' : (scrollHeight||'auto')\">\n                    <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"optionsToDisplay\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                        [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                        <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                        <ng-container *ngIf=\"loaderTemplate\">\n                            <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                                <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                    </p-scroller>\n                    <ng-container *ngIf=\"!virtualScroll\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: optionsToDisplay, options: {}}\"></ng-container>\n                    </ng-container>\n\n                    <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                        <ul #items [attr.id]=\"listId\" class=\"p-dropdown-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"listbox\">\n                            <ng-container *ngIf=\"group\">\n                                <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                    <li class=\"p-dropdown-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                        <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                        <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                    </li>\n                                    <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup), selectedOption: selectedOption}\"></ng-container>\n                                </ng-template>\n                            </ng-container>\n                            <ng-container *ngIf=\"!group\">\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items, selectedOption: selectedOption}\"></ng-container>\n                            </ng-container>\n                            <ng-template #itemslist let-options let-selectedOption=\"selectedOption\">\n                                <ng-template ngFor let-option let-i=\"index\" [ngForOf]=\"options\">\n                                    <p-dropdownItem [option]=\"option\" [selected]=\"selectedOption == option\" [label]=\"getOptionLabel(option)\" [disabled]=\"isOptionDisabled(option)\"\n                                                    (onClick)=\"onItemClick($event)\"\n                                                    [template]=\"itemTemplate\"></p-dropdownItem>\n                                </ng-template>\n                            </ng-template>\n                            <li *ngIf=\"filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyFilterTemplate && !emptyTemplate; else emptyFilter\">\n                                    {{emptyFilterMessageLabel}}\n                                </ng-container>\n                                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyFilterTemplate || emptyTemplate\"></ng-container>\n                            </li>\n                            <li *ngIf=\"!filterValue && isEmpty()\" class=\"p-dropdown-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ul>\n                    </ng-template>\n                </div>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('overlayAnimation', [\n                            transition(':enter', [\n                                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition(':leave', [\n                                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n                            ])\n                        ])\n                    ], host: {\n                        'class': 'p-element p-inputwrapper',\n                        '[class.p-inputwrapper-filled]': 'filled',\n                        '[class.p-inputwrapper-focus]': 'focused || overlayVisible'\n                    }, providers: [DROPDOWN_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, styles: [\".p-dropdown{display:inline-flex;cursor:pointer;position:relative;-webkit-user-select:none;user-select:none}.p-dropdown-clear-icon{position:absolute;top:50%;margin-top:-.5rem}.p-dropdown-trigger{display:flex;align-items:center;justify-content:center;flex-shrink:0}.p-dropdown-label{display:block;white-space:nowrap;overflow:hidden;flex:1 1 auto;width:1%;text-overflow:ellipsis;cursor:pointer}.p-dropdown-label-empty{overflow:hidden;visibility:hidden}input.p-dropdown-label{cursor:default}.p-dropdown .p-dropdown-panel{min-width:100%}.p-dropdown-panel{position:absolute;top:0;left:0}.p-dropdown-items-wrapper{overflow:auto}.p-dropdown-item{cursor:pointer;font-weight:400;white-space:nowrap;position:relative;overflow:hidden}.p-dropdown-items{margin:0;padding:0;list-style-type:none}.p-dropdown-filter{width:100%}.p-dropdown-filter-container{position:relative}.p-dropdown-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-fluid .p-dropdown{display:flex}.p-fluid .p-dropdown .p-dropdown-label{width:1%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i3.FilterService }, { type: i3.PrimeNGConfig }, { type: i3.OverlayService }]; }, propDecorators: { scrollHeight: [{\n                type: Input\n            }], filter: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], panelStyle: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], panelStyleClass: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], editable: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], filterPlaceholder: [{\n                type: Input\n            }], filterLocale: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], selectId: [{\n                type: Input\n            }], dataKey: [{\n                type: Input\n            }], filterBy: [{\n                type: Input\n            }], autofocus: [{\n                type: Input\n            }], resetFilterOnHide: [{\n                type: Input\n            }], dropdownIcon: [{\n                type: Input\n            }], optionLabel: [{\n                type: Input\n            }], optionValue: [{\n                type: Input\n            }], optionDisabled: [{\n                type: Input\n            }], optionGroupLabel: [{\n                type: Input\n            }], optionGroupChildren: [{\n                type: Input\n            }], autoDisplayFirst: [{\n                type: Input\n            }], group: [{\n                type: Input\n            }], showClear: [{\n                type: Input\n            }], emptyFilterMessage: [{\n                type: Input\n            }], emptyMessage: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], virtualScrollItemSize: [{\n                type: Input\n            }], virtualScrollOptions: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], ariaFilterLabel: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], filterMatchMode: [{\n                type: Input\n            }], maxlength: [{\n                type: Input\n            }], tooltip: [{\n                type: Input\n            }], tooltipPosition: [{\n                type: Input\n            }], tooltipPositionStyle: [{\n                type: Input\n            }], tooltipStyleClass: [{\n                type: Input\n            }], autofocusFilter: [{\n                type: Input\n            }], onChange: [{\n                type: Output\n            }], onFilter: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onClick: [{\n                type: Output\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], onClear: [{\n                type: Output\n            }], onLazyLoad: [{\n                type: Output\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }], filterViewChild: [{\n                type: ViewChild,\n                args: ['filter']\n            }], accessibleViewChild: [{\n                type: ViewChild,\n                args: ['in']\n            }], editableInputViewChild: [{\n                type: ViewChild,\n                args: ['editableInput']\n            }], itemsViewChild: [{\n                type: ViewChild,\n                args: ['items']\n            }], scroller: [{\n                type: ViewChild,\n                args: ['scroller']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], disabled: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], filterValue: [{\n                type: Input\n            }] } });\nclass DropdownModule {\n}\nDropdownModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDropdownModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownModule, declarations: [Dropdown, DropdownItem], imports: [CommonModule, SharedModule, TooltipModule, RippleModule, ScrollerModule], exports: [Dropdown, SharedModule, ScrollerModule] });\nDropdownModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownModule, imports: [CommonModule, SharedModule, TooltipModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: DropdownModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, TooltipModule, RippleModule, ScrollerModule],\n                    exports: [Dropdown, SharedModule, ScrollerModule],\n                    declarations: [Dropdown, DropdownItem]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DROPDOWN_VALUE_ACCESSOR, Dropdown, DropdownItem, DropdownModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,KAA9C,EAAqDC,MAArD,EAA6DC,uBAA7D,EAAsFC,iBAAtF,EAAyGC,SAAzG,EAAoHC,eAApH,EAAqIC,QAArI,QAAqJ,eAArJ;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,eAAT,EAA0BC,aAA1B,EAAyCC,YAAzC,QAA6D,aAA7D;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,SAASC,iBAAT,EAA4BC,WAA5B,EAAyCC,WAAzC,QAA4D,eAA5D;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,kBAApB;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;;;;IAkB+FhC,EAMnF,0B;IANmFA,EAM3D,U;IAN2DA,EAMzC,e;;;;mBANyCA,E;IAAAA,EAM3D,a;IAN2DA,EAM3D,2C;;;;;;IAN2DA,EAOnF,sB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAPmFA,EA+0B/E,2B;IA/0B+EA,EA+0BnC,U;IA/0BmCA,EA+0BjB,wB;;;;mBA/0BiBA,E;IAAAA,EA+0BnC,a;IA/0BmCA,EA+0BnC,2C;;;;;;IA/0BmCA,EAg1B/E,sB;;;;;;;;;;;;;IAh1B+EA,EA80BnF,8B;IA90BmFA,EA+0B/E,iF;IA/0B+EA,EAg1B/E,iF;IAh1B+EA,EAi1BnF,e;;;;mBAj1BmFA,E;IAAAA,EA80BzD,uBA90ByDA,EA80BzD,gP;IA90ByDA,EA80B7E,kC;IA90B6EA,EA+0BhE,a;IA/0BgEA,EA+0BhE,iD;IA/0BgEA,EAg1BhE,a;IAh1BgEA,EAg1BhE,wFAh1BgEA,EAg1BhE,iD;;;;;;;;;;;;;IAh1BgEA,EAk1BnF,8B;IAl1BmFA,EAk1BoG,U;IAl1BpGA,EAk1B4H,e;;;;mBAl1B5HA,E;IAAAA,EAk1B7E,uBAl1B6EA,EAk1B7E,yF;IAl1B6EA,EAk1BoG,a;IAl1BpGA,EAk1BoG,iD;;;;;;iBAl1BpGA,E;;IAAAA,EAm1BnF,mC;IAn1BmFA,EAo1BjB;MAp1BiBA,EAo1BjB;MAAA,gBAp1BiBA,EAo1BjB;MAAA,OAp1BiBA,EAo1BR,4CAAT;IAAA;MAp1BiBA,EAo1BjB;MAAA,gBAp1BiBA,EAo1BjB;MAAA,OAp1BiBA,EAo1ByB,mDAA1C;IAAA;MAp1BiBA,EAo1BjB;MAAA,gBAp1BiBA,EAo1BjB;MAAA,OAp1BiBA,EAo1BiE,kDAAlF;IAAA;MAp1BiBA,EAo1BjB;MAAA,gBAp1BiBA,EAo1BjB;MAAA,OAp1BiBA,EAo1BuG,yCAAxH;IAAA,E;IAp1BiBA,EAm1BnF,e;;;;mBAn1BmFA,E;IAAAA,EAm1BkC,wC;IAn1BlCA,EAm1BjD,sH;;;;;;iBAn1BiDA,E;;IAAAA,EAq1BnF,2B;IAr1BmFA,EAq1BtC;MAr1BsCA,EAq1BtC;MAAA,gBAr1BsCA,EAq1BtC;MAAA,OAr1BsCA,EAq1B7B,mCAAT;IAAA,E;IAr1BsCA,EAq1BkC,e;;;;;;IAr1BlCA,EA01B/E,sB;;;;;;IA11B+EA,EA61BvE,sB;;;;;;;;;;;;IA71BuEA,EA41B3E,2B;IA51B2EA,EA61BvE,sG;IA71BuEA,EA81B3E,wB;;;;oBA91B2EA,E;IAAAA,EA61BxD,a;IA71BwDA,EA61BxD,mFA71BwDA,EA61BxD,iD;;;;;;iBA71BwDA,E;;IAAAA,EAg2BvE,iD;IAh2BuEA,EAk2BnE;MAAA,OAAiB,uBAAjB;IAAA;MAl2BmEA,EAk2BnE;MAAA,gBAl2BmEA,EAk2BnE;MAAA,OAl2BmEA,EAk2Bd,uCAAkB,KAAlB,EAArD;IAAA;MAl2BmEA,EAk2BnE;MAAA,gBAl2BmEA,EAk2BnE;MAAA,OAl2BmEA,EAk2BqB,iDAAxF;IAAA,E;IAl2BmEA,EAi2BnE,e;IAj2BmEA,EAm2BnE,yB;IAn2BmEA,EAo2BvE,e;;;;oBAp2BuEA,E;IAAAA,EAi2BrB,a;IAj2BqBA,EAi2BrB,+C;IAj2BqBA,EAi2BuD,yL;;;;;;IAj2BvDA,EA21B/E,6B;IA31B+EA,EA21BjC;MAAA,OAAS,wBAAT;IAAA,E;IA31BiCA,EA41B3E,uF;IA51B2EA,EA+1B3E,4FA/1B2EA,EA+1B3E,wB;IA/1B2EA,EAs2B/E,e;;;;iBAt2B+EA,E;;oBAAAA,E;IAAAA,EA41B5D,a;IA51B4DA,EA41B5D,6D;;;;;;IA51B4DA,EA22BnE,sB;;;;;;;;;;;;;IA32BmEA,EA22BnE,4G;;;;;;IA32BmEA,E;;iBAAAA,E;;IAAAA,EA22BpD,iEA32BoDA,EA22BpD,0D;;;;;;IA32BoDA,EA+2B/D,sB;;;;;;IA/2B+DA,EA+2B/D,2H;;;;;oBA/2B+DA,E;IAAAA,EA+2BhD,mFA/2BgDA,EA+2BhD,+C;;;;;;IA/2BgDA,EA62BvE,2B;IA72BuEA,EA82BnE,2G;IA92BmEA,EAi3BvE,wB;;;;;;iBAj3BuEA,E;;IAAAA,EAw2B3E,wC;IAx2B2EA,EAy2BzD;MAz2ByDA,EAy2BzD;MAAA,gBAz2ByDA,EAy2BzD;MAAA,OAz2ByDA,EAy2B3C,6CAAd;IAAA,E;IAz2ByDA,EA02BvE,4F;IA12BuEA,EA62BvE,8F;IA72BuEA,EAk3B3E,e;;;;oBAl3B2EA,E;IAAAA,EAw2BJ,YAx2BIA,EAw2BJ,+C;IAx2BIA,EAw2B/B,+L;IAx2B+BA,EA62BxD,a;IA72BwDA,EA62BxD,2C;;;;;;IA72BwDA,EAo3BvE,sB;;;;;;;;;;IAp3BuEA,EAm3B3E,2B;IAn3B2EA,EAo3BvE,gG;IAp3BuEA,EAq3B3E,wB;;;;IAr3B2EA,E;;iBAAAA,E;;oBAAAA,E;IAAAA,EAo3BxD,a;IAp3BwDA,EAo3BxD,iEAp3BwDA,EAo3BxD,oDAp3BwDA,EAo3BxD,2B;;;;;;IAp3BwDA,EA43BvD,0B;IA53BuDA,EA43B1B,U;IA53B0BA,EA43BgB,e;;;;yBA53BhBA,E;oBAAAA,E;IAAAA,EA43B1B,a;IA53B0BA,EA43B1B,wE;;;;;;IA53B0BA,EA63BvD,sB;;;;;;IA73BuDA,EA+3B3D,sB;;;;;;;;;;;;;IA/3B2DA,EA23B3D,4B;IA33B2DA,EA43BvD,4G;IA53BuDA,EA63BvD,4H;IA73BuDA,EA83B3D,e;IA93B2DA,EA+3B3D,4H;;;;;gCA/3B2DA,E;;iBAAAA,E;;oBAAAA,E;IAAAA,EA23BzB,uBA33ByBA,EA23BzB,8D;IA33ByBA,EA43BhD,a;IA53BgDA,EA43BhD,2C;IA53BgDA,EA63BxC,a;IA73BwCA,EA63BxC,kFA73BwCA,EA63BxC,uC;IA73BwCA,EA+3B5C,a;IA/3B4CA,EA+3B5C,iEA/3B4CA,EA+3B5C,iG;;;;;;IA/3B4CA,EAy3BnE,2B;IAz3BmEA,EA03B/D,6G;IA13B+DA,EAi4BnE,wB;;;;sBAj4BmEA,E;IAAAA,EA03B/B,a;IA13B+BA,EA03B/B,iC;;;;;;IA13B+BA,EAm4B/D,sB;;;;;;IAn4B+DA,EAk4BnE,2B;IAl4BmEA,EAm4B/D,8G;IAn4B+DA,EAo4BnE,wB;;;;sBAp4BmEA,E;;iBAAAA,E;;oBAAAA,E;IAAAA,EAm4BhD,a;IAn4BgDA,EAm4BhD,iEAn4BgDA,EAm4BhD,6D;;;;;;iBAn4BgDA,E;;IAAAA,EAu4B3D,wC;IAv4B2DA,EAw4B3C;MAx4B2CA,EAw4B3C;MAAA,gBAx4B2CA,EAw4B3C;MAAA,OAx4B2CA,EAw4BhC,yCAAX;IAAA,E;IAx4B2CA,EAy4BjB,e;;;;;+BAz4BiBA,E;oBAAAA,E;IAAAA,EAu4B3C,gN;;;;;;IAv4B2CA,EAs4B/D,2G;;;;;IAt4B+DA,EAs4BnB,mC;;;;;;IAt4BmBA,EA64B/D,2B;IA74B+DA,EA84B3D,U;IA94B2DA,EA+4B/D,wB;;;;oBA/4B+DA,E;IAAAA,EA84B3D,a;IA94B2DA,EA84B3D,8D;;;;;;IA94B2DA,EAg5B/D,gC;;;;;;IAh5B+DA,EA44BnE,4B;IA54BmEA,EA64B/D,oG;IA74B+DA,EAg5B/D,oG;IAh5B+DA,EAi5BnE,e;;;;gCAj5BmEA,E;oBAAAA,E;IAAAA,EA44BG,uBA54BHA,EA44BG,8D;IA54BHA,EA64BhD,a;IA74BgDA,EA64BhD,4G;IA74BgDA,EAg5BnC,a;IAh5BmCA,EAg5BnC,qF;;;;;;IAh5BmCA,EAm5B/D,2B;IAn5B+DA,EAo5B3D,U;IAp5B2DA,EAq5B/D,wB;;;;oBAr5B+DA,E;IAAAA,EAo5B3D,a;IAp5B2DA,EAo5B3D,wD;;;;;;IAp5B2DA,EAs5B/D,gC;;;;;;IAt5B+DA,EAk5BnE,4B;IAl5BmEA,EAm5B/D,oG;IAn5B+DA,EAs5B/D,oG;IAt5B+DA,EAu5BnE,e;;;;gCAv5BmEA,E;oBAAAA,E;IAAAA,EAk5BI,uBAl5BJA,EAk5BI,8D;IAl5BJA,EAm5BhD,a;IAn5BgDA,EAm5BhD,sE;IAn5BgDA,EAs5BzC,a;IAt5ByCA,EAs5BzC,sD;;;;;;IAt5ByCA,EAw3BvE,gC;IAx3BuEA,EAy3BnE,+F;IAz3BmEA,EAk4BnE,+F;IAl4BmEA,EAq4BnE,oGAr4BmEA,EAq4BnE,wB;IAr4BmEA,EA44BnE,2E;IA54BmEA,EAk5BnE,2E;IAl5BmEA,EAw5BvE,e;;;;;oBAx5BuEA,E;IAAAA,EAw3B8B,6C;IAx3B9BA,EAw3BhB,6D;IAx3BgBA,EAw3B5D,kC;IAx3B4DA,EAy3BpD,a;IAz3BoDA,EAy3BpD,kC;IAz3BoDA,EAk4BpD,a;IAl4BoDA,EAk4BpD,mC;IAl4BoDA,EA44B9D,a;IA54B8DA,EA44B9D,6D;IA54B8DA,EAk5B9D,a;IAl5B8DA,EAk5B9D,8D;;;;;;IAl5B8DA,EA25B/E,sB;;;;;;;;;;;;;;;;;;;;iBA35B+EA,E;;IAAAA,EAy1BnF,4B;IAz1BmFA,EAy1BZ;MAz1BYA,EAy1BZ;MAAA,gBAz1BYA,EAy1BZ;MAAA,OAz1BYA,EAy1BH,4CAAT;IAAA;MAz1BYA,EAy1BZ;MAAA,gBAz1BYA,EAy1BZ;MAAA,OAz1BYA,EAy1B6L,qDAAzM;IAAA;MAz1BYA,EAy1BZ;MAAA,gBAz1BYA,EAy1BZ;MAAA,OAz1BYA,EAy1BwP,mDAApQ;IAAA,E;IAz1BYA,EA01B/E,iF;IA11B+EA,EA21B/E,+D;IA31B+EA,EAu2B/E,6B;IAv2B+EA,EAw2B3E,8E;IAx2B2EA,EAm3B3E,iF;IAn3B2EA,EAu3B3E,sFAv3B2EA,EAu3B3E,wB;IAv3B2EA,EA05B/E,e;IA15B+EA,EA25B/E,iF;IA35B+EA,EA45BnF,e;;;;mBA55BmFA,E;IAAAA,EAy1B8S,mC;IAz1B9SA,EAy1BvD,4EAz1BuDA,EAy1BvD,2BAz1BuDA,EAy1BvD,sH;IAz1BuDA,EA01BhE,a;IA11BgEA,EA01BhE,sD;IA11BgEA,EA21B/C,a;IA31B+CA,EA21B/C,kC;IA31B+CA,EAu2BzC,a;IAv2ByCA,EAu2BzC,yF;IAv2ByCA,EAw2B9D,a;IAx2B8DA,EAw2B9D,yC;IAx2B8DA,EAm3B5D,a;IAn3B4DA,EAm3B5D,0C;IAn3B4DA,EA25BhE,a;IA35BgEA,EA25BhE,sD;;;;;;;;;;;;;;AA36B/B,MAAMiC,uBAAuB,GAAG;EAC5BC,OAAO,EAAER,iBADmB;EAE5BS,WAAW,EAAElC,UAAU,CAAC,MAAMmC,QAAP,CAFK;EAG5BC,KAAK,EAAE;AAHqB,CAAhC;;AAKA,MAAMC,YAAN,CAAmB;EACfC,WAAW,GAAG;IACV,KAAKC,OAAL,GAAe,IAAItC,YAAJ,EAAf;EACH;;EACDuC,aAAa,CAACC,KAAD,EAAQ;IACjB,KAAKF,OAAL,CAAaG,IAAb,CAAkB;MACdC,aAAa,EAAEF,KADD;MAEdG,MAAM,EAAE,KAAKA;IAFC,CAAlB;EAIH;;AATc;;AAWnBP,YAAY,CAACQ,IAAb;EAAA,iBAAyGR,YAAzG;AAAA;;AACAA,YAAY,CAACS,IAAb,kBAD+F/C,EAC/F;EAAA,MAA6FsC,YAA7F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FtC,EAEvF,2BADR;MAD+FA,EAEnF;QAAA,OAAS,yBAAT;MAAA,EADZ;MAD+FA,EAMnF,6DALZ;MAD+FA,EAOnF,6EANZ;MAD+FA,EAQvF,eAPR;IAAA;;IAAA;MAD+FA,EAInF,uBAJmFA,EAInF,4GAJmFA,EAInF,sDAHZ;MAD+FA,EAGnF,oEAFZ;MAD+FA,EAM5E,aALnB;MAD+FA,EAM5E,kCALnB;MAD+FA,EAOpE,aAN3B;MAD+FA,EAOpE,yEAPoEA,EAOpE,sCAN3B;IAAA;EAAA;EAAA,eAQiEe,EAAE,CAACiC,OARpE,EAQ+JjC,EAAE,CAACkC,IARlK,EAQmQlC,EAAE,CAACmC,gBARtQ,EAQ0anC,EAAE,CAACoC,OAR7a,EAQ+fpB,EAAE,CAACqB,MARlgB;EAAA;AAAA;;AASA;EAAA,mDAV+FpD,EAU/F,mBAA2FsC,YAA3F,EAAqH,CAAC;IAC1Ge,IAAI,EAAElD,SADoG;IAE1GmD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAVmB;MAWCC,IAAI,EAAE;QACF,SAAS;MADP;IAXP,CAAD;EAFoG,CAAD,CAArH,QAiB4B;IAAEZ,MAAM,EAAE,CAAC;MACvBQ,IAAI,EAAEjD;IADiB,CAAD,CAAV;IAEZsD,QAAQ,EAAE,CAAC;MACXL,IAAI,EAAEjD;IADK,CAAD,CAFE;IAIZuD,KAAK,EAAE,CAAC;MACRN,IAAI,EAAEjD;IADE,CAAD,CAJK;IAMZwD,QAAQ,EAAE,CAAC;MACXP,IAAI,EAAEjD;IADK,CAAD,CANE;IAQZyD,OAAO,EAAE,CAAC;MACVR,IAAI,EAAEjD;IADI,CAAD,CARG;IAUZ0D,QAAQ,EAAE,CAAC;MACXT,IAAI,EAAEjD;IADK,CAAD,CAVE;IAYZoD,QAAQ,EAAE,CAAC;MACXH,IAAI,EAAEjD;IADK,CAAD,CAZE;IAcZoC,OAAO,EAAE,CAAC;MACVa,IAAI,EAAEhD;IADI,CAAD;EAdG,CAjB5B;AAAA;;AAkCA,MAAM+B,QAAN,CAAe;EACXG,WAAW,CAACwB,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,IAAnB,EAAyBC,aAAzB,EAAwCC,MAAxC,EAAgDC,cAAhD,EAAgE;IACvE,KAAKN,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,aAAL,GAAqBA,aAArB;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,YAAL,GAAoB,OAApB;IACA,KAAKC,iBAAL,GAAyB,KAAzB;IACA,KAAKC,YAAL,GAAoB,oBAApB;IACA,KAAKC,mBAAL,GAA2B,OAA3B;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,kBAAL,GAA0B,EAA1B;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,IAAL,GAAY,KAAZ;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,eAAL,GAAuB,UAAvB;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,eAAL,GAAuB,OAAvB;IACA,KAAKC,oBAAL,GAA4B,UAA5B;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA,KAAKC,QAAL,GAAgB,IAAIrF,YAAJ,EAAhB;IACA,KAAKsF,QAAL,GAAgB,IAAItF,YAAJ,EAAhB;IACA,KAAKuF,OAAL,GAAe,IAAIvF,YAAJ,EAAf;IACA,KAAKwF,MAAL,GAAc,IAAIxF,YAAJ,EAAd;IACA,KAAKsC,OAAL,GAAe,IAAItC,YAAJ,EAAf;IACA,KAAKyF,MAAL,GAAc,IAAIzF,YAAJ,EAAd;IACA,KAAK0F,MAAL,GAAc,IAAI1F,YAAJ,EAAd;IACA,KAAK2F,OAAL,GAAe,IAAI3F,YAAJ,EAAf;IACA,KAAK4F,UAAL,GAAkB,IAAI5F,YAAJ,EAAlB;;IACA,KAAK6F,aAAL,GAAqB,MAAM,CAAG,CAA9B;;IACA,KAAKC,cAAL,GAAsB,MAAM,CAAG,CAA/B;;IACA,KAAKC,EAAL,GAAU1E,iBAAiB,EAA3B;EACH;;EACW,IAARqC,QAAQ,GAAG;IACX,OAAO,KAAKsC,SAAZ;EACH;;EACW,IAARtC,QAAQ,CAACsC,SAAD,EAAY;IACpB,IAAIA,SAAJ,EAAe;MACX,KAAKC,OAAL,GAAe,KAAf;MACA,IAAI,KAAKC,cAAT,EACI,KAAKC,IAAL;IACP;;IACD,KAAKH,SAAL,GAAiBA,SAAjB;;IACA,IAAI,CAAC,KAAKjC,EAAL,CAAQqC,SAAb,EAAwB;MACpB,KAAKrC,EAAL,CAAQsC,aAAR;IACH;EACJ;;EACW,IAARzC,QAAQ,GAAG;IACX,OAAO,KAAK0C,SAAZ;EACH;;EACW,IAAR1C,QAAQ,CAAC2C,GAAD,EAAM;IACd,KAAKD,SAAL,GAAiBC,GAAjB;IACAC,OAAO,CAACC,IAAR,CAAa,kFAAb;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,MAAL;UACI,KAAKC,YAAL,GAAoBF,IAAI,CAACvD,QAAzB;UACA;;QACJ,KAAK,cAAL;UACI,KAAK0D,oBAAL,GAA4BH,IAAI,CAACvD,QAAjC;UACA;;QACJ,KAAK,QAAL;UACI,KAAK2D,cAAL,GAAsBJ,IAAI,CAACvD,QAA3B;UACA;;QACJ,KAAK,QAAL;UACI,KAAK4D,cAAL,GAAsBL,IAAI,CAACvD,QAA3B;UACA;;QACJ,KAAK,QAAL;UACI,KAAK6D,cAAL,GAAsBN,IAAI,CAACvD,QAA3B;UACA;;QACJ,KAAK,aAAL;UACI,KAAK8D,mBAAL,GAA2BP,IAAI,CAACvD,QAAhC;UACA;;QACJ,KAAK,OAAL;UACI,KAAK+D,aAAL,GAAqBR,IAAI,CAACvD,QAA1B;UACA;;QACJ,KAAK,OAAL;UACI,KAAKgE,aAAL,GAAqBT,IAAI,CAACvD,QAA1B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKiE,cAAL,GAAsBV,IAAI,CAACvD,QAA3B;UACA;;QACJ;UACI,KAAKyD,YAAL,GAAoBF,IAAI,CAACvD,QAAzB;UACA;MA9BR;IAgCH,CAjCD;EAkCH;;EACDkE,QAAQ,GAAG;IACP,KAAKC,gBAAL,GAAwB,KAAKC,OAA7B;IACA,KAAKC,oBAAL,CAA0B,IAA1B;IACA,KAAKC,OAAL,GAAe,KAAK7B,EAAL,GAAU,QAAzB;IACA,KAAK8B,MAAL,GAAc,KAAK9B,EAAL,GAAU,OAAxB;;IACA,IAAI,KAAK+B,QAAT,EAAmB;MACf,KAAKC,aAAL,GAAqB;QACjBC,MAAM,EAAGC,KAAD,IAAW,KAAKC,mBAAL,CAAyBD,KAAzB,CADF;QAEjBE,KAAK,EAAE,MAAM,KAAKC,WAAL;MAFI,CAArB;IAIH;EACJ;;EACU,IAAPV,OAAO,GAAG;IACV,OAAO,KAAKW,QAAZ;EACH;;EACU,IAAPX,OAAO,CAACnB,GAAD,EAAM;IACb,KAAK8B,QAAL,GAAgB9B,GAAhB;IACA,KAAKkB,gBAAL,GAAwB,KAAKY,QAA7B;IACA,KAAKV,oBAAL,CAA0B,KAAKM,KAA/B;IACA,KAAKK,cAAL,GAAsB,KAAKC,UAAL,CAAgB,KAAKN,KAArB,EAA4B,KAAKR,gBAAjC,CAAtB;;IACA,IAAI,CAAC,KAAKa,cAAN,IAAwBhH,WAAW,CAACkH,UAAZ,CAAuB,KAAKP,KAA5B,CAA5B,EAAgE;MAC5D,KAAKA,KAAL,GAAa,IAAb;MACA,KAAKpC,aAAL,CAAmB,KAAKoC,KAAxB;IACH;;IACD,KAAKQ,cAAL,GAAsB,IAAtB;;IACA,IAAI,KAAKC,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,MAA3C,EAAmD;MAC/C,KAAKC,cAAL;IACH;EACJ;;EACc,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKH,YAAZ;EACH;;EACc,IAAXG,WAAW,CAACtC,GAAD,EAAM;IACjB,KAAKmC,YAAL,GAAoBnC,GAApB;IACA,KAAKqC,cAAL;EACH;;EACDE,eAAe,GAAG;IACd,IAAI,KAAKC,QAAT,EAAmB;MACf,KAAKC,mBAAL;IACH;EACJ;;EACQ,IAALvF,KAAK,GAAG;IACR,OAAO,KAAK6E,cAAL,GAAsB,KAAKW,cAAL,CAAoB,KAAKX,cAAzB,CAAtB,GAAiE,IAAxE;EACH;;EACoB,IAAjBY,iBAAiB,GAAG;IACpB,OAAO,KAAKxE,YAAL,IAAqB,KAAKR,MAAL,CAAYiF,cAAZ,CAA2BnI,eAAe,CAACoI,aAA3C,CAA5B;EACH;;EAC0B,IAAvBC,uBAAuB,GAAG;IAC1B,OAAO,KAAK5E,kBAAL,IAA2B,KAAKP,MAAL,CAAYiF,cAAZ,CAA2BnI,eAAe,CAACsI,oBAA3C,CAAlC;EACH;;EACS,IAANC,MAAM,GAAG;IACT,OAAO,KAAKtB,KAAL,IAAc,KAAKA,KAAL,IAAc,IAA5B,IAAoC,KAAKA,KAAL,IAAcuB,SAAzD;EACH;;EACDR,mBAAmB,GAAG;IAClB,IAAI,KAAKS,sBAAL,IAA+B,KAAKA,sBAAL,CAA4BC,aAA/D,EAA8E;MAC1E,KAAKD,sBAAL,CAA4BC,aAA5B,CAA0CzB,KAA1C,GAAmD,KAAKK,cAAL,GAAsB,KAAKW,cAAL,CAAoB,KAAKX,cAAzB,CAAtB,GAAiE,KAAKL,KAAL,IAAc,EAAlI;IACH;EACJ;;EACDgB,cAAc,CAACtG,MAAD,EAAS;IACnB,OAAO,KAAKgH,WAAL,GAAmBrI,WAAW,CAACsI,gBAAZ,CAA6BjH,MAA7B,EAAqC,KAAKgH,WAA1C,CAAnB,GAA6EhH,MAAM,IAAIA,MAAM,CAACc,KAAP,KAAiB+F,SAA3B,GAAuC7G,MAAM,CAACc,KAA9C,GAAsDd,MAA1I;EACH;;EACDkH,cAAc,CAAClH,MAAD,EAAS;IACnB,OAAO,KAAKmH,WAAL,GAAmBxI,WAAW,CAACsI,gBAAZ,CAA6BjH,MAA7B,EAAqC,KAAKmH,WAA1C,CAAnB,GAA6E,CAAC,KAAKH,WAAN,IAAsBhH,MAAM,IAAIA,MAAM,CAACsF,KAAP,KAAiBuB,SAAjD,GAA8D7G,MAAM,CAACsF,KAArE,GAA6EtF,MAAjK;EACH;;EACDoH,gBAAgB,CAACpH,MAAD,EAAS;IACrB,OAAO,KAAKqH,cAAL,GAAsB1I,WAAW,CAACsI,gBAAZ,CAA6BjH,MAA7B,EAAqC,KAAKqH,cAA1C,CAAtB,GAAmFrH,MAAM,IAAIA,MAAM,CAACe,QAAP,KAAoB8F,SAA9B,GAA0C7G,MAAM,CAACe,QAAjD,GAA4D,KAAtJ;EACH;;EACDuG,mBAAmB,CAACC,WAAD,EAAc;IAC7B,OAAO,KAAKC,gBAAL,GAAwB7I,WAAW,CAACsI,gBAAZ,CAA6BM,WAA7B,EAA0C,KAAKC,gBAA/C,CAAxB,GAA4FD,WAAW,IAAIA,WAAW,CAACzG,KAAZ,KAAsB+F,SAArC,GAAiDU,WAAW,CAACzG,KAA7D,GAAqEyG,WAAxK;EACH;;EACDE,sBAAsB,CAACF,WAAD,EAAc;IAChC,OAAO,KAAK3F,mBAAL,GAA2BjD,WAAW,CAACsI,gBAAZ,CAA6BM,WAA7B,EAA0C,KAAK3F,mBAA/C,CAA3B,GAAiG2F,WAAW,CAACG,KAApH;EACH;;EACDC,WAAW,CAAC9H,KAAD,EAAQ;IACf,MAAMG,MAAM,GAAGH,KAAK,CAACG,MAArB;;IACA,IAAI,CAAC,KAAKoH,gBAAL,CAAsBpH,MAAtB,CAAL,EAAoC;MAChC,KAAK4H,UAAL,CAAgB/H,KAAK,CAACE,aAAtB,EAAqCC,MAArC;MACA,KAAK6H,mBAAL,CAAyBd,aAAzB,CAAuCe,KAAvC,CAA6C;QAAEC,aAAa,EAAE;MAAjB,CAA7C;IACH;;IACDC,UAAU,CAAC,MAAM;MACb,KAAKxE,IAAL;IACH,CAFS,EAEP,GAFO,CAAV;EAGH;;EACDoE,UAAU,CAAC/H,KAAD,EAAQG,MAAR,EAAgB;IACtB,IAAI,KAAK2F,cAAL,IAAuB3F,MAA3B,EAAmC;MAC/B,KAAK2F,cAAL,GAAsB3F,MAAtB;MACA,KAAKsF,KAAL,GAAa,KAAK4B,cAAL,CAAoBlH,MAApB,CAAb;MACA,KAAKkD,aAAL,CAAmB,KAAKoC,KAAxB;MACA,KAAKe,mBAAL;MACA,KAAK3D,QAAL,CAAc5C,IAAd,CAAmB;QACfC,aAAa,EAAEF,KADA;QAEfyF,KAAK,EAAE,KAAKA;MAFG,CAAnB;IAIH;EACJ;;EACD2C,kBAAkB,GAAG;IACjB,IAAI,KAAKnC,cAAL,IAAuB,KAAKvC,cAAhC,EAAgD;MAC5C,KAAKuC,cAAL,GAAsB,KAAtB;MACA,KAAKzE,IAAL,CAAU6G,iBAAV,CAA4B,MAAM;QAC9BF,UAAU,CAAC,MAAM;UACb,KAAKG,YAAL;QACH,CAFS,EAEP,CAFO,CAAV;MAGH,CAJD;IAKH;;IACD,IAAI,KAAKC,qBAAL,IAA8B,KAAKC,YAAvC,EAAqD;MACjD,IAAIC,YAAY,GAAG9J,UAAU,CAAC+J,UAAX,CAAsB,KAAKC,OAA3B,EAAoC,gBAApC,CAAnB;;MACA,IAAIF,YAAJ,EAAkB;QACd9J,UAAU,CAACiK,YAAX,CAAwB,KAAKJ,YAA7B,EAA2C7J,UAAU,CAAC+J,UAAX,CAAsB,KAAKC,OAA3B,EAAoC,gBAApC,CAA3C;MACH;;MACD,KAAKJ,qBAAL,GAA6B,KAA7B;IACH;EACJ;;EACDM,UAAU,CAACpD,KAAD,EAAQ;IACd,IAAI,KAAKD,MAAT,EAAiB;MACb,KAAKI,WAAL;IACH;;IACD,KAAKH,KAAL,GAAaA,KAAb;IACA,KAAKN,oBAAL,CAA0BM,KAA1B;IACA,KAAKe,mBAAL;IACA,KAAKjF,EAAL,CAAQuH,YAAR;EACH;;EACDlD,WAAW,GAAG;IACV,KAAKM,YAAL,GAAoB,IAApB;;IACA,IAAI,KAAK6C,eAAL,IAAwB,KAAKA,eAAL,CAAqB7B,aAAjD,EAAgE;MAC5D,KAAK6B,eAAL,CAAqB7B,aAArB,CAAmCzB,KAAnC,GAA2C,EAA3C;IACH;;IACD,KAAKR,gBAAL,GAAwB,KAAKC,OAA7B;EACH;;EACDC,oBAAoB,CAACpB,GAAD,EAAM;IACtB,KAAK+B,cAAL,GAAsB,KAAKC,UAAL,CAAgBhC,GAAhB,EAAqB,KAAKkB,gBAA1B,CAAtB;;IACA,IAAI,KAAKjD,gBAAL,IAAyB,CAAC,KAAKgH,WAA/B,IAA8C,CAAC,KAAKlD,cAApD,IAAsE,KAAKb,gBAA3E,IAA+F,KAAKA,gBAAL,CAAsBkB,MAArH,IAA+H,CAAC,KAAKI,QAAzI,EAAmJ;MAC/I,IAAI,KAAK0C,KAAT,EAAgB;QACZ,KAAKnD,cAAL,GAAsB,KAAKb,gBAAL,CAAsB,CAAtB,EAAyB4C,KAAzB,CAA+B,CAA/B,CAAtB;MACH,CAFD,MAGK;QACD,KAAK/B,cAAL,GAAsB,KAAKb,gBAAL,CAAsB,CAAtB,CAAtB;MACH;;MACD,KAAKQ,KAAL,GAAa,KAAK4B,cAAL,CAAoB,KAAKvB,cAAzB,CAAb;MACA,KAAKzC,aAAL,CAAmB,KAAKoC,KAAxB;IACH;;IACD,KAAK8C,qBAAL,GAA6B,IAA7B;EACH;;EACDW,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAK9F,aAAL,GAAqB8F,EAArB;EACH;;EACDC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAK7F,cAAL,GAAsB6F,EAAtB;EACH;;EACDE,gBAAgB,CAACtF,GAAD,EAAM;IAClB,KAAK7C,QAAL,GAAgB6C,GAAhB;IACA,KAAKxC,EAAL,CAAQuH,YAAR;EACH;;EACDQ,YAAY,CAACtJ,KAAD,EAAQ;IAChB,IAAI,KAAKkB,QAAL,IAAiB,KAAKqI,QAAtB,IAAkC,KAAKC,YAAL,CAAkBxJ,KAAlB,CAAtC,EAAgE;MAC5D;IACH;;IACD,KAAKF,OAAL,CAAaG,IAAb,CAAkBD,KAAlB;IACA,KAAKgI,mBAAL,CAAyBd,aAAzB,CAAuCe,KAAvC,CAA6C;MAAEC,aAAa,EAAE;IAAjB,CAA7C;IACA,IAAI,KAAKxE,cAAT,EACI,KAAKC,IAAL,GADJ,KAGI,KAAK8F,IAAL;IACJ,KAAKlI,EAAL,CAAQsC,aAAR;EACH;;EACD6F,cAAc,CAAC1J,KAAD,EAAQ;IAClB,KAAK2B,cAAL,CAAoBgI,GAApB,CAAwB;MACpBzJ,aAAa,EAAEF,KADK;MAEpB4J,MAAM,EAAE,KAAKvI,EAAL,CAAQ6F;IAFI,CAAxB;EAIH;;EACDsC,YAAY,CAACxJ,KAAD,EAAQ;IAChB,OAAOrB,UAAU,CAACkL,QAAX,CAAoB7J,KAAK,CAAC4J,MAA1B,EAAkC,uBAAlC,KACH5J,KAAK,CAAC4J,MAAN,CAAaE,UAAb,CAAwB,KAAK9B,mBAAL,CAAyBd,aAAjD,CADG,IAEF,KAAKD,sBAAL,IAA+BjH,KAAK,CAAC4J,MAAN,CAAaE,UAAb,CAAwB,KAAK7C,sBAAL,CAA4BC,aAApD,CAFpC;EAGH;;EACD6C,gBAAgB,CAAC/J,KAAD,EAAQ;IACpB,OAAO,EAAE,KAAKqB,EAAL,CAAQ6F,aAAR,CAAsB4C,UAAtB,CAAiC9J,KAAK,CAAC4J,MAAvC,KAAkD,KAAKvI,EAAL,CAAQ6F,aAAR,CAAsB8C,QAAtB,CAA+BhK,KAAK,CAAC4J,MAArC,CAAlD,IAAmG,KAAKjB,OAAL,IAAgB,KAAKA,OAAL,CAAaqB,QAAb,CAAsBhK,KAAK,CAAC4J,MAA5B,CAArH,CAAP;EACH;;EACDK,OAAO,GAAG;IACN,OAAO,CAAC,KAAKhF,gBAAN,IAA2B,KAAKA,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBkB,MAAtB,KAAiC,CAA5F;EACH;;EACD+D,oBAAoB,GAAG;IACnB,KAAKC,yBAAL;EACH;;EACDC,oBAAoB,CAACpK,KAAD,EAAQ;IACxB,KAAKyD,OAAL,GAAe,IAAf;IACA,KAAKE,IAAL;IACA,KAAKZ,OAAL,CAAa9C,IAAb,CAAkBD,KAAlB;EACH;;EACDqK,qBAAqB,CAACrK,KAAD,EAAQ;IACzB,KAAKyF,KAAL,GAAazF,KAAK,CAAC4J,MAAN,CAAanE,KAA1B;IACA,KAAKN,oBAAL,CAA0B,KAAKM,KAA/B;IACA,KAAKpC,aAAL,CAAmB,KAAKoC,KAAxB;IACA,KAAK5C,QAAL,CAAc5C,IAAd,CAAmB;MACfC,aAAa,EAAEF,KADA;MAEfyF,KAAK,EAAE,KAAKA;IAFG,CAAnB;EAIH;;EACDgE,IAAI,GAAG;IACH,KAAK/F,cAAL,GAAsB,IAAtB;IACA,KAAK4G,sBAAL,GAA8B,IAA9B;IACA,KAAK/I,EAAL,CAAQuH,YAAR;EACH;;EACDyB,uBAAuB,CAACvK,KAAD,EAAQ;IAC3B,QAAQA,KAAK,CAACwK,OAAd;MACI,KAAK,SAAL;QACI,KAAK7B,OAAL,GAAe3I,KAAK,CAACyK,OAArB;QACA,KAAKjC,YAAL,GAAoB7J,UAAU,CAAC+J,UAAX,CAAsB,KAAKC,OAA3B,EAAoC,KAAK+B,aAAL,GAAqB,aAArB,GAAqC,2BAAzE,CAApB;QACA,KAAKA,aAAL,IAAsB,KAAKC,QAAL,CAAcC,YAAd,CAA2B,KAAKC,cAAL,CAAoB3D,aAA/C,CAAtB;QACA,KAAK4D,aAAL;;QACA,IAAI,KAAK1I,UAAT,EAAqB;UACjBrD,WAAW,CAACgM,GAAZ,CAAgB,SAAhB,EAA2B,KAAKpC,OAAhC,EAAyC,KAAKtG,UAAL,GAAkB,KAAKX,MAAL,CAAYsJ,MAAZ,CAAmBrC,OAA9E;QACH;;QACD,KAAKL,YAAL;QACA,KAAK6B,yBAAL;QACA,KAAKc,0BAAL;QACA,KAAKC,kBAAL;;QACA,IAAI,KAAKhG,OAAL,IAAgB,KAAKA,OAAL,CAAaiB,MAAjC,EAAyC;UACrC,IAAI,KAAKuE,aAAT,EAAwB;YACpB,MAAMS,aAAa,GAAG,KAAKrF,cAAL,GAAsB,KAAKsF,eAAL,CAAqB,KAAK/D,cAAL,CAAoB,KAAKvB,cAAzB,CAArB,EAA+D,KAAKb,gBAApE,CAAtB,GAA8G,CAAC,CAArI;;YACA,IAAIkG,aAAa,KAAK,CAAC,CAAvB,EAA0B;cACtB,KAAKR,QAAL,CAAcU,aAAd,CAA4BF,aAA5B;YACH;UACJ,CALD,MAMK;YACD,IAAIG,gBAAgB,GAAG3M,UAAU,CAAC+J,UAAX,CAAsB,KAAKF,YAA3B,EAAyC,8BAAzC,CAAvB;;YACA,IAAI8C,gBAAJ,EAAsB;cAClBA,gBAAgB,CAACC,cAAjB,CAAgC;gBAAEC,KAAK,EAAE,SAAT;gBAAoBC,MAAM,EAAE;cAA5B,CAAhC;YACH;UACJ;QACJ;;QACD,IAAI,KAAK1C,eAAL,IAAwB,KAAKA,eAAL,CAAqB7B,aAAjD,EAAgE;UAC5D,KAAKwE,mBAAL,GAA2B,IAA3B;;UACA,IAAI,KAAK9I,eAAT,EAA0B;YACtB,KAAKmG,eAAL,CAAqB7B,aAArB,CAAmCe,KAAnC;UACH;QACJ;;QACD,KAAKhF,MAAL,CAAYhD,IAAZ,CAAiBD,KAAjB;QACA;;MACJ,KAAK,MAAL;QACI,KAAK2L,aAAL;QACA,KAAKzI,MAAL,CAAYjD,IAAZ,CAAiBD,KAAjB;QACA;IAtCR;EAwCH;;EACD4L,qBAAqB,CAAC5L,KAAD,EAAQ;IACzB,QAAQA,KAAK,CAACwK,OAAd;MACI,KAAK,MAAL;QACIzL,WAAW,CAAC8M,KAAZ,CAAkB7L,KAAK,CAACyK,OAAxB;QACA;IAHR;EAKH;;EACDK,aAAa,GAAG;IACZ,IAAI,KAAKgB,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACIC,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKtD,OAA/B,EADJ,KAGIhK,UAAU,CAACsN,WAAX,CAAuB,KAAKtD,OAA5B,EAAqC,KAAKmD,QAA1C;;MACJ,IAAI,CAAC,KAAKnD,OAAL,CAAaxK,KAAb,CAAmB+N,QAAxB,EAAkC;QAC9B,KAAKvD,OAAL,CAAaxK,KAAb,CAAmB+N,QAAnB,GAA8BvN,UAAU,CAACwN,QAAX,CAAoB,KAAKC,kBAAL,CAAwBlF,aAA5C,IAA6D,IAA3F;MACH;IACJ;EACJ;;EACDmF,oBAAoB,GAAG;IACnB,IAAI,KAAK1D,OAAL,IAAgB,KAAKmD,QAAzB,EAAmC;MAC/B,KAAKzK,EAAL,CAAQ6F,aAAR,CAAsB+E,WAAtB,CAAkC,KAAKtD,OAAvC;IACH;EACJ;;EACDhF,IAAI,GAAG;IACH,KAAKD,cAAL,GAAsB,KAAtB;;IACA,IAAI,KAAK8B,MAAL,IAAe,KAAK3D,iBAAxB,EAA2C;MACvC,KAAK+D,WAAL;IACH;;IACD,KAAKrE,EAAL,CAAQuH,YAAR;EACH;;EACDR,YAAY,GAAG;IACX,IAAI,KAAKK,OAAT,EAAkB;MACd,IAAI,KAAKmD,QAAT,EACInN,UAAU,CAAC2N,gBAAX,CAA4B,KAAK3D,OAAjC,EAA0C,KAAKyD,kBAAL,CAAwBlF,aAAlE,EADJ,KAGIvI,UAAU,CAAC4N,gBAAX,CAA4B,KAAK5D,OAAjC,EAA0C,KAAKyD,kBAAL,CAAwBlF,aAAlE;IACP;EACJ;;EACDsF,YAAY,CAACxM,KAAD,EAAQ;IAChB,KAAKyD,OAAL,GAAe,IAAf;IACA,KAAKV,OAAL,CAAa9C,IAAb,CAAkBD,KAAlB;EACH;;EACDyM,WAAW,CAACzM,KAAD,EAAQ;IACf,KAAKyD,OAAL,GAAe,KAAf;IACA,KAAKT,MAAL,CAAY/C,IAAZ,CAAiBD,KAAjB;;IACA,IAAI,CAAC,KAAK0L,mBAAV,EAA+B;MAC3B,KAAKpI,cAAL;IACH;;IACD,KAAKoI,mBAAL,GAA2B,KAA3B;EACH;;EACDgB,qBAAqB,CAACC,KAAD,EAAQ;IACzB,IAAIC,iBAAJ;;IACA,IAAI,KAAK3H,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBkB,MAAnD,EAA2D;MACvD,KAAK,IAAI0G,CAAC,GAAIF,KAAK,GAAG,CAAtB,EAA0B,KAAKE,CAA/B,EAAkCA,CAAC,EAAnC,EAAuC;QACnC,IAAI1M,MAAM,GAAG,KAAK8E,gBAAL,CAAsB4H,CAAtB,CAAb;;QACA,IAAI,KAAKtF,gBAAL,CAAsBpH,MAAtB,CAAJ,EAAmC;UAC/B;QACH,CAFD,MAGK;UACDyM,iBAAiB,GAAGzM,MAApB;UACA;QACH;MACJ;;MACD,IAAI,CAACyM,iBAAL,EAAwB;QACpB,KAAK,IAAIC,CAAC,GAAG,KAAK5H,gBAAL,CAAsBkB,MAAtB,GAA+B,CAA5C,EAA+C0G,CAAC,IAAIF,KAApD,EAA2DE,CAAC,EAA5D,EAAgE;UAC5D,IAAI1M,MAAM,GAAG,KAAK8E,gBAAL,CAAsB4H,CAAtB,CAAb;;UACA,IAAI,KAAKtF,gBAAL,CAAsBpH,MAAtB,CAAJ,EAAmC;YAC/B;UACH,CAFD,MAGK;YACDyM,iBAAiB,GAAGzM,MAApB;YACA;UACH;QACJ;MACJ;IACJ;;IACD,OAAOyM,iBAAP;EACH;;EACDE,qBAAqB,CAACH,KAAD,EAAQ;IACzB,IAAII,iBAAJ;;IACA,IAAI,KAAK9H,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBkB,MAAnD,EAA2D;MACvD,KAAK,IAAI0G,CAAC,GAAIF,KAAK,GAAG,CAAtB,EAA0BE,CAAC,GAAG,KAAK5H,gBAAL,CAAsBkB,MAApD,EAA4D0G,CAAC,EAA7D,EAAiE;QAC7D,IAAI1M,MAAM,GAAG,KAAK8E,gBAAL,CAAsB4H,CAAtB,CAAb;;QACA,IAAI,KAAKtF,gBAAL,CAAsBpH,MAAtB,CAAJ,EAAmC;UAC/B;QACH,CAFD,MAGK;UACD4M,iBAAiB,GAAG5M,MAApB;UACA;QACH;MACJ;;MACD,IAAI,CAAC4M,iBAAL,EAAwB;QACpB,KAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,KAApB,EAA2BE,CAAC,EAA5B,EAAgC;UAC5B,IAAI1M,MAAM,GAAG,KAAK8E,gBAAL,CAAsB4H,CAAtB,CAAb;;UACA,IAAI,KAAKtF,gBAAL,CAAsBpH,MAAtB,CAAJ,EAAmC;YAC/B;UACH,CAFD,MAGK;YACD4M,iBAAiB,GAAG5M,MAApB;YACA;UACH;QACJ;MACJ;IACJ;;IACD,OAAO4M,iBAAP;EACH;;EACDC,SAAS,CAAChN,KAAD,EAAQiN,MAAR,EAAgB;IACrB,IAAI,KAAK1D,QAAL,IAAiB,CAAC,KAAKtE,gBAAvB,IAA2C,KAAKA,gBAAL,CAAsBkB,MAAtB,KAAiC,IAAhF,EAAsF;MAClF;IACH;;IACD,QAAQnG,KAAK,CAACkN,KAAd;MACI;MACA,KAAK,EAAL;QACI,IAAI,CAAC,KAAKxJ,cAAN,IAAwB1D,KAAK,CAACmN,MAAlC,EAA0C;UACtC,KAAK1D,IAAL;QACH,CAFD,MAGK;UACD,IAAI,KAAKR,KAAT,EAAgB;YACZ,IAAImE,iBAAiB,GAAG,KAAKtH,cAAL,GAAsB,KAAKuH,oBAAL,CAA0B,KAAKhG,cAAL,CAAoB,KAAKvB,cAAzB,CAA1B,EAAoE,KAAKb,gBAAzE,CAAtB,GAAmH,CAAC,CAA5I;;YACA,IAAImI,iBAAiB,KAAK,CAAC,CAA3B,EAA8B;cAC1B,IAAIE,aAAa,GAAGF,iBAAiB,CAACG,SAAlB,GAA8B,CAAlD;;cACA,IAAID,aAAa,GAAI,KAAK1F,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAxC,CAA5B,EAAiFrH,MAAtG,EAA+G;gBAC3G,KAAK4B,UAAL,CAAgB/H,KAAhB,EAAuB,KAAK4H,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAxC,CAA5B,EAAiFF,aAAjF,CAAvB;gBACA,KAAK/E,qBAAL,GAA6B,IAA7B;cACH,CAHD,MAIK,IAAI,KAAKtD,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAlB,GAA+B,CAArD,CAAJ,EAA6D;gBAC9D,KAAKzF,UAAL,CAAgB/H,KAAhB,EAAuB,KAAK4H,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAlB,GAA+B,CAArD,CAA5B,EAAqF,CAArF,CAAvB;gBACA,KAAKjF,qBAAL,GAA6B,IAA7B;cACH;YACJ,CAVD,MAWK;cACD,IAAI,KAAKtD,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBkB,MAAtB,GAA+B,CAA5D,EAA+D;gBAC3D,KAAK4B,UAAL,CAAgB/H,KAAhB,EAAuB,KAAK4H,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsB,CAAtB,CAA5B,EAAsD,CAAtD,CAAvB;cACH;YACJ;UACJ,CAlBD,MAmBK;YACD,IAAImI,iBAAiB,GAAG,KAAKtH,cAAL,GAAsB,KAAKsF,eAAL,CAAqB,KAAK/D,cAAL,CAAoB,KAAKvB,cAAzB,CAArB,EAA+D,KAAKb,gBAApE,CAAtB,GAA8G,CAAC,CAAvI;YACA,IAAI8H,iBAAiB,GAAG,KAAKD,qBAAL,CAA2BM,iBAA3B,CAAxB;;YACA,IAAIL,iBAAJ,EAAuB;cACnB,KAAKhF,UAAL,CAAgB/H,KAAhB,EAAuB+M,iBAAvB;cACA,KAAKxE,qBAAL,GAA6B,IAA7B;YACH;UACJ;QACJ;;QACDvI,KAAK,CAACyN,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,KAAKxE,KAAT,EAAgB;UACZ,IAAImE,iBAAiB,GAAG,KAAKtH,cAAL,GAAsB,KAAKuH,oBAAL,CAA0B,KAAKhG,cAAL,CAAoB,KAAKvB,cAAzB,CAA1B,EAAoE,KAAKb,gBAAzE,CAAtB,GAAmH,CAAC,CAA5I;;UACA,IAAImI,iBAAiB,KAAK,CAAC,CAA3B,EAA8B;YAC1B,IAAIM,aAAa,GAAGN,iBAAiB,CAACG,SAAlB,GAA8B,CAAlD;;YACA,IAAIG,aAAa,IAAI,CAArB,EAAwB;cACpB,KAAK3F,UAAL,CAAgB/H,KAAhB,EAAuB,KAAK4H,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAxC,CAA5B,EAAiFE,aAAjF,CAAvB;cACA,KAAKnF,qBAAL,GAA6B,IAA7B;YACH,CAHD,MAIK,IAAImF,aAAa,GAAG,CAApB,EAAuB;cACxB,IAAIC,SAAS,GAAG,KAAK1I,gBAAL,CAAsBmI,iBAAiB,CAACI,UAAlB,GAA+B,CAArD,CAAhB;;cACA,IAAIG,SAAJ,EAAe;gBACX,KAAK5F,UAAL,CAAgB/H,KAAhB,EAAuB,KAAK4H,sBAAL,CAA4B+F,SAA5B,EAAuC,KAAK/F,sBAAL,CAA4B+F,SAA5B,EAAuCxH,MAAvC,GAAgD,CAAvF,CAAvB;gBACA,KAAKoC,qBAAL,GAA6B,IAA7B;cACH;YACJ;UACJ;QACJ,CAhBD,MAiBK;UACD,IAAI6E,iBAAiB,GAAG,KAAKtH,cAAL,GAAsB,KAAKsF,eAAL,CAAqB,KAAK/D,cAAL,CAAoB,KAAKvB,cAAzB,CAArB,EAA+D,KAAKb,gBAApE,CAAtB,GAA8G,CAAC,CAAvI;UACA,IAAI2H,iBAAiB,GAAG,KAAKF,qBAAL,CAA2BU,iBAA3B,CAAxB;;UACA,IAAIR,iBAAJ,EAAuB;YACnB,KAAK7E,UAAL,CAAgB/H,KAAhB,EAAuB4M,iBAAvB;YACA,KAAKrE,qBAAL,GAA6B,IAA7B;UACH;QACJ;;QACDvI,KAAK,CAACyN,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAIR,MAAJ,EAAY;UACR,IAAI,CAAC,KAAKvJ,cAAV,EAA0B;YACtB,KAAK+F,IAAL;UACH,CAFD,MAGK;YACD,KAAK9F,IAAL;UACH;;UACD3D,KAAK,CAACyN,cAAN;QACH;;QACD;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,KAAK/J,cAAL,KAAwB,CAAC,KAAK8B,MAAN,IAAiB,KAAKP,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBkB,MAAtB,GAA+B,CAAjG,CAAJ,EAA0G;UACtG,KAAKxC,IAAL;QACH,CAFD,MAGK,IAAI,CAAC,KAAKD,cAAV,EAA0B;UAC3B,KAAK+F,IAAL;QACH;;QACDzJ,KAAK,CAACyN,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;MACA,KAAK,CAAL;QACI,KAAK9J,IAAL;QACA;MACJ;;MACA;QACI,IAAIsJ,MAAM,IAAI,CAACjN,KAAK,CAAC4N,OAArB,EAA8B;UAC1B,KAAKX,MAAL,CAAYjN,KAAZ;QACH;;QACD;IAlGR;EAoGH;;EACDiN,MAAM,CAACjN,KAAD,EAAQ;IACV,IAAI,KAAK6N,aAAT,EAAwB;MACpBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;IACH;;IACD,MAAME,IAAI,GAAG/N,KAAK,CAACgO,GAAnB;IACA,KAAKC,kBAAL,GAA0B,KAAKC,iBAA/B;IACA,KAAKA,iBAAL,GAAyBH,IAAzB;IACA,IAAI,KAAKE,kBAAL,KAA4B,KAAKC,iBAArC,EACI,KAAKC,WAAL,GAAmB,KAAKD,iBAAxB,CADJ,KAGI,KAAKC,WAAL,GAAmB,KAAKA,WAAL,GAAmB,KAAKA,WAAL,GAAmBJ,IAAtC,GAA6CA,IAAhE;IACJ,IAAIK,SAAJ;;IACA,IAAI,KAAKnF,KAAT,EAAgB;MACZ,IAAIoF,WAAW,GAAG,KAAKvI,cAAL,GAAsB,KAAKuH,oBAAL,CAA0B,KAAKhG,cAAL,CAAoB,KAAKvB,cAAzB,CAA1B,EAAoE,KAAKb,gBAAzE,CAAtB,GAAmH;QAAEuI,UAAU,EAAE,CAAd;QAAiBD,SAAS,EAAE;MAA5B,CAArI;MACAa,SAAS,GAAG,KAAKE,uBAAL,CAA6BD,WAA7B,CAAZ;IACH,CAHD,MAIK;MACD,IAAIA,WAAW,GAAG,KAAKvI,cAAL,GAAsB,KAAKsF,eAAL,CAAqB,KAAK/D,cAAL,CAAoB,KAAKvB,cAAzB,CAArB,EAA+D,KAAKb,gBAApE,CAAtB,GAA8G,CAAC,CAAjI;MACAmJ,SAAS,GAAG,KAAKG,YAAL,CAAkB,EAAEF,WAApB,CAAZ;IACH;;IACD,IAAID,SAAS,IAAI,CAAC,KAAK7G,gBAAL,CAAsB6G,SAAtB,CAAlB,EAAoD;MAChD,KAAKrG,UAAL,CAAgB/H,KAAhB,EAAuBoO,SAAvB;MACA,KAAK7F,qBAAL,GAA6B,IAA7B;IACH;;IACD,KAAKsF,aAAL,GAAqB1F,UAAU,CAAC,MAAM;MAClC,KAAKgG,WAAL,GAAmB,IAAnB;IACH,CAF8B,EAE5B,GAF4B,CAA/B;EAGH;;EACDI,YAAY,CAAC5B,KAAD,EAAQ;IAChB,IAAIxM,MAAJ;;IACA,IAAI,KAAKgO,WAAT,EAAsB;MAClBhO,MAAM,GAAG,KAAKqO,mBAAL,CAAyB7B,KAAzB,EAAgC,KAAK1H,gBAAL,CAAsBkB,MAAtD,CAAT;;MACA,IAAI,CAAChG,MAAL,EAAa;QACTA,MAAM,GAAG,KAAKqO,mBAAL,CAAyB,CAAzB,EAA4B7B,KAA5B,CAAT;MACH;IACJ;;IACD,OAAOxM,MAAP;EACH;;EACDqO,mBAAmB,CAACC,KAAD,EAAQC,GAAR,EAAa;IAC5B,KAAK,IAAI7B,CAAC,GAAG4B,KAAb,EAAoB5B,CAAC,GAAG6B,GAAxB,EAA6B7B,CAAC,EAA9B,EAAkC;MAC9B,IAAI8B,GAAG,GAAG,KAAK1J,gBAAL,CAAsB4H,CAAtB,CAAV;;MACA,IAAI,KAAKpG,cAAL,CAAoBkI,GAApB,EAAyBC,iBAAzB,CAA2C,KAAKC,YAAhD,EAA8DC,UAA9D,CAAyE,KAAKX,WAAL,CAAiBS,iBAAjB,CAAmC,KAAKC,YAAxC,CAAzE,KAAmI,CAAC,KAAKtH,gBAAL,CAAsBoH,GAAtB,CAAxI,EAAoK;QAChK,OAAOA,GAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;;EACDL,uBAAuB,CAAC3B,KAAD,EAAQ;IAC3B,IAAIxM,MAAJ;;IACA,IAAI,KAAKgO,WAAT,EAAsB;MAClB,KAAK,IAAItB,CAAC,GAAGF,KAAK,CAACa,UAAnB,EAA+BX,CAAC,GAAG,KAAK5H,gBAAL,CAAsBkB,MAAzD,EAAiE0G,CAAC,EAAlE,EAAsE;QAClE,KAAK,IAAIkC,CAAC,GAAIpC,KAAK,CAACa,UAAN,KAAqBX,CAAtB,GAA4BF,KAAK,CAACY,SAAN,GAAkB,CAA9C,GAAmD,CAAhE,EAAmEwB,CAAC,GAAG,KAAKnH,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsB4H,CAAtB,CAA5B,EAAsD1G,MAA7H,EAAqI4I,CAAC,EAAtI,EAA0I;UACtI,IAAIJ,GAAG,GAAG,KAAK/G,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsB4H,CAAtB,CAA5B,EAAsDkC,CAAtD,CAAV;;UACA,IAAI,KAAKtI,cAAL,CAAoBkI,GAApB,EAAyBC,iBAAzB,CAA2C,KAAKC,YAAhD,EAA8DC,UAA9D,CAAyE,KAAKX,WAAL,CAAiBS,iBAAjB,CAAmC,KAAKC,YAAxC,CAAzE,KAAmI,CAAC,KAAKtH,gBAAL,CAAsBoH,GAAtB,CAAxI,EAAoK;YAChK,OAAOA,GAAP;UACH;QACJ;MACJ;;MACD,IAAI,CAACxO,MAAL,EAAa;QACT,KAAK,IAAI0M,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,KAAK,CAACa,UAA3B,EAAuCX,CAAC,EAAxC,EAA4C;UACxC,KAAK,IAAIkC,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAKpC,KAAK,CAACa,UAAN,KAAqBX,CAAtB,GAA2BF,KAAK,CAACY,SAAjC,GAA6C,KAAK3F,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsB4H,CAAtB,CAA5B,EAAsD1G,MAAvG,CAAjB,EAAiI4I,CAAC,EAAlI,EAAsI;YAClI,IAAIJ,GAAG,GAAG,KAAK/G,sBAAL,CAA4B,KAAK3C,gBAAL,CAAsB4H,CAAtB,CAA5B,EAAsDkC,CAAtD,CAAV;;YACA,IAAI,KAAKtI,cAAL,CAAoBkI,GAApB,EAAyBC,iBAAzB,CAA2C,KAAKC,YAAhD,EAA8DC,UAA9D,CAAyE,KAAKX,WAAL,CAAiBS,iBAAjB,CAAmC,KAAKC,YAAxC,CAAzE,KAAmI,CAAC,KAAKtH,gBAAL,CAAsBoH,GAAtB,CAAxI,EAAoK;cAChK,OAAOA,GAAP;YACH;UACJ;QACJ;MACJ;IACJ;;IACD,OAAO,IAAP;EACH;;EACDvD,eAAe,CAACrH,GAAD,EAAMiL,IAAN,EAAY;IACvB,IAAIrC,KAAK,GAAG,CAAC,CAAb;;IACA,IAAIqC,IAAJ,EAAU;MACN,KAAK,IAAInC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmC,IAAI,CAAC7I,MAAzB,EAAiC0G,CAAC,EAAlC,EAAsC;QAClC,IAAK9I,GAAG,IAAI,IAAP,IAAe,KAAKsD,cAAL,CAAoB2H,IAAI,CAACnC,CAAD,CAAxB,KAAgC,IAAhD,IAAyD/N,WAAW,CAACmQ,MAAZ,CAAmBlL,GAAnB,EAAwB,KAAKsD,cAAL,CAAoB2H,IAAI,CAACnC,CAAD,CAAxB,CAAxB,EAAsD,KAAKqC,OAA3D,CAA7D,EAAkI;UAC9HvC,KAAK,GAAGE,CAAR;UACA;QACH;MACJ;IACJ;;IACD,OAAOF,KAAP;EACH;;EACDU,oBAAoB,CAACtJ,GAAD,EAAMiL,IAAN,EAAY;IAC5B,IAAIxB,UAAJ,EAAgBD,SAAhB;;IACA,IAAIyB,IAAJ,EAAU;MACN,KAAK,IAAInC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmC,IAAI,CAAC7I,MAAzB,EAAiC0G,CAAC,EAAlC,EAAsC;QAClCW,UAAU,GAAGX,CAAb;QACAU,SAAS,GAAG,KAAKnC,eAAL,CAAqBrH,GAArB,EAA0B,KAAK6D,sBAAL,CAA4BoH,IAAI,CAACnC,CAAD,CAAhC,CAA1B,CAAZ;;QACA,IAAIU,SAAS,KAAK,CAAC,CAAnB,EAAsB;UAClB;QACH;MACJ;IACJ;;IACD,IAAIA,SAAS,KAAK,CAAC,CAAnB,EAAsB;MAClB,OAAO;QAAEC,UAAU,EAAEA,UAAd;QAA0BD,SAAS,EAAEA;MAArC,CAAP;IACH,CAFD,MAGK;MACD,OAAO,CAAC,CAAR;IACH;EACJ;;EACDxH,UAAU,CAAChC,GAAD,EAAMiL,IAAN,EAAYG,OAAZ,EAAqB;IAC3B,IAAI,KAAKlG,KAAL,IAAc,CAACkG,OAAnB,EAA4B;MACxB,IAAIR,GAAJ;;MACA,IAAIK,IAAI,IAAIA,IAAI,CAAC7I,MAAjB,EAAyB;QACrB,KAAK,IAAIiJ,QAAT,IAAqBJ,IAArB,EAA2B;UACvBL,GAAG,GAAG,KAAK5I,UAAL,CAAgBhC,GAAhB,EAAqB,KAAK6D,sBAAL,CAA4BwH,QAA5B,CAArB,EAA4D,IAA5D,CAAN;;UACA,IAAIT,GAAJ,EAAS;YACL;UACH;QACJ;MACJ;;MACD,OAAOA,GAAP;IACH,CAXD,MAYK;MACD,IAAIhC,KAAK,GAAG,KAAKvB,eAAL,CAAqBrH,GAArB,EAA0BiL,IAA1B,CAAZ;MACA,OAAQrC,KAAK,IAAI,CAAC,CAAX,GAAgBqC,IAAI,CAACrC,KAAD,CAApB,GAA8B,IAArC;IACH;EACJ;;EACDjH,mBAAmB,CAAC1F,KAAD,EAAQ;IACvB,IAAIqP,UAAU,GAAGrP,KAAK,CAAC4J,MAAN,CAAanE,KAA9B;;IACA,IAAI4J,UAAU,IAAIA,UAAU,CAAClJ,MAA7B,EAAqC;MACjC,KAAKD,YAAL,GAAoBmJ,UAApB;MACA,KAAKjJ,cAAL;IACH,CAHD,MAIK;MACD,KAAKF,YAAL,GAAoB,IAApB;MACA,KAAKjB,gBAAL,GAAwB,KAAKC,OAA7B;IACH;;IACD,KAAKwF,aAAL,IAAsB,KAAKC,QAAL,CAAcU,aAAd,CAA4B,CAA5B,CAAtB;IACA,KAAKpF,cAAL,GAAsB,IAAtB;IACA,KAAKnD,QAAL,CAAc7C,IAAd,CAAmB;MAAEC,aAAa,EAAEF,KAAjB;MAAwBwF,MAAM,EAAE,KAAKU;IAArC,CAAnB;EACH;;EACDE,cAAc,GAAG;IACb,IAAIkJ,YAAY,GAAG,CAAC,KAAKhK,QAAL,IAAiB,KAAK6B,WAAtB,IAAqC,OAAtC,EAA+CoI,KAA/C,CAAqD,GAArD,CAAnB;;IACA,IAAI,KAAKrK,OAAL,IAAgB,KAAKA,OAAL,CAAaiB,MAAjC,EAAyC;MACrC,IAAI,KAAK8C,KAAT,EAAgB;QACZ,IAAIuG,cAAc,GAAG,EAArB;;QACA,KAAK,IAAIJ,QAAT,IAAqB,KAAKlK,OAA1B,EAAmC;UAC/B,IAAIuK,kBAAkB,GAAG,KAAKhO,aAAL,CAAmB+D,MAAnB,CAA0B,KAAKoC,sBAAL,CAA4BwH,QAA5B,CAA1B,EAAiEE,YAAjE,EAA+E,KAAKjJ,WAApF,EAAiG,KAAK7D,eAAtG,EAAuH,KAAKqM,YAA5H,CAAzB;;UACA,IAAIY,kBAAkB,IAAIA,kBAAkB,CAACtJ,MAA7C,EAAqD;YACjDqJ,cAAc,CAACE,IAAf,CAAoBC,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBR,QAAlB,CAAd,EAA2C;cAAE,CAAC,KAAKrN,mBAAN,GAA4B0N;YAA9B,CAA3C,CAApB;UACH;QACJ;;QACD,KAAKxK,gBAAL,GAAwBuK,cAAxB;MACH,CATD,MAUK;QACD,KAAKvK,gBAAL,GAAwB,KAAKxD,aAAL,CAAmB+D,MAAnB,CAA0B,KAAKN,OAA/B,EAAwCoK,YAAxC,EAAsD,KAAKjJ,WAA3D,EAAwE,KAAK7D,eAA7E,EAA8F,KAAKqM,YAAnG,CAAxB;MACH;;MACD,KAAK5I,cAAL,GAAsB,IAAtB;IACH;EACJ;;EACD4J,UAAU,GAAG;IACT,IAAI,KAAKtJ,QAAT,EACI5H,UAAU,CAAC+J,UAAX,CAAsB,KAAKrH,EAAL,CAAQ6F,aAA9B,EAA6C,+BAA7C,EAA8Ee,KAA9E,GADJ,KAGItJ,UAAU,CAAC+J,UAAX,CAAsB,KAAKrH,EAAL,CAAQ6F,aAA9B,EAA6C,iBAA7C,EAAgEe,KAAhE;EACP;;EACDA,KAAK,GAAG;IACJ,KAAK4H,UAAL;EACH;;EACD1F,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAK2F,qBAAV,EAAiC;MAC7B,MAAMC,cAAc,GAAG,KAAK1O,EAAL,GAAU,KAAKA,EAAL,CAAQ6F,aAAR,CAAsB8I,aAAhC,GAAgD,UAAvE;MACA,KAAKF,qBAAL,GAA6B,KAAKxO,QAAL,CAAc2O,MAAd,CAAqBF,cAArB,EAAqC,OAArC,EAA+C/P,KAAD,IAAW;QAClF,IAAI,CAAC,KAAKsK,sBAAN,IAAgC,KAAKP,gBAAL,CAAsB/J,KAAtB,CAApC,EAAkE;UAC9D,KAAK2D,IAAL;UACA,KAAKuM,2BAAL;QACH;;QACD,KAAK5F,sBAAL,GAA8B,KAA9B;MACH,CAN4B,CAA7B;IAOH;EACJ;;EACD4F,2BAA2B,GAAG;IAC1B,IAAI,KAAKJ,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACD7E,0BAA0B,GAAG;IACzB,KAAKkF,sBAAL,GAA8B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKJ,sBAAvC;EACH;;EACDK,4BAA4B,GAAG;IAC3B,IAAI,KAAKL,sBAAT,EAAiC;MAC7BG,MAAM,CAACG,mBAAP,CAA2B,QAA3B,EAAqC,KAAKN,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDC,cAAc,GAAG;IACb,IAAI,KAAK1M,cAAL,IAAuB,CAAC/E,UAAU,CAAC+R,aAAX,EAA5B,EAAwD;MACpD,KAAK/M,IAAL;IACH;EACJ;;EACDuH,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKyF,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAI/R,6BAAJ,CAAkC,KAAKwN,kBAAL,CAAwBlF,aAA1D,EAA0ElH,KAAD,IAAW;QACrG,IAAI,KAAK0D,cAAT,EAAyB;UACrB,KAAKC,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKgN,aAAL,CAAmBzF,kBAAnB;EACH;;EACD0F,oBAAoB,GAAG;IACnB,IAAI,KAAKD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBC,oBAAnB;IACH;EACJ;;EACD/E,KAAK,CAAC7L,KAAD,EAAQ;IACT,KAAKyF,KAAL,GAAa,IAAb;IACA,KAAKpC,aAAL,CAAmB,KAAKoC,KAAxB;IACA,KAAK5C,QAAL,CAAc5C,IAAd,CAAmB;MACfC,aAAa,EAAEF,KADA;MAEfyF,KAAK,EAAE,KAAKA;IAFG,CAAnB;IAIA,KAAKN,oBAAL,CAA0B,KAAKM,KAA/B;IACA,KAAKe,mBAAL;IACA,KAAKrD,OAAL,CAAalD,IAAb,CAAkBD,KAAlB;EACH;;EACD2L,aAAa,GAAG;IACZ,KAAKuE,2BAAL;IACA,KAAKM,4BAAL;IACA,KAAKI,oBAAL;IACA,KAAKjI,OAAL,GAAe,IAAf;IACA,KAAKH,YAAL,GAAoB,IAApB;IACA,KAAKlF,cAAL;EACH;;EACDuN,WAAW,GAAG;IACV,IAAI,KAAKF,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBG,OAAnB;MACA,KAAKH,aAAL,GAAqB,IAArB;IACH;;IACD,IAAI,KAAKhI,OAAT,EAAkB;MACd5J,WAAW,CAAC8M,KAAZ,CAAkB,KAAKlD,OAAvB;IACH;;IACD,KAAK0D,oBAAL;IACA,KAAKV,aAAL;EACH;;AAtxBU;;AAwxBfjM,QAAQ,CAACU,IAAT;EAAA,iBAAqGV,QAArG,EAp0B+FpC,EAo0B/F,mBAA+HA,EAAE,CAACyT,UAAlI,GAp0B+FzT,EAo0B/F,mBAAyJA,EAAE,CAAC0T,SAA5J,GAp0B+F1T,EAo0B/F,mBAAkLA,EAAE,CAAC2T,iBAArL,GAp0B+F3T,EAo0B/F,mBAAmNA,EAAE,CAAC4T,MAAtN,GAp0B+F5T,EAo0B/F,mBAAyOiB,EAAE,CAAC4S,aAA5O,GAp0B+F7T,EAo0B/F,mBAAsQiB,EAAE,CAAC6S,aAAzQ,GAp0B+F9T,EAo0B/F,mBAAmSiB,EAAE,CAAC8S,cAAtS;AAAA;;AACA3R,QAAQ,CAACW,IAAT,kBAr0B+F/C,EAq0B/F;EAAA,MAAyFoC,QAAzF;EAAA;EAAA;IAAA;MAr0B+FpC,EAq0B/F,0BAAgoEmB,aAAhoE;IAAA;;IAAA;MAAA;;MAr0B+FnB,EAq0B/F,qBAr0B+FA,EAq0B/F;IAAA;EAAA;EAAA;IAAA;MAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F;IAAA;;IAAA;MAAA;;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;MAr0B+FA,EAq0B/F,qBAr0B+FA,EAq0B/F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAr0B+FA,EAq0B/F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAr0B+FA,EAq0B/F,oBAAmjE,CAACiC,uBAAD,CAAnjE;EAAA;EAAA;EAAA;EAAA;IAAA;MAr0B+FjC,EAs0BtF,+BADT;MAr0B+FA,EAw0BnF;QAAA,OAAS,wBAAT;MAAA,EAHZ;MAr0B+FA,EAy0BnF,8CAJZ;MAr0B+FA,EA00B3B;QAAA,OAAS,wBAAT;MAAA;QAAA,OACmF,uBADnF;MAAA;QAAA,OACmH,sBAAkB,IAAlB,CADnH;MAAA,EALpE;MAr0B+FA,EA00B/E,iBALhB;MAr0B+FA,EA80BnF,0DATZ;MAr0B+FA,EAk1BnF,yDAbZ;MAr0B+FA,EAm1BnF,2DAdZ;MAr0B+FA,EAq1BnF,mDAhBZ;MAr0B+FA,EAs1BnF,4BAjBZ;MAr0B+FA,EAu1B/E,0BAlBhB;MAr0B+FA,EAw1BnF,eAnBZ;MAr0B+FA,EAy1BnF,2DApBZ;MAr0B+FA,EA65BvF,eAxFR;IAAA;;IAAA;MAr0B+FA,EAw0BlC,2BAH7D;MAr0B+FA,EAs0BtE,uBAt0BsEA,EAs0BtE,gIADzB;MAr0B+FA,EA40B3E,aAPpB;MAr0B+FA,EA40B3E,qCAPpB;MAr0B+FA,EA00BpE,mRAL3B;MAr0B+FA,EA80ByD,aATxJ;MAr0B+FA,EA80ByD,uDATxJ;MAr0B+FA,EAk1BgE,aAb/J;MAr0B+FA,EAk1BgE,uDAb/J;MAr0B+FA,EAm1BkB,aAdjH;MAr0B+FA,EAm1BkB,iCAdjH;MAr0B+FA,EAq1Bb,aAhBlF;MAr0B+FA,EAq1Bb,wEAhBlF;MAr0B+FA,EAs1BiB,aAjBhH;MAr0B+FA,EAs1BiB,iDAjBhH;MAr0B+FA,EAu1BzC,aAlBtD;MAr0B+FA,EAu1BzC,wCAlBtD;MAr0B+FA,EAy1B7E,aApBlB;MAr0B+FA,EAy1B7E,uCApBlB;IAAA;EAAA;EAAA,eAyFgkCe,EAAE,CAACiC,OAzFnkC,EAyF8pCjC,EAAE,CAACiT,OAzFjqC,EAyF2xCjT,EAAE,CAACkC,IAzF9xC,EAyF+3ClC,EAAE,CAACmC,gBAzFl4C,EAyFsiDnC,EAAE,CAACoC,OAzFziD,EAyF2nDlC,EAAE,CAACE,aAzF9nD,EAyFkuDQ,EAAE,CAACsS,OAzFruD,EAyF6hEpS,EAAE,CAACqS,QAzFhiE,EAyFw7E5R,YAzFx7E;EAAA;EAAA;EAAA;IAAA,WAyF+lF,CACvlF3B,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;MAAEsT,OAAO,EAAE,CAAX;MAAcC,SAAS,EAAE;IAAzB,CAAD,CADY,EAEjBtT,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAEsT,OAAO,EAAE;IAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADglF;EAzF/lF;EAAA;AAAA;;AAoGA;EAAA,mDAz6B+FnU,EAy6B/F,mBAA2FoC,QAA3F,EAAiH,CAAC;IACtGiB,IAAI,EAAElD,SADgG;IAEtGmD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BC,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAzFmB;MAyFZ6Q,UAAU,EAAE,CACK1T,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;QAAEsT,OAAO,EAAE,CAAX;QAAcC,SAAS,EAAE;MAAzB,CAAD,CADY,EAEjBtT,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAEsT,OAAO,EAAE;MAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADZ,CAzFA;MAmGI1Q,IAAI,EAAE;QACL,SAAS,0BADJ;QAEL,iCAAiC,QAF5B;QAGL,gCAAgC;MAH3B,CAnGV;MAuGI6Q,SAAS,EAAE,CAACrS,uBAAD,CAvGf;MAuG0CsS,eAAe,EAAEjU,uBAAuB,CAACkU,MAvGnF;MAuG2FC,aAAa,EAAElU,iBAAiB,CAACmU,IAvG5H;MAuGkIC,MAAM,EAAE,CAAC,m/BAAD;IAvG1I,CAAD;EAFgG,CAAD,CAAjH,EA0G4B,YAAY;IAAE,OAAO,CAAC;MAAEtR,IAAI,EAAErD,EAAE,CAACyT;IAAX,CAAD,EAA0B;MAAEpQ,IAAI,EAAErD,EAAE,CAAC0T;IAAX,CAA1B,EAAkD;MAAErQ,IAAI,EAAErD,EAAE,CAAC2T;IAAX,CAAlD,EAAkF;MAAEtQ,IAAI,EAAErD,EAAE,CAAC4T;IAAX,CAAlF,EAAuG;MAAEvQ,IAAI,EAAEpC,EAAE,CAAC4S;IAAX,CAAvG,EAAmI;MAAExQ,IAAI,EAAEpC,EAAE,CAAC6S;IAAX,CAAnI,EAA+J;MAAEzQ,IAAI,EAAEpC,EAAE,CAAC8S;IAAX,CAA/J,CAAP;EAAqM,CA1G/O,EA0GiQ;IAAEzP,YAAY,EAAE,CAAC;MAClQjB,IAAI,EAAEjD;IAD4P,CAAD,CAAhB;IAEjP8H,MAAM,EAAE,CAAC;MACT7E,IAAI,EAAEjD;IADG,CAAD,CAFyO;IAIjPwU,IAAI,EAAE,CAAC;MACPvR,IAAI,EAAEjD;IADC,CAAD,CAJ2O;IAMjPS,KAAK,EAAE,CAAC;MACRwC,IAAI,EAAEjD;IADE,CAAD,CAN0O;IAQjPyU,UAAU,EAAE,CAAC;MACbxR,IAAI,EAAEjD;IADO,CAAD,CARqO;IAUjP0U,UAAU,EAAE,CAAC;MACbzR,IAAI,EAAEjD;IADO,CAAD,CAVqO;IAYjP2U,eAAe,EAAE,CAAC;MAClB1R,IAAI,EAAEjD;IADY,CAAD,CAZgO;IAcjP6L,QAAQ,EAAE,CAAC;MACX5I,IAAI,EAAEjD;IADK,CAAD,CAduO;IAgBjP4U,QAAQ,EAAE,CAAC;MACX3R,IAAI,EAAEjD;IADK,CAAD,CAhBuO;IAkBjP6I,QAAQ,EAAE,CAAC;MACX5F,IAAI,EAAEjD;IADK,CAAD,CAlBuO;IAoBjPoO,QAAQ,EAAE,CAAC;MACXnL,IAAI,EAAEjD;IADK,CAAD,CApBuO;IAsBjP6U,QAAQ,EAAE,CAAC;MACX5R,IAAI,EAAEjD;IADK,CAAD,CAtBuO;IAwBjPsL,WAAW,EAAE,CAAC;MACdrI,IAAI,EAAEjD;IADQ,CAAD,CAxBoO;IA0BjP8U,iBAAiB,EAAE,CAAC;MACpB7R,IAAI,EAAEjD;IADc,CAAD,CA1B8N;IA4BjPmR,YAAY,EAAE,CAAC;MACflO,IAAI,EAAEjD;IADS,CAAD,CA5BmO;IA8BjP+U,OAAO,EAAE,CAAC;MACV9R,IAAI,EAAEjD;IADI,CAAD,CA9BwO;IAgCjPgV,QAAQ,EAAE,CAAC;MACX/R,IAAI,EAAEjD;IADK,CAAD,CAhCuO;IAkCjPwR,OAAO,EAAE,CAAC;MACVvO,IAAI,EAAEjD;IADI,CAAD,CAlCwO;IAoCjP4H,QAAQ,EAAE,CAAC;MACX3E,IAAI,EAAEjD;IADK,CAAD,CApCuO;IAsCjPiV,SAAS,EAAE,CAAC;MACZhS,IAAI,EAAEjD;IADM,CAAD,CAtCsO;IAwCjPmE,iBAAiB,EAAE,CAAC;MACpBlB,IAAI,EAAEjD;IADc,CAAD,CAxC8N;IA0CjPoE,YAAY,EAAE,CAAC;MACfnB,IAAI,EAAEjD;IADS,CAAD,CA1CmO;IA4CjPyJ,WAAW,EAAE,CAAC;MACdxG,IAAI,EAAEjD;IADQ,CAAD,CA5CoO;IA8CjP4J,WAAW,EAAE,CAAC;MACd3G,IAAI,EAAEjD;IADQ,CAAD,CA9CoO;IAgDjP8J,cAAc,EAAE,CAAC;MACjB7G,IAAI,EAAEjD;IADW,CAAD,CAhDiO;IAkDjPiK,gBAAgB,EAAE,CAAC;MACnBhH,IAAI,EAAEjD;IADa,CAAD,CAlD+N;IAoDjPqE,mBAAmB,EAAE,CAAC;MACtBpB,IAAI,EAAEjD;IADgB,CAAD,CApD4N;IAsDjPsE,gBAAgB,EAAE,CAAC;MACnBrB,IAAI,EAAEjD;IADa,CAAD,CAtD+N;IAwDjPuL,KAAK,EAAE,CAAC;MACRtI,IAAI,EAAEjD;IADE,CAAD,CAxD0O;IA0DjPkV,SAAS,EAAE,CAAC;MACZjS,IAAI,EAAEjD;IADM,CAAD,CA1DsO;IA4DjPuE,kBAAkB,EAAE,CAAC;MACrBtB,IAAI,EAAEjD;IADe,CAAD,CA5D6N;IA8DjPwE,YAAY,EAAE,CAAC;MACfvB,IAAI,EAAEjD;IADS,CAAD,CA9DmO;IAgEjPyE,IAAI,EAAE,CAAC;MACPxB,IAAI,EAAEjD;IADC,CAAD,CAhE2O;IAkEjPgN,aAAa,EAAE,CAAC;MAChB/J,IAAI,EAAEjD;IADU,CAAD,CAlEkO;IAoEjPmV,qBAAqB,EAAE,CAAC;MACxBlS,IAAI,EAAEjD;IADkB,CAAD,CApE0N;IAsEjPoV,oBAAoB,EAAE,CAAC;MACvBnS,IAAI,EAAEjD;IADiB,CAAD,CAtE2N;IAwEjP0E,UAAU,EAAE,CAAC;MACbzB,IAAI,EAAEjD;IADO,CAAD,CAxEqO;IA0EjP2E,UAAU,EAAE,CAAC;MACb1B,IAAI,EAAEjD;IADO,CAAD,CA1EqO;IA4EjP4E,qBAAqB,EAAE,CAAC;MACxB3B,IAAI,EAAEjD;IADkB,CAAD,CA5E0N;IA8EjP6E,qBAAqB,EAAE,CAAC;MACxB5B,IAAI,EAAEjD;IADkB,CAAD,CA9E0N;IAgFjPqV,eAAe,EAAE,CAAC;MAClBpS,IAAI,EAAEjD;IADY,CAAD,CAhFgO;IAkFjPsV,SAAS,EAAE,CAAC;MACZrS,IAAI,EAAEjD;IADM,CAAD,CAlFsO;IAoFjPuV,cAAc,EAAE,CAAC;MACjBtS,IAAI,EAAEjD;IADW,CAAD,CApFiO;IAsFjP8E,eAAe,EAAE,CAAC;MAClB7B,IAAI,EAAEjD;IADY,CAAD,CAtFgO;IAwFjPwV,SAAS,EAAE,CAAC;MACZvS,IAAI,EAAEjD;IADM,CAAD,CAxFsO;IA0FjP+E,OAAO,EAAE,CAAC;MACV9B,IAAI,EAAEjD;IADI,CAAD,CA1FwO;IA4FjPgF,eAAe,EAAE,CAAC;MAClB/B,IAAI,EAAEjD;IADY,CAAD,CA5FgO;IA8FjPiF,oBAAoB,EAAE,CAAC;MACvBhC,IAAI,EAAEjD;IADiB,CAAD,CA9F2N;IAgGjPyV,iBAAiB,EAAE,CAAC;MACpBxS,IAAI,EAAEjD;IADc,CAAD,CAhG8N;IAkGjPkF,eAAe,EAAE,CAAC;MAClBjC,IAAI,EAAEjD;IADY,CAAD,CAlGgO;IAoGjPmF,QAAQ,EAAE,CAAC;MACXlC,IAAI,EAAEhD;IADK,CAAD,CApGuO;IAsGjPmF,QAAQ,EAAE,CAAC;MACXnC,IAAI,EAAEhD;IADK,CAAD,CAtGuO;IAwGjPoF,OAAO,EAAE,CAAC;MACVpC,IAAI,EAAEhD;IADI,CAAD,CAxGwO;IA0GjPqF,MAAM,EAAE,CAAC;MACTrC,IAAI,EAAEhD;IADG,CAAD,CA1GyO;IA4GjPmC,OAAO,EAAE,CAAC;MACVa,IAAI,EAAEhD;IADI,CAAD,CA5GwO;IA8GjPsF,MAAM,EAAE,CAAC;MACTtC,IAAI,EAAEhD;IADG,CAAD,CA9GyO;IAgHjPuF,MAAM,EAAE,CAAC;MACTvC,IAAI,EAAEhD;IADG,CAAD,CAhHyO;IAkHjPwF,OAAO,EAAE,CAAC;MACVxC,IAAI,EAAEhD;IADI,CAAD,CAlHwO;IAoHjPyF,UAAU,EAAE,CAAC;MACbzC,IAAI,EAAEhD;IADO,CAAD,CApHqO;IAsHjPyO,kBAAkB,EAAE,CAAC;MACrBzL,IAAI,EAAE7C,SADe;MAErB8C,IAAI,EAAE,CAAC,WAAD;IAFe,CAAD,CAtH6N;IAyHjPmI,eAAe,EAAE,CAAC;MAClBpI,IAAI,EAAE7C,SADY;MAElB8C,IAAI,EAAE,CAAC,QAAD;IAFY,CAAD,CAzHgO;IA4HjPoH,mBAAmB,EAAE,CAAC;MACtBrH,IAAI,EAAE7C,SADgB;MAEtB8C,IAAI,EAAE,CAAC,IAAD;IAFgB,CAAD,CA5H4N;IA+HjPqG,sBAAsB,EAAE,CAAC;MACzBtG,IAAI,EAAE7C,SADmB;MAEzB8C,IAAI,EAAE,CAAC,eAAD;IAFmB,CAAD,CA/HyN;IAkIjPiK,cAAc,EAAE,CAAC;MACjBlK,IAAI,EAAE7C,SADW;MAEjB8C,IAAI,EAAE,CAAC,OAAD;IAFW,CAAD,CAlIiO;IAqIjP+J,QAAQ,EAAE,CAAC;MACXhK,IAAI,EAAE7C,SADK;MAEX8C,IAAI,EAAE,CAAC,UAAD;IAFK,CAAD,CArIuO;IAwIjPuD,SAAS,EAAE,CAAC;MACZxD,IAAI,EAAE5C,eADM;MAEZ6C,IAAI,EAAE,CAACnC,aAAD;IAFM,CAAD,CAxIsO;IA2IjPyC,QAAQ,EAAE,CAAC;MACXP,IAAI,EAAEjD;IADK,CAAD,CA3IuO;IA6IjP0D,QAAQ,EAAE,CAAC;MACXT,IAAI,EAAEjD;IADK,CAAD,CA7IuO;IA+IjPwH,OAAO,EAAE,CAAC;MACVvE,IAAI,EAAEjD;IADI,CAAD,CA/IwO;IAiJjP2I,WAAW,EAAE,CAAC;MACd1F,IAAI,EAAEjD;IADQ,CAAD;EAjJoO,CA1GjQ;AAAA;;AA8PA,MAAM0V,cAAN,CAAqB;;AAErBA,cAAc,CAAChT,IAAf;EAAA,iBAA2GgT,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBA1qC+F/V,EA0qC/F;EAAA,MAA4G8V;AAA5G;AACAA,cAAc,CAACE,IAAf,kBA3qC+FhW,EA2qC/F;EAAA,UAAsIgB,YAAtI,EAAoJI,YAApJ,EAAkKQ,aAAlK,EAAiLI,YAAjL,EAA+LF,cAA/L,EAA+MV,YAA/M,EAA6NU,cAA7N;AAAA;;AACA;EAAA,mDA5qC+F9B,EA4qC/F,mBAA2F8V,cAA3F,EAAuH,CAAC;IAC5GzS,IAAI,EAAE3C,QADsG;IAE5G4C,IAAI,EAAE,CAAC;MACC2S,OAAO,EAAE,CAACjV,YAAD,EAAeI,YAAf,EAA6BQ,aAA7B,EAA4CI,YAA5C,EAA0DF,cAA1D,CADV;MAECoU,OAAO,EAAE,CAAC9T,QAAD,EAAWhB,YAAX,EAAyBU,cAAzB,CAFV;MAGCqU,YAAY,EAAE,CAAC/T,QAAD,EAAWE,YAAX;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASL,uBAAT,EAAkCG,QAAlC,EAA4CE,YAA5C,EAA0DwT,cAA1D"}, "metadata": {}, "sourceType": "module"}