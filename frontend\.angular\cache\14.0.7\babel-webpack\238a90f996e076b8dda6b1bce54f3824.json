{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nexport class AsapAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    var _a;\n\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n\n    const {\n      actions\n    } = scheduler;\n\n    if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      immediateProvider.clearImmediate(id);\n      scheduler._scheduled = undefined;\n    }\n\n    return undefined;\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "immediate<PERSON>rovider", "AsapAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "setImmediate", "flush", "bind", "undefined", "recycleAsyncId", "_a", "length", "clearImmediate"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/AsapAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nexport class AsapAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            immediateProvider.clearImmediate(id);\n            scheduler._scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,iBAAT,QAAkC,qBAAlC;AACA,OAAO,MAAMC,UAAN,SAAyBF,WAAzB,CAAqC;EACxCG,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;EACH;;EACDC,cAAc,CAACF,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA9B,EAAiC;MAC7B,OAAO,MAAMF,cAAN,CAAqBF,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACDJ,SAAS,CAACK,OAAV,CAAkBC,IAAlB,CAAuB,IAAvB;IACA,OAAON,SAAS,CAACO,UAAV,KAAyBP,SAAS,CAACO,UAAV,GAAuBV,iBAAiB,CAACW,YAAlB,CAA+BR,SAAS,CAACS,KAAV,CAAgBC,IAAhB,CAAqBV,SAArB,EAAgCW,SAAhC,CAA/B,CAAhD,CAAP;EACH;;EACDC,cAAc,CAACZ,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIS,EAAJ;;IACA,IAAIT,KAAK,IAAI,IAAT,GAAgBA,KAAK,GAAG,CAAxB,GAA4B,KAAKA,KAAL,GAAa,CAA7C,EAAgD;MAC5C,OAAO,MAAMQ,cAAN,CAAqBZ,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACD,MAAM;MAAEC;IAAF,IAAcL,SAApB;;IACA,IAAIG,EAAE,IAAI,IAAN,IAAc,CAAC,CAACU,EAAE,GAAGR,OAAO,CAACA,OAAO,CAACS,MAAR,GAAiB,CAAlB,CAAb,MAAuC,IAAvC,IAA+CD,EAAE,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,EAAE,CAACV,EAA5E,MAAoFA,EAAtG,EAA0G;MACtGN,iBAAiB,CAACkB,cAAlB,CAAiCZ,EAAjC;MACAH,SAAS,CAACO,UAAV,GAAuBI,SAAvB;IACH;;IACD,OAAOA,SAAP;EACH;;AAxBuC"}, "metadata": {}, "sourceType": "module"}