{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nfunction Breadcrumb_li_2_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r5.home.icon || \"pi pi-home\")(\"ngStyle\", ctx_r5.home.iconStyle);\n  }\n}\n\nfunction Breadcrumb_li_2_a_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r7.home.label);\n  }\n}\n\nfunction Breadcrumb_li_2_a_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r9.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Breadcrumb_li_2_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_ng_container_2_span_1_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, Breadcrumb_li_2_a_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r8 = i0.ɵɵreference(3);\n\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.home.escape !== false)(\"ngIfElse\", _r8);\n  }\n}\n\nfunction Breadcrumb_li_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.itemClick($event, ctx_r10.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_span_1_Template, 1, 2, \"span\", 8);\n    i0.ɵɵtemplate(2, Breadcrumb_li_2_a_1_ng_container_2_Template, 4, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", ctx_r3.home.url ? ctx_r3.home.url : null, i0.ɵɵsanitizeUrl)(\"target\", ctx_r3.home.target);\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.homeAriaLabel)(\"title\", ctx_r3.home.title)(\"id\", ctx_r3.home.id)(\"tabindex\", ctx_r3.home.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.home.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.home.label);\n  }\n}\n\nfunction Breadcrumb_li_2_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r12.home.icon || \"pi pi-home\")(\"ngStyle\", ctx_r12.home.iconStyle);\n  }\n}\n\nfunction Breadcrumb_li_2_a_2_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.home.label);\n  }\n}\n\nfunction Breadcrumb_li_2_a_2_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r16.home.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Breadcrumb_li_2_a_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_ng_container_2_span_1_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, Breadcrumb_li_2_a_2_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 16, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(3);\n\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r13.home.escape !== false)(\"ngIfElse\", _r15);\n  }\n}\n\nconst _c0 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction Breadcrumb_li_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_li_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r17.itemClick($event, ctx_r17.home));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_2_span_1_Template, 1, 2, \"span\", 8);\n    i0.ɵɵtemplate(2, Breadcrumb_li_2_a_2_ng_container_2_Template, 4, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.home.routerLink)(\"queryParams\", ctx_r4.home.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r4.home.routerLinkActiveOptions || i0.ɵɵpureFunction0(17, _c0))(\"target\", ctx_r4.home.target)(\"fragment\", ctx_r4.home.fragment)(\"queryParamsHandling\", ctx_r4.home.queryParamsHandling)(\"preserveFragment\", ctx_r4.home.preserveFragment)(\"skipLocationChange\", ctx_r4.home.skipLocationChange)(\"replaceUrl\", ctx_r4.home.replaceUrl)(\"state\", ctx_r4.home.state);\n    i0.ɵɵattribute(\"aria-label\", ctx_r4.homeAriaLabel)(\"title\", ctx_r4.home.title)(\"id\", ctx_r4.home.id)(\"tabindex\", ctx_r4.home.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.home.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.home.label);\n  }\n}\n\nconst _c1 = function (a1) {\n  return {\n    \"p-breadcrumb-home\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction Breadcrumb_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 4);\n    i0.ɵɵtemplate(1, Breadcrumb_li_2_a_1_Template, 3, 8, \"a\", 5);\n    i0.ɵɵtemplate(2, Breadcrumb_li_2_a_2_Template, 3, 18, \"a\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.home.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r0.home.disabled))(\"ngStyle\", ctx_r0.home.style)(\"tooltipOptions\", ctx_r0.home.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.home.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.home.routerLink);\n  }\n}\n\nfunction Breadcrumb_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 17);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r19.icon)(\"ngStyle\", item_r19.iconStyle);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r19.label);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r19.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_ng_container_2_span_1_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, Breadcrumb_ng_template_4_a_1_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 21, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(3);\n\n    const item_r19 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.escape !== false)(\"ngIfElse\", _r28);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 20);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const item_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.itemClick($event, item_r19));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_span_1_Template, 1, 2, \"span\", 8);\n    i0.ɵɵtemplate(2, Breadcrumb_ng_template_4_a_1_ng_container_2_Template, 4, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"target\", item_r19.target);\n    i0.ɵɵattribute(\"href\", item_r19.url ? item_r19.url : null, i0.ɵɵsanitizeUrl)(\"title\", item_r19.title)(\"id\", item_r19.id)(\"tabindex\", item_r19.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.label);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r19.icon)(\"ngStyle\", item_r19.iconStyle);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r19.label);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r19.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_ng_container_2_span_1_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(2, Breadcrumb_ng_template_4_a_2_ng_container_2_ng_template_2_Template, 1, 1, \"ng-template\", null, 22, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r41 = i0.ɵɵreference(3);\n\n    const item_r19 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.escape !== false)(\"ngIfElse\", _r41);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function Breadcrumb_ng_template_4_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r48);\n      const item_r19 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.itemClick($event, item_r19));\n    });\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_2_span_1_Template, 1, 2, \"span\", 8);\n    i0.ɵɵtemplate(2, Breadcrumb_ng_template_4_a_2_ng_container_2_Template, 4, 2, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r19.routerLink)(\"queryParams\", item_r19.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r19.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c0))(\"target\", item_r19.target)(\"fragment\", item_r19.fragment)(\"queryParamsHandling\", item_r19.queryParamsHandling)(\"preserveFragment\", item_r19.preserveFragment)(\"skipLocationChange\", item_r19.skipLocationChange)(\"replaceUrl\", item_r19.replaceUrl)(\"state\", item_r19.state);\n    i0.ɵɵattribute(\"title\", item_r19.title)(\"id\", item_r19.id)(\"tabindex\", item_r19.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.label);\n  }\n}\n\nfunction Breadcrumb_ng_template_4_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 17);\n  }\n}\n\nconst _c2 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\n\nfunction Breadcrumb_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 18);\n    i0.ɵɵtemplate(1, Breadcrumb_ng_template_4_a_1_Template, 3, 7, \"a\", 19);\n    i0.ɵɵtemplate(2, Breadcrumb_ng_template_4_a_2_Template, 3, 17, \"a\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Breadcrumb_ng_template_4_li_3_Template, 1, 0, \"li\", 2);\n  }\n\n  if (rf & 2) {\n    const item_r19 = ctx.$implicit;\n    const end_r20 = ctx.last;\n    i0.ɵɵclassMap(item_r19.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r19.style)(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, item_r19.disabled))(\"tooltipOptions\", item_r19.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r19.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r19.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !end_r20);\n  }\n}\n\nclass Breadcrumb {\n  constructor() {\n    this.onItemClick = new EventEmitter();\n  }\n\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    this.onItemClick.emit({\n      originalEvent: event,\n      item: item\n    });\n  }\n\n  onHomeClick(event) {\n    if (this.home) {\n      this.itemClick(event, this.home);\n    }\n  }\n\n}\n\nBreadcrumb.ɵfac = function Breadcrumb_Factory(t) {\n  return new (t || Breadcrumb)();\n};\n\nBreadcrumb.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Breadcrumb,\n  selectors: [[\"p-breadcrumb\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    home: \"home\",\n    homeAriaLabel: \"homeAriaLabel\"\n  },\n  outputs: {\n    onItemClick: \"onItemClick\"\n  },\n  decls: 5,\n  vars: 7,\n  consts: [[3, \"ngStyle\", \"ngClass\"], [\"pTooltip\", \"\", 3, \"class\", \"ngClass\", \"ngStyle\", \"tooltipOptions\", 4, \"ngIf\"], [\"class\", \"p-breadcrumb-chevron pi pi-chevron-right\", 4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", 3, \"href\", \"target\", \"click\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"href\", \"target\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlHomeLabel\", \"\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\"], [\"htmlHomeRouteLabel\", \"\"], [1, \"p-breadcrumb-chevron\", \"pi\", \"pi-chevron-right\"], [\"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", 3, \"target\", \"click\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"target\", \"click\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"]],\n  template: function Breadcrumb_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"ul\");\n      i0.ɵɵtemplate(2, Breadcrumb_li_2_Template, 3, 9, \"li\", 1);\n      i0.ɵɵtemplate(3, Breadcrumb_li_3_Template, 1, 0, \"li\", 2);\n      i0.ɵɵtemplate(4, Breadcrumb_ng_template_4_Template, 4, 10, \"ng-template\", 3);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-breadcrumb p-component\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.home);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.home);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.model);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Tooltip],\n  styles: [\".p-breadcrumb{overflow-x:auto}.p-breadcrumb ul{margin:0;padding:0;list-style-type:none;display:flex;align-items:center;flex-wrap:nowrap}.p-breadcrumb .p-menuitem-text{line-height:1}.p-breadcrumb .p-menuitem-link{text-decoration:none;cursor:pointer}.p-breadcrumb::-webkit-scrollbar{display:none}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Breadcrumb, [{\n    type: Component,\n    args: [{\n      selector: 'p-breadcrumb',\n      template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-breadcrumb p-component'\">\n            <ul>\n                <li [class]=\"home.styleClass\" [ngClass]=\"{'p-breadcrumb-home': true, 'p-disabled':home.disabled}\" [ngStyle]=\"home.style\" *ngIf=\"home\" pTooltip [tooltipOptions]=\"home.tooltipOptions\">\n                    <a *ngIf=\"!home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [href]=\"home.url ? home.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                    <a *ngIf=\"home.routerLink\" [routerLink]=\"home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [queryParams]=\"home.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"home.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\"\n                        [fragment]=\"home.fragment\" [queryParamsHandling]=\"home.queryParamsHandling\" [preserveFragment]=\"home.preserveFragment\" [skipLocationChange]=\"home.skipLocationChange\" [replaceUrl]=\"home.replaceUrl\" [state]=\"home.state\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeRouteLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                </li>\n                <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"model&&home\"></li>\n                <ng-template ngFor let-item let-end=\"last\" [ngForOf]=\"model\">\n                    <li [class]=\"item.styleClass\" [ngStyle]=\"item.style\" [ngClass]=\"{'p-disabled':item.disabled}\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url ? item.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"  [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlRouteLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                    </li>\n                    <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"!end\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-breadcrumb{overflow-x:auto}.p-breadcrumb ul{margin:0;padding:0;list-style-type:none;display:flex;align-items:center;flex-wrap:nowrap}.p-breadcrumb .p-menuitem-text{line-height:1}.p-breadcrumb .p-menuitem-link{text-decoration:none;cursor:pointer}.p-breadcrumb::-webkit-scrollbar{display:none}\\n\"]\n    }]\n  }], null, {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    home: [{\n      type: Input\n    }],\n    homeAriaLabel: [{\n      type: Input\n    }],\n    onItemClick: [{\n      type: Output\n    }]\n  });\n})();\n\nclass BreadcrumbModule {}\n\nBreadcrumbModule.ɵfac = function BreadcrumbModule_Factory(t) {\n  return new (t || BreadcrumbModule)();\n};\n\nBreadcrumbModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BreadcrumbModule\n});\nBreadcrumbModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BreadcrumbModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, TooltipModule],\n      exports: [Breadcrumb, RouterModule, TooltipModule],\n      declarations: [Breadcrumb]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Breadcrumb, BreadcrumbModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "i1", "CommonModule", "i2", "RouterModule", "i3", "TooltipModule", "Breadcrumb", "constructor", "onItemClick", "itemClick", "event", "item", "disabled", "preventDefault", "url", "routerLink", "command", "originalEvent", "emit", "onHomeClick", "home", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "model", "style", "styleClass", "homeAriaLabel", "BreadcrumbModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-breadcrumb.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nclass Breadcrumb {\n    constructor() {\n        this.onItemClick = new EventEmitter();\n    }\n    itemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        this.onItemClick.emit({\n            originalEvent: event,\n            item: item\n        });\n    }\n    onHomeClick(event) {\n        if (this.home) {\n            this.itemClick(event, this.home);\n        }\n    }\n}\nBreadcrumb.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Breadcrumb, deps: [], target: i0.ɵɵFactoryTarget.Component });\nBreadcrumb.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Breadcrumb, selector: \"p-breadcrumb\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", home: \"home\", homeAriaLabel: \"homeAriaLabel\" }, outputs: { onItemClick: \"onItemClick\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-breadcrumb p-component'\">\n            <ul>\n                <li [class]=\"home.styleClass\" [ngClass]=\"{'p-breadcrumb-home': true, 'p-disabled':home.disabled}\" [ngStyle]=\"home.style\" *ngIf=\"home\" pTooltip [tooltipOptions]=\"home.tooltipOptions\">\n                    <a *ngIf=\"!home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [href]=\"home.url ? home.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                    <a *ngIf=\"home.routerLink\" [routerLink]=\"home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [queryParams]=\"home.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"home.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\"\n                        [fragment]=\"home.fragment\" [queryParamsHandling]=\"home.queryParamsHandling\" [preserveFragment]=\"home.preserveFragment\" [skipLocationChange]=\"home.skipLocationChange\" [replaceUrl]=\"home.replaceUrl\" [state]=\"home.state\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeRouteLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                </li>\n                <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"model&&home\"></li>\n                <ng-template ngFor let-item let-end=\"last\" [ngForOf]=\"model\">\n                    <li [class]=\"item.styleClass\" [ngStyle]=\"item.style\" [ngClass]=\"{'p-disabled':item.disabled}\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url ? item.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"  [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlRouteLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                    </li>\n                    <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"!end\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `, isInline: true, styles: [\".p-breadcrumb{overflow-x:auto}.p-breadcrumb ul{margin:0;padding:0;list-style-type:none;display:flex;align-items:center;flex-wrap:nowrap}.p-breadcrumb .p-menuitem-text{line-height:1}.p-breadcrumb .p-menuitem-link{text-decoration:none;cursor:pointer}.p-breadcrumb::-webkit-scrollbar{display:none}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Breadcrumb, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-breadcrumb', template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-breadcrumb p-component'\">\n            <ul>\n                <li [class]=\"home.styleClass\" [ngClass]=\"{'p-breadcrumb-home': true, 'p-disabled':home.disabled}\" [ngStyle]=\"home.style\" *ngIf=\"home\" pTooltip [tooltipOptions]=\"home.tooltipOptions\">\n                    <a *ngIf=\"!home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [href]=\"home.url ? home.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                    <a *ngIf=\"home.routerLink\" [routerLink]=\"home.routerLink\" [attr.aria-label]=\"homeAriaLabel\" [queryParams]=\"home.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"home.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, home)\"\n                        [target]=\"home.target\" [attr.title]=\"home.title\" [attr.id]=\"home.id\" [attr.tabindex]=\"home.disabled ? null : '0'\"\n                        [fragment]=\"home.fragment\" [queryParamsHandling]=\"home.queryParamsHandling\" [preserveFragment]=\"home.preserveFragment\" [skipLocationChange]=\"home.skipLocationChange\" [replaceUrl]=\"home.replaceUrl\" [state]=\"home.state\">\n                        <span *ngIf=\"home.icon\" class=\"p-menuitem-icon\" [ngClass]=\"home.icon||'pi pi-home'\" [ngStyle]=\"home.iconStyle\"></span>\n                        <ng-container *ngIf=\"home.label\">\n                            <span *ngIf=\"home.escape !== false; else htmlHomeRouteLabel\" class=\"p-menuitem-text\">{{home.label}}</span>\n                            <ng-template #htmlHomeRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"home.label\"></span></ng-template>\n                        </ng-container>\n                    </a>\n                </li>\n                <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"model&&home\"></li>\n                <ng-template ngFor let-item let-end=\"last\" [ngForOf]=\"model\">\n                    <li [class]=\"item.styleClass\" [ngStyle]=\"item.style\" [ngClass]=\"{'p-disabled':item.disabled}\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url ? item.url : null\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"  [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" (click)=\"itemClick($event, item)\"\n                            [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.icon\" class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <ng-container *ngIf=\"item.label\">\n                                <span *ngIf=\"item.escape !== false; else htmlRouteLabel\" class=\"p-menuitem-text\">{{item.label}}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            </ng-container>\n                        </a>\n                    </li>\n                    <li class=\"p-breadcrumb-chevron pi pi-chevron-right\" *ngIf=\"!end\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-breadcrumb{overflow-x:auto}.p-breadcrumb ul{margin:0;padding:0;list-style-type:none;display:flex;align-items:center;flex-wrap:nowrap}.p-breadcrumb .p-menuitem-text{line-height:1}.p-breadcrumb .p-menuitem-link{text-decoration:none;cursor:pointer}.p-breadcrumb::-webkit-scrollbar{display:none}\\n\"] }]\n        }], propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], home: [{\n                type: Input\n            }], homeAriaLabel: [{\n                type: Input\n            }], onItemClick: [{\n                type: Output\n            }] } });\nclass BreadcrumbModule {\n}\nBreadcrumbModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BreadcrumbModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBreadcrumbModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: BreadcrumbModule, declarations: [Breadcrumb], imports: [CommonModule, RouterModule, TooltipModule], exports: [Breadcrumb, RouterModule, TooltipModule] });\nBreadcrumbModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BreadcrumbModule, imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: BreadcrumbModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, TooltipModule],\n                    exports: [Breadcrumb, RouterModule, TooltipModule],\n                    declarations: [Breadcrumb]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Breadcrumb, BreadcrumbModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;;;;IA+B6Fb,EAOrE,yB;;;;mBAPqEA,E;IAAAA,EAOrB,0F;;;;;;IAPqBA,EASjE,8B;IATiEA,EASe,U;IATfA,EAS6B,e;;;;mBAT7BA,E;IAAAA,EASe,a;IATfA,EASe,qC;;;;;;IATfA,EAUrC,yB;;;;mBAVqCA,E;IAAAA,EAUP,4CAVOA,EAUP,gB;;;;;;IAVOA,EAQrE,2B;IARqEA,EASjE,oF;IATiEA,EAUjE,yGAViEA,EAUjE,wB;IAViEA,EAWrE,wB;;;;gBAXqEA,E;;mBAAAA,E;IAAAA,EAS1D,a;IAT0DA,EAS1D,kE;;;;;;iBAT0DA,E;;IAAAA,EAKzE,0B;IALyEA,EAKiD;MALjDA,EAKiD;MAAA,gBALjDA,EAKiD;MAAA,OALjDA,EAK0D,qDAAT;IAAA,E;IALjDA,EAOrE,oE;IAPqEA,EAQrE,oF;IARqEA,EAYzE,e;;;;mBAZyEA,E;IAAAA,EAKX,8DALWA,EAKX,6C;IALWA,EAK7C,iJ;IAL6CA,EAO9D,a;IAP8DA,EAO9D,qC;IAP8DA,EAQtD,a;IARsDA,EAQtD,sC;;;;;;IARsDA,EAgBrE,yB;;;;oBAhBqEA,E;IAAAA,EAgBrB,4F;;;;;;IAhBqBA,EAkBjE,8B;IAlBiEA,EAkBoB,U;IAlBpBA,EAkBkC,e;;;;oBAlBlCA,E;IAAAA,EAkBoB,a;IAlBpBA,EAkBoB,sC;;;;;;IAlBpBA,EAmBhC,yB;;;;oBAnBgCA,E;IAAAA,EAmBF,6CAnBEA,EAmBF,gB;;;;;;IAnBEA,EAiBrE,2B;IAjBqEA,EAkBjE,oF;IAlBiEA,EAmBjE,yGAnBiEA,EAmBjE,wB;IAnBiEA,EAoBrE,wB;;;;iBApBqEA,E;;oBAAAA,E;IAAAA,EAkB1D,a;IAlB0DA,EAkB1D,oE;;;;;;;;;;;;iBAlB0DA,E;;IAAAA,EAazE,2B;IAbyEA,EAakM;MAblMA,EAakM;MAAA,gBAblMA,EAakM;MAAA,OAblMA,EAa2M,qDAAT;IAAA,E;IAblMA,EAgBrE,oE;IAhBqEA,EAiBrE,oF;IAjBqEA,EAqBzE,e;;;;mBArByEA,E;IAAAA,EAa9C,0MAb8CA,EAa9C,4T;IAb8CA,EAaf,iJ;IAbeA,EAgB9D,a;IAhB8DA,EAgB9D,qC;IAhB8DA,EAiBtD,a;IAjBsDA,EAiBtD,sC;;;;;;;;;;;;;IAjBsDA,EAI7E,2B;IAJ6EA,EAKzE,0D;IALyEA,EAazE,2D;IAbyEA,EAsB7E,e;;;;mBAtB6EA,E;IAAAA,EAIzE,mC;IAJyEA,EAI/C,uBAJ+CA,EAI/C,2H;IAJ+CA,EAKrE,a;IALqEA,EAKrE,4C;IALqEA,EAarE,a;IAbqEA,EAarE,2C;;;;;;IAbqEA,EAuB7E,uB;;;;;;IAvB6EA,EA4BjE,yB;;;;qBA5BiEA,E;IAAAA,EA4BjB,oE;;;;;;IA5BiBA,EA8B7D,8B;IA9B6DA,EA8Be,U;IA9BfA,EA8B6B,e;;;;qBA9B7BA,E;IAAAA,EA8Be,a;IA9BfA,EA8Be,kC;;;;;;IA9BfA,EA+BrC,yB;;;;qBA/BqCA,E;IAAAA,EA+BP,yCA/BOA,EA+BP,gB;;;;;;IA/BOA,EA6BjE,2B;IA7BiEA,EA8B7D,6F;IA9B6DA,EA+B7D,kHA/B6DA,EA+B7D,wB;IA/B6DA,EAgCjE,wB;;;;iBAhCiEA,E;;qBAAAA,E;IAAAA,EA8BtD,a;IA9BsDA,EA8BtD,gE;;;;;;iBA9BsDA,E;;IAAAA,EA0BrE,2B;IA1BqEA,EA0BwB;MA1BxBA,EA0BwB;MAAA,iBA1BxBA,EA0BwB;MAAA,gBA1BxBA,EA0BwB;MAAA,OA1BxBA,EA0BiC,iDAAT;IAAA,E;IA1BxBA,EA4BjE,6E;IA5BiEA,EA6BjE,6F;IA7BiEA,EAiCrE,e;;;;qBAjCqEA,E;IAAAA,EA2BjE,sC;IA3BiEA,EA0BzC,yDA1ByCA,EA0BzC,uG;IA1ByCA,EA4B1D,a;IA5B0DA,EA4B1D,kC;IA5B0DA,EA6BlD,a;IA7BkDA,EA6BlD,mC;;;;;;IA7BkDA,EAqCjE,yB;;;;qBArCiEA,E;IAAAA,EAqCjB,oE;;;;;;IArCiBA,EAuC7D,8B;IAvC6DA,EAuCoB,U;IAvCpBA,EAuCkC,e;;;;qBAvClCA,E;IAAAA,EAuCoB,a;IAvCpBA,EAuCoB,kC;;;;;;IAvCpBA,EAwChC,yB;;;;qBAxCgCA,E;IAAAA,EAwCF,yCAxCEA,EAwCF,gB;;;;;;IAxCEA,EAsCjE,2B;IAtCiEA,EAuC7D,6F;IAvC6DA,EAwC7D,kHAxC6DA,EAwC7D,wB;IAxC6DA,EAyCjE,wB;;;;iBAzCiEA,E;;qBAAAA,E;IAAAA,EAuCtD,a;IAvCsDA,EAuCtD,gE;;;;;;iBAvCsDA,E;;IAAAA,EAkCrE,2B;IAlCqEA,EAkCqK;MAlCrKA,EAkCqK;MAAA,iBAlCrKA,EAkCqK;MAAA,gBAlCrKA,EAkCqK;MAAA,OAlCrKA,EAkC8K,iDAAT;IAAA,E;IAlCrKA,EAqCjE,6E;IArCiEA,EAsCjE,6F;IAtCiEA,EA0CrE,e;;;;qBA1CqEA,E;IAAAA,EAkC1C,iMAlC0CA,EAkC1C,uS;IAlC0CA,EAmC1C,oG;IAnC0CA,EAqC1D,a;IArC0DA,EAqC1D,kC;IArC0DA,EAsClD,a;IAtCkDA,EAsClD,mC;;;;;;IAtCkDA,EA4CzE,uB;;;;;;;;;;;;IA5CyEA,EAyBzE,4B;IAzByEA,EA0BrE,oE;IA1BqEA,EAkCrE,oE;IAlCqEA,EA2CzE,e;IA3CyEA,EA4CzE,qE;;;;;;IA5CyEA,EAyBrE,gC;IAzBqEA,EAyB3C,kDAzB2CA,EAyB3C,uF;IAzB2CA,EA0BjE,a;IA1BiEA,EA0BjE,yC;IA1BiEA,EAkCjE,a;IAlCiEA,EAkCjE,wC;IAlCiEA,EA4CnB,a;IA5CmBA,EA4CnB,6B;;;;AAzE1E,MAAMc,UAAN,CAAiB;EACbC,WAAW,GAAG;IACV,KAAKC,WAAL,GAAmB,IAAIf,YAAJ,EAAnB;EACH;;EACDgB,SAAS,CAACC,KAAD,EAAQC,IAAR,EAAc;IACnB,IAAIA,IAAI,CAACC,QAAT,EAAmB;MACfF,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,CAACF,IAAI,CAACG,GAAN,IAAa,CAACH,IAAI,CAACI,UAAvB,EAAmC;MAC/BL,KAAK,CAACG,cAAN;IACH;;IACD,IAAIF,IAAI,CAACK,OAAT,EAAkB;MACdL,IAAI,CAACK,OAAL,CAAa;QACTC,aAAa,EAAEP,KADN;QAETC,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,KAAKH,WAAL,CAAiBU,IAAjB,CAAsB;MAClBD,aAAa,EAAEP,KADG;MAElBC,IAAI,EAAEA;IAFY,CAAtB;EAIH;;EACDQ,WAAW,CAACT,KAAD,EAAQ;IACf,IAAI,KAAKU,IAAT,EAAe;MACX,KAAKX,SAAL,CAAeC,KAAf,EAAsB,KAAKU,IAA3B;IACH;EACJ;;AA3BY;;AA6BjBd,UAAU,CAACe,IAAX;EAAA,iBAAuGf,UAAvG;AAAA;;AACAA,UAAU,CAACgB,IAAX,kBAD6F9B,EAC7F;EAAA,MAA2Fc,UAA3F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD6Fd,EAErF,qCADR;MAD6FA,EAI7E,uDAHhB;MAD6FA,EAuB7E,uDAtBhB;MAD6FA,EAwB7E,0EAvBhB;MAD6FA,EA8CjF,iBA7CZ;IAAA;;IAAA;MAD6FA,EAEhF,2BADb;MAD6FA,EAE3D,wEADlC;MAD6FA,EAI6C,aAH1I;MAD6FA,EAI6C,6BAH1I;MAD6FA,EAuBvB,aAtBtE;MAD6FA,EAuBvB,0CAtBtE;MAD6FA,EAwBlC,aAvB3D;MAD6FA,EAwBlC,iCAvB3D;IAAA;EAAA;EAAA,eA+CuXQ,EAAE,CAACuB,OA/C1X,EA+CqdvB,EAAE,CAACwB,OA/Cxd,EA+CklBxB,EAAE,CAACyB,IA/CrlB,EA+CsrBzB,EAAE,CAAC0B,OA/CzrB,EA+C2wBxB,EAAE,CAACyB,kBA/C9wB,EA+C8gCzB,EAAE,CAAC0B,gBA/CjhC,EA+C+uCxB,EAAE,CAACyB,OA/ClvC;EAAA;EAAA;EAAA;AAAA;;AAgDA;EAAA,mDAjD6FrC,EAiD7F,mBAA2Fc,UAA3F,EAAmH,CAAC;IACxGwB,IAAI,EAAEpC,SADkG;IAExGqC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAZ;MAA4BC,QAAQ,EAAG;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA/CmB;MA+CZC,eAAe,EAAEvC,uBAAuB,CAACwC,MA/C7B;MA+CqCC,aAAa,EAAExC,iBAAiB,CAACyC,IA/CtE;MA+C4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CA/ClF;MAiDIC,MAAM,EAAE,CAAC,0SAAD;IAjDZ,CAAD;EAFkG,CAAD,CAAnH,QAoD4B;IAAEC,KAAK,EAAE,CAAC;MACtBV,IAAI,EAAEjC;IADgB,CAAD,CAAT;IAEZ4C,KAAK,EAAE,CAAC;MACRX,IAAI,EAAEjC;IADE,CAAD,CAFK;IAIZ6C,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEjC;IADO,CAAD,CAJA;IAMZuB,IAAI,EAAE,CAAC;MACPU,IAAI,EAAEjC;IADC,CAAD,CANM;IAQZ8C,aAAa,EAAE,CAAC;MAChBb,IAAI,EAAEjC;IADU,CAAD,CARH;IAUZW,WAAW,EAAE,CAAC;MACdsB,IAAI,EAAEhC;IADQ,CAAD;EAVD,CApD5B;AAAA;;AAiEA,MAAM8C,gBAAN,CAAuB;;AAEvBA,gBAAgB,CAACvB,IAAjB;EAAA,iBAA6GuB,gBAA7G;AAAA;;AACAA,gBAAgB,CAACC,IAAjB,kBArH6FrD,EAqH7F;EAAA,MAA8GoD;AAA9G;AACAA,gBAAgB,CAACE,IAAjB,kBAtH6FtD,EAsH7F;EAAA,UAA0IS,YAA1I,EAAwJE,YAAxJ,EAAsKE,aAAtK,EAAqLF,YAArL,EAAmME,aAAnM;AAAA;;AACA;EAAA,mDAvH6Fb,EAuH7F,mBAA2FoD,gBAA3F,EAAyH,CAAC;IAC9Gd,IAAI,EAAE/B,QADwG;IAE9GgC,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAAC9C,YAAD,EAAeE,YAAf,EAA6BE,aAA7B,CADV;MAEC2C,OAAO,EAAE,CAAC1C,UAAD,EAAaH,YAAb,EAA2BE,aAA3B,CAFV;MAGC4C,YAAY,EAAE,CAAC3C,UAAD;IAHf,CAAD;EAFwG,CAAD,CAAzH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,UAAT,EAAqBsC,gBAArB"}, "metadata": {}, "sourceType": "module"}