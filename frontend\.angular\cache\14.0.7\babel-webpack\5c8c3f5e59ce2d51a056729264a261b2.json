{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../../../services/statistics.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/chart\";\n\nfunction ChartsComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 2)(2, \"div\", 14)(3, \"div\", 15)(4, \"div\", 16)(5, \"div\")(6, \"span\", 17);\n    i0.ɵɵtext(7, \"Total Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 18);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 19);\n    i0.ɵɵelement(11, \"i\", 20);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"div\", 14)(13, \"div\", 15)(14, \"div\", 16)(15, \"div\")(16, \"span\", 17);\n    i0.ɵɵtext(17, \"Total Employees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 18);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 21);\n    i0.ɵɵelement(21, \"i\", 22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 14)(23, \"div\", 15)(24, \"div\", 16)(25, \"div\")(26, \"span\", 17);\n    i0.ɵɵtext(27, \"Total Teams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 18);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 23);\n    i0.ɵɵelement(31, \"i\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 14)(33, \"div\", 15)(34, \"div\", 16)(35, \"div\")(36, \"span\", 17);\n    i0.ɵɵtext(37, \"Attendance Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 18);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 25);\n    i0.ɵɵelement(41, \"i\", 26);\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r3.dashboardStats.totalFormations);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r3.dashboardStats.totalEmployees);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r3.dashboardStats.totalTeams);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.dashboardStats.globalAttendanceRate, \"%\");\n  }\n}\n\nconst _c0 = function () {\n  return {\n    \"width\": \"70%\"\n  };\n};\n\nfunction ChartsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChartsComponent_div_0_div_1_Template, 42, 4, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"h5\");\n    i0.ɵɵtext(5, \"Formation Attendance Trends\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-chart\", 6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 4)(8, \"div\", 5)(9, \"h5\");\n    i0.ɵɵtext(10, \"Team Performance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"p-chart\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 4)(13, \"div\", 8)(14, \"h5\", 9);\n    i0.ɵɵtext(15, \"Formation Status Distribution\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"p-chart\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 8)(19, \"h5\", 9);\n    i0.ɵɵtext(20, \"Monthly Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"p-chart\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 12)(23, \"div\", 5)(24, \"h5\");\n    i0.ɵɵtext(25, \"Team Speciality Analysis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"p-chart\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.dashboardStats);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r0.lineData)(\"options\", ctx_r0.lineOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r0.barData)(\"options\", ctx_r0.barOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(15, _c0));\n    i0.ɵɵproperty(\"data\", ctx_r0.pieData)(\"options\", ctx_r0.pieOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(16, _c0));\n    i0.ɵɵproperty(\"data\", ctx_r0.polarData)(\"options\", ctx_r0.polarOptions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r0.radarData)(\"options\", ctx_r0.radarOptions);\n  }\n}\n\nfunction ChartsComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 5)(2, \"div\", 27);\n    i0.ɵɵelement(3, \"p-progressSpinner\");\n    i0.ɵɵelementStart(4, \"span\", 28);\n    i0.ɵɵtext(5, \"Loading charts...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nexport class ChartsComponent {\n  constructor(layoutService, statisticsService, messageService) {\n    this.layoutService = layoutService;\n    this.statisticsService = statisticsService;\n    this.messageService = messageService; // Data\n\n    this.formationStats = [];\n    this.teamStats = [];\n    this.employeeStats = [];\n    this.dashboardStats = null;\n    this.monthlyFormations = []; // Loading states\n\n    this.loadingFormations = false;\n    this.loadingTeams = false;\n    this.loadingEmployees = false;\n    this.loading = true; // Filters\n\n    this.startDate = '';\n    this.endDate = '';\n    this.selectedTab = 0;\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initCharts();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initCharts();\n\n      yield _this.loadStatistics();\n    })();\n  }\n\n  loadStatistics() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.loading = true; // Load dashboard stats\n\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats()); // Load monthly formations\n\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations()); // Load formation stats\n\n        _this2.formationStats = yield lastValueFrom(_this2.statisticsService.getFormationStats()); // Load team stats\n\n        _this2.teamStats = yield lastValueFrom(_this2.statisticsService.getTeamStats()); // Load employee stats\n\n        _this2.employeeStats = yield lastValueFrom(_this2.statisticsService.getEmployeeAttendance(20)); // Update charts with real data\n\n        _this2.updateChartsWithRealData();\n      } catch (error) {\n        console.error('Error loading statistics:', error);\n\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load statistics',\n          life: 3000\n        });\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  initCharts() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border'); // Bar Chart\n\n    this.barData = {\n      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n      datasets: [{\n        label: 'Formations',\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        data: [65, 59, 80, 81, 56, 55, 40]\n      }, {\n        label: 'Attendance Rate',\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        data: [28, 48, 40, 19, 86, 27, 90]\n      }]\n    };\n    this.barOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary,\n            font: {\n              weight: 500\n            }\n          },\n          grid: {\n            display: false,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    }; // Pie Chart\n\n    this.pieData = {\n      labels: ['Present', 'Absent', 'Pending'],\n      datasets: [{\n        data: [540, 325, 702],\n        backgroundColor: [documentStyle.getPropertyValue('--green-500'), documentStyle.getPropertyValue('--red-500'), documentStyle.getPropertyValue('--yellow-500')],\n        hoverBackgroundColor: [documentStyle.getPropertyValue('--green-400'), documentStyle.getPropertyValue('--red-400'), documentStyle.getPropertyValue('--yellow-400')]\n      }]\n    };\n    this.pieOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            usePointStyle: true,\n            color: textColor\n          }\n        }\n      }\n    }; // Line Chart\n\n    this.lineData = {\n      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n      datasets: [{\n        label: 'Formations Count',\n        data: [65, 59, 80, 81, 56, 55, 40],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate',\n        data: [28, 48, 40, 19, 86, 27, 90],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4\n      }]\n    };\n    this.lineOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    }; // Polar Area Chart\n\n    this.polarData = {\n      datasets: [{\n        data: [11, 16, 7, 3],\n        backgroundColor: [documentStyle.getPropertyValue('--red-500'), documentStyle.getPropertyValue('--blue-500'), documentStyle.getPropertyValue('--yellow-500'), documentStyle.getPropertyValue('--green-500')],\n        label: 'Team Performance'\n      }],\n      labels: ['Team A', 'Team B', 'Team C', 'Team D']\n    };\n    this.polarOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        r: {\n          grid: {\n            color: surfaceBorder\n          }\n        }\n      }\n    }; // Radar Chart\n\n    this.radarData = {\n      labels: ['Attendance', 'Participation', 'Completion', 'Feedback', 'Performance', 'Engagement', 'Results'],\n      datasets: [{\n        label: 'Current Period',\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        pointBackgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        pointBorderColor: documentStyle.getPropertyValue('--primary-500'),\n        pointHoverBackgroundColor: textColor,\n        pointHoverBorderColor: documentStyle.getPropertyValue('--primary-500'),\n        data: [65, 59, 90, 81, 56, 55, 40]\n      }, {\n        label: 'Previous Period',\n        borderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointBackgroundColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointHoverBackgroundColor: textColor,\n        pointHoverBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        data: [28, 48, 40, 19, 96, 27, 100]\n      }]\n    };\n    this.radarOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        r: {\n          grid: {\n            color: textColorSecondary\n          }\n        }\n      }\n    };\n  }\n\n  updateChartsWithRealData() {\n    if (this.monthlyFormations.length > 0) {\n      const months = this.monthlyFormations.map(f => f.month);\n      const formationCounts = this.monthlyFormations.map(f => f.count);\n      const attendanceRates = this.monthlyFormations.map(f => f.attendanceRate); // Update line chart with real data\n\n      this.lineData = Object.assign(Object.assign({}, this.lineData), {\n        labels: months,\n        datasets: [Object.assign(Object.assign({}, this.lineData.datasets[0]), {\n          data: formationCounts\n        }), Object.assign(Object.assign({}, this.lineData.datasets[1]), {\n          data: attendanceRates\n        })]\n      }); // Update bar chart with real data\n\n      this.barData = Object.assign(Object.assign({}, this.barData), {\n        labels: months,\n        datasets: [Object.assign(Object.assign({}, this.barData.datasets[0]), {\n          data: formationCounts\n        }), Object.assign(Object.assign({}, this.barData.datasets[1]), {\n          data: attendanceRates\n        })]\n      });\n    }\n\n    if (this.teamStats.length > 0) {\n      const teamNames = this.teamStats.map(t => t.name);\n      const teamAttendance = this.teamStats.map(t => t.attendanceRate); // Update polar chart with team data\n\n      this.polarData = Object.assign(Object.assign({}, this.polarData), {\n        labels: teamNames,\n        datasets: [Object.assign(Object.assign({}, this.polarData.datasets[0]), {\n          data: teamAttendance\n        })]\n      });\n    }\n  }\n\n  exportToPDF(type) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this3.statisticsService.exportToPDF(type));\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-statistics.pdf`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'PDF exported successfully',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error exporting PDF:', error);\n\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to export PDF',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  exportToCSV(type) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this4.statisticsService.exportToCSV(type));\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-statistics.csv`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'CSV exported successfully',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error exporting CSV:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to export CSV',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nChartsComponent.ɵfac = function ChartsComponent_Factory(t) {\n  return new (t || ChartsComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StatisticsService), i0.ɵɵdirectiveInject(i3.MessageService));\n};\n\nChartsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ChartsComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 3,\n  vars: 2,\n  consts: [[\"class\", \"grid\", 4, \"ngIf\", \"ngIfElse\"], [\"loadingTemplate\", \"\"], [1, \"grid\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"col-12\", \"lg:col-6\"], [1, \"card\"], [\"type\", \"line\", 3, \"data\", \"options\"], [\"type\", \"bar\", 3, \"data\", \"options\"], [1, \"card\", \"flex\", \"flex-column\", \"align-items-center\"], [1, \"text-left\", \"w-full\"], [\"type\", \"pie\", 3, \"data\", \"options\"], [\"type\", \"doughnut\", 3, \"data\", \"options\"], [1, \"col-12\"], [\"type\", \"radar\", 3, \"data\", \"options\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 2, \"width\", \"2.5rem\", \"height\", \"2.5rem\"], [1, \"pi\", \"pi-book\", \"text-blue-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-100\", \"border-round\", 2, \"width\", \"2.5rem\", \"height\", \"2.5rem\"], [1, \"pi\", \"pi-users\", \"text-orange-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-cyan-100\", \"border-round\", 2, \"width\", \"2.5rem\", \"height\", \"2.5rem\"], [1, \"pi\", \"pi-sitemap\", \"text-cyan-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-100\", \"border-round\", 2, \"width\", \"2.5rem\", \"height\", \"2.5rem\"], [1, \"pi\", \"pi-chart-line\", \"text-purple-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"400px\"], [1, \"ml-3\"]],\n  template: function ChartsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, ChartsComponent_div_0_Template, 27, 17, \"div\", 0);\n      i0.ɵɵtemplate(1, ChartsComponent_ng_template_1_Template, 6, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i4.NgIf, i5.UIChart],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AACA,SAAuBA,aAAvB,QAA4C,MAA5C;AAGA,SAASC,cAAT,QAA+B,aAA/B;;;;;;;;;;ICFIC,gCAA2C,CAA3C,EAA2C,KAA3C,EAA2C,CAA3C,EAA2C,CAA3C,EAA2C,KAA3C,EAA2C,EAA3C,EAA2C,CAA3C,EAA2C,KAA3C,EAA2C,EAA3C,EAA2C,CAA3C,EAA2C,KAA3C,EAA2C,EAA3C,EAA2C,CAA3C,EAA2C,KAA3C,EAA2C,CAA3C,EAA2C,MAA3C,EAA2C,EAA3C;IAMsEA;IAAgBA;IAC9DA;IAA0CA;IAAkCA;IAEhFA;IACIA;IACJA;IAIZA,iCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,EAAtC;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAiCA;IAE/EA;IACIA;IACJA;IAIZA,iCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,EAAtC;IAI8DA;IAAWA;IACzDA;IAA0CA;IAA6BA;IAE3EA;IACIA;IACJA;IAIZA,iCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,EAAtC,EAAsC,KAAtC,EAAsC,EAAtC,EAAsC,MAAtC,EAAsC,EAAtC;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAwCA;IAEtFA;IACIA;IACJA;;;;;IA3C8CA;IAAAA;IAaAA;IAAAA;IAaAA;IAAAA;IAaAA;IAAAA;;;;;;;;;;;;IAhDtEA;IAEIA;IA0DAA,+BAA6B,CAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,CAA7B,EAA6B,IAA7B;IAEYA;IAA2BA;IAC/BA;IACJA;IAGJA,+BAA6B,CAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,CAA7B,EAA6B,IAA7B;IAEYA;IAAgBA;IACpBA;IACJA;IAIJA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B,EAA6B,CAA7B;IAEqCA;IAA6BA;IAC1DA;IACJA;IAGJA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B,EAA6B,CAA7B;IAEqCA;IAAkBA;IAC/CA;IACJA;IAIJA,iCAAoB,EAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,EAApB,EAAoB,IAApB;IAEYA;IAAwBA;IAC5BA;IACJA;;;;;IA5FiBA;IAAAA;IA6DQA;IAAAA,uCAAiB,SAAjB,EAAiBC,kBAAjB;IAODD;IAAAA,sCAAgB,SAAhB,EAAgBC,iBAAhB;IAQwCD;IAAAA;IAAxCA,sCAAgB,SAAhB,EAAgBC,iBAAhB;IAOiDD;IAAAA;IAA5CA,wCAAkB,SAAlB,EAAkBC,mBAAlB;IAQHD;IAAAA,wCAAkB,SAAlB,EAAkBC,mBAAlB;;;;;;IAM9BD,gCAAoB,CAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,CAApB,EAAoB,KAApB,EAAoB,EAApB;IAGYA;IACAA;IAAmBA;IAAiBA;;;;AD7FpD,OAAM,MAAOE,eAAP,CAAsB;EA0CxBC,YACWC,aADX,EAEYC,iBAFZ,EAGYC,cAHZ,EAG0C;IAF/B;IACC;IACA,qCAA8B,CA3C1C;;IACA,sBAAmC,EAAnC;IACA,iBAAyB,EAAzB;IACA,qBAAsC,EAAtC;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC,CAsC0C,CApC1C;;IACA,yBAA6B,KAA7B;IACA,oBAAwB,KAAxB;IACA,wBAA4B,KAA5B;IACA,eAAmB,IAAnB,CAgC0C,CA9B1C;;IACA,iBAAoB,EAApB;IACA,eAAkB,EAAlB;IACA,mBAAsB,CAAtB;IA6BI,KAAKC,YAAL,GAAoB,KAAKH,aAAL,CAAmBI,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,UAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,UAAL;;MACA,MAAM,KAAI,CAACE,cAAL,EAAN;IAFU;EAGb;;EAEKA,cAAc;IAAA;;IAAA;MAChB,IAAI;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CADA,CAGA;;QACA,MAAI,CAACC,cAAL,SAA4BhB,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBU,iBAAvB,EAAD,CAAzC,CAJA,CAMA;;QACA,MAAI,CAACC,iBAAL,SAA+BlB,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBY,oBAAvB,EAAD,CAA5C,CAPA,CASA;;QACA,MAAI,CAACC,cAAL,SAA4BpB,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBc,iBAAvB,EAAD,CAAzC,CAVA,CAYA;;QACA,MAAI,CAACC,SAAL,SAAuBtB,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBgB,YAAvB,EAAD,CAApC,CAbA,CAeA;;QACA,MAAI,CAACC,aAAL,SAA2BxB,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBkB,qBAAvB,CAA6C,EAA7C,CAAD,CAAxC,CAhBA,CAkBA;;QACA,MAAI,CAACC,wBAAL;MAEH,CArBD,CAqBE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CA7BD,SA6BU;QACN,MAAI,CAAClB,OAAL,GAAe,KAAf;MACH;IAhCe;EAiCnB;;EAEDH,UAAU;IACN,MAAMsB,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB,CAJM,CAMN;;IACA,KAAKG,OAAL,GAAe;MACXC,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADG;MAEXC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,YADX;QAEIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAFrB;QAGIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAHjB;QAIIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAJV,CADM,EAON;QACIH,KAAK,EAAE,iBADX;QAEIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAFrB;QAGIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAHjB;QAIIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAJV,CAPM;IAFC,CAAf;IAkBA,KAAKC,UAAL,GAAkB;MACdC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADK;MAQde,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEZ,kBADJ;YAEHgB,IAAI,EAAE;cACFC,MAAM,EAAE;YADN;UAFH,CADR;UAOCC,IAAI,EAAE;YACFC,OAAO,EAAE,KADP;YAEFC,UAAU,EAAE;UAFV;QAPP,CADC;QAaJC,CAAC,EAAE;UACCN,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP;MAbC;IARM,CAAlB,CAzBM,CA0DN;;IACA,KAAKE,OAAL,GAAe;MACXnB,MAAM,EAAE,CAAC,SAAD,EAAY,QAAZ,EAAsB,SAAtB,CADG;MAEXC,QAAQ,EAAE,CACN;QACII,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADV;QAEIF,eAAe,EAAE,CACbZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CADa,EAEbL,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CAFa,EAGbL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHa,CAFrB;QAOIwB,oBAAoB,EAAE,CAClB7B,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CADkB,EAElBL,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CAFkB,EAGlBL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHkB;MAP1B,CADM;IAFC,CAAf;IAkBA,KAAKyB,UAAL,GAAkB;MACdd,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJsB,aAAa,EAAE,IADX;YAEJb,KAAK,EAAEd;UAFH;QADJ;MADH;IADK,CAAlB,CA7EM,CAwFN;;IACA,KAAK4B,QAAL,GAAgB;MACZvB,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADI;MAEZC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,kBADX;QAEIG,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,CAFV;QAGImB,IAAI,EAAE,KAHV;QAIIrB,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMI6B,OAAO,EAAE;MANb,CADM,EASN;QACIvB,KAAK,EAAE,iBADX;QAEIG,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,CAFV;QAGImB,IAAI,EAAE,KAHV;QAIIrB,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMI6B,OAAO,EAAE;MANb,CATM;IAFE,CAAhB;IAsBA,KAAKC,WAAL,GAAmB;MACfnB,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADM;MAQfe,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCN,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP;MAVC;IARO,CAAnB,CA/GM,CA6IN;;IACA,KAAKU,SAAL,GAAiB;MACb1B,QAAQ,EAAE,CAAC;QACPI,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,CAAT,EAAY,CAAZ,CADC;QAEPF,eAAe,EAAE,CACbZ,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CADa,EAEbL,aAAa,CAACK,gBAAd,CAA+B,YAA/B,CAFa,EAGbL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHa,EAIbL,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJa,CAFV;QAQPM,KAAK,EAAE;MARA,CAAD,CADG;MAWbF,MAAM,EAAE,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,EAA+B,QAA/B;IAXK,CAAjB;IAcA,KAAK4B,YAAL,GAAoB;MAChBrB,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADO;MAQhBe,MAAM,EAAE;QACJmB,CAAC,EAAE;UACCd,IAAI,EAAE;YACFN,KAAK,EAAEX;UADL;QADP;MADC;IARQ,CAApB,CA5JM,CA6KN;;IACA,KAAKgC,SAAL,GAAiB;MACb9B,MAAM,EAAE,CAAC,YAAD,EAAe,eAAf,EAAgC,YAAhC,EAA8C,UAA9C,EAA0D,aAA1D,EAAyE,YAAzE,EAAuF,SAAvF,CADK;MAEbC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,gBADX;QAEIE,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAFjB;QAGImC,oBAAoB,EAAExC,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAH1B;QAIIoC,gBAAgB,EAAEzC,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJtB;QAKIqC,yBAAyB,EAAEtC,SAL/B;QAMIuC,qBAAqB,EAAE3C,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAN3B;QAOIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAPV,CADM,EAUN;QACIH,KAAK,EAAE,iBADX;QAEIE,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAFjB;QAGImC,oBAAoB,EAAExC,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAH1B;QAIIoC,gBAAgB,EAAEzC,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAJtB;QAKIqC,yBAAyB,EAAEtC,SAL/B;QAMIuC,qBAAqB,EAAE3C,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAN3B;QAOIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,GAAzB;MAPV,CAVM;IAFG,CAAjB;IAwBA,KAAK8B,YAAL,GAAoB;MAChB5B,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADO;MAQhBe,MAAM,EAAE;QACJmB,CAAC,EAAE;UACCd,IAAI,EAAE;YACFN,KAAK,EAAEZ;UADL;QADP;MADC;IARQ,CAApB;EAgBH;;EAEDd,wBAAwB;IACpB,IAAI,KAAKR,iBAAL,CAAuB6D,MAAvB,GAAgC,CAApC,EAAuC;MACnC,MAAMC,MAAM,GAAG,KAAK9D,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACC,KAAlC,CAAf;MACA,MAAMC,eAAe,GAAG,KAAKlE,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACG,KAAlC,CAAxB;MACA,MAAMC,eAAe,GAAG,KAAKpE,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACK,cAAlC,CAAxB,CAHmC,CAKnC;;MACA,KAAKrB,QAAL,GAAasB,gCACN,KAAKtB,QADC,GACO;QAChBvB,MAAM,EAAEqC,MADQ;QAEhBpC,QAAQ,EAAE,iCAEC,KAAKsB,QAAL,CAActB,QAAd,CAAuB,CAAvB,IAAyB;UAC5BI,IAAI,EAAEoC;QADsB,EAF1B,kCAMC,KAAKlB,QAAL,CAActB,QAAd,CAAuB,CAAvB,IAAyB;UAC5BI,IAAI,EAAEsC;QADsB,EAN1B;MAFM,CADP,CAAb,CANmC,CAqBnC;;MACA,KAAK5C,OAAL,GAAY8C,gCACL,KAAK9C,OADA,GACO;QACfC,MAAM,EAAEqC,MADO;QAEfpC,QAAQ,EAAE,iCAEC,KAAKF,OAAL,CAAaE,QAAb,CAAsB,CAAtB,IAAwB;UAC3BI,IAAI,EAAEoC;QADqB,EAFzB,kCAMC,KAAK1C,OAAL,CAAaE,QAAb,CAAsB,CAAtB,IAAwB;UAC3BI,IAAI,EAAEsC;QADqB,EANzB;MAFK,CADP,CAAZ;IAcH;;IAED,IAAI,KAAKhE,SAAL,CAAeyD,MAAf,GAAwB,CAA5B,EAA+B;MAC3B,MAAMU,SAAS,GAAG,KAAKnE,SAAL,CAAe2D,GAAf,CAAmBS,CAAC,IAAIA,CAAC,CAACC,IAA1B,CAAlB;MACA,MAAMC,cAAc,GAAG,KAAKtE,SAAL,CAAe2D,GAAf,CAAmBS,CAAC,IAAIA,CAAC,CAACH,cAA1B,CAAvB,CAF2B,CAI3B;;MACA,KAAKjB,SAAL,GAAckB,gCACP,KAAKlB,SADE,GACO;QACjB3B,MAAM,EAAE8C,SADS;QAEjB7C,QAAQ,EAAE,iCACH,KAAK0B,SAAL,CAAe1B,QAAf,CAAwB,CAAxB,CADG,GACuB;UAC7BI,IAAI,EAAE4C;QADuB,CADvB;MAFO,CADP,CAAd;IAQH;EACJ;;EAEKC,WAAW,CAACC,IAAD,EAA2C;IAAA;;IAAA;MACxD,IAAI;QACA,MAAMC,IAAI,SAAS/F,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBsF,WAAvB,CAAmCC,IAAnC,CAAD,CAAhC;QACA,MAAME,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BJ,IAA3B,CAAZ;QACA,MAAMK,IAAI,GAAGhE,QAAQ,CAACiE,aAAT,CAAuB,GAAvB,CAAb;QACAD,IAAI,CAACE,IAAL,GAAYN,GAAZ;QACAI,IAAI,CAACG,QAAL,GAAgB,GAAGT,IAAI,iBAAvB;QACAM,IAAI,CAACI,KAAL;QACAP,MAAM,CAACC,GAAP,CAAWO,eAAX,CAA2BT,GAA3B;;QAEA,MAAI,CAACxF,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,SAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAfD,CAeE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,sBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAxBuD;EAyB3D;;EAEKyE,WAAW,CAACZ,IAAD,EAA2C;IAAA;;IAAA;MACxD,IAAI;QACA,MAAMC,IAAI,SAAS/F,aAAa,CAAC,MAAI,CAACO,iBAAL,CAAuBmG,WAAvB,CAAmCZ,IAAnC,CAAD,CAAhC;QACA,MAAME,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BJ,IAA3B,CAAZ;QACA,MAAMK,IAAI,GAAGhE,QAAQ,CAACiE,aAAT,CAAuB,GAAvB,CAAb;QACAD,IAAI,CAACE,IAAL,GAAYN,GAAZ;QACAI,IAAI,CAACG,QAAL,GAAgB,GAAGT,IAAI,iBAAvB;QACAM,IAAI,CAACI,KAAL;QACAP,MAAM,CAACC,GAAP,CAAWO,eAAX,CAA2BT,GAA3B;;QAEA,MAAI,CAACxF,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,SAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAfD,CAeE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,sBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAxBuD;EAyB3D;;EAED0E,WAAW;IACP,IAAI,KAAKlG,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBmG,WAAlB;IACH;EACJ;;AArauB;;;mBAAfxG,iBAAeF;AAAA;;;QAAfE;EAAeyG;EAAAC,iCAFb,CAAC7G,cAAD,CAEa;EAFG8G;EAAAC;EAAAC;EAAAC;IAAA;MCR/BhH;MAkGAA;;;;;;MAlGmBA,oCAAgB,UAAhB,EAAgBiH,GAAhB", "names": ["lastValueFrom", "MessageService", "i0", "ctx_r0", "ChartsComponent", "constructor", "layoutService", "statisticsService", "messageService", "subscription", "configUpdate$", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "loadStatistics", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "formationStats", "getFormationStats", "teamStats", "getTeamStats", "employeeStats", "getEmployeeAttendance", "updateChartsWithRealData", "error", "console", "add", "severity", "summary", "detail", "life", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "barData", "labels", "datasets", "label", "backgroundColor", "borderColor", "data", "barOptions", "plugins", "legend", "color", "scales", "x", "ticks", "font", "weight", "grid", "display", "drawBorder", "y", "pieData", "hoverBackgroundColor", "pieOptions", "usePointStyle", "lineData", "fill", "tension", "lineOptions", "polarData", "polarOptions", "r", "radarData", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "radarOptions", "length", "months", "map", "f", "month", "formationCounts", "count", "attendanceRates", "attendanceRate", "Object", "teamNames", "t", "name", "teamAttendance", "exportToPDF", "type", "blob", "url", "window", "URL", "createObjectURL", "link", "createElement", "href", "download", "click", "revokeObjectURL", "exportToCSV", "ngOnDestroy", "unsubscribe", "selectors", "features", "decls", "vars", "consts", "template", "_r1"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\charts\\charts.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\charts\\charts.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\nimport { StatisticsService, FormationStats, TeamStats, EmployeeAttendance, DashboardStats, MonthlyFormations } from '../../../../services/statistics.service';\nimport { MessageService } from 'primeng/api';\n\n@Component({\n    templateUrl: './charts.component.html',\n    providers: [MessageService]\n})\nexport class ChartsComponent implements OnInit, OnDestroy {\n\n    // Data\n    formationStats: FormationStats[] = [];\n    teamStats: TeamStats[] = [];\n    employeeStats: EmployeeAttendance[] = [];\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n\n    // Loading states\n    loadingFormations: boolean = false;\n    loadingTeams: boolean = false;\n    loadingEmployees: boolean = false;\n    loading: boolean = true;\n\n    // Filters\n    startDate: string = '';\n    endDate: string = '';\n    selectedTab: number = 0;\n\n    // Chart data properties\n    barData: any;\n    barOptions: any;\n    pieData: any;\n    pieOptions: any;\n    lineData: any;\n    lineOptions: any;\n    polarData: any;\n    polarOptions: any;\n    radarData: any;\n    radarOptions: any;\n    \n    // Additional chart data\n    attendanceChartData: any;\n    attendanceChartOptions: any;\n    teamChartData: any;\n    teamChartOptions: any;\n    monthlyChartData: any;\n    monthlyChartOptions: any;\n\n    subscription!: Subscription;\n\n    constructor(\n        public layoutService: LayoutService,\n        private statisticsService: StatisticsService,\n        private messageService: MessageService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initCharts();\n        });\n    }\n\n    async ngOnInit() {\n        this.initCharts();\n        await this.loadStatistics();\n    }\n\n    async loadStatistics() {\n        try {\n            this.loading = true;\n            \n            // Load dashboard stats\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            \n            // Load monthly formations\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n            \n            // Load formation stats\n            this.formationStats = await lastValueFrom(this.statisticsService.getFormationStats());\n            \n            // Load team stats\n            this.teamStats = await lastValueFrom(this.statisticsService.getTeamStats());\n            \n            // Load employee stats\n            this.employeeStats = await lastValueFrom(this.statisticsService.getEmployeeAttendance(20));\n            \n            // Update charts with real data\n            this.updateChartsWithRealData();\n            \n        } catch (error) {\n            console.error('Error loading statistics:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to load statistics',\n                life: 3000\n            });\n        } finally {\n            this.loading = false;\n        }\n    }\n\n    initCharts() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        // Bar Chart\n        this.barData = {\n            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    data: [65, 59, 80, 81, 56, 55, 40]\n                },\n                {\n                    label: 'Attendance Rate',\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    data: [28, 48, 40, 19, 86, 27, 90]\n                }\n            ]\n        };\n\n        this.barOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary,\n                        font: {\n                            weight: 500\n                        }\n                    },\n                    grid: {\n                        display: false,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n            }\n        };\n\n        // Pie Chart\n        this.pieData = {\n            labels: ['Present', 'Absent', 'Pending'],\n            datasets: [\n                {\n                    data: [540, 325, 702],\n                    backgroundColor: [\n                        documentStyle.getPropertyValue('--green-500'),\n                        documentStyle.getPropertyValue('--red-500'),\n                        documentStyle.getPropertyValue('--yellow-500')\n                    ],\n                    hoverBackgroundColor: [\n                        documentStyle.getPropertyValue('--green-400'),\n                        documentStyle.getPropertyValue('--red-400'),\n                        documentStyle.getPropertyValue('--yellow-400')\n                    ]\n                }]\n        };\n\n        this.pieOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        usePointStyle: true,\n                        color: textColor\n                    }\n                }\n            }\n        };\n\n        // Line Chart\n        this.lineData = {\n            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n            datasets: [\n                {\n                    label: 'Formations Count',\n                    data: [65, 59, 80, 81, 56, 55, 40],\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate',\n                    data: [28, 48, 40, 19, 86, 27, 90],\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4\n                }\n            ]\n        };\n\n        this.lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                }\n            }\n        };\n\n        // Polar Area Chart\n        this.polarData = {\n            datasets: [{\n                data: [11, 16, 7, 3],\n                backgroundColor: [\n                    documentStyle.getPropertyValue('--red-500'),\n                    documentStyle.getPropertyValue('--blue-500'),\n                    documentStyle.getPropertyValue('--yellow-500'),\n                    documentStyle.getPropertyValue('--green-500')\n                ],\n                label: 'Team Performance'\n            }],\n            labels: ['Team A', 'Team B', 'Team C', 'Team D']\n        };\n\n        this.polarOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                r: {\n                    grid: {\n                        color: surfaceBorder\n                    }\n                }\n            }\n        };\n\n        // Radar Chart\n        this.radarData = {\n            labels: ['Attendance', 'Participation', 'Completion', 'Feedback', 'Performance', 'Engagement', 'Results'],\n            datasets: [\n                {\n                    label: 'Current Period',\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointBackgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointBorderColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointHoverBackgroundColor: textColor,\n                    pointHoverBorderColor: documentStyle.getPropertyValue('--primary-500'),\n                    data: [65, 59, 90, 81, 56, 55, 40]\n                },\n                {\n                    label: 'Previous Period',\n                    borderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointBackgroundColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointHoverBackgroundColor: textColor,\n                    pointHoverBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    data: [28, 48, 40, 19, 96, 27, 100]\n                }\n            ]\n        };\n\n        this.radarOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                r: {\n                    grid: {\n                        color: textColorSecondary\n                    }\n                }\n            }\n        };\n    }\n\n    updateChartsWithRealData() {\n        if (this.monthlyFormations.length > 0) {\n            const months = this.monthlyFormations.map(f => f.month);\n            const formationCounts = this.monthlyFormations.map(f => f.count);\n            const attendanceRates = this.monthlyFormations.map(f => f.attendanceRate);\n\n            // Update line chart with real data\n            this.lineData = {\n                ...this.lineData,\n                labels: months,\n                datasets: [\n                    {\n                        ...this.lineData.datasets[0],\n                        data: formationCounts\n                    },\n                    {\n                        ...this.lineData.datasets[1],\n                        data: attendanceRates\n                    }\n                ]\n            };\n\n            // Update bar chart with real data\n            this.barData = {\n                ...this.barData,\n                labels: months,\n                datasets: [\n                    {\n                        ...this.barData.datasets[0],\n                        data: formationCounts\n                    },\n                    {\n                        ...this.barData.datasets[1],\n                        data: attendanceRates\n                    }\n                ]\n            };\n        }\n\n        if (this.teamStats.length > 0) {\n            const teamNames = this.teamStats.map(t => t.name);\n            const teamAttendance = this.teamStats.map(t => t.attendanceRate);\n\n            // Update polar chart with team data\n            this.polarData = {\n                ...this.polarData,\n                labels: teamNames,\n                datasets: [{\n                    ...this.polarData.datasets[0],\n                    data: teamAttendance\n                }]\n            };\n        }\n    }\n\n    async exportToPDF(type: 'formations' | 'teams' | 'employees') {\n        try {\n            const blob = await lastValueFrom(this.statisticsService.exportToPDF(type));\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${type}-statistics.pdf`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n            \n            this.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'PDF exported successfully',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error exporting PDF:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to export PDF',\n                life: 3000\n            });\n        }\n    }\n\n    async exportToCSV(type: 'formations' | 'teams' | 'employees') {\n        try {\n            const blob = await lastValueFrom(this.statisticsService.exportToCSV(type));\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${type}-statistics.csv`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n            \n            this.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'CSV exported successfully',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error exporting CSV:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to export CSV',\n                life: 3000\n            });\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n\n", "<div class=\"grid\" *ngIf=\"!loading; else loadingTemplate\">\n    <!-- Dashboard Stats Cards -->\n    <div class=\"col-12\" *ngIf=\"dashboardStats\">\n        <div class=\"grid\">\n            <div class=\"col-12 md:col-6 lg:col-3\">\n                <div class=\"card mb-0\">\n                    <div class=\"flex justify-content-between mb-3\">\n                        <div>\n                            <span class=\"block text-500 font-medium mb-3\">Total Formations</span>\n                            <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalFormations}}</div>\n                        </div>\n                        <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" style=\"width:2.5rem;height:2.5rem\">\n                            <i class=\"pi pi-book text-blue-500 text-xl\"></i>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-12 md:col-6 lg:col-3\">\n                <div class=\"card mb-0\">\n                    <div class=\"flex justify-content-between mb-3\">\n                        <div>\n                            <span class=\"block text-500 font-medium mb-3\">Total Employees</span>\n                            <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalEmployees}}</div>\n                        </div>\n                        <div class=\"flex align-items-center justify-content-center bg-orange-100 border-round\" style=\"width:2.5rem;height:2.5rem\">\n                            <i class=\"pi pi-users text-orange-500 text-xl\"></i>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-12 md:col-6 lg:col-3\">\n                <div class=\"card mb-0\">\n                    <div class=\"flex justify-content-between mb-3\">\n                        <div>\n                            <span class=\"block text-500 font-medium mb-3\">Total Teams</span>\n                            <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalTeams}}</div>\n                        </div>\n                        <div class=\"flex align-items-center justify-content-center bg-cyan-100 border-round\" style=\"width:2.5rem;height:2.5rem\">\n                            <i class=\"pi pi-sitemap text-cyan-500 text-xl\"></i>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class=\"col-12 md:col-6 lg:col-3\">\n                <div class=\"card mb-0\">\n                    <div class=\"flex justify-content-between mb-3\">\n                        <div>\n                            <span class=\"block text-500 font-medium mb-3\">Attendance Rate</span>\n                            <div class=\"text-900 font-medium text-xl\">{{dashboardStats.globalAttendanceRate}}%</div>\n                        </div>\n                        <div class=\"flex align-items-center justify-content-center bg-purple-100 border-round\" style=\"width:2.5rem;height:2.5rem\">\n                            <i class=\"pi pi-chart-line text-purple-500 text-xl\"></i>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Charts Row 1 -->\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card\">\n            <h5>Formation Attendance Trends</h5>\n            <p-chart type=\"line\" [data]=\"lineData\" [options]=\"lineOptions\"></p-chart>\n        </div>\n    </div>\n\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card\">\n            <h5>Team Performance</h5>\n            <p-chart type=\"bar\" [data]=\"barData\" [options]=\"barOptions\"></p-chart>\n        </div>\n    </div>\n\n    <!-- Charts Row 2 -->\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Formation Status Distribution</h5>\n            <p-chart type=\"pie\" [data]=\"pieData\" [options]=\"pieOptions\" [style]=\"{'width': '70%'}\"></p-chart>\n        </div>\n    </div>\n\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Monthly Formations</h5>\n            <p-chart type=\"doughnut\" [data]=\"polarData\" [options]=\"polarOptions\" [style]=\"{'width': '70%'}\"></p-chart>\n        </div>\n    </div>\n\n    <!-- Charts Row 3 -->\n    <div class=\"col-12\">\n        <div class=\"card\">\n            <h5>Team Speciality Analysis</h5>\n            <p-chart type=\"radar\" [data]=\"radarData\" [options]=\"radarOptions\"></p-chart>\n        </div>\n    </div>\n</div>\n\n<ng-template #loadingTemplate>\n    <div class=\"col-12\">\n        <div class=\"card\">\n            <div class=\"flex align-items-center justify-content-center\" style=\"height: 400px;\">\n                <p-progressSpinner></p-progressSpinner>\n                <span class=\"ml-3\">Loading charts...</span>\n            </div>\n        </div>\n    </div>\n</ng-template>\n"]}, "metadata": {}, "sourceType": "module"}