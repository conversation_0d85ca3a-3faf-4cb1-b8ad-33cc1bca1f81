{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n  return sample(interval(period, scheduler));\n}", "map": {"version": 3, "names": ["asyncScheduler", "sample", "interval", "sampleTime", "period", "scheduler"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/sampleTime.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler = asyncScheduler) {\n    return sample(interval(period, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,QAAT,QAAyB,wBAAzB;AACA,OAAO,SAASC,UAAT,CAAoBC,MAApB,EAA4BC,SAAS,GAAGL,cAAxC,EAAwD;EAC3D,OAAOC,MAAM,CAACC,QAAQ,CAACE,MAAD,EAASC,SAAT,CAAT,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}