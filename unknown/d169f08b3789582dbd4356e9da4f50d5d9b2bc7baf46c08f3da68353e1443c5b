{"ast": null, "code": "import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n  return new Observable(subscriber => {\n    let iterator;\n    executeSchedule(subscriber, scheduler, () => {\n      iterator = input[Symbol_iterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        let value;\n        let done;\n\n        try {\n          ({\n            value,\n            done\n          } = iterator.next());\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return () => isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n  });\n}", "map": {"version": 3, "names": ["Observable", "iterator", "Symbol_iterator", "isFunction", "executeSchedule", "scheduleIterable", "input", "scheduler", "subscriber", "value", "done", "next", "err", "error", "complete", "return"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleIterable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n    return new Observable((subscriber) => {\n        let iterator;\n        executeSchedule(subscriber, scheduler, () => {\n            iterator = input[Symbol_iterator]();\n            executeSchedule(subscriber, scheduler, () => {\n                let value;\n                let done;\n                try {\n                    ({ value, done } = iterator.next());\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return;\n                }\n                if (done) {\n                    subscriber.complete();\n                }\n                else {\n                    subscriber.next(value);\n                }\n            }, 0, true);\n        });\n        return () => isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,gBAAT,CAA0BC,KAA1B,EAAiCC,SAAjC,EAA4C;EAC/C,OAAO,IAAIP,UAAJ,CAAgBQ,UAAD,IAAgB;IAClC,IAAIP,QAAJ;IACAG,eAAe,CAACI,UAAD,EAAaD,SAAb,EAAwB,MAAM;MACzCN,QAAQ,GAAGK,KAAK,CAACJ,eAAD,CAAL,EAAX;MACAE,eAAe,CAACI,UAAD,EAAaD,SAAb,EAAwB,MAAM;QACzC,IAAIE,KAAJ;QACA,IAAIC,IAAJ;;QACA,IAAI;UACA,CAAC;YAAED,KAAF;YAASC;UAAT,IAAkBT,QAAQ,CAACU,IAAT,EAAnB;QACH,CAFD,CAGA,OAAOC,GAAP,EAAY;UACRJ,UAAU,CAACK,KAAX,CAAiBD,GAAjB;UACA;QACH;;QACD,IAAIF,IAAJ,EAAU;UACNF,UAAU,CAACM,QAAX;QACH,CAFD,MAGK;UACDN,UAAU,CAACG,IAAX,CAAgBF,KAAhB;QACH;MACJ,CAhBc,EAgBZ,CAhBY,EAgBT,IAhBS,CAAf;IAiBH,CAnBc,CAAf;IAoBA,OAAO,MAAMN,UAAU,CAACF,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACc,MAA9D,CAAV,IAAmFd,QAAQ,CAACc,MAAT,EAAhG;EACH,CAvBM,CAAP;AAwBH"}, "metadata": {}, "sourceType": "module"}