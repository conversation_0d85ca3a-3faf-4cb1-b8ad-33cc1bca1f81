{"ast": null, "code": "import { Subscription } from '../Subscription';\nexport const animationFrameProvider = {\n  schedule(callback) {\n    let request = requestAnimationFrame;\n    let cancel = cancelAnimationFrame;\n    const {\n      delegate\n    } = animationFrameProvider;\n\n    if (delegate) {\n      request = delegate.requestAnimationFrame;\n      cancel = delegate.cancelAnimationFrame;\n    }\n\n    const handle = request(timestamp => {\n      cancel = undefined;\n      callback(timestamp);\n    });\n    return new Subscription(() => cancel === null || cancel === void 0 ? void 0 : cancel(handle));\n  },\n\n  requestAnimationFrame(...args) {\n    const {\n      delegate\n    } = animationFrameProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame)(...args);\n  },\n\n  cancelAnimationFrame(...args) {\n    const {\n      delegate\n    } = animationFrameProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame)(...args);\n  },\n\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Subscription", "animationFrameProvider", "schedule", "callback", "request", "requestAnimationFrame", "cancel", "cancelAnimationFrame", "delegate", "handle", "timestamp", "undefined", "args"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/animationFrameProvider.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nexport const animationFrameProvider = {\n    schedule(callback) {\n        let request = requestAnimationFrame;\n        let cancel = cancelAnimationFrame;\n        const { delegate } = animationFrameProvider;\n        if (delegate) {\n            request = delegate.requestAnimationFrame;\n            cancel = delegate.cancelAnimationFrame;\n        }\n        const handle = request((timestamp) => {\n            cancel = undefined;\n            callback(timestamp);\n        });\n        return new Subscription(() => cancel === null || cancel === void 0 ? void 0 : cancel(handle));\n    },\n    requestAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame)(...args);\n    },\n    cancelAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame)(...args);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,SAASA,YAAT,QAA6B,iBAA7B;AACA,OAAO,MAAMC,sBAAsB,GAAG;EAClCC,QAAQ,CAACC,QAAD,EAAW;IACf,IAAIC,OAAO,GAAGC,qBAAd;IACA,IAAIC,MAAM,GAAGC,oBAAb;IACA,MAAM;MAAEC;IAAF,IAAeP,sBAArB;;IACA,IAAIO,QAAJ,EAAc;MACVJ,OAAO,GAAGI,QAAQ,CAACH,qBAAnB;MACAC,MAAM,GAAGE,QAAQ,CAACD,oBAAlB;IACH;;IACD,MAAME,MAAM,GAAGL,OAAO,CAAEM,SAAD,IAAe;MAClCJ,MAAM,GAAGK,SAAT;MACAR,QAAQ,CAACO,SAAD,CAAR;IACH,CAHqB,CAAtB;IAIA,OAAO,IAAIV,YAAJ,CAAiB,MAAMM,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACG,MAAD,CAA7E,CAAP;EACH,CAdiC;;EAelCJ,qBAAqB,CAAC,GAAGO,IAAJ,EAAU;IAC3B,MAAM;MAAEJ;IAAF,IAAeP,sBAArB;IACA,OAAO,CAAC,CAACO,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACH,qBAA9D,KAAwFA,qBAAzF,EAAgH,GAAGO,IAAnH,CAAP;EACH,CAlBiC;;EAmBlCL,oBAAoB,CAAC,GAAGK,IAAJ,EAAU;IAC1B,MAAM;MAAEJ;IAAF,IAAeP,sBAArB;IACA,OAAO,CAAC,CAACO,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACD,oBAA9D,KAAuFA,oBAAxF,EAA8G,GAAGK,IAAjH,CAAP;EACH,CAtBiC;;EAuBlCJ,QAAQ,EAAEG;AAvBwB,CAA/B"}, "metadata": {}, "sourceType": "module"}