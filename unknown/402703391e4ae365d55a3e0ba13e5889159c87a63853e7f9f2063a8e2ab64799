{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass Skeleton {\n  constructor() {\n    this.shape = \"rectangle\";\n    this.animation = \"wave\";\n    this.borderRadius = null;\n    this.size = null;\n    this.width = \"100%\";\n    this.height = \"1rem\";\n  }\n\n  containerClass() {\n    return {\n      'p-skeleton p-component': true,\n      'p-skeleton-circle': this.shape === 'circle',\n      'p-skeleton-none': this.animation === 'none'\n    };\n  }\n\n  containerStyle() {\n    if (this.size) return Object.assign(Object.assign({}, this.style), {\n      width: this.size,\n      height: this.size,\n      borderRadius: this.borderRadius\n    });else return Object.assign(Object.assign({}, this.style), {\n      width: this.width,\n      height: this.height,\n      borderRadius: this.borderRadius\n    });\n  }\n\n}\n\nSkeleton.ɵfac = function Skeleton_Factory(t) {\n  return new (t || Skeleton)();\n};\n\nSkeleton.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Skeleton,\n  selectors: [[\"p-skeleton\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    styleClass: \"styleClass\",\n    style: \"style\",\n    shape: \"shape\",\n    animation: \"animation\",\n    borderRadius: \"borderRadius\",\n    size: \"size\",\n    width: \"width\",\n    height: \"height\"\n  },\n  decls: 1,\n  vars: 4,\n  consts: [[3, \"ngClass\", \"ngStyle\"]],\n  template: function Skeleton_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelement(0, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.containerStyle());\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgStyle],\n  styles: [\".p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Skeleton, [{\n    type: Component,\n    args: [{\n      selector: 'p-skeleton',\n      template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\">\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    animation: [{\n      type: Input\n    }],\n    borderRadius: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }]\n  });\n})();\n\nclass SkeletonModule {}\n\nSkeletonModule.ɵfac = function SkeletonModule_Factory(t) {\n  return new (t || SkeletonModule)();\n};\n\nSkeletonModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: SkeletonModule\n});\nSkeletonModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SkeletonModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Skeleton],\n      declarations: [Skeleton]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Skeleton, SkeletonModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "Skeleton", "constructor", "shape", "animation", "borderRadius", "size", "width", "height", "containerClass", "containerStyle", "Object", "assign", "style", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "SkeletonModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-skeleton.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass Skeleton {\n    constructor() {\n        this.shape = \"rectangle\";\n        this.animation = \"wave\";\n        this.borderRadius = null;\n        this.size = null;\n        this.width = \"100%\";\n        this.height = \"1rem\";\n    }\n    containerClass() {\n        return {\n            'p-skeleton p-component': true,\n            'p-skeleton-circle': this.shape === 'circle',\n            'p-skeleton-none': this.animation === 'none'\n        };\n    }\n    containerStyle() {\n        if (this.size)\n            return Object.assign(Object.assign({}, this.style), { width: this.size, height: this.size, borderRadius: this.borderRadius });\n        else\n            return Object.assign(Object.assign({}, this.style), { width: this.width, height: this.height, borderRadius: this.borderRadius });\n    }\n}\nSkeleton.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Skeleton, deps: [], target: i0.ɵɵFactoryTarget.Component });\nSkeleton.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Skeleton, selector: \"p-skeleton\", inputs: { styleClass: \"styleClass\", style: \"style\", shape: \"shape\", animation: \"animation\", borderRadius: \"borderRadius\", size: \"size\", width: \"width\", height: \"height\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\">\n        </div>\n    `, isInline: true, styles: [\".p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Skeleton, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-skeleton', template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"containerStyle()\">\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-skeleton{position:relative;overflow:hidden}.p-skeleton:after{content:\\\"\\\";animation:p-skeleton-animation 1.2s infinite;height:100%;left:0;position:absolute;right:0;top:0;transform:translate(-100%);z-index:1}.p-skeleton.p-skeleton-circle{border-radius:50%}.p-skeleton-none:after{animation:none}@keyframes p-skeleton-animation{0%{transform:translate(-100%)}to{transform:translate(100%)}}\\n\"] }]\n        }], propDecorators: { styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], shape: [{\n                type: Input\n            }], animation: [{\n                type: Input\n            }], borderRadius: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }] } });\nclass SkeletonModule {\n}\nSkeletonModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SkeletonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nSkeletonModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: SkeletonModule, declarations: [Skeleton], imports: [CommonModule], exports: [Skeleton] });\nSkeletonModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SkeletonModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SkeletonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Skeleton],\n                    declarations: [Skeleton]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Skeleton, SkeletonModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;AAEA,MAAMC,QAAN,CAAe;EACXC,WAAW,GAAG;IACV,KAAKC,KAAL,GAAa,WAAb;IACA,KAAKC,SAAL,GAAiB,MAAjB;IACA,KAAKC,YAAL,GAAoB,IAApB;IACA,KAAKC,IAAL,GAAY,IAAZ;IACA,KAAKC,KAAL,GAAa,MAAb;IACA,KAAKC,MAAL,GAAc,MAAd;EACH;;EACDC,cAAc,GAAG;IACb,OAAO;MACH,0BAA0B,IADvB;MAEH,qBAAqB,KAAKN,KAAL,KAAe,QAFjC;MAGH,mBAAmB,KAAKC,SAAL,KAAmB;IAHnC,CAAP;EAKH;;EACDM,cAAc,GAAG;IACb,IAAI,KAAKJ,IAAT,EACI,OAAOK,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKC,KAAvB,CAAd,EAA6C;MAAEN,KAAK,EAAE,KAAKD,IAAd;MAAoBE,MAAM,EAAE,KAAKF,IAAjC;MAAuCD,YAAY,EAAE,KAAKA;IAA1D,CAA7C,CAAP,CADJ,KAGI,OAAOM,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKC,KAAvB,CAAd,EAA6C;MAAEN,KAAK,EAAE,KAAKA,KAAd;MAAqBC,MAAM,EAAE,KAAKA,MAAlC;MAA0CH,YAAY,EAAE,KAAKA;IAA7D,CAA7C,CAAP;EACP;;AArBU;;AAuBfJ,QAAQ,CAACa,IAAT;EAAA,iBAAqGb,QAArG;AAAA;;AACAA,QAAQ,CAACc,IAAT,kBAD2FtB,EAC3F;EAAA,MAAyFQ,QAAzF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FR,EAEnF,uBADR;IAAA;;IAAA;MAD2FA,EAEjD,2BAD1C;MAD2FA,EAE9E,6EADb;IAAA;EAAA;EAAA,eAGqdM,EAAE,CAACiB,OAHxd,EAGmjBjB,EAAE,CAACkB,OAHtjB;EAAA;EAAA;EAAA;AAAA;;AAIA;EAAA,mDAL2FxB,EAK3F,mBAA2FQ,QAA3F,EAAiH,CAAC;IACtGiB,IAAI,EAAExB,SADgG;IAEtGyB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BC,QAAQ,EAAG;AACxD;AACA;AACA,KAHmB;MAGZC,eAAe,EAAE3B,uBAAuB,CAAC4B,MAH7B;MAGqCC,aAAa,EAAE5B,iBAAiB,CAAC6B,IAHtE;MAG4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAHlF;MAKIC,MAAM,EAAE,CAAC,wYAAD;IALZ,CAAD;EAFgG,CAAD,CAAjH,QAQ4B;IAAEC,UAAU,EAAE,CAAC;MAC3BV,IAAI,EAAErB;IADqB,CAAD,CAAd;IAEZgB,KAAK,EAAE,CAAC;MACRK,IAAI,EAAErB;IADE,CAAD,CAFK;IAIZM,KAAK,EAAE,CAAC;MACRe,IAAI,EAAErB;IADE,CAAD,CAJK;IAMZO,SAAS,EAAE,CAAC;MACZc,IAAI,EAAErB;IADM,CAAD,CANC;IAQZQ,YAAY,EAAE,CAAC;MACfa,IAAI,EAAErB;IADS,CAAD,CARF;IAUZS,IAAI,EAAE,CAAC;MACPY,IAAI,EAAErB;IADC,CAAD,CAVM;IAYZU,KAAK,EAAE,CAAC;MACRW,IAAI,EAAErB;IADE,CAAD,CAZK;IAcZW,MAAM,EAAE,CAAC;MACTU,IAAI,EAAErB;IADG,CAAD;EAdI,CAR5B;AAAA;;AAyBA,MAAMgC,cAAN,CAAqB;;AAErBA,cAAc,CAACf,IAAf;EAAA,iBAA2Ge,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAjC2FrC,EAiC3F;EAAA,MAA4GoC;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAlC2FtC,EAkC3F;EAAA,UAAsIO,YAAtI;AAAA;;AACA;EAAA,mDAnC2FP,EAmC3F,mBAA2FoC,cAA3F,EAAuH,CAAC;IAC5GX,IAAI,EAAEpB,QADsG;IAE5GqB,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAAChC,YAAD,CADV;MAECiC,OAAO,EAAE,CAAChC,QAAD,CAFV;MAGCiC,YAAY,EAAE,CAACjC,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmB4B,cAAnB"}, "metadata": {}, "sourceType": "module"}