{"ast": null, "code": "export function isValidDate(value) {\n  return value instanceof Date && !isNaN(value);\n}", "map": {"version": 3, "names": ["isValidDate", "value", "Date", "isNaN"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isDate.js"], "sourcesContent": ["export function isValidDate(value) {\n    return value instanceof Date && !isNaN(value);\n}\n"], "mappings": "AAAA,OAAO,SAASA,WAAT,CAAqBC,KAArB,EAA4B;EAC/B,OAAOA,KAAK,YAAYC,IAAjB,IAAyB,CAACC,KAAK,CAACF,KAAD,CAAtC;AACH"}, "metadata": {}, "sourceType": "module"}