{"ast": null, "code": "var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n\n    return t;\n  };\n\n  return __assign.apply(this, arguments);\n};\n\nimport { bodyRegExps, namedReferences } from './named-references.js';\nimport { numericUnicodeMap } from './numeric-unicode-map.js';\nimport { fromCodePoint, getCodePoint } from './surrogate-pairs.js';\n\nvar allNamedReferences = __assign(__assign({}, namedReferences), {\n  all: namedReferences.html5\n});\n\nvar encodeRegExps = {\n  specialChars: /[<>'\"&]/g,\n  nonAscii: /[<>'\"&\\u0080-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  nonAsciiPrintable: /[<>'\"&\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  nonAsciiPrintableOnly: /[\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n  extensive: /[\\x01-\\x0c\\x0e-\\x1f\\x21-\\x2c\\x2e-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7d\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g\n};\nvar defaultEncodeOptions = {\n  mode: 'specialChars',\n  level: 'all',\n  numeric: 'decimal'\n};\n/** Encodes all the necessary (specified by `level`) characters in the text */\n\nexport function encode(text, _a) {\n  var _b = _a === void 0 ? defaultEncodeOptions : _a,\n      _c = _b.mode,\n      mode = _c === void 0 ? 'specialChars' : _c,\n      _d = _b.numeric,\n      numeric = _d === void 0 ? 'decimal' : _d,\n      _e = _b.level,\n      level = _e === void 0 ? 'all' : _e;\n\n  if (!text) {\n    return '';\n  }\n\n  var encodeRegExp = encodeRegExps[mode];\n  var references = allNamedReferences[level].characters;\n  var isHex = numeric === 'hexadecimal';\n  return String.prototype.replace.call(text, encodeRegExp, function (input) {\n    var result = references[input];\n\n    if (!result) {\n      var code = input.length > 1 ? getCodePoint(input, 0) : input.charCodeAt(0);\n      result = (isHex ? '&#x' + code.toString(16) : '&#' + code) + ';';\n    }\n\n    return result;\n  });\n}\nvar defaultDecodeOptions = {\n  scope: 'body',\n  level: 'all'\n};\nvar strict = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);/g;\nvar attribute = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;\nvar baseDecodeRegExps = {\n  xml: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.xml\n  },\n  html4: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.html4\n  },\n  html5: {\n    strict: strict,\n    attribute: attribute,\n    body: bodyRegExps.html5\n  }\n};\n\nvar decodeRegExps = __assign(__assign({}, baseDecodeRegExps), {\n  all: baseDecodeRegExps.html5\n});\n\nvar fromCharCode = String.fromCharCode;\nvar outOfBoundsChar = fromCharCode(65533);\nvar defaultDecodeEntityOptions = {\n  level: 'all'\n};\n\nfunction getDecodedEntity(entity, references, isAttribute, isStrict) {\n  var decodeResult = entity;\n  var decodeEntityLastChar = entity[entity.length - 1];\n\n  if (isAttribute && decodeEntityLastChar === '=') {\n    decodeResult = entity;\n  } else if (isStrict && decodeEntityLastChar !== ';') {\n    decodeResult = entity;\n  } else {\n    var decodeResultByReference = references[entity];\n\n    if (decodeResultByReference) {\n      decodeResult = decodeResultByReference;\n    } else if (entity[0] === '&' && entity[1] === '#') {\n      var decodeSecondChar = entity[2];\n      var decodeCode = decodeSecondChar == 'x' || decodeSecondChar == 'X' ? parseInt(entity.substr(3), 16) : parseInt(entity.substr(2));\n      decodeResult = decodeCode >= 0x10ffff ? outOfBoundsChar : decodeCode > 65535 ? fromCodePoint(decodeCode) : fromCharCode(numericUnicodeMap[decodeCode] || decodeCode);\n    }\n  }\n\n  return decodeResult;\n}\n/** Decodes a single entity */\n\n\nexport function decodeEntity(entity, _a) {\n  var _b = _a === void 0 ? defaultDecodeEntityOptions : _a,\n      _c = _b.level,\n      level = _c === void 0 ? 'all' : _c;\n\n  if (!entity) {\n    return '';\n  }\n\n  return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);\n}\n/** Decodes all entities in the text */\n\nexport function decode(text, _a) {\n  var _b = _a === void 0 ? defaultDecodeOptions : _a,\n      _c = _b.level,\n      level = _c === void 0 ? 'all' : _c,\n      _d = _b.scope,\n      scope = _d === void 0 ? level === 'xml' ? 'strict' : 'body' : _d;\n\n  if (!text) {\n    return '';\n  }\n\n  var decodeRegExp = decodeRegExps[level][scope];\n  var references = allNamedReferences[level].entities;\n  var isAttribute = scope === 'attribute';\n  var isStrict = scope === 'strict';\n  return text.replace(decodeRegExp, function (entity) {\n    return getDecodedEntity(entity, references, isAttribute, isStrict);\n  });\n}", "map": {"version": 3, "names": ["__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "bodyRegExps", "namedReferences", "numericUnicodeMap", "fromCodePoint", "getCodePoint", "allNamedReferences", "all", "html5", "encodeRegExps", "specialChars", "non<PERSON><PERSON><PERSON>", "nonAsciiPrintable", "nonAsciiPrintableOnly", "extensive", "defaultEncodeOptions", "mode", "level", "numeric", "encode", "text", "_a", "_b", "_c", "_d", "_e", "encodeRegExp", "references", "characters", "isHex", "String", "replace", "input", "result", "code", "charCodeAt", "toString", "defaultDecodeOptions", "scope", "strict", "attribute", "baseDecodeRegExps", "xml", "body", "html4", "decodeRegExps", "fromCharCode", "outOfBoundsChar", "defaultDecodeEntityOptions", "getDecodedEntity", "entity", "isAttribute", "isStrict", "decodeResult", "decodeEntityLastChar", "decodeResultByReference", "decodeSecondChar", "decodeCode", "parseInt", "substr", "decodeEntity", "entities", "decode", "decodeRegExp"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/html-entities/dist/esm/index.js"], "sourcesContent": ["var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { bodyRegExps, namedReferences } from './named-references.js';\nimport { numericUnicodeMap } from './numeric-unicode-map.js';\nimport { fromCodePoint, getCodePoint } from './surrogate-pairs.js';\nvar allNamedReferences = __assign(__assign({}, namedReferences), { all: namedReferences.html5 });\nvar encodeRegExps = {\n    specialChars: /[<>'\"&]/g,\n    nonAscii: /[<>'\"&\\u0080-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    nonAsciiPrintable: /[<>'\"&\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    nonAsciiPrintableOnly: /[\\x01-\\x08\\x11-\\x15\\x17-\\x1F\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g,\n    extensive: /[\\x01-\\x0c\\x0e-\\x1f\\x21-\\x2c\\x2e-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\x7d\\x7f-\\uD7FF\\uE000-\\uFFFF\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]?/g\n};\nvar defaultEncodeOptions = {\n    mode: 'specialChars',\n    level: 'all',\n    numeric: 'decimal'\n};\n/** Encodes all the necessary (specified by `level`) characters in the text */\nexport function encode(text, _a) {\n    var _b = _a === void 0 ? defaultEncodeOptions : _a, _c = _b.mode, mode = _c === void 0 ? 'specialChars' : _c, _d = _b.numeric, numeric = _d === void 0 ? 'decimal' : _d, _e = _b.level, level = _e === void 0 ? 'all' : _e;\n    if (!text) {\n        return '';\n    }\n    var encodeRegExp = encodeRegExps[mode];\n    var references = allNamedReferences[level].characters;\n    var isHex = numeric === 'hexadecimal';\n    return String.prototype.replace.call(text, encodeRegExp, function (input) {\n        var result = references[input];\n        if (!result) {\n            var code = input.length > 1 ? getCodePoint(input, 0) : input.charCodeAt(0);\n            result = (isHex ? '&#x' + code.toString(16) : '&#' + code) + ';';\n        }\n        return result;\n    });\n}\nvar defaultDecodeOptions = {\n    scope: 'body',\n    level: 'all'\n};\nvar strict = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+);/g;\nvar attribute = /&(?:#\\d+|#[xX][\\da-fA-F]+|[0-9a-zA-Z]+)[;=]?/g;\nvar baseDecodeRegExps = {\n    xml: {\n        strict: strict,\n        attribute: attribute,\n        body: bodyRegExps.xml\n    },\n    html4: {\n        strict: strict,\n        attribute: attribute,\n        body: bodyRegExps.html4\n    },\n    html5: {\n        strict: strict,\n        attribute: attribute,\n        body: bodyRegExps.html5\n    }\n};\nvar decodeRegExps = __assign(__assign({}, baseDecodeRegExps), { all: baseDecodeRegExps.html5 });\nvar fromCharCode = String.fromCharCode;\nvar outOfBoundsChar = fromCharCode(65533);\nvar defaultDecodeEntityOptions = {\n    level: 'all'\n};\nfunction getDecodedEntity(entity, references, isAttribute, isStrict) {\n    var decodeResult = entity;\n    var decodeEntityLastChar = entity[entity.length - 1];\n    if (isAttribute && decodeEntityLastChar === '=') {\n        decodeResult = entity;\n    }\n    else if (isStrict && decodeEntityLastChar !== ';') {\n        decodeResult = entity;\n    }\n    else {\n        var decodeResultByReference = references[entity];\n        if (decodeResultByReference) {\n            decodeResult = decodeResultByReference;\n        }\n        else if (entity[0] === '&' && entity[1] === '#') {\n            var decodeSecondChar = entity[2];\n            var decodeCode = decodeSecondChar == 'x' || decodeSecondChar == 'X'\n                ? parseInt(entity.substr(3), 16)\n                : parseInt(entity.substr(2));\n            decodeResult =\n                decodeCode >= 0x10ffff\n                    ? outOfBoundsChar\n                    : decodeCode > 65535\n                        ? fromCodePoint(decodeCode)\n                        : fromCharCode(numericUnicodeMap[decodeCode] || decodeCode);\n        }\n    }\n    return decodeResult;\n}\n/** Decodes a single entity */\nexport function decodeEntity(entity, _a) {\n    var _b = _a === void 0 ? defaultDecodeEntityOptions : _a, _c = _b.level, level = _c === void 0 ? 'all' : _c;\n    if (!entity) {\n        return '';\n    }\n    return getDecodedEntity(entity, allNamedReferences[level].entities, false, false);\n}\n/** Decodes all entities in the text */\nexport function decode(text, _a) {\n    var _b = _a === void 0 ? defaultDecodeOptions : _a, _c = _b.level, level = _c === void 0 ? 'all' : _c, _d = _b.scope, scope = _d === void 0 ? level === 'xml' ? 'strict' : 'body' : _d;\n    if (!text) {\n        return '';\n    }\n    var decodeRegExp = decodeRegExps[level][scope];\n    var references = allNamedReferences[level].entities;\n    var isAttribute = scope === 'attribute';\n    var isStrict = scope === 'strict';\n    return text.replace(decodeRegExp, function (entity) { return getDecodedEntity(entity, references, isAttribute, isStrict); });\n}\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,QAAQ,KAAKA,QAAd,IAA2B,YAAY;EAClDA,QAAQ,GAAGC,MAAM,CAACC,MAAP,IAAiB,UAASC,CAAT,EAAY;IACpC,KAAK,IAAIC,CAAJ,EAAOC,CAAC,GAAG,CAAX,EAAcC,CAAC,GAAGC,SAAS,CAACC,MAAjC,EAAyCH,CAAC,GAAGC,CAA7C,EAAgDD,CAAC,EAAjD,EAAqD;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAD,CAAb;;MACA,KAAK,IAAII,CAAT,IAAcL,CAAd,EAAiB,IAAIH,MAAM,CAACS,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCR,CAArC,EAAwCK,CAAxC,CAAJ,EACbN,CAAC,CAACM,CAAD,CAAD,GAAOL,CAAC,CAACK,CAAD,CAAR;IACP;;IACD,OAAON,CAAP;EACH,CAPD;;EAQA,OAAOH,QAAQ,CAACa,KAAT,CAAe,IAAf,EAAqBN,SAArB,CAAP;AACH,CAVD;;AAWA,SAASO,WAAT,EAAsBC,eAAtB,QAA6C,uBAA7C;AACA,SAASC,iBAAT,QAAkC,0BAAlC;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,sBAA5C;;AACA,IAAIC,kBAAkB,GAAGnB,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKe,eAAL,CAAT,EAAgC;EAAEK,GAAG,EAAEL,eAAe,CAACM;AAAvB,CAAhC,CAAjC;;AACA,IAAIC,aAAa,GAAG;EAChBC,YAAY,EAAE,UADE;EAEhBC,QAAQ,EAAE,iFAFM;EAGhBC,iBAAiB,EAAE,0GAHH;EAIhBC,qBAAqB,EAAE,qGAJP;EAKhBC,SAAS,EAAE;AALK,CAApB;AAOA,IAAIC,oBAAoB,GAAG;EACvBC,IAAI,EAAE,cADiB;EAEvBC,KAAK,EAAE,KAFgB;EAGvBC,OAAO,EAAE;AAHc,CAA3B;AAKA;;AACA,OAAO,SAASC,MAAT,CAAgBC,IAAhB,EAAsBC,EAAtB,EAA0B;EAC7B,IAAIC,EAAE,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgBN,oBAAhB,GAAuCM,EAAhD;EAAA,IAAoDE,EAAE,GAAGD,EAAE,CAACN,IAA5D;EAAA,IAAkEA,IAAI,GAAGO,EAAE,KAAK,KAAK,CAAZ,GAAgB,cAAhB,GAAiCA,EAA1G;EAAA,IAA8GC,EAAE,GAAGF,EAAE,CAACJ,OAAtH;EAAA,IAA+HA,OAAO,GAAGM,EAAE,KAAK,KAAK,CAAZ,GAAgB,SAAhB,GAA4BA,EAArK;EAAA,IAAyKC,EAAE,GAAGH,EAAE,CAACL,KAAjL;EAAA,IAAwLA,KAAK,GAAGQ,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EAAxN;;EACA,IAAI,CAACL,IAAL,EAAW;IACP,OAAO,EAAP;EACH;;EACD,IAAIM,YAAY,GAAGjB,aAAa,CAACO,IAAD,CAAhC;EACA,IAAIW,UAAU,GAAGrB,kBAAkB,CAACW,KAAD,CAAlB,CAA0BW,UAA3C;EACA,IAAIC,KAAK,GAAGX,OAAO,KAAK,aAAxB;EACA,OAAOY,MAAM,CAACjC,SAAP,CAAiBkC,OAAjB,CAAyBhC,IAAzB,CAA8BqB,IAA9B,EAAoCM,YAApC,EAAkD,UAAUM,KAAV,EAAiB;IACtE,IAAIC,MAAM,GAAGN,UAAU,CAACK,KAAD,CAAvB;;IACA,IAAI,CAACC,MAAL,EAAa;MACT,IAAIC,IAAI,GAAGF,KAAK,CAACrC,MAAN,GAAe,CAAf,GAAmBU,YAAY,CAAC2B,KAAD,EAAQ,CAAR,CAA/B,GAA4CA,KAAK,CAACG,UAAN,CAAiB,CAAjB,CAAvD;MACAF,MAAM,GAAG,CAACJ,KAAK,GAAG,QAAQK,IAAI,CAACE,QAAL,CAAc,EAAd,CAAX,GAA+B,OAAOF,IAA5C,IAAoD,GAA7D;IACH;;IACD,OAAOD,MAAP;EACH,CAPM,CAAP;AAQH;AACD,IAAII,oBAAoB,GAAG;EACvBC,KAAK,EAAE,MADgB;EAEvBrB,KAAK,EAAE;AAFgB,CAA3B;AAIA,IAAIsB,MAAM,GAAG,2CAAb;AACA,IAAIC,SAAS,GAAG,+CAAhB;AACA,IAAIC,iBAAiB,GAAG;EACpBC,GAAG,EAAE;IACDH,MAAM,EAAEA,MADP;IAEDC,SAAS,EAAEA,SAFV;IAGDG,IAAI,EAAE1C,WAAW,CAACyC;EAHjB,CADe;EAMpBE,KAAK,EAAE;IACHL,MAAM,EAAEA,MADL;IAEHC,SAAS,EAAEA,SAFR;IAGHG,IAAI,EAAE1C,WAAW,CAAC2C;EAHf,CANa;EAWpBpC,KAAK,EAAE;IACH+B,MAAM,EAAEA,MADL;IAEHC,SAAS,EAAEA,SAFR;IAGHG,IAAI,EAAE1C,WAAW,CAACO;EAHf;AAXa,CAAxB;;AAiBA,IAAIqC,aAAa,GAAG1D,QAAQ,CAACA,QAAQ,CAAC,EAAD,EAAKsD,iBAAL,CAAT,EAAkC;EAAElC,GAAG,EAAEkC,iBAAiB,CAACjC;AAAzB,CAAlC,CAA5B;;AACA,IAAIsC,YAAY,GAAGhB,MAAM,CAACgB,YAA1B;AACA,IAAIC,eAAe,GAAGD,YAAY,CAAC,KAAD,CAAlC;AACA,IAAIE,0BAA0B,GAAG;EAC7B/B,KAAK,EAAE;AADsB,CAAjC;;AAGA,SAASgC,gBAAT,CAA0BC,MAA1B,EAAkCvB,UAAlC,EAA8CwB,WAA9C,EAA2DC,QAA3D,EAAqE;EACjE,IAAIC,YAAY,GAAGH,MAAnB;EACA,IAAII,oBAAoB,GAAGJ,MAAM,CAACA,MAAM,CAACvD,MAAP,GAAgB,CAAjB,CAAjC;;EACA,IAAIwD,WAAW,IAAIG,oBAAoB,KAAK,GAA5C,EAAiD;IAC7CD,YAAY,GAAGH,MAAf;EACH,CAFD,MAGK,IAAIE,QAAQ,IAAIE,oBAAoB,KAAK,GAAzC,EAA8C;IAC/CD,YAAY,GAAGH,MAAf;EACH,CAFI,MAGA;IACD,IAAIK,uBAAuB,GAAG5B,UAAU,CAACuB,MAAD,CAAxC;;IACA,IAAIK,uBAAJ,EAA6B;MACzBF,YAAY,GAAGE,uBAAf;IACH,CAFD,MAGK,IAAIL,MAAM,CAAC,CAAD,CAAN,KAAc,GAAd,IAAqBA,MAAM,CAAC,CAAD,CAAN,KAAc,GAAvC,EAA4C;MAC7C,IAAIM,gBAAgB,GAAGN,MAAM,CAAC,CAAD,CAA7B;MACA,IAAIO,UAAU,GAAGD,gBAAgB,IAAI,GAApB,IAA2BA,gBAAgB,IAAI,GAA/C,GACXE,QAAQ,CAACR,MAAM,CAACS,MAAP,CAAc,CAAd,CAAD,EAAmB,EAAnB,CADG,GAEXD,QAAQ,CAACR,MAAM,CAACS,MAAP,CAAc,CAAd,CAAD,CAFd;MAGAN,YAAY,GACRI,UAAU,IAAI,QAAd,GACMV,eADN,GAEMU,UAAU,GAAG,KAAb,GACIrD,aAAa,CAACqD,UAAD,CADjB,GAEIX,YAAY,CAAC3C,iBAAiB,CAACsD,UAAD,CAAjB,IAAiCA,UAAlC,CAL1B;IAMH;EACJ;;EACD,OAAOJ,YAAP;AACH;AACD;;;AACA,OAAO,SAASO,YAAT,CAAsBV,MAAtB,EAA8B7B,EAA9B,EAAkC;EACrC,IAAIC,EAAE,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgB2B,0BAAhB,GAA6C3B,EAAtD;EAAA,IAA0DE,EAAE,GAAGD,EAAE,CAACL,KAAlE;EAAA,IAAyEA,KAAK,GAAGM,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EAAzG;;EACA,IAAI,CAAC2B,MAAL,EAAa;IACT,OAAO,EAAP;EACH;;EACD,OAAOD,gBAAgB,CAACC,MAAD,EAAS5C,kBAAkB,CAACW,KAAD,CAAlB,CAA0B4C,QAAnC,EAA6C,KAA7C,EAAoD,KAApD,CAAvB;AACH;AACD;;AACA,OAAO,SAASC,MAAT,CAAgB1C,IAAhB,EAAsBC,EAAtB,EAA0B;EAC7B,IAAIC,EAAE,GAAGD,EAAE,KAAK,KAAK,CAAZ,GAAgBgB,oBAAhB,GAAuChB,EAAhD;EAAA,IAAoDE,EAAE,GAAGD,EAAE,CAACL,KAA5D;EAAA,IAAmEA,KAAK,GAAGM,EAAE,KAAK,KAAK,CAAZ,GAAgB,KAAhB,GAAwBA,EAAnG;EAAA,IAAuGC,EAAE,GAAGF,EAAE,CAACgB,KAA/G;EAAA,IAAsHA,KAAK,GAAGd,EAAE,KAAK,KAAK,CAAZ,GAAgBP,KAAK,KAAK,KAAV,GAAkB,QAAlB,GAA6B,MAA7C,GAAsDO,EAApL;;EACA,IAAI,CAACJ,IAAL,EAAW;IACP,OAAO,EAAP;EACH;;EACD,IAAI2C,YAAY,GAAGlB,aAAa,CAAC5B,KAAD,CAAb,CAAqBqB,KAArB,CAAnB;EACA,IAAIX,UAAU,GAAGrB,kBAAkB,CAACW,KAAD,CAAlB,CAA0B4C,QAA3C;EACA,IAAIV,WAAW,GAAGb,KAAK,KAAK,WAA5B;EACA,IAAIc,QAAQ,GAAGd,KAAK,KAAK,QAAzB;EACA,OAAOlB,IAAI,CAACW,OAAL,CAAagC,YAAb,EAA2B,UAAUb,MAAV,EAAkB;IAAE,OAAOD,gBAAgB,CAACC,MAAD,EAASvB,UAAT,EAAqBwB,WAArB,EAAkCC,QAAlC,CAAvB;EAAqE,CAApH,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}