{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction Tag_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.icon);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nclass Tag {\n  containerClass() {\n    return {\n      'p-tag p-component': true,\n      'p-tag-info': this.severity === 'info',\n      'p-tag-success': this.severity === 'success',\n      'p-tag-warning': this.severity === 'warning',\n      'p-tag-danger': this.severity === 'danger',\n      'p-tag-rounded': this.rounded\n    };\n  }\n\n}\n\nTag.ɵfac = function Tag_Factory(t) {\n  return new (t || Tag)();\n};\n\nTag.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Tag,\n  selectors: [[\"p-tag\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    styleClass: \"styleClass\",\n    style: \"style\",\n    severity: \"severity\",\n    value: \"value\",\n    icon: \"icon\",\n    rounded: \"rounded\"\n  },\n  ngContentSelectors: _c0,\n  decls: 5,\n  vars: 6,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-tag-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-tag-value\"], [1, \"p-tag-icon\", 3, \"ngClass\"]],\n  template: function Tag_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"span\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵtemplate(2, Tag_span_2_Template, 1, 1, \"span\", 1);\n      i0.ɵɵelementStart(3, \"span\", 2);\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.icon);\n      i0.ɵɵadvance(2);\n      i0.ɵɵtextInterpolate(ctx.value);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n  styles: [\".p-tag{display:inline-flex;align-items:center;justify-content:center}.p-tag-icon,.p-tag-value,.p-tag-icon.pi{line-height:1.5}.p-tag.p-tag-rounded{border-radius:10rem}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tag, [{\n    type: Component,\n    args: [{\n      selector: 'p-tag',\n      template: `\n        <span [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-tag-icon\" [ngClass]=\"icon\" *ngIf=\"icon\"></span>\n            <span class=\"p-tag-value\">{{value}}</span>\n        </span>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-tag{display:inline-flex;align-items:center;justify-content:center}.p-tag-icon,.p-tag-value,.p-tag-icon.pi{line-height:1.5}.p-tag.p-tag-rounded{border-radius:10rem}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    severity: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    rounded: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TagModule {}\n\nTagModule.ɵfac = function TagModule_Factory(t) {\n  return new (t || TagModule)();\n};\n\nTagModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TagModule\n});\nTagModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TagModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tag],\n      declarations: [Tag]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Tag, TagModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "Tag", "containerClass", "severity", "rounded", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "style", "value", "icon", "TagModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-tag.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass Tag {\n    containerClass() {\n        return {\n            'p-tag p-component': true,\n            'p-tag-info': this.severity === 'info',\n            'p-tag-success': this.severity === 'success',\n            'p-tag-warning': this.severity === 'warning',\n            'p-tag-danger': this.severity === 'danger',\n            'p-tag-rounded': this.rounded\n        };\n    }\n}\nTag.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tag, deps: [], target: i0.ɵɵFactoryTarget.Component });\nTag.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Tag, selector: \"p-tag\", inputs: { styleClass: \"styleClass\", style: \"style\", severity: \"severity\", value: \"value\", icon: \"icon\", rounded: \"rounded\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <span [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-tag-icon\" [ngClass]=\"icon\" *ngIf=\"icon\"></span>\n            <span class=\"p-tag-value\">{{value}}</span>\n        </span>\n    `, isInline: true, styles: [\".p-tag{display:inline-flex;align-items:center;justify-content:center}.p-tag-icon,.p-tag-value,.p-tag-icon.pi{line-height:1.5}.p-tag.p-tag-rounded{border-radius:10rem}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tag, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tag', template: `\n        <span [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-tag-icon\" [ngClass]=\"icon\" *ngIf=\"icon\"></span>\n            <span class=\"p-tag-value\">{{value}}</span>\n        </span>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-tag{display:inline-flex;align-items:center;justify-content:center}.p-tag-icon,.p-tag-value,.p-tag-icon.pi{line-height:1.5}.p-tag.p-tag-rounded{border-radius:10rem}\\n\"] }]\n        }], propDecorators: { styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], severity: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], rounded: [{\n                type: Input\n            }] } });\nclass TagModule {\n}\nTagModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TagModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTagModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TagModule, declarations: [Tag], imports: [CommonModule], exports: [Tag] });\nTagModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TagModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TagModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Tag],\n                    declarations: [Tag]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tag, TagModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;IAcsFP,EAI1E,wB;;;;mBAJ0EA,E;IAAAA,EAIjD,mC;;;;;;AAhBrC,MAAMQ,GAAN,CAAU;EACNC,cAAc,GAAG;IACb,OAAO;MACH,qBAAqB,IADlB;MAEH,cAAc,KAAKC,QAAL,KAAkB,MAF7B;MAGH,iBAAiB,KAAKA,QAAL,KAAkB,SAHhC;MAIH,iBAAiB,KAAKA,QAAL,KAAkB,SAJhC;MAKH,gBAAgB,KAAKA,QAAL,KAAkB,QAL/B;MAMH,iBAAiB,KAAKC;IANnB,CAAP;EAQH;;AAVK;;AAYVH,GAAG,CAACI,IAAJ;EAAA,iBAAgGJ,GAAhG;AAAA;;AACAA,GAAG,CAACK,IAAJ,kBADsFb,EACtF;EAAA,MAAoFQ,GAApF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADsFR,EACtF;MADsFA,EAE9E,6BADR;MADsFA,EAG1E,gBAFZ;MADsFA,EAI1E,oDAHZ;MADsFA,EAK1E,6BAJZ;MADsFA,EAKhD,UAJtC;MADsFA,EAKvC,iBAJ/C;IAAA;;IAAA;MADsFA,EAE3C,2BAD3C;MADsFA,EAExE,kEADd;MADsFA,EAI/B,aAHvD;MADsFA,EAI/B,6BAHvD;MADsFA,EAKhD,aAJtC;MADsFA,EAKhD,6BAJtC;IAAA;EAAA;EAAA,eAMuPM,EAAE,CAACQ,OAN1P,EAMqVR,EAAE,CAACS,IANxV,EAMybT,EAAE,CAACU,OAN5b;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA,mDARsFhB,EAQtF,mBAA2FQ,GAA3F,EAA4G,CAAC;IACjGS,IAAI,EAAEhB,SAD2F;IAEjGiB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,OAAZ;MAAqBC,QAAQ,EAAG;AACnD;AACA;AACA;AACA;AACA;AACA,KANmB;MAMZC,eAAe,EAAEnB,uBAAuB,CAACoB,MAN7B;MAMqCC,aAAa,EAAEpB,iBAAiB,CAACqB,IANtE;MAM4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CANlF;MAQIC,MAAM,EAAE,CAAC,0KAAD;IARZ,CAAD;EAF2F,CAAD,CAA5G,QAW4B;IAAEC,UAAU,EAAE,CAAC;MAC3BV,IAAI,EAAEb;IADqB,CAAD,CAAd;IAEZwB,KAAK,EAAE,CAAC;MACRX,IAAI,EAAEb;IADE,CAAD,CAFK;IAIZM,QAAQ,EAAE,CAAC;MACXO,IAAI,EAAEb;IADK,CAAD,CAJE;IAMZyB,KAAK,EAAE,CAAC;MACRZ,IAAI,EAAEb;IADE,CAAD,CANK;IAQZ0B,IAAI,EAAE,CAAC;MACPb,IAAI,EAAEb;IADC,CAAD,CARM;IAUZO,OAAO,EAAE,CAAC;MACVM,IAAI,EAAEb;IADI,CAAD;EAVG,CAX5B;AAAA;;AAwBA,MAAM2B,SAAN,CAAgB;;AAEhBA,SAAS,CAACnB,IAAV;EAAA,iBAAsGmB,SAAtG;AAAA;;AACAA,SAAS,CAACC,IAAV,kBAnCsFhC,EAmCtF;EAAA,MAAuG+B;AAAvG;AACAA,SAAS,CAACE,IAAV,kBApCsFjC,EAoCtF;EAAA,UAA4HO,YAA5H;AAAA;;AACA;EAAA,mDArCsFP,EAqCtF,mBAA2F+B,SAA3F,EAAkH,CAAC;IACvGd,IAAI,EAAEZ,QADiG;IAEvGa,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAAC3B,YAAD,CADV;MAEC4B,OAAO,EAAE,CAAC3B,GAAD,CAFV;MAGC4B,YAAY,EAAE,CAAC5B,GAAD;IAHf,CAAD;EAFiG,CAAD,CAAlH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,GAAT,EAAcuB,SAAd"}, "metadata": {}, "sourceType": "module"}