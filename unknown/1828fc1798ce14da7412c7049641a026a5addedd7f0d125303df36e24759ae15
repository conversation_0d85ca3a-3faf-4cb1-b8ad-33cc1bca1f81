{"ast": null, "code": "import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n  return (source, subscriber) => {\n    let hasState = hasSeed;\n    let state = seed;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n      state = hasState ? accumulator(state, value, i) : (hasState = true, value);\n      emitOnNext && subscriber.next(state);\n    }, emitBeforeComplete && (() => {\n      hasState && subscriber.next(state);\n      subscriber.complete();\n    })));\n  };\n}", "map": {"version": 3, "names": ["createOperatorSubscriber", "scanInternals", "accumulator", "seed", "hasSeed", "emitOnNext", "emitBeforeComplete", "source", "subscriber", "hasState", "state", "index", "subscribe", "value", "i", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/scanInternals.js"], "sourcesContent": ["import { createOperatorSubscriber } from './OperatorSubscriber';\nexport function scanInternals(accumulator, seed, hasSeed, emitOnNext, emitBeforeComplete) {\n    return (source, subscriber) => {\n        let hasState = hasSeed;\n        let state = seed;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const i = index++;\n            state = hasState\n                ?\n                    accumulator(state, value, i)\n                :\n                    ((hasState = true), value);\n            emitOnNext && subscriber.next(state);\n        }, emitBeforeComplete &&\n            (() => {\n                hasState && subscriber.next(state);\n                subscriber.complete();\n            })));\n    };\n}\n"], "mappings": "AAAA,SAASA,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,aAAT,CAAuBC,WAAvB,EAAoCC,IAApC,EAA0CC,OAA1C,EAAmDC,UAAnD,EAA+DC,kBAA/D,EAAmF;EACtF,OAAO,CAACC,MAAD,EAASC,UAAT,KAAwB;IAC3B,IAAIC,QAAQ,GAAGL,OAAf;IACA,IAAIM,KAAK,GAAGP,IAAZ;IACA,IAAIQ,KAAK,GAAG,CAAZ;IACAJ,MAAM,CAACK,SAAP,CAAiBZ,wBAAwB,CAACQ,UAAD,EAAcK,KAAD,IAAW;MAC7D,MAAMC,CAAC,GAAGH,KAAK,EAAf;MACAD,KAAK,GAAGD,QAAQ,GAERP,WAAW,CAACQ,KAAD,EAAQG,KAAR,EAAeC,CAAf,CAFH,IAINL,QAAQ,GAAG,IAAZ,EAAmBI,KAJZ,CAAhB;MAKAR,UAAU,IAAIG,UAAU,CAACO,IAAX,CAAgBL,KAAhB,CAAd;IACH,CARwC,EAQtCJ,kBAAkB,KAChB,MAAM;MACHG,QAAQ,IAAID,UAAU,CAACO,IAAX,CAAgBL,KAAhB,CAAZ;MACAF,UAAU,CAACQ,QAAX;IACH,CAJgB,CARoB,CAAzC;EAaH,CAjBD;AAkBH"}, "metadata": {}, "sourceType": "module"}