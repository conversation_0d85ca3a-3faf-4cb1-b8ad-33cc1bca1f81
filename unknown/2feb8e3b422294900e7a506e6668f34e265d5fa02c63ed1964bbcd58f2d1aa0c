{"ast": null, "code": "import { Chart, registerables } from '../dist/chart.mjs';\nChart.register(...registerables);\nexport default Chart;", "map": {"version": 3, "names": ["Chart", "registerables", "register"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/chart.js/auto/auto.mjs"], "sourcesContent": ["import {Chart, registerables} from '../dist/chart.mjs';\n\nChart.register(...registerables);\n\nexport default Chart;\n"], "mappings": "AAAA,SAAQA,KAAR,EAAeC,aAAf,QAAmC,mBAAnC;AAEAD,KAAK,CAACE,QAAN,CAAe,GAAGD,aAAlB;AAEA,eAAeD,KAAf"}, "metadata": {}, "sourceType": "module"}