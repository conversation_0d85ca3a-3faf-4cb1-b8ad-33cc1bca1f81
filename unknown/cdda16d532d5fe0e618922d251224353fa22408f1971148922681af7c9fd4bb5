{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent = Infinity, scheduler) {\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler));\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "expand", "project", "concurrent", "Infinity", "scheduler", "source", "subscriber", "undefined"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/expand.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent = Infinity, scheduler) {\n    concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n    return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler));\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,SAASC,MAAT,CAAgBC,OAAhB,EAAyBC,UAAU,GAAGC,QAAtC,EAAgDC,SAAhD,EAA2D;EAC9DF,UAAU,GAAG,CAACA,UAAU,IAAI,CAAf,IAAoB,CAApB,GAAwBC,QAAxB,GAAmCD,UAAhD;EACA,OAAOJ,OAAO,CAAC,CAACO,MAAD,EAASC,UAAT,KAAwBP,cAAc,CAACM,MAAD,EAASC,UAAT,EAAqBL,OAArB,EAA8BC,UAA9B,EAA0CK,SAA1C,EAAqD,IAArD,EAA2DH,SAA3D,CAAvC,CAAd;AACH"}, "metadata": {}, "sourceType": "module"}