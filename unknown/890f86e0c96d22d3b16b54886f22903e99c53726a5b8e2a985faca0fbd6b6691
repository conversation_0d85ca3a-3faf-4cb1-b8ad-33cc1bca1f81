{"ast": null, "code": "import { Immediate } from '../util/Immediate';\nconst {\n  setImmediate,\n  clearImmediate\n} = Immediate;\nexport const immediateProvider = {\n  setImmediate(...args) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n  },\n\n  clearImmediate(handle) {\n    const {\n      delegate\n    } = immediateProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n  },\n\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Immediate", "setImmediate", "clearImmediate", "immediate<PERSON>rovider", "args", "delegate", "handle", "undefined"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/immediateProvider.js"], "sourcesContent": ["import { Immediate } from '../util/Immediate';\nconst { setImmediate, clearImmediate } = Immediate;\nexport const immediateProvider = {\n    setImmediate(...args) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.setImmediate) || setImmediate)(...args);\n    },\n    clearImmediate(handle) {\n        const { delegate } = immediateProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.clearImmediate) || clearImmediate)(handle);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,mBAA1B;AACA,MAAM;EAAEC,YAAF;EAAgBC;AAAhB,IAAmCF,SAAzC;AACA,OAAO,MAAMG,iBAAiB,GAAG;EAC7BF,YAAY,CAAC,GAAGG,IAAJ,EAAU;IAClB,MAAM;MAAEC;IAAF,IAAeF,iBAArB;IACA,OAAO,CAAC,CAACE,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACJ,YAA9D,KAA+EA,YAAhF,EAA8F,GAAGG,IAAjG,CAAP;EACH,CAJ4B;;EAK7BF,cAAc,CAACI,MAAD,EAAS;IACnB,MAAM;MAAED;IAAF,IAAeF,iBAArB;IACA,OAAO,CAAC,CAACE,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,KAAK,KAAK,CAAvC,GAA2C,KAAK,CAAhD,GAAoDA,QAAQ,CAACH,cAA9D,KAAiFA,cAAlF,EAAkGI,MAAlG,CAAP;EACH,CAR4B;;EAS7BD,QAAQ,EAAEE;AATmB,CAA1B"}, "metadata": {}, "sourceType": "module"}