{"ast": null, "code": "import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest(...args) {\n  const scheduler = popScheduler(args);\n  const resultSelector = popResultSelector(args);\n  const {\n    args: observables,\n    keys\n  } = argsArgArrayOrObject(args);\n\n  if (observables.length === 0) {\n    return from([], scheduler);\n  }\n\n  const result = new Observable(combineLatestInit(observables, scheduler, keys ? values => createObject(keys, values) : identity));\n  return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform = identity) {\n  return subscriber => {\n    maybeSchedule(scheduler, () => {\n      const {\n        length\n      } = observables;\n      const values = new Array(length);\n      let active = length;\n      let remainingFirstValues = length;\n\n      for (let i = 0; i < length; i++) {\n        maybeSchedule(scheduler, () => {\n          const source = from(observables[i], scheduler);\n          let hasFirstValue = false;\n          source.subscribe(createOperatorSubscriber(subscriber, value => {\n            values[i] = value;\n\n            if (!hasFirstValue) {\n              hasFirstValue = true;\n              remainingFirstValues--;\n            }\n\n            if (!remainingFirstValues) {\n              subscriber.next(valueTransform(values.slice()));\n            }\n          }, () => {\n            if (! --active) {\n              subscriber.complete();\n            }\n          }));\n        }, subscriber);\n      }\n    }, subscriber);\n  };\n}\n\nfunction maybeSchedule(scheduler, execute, subscription) {\n  if (scheduler) {\n    executeSchedule(subscription, scheduler, execute);\n  } else {\n    execute();\n  }\n}", "map": {"version": 3, "names": ["Observable", "argsArgArrayOrObject", "from", "identity", "mapOneOrManyArgs", "popResultSelector", "popScheduler", "createObject", "createOperatorSubscriber", "executeSchedule", "combineLatest", "args", "scheduler", "resultSelector", "observables", "keys", "length", "result", "combineLatestInit", "values", "pipe", "valueTransform", "subscriber", "maybeSchedule", "Array", "active", "remainingFirstValues", "i", "source", "hasFirstValue", "subscribe", "value", "next", "slice", "complete", "execute", "subscription"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/combineLatest.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { argsArgArrayOrObject } from '../util/argsArgArrayOrObject';\nimport { from } from './from';\nimport { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { popResultSelector, popScheduler } from '../util/args';\nimport { createObject } from '../util/createObject';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function combineLatest(...args) {\n    const scheduler = popScheduler(args);\n    const resultSelector = popResultSelector(args);\n    const { args: observables, keys } = argsArgArrayOrObject(args);\n    if (observables.length === 0) {\n        return from([], scheduler);\n    }\n    const result = new Observable(combineLatestInit(observables, scheduler, keys\n        ?\n            (values) => createObject(keys, values)\n        :\n            identity));\n    return resultSelector ? result.pipe(mapOneOrManyArgs(resultSelector)) : result;\n}\nexport function combineLatestInit(observables, scheduler, valueTransform = identity) {\n    return (subscriber) => {\n        maybeSchedule(scheduler, () => {\n            const { length } = observables;\n            const values = new Array(length);\n            let active = length;\n            let remainingFirstValues = length;\n            for (let i = 0; i < length; i++) {\n                maybeSchedule(scheduler, () => {\n                    const source = from(observables[i], scheduler);\n                    let hasFirstValue = false;\n                    source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                        values[i] = value;\n                        if (!hasFirstValue) {\n                            hasFirstValue = true;\n                            remainingFirstValues--;\n                        }\n                        if (!remainingFirstValues) {\n                            subscriber.next(valueTransform(values.slice()));\n                        }\n                    }, () => {\n                        if (!--active) {\n                            subscriber.complete();\n                        }\n                    }));\n                }, subscriber);\n            }\n        }, subscriber);\n    };\n}\nfunction maybeSchedule(scheduler, execute, subscription) {\n    if (scheduler) {\n        executeSchedule(subscription, scheduler, execute);\n    }\n    else {\n        execute();\n    }\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,oBAAT,QAAqC,8BAArC;AACA,SAASC,IAAT,QAAqB,QAArB;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,iBAAT,EAA4BC,YAA5B,QAAgD,cAAhD;AACA,SAASC,YAAT,QAA6B,sBAA7B;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,OAAO,SAASC,aAAT,CAAuB,GAAGC,IAA1B,EAAgC;EACnC,MAAMC,SAAS,GAAGN,YAAY,CAACK,IAAD,CAA9B;EACA,MAAME,cAAc,GAAGR,iBAAiB,CAACM,IAAD,CAAxC;EACA,MAAM;IAAEA,IAAI,EAAEG,WAAR;IAAqBC;EAArB,IAA8Bd,oBAAoB,CAACU,IAAD,CAAxD;;EACA,IAAIG,WAAW,CAACE,MAAZ,KAAuB,CAA3B,EAA8B;IAC1B,OAAOd,IAAI,CAAC,EAAD,EAAKU,SAAL,CAAX;EACH;;EACD,MAAMK,MAAM,GAAG,IAAIjB,UAAJ,CAAekB,iBAAiB,CAACJ,WAAD,EAAcF,SAAd,EAAyBG,IAAI,GAEnEI,MAAD,IAAYZ,YAAY,CAACQ,IAAD,EAAOI,MAAP,CAF4C,GAIpEhB,QAJuC,CAAhC,CAAf;EAKA,OAAOU,cAAc,GAAGI,MAAM,CAACG,IAAP,CAAYhB,gBAAgB,CAACS,cAAD,CAA5B,CAAH,GAAmDI,MAAxE;AACH;AACD,OAAO,SAASC,iBAAT,CAA2BJ,WAA3B,EAAwCF,SAAxC,EAAmDS,cAAc,GAAGlB,QAApE,EAA8E;EACjF,OAAQmB,UAAD,IAAgB;IACnBC,aAAa,CAACX,SAAD,EAAY,MAAM;MAC3B,MAAM;QAAEI;MAAF,IAAaF,WAAnB;MACA,MAAMK,MAAM,GAAG,IAAIK,KAAJ,CAAUR,MAAV,CAAf;MACA,IAAIS,MAAM,GAAGT,MAAb;MACA,IAAIU,oBAAoB,GAAGV,MAA3B;;MACA,KAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGX,MAApB,EAA4BW,CAAC,EAA7B,EAAiC;QAC7BJ,aAAa,CAACX,SAAD,EAAY,MAAM;UAC3B,MAAMgB,MAAM,GAAG1B,IAAI,CAACY,WAAW,CAACa,CAAD,CAAZ,EAAiBf,SAAjB,CAAnB;UACA,IAAIiB,aAAa,GAAG,KAApB;UACAD,MAAM,CAACE,SAAP,CAAiBtB,wBAAwB,CAACc,UAAD,EAAcS,KAAD,IAAW;YAC7DZ,MAAM,CAACQ,CAAD,CAAN,GAAYI,KAAZ;;YACA,IAAI,CAACF,aAAL,EAAoB;cAChBA,aAAa,GAAG,IAAhB;cACAH,oBAAoB;YACvB;;YACD,IAAI,CAACA,oBAAL,EAA2B;cACvBJ,UAAU,CAACU,IAAX,CAAgBX,cAAc,CAACF,MAAM,CAACc,KAAP,EAAD,CAA9B;YACH;UACJ,CATwC,EAStC,MAAM;YACL,IAAI,CAAC,GAAER,MAAP,EAAe;cACXH,UAAU,CAACY,QAAX;YACH;UACJ,CAbwC,CAAzC;QAcH,CAjBY,EAiBVZ,UAjBU,CAAb;MAkBH;IACJ,CAzBY,EAyBVA,UAzBU,CAAb;EA0BH,CA3BD;AA4BH;;AACD,SAASC,aAAT,CAAuBX,SAAvB,EAAkCuB,OAAlC,EAA2CC,YAA3C,EAAyD;EACrD,IAAIxB,SAAJ,EAAe;IACXH,eAAe,CAAC2B,YAAD,EAAexB,SAAf,EAA0BuB,OAA1B,CAAf;EACH,CAFD,MAGK;IACDA,OAAO;EACV;AACJ"}, "metadata": {}, "sourceType": "module"}