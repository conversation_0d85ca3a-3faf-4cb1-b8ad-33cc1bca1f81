{"ast": null, "code": "import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n  return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}", "map": {"version": 3, "names": ["iterator", "Symbol_iterator", "isFunction", "isIterable", "input"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isIterable.js"], "sourcesContent": ["import { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from './isFunction';\nexport function isIterable(input) {\n    return isFunction(input === null || input === void 0 ? void 0 : input[Symbol_iterator]);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,IAAIC,eAArB,QAA4C,oBAA5C;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,UAAT,CAAoBC,KAApB,EAA2B;EAC9B,OAAOF,UAAU,CAACE,KAAK,KAAK,IAAV,IAAkBA,KAAK,KAAK,KAAK,CAAjC,GAAqC,KAAK,CAA1C,GAA8CA,KAAK,CAACH,eAAD,CAApD,CAAjB;AACH"}, "metadata": {}, "sourceType": "module"}