{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, Component, ViewEncapsulation, Inject, Input, EventEmitter, ChangeDetectionStrategy, Optional, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\n\nconst _c0 = function (a0) {\n  return {\n    \"p-treenode-droppoint-active\": a0\n  };\n};\n\nfunction UITreeNode_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 4);\n    i0.ɵɵlistener(\"drop\", function UITreeNode_ng_template_0_li_0_Template_li_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.onDropPoint($event, -1));\n    })(\"dragover\", function UITreeNode_ng_template_0_li_0_Template_li_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onDropPointDragOver($event));\n    })(\"dragenter\", function UITreeNode_ng_template_0_li_0_Template_li_dragenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.onDropPointDragEnter($event, -1));\n    })(\"dragleave\", function UITreeNode_ng_template_0_li_0_Template_li_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onDropPointDragLeave($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.draghoverPrev));\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"p-checkbox-disabled\": a0\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"p-highlight\": a0,\n    \"p-indeterminate\": a1\n  };\n};\n\nconst _c3 = function (a0, a1) {\n  return {\n    \"pi-check\": a0,\n    \"pi-minus\": a1\n  };\n};\n\nfunction UITreeNode_ng_template_0_li_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"span\", 15);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, ctx_r10.node.selectable === false));\n    i0.ɵɵattribute(\"aria-checked\", ctx_r10.isSelected());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c2, ctx_r10.isSelected(), ctx_r10.node.partialSelected));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c3, ctx_r10.isSelected(), ctx_r10.node.partialSelected));\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r11.getIcon());\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r12.node.label);\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_1_span_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\n\nfunction UITreeNode_ng_template_0_li_1_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, UITreeNode_ng_template_0_li_1_span_8_ng_container_1_Template, 1, 0, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r13.tree.getTemplateForNode(ctx_r13.node))(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r13.node));\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_1_ul_9_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 19);\n  }\n\n  if (rf & 2) {\n    const childNode_r17 = ctx.$implicit;\n    const firstChild_r18 = ctx.first;\n    const lastChild_r19 = ctx.last;\n    const index_r20 = ctx.index;\n    const ctx_r16 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"node\", childNode_r17)(\"parentNode\", ctx_r16.node)(\"firstChild\", firstChild_r18)(\"lastChild\", lastChild_r19)(\"index\", index_r20)(\"itemSize\", ctx_r16.itemSize)(\"level\", ctx_r16.level + 1);\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_1_ul_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 17);\n    i0.ɵɵtemplate(1, UITreeNode_ng_template_0_li_1_ul_9_p_treeNode_1_Template, 1, 7, \"p-treeNode\", 18);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r14.node.expanded ? \"block\" : \"none\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r14.node.children)(\"ngForTrackBy\", ctx_r14.tree.trackBy);\n  }\n}\n\nconst _c5 = function (a1, a2) {\n  return [\"p-treenode\", a1, a2];\n};\n\nconst _c6 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nconst _c7 = function (a0, a1, a2) {\n  return {\n    \"p-treenode-selectable\": a0,\n    \"p-treenode-dragover\": a1,\n    \"p-highlight\": a2\n  };\n};\n\nconst _c8 = function (a0, a1) {\n  return {\n    \"pi-chevron-right\": a0,\n    \"pi-chevron-down\": a1\n  };\n};\n\nfunction UITreeNode_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 5)(1, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function UITreeNode_ng_template_0_li_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onNodeClick($event));\n    })(\"contextmenu\", function UITreeNode_ng_template_0_li_1_Template_div_contextmenu_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r23 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r23.onNodeRightClick($event));\n    })(\"touchend\", function UITreeNode_ng_template_0_li_1_Template_div_touchend_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r24 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r24.onNodeTouchEnd());\n    })(\"drop\", function UITreeNode_ng_template_0_li_1_Template_div_drop_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.onDropNode($event));\n    })(\"dragover\", function UITreeNode_ng_template_0_li_1_Template_div_dragover_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onDropNodeDragOver($event));\n    })(\"dragenter\", function UITreeNode_ng_template_0_li_1_Template_div_dragenter_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.onDropNodeDragEnter($event));\n    })(\"dragleave\", function UITreeNode_ng_template_0_li_1_Template_div_dragleave_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onDropNodeDragLeave($event));\n    })(\"dragstart\", function UITreeNode_ng_template_0_li_1_Template_div_dragstart_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onDragStart($event));\n    })(\"dragend\", function UITreeNode_ng_template_0_li_1_Template_div_dragend_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r30 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r30.onDragStop($event));\n    })(\"keydown\", function UITreeNode_ng_template_0_li_1_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.onKeyDown($event));\n    });\n    i0.ɵɵelementStart(2, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function UITreeNode_ng_template_0_li_1_Template_button_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.toggle($event));\n    });\n    i0.ɵɵelement(3, \"span\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, UITreeNode_ng_template_0_li_1_div_4_Template, 3, 12, \"div\", 9);\n    i0.ɵɵtemplate(5, UITreeNode_ng_template_0_li_1_span_5_Template, 1, 2, \"span\", 3);\n    i0.ɵɵelementStart(6, \"span\", 10);\n    i0.ɵɵtemplate(7, UITreeNode_ng_template_0_li_1_span_7_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(8, UITreeNode_ng_template_0_li_1_span_8_Template, 2, 4, \"span\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, UITreeNode_ng_template_0_li_1_ul_9_Template, 2, 4, \"ul\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c5, ctx_r2.node.styleClass || \"\", ctx_r2.isLeaf() ? \"p-treenode-leaf\" : \"\"))(\"ngStyle\", i0.ɵɵpureFunction1(21, _c6, ctx_r2.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"padding-left\", ctx_r2.level * ctx_r2.indentation + \"rem\");\n    i0.ɵɵproperty(\"draggable\", ctx_r2.tree.draggableNodes)(\"ngClass\", i0.ɵɵpureFunction3(23, _c7, ctx_r2.tree.selectionMode && ctx_r2.node.selectable !== false, ctx_r2.draghoverNode, ctx_r2.isSelected()));\n    i0.ɵɵattribute(\"tabindex\", 0)(\"aria-posinset\", ctx_r2.index + 1)(\"aria-expanded\", ctx_r2.node.expanded)(\"aria-selected\", ctx_r2.isSelected())(\"aria-label\", ctx_r2.node.label);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.tree.togglerAriaLabel);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c8, !ctx_r2.node.expanded, ctx_r2.node.expanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.selectionMode == \"checkbox\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.node.icon || ctx_r2.node.expandedIcon || ctx_r2.node.collapsedIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tree.getTemplateForNode(ctx_r2.node));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.tree.getTemplateForNode(ctx_r2.node));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.tree.virtualScroll && ctx_r2.node.children && ctx_r2.node.expanded);\n  }\n}\n\nfunction UITreeNode_ng_template_0_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 4);\n    i0.ɵɵlistener(\"drop\", function UITreeNode_ng_template_0_li_2_Template_li_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.onDropPoint($event, 1));\n    })(\"dragover\", function UITreeNode_ng_template_0_li_2_Template_li_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r35.onDropPointDragOver($event));\n    })(\"dragenter\", function UITreeNode_ng_template_0_li_2_Template_li_dragenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.onDropPointDragEnter($event, 1));\n    })(\"dragleave\", function UITreeNode_ng_template_0_li_2_Template_li_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r37 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r37.onDropPointDragLeave($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r3.draghoverNext));\n  }\n}\n\nconst _c9 = function (a0) {\n  return {\n    \"p-treenode-connector-line\": a0\n  };\n};\n\nfunction UITreeNode_ng_template_0_table_3_td_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 25)(1, \"table\", 26)(2, \"tbody\")(3, \"tr\");\n    i0.ɵɵelement(4, \"td\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"tr\");\n    i0.ɵɵelement(6, \"td\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c9, !ctx_r38.firstChild));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, !ctx_r38.lastChild));\n  }\n}\n\nconst _c10 = function (a0, a1) {\n  return {\n    \"pi-plus\": a0,\n    \"pi-minus\": a1\n  };\n};\n\nfunction UITreeNode_ng_template_0_table_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 28);\n    i0.ɵɵlistener(\"click\", function UITreeNode_ng_template_0_table_3_span_6_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r44.toggle($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r39 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c10, !ctx_r39.node.expanded, ctx_r39.node.expanded));\n    i0.ɵɵattribute(\"aria-label\", ctx_r39.tree.togglerAriaLabel);\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r40.getIcon());\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r41.node.label);\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_span_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, UITreeNode_ng_template_0_table_3_span_10_ng_container_1_Template, 1, 0, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r42.tree.getTemplateForNode(ctx_r42.node))(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r42.node));\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_td_11_p_treeNode_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 32);\n  }\n\n  if (rf & 2) {\n    const childNode_r48 = ctx.$implicit;\n    const firstChild_r49 = ctx.first;\n    const lastChild_r50 = ctx.last;\n    i0.ɵɵproperty(\"node\", childNode_r48)(\"firstChild\", firstChild_r49)(\"lastChild\", lastChild_r50);\n  }\n}\n\nfunction UITreeNode_ng_template_0_table_3_td_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 29)(1, \"div\", 30);\n    i0.ɵɵtemplate(2, UITreeNode_ng_template_0_table_3_td_11_p_treeNode_2_Template, 1, 3, \"p-treeNode\", 31);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r43 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r43.node.expanded ? \"table-cell\" : \"none\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r43.node.children)(\"ngForTrackBy\", ctx_r43.tree.trackBy);\n  }\n}\n\nconst _c11 = function (a0) {\n  return {\n    \"p-treenode-collapsed\": a0\n  };\n};\n\nconst _c12 = function (a0, a1) {\n  return {\n    \"p-treenode-selectable\": a0,\n    \"p-highlight\": a1\n  };\n};\n\nfunction UITreeNode_ng_template_0_table_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"table\")(1, \"tbody\")(2, \"tr\");\n    i0.ɵɵtemplate(3, UITreeNode_ng_template_0_table_3_td_3_Template, 7, 6, \"td\", 20);\n    i0.ɵɵelementStart(4, \"td\", 21)(5, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function UITreeNode_ng_template_0_table_3_Template_div_click_5_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r51.onNodeClick($event));\n    })(\"contextmenu\", function UITreeNode_ng_template_0_table_3_Template_div_contextmenu_5_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r53 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r53.onNodeRightClick($event));\n    })(\"touchend\", function UITreeNode_ng_template_0_table_3_Template_div_touchend_5_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r54 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r54.onNodeTouchEnd());\n    })(\"keydown\", function UITreeNode_ng_template_0_table_3_Template_div_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r55 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r55.onNodeKeydown($event));\n    });\n    i0.ɵɵtemplate(6, UITreeNode_ng_template_0_table_3_span_6_Template, 1, 5, \"span\", 23);\n    i0.ɵɵtemplate(7, UITreeNode_ng_template_0_table_3_span_7_Template, 1, 2, \"span\", 3);\n    i0.ɵɵelementStart(8, \"span\", 10);\n    i0.ɵɵtemplate(9, UITreeNode_ng_template_0_table_3_span_9_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(10, UITreeNode_ng_template_0_table_3_span_10_Template, 2, 4, \"span\", 11);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, UITreeNode_ng_template_0_table_3_td_11_Template, 3, 4, \"td\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r4.node.styleClass);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.root);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c11, !ctx_r4.node.expanded));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c12, ctx_r4.tree.selectionMode, ctx_r4.isSelected()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isLeaf());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.node.icon || ctx_r4.node.expandedIcon || ctx_r4.node.collapsedIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.tree.getTemplateForNode(ctx_r4.node));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.tree.getTemplateForNode(ctx_r4.node));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.node.children && ctx_r4.node.expanded);\n  }\n}\n\nfunction UITreeNode_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UITreeNode_ng_template_0_li_0_Template, 1, 3, \"li\", 1);\n    i0.ɵɵtemplate(1, UITreeNode_ng_template_0_li_1_Template, 10, 30, \"li\", 2);\n    i0.ɵɵtemplate(2, UITreeNode_ng_template_0_li_2_Template, 1, 3, \"li\", 1);\n    i0.ɵɵtemplate(3, UITreeNode_ng_template_0_table_3_Template, 12, 15, \"table\", 3);\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.tree.droppableNodes);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.tree.horizontal);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.tree.droppableNodes && ctx_r0.lastChild);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.tree.horizontal);\n  }\n}\n\nconst _c13 = [\"filter\"];\nconst _c14 = [\"scroller\"];\nconst _c15 = [\"wrapper\"];\n\nfunction Tree_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"p-tree-loading-icon pi-spin \" + ctx_r2.loadingIcon);\n  }\n}\n\nfunction Tree_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Tree_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"input\", 11, 12);\n    i0.ɵɵlistener(\"keydown.enter\", function Tree_div_0_div_3_Template_input_keydown_enter_1_listener($event) {\n      return $event.preventDefault();\n    })(\"input\", function Tree_div_0_div_3_Template_input_input_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11._filter($event.target.value));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"placeholder\", ctx_r4.filterPlaceholder);\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_ng_template_2_ul_0_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 20);\n  }\n\n  if (rf & 2) {\n    const rowNode_r20 = ctx.$implicit;\n    const firstChild_r21 = ctx.first;\n    const lastChild_r22 = ctx.last;\n    const index_r23 = ctx.index;\n    const scrollerOptions_r17 = i0.ɵɵnextContext(2).options;\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"level\", rowNode_r20.level)(\"rowNode\", rowNode_r20)(\"node\", rowNode_r20.node)(\"firstChild\", firstChild_r21)(\"lastChild\", lastChild_r22)(\"index\", ctx_r19.getIndex(scrollerOptions_r17, index_r23))(\"itemSize\", scrollerOptions_r17.itemSize)(\"indentation\", ctx_r19.indentation);\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_ng_template_2_ul_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 18);\n    i0.ɵɵtemplate(1, Tree_div_0_p_scroller_4_ng_template_2_ul_0_p_treeNode_1_Template, 1, 8, \"p-treeNode\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r25 = i0.ɵɵnextContext();\n    const scrollerOptions_r17 = ctx_r25.options;\n    const items_r16 = ctx_r25.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleMap(scrollerOptions_r17.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r17.contentStyleClass);\n    i0.ɵɵattribute(\"aria-label\", ctx_r18.ariaLabel)(\"aria-labelledby\", ctx_r18.ariaLabelledBy);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", items_r16)(\"ngForTrackBy\", ctx_r18.trackBy);\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_div_0_p_scroller_4_ng_template_2_ul_0_Template, 2, 7, \"ul\", 17);\n  }\n\n  if (rf & 2) {\n    const items_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", items_r16);\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c16 = function (a0) {\n  return {\n    options: a0\n  };\n};\n\nfunction Tree_div_0_p_scroller_4_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Tree_div_0_p_scroller_4_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 22);\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r27 = ctx.options;\n    const ctx_r26 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r26.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c16, scrollerOptions_r27));\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Tree_div_0_p_scroller_4_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 21);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nfunction Tree_div_0_p_scroller_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-scroller\", 14, 15);\n    i0.ɵɵlistener(\"onScroll\", function Tree_div_0_p_scroller_4_Template_p_scroller_onScroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.onScroll.emit($event));\n    })(\"onScrollIndexChange\", function Tree_div_0_p_scroller_4_Template_p_scroller_onScrollIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.onScrollIndexChange.emit($event));\n    })(\"onLazyLoad\", function Tree_div_0_p_scroller_4_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, Tree_div_0_p_scroller_4_ng_template_2_Template, 1, 1, \"ng-template\", 16);\n    i0.ɵɵtemplate(3, Tree_div_0_p_scroller_4_ng_container_3_Template, 2, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(7, _c6, ctx_r5.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r5.serializedValue)(\"itemSize\", ctx_r5.virtualScrollItemSize || ctx_r5._virtualNodeHeight)(\"lazy\", ctx_r5.lazy)(\"options\", ctx_r5.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.loaderTemplate);\n  }\n}\n\nfunction Tree_div_0_ng_container_5_ul_3_p_treeNode_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-treeNode\", 28);\n  }\n\n  if (rf & 2) {\n    const node_r36 = ctx.$implicit;\n    const firstChild_r37 = ctx.first;\n    const lastChild_r38 = ctx.last;\n    const index_r39 = ctx.index;\n    i0.ɵɵproperty(\"node\", node_r36)(\"firstChild\", firstChild_r37)(\"lastChild\", lastChild_r38)(\"index\", index_r39)(\"level\", 0);\n  }\n}\n\nfunction Tree_div_0_ng_container_5_ul_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 26);\n    i0.ɵɵtemplate(1, Tree_div_0_ng_container_5_ul_3_p_treeNode_1_Template, 1, 5, \"p-treeNode\", 27);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"aria-label\", ctx_r34.ariaLabel)(\"aria-labelledby\", ctx_r34.ariaLabelledBy);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r34.getRootNode())(\"ngForTrackBy\", ctx_r34.trackBy);\n  }\n}\n\nfunction Tree_div_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23, 24);\n    i0.ɵɵtemplate(3, Tree_div_0_ng_container_5_ul_3_Template, 2, 4, \"ul\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵstyleProp(\"max-height\", ctx_r6.scrollHeight);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.getRootNode());\n  }\n}\n\nfunction Tree_div_0_div_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r40 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r40.emptyMessageLabel, \" \");\n  }\n}\n\nfunction Tree_div_0_div_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 31);\n  }\n}\n\nfunction Tree_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, Tree_div_0_div_6_ng_container_1_Template, 2, 1, \"ng-container\", 30);\n    i0.ɵɵtemplate(2, Tree_div_0_div_6_ng_container_2_Template, 2, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.emptyMessageTemplate)(\"ngIfElse\", ctx_r7.emptyFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.emptyMessageTemplate);\n  }\n}\n\nfunction Tree_div_0_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c17 = function (a1, a2, a3, a4) {\n  return {\n    \"p-tree p-component\": true,\n    \"p-tree-selectable\": a1,\n    \"p-treenode-dragover\": a2,\n    \"p-tree-loading\": a3,\n    \"p-tree-flex-scrollable\": a4\n  };\n};\n\nfunction Tree_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r44 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵlistener(\"drop\", function Tree_div_0_Template_div_drop_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onDrop($event));\n    })(\"dragover\", function Tree_div_0_Template_div_dragover_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r45 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r45.onDragOver($event));\n    })(\"dragenter\", function Tree_div_0_Template_div_dragenter_0_listener() {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onDragEnter());\n    })(\"dragleave\", function Tree_div_0_Template_div_dragleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r44);\n      const ctx_r47 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r47.onDragLeave($event));\n    });\n    i0.ɵɵtemplate(1, Tree_div_0_div_1_Template, 2, 2, \"div\", 3);\n    i0.ɵɵtemplate(2, Tree_div_0_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵtemplate(3, Tree_div_0_div_3_Template, 4, 1, \"div\", 5);\n    i0.ɵɵtemplate(4, Tree_div_0_p_scroller_4_Template, 4, 9, \"p-scroller\", 6);\n    i0.ɵɵtemplate(5, Tree_div_0_ng_container_5_Template, 4, 3, \"ng-container\", 7);\n    i0.ɵɵtemplate(6, Tree_div_0_div_6_Template, 3, 3, \"div\", 8);\n    i0.ɵɵtemplate(7, Tree_div_0_ng_container_7_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(11, _c17, ctx_r0.selectionMode, ctx_r0.dragHover, ctx_r0.loading, ctx_r0.scrollHeight === \"flex\"))(\"ngStyle\", ctx_r0.style);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.virtualScroll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.virtualScroll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loading && (ctx_r0.getRootNode() == null || ctx_r0.getRootNode().length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.footerTemplate);\n  }\n}\n\nfunction Tree_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Tree_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r49 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(\"p-tree-loading-icon pi-spin \" + ctx_r49.loadingIcon);\n  }\n}\n\nfunction Tree_div_1_table_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"table\");\n    i0.ɵɵelement(1, \"p-treeNode\", 35);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r50 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"node\", ctx_r50.value[0])(\"root\", true);\n  }\n}\n\nfunction Tree_div_1_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r53 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r53.emptyMessageLabel, \" \");\n  }\n}\n\nfunction Tree_div_1_div_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 31);\n  }\n}\n\nfunction Tree_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, Tree_div_1_div_4_ng_container_1_Template, 2, 1, \"ng-container\", 30);\n    i0.ɵɵtemplate(2, Tree_div_1_div_4_ng_container_2_Template, 2, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r51.emptyMessageTemplate)(\"ngIfElse\", ctx_r51.emptyFilter);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r51.emptyMessageTemplate);\n  }\n}\n\nfunction Tree_div_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c18 = function (a1) {\n  return {\n    \"p-tree p-tree-horizontal p-component\": true,\n    \"p-tree-selectable\": a1\n  };\n};\n\nfunction Tree_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, Tree_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵtemplate(2, Tree_div_1_div_2_Template, 2, 2, \"div\", 33);\n    i0.ɵɵtemplate(3, Tree_div_1_table_3_Template, 2, 2, \"table\", 7);\n    i0.ɵɵtemplate(4, Tree_div_1_div_4_Template, 3, 3, \"div\", 8);\n    i0.ɵɵtemplate(5, Tree_div_1_ng_container_5_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c18, ctx_r1.selectionMode))(\"ngStyle\", ctx_r1.style);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.value && ctx_r1.value[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading && (ctx_r1.getRootNode() == null || ctx_r1.getRootNode().length === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\n\nclass UITreeNode {\n  constructor(tree) {\n    this.tree = tree;\n  }\n\n  ngOnInit() {\n    this.node.parent = this.parentNode;\n\n    if (this.parentNode) {\n      this.tree.syncNodeOption(this.node, this.tree.value, 'parent', this.tree.getNodeWithKey(this.parentNode.key, this.tree.value));\n    }\n  }\n\n  getIcon() {\n    let icon;\n    if (this.node.icon) icon = this.node.icon;else icon = this.node.expanded && this.node.children && this.node.children.length ? this.node.expandedIcon : this.node.collapsedIcon;\n    return UITreeNode.ICON_CLASS + ' ' + icon;\n  }\n\n  isLeaf() {\n    return this.tree.isNodeLeaf(this.node);\n  }\n\n  toggle(event) {\n    if (this.node.expanded) this.collapse(event);else this.expand(event);\n  }\n\n  expand(event) {\n    this.node.expanded = true;\n\n    if (this.tree.virtualScroll) {\n      this.tree.updateSerializedValue();\n    }\n\n    this.tree.onNodeExpand.emit({\n      originalEvent: event,\n      node: this.node\n    });\n  }\n\n  collapse(event) {\n    this.node.expanded = false;\n\n    if (this.tree.virtualScroll) {\n      this.tree.updateSerializedValue();\n    }\n\n    this.tree.onNodeCollapse.emit({\n      originalEvent: event,\n      node: this.node\n    });\n  }\n\n  onNodeClick(event) {\n    this.tree.onNodeClick(event, this.node);\n  }\n\n  onNodeKeydown(event) {\n    if (event.which === 13) {\n      this.tree.onNodeClick(event, this.node);\n    }\n  }\n\n  onNodeTouchEnd() {\n    this.tree.onNodeTouchEnd();\n  }\n\n  onNodeRightClick(event) {\n    this.tree.onNodeRightClick(event, this.node);\n  }\n\n  isSelected() {\n    return this.tree.isSelected(this.node);\n  }\n\n  onDropPoint(event, position) {\n    event.preventDefault();\n    let dragNode = this.tree.dragNode;\n    let dragNodeIndex = this.tree.dragNodeIndex;\n    let dragNodeScope = this.tree.dragNodeScope;\n    let isValidDropPointIndex = this.tree.dragNodeTree === this.tree ? position === 1 || dragNodeIndex !== this.index - 1 : true;\n\n    if (this.tree.allowDrop(dragNode, this.node, dragNodeScope) && isValidDropPointIndex) {\n      let dropParams = Object.assign({}, this.createDropPointEventMetadata(position));\n\n      if (this.tree.validateDrop) {\n        this.tree.onNodeDrop.emit({\n          originalEvent: event,\n          dragNode: dragNode,\n          dropNode: this.node,\n          index: this.index,\n          accept: () => {\n            this.processPointDrop(dropParams);\n          }\n        });\n      } else {\n        this.processPointDrop(dropParams);\n        this.tree.onNodeDrop.emit({\n          originalEvent: event,\n          dragNode: dragNode,\n          dropNode: this.node,\n          index: this.index\n        });\n      }\n    }\n\n    this.draghoverPrev = false;\n    this.draghoverNext = false;\n  }\n\n  processPointDrop(event) {\n    let newNodeList = event.dropNode.parent ? event.dropNode.parent.children : this.tree.value;\n    event.dragNodeSubNodes.splice(event.dragNodeIndex, 1);\n    let dropIndex = this.index;\n\n    if (event.position < 0) {\n      dropIndex = event.dragNodeSubNodes === newNodeList ? event.dragNodeIndex > event.index ? event.index : event.index - 1 : event.index;\n      newNodeList.splice(dropIndex, 0, event.dragNode);\n    } else {\n      dropIndex = newNodeList.length;\n      newNodeList.push(event.dragNode);\n    }\n\n    this.tree.dragDropService.stopDrag({\n      node: event.dragNode,\n      subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n      index: event.dragNodeIndex\n    });\n  }\n\n  createDropPointEventMetadata(position) {\n    return {\n      dragNode: this.tree.dragNode,\n      dragNodeIndex: this.tree.dragNodeIndex,\n      dragNodeSubNodes: this.tree.dragNodeSubNodes,\n      dropNode: this.node,\n      index: this.index,\n      position: position\n    };\n  }\n\n  onDropPointDragOver(event) {\n    event.dataTransfer.dropEffect = 'move';\n    event.preventDefault();\n  }\n\n  onDropPointDragEnter(event, position) {\n    if (this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n      if (position < 0) this.draghoverPrev = true;else this.draghoverNext = true;\n    }\n  }\n\n  onDropPointDragLeave(event) {\n    this.draghoverPrev = false;\n    this.draghoverNext = false;\n  }\n\n  onDragStart(event) {\n    if (this.tree.draggableNodes && this.node.draggable !== false) {\n      event.dataTransfer.setData(\"text\", \"data\");\n      this.tree.dragDropService.startDrag({\n        tree: this,\n        node: this.node,\n        subNodes: this.node.parent ? this.node.parent.children : this.tree.value,\n        index: this.index,\n        scope: this.tree.draggableScope\n      });\n    } else {\n      event.preventDefault();\n    }\n  }\n\n  onDragStop(event) {\n    this.tree.dragDropService.stopDrag({\n      node: this.node,\n      subNodes: this.node.parent ? this.node.parent.children : this.tree.value,\n      index: this.index\n    });\n  }\n\n  onDropNodeDragOver(event) {\n    event.dataTransfer.dropEffect = 'move';\n\n    if (this.tree.droppableNodes) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n\n  onDropNode(event) {\n    if (this.tree.droppableNodes && this.node.droppable !== false) {\n      let dragNode = this.tree.dragNode;\n\n      if (this.tree.allowDrop(dragNode, this.node, this.tree.dragNodeScope)) {\n        let dropParams = Object.assign({}, this.createDropNodeEventMetadata());\n\n        if (this.tree.validateDrop) {\n          this.tree.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: this.node,\n            index: this.index,\n            accept: () => {\n              this.processNodeDrop(dropParams);\n            }\n          });\n        } else {\n          this.processNodeDrop(dropParams);\n          this.tree.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: this.node,\n            index: this.index\n          });\n        }\n      }\n    }\n\n    event.preventDefault();\n    event.stopPropagation();\n    this.draghoverNode = false;\n  }\n\n  createDropNodeEventMetadata() {\n    return {\n      dragNode: this.tree.dragNode,\n      dragNodeIndex: this.tree.dragNodeIndex,\n      dragNodeSubNodes: this.tree.dragNodeSubNodes,\n      dropNode: this.node\n    };\n  }\n\n  processNodeDrop(event) {\n    let dragNodeIndex = event.dragNodeIndex;\n    event.dragNodeSubNodes.splice(dragNodeIndex, 1);\n    if (event.dropNode.children) event.dropNode.children.push(event.dragNode);else event.dropNode.children = [event.dragNode];\n    this.tree.dragDropService.stopDrag({\n      node: event.dragNode,\n      subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n      index: dragNodeIndex\n    });\n  }\n\n  onDropNodeDragEnter(event) {\n    if (this.tree.droppableNodes && this.node.droppable !== false && this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n      this.draghoverNode = true;\n    }\n  }\n\n  onDropNodeDragLeave(event) {\n    if (this.tree.droppableNodes) {\n      let rect = event.currentTarget.getBoundingClientRect();\n\n      if (event.x > rect.left + rect.width || event.x < rect.left || event.y >= Math.floor(rect.top + rect.height) || event.y < rect.top) {\n        this.draghoverNode = false;\n      }\n    }\n  }\n\n  onKeyDown(event) {\n    const nodeElement = event.target.parentElement.parentElement;\n\n    if (nodeElement.nodeName !== 'P-TREENODE' || this.tree.contextMenu && this.tree.contextMenu.containerViewChild.nativeElement.style.display === 'block') {\n      return;\n    }\n\n    switch (event.which) {\n      //down arrow\n      case 40:\n        const listElement = this.tree.droppableNodes ? nodeElement.children[1].children[1] : nodeElement.children[0].children[1];\n\n        if (listElement && listElement.children.length > 0) {\n          this.focusNode(listElement.children[0]);\n        } else {\n          const nextNodeElement = nodeElement.nextElementSibling;\n\n          if (nextNodeElement) {\n            this.focusNode(nextNodeElement);\n          } else {\n            let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement);\n\n            if (nextSiblingAncestor) {\n              this.focusNode(nextSiblingAncestor);\n            }\n          }\n        }\n\n        event.preventDefault();\n        break;\n      //up arrow\n\n      case 38:\n        if (nodeElement.previousElementSibling) {\n          this.focusNode(this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n        } else {\n          let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n          if (parentNodeElement) {\n            this.focusNode(parentNodeElement);\n          }\n        }\n\n        event.preventDefault();\n        break;\n      //right arrow\n\n      case 39:\n        if (!this.node.expanded && !this.tree.isNodeLeaf(this.node)) {\n          this.expand(event);\n        }\n\n        event.preventDefault();\n        break;\n      //left arrow\n\n      case 37:\n        if (this.node.expanded) {\n          this.collapse(event);\n        } else {\n          let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n          if (parentNodeElement) {\n            this.focusNode(parentNodeElement);\n          }\n        }\n\n        event.preventDefault();\n        break;\n      //enter\n\n      case 13:\n        this.tree.onNodeClick(event, this.node);\n        event.preventDefault();\n        break;\n\n      default:\n        //no op\n        break;\n    }\n  }\n\n  findNextSiblingOfAncestor(nodeElement) {\n    let parentNodeElement = this.getParentNodeElement(nodeElement);\n\n    if (parentNodeElement) {\n      if (parentNodeElement.nextElementSibling) return parentNodeElement.nextElementSibling;else return this.findNextSiblingOfAncestor(parentNodeElement);\n    } else {\n      return null;\n    }\n  }\n\n  findLastVisibleDescendant(nodeElement) {\n    const listElement = Array.from(nodeElement.children).find(el => DomHandler.hasClass(el, 'p-treenode'));\n    const childrenListElement = listElement.children[1];\n\n    if (childrenListElement && childrenListElement.children.length > 0) {\n      const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n      return this.findLastVisibleDescendant(lastChildElement);\n    } else {\n      return nodeElement;\n    }\n  }\n\n  getParentNodeElement(nodeElement) {\n    const parentNodeElement = nodeElement.parentElement.parentElement.parentElement;\n    return parentNodeElement.tagName === 'P-TREENODE' ? parentNodeElement : null;\n  }\n\n  focusNode(element) {\n    if (this.tree.droppableNodes) element.children[1].children[0].focus();else element.children[0].children[0].focus();\n  }\n\n}\n\nUITreeNode.ICON_CLASS = 'p-treenode-icon ';\n\nUITreeNode.ɵfac = function UITreeNode_Factory(t) {\n  return new (t || UITreeNode)(i0.ɵɵdirectiveInject(forwardRef(() => Tree)));\n};\n\nUITreeNode.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: UITreeNode,\n  selectors: [[\"p-treeNode\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    rowNode: \"rowNode\",\n    node: \"node\",\n    parentNode: \"parentNode\",\n    root: \"root\",\n    index: \"index\",\n    firstChild: \"firstChild\",\n    lastChild: \"lastChild\",\n    level: \"level\",\n    indentation: \"indentation\",\n    itemSize: \"itemSize\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngIf\"], [\"class\", \"p-treenode-droppoint\", 3, \"ngClass\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [1, \"p-treenode-droppoint\", 3, \"ngClass\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\"], [3, \"ngClass\", \"ngStyle\"], [\"role\", \"treeitem\", 1, \"p-treenode-content\", 3, \"draggable\", \"ngClass\", \"click\", \"contextmenu\", \"touchend\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", \"dragstart\", \"dragend\", \"keydown\"], [\"type\", \"button\", \"pRipple\", \"\", \"tabindex\", \"-1\", 1, \"p-tree-toggler\", \"p-link\", 3, \"click\"], [1, \"p-tree-toggler-icon\", \"pi\", \"pi-fw\", 3, \"ngClass\"], [\"class\", \"p-checkbox p-component\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-treenode-label\"], [4, \"ngIf\"], [\"class\", \"p-treenode-children\", \"style\", \"display: none;\", \"role\", \"group\", 3, \"display\", 4, \"ngIf\"], [1, \"p-checkbox\", \"p-component\", 3, \"ngClass\"], [1, \"p-checkbox-box\", 3, \"ngClass\"], [1, \"p-checkbox-icon\", \"pi\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"role\", \"group\", 1, \"p-treenode-children\", 2, \"display\", \"none\"], [3, \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"level\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"node\", \"parentNode\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"level\"], [\"class\", \"p-treenode-connector\", 4, \"ngIf\"], [1, \"p-treenode\", 3, \"ngClass\"], [\"tabindex\", \"0\", 1, \"p-treenode-content\", 3, \"ngClass\", \"click\", \"contextmenu\", \"touchend\", \"keydown\"], [\"class\", \"p-tree-toggler pi pi-fw\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-treenode-children-container\", 3, \"display\", 4, \"ngIf\"], [1, \"p-treenode-connector\"], [1, \"p-treenode-connector-table\"], [3, \"ngClass\"], [1, \"p-tree-toggler\", \"pi\", \"pi-fw\", 3, \"ngClass\", \"click\"], [1, \"p-treenode-children-container\"], [1, \"p-treenode-children\"], [3, \"node\", \"firstChild\", \"lastChild\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"node\", \"firstChild\", \"lastChild\"]],\n  template: function UITreeNode_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, UITreeNode_ng_template_0_Template, 4, 4, \"ng-template\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.node);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple, UITreeNode],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UITreeNode, [{\n    type: Component,\n    args: [{\n      selector: 'p-treeNode',\n      template: `\n        <ng-template [ngIf]=\"node\">\n            <li *ngIf=\"tree.droppableNodes\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverPrev}\"\n            (drop)=\"onDropPoint($event,-1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,-1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <li *ngIf=\"!tree.horizontal\" [ngClass]=\"['p-treenode',node.styleClass||'', isLeaf() ? 'p-treenode-leaf': '']\" [ngStyle]=\"{'height': itemSize + 'px'}\">\n                <div class=\"p-treenode-content\" [style.paddingLeft]=\"(level * indentation)  + 'rem'\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\" (touchend)=\"onNodeTouchEnd()\"\n                    (drop)=\"onDropNode($event)\" (dragover)=\"onDropNodeDragOver($event)\" (dragenter)=\"onDropNodeDragEnter($event)\" (dragleave)=\"onDropNodeDragLeave($event)\"\n                    [draggable]=\"tree.draggableNodes\" (dragstart)=\"onDragStart($event)\" (dragend)=\"onDragStop($event)\" [attr.tabindex]=\"0\"\n                    [ngClass]=\"{'p-treenode-selectable':tree.selectionMode && node.selectable !== false,'p-treenode-dragover':draghoverNode, 'p-highlight':isSelected()}\" role=\"treeitem\"\n                    (keydown)=\"onKeyDown($event)\" [attr.aria-posinset]=\"this.index + 1\" [attr.aria-expanded]=\"this.node.expanded\" [attr.aria-selected]=\"isSelected()\" [attr.aria-label]=\"node.label\">\n                    <button type=\"button\" [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler p-link\" (click)=\"toggle($event)\" pRipple tabindex=\"-1\">\n                        <span class=\"p-tree-toggler-icon pi pi-fw\" [ngClass]=\"{'pi-chevron-right':!node.expanded,'pi-chevron-down':node.expanded}\"></span>\n                    </button>\n                    <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-disabled': node.selectable === false}\" *ngIf=\"tree.selectionMode == 'checkbox'\" [attr.aria-checked]=\"isSelected()\">\n                        <div class=\"p-checkbox-box\" [ngClass]=\"{'p-highlight': isSelected(), 'p-indeterminate': node.partialSelected}\">\n                            <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':isSelected(),'pi-minus':node.partialSelected}\"></span>\n                        </div>\n                    </div>\n                    <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                    <span class=\"p-treenode-label\">\n                            <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                            <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                            </span>\n                    </span>\n                </div>\n                <ul class=\"p-treenode-children\" style=\"display: none;\" *ngIf=\"!tree.virtualScroll && node.children && node.expanded\" [style.display]=\"node.expanded ? 'block' : 'none'\" role=\"group\">\n                    <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; let index=index; trackBy: tree.trackBy\" [node]=\"childNode\" [parentNode]=\"node\"\n                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [itemSize]=\"itemSize\" [level]=\"level + 1\"></p-treeNode>\n                </ul>\n            </li>\n            <li *ngIf=\"tree.droppableNodes&&lastChild\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverNext}\"\n            (drop)=\"onDropPoint($event,1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <table *ngIf=\"tree.horizontal\" [class]=\"node.styleClass\">\n                <tbody>\n                    <tr>\n                        <td class=\"p-treenode-connector\" *ngIf=\"!root\">\n                            <table class=\"p-treenode-connector-table\">\n                                <tbody>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!firstChild}\"></td>\n                                    </tr>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!lastChild}\"></td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </td>\n                        <td class=\"p-treenode\" [ngClass]=\"{'p-treenode-collapsed':!node.expanded}\">\n                            <div class=\"p-treenode-content\" tabindex=\"0\" [ngClass]=\"{'p-treenode-selectable':tree.selectionMode,'p-highlight':isSelected()}\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\"\n                                (touchend)=\"onNodeTouchEnd()\" (keydown)=\"onNodeKeydown($event)\">\n                                <span [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler pi pi-fw\" [ngClass]=\"{'pi-plus':!node.expanded,'pi-minus':node.expanded}\" *ngIf=\"!isLeaf()\" (click)=\"toggle($event)\"></span>\n                                <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                                <span class=\"p-treenode-label\">\n                                    <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                                    <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                        <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                                    </span>\n                                </span>\n                            </div>\n                        </td>\n                        <td class=\"p-treenode-children-container\" *ngIf=\"node.children && node.expanded\" [style.display]=\"node.expanded ? 'table-cell' : 'none'\">\n                            <div class=\"p-treenode-children\">\n                                <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; trackBy: tree.trackBy\" [node]=\"childNode\"\n                                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\"></p-treeNode>\n                            </div>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </ng-template>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => Tree)]\n      }]\n    }];\n  }, {\n    rowNode: [{\n      type: Input\n    }],\n    node: [{\n      type: Input\n    }],\n    parentNode: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    firstChild: [{\n      type: Input\n    }],\n    lastChild: [{\n      type: Input\n    }],\n    level: [{\n      type: Input\n    }],\n    indentation: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }]\n  });\n})();\n\nclass Tree {\n  constructor(el, dragDropService, config) {\n    this.el = el;\n    this.dragDropService = dragDropService;\n    this.config = config;\n    this.layout = 'vertical';\n    this.metaKeySelection = true;\n    this.propagateSelectionUp = true;\n    this.propagateSelectionDown = true;\n    this.loadingIcon = 'pi pi-spinner';\n    this.emptyMessage = '';\n    this.filterBy = 'label';\n    this.filterMode = 'lenient';\n    this.lazy = false;\n    this.indentation = 1.5;\n\n    this.trackBy = (index, item) => item;\n\n    this.selectionChange = new EventEmitter();\n    this.onNodeSelect = new EventEmitter();\n    this.onNodeUnselect = new EventEmitter();\n    this.onNodeExpand = new EventEmitter();\n    this.onNodeCollapse = new EventEmitter();\n    this.onNodeContextMenuSelect = new EventEmitter();\n    this.onNodeDrop = new EventEmitter();\n    this.onLazyLoad = new EventEmitter();\n    this.onScroll = new EventEmitter();\n    this.onScrollIndexChange = new EventEmitter();\n    this.onFilter = new EventEmitter();\n  }\n\n  get virtualNodeHeight() {\n    return this._virtualNodeHeight;\n  }\n\n  set virtualNodeHeight(val) {\n    this._virtualNodeHeight = val;\n    console.warn(\"The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.\");\n  }\n\n  ngOnInit() {\n    if (this.droppableNodes) {\n      this.dragStartSubscription = this.dragDropService.dragStart$.subscribe(event => {\n        this.dragNodeTree = event.tree;\n        this.dragNode = event.node;\n        this.dragNodeSubNodes = event.subNodes;\n        this.dragNodeIndex = event.index;\n        this.dragNodeScope = event.scope;\n      });\n      this.dragStopSubscription = this.dragDropService.dragStop$.subscribe(event => {\n        this.dragNodeTree = null;\n        this.dragNode = null;\n        this.dragNodeSubNodes = null;\n        this.dragNodeIndex = null;\n        this.dragNodeScope = null;\n        this.dragHover = false;\n      });\n    }\n  }\n\n  ngOnChanges(simpleChange) {\n    if (simpleChange.value) {\n      this.updateSerializedValue();\n    }\n  }\n\n  get horizontal() {\n    return this.layout == 'horizontal';\n  }\n\n  get emptyMessageLabel() {\n    return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n  }\n\n  ngAfterContentInit() {\n    if (this.templates.length) {\n      this.templateMap = {};\n    }\n\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'empty':\n          this.emptyMessageTemplate = item.template;\n          break;\n\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n\n        default:\n          this.templateMap[item.name] = item.template;\n          break;\n      }\n    });\n  }\n\n  updateSerializedValue() {\n    this.serializedValue = [];\n    this.serializeNodes(null, this.getRootNode(), 0, true);\n  }\n\n  serializeNodes(parent, nodes, level, visible) {\n    if (nodes && nodes.length) {\n      for (let node of nodes) {\n        node.parent = parent;\n        const rowNode = {\n          node: node,\n          parent: parent,\n          level: level,\n          visible: visible && (parent ? parent.expanded : true)\n        };\n        this.serializedValue.push(rowNode);\n\n        if (rowNode.visible && node.expanded) {\n          this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n        }\n      }\n    }\n  }\n\n  onNodeClick(event, node) {\n    let eventTarget = event.target;\n\n    if (DomHandler.hasClass(eventTarget, 'p-tree-toggler') || DomHandler.hasClass(eventTarget, 'p-tree-toggler-icon')) {\n      return;\n    } else if (this.selectionMode) {\n      if (node.selectable === false) {\n        return;\n      }\n\n      if (this.hasFilteredNodes()) {\n        node = this.getNodeWithKey(node.key, this.value);\n\n        if (!node) {\n          return;\n        }\n      }\n\n      let index = this.findIndexInSelection(node);\n      let selected = index >= 0;\n\n      if (this.isCheckboxSelectionMode()) {\n        if (selected) {\n          if (this.propagateSelectionDown) this.propagateDown(node, false);else this.selection = this.selection.filter((val, i) => i != index);\n\n          if (this.propagateSelectionUp && node.parent) {\n            this.propagateUp(node.parent, false);\n          }\n\n          this.selectionChange.emit(this.selection);\n          this.onNodeUnselect.emit({\n            originalEvent: event,\n            node: node\n          });\n        } else {\n          if (this.propagateSelectionDown) this.propagateDown(node, true);else this.selection = [...(this.selection || []), node];\n\n          if (this.propagateSelectionUp && node.parent) {\n            this.propagateUp(node.parent, true);\n          }\n\n          this.selectionChange.emit(this.selection);\n          this.onNodeSelect.emit({\n            originalEvent: event,\n            node: node\n          });\n        }\n      } else {\n        let metaSelection = this.nodeTouched ? false : this.metaKeySelection;\n\n        if (metaSelection) {\n          let metaKey = event.metaKey || event.ctrlKey;\n\n          if (selected && metaKey) {\n            if (this.isSingleSelectionMode()) {\n              this.selectionChange.emit(null);\n            } else {\n              this.selection = this.selection.filter((val, i) => i != index);\n              this.selectionChange.emit(this.selection);\n            }\n\n            this.onNodeUnselect.emit({\n              originalEvent: event,\n              node: node\n            });\n          } else {\n            if (this.isSingleSelectionMode()) {\n              this.selectionChange.emit(node);\n            } else if (this.isMultipleSelectionMode()) {\n              this.selection = !metaKey ? [] : this.selection || [];\n              this.selection = [...this.selection, node];\n              this.selectionChange.emit(this.selection);\n            }\n\n            this.onNodeSelect.emit({\n              originalEvent: event,\n              node: node\n            });\n          }\n        } else {\n          if (this.isSingleSelectionMode()) {\n            if (selected) {\n              this.selection = null;\n              this.onNodeUnselect.emit({\n                originalEvent: event,\n                node: node\n              });\n            } else {\n              this.selection = node;\n              this.onNodeSelect.emit({\n                originalEvent: event,\n                node: node\n              });\n            }\n          } else {\n            if (selected) {\n              this.selection = this.selection.filter((val, i) => i != index);\n              this.onNodeUnselect.emit({\n                originalEvent: event,\n                node: node\n              });\n            } else {\n              this.selection = [...(this.selection || []), node];\n              this.onNodeSelect.emit({\n                originalEvent: event,\n                node: node\n              });\n            }\n          }\n\n          this.selectionChange.emit(this.selection);\n        }\n      }\n    }\n\n    this.nodeTouched = false;\n  }\n\n  onNodeTouchEnd() {\n    this.nodeTouched = true;\n  }\n\n  onNodeRightClick(event, node) {\n    if (this.contextMenu) {\n      let eventTarget = event.target;\n\n      if (eventTarget.className && eventTarget.className.indexOf('p-tree-toggler') === 0) {\n        return;\n      } else {\n        let index = this.findIndexInSelection(node);\n        let selected = index >= 0;\n\n        if (!selected) {\n          if (this.isSingleSelectionMode()) this.selectionChange.emit(node);else this.selectionChange.emit([node]);\n        }\n\n        this.contextMenu.show(event);\n        this.onNodeContextMenuSelect.emit({\n          originalEvent: event,\n          node: node\n        });\n      }\n    }\n  }\n\n  findIndexInSelection(node) {\n    let index = -1;\n\n    if (this.selectionMode && this.selection) {\n      if (this.isSingleSelectionMode()) {\n        let areNodesEqual = this.selection.key && this.selection.key === node.key || this.selection == node;\n        index = areNodesEqual ? 0 : -1;\n      } else {\n        for (let i = 0; i < this.selection.length; i++) {\n          let selectedNode = this.selection[i];\n          let areNodesEqual = selectedNode.key && selectedNode.key === node.key || selectedNode == node;\n\n          if (areNodesEqual) {\n            index = i;\n            break;\n          }\n        }\n      }\n    }\n\n    return index;\n  }\n\n  syncNodeOption(node, parentNodes, option, value) {\n    // to synchronize the node option between the filtered nodes and the original nodes(this.value)\n    const _node = this.hasFilteredNodes() ? this.getNodeWithKey(node.key, parentNodes) : null;\n\n    if (_node) {\n      _node[option] = value || node[option];\n    }\n  }\n\n  hasFilteredNodes() {\n    return this.filter && this.filteredNodes && this.filteredNodes.length;\n  }\n\n  getNodeWithKey(key, nodes) {\n    for (let node of nodes) {\n      if (node.key === key) {\n        return node;\n      }\n\n      if (node.children) {\n        let matchedNode = this.getNodeWithKey(key, node.children);\n\n        if (matchedNode) {\n          return matchedNode;\n        }\n      }\n    }\n  }\n\n  propagateUp(node, select) {\n    if (node.children && node.children.length) {\n      let selectedCount = 0;\n      let childPartialSelected = false;\n\n      for (let child of node.children) {\n        if (this.isSelected(child)) {\n          selectedCount++;\n        } else if (child.partialSelected) {\n          childPartialSelected = true;\n        }\n      }\n\n      if (select && selectedCount == node.children.length) {\n        this.selection = [...(this.selection || []), node];\n        node.partialSelected = false;\n      } else {\n        if (!select) {\n          let index = this.findIndexInSelection(node);\n\n          if (index >= 0) {\n            this.selection = this.selection.filter((val, i) => i != index);\n          }\n        }\n\n        if (childPartialSelected || selectedCount > 0 && selectedCount != node.children.length) node.partialSelected = true;else node.partialSelected = false;\n      }\n\n      this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n    }\n\n    let parent = node.parent;\n\n    if (parent) {\n      this.propagateUp(parent, select);\n    }\n  }\n\n  propagateDown(node, select) {\n    let index = this.findIndexInSelection(node);\n\n    if (select && index == -1) {\n      this.selection = [...(this.selection || []), node];\n    } else if (!select && index > -1) {\n      this.selection = this.selection.filter((val, i) => i != index);\n    }\n\n    node.partialSelected = false;\n    this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n\n    if (node.children && node.children.length) {\n      for (let child of node.children) {\n        this.propagateDown(child, select);\n      }\n    }\n  }\n\n  isSelected(node) {\n    return this.findIndexInSelection(node) != -1;\n  }\n\n  isSingleSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'single';\n  }\n\n  isMultipleSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'multiple';\n  }\n\n  isCheckboxSelectionMode() {\n    return this.selectionMode && this.selectionMode == 'checkbox';\n  }\n\n  isNodeLeaf(node) {\n    return node.leaf == false ? false : !(node.children && node.children.length);\n  }\n\n  getRootNode() {\n    return this.filteredNodes ? this.filteredNodes : this.value;\n  }\n\n  getTemplateForNode(node) {\n    if (this.templateMap) return node.type ? this.templateMap[node.type] : this.templateMap['default'];else return null;\n  }\n\n  onDragOver(event) {\n    if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n      event.dataTransfer.dropEffect = 'move';\n      event.preventDefault();\n    }\n  }\n\n  onDrop(event) {\n    if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n      event.preventDefault();\n      let dragNode = this.dragNode;\n\n      if (this.allowDrop(dragNode, null, this.dragNodeScope)) {\n        let dragNodeIndex = this.dragNodeIndex;\n        this.value = this.value || [];\n\n        if (this.validateDrop) {\n          this.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: null,\n            index: dragNodeIndex,\n            accept: () => {\n              this.processTreeDrop(dragNode, dragNodeIndex);\n            }\n          });\n        } else {\n          this.onNodeDrop.emit({\n            originalEvent: event,\n            dragNode: dragNode,\n            dropNode: null,\n            index: dragNodeIndex\n          });\n          this.processTreeDrop(dragNode, dragNodeIndex);\n        }\n      }\n    }\n  }\n\n  processTreeDrop(dragNode, dragNodeIndex) {\n    this.dragNodeSubNodes.splice(dragNodeIndex, 1);\n    this.value.push(dragNode);\n    this.dragDropService.stopDrag({\n      node: dragNode\n    });\n  }\n\n  onDragEnter() {\n    if (this.droppableNodes && this.allowDrop(this.dragNode, null, this.dragNodeScope)) {\n      this.dragHover = true;\n    }\n  }\n\n  onDragLeave(event) {\n    if (this.droppableNodes) {\n      let rect = event.currentTarget.getBoundingClientRect();\n\n      if (event.x > rect.left + rect.width || event.x < rect.left || event.y > rect.top + rect.height || event.y < rect.top) {\n        this.dragHover = false;\n      }\n    }\n  }\n\n  allowDrop(dragNode, dropNode, dragNodeScope) {\n    if (!dragNode) {\n      //prevent random html elements to be dragged\n      return false;\n    } else if (this.isValidDragScope(dragNodeScope)) {\n      let allow = true;\n\n      if (dropNode) {\n        if (dragNode === dropNode) {\n          allow = false;\n        } else {\n          let parent = dropNode.parent;\n\n          while (parent != null) {\n            if (parent === dragNode) {\n              allow = false;\n              break;\n            }\n\n            parent = parent.parent;\n          }\n        }\n      }\n\n      return allow;\n    } else {\n      return false;\n    }\n  }\n\n  isValidDragScope(dragScope) {\n    let dropScope = this.droppableScope;\n\n    if (dropScope) {\n      if (typeof dropScope === 'string') {\n        if (typeof dragScope === 'string') return dropScope === dragScope;else if (dragScope instanceof Array) return dragScope.indexOf(dropScope) != -1;\n      } else if (dropScope instanceof Array) {\n        if (typeof dragScope === 'string') {\n          return dropScope.indexOf(dragScope) != -1;\n        } else if (dragScope instanceof Array) {\n          for (let s of dropScope) {\n            for (let ds of dragScope) {\n              if (s === ds) {\n                return true;\n              }\n            }\n          }\n        }\n      }\n\n      return false;\n    } else {\n      return true;\n    }\n  }\n\n  _filter(value) {\n    let filterValue = value;\n\n    if (filterValue === '') {\n      this.filteredNodes = null;\n    } else {\n      this.filteredNodes = [];\n      const searchFields = this.filterBy.split(',');\n      const filterText = ObjectUtils.removeAccents(filterValue).toLocaleLowerCase(this.filterLocale);\n      const isStrictMode = this.filterMode === 'strict';\n\n      for (let node of this.value) {\n        let copyNode = Object.assign({}, node);\n        let paramsWithoutNode = {\n          searchFields,\n          filterText,\n          isStrictMode\n        };\n\n        if (isStrictMode && (this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode)) || !isStrictMode && (this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode))) {\n          this.filteredNodes.push(copyNode);\n        }\n      }\n    }\n\n    this.updateSerializedValue();\n    this.onFilter.emit({\n      filter: filterValue,\n      filteredValue: this.filteredNodes\n    });\n  }\n\n  resetFilter() {\n    this.filteredNodes = null;\n\n    if (this.filterViewChild && this.filterViewChild.nativeElement) {\n      this.filterViewChild.nativeElement.value = '';\n    }\n  }\n\n  scrollToVirtualIndex(index) {\n    this.virtualScroll && this.scroller.scrollToIndex(index);\n  }\n\n  scrollTo(options) {\n    if (this.virtualScroll) {\n      this.scroller.scrollTo(options);\n    } else if (this.wrapperViewChild && this.wrapperViewChild.nativeElement) {\n      if (this.wrapperViewChild.nativeElement.scrollTo) {\n        this.wrapperViewChild.nativeElement.scrollTo(options);\n      } else {\n        this.wrapperViewChild.nativeElement.scrollLeft = options.left;\n        this.wrapperViewChild.nativeElement.scrollTop = options.top;\n      }\n    }\n  }\n\n  findFilteredNodes(node, paramsWithoutNode) {\n    if (node) {\n      let matched = false;\n\n      if (node.children) {\n        let childNodes = [...node.children];\n        node.children = [];\n\n        for (let childNode of childNodes) {\n          let copyChildNode = Object.assign({}, childNode);\n\n          if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n            matched = true;\n            node.children.push(copyChildNode);\n          }\n        }\n      }\n\n      if (matched) {\n        node.expanded = true;\n        return true;\n      }\n    }\n  }\n\n  isFilterMatched(node, {\n    searchFields,\n    filterText,\n    isStrictMode\n  }) {\n    let matched = false;\n\n    for (let field of searchFields) {\n      let fieldValue = ObjectUtils.removeAccents(String(ObjectUtils.resolveFieldData(node, field))).toLocaleLowerCase(this.filterLocale);\n\n      if (fieldValue.indexOf(filterText) > -1) {\n        matched = true;\n      }\n    }\n\n    if (!matched || isStrictMode && !this.isNodeLeaf(node)) {\n      matched = this.findFilteredNodes(node, {\n        searchFields,\n        filterText,\n        isStrictMode\n      }) || matched;\n    }\n\n    return matched;\n  }\n\n  getIndex(options, index) {\n    const getItemOptions = options['getItemOptions'];\n    return getItemOptions ? getItemOptions(index).index : index;\n  }\n\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n\n  ngOnDestroy() {\n    if (this.dragStartSubscription) {\n      this.dragStartSubscription.unsubscribe();\n    }\n\n    if (this.dragStopSubscription) {\n      this.dragStopSubscription.unsubscribe();\n    }\n  }\n\n}\n\nTree.ɵfac = function Tree_Factory(t) {\n  return new (t || Tree)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i3.TreeDragDropService, 8), i0.ɵɵdirectiveInject(i3.PrimeNGConfig));\n};\n\nTree.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Tree,\n  selectors: [[\"p-tree\"]],\n  contentQueries: function Tree_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Tree_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c13, 5);\n      i0.ɵɵviewQuery(_c14, 5);\n      i0.ɵɵviewQuery(_c15, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filterViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.wrapperViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    value: \"value\",\n    selectionMode: \"selectionMode\",\n    selection: \"selection\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    contextMenu: \"contextMenu\",\n    layout: \"layout\",\n    draggableScope: \"draggableScope\",\n    droppableScope: \"droppableScope\",\n    draggableNodes: \"draggableNodes\",\n    droppableNodes: \"droppableNodes\",\n    metaKeySelection: \"metaKeySelection\",\n    propagateSelectionUp: \"propagateSelectionUp\",\n    propagateSelectionDown: \"propagateSelectionDown\",\n    loading: \"loading\",\n    loadingIcon: \"loadingIcon\",\n    emptyMessage: \"emptyMessage\",\n    ariaLabel: \"ariaLabel\",\n    togglerAriaLabel: \"togglerAriaLabel\",\n    ariaLabelledBy: \"ariaLabelledBy\",\n    validateDrop: \"validateDrop\",\n    filter: \"filter\",\n    filterBy: \"filterBy\",\n    filterMode: \"filterMode\",\n    filterPlaceholder: \"filterPlaceholder\",\n    filteredNodes: \"filteredNodes\",\n    filterLocale: \"filterLocale\",\n    scrollHeight: \"scrollHeight\",\n    lazy: \"lazy\",\n    virtualScroll: \"virtualScroll\",\n    virtualScrollItemSize: \"virtualScrollItemSize\",\n    virtualScrollOptions: \"virtualScrollOptions\",\n    indentation: \"indentation\",\n    trackBy: \"trackBy\",\n    virtualNodeHeight: \"virtualNodeHeight\"\n  },\n  outputs: {\n    selectionChange: \"selectionChange\",\n    onNodeSelect: \"onNodeSelect\",\n    onNodeUnselect: \"onNodeUnselect\",\n    onNodeExpand: \"onNodeExpand\",\n    onNodeCollapse: \"onNodeCollapse\",\n    onNodeContextMenuSelect: \"onNodeContextMenuSelect\",\n    onNodeDrop: \"onNodeDrop\",\n    onLazyLoad: \"onLazyLoad\",\n    onScroll: \"onScroll\",\n    onScrollIndexChange: \"onScrollIndexChange\",\n    onFilter: \"onFilter\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 2,\n  vars: 2,\n  consts: [[3, \"ngClass\", \"ngStyle\", \"class\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"drop\", \"dragover\", \"dragenter\", \"dragleave\"], [\"class\", \"p-tree-loading-overlay p-component-overlay\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-tree-filter-container\", 4, \"ngIf\"], [\"styleClass\", \"p-tree-wrapper\", 3, \"items\", \"style\", \"itemSize\", \"lazy\", \"options\", \"onScroll\", \"onScrollIndexChange\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"p-tree-empty-message\", 4, \"ngIf\"], [1, \"p-tree-loading-overlay\", \"p-component-overlay\"], [1, \"p-tree-filter-container\"], [\"type\", \"text\", \"autocomplete\", \"off\", 1, \"p-tree-filter\", \"p-inputtext\", \"p-component\", 3, \"keydown.enter\", \"input\"], [\"filter\", \"\"], [1, \"p-tree-filter-icon\", \"pi\", \"pi-search\"], [\"styleClass\", \"p-tree-wrapper\", 3, \"items\", \"itemSize\", \"lazy\", \"options\", \"onScroll\", \"onScrollIndexChange\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"class\", \"p-tree-container\", \"role\", \"tree\", 3, \"ngClass\", \"style\", 4, \"ngIf\"], [\"role\", \"tree\", 1, \"p-tree-container\", 3, \"ngClass\"], [3, \"level\", \"rowNode\", \"node\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"indentation\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"level\", \"rowNode\", \"node\", \"firstChild\", \"lastChild\", \"index\", \"itemSize\", \"indentation\"], [\"pTemplate\", \"loader\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-tree-wrapper\"], [\"wrapper\", \"\"], [\"class\", \"p-tree-container\", \"role\", \"tree\", 4, \"ngIf\"], [\"role\", \"tree\", 1, \"p-tree-container\"], [3, \"node\", \"firstChild\", \"lastChild\", \"index\", \"level\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [3, \"node\", \"firstChild\", \"lastChild\", \"index\", \"level\"], [1, \"p-tree-empty-message\"], [4, \"ngIf\", \"ngIfElse\"], [\"emptyFilter\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-tree-loading-mask p-component-overlay\", 4, \"ngIf\"], [1, \"p-tree-loading-mask\", \"p-component-overlay\"], [3, \"node\", \"root\"]],\n  template: function Tree_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, Tree_div_0_Template, 8, 16, \"div\", 0);\n      i0.ɵɵtemplate(1, Tree_div_1_Template, 6, 11, \"div\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.horizontal);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.horizontal);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i3.PrimeTemplate, i4.Scroller, UITreeNode],\n  styles: [\".p-tree-container{margin:0;padding:0;list-style-type:none;overflow:auto}.p-treenode-children{margin:0;padding:0;list-style-type:none}.p-tree-wrapper{overflow:auto}.p-treenode-selectable{cursor:pointer;-webkit-user-select:none;user-select:none}.p-tree-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex-shrink:0}.p-treenode-leaf>.p-treenode-content .p-tree-toggler{visibility:hidden}.p-treenode-content{display:flex;align-items:center}.p-tree-filter{width:100%}.p-tree-filter-container{position:relative;display:block;width:100%}.p-tree-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-tree-loading{position:relative;min-height:4rem}.p-tree .p-tree-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-tree-flex-scrollable{display:flex;flex:1;height:100%;flex-direction:column}.p-tree-flex-scrollable .p-tree-wrapper{flex:1}.p-tree .p-treenode-droppoint{height:4px;list-style-type:none}.p-tree .p-treenode-droppoint-active{border:0 none}.p-tree-horizontal{width:auto;padding-left:0;padding-right:0;overflow:auto}.p-tree.p-tree-horizontal table,.p-tree.p-tree-horizontal tr,.p-tree.p-tree-horizontal td{border-collapse:collapse;margin:0;padding:0;vertical-align:middle}.p-tree-horizontal .p-treenode-content{font-weight:400;padding:.4em 1em .4em .2em;display:flex;align-items:center}.p-tree-horizontal .p-treenode-parent .p-treenode-content{font-weight:400;white-space:nowrap}.p-tree.p-tree-horizontal .p-treenode{background:url(data:image/gif;base64,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) repeat-x scroll center center transparent;padding:.25rem 2.5rem}.p-tree.p-tree-horizontal .p-treenode.p-treenode-leaf,.p-tree.p-tree-horizontal .p-treenode.p-treenode-collapsed{padding-right:0}.p-tree.p-tree-horizontal .p-treenode-children{padding:0;margin:0}.p-tree.p-tree-horizontal .p-treenode-connector{width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-table{height:100%;width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-line{background:url(data:image/gif;base64,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) repeat-y scroll 0 0 transparent;width:1px}.p-tree.p-tree-horizontal table{height:0}.p-scroller .p-tree-container{overflow:visible}\\n\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tree, [{\n    type: Component,\n    args: [{\n      selector: 'p-tree',\n      template: `\n        <div [ngClass]=\"{'p-tree p-component':true,'p-tree-selectable':selectionMode,\n                'p-treenode-dragover':dragHover,'p-tree-loading': loading, 'p-tree-flex-scrollable': scrollHeight === 'flex'}\"\n            [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"!horizontal\"\n            (drop)=\"onDrop($event)\" (dragover)=\"onDragOver($event)\" (dragenter)=\"onDragEnter()\" (dragleave)=\"onDragLeave($event)\">\n            <div class=\"p-tree-loading-overlay p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div *ngIf=\"filter\" class=\"p-tree-filter-container\">\n                <input #filter type=\"text\" autocomplete=\"off\" class=\"p-tree-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                    (keydown.enter)=\"$event.preventDefault()\" (input)=\"_filter($event.target.value)\">\n                    <span class=\"p-tree-filter-icon pi pi-search\"></span>\n            </div>\n\n            <p-scroller #scroller *ngIf=\"virtualScroll\" [items]=\"serializedValue\" styleClass=\"p-tree-wrapper\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_virtualNodeHeight\"\n                [lazy]=\"lazy\" (onScroll)=\"onScroll.emit($event)\" (onScrollIndexChange)=\"onScrollIndexChange.emit($event)\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                    <ul *ngIf=\"items\" class=\"p-tree-container\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let rowNode of items; let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [level]=\"rowNode.level\"\n                                    [rowNode]=\"rowNode\" [node]=\"rowNode.node\" [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"getIndex(scrollerOptions, index)\" [itemSize]=\"scrollerOptions.itemSize\" [indentation]=\"indentation\"></p-treeNode>\n                    </ul>\n                </ng-template>\n                <ng-container *ngIf=\"loaderTemplate\">\n                    <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                </ng-container>\n            </p-scroller>\n            <ng-container *ngIf=\"!virtualScroll\">\n                <div #wrapper class=\"p-tree-wrapper\" [style.max-height]=\"scrollHeight\">\n                    <ul class=\"p-tree-container\" *ngIf=\"getRootNode()\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let node of getRootNode(); let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [node]=\"node\"\n                                    [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [level]=\"0\"></p-treeNode>\n                    </ul>\n                </div>\n            </ng-container>\n\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n        <div [ngClass]=\"{'p-tree p-tree-horizontal p-component':true,'p-tree-selectable':selectionMode}\"  [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"horizontal\">\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div class=\"p-tree-loading-mask p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <table *ngIf=\"value&&value[0]\">\n                <p-treeNode [node]=\"value[0]\" [root]=\"true\"></p-treeNode>\n            </table>\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-tree-container{margin:0;padding:0;list-style-type:none;overflow:auto}.p-treenode-children{margin:0;padding:0;list-style-type:none}.p-tree-wrapper{overflow:auto}.p-treenode-selectable{cursor:pointer;-webkit-user-select:none;user-select:none}.p-tree-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex-shrink:0}.p-treenode-leaf>.p-treenode-content .p-tree-toggler{visibility:hidden}.p-treenode-content{display:flex;align-items:center}.p-tree-filter{width:100%}.p-tree-filter-container{position:relative;display:block;width:100%}.p-tree-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-tree-loading{position:relative;min-height:4rem}.p-tree .p-tree-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-tree-flex-scrollable{display:flex;flex:1;height:100%;flex-direction:column}.p-tree-flex-scrollable .p-tree-wrapper{flex:1}.p-tree .p-treenode-droppoint{height:4px;list-style-type:none}.p-tree .p-treenode-droppoint-active{border:0 none}.p-tree-horizontal{width:auto;padding-left:0;padding-right:0;overflow:auto}.p-tree.p-tree-horizontal table,.p-tree.p-tree-horizontal tr,.p-tree.p-tree-horizontal td{border-collapse:collapse;margin:0;padding:0;vertical-align:middle}.p-tree-horizontal .p-treenode-content{font-weight:400;padding:.4em 1em .4em .2em;display:flex;align-items:center}.p-tree-horizontal .p-treenode-parent .p-treenode-content{font-weight:400;white-space:nowrap}.p-tree.p-tree-horizontal .p-treenode{background:url(data:image/gif;base64,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) repeat-x scroll center center transparent;padding:.25rem 2.5rem}.p-tree.p-tree-horizontal .p-treenode.p-treenode-leaf,.p-tree.p-tree-horizontal .p-treenode.p-treenode-collapsed{padding-right:0}.p-tree.p-tree-horizontal .p-treenode-children{padding:0;margin:0}.p-tree.p-tree-horizontal .p-treenode-connector{width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-table{height:100%;width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-line{background:url(data:image/gif;base64,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) repeat-y scroll 0 0 transparent;width:1px}.p-tree.p-tree-horizontal table{height:0}.p-scroller .p-tree-container{overflow:visible}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i3.TreeDragDropService,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: i3.PrimeNGConfig\n    }];\n  }, {\n    value: [{\n      type: Input\n    }],\n    selectionMode: [{\n      type: Input\n    }],\n    selection: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    contextMenu: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    draggableScope: [{\n      type: Input\n    }],\n    droppableScope: [{\n      type: Input\n    }],\n    draggableNodes: [{\n      type: Input\n    }],\n    droppableNodes: [{\n      type: Input\n    }],\n    metaKeySelection: [{\n      type: Input\n    }],\n    propagateSelectionUp: [{\n      type: Input\n    }],\n    propagateSelectionDown: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    loadingIcon: [{\n      type: Input\n    }],\n    emptyMessage: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    togglerAriaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    validateDrop: [{\n      type: Input\n    }],\n    filter: [{\n      type: Input\n    }],\n    filterBy: [{\n      type: Input\n    }],\n    filterMode: [{\n      type: Input\n    }],\n    filterPlaceholder: [{\n      type: Input\n    }],\n    filteredNodes: [{\n      type: Input\n    }],\n    filterLocale: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    virtualScrollItemSize: [{\n      type: Input\n    }],\n    virtualScrollOptions: [{\n      type: Input\n    }],\n    indentation: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    onNodeSelect: [{\n      type: Output\n    }],\n    onNodeUnselect: [{\n      type: Output\n    }],\n    onNodeExpand: [{\n      type: Output\n    }],\n    onNodeCollapse: [{\n      type: Output\n    }],\n    onNodeContextMenuSelect: [{\n      type: Output\n    }],\n    onNodeDrop: [{\n      type: Output\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    onFilter: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    filterViewChild: [{\n      type: ViewChild,\n      args: ['filter']\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: ['scroller']\n    }],\n    wrapperViewChild: [{\n      type: ViewChild,\n      args: ['wrapper']\n    }],\n    virtualNodeHeight: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TreeModule {}\n\nTreeModule.ɵfac = function TreeModule_Factory(t) {\n  return new (t || TreeModule)();\n};\n\nTreeModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TreeModule\n});\nTreeModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TreeModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule, ScrollerModule],\n      exports: [Tree, SharedModule, ScrollerModule],\n      declarations: [Tree, UITreeNode]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Tree, TreeModule, UITreeNode };", "map": {"version": 3, "names": ["i0", "forwardRef", "Component", "ViewEncapsulation", "Inject", "Input", "EventEmitter", "ChangeDetectionStrategy", "Optional", "Output", "ContentChildren", "ViewChild", "NgModule", "i1", "CommonModule", "i3", "Translation<PERSON>eys", "PrimeTemplate", "SharedModule", "ObjectUtils", "<PERSON><PERSON><PERSON><PERSON>", "i2", "RippleModule", "i4", "ScrollerModule", "UITreeNode", "constructor", "tree", "ngOnInit", "node", "parent", "parentNode", "syncNodeOption", "value", "getNodeWithKey", "key", "getIcon", "icon", "expanded", "children", "length", "expandedIcon", "collapsedIcon", "ICON_CLASS", "<PERSON><PERSON><PERSON><PERSON>", "isNodeLeaf", "toggle", "event", "collapse", "expand", "virtualScroll", "updateSerializedValue", "onNodeExpand", "emit", "originalEvent", "onNodeCollapse", "onNodeClick", "onNodeKeydown", "which", "onNodeTouchEnd", "onNodeRightClick", "isSelected", "onDropPoint", "position", "preventDefault", "dragNode", "dragNodeIndex", "dragNodeScope", "isValidDropPointIndex", "dragNodeTree", "index", "allowDrop", "dropParams", "Object", "assign", "createDropPointEventMetadata", "validateDrop", "onNodeDrop", "dropNode", "accept", "processPointDrop", "draghoverPrev", "draghoverNext", "newNodeList", "dragNodeSubNodes", "splice", "dropIndex", "push", "dragDropService", "stopDrag", "subNodes", "onDropPointDragOver", "dataTransfer", "dropEffect", "onDropPointDragEnter", "onDropPointDragLeave", "onDragStart", "draggableNodes", "draggable", "setData", "startDrag", "scope", "draggableScope", "onDragStop", "onDropNodeDragOver", "droppableNodes", "stopPropagation", "onDropNode", "droppable", "createDropNodeEventMetadata", "processNodeDrop", "draghoverNode", "onDropNodeDragEnter", "onDropNodeDragLeave", "rect", "currentTarget", "getBoundingClientRect", "x", "left", "width", "y", "Math", "floor", "top", "height", "onKeyDown", "nodeElement", "target", "parentElement", "nodeName", "contextMenu", "containerViewChild", "nativeElement", "style", "display", "listElement", "focusNode", "nextNodeElement", "nextElement<PERSON><PERSON>ling", "nextSiblingAncestor", "findNextSiblingOfAncestor", "previousElementSibling", "findLastVisibleDescendant", "parentNodeElement", "getParentNodeElement", "Array", "from", "find", "el", "hasClass", "childrenListElement", "lastChildElement", "tagName", "element", "focus", "ɵfac", "Tree", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "encapsulation", "None", "host", "undefined", "decorators", "rowNode", "root", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "level", "indentation", "itemSize", "config", "layout", "metaKeySelection", "propagateSelectionUp", "propagateSelectionDown", "loadingIcon", "emptyMessage", "filterBy", "filterMode", "lazy", "trackBy", "item", "selectionChange", "onNodeSelect", "onNodeUnselect", "onNodeContextMenuSelect", "onLazyLoad", "onScroll", "onScrollIndexChange", "onFilter", "virtualNodeHeight", "_virtualNodeHeight", "val", "console", "warn", "dragStartSubscription", "dragStart$", "subscribe", "dragStopSubscription", "dragStop$", "dragHover", "ngOnChanges", "simpleChange", "horizontal", "emptyMessageLabel", "getTranslation", "EMPTY_MESSAGE", "ngAfterContentInit", "templates", "templateMap", "for<PERSON>ach", "getType", "headerTemplate", "emptyMessageTemplate", "footerTemplate", "loaderTemplate", "name", "serializedValue", "serializeNodes", "getRootNode", "nodes", "visible", "eventTarget", "selectionMode", "selectable", "hasFilteredNodes", "findIndexInSelection", "selected", "isCheckboxSelectionMode", "propagateDown", "selection", "filter", "i", "propagateUp", "metaSelection", "nodeTouched", "metaKey", "ctrl<PERSON>ey", "isSingleSelectionMode", "isMultipleSelectionMode", "className", "indexOf", "show", "areNodesEqual", "selectedNode", "parentNodes", "option", "_node", "filteredNodes", "matchedNode", "select", "selectedCount", "childPartialSelected", "child", "partialSelected", "leaf", "getTemplateForNode", "onDragOver", "onDrop", "processTreeDrop", "onDragEnter", "onDragLeave", "isValidDragScope", "allow", "dragScope", "dropScope", "droppableScope", "s", "ds", "_filter", "filterValue", "searchFields", "split", "filterText", "removeAccents", "toLocaleLowerCase", "filterLocale", "isStrictMode", "copyNode", "paramsWithoutNode", "findFilteredNodes", "isFilterMatched", "filteredValue", "resetFilter", "filterView<PERSON>hild", "scrollToVirtualIndex", "scroller", "scrollToIndex", "scrollTo", "options", "wrapperViewChild", "scrollLeft", "scrollTop", "matched", "childNodes", "childNode", "copyChildNode", "field", "fieldValue", "String", "resolveFieldData", "getIndex", "getItemOptions", "getBlockableElement", "ngOnDestroy", "unsubscribe", "ElementRef", "TreeDragDropService", "PrimeNGConfig", "<PERSON><PERSON><PERSON>", "changeDetection", "<PERSON><PERSON><PERSON>", "styles", "styleClass", "loading", "aria<PERSON><PERSON><PERSON>", "toggler<PERSON><PERSON><PERSON><PERSON><PERSON>", "ariaLabelledBy", "filterPlaceholder", "scrollHeight", "virtualScrollItemSize", "virtualScrollOptions", "TreeModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-tree.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, Component, ViewEncapsulation, Inject, Input, EventEmitter, ChangeDetectionStrategy, Optional, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i3 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ObjectUtils } from 'primeng/utils';\nimport { <PERSON>Handler } from 'primeng/dom';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\n\nclass UITreeNode {\n    constructor(tree) {\n        this.tree = tree;\n    }\n    ngOnInit() {\n        this.node.parent = this.parentNode;\n        if (this.parentNode) {\n            this.tree.syncNodeOption(this.node, this.tree.value, 'parent', this.tree.getNodeWithKey(this.parentNode.key, this.tree.value));\n        }\n    }\n    getIcon() {\n        let icon;\n        if (this.node.icon)\n            icon = this.node.icon;\n        else\n            icon = this.node.expanded && this.node.children && this.node.children.length ? this.node.expandedIcon : this.node.collapsedIcon;\n        return UITreeNode.ICON_CLASS + ' ' + icon;\n    }\n    isLeaf() {\n        return this.tree.isNodeLeaf(this.node);\n    }\n    toggle(event) {\n        if (this.node.expanded)\n            this.collapse(event);\n        else\n            this.expand(event);\n    }\n    expand(event) {\n        this.node.expanded = true;\n        if (this.tree.virtualScroll) {\n            this.tree.updateSerializedValue();\n        }\n        this.tree.onNodeExpand.emit({ originalEvent: event, node: this.node });\n    }\n    collapse(event) {\n        this.node.expanded = false;\n        if (this.tree.virtualScroll) {\n            this.tree.updateSerializedValue();\n        }\n        this.tree.onNodeCollapse.emit({ originalEvent: event, node: this.node });\n    }\n    onNodeClick(event) {\n        this.tree.onNodeClick(event, this.node);\n    }\n    onNodeKeydown(event) {\n        if (event.which === 13) {\n            this.tree.onNodeClick(event, this.node);\n        }\n    }\n    onNodeTouchEnd() {\n        this.tree.onNodeTouchEnd();\n    }\n    onNodeRightClick(event) {\n        this.tree.onNodeRightClick(event, this.node);\n    }\n    isSelected() {\n        return this.tree.isSelected(this.node);\n    }\n    onDropPoint(event, position) {\n        event.preventDefault();\n        let dragNode = this.tree.dragNode;\n        let dragNodeIndex = this.tree.dragNodeIndex;\n        let dragNodeScope = this.tree.dragNodeScope;\n        let isValidDropPointIndex = this.tree.dragNodeTree === this.tree ? (position === 1 || dragNodeIndex !== this.index - 1) : true;\n        if (this.tree.allowDrop(dragNode, this.node, dragNodeScope) && isValidDropPointIndex) {\n            let dropParams = Object.assign({}, this.createDropPointEventMetadata(position));\n            if (this.tree.validateDrop) {\n                this.tree.onNodeDrop.emit({\n                    originalEvent: event,\n                    dragNode: dragNode,\n                    dropNode: this.node,\n                    index: this.index,\n                    accept: () => {\n                        this.processPointDrop(dropParams);\n                    }\n                });\n            }\n            else {\n                this.processPointDrop(dropParams);\n                this.tree.onNodeDrop.emit({\n                    originalEvent: event,\n                    dragNode: dragNode,\n                    dropNode: this.node,\n                    index: this.index\n                });\n            }\n        }\n        this.draghoverPrev = false;\n        this.draghoverNext = false;\n    }\n    processPointDrop(event) {\n        let newNodeList = event.dropNode.parent ? event.dropNode.parent.children : this.tree.value;\n        event.dragNodeSubNodes.splice(event.dragNodeIndex, 1);\n        let dropIndex = this.index;\n        if (event.position < 0) {\n            dropIndex = (event.dragNodeSubNodes === newNodeList) ? ((event.dragNodeIndex > event.index) ? event.index : event.index - 1) : event.index;\n            newNodeList.splice(dropIndex, 0, event.dragNode);\n        }\n        else {\n            dropIndex = newNodeList.length;\n            newNodeList.push(event.dragNode);\n        }\n        this.tree.dragDropService.stopDrag({\n            node: event.dragNode,\n            subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n            index: event.dragNodeIndex\n        });\n    }\n    createDropPointEventMetadata(position) {\n        return {\n            dragNode: this.tree.dragNode,\n            dragNodeIndex: this.tree.dragNodeIndex,\n            dragNodeSubNodes: this.tree.dragNodeSubNodes,\n            dropNode: this.node,\n            index: this.index,\n            position: position\n        };\n    }\n    onDropPointDragOver(event) {\n        event.dataTransfer.dropEffect = 'move';\n        event.preventDefault();\n    }\n    onDropPointDragEnter(event, position) {\n        if (this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n            if (position < 0)\n                this.draghoverPrev = true;\n            else\n                this.draghoverNext = true;\n        }\n    }\n    onDropPointDragLeave(event) {\n        this.draghoverPrev = false;\n        this.draghoverNext = false;\n    }\n    onDragStart(event) {\n        if (this.tree.draggableNodes && this.node.draggable !== false) {\n            event.dataTransfer.setData(\"text\", \"data\");\n            this.tree.dragDropService.startDrag({\n                tree: this,\n                node: this.node,\n                subNodes: this.node.parent ? this.node.parent.children : this.tree.value,\n                index: this.index,\n                scope: this.tree.draggableScope\n            });\n        }\n        else {\n            event.preventDefault();\n        }\n    }\n    onDragStop(event) {\n        this.tree.dragDropService.stopDrag({\n            node: this.node,\n            subNodes: this.node.parent ? this.node.parent.children : this.tree.value,\n            index: this.index\n        });\n    }\n    onDropNodeDragOver(event) {\n        event.dataTransfer.dropEffect = 'move';\n        if (this.tree.droppableNodes) {\n            event.preventDefault();\n            event.stopPropagation();\n        }\n    }\n    onDropNode(event) {\n        if (this.tree.droppableNodes && this.node.droppable !== false) {\n            let dragNode = this.tree.dragNode;\n            if (this.tree.allowDrop(dragNode, this.node, this.tree.dragNodeScope)) {\n                let dropParams = Object.assign({}, this.createDropNodeEventMetadata());\n                if (this.tree.validateDrop) {\n                    this.tree.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: this.node,\n                        index: this.index,\n                        accept: () => {\n                            this.processNodeDrop(dropParams);\n                        }\n                    });\n                }\n                else {\n                    this.processNodeDrop(dropParams);\n                    this.tree.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: this.node,\n                        index: this.index\n                    });\n                }\n            }\n        }\n        event.preventDefault();\n        event.stopPropagation();\n        this.draghoverNode = false;\n    }\n    createDropNodeEventMetadata() {\n        return {\n            dragNode: this.tree.dragNode,\n            dragNodeIndex: this.tree.dragNodeIndex,\n            dragNodeSubNodes: this.tree.dragNodeSubNodes,\n            dropNode: this.node\n        };\n    }\n    processNodeDrop(event) {\n        let dragNodeIndex = event.dragNodeIndex;\n        event.dragNodeSubNodes.splice(dragNodeIndex, 1);\n        if (event.dropNode.children)\n            event.dropNode.children.push(event.dragNode);\n        else\n            event.dropNode.children = [event.dragNode];\n        this.tree.dragDropService.stopDrag({\n            node: event.dragNode,\n            subNodes: event.dropNode.parent ? event.dropNode.parent.children : this.tree.value,\n            index: dragNodeIndex\n        });\n    }\n    onDropNodeDragEnter(event) {\n        if (this.tree.droppableNodes && this.node.droppable !== false && this.tree.allowDrop(this.tree.dragNode, this.node, this.tree.dragNodeScope)) {\n            this.draghoverNode = true;\n        }\n    }\n    onDropNodeDragLeave(event) {\n        if (this.tree.droppableNodes) {\n            let rect = event.currentTarget.getBoundingClientRect();\n            if (event.x > rect.left + rect.width || event.x < rect.left || event.y >= Math.floor(rect.top + rect.height) || event.y < rect.top) {\n                this.draghoverNode = false;\n            }\n        }\n    }\n    onKeyDown(event) {\n        const nodeElement = event.target.parentElement.parentElement;\n        if (nodeElement.nodeName !== 'P-TREENODE' || (this.tree.contextMenu && this.tree.contextMenu.containerViewChild.nativeElement.style.display === 'block')) {\n            return;\n        }\n        switch (event.which) {\n            //down arrow\n            case 40:\n                const listElement = (this.tree.droppableNodes) ? nodeElement.children[1].children[1] : nodeElement.children[0].children[1];\n                if (listElement && listElement.children.length > 0) {\n                    this.focusNode(listElement.children[0]);\n                }\n                else {\n                    const nextNodeElement = nodeElement.nextElementSibling;\n                    if (nextNodeElement) {\n                        this.focusNode(nextNodeElement);\n                    }\n                    else {\n                        let nextSiblingAncestor = this.findNextSiblingOfAncestor(nodeElement);\n                        if (nextSiblingAncestor) {\n                            this.focusNode(nextSiblingAncestor);\n                        }\n                    }\n                }\n                event.preventDefault();\n                break;\n            //up arrow\n            case 38:\n                if (nodeElement.previousElementSibling) {\n                    this.focusNode(this.findLastVisibleDescendant(nodeElement.previousElementSibling));\n                }\n                else {\n                    let parentNodeElement = this.getParentNodeElement(nodeElement);\n                    if (parentNodeElement) {\n                        this.focusNode(parentNodeElement);\n                    }\n                }\n                event.preventDefault();\n                break;\n            //right arrow\n            case 39:\n                if (!this.node.expanded && !this.tree.isNodeLeaf(this.node)) {\n                    this.expand(event);\n                }\n                event.preventDefault();\n                break;\n            //left arrow\n            case 37:\n                if (this.node.expanded) {\n                    this.collapse(event);\n                }\n                else {\n                    let parentNodeElement = this.getParentNodeElement(nodeElement);\n                    if (parentNodeElement) {\n                        this.focusNode(parentNodeElement);\n                    }\n                }\n                event.preventDefault();\n                break;\n            //enter\n            case 13:\n                this.tree.onNodeClick(event, this.node);\n                event.preventDefault();\n                break;\n            default:\n                //no op\n                break;\n        }\n    }\n    findNextSiblingOfAncestor(nodeElement) {\n        let parentNodeElement = this.getParentNodeElement(nodeElement);\n        if (parentNodeElement) {\n            if (parentNodeElement.nextElementSibling)\n                return parentNodeElement.nextElementSibling;\n            else\n                return this.findNextSiblingOfAncestor(parentNodeElement);\n        }\n        else {\n            return null;\n        }\n    }\n    findLastVisibleDescendant(nodeElement) {\n        const listElement = Array.from(nodeElement.children).find(el => DomHandler.hasClass(el, 'p-treenode'));\n        const childrenListElement = listElement.children[1];\n        if (childrenListElement && childrenListElement.children.length > 0) {\n            const lastChildElement = childrenListElement.children[childrenListElement.children.length - 1];\n            return this.findLastVisibleDescendant(lastChildElement);\n        }\n        else {\n            return nodeElement;\n        }\n    }\n    getParentNodeElement(nodeElement) {\n        const parentNodeElement = nodeElement.parentElement.parentElement.parentElement;\n        return parentNodeElement.tagName === 'P-TREENODE' ? parentNodeElement : null;\n    }\n    focusNode(element) {\n        if (this.tree.droppableNodes)\n            element.children[1].children[0].focus();\n        else\n            element.children[0].children[0].focus();\n    }\n}\nUITreeNode.ICON_CLASS = 'p-treenode-icon ';\nUITreeNode.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UITreeNode, deps: [{ token: forwardRef(() => Tree) }], target: i0.ɵɵFactoryTarget.Component });\nUITreeNode.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: UITreeNode, selector: \"p-treeNode\", inputs: { rowNode: \"rowNode\", node: \"node\", parentNode: \"parentNode\", root: \"root\", index: \"index\", firstChild: \"firstChild\", lastChild: \"lastChild\", level: \"level\", indentation: \"indentation\", itemSize: \"itemSize\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <ng-template [ngIf]=\"node\">\n            <li *ngIf=\"tree.droppableNodes\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverPrev}\"\n            (drop)=\"onDropPoint($event,-1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,-1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <li *ngIf=\"!tree.horizontal\" [ngClass]=\"['p-treenode',node.styleClass||'', isLeaf() ? 'p-treenode-leaf': '']\" [ngStyle]=\"{'height': itemSize + 'px'}\">\n                <div class=\"p-treenode-content\" [style.paddingLeft]=\"(level * indentation)  + 'rem'\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\" (touchend)=\"onNodeTouchEnd()\"\n                    (drop)=\"onDropNode($event)\" (dragover)=\"onDropNodeDragOver($event)\" (dragenter)=\"onDropNodeDragEnter($event)\" (dragleave)=\"onDropNodeDragLeave($event)\"\n                    [draggable]=\"tree.draggableNodes\" (dragstart)=\"onDragStart($event)\" (dragend)=\"onDragStop($event)\" [attr.tabindex]=\"0\"\n                    [ngClass]=\"{'p-treenode-selectable':tree.selectionMode && node.selectable !== false,'p-treenode-dragover':draghoverNode, 'p-highlight':isSelected()}\" role=\"treeitem\"\n                    (keydown)=\"onKeyDown($event)\" [attr.aria-posinset]=\"this.index + 1\" [attr.aria-expanded]=\"this.node.expanded\" [attr.aria-selected]=\"isSelected()\" [attr.aria-label]=\"node.label\">\n                    <button type=\"button\" [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler p-link\" (click)=\"toggle($event)\" pRipple tabindex=\"-1\">\n                        <span class=\"p-tree-toggler-icon pi pi-fw\" [ngClass]=\"{'pi-chevron-right':!node.expanded,'pi-chevron-down':node.expanded}\"></span>\n                    </button>\n                    <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-disabled': node.selectable === false}\" *ngIf=\"tree.selectionMode == 'checkbox'\" [attr.aria-checked]=\"isSelected()\">\n                        <div class=\"p-checkbox-box\" [ngClass]=\"{'p-highlight': isSelected(), 'p-indeterminate': node.partialSelected}\">\n                            <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':isSelected(),'pi-minus':node.partialSelected}\"></span>\n                        </div>\n                    </div>\n                    <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                    <span class=\"p-treenode-label\">\n                            <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                            <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                            </span>\n                    </span>\n                </div>\n                <ul class=\"p-treenode-children\" style=\"display: none;\" *ngIf=\"!tree.virtualScroll && node.children && node.expanded\" [style.display]=\"node.expanded ? 'block' : 'none'\" role=\"group\">\n                    <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; let index=index; trackBy: tree.trackBy\" [node]=\"childNode\" [parentNode]=\"node\"\n                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [itemSize]=\"itemSize\" [level]=\"level + 1\"></p-treeNode>\n                </ul>\n            </li>\n            <li *ngIf=\"tree.droppableNodes&&lastChild\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverNext}\"\n            (drop)=\"onDropPoint($event,1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <table *ngIf=\"tree.horizontal\" [class]=\"node.styleClass\">\n                <tbody>\n                    <tr>\n                        <td class=\"p-treenode-connector\" *ngIf=\"!root\">\n                            <table class=\"p-treenode-connector-table\">\n                                <tbody>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!firstChild}\"></td>\n                                    </tr>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!lastChild}\"></td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </td>\n                        <td class=\"p-treenode\" [ngClass]=\"{'p-treenode-collapsed':!node.expanded}\">\n                            <div class=\"p-treenode-content\" tabindex=\"0\" [ngClass]=\"{'p-treenode-selectable':tree.selectionMode,'p-highlight':isSelected()}\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\"\n                                (touchend)=\"onNodeTouchEnd()\" (keydown)=\"onNodeKeydown($event)\">\n                                <span [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler pi pi-fw\" [ngClass]=\"{'pi-plus':!node.expanded,'pi-minus':node.expanded}\" *ngIf=\"!isLeaf()\" (click)=\"toggle($event)\"></span>\n                                <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                                <span class=\"p-treenode-label\">\n                                    <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                                    <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                        <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                                    </span>\n                                </span>\n                            </div>\n                        </td>\n                        <td class=\"p-treenode-children-container\" *ngIf=\"node.children && node.expanded\" [style.display]=\"node.expanded ? 'table-cell' : 'none'\">\n                            <div class=\"p-treenode-children\">\n                                <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; trackBy: tree.trackBy\" [node]=\"childNode\"\n                                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\"></p-treeNode>\n                            </div>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </ng-template>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.Ripple, selector: \"[pRipple]\" }, { kind: \"component\", type: UITreeNode, selector: \"p-treeNode\", inputs: [\"rowNode\", \"node\", \"parentNode\", \"root\", \"index\", \"firstChild\", \"lastChild\", \"level\", \"indentation\", \"itemSize\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UITreeNode, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-treeNode',\n                    template: `\n        <ng-template [ngIf]=\"node\">\n            <li *ngIf=\"tree.droppableNodes\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverPrev}\"\n            (drop)=\"onDropPoint($event,-1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,-1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <li *ngIf=\"!tree.horizontal\" [ngClass]=\"['p-treenode',node.styleClass||'', isLeaf() ? 'p-treenode-leaf': '']\" [ngStyle]=\"{'height': itemSize + 'px'}\">\n                <div class=\"p-treenode-content\" [style.paddingLeft]=\"(level * indentation)  + 'rem'\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\" (touchend)=\"onNodeTouchEnd()\"\n                    (drop)=\"onDropNode($event)\" (dragover)=\"onDropNodeDragOver($event)\" (dragenter)=\"onDropNodeDragEnter($event)\" (dragleave)=\"onDropNodeDragLeave($event)\"\n                    [draggable]=\"tree.draggableNodes\" (dragstart)=\"onDragStart($event)\" (dragend)=\"onDragStop($event)\" [attr.tabindex]=\"0\"\n                    [ngClass]=\"{'p-treenode-selectable':tree.selectionMode && node.selectable !== false,'p-treenode-dragover':draghoverNode, 'p-highlight':isSelected()}\" role=\"treeitem\"\n                    (keydown)=\"onKeyDown($event)\" [attr.aria-posinset]=\"this.index + 1\" [attr.aria-expanded]=\"this.node.expanded\" [attr.aria-selected]=\"isSelected()\" [attr.aria-label]=\"node.label\">\n                    <button type=\"button\" [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler p-link\" (click)=\"toggle($event)\" pRipple tabindex=\"-1\">\n                        <span class=\"p-tree-toggler-icon pi pi-fw\" [ngClass]=\"{'pi-chevron-right':!node.expanded,'pi-chevron-down':node.expanded}\"></span>\n                    </button>\n                    <div class=\"p-checkbox p-component\" [ngClass]=\"{'p-checkbox-disabled': node.selectable === false}\" *ngIf=\"tree.selectionMode == 'checkbox'\" [attr.aria-checked]=\"isSelected()\">\n                        <div class=\"p-checkbox-box\" [ngClass]=\"{'p-highlight': isSelected(), 'p-indeterminate': node.partialSelected}\">\n                            <span class=\"p-checkbox-icon pi\" [ngClass]=\"{'pi-check':isSelected(),'pi-minus':node.partialSelected}\"></span>\n                        </div>\n                    </div>\n                    <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                    <span class=\"p-treenode-label\">\n                            <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                            <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                            </span>\n                    </span>\n                </div>\n                <ul class=\"p-treenode-children\" style=\"display: none;\" *ngIf=\"!tree.virtualScroll && node.children && node.expanded\" [style.display]=\"node.expanded ? 'block' : 'none'\" role=\"group\">\n                    <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; let index=index; trackBy: tree.trackBy\" [node]=\"childNode\" [parentNode]=\"node\"\n                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [itemSize]=\"itemSize\" [level]=\"level + 1\"></p-treeNode>\n                </ul>\n            </li>\n            <li *ngIf=\"tree.droppableNodes&&lastChild\" class=\"p-treenode-droppoint\" [ngClass]=\"{'p-treenode-droppoint-active':draghoverNext}\"\n            (drop)=\"onDropPoint($event,1)\" (dragover)=\"onDropPointDragOver($event)\" (dragenter)=\"onDropPointDragEnter($event,1)\" (dragleave)=\"onDropPointDragLeave($event)\"></li>\n            <table *ngIf=\"tree.horizontal\" [class]=\"node.styleClass\">\n                <tbody>\n                    <tr>\n                        <td class=\"p-treenode-connector\" *ngIf=\"!root\">\n                            <table class=\"p-treenode-connector-table\">\n                                <tbody>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!firstChild}\"></td>\n                                    </tr>\n                                    <tr>\n                                        <td [ngClass]=\"{'p-treenode-connector-line':!lastChild}\"></td>\n                                    </tr>\n                                </tbody>\n                            </table>\n                        </td>\n                        <td class=\"p-treenode\" [ngClass]=\"{'p-treenode-collapsed':!node.expanded}\">\n                            <div class=\"p-treenode-content\" tabindex=\"0\" [ngClass]=\"{'p-treenode-selectable':tree.selectionMode,'p-highlight':isSelected()}\" (click)=\"onNodeClick($event)\" (contextmenu)=\"onNodeRightClick($event)\"\n                                (touchend)=\"onNodeTouchEnd()\" (keydown)=\"onNodeKeydown($event)\">\n                                <span [attr.aria-label]=\"tree.togglerAriaLabel\" class=\"p-tree-toggler pi pi-fw\" [ngClass]=\"{'pi-plus':!node.expanded,'pi-minus':node.expanded}\" *ngIf=\"!isLeaf()\" (click)=\"toggle($event)\"></span>\n                                <span [class]=\"getIcon()\" *ngIf=\"node.icon||node.expandedIcon||node.collapsedIcon\"></span>\n                                <span class=\"p-treenode-label\">\n                                    <span *ngIf=\"!tree.getTemplateForNode(node)\">{{node.label}}</span>\n                                    <span *ngIf=\"tree.getTemplateForNode(node)\">\n                                        <ng-container *ngTemplateOutlet=\"tree.getTemplateForNode(node); context: {$implicit: node}\"></ng-container>\n                                    </span>\n                                </span>\n                            </div>\n                        </td>\n                        <td class=\"p-treenode-children-container\" *ngIf=\"node.children && node.expanded\" [style.display]=\"node.expanded ? 'table-cell' : 'none'\">\n                            <div class=\"p-treenode-children\">\n                                <p-treeNode *ngFor=\"let childNode of node.children;let firstChild=first;let lastChild=last; trackBy: tree.trackBy\" [node]=\"childNode\"\n                                        [firstChild]=\"firstChild\" [lastChild]=\"lastChild\"></p-treeNode>\n                            </div>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </ng-template>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [forwardRef(() => Tree)]\n                    }] }];\n    }, propDecorators: { rowNode: [{\n                type: Input\n            }], node: [{\n                type: Input\n            }], parentNode: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], firstChild: [{\n                type: Input\n            }], lastChild: [{\n                type: Input\n            }], level: [{\n                type: Input\n            }], indentation: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }] } });\nclass Tree {\n    constructor(el, dragDropService, config) {\n        this.el = el;\n        this.dragDropService = dragDropService;\n        this.config = config;\n        this.layout = 'vertical';\n        this.metaKeySelection = true;\n        this.propagateSelectionUp = true;\n        this.propagateSelectionDown = true;\n        this.loadingIcon = 'pi pi-spinner';\n        this.emptyMessage = '';\n        this.filterBy = 'label';\n        this.filterMode = 'lenient';\n        this.lazy = false;\n        this.indentation = 1.5;\n        this.trackBy = (index, item) => item;\n        this.selectionChange = new EventEmitter();\n        this.onNodeSelect = new EventEmitter();\n        this.onNodeUnselect = new EventEmitter();\n        this.onNodeExpand = new EventEmitter();\n        this.onNodeCollapse = new EventEmitter();\n        this.onNodeContextMenuSelect = new EventEmitter();\n        this.onNodeDrop = new EventEmitter();\n        this.onLazyLoad = new EventEmitter();\n        this.onScroll = new EventEmitter();\n        this.onScrollIndexChange = new EventEmitter();\n        this.onFilter = new EventEmitter();\n    }\n    get virtualNodeHeight() {\n        return this._virtualNodeHeight;\n    }\n    set virtualNodeHeight(val) {\n        this._virtualNodeHeight = val;\n        console.warn(\"The virtualNodeHeight property is deprecated, use virtualScrollItemSize property instead.\");\n    }\n    ngOnInit() {\n        if (this.droppableNodes) {\n            this.dragStartSubscription = this.dragDropService.dragStart$.subscribe(event => {\n                this.dragNodeTree = event.tree;\n                this.dragNode = event.node;\n                this.dragNodeSubNodes = event.subNodes;\n                this.dragNodeIndex = event.index;\n                this.dragNodeScope = event.scope;\n            });\n            this.dragStopSubscription = this.dragDropService.dragStop$.subscribe(event => {\n                this.dragNodeTree = null;\n                this.dragNode = null;\n                this.dragNodeSubNodes = null;\n                this.dragNodeIndex = null;\n                this.dragNodeScope = null;\n                this.dragHover = false;\n            });\n        }\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.value) {\n            this.updateSerializedValue();\n        }\n    }\n    get horizontal() {\n        return this.layout == 'horizontal';\n    }\n    get emptyMessageLabel() {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n    ngAfterContentInit() {\n        if (this.templates.length) {\n            this.templateMap = {};\n        }\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'empty':\n                    this.emptyMessageTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                default:\n                    this.templateMap[item.name] = item.template;\n                    break;\n            }\n        });\n    }\n    updateSerializedValue() {\n        this.serializedValue = [];\n        this.serializeNodes(null, this.getRootNode(), 0, true);\n    }\n    serializeNodes(parent, nodes, level, visible) {\n        if (nodes && nodes.length) {\n            for (let node of nodes) {\n                node.parent = parent;\n                const rowNode = {\n                    node: node,\n                    parent: parent,\n                    level: level,\n                    visible: visible && (parent ? parent.expanded : true)\n                };\n                this.serializedValue.push(rowNode);\n                if (rowNode.visible && node.expanded) {\n                    this.serializeNodes(node, node.children, level + 1, rowNode.visible);\n                }\n            }\n        }\n    }\n    onNodeClick(event, node) {\n        let eventTarget = event.target;\n        if (DomHandler.hasClass(eventTarget, 'p-tree-toggler') || DomHandler.hasClass(eventTarget, 'p-tree-toggler-icon')) {\n            return;\n        }\n        else if (this.selectionMode) {\n            if (node.selectable === false) {\n                return;\n            }\n            if (this.hasFilteredNodes()) {\n                node = this.getNodeWithKey(node.key, this.value);\n                if (!node) {\n                    return;\n                }\n            }\n            let index = this.findIndexInSelection(node);\n            let selected = (index >= 0);\n            if (this.isCheckboxSelectionMode()) {\n                if (selected) {\n                    if (this.propagateSelectionDown)\n                        this.propagateDown(node, false);\n                    else\n                        this.selection = this.selection.filter((val, i) => i != index);\n                    if (this.propagateSelectionUp && node.parent) {\n                        this.propagateUp(node.parent, false);\n                    }\n                    this.selectionChange.emit(this.selection);\n                    this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                }\n                else {\n                    if (this.propagateSelectionDown)\n                        this.propagateDown(node, true);\n                    else\n                        this.selection = [...this.selection || [], node];\n                    if (this.propagateSelectionUp && node.parent) {\n                        this.propagateUp(node.parent, true);\n                    }\n                    this.selectionChange.emit(this.selection);\n                    this.onNodeSelect.emit({ originalEvent: event, node: node });\n                }\n            }\n            else {\n                let metaSelection = this.nodeTouched ? false : this.metaKeySelection;\n                if (metaSelection) {\n                    let metaKey = (event.metaKey || event.ctrlKey);\n                    if (selected && metaKey) {\n                        if (this.isSingleSelectionMode()) {\n                            this.selectionChange.emit(null);\n                        }\n                        else {\n                            this.selection = this.selection.filter((val, i) => i != index);\n                            this.selectionChange.emit(this.selection);\n                        }\n                        this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                    }\n                    else {\n                        if (this.isSingleSelectionMode()) {\n                            this.selectionChange.emit(node);\n                        }\n                        else if (this.isMultipleSelectionMode()) {\n                            this.selection = (!metaKey) ? [] : this.selection || [];\n                            this.selection = [...this.selection, node];\n                            this.selectionChange.emit(this.selection);\n                        }\n                        this.onNodeSelect.emit({ originalEvent: event, node: node });\n                    }\n                }\n                else {\n                    if (this.isSingleSelectionMode()) {\n                        if (selected) {\n                            this.selection = null;\n                            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                        }\n                        else {\n                            this.selection = node;\n                            this.onNodeSelect.emit({ originalEvent: event, node: node });\n                        }\n                    }\n                    else {\n                        if (selected) {\n                            this.selection = this.selection.filter((val, i) => i != index);\n                            this.onNodeUnselect.emit({ originalEvent: event, node: node });\n                        }\n                        else {\n                            this.selection = [...this.selection || [], node];\n                            this.onNodeSelect.emit({ originalEvent: event, node: node });\n                        }\n                    }\n                    this.selectionChange.emit(this.selection);\n                }\n            }\n        }\n        this.nodeTouched = false;\n    }\n    onNodeTouchEnd() {\n        this.nodeTouched = true;\n    }\n    onNodeRightClick(event, node) {\n        if (this.contextMenu) {\n            let eventTarget = event.target;\n            if (eventTarget.className && eventTarget.className.indexOf('p-tree-toggler') === 0) {\n                return;\n            }\n            else {\n                let index = this.findIndexInSelection(node);\n                let selected = (index >= 0);\n                if (!selected) {\n                    if (this.isSingleSelectionMode())\n                        this.selectionChange.emit(node);\n                    else\n                        this.selectionChange.emit([node]);\n                }\n                this.contextMenu.show(event);\n                this.onNodeContextMenuSelect.emit({ originalEvent: event, node: node });\n            }\n        }\n    }\n    findIndexInSelection(node) {\n        let index = -1;\n        if (this.selectionMode && this.selection) {\n            if (this.isSingleSelectionMode()) {\n                let areNodesEqual = (this.selection.key && this.selection.key === node.key) || this.selection == node;\n                index = areNodesEqual ? 0 : -1;\n            }\n            else {\n                for (let i = 0; i < this.selection.length; i++) {\n                    let selectedNode = this.selection[i];\n                    let areNodesEqual = (selectedNode.key && selectedNode.key === node.key) || selectedNode == node;\n                    if (areNodesEqual) {\n                        index = i;\n                        break;\n                    }\n                }\n            }\n        }\n        return index;\n    }\n    syncNodeOption(node, parentNodes, option, value) {\n        // to synchronize the node option between the filtered nodes and the original nodes(this.value)\n        const _node = this.hasFilteredNodes() ? this.getNodeWithKey(node.key, parentNodes) : null;\n        if (_node) {\n            _node[option] = value || node[option];\n        }\n    }\n    hasFilteredNodes() {\n        return this.filter && this.filteredNodes && this.filteredNodes.length;\n    }\n    getNodeWithKey(key, nodes) {\n        for (let node of nodes) {\n            if (node.key === key) {\n                return node;\n            }\n            if (node.children) {\n                let matchedNode = this.getNodeWithKey(key, node.children);\n                if (matchedNode) {\n                    return matchedNode;\n                }\n            }\n        }\n    }\n    propagateUp(node, select) {\n        if (node.children && node.children.length) {\n            let selectedCount = 0;\n            let childPartialSelected = false;\n            for (let child of node.children) {\n                if (this.isSelected(child)) {\n                    selectedCount++;\n                }\n                else if (child.partialSelected) {\n                    childPartialSelected = true;\n                }\n            }\n            if (select && selectedCount == node.children.length) {\n                this.selection = [...this.selection || [], node];\n                node.partialSelected = false;\n            }\n            else {\n                if (!select) {\n                    let index = this.findIndexInSelection(node);\n                    if (index >= 0) {\n                        this.selection = this.selection.filter((val, i) => i != index);\n                    }\n                }\n                if (childPartialSelected || selectedCount > 0 && selectedCount != node.children.length)\n                    node.partialSelected = true;\n                else\n                    node.partialSelected = false;\n            }\n            this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n        }\n        let parent = node.parent;\n        if (parent) {\n            this.propagateUp(parent, select);\n        }\n    }\n    propagateDown(node, select) {\n        let index = this.findIndexInSelection(node);\n        if (select && index == -1) {\n            this.selection = [...this.selection || [], node];\n        }\n        else if (!select && index > -1) {\n            this.selection = this.selection.filter((val, i) => i != index);\n        }\n        node.partialSelected = false;\n        this.syncNodeOption(node, this.filteredNodes, 'partialSelected');\n        if (node.children && node.children.length) {\n            for (let child of node.children) {\n                this.propagateDown(child, select);\n            }\n        }\n    }\n    isSelected(node) {\n        return this.findIndexInSelection(node) != -1;\n    }\n    isSingleSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'single';\n    }\n    isMultipleSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'multiple';\n    }\n    isCheckboxSelectionMode() {\n        return this.selectionMode && this.selectionMode == 'checkbox';\n    }\n    isNodeLeaf(node) {\n        return node.leaf == false ? false : !(node.children && node.children.length);\n    }\n    getRootNode() {\n        return this.filteredNodes ? this.filteredNodes : this.value;\n    }\n    getTemplateForNode(node) {\n        if (this.templateMap)\n            return node.type ? this.templateMap[node.type] : this.templateMap['default'];\n        else\n            return null;\n    }\n    onDragOver(event) {\n        if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n            event.dataTransfer.dropEffect = 'move';\n            event.preventDefault();\n        }\n    }\n    onDrop(event) {\n        if (this.droppableNodes && (!this.value || this.value.length === 0)) {\n            event.preventDefault();\n            let dragNode = this.dragNode;\n            if (this.allowDrop(dragNode, null, this.dragNodeScope)) {\n                let dragNodeIndex = this.dragNodeIndex;\n                this.value = this.value || [];\n                if (this.validateDrop) {\n                    this.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: null,\n                        index: dragNodeIndex,\n                        accept: () => {\n                            this.processTreeDrop(dragNode, dragNodeIndex);\n                        }\n                    });\n                }\n                else {\n                    this.onNodeDrop.emit({\n                        originalEvent: event,\n                        dragNode: dragNode,\n                        dropNode: null,\n                        index: dragNodeIndex\n                    });\n                    this.processTreeDrop(dragNode, dragNodeIndex);\n                }\n            }\n        }\n    }\n    processTreeDrop(dragNode, dragNodeIndex) {\n        this.dragNodeSubNodes.splice(dragNodeIndex, 1);\n        this.value.push(dragNode);\n        this.dragDropService.stopDrag({\n            node: dragNode\n        });\n    }\n    onDragEnter() {\n        if (this.droppableNodes && this.allowDrop(this.dragNode, null, this.dragNodeScope)) {\n            this.dragHover = true;\n        }\n    }\n    onDragLeave(event) {\n        if (this.droppableNodes) {\n            let rect = event.currentTarget.getBoundingClientRect();\n            if (event.x > rect.left + rect.width || event.x < rect.left || event.y > rect.top + rect.height || event.y < rect.top) {\n                this.dragHover = false;\n            }\n        }\n    }\n    allowDrop(dragNode, dropNode, dragNodeScope) {\n        if (!dragNode) {\n            //prevent random html elements to be dragged\n            return false;\n        }\n        else if (this.isValidDragScope(dragNodeScope)) {\n            let allow = true;\n            if (dropNode) {\n                if (dragNode === dropNode) {\n                    allow = false;\n                }\n                else {\n                    let parent = dropNode.parent;\n                    while (parent != null) {\n                        if (parent === dragNode) {\n                            allow = false;\n                            break;\n                        }\n                        parent = parent.parent;\n                    }\n                }\n            }\n            return allow;\n        }\n        else {\n            return false;\n        }\n    }\n    isValidDragScope(dragScope) {\n        let dropScope = this.droppableScope;\n        if (dropScope) {\n            if (typeof dropScope === 'string') {\n                if (typeof dragScope === 'string')\n                    return dropScope === dragScope;\n                else if (dragScope instanceof Array)\n                    return dragScope.indexOf(dropScope) != -1;\n            }\n            else if (dropScope instanceof Array) {\n                if (typeof dragScope === 'string') {\n                    return dropScope.indexOf(dragScope) != -1;\n                }\n                else if (dragScope instanceof Array) {\n                    for (let s of dropScope) {\n                        for (let ds of dragScope) {\n                            if (s === ds) {\n                                return true;\n                            }\n                        }\n                    }\n                }\n            }\n            return false;\n        }\n        else {\n            return true;\n        }\n    }\n    _filter(value) {\n        let filterValue = value;\n        if (filterValue === '') {\n            this.filteredNodes = null;\n        }\n        else {\n            this.filteredNodes = [];\n            const searchFields = this.filterBy.split(',');\n            const filterText = ObjectUtils.removeAccents(filterValue).toLocaleLowerCase(this.filterLocale);\n            const isStrictMode = this.filterMode === 'strict';\n            for (let node of this.value) {\n                let copyNode = Object.assign({}, node);\n                let paramsWithoutNode = { searchFields, filterText, isStrictMode };\n                if ((isStrictMode && (this.findFilteredNodes(copyNode, paramsWithoutNode) || this.isFilterMatched(copyNode, paramsWithoutNode))) ||\n                    (!isStrictMode && (this.isFilterMatched(copyNode, paramsWithoutNode) || this.findFilteredNodes(copyNode, paramsWithoutNode)))) {\n                    this.filteredNodes.push(copyNode);\n                }\n            }\n        }\n        this.updateSerializedValue();\n        this.onFilter.emit({\n            filter: filterValue,\n            filteredValue: this.filteredNodes\n        });\n    }\n    resetFilter() {\n        this.filteredNodes = null;\n        if (this.filterViewChild && this.filterViewChild.nativeElement) {\n            this.filterViewChild.nativeElement.value = '';\n        }\n    }\n    scrollToVirtualIndex(index) {\n        this.virtualScroll && this.scroller.scrollToIndex(index);\n    }\n    scrollTo(options) {\n        if (this.virtualScroll) {\n            this.scroller.scrollTo(options);\n        }\n        else if (this.wrapperViewChild && this.wrapperViewChild.nativeElement) {\n            if (this.wrapperViewChild.nativeElement.scrollTo) {\n                this.wrapperViewChild.nativeElement.scrollTo(options);\n            }\n            else {\n                this.wrapperViewChild.nativeElement.scrollLeft = options.left;\n                this.wrapperViewChild.nativeElement.scrollTop = options.top;\n            }\n        }\n    }\n    findFilteredNodes(node, paramsWithoutNode) {\n        if (node) {\n            let matched = false;\n            if (node.children) {\n                let childNodes = [...node.children];\n                node.children = [];\n                for (let childNode of childNodes) {\n                    let copyChildNode = Object.assign({}, childNode);\n                    if (this.isFilterMatched(copyChildNode, paramsWithoutNode)) {\n                        matched = true;\n                        node.children.push(copyChildNode);\n                    }\n                }\n            }\n            if (matched) {\n                node.expanded = true;\n                return true;\n            }\n        }\n    }\n    isFilterMatched(node, { searchFields, filterText, isStrictMode }) {\n        let matched = false;\n        for (let field of searchFields) {\n            let fieldValue = ObjectUtils.removeAccents(String(ObjectUtils.resolveFieldData(node, field))).toLocaleLowerCase(this.filterLocale);\n            if (fieldValue.indexOf(filterText) > -1) {\n                matched = true;\n            }\n        }\n        if (!matched || (isStrictMode && !this.isNodeLeaf(node))) {\n            matched = this.findFilteredNodes(node, { searchFields, filterText, isStrictMode }) || matched;\n        }\n        return matched;\n    }\n    getIndex(options, index) {\n        const getItemOptions = options['getItemOptions'];\n        return getItemOptions ? getItemOptions(index).index : index;\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    ngOnDestroy() {\n        if (this.dragStartSubscription) {\n            this.dragStartSubscription.unsubscribe();\n        }\n        if (this.dragStopSubscription) {\n            this.dragStopSubscription.unsubscribe();\n        }\n    }\n}\nTree.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tree, deps: [{ token: i0.ElementRef }, { token: i3.TreeDragDropService, optional: true }, { token: i3.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\nTree.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Tree, selector: \"p-tree\", inputs: { value: \"value\", selectionMode: \"selectionMode\", selection: \"selection\", style: \"style\", styleClass: \"styleClass\", contextMenu: \"contextMenu\", layout: \"layout\", draggableScope: \"draggableScope\", droppableScope: \"droppableScope\", draggableNodes: \"draggableNodes\", droppableNodes: \"droppableNodes\", metaKeySelection: \"metaKeySelection\", propagateSelectionUp: \"propagateSelectionUp\", propagateSelectionDown: \"propagateSelectionDown\", loading: \"loading\", loadingIcon: \"loadingIcon\", emptyMessage: \"emptyMessage\", ariaLabel: \"ariaLabel\", togglerAriaLabel: \"togglerAriaLabel\", ariaLabelledBy: \"ariaLabelledBy\", validateDrop: \"validateDrop\", filter: \"filter\", filterBy: \"filterBy\", filterMode: \"filterMode\", filterPlaceholder: \"filterPlaceholder\", filteredNodes: \"filteredNodes\", filterLocale: \"filterLocale\", scrollHeight: \"scrollHeight\", lazy: \"lazy\", virtualScroll: \"virtualScroll\", virtualScrollItemSize: \"virtualScrollItemSize\", virtualScrollOptions: \"virtualScrollOptions\", indentation: \"indentation\", trackBy: \"trackBy\", virtualNodeHeight: \"virtualNodeHeight\" }, outputs: { selectionChange: \"selectionChange\", onNodeSelect: \"onNodeSelect\", onNodeUnselect: \"onNodeUnselect\", onNodeExpand: \"onNodeExpand\", onNodeCollapse: \"onNodeCollapse\", onNodeContextMenuSelect: \"onNodeContextMenuSelect\", onNodeDrop: \"onNodeDrop\", onLazyLoad: \"onLazyLoad\", onScroll: \"onScroll\", onScrollIndexChange: \"onScrollIndexChange\", onFilter: \"onFilter\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"filterViewChild\", first: true, predicate: [\"filter\"], descendants: true }, { propertyName: \"scroller\", first: true, predicate: [\"scroller\"], descendants: true }, { propertyName: \"wrapperViewChild\", first: true, predicate: [\"wrapper\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div [ngClass]=\"{'p-tree p-component':true,'p-tree-selectable':selectionMode,\n                'p-treenode-dragover':dragHover,'p-tree-loading': loading, 'p-tree-flex-scrollable': scrollHeight === 'flex'}\"\n            [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"!horizontal\"\n            (drop)=\"onDrop($event)\" (dragover)=\"onDragOver($event)\" (dragenter)=\"onDragEnter()\" (dragleave)=\"onDragLeave($event)\">\n            <div class=\"p-tree-loading-overlay p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div *ngIf=\"filter\" class=\"p-tree-filter-container\">\n                <input #filter type=\"text\" autocomplete=\"off\" class=\"p-tree-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                    (keydown.enter)=\"$event.preventDefault()\" (input)=\"_filter($event.target.value)\">\n                    <span class=\"p-tree-filter-icon pi pi-search\"></span>\n            </div>\n\n            <p-scroller #scroller *ngIf=\"virtualScroll\" [items]=\"serializedValue\" styleClass=\"p-tree-wrapper\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_virtualNodeHeight\"\n                [lazy]=\"lazy\" (onScroll)=\"onScroll.emit($event)\" (onScrollIndexChange)=\"onScrollIndexChange.emit($event)\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                    <ul *ngIf=\"items\" class=\"p-tree-container\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let rowNode of items; let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [level]=\"rowNode.level\"\n                                    [rowNode]=\"rowNode\" [node]=\"rowNode.node\" [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"getIndex(scrollerOptions, index)\" [itemSize]=\"scrollerOptions.itemSize\" [indentation]=\"indentation\"></p-treeNode>\n                    </ul>\n                </ng-template>\n                <ng-container *ngIf=\"loaderTemplate\">\n                    <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                </ng-container>\n            </p-scroller>\n            <ng-container *ngIf=\"!virtualScroll\">\n                <div #wrapper class=\"p-tree-wrapper\" [style.max-height]=\"scrollHeight\">\n                    <ul class=\"p-tree-container\" *ngIf=\"getRootNode()\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let node of getRootNode(); let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [node]=\"node\"\n                                    [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [level]=\"0\"></p-treeNode>\n                    </ul>\n                </div>\n            </ng-container>\n\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n        <div [ngClass]=\"{'p-tree p-tree-horizontal p-component':true,'p-tree-selectable':selectionMode}\"  [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"horizontal\">\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div class=\"p-tree-loading-mask p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <table *ngIf=\"value&&value[0]\">\n                <p-treeNode [node]=\"value[0]\" [root]=\"true\"></p-treeNode>\n            </table>\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n    `, isInline: true, styles: [\".p-tree-container{margin:0;padding:0;list-style-type:none;overflow:auto}.p-treenode-children{margin:0;padding:0;list-style-type:none}.p-tree-wrapper{overflow:auto}.p-treenode-selectable{cursor:pointer;-webkit-user-select:none;user-select:none}.p-tree-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex-shrink:0}.p-treenode-leaf>.p-treenode-content .p-tree-toggler{visibility:hidden}.p-treenode-content{display:flex;align-items:center}.p-tree-filter{width:100%}.p-tree-filter-container{position:relative;display:block;width:100%}.p-tree-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-tree-loading{position:relative;min-height:4rem}.p-tree .p-tree-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-tree-flex-scrollable{display:flex;flex:1;height:100%;flex-direction:column}.p-tree-flex-scrollable .p-tree-wrapper{flex:1}.p-tree .p-treenode-droppoint{height:4px;list-style-type:none}.p-tree .p-treenode-droppoint-active{border:0 none}.p-tree-horizontal{width:auto;padding-left:0;padding-right:0;overflow:auto}.p-tree.p-tree-horizontal table,.p-tree.p-tree-horizontal tr,.p-tree.p-tree-horizontal td{border-collapse:collapse;margin:0;padding:0;vertical-align:middle}.p-tree-horizontal .p-treenode-content{font-weight:400;padding:.4em 1em .4em .2em;display:flex;align-items:center}.p-tree-horizontal .p-treenode-parent .p-treenode-content{font-weight:400;white-space:nowrap}.p-tree.p-tree-horizontal .p-treenode{background:url(data:image/gif;base64,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) repeat-x scroll center center transparent;padding:.25rem 2.5rem}.p-tree.p-tree-horizontal .p-treenode.p-treenode-leaf,.p-tree.p-tree-horizontal .p-treenode.p-treenode-collapsed{padding-right:0}.p-tree.p-tree-horizontal .p-treenode-children{padding:0;margin:0}.p-tree.p-tree-horizontal .p-treenode-connector{width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-table{height:100%;width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-line{background:url(data:image/gif;base64,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) repeat-y scroll 0 0 transparent;width:1px}.p-tree.p-tree-horizontal table{height:0}.p-scroller .p-tree-container{overflow:visible}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.PrimeTemplate, selector: \"[pTemplate]\", inputs: [\"type\", \"pTemplate\"] }, { kind: \"component\", type: i4.Scroller, selector: \"p-scroller\", inputs: [\"id\", \"style\", \"styleClass\", \"tabindex\", \"items\", \"itemSize\", \"scrollHeight\", \"scrollWidth\", \"orientation\", \"delay\", \"resizeDelay\", \"lazy\", \"disabled\", \"loaderDisabled\", \"columns\", \"showSpacer\", \"showLoader\", \"numToleratedItems\", \"loading\", \"autoSize\", \"trackBy\", \"options\"], outputs: [\"onLazyLoad\", \"onScroll\", \"onScrollIndexChange\"] }, { kind: \"component\", type: UITreeNode, selector: \"p-treeNode\", inputs: [\"rowNode\", \"node\", \"parentNode\", \"root\", \"index\", \"firstChild\", \"lastChild\", \"level\", \"indentation\", \"itemSize\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tree, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-tree', template: `\n        <div [ngClass]=\"{'p-tree p-component':true,'p-tree-selectable':selectionMode,\n                'p-treenode-dragover':dragHover,'p-tree-loading': loading, 'p-tree-flex-scrollable': scrollHeight === 'flex'}\"\n            [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"!horizontal\"\n            (drop)=\"onDrop($event)\" (dragover)=\"onDragOver($event)\" (dragenter)=\"onDragEnter()\" (dragleave)=\"onDragLeave($event)\">\n            <div class=\"p-tree-loading-overlay p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div *ngIf=\"filter\" class=\"p-tree-filter-container\">\n                <input #filter type=\"text\" autocomplete=\"off\" class=\"p-tree-filter p-inputtext p-component\" [attr.placeholder]=\"filterPlaceholder\"\n                    (keydown.enter)=\"$event.preventDefault()\" (input)=\"_filter($event.target.value)\">\n                    <span class=\"p-tree-filter-icon pi pi-search\"></span>\n            </div>\n\n            <p-scroller #scroller *ngIf=\"virtualScroll\" [items]=\"serializedValue\" styleClass=\"p-tree-wrapper\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_virtualNodeHeight\"\n                [lazy]=\"lazy\" (onScroll)=\"onScroll.emit($event)\" (onScrollIndexChange)=\"onScrollIndexChange.emit($event)\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                    <ul *ngIf=\"items\" class=\"p-tree-container\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let rowNode of items; let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [level]=\"rowNode.level\"\n                                    [rowNode]=\"rowNode\" [node]=\"rowNode.node\" [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"getIndex(scrollerOptions, index)\" [itemSize]=\"scrollerOptions.itemSize\" [indentation]=\"indentation\"></p-treeNode>\n                    </ul>\n                </ng-template>\n                <ng-container *ngIf=\"loaderTemplate\">\n                    <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                </ng-container>\n            </p-scroller>\n            <ng-container *ngIf=\"!virtualScroll\">\n                <div #wrapper class=\"p-tree-wrapper\" [style.max-height]=\"scrollHeight\">\n                    <ul class=\"p-tree-container\" *ngIf=\"getRootNode()\" role=\"tree\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\">\n                        <p-treeNode *ngFor=\"let node of getRootNode(); let firstChild=first;let lastChild=last; let index=index; trackBy: trackBy\" [node]=\"node\"\n                                    [firstChild]=\"firstChild\" [lastChild]=\"lastChild\" [index]=\"index\" [level]=\"0\"></p-treeNode>\n                    </ul>\n                </div>\n            </ng-container>\n\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n        <div [ngClass]=\"{'p-tree p-tree-horizontal p-component':true,'p-tree-selectable':selectionMode}\"  [ngStyle]=\"style\" [class]=\"styleClass\" *ngIf=\"horizontal\">\n            <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            <div class=\"p-tree-loading-mask p-component-overlay\" *ngIf=\"loading\">\n                <i [class]=\"'p-tree-loading-icon pi-spin ' + loadingIcon\"></i>\n            </div>\n            <table *ngIf=\"value&&value[0]\">\n                <p-treeNode [node]=\"value[0]\" [root]=\"true\"></p-treeNode>\n            </table>\n            <div class=\"p-tree-empty-message\" *ngIf=\"!loading && (getRootNode() == null || getRootNode().length === 0)\">\n                <ng-container *ngIf=\"!emptyMessageTemplate; else emptyFilter\">\n                    {{emptyMessageLabel}}\n                </ng-container>\n                <ng-container #emptyFilter *ngTemplateOutlet=\"emptyMessageTemplate\"></ng-container>\n            </div>\n            <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-tree-container{margin:0;padding:0;list-style-type:none;overflow:auto}.p-treenode-children{margin:0;padding:0;list-style-type:none}.p-tree-wrapper{overflow:auto}.p-treenode-selectable{cursor:pointer;-webkit-user-select:none;user-select:none}.p-tree-toggler{cursor:pointer;-webkit-user-select:none;user-select:none;display:inline-flex;align-items:center;justify-content:center;overflow:hidden;position:relative;flex-shrink:0}.p-treenode-leaf>.p-treenode-content .p-tree-toggler{visibility:hidden}.p-treenode-content{display:flex;align-items:center}.p-tree-filter{width:100%}.p-tree-filter-container{position:relative;display:block;width:100%}.p-tree-filter-icon{position:absolute;top:50%;margin-top:-.5rem}.p-tree-loading{position:relative;min-height:4rem}.p-tree .p-tree-loading-overlay{position:absolute;display:flex;align-items:center;justify-content:center;z-index:2}.p-tree-flex-scrollable{display:flex;flex:1;height:100%;flex-direction:column}.p-tree-flex-scrollable .p-tree-wrapper{flex:1}.p-tree .p-treenode-droppoint{height:4px;list-style-type:none}.p-tree .p-treenode-droppoint-active{border:0 none}.p-tree-horizontal{width:auto;padding-left:0;padding-right:0;overflow:auto}.p-tree.p-tree-horizontal table,.p-tree.p-tree-horizontal tr,.p-tree.p-tree-horizontal td{border-collapse:collapse;margin:0;padding:0;vertical-align:middle}.p-tree-horizontal .p-treenode-content{font-weight:400;padding:.4em 1em .4em .2em;display:flex;align-items:center}.p-tree-horizontal .p-treenode-parent .p-treenode-content{font-weight:400;white-space:nowrap}.p-tree.p-tree-horizontal .p-treenode{background:url(data:image/gif;base64,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) repeat-x scroll center center transparent;padding:.25rem 2.5rem}.p-tree.p-tree-horizontal .p-treenode.p-treenode-leaf,.p-tree.p-tree-horizontal .p-treenode.p-treenode-collapsed{padding-right:0}.p-tree.p-tree-horizontal .p-treenode-children{padding:0;margin:0}.p-tree.p-tree-horizontal .p-treenode-connector{width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-table{height:100%;width:1px}.p-tree.p-tree-horizontal .p-treenode-connector-line{background:url(data:image/gif;base64,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) repeat-y scroll 0 0 transparent;width:1px}.p-tree.p-tree-horizontal table{height:0}.p-scroller .p-tree-container{overflow:visible}\\n\"] }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i3.TreeDragDropService, decorators: [{\n                        type: Optional\n                    }] }, { type: i3.PrimeNGConfig }];\n    }, propDecorators: { value: [{\n                type: Input\n            }], selectionMode: [{\n                type: Input\n            }], selection: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], contextMenu: [{\n                type: Input\n            }], layout: [{\n                type: Input\n            }], draggableScope: [{\n                type: Input\n            }], droppableScope: [{\n                type: Input\n            }], draggableNodes: [{\n                type: Input\n            }], droppableNodes: [{\n                type: Input\n            }], metaKeySelection: [{\n                type: Input\n            }], propagateSelectionUp: [{\n                type: Input\n            }], propagateSelectionDown: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], loadingIcon: [{\n                type: Input\n            }], emptyMessage: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], togglerAriaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], validateDrop: [{\n                type: Input\n            }], filter: [{\n                type: Input\n            }], filterBy: [{\n                type: Input\n            }], filterMode: [{\n                type: Input\n            }], filterPlaceholder: [{\n                type: Input\n            }], filteredNodes: [{\n                type: Input\n            }], filterLocale: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], virtualScrollItemSize: [{\n                type: Input\n            }], virtualScrollOptions: [{\n                type: Input\n            }], indentation: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], onNodeSelect: [{\n                type: Output\n            }], onNodeUnselect: [{\n                type: Output\n            }], onNodeExpand: [{\n                type: Output\n            }], onNodeCollapse: [{\n                type: Output\n            }], onNodeContextMenuSelect: [{\n                type: Output\n            }], onNodeDrop: [{\n                type: Output\n            }], onLazyLoad: [{\n                type: Output\n            }], onScroll: [{\n                type: Output\n            }], onScrollIndexChange: [{\n                type: Output\n            }], onFilter: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], filterViewChild: [{\n                type: ViewChild,\n                args: ['filter']\n            }], scroller: [{\n                type: ViewChild,\n                args: ['scroller']\n            }], wrapperViewChild: [{\n                type: ViewChild,\n                args: ['wrapper']\n            }], virtualNodeHeight: [{\n                type: Input\n            }] } });\nclass TreeModule {\n}\nTreeModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTreeModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeModule, declarations: [Tree, UITreeNode], imports: [CommonModule, SharedModule, RippleModule, ScrollerModule], exports: [Tree, SharedModule, ScrollerModule] });\nTreeModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeModule, imports: [CommonModule, SharedModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TreeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, RippleModule, ScrollerModule],\n                    exports: [Tree, SharedModule, ScrollerModule],\n                    declarations: [Tree, UITreeNode]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tree, TreeModule, UITreeNode };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,SAArB,EAAgCC,iBAAhC,EAAmDC,MAAnD,EAA2DC,KAA3D,EAAkEC,YAAlE,EAAgFC,uBAAhF,EAAyGC,QAAzG,EAAmHC,MAAnH,EAA2HC,eAA3H,EAA4IC,SAA5I,EAAuJC,QAAvJ,QAAuK,eAAvK;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,eAAT,EAA0BC,aAA1B,EAAyCC,YAAzC,QAA6D,aAA7D;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,kBAApB;AACA,SAASC,cAAT,QAA+B,kBAA/B;;;;;;;;;;gBA8U6FxB,E;;IAAAA,EAGjF,2B;IAHiFA,EAIjF;MAJiFA,EAIjF;MAAA,eAJiFA,EAIjF;MAAA,OAJiFA,EAIzE,yCAAoB,CAApB,EAAR;IAAA;MAJiFA,EAIjF;MAAA,eAJiFA,EAIjF;MAAA,OAJiFA,EAIrC,gDAA5C;IAAA;MAJiFA,EAIjF;MAAA,eAJiFA,EAIjF;MAAA,OAJiFA,EAIK,kDAA6B,CAA7B,EAAtF;IAAA;MAJiFA,EAIjF;MAAA,eAJiFA,EAIjF;MAAA,OAJiFA,EAImD,iDAApI;IAAA,E;IAJiFA,EAIiF,e;;;;mBAJjFA,E;IAAAA,EAGpB,uBAHoBA,EAGpB,+C;;;;;;;;;;;;;;;;;;;;;;;;;;IAHoBA,EAczE,2C;IAdyEA,EAgBjE,yB;IAhBiEA,EAiBrE,iB;;;;oBAjBqEA,E;IAAAA,EAcrC,uBAdqCA,EAcrC,4D;IAdqCA,EAcmE,kD;IAdnEA,EAezC,a;IAfyCA,EAezC,uBAfyCA,EAezC,6E;IAfyCA,EAgBhC,a;IAhBgCA,EAgBhC,uBAhBgCA,EAgBhC,6E;;;;;;IAhBgCA,EAmBzE,qB;;;;oBAnByEA,E;IAAAA,EAmBnE,8B;;;;;;IAnBmEA,EAqBjE,0B;IArBiEA,EAqBpB,U;IArBoBA,EAqBN,e;;;;oBArBMA,E;IAAAA,EAqBpB,a;IArBoBA,EAqBpB,sC;;;;;;IArBoBA,EAuB7D,sB;;;;;;;;;;;;IAvB6DA,EAsBjE,0B;IAtBiEA,EAuB7D,sG;IAvB6DA,EAwBjE,e;;;;oBAxBiEA,E;IAAAA,EAuB9C,a;IAvB8CA,EAuB9C,0GAvB8CA,EAuB9C,uC;;;;;;IAvB8CA,EA4BzE,+B;;;;;;;;oBA5ByEA,E;IAAAA,EA4B2D,sM;;;;;;IA5B3DA,EA2B7E,4B;IA3B6EA,EA4BzE,gG;IA5ByEA,EA8B7E,e;;;;oBA9B6EA,E;IAAAA,EA2BwC,iE;IA3BxCA,EA4BvC,a;IA5BuCA,EA4BvC,mF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBA5BuCA,E;;IAAAA,EAKjF,wC;IALiFA,EAMQ;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAMiB,yCAAT;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAMqD,8CAA7C;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAM2F,sCAAnF;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAOjE,wCADyE;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAOjC,gDADyC;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAOQ,iDADA;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAOkD,iDAD1C;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAQ1B,yCAFkC;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAQM,wCAFE;IAAA;MANRA,EAMQ;MAAA,gBANRA,EAMQ;MAAA,OANRA,EAU9D,uCAJsE;IAAA,E;IANRA,EAWzE,+B;IAXyEA,EAWqB;MAXrBA,EAWqB;MAAA,gBAXrBA,EAWqB;MAAA,OAXrBA,EAW8B,oCAAT;IAAA,E;IAXrBA,EAYrE,wB;IAZqEA,EAazE,e;IAbyEA,EAczE,6E;IAdyEA,EAmBzE,8E;IAnByEA,EAoBzE,8B;IApByEA,EAqBjE,+E;IArBiEA,EAsBjE,+E;IAtBiEA,EAyBzE,iB;IAzByEA,EA2B7E,2E;IA3B6EA,EA+BjF,e;;;;mBA/BiFA,E;IAAAA,EAKpD,uBALoDA,EAKpD,8GALoDA,EAKpD,kD;IALoDA,EAM7C,a;IAN6CA,EAM7C,uE;IAN6CA,EAQzE,gEARyEA,EAQzE,oI;IARyEA,EAQ0B,4K;IAR1BA,EAWnD,a;IAXmDA,EAWnD,wD;IAXmDA,EAY1B,a;IAZ0BA,EAY1B,uBAZ0BA,EAY1B,uE;IAZ0BA,EAc2B,a;IAd3BA,EAc2B,4D;IAd3BA,EAmB9C,a;IAnB8CA,EAmB9C,8F;IAnB8CA,EAqB1D,a;IArB0DA,EAqB1D,iE;IArB0DA,EAsB1D,a;IAtB0DA,EAsB1D,gE;IAtB0DA,EA2BrB,a;IA3BqBA,EA2BrB,+F;;;;;;iBA3BqBA,E;;IAAAA,EAgCjF,2B;IAhCiFA,EAiCjF;MAjCiFA,EAiCjF;MAAA,gBAjCiFA,EAiCjF;MAAA,OAjCiFA,EAiCzE,yCAAmB,CAAnB,EAAR;IAAA;MAjCiFA,EAiCjF;MAAA,gBAjCiFA,EAiCjF;MAAA,OAjCiFA,EAiCtC,iDAA3C;IAAA;MAjCiFA,EAiCjF;MAAA,gBAjCiFA,EAiCjF;MAAA,OAjCiFA,EAiCI,kDAA4B,CAA5B,EAArF;IAAA;MAjCiFA,EAiCjF;MAAA,gBAjCiFA,EAiCjF;MAAA,OAjCiFA,EAiCiD,kDAAlI;IAAA,E;IAjCiFA,EAiC+E,e;;;;mBAjC/EA,E;IAAAA,EAgCT,uBAhCSA,EAgCT,+C;;;;;;;;;;;;IAhCSA,EAqCrE,iE;IArCqEA,EAyCrD,uB;IAzCqDA,EA0CzD,e;IA1CyDA,EA2CzD,wB;IA3CyDA,EA4CrD,uB;IA5CqDA,EA6CzD,qB;;;;oBA7CyDA,E;IAAAA,EAyCjD,a;IAzCiDA,EAyCjD,uBAzCiDA,EAyCjD,8C;IAzCiDA,EA4CjD,a;IA5CiDA,EA4CjD,uBA5CiDA,EA4CjD,6C;;;;;;;;;;;;;iBA5CiDA,E;;IAAAA,EAoD7D,8B;IApD6DA,EAoDqG;MApDrGA,EAoDqG;MAAA,gBApDrGA,EAoDqG;MAAA,OApDrGA,EAoD8G,oCAAT;IAAA,E;IApDrGA,EAoD8H,e;;;;oBApD9HA,E;IAAAA,EAoDmB,uBApDnBA,EAoDmB,yE;IApDnBA,EAoDvD,yD;;;;;;IApDuDA,EAqD7D,qB;;;;oBArD6DA,E;IAAAA,EAqDvD,8B;;;;;;IArDuDA,EAuDzD,0B;IAvDyDA,EAuDZ,U;IAvDYA,EAuDE,e;;;;oBAvDFA,E;IAAAA,EAuDZ,a;IAvDYA,EAuDZ,sC;;;;;;IAvDYA,EAyDrD,sB;;;;;;IAzDqDA,EAwDzD,0B;IAxDyDA,EAyDrD,0G;IAzDqDA,EA0DzD,e;;;;oBA1DyDA,E;IAAAA,EAyDtC,a;IAzDsCA,EAyDtC,0GAzDsCA,EAyDtC,uC;;;;;;IAzDsCA,EAgE7D,+B;;;;;;;IAhE6DA,EAgEsD,4F;;;;;;IAhEtDA,EA8DrE,0C;IA9DqEA,EAgE7D,oG;IAhE6DA,EAkEjE,iB;;;;oBAlEiEA,E;IAAAA,EA8DY,sE;IA9DZA,EAgE3B,a;IAhE2BA,EAgE3B,mF;;;;;;;;;;;;;;;;;;;iBAhE2BA,E;;IAAAA,EAkCjF,gD;IAlCiFA,EAqCrE,8E;IArCqEA,EAiDrE,0C;IAjDqEA,EAkDgE;MAlDhEA,EAkDgE;MAAA,gBAlDhEA,EAkDgE;MAAA,OAlDhEA,EAkDyE,yCAAT;IAAA;MAlDhEA,EAkDgE;MAAA,gBAlDhEA,EAkDgE;MAAA,OAlDhEA,EAkD6G,8CAA7C;IAAA;MAlDhEA,EAkDgE;MAAA,gBAlDhEA,EAkDgE;MAAA,OAlDhEA,EAmDjD,sCADiH;IAAA;MAlDhEA,EAkDgE;MAAA,gBAlDhEA,EAkDgE;MAAA,OAlDhEA,EAmDpB,2CADoF;IAAA,E;IAlDhEA,EAoD7D,kF;IApD6DA,EAqD7D,iF;IArD6DA,EAsD7D,8B;IAtD6DA,EAuDzD,kF;IAvDyDA,EAwDzD,oF;IAxDyDA,EA2D7D,mB;IA3D6DA,EA8DrE,gF;IA9DqEA,EAoEzE,mB;;;;mBApEyEA,E;IAAAA,EAkClD,mC;IAlCkDA,EAqCnC,a;IArCmCA,EAqCnC,iC;IArCmCA,EAiD9C,a;IAjD8CA,EAiD9C,uBAjD8CA,EAiD9C,kD;IAjD8CA,EAkDpB,a;IAlDoBA,EAkDpB,uBAlDoBA,EAkDpB,2E;IAlDoBA,EAoDoF,a;IApDpFA,EAoDoF,qC;IApDpFA,EAqDlC,a;IArDkCA,EAqDlC,8F;IArDkCA,EAuDlD,a;IAvDkDA,EAuDlD,iE;IAvDkDA,EAwDlD,a;IAxDkDA,EAwDlD,gE;IAxDkDA,EA8D1B,a;IA9D0BA,EA8D1B,iE;;;;;;IA9D0BA,EAGjF,qE;IAHiFA,EAKjF,uE;IALiFA,EAgCjF,qE;IAhCiFA,EAkCjF,6E;;;;mBAlCiFA,E;IAAAA,EAG5E,+C;IAH4EA,EAK5E,a;IAL4EA,EAK5E,4C;IAL4EA,EAgC5E,a;IAhC4EA,EAgC5E,mE;IAhC4EA,EAkCzE,a;IAlCyEA,EAkCzE,2C;;;;;;;;;;IAlCyEA,EAquBjF,4B;IAruBiFA,EAsuB7E,kB;IAtuB6EA,EAuuBjF,e;;;;mBAvuBiFA,E;IAAAA,EAsuB1E,a;IAtuB0EA,EAsuB1E,gE;;;;;;IAtuB0EA,EAwuBjF,sB;;;;;;iBAxuBiFA,E;;IAAAA,EAyuBjF,iD;IAzuBiFA,EA2uBzE;MAAA,OAAiB,uBAAjB;IAAA;MA3uByEA,EA2uBzE;MAAA,gBA3uByEA,EA2uBzE;MAAA,OA3uByEA,EA2uBtB,kDAAnD;IAAA,E;IA3uByEA,EA0uB7E,e;IA1uB6EA,EA4uBzE,yB;IA5uByEA,EA6uBjF,e;;;;mBA7uBiFA,E;IAAAA,EA0uBe,a;IA1uBfA,EA0uBe,qD;;;;;;IA1uBfA,EAmvBrE,+B;;;;;;;;gCAnvBqEA,E;oBAAAA,E;IAAAA,EAmvBiD,4R;;;;;;IAnvBjDA,EAkvBzE,4B;IAlvByEA,EAmvBrE,wG;IAnvBqEA,EAqvBzE,e;;;;oBArvByEA,E;;;oBAAAA,E;IAAAA,EAkvBgB,6C;IAlvBhBA,EAkvB9B,6D;IAlvB8BA,EAkvBmE,wF;IAlvBnEA,EAmvBrC,a;IAnvBqCA,EAmvBrC,kE;;;;;;IAnvBqCA,EAkvBzE,mF;;;;;IAlvByEA,EAkvBpE,8B;;;;;;IAlvBoEA,EAyvBrE,sB;;;;;;;;;;;;IAzvBqEA,EAyvBrE,sH;;;;;oBAzvBqEA,E;IAAAA,EAyvBtD,mFAzvBsDA,EAyvBtD,+C;;;;;;IAzvBsDA,EAuvB7E,2B;IAvvB6EA,EAwvBzE,sG;IAxvByEA,EA2vB7E,wB;;;;;;iBA3vB6EA,E;;IAAAA,EA+uBjF,wC;IA/uBiFA,EAgvB/D;MAhvB+DA,EAgvB/D;MAAA,gBAhvB+DA,EAgvB/D;MAAA,OAhvB+DA,EAgvBnD,2CAAZ;IAAA;MAhvB+DA,EAgvB/D;MAAA,gBAhvB+DA,EAgvB/D;MAAA,OAhvB+DA,EAgvBL,sDAA1D;IAAA;MAhvB+DA,EAgvB/D;MAAA,gBAhvB+DA,EAgvB/D;MAAA,OAhvB+DA,EAgvB2C,6CAA1G;IAAA,E;IAhvB+DA,EAivB7E,uF;IAjvB6EA,EAuvB7E,wF;IAvvB6EA,EA4vBjF,e;;;;mBA5vBiFA,E;IAAAA,EA+uBiB,YA/uBjBA,EA+uBiB,8C;IA/uBjBA,EA+uBrC,gL;IA/uBqCA,EAuvB9D,a;IAvvB8DA,EAuvB9D,0C;;;;;;IAvvB8DA,EAgwBrE,+B;;;;;;;;IAhwBqEA,EAgwBsD,uH;;;;;;IAhwBtDA,EA+vBzE,4B;IA/vByEA,EAgwBrE,4F;IAhwBqEA,EAkwBzE,e;;;;oBAlwByEA,E;IAAAA,EA+vBV,wF;IA/vBUA,EAgwBxC,a;IAhwBwCA,EAgwBxC,8E;;;;;;IAhwBwCA,EA6vBjF,2B;IA7vBiFA,EA8vB7E,iC;IA9vB6EA,EA+vBzE,uE;IA/vByEA,EAmwB7E,e;IAnwB6EA,EAowBjF,wB;;;;mBApwBiFA,E;IAAAA,EA8vBxC,a;IA9vBwCA,EA8vBxC,+C;IA9vBwCA,EA+vB3C,a;IA/vB2CA,EA+vB3C,yC;;;;;;IA/vB2CA,EAuwB7E,2B;IAvwB6EA,EAwwBzE,U;IAxwByEA,EAywB7E,wB;;;;oBAzwB6EA,E;IAAAA,EAwwBzE,a;IAxwByEA,EAwwBzE,wD;;;;;;IAxwByEA,EA0wB7E,gC;;;;;;IA1wB6EA,EAswBjF,6B;IAtwBiFA,EAuwB7E,kF;IAvwB6EA,EA0wB7E,iF;IA1wB6EA,EA2wBjF,e;;;;mBA3wBiFA,E;IAAAA,EAuwB9D,a;IAvwB8DA,EAuwB9D,iF;IAvwB8DA,EA0wBjD,a;IA1wBiDA,EA0wBjD,4D;;;;;;IA1wBiDA,EA4wBjF,sB;;;;;;;;;;;;;;;;iBA5wBiFA,E;;IAAAA,EAiuBrF,4B;IAjuBqFA,EAouBjF;MApuBiFA,EAouBjF;MAAA,gBApuBiFA,EAouBjF;MAAA,OApuBiFA,EAouBzE,oCAAR;IAAA;MApuBiFA,EAouBjF;MAAA,gBApuBiFA,EAouBjF;MAAA,OApuBiFA,EAouB7C,wCAApC;IAAA;MApuBiFA,EAouBjF;MAAA,gBApuBiFA,EAouBjF;MAAA,OApuBiFA,EAouBZ,mCAArE;IAAA;MApuBiFA,EAouBjF;MAAA,gBApuBiFA,EAouBjF;MAAA,OApuBiFA,EAouBgB,yCAAjG;IAAA,E;IApuBiFA,EAquBjF,yD;IAruBiFA,EAwuBjF,2E;IAxuBiFA,EAyuBjF,yD;IAzuBiFA,EA+uBjF,uE;IA/uBiFA,EA6vBjF,2E;IA7vBiFA,EAswBjF,yD;IAtwBiFA,EA4wBjF,2E;IA5wBiFA,EA6wBrF,e;;;;mBA7wBqFA,E;IAAAA,EAmuB/D,8B;IAnuB+DA,EAiuBhF,uBAjuBgFA,EAiuBhF,4I;IAjuBgFA,EAquBxB,a;IAruBwBA,EAquBxB,mC;IAruBwBA,EAwuBlE,a;IAxuBkEA,EAwuBlE,sD;IAxuBkEA,EAyuB3E,a;IAzuB2EA,EAyuB3E,kC;IAzuB2EA,EA+uB1D,a;IA/uB0DA,EA+uB1D,yC;IA/uB0DA,EA6vBlE,a;IA7vBkEA,EA6vBlE,0C;IA7vBkEA,EAswB9C,a;IAtwB8CA,EAswB9C,2G;IAtwB8CA,EA4wBlE,a;IA5wBkEA,EA4wBlE,sD;;;;;;IA5wBkEA,EA+wBjF,sB;;;;;;IA/wBiFA,EAgxBjF,6B;IAhxBiFA,EAixB7E,kB;IAjxB6EA,EAkxBjF,e;;;;oBAlxBiFA,E;IAAAA,EAixB1E,a;IAjxB0EA,EAixB1E,iE;;;;;;IAjxB0EA,EAmxBjF,2B;IAnxBiFA,EAoxB7E,+B;IApxB6EA,EAqxBjF,e;;;;oBArxBiFA,E;IAAAA,EAoxBjE,a;IApxBiEA,EAoxBjE,mD;;;;;;IApxBiEA,EAuxB7E,2B;IAvxB6EA,EAwxBzE,U;IAxxByEA,EAyxB7E,wB;;;;oBAzxB6EA,E;IAAAA,EAwxBzE,a;IAxxByEA,EAwxBzE,wD;;;;;;IAxxByEA,EA0xB7E,gC;;;;;;IA1xB6EA,EAsxBjF,6B;IAtxBiFA,EAuxB7E,kF;IAvxB6EA,EA0xB7E,iF;IA1xB6EA,EA2xBjF,e;;;;oBA3xBiFA,E;IAAAA,EAuxB9D,a;IAvxB8DA,EAuxB9D,mF;IAvxB8DA,EA0xBjD,a;IA1xBiDA,EA0xBjD,6D;;;;;;IA1xBiDA,EA4xBjF,sB;;;;;;;;;;;;;IA5xBiFA,EA8wBrF,6B;IA9wBqFA,EA+wBjF,2E;IA/wBiFA,EAgxBjF,0D;IAhxBiFA,EAmxBjF,6D;IAnxBiFA,EAsxBjF,yD;IAtxBiFA,EA4xBjF,2E;IA5xBiFA,EA6xBrF,e;;;;mBA7xBqFA,E;IAAAA,EA8wB+B,8B;IA9wB/BA,EA8wBhF,uBA9wBgFA,EA8wBhF,yE;IA9wBgFA,EA+wBlE,a;IA/wBkEA,EA+wBlE,sD;IA/wBkEA,EAgxB3B,a;IAhxB2BA,EAgxB3B,mC;IAhxB2BA,EAmxBzE,a;IAnxByEA,EAmxBzE,oD;IAnxByEA,EAsxB9C,a;IAtxB8CA,EAsxB9C,2G;IAtxB8CA,EA4xBlE,a;IA5xBkEA,EA4xBlE,sD;;;;AAxmC3B,MAAMyB,UAAN,CAAiB;EACbC,WAAW,CAACC,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACDC,QAAQ,GAAG;IACP,KAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAKC,UAAxB;;IACA,IAAI,KAAKA,UAAT,EAAqB;MACjB,KAAKJ,IAAL,CAAUK,cAAV,CAAyB,KAAKH,IAA9B,EAAoC,KAAKF,IAAL,CAAUM,KAA9C,EAAqD,QAArD,EAA+D,KAAKN,IAAL,CAAUO,cAAV,CAAyB,KAAKH,UAAL,CAAgBI,GAAzC,EAA8C,KAAKR,IAAL,CAAUM,KAAxD,CAA/D;IACH;EACJ;;EACDG,OAAO,GAAG;IACN,IAAIC,IAAJ;IACA,IAAI,KAAKR,IAAL,CAAUQ,IAAd,EACIA,IAAI,GAAG,KAAKR,IAAL,CAAUQ,IAAjB,CADJ,KAGIA,IAAI,GAAG,KAAKR,IAAL,CAAUS,QAAV,IAAsB,KAAKT,IAAL,CAAUU,QAAhC,IAA4C,KAAKV,IAAL,CAAUU,QAAV,CAAmBC,MAA/D,GAAwE,KAAKX,IAAL,CAAUY,YAAlF,GAAiG,KAAKZ,IAAL,CAAUa,aAAlH;IACJ,OAAOjB,UAAU,CAACkB,UAAX,GAAwB,GAAxB,GAA8BN,IAArC;EACH;;EACDO,MAAM,GAAG;IACL,OAAO,KAAKjB,IAAL,CAAUkB,UAAV,CAAqB,KAAKhB,IAA1B,CAAP;EACH;;EACDiB,MAAM,CAACC,KAAD,EAAQ;IACV,IAAI,KAAKlB,IAAL,CAAUS,QAAd,EACI,KAAKU,QAAL,CAAcD,KAAd,EADJ,KAGI,KAAKE,MAAL,CAAYF,KAAZ;EACP;;EACDE,MAAM,CAACF,KAAD,EAAQ;IACV,KAAKlB,IAAL,CAAUS,QAAV,GAAqB,IAArB;;IACA,IAAI,KAAKX,IAAL,CAAUuB,aAAd,EAA6B;MACzB,KAAKvB,IAAL,CAAUwB,qBAAV;IACH;;IACD,KAAKxB,IAAL,CAAUyB,YAAV,CAAuBC,IAAvB,CAA4B;MAAEC,aAAa,EAAEP,KAAjB;MAAwBlB,IAAI,EAAE,KAAKA;IAAnC,CAA5B;EACH;;EACDmB,QAAQ,CAACD,KAAD,EAAQ;IACZ,KAAKlB,IAAL,CAAUS,QAAV,GAAqB,KAArB;;IACA,IAAI,KAAKX,IAAL,CAAUuB,aAAd,EAA6B;MACzB,KAAKvB,IAAL,CAAUwB,qBAAV;IACH;;IACD,KAAKxB,IAAL,CAAU4B,cAAV,CAAyBF,IAAzB,CAA8B;MAAEC,aAAa,EAAEP,KAAjB;MAAwBlB,IAAI,EAAE,KAAKA;IAAnC,CAA9B;EACH;;EACD2B,WAAW,CAACT,KAAD,EAAQ;IACf,KAAKpB,IAAL,CAAU6B,WAAV,CAAsBT,KAAtB,EAA6B,KAAKlB,IAAlC;EACH;;EACD4B,aAAa,CAACV,KAAD,EAAQ;IACjB,IAAIA,KAAK,CAACW,KAAN,KAAgB,EAApB,EAAwB;MACpB,KAAK/B,IAAL,CAAU6B,WAAV,CAAsBT,KAAtB,EAA6B,KAAKlB,IAAlC;IACH;EACJ;;EACD8B,cAAc,GAAG;IACb,KAAKhC,IAAL,CAAUgC,cAAV;EACH;;EACDC,gBAAgB,CAACb,KAAD,EAAQ;IACpB,KAAKpB,IAAL,CAAUiC,gBAAV,CAA2Bb,KAA3B,EAAkC,KAAKlB,IAAvC;EACH;;EACDgC,UAAU,GAAG;IACT,OAAO,KAAKlC,IAAL,CAAUkC,UAAV,CAAqB,KAAKhC,IAA1B,CAAP;EACH;;EACDiC,WAAW,CAACf,KAAD,EAAQgB,QAAR,EAAkB;IACzBhB,KAAK,CAACiB,cAAN;IACA,IAAIC,QAAQ,GAAG,KAAKtC,IAAL,CAAUsC,QAAzB;IACA,IAAIC,aAAa,GAAG,KAAKvC,IAAL,CAAUuC,aAA9B;IACA,IAAIC,aAAa,GAAG,KAAKxC,IAAL,CAAUwC,aAA9B;IACA,IAAIC,qBAAqB,GAAG,KAAKzC,IAAL,CAAU0C,YAAV,KAA2B,KAAK1C,IAAhC,GAAwCoC,QAAQ,KAAK,CAAb,IAAkBG,aAAa,KAAK,KAAKI,KAAL,GAAa,CAAzF,GAA8F,IAA1H;;IACA,IAAI,KAAK3C,IAAL,CAAU4C,SAAV,CAAoBN,QAApB,EAA8B,KAAKpC,IAAnC,EAAyCsC,aAAzC,KAA2DC,qBAA/D,EAAsF;MAClF,IAAII,UAAU,GAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKC,4BAAL,CAAkCZ,QAAlC,CAAlB,CAAjB;;MACA,IAAI,KAAKpC,IAAL,CAAUiD,YAAd,EAA4B;QACxB,KAAKjD,IAAL,CAAUkD,UAAV,CAAqBxB,IAArB,CAA0B;UACtBC,aAAa,EAAEP,KADO;UAEtBkB,QAAQ,EAAEA,QAFY;UAGtBa,QAAQ,EAAE,KAAKjD,IAHO;UAItByC,KAAK,EAAE,KAAKA,KAJU;UAKtBS,MAAM,EAAE,MAAM;YACV,KAAKC,gBAAL,CAAsBR,UAAtB;UACH;QAPqB,CAA1B;MASH,CAVD,MAWK;QACD,KAAKQ,gBAAL,CAAsBR,UAAtB;QACA,KAAK7C,IAAL,CAAUkD,UAAV,CAAqBxB,IAArB,CAA0B;UACtBC,aAAa,EAAEP,KADO;UAEtBkB,QAAQ,EAAEA,QAFY;UAGtBa,QAAQ,EAAE,KAAKjD,IAHO;UAItByC,KAAK,EAAE,KAAKA;QAJU,CAA1B;MAMH;IACJ;;IACD,KAAKW,aAAL,GAAqB,KAArB;IACA,KAAKC,aAAL,GAAqB,KAArB;EACH;;EACDF,gBAAgB,CAACjC,KAAD,EAAQ;IACpB,IAAIoC,WAAW,GAAGpC,KAAK,CAAC+B,QAAN,CAAehD,MAAf,GAAwBiB,KAAK,CAAC+B,QAAN,CAAehD,MAAf,CAAsBS,QAA9C,GAAyD,KAAKZ,IAAL,CAAUM,KAArF;IACAc,KAAK,CAACqC,gBAAN,CAAuBC,MAAvB,CAA8BtC,KAAK,CAACmB,aAApC,EAAmD,CAAnD;IACA,IAAIoB,SAAS,GAAG,KAAKhB,KAArB;;IACA,IAAIvB,KAAK,CAACgB,QAAN,GAAiB,CAArB,EAAwB;MACpBuB,SAAS,GAAIvC,KAAK,CAACqC,gBAAN,KAA2BD,WAA5B,GAA6CpC,KAAK,CAACmB,aAAN,GAAsBnB,KAAK,CAACuB,KAA7B,GAAsCvB,KAAK,CAACuB,KAA5C,GAAoDvB,KAAK,CAACuB,KAAN,GAAc,CAA9G,GAAmHvB,KAAK,CAACuB,KAArI;MACAa,WAAW,CAACE,MAAZ,CAAmBC,SAAnB,EAA8B,CAA9B,EAAiCvC,KAAK,CAACkB,QAAvC;IACH,CAHD,MAIK;MACDqB,SAAS,GAAGH,WAAW,CAAC3C,MAAxB;MACA2C,WAAW,CAACI,IAAZ,CAAiBxC,KAAK,CAACkB,QAAvB;IACH;;IACD,KAAKtC,IAAL,CAAU6D,eAAV,CAA0BC,QAA1B,CAAmC;MAC/B5D,IAAI,EAAEkB,KAAK,CAACkB,QADmB;MAE/ByB,QAAQ,EAAE3C,KAAK,CAAC+B,QAAN,CAAehD,MAAf,GAAwBiB,KAAK,CAAC+B,QAAN,CAAehD,MAAf,CAAsBS,QAA9C,GAAyD,KAAKZ,IAAL,CAAUM,KAF9C;MAG/BqC,KAAK,EAAEvB,KAAK,CAACmB;IAHkB,CAAnC;EAKH;;EACDS,4BAA4B,CAACZ,QAAD,EAAW;IACnC,OAAO;MACHE,QAAQ,EAAE,KAAKtC,IAAL,CAAUsC,QADjB;MAEHC,aAAa,EAAE,KAAKvC,IAAL,CAAUuC,aAFtB;MAGHkB,gBAAgB,EAAE,KAAKzD,IAAL,CAAUyD,gBAHzB;MAIHN,QAAQ,EAAE,KAAKjD,IAJZ;MAKHyC,KAAK,EAAE,KAAKA,KALT;MAMHP,QAAQ,EAAEA;IANP,CAAP;EAQH;;EACD4B,mBAAmB,CAAC5C,KAAD,EAAQ;IACvBA,KAAK,CAAC6C,YAAN,CAAmBC,UAAnB,GAAgC,MAAhC;IACA9C,KAAK,CAACiB,cAAN;EACH;;EACD8B,oBAAoB,CAAC/C,KAAD,EAAQgB,QAAR,EAAkB;IAClC,IAAI,KAAKpC,IAAL,CAAU4C,SAAV,CAAoB,KAAK5C,IAAL,CAAUsC,QAA9B,EAAwC,KAAKpC,IAA7C,EAAmD,KAAKF,IAAL,CAAUwC,aAA7D,CAAJ,EAAiF;MAC7E,IAAIJ,QAAQ,GAAG,CAAf,EACI,KAAKkB,aAAL,GAAqB,IAArB,CADJ,KAGI,KAAKC,aAAL,GAAqB,IAArB;IACP;EACJ;;EACDa,oBAAoB,CAAChD,KAAD,EAAQ;IACxB,KAAKkC,aAAL,GAAqB,KAArB;IACA,KAAKC,aAAL,GAAqB,KAArB;EACH;;EACDc,WAAW,CAACjD,KAAD,EAAQ;IACf,IAAI,KAAKpB,IAAL,CAAUsE,cAAV,IAA4B,KAAKpE,IAAL,CAAUqE,SAAV,KAAwB,KAAxD,EAA+D;MAC3DnD,KAAK,CAAC6C,YAAN,CAAmBO,OAAnB,CAA2B,MAA3B,EAAmC,MAAnC;MACA,KAAKxE,IAAL,CAAU6D,eAAV,CAA0BY,SAA1B,CAAoC;QAChCzE,IAAI,EAAE,IAD0B;QAEhCE,IAAI,EAAE,KAAKA,IAFqB;QAGhC6D,QAAQ,EAAE,KAAK7D,IAAL,CAAUC,MAAV,GAAmB,KAAKD,IAAL,CAAUC,MAAV,CAAiBS,QAApC,GAA+C,KAAKZ,IAAL,CAAUM,KAHnC;QAIhCqC,KAAK,EAAE,KAAKA,KAJoB;QAKhC+B,KAAK,EAAE,KAAK1E,IAAL,CAAU2E;MALe,CAApC;IAOH,CATD,MAUK;MACDvD,KAAK,CAACiB,cAAN;IACH;EACJ;;EACDuC,UAAU,CAACxD,KAAD,EAAQ;IACd,KAAKpB,IAAL,CAAU6D,eAAV,CAA0BC,QAA1B,CAAmC;MAC/B5D,IAAI,EAAE,KAAKA,IADoB;MAE/B6D,QAAQ,EAAE,KAAK7D,IAAL,CAAUC,MAAV,GAAmB,KAAKD,IAAL,CAAUC,MAAV,CAAiBS,QAApC,GAA+C,KAAKZ,IAAL,CAAUM,KAFpC;MAG/BqC,KAAK,EAAE,KAAKA;IAHmB,CAAnC;EAKH;;EACDkC,kBAAkB,CAACzD,KAAD,EAAQ;IACtBA,KAAK,CAAC6C,YAAN,CAAmBC,UAAnB,GAAgC,MAAhC;;IACA,IAAI,KAAKlE,IAAL,CAAU8E,cAAd,EAA8B;MAC1B1D,KAAK,CAACiB,cAAN;MACAjB,KAAK,CAAC2D,eAAN;IACH;EACJ;;EACDC,UAAU,CAAC5D,KAAD,EAAQ;IACd,IAAI,KAAKpB,IAAL,CAAU8E,cAAV,IAA4B,KAAK5E,IAAL,CAAU+E,SAAV,KAAwB,KAAxD,EAA+D;MAC3D,IAAI3C,QAAQ,GAAG,KAAKtC,IAAL,CAAUsC,QAAzB;;MACA,IAAI,KAAKtC,IAAL,CAAU4C,SAAV,CAAoBN,QAApB,EAA8B,KAAKpC,IAAnC,EAAyC,KAAKF,IAAL,CAAUwC,aAAnD,CAAJ,EAAuE;QACnE,IAAIK,UAAU,GAAGC,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKmC,2BAAL,EAAlB,CAAjB;;QACA,IAAI,KAAKlF,IAAL,CAAUiD,YAAd,EAA4B;UACxB,KAAKjD,IAAL,CAAUkD,UAAV,CAAqBxB,IAArB,CAA0B;YACtBC,aAAa,EAAEP,KADO;YAEtBkB,QAAQ,EAAEA,QAFY;YAGtBa,QAAQ,EAAE,KAAKjD,IAHO;YAItByC,KAAK,EAAE,KAAKA,KAJU;YAKtBS,MAAM,EAAE,MAAM;cACV,KAAK+B,eAAL,CAAqBtC,UAArB;YACH;UAPqB,CAA1B;QASH,CAVD,MAWK;UACD,KAAKsC,eAAL,CAAqBtC,UAArB;UACA,KAAK7C,IAAL,CAAUkD,UAAV,CAAqBxB,IAArB,CAA0B;YACtBC,aAAa,EAAEP,KADO;YAEtBkB,QAAQ,EAAEA,QAFY;YAGtBa,QAAQ,EAAE,KAAKjD,IAHO;YAItByC,KAAK,EAAE,KAAKA;UAJU,CAA1B;QAMH;MACJ;IACJ;;IACDvB,KAAK,CAACiB,cAAN;IACAjB,KAAK,CAAC2D,eAAN;IACA,KAAKK,aAAL,GAAqB,KAArB;EACH;;EACDF,2BAA2B,GAAG;IAC1B,OAAO;MACH5C,QAAQ,EAAE,KAAKtC,IAAL,CAAUsC,QADjB;MAEHC,aAAa,EAAE,KAAKvC,IAAL,CAAUuC,aAFtB;MAGHkB,gBAAgB,EAAE,KAAKzD,IAAL,CAAUyD,gBAHzB;MAIHN,QAAQ,EAAE,KAAKjD;IAJZ,CAAP;EAMH;;EACDiF,eAAe,CAAC/D,KAAD,EAAQ;IACnB,IAAImB,aAAa,GAAGnB,KAAK,CAACmB,aAA1B;IACAnB,KAAK,CAACqC,gBAAN,CAAuBC,MAAvB,CAA8BnB,aAA9B,EAA6C,CAA7C;IACA,IAAInB,KAAK,CAAC+B,QAAN,CAAevC,QAAnB,EACIQ,KAAK,CAAC+B,QAAN,CAAevC,QAAf,CAAwBgD,IAAxB,CAA6BxC,KAAK,CAACkB,QAAnC,EADJ,KAGIlB,KAAK,CAAC+B,QAAN,CAAevC,QAAf,GAA0B,CAACQ,KAAK,CAACkB,QAAP,CAA1B;IACJ,KAAKtC,IAAL,CAAU6D,eAAV,CAA0BC,QAA1B,CAAmC;MAC/B5D,IAAI,EAAEkB,KAAK,CAACkB,QADmB;MAE/ByB,QAAQ,EAAE3C,KAAK,CAAC+B,QAAN,CAAehD,MAAf,GAAwBiB,KAAK,CAAC+B,QAAN,CAAehD,MAAf,CAAsBS,QAA9C,GAAyD,KAAKZ,IAAL,CAAUM,KAF9C;MAG/BqC,KAAK,EAAEJ;IAHwB,CAAnC;EAKH;;EACD8C,mBAAmB,CAACjE,KAAD,EAAQ;IACvB,IAAI,KAAKpB,IAAL,CAAU8E,cAAV,IAA4B,KAAK5E,IAAL,CAAU+E,SAAV,KAAwB,KAApD,IAA6D,KAAKjF,IAAL,CAAU4C,SAAV,CAAoB,KAAK5C,IAAL,CAAUsC,QAA9B,EAAwC,KAAKpC,IAA7C,EAAmD,KAAKF,IAAL,CAAUwC,aAA7D,CAAjE,EAA8I;MAC1I,KAAK4C,aAAL,GAAqB,IAArB;IACH;EACJ;;EACDE,mBAAmB,CAAClE,KAAD,EAAQ;IACvB,IAAI,KAAKpB,IAAL,CAAU8E,cAAd,EAA8B;MAC1B,IAAIS,IAAI,GAAGnE,KAAK,CAACoE,aAAN,CAAoBC,qBAApB,EAAX;;MACA,IAAIrE,KAAK,CAACsE,CAAN,GAAUH,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACK,KAA3B,IAAoCxE,KAAK,CAACsE,CAAN,GAAUH,IAAI,CAACI,IAAnD,IAA2DvE,KAAK,CAACyE,CAAN,IAAWC,IAAI,CAACC,KAAL,CAAWR,IAAI,CAACS,GAAL,GAAWT,IAAI,CAACU,MAA3B,CAAtE,IAA4G7E,KAAK,CAACyE,CAAN,GAAUN,IAAI,CAACS,GAA/H,EAAoI;QAChI,KAAKZ,aAAL,GAAqB,KAArB;MACH;IACJ;EACJ;;EACDc,SAAS,CAAC9E,KAAD,EAAQ;IACb,MAAM+E,WAAW,GAAG/E,KAAK,CAACgF,MAAN,CAAaC,aAAb,CAA2BA,aAA/C;;IACA,IAAIF,WAAW,CAACG,QAAZ,KAAyB,YAAzB,IAA0C,KAAKtG,IAAL,CAAUuG,WAAV,IAAyB,KAAKvG,IAAL,CAAUuG,WAAV,CAAsBC,kBAAtB,CAAyCC,aAAzC,CAAuDC,KAAvD,CAA6DC,OAA7D,KAAyE,OAAhJ,EAA0J;MACtJ;IACH;;IACD,QAAQvF,KAAK,CAACW,KAAd;MACI;MACA,KAAK,EAAL;QACI,MAAM6E,WAAW,GAAI,KAAK5G,IAAL,CAAU8E,cAAX,GAA6BqB,WAAW,CAACvF,QAAZ,CAAqB,CAArB,EAAwBA,QAAxB,CAAiC,CAAjC,CAA7B,GAAmEuF,WAAW,CAACvF,QAAZ,CAAqB,CAArB,EAAwBA,QAAxB,CAAiC,CAAjC,CAAvF;;QACA,IAAIgG,WAAW,IAAIA,WAAW,CAAChG,QAAZ,CAAqBC,MAArB,GAA8B,CAAjD,EAAoD;UAChD,KAAKgG,SAAL,CAAeD,WAAW,CAAChG,QAAZ,CAAqB,CAArB,CAAf;QACH,CAFD,MAGK;UACD,MAAMkG,eAAe,GAAGX,WAAW,CAACY,kBAApC;;UACA,IAAID,eAAJ,EAAqB;YACjB,KAAKD,SAAL,CAAeC,eAAf;UACH,CAFD,MAGK;YACD,IAAIE,mBAAmB,GAAG,KAAKC,yBAAL,CAA+Bd,WAA/B,CAA1B;;YACA,IAAIa,mBAAJ,EAAyB;cACrB,KAAKH,SAAL,CAAeG,mBAAf;YACH;UACJ;QACJ;;QACD5F,KAAK,CAACiB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI8D,WAAW,CAACe,sBAAhB,EAAwC;UACpC,KAAKL,SAAL,CAAe,KAAKM,yBAAL,CAA+BhB,WAAW,CAACe,sBAA3C,CAAf;QACH,CAFD,MAGK;UACD,IAAIE,iBAAiB,GAAG,KAAKC,oBAAL,CAA0BlB,WAA1B,CAAxB;;UACA,IAAIiB,iBAAJ,EAAuB;YACnB,KAAKP,SAAL,CAAeO,iBAAf;UACH;QACJ;;QACDhG,KAAK,CAACiB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,CAAC,KAAKnC,IAAL,CAAUS,QAAX,IAAuB,CAAC,KAAKX,IAAL,CAAUkB,UAAV,CAAqB,KAAKhB,IAA1B,CAA5B,EAA6D;UACzD,KAAKoB,MAAL,CAAYF,KAAZ;QACH;;QACDA,KAAK,CAACiB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,IAAI,KAAKnC,IAAL,CAAUS,QAAd,EAAwB;UACpB,KAAKU,QAAL,CAAcD,KAAd;QACH,CAFD,MAGK;UACD,IAAIgG,iBAAiB,GAAG,KAAKC,oBAAL,CAA0BlB,WAA1B,CAAxB;;UACA,IAAIiB,iBAAJ,EAAuB;YACnB,KAAKP,SAAL,CAAeO,iBAAf;UACH;QACJ;;QACDhG,KAAK,CAACiB,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;QACI,KAAKrC,IAAL,CAAU6B,WAAV,CAAsBT,KAAtB,EAA6B,KAAKlB,IAAlC;QACAkB,KAAK,CAACiB,cAAN;QACA;;MACJ;QACI;QACA;IA7DR;EA+DH;;EACD4E,yBAAyB,CAACd,WAAD,EAAc;IACnC,IAAIiB,iBAAiB,GAAG,KAAKC,oBAAL,CAA0BlB,WAA1B,CAAxB;;IACA,IAAIiB,iBAAJ,EAAuB;MACnB,IAAIA,iBAAiB,CAACL,kBAAtB,EACI,OAAOK,iBAAiB,CAACL,kBAAzB,CADJ,KAGI,OAAO,KAAKE,yBAAL,CAA+BG,iBAA/B,CAAP;IACP,CALD,MAMK;MACD,OAAO,IAAP;IACH;EACJ;;EACDD,yBAAyB,CAAChB,WAAD,EAAc;IACnC,MAAMS,WAAW,GAAGU,KAAK,CAACC,IAAN,CAAWpB,WAAW,CAACvF,QAAvB,EAAiC4G,IAAjC,CAAsCC,EAAE,IAAIhI,UAAU,CAACiI,QAAX,CAAoBD,EAApB,EAAwB,YAAxB,CAA5C,CAApB;IACA,MAAME,mBAAmB,GAAGf,WAAW,CAAChG,QAAZ,CAAqB,CAArB,CAA5B;;IACA,IAAI+G,mBAAmB,IAAIA,mBAAmB,CAAC/G,QAApB,CAA6BC,MAA7B,GAAsC,CAAjE,EAAoE;MAChE,MAAM+G,gBAAgB,GAAGD,mBAAmB,CAAC/G,QAApB,CAA6B+G,mBAAmB,CAAC/G,QAApB,CAA6BC,MAA7B,GAAsC,CAAnE,CAAzB;MACA,OAAO,KAAKsG,yBAAL,CAA+BS,gBAA/B,CAAP;IACH,CAHD,MAIK;MACD,OAAOzB,WAAP;IACH;EACJ;;EACDkB,oBAAoB,CAAClB,WAAD,EAAc;IAC9B,MAAMiB,iBAAiB,GAAGjB,WAAW,CAACE,aAAZ,CAA0BA,aAA1B,CAAwCA,aAAlE;IACA,OAAOe,iBAAiB,CAACS,OAAlB,KAA8B,YAA9B,GAA6CT,iBAA7C,GAAiE,IAAxE;EACH;;EACDP,SAAS,CAACiB,OAAD,EAAU;IACf,IAAI,KAAK9H,IAAL,CAAU8E,cAAd,EACIgD,OAAO,CAAClH,QAAR,CAAiB,CAAjB,EAAoBA,QAApB,CAA6B,CAA7B,EAAgCmH,KAAhC,GADJ,KAGID,OAAO,CAAClH,QAAR,CAAiB,CAAjB,EAAoBA,QAApB,CAA6B,CAA7B,EAAgCmH,KAAhC;EACP;;AAzUY;;AA2UjBjI,UAAU,CAACkB,UAAX,GAAwB,kBAAxB;;AACAlB,UAAU,CAACkI,IAAX;EAAA,iBAAuGlI,UAAvG,EAA6FzB,EAA7F,mBAAmIC,UAAU,CAAC,MAAM2J,IAAP,CAA7I;AAAA;;AACAnI,UAAU,CAACoI,IAAX,kBAD6F7J,EAC7F;EAAA,MAA2FyB,UAA3F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD6FzB,EAErF,yEADR;IAAA;;IAAA;MAD6FA,EAExE,6BADrB;IAAA;EAAA;EAAA,eAuEiEa,EAAE,CAACiJ,OAvEpE,EAuE+JjJ,EAAE,CAACkJ,OAvElK,EAuE4RlJ,EAAE,CAACmJ,IAvE/R,EAuEgYnJ,EAAE,CAACoJ,gBAvEnY,EAuEuiBpJ,EAAE,CAACqJ,OAvE1iB,EAuE4nB7I,EAAE,CAAC8I,MAvE/nB,EAuE2rB1I,UAvE3rB;EAAA;AAAA;;AAwEA;EAAA,mDAzE6FzB,EAyE7F,mBAA2FyB,UAA3F,EAAmH,CAAC;IACxG2I,IAAI,EAAElK,SADkG;IAExGmK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,YADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAzEmB;MA0ECC,aAAa,EAAErK,iBAAiB,CAACsK,IA1ElC;MA2ECC,IAAI,EAAE;QACF,SAAS;MADP;IA3EP,CAAD;EAFkG,CAAD,CAAnH,EAiF4B,YAAY;IAChC,OAAO,CAAC;MAAEN,IAAI,EAAEO,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBR,IAAI,EAAEhK,MADkB;QAExBiK,IAAI,EAAE,CAACpK,UAAU,CAAC,MAAM2J,IAAP,CAAX;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CAtFL,EAsFuB;IAAEiB,OAAO,EAAE,CAAC;MACnBT,IAAI,EAAE/J;IADa,CAAD,CAAX;IAEPwB,IAAI,EAAE,CAAC;MACPuI,IAAI,EAAE/J;IADC,CAAD,CAFC;IAIP0B,UAAU,EAAE,CAAC;MACbqI,IAAI,EAAE/J;IADO,CAAD,CAJL;IAMPyK,IAAI,EAAE,CAAC;MACPV,IAAI,EAAE/J;IADC,CAAD,CANC;IAQPiE,KAAK,EAAE,CAAC;MACR8F,IAAI,EAAE/J;IADE,CAAD,CARA;IAUP0K,UAAU,EAAE,CAAC;MACbX,IAAI,EAAE/J;IADO,CAAD,CAVL;IAYP2K,SAAS,EAAE,CAAC;MACZZ,IAAI,EAAE/J;IADM,CAAD,CAZJ;IAcP4K,KAAK,EAAE,CAAC;MACRb,IAAI,EAAE/J;IADE,CAAD,CAdA;IAgBP6K,WAAW,EAAE,CAAC;MACdd,IAAI,EAAE/J;IADQ,CAAD,CAhBN;IAkBP8K,QAAQ,EAAE,CAAC;MACXf,IAAI,EAAE/J;IADK,CAAD;EAlBH,CAtFvB;AAAA;;AA2GA,MAAMuJ,IAAN,CAAW;EACPlI,WAAW,CAAC0H,EAAD,EAAK5D,eAAL,EAAsB4F,MAAtB,EAA8B;IACrC,KAAKhC,EAAL,GAAUA,EAAV;IACA,KAAK5D,eAAL,GAAuBA,eAAvB;IACA,KAAK4F,MAAL,GAAcA,MAAd;IACA,KAAKC,MAAL,GAAc,UAAd;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,oBAAL,GAA4B,IAA5B;IACA,KAAKC,sBAAL,GAA8B,IAA9B;IACA,KAAKC,WAAL,GAAmB,eAAnB;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,QAAL,GAAgB,OAAhB;IACA,KAAKC,UAAL,GAAkB,SAAlB;IACA,KAAKC,IAAL,GAAY,KAAZ;IACA,KAAKX,WAAL,GAAmB,GAAnB;;IACA,KAAKY,OAAL,GAAe,CAACxH,KAAD,EAAQyH,IAAR,KAAiBA,IAAhC;;IACA,KAAKC,eAAL,GAAuB,IAAI1L,YAAJ,EAAvB;IACA,KAAK2L,YAAL,GAAoB,IAAI3L,YAAJ,EAApB;IACA,KAAK4L,cAAL,GAAsB,IAAI5L,YAAJ,EAAtB;IACA,KAAK8C,YAAL,GAAoB,IAAI9C,YAAJ,EAApB;IACA,KAAKiD,cAAL,GAAsB,IAAIjD,YAAJ,EAAtB;IACA,KAAK6L,uBAAL,GAA+B,IAAI7L,YAAJ,EAA/B;IACA,KAAKuE,UAAL,GAAkB,IAAIvE,YAAJ,EAAlB;IACA,KAAK8L,UAAL,GAAkB,IAAI9L,YAAJ,EAAlB;IACA,KAAK+L,QAAL,GAAgB,IAAI/L,YAAJ,EAAhB;IACA,KAAKgM,mBAAL,GAA2B,IAAIhM,YAAJ,EAA3B;IACA,KAAKiM,QAAL,GAAgB,IAAIjM,YAAJ,EAAhB;EACH;;EACoB,IAAjBkM,iBAAiB,GAAG;IACpB,OAAO,KAAKC,kBAAZ;EACH;;EACoB,IAAjBD,iBAAiB,CAACE,GAAD,EAAM;IACvB,KAAKD,kBAAL,GAA0BC,GAA1B;IACAC,OAAO,CAACC,IAAR,CAAa,2FAAb;EACH;;EACDhL,QAAQ,GAAG;IACP,IAAI,KAAK6E,cAAT,EAAyB;MACrB,KAAKoG,qBAAL,GAA6B,KAAKrH,eAAL,CAAqBsH,UAArB,CAAgCC,SAAhC,CAA0ChK,KAAK,IAAI;QAC5E,KAAKsB,YAAL,GAAoBtB,KAAK,CAACpB,IAA1B;QACA,KAAKsC,QAAL,GAAgBlB,KAAK,CAAClB,IAAtB;QACA,KAAKuD,gBAAL,GAAwBrC,KAAK,CAAC2C,QAA9B;QACA,KAAKxB,aAAL,GAAqBnB,KAAK,CAACuB,KAA3B;QACA,KAAKH,aAAL,GAAqBpB,KAAK,CAACsD,KAA3B;MACH,CAN4B,CAA7B;MAOA,KAAK2G,oBAAL,GAA4B,KAAKxH,eAAL,CAAqByH,SAArB,CAA+BF,SAA/B,CAAyChK,KAAK,IAAI;QAC1E,KAAKsB,YAAL,GAAoB,IAApB;QACA,KAAKJ,QAAL,GAAgB,IAAhB;QACA,KAAKmB,gBAAL,GAAwB,IAAxB;QACA,KAAKlB,aAAL,GAAqB,IAArB;QACA,KAAKC,aAAL,GAAqB,IAArB;QACA,KAAK+I,SAAL,GAAiB,KAAjB;MACH,CAP2B,CAA5B;IAQH;EACJ;;EACDC,WAAW,CAACC,YAAD,EAAe;IACtB,IAAIA,YAAY,CAACnL,KAAjB,EAAwB;MACpB,KAAKkB,qBAAL;IACH;EACJ;;EACa,IAAVkK,UAAU,GAAG;IACb,OAAO,KAAKhC,MAAL,IAAe,YAAtB;EACH;;EACoB,IAAjBiC,iBAAiB,GAAG;IACpB,OAAO,KAAK5B,YAAL,IAAqB,KAAKN,MAAL,CAAYmC,cAAZ,CAA2BvM,eAAe,CAACwM,aAA3C,CAA5B;EACH;;EACDC,kBAAkB,GAAG;IACjB,IAAI,KAAKC,SAAL,CAAelL,MAAnB,EAA2B;MACvB,KAAKmL,WAAL,GAAmB,EAAnB;IACH;;IACD,KAAKD,SAAL,CAAeE,OAAf,CAAwB7B,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAAC8B,OAAL,EAAR;QACI,KAAK,QAAL;UACI,KAAKC,cAAL,GAAsB/B,IAAI,CAACxB,QAA3B;UACA;;QACJ,KAAK,OAAL;UACI,KAAKwD,oBAAL,GAA4BhC,IAAI,CAACxB,QAAjC;UACA;;QACJ,KAAK,QAAL;UACI,KAAKyD,cAAL,GAAsBjC,IAAI,CAACxB,QAA3B;UACA;;QACJ,KAAK,QAAL;UACI,KAAK0D,cAAL,GAAsBlC,IAAI,CAACxB,QAA3B;UACA;;QACJ;UACI,KAAKoD,WAAL,CAAiB5B,IAAI,CAACmC,IAAtB,IAA8BnC,IAAI,CAACxB,QAAnC;UACA;MAfR;IAiBH,CAlBD;EAmBH;;EACDpH,qBAAqB,GAAG;IACpB,KAAKgL,eAAL,GAAuB,EAAvB;IACA,KAAKC,cAAL,CAAoB,IAApB,EAA0B,KAAKC,WAAL,EAA1B,EAA8C,CAA9C,EAAiD,IAAjD;EACH;;EACDD,cAAc,CAACtM,MAAD,EAASwM,KAAT,EAAgBrD,KAAhB,EAAuBsD,OAAvB,EAAgC;IAC1C,IAAID,KAAK,IAAIA,KAAK,CAAC9L,MAAnB,EAA2B;MACvB,KAAK,IAAIX,IAAT,IAAiByM,KAAjB,EAAwB;QACpBzM,IAAI,CAACC,MAAL,GAAcA,MAAd;QACA,MAAM+I,OAAO,GAAG;UACZhJ,IAAI,EAAEA,IADM;UAEZC,MAAM,EAAEA,MAFI;UAGZmJ,KAAK,EAAEA,KAHK;UAIZsD,OAAO,EAAEA,OAAO,KAAKzM,MAAM,GAAGA,MAAM,CAACQ,QAAV,GAAqB,IAAhC;QAJJ,CAAhB;QAMA,KAAK6L,eAAL,CAAqB5I,IAArB,CAA0BsF,OAA1B;;QACA,IAAIA,OAAO,CAAC0D,OAAR,IAAmB1M,IAAI,CAACS,QAA5B,EAAsC;UAClC,KAAK8L,cAAL,CAAoBvM,IAApB,EAA0BA,IAAI,CAACU,QAA/B,EAAyC0I,KAAK,GAAG,CAAjD,EAAoDJ,OAAO,CAAC0D,OAA5D;QACH;MACJ;IACJ;EACJ;;EACD/K,WAAW,CAACT,KAAD,EAAQlB,IAAR,EAAc;IACrB,IAAI2M,WAAW,GAAGzL,KAAK,CAACgF,MAAxB;;IACA,IAAI3G,UAAU,CAACiI,QAAX,CAAoBmF,WAApB,EAAiC,gBAAjC,KAAsDpN,UAAU,CAACiI,QAAX,CAAoBmF,WAApB,EAAiC,qBAAjC,CAA1D,EAAmH;MAC/G;IACH,CAFD,MAGK,IAAI,KAAKC,aAAT,EAAwB;MACzB,IAAI5M,IAAI,CAAC6M,UAAL,KAAoB,KAAxB,EAA+B;QAC3B;MACH;;MACD,IAAI,KAAKC,gBAAL,EAAJ,EAA6B;QACzB9M,IAAI,GAAG,KAAKK,cAAL,CAAoBL,IAAI,CAACM,GAAzB,EAA8B,KAAKF,KAAnC,CAAP;;QACA,IAAI,CAACJ,IAAL,EAAW;UACP;QACH;MACJ;;MACD,IAAIyC,KAAK,GAAG,KAAKsK,oBAAL,CAA0B/M,IAA1B,CAAZ;MACA,IAAIgN,QAAQ,GAAIvK,KAAK,IAAI,CAAzB;;MACA,IAAI,KAAKwK,uBAAL,EAAJ,EAAoC;QAChC,IAAID,QAAJ,EAAc;UACV,IAAI,KAAKrD,sBAAT,EACI,KAAKuD,aAAL,CAAmBlN,IAAnB,EAAyB,KAAzB,EADJ,KAGI,KAAKmN,SAAL,GAAiB,KAAKA,SAAL,CAAeC,MAAf,CAAsB,CAACvC,GAAD,EAAMwC,CAAN,KAAYA,CAAC,IAAI5K,KAAvC,CAAjB;;UACJ,IAAI,KAAKiH,oBAAL,IAA6B1J,IAAI,CAACC,MAAtC,EAA8C;YAC1C,KAAKqN,WAAL,CAAiBtN,IAAI,CAACC,MAAtB,EAA8B,KAA9B;UACH;;UACD,KAAKkK,eAAL,CAAqB3I,IAArB,CAA0B,KAAK2L,SAA/B;UACA,KAAK9C,cAAL,CAAoB7I,IAApB,CAAyB;YAAEC,aAAa,EAAEP,KAAjB;YAAwBlB,IAAI,EAAEA;UAA9B,CAAzB;QACH,CAVD,MAWK;UACD,IAAI,KAAK2J,sBAAT,EACI,KAAKuD,aAAL,CAAmBlN,IAAnB,EAAyB,IAAzB,EADJ,KAGI,KAAKmN,SAAL,GAAiB,CAAC,IAAG,KAAKA,SAAL,IAAkB,EAArB,CAAD,EAA0BnN,IAA1B,CAAjB;;UACJ,IAAI,KAAK0J,oBAAL,IAA6B1J,IAAI,CAACC,MAAtC,EAA8C;YAC1C,KAAKqN,WAAL,CAAiBtN,IAAI,CAACC,MAAtB,EAA8B,IAA9B;UACH;;UACD,KAAKkK,eAAL,CAAqB3I,IAArB,CAA0B,KAAK2L,SAA/B;UACA,KAAK/C,YAAL,CAAkB5I,IAAlB,CAAuB;YAAEC,aAAa,EAAEP,KAAjB;YAAwBlB,IAAI,EAAEA;UAA9B,CAAvB;QACH;MACJ,CAvBD,MAwBK;QACD,IAAIuN,aAAa,GAAG,KAAKC,WAAL,GAAmB,KAAnB,GAA2B,KAAK/D,gBAApD;;QACA,IAAI8D,aAAJ,EAAmB;UACf,IAAIE,OAAO,GAAIvM,KAAK,CAACuM,OAAN,IAAiBvM,KAAK,CAACwM,OAAtC;;UACA,IAAIV,QAAQ,IAAIS,OAAhB,EAAyB;YACrB,IAAI,KAAKE,qBAAL,EAAJ,EAAkC;cAC9B,KAAKxD,eAAL,CAAqB3I,IAArB,CAA0B,IAA1B;YACH,CAFD,MAGK;cACD,KAAK2L,SAAL,GAAiB,KAAKA,SAAL,CAAeC,MAAf,CAAsB,CAACvC,GAAD,EAAMwC,CAAN,KAAYA,CAAC,IAAI5K,KAAvC,CAAjB;cACA,KAAK0H,eAAL,CAAqB3I,IAArB,CAA0B,KAAK2L,SAA/B;YACH;;YACD,KAAK9C,cAAL,CAAoB7I,IAApB,CAAyB;cAAEC,aAAa,EAAEP,KAAjB;cAAwBlB,IAAI,EAAEA;YAA9B,CAAzB;UACH,CATD,MAUK;YACD,IAAI,KAAK2N,qBAAL,EAAJ,EAAkC;cAC9B,KAAKxD,eAAL,CAAqB3I,IAArB,CAA0BxB,IAA1B;YACH,CAFD,MAGK,IAAI,KAAK4N,uBAAL,EAAJ,EAAoC;cACrC,KAAKT,SAAL,GAAkB,CAACM,OAAF,GAAa,EAAb,GAAkB,KAAKN,SAAL,IAAkB,EAArD;cACA,KAAKA,SAAL,GAAiB,CAAC,GAAG,KAAKA,SAAT,EAAoBnN,IAApB,CAAjB;cACA,KAAKmK,eAAL,CAAqB3I,IAArB,CAA0B,KAAK2L,SAA/B;YACH;;YACD,KAAK/C,YAAL,CAAkB5I,IAAlB,CAAuB;cAAEC,aAAa,EAAEP,KAAjB;cAAwBlB,IAAI,EAAEA;YAA9B,CAAvB;UACH;QACJ,CAvBD,MAwBK;UACD,IAAI,KAAK2N,qBAAL,EAAJ,EAAkC;YAC9B,IAAIX,QAAJ,EAAc;cACV,KAAKG,SAAL,GAAiB,IAAjB;cACA,KAAK9C,cAAL,CAAoB7I,IAApB,CAAyB;gBAAEC,aAAa,EAAEP,KAAjB;gBAAwBlB,IAAI,EAAEA;cAA9B,CAAzB;YACH,CAHD,MAIK;cACD,KAAKmN,SAAL,GAAiBnN,IAAjB;cACA,KAAKoK,YAAL,CAAkB5I,IAAlB,CAAuB;gBAAEC,aAAa,EAAEP,KAAjB;gBAAwBlB,IAAI,EAAEA;cAA9B,CAAvB;YACH;UACJ,CATD,MAUK;YACD,IAAIgN,QAAJ,EAAc;cACV,KAAKG,SAAL,GAAiB,KAAKA,SAAL,CAAeC,MAAf,CAAsB,CAACvC,GAAD,EAAMwC,CAAN,KAAYA,CAAC,IAAI5K,KAAvC,CAAjB;cACA,KAAK4H,cAAL,CAAoB7I,IAApB,CAAyB;gBAAEC,aAAa,EAAEP,KAAjB;gBAAwBlB,IAAI,EAAEA;cAA9B,CAAzB;YACH,CAHD,MAIK;cACD,KAAKmN,SAAL,GAAiB,CAAC,IAAG,KAAKA,SAAL,IAAkB,EAArB,CAAD,EAA0BnN,IAA1B,CAAjB;cACA,KAAKoK,YAAL,CAAkB5I,IAAlB,CAAuB;gBAAEC,aAAa,EAAEP,KAAjB;gBAAwBlB,IAAI,EAAEA;cAA9B,CAAvB;YACH;UACJ;;UACD,KAAKmK,eAAL,CAAqB3I,IAArB,CAA0B,KAAK2L,SAA/B;QACH;MACJ;IACJ;;IACD,KAAKK,WAAL,GAAmB,KAAnB;EACH;;EACD1L,cAAc,GAAG;IACb,KAAK0L,WAAL,GAAmB,IAAnB;EACH;;EACDzL,gBAAgB,CAACb,KAAD,EAAQlB,IAAR,EAAc;IAC1B,IAAI,KAAKqG,WAAT,EAAsB;MAClB,IAAIsG,WAAW,GAAGzL,KAAK,CAACgF,MAAxB;;MACA,IAAIyG,WAAW,CAACkB,SAAZ,IAAyBlB,WAAW,CAACkB,SAAZ,CAAsBC,OAAtB,CAA8B,gBAA9B,MAAoD,CAAjF,EAAoF;QAChF;MACH,CAFD,MAGK;QACD,IAAIrL,KAAK,GAAG,KAAKsK,oBAAL,CAA0B/M,IAA1B,CAAZ;QACA,IAAIgN,QAAQ,GAAIvK,KAAK,IAAI,CAAzB;;QACA,IAAI,CAACuK,QAAL,EAAe;UACX,IAAI,KAAKW,qBAAL,EAAJ,EACI,KAAKxD,eAAL,CAAqB3I,IAArB,CAA0BxB,IAA1B,EADJ,KAGI,KAAKmK,eAAL,CAAqB3I,IAArB,CAA0B,CAACxB,IAAD,CAA1B;QACP;;QACD,KAAKqG,WAAL,CAAiB0H,IAAjB,CAAsB7M,KAAtB;QACA,KAAKoJ,uBAAL,CAA6B9I,IAA7B,CAAkC;UAAEC,aAAa,EAAEP,KAAjB;UAAwBlB,IAAI,EAAEA;QAA9B,CAAlC;MACH;IACJ;EACJ;;EACD+M,oBAAoB,CAAC/M,IAAD,EAAO;IACvB,IAAIyC,KAAK,GAAG,CAAC,CAAb;;IACA,IAAI,KAAKmK,aAAL,IAAsB,KAAKO,SAA/B,EAA0C;MACtC,IAAI,KAAKQ,qBAAL,EAAJ,EAAkC;QAC9B,IAAIK,aAAa,GAAI,KAAKb,SAAL,CAAe7M,GAAf,IAAsB,KAAK6M,SAAL,CAAe7M,GAAf,KAAuBN,IAAI,CAACM,GAAnD,IAA2D,KAAK6M,SAAL,IAAkBnN,IAAjG;QACAyC,KAAK,GAAGuL,aAAa,GAAG,CAAH,GAAO,CAAC,CAA7B;MACH,CAHD,MAIK;QACD,KAAK,IAAIX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKF,SAAL,CAAexM,MAAnC,EAA2C0M,CAAC,EAA5C,EAAgD;UAC5C,IAAIY,YAAY,GAAG,KAAKd,SAAL,CAAeE,CAAf,CAAnB;UACA,IAAIW,aAAa,GAAIC,YAAY,CAAC3N,GAAb,IAAoB2N,YAAY,CAAC3N,GAAb,KAAqBN,IAAI,CAACM,GAA/C,IAAuD2N,YAAY,IAAIjO,IAA3F;;UACA,IAAIgO,aAAJ,EAAmB;YACfvL,KAAK,GAAG4K,CAAR;YACA;UACH;QACJ;MACJ;IACJ;;IACD,OAAO5K,KAAP;EACH;;EACDtC,cAAc,CAACH,IAAD,EAAOkO,WAAP,EAAoBC,MAApB,EAA4B/N,KAA5B,EAAmC;IAC7C;IACA,MAAMgO,KAAK,GAAG,KAAKtB,gBAAL,KAA0B,KAAKzM,cAAL,CAAoBL,IAAI,CAACM,GAAzB,EAA8B4N,WAA9B,CAA1B,GAAuE,IAArF;;IACA,IAAIE,KAAJ,EAAW;MACPA,KAAK,CAACD,MAAD,CAAL,GAAgB/N,KAAK,IAAIJ,IAAI,CAACmO,MAAD,CAA7B;IACH;EACJ;;EACDrB,gBAAgB,GAAG;IACf,OAAO,KAAKM,MAAL,IAAe,KAAKiB,aAApB,IAAqC,KAAKA,aAAL,CAAmB1N,MAA/D;EACH;;EACDN,cAAc,CAACC,GAAD,EAAMmM,KAAN,EAAa;IACvB,KAAK,IAAIzM,IAAT,IAAiByM,KAAjB,EAAwB;MACpB,IAAIzM,IAAI,CAACM,GAAL,KAAaA,GAAjB,EAAsB;QAClB,OAAON,IAAP;MACH;;MACD,IAAIA,IAAI,CAACU,QAAT,EAAmB;QACf,IAAI4N,WAAW,GAAG,KAAKjO,cAAL,CAAoBC,GAApB,EAAyBN,IAAI,CAACU,QAA9B,CAAlB;;QACA,IAAI4N,WAAJ,EAAiB;UACb,OAAOA,WAAP;QACH;MACJ;IACJ;EACJ;;EACDhB,WAAW,CAACtN,IAAD,EAAOuO,MAAP,EAAe;IACtB,IAAIvO,IAAI,CAACU,QAAL,IAAiBV,IAAI,CAACU,QAAL,CAAcC,MAAnC,EAA2C;MACvC,IAAI6N,aAAa,GAAG,CAApB;MACA,IAAIC,oBAAoB,GAAG,KAA3B;;MACA,KAAK,IAAIC,KAAT,IAAkB1O,IAAI,CAACU,QAAvB,EAAiC;QAC7B,IAAI,KAAKsB,UAAL,CAAgB0M,KAAhB,CAAJ,EAA4B;UACxBF,aAAa;QAChB,CAFD,MAGK,IAAIE,KAAK,CAACC,eAAV,EAA2B;UAC5BF,oBAAoB,GAAG,IAAvB;QACH;MACJ;;MACD,IAAIF,MAAM,IAAIC,aAAa,IAAIxO,IAAI,CAACU,QAAL,CAAcC,MAA7C,EAAqD;QACjD,KAAKwM,SAAL,GAAiB,CAAC,IAAG,KAAKA,SAAL,IAAkB,EAArB,CAAD,EAA0BnN,IAA1B,CAAjB;QACAA,IAAI,CAAC2O,eAAL,GAAuB,KAAvB;MACH,CAHD,MAIK;QACD,IAAI,CAACJ,MAAL,EAAa;UACT,IAAI9L,KAAK,GAAG,KAAKsK,oBAAL,CAA0B/M,IAA1B,CAAZ;;UACA,IAAIyC,KAAK,IAAI,CAAb,EAAgB;YACZ,KAAK0K,SAAL,GAAiB,KAAKA,SAAL,CAAeC,MAAf,CAAsB,CAACvC,GAAD,EAAMwC,CAAN,KAAYA,CAAC,IAAI5K,KAAvC,CAAjB;UACH;QACJ;;QACD,IAAIgM,oBAAoB,IAAID,aAAa,GAAG,CAAhB,IAAqBA,aAAa,IAAIxO,IAAI,CAACU,QAAL,CAAcC,MAAhF,EACIX,IAAI,CAAC2O,eAAL,GAAuB,IAAvB,CADJ,KAGI3O,IAAI,CAAC2O,eAAL,GAAuB,KAAvB;MACP;;MACD,KAAKxO,cAAL,CAAoBH,IAApB,EAA0B,KAAKqO,aAA/B,EAA8C,iBAA9C;IACH;;IACD,IAAIpO,MAAM,GAAGD,IAAI,CAACC,MAAlB;;IACA,IAAIA,MAAJ,EAAY;MACR,KAAKqN,WAAL,CAAiBrN,MAAjB,EAAyBsO,MAAzB;IACH;EACJ;;EACDrB,aAAa,CAAClN,IAAD,EAAOuO,MAAP,EAAe;IACxB,IAAI9L,KAAK,GAAG,KAAKsK,oBAAL,CAA0B/M,IAA1B,CAAZ;;IACA,IAAIuO,MAAM,IAAI9L,KAAK,IAAI,CAAC,CAAxB,EAA2B;MACvB,KAAK0K,SAAL,GAAiB,CAAC,IAAG,KAAKA,SAAL,IAAkB,EAArB,CAAD,EAA0BnN,IAA1B,CAAjB;IACH,CAFD,MAGK,IAAI,CAACuO,MAAD,IAAW9L,KAAK,GAAG,CAAC,CAAxB,EAA2B;MAC5B,KAAK0K,SAAL,GAAiB,KAAKA,SAAL,CAAeC,MAAf,CAAsB,CAACvC,GAAD,EAAMwC,CAAN,KAAYA,CAAC,IAAI5K,KAAvC,CAAjB;IACH;;IACDzC,IAAI,CAAC2O,eAAL,GAAuB,KAAvB;IACA,KAAKxO,cAAL,CAAoBH,IAApB,EAA0B,KAAKqO,aAA/B,EAA8C,iBAA9C;;IACA,IAAIrO,IAAI,CAACU,QAAL,IAAiBV,IAAI,CAACU,QAAL,CAAcC,MAAnC,EAA2C;MACvC,KAAK,IAAI+N,KAAT,IAAkB1O,IAAI,CAACU,QAAvB,EAAiC;QAC7B,KAAKwM,aAAL,CAAmBwB,KAAnB,EAA0BH,MAA1B;MACH;IACJ;EACJ;;EACDvM,UAAU,CAAChC,IAAD,EAAO;IACb,OAAO,KAAK+M,oBAAL,CAA0B/M,IAA1B,KAAmC,CAAC,CAA3C;EACH;;EACD2N,qBAAqB,GAAG;IACpB,OAAO,KAAKf,aAAL,IAAsB,KAAKA,aAAL,IAAsB,QAAnD;EACH;;EACDgB,uBAAuB,GAAG;IACtB,OAAO,KAAKhB,aAAL,IAAsB,KAAKA,aAAL,IAAsB,UAAnD;EACH;;EACDK,uBAAuB,GAAG;IACtB,OAAO,KAAKL,aAAL,IAAsB,KAAKA,aAAL,IAAsB,UAAnD;EACH;;EACD5L,UAAU,CAAChB,IAAD,EAAO;IACb,OAAOA,IAAI,CAAC4O,IAAL,IAAa,KAAb,GAAqB,KAArB,GAA6B,EAAE5O,IAAI,CAACU,QAAL,IAAiBV,IAAI,CAACU,QAAL,CAAcC,MAAjC,CAApC;EACH;;EACD6L,WAAW,GAAG;IACV,OAAO,KAAK6B,aAAL,GAAqB,KAAKA,aAA1B,GAA0C,KAAKjO,KAAtD;EACH;;EACDyO,kBAAkB,CAAC7O,IAAD,EAAO;IACrB,IAAI,KAAK8L,WAAT,EACI,OAAO9L,IAAI,CAACuI,IAAL,GAAY,KAAKuD,WAAL,CAAiB9L,IAAI,CAACuI,IAAtB,CAAZ,GAA0C,KAAKuD,WAAL,CAAiB,SAAjB,CAAjD,CADJ,KAGI,OAAO,IAAP;EACP;;EACDgD,UAAU,CAAC5N,KAAD,EAAQ;IACd,IAAI,KAAK0D,cAAL,KAAwB,CAAC,KAAKxE,KAAN,IAAe,KAAKA,KAAL,CAAWO,MAAX,KAAsB,CAA7D,CAAJ,EAAqE;MACjEO,KAAK,CAAC6C,YAAN,CAAmBC,UAAnB,GAAgC,MAAhC;MACA9C,KAAK,CAACiB,cAAN;IACH;EACJ;;EACD4M,MAAM,CAAC7N,KAAD,EAAQ;IACV,IAAI,KAAK0D,cAAL,KAAwB,CAAC,KAAKxE,KAAN,IAAe,KAAKA,KAAL,CAAWO,MAAX,KAAsB,CAA7D,CAAJ,EAAqE;MACjEO,KAAK,CAACiB,cAAN;MACA,IAAIC,QAAQ,GAAG,KAAKA,QAApB;;MACA,IAAI,KAAKM,SAAL,CAAeN,QAAf,EAAyB,IAAzB,EAA+B,KAAKE,aAApC,CAAJ,EAAwD;QACpD,IAAID,aAAa,GAAG,KAAKA,aAAzB;QACA,KAAKjC,KAAL,GAAa,KAAKA,KAAL,IAAc,EAA3B;;QACA,IAAI,KAAK2C,YAAT,EAAuB;UACnB,KAAKC,UAAL,CAAgBxB,IAAhB,CAAqB;YACjBC,aAAa,EAAEP,KADE;YAEjBkB,QAAQ,EAAEA,QAFO;YAGjBa,QAAQ,EAAE,IAHO;YAIjBR,KAAK,EAAEJ,aAJU;YAKjBa,MAAM,EAAE,MAAM;cACV,KAAK8L,eAAL,CAAqB5M,QAArB,EAA+BC,aAA/B;YACH;UAPgB,CAArB;QASH,CAVD,MAWK;UACD,KAAKW,UAAL,CAAgBxB,IAAhB,CAAqB;YACjBC,aAAa,EAAEP,KADE;YAEjBkB,QAAQ,EAAEA,QAFO;YAGjBa,QAAQ,EAAE,IAHO;YAIjBR,KAAK,EAAEJ;UAJU,CAArB;UAMA,KAAK2M,eAAL,CAAqB5M,QAArB,EAA+BC,aAA/B;QACH;MACJ;IACJ;EACJ;;EACD2M,eAAe,CAAC5M,QAAD,EAAWC,aAAX,EAA0B;IACrC,KAAKkB,gBAAL,CAAsBC,MAAtB,CAA6BnB,aAA7B,EAA4C,CAA5C;IACA,KAAKjC,KAAL,CAAWsD,IAAX,CAAgBtB,QAAhB;IACA,KAAKuB,eAAL,CAAqBC,QAArB,CAA8B;MAC1B5D,IAAI,EAAEoC;IADoB,CAA9B;EAGH;;EACD6M,WAAW,GAAG;IACV,IAAI,KAAKrK,cAAL,IAAuB,KAAKlC,SAAL,CAAe,KAAKN,QAApB,EAA8B,IAA9B,EAAoC,KAAKE,aAAzC,CAA3B,EAAoF;MAChF,KAAK+I,SAAL,GAAiB,IAAjB;IACH;EACJ;;EACD6D,WAAW,CAAChO,KAAD,EAAQ;IACf,IAAI,KAAK0D,cAAT,EAAyB;MACrB,IAAIS,IAAI,GAAGnE,KAAK,CAACoE,aAAN,CAAoBC,qBAApB,EAAX;;MACA,IAAIrE,KAAK,CAACsE,CAAN,GAAUH,IAAI,CAACI,IAAL,GAAYJ,IAAI,CAACK,KAA3B,IAAoCxE,KAAK,CAACsE,CAAN,GAAUH,IAAI,CAACI,IAAnD,IAA2DvE,KAAK,CAACyE,CAAN,GAAUN,IAAI,CAACS,GAAL,GAAWT,IAAI,CAACU,MAArF,IAA+F7E,KAAK,CAACyE,CAAN,GAAUN,IAAI,CAACS,GAAlH,EAAuH;QACnH,KAAKuF,SAAL,GAAiB,KAAjB;MACH;IACJ;EACJ;;EACD3I,SAAS,CAACN,QAAD,EAAWa,QAAX,EAAqBX,aAArB,EAAoC;IACzC,IAAI,CAACF,QAAL,EAAe;MACX;MACA,OAAO,KAAP;IACH,CAHD,MAIK,IAAI,KAAK+M,gBAAL,CAAsB7M,aAAtB,CAAJ,EAA0C;MAC3C,IAAI8M,KAAK,GAAG,IAAZ;;MACA,IAAInM,QAAJ,EAAc;QACV,IAAIb,QAAQ,KAAKa,QAAjB,EAA2B;UACvBmM,KAAK,GAAG,KAAR;QACH,CAFD,MAGK;UACD,IAAInP,MAAM,GAAGgD,QAAQ,CAAChD,MAAtB;;UACA,OAAOA,MAAM,IAAI,IAAjB,EAAuB;YACnB,IAAIA,MAAM,KAAKmC,QAAf,EAAyB;cACrBgN,KAAK,GAAG,KAAR;cACA;YACH;;YACDnP,MAAM,GAAGA,MAAM,CAACA,MAAhB;UACH;QACJ;MACJ;;MACD,OAAOmP,KAAP;IACH,CAlBI,MAmBA;MACD,OAAO,KAAP;IACH;EACJ;;EACDD,gBAAgB,CAACE,SAAD,EAAY;IACxB,IAAIC,SAAS,GAAG,KAAKC,cAArB;;IACA,IAAID,SAAJ,EAAe;MACX,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EAAmC;QAC/B,IAAI,OAAOD,SAAP,KAAqB,QAAzB,EACI,OAAOC,SAAS,KAAKD,SAArB,CADJ,KAEK,IAAIA,SAAS,YAAYjI,KAAzB,EACD,OAAOiI,SAAS,CAACvB,OAAV,CAAkBwB,SAAlB,KAAgC,CAAC,CAAxC;MACP,CALD,MAMK,IAAIA,SAAS,YAAYlI,KAAzB,EAAgC;QACjC,IAAI,OAAOiI,SAAP,KAAqB,QAAzB,EAAmC;UAC/B,OAAOC,SAAS,CAACxB,OAAV,CAAkBuB,SAAlB,KAAgC,CAAC,CAAxC;QACH,CAFD,MAGK,IAAIA,SAAS,YAAYjI,KAAzB,EAAgC;UACjC,KAAK,IAAIoI,CAAT,IAAcF,SAAd,EAAyB;YACrB,KAAK,IAAIG,EAAT,IAAeJ,SAAf,EAA0B;cACtB,IAAIG,CAAC,KAAKC,EAAV,EAAc;gBACV,OAAO,IAAP;cACH;YACJ;UACJ;QACJ;MACJ;;MACD,OAAO,KAAP;IACH,CAtBD,MAuBK;MACD,OAAO,IAAP;IACH;EACJ;;EACDC,OAAO,CAACtP,KAAD,EAAQ;IACX,IAAIuP,WAAW,GAAGvP,KAAlB;;IACA,IAAIuP,WAAW,KAAK,EAApB,EAAwB;MACpB,KAAKtB,aAAL,GAAqB,IAArB;IACH,CAFD,MAGK;MACD,KAAKA,aAAL,GAAqB,EAArB;MACA,MAAMuB,YAAY,GAAG,KAAK9F,QAAL,CAAc+F,KAAd,CAAoB,GAApB,CAArB;MACA,MAAMC,UAAU,GAAGxQ,WAAW,CAACyQ,aAAZ,CAA0BJ,WAA1B,EAAuCK,iBAAvC,CAAyD,KAAKC,YAA9D,CAAnB;MACA,MAAMC,YAAY,GAAG,KAAKnG,UAAL,KAAoB,QAAzC;;MACA,KAAK,IAAI/J,IAAT,IAAiB,KAAKI,KAAtB,EAA6B;QACzB,IAAI+P,QAAQ,GAAGvN,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB7C,IAAlB,CAAf;QACA,IAAIoQ,iBAAiB,GAAG;UAAER,YAAF;UAAgBE,UAAhB;UAA4BI;QAA5B,CAAxB;;QACA,IAAKA,YAAY,KAAK,KAAKG,iBAAL,CAAuBF,QAAvB,EAAiCC,iBAAjC,KAAuD,KAAKE,eAAL,CAAqBH,QAArB,EAA+BC,iBAA/B,CAA5D,CAAb,IACC,CAACF,YAAD,KAAkB,KAAKI,eAAL,CAAqBH,QAArB,EAA+BC,iBAA/B,KAAqD,KAAKC,iBAAL,CAAuBF,QAAvB,EAAiCC,iBAAjC,CAAvE,CADL,EACmI;UAC/H,KAAK/B,aAAL,CAAmB3K,IAAnB,CAAwByM,QAAxB;QACH;MACJ;IACJ;;IACD,KAAK7O,qBAAL;IACA,KAAKoJ,QAAL,CAAclJ,IAAd,CAAmB;MACf4L,MAAM,EAAEuC,WADO;MAEfY,aAAa,EAAE,KAAKlC;IAFL,CAAnB;EAIH;;EACDmC,WAAW,GAAG;IACV,KAAKnC,aAAL,GAAqB,IAArB;;IACA,IAAI,KAAKoC,eAAL,IAAwB,KAAKA,eAAL,CAAqBlK,aAAjD,EAAgE;MAC5D,KAAKkK,eAAL,CAAqBlK,aAArB,CAAmCnG,KAAnC,GAA2C,EAA3C;IACH;EACJ;;EACDsQ,oBAAoB,CAACjO,KAAD,EAAQ;IACxB,KAAKpB,aAAL,IAAsB,KAAKsP,QAAL,CAAcC,aAAd,CAA4BnO,KAA5B,CAAtB;EACH;;EACDoO,QAAQ,CAACC,OAAD,EAAU;IACd,IAAI,KAAKzP,aAAT,EAAwB;MACpB,KAAKsP,QAAL,CAAcE,QAAd,CAAuBC,OAAvB;IACH,CAFD,MAGK,IAAI,KAAKC,gBAAL,IAAyB,KAAKA,gBAAL,CAAsBxK,aAAnD,EAAkE;MACnE,IAAI,KAAKwK,gBAAL,CAAsBxK,aAAtB,CAAoCsK,QAAxC,EAAkD;QAC9C,KAAKE,gBAAL,CAAsBxK,aAAtB,CAAoCsK,QAApC,CAA6CC,OAA7C;MACH,CAFD,MAGK;QACD,KAAKC,gBAAL,CAAsBxK,aAAtB,CAAoCyK,UAApC,GAAiDF,OAAO,CAACrL,IAAzD;QACA,KAAKsL,gBAAL,CAAsBxK,aAAtB,CAAoC0K,SAApC,GAAgDH,OAAO,CAAChL,GAAxD;MACH;IACJ;EACJ;;EACDuK,iBAAiB,CAACrQ,IAAD,EAAOoQ,iBAAP,EAA0B;IACvC,IAAIpQ,IAAJ,EAAU;MACN,IAAIkR,OAAO,GAAG,KAAd;;MACA,IAAIlR,IAAI,CAACU,QAAT,EAAmB;QACf,IAAIyQ,UAAU,GAAG,CAAC,GAAGnR,IAAI,CAACU,QAAT,CAAjB;QACAV,IAAI,CAACU,QAAL,GAAgB,EAAhB;;QACA,KAAK,IAAI0Q,SAAT,IAAsBD,UAAtB,EAAkC;UAC9B,IAAIE,aAAa,GAAGzO,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkBuO,SAAlB,CAApB;;UACA,IAAI,KAAKd,eAAL,CAAqBe,aAArB,EAAoCjB,iBAApC,CAAJ,EAA4D;YACxDc,OAAO,GAAG,IAAV;YACAlR,IAAI,CAACU,QAAL,CAAcgD,IAAd,CAAmB2N,aAAnB;UACH;QACJ;MACJ;;MACD,IAAIH,OAAJ,EAAa;QACTlR,IAAI,CAACS,QAAL,GAAgB,IAAhB;QACA,OAAO,IAAP;MACH;IACJ;EACJ;;EACD6P,eAAe,CAACtQ,IAAD,EAAO;IAAE4P,YAAF;IAAgBE,UAAhB;IAA4BI;EAA5B,CAAP,EAAmD;IAC9D,IAAIgB,OAAO,GAAG,KAAd;;IACA,KAAK,IAAII,KAAT,IAAkB1B,YAAlB,EAAgC;MAC5B,IAAI2B,UAAU,GAAGjS,WAAW,CAACyQ,aAAZ,CAA0ByB,MAAM,CAAClS,WAAW,CAACmS,gBAAZ,CAA6BzR,IAA7B,EAAmCsR,KAAnC,CAAD,CAAhC,EAA6EtB,iBAA7E,CAA+F,KAAKC,YAApG,CAAjB;;MACA,IAAIsB,UAAU,CAACzD,OAAX,CAAmBgC,UAAnB,IAAiC,CAAC,CAAtC,EAAyC;QACrCoB,OAAO,GAAG,IAAV;MACH;IACJ;;IACD,IAAI,CAACA,OAAD,IAAahB,YAAY,IAAI,CAAC,KAAKlP,UAAL,CAAgBhB,IAAhB,CAAlC,EAA0D;MACtDkR,OAAO,GAAG,KAAKb,iBAAL,CAAuBrQ,IAAvB,EAA6B;QAAE4P,YAAF;QAAgBE,UAAhB;QAA4BI;MAA5B,CAA7B,KAA4EgB,OAAtF;IACH;;IACD,OAAOA,OAAP;EACH;;EACDQ,QAAQ,CAACZ,OAAD,EAAUrO,KAAV,EAAiB;IACrB,MAAMkP,cAAc,GAAGb,OAAO,CAAC,gBAAD,CAA9B;IACA,OAAOa,cAAc,GAAGA,cAAc,CAAClP,KAAD,CAAd,CAAsBA,KAAzB,GAAiCA,KAAtD;EACH;;EACDmP,mBAAmB,GAAG;IAClB,OAAO,KAAKrK,EAAL,CAAQhB,aAAR,CAAsB7F,QAAtB,CAA+B,CAA/B,CAAP;EACH;;EACDmR,WAAW,GAAG;IACV,IAAI,KAAK7G,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL,CAA2B8G,WAA3B;IACH;;IACD,IAAI,KAAK3G,oBAAT,EAA+B;MAC3B,KAAKA,oBAAL,CAA0B2G,WAA1B;IACH;EACJ;;AAziBM;;AA2iBX/J,IAAI,CAACD,IAAL;EAAA,iBAAiGC,IAAjG,EA/tB6F5J,EA+tB7F,mBAAuHA,EAAE,CAAC4T,UAA1H,GA/tB6F5T,EA+tB7F,mBAAiJe,EAAE,CAAC8S,mBAApJ,MA/tB6F7T,EA+tB7F,mBAAoMe,EAAE,CAAC+S,aAAvM;AAAA;;AACAlK,IAAI,CAACC,IAAL,kBAhuB6F7J,EAguB7F;EAAA,MAAqF4J,IAArF;EAAA;EAAA;IAAA;MAhuB6F5J,EAguB7F,0BAAymDiB,aAAzmD;IAAA;;IAAA;MAAA;;MAhuB6FjB,EAguB7F,qBAhuB6FA,EAguB7F;IAAA;EAAA;EAAA;IAAA;MAhuB6FA,EAguB7F;MAhuB6FA,EAguB7F;MAhuB6FA,EAguB7F;IAAA;;IAAA;MAAA;;MAhuB6FA,EAguB7F,qBAhuB6FA,EAguB7F;MAhuB6FA,EAguB7F,qBAhuB6FA,EAguB7F;MAhuB6FA,EAguB7F,qBAhuB6FA,EAguB7F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAhuB6FA,EAguB7F;EAAA;EAAA;EAAA;EAAA;IAAA;MAhuB6FA,EAiuBrF,oDADR;MAhuB6FA,EA8wBrF,oDA9CR;IAAA;;IAAA;MAhuB6FA,EAmuBzC,oCAHpD;MAhuB6FA,EA8wBqD,aA9ClJ;MAhuB6FA,EA8wBqD,mCA9ClJ;IAAA;EAAA;EAAA,eA8Dy6oCa,EAAE,CAACiJ,OA9D56oC,EA8DugpCjJ,EAAE,CAACkJ,OA9D1gpC,EA8DoopClJ,EAAE,CAACmJ,IA9DvopC,EA8DwupCnJ,EAAE,CAACoJ,gBA9D3upC,EA8D+4pCpJ,EAAE,CAACqJ,OA9Dl5pC,EA8Do+pCnJ,EAAE,CAACE,aA9Dv+pC,EA8D2kqCM,EAAE,CAACwS,QA9D9kqC,EA8Ds+qCtS,UA9Dt+qC;EAAA;EAAA;AAAA;;AA+DA;EAAA,mDA/xB6FzB,EA+xB7F,mBAA2F4J,IAA3F,EAA6G,CAAC;IAClGQ,IAAI,EAAElK,SAD4F;IAElGmK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAZ;MAAsBC,QAAQ,EAAG;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA9DmB;MA8DZyJ,eAAe,EAAEzT,uBAAuB,CAAC0T,OA9D7B;MA8DsCzJ,aAAa,EAAErK,iBAAiB,CAACsK,IA9DvE;MA8D6EC,IAAI,EAAE;QAC9E,SAAS;MADqE,CA9DnF;MAgEIwJ,MAAM,EAAE,CAAC,41oCAAD;IAhEZ,CAAD;EAF4F,CAAD,CAA7G,EAmE4B,YAAY;IAChC,OAAO,CAAC;MAAE9J,IAAI,EAAEpK,EAAE,CAAC4T;IAAX,CAAD,EAA0B;MAAExJ,IAAI,EAAErJ,EAAE,CAAC8S,mBAAX;MAAgCjJ,UAAU,EAAE,CAAC;QAC9DR,IAAI,EAAE5J;MADwD,CAAD;IAA5C,CAA1B,EAEW;MAAE4J,IAAI,EAAErJ,EAAE,CAAC+S;IAAX,CAFX,CAAP;EAGH,CAvEL,EAuEuB;IAAE7R,KAAK,EAAE,CAAC;MACjBmI,IAAI,EAAE/J;IADW,CAAD,CAAT;IAEPoO,aAAa,EAAE,CAAC;MAChBrE,IAAI,EAAE/J;IADU,CAAD,CAFR;IAIP2O,SAAS,EAAE,CAAC;MACZ5E,IAAI,EAAE/J;IADM,CAAD,CAJJ;IAMPgI,KAAK,EAAE,CAAC;MACR+B,IAAI,EAAE/J;IADE,CAAD,CANA;IAQP8T,UAAU,EAAE,CAAC;MACb/J,IAAI,EAAE/J;IADO,CAAD,CARL;IAUP6H,WAAW,EAAE,CAAC;MACdkC,IAAI,EAAE/J;IADQ,CAAD,CAVN;IAYPgL,MAAM,EAAE,CAAC;MACTjB,IAAI,EAAE/J;IADG,CAAD,CAZD;IAcPiG,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAE/J;IADW,CAAD,CAdT;IAgBP+Q,cAAc,EAAE,CAAC;MACjBhH,IAAI,EAAE/J;IADW,CAAD,CAhBT;IAkBP4F,cAAc,EAAE,CAAC;MACjBmE,IAAI,EAAE/J;IADW,CAAD,CAlBT;IAoBPoG,cAAc,EAAE,CAAC;MACjB2D,IAAI,EAAE/J;IADW,CAAD,CApBT;IAsBPiL,gBAAgB,EAAE,CAAC;MACnBlB,IAAI,EAAE/J;IADa,CAAD,CAtBX;IAwBPkL,oBAAoB,EAAE,CAAC;MACvBnB,IAAI,EAAE/J;IADiB,CAAD,CAxBf;IA0BPmL,sBAAsB,EAAE,CAAC;MACzBpB,IAAI,EAAE/J;IADmB,CAAD,CA1BjB;IA4BP+T,OAAO,EAAE,CAAC;MACVhK,IAAI,EAAE/J;IADI,CAAD,CA5BF;IA8BPoL,WAAW,EAAE,CAAC;MACdrB,IAAI,EAAE/J;IADQ,CAAD,CA9BN;IAgCPqL,YAAY,EAAE,CAAC;MACftB,IAAI,EAAE/J;IADS,CAAD,CAhCP;IAkCPgU,SAAS,EAAE,CAAC;MACZjK,IAAI,EAAE/J;IADM,CAAD,CAlCJ;IAoCPiU,gBAAgB,EAAE,CAAC;MACnBlK,IAAI,EAAE/J;IADa,CAAD,CApCX;IAsCPkU,cAAc,EAAE,CAAC;MACjBnK,IAAI,EAAE/J;IADW,CAAD,CAtCT;IAwCPuE,YAAY,EAAE,CAAC;MACfwF,IAAI,EAAE/J;IADS,CAAD,CAxCP;IA0CP4O,MAAM,EAAE,CAAC;MACT7E,IAAI,EAAE/J;IADG,CAAD,CA1CD;IA4CPsL,QAAQ,EAAE,CAAC;MACXvB,IAAI,EAAE/J;IADK,CAAD,CA5CH;IA8CPuL,UAAU,EAAE,CAAC;MACbxB,IAAI,EAAE/J;IADO,CAAD,CA9CL;IAgDPmU,iBAAiB,EAAE,CAAC;MACpBpK,IAAI,EAAE/J;IADc,CAAD,CAhDZ;IAkDP6P,aAAa,EAAE,CAAC;MAChB9F,IAAI,EAAE/J;IADU,CAAD,CAlDR;IAoDPyR,YAAY,EAAE,CAAC;MACf1H,IAAI,EAAE/J;IADS,CAAD,CApDP;IAsDPoU,YAAY,EAAE,CAAC;MACfrK,IAAI,EAAE/J;IADS,CAAD,CAtDP;IAwDPwL,IAAI,EAAE,CAAC;MACPzB,IAAI,EAAE/J;IADC,CAAD,CAxDC;IA0DP6C,aAAa,EAAE,CAAC;MAChBkH,IAAI,EAAE/J;IADU,CAAD,CA1DR;IA4DPqU,qBAAqB,EAAE,CAAC;MACxBtK,IAAI,EAAE/J;IADkB,CAAD,CA5DhB;IA8DPsU,oBAAoB,EAAE,CAAC;MACvBvK,IAAI,EAAE/J;IADiB,CAAD,CA9Df;IAgEP6K,WAAW,EAAE,CAAC;MACdd,IAAI,EAAE/J;IADQ,CAAD,CAhEN;IAkEPyL,OAAO,EAAE,CAAC;MACV1B,IAAI,EAAE/J;IADI,CAAD,CAlEF;IAoEP2L,eAAe,EAAE,CAAC;MAClB5B,IAAI,EAAE3J;IADY,CAAD,CApEV;IAsEPwL,YAAY,EAAE,CAAC;MACf7B,IAAI,EAAE3J;IADS,CAAD,CAtEP;IAwEPyL,cAAc,EAAE,CAAC;MACjB9B,IAAI,EAAE3J;IADW,CAAD,CAxET;IA0EP2C,YAAY,EAAE,CAAC;MACfgH,IAAI,EAAE3J;IADS,CAAD,CA1EP;IA4EP8C,cAAc,EAAE,CAAC;MACjB6G,IAAI,EAAE3J;IADW,CAAD,CA5ET;IA8EP0L,uBAAuB,EAAE,CAAC;MAC1B/B,IAAI,EAAE3J;IADoB,CAAD,CA9ElB;IAgFPoE,UAAU,EAAE,CAAC;MACbuF,IAAI,EAAE3J;IADO,CAAD,CAhFL;IAkFP2L,UAAU,EAAE,CAAC;MACbhC,IAAI,EAAE3J;IADO,CAAD,CAlFL;IAoFP4L,QAAQ,EAAE,CAAC;MACXjC,IAAI,EAAE3J;IADK,CAAD,CApFH;IAsFP6L,mBAAmB,EAAE,CAAC;MACtBlC,IAAI,EAAE3J;IADgB,CAAD,CAtFd;IAwFP8L,QAAQ,EAAE,CAAC;MACXnC,IAAI,EAAE3J;IADK,CAAD,CAxFH;IA0FPiN,SAAS,EAAE,CAAC;MACZtD,IAAI,EAAE1J,eADM;MAEZ2J,IAAI,EAAE,CAACpJ,aAAD;IAFM,CAAD,CA1FJ;IA6FPqR,eAAe,EAAE,CAAC;MAClBlI,IAAI,EAAEzJ,SADY;MAElB0J,IAAI,EAAE,CAAC,QAAD;IAFY,CAAD,CA7FV;IAgGPmI,QAAQ,EAAE,CAAC;MACXpI,IAAI,EAAEzJ,SADK;MAEX0J,IAAI,EAAE,CAAC,UAAD;IAFK,CAAD,CAhGH;IAmGPuI,gBAAgB,EAAE,CAAC;MACnBxI,IAAI,EAAEzJ,SADa;MAEnB0J,IAAI,EAAE,CAAC,SAAD;IAFa,CAAD,CAnGX;IAsGPmC,iBAAiB,EAAE,CAAC;MACpBpC,IAAI,EAAE/J;IADc,CAAD;EAtGZ,CAvEvB;AAAA;;AAgLA,MAAMuU,UAAN,CAAiB;;AAEjBA,UAAU,CAACjL,IAAX;EAAA,iBAAuGiL,UAAvG;AAAA;;AACAA,UAAU,CAACC,IAAX,kBAl9B6F7U,EAk9B7F;EAAA,MAAwG4U;AAAxG;AACAA,UAAU,CAACE,IAAX,kBAn9B6F9U,EAm9B7F;EAAA,UAA8Hc,YAA9H,EAA4II,YAA5I,EAA0JI,YAA1J,EAAwKE,cAAxK,EAAwLN,YAAxL,EAAsMM,cAAtM;AAAA;;AACA;EAAA,mDAp9B6FxB,EAo9B7F,mBAA2F4U,UAA3F,EAAmH,CAAC;IACxGxK,IAAI,EAAExJ,QADkG;IAExGyJ,IAAI,EAAE,CAAC;MACC0K,OAAO,EAAE,CAACjU,YAAD,EAAeI,YAAf,EAA6BI,YAA7B,EAA2CE,cAA3C,CADV;MAECwT,OAAO,EAAE,CAACpL,IAAD,EAAO1I,YAAP,EAAqBM,cAArB,CAFV;MAGCyT,YAAY,EAAE,CAACrL,IAAD,EAAOnI,UAAP;IAHf,CAAD;EAFkG,CAAD,CAAnH;AAAA;AASA;AACA;AACA;;;AAEA,SAASmI,IAAT,EAAegL,UAAf,EAA2BnT,UAA3B"}, "metadata": {}, "sourceType": "module"}