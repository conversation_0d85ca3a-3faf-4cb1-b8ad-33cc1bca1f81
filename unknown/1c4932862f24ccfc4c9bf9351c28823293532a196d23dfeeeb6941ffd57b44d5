{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Optional, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\n\nclass Ripple {\n  constructor(el, zone, config) {\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n  }\n\n  ngAfterViewInit() {\n    if (this.config && this.config.ripple) {\n      this.zone.runOutsideAngular(() => {\n        this.create();\n        this.mouseDownListener = this.onMouseDown.bind(this);\n        this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n      });\n    }\n  }\n\n  onMouseDown(event) {\n    let ink = this.getInk();\n\n    if (!ink || getComputedStyle(ink, null).display === 'none') {\n      return;\n    }\n\n    DomHandler.removeClass(ink, 'p-ink-active');\n\n    if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n      let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n      ink.style.height = d + 'px';\n      ink.style.width = d + 'px';\n    }\n\n    let offset = DomHandler.getOffset(this.el.nativeElement);\n    let x = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n    let y = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n    ink.style.top = y + 'px';\n    ink.style.left = x + 'px';\n    DomHandler.addClass(ink, 'p-ink-active');\n  }\n\n  getInk() {\n    for (let i = 0; i < this.el.nativeElement.children.length; i++) {\n      if (this.el.nativeElement.children[i].className.indexOf('p-ink') !== -1) {\n        return this.el.nativeElement.children[i];\n      }\n    }\n\n    return null;\n  }\n\n  resetInk() {\n    let ink = this.getInk();\n\n    if (ink) {\n      DomHandler.removeClass(ink, 'p-ink-active');\n    }\n  }\n\n  onAnimationEnd(event) {\n    DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n  }\n\n  create() {\n    let ink = document.createElement('span');\n    ink.className = 'p-ink';\n    this.el.nativeElement.appendChild(ink);\n    this.animationListener = this.onAnimationEnd.bind(this);\n    ink.addEventListener('animationend', this.animationListener);\n  }\n\n  remove() {\n    let ink = this.getInk();\n\n    if (ink) {\n      this.el.nativeElement.removeEventListener('mousedown', this.mouseDownListener);\n      ink.removeEventListener('animationend', this.animationListener);\n      DomHandler.removeElement(ink);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.config && this.config.ripple) {\n      this.remove();\n    }\n  }\n\n}\n\nRipple.ɵfac = function Ripple_Factory(t) {\n  return new (t || Ripple)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig, 8));\n};\n\nRipple.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Ripple,\n  selectors: [[\"\", \"pRipple\", \"\"]],\n  hostAttrs: [1, \"p-ripple\", \"p-element\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Ripple, [{\n    type: Directive,\n    args: [{\n      selector: '[pRipple]',\n      host: {\n        'class': 'p-ripple p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.PrimeNGConfig,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\n\nclass RippleModule {}\n\nRippleModule.ɵfac = function RippleModule_Factory(t) {\n  return new (t || RippleModule)();\n};\n\nRippleModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: RippleModule\n});\nRippleModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RippleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Ripple],\n      declarations: [Ripple]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Ripple, RippleModule };", "map": {"version": 3, "names": ["i0", "Directive", "Optional", "NgModule", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "i1", "<PERSON><PERSON><PERSON>", "constructor", "el", "zone", "config", "ngAfterViewInit", "ripple", "runOutsideAngular", "create", "mouseDownListener", "onMouseDown", "bind", "nativeElement", "addEventListener", "event", "ink", "getInk", "getComputedStyle", "display", "removeClass", "getHeight", "getWidth", "d", "Math", "max", "getOuterWidth", "getOuterHeight", "style", "height", "width", "offset", "getOffset", "x", "pageX", "left", "document", "body", "scrollTop", "y", "pageY", "top", "scrollLeft", "addClass", "i", "children", "length", "className", "indexOf", "resetInk", "onAnimationEnd", "currentTarget", "createElement", "append<PERSON><PERSON><PERSON>", "animationListener", "remove", "removeEventListener", "removeElement", "ngOnDestroy", "ɵfac", "ElementRef", "NgZone", "PrimeNGConfig", "ɵdir", "type", "args", "selector", "host", "decorators", "RippleModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-ripple.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Optional, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\nimport * as i1 from 'primeng/api';\n\nclass Ripple {\n    constructor(el, zone, config) {\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n    }\n    ngAfterViewInit() {\n        if (this.config && this.config.ripple) {\n            this.zone.runOutsideAngular(() => {\n                this.create();\n                this.mouseDownListener = this.onMouseDown.bind(this);\n                this.el.nativeElement.addEventListener('mousedown', this.mouseDownListener);\n            });\n        }\n    }\n    onMouseDown(event) {\n        let ink = this.getInk();\n        if (!ink || getComputedStyle(ink, null).display === 'none') {\n            return;\n        }\n        DomHandler.removeClass(ink, 'p-ink-active');\n        if (!DomHandler.getHeight(ink) && !DomHandler.getWidth(ink)) {\n            let d = Math.max(DomHandler.getOuterWidth(this.el.nativeElement), DomHandler.getOuterHeight(this.el.nativeElement));\n            ink.style.height = d + 'px';\n            ink.style.width = d + 'px';\n        }\n        let offset = DomHandler.getOffset(this.el.nativeElement);\n        let x = event.pageX - offset.left + document.body.scrollTop - DomHandler.getWidth(ink) / 2;\n        let y = event.pageY - offset.top + document.body.scrollLeft - DomHandler.getHeight(ink) / 2;\n        ink.style.top = y + 'px';\n        ink.style.left = x + 'px';\n        DomHandler.addClass(ink, 'p-ink-active');\n    }\n    getInk() {\n        for (let i = 0; i < this.el.nativeElement.children.length; i++) {\n            if (this.el.nativeElement.children[i].className.indexOf('p-ink') !== -1) {\n                return this.el.nativeElement.children[i];\n            }\n        }\n        return null;\n    }\n    resetInk() {\n        let ink = this.getInk();\n        if (ink) {\n            DomHandler.removeClass(ink, 'p-ink-active');\n        }\n    }\n    onAnimationEnd(event) {\n        DomHandler.removeClass(event.currentTarget, 'p-ink-active');\n    }\n    create() {\n        let ink = document.createElement('span');\n        ink.className = 'p-ink';\n        this.el.nativeElement.appendChild(ink);\n        this.animationListener = this.onAnimationEnd.bind(this);\n        ink.addEventListener('animationend', this.animationListener);\n    }\n    remove() {\n        let ink = this.getInk();\n        if (ink) {\n            this.el.nativeElement.removeEventListener('mousedown', this.mouseDownListener);\n            ink.removeEventListener('animationend', this.animationListener);\n            DomHandler.removeElement(ink);\n        }\n    }\n    ngOnDestroy() {\n        if (this.config && this.config.ripple) {\n            this.remove();\n        }\n    }\n}\nRipple.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Ripple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nRipple.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Ripple, selector: \"[pRipple]\", host: { classAttribute: \"p-ripple p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Ripple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pRipple]',\n                    host: {\n                        'class': 'p-ripple p-element'\n                    }\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig, decorators: [{\n                        type: Optional\n                    }] }];\n    } });\nclass RippleModule {\n}\nRippleModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: RippleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nRippleModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: RippleModule, declarations: [Ripple], imports: [CommonModule], exports: [Ripple] });\nRippleModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: RippleModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: RippleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Ripple],\n                    declarations: [Ripple]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Ripple, RippleModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,QAA9B,QAA8C,eAA9C;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;;AAEA,MAAMC,MAAN,CAAa;EACTC,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAWC,MAAX,EAAmB;IAC1B,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,MAAL,GAAcA,MAAd;EACH;;EACDC,eAAe,GAAG;IACd,IAAI,KAAKD,MAAL,IAAe,KAAKA,MAAL,CAAYE,MAA/B,EAAuC;MACnC,KAAKH,IAAL,CAAUI,iBAAV,CAA4B,MAAM;QAC9B,KAAKC,MAAL;QACA,KAAKC,iBAAL,GAAyB,KAAKC,WAAL,CAAiBC,IAAjB,CAAsB,IAAtB,CAAzB;QACA,KAAKT,EAAL,CAAQU,aAAR,CAAsBC,gBAAtB,CAAuC,WAAvC,EAAoD,KAAKJ,iBAAzD;MACH,CAJD;IAKH;EACJ;;EACDC,WAAW,CAACI,KAAD,EAAQ;IACf,IAAIC,GAAG,GAAG,KAAKC,MAAL,EAAV;;IACA,IAAI,CAACD,GAAD,IAAQE,gBAAgB,CAACF,GAAD,EAAM,IAAN,CAAhB,CAA4BG,OAA5B,KAAwC,MAApD,EAA4D;MACxD;IACH;;IACDpB,UAAU,CAACqB,WAAX,CAAuBJ,GAAvB,EAA4B,cAA5B;;IACA,IAAI,CAACjB,UAAU,CAACsB,SAAX,CAAqBL,GAArB,CAAD,IAA8B,CAACjB,UAAU,CAACuB,QAAX,CAAoBN,GAApB,CAAnC,EAA6D;MACzD,IAAIO,CAAC,GAAGC,IAAI,CAACC,GAAL,CAAS1B,UAAU,CAAC2B,aAAX,CAAyB,KAAKvB,EAAL,CAAQU,aAAjC,CAAT,EAA0Dd,UAAU,CAAC4B,cAAX,CAA0B,KAAKxB,EAAL,CAAQU,aAAlC,CAA1D,CAAR;MACAG,GAAG,CAACY,KAAJ,CAAUC,MAAV,GAAmBN,CAAC,GAAG,IAAvB;MACAP,GAAG,CAACY,KAAJ,CAAUE,KAAV,GAAkBP,CAAC,GAAG,IAAtB;IACH;;IACD,IAAIQ,MAAM,GAAGhC,UAAU,CAACiC,SAAX,CAAqB,KAAK7B,EAAL,CAAQU,aAA7B,CAAb;IACA,IAAIoB,CAAC,GAAGlB,KAAK,CAACmB,KAAN,GAAcH,MAAM,CAACI,IAArB,GAA4BC,QAAQ,CAACC,IAAT,CAAcC,SAA1C,GAAsDvC,UAAU,CAACuB,QAAX,CAAoBN,GAApB,IAA2B,CAAzF;IACA,IAAIuB,CAAC,GAAGxB,KAAK,CAACyB,KAAN,GAAcT,MAAM,CAACU,GAArB,GAA2BL,QAAQ,CAACC,IAAT,CAAcK,UAAzC,GAAsD3C,UAAU,CAACsB,SAAX,CAAqBL,GAArB,IAA4B,CAA1F;IACAA,GAAG,CAACY,KAAJ,CAAUa,GAAV,GAAgBF,CAAC,GAAG,IAApB;IACAvB,GAAG,CAACY,KAAJ,CAAUO,IAAV,GAAiBF,CAAC,GAAG,IAArB;IACAlC,UAAU,CAAC4C,QAAX,CAAoB3B,GAApB,EAAyB,cAAzB;EACH;;EACDC,MAAM,GAAG;IACL,KAAK,IAAI2B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKzC,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BC,MAAnD,EAA2DF,CAAC,EAA5D,EAAgE;MAC5D,IAAI,KAAKzC,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BD,CAA/B,EAAkCG,SAAlC,CAA4CC,OAA5C,CAAoD,OAApD,MAAiE,CAAC,CAAtE,EAAyE;QACrE,OAAO,KAAK7C,EAAL,CAAQU,aAAR,CAAsBgC,QAAtB,CAA+BD,CAA/B,CAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH;;EACDK,QAAQ,GAAG;IACP,IAAIjC,GAAG,GAAG,KAAKC,MAAL,EAAV;;IACA,IAAID,GAAJ,EAAS;MACLjB,UAAU,CAACqB,WAAX,CAAuBJ,GAAvB,EAA4B,cAA5B;IACH;EACJ;;EACDkC,cAAc,CAACnC,KAAD,EAAQ;IAClBhB,UAAU,CAACqB,WAAX,CAAuBL,KAAK,CAACoC,aAA7B,EAA4C,cAA5C;EACH;;EACD1C,MAAM,GAAG;IACL,IAAIO,GAAG,GAAGoB,QAAQ,CAACgB,aAAT,CAAuB,MAAvB,CAAV;IACApC,GAAG,CAAC+B,SAAJ,GAAgB,OAAhB;IACA,KAAK5C,EAAL,CAAQU,aAAR,CAAsBwC,WAAtB,CAAkCrC,GAAlC;IACA,KAAKsC,iBAAL,GAAyB,KAAKJ,cAAL,CAAoBtC,IAApB,CAAyB,IAAzB,CAAzB;IACAI,GAAG,CAACF,gBAAJ,CAAqB,cAArB,EAAqC,KAAKwC,iBAA1C;EACH;;EACDC,MAAM,GAAG;IACL,IAAIvC,GAAG,GAAG,KAAKC,MAAL,EAAV;;IACA,IAAID,GAAJ,EAAS;MACL,KAAKb,EAAL,CAAQU,aAAR,CAAsB2C,mBAAtB,CAA0C,WAA1C,EAAuD,KAAK9C,iBAA5D;MACAM,GAAG,CAACwC,mBAAJ,CAAwB,cAAxB,EAAwC,KAAKF,iBAA7C;MACAvD,UAAU,CAAC0D,aAAX,CAAyBzC,GAAzB;IACH;EACJ;;EACD0C,WAAW,GAAG;IACV,IAAI,KAAKrD,MAAL,IAAe,KAAKA,MAAL,CAAYE,MAA/B,EAAuC;MACnC,KAAKgD,MAAL;IACH;EACJ;;AArEQ;;AAuEbtD,MAAM,CAAC0D,IAAP;EAAA,iBAAmG1D,MAAnG,EAAyFP,EAAzF,mBAA2HA,EAAE,CAACkE,UAA9H,GAAyFlE,EAAzF,mBAAqJA,EAAE,CAACmE,MAAxJ,GAAyFnE,EAAzF,mBAA2KM,EAAE,CAAC8D,aAA9K;AAAA;;AACA7D,MAAM,CAAC8D,IAAP,kBADyFrE,EACzF;EAAA,MAAuFO,MAAvF;EAAA;EAAA;AAAA;;AACA;EAAA,mDAFyFP,EAEzF,mBAA2FO,MAA3F,EAA+G,CAAC;IACpG+D,IAAI,EAAErE,SAD8F;IAEpGsE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,WADX;MAECC,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAF8F,CAAD,CAA/G,EAQ4B,YAAY;IAChC,OAAO,CAAC;MAAEH,IAAI,EAAEtE,EAAE,CAACkE;IAAX,CAAD,EAA0B;MAAEI,IAAI,EAAEtE,EAAE,CAACmE;IAAX,CAA1B,EAA+C;MAAEG,IAAI,EAAEhE,EAAE,CAAC8D,aAAX;MAA0BM,UAAU,EAAE,CAAC;QAC7EJ,IAAI,EAAEpE;MADuE,CAAD;IAAtC,CAA/C,CAAP;EAGH,CAZL;AAAA;;AAaA,MAAMyE,YAAN,CAAmB;;AAEnBA,YAAY,CAACV,IAAb;EAAA,iBAAyGU,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAlByF5E,EAkBzF;EAAA,MAA0G2E;AAA1G;AACAA,YAAY,CAACE,IAAb,kBAnByF7E,EAmBzF;EAAA,UAAkII,YAAlI;AAAA;;AACA;EAAA,mDApByFJ,EAoBzF,mBAA2F2E,YAA3F,EAAqH,CAAC;IAC1GL,IAAI,EAAEnE,QADoG;IAE1GoE,IAAI,EAAE,CAAC;MACCO,OAAO,EAAE,CAAC1E,YAAD,CADV;MAEC2E,OAAO,EAAE,CAACxE,MAAD,CAFV;MAGCyE,YAAY,EAAE,CAACzE,MAAD;IAHf,CAAD;EAFoG,CAAD,CAArH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,MAAT,EAAiBoE,YAAjB"}, "metadata": {}, "sourceType": "module"}