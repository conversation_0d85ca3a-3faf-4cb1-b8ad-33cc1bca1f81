{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n  return !!obj && (obj instanceof Observable || isFunction(obj.lift) && isFunction(obj.subscribe));\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "isObservable", "obj", "lift", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n    return !!obj && (obj instanceof Observable || (isFunction(obj.lift) && isFunction(obj.subscribe)));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,YAAT,CAAsBC,GAAtB,EAA2B;EAC9B,OAAO,CAAC,CAACA,GAAF,KAAUA,GAAG,YAAYH,UAAf,IAA8BC,UAAU,CAACE,GAAG,CAACC,IAAL,CAAV,IAAwBH,UAAU,CAACE,GAAG,CAACE,SAAL,CAA1E,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}