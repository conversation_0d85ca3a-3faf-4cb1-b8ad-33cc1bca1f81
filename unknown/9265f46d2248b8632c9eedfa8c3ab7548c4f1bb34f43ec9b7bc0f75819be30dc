{"ast": null, "code": "import { distinctUntilChanged } from './distinctUntilChanged';\nexport function distinctUntilKeyChanged(key, compare) {\n  return distinctUntilChanged((x, y) => compare ? compare(x[key], y[key]) : x[key] === y[key]);\n}", "map": {"version": 3, "names": ["distinctUntilChanged", "distinctUntilKeyChanged", "key", "compare", "x", "y"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/distinctUntilKeyChanged.js"], "sourcesContent": ["import { distinctUntilChanged } from './distinctUntilChanged';\nexport function distinctUntilKeyChanged(key, compare) {\n    return distinctUntilChanged((x, y) => compare ? compare(x[key], y[key]) : x[key] === y[key]);\n}\n"], "mappings": "AAAA,SAASA,oBAAT,QAAqC,wBAArC;AACA,OAAO,SAASC,uBAAT,CAAiCC,GAAjC,EAAsCC,OAAtC,EAA+C;EAClD,OAAOH,oBAAoB,CAAC,CAACI,CAAD,EAAIC,CAAJ,KAAUF,OAAO,GAAGA,OAAO,CAACC,CAAC,CAACF,GAAD,CAAF,EAASG,CAAC,CAACH,GAAD,CAAV,CAAV,GAA6BE,CAAC,CAACF,GAAD,CAAD,KAAWG,CAAC,CAACH,GAAD,CAA3D,CAA3B;AACH"}, "metadata": {}, "sourceType": "module"}