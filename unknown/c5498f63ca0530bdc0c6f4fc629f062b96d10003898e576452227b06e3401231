{"ast": null, "code": "import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { connect } from './connect';\nexport function publish(selector) {\n  return selector ? source => connect(selector)(source) : source => multicast(new Subject())(source);\n}", "map": {"version": 3, "names": ["Subject", "multicast", "connect", "publish", "selector", "source"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/publish.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { connect } from './connect';\nexport function publish(selector) {\n    return selector ? (source) => connect(selector)(source) : (source) => multicast(new Subject())(source);\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,OAAO,SAASC,OAAT,CAAiBC,QAAjB,EAA2B;EAC9B,OAAOA,QAAQ,GAAIC,MAAD,IAAYH,OAAO,CAACE,QAAD,CAAP,CAAkBC,MAAlB,CAAf,GAA4CA,MAAD,IAAYJ,SAAS,CAAC,IAAID,OAAJ,EAAD,CAAT,CAAyBK,MAAzB,CAAtE;AACH"}, "metadata": {}, "sourceType": "module"}