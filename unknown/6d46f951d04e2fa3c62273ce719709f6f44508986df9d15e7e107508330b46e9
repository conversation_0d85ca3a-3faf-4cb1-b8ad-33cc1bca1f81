{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n  return operate((source, subscriber) => {\n    let state = seed;\n    switchMap((value, index) => accumulator(state, value, index), (_, innerValue) => (state = innerValue, innerValue))(source).subscribe(subscriber);\n    return () => {\n      state = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["switchMap", "operate", "switchScan", "accumulator", "seed", "source", "subscriber", "state", "value", "index", "_", "innerValue", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/switchScan.js"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n    return operate((source, subscriber) => {\n        let state = seed;\n        switchMap((value, index) => accumulator(state, value, index), (_, innerValue) => ((state = innerValue), innerValue))(source).subscribe(subscriber);\n        return () => {\n            state = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,aAA1B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,OAAO,SAASC,UAAT,CAAoBC,WAApB,EAAiCC,IAAjC,EAAuC;EAC1C,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAGH,IAAZ;IACAJ,SAAS,CAAC,CAACQ,KAAD,EAAQC,KAAR,KAAkBN,WAAW,CAACI,KAAD,EAAQC,KAAR,EAAeC,KAAf,CAA9B,EAAqD,CAACC,CAAD,EAAIC,UAAJ,MAAqBJ,KAAK,GAAGI,UAAT,EAAsBA,UAA1C,CAArD,CAAT,CAAqHN,MAArH,EAA6HO,SAA7H,CAAuIN,UAAvI;IACA,OAAO,MAAM;MACTC,KAAK,GAAG,IAAR;IACH,CAFD;EAGH,CANa,CAAd;AAOH"}, "metadata": {}, "sourceType": "module"}