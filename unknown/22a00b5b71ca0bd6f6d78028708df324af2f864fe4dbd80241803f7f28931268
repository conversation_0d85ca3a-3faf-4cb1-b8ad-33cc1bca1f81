{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n  return operate((source, subscriber) => {\n    let window;\n    let closingSubscriber;\n\n    const handleError = err => {\n      window.error(err);\n      subscriber.error(err);\n    };\n\n    const openWindow = () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window === null || window === void 0 ? void 0 : window.complete();\n      window = new Subject();\n      subscriber.next(window.asObservable());\n      let closingNotifier;\n\n      try {\n        closingNotifier = innerFrom(closingSelector());\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n\n      closingNotifier.subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError));\n    };\n\n    openWindow();\n    source.subscribe(createOperatorSubscriber(subscriber, value => window.next(value), () => {\n      window.complete();\n      subscriber.complete();\n    }, handleError, () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      window = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "innerFrom", "windowWhen", "closingSelector", "source", "subscriber", "window", "closingSubscriber", "handleError", "err", "error", "openWindow", "unsubscribe", "complete", "next", "asObservable", "closingNotifier", "subscribe", "value"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/windowWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function windowWhen(closingSelector) {\n    return operate((source, subscriber) => {\n        let window;\n        let closingSubscriber;\n        const handleError = (err) => {\n            window.error(err);\n            subscriber.error(err);\n        };\n        const openWindow = () => {\n            closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n            window === null || window === void 0 ? void 0 : window.complete();\n            window = new Subject();\n            subscriber.next(window.asObservable());\n            let closingNotifier;\n            try {\n                closingNotifier = innerFrom(closingSelector());\n            }\n            catch (err) {\n                handleError(err);\n                return;\n            }\n            closingNotifier.subscribe((closingSubscriber = createOperatorSubscriber(subscriber, openWindow, openWindow, handleError)));\n        };\n        openWindow();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => window.next(value), () => {\n            window.complete();\n            subscriber.complete();\n        }, handleError, () => {\n            closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n            window = null;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,OAAO,SAASC,UAAT,CAAoBC,eAApB,EAAqC;EACxC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,MAAJ;IACA,IAAIC,iBAAJ;;IACA,MAAMC,WAAW,GAAIC,GAAD,IAAS;MACzBH,MAAM,CAACI,KAAP,CAAaD,GAAb;MACAJ,UAAU,CAACK,KAAX,CAAiBD,GAAjB;IACH,CAHD;;IAIA,MAAME,UAAU,GAAG,MAAM;MACrBJ,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;MACAN,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAACO,QAAP,EAAhD;MACAP,MAAM,GAAG,IAAIR,OAAJ,EAAT;MACAO,UAAU,CAACS,IAAX,CAAgBR,MAAM,CAACS,YAAP,EAAhB;MACA,IAAIC,eAAJ;;MACA,IAAI;QACAA,eAAe,GAAGf,SAAS,CAACE,eAAe,EAAhB,CAA3B;MACH,CAFD,CAGA,OAAOM,GAAP,EAAY;QACRD,WAAW,CAACC,GAAD,CAAX;QACA;MACH;;MACDO,eAAe,CAACC,SAAhB,CAA2BV,iBAAiB,GAAGP,wBAAwB,CAACK,UAAD,EAAaM,UAAb,EAAyBA,UAAzB,EAAqCH,WAArC,CAAvE;IACH,CAdD;;IAeAG,UAAU;IACVP,MAAM,CAACa,SAAP,CAAiBjB,wBAAwB,CAACK,UAAD,EAAca,KAAD,IAAWZ,MAAM,CAACQ,IAAP,CAAYI,KAAZ,CAAxB,EAA4C,MAAM;MACvFZ,MAAM,CAACO,QAAP;MACAR,UAAU,CAACQ,QAAX;IACH,CAHwC,EAGtCL,WAHsC,EAGzB,MAAM;MAClBD,iBAAiB,KAAK,IAAtB,IAA8BA,iBAAiB,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,iBAAiB,CAACK,WAAlB,EAAtE;MACAN,MAAM,GAAG,IAAT;IACH,CANwC,CAAzC;EAOH,CA9Ba,CAAd;AA+BH"}, "metadata": {}, "sourceType": "module"}