{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, Component, ViewEncapsulation, Inject, Input, EventEmitter, ChangeDetectionStrategy, ViewChild, Output, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i5 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i4 from 'primeng/api';\nconst _c0 = [\"pMenuItemContent\", \"\"];\n\nfunction MenuItemContent_a_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.item.iconClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.item.icon)(\"ngStyle\", ctx_r2.item.iconStyle);\n  }\n}\n\nfunction MenuItemContent_a_0_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.item.label);\n  }\n}\n\nfunction MenuItemContent_a_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r5.item.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MenuItemContent_a_0_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r6.item.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.item.badge);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\n\nfunction MenuItemContent_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 2);\n    i0.ɵɵlistener(\"keydown\", function MenuItemContent_a_0_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onItemKeyDown($event));\n    })(\"click\", function MenuItemContent_a_0_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.menu.itemClick($event, ctx_r9.item));\n    });\n    i0.ɵɵtemplate(1, MenuItemContent_a_0_span_1_Template, 1, 4, \"span\", 3);\n    i0.ɵɵtemplate(2, MenuItemContent_a_0_span_2_Template, 2, 1, \"span\", 4);\n    i0.ɵɵtemplate(3, MenuItemContent_a_0_ng_template_3_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MenuItemContent_a_0_span_5_Template, 2, 2, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r4 = i0.ɵɵreference(4);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r0.item.target)(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, ctx_r0.item.disabled))(\"target\", ctx_r0.item.target);\n    i0.ɵɵattribute(\"href\", ctx_r0.item.url || null, i0.ɵɵsanitizeUrl)(\"tabindex\", ctx_r0.item.disabled ? null : \"0\")(\"data-automationid\", ctx_r0.item.automationId)(\"title\", ctx_r0.item.title)(\"id\", ctx_r0.item.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.escape !== false)(\"ngIfElse\", _r4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.item.badge);\n  }\n}\n\nfunction MenuItemContent_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.item.icon);\n  }\n}\n\nfunction MenuItemContent_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r11.item.label);\n  }\n}\n\nfunction MenuItemContent_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r13.item.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MenuItemContent_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.item.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r14.item.badge);\n  }\n}\n\nconst _c2 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction MenuItemContent_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"keydown\", function MenuItemContent_a_1_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onItemKeyDown($event));\n    })(\"click\", function MenuItemContent_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.menu.itemClick($event, ctx_r17.item));\n    });\n    i0.ɵɵtemplate(1, MenuItemContent_a_1_span_1_Template, 1, 1, \"span\", 12);\n    i0.ɵɵtemplate(2, MenuItemContent_a_1_span_2_Template, 2, 1, \"span\", 4);\n    i0.ɵɵtemplate(3, MenuItemContent_a_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MenuItemContent_a_1_span_5_Template, 2, 2, \"span\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r12 = i0.ɵɵreference(4);\n\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r1.item.routerLink)(\"queryParams\", ctx_r1.item.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r1.item.routerLinkActiveOptions || i0.ɵɵpureFunction0(20, _c2))(\"target\", ctx_r1.item.target)(\"ngClass\", i0.ɵɵpureFunction1(21, _c1, ctx_r1.item.disabled))(\"fragment\", ctx_r1.item.fragment)(\"queryParamsHandling\", ctx_r1.item.queryParamsHandling)(\"preserveFragment\", ctx_r1.item.preserveFragment)(\"skipLocationChange\", ctx_r1.item.skipLocationChange)(\"replaceUrl\", ctx_r1.item.replaceUrl)(\"state\", ctx_r1.item.state);\n    i0.ɵɵattribute(\"data-automationid\", ctx_r1.item.automationId)(\"id\", ctx_r1.item.id)(\"tabindex\", ctx_r1.item.disabled ? null : \"0\")(\"title\", ctx_r1.item.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.escape !== false)(\"ngIfElse\", _r12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.item.badge);\n  }\n}\n\nconst _c3 = [\"container\"];\n\nconst _c4 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\n\nfunction Menu_div_0_3_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n\n  if (rf & 2) {\n    const submenu_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c4, submenu_r5.visible === false));\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_li_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const submenu_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(submenu_r5.label);\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_li_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n\n  if (rf & 2) {\n    const submenu_r5 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", submenu_r5.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵtemplate(1, Menu_div_0_3_ng_template_0_li_1_span_1_Template, 2, 1, \"span\", 10);\n    i0.ɵɵtemplate(2, Menu_div_0_3_ng_template_0_li_1_ng_template_2_Template, 1, 1, \"ng-template\", null, 11, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(3);\n\n    const submenu_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c4, submenu_r5.visible === false))(\"tooltipOptions\", submenu_r5.tooltipOptions);\n    i0.ɵɵattribute(\"data-automationid\", submenu_r5.automationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", submenu_r5.escape !== false)(\"ngIfElse\", _r11);\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n\n  if (rf & 2) {\n    const item_r16 = i0.ɵɵnextContext().$implicit;\n    const submenu_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c4, item_r16.visible === false || submenu_r5.visible === false));\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r16 = i0.ɵɵnextContext().$implicit;\n    const submenu_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(item_r16.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r16)(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, item_r16.visible === false || submenu_r5.visible === false))(\"ngStyle\", item_r16.style)(\"tooltipOptions\", item_r16.tooltipOptions);\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_3_ng_template_0_ng_template_2_li_0_Template, 1, 3, \"li\", 6);\n    i0.ɵɵtemplate(1, Menu_div_0_3_ng_template_0_ng_template_2_li_1_Template, 1, 8, \"li\", 13);\n  }\n\n  if (rf & 2) {\n    const item_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r16.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r16.separator);\n  }\n}\n\nfunction Menu_div_0_3_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_3_ng_template_0_li_0_Template, 1, 3, \"li\", 6);\n    i0.ɵɵtemplate(1, Menu_div_0_3_ng_template_0_li_1_Template, 4, 7, \"li\", 7);\n    i0.ɵɵtemplate(2, Menu_div_0_3_ng_template_0_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n  }\n\n  if (rf & 2) {\n    const submenu_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", submenu_r5.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !submenu_r5.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", submenu_r5.items);\n  }\n}\n\nfunction Menu_div_0_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_3_ng_template_0_Template, 3, 3, \"ng-template\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.model);\n  }\n}\n\nfunction Menu_div_0_4_ng_template_0_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n\n  if (rf & 2) {\n    const item_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c4, item_r24.visible === false));\n  }\n}\n\nfunction Menu_div_0_4_ng_template_0_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassMap(item_r24.styleClass);\n    i0.ɵɵproperty(\"pMenuItemContent\", item_r24)(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, item_r24.visible === false))(\"ngStyle\", item_r24.style)(\"tooltipOptions\", item_r24.tooltipOptions);\n  }\n}\n\nfunction Menu_div_0_4_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_4_ng_template_0_li_0_Template, 1, 3, \"li\", 6);\n    i0.ɵɵtemplate(1, Menu_div_0_4_ng_template_0_li_1_Template, 1, 8, \"li\", 13);\n  }\n\n  if (rf & 2) {\n    const item_r24 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r24.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r24.separator);\n  }\n}\n\nfunction Menu_div_0_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menu_div_0_4_ng_template_0_Template, 2, 2, \"ng-template\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.model);\n  }\n}\n\nconst _c5 = function (a1) {\n  return {\n    \"p-menu p-component\": true,\n    \"p-menu-overlay\": a1\n  };\n};\n\nconst _c6 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c7 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction Menu_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1, 2);\n    i0.ɵɵlistener(\"click\", function Menu_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function Menu_div_0_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Menu_div_0_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(2, \"ul\", 3);\n    i0.ɵɵtemplate(3, Menu_div_0_3_Template, 1, 1, null, 4);\n    i0.ɵɵtemplate(4, Menu_div_0_4_Template, 1, 1, null, 4);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c5, ctx_r0.popup))(\"ngStyle\", ctx_r0.style)(\"@overlayAnimation\", i0.ɵɵpureFunction1(13, _c7, i0.ɵɵpureFunction2(10, _c6, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)))(\"@.disabled\", ctx_r0.popup !== true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.hasSubMenu());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.hasSubMenu());\n  }\n}\n\nclass MenuItemContent {\n  constructor(menu) {\n    this.menu = menu;\n  }\n\n  onItemKeyDown(event) {\n    let listItem = event.currentTarget.parentElement;\n\n    switch (event.code) {\n      case 'ArrowDown':\n        var nextItem = this.findNextItem(listItem);\n\n        if (nextItem) {\n          nextItem.children[0].focus();\n        }\n\n        event.preventDefault();\n        break;\n\n      case 'ArrowUp':\n        var prevItem = this.findPrevItem(listItem);\n\n        if (prevItem) {\n          prevItem.children[0].focus();\n        }\n\n        event.preventDefault();\n        break;\n\n      case 'Space':\n      case 'Enter':\n        if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n          listItem.children[0].click();\n        }\n\n        event.preventDefault();\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  findNextItem(item) {\n    let nextItem = item.nextElementSibling;\n    if (nextItem) return DomHandler.hasClass(nextItem, 'p-disabled') || !DomHandler.hasClass(nextItem, 'p-menuitem') ? this.findNextItem(nextItem) : nextItem;else return null;\n  }\n\n  findPrevItem(item) {\n    let prevItem = item.previousElementSibling;\n    if (prevItem) return DomHandler.hasClass(prevItem, 'p-disabled') || !DomHandler.hasClass(prevItem, 'p-menuitem') ? this.findPrevItem(prevItem) : prevItem;else return null;\n  }\n\n}\n\nMenuItemContent.ɵfac = function MenuItemContent_Factory(t) {\n  return new (t || MenuItemContent)(i0.ɵɵdirectiveInject(forwardRef(() => Menu)));\n};\n\nMenuItemContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MenuItemContent,\n  selectors: [[\"\", \"pMenuItemContent\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    item: [\"pMenuItemContent\", \"item\"]\n  },\n  attrs: _c0,\n  decls: 2,\n  vars: 2,\n  consts: [[\"class\", \"p-menuitem-link\", \"role\", \"menuitem\", 3, \"target\", \"ngClass\", \"keydown\", \"click\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", \"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\", 4, \"ngIf\"], [\"role\", \"menuitem\", 1, \"p-menuitem-link\", 3, \"target\", \"ngClass\", \"keydown\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", 4, \"ngIf\"], [\"htmlRouteLabel\", \"\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"]],\n  template: function MenuItemContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, MenuItemContent_a_0_Template, 6, 14, \"a\", 0);\n      i0.ɵɵtemplate(1, MenuItemContent_a_1_Template, 6, 23, \"a\", 1);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.item.routerLink);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.item.routerLink);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Ripple],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuItemContent, [{\n    type: Component,\n    args: [{\n      selector: '[pMenuItemContent]',\n      template: `\n        <a *ngIf=\"!item.routerLink\"  (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"item.url||null\" class=\"p-menuitem-link\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.data-automationid]=\"item.automationId\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" [target]=\"item.target\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [class]=\"item.iconClass\" [ngStyle]=\"item.iconStyle\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n        <a *ngIf=\"item.routerLink\" (keydown)=\"onItemKeyDown($event)\" [routerLink]=\"item.routerLink\" [attr.data-automationid]=\"item.automationId\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"\n            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n            [attr.title]=\"item.title\" [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" pRipple\n            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => Menu)]\n      }]\n    }];\n  }, {\n    item: [{\n      type: Input,\n      args: [\"pMenuItemContent\"]\n    }]\n  });\n})();\n\nclass Menu {\n  constructor(el, renderer, cd, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n  }\n\n  toggle(event) {\n    if (this.visible) this.hide();else this.show(event);\n    this.preventDocumentDefault = true;\n  }\n\n  show(event) {\n    this.target = event.currentTarget;\n    this.relativeAlign = event.relativeAlign;\n    this.visible = true;\n    this.preventDocumentDefault = true;\n    this.cd.markForCheck();\n  }\n\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (this.popup) {\n          this.container = event.element;\n          this.moveOnTop();\n          this.onShow.emit({});\n          this.appendOverlay();\n          this.alignOverlay();\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n        }\n\n        break;\n\n      case 'void':\n        this.onOverlayHide();\n        this.onHide.emit({});\n        break;\n    }\n  }\n\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(event.element);\n        }\n\n        break;\n    }\n  }\n\n  alignOverlay() {\n    if (this.relativeAlign) DomHandler.relativePosition(this.container, this.target);else DomHandler.absolutePosition(this.container, this.target);\n  }\n\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.container);else DomHandler.appendChild(this.container, this.appendTo);\n    }\n  }\n\n  restoreOverlayAppend() {\n    if (this.container && this.appendTo) {\n      this.el.nativeElement.appendChild(this.container);\n    }\n  }\n\n  moveOnTop() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n\n  hide() {\n    this.visible = false;\n    this.relativeAlign = false;\n    this.cd.markForCheck();\n  }\n\n  onWindowResize() {\n    if (this.visible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (this.popup) {\n      this.hide();\n    }\n  }\n\n  onOverlayClick(event) {\n    if (this.popup) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n\n    this.preventDocumentDefault = true;\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n        if (!this.preventDocumentDefault) {\n          this.hide();\n        }\n\n        this.preventDocumentDefault = false;\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n        if (this.visible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.preventDocumentDefault = false;\n\n    if (!this.cd.destroyed) {\n      this.target = null;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.popup) {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n  }\n\n  hasSubMenu() {\n    if (this.model) {\n      for (var item of this.model) {\n        if (item.items) {\n          return true;\n        }\n      }\n    }\n\n    return false;\n  }\n\n}\n\nMenu.ɵfac = function Menu_Factory(t) {\n  return new (t || Menu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.PrimeNGConfig), i0.ɵɵdirectiveInject(i4.OverlayService));\n};\n\nMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Menu,\n  selectors: [[\"p-menu\"]],\n  viewQuery: function Menu_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c3, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    popup: \"popup\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    appendTo: \"appendTo\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\"\n  },\n  outputs: {\n    onShow: \"onShow\",\n    onHide: \"onHide\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"class\", \"ngStyle\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [\"container\", \"\"], [\"role\", \"menu\", 1, \"p-menu-list\", \"p-reset\"], [4, \"ngIf\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menu-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-header\", \"pTooltip\", \"\", \"role\", \"none\", 3, \"ngClass\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menu-separator\", 3, \"ngClass\"], [\"pTooltip\", \"\", \"role\", \"none\", 1, \"p-submenu-header\", 3, \"ngClass\", \"tooltipOptions\"], [4, \"ngIf\", \"ngIfElse\"], [\"htmlSubmenuLabel\", \"\"], [3, \"innerHTML\"], [\"class\", \"p-menuitem\", \"pTooltip\", \"\", \"role\", \"none\", 3, \"pMenuItemContent\", \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"none\", 1, \"p-menuitem\", 3, \"pMenuItemContent\", \"ngClass\", \"ngStyle\", \"tooltipOptions\"]],\n  template: function Menu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, Menu_div_0_Template, 5, 15, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", !ctx.popup || ctx.visible);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i5.Tooltip, MenuItemContent],\n  styles: [\".p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{display:flex;align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('overlayAnimation', [transition(':enter', [style({\n      opacity: 0,\n      transform: 'scaleY(0.8)'\n    }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menu, [{\n    type: Component,\n    args: [{\n      selector: 'p-menu',\n      template: `\n        <div #container [ngClass]=\"{'p-menu p-component': true, 'p-menu-overlay': popup}\"\n            [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"!popup || visible\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n            <ul class=\"p-menu-list p-reset\" role=\"menu\">\n                <ng-template ngFor let-submenu [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-submenu-header\" [attr.data-automationid]=\"submenu.automationId\" *ngIf=\"!submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" pTooltip [tooltipOptions]=\"submenu.tooltipOptions\" role=\"none\">\n                        <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{submenu.label}}</span>\n                        <ng-template #htmlSubmenuLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                    </li>\n                    <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                        <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\"  role=\"separator\"></li>\n                        <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': item.visible === false}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{display:flex;align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i4.PrimeNGConfig\n    }, {\n      type: i4.OverlayService\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    popup: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }]\n  });\n})();\n\nclass MenuModule {}\n\nMenuModule.ɵfac = function MenuModule_Factory(t) {\n  return new (t || MenuModule)();\n};\n\nMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MenuModule\n});\nMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n      exports: [Menu, RouterModule, TooltipModule],\n      declarations: [Menu, MenuItemContent]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Menu, MenuItemContent, MenuModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "Component", "ViewEncapsulation", "Inject", "Input", "EventEmitter", "ChangeDetectionStrategy", "ViewChild", "Output", "NgModule", "trigger", "transition", "style", "animate", "i1", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ZIndexUtils", "i2", "RouterModule", "i3", "RippleModule", "i5", "TooltipModule", "i4", "MenuItemContent", "constructor", "menu", "onItemKeyDown", "event", "listItem", "currentTarget", "parentElement", "code", "nextItem", "findNextItem", "children", "focus", "preventDefault", "prevItem", "findPrevItem", "hasClass", "click", "item", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "ɵfac", "<PERSON><PERSON>", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "encapsulation", "None", "host", "undefined", "decorators", "el", "renderer", "cd", "config", "overlayService", "autoZIndex", "baseZIndex", "showTransitionOptions", "hideTransitionOptions", "onShow", "onHide", "toggle", "visible", "hide", "show", "preventDocumentDefault", "target", "relativeAlign", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOverlayAnimationStart", "toState", "popup", "container", "element", "moveOnTop", "emit", "appendOverlay", "alignOverlay", "bindDocumentClickListener", "bindDocumentResizeListener", "bindScrollListener", "onOverlayHide", "onOverlayAnimationEnd", "clear", "relativePosition", "absolutePosition", "appendTo", "document", "body", "append<PERSON><PERSON><PERSON>", "restoreOverlayAppend", "nativeElement", "set", "zIndex", "onWindowResize", "isTouchDevice", "itemClick", "disabled", "url", "routerLink", "command", "originalEvent", "onOverlayClick", "add", "documentClickListener", "documentTarget", "ownerDocument", "listen", "unbindDocumentClickListener", "documentResizeListener", "bind", "window", "addEventListener", "unbindDocumentResizeListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "destroyed", "ngOnDestroy", "destroy", "hasSubMenu", "model", "items", "ElementRef", "Renderer2", "ChangeDetectorRef", "PrimeNGConfig", "OverlayService", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "opacity", "transform", "animations", "changeDetection", "OnPush", "styles", "styleClass", "containerViewChild", "MenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, Component, ViewEncapsulation, Inject, Input, EventEmitter, ChangeDetectionStrategy, ViewChild, Output, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i5 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i4 from 'primeng/api';\n\nclass MenuItemContent {\n    constructor(menu) {\n        this.menu = menu;\n    }\n    onItemKeyDown(event) {\n        let listItem = event.currentTarget.parentElement;\n        switch (event.code) {\n            case 'ArrowDown':\n                var nextItem = this.findNextItem(listItem);\n                if (nextItem) {\n                    nextItem.children[0].focus();\n                }\n                event.preventDefault();\n                break;\n            case 'ArrowUp':\n                var prevItem = this.findPrevItem(listItem);\n                if (prevItem) {\n                    prevItem.children[0].focus();\n                }\n                event.preventDefault();\n                break;\n            case 'Space':\n            case 'Enter':\n                if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n                    listItem.children[0].click();\n                }\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n    findNextItem(item) {\n        let nextItem = item.nextElementSibling;\n        if (nextItem)\n            return DomHandler.hasClass(nextItem, 'p-disabled') || !DomHandler.hasClass(nextItem, 'p-menuitem') ? this.findNextItem(nextItem) : nextItem;\n        else\n            return null;\n    }\n    findPrevItem(item) {\n        let prevItem = item.previousElementSibling;\n        if (prevItem)\n            return DomHandler.hasClass(prevItem, 'p-disabled') || !DomHandler.hasClass(prevItem, 'p-menuitem') ? this.findPrevItem(prevItem) : prevItem;\n        else\n            return null;\n    }\n}\nMenuItemContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuItemContent, deps: [{ token: forwardRef(() => Menu) }], target: i0.ɵɵFactoryTarget.Component });\nMenuItemContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: MenuItemContent, selector: \"[pMenuItemContent]\", inputs: { item: [\"pMenuItemContent\", \"item\"] }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <a *ngIf=\"!item.routerLink\"  (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"item.url||null\" class=\"p-menuitem-link\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.data-automationid]=\"item.automationId\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" [target]=\"item.target\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [class]=\"item.iconClass\" [ngStyle]=\"item.iconStyle\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n        <a *ngIf=\"item.routerLink\" (keydown)=\"onItemKeyDown($event)\" [routerLink]=\"item.routerLink\" [attr.data-automationid]=\"item.automationId\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"\n            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n            [attr.title]=\"item.title\" [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" pRipple\n            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuItemContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[pMenuItemContent]',\n                    template: `\n        <a *ngIf=\"!item.routerLink\"  (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"item.url||null\" class=\"p-menuitem-link\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.data-automationid]=\"item.automationId\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" [target]=\"item.target\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [class]=\"item.iconClass\" [ngStyle]=\"item.iconStyle\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n        <a *ngIf=\"item.routerLink\" (keydown)=\"onItemKeyDown($event)\" [routerLink]=\"item.routerLink\" [attr.data-automationid]=\"item.automationId\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\"\n            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n            [attr.title]=\"item.title\" [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"menu.itemClick($event, item)\" role=\"menuitem\" pRipple\n            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\"></span>\n            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n        </a>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [forwardRef(() => Menu)]\n                    }] }];\n    }, propDecorators: { item: [{\n                type: Input,\n                args: [\"pMenuItemContent\"]\n            }] } });\nclass Menu {\n    constructor(el, renderer, cd, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n    }\n    toggle(event) {\n        if (this.visible)\n            this.hide();\n        else\n            this.show(event);\n        this.preventDocumentDefault = true;\n    }\n    show(event) {\n        this.target = event.currentTarget;\n        this.relativeAlign = event.relativeAlign;\n        this.visible = true;\n        this.preventDocumentDefault = true;\n        this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (this.popup) {\n                    this.container = event.element;\n                    this.moveOnTop();\n                    this.onShow.emit({});\n                    this.appendOverlay();\n                    this.alignOverlay();\n                    this.bindDocumentClickListener();\n                    this.bindDocumentResizeListener();\n                    this.bindScrollListener();\n                }\n                break;\n            case 'void':\n                this.onOverlayHide();\n                this.onHide.emit({});\n                break;\n        }\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(event.element);\n                }\n                break;\n        }\n    }\n    alignOverlay() {\n        if (this.relativeAlign)\n            DomHandler.relativePosition(this.container, this.target);\n        else\n            DomHandler.absolutePosition(this.container, this.target);\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.container);\n            else\n                DomHandler.appendChild(this.container, this.appendTo);\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.container && this.appendTo) {\n            this.el.nativeElement.appendChild(this.container);\n        }\n    }\n    moveOnTop() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('menu', this.container, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    hide() {\n        this.visible = false;\n        this.relativeAlign = false;\n        this.cd.markForCheck();\n    }\n    onWindowResize() {\n        if (this.visible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    itemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (this.popup) {\n            this.hide();\n        }\n    }\n    onOverlayClick(event) {\n        if (this.popup) {\n            this.overlayService.add({\n                originalEvent: event,\n                target: this.el.nativeElement\n            });\n        }\n        this.preventDocumentDefault = true;\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n                if (!this.preventDocumentDefault) {\n                    this.hide();\n                }\n                this.preventDocumentDefault = false;\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.target, () => {\n                if (this.visible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    onOverlayHide() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.preventDocumentDefault = false;\n        if (!this.cd.destroyed) {\n            this.target = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.popup) {\n            if (this.scrollHandler) {\n                this.scrollHandler.destroy();\n                this.scrollHandler = null;\n            }\n            if (this.container && this.autoZIndex) {\n                ZIndexUtils.clear(this.container);\n            }\n            this.restoreOverlayAppend();\n            this.onOverlayHide();\n        }\n    }\n    hasSubMenu() {\n        if (this.model) {\n            for (var item of this.model) {\n                if (item.items) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    }\n}\nMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Menu, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i4.PrimeNGConfig }, { token: i4.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Menu, selector: \"p-menu\", inputs: { model: \"model\", popup: \"popup\", style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div #container [ngClass]=\"{'p-menu p-component': true, 'p-menu-overlay': popup}\"\n            [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"!popup || visible\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n            <ul class=\"p-menu-list p-reset\" role=\"menu\">\n                <ng-template ngFor let-submenu [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-submenu-header\" [attr.data-automationid]=\"submenu.automationId\" *ngIf=\"!submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" pTooltip [tooltipOptions]=\"submenu.tooltipOptions\" role=\"none\">\n                        <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{submenu.label}}</span>\n                        <ng-template #htmlSubmenuLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                    </li>\n                    <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                        <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\"  role=\"separator\"></li>\n                        <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': item.visible === false}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `, isInline: true, styles: [\".p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{display:flex;align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i5.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: MenuItemContent, selector: \"[pMenuItemContent]\", inputs: [\"pMenuItemContent\"] }], animations: [\n        trigger('overlayAnimation', [\n            transition(':enter', [\n                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition(':leave', [\n                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Menu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-menu', template: `\n        <div #container [ngClass]=\"{'p-menu p-component': true, 'p-menu-overlay': popup}\"\n            [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"!popup || visible\" (click)=\"onOverlayClick($event)\"\n            [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"popup !== true\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n            <ul class=\"p-menu-list p-reset\" role=\"menu\">\n                <ng-template ngFor let-submenu [ngForOf]=\"model\" *ngIf=\"hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-submenu-header\" [attr.data-automationid]=\"submenu.automationId\" *ngIf=\"!submenu.separator\" [ngClass]=\"{'p-hidden': submenu.visible === false}\" pTooltip [tooltipOptions]=\"submenu.tooltipOptions\" role=\"none\">\n                        <span *ngIf=\"submenu.escape !== false; else htmlSubmenuLabel\">{{submenu.label}}</span>\n                        <ng-template #htmlSubmenuLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                    </li>\n                    <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                        <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\"  role=\"separator\"></li>\n                        <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': (item.visible === false || submenu.visible === false)}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                    </ng-template>\n                </ng-template>\n                <ng-template ngFor let-item [ngForOf]=\"model\" *ngIf=\"!hasSubMenu()\">\n                    <li class=\"p-menu-separator\" *ngIf=\"item.separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\"></li>\n                    <li class=\"p-menuitem\" *ngIf=\"!item.separator\" [pMenuItemContent]=\"item\" [ngClass]=\"{'p-hidden': item.visible === false}\" [ngStyle]=\"item.style\" [class]=\"item.styleClass\" pTooltip [tooltipOptions]=\"item.tooltipOptions\" role=\"none\"></li>\n                </ng-template>\n            </ul>\n        </div>\n    `, animations: [\n                        trigger('overlayAnimation', [\n                            transition(':enter', [\n                                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition(':leave', [\n                                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n                            ])\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-menu-overlay{position:absolute;top:0;left:0}.p-menu ul{margin:0;padding:0;list-style:none}.p-menu .p-submenu-header{display:flex;align-items:center}.p-menu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menu .p-menuitem-text{line-height:1}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i4.PrimeNGConfig }, { type: i4.OverlayService }]; }, propDecorators: { model: [{\n                type: Input\n            }], popup: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }] } });\nclass MenuModule {\n}\nMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuModule, declarations: [Menu, MenuItemContent], imports: [CommonModule, RouterModule, RippleModule, TooltipModule], exports: [Menu, RouterModule, TooltipModule] });\nMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n                    exports: [Menu, RouterModule, TooltipModule],\n                    declarations: [Menu, MenuItemContent]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menu, MenuItemContent, MenuModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,SAArB,EAAgCC,iBAAhC,EAAmDC,MAAnD,EAA2DC,KAA3D,EAAkEC,YAAlE,EAAgFC,uBAAhF,EAAyGC,SAAzG,EAAoHC,MAApH,EAA4HC,QAA5H,QAA4I,eAA5I;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;;;;;IAiDkG1B,EAItF,wB;;;;mBAJsFA,E;IAAAA,EAIhB,kC;IAJgBA,EAItC,0E;;;;;;IAJsCA,EAKtF,6B;IALsFA,EAKV,U;IALUA,EAKI,e;;;;mBALJA,E;IAAAA,EAKV,a;IALUA,EAKV,qC;;;;;;IALUA,EAM9D,wB;;;;mBAN8DA,E;IAAAA,EAMhC,4CANgCA,EAMhC,gB;;;;;;IANgCA,EAOtF,8B;IAPsFA,EAOH,U;IAPGA,EAOW,e;;;;mBAPXA,E;IAAAA,EAOpC,mD;IAPoCA,EAOH,a;IAPGA,EAOH,qC;;;;;;;;;;;;gBAPGA,E;;IAAAA,EAE1F,0B;IAF0FA,EAE7D;MAF6DA,EAE7D;MAAA,eAF6DA,EAE7D;MAAA,OAF6DA,EAElD,0CAAX;IAAA;MAF6DA,EAE7D;MAAA,eAF6DA,EAE7D;MAAA,OAF6DA,EAGpC,wDADzB;IAAA,E;IAF6DA,EAItF,oE;IAJsFA,EAKtF,oE;IALsFA,EAMtF,yFANsFA,EAMtF,wB;IANsFA,EAOtF,oE;IAPsFA,EAQ1F,e;;;;gBAR0FA,E;;mBAAAA,E;IAAAA,EAEoH,qDAFpHA,EAEoH,8E;IAFpHA,EAE3B,8CAF2BA,EAE3B,+J;IAF2BA,EAIvD,a;IAJuDA,EAIvD,qC;IAJuDA,EAKvD,a;IALuDA,EAKvD,kE;IALuDA,EAOtD,a;IAPsDA,EAOtD,sC;;;;;;IAPsDA,EAatF,yB;;;;oBAbsFA,E;IAAAA,EAatC,yC;;;;;;IAbsCA,EActF,6B;IAdsFA,EAcL,U;IAdKA,EAcS,e;;;;oBAdTA,E;IAAAA,EAcL,a;IAdKA,EAcL,sC;;;;;;IAdKA,EAezD,wB;;;;oBAfyDA,E;IAAAA,EAe3B,6CAf2BA,EAe3B,gB;;;;;;IAf2BA,EAgBtF,8B;IAhBsFA,EAgBH,U;IAhBGA,EAgBW,e;;;;oBAhBXA,E;IAAAA,EAgBpC,oD;IAhBoCA,EAgBH,a;IAhBGA,EAgBH,sC;;;;;;;;;;;;iBAhBGA,E;;IAAAA,EAS1F,2B;IAT0FA,EAS/D;MAT+DA,EAS/D;MAAA,gBAT+DA,EAS/D;MAAA,OAT+DA,EASpD,2CAAX;IAAA;MAT+DA,EAS/D;MAAA,gBAT+DA,EAS/D;MAAA,OAT+DA,EAWV,0DAFrD;IAAA,E;IAT+DA,EAatF,qE;IAbsFA,EActF,oE;IAdsFA,EAetF,0FAfsFA,EAetF,wB;IAfsFA,EAgBtF,oE;IAhBsFA,EAiB1F,e;;;;iBAjB0FA,E;;mBAAAA,E;IAAAA,EAS7B,0MAT6BA,EAS7B,oEAT6BA,EAS7B,oT;IAT6BA,EASE,4J;IATFA,EAavD,a;IAbuDA,EAavD,qC;IAbuDA,EAcvD,a;IAduDA,EAcvD,mE;IAduDA,EAgBtD,a;IAhBsDA,EAgBtD,sC;;;;;;;;;;;;;;IAhBsDA,EA+P9E,sB;;;;uBA/P8EA,E;IAAAA,EA+PvB,uBA/PuBA,EA+PvB,uD;;;;;;IA/PuBA,EAiQ1E,0B;IAjQ0EA,EAiQZ,U;IAjQYA,EAiQK,e;;;;uBAjQLA,E;IAAAA,EAiQZ,a;IAjQYA,EAiQZ,oC;;;;;;IAjQYA,EAkQ3C,yB;;;;uBAlQ2CA,E;IAAAA,EAkQrC,2CAlQqCA,EAkQrC,gB;;;;;;IAlQqCA,EAgQ9E,2B;IAhQ8EA,EAiQ1E,iF;IAjQ0EA,EAkQ1E,sGAlQ0EA,EAkQ1E,wB;IAlQ0EA,EAmQ9E,e;;;;iBAnQ8EA,E;;uBAAAA,E;IAAAA,EAgQ0B,uBAhQ1BA,EAgQ0B,oG;IAhQ1BA,EAgQjD,0D;IAhQiDA,EAiQnE,a;IAjQmEA,EAiQnE,kE;;;;;;IAjQmEA,EAqQ1E,sB;;;;qBArQ0EA,E;uBAAAA,E;IAAAA,EAqQtB,uBArQsBA,EAqQtB,qF;;;;;;IArQsBA,EAsQ1E,uB;;;;qBAtQ0EA,E;uBAAAA,E;IAAAA,EAsQsG,gC;IAtQtGA,EAsQ3B,qDAtQ2BA,EAsQ3B,2J;;;;;;IAtQ2BA,EAqQ1E,qF;IArQ0EA,EAsQ1E,sF;;;;;IAtQ0EA,EAqQ5C,uC;IArQ4CA,EAsQlD,a;IAtQkDA,EAsQlD,wC;;;;;;IAtQkDA,EA+P9E,uE;IA/P8EA,EAgQ9E,uE;IAhQ8EA,EAoQ9E,yF;;;;;IApQ8EA,EA+PhD,yC;IA/PgDA,EAgQA,a;IAhQAA,EAgQA,0C;IAhQAA,EAoQlD,a;IApQkDA,EAoQlD,wC;;;;;;IApQkDA,EA8PlF,2E;;;;mBA9PkFA,E;IAAAA,EA8PnD,oC;;;;;;IA9PmDA,EA0Q9E,sB;;;;qBA1Q8EA,E;IAAAA,EA0Q1B,uBA1Q0BA,EA0Q1B,qD;;;;;;IA1Q0BA,EA2Q9E,uB;;;;qBA3Q8EA,E;IAAAA,EA2QmE,gC;IA3QnEA,EA2Q/B,qDA3Q+BA,EA2Q/B,2H;;;;;;IA3Q+BA,EA0Q9E,uE;IA1Q8EA,EA2Q9E,wE;;;;;IA3Q8EA,EA0QhD,uC;IA1QgDA,EA2QtD,a;IA3QsDA,EA2QtD,wC;;;;;;IA3QsDA,EAyQlF,2E;;;;mBAzQkFA,E;IAAAA,EAyQtD,oC;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAzQsDA,E;;IAAAA,EA0P1F,+B;IA1P0FA,EA2PrB;MA3PqBA,EA2PrB;MAAA,gBA3PqBA,EA2PrB;MAAA,OA3PqBA,EA2PZ,4CAAT;IAAA;MA3PqBA,EA2PrB;MAAA,gBA3PqBA,EA2PrB;MAAA,OA3PqBA,EA4PgH,qDADrI;IAAA;MA3PqBA,EA2PrB;MAAA,gBA3PqBA,EA2PrB;MAAA,OA3PqBA,EA4P2K,mDADhM;IAAA,E;IA3PqBA,EA6PtF,2B;IA7PsFA,EA8PlF,oD;IA9PkFA,EAyQlF,oD;IAzQkFA,EA6QtF,iB;;;;mBA7QsFA,E;IAAAA,EA2PtF,8B;IA3PsFA,EA0P1E,uBA1P0EA,EA0P1E,sFA1P0EA,EA0P1E,0BA1P0EA,EA0P1E,4H;IA1P0EA,EA8PhC,a;IA9PgCA,EA8PhC,wC;IA9PgCA,EAyQnC,a;IAzQmCA,EAyQnC,yC;;;;AAxT/D,MAAM2B,eAAN,CAAsB;EAClBC,WAAW,CAACC,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACDC,aAAa,CAACC,KAAD,EAAQ;IACjB,IAAIC,QAAQ,GAAGD,KAAK,CAACE,aAAN,CAAoBC,aAAnC;;IACA,QAAQH,KAAK,CAACI,IAAd;MACI,KAAK,WAAL;QACI,IAAIC,QAAQ,GAAG,KAAKC,YAAL,CAAkBL,QAAlB,CAAf;;QACA,IAAII,QAAJ,EAAc;UACVA,QAAQ,CAACE,QAAT,CAAkB,CAAlB,EAAqBC,KAArB;QACH;;QACDR,KAAK,CAACS,cAAN;QACA;;MACJ,KAAK,SAAL;QACI,IAAIC,QAAQ,GAAG,KAAKC,YAAL,CAAkBV,QAAlB,CAAf;;QACA,IAAIS,QAAJ,EAAc;UACVA,QAAQ,CAACH,QAAT,CAAkB,CAAlB,EAAqBC,KAArB;QACH;;QACDR,KAAK,CAACS,cAAN;QACA;;MACJ,KAAK,OAAL;MACA,KAAK,OAAL;QACI,IAAIR,QAAQ,IAAI,CAACf,UAAU,CAAC0B,QAAX,CAAoBX,QAApB,EAA8B,YAA9B,CAAjB,EAA8D;UAC1DA,QAAQ,CAACM,QAAT,CAAkB,CAAlB,EAAqBM,KAArB;QACH;;QACDb,KAAK,CAACS,cAAN;QACA;;MACJ;QACI;IAvBR;EAyBH;;EACDH,YAAY,CAACQ,IAAD,EAAO;IACf,IAAIT,QAAQ,GAAGS,IAAI,CAACC,kBAApB;IACA,IAAIV,QAAJ,EACI,OAAOnB,UAAU,CAAC0B,QAAX,CAAoBP,QAApB,EAA8B,YAA9B,KAA+C,CAACnB,UAAU,CAAC0B,QAAX,CAAoBP,QAApB,EAA8B,YAA9B,CAAhD,GAA8F,KAAKC,YAAL,CAAkBD,QAAlB,CAA9F,GAA4HA,QAAnI,CADJ,KAGI,OAAO,IAAP;EACP;;EACDM,YAAY,CAACG,IAAD,EAAO;IACf,IAAIJ,QAAQ,GAAGI,IAAI,CAACE,sBAApB;IACA,IAAIN,QAAJ,EACI,OAAOxB,UAAU,CAAC0B,QAAX,CAAoBF,QAApB,EAA8B,YAA9B,KAA+C,CAACxB,UAAU,CAAC0B,QAAX,CAAoBF,QAApB,EAA8B,YAA9B,CAAhD,GAA8F,KAAKC,YAAL,CAAkBD,QAAlB,CAA9F,GAA4HA,QAAnI,CADJ,KAGI,OAAO,IAAP;EACP;;AA7CiB;;AA+CtBd,eAAe,CAACqB,IAAhB;EAAA,iBAA4GrB,eAA5G,EAAkG3B,EAAlG,mBAA6IC,UAAU,CAAC,MAAMgD,IAAP,CAAvJ;AAAA;;AACAtB,eAAe,CAACuB,IAAhB,kBADkGlD,EAClG;EAAA,MAAgG2B,eAAhG;EAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADkG3B,EAE1F,2DADR;MADkGA,EAS1F,2DARR;IAAA;;IAAA;MADkGA,EAEtF,yCADZ;MADkGA,EAStF,aARZ;MADkGA,EAStF,wCARZ;IAAA;EAAA;EAAA,eAiBiEe,EAAE,CAACoC,OAjBpE,EAiB+JpC,EAAE,CAACqC,IAjBlK,EAiBmQrC,EAAE,CAACsC,OAjBtQ,EAiBwVjC,EAAE,CAACkC,kBAjB3V,EAiB2lBlC,EAAE,CAACmC,gBAjB9lB,EAiB4zBjC,EAAE,CAACkC,MAjB/zB;EAAA;AAAA;;AAkBA;EAAA,mDAnBkGxD,EAmBlG,mBAA2F2B,eAA3F,EAAwH,CAAC;IAC7G8B,IAAI,EAAEvD,SADuG;IAE7GwD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAnBmB;MAoBCC,aAAa,EAAE1D,iBAAiB,CAAC2D,IApBlC;MAqBCC,IAAI,EAAE;QACF,SAAS;MADP;IArBP,CAAD;EAFuG,CAAD,CAAxH,EA2B4B,YAAY;IAChC,OAAO,CAAC;MAAEN,IAAI,EAAEO,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBR,IAAI,EAAErD,MADkB;QAExBsD,IAAI,EAAE,CAACzD,UAAU,CAAC,MAAMgD,IAAP,CAAX;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CAhCL,EAgCuB;IAAEJ,IAAI,EAAE,CAAC;MAChBY,IAAI,EAAEpD,KADU;MAEhBqD,IAAI,EAAE,CAAC,kBAAD;IAFU,CAAD;EAAR,CAhCvB;AAAA;;AAoCA,MAAMT,IAAN,CAAW;EACPrB,WAAW,CAACsC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,MAAnB,EAA2BC,cAA3B,EAA2C;IAClD,KAAKJ,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,MAAL,GAAc,IAAIrE,YAAJ,EAAd;IACA,KAAKsE,MAAL,GAAc,IAAItE,YAAJ,EAAd;EACH;;EACDuE,MAAM,CAAC9C,KAAD,EAAQ;IACV,IAAI,KAAK+C,OAAT,EACI,KAAKC,IAAL,GADJ,KAGI,KAAKC,IAAL,CAAUjD,KAAV;IACJ,KAAKkD,sBAAL,GAA8B,IAA9B;EACH;;EACDD,IAAI,CAACjD,KAAD,EAAQ;IACR,KAAKmD,MAAL,GAAcnD,KAAK,CAACE,aAApB;IACA,KAAKkD,aAAL,GAAqBpD,KAAK,CAACoD,aAA3B;IACA,KAAKL,OAAL,GAAe,IAAf;IACA,KAAKG,sBAAL,GAA8B,IAA9B;IACA,KAAKb,EAAL,CAAQgB,YAAR;EACH;;EACDC,uBAAuB,CAACtD,KAAD,EAAQ;IAC3B,QAAQA,KAAK,CAACuD,OAAd;MACI,KAAK,SAAL;QACI,IAAI,KAAKC,KAAT,EAAgB;UACZ,KAAKC,SAAL,GAAiBzD,KAAK,CAAC0D,OAAvB;UACA,KAAKC,SAAL;UACA,KAAKf,MAAL,CAAYgB,IAAZ,CAAiB,EAAjB;UACA,KAAKC,aAAL;UACA,KAAKC,YAAL;UACA,KAAKC,yBAAL;UACA,KAAKC,0BAAL;UACA,KAAKC,kBAAL;QACH;;QACD;;MACJ,KAAK,MAAL;QACI,KAAKC,aAAL;QACA,KAAKrB,MAAL,CAAYe,IAAZ,CAAiB,EAAjB;QACA;IAhBR;EAkBH;;EACDO,qBAAqB,CAACnE,KAAD,EAAQ;IACzB,QAAQA,KAAK,CAACuD,OAAd;MACI,KAAK,MAAL;QACI,IAAI,KAAKf,UAAT,EAAqB;UACjBpD,WAAW,CAACgF,KAAZ,CAAkBpE,KAAK,CAAC0D,OAAxB;QACH;;QACD;IALR;EAOH;;EACDI,YAAY,GAAG;IACX,IAAI,KAAKV,aAAT,EACIlE,UAAU,CAACmF,gBAAX,CAA4B,KAAKZ,SAAjC,EAA4C,KAAKN,MAAjD,EADJ,KAGIjE,UAAU,CAACoF,gBAAX,CAA4B,KAAKb,SAAjC,EAA4C,KAAKN,MAAjD;EACP;;EACDU,aAAa,GAAG;IACZ,IAAI,KAAKU,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACIC,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKjB,SAA/B,EADJ,KAGIvE,UAAU,CAACwF,WAAX,CAAuB,KAAKjB,SAA5B,EAAuC,KAAKc,QAA5C;IACP;EACJ;;EACDI,oBAAoB,GAAG;IACnB,IAAI,KAAKlB,SAAL,IAAkB,KAAKc,QAA3B,EAAqC;MACjC,KAAKpC,EAAL,CAAQyC,aAAR,CAAsBF,WAAtB,CAAkC,KAAKjB,SAAvC;IACH;EACJ;;EACDE,SAAS,GAAG;IACR,IAAI,KAAKnB,UAAT,EAAqB;MACjBpD,WAAW,CAACyF,GAAZ,CAAgB,MAAhB,EAAwB,KAAKpB,SAA7B,EAAwC,KAAKhB,UAAL,GAAkB,KAAKH,MAAL,CAAYwC,MAAZ,CAAmBhF,IAA7E;IACH;EACJ;;EACDkD,IAAI,GAAG;IACH,KAAKD,OAAL,GAAe,KAAf;IACA,KAAKK,aAAL,GAAqB,KAArB;IACA,KAAKf,EAAL,CAAQgB,YAAR;EACH;;EACD0B,cAAc,GAAG;IACb,IAAI,KAAKhC,OAAL,IAAgB,CAAC7D,UAAU,CAAC8F,aAAX,EAArB,EAAiD;MAC7C,KAAKhC,IAAL;IACH;EACJ;;EACDiC,SAAS,CAACjF,KAAD,EAAQc,IAAR,EAAc;IACnB,IAAIA,IAAI,CAACoE,QAAT,EAAmB;MACflF,KAAK,CAACS,cAAN;MACA;IACH;;IACD,IAAI,CAACK,IAAI,CAACqE,GAAN,IAAa,CAACrE,IAAI,CAACsE,UAAvB,EAAmC;MAC/BpF,KAAK,CAACS,cAAN;IACH;;IACD,IAAIK,IAAI,CAACuE,OAAT,EAAkB;MACdvE,IAAI,CAACuE,OAAL,CAAa;QACTC,aAAa,EAAEtF,KADN;QAETc,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,IAAI,KAAK0C,KAAT,EAAgB;MACZ,KAAKR,IAAL;IACH;EACJ;;EACDuC,cAAc,CAACvF,KAAD,EAAQ;IAClB,IAAI,KAAKwD,KAAT,EAAgB;MACZ,KAAKjB,cAAL,CAAoBiD,GAApB,CAAwB;QACpBF,aAAa,EAAEtF,KADK;QAEpBmD,MAAM,EAAE,KAAKhB,EAAL,CAAQyC;MAFI,CAAxB;IAIH;;IACD,KAAK1B,sBAAL,GAA8B,IAA9B;EACH;;EACDa,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAK0B,qBAAV,EAAiC;MAC7B,MAAMC,cAAc,GAAG,KAAKvD,EAAL,GAAU,KAAKA,EAAL,CAAQyC,aAAR,CAAsBe,aAAhC,GAAgD,UAAvE;MACA,KAAKF,qBAAL,GAA6B,KAAKrD,QAAL,CAAcwD,MAAd,CAAqBF,cAArB,EAAqC,OAArC,EAA8C,MAAM;QAC7E,IAAI,CAAC,KAAKxC,sBAAV,EAAkC;UAC9B,KAAKF,IAAL;QACH;;QACD,KAAKE,sBAAL,GAA8B,KAA9B;MACH,CAL4B,CAA7B;IAMH;EACJ;;EACD2C,2BAA2B,GAAG;IAC1B,IAAI,KAAKJ,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACDzB,0BAA0B,GAAG;IACzB,KAAK8B,sBAAL,GAA8B,KAAKf,cAAL,CAAoBgB,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKH,sBAAvC;EACH;;EACDI,4BAA4B,GAAG;IAC3B,IAAI,KAAKJ,sBAAT,EAAiC;MAC7BE,MAAM,CAACG,mBAAP,CAA2B,QAA3B,EAAqC,KAAKL,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACD7B,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKmC,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAIjH,6BAAJ,CAAkC,KAAKgE,MAAvC,EAA+C,MAAM;QACtE,IAAI,KAAKJ,OAAT,EAAkB;UACd,KAAKC,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKoD,aAAL,CAAmBnC,kBAAnB;EACH;;EACDoC,oBAAoB,GAAG;IACnB,IAAI,KAAKD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBC,oBAAnB;IACH;EACJ;;EACDnC,aAAa,GAAG;IACZ,KAAK2B,2BAAL;IACA,KAAKK,4BAAL;IACA,KAAKG,oBAAL;IACA,KAAKnD,sBAAL,GAA8B,KAA9B;;IACA,IAAI,CAAC,KAAKb,EAAL,CAAQiE,SAAb,EAAwB;MACpB,KAAKnD,MAAL,GAAc,IAAd;IACH;EACJ;;EACDoD,WAAW,GAAG;IACV,IAAI,KAAK/C,KAAT,EAAgB;MACZ,IAAI,KAAK4C,aAAT,EAAwB;QACpB,KAAKA,aAAL,CAAmBI,OAAnB;QACA,KAAKJ,aAAL,GAAqB,IAArB;MACH;;MACD,IAAI,KAAK3C,SAAL,IAAkB,KAAKjB,UAA3B,EAAuC;QACnCpD,WAAW,CAACgF,KAAZ,CAAkB,KAAKX,SAAvB;MACH;;MACD,KAAKkB,oBAAL;MACA,KAAKT,aAAL;IACH;EACJ;;EACDuC,UAAU,GAAG;IACT,IAAI,KAAKC,KAAT,EAAgB;MACZ,KAAK,IAAI5F,IAAT,IAAiB,KAAK4F,KAAtB,EAA6B;QACzB,IAAI5F,IAAI,CAAC6F,KAAT,EAAgB;UACZ,OAAO,IAAP;QACH;MACJ;IACJ;;IACD,OAAO,KAAP;EACH;;AA/LM;;AAiMXzF,IAAI,CAACD,IAAL;EAAA,iBAAiGC,IAAjG,EAxPkGjD,EAwPlG,mBAAuHA,EAAE,CAAC2I,UAA1H,GAxPkG3I,EAwPlG,mBAAiJA,EAAE,CAAC4I,SAApJ,GAxPkG5I,EAwPlG,mBAA0KA,EAAE,CAAC6I,iBAA7K,GAxPkG7I,EAwPlG,mBAA2M0B,EAAE,CAACoH,aAA9M,GAxPkG9I,EAwPlG,mBAAwO0B,EAAE,CAACqH,cAA3O;AAAA;;AACA9F,IAAI,CAACC,IAAL,kBAzPkGlD,EAyPlG;EAAA,MAAqFiD,IAArF;EAAA;EAAA;IAAA;MAzPkGjD,EAyPlG;IAAA;;IAAA;MAAA;;MAzPkGA,EAyPlG,qBAzPkGA,EAyPlG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAzPkGA,EA0P1F,oDADR;IAAA;;IAAA;MAzPkGA,EA2P9C,8CAFpD;IAAA;EAAA;EAAA,eAsB8Ye,EAAE,CAACoC,OAtBjZ,EAsB4epC,EAAE,CAACiI,OAtB/e,EAsBymBjI,EAAE,CAACqC,IAtB5mB,EAsB6sBrC,EAAE,CAACsC,OAtBhtB,EAsBkyB7B,EAAE,CAACyH,OAtBryB,EAsB6lCtH,eAtB7lC;EAAA;EAAA;EAAA;IAAA,WAsB2rC,CACnrChB,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;MAAEqI,OAAO,EAAE,CAAX;MAAcC,SAAS,EAAE;IAAzB,CAAD,CADY,EAEjBrI,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAEqI,OAAO,EAAE;IAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CAD4qC;EAtB3rC;EAAA;AAAA;;AAiCA;EAAA,mDA1RkGlJ,EA0RlG,mBAA2FiD,IAA3F,EAA6G,CAAC;IAClGQ,IAAI,EAAEvD,SAD4F;IAElGwD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAZ;MAAsBC,QAAQ,EAAG;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAtBmB;MAsBZwF,UAAU,EAAE,CACKzI,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;QAAEqI,OAAO,EAAE,CAAX;QAAcC,SAAS,EAAE;MAAzB,CAAD,CADY,EAEjBrI,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAEqI,OAAO,EAAE;MAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADZ,CAtBA;MAgCIG,eAAe,EAAE9I,uBAAuB,CAAC+I,MAhC7C;MAgCqDzF,aAAa,EAAE1D,iBAAiB,CAAC2D,IAhCtF;MAgC4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CAhClG;MAkCIwF,MAAM,EAAE,CAAC,iUAAD;IAlCZ,CAAD;EAF4F,CAAD,CAA7G,EAqC4B,YAAY;IAAE,OAAO,CAAC;MAAE9F,IAAI,EAAEzD,EAAE,CAAC2I;IAAX,CAAD,EAA0B;MAAElF,IAAI,EAAEzD,EAAE,CAAC4I;IAAX,CAA1B,EAAkD;MAAEnF,IAAI,EAAEzD,EAAE,CAAC6I;IAAX,CAAlD,EAAkF;MAAEpF,IAAI,EAAE/B,EAAE,CAACoH;IAAX,CAAlF,EAA8G;MAAErF,IAAI,EAAE/B,EAAE,CAACqH;IAAX,CAA9G,CAAP;EAAoJ,CArC9L,EAqCgN;IAAEN,KAAK,EAAE,CAAC;MAC1MhF,IAAI,EAAEpD;IADoM,CAAD,CAAT;IAEhMkF,KAAK,EAAE,CAAC;MACR9B,IAAI,EAAEpD;IADE,CAAD,CAFyL;IAIhMQ,KAAK,EAAE,CAAC;MACR4C,IAAI,EAAEpD;IADE,CAAD,CAJyL;IAMhMmJ,UAAU,EAAE,CAAC;MACb/F,IAAI,EAAEpD;IADO,CAAD,CANoL;IAQhMiG,QAAQ,EAAE,CAAC;MACX7C,IAAI,EAAEpD;IADK,CAAD,CARsL;IAUhMkE,UAAU,EAAE,CAAC;MACbd,IAAI,EAAEpD;IADO,CAAD,CAVoL;IAYhMmE,UAAU,EAAE,CAAC;MACbf,IAAI,EAAEpD;IADO,CAAD,CAZoL;IAchMoE,qBAAqB,EAAE,CAAC;MACxBhB,IAAI,EAAEpD;IADkB,CAAD,CAdyK;IAgBhMqE,qBAAqB,EAAE,CAAC;MACxBjB,IAAI,EAAEpD;IADkB,CAAD,CAhByK;IAkBhMoJ,kBAAkB,EAAE,CAAC;MACrBhG,IAAI,EAAEjD,SADe;MAErBkD,IAAI,EAAE,CAAC,WAAD;IAFe,CAAD,CAlB4K;IAqBhMiB,MAAM,EAAE,CAAC;MACTlB,IAAI,EAAEhD;IADG,CAAD,CArBwL;IAuBhMmE,MAAM,EAAE,CAAC;MACTnB,IAAI,EAAEhD;IADG,CAAD;EAvBwL,CArChN;AAAA;;AA+DA,MAAMiJ,UAAN,CAAiB;;AAEjBA,UAAU,CAAC1G,IAAX;EAAA,iBAAuG0G,UAAvG;AAAA;;AACAA,UAAU,CAACC,IAAX,kBA5VkG3J,EA4VlG;EAAA,MAAwG0J;AAAxG;AACAA,UAAU,CAACE,IAAX,kBA7VkG5J,EA6VlG;EAAA,UAA8HgB,YAA9H,EAA4IK,YAA5I,EAA0JE,YAA1J,EAAwKE,aAAxK,EAAuLJ,YAAvL,EAAqMI,aAArM;AAAA;;AACA;EAAA,mDA9VkGzB,EA8VlG,mBAA2F0J,UAA3F,EAAmH,CAAC;IACxGjG,IAAI,EAAE/C,QADkG;IAExGgD,IAAI,EAAE,CAAC;MACCmG,OAAO,EAAE,CAAC7I,YAAD,EAAeK,YAAf,EAA6BE,YAA7B,EAA2CE,aAA3C,CADV;MAECqI,OAAO,EAAE,CAAC7G,IAAD,EAAO5B,YAAP,EAAqBI,aAArB,CAFV;MAGCsI,YAAY,EAAE,CAAC9G,IAAD,EAAOtB,eAAP;IAHf,CAAD;EAFkG,CAAD,CAAnH;AAAA;AASA;AACA;AACA;;;AAEA,SAASsB,IAAT,EAAetB,eAAf,EAAgC+H,UAAhC"}, "metadata": {}, "sourceType": "module"}