{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\nclass Tooltip {\n  constructor(el, zone, config) {\n    this.el = el;\n    this.zone = zone;\n    this.config = config;\n    this.escape = true;\n    this.fitContent = true;\n    this._tooltipOptions = {\n      tooltipPosition: 'right',\n      tooltipEvent: 'hover',\n      appendTo: 'body',\n      tooltipZIndex: 'auto',\n      escape: true,\n      positionTop: 0,\n      positionLeft: 0\n    };\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(val) {\n    this._disabled = val;\n    this.deactivate();\n  }\n\n  ngAfterViewInit() {\n    this.zone.runOutsideAngular(() => {\n      if (this.getOption('tooltipEvent') === 'hover') {\n        this.mouseEnterListener = this.onMouseEnter.bind(this);\n        this.mouseLeaveListener = this.onMouseLeave.bind(this);\n        this.clickListener = this.onClick.bind(this);\n        this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n        this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n        this.el.nativeElement.addEventListener('click', this.clickListener);\n      } else if (this.getOption('tooltipEvent') === 'focus') {\n        this.focusListener = this.onFocus.bind(this);\n        this.blurListener = this.onBlur.bind(this);\n        let target = this.getTarget(this.el.nativeElement);\n        target.addEventListener('focus', this.focusListener);\n        target.addEventListener('blur', this.blurListener);\n      }\n    });\n  }\n\n  ngOnChanges(simpleChange) {\n    if (simpleChange.tooltipPosition) {\n      this.setOption({\n        tooltipPosition: simpleChange.tooltipPosition.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipEvent) {\n      this.setOption({\n        tooltipEvent: simpleChange.tooltipEvent.currentValue\n      });\n    }\n\n    if (simpleChange.appendTo) {\n      this.setOption({\n        appendTo: simpleChange.appendTo.currentValue\n      });\n    }\n\n    if (simpleChange.positionStyle) {\n      this.setOption({\n        positionStyle: simpleChange.positionStyle.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipStyleClass) {\n      this.setOption({\n        tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue\n      });\n    }\n\n    if (simpleChange.tooltipZIndex) {\n      this.setOption({\n        tooltipZIndex: simpleChange.tooltipZIndex.currentValue\n      });\n    }\n\n    if (simpleChange.escape) {\n      this.setOption({\n        escape: simpleChange.escape.currentValue\n      });\n    }\n\n    if (simpleChange.showDelay) {\n      this.setOption({\n        showDelay: simpleChange.showDelay.currentValue\n      });\n    }\n\n    if (simpleChange.hideDelay) {\n      this.setOption({\n        hideDelay: simpleChange.hideDelay.currentValue\n      });\n    }\n\n    if (simpleChange.life) {\n      this.setOption({\n        life: simpleChange.life.currentValue\n      });\n    }\n\n    if (simpleChange.positionTop) {\n      this.setOption({\n        positionTop: simpleChange.positionTop.currentValue\n      });\n    }\n\n    if (simpleChange.positionLeft) {\n      this.setOption({\n        positionLeft: simpleChange.positionLeft.currentValue\n      });\n    }\n\n    if (simpleChange.disabled) {\n      this.setOption({\n        disabled: simpleChange.disabled.currentValue\n      });\n    }\n\n    if (simpleChange.text) {\n      this.setOption({\n        tooltipLabel: simpleChange.text.currentValue\n      });\n\n      if (this.active) {\n        if (simpleChange.text.currentValue) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n\n    if (simpleChange.tooltipOptions) {\n      this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), simpleChange.tooltipOptions.currentValue);\n      this.deactivate();\n\n      if (this.active) {\n        if (this.getOption('tooltipLabel')) {\n          if (this.container && this.container.offsetParent) {\n            this.updateText();\n            this.align();\n          } else {\n            this.show();\n          }\n        } else {\n          this.hide();\n        }\n      }\n    }\n  }\n\n  onMouseEnter(e) {\n    if (!this.container && !this.showTimeout) {\n      this.activate();\n    }\n  }\n\n  onMouseLeave(e) {\n    this.deactivate();\n  }\n\n  onFocus(e) {\n    this.activate();\n  }\n\n  onBlur(e) {\n    this.deactivate();\n  }\n\n  onClick(e) {\n    this.deactivate();\n  }\n\n  activate() {\n    this.active = true;\n    this.clearHideTimeout();\n    if (this.getOption('showDelay')) this.showTimeout = setTimeout(() => {\n      this.show();\n    }, this.getOption('showDelay'));else this.show();\n\n    if (this.getOption('life')) {\n      let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, duration);\n    }\n  }\n\n  deactivate() {\n    this.active = false;\n    this.clearShowTimeout();\n\n    if (this.getOption('hideDelay')) {\n      this.clearHideTimeout(); //life timeout\n\n      this.hideTimeout = setTimeout(() => {\n        this.hide();\n      }, this.getOption('hideDelay'));\n    } else {\n      this.hide();\n    }\n  }\n\n  create() {\n    if (this.container) {\n      this.clearHideTimeout();\n      this.remove();\n    }\n\n    this.container = document.createElement('div');\n    let tooltipArrow = document.createElement('div');\n    tooltipArrow.className = 'p-tooltip-arrow';\n    this.container.appendChild(tooltipArrow);\n    this.tooltipText = document.createElement('div');\n    this.tooltipText.className = 'p-tooltip-text';\n    this.updateText();\n\n    if (this.getOption('positionStyle')) {\n      this.container.style.position = this.getOption('positionStyle');\n    }\n\n    this.container.appendChild(this.tooltipText);\n    if (this.getOption('appendTo') === 'body') document.body.appendChild(this.container);else if (this.getOption('appendTo') === 'target') DomHandler.appendChild(this.container, this.el.nativeElement);else DomHandler.appendChild(this.container, this.getOption('appendTo'));\n    this.container.style.display = 'inline-block';\n\n    if (this.fitContent) {\n      this.container.style.width = 'fit-content';\n    }\n  }\n\n  show() {\n    if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n      return;\n    }\n\n    this.create();\n    this.align();\n    DomHandler.fadeIn(this.container, 250);\n    if (this.getOption('tooltipZIndex') === 'auto') ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);else this.container.style.zIndex = this.getOption('tooltipZIndex');\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n\n  hide() {\n    if (this.getOption('tooltipZIndex') === 'auto') {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.remove();\n  }\n\n  updateText() {\n    if (this.getOption('escape')) {\n      this.tooltipText.innerHTML = '';\n      this.tooltipText.appendChild(document.createTextNode(this.getOption('tooltipLabel')));\n    } else {\n      this.tooltipText.innerHTML = this.getOption('tooltipLabel');\n    }\n  }\n\n  align() {\n    let position = this.getOption('tooltipPosition');\n\n    switch (position) {\n      case 'top':\n        this.alignTop();\n\n        if (this.isOutOfBounds()) {\n          this.alignBottom();\n\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n\n        break;\n\n      case 'bottom':\n        this.alignBottom();\n\n        if (this.isOutOfBounds()) {\n          this.alignTop();\n\n          if (this.isOutOfBounds()) {\n            this.alignRight();\n\n            if (this.isOutOfBounds()) {\n              this.alignLeft();\n            }\n          }\n        }\n\n        break;\n\n      case 'left':\n        this.alignLeft();\n\n        if (this.isOutOfBounds()) {\n          this.alignRight();\n\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n\n        break;\n\n      case 'right':\n        this.alignRight();\n\n        if (this.isOutOfBounds()) {\n          this.alignLeft();\n\n          if (this.isOutOfBounds()) {\n            this.alignTop();\n\n            if (this.isOutOfBounds()) {\n              this.alignBottom();\n            }\n          }\n        }\n\n        break;\n    }\n  }\n\n  getHostOffset() {\n    if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n      let offset = this.el.nativeElement.getBoundingClientRect();\n      let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n      let targetTop = offset.top + DomHandler.getWindowScrollTop();\n      return {\n        left: targetLeft,\n        top: targetTop\n      };\n    } else {\n      return {\n        left: 0,\n        top: 0\n      };\n    }\n  }\n\n  alignRight() {\n    this.preAlign('right');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignLeft() {\n    this.preAlign('left');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n    let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignTop() {\n    this.preAlign('top');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  alignBottom() {\n    this.preAlign('bottom');\n    let hostOffset = this.getHostOffset();\n    let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n    let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n    this.container.style.left = left + this.getOption('positionLeft') + 'px';\n    this.container.style.top = top + this.getOption('positionTop') + 'px';\n  }\n\n  setOption(option) {\n    this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), option);\n  }\n\n  getOption(option) {\n    return this._tooltipOptions[option];\n  }\n\n  getTarget(el) {\n    return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n  }\n\n  preAlign(position) {\n    this.container.style.left = -999 + 'px';\n    this.container.style.top = -999 + 'px';\n    let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n    this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n  }\n\n  isOutOfBounds() {\n    let offset = this.container.getBoundingClientRect();\n    let targetTop = offset.top;\n    let targetLeft = offset.left;\n    let width = DomHandler.getOuterWidth(this.container);\n    let height = DomHandler.getOuterHeight(this.container);\n    let viewport = DomHandler.getViewport();\n    return targetLeft + width > viewport.width || targetLeft < 0 || targetTop < 0 || targetTop + height > viewport.height;\n  }\n\n  onWindowResize(e) {\n    this.hide();\n  }\n\n  bindDocumentResizeListener() {\n    this.zone.runOutsideAngular(() => {\n      this.resizeListener = this.onWindowResize.bind(this);\n      window.addEventListener('resize', this.resizeListener);\n    });\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.resizeListener) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.resizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n        if (this.container) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  unbindEvents() {\n    if (this.getOption('tooltipEvent') === 'hover') {\n      this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n      this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n      this.el.nativeElement.removeEventListener('click', this.clickListener);\n    } else if (this.getOption('tooltipEvent') === 'focus') {\n      let target = this.getTarget(this.el.nativeElement);\n      target.removeEventListener('focus', this.focusListener);\n      target.removeEventListener('blur', this.blurListener);\n    }\n\n    this.unbindDocumentResizeListener();\n  }\n\n  remove() {\n    if (this.container && this.container.parentElement) {\n      if (this.getOption('appendTo') === 'body') document.body.removeChild(this.container);else if (this.getOption('appendTo') === 'target') this.el.nativeElement.removeChild(this.container);else DomHandler.removeChild(this.container, this.getOption('appendTo'));\n    }\n\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.clearTimeouts();\n    this.container = null;\n    this.scrollHandler = null;\n  }\n\n  clearShowTimeout() {\n    if (this.showTimeout) {\n      clearTimeout(this.showTimeout);\n      this.showTimeout = null;\n    }\n  }\n\n  clearHideTimeout() {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n  }\n\n  clearTimeouts() {\n    this.clearShowTimeout();\n    this.clearHideTimeout();\n  }\n\n  ngOnDestroy() {\n    this.unbindEvents();\n\n    if (this.container) {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.remove();\n\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n  }\n\n}\n\nTooltip.ɵfac = function Tooltip_Factory(t) {\n  return new (t || Tooltip)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n};\n\nTooltip.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: Tooltip,\n  selectors: [[\"\", \"pTooltip\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    tooltipPosition: \"tooltipPosition\",\n    tooltipEvent: \"tooltipEvent\",\n    appendTo: \"appendTo\",\n    positionStyle: \"positionStyle\",\n    tooltipStyleClass: \"tooltipStyleClass\",\n    tooltipZIndex: \"tooltipZIndex\",\n    escape: \"escape\",\n    showDelay: \"showDelay\",\n    hideDelay: \"hideDelay\",\n    life: \"life\",\n    positionTop: \"positionTop\",\n    positionLeft: \"positionLeft\",\n    fitContent: \"fitContent\",\n    text: [\"pTooltip\", \"text\"],\n    disabled: [\"tooltipDisabled\", \"disabled\"],\n    tooltipOptions: \"tooltipOptions\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Tooltip, [{\n    type: Directive,\n    args: [{\n      selector: '[pTooltip]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.PrimeNGConfig\n    }];\n  }, {\n    tooltipPosition: [{\n      type: Input\n    }],\n    tooltipEvent: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    positionStyle: [{\n      type: Input\n    }],\n    tooltipStyleClass: [{\n      type: Input\n    }],\n    tooltipZIndex: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }],\n    showDelay: [{\n      type: Input\n    }],\n    hideDelay: [{\n      type: Input\n    }],\n    life: [{\n      type: Input\n    }],\n    positionTop: [{\n      type: Input\n    }],\n    positionLeft: [{\n      type: Input\n    }],\n    fitContent: [{\n      type: Input\n    }],\n    text: [{\n      type: Input,\n      args: ['pTooltip']\n    }],\n    disabled: [{\n      type: Input,\n      args: [\"tooltipDisabled\"]\n    }],\n    tooltipOptions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass TooltipModule {}\n\nTooltipModule.ɵfac = function TooltipModule_Factory(t) {\n  return new (t || TooltipModule)();\n};\n\nTooltipModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: TooltipModule\n});\nTooltipModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TooltipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Tooltip],\n      declarations: [Tooltip]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Tooltip, TooltipModule };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "NgModule", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ZIndexUtils", "i1", "<PERSON><PERSON><PERSON>", "constructor", "el", "zone", "config", "escape", "<PERSON><PERSON><PERSON><PERSON>", "_tooltipOptions", "tooltipPosition", "tooltipEvent", "appendTo", "tooltipZIndex", "positionTop", "positionLeft", "disabled", "_disabled", "val", "deactivate", "ngAfterViewInit", "runOutsideAngular", "getOption", "mouseEnterListener", "onMouseEnter", "bind", "mouseLeaveListener", "onMouseLeave", "clickListener", "onClick", "nativeElement", "addEventListener", "focusListener", "onFocus", "blurListener", "onBlur", "target", "get<PERSON><PERSON><PERSON>", "ngOnChanges", "simpleChange", "setOption", "currentValue", "positionStyle", "tooltipStyleClass", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "life", "text", "tooltipLabel", "active", "container", "offsetParent", "updateText", "align", "show", "hide", "tooltipOptions", "Object", "assign", "e", "showTimeout", "activate", "clearHideTimeout", "setTimeout", "duration", "hideTimeout", "clearShowTimeout", "create", "remove", "document", "createElement", "tooltipArrow", "className", "append<PERSON><PERSON><PERSON>", "tooltipText", "style", "position", "body", "display", "width", "fadeIn", "set", "zIndex", "tooltip", "bindDocumentResizeListener", "bindScrollListener", "clear", "innerHTML", "createTextNode", "alignTop", "isOutOfBounds", "alignBottom", "alignRight", "alignLeft", "getHostOffset", "offset", "getBoundingClientRect", "targetLeft", "left", "getWindowScrollLeft", "targetTop", "top", "getWindowScrollTop", "preAlign", "hostOffset", "getOuterWidth", "getOuterHeight", "option", "hasClass", "findSingle", "defaultClassName", "height", "viewport", "getViewport", "onWindowResize", "resizeListener", "window", "unbindDocumentResizeListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unbindScrollListener", "unbindEvents", "parentElement", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeouts", "clearTimeout", "ngOnDestroy", "destroy", "ɵfac", "ElementRef", "NgZone", "PrimeNGConfig", "ɵdir", "type", "args", "selector", "host", "TooltipModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-tooltip.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\nclass Tooltip {\n    constructor(el, zone, config) {\n        this.el = el;\n        this.zone = zone;\n        this.config = config;\n        this.escape = true;\n        this.fitContent = true;\n        this._tooltipOptions = {\n            tooltipPosition: 'right',\n            tooltipEvent: 'hover',\n            appendTo: 'body',\n            tooltipZIndex: 'auto',\n            escape: true,\n            positionTop: 0,\n            positionLeft: 0\n        };\n    }\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n        this.deactivate();\n    }\n    ngAfterViewInit() {\n        this.zone.runOutsideAngular(() => {\n            if (this.getOption('tooltipEvent') === 'hover') {\n                this.mouseEnterListener = this.onMouseEnter.bind(this);\n                this.mouseLeaveListener = this.onMouseLeave.bind(this);\n                this.clickListener = this.onClick.bind(this);\n                this.el.nativeElement.addEventListener('mouseenter', this.mouseEnterListener);\n                this.el.nativeElement.addEventListener('mouseleave', this.mouseLeaveListener);\n                this.el.nativeElement.addEventListener('click', this.clickListener);\n            }\n            else if (this.getOption('tooltipEvent') === 'focus') {\n                this.focusListener = this.onFocus.bind(this);\n                this.blurListener = this.onBlur.bind(this);\n                let target = this.getTarget(this.el.nativeElement);\n                target.addEventListener('focus', this.focusListener);\n                target.addEventListener('blur', this.blurListener);\n            }\n        });\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.tooltipPosition) {\n            this.setOption({ tooltipPosition: simpleChange.tooltipPosition.currentValue });\n        }\n        if (simpleChange.tooltipEvent) {\n            this.setOption({ tooltipEvent: simpleChange.tooltipEvent.currentValue });\n        }\n        if (simpleChange.appendTo) {\n            this.setOption({ appendTo: simpleChange.appendTo.currentValue });\n        }\n        if (simpleChange.positionStyle) {\n            this.setOption({ positionStyle: simpleChange.positionStyle.currentValue });\n        }\n        if (simpleChange.tooltipStyleClass) {\n            this.setOption({ tooltipStyleClass: simpleChange.tooltipStyleClass.currentValue });\n        }\n        if (simpleChange.tooltipZIndex) {\n            this.setOption({ tooltipZIndex: simpleChange.tooltipZIndex.currentValue });\n        }\n        if (simpleChange.escape) {\n            this.setOption({ escape: simpleChange.escape.currentValue });\n        }\n        if (simpleChange.showDelay) {\n            this.setOption({ showDelay: simpleChange.showDelay.currentValue });\n        }\n        if (simpleChange.hideDelay) {\n            this.setOption({ hideDelay: simpleChange.hideDelay.currentValue });\n        }\n        if (simpleChange.life) {\n            this.setOption({ life: simpleChange.life.currentValue });\n        }\n        if (simpleChange.positionTop) {\n            this.setOption({ positionTop: simpleChange.positionTop.currentValue });\n        }\n        if (simpleChange.positionLeft) {\n            this.setOption({ positionLeft: simpleChange.positionLeft.currentValue });\n        }\n        if (simpleChange.disabled) {\n            this.setOption({ disabled: simpleChange.disabled.currentValue });\n        }\n        if (simpleChange.text) {\n            this.setOption({ tooltipLabel: simpleChange.text.currentValue });\n            if (this.active) {\n                if (simpleChange.text.currentValue) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n        if (simpleChange.tooltipOptions) {\n            this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), simpleChange.tooltipOptions.currentValue);\n            this.deactivate();\n            if (this.active) {\n                if (this.getOption('tooltipLabel')) {\n                    if (this.container && this.container.offsetParent) {\n                        this.updateText();\n                        this.align();\n                    }\n                    else {\n                        this.show();\n                    }\n                }\n                else {\n                    this.hide();\n                }\n            }\n        }\n    }\n    onMouseEnter(e) {\n        if (!this.container && !this.showTimeout) {\n            this.activate();\n        }\n    }\n    onMouseLeave(e) {\n        this.deactivate();\n    }\n    onFocus(e) {\n        this.activate();\n    }\n    onBlur(e) {\n        this.deactivate();\n    }\n    onClick(e) {\n        this.deactivate();\n    }\n    activate() {\n        this.active = true;\n        this.clearHideTimeout();\n        if (this.getOption('showDelay'))\n            this.showTimeout = setTimeout(() => { this.show(); }, this.getOption('showDelay'));\n        else\n            this.show();\n        if (this.getOption('life')) {\n            let duration = this.getOption('showDelay') ? this.getOption('life') + this.getOption('showDelay') : this.getOption('life');\n            this.hideTimeout = setTimeout(() => { this.hide(); }, duration);\n        }\n    }\n    deactivate() {\n        this.active = false;\n        this.clearShowTimeout();\n        if (this.getOption('hideDelay')) {\n            this.clearHideTimeout(); //life timeout\n            this.hideTimeout = setTimeout(() => { this.hide(); }, this.getOption('hideDelay'));\n        }\n        else {\n            this.hide();\n        }\n    }\n    create() {\n        if (this.container) {\n            this.clearHideTimeout();\n            this.remove();\n        }\n        this.container = document.createElement('div');\n        let tooltipArrow = document.createElement('div');\n        tooltipArrow.className = 'p-tooltip-arrow';\n        this.container.appendChild(tooltipArrow);\n        this.tooltipText = document.createElement('div');\n        this.tooltipText.className = 'p-tooltip-text';\n        this.updateText();\n        if (this.getOption('positionStyle')) {\n            this.container.style.position = this.getOption('positionStyle');\n        }\n        this.container.appendChild(this.tooltipText);\n        if (this.getOption('appendTo') === 'body')\n            document.body.appendChild(this.container);\n        else if (this.getOption('appendTo') === 'target')\n            DomHandler.appendChild(this.container, this.el.nativeElement);\n        else\n            DomHandler.appendChild(this.container, this.getOption('appendTo'));\n        this.container.style.display = 'inline-block';\n        if (this.fitContent) {\n            this.container.style.width = 'fit-content';\n        }\n    }\n    show() {\n        if (!this.getOption('tooltipLabel') || this.getOption('disabled')) {\n            return;\n        }\n        this.create();\n        this.align();\n        DomHandler.fadeIn(this.container, 250);\n        if (this.getOption('tooltipZIndex') === 'auto')\n            ZIndexUtils.set('tooltip', this.container, this.config.zIndex.tooltip);\n        else\n            this.container.style.zIndex = this.getOption('tooltipZIndex');\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n    hide() {\n        if (this.getOption('tooltipZIndex') === 'auto') {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n    }\n    updateText() {\n        if (this.getOption('escape')) {\n            this.tooltipText.innerHTML = '';\n            this.tooltipText.appendChild(document.createTextNode(this.getOption('tooltipLabel')));\n        }\n        else {\n            this.tooltipText.innerHTML = this.getOption('tooltipLabel');\n        }\n    }\n    align() {\n        let position = this.getOption('tooltipPosition');\n        switch (position) {\n            case 'top':\n                this.alignTop();\n                if (this.isOutOfBounds()) {\n                    this.alignBottom();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'bottom':\n                this.alignBottom();\n                if (this.isOutOfBounds()) {\n                    this.alignTop();\n                    if (this.isOutOfBounds()) {\n                        this.alignRight();\n                        if (this.isOutOfBounds()) {\n                            this.alignLeft();\n                        }\n                    }\n                }\n                break;\n            case 'left':\n                this.alignLeft();\n                if (this.isOutOfBounds()) {\n                    this.alignRight();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n            case 'right':\n                this.alignRight();\n                if (this.isOutOfBounds()) {\n                    this.alignLeft();\n                    if (this.isOutOfBounds()) {\n                        this.alignTop();\n                        if (this.isOutOfBounds()) {\n                            this.alignBottom();\n                        }\n                    }\n                }\n                break;\n        }\n    }\n    getHostOffset() {\n        if (this.getOption('appendTo') === 'body' || this.getOption('appendTo') === 'target') {\n            let offset = this.el.nativeElement.getBoundingClientRect();\n            let targetLeft = offset.left + DomHandler.getWindowScrollLeft();\n            let targetTop = offset.top + DomHandler.getWindowScrollTop();\n            return { left: targetLeft, top: targetTop };\n        }\n        else {\n            return { left: 0, top: 0 };\n        }\n    }\n    alignRight() {\n        this.preAlign('right');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + DomHandler.getOuterWidth(this.el.nativeElement);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignLeft() {\n        this.preAlign('left');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left - DomHandler.getOuterWidth(this.container);\n        let top = hostOffset.top + (DomHandler.getOuterHeight(this.el.nativeElement) - DomHandler.getOuterHeight(this.container)) / 2;\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignTop() {\n        this.preAlign('top');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top - DomHandler.getOuterHeight(this.container);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    alignBottom() {\n        this.preAlign('bottom');\n        let hostOffset = this.getHostOffset();\n        let left = hostOffset.left + (DomHandler.getOuterWidth(this.el.nativeElement) - DomHandler.getOuterWidth(this.container)) / 2;\n        let top = hostOffset.top + DomHandler.getOuterHeight(this.el.nativeElement);\n        this.container.style.left = left + this.getOption('positionLeft') + 'px';\n        this.container.style.top = top + this.getOption('positionTop') + 'px';\n    }\n    setOption(option) {\n        this._tooltipOptions = Object.assign(Object.assign({}, this._tooltipOptions), option);\n    }\n    getOption(option) {\n        return this._tooltipOptions[option];\n    }\n    getTarget(el) {\n        return DomHandler.hasClass(el, 'p-inputwrapper') ? DomHandler.findSingle(el, 'input') : el;\n    }\n    preAlign(position) {\n        this.container.style.left = -999 + 'px';\n        this.container.style.top = -999 + 'px';\n        let defaultClassName = 'p-tooltip p-component p-tooltip-' + position;\n        this.container.className = this.getOption('tooltipStyleClass') ? defaultClassName + ' ' + this.getOption('tooltipStyleClass') : defaultClassName;\n    }\n    isOutOfBounds() {\n        let offset = this.container.getBoundingClientRect();\n        let targetTop = offset.top;\n        let targetLeft = offset.left;\n        let width = DomHandler.getOuterWidth(this.container);\n        let height = DomHandler.getOuterHeight(this.container);\n        let viewport = DomHandler.getViewport();\n        return (targetLeft + width > viewport.width) || (targetLeft < 0) || (targetTop < 0) || (targetTop + height > viewport.height);\n    }\n    onWindowResize(e) {\n        this.hide();\n    }\n    bindDocumentResizeListener() {\n        this.zone.runOutsideAngular(() => {\n            this.resizeListener = this.onWindowResize.bind(this);\n            window.addEventListener('resize', this.resizeListener);\n        });\n    }\n    unbindDocumentResizeListener() {\n        if (this.resizeListener) {\n            window.removeEventListener('resize', this.resizeListener);\n            this.resizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.el.nativeElement, () => {\n                if (this.container) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    unbindEvents() {\n        if (this.getOption('tooltipEvent') === 'hover') {\n            this.el.nativeElement.removeEventListener('mouseenter', this.mouseEnterListener);\n            this.el.nativeElement.removeEventListener('mouseleave', this.mouseLeaveListener);\n            this.el.nativeElement.removeEventListener('click', this.clickListener);\n        }\n        else if (this.getOption('tooltipEvent') === 'focus') {\n            let target = this.getTarget(this.el.nativeElement);\n            target.removeEventListener('focus', this.focusListener);\n            target.removeEventListener('blur', this.blurListener);\n        }\n        this.unbindDocumentResizeListener();\n    }\n    remove() {\n        if (this.container && this.container.parentElement) {\n            if (this.getOption('appendTo') === 'body')\n                document.body.removeChild(this.container);\n            else if (this.getOption('appendTo') === 'target')\n                this.el.nativeElement.removeChild(this.container);\n            else\n                DomHandler.removeChild(this.container, this.getOption('appendTo'));\n        }\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.clearTimeouts();\n        this.container = null;\n        this.scrollHandler = null;\n    }\n    clearShowTimeout() {\n        if (this.showTimeout) {\n            clearTimeout(this.showTimeout);\n            this.showTimeout = null;\n        }\n    }\n    clearHideTimeout() {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n    }\n    clearTimeouts() {\n        this.clearShowTimeout();\n        this.clearHideTimeout();\n    }\n    ngOnDestroy() {\n        this.unbindEvents();\n        if (this.container) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.remove();\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n    }\n}\nTooltip.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tooltip, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Directive });\nTooltip.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Tooltip, selector: \"[pTooltip]\", inputs: { tooltipPosition: \"tooltipPosition\", tooltipEvent: \"tooltipEvent\", appendTo: \"appendTo\", positionStyle: \"positionStyle\", tooltipStyleClass: \"tooltipStyleClass\", tooltipZIndex: \"tooltipZIndex\", escape: \"escape\", showDelay: \"showDelay\", hideDelay: \"hideDelay\", life: \"life\", positionTop: \"positionTop\", positionLeft: \"positionLeft\", fitContent: \"fitContent\", text: [\"pTooltip\", \"text\"], disabled: [\"tooltipDisabled\", \"disabled\"], tooltipOptions: \"tooltipOptions\" }, host: { classAttribute: \"p-element\" }, usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Tooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pTooltip]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i1.PrimeNGConfig }]; }, propDecorators: { tooltipPosition: [{\n                type: Input\n            }], tooltipEvent: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], positionStyle: [{\n                type: Input\n            }], tooltipStyleClass: [{\n                type: Input\n            }], tooltipZIndex: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }], showDelay: [{\n                type: Input\n            }], hideDelay: [{\n                type: Input\n            }], life: [{\n                type: Input\n            }], positionTop: [{\n                type: Input\n            }], positionLeft: [{\n                type: Input\n            }], fitContent: [{\n                type: Input\n            }], text: [{\n                type: Input,\n                args: ['pTooltip']\n            }], disabled: [{\n                type: Input,\n                args: [\"tooltipDisabled\"]\n            }], tooltipOptions: [{\n                type: Input\n            }] } });\nclass TooltipModule {\n}\nTooltipModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nTooltipModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: TooltipModule, declarations: [Tooltip], imports: [CommonModule], exports: [Tooltip] });\nTooltipModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TooltipModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: TooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Tooltip],\n                    declarations: [Tooltip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Tooltip, TooltipModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,QAA3B,QAA2C,eAA3C;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;;AAEA,MAAMC,OAAN,CAAc;EACVC,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAWC,MAAX,EAAmB;IAC1B,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,eAAL,GAAuB;MACnBC,eAAe,EAAE,OADE;MAEnBC,YAAY,EAAE,OAFK;MAGnBC,QAAQ,EAAE,MAHS;MAInBC,aAAa,EAAE,MAJI;MAKnBN,MAAM,EAAE,IALW;MAMnBO,WAAW,EAAE,CANM;MAOnBC,YAAY,EAAE;IAPK,CAAvB;EASH;;EACW,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACE,GAAD,EAAM;IACd,KAAKD,SAAL,GAAiBC,GAAjB;IACA,KAAKC,UAAL;EACH;;EACDC,eAAe,GAAG;IACd,KAAKf,IAAL,CAAUgB,iBAAV,CAA4B,MAAM;MAC9B,IAAI,KAAKC,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;QAC5C,KAAKC,kBAAL,GAA0B,KAAKC,YAAL,CAAkBC,IAAlB,CAAuB,IAAvB,CAA1B;QACA,KAAKC,kBAAL,GAA0B,KAAKC,YAAL,CAAkBF,IAAlB,CAAuB,IAAvB,CAA1B;QACA,KAAKG,aAAL,GAAqB,KAAKC,OAAL,CAAaJ,IAAb,CAAkB,IAAlB,CAArB;QACA,KAAKrB,EAAL,CAAQ0B,aAAR,CAAsBC,gBAAtB,CAAuC,YAAvC,EAAqD,KAAKR,kBAA1D;QACA,KAAKnB,EAAL,CAAQ0B,aAAR,CAAsBC,gBAAtB,CAAuC,YAAvC,EAAqD,KAAKL,kBAA1D;QACA,KAAKtB,EAAL,CAAQ0B,aAAR,CAAsBC,gBAAtB,CAAuC,OAAvC,EAAgD,KAAKH,aAArD;MACH,CAPD,MAQK,IAAI,KAAKN,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;QACjD,KAAKU,aAAL,GAAqB,KAAKC,OAAL,CAAaR,IAAb,CAAkB,IAAlB,CAArB;QACA,KAAKS,YAAL,GAAoB,KAAKC,MAAL,CAAYV,IAAZ,CAAiB,IAAjB,CAApB;QACA,IAAIW,MAAM,GAAG,KAAKC,SAAL,CAAe,KAAKjC,EAAL,CAAQ0B,aAAvB,CAAb;QACAM,MAAM,CAACL,gBAAP,CAAwB,OAAxB,EAAiC,KAAKC,aAAtC;QACAI,MAAM,CAACL,gBAAP,CAAwB,MAAxB,EAAgC,KAAKG,YAArC;MACH;IACJ,CAhBD;EAiBH;;EACDI,WAAW,CAACC,YAAD,EAAe;IACtB,IAAIA,YAAY,CAAC7B,eAAjB,EAAkC;MAC9B,KAAK8B,SAAL,CAAe;QAAE9B,eAAe,EAAE6B,YAAY,CAAC7B,eAAb,CAA6B+B;MAAhD,CAAf;IACH;;IACD,IAAIF,YAAY,CAAC5B,YAAjB,EAA+B;MAC3B,KAAK6B,SAAL,CAAe;QAAE7B,YAAY,EAAE4B,YAAY,CAAC5B,YAAb,CAA0B8B;MAA1C,CAAf;IACH;;IACD,IAAIF,YAAY,CAAC3B,QAAjB,EAA2B;MACvB,KAAK4B,SAAL,CAAe;QAAE5B,QAAQ,EAAE2B,YAAY,CAAC3B,QAAb,CAAsB6B;MAAlC,CAAf;IACH;;IACD,IAAIF,YAAY,CAACG,aAAjB,EAAgC;MAC5B,KAAKF,SAAL,CAAe;QAAEE,aAAa,EAAEH,YAAY,CAACG,aAAb,CAA2BD;MAA5C,CAAf;IACH;;IACD,IAAIF,YAAY,CAACI,iBAAjB,EAAoC;MAChC,KAAKH,SAAL,CAAe;QAAEG,iBAAiB,EAAEJ,YAAY,CAACI,iBAAb,CAA+BF;MAApD,CAAf;IACH;;IACD,IAAIF,YAAY,CAAC1B,aAAjB,EAAgC;MAC5B,KAAK2B,SAAL,CAAe;QAAE3B,aAAa,EAAE0B,YAAY,CAAC1B,aAAb,CAA2B4B;MAA5C,CAAf;IACH;;IACD,IAAIF,YAAY,CAAChC,MAAjB,EAAyB;MACrB,KAAKiC,SAAL,CAAe;QAAEjC,MAAM,EAAEgC,YAAY,CAAChC,MAAb,CAAoBkC;MAA9B,CAAf;IACH;;IACD,IAAIF,YAAY,CAACK,SAAjB,EAA4B;MACxB,KAAKJ,SAAL,CAAe;QAAEI,SAAS,EAAEL,YAAY,CAACK,SAAb,CAAuBH;MAApC,CAAf;IACH;;IACD,IAAIF,YAAY,CAACM,SAAjB,EAA4B;MACxB,KAAKL,SAAL,CAAe;QAAEK,SAAS,EAAEN,YAAY,CAACM,SAAb,CAAuBJ;MAApC,CAAf;IACH;;IACD,IAAIF,YAAY,CAACO,IAAjB,EAAuB;MACnB,KAAKN,SAAL,CAAe;QAAEM,IAAI,EAAEP,YAAY,CAACO,IAAb,CAAkBL;MAA1B,CAAf;IACH;;IACD,IAAIF,YAAY,CAACzB,WAAjB,EAA8B;MAC1B,KAAK0B,SAAL,CAAe;QAAE1B,WAAW,EAAEyB,YAAY,CAACzB,WAAb,CAAyB2B;MAAxC,CAAf;IACH;;IACD,IAAIF,YAAY,CAACxB,YAAjB,EAA+B;MAC3B,KAAKyB,SAAL,CAAe;QAAEzB,YAAY,EAAEwB,YAAY,CAACxB,YAAb,CAA0B0B;MAA1C,CAAf;IACH;;IACD,IAAIF,YAAY,CAACvB,QAAjB,EAA2B;MACvB,KAAKwB,SAAL,CAAe;QAAExB,QAAQ,EAAEuB,YAAY,CAACvB,QAAb,CAAsByB;MAAlC,CAAf;IACH;;IACD,IAAIF,YAAY,CAACQ,IAAjB,EAAuB;MACnB,KAAKP,SAAL,CAAe;QAAEQ,YAAY,EAAET,YAAY,CAACQ,IAAb,CAAkBN;MAAlC,CAAf;;MACA,IAAI,KAAKQ,MAAT,EAAiB;QACb,IAAIV,YAAY,CAACQ,IAAb,CAAkBN,YAAtB,EAAoC;UAChC,IAAI,KAAKS,SAAL,IAAkB,KAAKA,SAAL,CAAeC,YAArC,EAAmD;YAC/C,KAAKC,UAAL;YACA,KAAKC,KAAL;UACH,CAHD,MAIK;YACD,KAAKC,IAAL;UACH;QACJ,CARD,MASK;UACD,KAAKC,IAAL;QACH;MACJ;IACJ;;IACD,IAAIhB,YAAY,CAACiB,cAAjB,EAAiC;MAC7B,KAAK/C,eAAL,GAAuBgD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKjD,eAAvB,CAAd,EAAuD8B,YAAY,CAACiB,cAAb,CAA4Bf,YAAnF,CAAvB;MACA,KAAKtB,UAAL;;MACA,IAAI,KAAK8B,MAAT,EAAiB;QACb,IAAI,KAAK3B,SAAL,CAAe,cAAf,CAAJ,EAAoC;UAChC,IAAI,KAAK4B,SAAL,IAAkB,KAAKA,SAAL,CAAeC,YAArC,EAAmD;YAC/C,KAAKC,UAAL;YACA,KAAKC,KAAL;UACH,CAHD,MAIK;YACD,KAAKC,IAAL;UACH;QACJ,CARD,MASK;UACD,KAAKC,IAAL;QACH;MACJ;IACJ;EACJ;;EACD/B,YAAY,CAACmC,CAAD,EAAI;IACZ,IAAI,CAAC,KAAKT,SAAN,IAAmB,CAAC,KAAKU,WAA7B,EAA0C;MACtC,KAAKC,QAAL;IACH;EACJ;;EACDlC,YAAY,CAACgC,CAAD,EAAI;IACZ,KAAKxC,UAAL;EACH;;EACDc,OAAO,CAAC0B,CAAD,EAAI;IACP,KAAKE,QAAL;EACH;;EACD1B,MAAM,CAACwB,CAAD,EAAI;IACN,KAAKxC,UAAL;EACH;;EACDU,OAAO,CAAC8B,CAAD,EAAI;IACP,KAAKxC,UAAL;EACH;;EACD0C,QAAQ,GAAG;IACP,KAAKZ,MAAL,GAAc,IAAd;IACA,KAAKa,gBAAL;IACA,IAAI,KAAKxC,SAAL,CAAe,WAAf,CAAJ,EACI,KAAKsC,WAAL,GAAmBG,UAAU,CAAC,MAAM;MAAE,KAAKT,IAAL;IAAc,CAAvB,EAAyB,KAAKhC,SAAL,CAAe,WAAf,CAAzB,CAA7B,CADJ,KAGI,KAAKgC,IAAL;;IACJ,IAAI,KAAKhC,SAAL,CAAe,MAAf,CAAJ,EAA4B;MACxB,IAAI0C,QAAQ,GAAG,KAAK1C,SAAL,CAAe,WAAf,IAA8B,KAAKA,SAAL,CAAe,MAAf,IAAyB,KAAKA,SAAL,CAAe,WAAf,CAAvD,GAAqF,KAAKA,SAAL,CAAe,MAAf,CAApG;MACA,KAAK2C,WAAL,GAAmBF,UAAU,CAAC,MAAM;QAAE,KAAKR,IAAL;MAAc,CAAvB,EAAyBS,QAAzB,CAA7B;IACH;EACJ;;EACD7C,UAAU,GAAG;IACT,KAAK8B,MAAL,GAAc,KAAd;IACA,KAAKiB,gBAAL;;IACA,IAAI,KAAK5C,SAAL,CAAe,WAAf,CAAJ,EAAiC;MAC7B,KAAKwC,gBAAL,GAD6B,CACJ;;MACzB,KAAKG,WAAL,GAAmBF,UAAU,CAAC,MAAM;QAAE,KAAKR,IAAL;MAAc,CAAvB,EAAyB,KAAKjC,SAAL,CAAe,WAAf,CAAzB,CAA7B;IACH,CAHD,MAIK;MACD,KAAKiC,IAAL;IACH;EACJ;;EACDY,MAAM,GAAG;IACL,IAAI,KAAKjB,SAAT,EAAoB;MAChB,KAAKY,gBAAL;MACA,KAAKM,MAAL;IACH;;IACD,KAAKlB,SAAL,GAAiBmB,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;IACA,IAAIC,YAAY,GAAGF,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAnB;IACAC,YAAY,CAACC,SAAb,GAAyB,iBAAzB;IACA,KAAKtB,SAAL,CAAeuB,WAAf,CAA2BF,YAA3B;IACA,KAAKG,WAAL,GAAmBL,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAnB;IACA,KAAKI,WAAL,CAAiBF,SAAjB,GAA6B,gBAA7B;IACA,KAAKpB,UAAL;;IACA,IAAI,KAAK9B,SAAL,CAAe,eAAf,CAAJ,EAAqC;MACjC,KAAK4B,SAAL,CAAeyB,KAAf,CAAqBC,QAArB,GAAgC,KAAKtD,SAAL,CAAe,eAAf,CAAhC;IACH;;IACD,KAAK4B,SAAL,CAAeuB,WAAf,CAA2B,KAAKC,WAAhC;IACA,IAAI,KAAKpD,SAAL,CAAe,UAAf,MAA+B,MAAnC,EACI+C,QAAQ,CAACQ,IAAT,CAAcJ,WAAd,CAA0B,KAAKvB,SAA/B,EADJ,KAEK,IAAI,KAAK5B,SAAL,CAAe,UAAf,MAA+B,QAAnC,EACDxB,UAAU,CAAC2E,WAAX,CAAuB,KAAKvB,SAA5B,EAAuC,KAAK9C,EAAL,CAAQ0B,aAA/C,EADC,KAGDhC,UAAU,CAAC2E,WAAX,CAAuB,KAAKvB,SAA5B,EAAuC,KAAK5B,SAAL,CAAe,UAAf,CAAvC;IACJ,KAAK4B,SAAL,CAAeyB,KAAf,CAAqBG,OAArB,GAA+B,cAA/B;;IACA,IAAI,KAAKtE,UAAT,EAAqB;MACjB,KAAK0C,SAAL,CAAeyB,KAAf,CAAqBI,KAArB,GAA6B,aAA7B;IACH;EACJ;;EACDzB,IAAI,GAAG;IACH,IAAI,CAAC,KAAKhC,SAAL,CAAe,cAAf,CAAD,IAAmC,KAAKA,SAAL,CAAe,UAAf,CAAvC,EAAmE;MAC/D;IACH;;IACD,KAAK6C,MAAL;IACA,KAAKd,KAAL;IACAvD,UAAU,CAACkF,MAAX,CAAkB,KAAK9B,SAAvB,EAAkC,GAAlC;IACA,IAAI,KAAK5B,SAAL,CAAe,eAAf,MAAoC,MAAxC,EACItB,WAAW,CAACiF,GAAZ,CAAgB,SAAhB,EAA2B,KAAK/B,SAAhC,EAA2C,KAAK5C,MAAL,CAAY4E,MAAZ,CAAmBC,OAA9D,EADJ,KAGI,KAAKjC,SAAL,CAAeyB,KAAf,CAAqBO,MAArB,GAA8B,KAAK5D,SAAL,CAAe,eAAf,CAA9B;IACJ,KAAK8D,0BAAL;IACA,KAAKC,kBAAL;EACH;;EACD9B,IAAI,GAAG;IACH,IAAI,KAAKjC,SAAL,CAAe,eAAf,MAAoC,MAAxC,EAAgD;MAC5CtB,WAAW,CAACsF,KAAZ,CAAkB,KAAKpC,SAAvB;IACH;;IACD,KAAKkB,MAAL;EACH;;EACDhB,UAAU,GAAG;IACT,IAAI,KAAK9B,SAAL,CAAe,QAAf,CAAJ,EAA8B;MAC1B,KAAKoD,WAAL,CAAiBa,SAAjB,GAA6B,EAA7B;MACA,KAAKb,WAAL,CAAiBD,WAAjB,CAA6BJ,QAAQ,CAACmB,cAAT,CAAwB,KAAKlE,SAAL,CAAe,cAAf,CAAxB,CAA7B;IACH,CAHD,MAIK;MACD,KAAKoD,WAAL,CAAiBa,SAAjB,GAA6B,KAAKjE,SAAL,CAAe,cAAf,CAA7B;IACH;EACJ;;EACD+B,KAAK,GAAG;IACJ,IAAIuB,QAAQ,GAAG,KAAKtD,SAAL,CAAe,iBAAf,CAAf;;IACA,QAAQsD,QAAR;MACI,KAAK,KAAL;QACI,KAAKa,QAAL;;QACA,IAAI,KAAKC,aAAL,EAAJ,EAA0B;UACtB,KAAKC,WAAL;;UACA,IAAI,KAAKD,aAAL,EAAJ,EAA0B;YACtB,KAAKE,UAAL;;YACA,IAAI,KAAKF,aAAL,EAAJ,EAA0B;cACtB,KAAKG,SAAL;YACH;UACJ;QACJ;;QACD;;MACJ,KAAK,QAAL;QACI,KAAKF,WAAL;;QACA,IAAI,KAAKD,aAAL,EAAJ,EAA0B;UACtB,KAAKD,QAAL;;UACA,IAAI,KAAKC,aAAL,EAAJ,EAA0B;YACtB,KAAKE,UAAL;;YACA,IAAI,KAAKF,aAAL,EAAJ,EAA0B;cACtB,KAAKG,SAAL;YACH;UACJ;QACJ;;QACD;;MACJ,KAAK,MAAL;QACI,KAAKA,SAAL;;QACA,IAAI,KAAKH,aAAL,EAAJ,EAA0B;UACtB,KAAKE,UAAL;;UACA,IAAI,KAAKF,aAAL,EAAJ,EAA0B;YACtB,KAAKD,QAAL;;YACA,IAAI,KAAKC,aAAL,EAAJ,EAA0B;cACtB,KAAKC,WAAL;YACH;UACJ;QACJ;;QACD;;MACJ,KAAK,OAAL;QACI,KAAKC,UAAL;;QACA,IAAI,KAAKF,aAAL,EAAJ,EAA0B;UACtB,KAAKG,SAAL;;UACA,IAAI,KAAKH,aAAL,EAAJ,EAA0B;YACtB,KAAKD,QAAL;;YACA,IAAI,KAAKC,aAAL,EAAJ,EAA0B;cACtB,KAAKC,WAAL;YACH;UACJ;QACJ;;QACD;IAhDR;EAkDH;;EACDG,aAAa,GAAG;IACZ,IAAI,KAAKxE,SAAL,CAAe,UAAf,MAA+B,MAA/B,IAAyC,KAAKA,SAAL,CAAe,UAAf,MAA+B,QAA5E,EAAsF;MAClF,IAAIyE,MAAM,GAAG,KAAK3F,EAAL,CAAQ0B,aAAR,CAAsBkE,qBAAtB,EAAb;MACA,IAAIC,UAAU,GAAGF,MAAM,CAACG,IAAP,GAAcpG,UAAU,CAACqG,mBAAX,EAA/B;MACA,IAAIC,SAAS,GAAGL,MAAM,CAACM,GAAP,GAAavG,UAAU,CAACwG,kBAAX,EAA7B;MACA,OAAO;QAAEJ,IAAI,EAAED,UAAR;QAAoBI,GAAG,EAAED;MAAzB,CAAP;IACH,CALD,MAMK;MACD,OAAO;QAAEF,IAAI,EAAE,CAAR;QAAWG,GAAG,EAAE;MAAhB,CAAP;IACH;EACJ;;EACDT,UAAU,GAAG;IACT,KAAKW,QAAL,CAAc,OAAd;IACA,IAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;IACA,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkBpG,UAAU,CAAC2G,aAAX,CAAyB,KAAKrG,EAAL,CAAQ0B,aAAjC,CAA7B;IACA,IAAIuE,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiB,CAACvG,UAAU,CAAC4G,cAAX,CAA0B,KAAKtG,EAAL,CAAQ0B,aAAlC,IAAmDhC,UAAU,CAAC4G,cAAX,CAA0B,KAAKxD,SAA/B,CAApD,IAAiG,CAA5H;IACA,KAAKA,SAAL,CAAeyB,KAAf,CAAqBuB,IAArB,GAA4BA,IAAI,GAAG,KAAK5E,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;IACA,KAAK4B,SAAL,CAAeyB,KAAf,CAAqB0B,GAArB,GAA2BA,GAAG,GAAG,KAAK/E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;EACH;;EACDuE,SAAS,GAAG;IACR,KAAKU,QAAL,CAAc,MAAd;IACA,IAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;IACA,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkBpG,UAAU,CAAC2G,aAAX,CAAyB,KAAKvD,SAA9B,CAA7B;IACA,IAAImD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiB,CAACvG,UAAU,CAAC4G,cAAX,CAA0B,KAAKtG,EAAL,CAAQ0B,aAAlC,IAAmDhC,UAAU,CAAC4G,cAAX,CAA0B,KAAKxD,SAA/B,CAApD,IAAiG,CAA5H;IACA,KAAKA,SAAL,CAAeyB,KAAf,CAAqBuB,IAArB,GAA4BA,IAAI,GAAG,KAAK5E,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;IACA,KAAK4B,SAAL,CAAeyB,KAAf,CAAqB0B,GAArB,GAA2BA,GAAG,GAAG,KAAK/E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;EACH;;EACDmE,QAAQ,GAAG;IACP,KAAKc,QAAL,CAAc,KAAd;IACA,IAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;IACA,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkB,CAACpG,UAAU,CAAC2G,aAAX,CAAyB,KAAKrG,EAAL,CAAQ0B,aAAjC,IAAkDhC,UAAU,CAAC2G,aAAX,CAAyB,KAAKvD,SAA9B,CAAnD,IAA+F,CAA5H;IACA,IAAImD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiBvG,UAAU,CAAC4G,cAAX,CAA0B,KAAKxD,SAA/B,CAA3B;IACA,KAAKA,SAAL,CAAeyB,KAAf,CAAqBuB,IAArB,GAA4BA,IAAI,GAAG,KAAK5E,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;IACA,KAAK4B,SAAL,CAAeyB,KAAf,CAAqB0B,GAArB,GAA2BA,GAAG,GAAG,KAAK/E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;EACH;;EACDqE,WAAW,GAAG;IACV,KAAKY,QAAL,CAAc,QAAd;IACA,IAAIC,UAAU,GAAG,KAAKV,aAAL,EAAjB;IACA,IAAII,IAAI,GAAGM,UAAU,CAACN,IAAX,GAAkB,CAACpG,UAAU,CAAC2G,aAAX,CAAyB,KAAKrG,EAAL,CAAQ0B,aAAjC,IAAkDhC,UAAU,CAAC2G,aAAX,CAAyB,KAAKvD,SAA9B,CAAnD,IAA+F,CAA5H;IACA,IAAImD,GAAG,GAAGG,UAAU,CAACH,GAAX,GAAiBvG,UAAU,CAAC4G,cAAX,CAA0B,KAAKtG,EAAL,CAAQ0B,aAAlC,CAA3B;IACA,KAAKoB,SAAL,CAAeyB,KAAf,CAAqBuB,IAArB,GAA4BA,IAAI,GAAG,KAAK5E,SAAL,CAAe,cAAf,CAAP,GAAwC,IAApE;IACA,KAAK4B,SAAL,CAAeyB,KAAf,CAAqB0B,GAArB,GAA2BA,GAAG,GAAG,KAAK/E,SAAL,CAAe,aAAf,CAAN,GAAsC,IAAjE;EACH;;EACDkB,SAAS,CAACmE,MAAD,EAAS;IACd,KAAKlG,eAAL,GAAuBgD,MAAM,CAACC,MAAP,CAAcD,MAAM,CAACC,MAAP,CAAc,EAAd,EAAkB,KAAKjD,eAAvB,CAAd,EAAuDkG,MAAvD,CAAvB;EACH;;EACDrF,SAAS,CAACqF,MAAD,EAAS;IACd,OAAO,KAAKlG,eAAL,CAAqBkG,MAArB,CAAP;EACH;;EACDtE,SAAS,CAACjC,EAAD,EAAK;IACV,OAAON,UAAU,CAAC8G,QAAX,CAAoBxG,EAApB,EAAwB,gBAAxB,IAA4CN,UAAU,CAAC+G,UAAX,CAAsBzG,EAAtB,EAA0B,OAA1B,CAA5C,GAAiFA,EAAxF;EACH;;EACDmG,QAAQ,CAAC3B,QAAD,EAAW;IACf,KAAK1B,SAAL,CAAeyB,KAAf,CAAqBuB,IAArB,GAA4B,CAAC,GAAD,GAAO,IAAnC;IACA,KAAKhD,SAAL,CAAeyB,KAAf,CAAqB0B,GAArB,GAA2B,CAAC,GAAD,GAAO,IAAlC;IACA,IAAIS,gBAAgB,GAAG,qCAAqClC,QAA5D;IACA,KAAK1B,SAAL,CAAesB,SAAf,GAA2B,KAAKlD,SAAL,CAAe,mBAAf,IAAsCwF,gBAAgB,GAAG,GAAnB,GAAyB,KAAKxF,SAAL,CAAe,mBAAf,CAA/D,GAAqGwF,gBAAhI;EACH;;EACDpB,aAAa,GAAG;IACZ,IAAIK,MAAM,GAAG,KAAK7C,SAAL,CAAe8C,qBAAf,EAAb;IACA,IAAII,SAAS,GAAGL,MAAM,CAACM,GAAvB;IACA,IAAIJ,UAAU,GAAGF,MAAM,CAACG,IAAxB;IACA,IAAInB,KAAK,GAAGjF,UAAU,CAAC2G,aAAX,CAAyB,KAAKvD,SAA9B,CAAZ;IACA,IAAI6D,MAAM,GAAGjH,UAAU,CAAC4G,cAAX,CAA0B,KAAKxD,SAA/B,CAAb;IACA,IAAI8D,QAAQ,GAAGlH,UAAU,CAACmH,WAAX,EAAf;IACA,OAAQhB,UAAU,GAAGlB,KAAb,GAAqBiC,QAAQ,CAACjC,KAA/B,IAA0CkB,UAAU,GAAG,CAAvD,IAA8DG,SAAS,GAAG,CAA1E,IAAiFA,SAAS,GAAGW,MAAZ,GAAqBC,QAAQ,CAACD,MAAtH;EACH;;EACDG,cAAc,CAACvD,CAAD,EAAI;IACd,KAAKJ,IAAL;EACH;;EACD6B,0BAA0B,GAAG;IACzB,KAAK/E,IAAL,CAAUgB,iBAAV,CAA4B,MAAM;MAC9B,KAAK8F,cAAL,GAAsB,KAAKD,cAAL,CAAoBzF,IAApB,CAAyB,IAAzB,CAAtB;MACA2F,MAAM,CAACrF,gBAAP,CAAwB,QAAxB,EAAkC,KAAKoF,cAAvC;IACH,CAHD;EAIH;;EACDE,4BAA4B,GAAG;IAC3B,IAAI,KAAKF,cAAT,EAAyB;MACrBC,MAAM,CAACE,mBAAP,CAA2B,QAA3B,EAAqC,KAAKH,cAA1C;MACA,KAAKA,cAAL,GAAsB,IAAtB;IACH;EACJ;;EACD9B,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKkC,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAIxH,6BAAJ,CAAkC,KAAKK,EAAL,CAAQ0B,aAA1C,EAAyD,MAAM;QAChF,IAAI,KAAKoB,SAAT,EAAoB;UAChB,KAAKK,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKgE,aAAL,CAAmBlC,kBAAnB;EACH;;EACDmC,oBAAoB,GAAG;IACnB,IAAI,KAAKD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBC,oBAAnB;IACH;EACJ;;EACDC,YAAY,GAAG;IACX,IAAI,KAAKnG,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;MAC5C,KAAKlB,EAAL,CAAQ0B,aAAR,CAAsBwF,mBAAtB,CAA0C,YAA1C,EAAwD,KAAK/F,kBAA7D;MACA,KAAKnB,EAAL,CAAQ0B,aAAR,CAAsBwF,mBAAtB,CAA0C,YAA1C,EAAwD,KAAK5F,kBAA7D;MACA,KAAKtB,EAAL,CAAQ0B,aAAR,CAAsBwF,mBAAtB,CAA0C,OAA1C,EAAmD,KAAK1F,aAAxD;IACH,CAJD,MAKK,IAAI,KAAKN,SAAL,CAAe,cAAf,MAAmC,OAAvC,EAAgD;MACjD,IAAIc,MAAM,GAAG,KAAKC,SAAL,CAAe,KAAKjC,EAAL,CAAQ0B,aAAvB,CAAb;MACAM,MAAM,CAACkF,mBAAP,CAA2B,OAA3B,EAAoC,KAAKtF,aAAzC;MACAI,MAAM,CAACkF,mBAAP,CAA2B,MAA3B,EAAmC,KAAKpF,YAAxC;IACH;;IACD,KAAKmF,4BAAL;EACH;;EACDjD,MAAM,GAAG;IACL,IAAI,KAAKlB,SAAL,IAAkB,KAAKA,SAAL,CAAewE,aAArC,EAAoD;MAChD,IAAI,KAAKpG,SAAL,CAAe,UAAf,MAA+B,MAAnC,EACI+C,QAAQ,CAACQ,IAAT,CAAc8C,WAAd,CAA0B,KAAKzE,SAA/B,EADJ,KAEK,IAAI,KAAK5B,SAAL,CAAe,UAAf,MAA+B,QAAnC,EACD,KAAKlB,EAAL,CAAQ0B,aAAR,CAAsB6F,WAAtB,CAAkC,KAAKzE,SAAvC,EADC,KAGDpD,UAAU,CAAC6H,WAAX,CAAuB,KAAKzE,SAA5B,EAAuC,KAAK5B,SAAL,CAAe,UAAf,CAAvC;IACP;;IACD,KAAK+F,4BAAL;IACA,KAAKG,oBAAL;IACA,KAAKI,aAAL;IACA,KAAK1E,SAAL,GAAiB,IAAjB;IACA,KAAKqE,aAAL,GAAqB,IAArB;EACH;;EACDrD,gBAAgB,GAAG;IACf,IAAI,KAAKN,WAAT,EAAsB;MAClBiE,YAAY,CAAC,KAAKjE,WAAN,CAAZ;MACA,KAAKA,WAAL,GAAmB,IAAnB;IACH;EACJ;;EACDE,gBAAgB,GAAG;IACf,IAAI,KAAKG,WAAT,EAAsB;MAClB4D,YAAY,CAAC,KAAK5D,WAAN,CAAZ;MACA,KAAKA,WAAL,GAAmB,IAAnB;IACH;EACJ;;EACD2D,aAAa,GAAG;IACZ,KAAK1D,gBAAL;IACA,KAAKJ,gBAAL;EACH;;EACDgE,WAAW,GAAG;IACV,KAAKL,YAAL;;IACA,IAAI,KAAKvE,SAAT,EAAoB;MAChBlD,WAAW,CAACsF,KAAZ,CAAkB,KAAKpC,SAAvB;IACH;;IACD,KAAKkB,MAAL;;IACA,IAAI,KAAKmD,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBQ,OAAnB;MACA,KAAKR,aAAL,GAAqB,IAArB;IACH;EACJ;;AAnaS;;AAqadrH,OAAO,CAAC8H,IAAR;EAAA,iBAAoG9H,OAApG,EAA0FT,EAA1F,mBAA6HA,EAAE,CAACwI,UAAhI,GAA0FxI,EAA1F,mBAAuJA,EAAE,CAACyI,MAA1J,GAA0FzI,EAA1F,mBAA6KQ,EAAE,CAACkI,aAAhL;AAAA;;AACAjI,OAAO,CAACkI,IAAR,kBAD0F3I,EAC1F;EAAA,MAAwFS,OAAxF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD0FT,EAC1F;AAAA;;AACA;EAAA,mDAF0FA,EAE1F,mBAA2FS,OAA3F,EAAgH,CAAC;IACrGmI,IAAI,EAAE3I,SAD+F;IAErG4I,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,YADX;MAECC,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAF+F,CAAD,CAAhH,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAE5I,EAAE,CAACwI;IAAX,CAAD,EAA0B;MAAEI,IAAI,EAAE5I,EAAE,CAACyI;IAAX,CAA1B,EAA+C;MAAEG,IAAI,EAAEpI,EAAE,CAACkI;IAAX,CAA/C,CAAP;EAAoF,CAR9H,EAQgJ;IAAEzH,eAAe,EAAE,CAAC;MACpJ2H,IAAI,EAAE1I;IAD8I,CAAD,CAAnB;IAEhIgB,YAAY,EAAE,CAAC;MACf0H,IAAI,EAAE1I;IADS,CAAD,CAFkH;IAIhIiB,QAAQ,EAAE,CAAC;MACXyH,IAAI,EAAE1I;IADK,CAAD,CAJsH;IAMhI+C,aAAa,EAAE,CAAC;MAChB2F,IAAI,EAAE1I;IADU,CAAD,CANiH;IAQhIgD,iBAAiB,EAAE,CAAC;MACpB0F,IAAI,EAAE1I;IADc,CAAD,CAR6G;IAUhIkB,aAAa,EAAE,CAAC;MAChBwH,IAAI,EAAE1I;IADU,CAAD,CAViH;IAYhIY,MAAM,EAAE,CAAC;MACT8H,IAAI,EAAE1I;IADG,CAAD,CAZwH;IAchIiD,SAAS,EAAE,CAAC;MACZyF,IAAI,EAAE1I;IADM,CAAD,CAdqH;IAgBhIkD,SAAS,EAAE,CAAC;MACZwF,IAAI,EAAE1I;IADM,CAAD,CAhBqH;IAkBhImD,IAAI,EAAE,CAAC;MACPuF,IAAI,EAAE1I;IADC,CAAD,CAlB0H;IAoBhImB,WAAW,EAAE,CAAC;MACduH,IAAI,EAAE1I;IADQ,CAAD,CApBmH;IAsBhIoB,YAAY,EAAE,CAAC;MACfsH,IAAI,EAAE1I;IADS,CAAD,CAtBkH;IAwBhIa,UAAU,EAAE,CAAC;MACb6H,IAAI,EAAE1I;IADO,CAAD,CAxBoH;IA0BhIoD,IAAI,EAAE,CAAC;MACPsF,IAAI,EAAE1I,KADC;MAEP2I,IAAI,EAAE,CAAC,UAAD;IAFC,CAAD,CA1B0H;IA6BhItH,QAAQ,EAAE,CAAC;MACXqH,IAAI,EAAE1I,KADK;MAEX2I,IAAI,EAAE,CAAC,iBAAD;IAFK,CAAD,CA7BsH;IAgChI9E,cAAc,EAAE,CAAC;MACjB6E,IAAI,EAAE1I;IADW,CAAD;EAhCgH,CARhJ;AAAA;;AA2CA,MAAM8I,aAAN,CAAoB;;AAEpBA,aAAa,CAACT,IAAd;EAAA,iBAA0GS,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAhD0FjJ,EAgD1F;EAAA,MAA2GgJ;AAA3G;AACAA,aAAa,CAACE,IAAd,kBAjD0FlJ,EAiD1F;EAAA,UAAoII,YAApI;AAAA;;AACA;EAAA,mDAlD0FJ,EAkD1F,mBAA2FgJ,aAA3F,EAAsH,CAAC;IAC3GJ,IAAI,EAAEzI,QADqG;IAE3G0I,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAAC/I,YAAD,CADV;MAECgJ,OAAO,EAAE,CAAC3I,OAAD,CAFV;MAGC4I,YAAY,EAAE,CAAC5I,OAAD;IAHf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,OAAT,EAAkBuI,aAAlB"}, "metadata": {}, "sourceType": "module"}