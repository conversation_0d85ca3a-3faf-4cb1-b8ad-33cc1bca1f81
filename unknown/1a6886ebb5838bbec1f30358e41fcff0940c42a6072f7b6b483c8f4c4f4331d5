{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i5 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\nconst _c0 = [\"container\"];\nconst _c1 = [\"in\"];\nconst _c2 = [\"multiIn\"];\nconst _c3 = [\"multiContainer\"];\nconst _c4 = [\"ddBtn\"];\nconst _c5 = [\"items\"];\nconst _c6 = [\"scroller\"];\n\nconst _c7 = function (a0, a1) {\n  return {\n    \"p-autocomplete-dd-input\": a0,\n    \"p-disabled\": a1\n  };\n};\n\nfunction AutoComplete_input_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 8, 9);\n    i0.ɵɵlistener(\"click\", function AutoComplete_input_2_Template_input_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onInputClick($event));\n    })(\"input\", function AutoComplete_input_2_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onInput($event));\n    })(\"keydown\", function AutoComplete_input_2_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onKeydown($event));\n    })(\"keyup\", function AutoComplete_input_2_Template_input_keyup_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onKeyup($event));\n    })(\"focus\", function AutoComplete_input_2_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onInputFocus($event));\n    })(\"blur\", function AutoComplete_input_2_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onInputBlur($event));\n    })(\"change\", function AutoComplete_input_2_Template_input_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onInputChange($event));\n    })(\"paste\", function AutoComplete_input_2_Template_input_paste_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onInputPaste($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.inputStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.inputStyle)(\"autocomplete\", ctx_r1.autocomplete)(\"ngClass\", i0.ɵɵpureFunction2(20, _c7, ctx_r1.dropdown, ctx_r1.disabled))(\"value\", ctx_r1.inputFieldValue)(\"readonly\", ctx_r1.readonly)(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"type\", ctx_r1.type)(\"id\", ctx_r1.inputId)(\"required\", ctx_r1.required)(\"name\", ctx_r1.name)(\"autofocus\", ctx_r1.autofocus)(\"placeholder\", ctx_r1.placeholder)(\"size\", ctx_r1.size)(\"maxlength\", ctx_r1.maxlength)(\"tabindex\", ctx_r1.tabindex)(\"aria-label\", ctx_r1.ariaLabel)(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-required\", ctx_r1.required);\n  }\n}\n\nfunction AutoComplete_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 10);\n    i0.ɵɵlistener(\"click\", function AutoComplete_i_3_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AutoComplete_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"i\", 10);\n    i0.ɵɵlistener(\"click\", function AutoComplete_i_4_Template_i_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction AutoComplete_ul_5_li_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AutoComplete_ul_5_li_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const val_r25 = i0.ɵɵnextContext().$implicit;\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r28.resolveFieldData(val_r25));\n  }\n}\n\nfunction AutoComplete_ul_5_li_2_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ul_5_li_2_span_4_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r32);\n      i0.ɵɵnextContext();\n\n      const _r26 = i0.ɵɵreference(1);\n\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.removeItem(_r26));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c8 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\n\nfunction AutoComplete_ul_5_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 17, 18);\n    i0.ɵɵtemplate(2, AutoComplete_ul_5_li_2_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵtemplate(3, AutoComplete_ul_5_li_2_span_3_Template, 2, 1, \"span\", 20);\n    i0.ɵɵtemplate(4, AutoComplete_ul_5_li_2_span_4_Template, 1, 0, \"span\", 21);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const val_r25 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r23.selectedItemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c8, val_r25));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.selectedItemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r23.disabled && !ctx_r23.readonly);\n  }\n}\n\nconst _c9 = function (a0, a1) {\n  return {\n    \"p-disabled\": a0,\n    \"p-focus\": a1\n  };\n};\n\nfunction AutoComplete_ul_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"ul\", 11, 12);\n    i0.ɵɵlistener(\"click\", function AutoComplete_ul_5_Template_ul_click_0_listener() {\n      i0.ɵɵrestoreView(_r34);\n\n      const _r24 = i0.ɵɵreference(5);\n\n      return i0.ɵɵresetView(_r24.focus());\n    });\n    i0.ɵɵtemplate(2, AutoComplete_ul_5_li_2_Template, 5, 6, \"li\", 13);\n    i0.ɵɵelementStart(3, \"li\", 14)(4, \"input\", 15, 16);\n    i0.ɵɵlistener(\"input\", function AutoComplete_ul_5_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.onInput($event));\n    })(\"click\", function AutoComplete_ul_5_Template_input_click_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onInputClick($event));\n    })(\"keydown\", function AutoComplete_ul_5_Template_input_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.onKeydown($event));\n    })(\"keyup\", function AutoComplete_ul_5_Template_input_keyup_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.onKeyup($event));\n    })(\"focus\", function AutoComplete_ul_5_Template_input_focus_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.onInputFocus($event));\n    })(\"blur\", function AutoComplete_ul_5_Template_input_blur_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.onInputBlur($event));\n    })(\"change\", function AutoComplete_ul_5_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.onInputChange($event));\n    })(\"paste\", function AutoComplete_ul_5_Template_input_paste_4_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.onInputPaste($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(20, _c9, ctx_r4.disabled, ctx_r4.focus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.inputStyleClass);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.disabled)(\"readonly\", ctx_r4.readonly)(\"autocomplete\", ctx_r4.autocomplete)(\"ngStyle\", ctx_r4.inputStyle);\n    i0.ɵɵattribute(\"type\", ctx_r4.type)(\"id\", ctx_r4.inputId)(\"placeholder\", ctx_r4.value && ctx_r4.value.length ? null : ctx_r4.placeholder)(\"tabindex\", ctx_r4.tabindex)(\"maxlength\", ctx_r4.maxlength)(\"autofocus\", ctx_r4.autofocus)(\"aria-label\", ctx_r4.ariaLabel)(\"aria-labelledby\", ctx_r4.ariaLabelledBy)(\"aria-required\", ctx_r4.required)(\"aria-controls\", ctx_r4.listId)(\"aria-expanded\", ctx_r4.overlayVisible)(\"aria-activedescendant\", \"p-highlighted-option\");\n  }\n}\n\nfunction AutoComplete_i_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 24);\n  }\n}\n\nfunction AutoComplete_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 25, 26);\n    i0.ɵɵlistener(\"click\", function AutoComplete_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.handleDropdownClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"icon\", ctx_r6.dropdownIcon)(\"disabled\", ctx_r6.disabled);\n    i0.ɵɵattribute(\"aria-label\", ctx_r6.dropdownAriaLabel)(\"tabindex\", ctx_r6.tabindex);\n  }\n}\n\nfunction AutoComplete_div_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AutoComplete_div_8_p_scroller_3_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c10 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    options: a1\n  };\n};\n\nfunction AutoComplete_div_8_p_scroller_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_div_8_p_scroller_3_ng_template_2_ng_container_0_Template, 1, 0, \"ng-container\", 19);\n  }\n\n  if (rf & 2) {\n    const items_r56 = ctx.$implicit;\n    const scrollerOptions_r57 = ctx.options;\n    i0.ɵɵnextContext(2);\n\n    const _r50 = i0.ɵɵreference(6);\n\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r50)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c10, items_r56, scrollerOptions_r57));\n  }\n}\n\nfunction AutoComplete_div_8_p_scroller_3_ng_container_3_ng_template_1_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c11 = function (a0) {\n  return {\n    options: a0\n  };\n};\n\nfunction AutoComplete_div_8_p_scroller_3_ng_container_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_div_8_p_scroller_3_ng_container_3_ng_template_1_ng_container_0_Template, 1, 0, \"ng-container\", 19);\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r60 = ctx.options;\n    const ctx_r59 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r59.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c11, scrollerOptions_r60));\n  }\n}\n\nfunction AutoComplete_div_8_p_scroller_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_p_scroller_3_ng_container_3_ng_template_1_Template, 1, 4, \"ng-template\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n}\n\nconst _c12 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nfunction AutoComplete_div_8_p_scroller_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r63 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-scroller\", 33, 34);\n    i0.ɵɵlistener(\"onLazyLoad\", function AutoComplete_div_8_p_scroller_3_Template_p_scroller_onLazyLoad_0_listener($event) {\n      i0.ɵɵrestoreView(_r63);\n      const ctx_r62 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r62.onLazyLoad.emit($event));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_div_8_p_scroller_3_ng_template_2_Template, 1, 5, \"ng-template\", 35);\n    i0.ɵɵtemplate(3, AutoComplete_div_8_p_scroller_3_ng_container_3_Template, 2, 0, \"ng-container\", 31);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(8, _c12, ctx_r48.scrollHeight));\n    i0.ɵɵproperty(\"items\", ctx_r48.suggestions)(\"itemSize\", ctx_r48.virtualScrollItemSize || ctx_r48._itemSize)(\"autoSize\", true)(\"lazy\", ctx_r48.lazy)(\"options\", ctx_r48.virtualScrollOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r48.loaderTemplate);\n  }\n}\n\nfunction AutoComplete_div_8_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c13 = function () {\n  return {};\n};\n\nfunction AutoComplete_div_8_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_container_4_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n\n    const _r50 = i0.ɵɵreference(6);\n\n    const ctx_r49 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r50)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c10, ctx_r49.suggestions, i0.ɵɵpureFunction0(2, _c13)));\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const optgroup_r73 = i0.ɵɵnextContext().$implicit;\n    const ctx_r74 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r74.getOptionGroupLabel(optgroup_r73) || \"empty\");\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 41);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_span_1_Template, 2, 1, \"span\", 31);\n    i0.ɵɵtemplate(2, AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_ng_container_3_Template, 1, 0, \"ng-container\", 19);\n  }\n\n  if (rf & 2) {\n    const optgroup_r73 = ctx.$implicit;\n    const scrollerOptions_r66 = i0.ɵɵnextContext(2).options;\n\n    const _r70 = i0.ɵɵreference(5);\n\n    const ctx_r72 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c12, scrollerOptions_r66.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r72.groupTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r72.groupTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(8, _c8, optgroup_r73));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r70)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(10, _c8, ctx_r72.getOptionGroupChildren(optgroup_r73)));\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_container_2_ng_template_1_Template, 4, 12, \"ng-template\", 40);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const items_r65 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", items_r65);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const items_r65 = i0.ɵɵnextContext().$implicit;\n\n    const _r70 = i0.ɵɵreference(5);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r70)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c8, items_r65));\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_0_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r85 = i0.ɵɵnextContext().$implicit;\n    const ctx_r87 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r87.resolveFieldData(option_r85));\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c14 = function (a0) {\n  return {\n    \"p-highlight\": a0\n  };\n};\n\nconst _c15 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    index: a1\n  };\n};\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r91 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 44);\n    i0.ɵɵlistener(\"click\", function AutoComplete_div_8_ng_template_5_ng_template_4_li_0_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r91);\n      const option_r85 = restoredCtx.$implicit;\n      const ctx_r90 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r90.selectItem(option_r85));\n    });\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_template_4_li_0_span_1_Template, 2, 1, \"span\", 31);\n    i0.ɵɵtemplate(2, AutoComplete_div_8_ng_template_5_ng_template_4_li_0_ng_container_2_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r85 = ctx.$implicit;\n    const idx_r86 = ctx.index;\n    const scrollerOptions_r66 = i0.ɵɵnextContext(2).options;\n    const ctx_r83 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c12, scrollerOptions_r66.itemSize + \"px\"))(\"ngClass\", i0.ɵɵpureFunction1(8, _c14, option_r85 === ctx_r83.highlightOption))(\"id\", ctx_r83.highlightOption == option_r85 ? \"p-highlighted-option\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r83.itemTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r83.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(10, _c15, option_r85, scrollerOptions_r66.getOptions ? scrollerOptions_r66.getOptions(idx_r86) : idx_r86));\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r93 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r93.emptyMessageLabel, \" \");\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, null, 47);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 45);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_template_4_li_1_ng_container_1_Template, 2, 1, \"ng-container\", 46);\n    i0.ɵɵtemplate(2, AutoComplete_div_8_ng_template_5_ng_template_4_li_1_ng_container_2_Template, 2, 0, \"ng-container\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r66 = i0.ɵɵnextContext(2).options;\n    const ctx_r84 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c12, scrollerOptions_r66.itemSize + \"px\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r84.emptyTemplate)(\"ngIfElse\", ctx_r84.empty);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r84.emptyTemplate);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AutoComplete_div_8_ng_template_5_ng_template_4_li_0_Template, 3, 13, \"li\", 42);\n    i0.ɵɵtemplate(1, AutoComplete_div_8_ng_template_5_ng_template_4_li_1_Template, 3, 6, \"li\", 43);\n  }\n\n  if (rf & 2) {\n    const suggestionsToDisplay_r82 = ctx.$implicit;\n    const ctx_r71 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngForOf\", suggestionsToDisplay_r82);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r71.noResults && ctx_r71.showEmptyMessage);\n  }\n}\n\nfunction AutoComplete_div_8_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 37, 38);\n    i0.ɵɵtemplate(2, AutoComplete_div_8_ng_template_5_ng_container_2_Template, 2, 1, \"ng-container\", 31);\n    i0.ɵɵtemplate(3, AutoComplete_div_8_ng_template_5_ng_container_3_Template, 2, 4, \"ng-container\", 31);\n    i0.ɵɵtemplate(4, AutoComplete_div_8_ng_template_5_ng_template_4_Template, 2, 2, \"ng-template\", null, 39, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const scrollerOptions_r66 = ctx.options;\n    const ctx_r51 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(scrollerOptions_r66.contentStyle);\n    i0.ɵɵproperty(\"ngClass\", scrollerOptions_r66.contentStyleClass);\n    i0.ɵɵattribute(\"id\", ctx_r51.listId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r51.group);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r51.group);\n  }\n}\n\nfunction AutoComplete_div_8_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c16 = function () {\n  return [\"p-autocomplete-panel p-component\"];\n};\n\nconst _c17 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c18 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction AutoComplete_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r98 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 27, 28);\n    i0.ɵɵlistener(\"click\", function AutoComplete_div_8_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r97 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r97.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function AutoComplete_div_8_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r99 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r99.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function AutoComplete_div_8_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r98);\n      const ctx_r100 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r100.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(2, AutoComplete_div_8_ng_container_2_Template, 1, 0, \"ng-container\", 29);\n    i0.ɵɵtemplate(3, AutoComplete_div_8_p_scroller_3_Template, 4, 10, \"p-scroller\", 30);\n    i0.ɵɵtemplate(4, AutoComplete_div_8_ng_container_4_Template, 2, 6, \"ng-container\", 31);\n    i0.ɵɵtemplate(5, AutoComplete_div_8_ng_template_5_Template, 6, 6, \"ng-template\", null, 32, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(7, AutoComplete_div_8_ng_container_7_Template, 1, 0, \"ng-container\", 29);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r7.panelStyleClass);\n    i0.ɵɵstyleProp(\"max-height\", ctx_r7.virtualScroll ? \"auto\" : ctx_r7.scrollHeight);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(11, _c16))(\"ngStyle\", ctx_r7.panelStyle)(\"@overlayAnimation\", i0.ɵɵpureFunction1(15, _c18, i0.ɵɵpureFunction2(12, _c17, ctx_r7.showTransitionOptions, ctx_r7.hideTransitionOptions)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.headerTemplate);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.virtualScroll);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r7.virtualScroll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r7.footerTemplate);\n  }\n}\n\nconst _c19 = function (a1, a2) {\n  return {\n    \"p-autocomplete p-component\": true,\n    \"p-autocomplete-dd\": a1,\n    \"p-autocomplete-multiple\": a2\n  };\n};\n\nconst AUTOCOMPLETE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => AutoComplete),\n  multi: true\n};\n\nclass AutoComplete {\n  constructor(el, renderer, cd, differs, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.differs = differs;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.minLength = 1;\n    this.delay = 300;\n    this.scrollHeight = '200px';\n    this.lazy = false;\n    this.type = 'text';\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.dropdownIcon = \"pi pi-chevron-down\";\n    this.unique = true;\n    this.completeOnFocus = false;\n    this.showClear = false;\n    this.dropdownMode = 'blank';\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.autocomplete = 'off';\n    this.completeMethod = new EventEmitter();\n    this.onSelect = new EventEmitter();\n    this.onUnselect = new EventEmitter();\n    this.onFocus = new EventEmitter();\n    this.onBlur = new EventEmitter();\n    this.onDropdownClick = new EventEmitter();\n    this.onClear = new EventEmitter();\n    this.onKeyUp = new EventEmitter();\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.onLazyLoad = new EventEmitter();\n\n    this.onModelChange = () => {};\n\n    this.onModelTouched = () => {};\n\n    this.overlayVisible = false;\n    this.focus = false;\n    this.inputFieldValue = null;\n    this.inputValue = null;\n    this.differ = differs.find([]).create(null);\n    this.listId = UniqueComponentId() + '_list';\n  }\n\n  get itemSize() {\n    return this._itemSize;\n  }\n\n  set itemSize(val) {\n    this._itemSize = val;\n    console.warn(\"The itemSize property is deprecated, use virtualScrollItemSize property instead.\");\n  }\n\n  get suggestions() {\n    return this._suggestions;\n  }\n\n  set suggestions(val) {\n    this._suggestions = val;\n    this.handleSuggestionsChange();\n  }\n\n  ngAfterViewChecked() {\n    //Use timeouts as since Angular 4.2, AfterViewChecked is broken and not called after panel is updated\n    if (this.suggestionsUpdated && this.overlay && this.overlay.offsetParent) {\n      setTimeout(() => {\n        if (this.overlay) {\n          this.alignOverlay();\n        }\n      }, 1);\n      this.suggestionsUpdated = false;\n    }\n\n    if (this.highlightOptionChanged) {\n      setTimeout(() => {\n        if (this.overlay && this.itemsWrapper) {\n          let listItem = DomHandler.findSingle(this.overlay, 'li.p-highlight');\n\n          if (listItem) {\n            DomHandler.scrollInView(this.itemsWrapper, listItem);\n          }\n        }\n      }, 1);\n      this.highlightOptionChanged = false;\n    }\n  }\n\n  handleSuggestionsChange() {\n    if (this._suggestions != null && this.loading) {\n      this.highlightOption = null;\n\n      if (this._suggestions.length) {\n        this.noResults = false;\n        this.show();\n        this.suggestionsUpdated = true;\n\n        if (this.autoHighlight) {\n          this.highlightOption = this._suggestions[0];\n        }\n      } else {\n        this.noResults = true;\n\n        if (this.showEmptyMessage) {\n          this.show();\n          this.suggestionsUpdated = true;\n        } else {\n          this.hide();\n        }\n      }\n\n      this.loading = false;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n\n        case 'group':\n          this.groupTemplate = item.template;\n          break;\n\n        case 'selectedItem':\n          this.selectedItemTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'empty':\n          this.emptyTemplate = item.template;\n          break;\n\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  writeValue(value) {\n    this.value = value;\n    this.filled = this.value && this.value != '';\n    this.updateInputField();\n    this.cd.markForCheck();\n  }\n\n  getOptionGroupChildren(optionGroup) {\n    return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n  }\n\n  getOptionGroupLabel(optionGroup) {\n    return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : optionGroup.label != undefined ? optionGroup.label : optionGroup;\n  }\n\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n\n  onInput(event) {\n    // When an input element with a placeholder is clicked, the onInput event is invoked in IE.\n    if (!this.inputKeyDown && DomHandler.isIE()) {\n      return;\n    }\n\n    if (this.timeout) {\n      clearTimeout(this.timeout);\n    }\n\n    let value = event.target.value;\n    this.inputValue = value;\n\n    if (!this.multiple && !this.forceSelection) {\n      this.onModelChange(value);\n    }\n\n    if (value.length === 0 && !this.multiple) {\n      this.hide();\n      this.onClear.emit(event);\n      this.onModelChange(value);\n    }\n\n    if (value.length >= this.minLength) {\n      this.timeout = setTimeout(() => {\n        this.search(event, value);\n      }, this.delay);\n    } else {\n      this.hide();\n    }\n\n    this.updateFilledState();\n    this.inputKeyDown = false;\n  }\n\n  onInputClick(event) {\n    if (this.documentClickListener) {\n      this.inputClick = true;\n    }\n  }\n\n  search(event, query) {\n    //allow empty string but not undefined or null\n    if (query === undefined || query === null) {\n      return;\n    }\n\n    this.loading = true;\n    this.completeMethod.emit({\n      originalEvent: event,\n      query: query\n    });\n  }\n\n  selectItem(option, focus = true) {\n    if (this.forceSelectionUpdateModelTimeout) {\n      clearTimeout(this.forceSelectionUpdateModelTimeout);\n      this.forceSelectionUpdateModelTimeout = null;\n    }\n\n    if (this.multiple) {\n      this.multiInputEL.nativeElement.value = '';\n      this.value = this.value || [];\n\n      if (!this.isSelected(option) || !this.unique) {\n        this.value = [...this.value, option];\n        this.onModelChange(this.value);\n      }\n    } else {\n      this.inputEL.nativeElement.value = this.resolveFieldData(option);\n      this.value = option;\n      this.onModelChange(this.value);\n    }\n\n    this.onSelect.emit(option);\n    this.updateFilledState();\n\n    if (focus) {\n      this.itemClicked = true;\n      this.focusInput();\n    }\n  }\n\n  show() {\n    if (this.multiInputEL || this.inputEL) {\n      let hasFocus = this.multiple ? this.multiInputEL.nativeElement.ownerDocument.activeElement == this.multiInputEL.nativeElement : this.inputEL.nativeElement.ownerDocument.activeElement == this.inputEL.nativeElement;\n\n      if (!this.overlayVisible && hasFocus) {\n        this.overlayVisible = true;\n      }\n    }\n  }\n\n  clear() {\n    if (this.multiple) {\n      this.value = null;\n    } else {\n      this.inputValue = null;\n      this.inputEL.nativeElement.value = '';\n    }\n\n    this.updateFilledState();\n    this.onModelChange(this.value);\n    this.onClear.emit();\n  }\n\n  onOverlayAnimationStart(event) {\n    var _a;\n\n    switch (event.toState) {\n      case 'visible':\n        this.overlay = event.element;\n        this.itemsWrapper = this.virtualScroll ? DomHandler.findSingle(this.overlay, '.p-scroller') : this.overlay;\n        this.virtualScroll && ((_a = this.scroller) === null || _a === void 0 ? void 0 : _a.setContentEl(this.itemsViewChild.nativeElement));\n        this.appendOverlay();\n\n        if (this.autoZIndex) {\n          ZIndexUtils.set('overlay', this.overlay, this.baseZIndex + this.config.zIndex.overlay);\n        }\n\n        this.alignOverlay();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n        this.onShow.emit(event);\n        break;\n\n      case 'void':\n        this.onOverlayHide();\n        break;\n    }\n  }\n\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(event.element);\n        }\n\n        break;\n    }\n  }\n\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n  }\n\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.overlay);else DomHandler.appendChild(this.overlay, this.appendTo);\n\n      if (!this.overlay.style.minWidth) {\n        this.overlay.style.minWidth = DomHandler.getWidth(this.el.nativeElement.children[0]) + 'px';\n      }\n    }\n  }\n\n  resolveFieldData(value) {\n    let data = this.field ? ObjectUtils.resolveFieldData(value, this.field) : value;\n    return data !== (null || undefined) ? data : '';\n  }\n\n  restoreOverlayAppend() {\n    if (this.overlay && this.appendTo) {\n      this.el.nativeElement.appendChild(this.overlay);\n    }\n  }\n\n  alignOverlay() {\n    if (this.appendTo) DomHandler.absolutePosition(this.overlay, this.multiple ? this.multiContainerEL.nativeElement : this.inputEL.nativeElement);else DomHandler.relativePosition(this.overlay, this.multiple ? this.multiContainerEL.nativeElement : this.inputEL.nativeElement);\n  }\n\n  hide() {\n    this.overlayVisible = false;\n    this.cd.markForCheck();\n  }\n\n  handleDropdownClick(event) {\n    if (!this.overlayVisible) {\n      this.focusInput();\n      let queryValue = this.multiple ? this.multiInputEL.nativeElement.value : this.inputEL.nativeElement.value;\n      if (this.dropdownMode === 'blank') this.search(event, '');else if (this.dropdownMode === 'current') this.search(event, queryValue);\n      this.onDropdownClick.emit({\n        originalEvent: event,\n        query: queryValue\n      });\n    } else {\n      this.hide();\n    }\n  }\n\n  focusInput() {\n    if (this.multiple) this.multiInputEL.nativeElement.focus();else this.inputEL.nativeElement.focus();\n  }\n\n  get emptyMessageLabel() {\n    return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n  }\n\n  removeItem(item) {\n    let itemIndex = DomHandler.index(item);\n    let removedValue = this.value[itemIndex];\n    this.value = this.value.filter((val, i) => i != itemIndex);\n    this.onModelChange(this.value);\n    this.updateFilledState();\n    this.onUnselect.emit(removedValue);\n  }\n\n  onKeydown(event) {\n    if (this.overlayVisible) {\n      switch (event.which) {\n        //down\n        case 40:\n          if (this.group) {\n            let highlightItemIndex = this.findOptionGroupIndex(this.highlightOption, this.suggestions);\n\n            if (highlightItemIndex !== -1) {\n              let nextItemIndex = highlightItemIndex.itemIndex + 1;\n\n              if (nextItemIndex < this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex]).length) {\n                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex])[nextItemIndex];\n                this.highlightOptionChanged = true;\n              } else if (this.suggestions[highlightItemIndex.groupIndex + 1]) {\n                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex + 1])[0];\n                this.highlightOptionChanged = true;\n              }\n            } else {\n              this.highlightOption = this.getOptionGroupChildren(this.suggestions[0])[0];\n            }\n          } else {\n            let highlightItemIndex = this.findOptionIndex(this.highlightOption, this.suggestions);\n\n            if (highlightItemIndex != -1) {\n              var nextItemIndex = highlightItemIndex + 1;\n\n              if (nextItemIndex != this.suggestions.length) {\n                this.highlightOption = this.suggestions[nextItemIndex];\n                this.highlightOptionChanged = true;\n              }\n            } else {\n              this.highlightOption = this.suggestions[0];\n            }\n          }\n\n          event.preventDefault();\n          break;\n        //up\n\n        case 38:\n          if (this.group) {\n            let highlightItemIndex = this.findOptionGroupIndex(this.highlightOption, this.suggestions);\n\n            if (highlightItemIndex !== -1) {\n              let prevItemIndex = highlightItemIndex.itemIndex - 1;\n\n              if (prevItemIndex >= 0) {\n                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex])[prevItemIndex];\n                this.highlightOptionChanged = true;\n              } else if (prevItemIndex < 0) {\n                let prevGroup = this.suggestions[highlightItemIndex.groupIndex - 1];\n\n                if (prevGroup) {\n                  this.highlightOption = this.getOptionGroupChildren(prevGroup)[this.getOptionGroupChildren(prevGroup).length - 1];\n                  this.highlightOptionChanged = true;\n                }\n              }\n            }\n          } else {\n            let highlightItemIndex = this.findOptionIndex(this.highlightOption, this.suggestions);\n\n            if (highlightItemIndex > 0) {\n              let prevItemIndex = highlightItemIndex - 1;\n              this.highlightOption = this.suggestions[prevItemIndex];\n              this.highlightOptionChanged = true;\n            }\n          }\n\n          event.preventDefault();\n          break;\n        //enter\n\n        case 13:\n          if (this.highlightOption) {\n            this.selectItem(this.highlightOption);\n            this.hide();\n          }\n\n          event.preventDefault();\n          break;\n        //escape\n\n        case 27:\n          this.hide();\n          event.preventDefault();\n          break;\n        //tab\n\n        case 9:\n          if (this.highlightOption) {\n            this.selectItem(this.highlightOption);\n          }\n\n          this.hide();\n          break;\n      }\n    } else {\n      if (event.which === 40 && this.suggestions) {\n        this.search(event, event.target.value);\n      } else if (event.ctrlKey && event.key === 'z' && !this.multiple) {\n        this.inputEL.nativeElement.value = this.resolveFieldData(null);\n        this.value = '';\n        this.onModelChange(this.value);\n      } else if (event.ctrlKey && event.key === 'z' && this.multiple) {\n        this.value.pop();\n        this.onModelChange(this.value);\n        this.updateFilledState();\n      }\n    }\n\n    if (this.multiple) {\n      switch (event.which) {\n        //backspace\n        case 8:\n          if (this.value && this.value.length && !this.multiInputEL.nativeElement.value) {\n            this.value = [...this.value];\n            const removedValue = this.value.pop();\n            this.onModelChange(this.value);\n            this.updateFilledState();\n            this.onUnselect.emit(removedValue);\n          }\n\n          break;\n      }\n    }\n\n    this.inputKeyDown = true;\n  }\n\n  onKeyup(event) {\n    this.onKeyUp.emit(event);\n  }\n\n  onInputFocus(event) {\n    if (!this.itemClicked && this.completeOnFocus) {\n      let queryValue = this.multiple ? this.multiInputEL.nativeElement.value : this.inputEL.nativeElement.value;\n      this.search(event, queryValue);\n    }\n\n    this.focus = true;\n    this.onFocus.emit(event);\n    this.itemClicked = false;\n  }\n\n  onInputBlur(event) {\n    this.focus = false;\n    this.onModelTouched();\n    this.onBlur.emit(event);\n  }\n\n  onInputChange(event) {\n    if (this.forceSelection) {\n      let valid = false;\n      let inputValue = event.target.value.trim();\n\n      if (this.suggestions) {\n        for (let suggestion of this.suggestions) {\n          let itemValue = this.field ? ObjectUtils.resolveFieldData(suggestion, this.field) : suggestion;\n\n          if (itemValue && inputValue === itemValue.trim()) {\n            valid = true;\n            this.forceSelectionUpdateModelTimeout = setTimeout(() => {\n              this.selectItem(suggestion, false);\n            }, 250);\n            break;\n          }\n        }\n      }\n\n      if (!valid) {\n        if (this.multiple) {\n          this.multiInputEL.nativeElement.value = '';\n        } else {\n          this.value = null;\n          this.inputEL.nativeElement.value = '';\n        }\n\n        this.onClear.emit(event);\n        this.onModelChange(this.value);\n        this.updateFilledState();\n      }\n    }\n  }\n\n  onInputPaste(event) {\n    this.onKeydown(event);\n  }\n\n  isSelected(val) {\n    let selected = false;\n\n    if (this.value && this.value.length) {\n      for (let i = 0; i < this.value.length; i++) {\n        if (ObjectUtils.equals(this.value[i], val, this.dataKey)) {\n          selected = true;\n          break;\n        }\n      }\n    }\n\n    return selected;\n  }\n\n  findOptionIndex(option, suggestions) {\n    let index = -1;\n\n    if (suggestions) {\n      for (let i = 0; i < suggestions.length; i++) {\n        if (ObjectUtils.equals(option, suggestions[i])) {\n          index = i;\n          break;\n        }\n      }\n    }\n\n    return index;\n  }\n\n  findOptionGroupIndex(val, opts) {\n    let groupIndex, itemIndex;\n\n    if (opts) {\n      for (let i = 0; i < opts.length; i++) {\n        groupIndex = i;\n        itemIndex = this.findOptionIndex(val, this.getOptionGroupChildren(opts[i]));\n\n        if (itemIndex !== -1) {\n          break;\n        }\n      }\n    }\n\n    if (itemIndex !== -1) {\n      return {\n        groupIndex: groupIndex,\n        itemIndex: itemIndex\n      };\n    } else {\n      return -1;\n    }\n  }\n\n  updateFilledState() {\n    if (this.multiple) this.filled = this.value && this.value.length || this.multiInputEL && this.multiInputEL.nativeElement && this.multiInputEL.nativeElement.value != '';else this.filled = this.inputFieldValue && this.inputFieldValue != '' || this.inputEL && this.inputEL.nativeElement && this.inputEL.nativeElement.value != '';\n  }\n\n  updateInputField() {\n    let formattedValue = this.resolveFieldData(this.value);\n    this.inputFieldValue = formattedValue;\n\n    if (this.inputEL && this.inputEL.nativeElement) {\n      this.inputEL.nativeElement.value = formattedValue;\n    }\n\n    this.updateFilledState();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n        if (event.which === 3) {\n          return;\n        }\n\n        if (!this.inputClick && !this.isDropdownClick(event)) {\n          this.hide();\n        }\n\n        this.inputClick = false;\n        this.cd.markForCheck();\n      });\n    }\n  }\n\n  isDropdownClick(event) {\n    if (this.dropdown) {\n      let target = event.target;\n      return target === this.dropdownButton.nativeElement || target.parentNode === this.dropdownButton.nativeElement;\n    } else {\n      return false;\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  onWindowResize() {\n    if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerEL.nativeElement, () => {\n        if (this.overlayVisible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  onOverlayHide() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n    this.overlay = null;\n    this.onHide.emit();\n  }\n\n  ngOnDestroy() {\n    if (this.forceSelectionUpdateModelTimeout) {\n      clearTimeout(this.forceSelectionUpdateModelTimeout);\n      this.forceSelectionUpdateModelTimeout = null;\n    }\n\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n\n    if (this.overlay) {\n      ZIndexUtils.clear(this.overlay);\n    }\n\n    this.restoreOverlayAppend();\n    this.onOverlayHide();\n  }\n\n}\n\nAutoComplete.ɵfac = function AutoComplete_Factory(t) {\n  return new (t || AutoComplete)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n};\n\nAutoComplete.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: AutoComplete,\n  selectors: [[\"p-autoComplete\"]],\n  contentQueries: function AutoComplete_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function AutoComplete_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n      i0.ɵɵviewQuery(_c3, 5);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n      i0.ɵɵviewQuery(_c6, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerEL = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputEL = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiInputEL = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.multiContainerEL = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dropdownButton = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scroller = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n  hostVars: 6,\n  hostBindings: function AutoComplete_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focus && !ctx.disabled || ctx.overlayVisible)(\"p-autocomplete-clearable\", ctx.showClear && !ctx.disabled);\n    }\n  },\n  inputs: {\n    minLength: \"minLength\",\n    delay: \"delay\",\n    style: \"style\",\n    panelStyle: \"panelStyle\",\n    styleClass: \"styleClass\",\n    panelStyleClass: \"panelStyleClass\",\n    inputStyle: \"inputStyle\",\n    inputId: \"inputId\",\n    inputStyleClass: \"inputStyleClass\",\n    placeholder: \"placeholder\",\n    readonly: \"readonly\",\n    disabled: \"disabled\",\n    scrollHeight: \"scrollHeight\",\n    lazy: \"lazy\",\n    virtualScroll: \"virtualScroll\",\n    virtualScrollItemSize: \"virtualScrollItemSize\",\n    virtualScrollOptions: \"virtualScrollOptions\",\n    maxlength: \"maxlength\",\n    name: \"name\",\n    required: \"required\",\n    size: \"size\",\n    appendTo: \"appendTo\",\n    autoHighlight: \"autoHighlight\",\n    forceSelection: \"forceSelection\",\n    type: \"type\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    ariaLabel: \"ariaLabel\",\n    dropdownAriaLabel: \"dropdownAriaLabel\",\n    ariaLabelledBy: \"ariaLabelledBy\",\n    dropdownIcon: \"dropdownIcon\",\n    unique: \"unique\",\n    group: \"group\",\n    completeOnFocus: \"completeOnFocus\",\n    showClear: \"showClear\",\n    field: \"field\",\n    dropdown: \"dropdown\",\n    showEmptyMessage: \"showEmptyMessage\",\n    dropdownMode: \"dropdownMode\",\n    multiple: \"multiple\",\n    tabindex: \"tabindex\",\n    dataKey: \"dataKey\",\n    emptyMessage: \"emptyMessage\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\",\n    autofocus: \"autofocus\",\n    autocomplete: \"autocomplete\",\n    optionGroupChildren: \"optionGroupChildren\",\n    optionGroupLabel: \"optionGroupLabel\",\n    itemSize: \"itemSize\",\n    suggestions: \"suggestions\"\n  },\n  outputs: {\n    completeMethod: \"completeMethod\",\n    onSelect: \"onSelect\",\n    onUnselect: \"onUnselect\",\n    onFocus: \"onFocus\",\n    onBlur: \"onBlur\",\n    onDropdownClick: \"onDropdownClick\",\n    onClear: \"onClear\",\n    onKeyUp: \"onKeyUp\",\n    onShow: \"onShow\",\n    onHide: \"onHide\",\n    onLazyLoad: \"onLazyLoad\"\n  },\n  features: [i0.ɵɵProvidersFeature([AUTOCOMPLETE_VALUE_ACCESSOR])],\n  decls: 9,\n  vars: 14,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [\"class\", \"p-autocomplete-input p-inputtext p-component\", \"aria-autocomplete\", \"list\", \"role\", \"searchbox\", 3, \"ngStyle\", \"class\", \"autocomplete\", \"ngClass\", \"value\", \"readonly\", \"disabled\", \"click\", \"input\", \"keydown\", \"keyup\", \"focus\", \"blur\", \"change\", \"paste\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-clear-icon pi pi-times\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-multiple-container p-component p-inputtext\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-loader pi pi-spinner pi-spin\", 4, \"ngIf\"], [\"type\", \"button\", \"pButton\", \"\", \"class\", \"p-autocomplete-dropdown\", \"pRipple\", \"\", 3, \"icon\", \"disabled\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"max-height\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [\"aria-autocomplete\", \"list\", \"role\", \"searchbox\", 1, \"p-autocomplete-input\", \"p-inputtext\", \"p-component\", 3, \"ngStyle\", \"autocomplete\", \"ngClass\", \"value\", \"readonly\", \"disabled\", \"click\", \"input\", \"keydown\", \"keyup\", \"focus\", \"blur\", \"change\", \"paste\"], [\"in\", \"\"], [1, \"p-autocomplete-clear-icon\", \"pi\", \"pi-times\", 3, \"click\"], [1, \"p-autocomplete-multiple-container\", \"p-component\", \"p-inputtext\", 3, \"ngClass\", \"click\"], [\"multiContainer\", \"\"], [\"class\", \"p-autocomplete-token\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-autocomplete-input-token\"], [\"aria-autocomplete\", \"list\", \"role\", \"searchbox\", \"aria-haspopup\", \"true\", 3, \"disabled\", \"readonly\", \"autocomplete\", \"ngStyle\", \"input\", \"click\", \"keydown\", \"keyup\", \"focus\", \"blur\", \"change\", \"paste\"], [\"multiIn\", \"\"], [1, \"p-autocomplete-token\"], [\"token\", \"\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-autocomplete-token-label\", 4, \"ngIf\"], [\"class\", \"p-autocomplete-token-icon pi pi-times-circle\", 3, \"click\", 4, \"ngIf\"], [1, \"p-autocomplete-token-label\"], [1, \"p-autocomplete-token-icon\", \"pi\", \"pi-times-circle\", 3, \"click\"], [1, \"p-autocomplete-loader\", \"pi\", \"pi-spinner\", \"pi-spin\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 1, \"p-autocomplete-dropdown\", 3, \"icon\", \"disabled\", \"click\"], [\"ddBtn\", \"\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [\"panel\", \"\"], [4, \"ngTemplateOutlet\"], [3, \"items\", \"style\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\", 4, \"ngIf\"], [4, \"ngIf\"], [\"buildInItems\", \"\"], [3, \"items\", \"itemSize\", \"autoSize\", \"lazy\", \"options\", \"onLazyLoad\"], [\"scroller\", \"\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"loader\"], [\"role\", \"listbox\", 1, \"p-autocomplete-items\", 3, \"ngClass\"], [\"items\", \"\"], [\"itemslist\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [1, \"p-autocomplete-item-group\", 3, \"ngStyle\"], [\"role\", \"option\", \"class\", \"p-autocomplete-item\", \"pRipple\", \"\", 3, \"ngStyle\", \"ngClass\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-autocomplete-empty-message\", 3, \"ngStyle\", 4, \"ngIf\"], [\"role\", \"option\", \"pRipple\", \"\", 1, \"p-autocomplete-item\", 3, \"ngStyle\", \"ngClass\", \"id\", \"click\"], [1, \"p-autocomplete-empty-message\", 3, \"ngStyle\"], [4, \"ngIf\", \"ngIfElse\"], [\"empty\", \"\"]],\n  template: function AutoComplete_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"span\", 0, 1);\n      i0.ɵɵtemplate(2, AutoComplete_input_2_Template, 2, 23, \"input\", 2);\n      i0.ɵɵtemplate(3, AutoComplete_i_3_Template, 1, 0, \"i\", 3);\n      i0.ɵɵtemplate(4, AutoComplete_i_4_Template, 1, 0, \"i\", 3);\n      i0.ɵɵtemplate(5, AutoComplete_ul_5_Template, 6, 23, \"ul\", 4);\n      i0.ɵɵtemplate(6, AutoComplete_i_6_Template, 1, 0, \"i\", 5);\n      i0.ɵɵtemplate(7, AutoComplete_button_7_Template, 2, 4, \"button\", 6);\n      i0.ɵɵtemplate(8, AutoComplete_div_8_Template, 8, 17, \"div\", 7);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c19, ctx.dropdown, ctx.multiple))(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.multiple);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.multiple && ctx.filled && !ctx.disabled && ctx.showClear);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.multiple && ctx.filled && !ctx.disabled && ctx.showClear);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.multiple);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.dropdown);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.overlayVisible);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.ButtonDirective, i1.PrimeTemplate, i4.Ripple, i5.Scroller],\n  styles: [\".p-autocomplete{display:inline-flex;position:relative}.p-autocomplete-loader{position:absolute;top:50%;margin-top:-.5rem}.p-autocomplete-dd .p-autocomplete-input{flex:1 1 auto;width:1%}.p-autocomplete-dd .p-autocomplete-input,.p-autocomplete-dd .p-autocomplete-multiple-container{border-top-right-radius:0;border-bottom-right-radius:0}.p-autocomplete-dd .p-autocomplete-dropdown{border-top-left-radius:0;border-bottom-left-radius:0}.p-autocomplete .p-autocomplete-panel{min-width:100%;top:0;left:0}.p-autocomplete-panel{position:absolute;overflow:auto}.p-autocomplete-items{margin:0;padding:0;list-style-type:none}.p-autocomplete-item{cursor:pointer;white-space:nowrap;position:relative;overflow:hidden}.p-autocomplete-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-autocomplete-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-autocomplete-token-icon{cursor:pointer}.p-autocomplete-input-token{flex:1 1 auto;display:inline-flex}.p-autocomplete-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-autocomplete{display:flex}.p-fluid .p-autocomplete-dd .p-autocomplete-input{width:1%}.p-autocomplete-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-autocomplete-clearable{position:relative}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('overlayAnimation', [transition(':enter', [style({\n      opacity: 0,\n      transform: 'scaleY(0.8)'\n    }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoComplete, [{\n    type: Component,\n    args: [{\n      selector: 'p-autoComplete',\n      template: `\n        <span #container [ngClass]=\"{'p-autocomplete p-component':true,'p-autocomplete-dd':dropdown,'p-autocomplete-multiple':multiple}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input *ngIf=\"!multiple\" #in [attr.type]=\"type\" [attr.id]=\"inputId\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [autocomplete]=\"autocomplete\" [attr.required]=\"required\" [attr.name]=\"name\"\n            class=\"p-autocomplete-input p-inputtext p-component\" [ngClass]=\"{'p-autocomplete-dd-input':dropdown,'p-disabled': disabled}\" [value]=\"inputFieldValue\" aria-autocomplete=\"list\" role=\"searchbox\"\n            (click)=\"onInputClick($event)\" (input)=\"onInput($event)\" (keydown)=\"onKeydown($event)\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\"\n            [attr.placeholder]=\"placeholder\" [attr.size]=\"size\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [readonly]=\"readonly\" [disabled]=\"disabled\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\">\n            <i *ngIf=\"!multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <i *ngIf=\"multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <ul *ngIf=\"multiple\" #multiContainer class=\"p-autocomplete-multiple-container p-component p-inputtext\" [ngClass]=\"{'p-disabled':disabled,'p-focus':focus}\" (click)=\"multiIn.focus()\">\n                <li #token *ngFor=\"let val of value\" class=\"p-autocomplete-token\">\n                    <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: val}\"></ng-container>\n                    <span *ngIf=\"!selectedItemTemplate\" class=\"p-autocomplete-token-label\">{{resolveFieldData(val)}}</span>\n                    <span  class=\"p-autocomplete-token-icon pi pi-times-circle\" (click)=\"removeItem(token)\" *ngIf=\"!disabled && !readonly\"></span>\n                </li>\n                <li class=\"p-autocomplete-input-token\">\n                    <input #multiIn [attr.type]=\"type\" [attr.id]=\"inputId\" [disabled]=\"disabled\" [attr.placeholder]=\"(value&&value.length ? null : placeholder)\" [attr.tabindex]=\"tabindex\" [attr.maxlength]=\"maxlength\" (input)=\"onInput($event)\"  (click)=\"onInputClick($event)\"\n                            (keydown)=\"onKeydown($event)\" [readonly]=\"readonly\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\" [autocomplete]=\"autocomplete\"\n                            [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\"\n                            aria-autocomplete=\"list\" [attr.aria-controls]=\"listId\" role=\"searchbox\" [attr.aria-expanded]=\"overlayVisible\" aria-haspopup=\"true\" [attr.aria-activedescendant]=\"'p-highlighted-option'\">\n                </li>\n            </ul>\n            <i *ngIf=\"loading\" class=\"p-autocomplete-loader pi pi-spinner pi-spin\"></i><button #ddBtn type=\"button\" pButton [icon]=\"dropdownIcon\" [attr.aria-label]=\"dropdownAriaLabel\" class=\"p-autocomplete-dropdown\" [disabled]=\"disabled\" pRipple\n                (click)=\"handleDropdownClick($event)\" *ngIf=\"dropdown\" [attr.tabindex]=\"tabindex\"></button>\n            <div #panel *ngIf=\"overlayVisible\" (click)=\"onOverlayClick($event)\" [ngClass]=\"['p-autocomplete-panel p-component']\" [style.max-height]=\"virtualScroll ? 'auto' : scrollHeight\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"suggestions\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                    [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                    <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate\">\n                        <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: suggestions, options: {}}\"></ng-container>\n                </ng-container>\n\n                <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                    <ul #items role=\"listbox\" [attr.id]=\"listId\" class=\"p-autocomplete-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\">\n                        <ng-container *ngIf=\"group\">\n                            <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                <li class=\"p-autocomplete-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                    <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                    <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                </li>\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup)}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                        <ng-container *ngIf=\"!group\">\n                            <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items}\"></ng-container>\n                        </ng-container>\n                        <ng-template #itemslist let-suggestionsToDisplay>\n                            <li role=\"option\" *ngFor=\"let option of suggestionsToDisplay; let idx = index\" class=\"p-autocomplete-item\" pRipple [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\" [ngClass]=\"{'p-highlight': (option === highlightOption)}\" [id]=\"highlightOption == option ? 'p-highlighted-option':''\" (click)=\"selectItem(option)\">\n                                <span *ngIf=\"!itemTemplate\">{{resolveFieldData(option)}}</span>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: option, index: scrollerOptions.getOptions ? scrollerOptions.getOptions(idx) : idx}\"></ng-container>\n                            </li>\n                            <li *ngIf=\"noResults && showEmptyMessage\" class=\"p-autocomplete-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ng-template>\n                    </ul>\n                </ng-template>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </span>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      host: {\n        'class': 'p-element p-inputwrapper',\n        '[class.p-inputwrapper-filled]': 'filled',\n        '[class.p-inputwrapper-focus]': '(focus && !disabled) || overlayVisible',\n        '[class.p-autocomplete-clearable]': 'showClear && !disabled'\n      },\n      providers: [AUTOCOMPLETE_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      styles: [\".p-autocomplete{display:inline-flex;position:relative}.p-autocomplete-loader{position:absolute;top:50%;margin-top:-.5rem}.p-autocomplete-dd .p-autocomplete-input{flex:1 1 auto;width:1%}.p-autocomplete-dd .p-autocomplete-input,.p-autocomplete-dd .p-autocomplete-multiple-container{border-top-right-radius:0;border-bottom-right-radius:0}.p-autocomplete-dd .p-autocomplete-dropdown{border-top-left-radius:0;border-bottom-left-radius:0}.p-autocomplete .p-autocomplete-panel{min-width:100%;top:0;left:0}.p-autocomplete-panel{position:absolute;overflow:auto}.p-autocomplete-items{margin:0;padding:0;list-style-type:none}.p-autocomplete-item{cursor:pointer;white-space:nowrap;position:relative;overflow:hidden}.p-autocomplete-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-autocomplete-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-autocomplete-token-icon{cursor:pointer}.p-autocomplete-input-token{flex:1 1 auto;display:inline-flex}.p-autocomplete-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-autocomplete{display:flex}.p-fluid .p-autocomplete-dd .p-autocomplete-input{width:1%}.p-autocomplete-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-autocomplete-clearable{position:relative}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.IterableDiffers\n    }, {\n      type: i1.PrimeNGConfig\n    }, {\n      type: i1.OverlayService\n    }];\n  }, {\n    minLength: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    panelStyle: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    panelStyleClass: [{\n      type: Input\n    }],\n    inputStyle: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    inputStyleClass: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    virtualScroll: [{\n      type: Input\n    }],\n    virtualScrollItemSize: [{\n      type: Input\n    }],\n    virtualScrollOptions: [{\n      type: Input\n    }],\n    maxlength: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoHighlight: [{\n      type: Input\n    }],\n    forceSelection: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    dropdownAriaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    dropdownIcon: [{\n      type: Input\n    }],\n    unique: [{\n      type: Input\n    }],\n    group: [{\n      type: Input\n    }],\n    completeOnFocus: [{\n      type: Input\n    }],\n    showClear: [{\n      type: Input\n    }],\n    field: [{\n      type: Input\n    }],\n    dropdown: [{\n      type: Input\n    }],\n    showEmptyMessage: [{\n      type: Input\n    }],\n    dropdownMode: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    dataKey: [{\n      type: Input\n    }],\n    emptyMessage: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input\n    }],\n    autocomplete: [{\n      type: Input\n    }],\n    optionGroupChildren: [{\n      type: Input\n    }],\n    optionGroupLabel: [{\n      type: Input\n    }],\n    containerEL: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    inputEL: [{\n      type: ViewChild,\n      args: ['in']\n    }],\n    multiInputEL: [{\n      type: ViewChild,\n      args: ['multiIn']\n    }],\n    multiContainerEL: [{\n      type: ViewChild,\n      args: ['multiContainer']\n    }],\n    dropdownButton: [{\n      type: ViewChild,\n      args: ['ddBtn']\n    }],\n    itemsViewChild: [{\n      type: ViewChild,\n      args: ['items']\n    }],\n    scroller: [{\n      type: ViewChild,\n      args: ['scroller']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    completeMethod: [{\n      type: Output\n    }],\n    onSelect: [{\n      type: Output\n    }],\n    onUnselect: [{\n      type: Output\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    onDropdownClick: [{\n      type: Output\n    }],\n    onClear: [{\n      type: Output\n    }],\n    onKeyUp: [{\n      type: Output\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    suggestions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass AutoCompleteModule {}\n\nAutoCompleteModule.ɵfac = function AutoCompleteModule_Factory(t) {\n  return new (t || AutoCompleteModule)();\n};\n\nAutoCompleteModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AutoCompleteModule\n});\nAutoCompleteModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutoCompleteModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule],\n      exports: [AutoComplete, SharedModule, ScrollerModule],\n      declarations: [AutoComplete]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AUTOCOMPLETE_VALUE_ACCESSOR, AutoComplete, AutoCompleteModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "ContentChildren", "Output", "NgModule", "i2", "CommonModule", "trigger", "transition", "style", "animate", "InputTextModule", "i3", "ButtonModule", "i4", "RippleModule", "i1", "Translation<PERSON>eys", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "UniqueComponentId", "ObjectUtils", "ZIndexUtils", "NG_VALUE_ACCESSOR", "i5", "ScrollerModule", "AUTOCOMPLETE_VALUE_ACCESSOR", "provide", "useExisting", "AutoComplete", "multi", "constructor", "el", "renderer", "cd", "differs", "config", "overlayService", "<PERSON><PERSON><PERSON><PERSON>", "delay", "scrollHeight", "lazy", "type", "autoZIndex", "baseZIndex", "dropdownIcon", "unique", "completeOnFocus", "showClear", "dropdownMode", "showTransitionOptions", "hideTransitionOptions", "autocomplete", "completeMethod", "onSelect", "onUnselect", "onFocus", "onBlur", "onDropdownClick", "onClear", "onKeyUp", "onShow", "onHide", "onLazyLoad", "onModelChange", "onModelTouched", "overlayVisible", "focus", "inputFieldValue", "inputValue", "differ", "find", "create", "listId", "itemSize", "_itemSize", "val", "console", "warn", "suggestions", "_suggestions", "handleSuggestionsChange", "ngAfterViewChecked", "suggestionsUpdated", "overlay", "offsetParent", "setTimeout", "alignOverlay", "highlightOptionChanged", "itemsWrapper", "listItem", "findSingle", "scrollInView", "loading", "highlightOption", "length", "noResults", "show", "autoHighlight", "showEmptyMessage", "hide", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "itemTemplate", "template", "groupTemplate", "selectedItemTemplate", "headerTemplate", "emptyTemplate", "footerTemplate", "loaderTemplate", "writeValue", "value", "filled", "updateInputField", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getOptionGroupChildren", "optionGroup", "optionGroupChildren", "resolveFieldData", "items", "getOptionGroupLabel", "optionGroupLabel", "label", "undefined", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "disabled", "onInput", "event", "inputKeyDown", "isIE", "timeout", "clearTimeout", "target", "multiple", "forceSelection", "emit", "search", "updateFilledState", "onInputClick", "documentClickListener", "inputClick", "query", "originalEvent", "selectItem", "option", "forceSelectionUpdateModelTimeout", "multiInputEL", "nativeElement", "isSelected", "inputEL", "itemClicked", "focusInput", "hasFocus", "ownerDocument", "activeElement", "clear", "onOverlayAnimationStart", "_a", "toState", "element", "virtualScroll", "scroller", "setContentEl", "itemsViewChild", "appendOverlay", "set", "zIndex", "bindDocumentClickListener", "bindDocumentResizeListener", "bindScrollListener", "onOverlayHide", "onOverlayAnimationEnd", "onOverlayClick", "add", "appendTo", "document", "body", "append<PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "getWidth", "children", "data", "field", "restoreOverlayAppend", "absolutePosition", "multiContainerEL", "relativePosition", "handleDropdownClick", "queryValue", "emptyMessageLabel", "emptyMessage", "getTranslation", "EMPTY_MESSAGE", "removeItem", "itemIndex", "index", "removedValue", "filter", "i", "onKeydown", "which", "group", "highlightItemIndex", "findOptionGroupIndex", "nextItemIndex", "groupIndex", "findOptionIndex", "preventDefault", "prevItemIndex", "prevGroup", "ctrl<PERSON>ey", "key", "pop", "onKeyup", "onInputFocus", "onInputBlur", "onInputChange", "valid", "trim", "suggestion", "itemValue", "onInputPaste", "selected", "equals", "dataKey", "opts", "formattedValue", "documentTarget", "listen", "isDropdownClick", "dropdown", "dropdownButton", "parentNode", "unbindDocumentClickListener", "documentResizeListener", "onWindowResize", "bind", "window", "addEventListener", "unbindDocumentResizeListener", "removeEventListener", "isTouchDevice", "<PERSON><PERSON><PERSON><PERSON>", "containerEL", "unbindScrollListener", "ngOnDestroy", "destroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "Iterable<PERSON><PERSON><PERSON>", "PrimeNGConfig", "OverlayService", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "ButtonDirective", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "opacity", "transform", "args", "selector", "animations", "host", "providers", "changeDetection", "OnPush", "encapsulation", "None", "styles", "panelStyle", "styleClass", "panelStyleClass", "inputStyle", "inputId", "inputStyleClass", "placeholder", "readonly", "virtualScrollItemSize", "virtualScrollOptions", "maxlength", "name", "required", "size", "aria<PERSON><PERSON><PERSON>", "dropdownAriaLabel", "ariaLabelledBy", "tabindex", "autofocus", "AutoCompleteModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-autocomplete.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { InputTextModule } from 'primeng/inputtext';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i5 from 'primeng/scroller';\nimport { ScrollerModule } from 'primeng/scroller';\n\nconst AUTOCOMPLETE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => AutoComplete),\n    multi: true\n};\nclass AutoComplete {\n    constructor(el, renderer, cd, differs, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.differs = differs;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.minLength = 1;\n        this.delay = 300;\n        this.scrollHeight = '200px';\n        this.lazy = false;\n        this.type = 'text';\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.dropdownIcon = \"pi pi-chevron-down\";\n        this.unique = true;\n        this.completeOnFocus = false;\n        this.showClear = false;\n        this.dropdownMode = 'blank';\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.autocomplete = 'off';\n        this.completeMethod = new EventEmitter();\n        this.onSelect = new EventEmitter();\n        this.onUnselect = new EventEmitter();\n        this.onFocus = new EventEmitter();\n        this.onBlur = new EventEmitter();\n        this.onDropdownClick = new EventEmitter();\n        this.onClear = new EventEmitter();\n        this.onKeyUp = new EventEmitter();\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.onLazyLoad = new EventEmitter();\n        this.onModelChange = () => { };\n        this.onModelTouched = () => { };\n        this.overlayVisible = false;\n        this.focus = false;\n        this.inputFieldValue = null;\n        this.inputValue = null;\n        this.differ = differs.find([]).create(null);\n        this.listId = UniqueComponentId() + '_list';\n    }\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(val) {\n        this._itemSize = val;\n        console.warn(\"The itemSize property is deprecated, use virtualScrollItemSize property instead.\");\n    }\n    get suggestions() {\n        return this._suggestions;\n    }\n    set suggestions(val) {\n        this._suggestions = val;\n        this.handleSuggestionsChange();\n    }\n    ngAfterViewChecked() {\n        //Use timeouts as since Angular 4.2, AfterViewChecked is broken and not called after panel is updated\n        if (this.suggestionsUpdated && this.overlay && this.overlay.offsetParent) {\n            setTimeout(() => {\n                if (this.overlay) {\n                    this.alignOverlay();\n                }\n            }, 1);\n            this.suggestionsUpdated = false;\n        }\n        if (this.highlightOptionChanged) {\n            setTimeout(() => {\n                if (this.overlay && this.itemsWrapper) {\n                    let listItem = DomHandler.findSingle(this.overlay, 'li.p-highlight');\n                    if (listItem) {\n                        DomHandler.scrollInView(this.itemsWrapper, listItem);\n                    }\n                }\n            }, 1);\n            this.highlightOptionChanged = false;\n        }\n    }\n    handleSuggestionsChange() {\n        if (this._suggestions != null && this.loading) {\n            this.highlightOption = null;\n            if (this._suggestions.length) {\n                this.noResults = false;\n                this.show();\n                this.suggestionsUpdated = true;\n                if (this.autoHighlight) {\n                    this.highlightOption = this._suggestions[0];\n                }\n            }\n            else {\n                this.noResults = true;\n                if (this.showEmptyMessage) {\n                    this.show();\n                    this.suggestionsUpdated = true;\n                }\n                else {\n                    this.hide();\n                }\n            }\n            this.loading = false;\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'group':\n                    this.groupTemplate = item.template;\n                    break;\n                case 'selectedItem':\n                    this.selectedItemTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'empty':\n                    this.emptyTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    writeValue(value) {\n        this.value = value;\n        this.filled = this.value && this.value != '';\n        this.updateInputField();\n        this.cd.markForCheck();\n    }\n    getOptionGroupChildren(optionGroup) {\n        return this.optionGroupChildren ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupChildren) : optionGroup.items;\n    }\n    getOptionGroupLabel(optionGroup) {\n        return this.optionGroupLabel ? ObjectUtils.resolveFieldData(optionGroup, this.optionGroupLabel) : (optionGroup.label != undefined ? optionGroup.label : optionGroup);\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    onInput(event) {\n        // When an input element with a placeholder is clicked, the onInput event is invoked in IE.\n        if (!this.inputKeyDown && DomHandler.isIE()) {\n            return;\n        }\n        if (this.timeout) {\n            clearTimeout(this.timeout);\n        }\n        let value = event.target.value;\n        this.inputValue = value;\n        if (!this.multiple && !this.forceSelection) {\n            this.onModelChange(value);\n        }\n        if (value.length === 0 && !this.multiple) {\n            this.hide();\n            this.onClear.emit(event);\n            this.onModelChange(value);\n        }\n        if (value.length >= this.minLength) {\n            this.timeout = setTimeout(() => {\n                this.search(event, value);\n            }, this.delay);\n        }\n        else {\n            this.hide();\n        }\n        this.updateFilledState();\n        this.inputKeyDown = false;\n    }\n    onInputClick(event) {\n        if (this.documentClickListener) {\n            this.inputClick = true;\n        }\n    }\n    search(event, query) {\n        //allow empty string but not undefined or null\n        if (query === undefined || query === null) {\n            return;\n        }\n        this.loading = true;\n        this.completeMethod.emit({\n            originalEvent: event,\n            query: query\n        });\n    }\n    selectItem(option, focus = true) {\n        if (this.forceSelectionUpdateModelTimeout) {\n            clearTimeout(this.forceSelectionUpdateModelTimeout);\n            this.forceSelectionUpdateModelTimeout = null;\n        }\n        if (this.multiple) {\n            this.multiInputEL.nativeElement.value = '';\n            this.value = this.value || [];\n            if (!this.isSelected(option) || !this.unique) {\n                this.value = [...this.value, option];\n                this.onModelChange(this.value);\n            }\n        }\n        else {\n            this.inputEL.nativeElement.value = this.resolveFieldData(option);\n            this.value = option;\n            this.onModelChange(this.value);\n        }\n        this.onSelect.emit(option);\n        this.updateFilledState();\n        if (focus) {\n            this.itemClicked = true;\n            this.focusInput();\n        }\n    }\n    show() {\n        if (this.multiInputEL || this.inputEL) {\n            let hasFocus = this.multiple ?\n                this.multiInputEL.nativeElement.ownerDocument.activeElement == this.multiInputEL.nativeElement :\n                this.inputEL.nativeElement.ownerDocument.activeElement == this.inputEL.nativeElement;\n            if (!this.overlayVisible && hasFocus) {\n                this.overlayVisible = true;\n            }\n        }\n    }\n    clear() {\n        if (this.multiple) {\n            this.value = null;\n        }\n        else {\n            this.inputValue = null;\n            this.inputEL.nativeElement.value = '';\n        }\n        this.updateFilledState();\n        this.onModelChange(this.value);\n        this.onClear.emit();\n    }\n    onOverlayAnimationStart(event) {\n        var _a;\n        switch (event.toState) {\n            case 'visible':\n                this.overlay = event.element;\n                this.itemsWrapper = this.virtualScroll ? DomHandler.findSingle(this.overlay, '.p-scroller') : this.overlay;\n                this.virtualScroll && ((_a = this.scroller) === null || _a === void 0 ? void 0 : _a.setContentEl(this.itemsViewChild.nativeElement));\n                this.appendOverlay();\n                if (this.autoZIndex) {\n                    ZIndexUtils.set('overlay', this.overlay, this.baseZIndex + this.config.zIndex.overlay);\n                }\n                this.alignOverlay();\n                this.bindDocumentClickListener();\n                this.bindDocumentResizeListener();\n                this.bindScrollListener();\n                this.onShow.emit(event);\n                break;\n            case 'void':\n                this.onOverlayHide();\n                break;\n        }\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(event.element);\n                }\n                break;\n        }\n    }\n    onOverlayClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.overlay);\n            else\n                DomHandler.appendChild(this.overlay, this.appendTo);\n            if (!this.overlay.style.minWidth) {\n                this.overlay.style.minWidth = DomHandler.getWidth(this.el.nativeElement.children[0]) + 'px';\n            }\n        }\n    }\n    resolveFieldData(value) {\n        let data = this.field ? ObjectUtils.resolveFieldData(value, this.field) : value;\n        return data !== (null || undefined) ? data : '';\n    }\n    restoreOverlayAppend() {\n        if (this.overlay && this.appendTo) {\n            this.el.nativeElement.appendChild(this.overlay);\n        }\n    }\n    alignOverlay() {\n        if (this.appendTo)\n            DomHandler.absolutePosition(this.overlay, (this.multiple ? this.multiContainerEL.nativeElement : this.inputEL.nativeElement));\n        else\n            DomHandler.relativePosition(this.overlay, (this.multiple ? this.multiContainerEL.nativeElement : this.inputEL.nativeElement));\n    }\n    hide() {\n        this.overlayVisible = false;\n        this.cd.markForCheck();\n    }\n    handleDropdownClick(event) {\n        if (!this.overlayVisible) {\n            this.focusInput();\n            let queryValue = this.multiple ? this.multiInputEL.nativeElement.value : this.inputEL.nativeElement.value;\n            if (this.dropdownMode === 'blank')\n                this.search(event, '');\n            else if (this.dropdownMode === 'current')\n                this.search(event, queryValue);\n            this.onDropdownClick.emit({\n                originalEvent: event,\n                query: queryValue\n            });\n        }\n        else {\n            this.hide();\n        }\n    }\n    focusInput() {\n        if (this.multiple)\n            this.multiInputEL.nativeElement.focus();\n        else\n            this.inputEL.nativeElement.focus();\n    }\n    get emptyMessageLabel() {\n        return this.emptyMessage || this.config.getTranslation(TranslationKeys.EMPTY_MESSAGE);\n    }\n    removeItem(item) {\n        let itemIndex = DomHandler.index(item);\n        let removedValue = this.value[itemIndex];\n        this.value = this.value.filter((val, i) => i != itemIndex);\n        this.onModelChange(this.value);\n        this.updateFilledState();\n        this.onUnselect.emit(removedValue);\n    }\n    onKeydown(event) {\n        if (this.overlayVisible) {\n            switch (event.which) {\n                //down\n                case 40:\n                    if (this.group) {\n                        let highlightItemIndex = this.findOptionGroupIndex(this.highlightOption, this.suggestions);\n                        if (highlightItemIndex !== -1) {\n                            let nextItemIndex = highlightItemIndex.itemIndex + 1;\n                            if (nextItemIndex < (this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex]).length)) {\n                                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex])[nextItemIndex];\n                                this.highlightOptionChanged = true;\n                            }\n                            else if (this.suggestions[highlightItemIndex.groupIndex + 1]) {\n                                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex + 1])[0];\n                                this.highlightOptionChanged = true;\n                            }\n                        }\n                        else {\n                            this.highlightOption = this.getOptionGroupChildren(this.suggestions[0])[0];\n                        }\n                    }\n                    else {\n                        let highlightItemIndex = this.findOptionIndex(this.highlightOption, this.suggestions);\n                        if (highlightItemIndex != -1) {\n                            var nextItemIndex = highlightItemIndex + 1;\n                            if (nextItemIndex != (this.suggestions.length)) {\n                                this.highlightOption = this.suggestions[nextItemIndex];\n                                this.highlightOptionChanged = true;\n                            }\n                        }\n                        else {\n                            this.highlightOption = this.suggestions[0];\n                        }\n                    }\n                    event.preventDefault();\n                    break;\n                //up\n                case 38:\n                    if (this.group) {\n                        let highlightItemIndex = this.findOptionGroupIndex(this.highlightOption, this.suggestions);\n                        if (highlightItemIndex !== -1) {\n                            let prevItemIndex = highlightItemIndex.itemIndex - 1;\n                            if (prevItemIndex >= 0) {\n                                this.highlightOption = this.getOptionGroupChildren(this.suggestions[highlightItemIndex.groupIndex])[prevItemIndex];\n                                this.highlightOptionChanged = true;\n                            }\n                            else if (prevItemIndex < 0) {\n                                let prevGroup = this.suggestions[highlightItemIndex.groupIndex - 1];\n                                if (prevGroup) {\n                                    this.highlightOption = this.getOptionGroupChildren(prevGroup)[this.getOptionGroupChildren(prevGroup).length - 1];\n                                    this.highlightOptionChanged = true;\n                                }\n                            }\n                        }\n                    }\n                    else {\n                        let highlightItemIndex = this.findOptionIndex(this.highlightOption, this.suggestions);\n                        if (highlightItemIndex > 0) {\n                            let prevItemIndex = highlightItemIndex - 1;\n                            this.highlightOption = this.suggestions[prevItemIndex];\n                            this.highlightOptionChanged = true;\n                        }\n                    }\n                    event.preventDefault();\n                    break;\n                //enter\n                case 13:\n                    if (this.highlightOption) {\n                        this.selectItem(this.highlightOption);\n                        this.hide();\n                    }\n                    event.preventDefault();\n                    break;\n                //escape\n                case 27:\n                    this.hide();\n                    event.preventDefault();\n                    break;\n                //tab\n                case 9:\n                    if (this.highlightOption) {\n                        this.selectItem(this.highlightOption);\n                    }\n                    this.hide();\n                    break;\n            }\n        }\n        else {\n            if (event.which === 40 && this.suggestions) {\n                this.search(event, event.target.value);\n            }\n            else if ((event.ctrlKey && event.key === 'z') && !this.multiple) {\n                this.inputEL.nativeElement.value = this.resolveFieldData(null);\n                this.value = '';\n                this.onModelChange(this.value);\n            }\n            else if ((event.ctrlKey && event.key === 'z') && this.multiple) {\n                this.value.pop();\n                this.onModelChange(this.value);\n                this.updateFilledState();\n            }\n        }\n        if (this.multiple) {\n            switch (event.which) {\n                //backspace\n                case 8:\n                    if (this.value && this.value.length && !this.multiInputEL.nativeElement.value) {\n                        this.value = [...this.value];\n                        const removedValue = this.value.pop();\n                        this.onModelChange(this.value);\n                        this.updateFilledState();\n                        this.onUnselect.emit(removedValue);\n                    }\n                    break;\n            }\n        }\n        this.inputKeyDown = true;\n    }\n    onKeyup(event) {\n        this.onKeyUp.emit(event);\n    }\n    onInputFocus(event) {\n        if (!this.itemClicked && this.completeOnFocus) {\n            let queryValue = this.multiple ? this.multiInputEL.nativeElement.value : this.inputEL.nativeElement.value;\n            this.search(event, queryValue);\n        }\n        this.focus = true;\n        this.onFocus.emit(event);\n        this.itemClicked = false;\n    }\n    onInputBlur(event) {\n        this.focus = false;\n        this.onModelTouched();\n        this.onBlur.emit(event);\n    }\n    onInputChange(event) {\n        if (this.forceSelection) {\n            let valid = false;\n            let inputValue = event.target.value.trim();\n            if (this.suggestions) {\n                for (let suggestion of this.suggestions) {\n                    let itemValue = this.field ? ObjectUtils.resolveFieldData(suggestion, this.field) : suggestion;\n                    if (itemValue && inputValue === itemValue.trim()) {\n                        valid = true;\n                        this.forceSelectionUpdateModelTimeout = setTimeout(() => {\n                            this.selectItem(suggestion, false);\n                        }, 250);\n                        break;\n                    }\n                }\n            }\n            if (!valid) {\n                if (this.multiple) {\n                    this.multiInputEL.nativeElement.value = '';\n                }\n                else {\n                    this.value = null;\n                    this.inputEL.nativeElement.value = '';\n                }\n                this.onClear.emit(event);\n                this.onModelChange(this.value);\n                this.updateFilledState();\n            }\n        }\n    }\n    onInputPaste(event) {\n        this.onKeydown(event);\n    }\n    isSelected(val) {\n        let selected = false;\n        if (this.value && this.value.length) {\n            for (let i = 0; i < this.value.length; i++) {\n                if (ObjectUtils.equals(this.value[i], val, this.dataKey)) {\n                    selected = true;\n                    break;\n                }\n            }\n        }\n        return selected;\n    }\n    findOptionIndex(option, suggestions) {\n        let index = -1;\n        if (suggestions) {\n            for (let i = 0; i < suggestions.length; i++) {\n                if (ObjectUtils.equals(option, suggestions[i])) {\n                    index = i;\n                    break;\n                }\n            }\n        }\n        return index;\n    }\n    findOptionGroupIndex(val, opts) {\n        let groupIndex, itemIndex;\n        if (opts) {\n            for (let i = 0; i < opts.length; i++) {\n                groupIndex = i;\n                itemIndex = this.findOptionIndex(val, this.getOptionGroupChildren(opts[i]));\n                if (itemIndex !== -1) {\n                    break;\n                }\n            }\n        }\n        if (itemIndex !== -1) {\n            return { groupIndex: groupIndex, itemIndex: itemIndex };\n        }\n        else {\n            return -1;\n        }\n    }\n    updateFilledState() {\n        if (this.multiple)\n            this.filled = (this.value && this.value.length) || (this.multiInputEL && this.multiInputEL.nativeElement && this.multiInputEL.nativeElement.value != '');\n        else\n            this.filled = (this.inputFieldValue && this.inputFieldValue != '') || (this.inputEL && this.inputEL.nativeElement && this.inputEL.nativeElement.value != '');\n    }\n    updateInputField() {\n        let formattedValue = this.resolveFieldData(this.value);\n        this.inputFieldValue = formattedValue;\n        if (this.inputEL && this.inputEL.nativeElement) {\n            this.inputEL.nativeElement.value = formattedValue;\n        }\n        this.updateFilledState();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', (event) => {\n                if (event.which === 3) {\n                    return;\n                }\n                if (!this.inputClick && !this.isDropdownClick(event)) {\n                    this.hide();\n                }\n                this.inputClick = false;\n                this.cd.markForCheck();\n            });\n        }\n    }\n    isDropdownClick(event) {\n        if (this.dropdown) {\n            let target = event.target;\n            return (target === this.dropdownButton.nativeElement || target.parentNode === this.dropdownButton.nativeElement);\n        }\n        else {\n            return false;\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerEL.nativeElement, () => {\n                if (this.overlayVisible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    onOverlayHide() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n        this.overlay = null;\n        this.onHide.emit();\n    }\n    ngOnDestroy() {\n        if (this.forceSelectionUpdateModelTimeout) {\n            clearTimeout(this.forceSelectionUpdateModelTimeout);\n            this.forceSelectionUpdateModelTimeout = null;\n        }\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        if (this.overlay) {\n            ZIndexUtils.clear(this.overlay);\n        }\n        this.restoreOverlayAppend();\n        this.onOverlayHide();\n    }\n}\nAutoComplete.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoComplete, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.IterableDiffers }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nAutoComplete.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: AutoComplete, selector: \"p-autoComplete\", inputs: { minLength: \"minLength\", delay: \"delay\", style: \"style\", panelStyle: \"panelStyle\", styleClass: \"styleClass\", panelStyleClass: \"panelStyleClass\", inputStyle: \"inputStyle\", inputId: \"inputId\", inputStyleClass: \"inputStyleClass\", placeholder: \"placeholder\", readonly: \"readonly\", disabled: \"disabled\", scrollHeight: \"scrollHeight\", lazy: \"lazy\", virtualScroll: \"virtualScroll\", virtualScrollItemSize: \"virtualScrollItemSize\", virtualScrollOptions: \"virtualScrollOptions\", maxlength: \"maxlength\", name: \"name\", required: \"required\", size: \"size\", appendTo: \"appendTo\", autoHighlight: \"autoHighlight\", forceSelection: \"forceSelection\", type: \"type\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", ariaLabel: \"ariaLabel\", dropdownAriaLabel: \"dropdownAriaLabel\", ariaLabelledBy: \"ariaLabelledBy\", dropdownIcon: \"dropdownIcon\", unique: \"unique\", group: \"group\", completeOnFocus: \"completeOnFocus\", showClear: \"showClear\", field: \"field\", dropdown: \"dropdown\", showEmptyMessage: \"showEmptyMessage\", dropdownMode: \"dropdownMode\", multiple: \"multiple\", tabindex: \"tabindex\", dataKey: \"dataKey\", emptyMessage: \"emptyMessage\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", autofocus: \"autofocus\", autocomplete: \"autocomplete\", optionGroupChildren: \"optionGroupChildren\", optionGroupLabel: \"optionGroupLabel\", itemSize: \"itemSize\", suggestions: \"suggestions\" }, outputs: { completeMethod: \"completeMethod\", onSelect: \"onSelect\", onUnselect: \"onUnselect\", onFocus: \"onFocus\", onBlur: \"onBlur\", onDropdownClick: \"onDropdownClick\", onClear: \"onClear\", onKeyUp: \"onKeyUp\", onShow: \"onShow\", onHide: \"onHide\", onLazyLoad: \"onLazyLoad\" }, host: { properties: { \"class.p-inputwrapper-filled\": \"filled\", \"class.p-inputwrapper-focus\": \"(focus && !disabled) || overlayVisible\", \"class.p-autocomplete-clearable\": \"showClear && !disabled\" }, classAttribute: \"p-element p-inputwrapper\" }, providers: [AUTOCOMPLETE_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"containerEL\", first: true, predicate: [\"container\"], descendants: true }, { propertyName: \"inputEL\", first: true, predicate: [\"in\"], descendants: true }, { propertyName: \"multiInputEL\", first: true, predicate: [\"multiIn\"], descendants: true }, { propertyName: \"multiContainerEL\", first: true, predicate: [\"multiContainer\"], descendants: true }, { propertyName: \"dropdownButton\", first: true, predicate: [\"ddBtn\"], descendants: true }, { propertyName: \"itemsViewChild\", first: true, predicate: [\"items\"], descendants: true }, { propertyName: \"scroller\", first: true, predicate: [\"scroller\"], descendants: true }], ngImport: i0, template: `\n        <span #container [ngClass]=\"{'p-autocomplete p-component':true,'p-autocomplete-dd':dropdown,'p-autocomplete-multiple':multiple}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input *ngIf=\"!multiple\" #in [attr.type]=\"type\" [attr.id]=\"inputId\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [autocomplete]=\"autocomplete\" [attr.required]=\"required\" [attr.name]=\"name\"\n            class=\"p-autocomplete-input p-inputtext p-component\" [ngClass]=\"{'p-autocomplete-dd-input':dropdown,'p-disabled': disabled}\" [value]=\"inputFieldValue\" aria-autocomplete=\"list\" role=\"searchbox\"\n            (click)=\"onInputClick($event)\" (input)=\"onInput($event)\" (keydown)=\"onKeydown($event)\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\"\n            [attr.placeholder]=\"placeholder\" [attr.size]=\"size\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [readonly]=\"readonly\" [disabled]=\"disabled\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\">\n            <i *ngIf=\"!multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <i *ngIf=\"multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <ul *ngIf=\"multiple\" #multiContainer class=\"p-autocomplete-multiple-container p-component p-inputtext\" [ngClass]=\"{'p-disabled':disabled,'p-focus':focus}\" (click)=\"multiIn.focus()\">\n                <li #token *ngFor=\"let val of value\" class=\"p-autocomplete-token\">\n                    <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: val}\"></ng-container>\n                    <span *ngIf=\"!selectedItemTemplate\" class=\"p-autocomplete-token-label\">{{resolveFieldData(val)}}</span>\n                    <span  class=\"p-autocomplete-token-icon pi pi-times-circle\" (click)=\"removeItem(token)\" *ngIf=\"!disabled && !readonly\"></span>\n                </li>\n                <li class=\"p-autocomplete-input-token\">\n                    <input #multiIn [attr.type]=\"type\" [attr.id]=\"inputId\" [disabled]=\"disabled\" [attr.placeholder]=\"(value&&value.length ? null : placeholder)\" [attr.tabindex]=\"tabindex\" [attr.maxlength]=\"maxlength\" (input)=\"onInput($event)\"  (click)=\"onInputClick($event)\"\n                            (keydown)=\"onKeydown($event)\" [readonly]=\"readonly\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\" [autocomplete]=\"autocomplete\"\n                            [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\"\n                            aria-autocomplete=\"list\" [attr.aria-controls]=\"listId\" role=\"searchbox\" [attr.aria-expanded]=\"overlayVisible\" aria-haspopup=\"true\" [attr.aria-activedescendant]=\"'p-highlighted-option'\">\n                </li>\n            </ul>\n            <i *ngIf=\"loading\" class=\"p-autocomplete-loader pi pi-spinner pi-spin\"></i><button #ddBtn type=\"button\" pButton [icon]=\"dropdownIcon\" [attr.aria-label]=\"dropdownAriaLabel\" class=\"p-autocomplete-dropdown\" [disabled]=\"disabled\" pRipple\n                (click)=\"handleDropdownClick($event)\" *ngIf=\"dropdown\" [attr.tabindex]=\"tabindex\"></button>\n            <div #panel *ngIf=\"overlayVisible\" (click)=\"onOverlayClick($event)\" [ngClass]=\"['p-autocomplete-panel p-component']\" [style.max-height]=\"virtualScroll ? 'auto' : scrollHeight\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"suggestions\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                    [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                    <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate\">\n                        <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: suggestions, options: {}}\"></ng-container>\n                </ng-container>\n\n                <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                    <ul #items role=\"listbox\" [attr.id]=\"listId\" class=\"p-autocomplete-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\">\n                        <ng-container *ngIf=\"group\">\n                            <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                <li class=\"p-autocomplete-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                    <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                    <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                </li>\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup)}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                        <ng-container *ngIf=\"!group\">\n                            <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items}\"></ng-container>\n                        </ng-container>\n                        <ng-template #itemslist let-suggestionsToDisplay>\n                            <li role=\"option\" *ngFor=\"let option of suggestionsToDisplay; let idx = index\" class=\"p-autocomplete-item\" pRipple [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\" [ngClass]=\"{'p-highlight': (option === highlightOption)}\" [id]=\"highlightOption == option ? 'p-highlighted-option':''\" (click)=\"selectItem(option)\">\n                                <span *ngIf=\"!itemTemplate\">{{resolveFieldData(option)}}</span>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: option, index: scrollerOptions.getOptions ? scrollerOptions.getOptions(idx) : idx}\"></ng-container>\n                            </li>\n                            <li *ngIf=\"noResults && showEmptyMessage\" class=\"p-autocomplete-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ng-template>\n                    </ul>\n                </ng-template>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </span>\n    `, isInline: true, styles: [\".p-autocomplete{display:inline-flex;position:relative}.p-autocomplete-loader{position:absolute;top:50%;margin-top:-.5rem}.p-autocomplete-dd .p-autocomplete-input{flex:1 1 auto;width:1%}.p-autocomplete-dd .p-autocomplete-input,.p-autocomplete-dd .p-autocomplete-multiple-container{border-top-right-radius:0;border-bottom-right-radius:0}.p-autocomplete-dd .p-autocomplete-dropdown{border-top-left-radius:0;border-bottom-left-radius:0}.p-autocomplete .p-autocomplete-panel{min-width:100%;top:0;left:0}.p-autocomplete-panel{position:absolute;overflow:auto}.p-autocomplete-items{margin:0;padding:0;list-style-type:none}.p-autocomplete-item{cursor:pointer;white-space:nowrap;position:relative;overflow:hidden}.p-autocomplete-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-autocomplete-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-autocomplete-token-icon{cursor:pointer}.p-autocomplete-input-token{flex:1 1 auto;display:inline-flex}.p-autocomplete-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-autocomplete{display:flex}.p-fluid .p-autocomplete-dd .p-autocomplete-input{width:1%}.p-autocomplete-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-autocomplete-clearable{position:relative}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.ButtonDirective, selector: \"[pButton]\", inputs: [\"iconPos\", \"loadingIcon\", \"label\", \"icon\", \"loading\"] }, { kind: \"directive\", type: i1.PrimeTemplate, selector: \"[pTemplate]\", inputs: [\"type\", \"pTemplate\"] }, { kind: \"directive\", type: i4.Ripple, selector: \"[pRipple]\" }, { kind: \"component\", type: i5.Scroller, selector: \"p-scroller\", inputs: [\"id\", \"style\", \"styleClass\", \"tabindex\", \"items\", \"itemSize\", \"scrollHeight\", \"scrollWidth\", \"orientation\", \"delay\", \"resizeDelay\", \"lazy\", \"disabled\", \"loaderDisabled\", \"columns\", \"showSpacer\", \"showLoader\", \"numToleratedItems\", \"loading\", \"autoSize\", \"trackBy\", \"options\"], outputs: [\"onLazyLoad\", \"onScroll\", \"onScrollIndexChange\"] }], animations: [\n        trigger('overlayAnimation', [\n            transition(':enter', [\n                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition(':leave', [\n                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoComplete, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-autoComplete', template: `\n        <span #container [ngClass]=\"{'p-autocomplete p-component':true,'p-autocomplete-dd':dropdown,'p-autocomplete-multiple':multiple}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <input *ngIf=\"!multiple\" #in [attr.type]=\"type\" [attr.id]=\"inputId\" [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [autocomplete]=\"autocomplete\" [attr.required]=\"required\" [attr.name]=\"name\"\n            class=\"p-autocomplete-input p-inputtext p-component\" [ngClass]=\"{'p-autocomplete-dd-input':dropdown,'p-disabled': disabled}\" [value]=\"inputFieldValue\" aria-autocomplete=\"list\" role=\"searchbox\"\n            (click)=\"onInputClick($event)\" (input)=\"onInput($event)\" (keydown)=\"onKeydown($event)\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\"\n            [attr.placeholder]=\"placeholder\" [attr.size]=\"size\" [attr.maxlength]=\"maxlength\" [attr.tabindex]=\"tabindex\" [readonly]=\"readonly\" [disabled]=\"disabled\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\">\n            <i *ngIf=\"!multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <i *ngIf=\"multiple && filled && !disabled && showClear\" class=\"p-autocomplete-clear-icon pi pi-times\" (click)=\"clear()\"></i>\n            <ul *ngIf=\"multiple\" #multiContainer class=\"p-autocomplete-multiple-container p-component p-inputtext\" [ngClass]=\"{'p-disabled':disabled,'p-focus':focus}\" (click)=\"multiIn.focus()\">\n                <li #token *ngFor=\"let val of value\" class=\"p-autocomplete-token\">\n                    <ng-container *ngTemplateOutlet=\"selectedItemTemplate; context: {$implicit: val}\"></ng-container>\n                    <span *ngIf=\"!selectedItemTemplate\" class=\"p-autocomplete-token-label\">{{resolveFieldData(val)}}</span>\n                    <span  class=\"p-autocomplete-token-icon pi pi-times-circle\" (click)=\"removeItem(token)\" *ngIf=\"!disabled && !readonly\"></span>\n                </li>\n                <li class=\"p-autocomplete-input-token\">\n                    <input #multiIn [attr.type]=\"type\" [attr.id]=\"inputId\" [disabled]=\"disabled\" [attr.placeholder]=\"(value&&value.length ? null : placeholder)\" [attr.tabindex]=\"tabindex\" [attr.maxlength]=\"maxlength\" (input)=\"onInput($event)\"  (click)=\"onInputClick($event)\"\n                            (keydown)=\"onKeydown($event)\" [readonly]=\"readonly\" (keyup)=\"onKeyup($event)\" [attr.autofocus]=\"autofocus\" (focus)=\"onInputFocus($event)\" (blur)=\"onInputBlur($event)\" (change)=\"onInputChange($event)\" (paste)=\"onInputPaste($event)\" [autocomplete]=\"autocomplete\"\n                            [ngStyle]=\"inputStyle\" [class]=\"inputStyleClass\" [attr.aria-label]=\"ariaLabel\" [attr.aria-labelledby]=\"ariaLabelledBy\" [attr.aria-required]=\"required\"\n                            aria-autocomplete=\"list\" [attr.aria-controls]=\"listId\" role=\"searchbox\" [attr.aria-expanded]=\"overlayVisible\" aria-haspopup=\"true\" [attr.aria-activedescendant]=\"'p-highlighted-option'\">\n                </li>\n            </ul>\n            <i *ngIf=\"loading\" class=\"p-autocomplete-loader pi pi-spinner pi-spin\"></i><button #ddBtn type=\"button\" pButton [icon]=\"dropdownIcon\" [attr.aria-label]=\"dropdownAriaLabel\" class=\"p-autocomplete-dropdown\" [disabled]=\"disabled\" pRipple\n                (click)=\"handleDropdownClick($event)\" *ngIf=\"dropdown\" [attr.tabindex]=\"tabindex\"></button>\n            <div #panel *ngIf=\"overlayVisible\" (click)=\"onOverlayClick($event)\" [ngClass]=\"['p-autocomplete-panel p-component']\" [style.max-height]=\"virtualScroll ? 'auto' : scrollHeight\" [ngStyle]=\"panelStyle\" [class]=\"panelStyleClass\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                <p-scroller *ngIf=\"virtualScroll\" #scroller [items]=\"suggestions\" [style]=\"{'height': scrollHeight}\" [itemSize]=\"virtualScrollItemSize||_itemSize\" [autoSize]=\"true\"\n                    [lazy]=\"lazy\" (onLazyLoad)=\"onLazyLoad.emit($event)\" [options]=\"virtualScrollOptions\">\n                    <ng-template pTemplate=\"content\" let-items let-scrollerOptions=\"options\">\n                        <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: items, options: scrollerOptions}\"></ng-container>\n                    </ng-template>\n                    <ng-container *ngIf=\"loaderTemplate\">\n                        <ng-template pTemplate=\"loader\" let-scrollerOptions=\"options\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: scrollerOptions}\"></ng-container>\n                        </ng-template>\n                    </ng-container>\n                </p-scroller>\n                <ng-container *ngIf=\"!virtualScroll\">\n                    <ng-container *ngTemplateOutlet=\"buildInItems; context: {$implicit: suggestions, options: {}}\"></ng-container>\n                </ng-container>\n\n                <ng-template #buildInItems let-items let-scrollerOptions=\"options\">\n                    <ul #items role=\"listbox\" [attr.id]=\"listId\" class=\"p-autocomplete-items\" [ngClass]=\"scrollerOptions.contentStyleClass\" [style]=\"scrollerOptions.contentStyle\">\n                        <ng-container *ngIf=\"group\">\n                            <ng-template ngFor let-optgroup [ngForOf]=\"items\">\n                                <li class=\"p-autocomplete-item-group\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                    <span *ngIf=\"!groupTemplate\">{{getOptionGroupLabel(optgroup)||'empty'}}</span>\n                                    <ng-container *ngTemplateOutlet=\"groupTemplate; context: {$implicit: optgroup}\"></ng-container>\n                                </li>\n                                <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: getOptionGroupChildren(optgroup)}\"></ng-container>\n                            </ng-template>\n                        </ng-container>\n                        <ng-container *ngIf=\"!group\">\n                            <ng-container *ngTemplateOutlet=\"itemslist; context: {$implicit: items}\"></ng-container>\n                        </ng-container>\n                        <ng-template #itemslist let-suggestionsToDisplay>\n                            <li role=\"option\" *ngFor=\"let option of suggestionsToDisplay; let idx = index\" class=\"p-autocomplete-item\" pRipple [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\" [ngClass]=\"{'p-highlight': (option === highlightOption)}\" [id]=\"highlightOption == option ? 'p-highlighted-option':''\" (click)=\"selectItem(option)\">\n                                <span *ngIf=\"!itemTemplate\">{{resolveFieldData(option)}}</span>\n                                <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: option, index: scrollerOptions.getOptions ? scrollerOptions.getOptions(idx) : idx}\"></ng-container>\n                            </li>\n                            <li *ngIf=\"noResults && showEmptyMessage\" class=\"p-autocomplete-empty-message\" [ngStyle]=\"{'height': scrollerOptions.itemSize + 'px'}\">\n                                <ng-container *ngIf=\"!emptyTemplate; else empty\">\n                                    {{emptyMessageLabel}}\n                                </ng-container>\n                                <ng-container #empty *ngTemplateOutlet=\"emptyTemplate\"></ng-container>\n                            </li>\n                        </ng-template>\n                    </ul>\n                </ng-template>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n            </div>\n        </span>\n    `, animations: [\n                        trigger('overlayAnimation', [\n                            transition(':enter', [\n                                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition(':leave', [\n                                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n                            ])\n                        ])\n                    ], host: {\n                        'class': 'p-element p-inputwrapper',\n                        '[class.p-inputwrapper-filled]': 'filled',\n                        '[class.p-inputwrapper-focus]': '(focus && !disabled) || overlayVisible',\n                        '[class.p-autocomplete-clearable]': 'showClear && !disabled'\n                    }, providers: [AUTOCOMPLETE_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, styles: [\".p-autocomplete{display:inline-flex;position:relative}.p-autocomplete-loader{position:absolute;top:50%;margin-top:-.5rem}.p-autocomplete-dd .p-autocomplete-input{flex:1 1 auto;width:1%}.p-autocomplete-dd .p-autocomplete-input,.p-autocomplete-dd .p-autocomplete-multiple-container{border-top-right-radius:0;border-bottom-right-radius:0}.p-autocomplete-dd .p-autocomplete-dropdown{border-top-left-radius:0;border-bottom-left-radius:0}.p-autocomplete .p-autocomplete-panel{min-width:100%;top:0;left:0}.p-autocomplete-panel{position:absolute;overflow:auto}.p-autocomplete-items{margin:0;padding:0;list-style-type:none}.p-autocomplete-item{cursor:pointer;white-space:nowrap;position:relative;overflow:hidden}.p-autocomplete-multiple-container{margin:0;padding:0;list-style-type:none;cursor:text;overflow:hidden;display:flex;align-items:center;flex-wrap:wrap}.p-autocomplete-token{cursor:default;display:inline-flex;align-items:center;flex:0 0 auto}.p-autocomplete-token-icon{cursor:pointer}.p-autocomplete-input-token{flex:1 1 auto;display:inline-flex}.p-autocomplete-input-token input{border:0 none;outline:0 none;background-color:transparent;margin:0;padding:0;box-shadow:none;border-radius:0;width:100%}.p-fluid .p-autocomplete{display:flex}.p-fluid .p-autocomplete-dd .p-autocomplete-input{width:1%}.p-autocomplete-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-autocomplete-clearable{position:relative}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.IterableDiffers }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }]; }, propDecorators: { minLength: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], panelStyle: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], panelStyleClass: [{\n                type: Input\n            }], inputStyle: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], inputStyleClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], virtualScroll: [{\n                type: Input\n            }], virtualScrollItemSize: [{\n                type: Input\n            }], virtualScrollOptions: [{\n                type: Input\n            }], maxlength: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoHighlight: [{\n                type: Input\n            }], forceSelection: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], dropdownAriaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], dropdownIcon: [{\n                type: Input\n            }], unique: [{\n                type: Input\n            }], group: [{\n                type: Input\n            }], completeOnFocus: [{\n                type: Input\n            }], showClear: [{\n                type: Input\n            }], field: [{\n                type: Input\n            }], dropdown: [{\n                type: Input\n            }], showEmptyMessage: [{\n                type: Input\n            }], dropdownMode: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], dataKey: [{\n                type: Input\n            }], emptyMessage: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], autofocus: [{\n                type: Input\n            }], autocomplete: [{\n                type: Input\n            }], optionGroupChildren: [{\n                type: Input\n            }], optionGroupLabel: [{\n                type: Input\n            }], containerEL: [{\n                type: ViewChild,\n                args: ['container']\n            }], inputEL: [{\n                type: ViewChild,\n                args: ['in']\n            }], multiInputEL: [{\n                type: ViewChild,\n                args: ['multiIn']\n            }], multiContainerEL: [{\n                type: ViewChild,\n                args: ['multiContainer']\n            }], dropdownButton: [{\n                type: ViewChild,\n                args: ['ddBtn']\n            }], itemsViewChild: [{\n                type: ViewChild,\n                args: ['items']\n            }], scroller: [{\n                type: ViewChild,\n                args: ['scroller']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], completeMethod: [{\n                type: Output\n            }], onSelect: [{\n                type: Output\n            }], onUnselect: [{\n                type: Output\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], onDropdownClick: [{\n                type: Output\n            }], onClear: [{\n                type: Output\n            }], onKeyUp: [{\n                type: Output\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], onLazyLoad: [{\n                type: Output\n            }], itemSize: [{\n                type: Input\n            }], suggestions: [{\n                type: Input\n            }] } });\nclass AutoCompleteModule {\n}\nAutoCompleteModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoCompleteModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAutoCompleteModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoCompleteModule, declarations: [AutoComplete], imports: [CommonModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule], exports: [AutoComplete, SharedModule, ScrollerModule] });\nAutoCompleteModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoCompleteModule, imports: [CommonModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule, SharedModule, ScrollerModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AutoCompleteModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, InputTextModule, ButtonModule, SharedModule, RippleModule, ScrollerModule],\n                    exports: [AutoComplete, SharedModule, ScrollerModule],\n                    declarations: [AutoComplete]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AUTOCOMPLETE_VALUE_ACCESSOR, AutoComplete, AutoCompleteModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,KAA1F,EAAiGC,SAAjG,EAA4GC,eAA5G,EAA6HC,MAA7H,EAAqIC,QAArI,QAAqJ,eAArJ;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,eAAT,EAA0BC,aAA1B,EAAyCC,YAAzC,QAA6D,aAA7D;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,SAASC,iBAAT,EAA4BC,WAA5B,EAAyCC,WAAzC,QAA4D,eAA5D;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,OAAO,KAAKC,EAAZ,MAAoB,kBAApB;AACA,SAASC,cAAT,QAA+B,kBAA/B;;;;;;;;;;;;;;;;;;iBAupB+FjC,E;;IAAAA,EAGnF,iC;IAHmFA,EAKnF;MALmFA,EAKnF;MAAA,eALmFA,EAKnF;MAAA,OALmFA,EAK1E,yCAAT;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAK3C,qCAAxC;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKf,uCAApE;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKa,qCAAhG;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKoE,0CAAvJ;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKkG,yCAArL;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKiI,2CAApN;IAAA;MALmFA,EAKnF;MAAA,gBALmFA,EAKnF;MAAA,OALmFA,EAKiK,0CAApP;IAAA,E;IALmFA,EAGnF,e;;;;mBAHmFA,E;IAAAA,EAGQ,mC;IAHRA,EAGf,0FAHeA,EAGf,uJ;IAHeA,EAGtD,wW;;;;;;iBAHsDA,E;;IAAAA,EAOnF,2B;IAPmFA,EAOoB;MAPpBA,EAOoB;MAAA,gBAPpBA,EAOoB;MAAA,OAPpBA,EAO6B,6BAAT;IAAA,E;IAPpBA,EAOsC,e;;;;;;iBAPtCA,E;;IAAAA,EAQnF,2B;IARmFA,EAQmB;MARnBA,EAQmB;MAAA,gBARnBA,EAQmB;MAAA,OARnBA,EAQ4B,6BAAT;IAAA,E;IARnBA,EAQqC,e;;;;;;IARrCA,EAW3E,sB;;;;;;IAX2EA,EAY3E,8B;IAZ2EA,EAYJ,U;IAZIA,EAYqB,e;;;;oBAZrBA,E;oBAAAA,E;IAAAA,EAYJ,a;IAZIA,EAYJ,qD;;;;;;iBAZIA,E;;IAAAA,EAa3E,8B;IAb2EA,EAaf;MAbeA,EAaf;MAbeA,EAaf;;MAAA,aAbeA,EAaf;;MAAA,gBAbeA,EAaf;MAAA,OAbeA,EAaN,sCAAT;IAAA,E;IAbeA,EAa4C,e;;;;;;;;;;;;IAb5CA,EAU/E,gC;IAV+EA,EAW3E,wF;IAX2EA,EAY3E,wE;IAZ2EA,EAa3E,wE;IAb2EA,EAc/E,e;;;;;oBAd+EA,E;IAAAA,EAW5D,a;IAX4DA,EAW5D,yFAX4DA,EAW5D,kC;IAX4DA,EAYpE,a;IAZoEA,EAYpE,kD;IAZoEA,EAac,a;IAbdA,EAac,2D;;;;;;;;;;;;;iBAbdA,E;;IAAAA,EASnF,gC;IATmFA,EASwE;MATxEA,EASwE;;MAAA,aATxEA,EASwE;;MAAA,OATxEA,EASiF,0BAAT;IAAA,E;IATxEA,EAU/E,+D;IAV+EA,EAe/E,gD;IAf+EA,EAgB0H;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAgBmI,qCAAT;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAgB8J,0CAApC;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiBxD,uCADkL;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiBN,qCADgI;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiBiD,0CADyE;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiB+E,yCAD2C;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiB8G,2CADY;IAAA;MAhB1HA,EAgB0H;MAAA,gBAhB1HA,EAgB0H;MAAA,OAhB1HA,EAiB8I,0CADpB;IAAA,E;IAhB1HA,EAgB3E,mB;;;;mBAhB2EA,E;IAAAA,EASoB,uBATpBA,EASoB,yD;IATpBA,EAUpD,a;IAVoDA,EAUpD,oC;IAVoDA,EAkB5C,a;IAlB4CA,EAkB5C,mC;IAlB4CA,EAgBpB,wI;IAhBoBA,EAgB3D,uc;;;;;;IAhB2DA,EAsBnF,sB;;;;;;iBAtBmFA,E;;IAAAA,EAsBR,oC;IAtBQA,EAuB/E;MAvB+EA,EAuB/E;MAAA,gBAvB+EA,EAuB/E;MAAA,OAvB+EA,EAuBtE,iDAAT;IAAA,E;IAvB+EA,EAuBG,e;;;;mBAvBHA,E;IAAAA,EAsB6B,qE;IAtB7BA,EAsBmD,iF;;;;;;IAtBnDA,EA0B/E,sB;;;;;;IA1B+EA,EA8BvE,sB;;;;;;;;;;;;;IA9BuEA,EA8BvE,+G;;;;;;IA9BuEA,E;;iBAAAA,E;;IAAAA,EA8BxD,iEA9BwDA,EA8BxD,0D;;;;;;IA9BwDA,EAkCnE,sB;;;;;;;;;;;;IAlCmEA,EAkCnE,8H;;;;;oBAlCmEA,E;IAAAA,EAkCpD,mFAlCoDA,EAkCpD,+C;;;;;;IAlCoDA,EAgC3E,2B;IAhC2EA,EAiCvE,8G;IAjCuEA,EAoC3E,wB;;;;;;;;;;;;iBApC2EA,E;;IAAAA,EA2B/E,wC;IA3B+EA,EA4B7D;MA5B6DA,EA4B7D;MAAA,gBA5B6DA,EA4B7D;MAAA,OA5B6DA,EA4B/C,6CAAd;IAAA,E;IA5B6DA,EA6B3E,+F;IA7B2EA,EAgC3E,iG;IAhC2EA,EAqC/E,e;;;;oBArC+EA,E;IAAAA,EA2Bb,YA3BaA,EA2Bb,gD;IA3BaA,EA2BnC,0L;IA3BmCA,EAgC5D,a;IAhC4DA,EAgC5D,2C;;;;;;IAhC4DA,EAuC3E,sB;;;;;;;;;;IAvC2EA,EAsC/E,2B;IAtC+EA,EAuC3E,mG;IAvC2EA,EAwC/E,wB;;;;IAxC+EA,E;;iBAAAA,E;;oBAAAA,E;IAAAA,EAuC5D,a;IAvC4DA,EAuC5D,iEAvC4DA,EAuC5D,+CAvC4DA,EAuC5D,2B;;;;;;IAvC4DA,EA+C3D,0B;IA/C2DA,EA+C9B,U;IA/C8BA,EA+CY,e;;;;yBA/CZA,E;oBAAAA,E;IAAAA,EA+C9B,a;IA/C8BA,EA+C9B,wE;;;;;;IA/C8BA,EAgD3D,sB;;;;;;IAhD2DA,EAkD/D,sB;;;;;;IAlD+DA,EA8C/D,4B;IA9C+DA,EA+C3D,+G;IA/C2DA,EAgD3D,+H;IAhD2DA,EAiD/D,e;IAjD+DA,EAkD/D,+H;;;;;gCAlD+DA,E;;iBAAAA,E;;oBAAAA,E;IAAAA,EA8CzB,uBA9CyBA,EA8CzB,+D;IA9CyBA,EA+CpD,a;IA/CoDA,EA+CpD,2C;IA/CoDA,EAgD5C,a;IAhD4CA,EAgD5C,kFAhD4CA,EAgD5C,uC;IAhD4CA,EAkDhD,a;IAlDgDA,EAkDhD,iEAlDgDA,EAkDhD,wE;;;;;;IAlDgDA,EA4CvE,2B;IA5CuEA,EA6CnE,gH;IA7CmEA,EAoDvE,wB;;;;sBApDuEA,E;IAAAA,EA6CnC,a;IA7CmCA,EA6CnC,iC;;;;;;IA7CmCA,EAsDnE,sB;;;;;;IAtDmEA,EAqDvE,2B;IArDuEA,EAsDnE,iH;IAtDmEA,EAuDvE,wB;;;;sBAvDuEA,E;;iBAAAA,E;;IAAAA,EAsDpD,a;IAtDoDA,EAsDpD,iEAtDoDA,EAsDpD,oC;;;;;;IAtDoDA,EA0D/D,0B;IA1D+DA,EA0DnC,U;IA1DmCA,EA0DP,e;;;;uBA1DOA,E;oBAAAA,E;IAAAA,EA0DnC,a;IA1DmCA,EA0DnC,wD;;;;;;IA1DmCA,EA2D/D,sB;;;;;;;;;;;;;;;;;;;iBA3D+DA,E;;IAAAA,EAyDnE,4B;IAzDmEA,EAyD+N;MAAA,oBAzD/NA,EAyD+N;MAAA;MAAA,gBAzD/NA,EAyD+N;MAAA,OAzD/NA,EAyDwO,4CAAT;IAAA,E;IAzD/NA,EA0D/D,qG;IA1D+DA,EA2D/D,qH;IA3D+DA,EA4DnE,e;;;;;;gCA5DmEA,E;oBAAAA,E;IAAAA,EAyDgD,uBAzDhDA,EAyDgD,2EAzDhDA,EAyDgD,6I;IAzDhDA,EA0DxD,a;IA1DwDA,EA0DxD,0C;IA1DwDA,EA2DhD,a;IA3DgDA,EA2DhD,iFA3DgDA,EA2DhD,2H;;;;;;IA3DgDA,EA8D/D,2B;IA9D+DA,EA+D3D,U;IA/D2DA,EAgE/D,wB;;;;oBAhE+DA,E;IAAAA,EA+D3D,a;IA/D2DA,EA+D3D,wD;;;;;;IA/D2DA,EAiE/D,gC;;;;;;IAjE+DA,EA6DnE,4B;IA7DmEA,EA8D/D,qH;IA9D+DA,EAiE/D,qH;IAjE+DA,EAkEnE,e;;;;gCAlEmEA,E;oBAAAA,E;IAAAA,EA6DY,uBA7DZA,EA6DY,+D;IA7DZA,EA8DhD,a;IA9DgDA,EA8DhD,sE;IA9DgDA,EAiEzC,a;IAjEyCA,EAiEzC,sD;;;;;;IAjEyCA,EAyDnE,6F;IAzDmEA,EA6DnE,4F;;;;;oBA7DmEA,E;IAAAA,EAyD9B,gD;IAzD8BA,EA6D9D,a;IA7D8DA,EA6D9D,kE;;;;;;IA7D8DA,EA2C3E,gC;IA3C2EA,EA4CvE,kG;IA5CuEA,EAqDvE,kG;IArDuEA,EAwDvE,uGAxDuEA,EAwDvE,wB;IAxDuEA,EAoE3E,e;;;;;oBApE2EA,E;IAAAA,EA2C6C,6C;IA3C7CA,EA2CD,6D;IA3CCA,EA2CjD,kC;IA3CiDA,EA4CxD,a;IA5CwDA,EA4CxD,kC;IA5CwDA,EAqDxD,a;IArDwDA,EAqDxD,mC;;;;;;IArDwDA,EAsE/E,sB;;;;;;;;;;;;;;;;;;;;;;;;iBAtE+EA,E;;IAAAA,EAwBnF,iC;IAxBmFA,EAwBhD;MAxBgDA,EAwBhD;MAAA,gBAxBgDA,EAwBhD;MAAA,OAxBgDA,EAwBvC,4CAAT;IAAA;MAxBgDA,EAwBhD;MAAA,gBAxBgDA,EAwBhD;MAAA,OAxBgDA,EAyByF,qDADzI;IAAA;MAxBgDA,EAwBhD;MAAA,iBAxBgDA,EAwBhD;MAAA,OAxBgDA,EAyBoJ,oDADpM;IAAA,E;IAxBgDA,EA0B/E,oF;IA1B+EA,EA2B/E,iF;IA3B+EA,EAsC/E,oF;IAtC+EA,EA0C/E,yFA1C+EA,EA0C/E,wB;IA1C+EA,EAsE/E,oF;IAtE+EA,EAuEnF,e;;;;mBAvEmFA,E;IAAAA,EAwBoH,mC;IAxBpHA,EAwBkC,+E;IAxBlCA,EAwBf,uBAxBeA,EAwBf,+EAxBeA,EAwBf,2BAxBeA,EAwBf,wF;IAxBeA,EA0BhE,a;IA1BgEA,EA0BhE,sD;IA1BgEA,EA2BlE,a;IA3BkEA,EA2BlE,yC;IA3BkEA,EAsChE,a;IAtCgEA,EAsChE,0C;IAtCgEA,EAsEhE,a;IAtEgEA,EAsEhE,sD;;;;;;;;;;;;AA3tB/B,MAAMkC,2BAA2B,GAAG;EAChCC,OAAO,EAAEJ,iBADuB;EAEhCK,WAAW,EAAEnC,UAAU,CAAC,MAAMoC,YAAP,CAFS;EAGhCC,KAAK,EAAE;AAHyB,CAApC;;AAKA,MAAMD,YAAN,CAAmB;EACfE,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,OAAnB,EAA4BC,MAA5B,EAAoCC,cAApC,EAAoD;IAC3D,KAAKL,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,KAAL,GAAa,GAAb;IACA,KAAKC,YAAL,GAAoB,OAApB;IACA,KAAKC,IAAL,GAAY,KAAZ;IACA,KAAKC,IAAL,GAAY,MAAZ;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,YAAL,GAAoB,oBAApB;IACA,KAAKC,MAAL,GAAc,IAAd;IACA,KAAKC,eAAL,GAAuB,KAAvB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,YAAL,GAAoB,OAApB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,YAAL,GAAoB,KAApB;IACA,KAAKC,cAAL,GAAsB,IAAI3D,YAAJ,EAAtB;IACA,KAAK4D,QAAL,GAAgB,IAAI5D,YAAJ,EAAhB;IACA,KAAK6D,UAAL,GAAkB,IAAI7D,YAAJ,EAAlB;IACA,KAAK8D,OAAL,GAAe,IAAI9D,YAAJ,EAAf;IACA,KAAK+D,MAAL,GAAc,IAAI/D,YAAJ,EAAd;IACA,KAAKgE,eAAL,GAAuB,IAAIhE,YAAJ,EAAvB;IACA,KAAKiE,OAAL,GAAe,IAAIjE,YAAJ,EAAf;IACA,KAAKkE,OAAL,GAAe,IAAIlE,YAAJ,EAAf;IACA,KAAKmE,MAAL,GAAc,IAAInE,YAAJ,EAAd;IACA,KAAKoE,MAAL,GAAc,IAAIpE,YAAJ,EAAd;IACA,KAAKqE,UAAL,GAAkB,IAAIrE,YAAJ,EAAlB;;IACA,KAAKsE,aAAL,GAAqB,MAAM,CAAG,CAA9B;;IACA,KAAKC,cAAL,GAAsB,MAAM,CAAG,CAA/B;;IACA,KAAKC,cAAL,GAAsB,KAAtB;IACA,KAAKC,KAAL,GAAa,KAAb;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,MAAL,GAAcnC,OAAO,CAACoC,IAAR,CAAa,EAAb,EAAiBC,MAAjB,CAAwB,IAAxB,CAAd;IACA,KAAKC,MAAL,GAAcrD,iBAAiB,KAAK,OAApC;EACH;;EACW,IAARsD,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACE,GAAD,EAAM;IACd,KAAKD,SAAL,GAAiBC,GAAjB;IACAC,OAAO,CAACC,IAAR,CAAa,kFAAb;EACH;;EACc,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKC,YAAZ;EACH;;EACc,IAAXD,WAAW,CAACH,GAAD,EAAM;IACjB,KAAKI,YAAL,GAAoBJ,GAApB;IACA,KAAKK,uBAAL;EACH;;EACDC,kBAAkB,GAAG;IACjB;IACA,IAAI,KAAKC,kBAAL,IAA2B,KAAKC,OAAhC,IAA2C,KAAKA,OAAL,CAAaC,YAA5D,EAA0E;MACtEC,UAAU,CAAC,MAAM;QACb,IAAI,KAAKF,OAAT,EAAkB;UACd,KAAKG,YAAL;QACH;MACJ,CAJS,EAIP,CAJO,CAAV;MAKA,KAAKJ,kBAAL,GAA0B,KAA1B;IACH;;IACD,IAAI,KAAKK,sBAAT,EAAiC;MAC7BF,UAAU,CAAC,MAAM;QACb,IAAI,KAAKF,OAAL,IAAgB,KAAKK,YAAzB,EAAuC;UACnC,IAAIC,QAAQ,GAAGxE,UAAU,CAACyE,UAAX,CAAsB,KAAKP,OAA3B,EAAoC,gBAApC,CAAf;;UACA,IAAIM,QAAJ,EAAc;YACVxE,UAAU,CAAC0E,YAAX,CAAwB,KAAKH,YAA7B,EAA2CC,QAA3C;UACH;QACJ;MACJ,CAPS,EAOP,CAPO,CAAV;MAQA,KAAKF,sBAAL,GAA8B,KAA9B;IACH;EACJ;;EACDP,uBAAuB,GAAG;IACtB,IAAI,KAAKD,YAAL,IAAqB,IAArB,IAA6B,KAAKa,OAAtC,EAA+C;MAC3C,KAAKC,eAAL,GAAuB,IAAvB;;MACA,IAAI,KAAKd,YAAL,CAAkBe,MAAtB,EAA8B;QAC1B,KAAKC,SAAL,GAAiB,KAAjB;QACA,KAAKC,IAAL;QACA,KAAKd,kBAAL,GAA0B,IAA1B;;QACA,IAAI,KAAKe,aAAT,EAAwB;UACpB,KAAKJ,eAAL,GAAuB,KAAKd,YAAL,CAAkB,CAAlB,CAAvB;QACH;MACJ,CAPD,MAQK;QACD,KAAKgB,SAAL,GAAiB,IAAjB;;QACA,IAAI,KAAKG,gBAAT,EAA2B;UACvB,KAAKF,IAAL;UACA,KAAKd,kBAAL,GAA0B,IAA1B;QACH,CAHD,MAIK;UACD,KAAKiB,IAAL;QACH;MACJ;;MACD,KAAKP,OAAL,GAAe,KAAf;IACH;EACJ;;EACDQ,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,MAAL;UACI,KAAKC,YAAL,GAAoBF,IAAI,CAACG,QAAzB;UACA;;QACJ,KAAK,OAAL;UACI,KAAKC,aAAL,GAAqBJ,IAAI,CAACG,QAA1B;UACA;;QACJ,KAAK,cAAL;UACI,KAAKE,oBAAL,GAA4BL,IAAI,CAACG,QAAjC;UACA;;QACJ,KAAK,QAAL;UACI,KAAKG,cAAL,GAAsBN,IAAI,CAACG,QAA3B;UACA;;QACJ,KAAK,OAAL;UACI,KAAKI,aAAL,GAAqBP,IAAI,CAACG,QAA1B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKK,cAAL,GAAsBR,IAAI,CAACG,QAA3B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKM,cAAL,GAAsBT,IAAI,CAACG,QAA3B;UACA;;QACJ;UACI,KAAKD,YAAL,GAAoBF,IAAI,CAACG,QAAzB;UACA;MAxBR;IA0BH,CA3BD;EA4BH;;EACDO,UAAU,CAACC,KAAD,EAAQ;IACd,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKC,MAAL,GAAc,KAAKD,KAAL,IAAc,KAAKA,KAAL,IAAc,EAA1C;IACA,KAAKE,gBAAL;IACA,KAAKnF,EAAL,CAAQoF,YAAR;EACH;;EACDC,sBAAsB,CAACC,WAAD,EAAc;IAChC,OAAO,KAAKC,mBAAL,GAA2BpG,WAAW,CAACqG,gBAAZ,CAA6BF,WAA7B,EAA0C,KAAKC,mBAA/C,CAA3B,GAAiGD,WAAW,CAACG,KAApH;EACH;;EACDC,mBAAmB,CAACJ,WAAD,EAAc;IAC7B,OAAO,KAAKK,gBAAL,GAAwBxG,WAAW,CAACqG,gBAAZ,CAA6BF,WAA7B,EAA0C,KAAKK,gBAA/C,CAAxB,GAA4FL,WAAW,CAACM,KAAZ,IAAqBC,SAArB,GAAiCP,WAAW,CAACM,KAA7C,GAAqDN,WAAxJ;EACH;;EACDQ,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAKjE,aAAL,GAAqBiE,EAArB;EACH;;EACDC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAKhE,cAAL,GAAsBgE,EAAtB;EACH;;EACDE,gBAAgB,CAACvD,GAAD,EAAM;IAClB,KAAKwD,QAAL,GAAgBxD,GAAhB;IACA,KAAK1C,EAAL,CAAQoF,YAAR;EACH;;EACDe,OAAO,CAACC,KAAD,EAAQ;IACX;IACA,IAAI,CAAC,KAAKC,YAAN,IAAsBrH,UAAU,CAACsH,IAAX,EAA1B,EAA6C;MACzC;IACH;;IACD,IAAI,KAAKC,OAAT,EAAkB;MACdC,YAAY,CAAC,KAAKD,OAAN,CAAZ;IACH;;IACD,IAAItB,KAAK,GAAGmB,KAAK,CAACK,MAAN,CAAaxB,KAAzB;IACA,KAAK9C,UAAL,GAAkB8C,KAAlB;;IACA,IAAI,CAAC,KAAKyB,QAAN,IAAkB,CAAC,KAAKC,cAA5B,EAA4C;MACxC,KAAK7E,aAAL,CAAmBmD,KAAnB;IACH;;IACD,IAAIA,KAAK,CAACpB,MAAN,KAAiB,CAAjB,IAAsB,CAAC,KAAK6C,QAAhC,EAA0C;MACtC,KAAKxC,IAAL;MACA,KAAKzC,OAAL,CAAamF,IAAb,CAAkBR,KAAlB;MACA,KAAKtE,aAAL,CAAmBmD,KAAnB;IACH;;IACD,IAAIA,KAAK,CAACpB,MAAN,IAAgB,KAAKzD,SAAzB,EAAoC;MAChC,KAAKmG,OAAL,GAAenD,UAAU,CAAC,MAAM;QAC5B,KAAKyD,MAAL,CAAYT,KAAZ,EAAmBnB,KAAnB;MACH,CAFwB,EAEtB,KAAK5E,KAFiB,CAAzB;IAGH,CAJD,MAKK;MACD,KAAK6D,IAAL;IACH;;IACD,KAAK4C,iBAAL;IACA,KAAKT,YAAL,GAAoB,KAApB;EACH;;EACDU,YAAY,CAACX,KAAD,EAAQ;IAChB,IAAI,KAAKY,qBAAT,EAAgC;MAC5B,KAAKC,UAAL,GAAkB,IAAlB;IACH;EACJ;;EACDJ,MAAM,CAACT,KAAD,EAAQc,KAAR,EAAe;IACjB;IACA,IAAIA,KAAK,KAAKrB,SAAV,IAAuBqB,KAAK,KAAK,IAArC,EAA2C;MACvC;IACH;;IACD,KAAKvD,OAAL,GAAe,IAAf;IACA,KAAKxC,cAAL,CAAoByF,IAApB,CAAyB;MACrBO,aAAa,EAAEf,KADM;MAErBc,KAAK,EAAEA;IAFc,CAAzB;EAIH;;EACDE,UAAU,CAACC,MAAD,EAASpF,KAAK,GAAG,IAAjB,EAAuB;IAC7B,IAAI,KAAKqF,gCAAT,EAA2C;MACvCd,YAAY,CAAC,KAAKc,gCAAN,CAAZ;MACA,KAAKA,gCAAL,GAAwC,IAAxC;IACH;;IACD,IAAI,KAAKZ,QAAT,EAAmB;MACf,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAhC,GAAwC,EAAxC;MACA,KAAKA,KAAL,GAAa,KAAKA,KAAL,IAAc,EAA3B;;MACA,IAAI,CAAC,KAAKwC,UAAL,CAAgBJ,MAAhB,CAAD,IAA4B,CAAC,KAAKzG,MAAtC,EAA8C;QAC1C,KAAKqE,KAAL,GAAa,CAAC,GAAG,KAAKA,KAAT,EAAgBoC,MAAhB,CAAb;QACA,KAAKvF,aAAL,CAAmB,KAAKmD,KAAxB;MACH;IACJ,CAPD,MAQK;MACD,KAAKyC,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,GAAmC,KAAKO,gBAAL,CAAsB6B,MAAtB,CAAnC;MACA,KAAKpC,KAAL,GAAaoC,MAAb;MACA,KAAKvF,aAAL,CAAmB,KAAKmD,KAAxB;IACH;;IACD,KAAK7D,QAAL,CAAcwF,IAAd,CAAmBS,MAAnB;IACA,KAAKP,iBAAL;;IACA,IAAI7E,KAAJ,EAAW;MACP,KAAK0F,WAAL,GAAmB,IAAnB;MACA,KAAKC,UAAL;IACH;EACJ;;EACD7D,IAAI,GAAG;IACH,IAAI,KAAKwD,YAAL,IAAqB,KAAKG,OAA9B,EAAuC;MACnC,IAAIG,QAAQ,GAAG,KAAKnB,QAAL,GACX,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCM,aAAhC,CAA8CC,aAA9C,IAA+D,KAAKR,YAAL,CAAkBC,aADtE,GAEX,KAAKE,OAAL,CAAaF,aAAb,CAA2BM,aAA3B,CAAyCC,aAAzC,IAA0D,KAAKL,OAAL,CAAaF,aAF3E;;MAGA,IAAI,CAAC,KAAKxF,cAAN,IAAwB6F,QAA5B,EAAsC;QAClC,KAAK7F,cAAL,GAAsB,IAAtB;MACH;IACJ;EACJ;;EACDgG,KAAK,GAAG;IACJ,IAAI,KAAKtB,QAAT,EAAmB;MACf,KAAKzB,KAAL,GAAa,IAAb;IACH,CAFD,MAGK;MACD,KAAK9C,UAAL,GAAkB,IAAlB;MACA,KAAKuF,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,GAAmC,EAAnC;IACH;;IACD,KAAK6B,iBAAL;IACA,KAAKhF,aAAL,CAAmB,KAAKmD,KAAxB;IACA,KAAKxD,OAAL,CAAamF,IAAb;EACH;;EACDqB,uBAAuB,CAAC7B,KAAD,EAAQ;IAC3B,IAAI8B,EAAJ;;IACA,QAAQ9B,KAAK,CAAC+B,OAAd;MACI,KAAK,SAAL;QACI,KAAKjF,OAAL,GAAekD,KAAK,CAACgC,OAArB;QACA,KAAK7E,YAAL,GAAoB,KAAK8E,aAAL,GAAqBrJ,UAAU,CAACyE,UAAX,CAAsB,KAAKP,OAA3B,EAAoC,aAApC,CAArB,GAA0E,KAAKA,OAAnG;QACA,KAAKmF,aAAL,KAAuB,CAACH,EAAE,GAAG,KAAKI,QAAX,MAAyB,IAAzB,IAAiCJ,EAAE,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,EAAE,CAACK,YAAH,CAAgB,KAAKC,cAAL,CAAoBhB,aAApC,CAAjF;QACA,KAAKiB,aAAL;;QACA,IAAI,KAAKhI,UAAT,EAAqB;UACjBrB,WAAW,CAACsJ,GAAZ,CAAgB,SAAhB,EAA2B,KAAKxF,OAAhC,EAAyC,KAAKxC,UAAL,GAAkB,KAAKR,MAAL,CAAYyI,MAAZ,CAAmBzF,OAA9E;QACH;;QACD,KAAKG,YAAL;QACA,KAAKuF,yBAAL;QACA,KAAKC,0BAAL;QACA,KAAKC,kBAAL;QACA,KAAKnH,MAAL,CAAYiF,IAAZ,CAAiBR,KAAjB;QACA;;MACJ,KAAK,MAAL;QACI,KAAK2C,aAAL;QACA;IAjBR;EAmBH;;EACDC,qBAAqB,CAAC5C,KAAD,EAAQ;IACzB,QAAQA,KAAK,CAAC+B,OAAd;MACI,KAAK,MAAL;QACI,IAAI,KAAK1H,UAAT,EAAqB;UACjBrB,WAAW,CAAC4I,KAAZ,CAAkB5B,KAAK,CAACgC,OAAxB;QACH;;QACD;IALR;EAOH;;EACDa,cAAc,CAAC7C,KAAD,EAAQ;IAClB,KAAKjG,cAAL,CAAoB+I,GAApB,CAAwB;MACpB/B,aAAa,EAAEf,KADK;MAEpBK,MAAM,EAAE,KAAK3G,EAAL,CAAQ0H;IAFI,CAAxB;EAIH;;EACDiB,aAAa,GAAG;IACZ,IAAI,KAAKU,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACIC,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKpG,OAA/B,EADJ,KAGIlE,UAAU,CAACsK,WAAX,CAAuB,KAAKpG,OAA5B,EAAqC,KAAKiG,QAA1C;;MACJ,IAAI,CAAC,KAAKjG,OAAL,CAAa7E,KAAb,CAAmBkL,QAAxB,EAAkC;QAC9B,KAAKrG,OAAL,CAAa7E,KAAb,CAAmBkL,QAAnB,GAA8BvK,UAAU,CAACwK,QAAX,CAAoB,KAAK1J,EAAL,CAAQ0H,aAAR,CAAsBiC,QAAtB,CAA+B,CAA/B,CAApB,IAAyD,IAAvF;MACH;IACJ;EACJ;;EACDjE,gBAAgB,CAACP,KAAD,EAAQ;IACpB,IAAIyE,IAAI,GAAG,KAAKC,KAAL,GAAaxK,WAAW,CAACqG,gBAAZ,CAA6BP,KAA7B,EAAoC,KAAK0E,KAAzC,CAAb,GAA+D1E,KAA1E;IACA,OAAOyE,IAAI,MAAM,QAAQ7D,SAAd,CAAJ,GAA+B6D,IAA/B,GAAsC,EAA7C;EACH;;EACDE,oBAAoB,GAAG;IACnB,IAAI,KAAK1G,OAAL,IAAgB,KAAKiG,QAAzB,EAAmC;MAC/B,KAAKrJ,EAAL,CAAQ0H,aAAR,CAAsB8B,WAAtB,CAAkC,KAAKpG,OAAvC;IACH;EACJ;;EACDG,YAAY,GAAG;IACX,IAAI,KAAK8F,QAAT,EACInK,UAAU,CAAC6K,gBAAX,CAA4B,KAAK3G,OAAjC,EAA2C,KAAKwD,QAAL,GAAgB,KAAKoD,gBAAL,CAAsBtC,aAAtC,GAAsD,KAAKE,OAAL,CAAaF,aAA9G,EADJ,KAGIxI,UAAU,CAAC+K,gBAAX,CAA4B,KAAK7G,OAAjC,EAA2C,KAAKwD,QAAL,GAAgB,KAAKoD,gBAAL,CAAsBtC,aAAtC,GAAsD,KAAKE,OAAL,CAAaF,aAA9G;EACP;;EACDtD,IAAI,GAAG;IACH,KAAKlC,cAAL,GAAsB,KAAtB;IACA,KAAKhC,EAAL,CAAQoF,YAAR;EACH;;EACD4E,mBAAmB,CAAC5D,KAAD,EAAQ;IACvB,IAAI,CAAC,KAAKpE,cAAV,EAA0B;MACtB,KAAK4F,UAAL;MACA,IAAIqC,UAAU,GAAG,KAAKvD,QAAL,GAAgB,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAhD,GAAwD,KAAKyC,OAAL,CAAaF,aAAb,CAA2BvC,KAApG;MACA,IAAI,KAAKlE,YAAL,KAAsB,OAA1B,EACI,KAAK8F,MAAL,CAAYT,KAAZ,EAAmB,EAAnB,EADJ,KAEK,IAAI,KAAKrF,YAAL,KAAsB,SAA1B,EACD,KAAK8F,MAAL,CAAYT,KAAZ,EAAmB6D,UAAnB;MACJ,KAAKzI,eAAL,CAAqBoF,IAArB,CAA0B;QACtBO,aAAa,EAAEf,KADO;QAEtBc,KAAK,EAAE+C;MAFe,CAA1B;IAIH,CAXD,MAYK;MACD,KAAK/F,IAAL;IACH;EACJ;;EACD0D,UAAU,GAAG;IACT,IAAI,KAAKlB,QAAT,EACI,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCvF,KAAhC,GADJ,KAGI,KAAKyF,OAAL,CAAaF,aAAb,CAA2BvF,KAA3B;EACP;;EACoB,IAAjBiI,iBAAiB,GAAG;IACpB,OAAO,KAAKC,YAAL,IAAqB,KAAKjK,MAAL,CAAYkK,cAAZ,CAA2BvL,eAAe,CAACwL,aAA3C,CAA5B;EACH;;EACDC,UAAU,CAAChG,IAAD,EAAO;IACb,IAAIiG,SAAS,GAAGvL,UAAU,CAACwL,KAAX,CAAiBlG,IAAjB,CAAhB;IACA,IAAImG,YAAY,GAAG,KAAKxF,KAAL,CAAWsF,SAAX,CAAnB;IACA,KAAKtF,KAAL,GAAa,KAAKA,KAAL,CAAWyF,MAAX,CAAkB,CAAChI,GAAD,EAAMiI,CAAN,KAAYA,CAAC,IAAIJ,SAAnC,CAAb;IACA,KAAKzI,aAAL,CAAmB,KAAKmD,KAAxB;IACA,KAAK6B,iBAAL;IACA,KAAKzF,UAAL,CAAgBuF,IAAhB,CAAqB6D,YAArB;EACH;;EACDG,SAAS,CAACxE,KAAD,EAAQ;IACb,IAAI,KAAKpE,cAAT,EAAyB;MACrB,QAAQoE,KAAK,CAACyE,KAAd;QACI;QACA,KAAK,EAAL;UACI,IAAI,KAAKC,KAAT,EAAgB;YACZ,IAAIC,kBAAkB,GAAG,KAAKC,oBAAL,CAA0B,KAAKpH,eAA/B,EAAgD,KAAKf,WAArD,CAAzB;;YACA,IAAIkI,kBAAkB,KAAK,CAAC,CAA5B,EAA+B;cAC3B,IAAIE,aAAa,GAAGF,kBAAkB,CAACR,SAAnB,GAA+B,CAAnD;;cACA,IAAIU,aAAa,GAAI,KAAK5F,sBAAL,CAA4B,KAAKxC,WAAL,CAAiBkI,kBAAkB,CAACG,UAApC,CAA5B,EAA6ErH,MAAlG,EAA2G;gBACvG,KAAKD,eAAL,GAAuB,KAAKyB,sBAAL,CAA4B,KAAKxC,WAAL,CAAiBkI,kBAAkB,CAACG,UAApC,CAA5B,EAA6ED,aAA7E,CAAvB;gBACA,KAAK3H,sBAAL,GAA8B,IAA9B;cACH,CAHD,MAIK,IAAI,KAAKT,WAAL,CAAiBkI,kBAAkB,CAACG,UAAnB,GAAgC,CAAjD,CAAJ,EAAyD;gBAC1D,KAAKtH,eAAL,GAAuB,KAAKyB,sBAAL,CAA4B,KAAKxC,WAAL,CAAiBkI,kBAAkB,CAACG,UAAnB,GAAgC,CAAjD,CAA5B,EAAiF,CAAjF,CAAvB;gBACA,KAAK5H,sBAAL,GAA8B,IAA9B;cACH;YACJ,CAVD,MAWK;cACD,KAAKM,eAAL,GAAuB,KAAKyB,sBAAL,CAA4B,KAAKxC,WAAL,CAAiB,CAAjB,CAA5B,EAAiD,CAAjD,CAAvB;YACH;UACJ,CAhBD,MAiBK;YACD,IAAIkI,kBAAkB,GAAG,KAAKI,eAAL,CAAqB,KAAKvH,eAA1B,EAA2C,KAAKf,WAAhD,CAAzB;;YACA,IAAIkI,kBAAkB,IAAI,CAAC,CAA3B,EAA8B;cAC1B,IAAIE,aAAa,GAAGF,kBAAkB,GAAG,CAAzC;;cACA,IAAIE,aAAa,IAAK,KAAKpI,WAAL,CAAiBgB,MAAvC,EAAgD;gBAC5C,KAAKD,eAAL,GAAuB,KAAKf,WAAL,CAAiBoI,aAAjB,CAAvB;gBACA,KAAK3H,sBAAL,GAA8B,IAA9B;cACH;YACJ,CAND,MAOK;cACD,KAAKM,eAAL,GAAuB,KAAKf,WAAL,CAAiB,CAAjB,CAAvB;YACH;UACJ;;UACDuD,KAAK,CAACgF,cAAN;UACA;QACJ;;QACA,KAAK,EAAL;UACI,IAAI,KAAKN,KAAT,EAAgB;YACZ,IAAIC,kBAAkB,GAAG,KAAKC,oBAAL,CAA0B,KAAKpH,eAA/B,EAAgD,KAAKf,WAArD,CAAzB;;YACA,IAAIkI,kBAAkB,KAAK,CAAC,CAA5B,EAA+B;cAC3B,IAAIM,aAAa,GAAGN,kBAAkB,CAACR,SAAnB,GAA+B,CAAnD;;cACA,IAAIc,aAAa,IAAI,CAArB,EAAwB;gBACpB,KAAKzH,eAAL,GAAuB,KAAKyB,sBAAL,CAA4B,KAAKxC,WAAL,CAAiBkI,kBAAkB,CAACG,UAApC,CAA5B,EAA6EG,aAA7E,CAAvB;gBACA,KAAK/H,sBAAL,GAA8B,IAA9B;cACH,CAHD,MAIK,IAAI+H,aAAa,GAAG,CAApB,EAAuB;gBACxB,IAAIC,SAAS,GAAG,KAAKzI,WAAL,CAAiBkI,kBAAkB,CAACG,UAAnB,GAAgC,CAAjD,CAAhB;;gBACA,IAAII,SAAJ,EAAe;kBACX,KAAK1H,eAAL,GAAuB,KAAKyB,sBAAL,CAA4BiG,SAA5B,EAAuC,KAAKjG,sBAAL,CAA4BiG,SAA5B,EAAuCzH,MAAvC,GAAgD,CAAvF,CAAvB;kBACA,KAAKP,sBAAL,GAA8B,IAA9B;gBACH;cACJ;YACJ;UACJ,CAhBD,MAiBK;YACD,IAAIyH,kBAAkB,GAAG,KAAKI,eAAL,CAAqB,KAAKvH,eAA1B,EAA2C,KAAKf,WAAhD,CAAzB;;YACA,IAAIkI,kBAAkB,GAAG,CAAzB,EAA4B;cACxB,IAAIM,aAAa,GAAGN,kBAAkB,GAAG,CAAzC;cACA,KAAKnH,eAAL,GAAuB,KAAKf,WAAL,CAAiBwI,aAAjB,CAAvB;cACA,KAAK/H,sBAAL,GAA8B,IAA9B;YACH;UACJ;;UACD8C,KAAK,CAACgF,cAAN;UACA;QACJ;;QACA,KAAK,EAAL;UACI,IAAI,KAAKxH,eAAT,EAA0B;YACtB,KAAKwD,UAAL,CAAgB,KAAKxD,eAArB;YACA,KAAKM,IAAL;UACH;;UACDkC,KAAK,CAACgF,cAAN;UACA;QACJ;;QACA,KAAK,EAAL;UACI,KAAKlH,IAAL;UACAkC,KAAK,CAACgF,cAAN;UACA;QACJ;;QACA,KAAK,CAAL;UACI,IAAI,KAAKxH,eAAT,EAA0B;YACtB,KAAKwD,UAAL,CAAgB,KAAKxD,eAArB;UACH;;UACD,KAAKM,IAAL;UACA;MAnFR;IAqFH,CAtFD,MAuFK;MACD,IAAIkC,KAAK,CAACyE,KAAN,KAAgB,EAAhB,IAAsB,KAAKhI,WAA/B,EAA4C;QACxC,KAAKgE,MAAL,CAAYT,KAAZ,EAAmBA,KAAK,CAACK,MAAN,CAAaxB,KAAhC;MACH,CAFD,MAGK,IAAKmB,KAAK,CAACmF,OAAN,IAAiBnF,KAAK,CAACoF,GAAN,KAAc,GAAhC,IAAwC,CAAC,KAAK9E,QAAlD,EAA4D;QAC7D,KAAKgB,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,GAAmC,KAAKO,gBAAL,CAAsB,IAAtB,CAAnC;QACA,KAAKP,KAAL,GAAa,EAAb;QACA,KAAKnD,aAAL,CAAmB,KAAKmD,KAAxB;MACH,CAJI,MAKA,IAAKmB,KAAK,CAACmF,OAAN,IAAiBnF,KAAK,CAACoF,GAAN,KAAc,GAAhC,IAAwC,KAAK9E,QAAjD,EAA2D;QAC5D,KAAKzB,KAAL,CAAWwG,GAAX;QACA,KAAK3J,aAAL,CAAmB,KAAKmD,KAAxB;QACA,KAAK6B,iBAAL;MACH;IACJ;;IACD,IAAI,KAAKJ,QAAT,EAAmB;MACf,QAAQN,KAAK,CAACyE,KAAd;QACI;QACA,KAAK,CAAL;UACI,IAAI,KAAK5F,KAAL,IAAc,KAAKA,KAAL,CAAWpB,MAAzB,IAAmC,CAAC,KAAK0D,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAxE,EAA+E;YAC3E,KAAKA,KAAL,GAAa,CAAC,GAAG,KAAKA,KAAT,CAAb;YACA,MAAMwF,YAAY,GAAG,KAAKxF,KAAL,CAAWwG,GAAX,EAArB;YACA,KAAK3J,aAAL,CAAmB,KAAKmD,KAAxB;YACA,KAAK6B,iBAAL;YACA,KAAKzF,UAAL,CAAgBuF,IAAhB,CAAqB6D,YAArB;UACH;;UACD;MAVR;IAYH;;IACD,KAAKpE,YAAL,GAAoB,IAApB;EACH;;EACDqF,OAAO,CAACtF,KAAD,EAAQ;IACX,KAAK1E,OAAL,CAAakF,IAAb,CAAkBR,KAAlB;EACH;;EACDuF,YAAY,CAACvF,KAAD,EAAQ;IAChB,IAAI,CAAC,KAAKuB,WAAN,IAAqB,KAAK9G,eAA9B,EAA+C;MAC3C,IAAIoJ,UAAU,GAAG,KAAKvD,QAAL,GAAgB,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAhD,GAAwD,KAAKyC,OAAL,CAAaF,aAAb,CAA2BvC,KAApG;MACA,KAAK4B,MAAL,CAAYT,KAAZ,EAAmB6D,UAAnB;IACH;;IACD,KAAKhI,KAAL,GAAa,IAAb;IACA,KAAKX,OAAL,CAAasF,IAAb,CAAkBR,KAAlB;IACA,KAAKuB,WAAL,GAAmB,KAAnB;EACH;;EACDiE,WAAW,CAACxF,KAAD,EAAQ;IACf,KAAKnE,KAAL,GAAa,KAAb;IACA,KAAKF,cAAL;IACA,KAAKR,MAAL,CAAYqF,IAAZ,CAAiBR,KAAjB;EACH;;EACDyF,aAAa,CAACzF,KAAD,EAAQ;IACjB,IAAI,KAAKO,cAAT,EAAyB;MACrB,IAAImF,KAAK,GAAG,KAAZ;MACA,IAAI3J,UAAU,GAAGiE,KAAK,CAACK,MAAN,CAAaxB,KAAb,CAAmB8G,IAAnB,EAAjB;;MACA,IAAI,KAAKlJ,WAAT,EAAsB;QAClB,KAAK,IAAImJ,UAAT,IAAuB,KAAKnJ,WAA5B,EAAyC;UACrC,IAAIoJ,SAAS,GAAG,KAAKtC,KAAL,GAAaxK,WAAW,CAACqG,gBAAZ,CAA6BwG,UAA7B,EAAyC,KAAKrC,KAA9C,CAAb,GAAoEqC,UAApF;;UACA,IAAIC,SAAS,IAAI9J,UAAU,KAAK8J,SAAS,CAACF,IAAV,EAAhC,EAAkD;YAC9CD,KAAK,GAAG,IAAR;YACA,KAAKxE,gCAAL,GAAwClE,UAAU,CAAC,MAAM;cACrD,KAAKgE,UAAL,CAAgB4E,UAAhB,EAA4B,KAA5B;YACH,CAFiD,EAE/C,GAF+C,CAAlD;YAGA;UACH;QACJ;MACJ;;MACD,IAAI,CAACF,KAAL,EAAY;QACR,IAAI,KAAKpF,QAAT,EAAmB;UACf,KAAKa,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAhC,GAAwC,EAAxC;QACH,CAFD,MAGK;UACD,KAAKA,KAAL,GAAa,IAAb;UACA,KAAKyC,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,GAAmC,EAAnC;QACH;;QACD,KAAKxD,OAAL,CAAamF,IAAb,CAAkBR,KAAlB;QACA,KAAKtE,aAAL,CAAmB,KAAKmD,KAAxB;QACA,KAAK6B,iBAAL;MACH;IACJ;EACJ;;EACDoF,YAAY,CAAC9F,KAAD,EAAQ;IAChB,KAAKwE,SAAL,CAAexE,KAAf;EACH;;EACDqB,UAAU,CAAC/E,GAAD,EAAM;IACZ,IAAIyJ,QAAQ,GAAG,KAAf;;IACA,IAAI,KAAKlH,KAAL,IAAc,KAAKA,KAAL,CAAWpB,MAA7B,EAAqC;MACjC,KAAK,IAAI8G,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1F,KAAL,CAAWpB,MAA/B,EAAuC8G,CAAC,EAAxC,EAA4C;QACxC,IAAIxL,WAAW,CAACiN,MAAZ,CAAmB,KAAKnH,KAAL,CAAW0F,CAAX,CAAnB,EAAkCjI,GAAlC,EAAuC,KAAK2J,OAA5C,CAAJ,EAA0D;UACtDF,QAAQ,GAAG,IAAX;UACA;QACH;MACJ;IACJ;;IACD,OAAOA,QAAP;EACH;;EACDhB,eAAe,CAAC9D,MAAD,EAASxE,WAAT,EAAsB;IACjC,IAAI2H,KAAK,GAAG,CAAC,CAAb;;IACA,IAAI3H,WAAJ,EAAiB;MACb,KAAK,IAAI8H,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG9H,WAAW,CAACgB,MAAhC,EAAwC8G,CAAC,EAAzC,EAA6C;QACzC,IAAIxL,WAAW,CAACiN,MAAZ,CAAmB/E,MAAnB,EAA2BxE,WAAW,CAAC8H,CAAD,CAAtC,CAAJ,EAAgD;UAC5CH,KAAK,GAAGG,CAAR;UACA;QACH;MACJ;IACJ;;IACD,OAAOH,KAAP;EACH;;EACDQ,oBAAoB,CAACtI,GAAD,EAAM4J,IAAN,EAAY;IAC5B,IAAIpB,UAAJ,EAAgBX,SAAhB;;IACA,IAAI+B,IAAJ,EAAU;MACN,KAAK,IAAI3B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2B,IAAI,CAACzI,MAAzB,EAAiC8G,CAAC,EAAlC,EAAsC;QAClCO,UAAU,GAAGP,CAAb;QACAJ,SAAS,GAAG,KAAKY,eAAL,CAAqBzI,GAArB,EAA0B,KAAK2C,sBAAL,CAA4BiH,IAAI,CAAC3B,CAAD,CAAhC,CAA1B,CAAZ;;QACA,IAAIJ,SAAS,KAAK,CAAC,CAAnB,EAAsB;UAClB;QACH;MACJ;IACJ;;IACD,IAAIA,SAAS,KAAK,CAAC,CAAnB,EAAsB;MAClB,OAAO;QAAEW,UAAU,EAAEA,UAAd;QAA0BX,SAAS,EAAEA;MAArC,CAAP;IACH,CAFD,MAGK;MACD,OAAO,CAAC,CAAR;IACH;EACJ;;EACDzD,iBAAiB,GAAG;IAChB,IAAI,KAAKJ,QAAT,EACI,KAAKxB,MAAL,GAAe,KAAKD,KAAL,IAAc,KAAKA,KAAL,CAAWpB,MAA1B,IAAsC,KAAK0D,YAAL,IAAqB,KAAKA,YAAL,CAAkBC,aAAvC,IAAwD,KAAKD,YAAL,CAAkBC,aAAlB,CAAgCvC,KAAhC,IAAyC,EAArJ,CADJ,KAGI,KAAKC,MAAL,GAAe,KAAKhD,eAAL,IAAwB,KAAKA,eAAL,IAAwB,EAAjD,IAAyD,KAAKwF,OAAL,IAAgB,KAAKA,OAAL,CAAaF,aAA7B,IAA8C,KAAKE,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,IAAoC,EAAzJ;EACP;;EACDE,gBAAgB,GAAG;IACf,IAAIoH,cAAc,GAAG,KAAK/G,gBAAL,CAAsB,KAAKP,KAA3B,CAArB;IACA,KAAK/C,eAAL,GAAuBqK,cAAvB;;IACA,IAAI,KAAK7E,OAAL,IAAgB,KAAKA,OAAL,CAAaF,aAAjC,EAAgD;MAC5C,KAAKE,OAAL,CAAaF,aAAb,CAA2BvC,KAA3B,GAAmCsH,cAAnC;IACH;;IACD,KAAKzF,iBAAL;EACH;;EACD8B,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAK5B,qBAAV,EAAiC;MAC7B,MAAMwF,cAAc,GAAG,KAAK1M,EAAL,GAAU,KAAKA,EAAL,CAAQ0H,aAAR,CAAsBM,aAAhC,GAAgD,UAAvE;MACA,KAAKd,qBAAL,GAA6B,KAAKjH,QAAL,CAAc0M,MAAd,CAAqBD,cAArB,EAAqC,OAArC,EAA+CpG,KAAD,IAAW;QAClF,IAAIA,KAAK,CAACyE,KAAN,KAAgB,CAApB,EAAuB;UACnB;QACH;;QACD,IAAI,CAAC,KAAK5D,UAAN,IAAoB,CAAC,KAAKyF,eAAL,CAAqBtG,KAArB,CAAzB,EAAsD;UAClD,KAAKlC,IAAL;QACH;;QACD,KAAK+C,UAAL,GAAkB,KAAlB;QACA,KAAKjH,EAAL,CAAQoF,YAAR;MACH,CAT4B,CAA7B;IAUH;EACJ;;EACDsH,eAAe,CAACtG,KAAD,EAAQ;IACnB,IAAI,KAAKuG,QAAT,EAAmB;MACf,IAAIlG,MAAM,GAAGL,KAAK,CAACK,MAAnB;MACA,OAAQA,MAAM,KAAK,KAAKmG,cAAL,CAAoBpF,aAA/B,IAAgDf,MAAM,CAACoG,UAAP,KAAsB,KAAKD,cAAL,CAAoBpF,aAAlG;IACH,CAHD,MAIK;MACD,OAAO,KAAP;IACH;EACJ;;EACDsF,2BAA2B,GAAG;IAC1B,IAAI,KAAK9F,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACD6B,0BAA0B,GAAG;IACzB,KAAKkE,sBAAL,GAA8B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKJ,sBAAvC;EACH;;EACDK,4BAA4B,GAAG;IAC3B,IAAI,KAAKL,sBAAT,EAAiC;MAC7BG,MAAM,CAACG,mBAAP,CAA2B,QAA3B,EAAqC,KAAKN,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDC,cAAc,GAAG;IACb,IAAI,KAAKhL,cAAL,IAAuB,CAAChD,UAAU,CAACsO,aAAX,EAA5B,EAAwD;MACpD,KAAKpJ,IAAL;IACH;EACJ;;EACD4E,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKyE,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAItO,6BAAJ,CAAkC,KAAKuO,WAAL,CAAiBhG,aAAnD,EAAkE,MAAM;QACzF,IAAI,KAAKxF,cAAT,EAAyB;UACrB,KAAKkC,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKqJ,aAAL,CAAmBzE,kBAAnB;EACH;;EACD2E,oBAAoB,GAAG;IACnB,IAAI,KAAKF,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBE,oBAAnB;IACH;EACJ;;EACD1E,aAAa,GAAG;IACZ,KAAK+D,2BAAL;IACA,KAAKM,4BAAL;IACA,KAAKK,oBAAL;IACA,KAAKvK,OAAL,GAAe,IAAf;IACA,KAAKtB,MAAL,CAAYgF,IAAZ;EACH;;EACD8G,WAAW,GAAG;IACV,IAAI,KAAKpG,gCAAT,EAA2C;MACvCd,YAAY,CAAC,KAAKc,gCAAN,CAAZ;MACA,KAAKA,gCAAL,GAAwC,IAAxC;IACH;;IACD,IAAI,KAAKiG,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBI,OAAnB;MACA,KAAKJ,aAAL,GAAqB,IAArB;IACH;;IACD,IAAI,KAAKrK,OAAT,EAAkB;MACd9D,WAAW,CAAC4I,KAAZ,CAAkB,KAAK9E,OAAvB;IACH;;IACD,KAAK0G,oBAAL;IACA,KAAKb,aAAL;EACH;;AA9oBc;;AAgpBnBpJ,YAAY,CAACiO,IAAb;EAAA,iBAAyGjO,YAAzG,EAA+FrC,EAA/F,mBAAuIA,EAAE,CAACuQ,UAA1I,GAA+FvQ,EAA/F,mBAAiKA,EAAE,CAACwQ,SAApK,GAA+FxQ,EAA/F,mBAA0LA,EAAE,CAACyQ,iBAA7L,GAA+FzQ,EAA/F,mBAA2NA,EAAE,CAAC0Q,eAA9N,GAA+F1Q,EAA/F,mBAA0PsB,EAAE,CAACqP,aAA7P,GAA+F3Q,EAA/F,mBAAuRsB,EAAE,CAACsP,cAA1R;AAAA;;AACAvO,YAAY,CAACwO,IAAb,kBAD+F7Q,EAC/F;EAAA,MAA6FqC,YAA7F;EAAA;EAAA;IAAA;MAD+FrC,EAC/F,0BAAymEwB,aAAzmE;IAAA;;IAAA;MAAA;;MAD+FxB,EAC/F,qBAD+FA,EAC/F;IAAA;EAAA;EAAA;IAAA;MAD+FA,EAC/F;MAD+FA,EAC/F;MAD+FA,EAC/F;MAD+FA,EAC/F;MAD+FA,EAC/F;MAD+FA,EAC/F;MAD+FA,EAC/F;IAAA;;IAAA;MAAA;;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FA,EAC/F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD+FA,EAC/F,oBAAwhE,CAACkC,2BAAD,CAAxhE;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FlC,EAEvF,gCADR;MAD+FA,EAGnF,gEAFZ;MAD+FA,EAOnF,uDANZ;MAD+FA,EAQnF,uDAPZ;MAD+FA,EASnF,0DARZ;MAD+FA,EAsBnF,uDArBZ;MAD+FA,EAsBR,iEArBvF;MAD+FA,EAwBnF,4DAvBZ;MAD+FA,EAwEvF,eAvER;IAAA;;IAAA;MAD+FA,EAE4D,2BAD3J;MAD+FA,EAEtE,uBAFsEA,EAEtE,6EADzB;MAD+FA,EAG3E,aAFpB;MAD+FA,EAG3E,kCAFpB;MAD+FA,EAO/E,aANhB;MAD+FA,EAO/E,kFANhB;MAD+FA,EAQ/E,aAPhB;MAD+FA,EAQ/E,iFAPhB;MAD+FA,EAS9E,aARjB;MAD+FA,EAS9E,iCARjB;MAD+FA,EAsB/E,aArBhB;MAD+FA,EAsB/E,gCArBhB;MAD+FA,EAuBxC,aAtBvD;MAD+FA,EAuBxC,iCAtBvD;MAD+FA,EAwBtE,aAvBzB;MAD+FA,EAwBtE,uCAvBzB;IAAA;EAAA;EAAA,eAwEq+CW,EAAE,CAACmQ,OAxEx+C,EAwEmkDnQ,EAAE,CAACoQ,OAxEtkD,EAwEgsDpQ,EAAE,CAACqQ,IAxEnsD,EAwEoyDrQ,EAAE,CAACsQ,gBAxEvyD,EAwE28DtQ,EAAE,CAACuQ,OAxE98D,EAwEgiEhQ,EAAE,CAACiQ,eAxEniE,EAwEwqE7P,EAAE,CAACE,aAxE3qE,EAwE+wEJ,EAAE,CAACgQ,MAxElxE,EAwE80EpP,EAAE,CAACqP,QAxEj1E;EAAA;EAAA;EAAA;IAAA,WAwE2tF,CACntFxQ,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;MAAEuQ,OAAO,EAAE,CAAX;MAAcC,SAAS,EAAE;IAAzB,CAAD,CADY,EAEjBvQ,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAEuQ,OAAO,EAAE;IAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CAD4sF;EAxE3tF;EAAA;AAAA;;AAmFA;EAAA,mDApF+FtR,EAoF/F,mBAA2FqC,YAA3F,EAAqH,CAAC;IAC1Ga,IAAI,EAAE/C,SADoG;IAE1GqR,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAZ;MAA8BtK,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAxEmB;MAwEZuK,UAAU,EAAE,CACK7Q,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;QAAEuQ,OAAO,EAAE,CAAX;QAAcC,SAAS,EAAE;MAAzB,CAAD,CADY,EAEjBvQ,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAEuQ,OAAO,EAAE;MAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADZ,CAxEA;MAkFIK,IAAI,EAAE;QACL,SAAS,0BADJ;QAEL,iCAAiC,QAF5B;QAGL,gCAAgC,wCAH3B;QAIL,oCAAoC;MAJ/B,CAlFV;MAuFIC,SAAS,EAAE,CAAC1P,2BAAD,CAvFf;MAuF8C2P,eAAe,EAAEzR,uBAAuB,CAAC0R,MAvFvF;MAuF+FC,aAAa,EAAE1R,iBAAiB,CAAC2R,IAvFhI;MAuFsIC,MAAM,EAAE,CAAC,w5CAAD;IAvF9I,CAAD;EAFoG,CAAD,CAArH,EA0F4B,YAAY;IAAE,OAAO,CAAC;MAAE/O,IAAI,EAAElD,EAAE,CAACuQ;IAAX,CAAD,EAA0B;MAAErN,IAAI,EAAElD,EAAE,CAACwQ;IAAX,CAA1B,EAAkD;MAAEtN,IAAI,EAAElD,EAAE,CAACyQ;IAAX,CAAlD,EAAkF;MAAEvN,IAAI,EAAElD,EAAE,CAAC0Q;IAAX,CAAlF,EAAgH;MAAExN,IAAI,EAAE5B,EAAE,CAACqP;IAAX,CAAhH,EAA4I;MAAEzN,IAAI,EAAE5B,EAAE,CAACsP;IAAX,CAA5I,CAAP;EAAkL,CA1F5N,EA0F8O;IAAE9N,SAAS,EAAE,CAAC;MAC5OI,IAAI,EAAE5C;IADsO,CAAD,CAAb;IAE9NyC,KAAK,EAAE,CAAC;MACRG,IAAI,EAAE5C;IADE,CAAD,CAFuN;IAI9NS,KAAK,EAAE,CAAC;MACRmC,IAAI,EAAE5C;IADE,CAAD,CAJuN;IAM9N4R,UAAU,EAAE,CAAC;MACbhP,IAAI,EAAE5C;IADO,CAAD,CANkN;IAQ9N6R,UAAU,EAAE,CAAC;MACbjP,IAAI,EAAE5C;IADO,CAAD,CARkN;IAU9N8R,eAAe,EAAE,CAAC;MAClBlP,IAAI,EAAE5C;IADY,CAAD,CAV6M;IAY9N+R,UAAU,EAAE,CAAC;MACbnP,IAAI,EAAE5C;IADO,CAAD,CAZkN;IAc9NgS,OAAO,EAAE,CAAC;MACVpP,IAAI,EAAE5C;IADI,CAAD,CAdqN;IAgB9NiS,eAAe,EAAE,CAAC;MAClBrP,IAAI,EAAE5C;IADY,CAAD,CAhB6M;IAkB9NkS,WAAW,EAAE,CAAC;MACdtP,IAAI,EAAE5C;IADQ,CAAD,CAlBiN;IAoB9NmS,QAAQ,EAAE,CAAC;MACXvP,IAAI,EAAE5C;IADK,CAAD,CApBoN;IAsB9NsI,QAAQ,EAAE,CAAC;MACX1F,IAAI,EAAE5C;IADK,CAAD,CAtBoN;IAwB9N0C,YAAY,EAAE,CAAC;MACfE,IAAI,EAAE5C;IADS,CAAD,CAxBgN;IA0B9N2C,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE5C;IADC,CAAD,CA1BwN;IA4B9NyK,aAAa,EAAE,CAAC;MAChB7H,IAAI,EAAE5C;IADU,CAAD,CA5B+M;IA8B9NoS,qBAAqB,EAAE,CAAC;MACxBxP,IAAI,EAAE5C;IADkB,CAAD,CA9BuM;IAgC9NqS,oBAAoB,EAAE,CAAC;MACvBzP,IAAI,EAAE5C;IADiB,CAAD,CAhCwM;IAkC9NsS,SAAS,EAAE,CAAC;MACZ1P,IAAI,EAAE5C;IADM,CAAD,CAlCmN;IAoC9NuS,IAAI,EAAE,CAAC;MACP3P,IAAI,EAAE5C;IADC,CAAD,CApCwN;IAsC9NwS,QAAQ,EAAE,CAAC;MACX5P,IAAI,EAAE5C;IADK,CAAD,CAtCoN;IAwC9NyS,IAAI,EAAE,CAAC;MACP7P,IAAI,EAAE5C;IADC,CAAD,CAxCwN;IA0C9NuL,QAAQ,EAAE,CAAC;MACX3I,IAAI,EAAE5C;IADK,CAAD,CA1CoN;IA4C9NoG,aAAa,EAAE,CAAC;MAChBxD,IAAI,EAAE5C;IADU,CAAD,CA5C+M;IA8C9N+I,cAAc,EAAE,CAAC;MACjBnG,IAAI,EAAE5C;IADW,CAAD,CA9C8M;IAgD9N4C,IAAI,EAAE,CAAC;MACPA,IAAI,EAAE5C;IADC,CAAD,CAhDwN;IAkD9N6C,UAAU,EAAE,CAAC;MACbD,IAAI,EAAE5C;IADO,CAAD,CAlDkN;IAoD9N8C,UAAU,EAAE,CAAC;MACbF,IAAI,EAAE5C;IADO,CAAD,CApDkN;IAsD9N0S,SAAS,EAAE,CAAC;MACZ9P,IAAI,EAAE5C;IADM,CAAD,CAtDmN;IAwD9N2S,iBAAiB,EAAE,CAAC;MACpB/P,IAAI,EAAE5C;IADc,CAAD,CAxD2M;IA0D9N4S,cAAc,EAAE,CAAC;MACjBhQ,IAAI,EAAE5C;IADW,CAAD,CA1D8M;IA4D9N+C,YAAY,EAAE,CAAC;MACfH,IAAI,EAAE5C;IADS,CAAD,CA5DgN;IA8D9NgD,MAAM,EAAE,CAAC;MACTJ,IAAI,EAAE5C;IADG,CAAD,CA9DsN;IAgE9NkN,KAAK,EAAE,CAAC;MACRtK,IAAI,EAAE5C;IADE,CAAD,CAhEuN;IAkE9NiD,eAAe,EAAE,CAAC;MAClBL,IAAI,EAAE5C;IADY,CAAD,CAlE6M;IAoE9NkD,SAAS,EAAE,CAAC;MACZN,IAAI,EAAE5C;IADM,CAAD,CApEmN;IAsE9N+L,KAAK,EAAE,CAAC;MACRnJ,IAAI,EAAE5C;IADE,CAAD,CAtEuN;IAwE9N+O,QAAQ,EAAE,CAAC;MACXnM,IAAI,EAAE5C;IADK,CAAD,CAxEoN;IA0E9NqG,gBAAgB,EAAE,CAAC;MACnBzD,IAAI,EAAE5C;IADa,CAAD,CA1E4M;IA4E9NmD,YAAY,EAAE,CAAC;MACfP,IAAI,EAAE5C;IADS,CAAD,CA5EgN;IA8E9N8I,QAAQ,EAAE,CAAC;MACXlG,IAAI,EAAE5C;IADK,CAAD,CA9EoN;IAgF9N6S,QAAQ,EAAE,CAAC;MACXjQ,IAAI,EAAE5C;IADK,CAAD,CAhFoN;IAkF9NyO,OAAO,EAAE,CAAC;MACV7L,IAAI,EAAE5C;IADI,CAAD,CAlFqN;IAoF9NuM,YAAY,EAAE,CAAC;MACf3J,IAAI,EAAE5C;IADS,CAAD,CApFgN;IAsF9NoD,qBAAqB,EAAE,CAAC;MACxBR,IAAI,EAAE5C;IADkB,CAAD,CAtFuM;IAwF9NqD,qBAAqB,EAAE,CAAC;MACxBT,IAAI,EAAE5C;IADkB,CAAD,CAxFuM;IA0F9N8S,SAAS,EAAE,CAAC;MACZlQ,IAAI,EAAE5C;IADM,CAAD,CA1FmN;IA4F9NsD,YAAY,EAAE,CAAC;MACfV,IAAI,EAAE5C;IADS,CAAD,CA5FgN;IA8F9N2H,mBAAmB,EAAE,CAAC;MACtB/E,IAAI,EAAE5C;IADgB,CAAD,CA9FyM;IAgG9N+H,gBAAgB,EAAE,CAAC;MACnBnF,IAAI,EAAE5C;IADa,CAAD,CAhG4M;IAkG9N4P,WAAW,EAAE,CAAC;MACdhN,IAAI,EAAE3C,SADQ;MAEdiR,IAAI,EAAE,CAAC,WAAD;IAFQ,CAAD,CAlGiN;IAqG9NpH,OAAO,EAAE,CAAC;MACVlH,IAAI,EAAE3C,SADI;MAEViR,IAAI,EAAE,CAAC,IAAD;IAFI,CAAD,CArGqN;IAwG9NvH,YAAY,EAAE,CAAC;MACf/G,IAAI,EAAE3C,SADS;MAEfiR,IAAI,EAAE,CAAC,SAAD;IAFS,CAAD,CAxGgN;IA2G9NhF,gBAAgB,EAAE,CAAC;MACnBtJ,IAAI,EAAE3C,SADa;MAEnBiR,IAAI,EAAE,CAAC,gBAAD;IAFa,CAAD,CA3G4M;IA8G9NlC,cAAc,EAAE,CAAC;MACjBpM,IAAI,EAAE3C,SADW;MAEjBiR,IAAI,EAAE,CAAC,OAAD;IAFW,CAAD,CA9G8M;IAiH9NtG,cAAc,EAAE,CAAC;MACjBhI,IAAI,EAAE3C,SADW;MAEjBiR,IAAI,EAAE,CAAC,OAAD;IAFW,CAAD,CAjH8M;IAoH9NxG,QAAQ,EAAE,CAAC;MACX9H,IAAI,EAAE3C,SADK;MAEXiR,IAAI,EAAE,CAAC,UAAD;IAFK,CAAD,CApHoN;IAuH9N1K,SAAS,EAAE,CAAC;MACZ5D,IAAI,EAAE1C,eADM;MAEZgR,IAAI,EAAE,CAAChQ,aAAD;IAFM,CAAD,CAvHmN;IA0H9NqC,cAAc,EAAE,CAAC;MACjBX,IAAI,EAAEzC;IADW,CAAD,CA1H8M;IA4H9NqD,QAAQ,EAAE,CAAC;MACXZ,IAAI,EAAEzC;IADK,CAAD,CA5HoN;IA8H9NsD,UAAU,EAAE,CAAC;MACbb,IAAI,EAAEzC;IADO,CAAD,CA9HkN;IAgI9NuD,OAAO,EAAE,CAAC;MACVd,IAAI,EAAEzC;IADI,CAAD,CAhIqN;IAkI9NwD,MAAM,EAAE,CAAC;MACTf,IAAI,EAAEzC;IADG,CAAD,CAlIsN;IAoI9NyD,eAAe,EAAE,CAAC;MAClBhB,IAAI,EAAEzC;IADY,CAAD,CApI6M;IAsI9N0D,OAAO,EAAE,CAAC;MACVjB,IAAI,EAAEzC;IADI,CAAD,CAtIqN;IAwI9N2D,OAAO,EAAE,CAAC;MACVlB,IAAI,EAAEzC;IADI,CAAD,CAxIqN;IA0I9N4D,MAAM,EAAE,CAAC;MACTnB,IAAI,EAAEzC;IADG,CAAD,CA1IsN;IA4I9N6D,MAAM,EAAE,CAAC;MACTpB,IAAI,EAAEzC;IADG,CAAD,CA5IsN;IA8I9N8D,UAAU,EAAE,CAAC;MACbrB,IAAI,EAAEzC;IADO,CAAD,CA9IkN;IAgJ9NyE,QAAQ,EAAE,CAAC;MACXhC,IAAI,EAAE5C;IADK,CAAD,CAhJoN;IAkJ9NiF,WAAW,EAAE,CAAC;MACdrC,IAAI,EAAE5C;IADQ,CAAD;EAlJiN,CA1F9O;AAAA;;AA+OA,MAAM+S,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAAC/C,IAAnB;EAAA,iBAA+G+C,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBAtU+FtT,EAsU/F;EAAA,MAAgHqT;AAAhH;AACAA,kBAAkB,CAACE,IAAnB,kBAvU+FvT,EAuU/F;EAAA,UAA8IY,YAA9I,EAA4JK,eAA5J,EAA6KE,YAA7K,EAA2LM,YAA3L,EAAyMJ,YAAzM,EAAuNY,cAAvN,EAAuOR,YAAvO,EAAqPQ,cAArP;AAAA;;AACA;EAAA,mDAxU+FjC,EAwU/F,mBAA2FqT,kBAA3F,EAA2H,CAAC;IAChHnQ,IAAI,EAAExC,QAD0G;IAEhH8Q,IAAI,EAAE,CAAC;MACCgC,OAAO,EAAE,CAAC5S,YAAD,EAAeK,eAAf,EAAgCE,YAAhC,EAA8CM,YAA9C,EAA4DJ,YAA5D,EAA0EY,cAA1E,CADV;MAECwR,OAAO,EAAE,CAACpR,YAAD,EAAeZ,YAAf,EAA6BQ,cAA7B,CAFV;MAGCyR,YAAY,EAAE,CAACrR,YAAD;IAHf,CAAD;EAF0G,CAAD,CAA3H;AAAA;AASA;AACA;AACA;;;AAEA,SAASH,2BAAT,EAAsCG,YAAtC,EAAoDgR,kBAApD"}, "metadata": {}, "sourceType": "module"}