{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector = identity) {\n  comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n  return operate((source, subscriber) => {\n    let previousKey;\n    let first = true;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const currentKey = keySelector(value);\n\n      if (first || !comparator(previousKey, currentKey)) {\n        first = false;\n        previousKey = currentKey;\n        subscriber.next(value);\n      }\n    }));\n  });\n}\n\nfunction defaultCompare(a, b) {\n  return a === b;\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "distinctUntilChanged", "comparator", "keySelector", "defaultCompare", "source", "subscriber", "previousKey", "first", "subscribe", "value", "current<PERSON><PERSON>", "next", "a", "b"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/distinctUntilChanged.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function distinctUntilChanged(comparator, keySelector = identity) {\n    comparator = comparator !== null && comparator !== void 0 ? comparator : defaultCompare;\n    return operate((source, subscriber) => {\n        let previousKey;\n        let first = true;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const currentKey = keySelector(value);\n            if (first || !comparator(previousKey, currentKey)) {\n                first = false;\n                previousKey = currentKey;\n                subscriber.next(value);\n            }\n        }));\n    });\n}\nfunction defaultCompare(a, b) {\n    return a === b;\n}\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,kBAAzB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,oBAAT,CAA8BC,UAA9B,EAA0CC,WAAW,GAAGL,QAAxD,EAAkE;EACrEI,UAAU,GAAGA,UAAU,KAAK,IAAf,IAAuBA,UAAU,KAAK,KAAK,CAA3C,GAA+CA,UAA/C,GAA4DE,cAAzE;EACA,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,WAAJ;IACA,IAAIC,KAAK,GAAG,IAAZ;IACAH,MAAM,CAACI,SAAP,CAAiBT,wBAAwB,CAACM,UAAD,EAAcI,KAAD,IAAW;MAC7D,MAAMC,UAAU,GAAGR,WAAW,CAACO,KAAD,CAA9B;;MACA,IAAIF,KAAK,IAAI,CAACN,UAAU,CAACK,WAAD,EAAcI,UAAd,CAAxB,EAAmD;QAC/CH,KAAK,GAAG,KAAR;QACAD,WAAW,GAAGI,UAAd;QACAL,UAAU,CAACM,IAAX,CAAgBF,KAAhB;MACH;IACJ,CAPwC,CAAzC;EAQH,CAXa,CAAd;AAYH;;AACD,SAASN,cAAT,CAAwBS,CAAxB,EAA2BC,CAA3B,EAA8B;EAC1B,OAAOD,CAAC,KAAKC,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}