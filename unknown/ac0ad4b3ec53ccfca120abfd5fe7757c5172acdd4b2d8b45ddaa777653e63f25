{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let completions$;\n    let isNotifierComplete = false;\n    let isMainComplete = false;\n\n    const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n\n    const getCompletionSubject = () => {\n      if (!completions$) {\n        completions$ = new Subject();\n        notifier(completions$).subscribe(createOperatorSubscriber(subscriber, () => {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, () => {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n\n      return completions$;\n    };\n\n    const subscribeForRepeatWhen = () => {\n      isMainComplete = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n\n    subscribeForRepeatWhen();\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "repeatWhen", "notifier", "source", "subscriber", "innerSub", "syncResub", "completions$", "isNotifierComplete", "isMainComplete", "checkComplete", "complete", "getCompletionSubject", "subscribe", "subscribeForRepeatWhen", "undefined", "next", "unsubscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/repeatWhen.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n    return operate((source, subscriber) => {\n        let innerSub;\n        let syncResub = false;\n        let completions$;\n        let isNotifierComplete = false;\n        let isMainComplete = false;\n        const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n        const getCompletionSubject = () => {\n            if (!completions$) {\n                completions$ = new Subject();\n                notifier(completions$).subscribe(createOperatorSubscriber(subscriber, () => {\n                    if (innerSub) {\n                        subscribeForRepeatWhen();\n                    }\n                    else {\n                        syncResub = true;\n                    }\n                }, () => {\n                    isNotifierComplete = true;\n                    checkComplete();\n                }));\n            }\n            return completions$;\n        };\n        const subscribeForRepeatWhen = () => {\n            isMainComplete = false;\n            innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n                isMainComplete = true;\n                !checkComplete() && getCompletionSubject().next();\n            }));\n            if (syncResub) {\n                innerSub.unsubscribe();\n                innerSub = null;\n                syncResub = false;\n                subscribeForRepeatWhen();\n            }\n        };\n        subscribeForRepeatWhen();\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,UAAT,CAAoBC,QAApB,EAA8B;EACjC,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAJ;IACA,IAAIC,SAAS,GAAG,KAAhB;IACA,IAAIC,YAAJ;IACA,IAAIC,kBAAkB,GAAG,KAAzB;IACA,IAAIC,cAAc,GAAG,KAArB;;IACA,MAAMC,aAAa,GAAG,MAAMD,cAAc,IAAID,kBAAlB,KAAyCJ,UAAU,CAACO,QAAX,IAAuB,IAAhE,CAA5B;;IACA,MAAMC,oBAAoB,GAAG,MAAM;MAC/B,IAAI,CAACL,YAAL,EAAmB;QACfA,YAAY,GAAG,IAAIT,OAAJ,EAAf;QACAI,QAAQ,CAACK,YAAD,CAAR,CAAuBM,SAAvB,CAAiCb,wBAAwB,CAACI,UAAD,EAAa,MAAM;UACxE,IAAIC,QAAJ,EAAc;YACVS,sBAAsB;UACzB,CAFD,MAGK;YACDR,SAAS,GAAG,IAAZ;UACH;QACJ,CAPwD,EAOtD,MAAM;UACLE,kBAAkB,GAAG,IAArB;UACAE,aAAa;QAChB,CAVwD,CAAzD;MAWH;;MACD,OAAOH,YAAP;IACH,CAhBD;;IAiBA,MAAMO,sBAAsB,GAAG,MAAM;MACjCL,cAAc,GAAG,KAAjB;MACAJ,QAAQ,GAAGF,MAAM,CAACU,SAAP,CAAiBb,wBAAwB,CAACI,UAAD,EAAaW,SAAb,EAAwB,MAAM;QAC9EN,cAAc,GAAG,IAAjB;QACA,CAACC,aAAa,EAAd,IAAoBE,oBAAoB,GAAGI,IAAvB,EAApB;MACH,CAHmD,CAAzC,CAAX;;MAIA,IAAIV,SAAJ,EAAe;QACXD,QAAQ,CAACY,WAAT;QACAZ,QAAQ,GAAG,IAAX;QACAC,SAAS,GAAG,KAAZ;QACAQ,sBAAsB;MACzB;IACJ,CAZD;;IAaAA,sBAAsB;EACzB,CAtCa,CAAd;AAuCH"}, "metadata": {}, "sourceType": "module"}