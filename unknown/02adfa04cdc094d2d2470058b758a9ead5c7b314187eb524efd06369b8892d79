{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      subscriber.next(project.call(thisArg, value, index++));\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "map", "project", "thisArg", "source", "subscriber", "index", "subscribe", "value", "next", "call"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/map.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function map(project, thisArg) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            subscriber.next(project.call(thisArg, value, index++));\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,GAAT,CAAaC,OAAb,EAAsBC,OAAtB,EAA+B;EAClC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAG,CAAZ;IACAF,MAAM,CAACG,SAAP,CAAiBP,wBAAwB,CAACK,UAAD,EAAcG,KAAD,IAAW;MAC7DH,UAAU,CAACI,IAAX,CAAgBP,OAAO,CAACQ,IAAR,CAAaP,OAAb,EAAsBK,KAAtB,EAA6BF,KAAK,EAAlC,CAAhB;IACH,CAFwC,CAAzC;EAGH,CALa,CAAd;AAMH"}, "metadata": {}, "sourceType": "module"}