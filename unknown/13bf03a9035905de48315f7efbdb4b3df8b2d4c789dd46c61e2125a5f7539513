{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nconst _c0 = [\"*\"];\n\nclass AvatarGroup {}\n\nAvatarGroup.ɵfac = function AvatarGroup_Factory(t) {\n  return new (t || AvatarGroup)();\n};\n\nAvatarGroup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: AvatarGroup,\n  selectors: [[\"p-avatarGroup\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    styleClass: \"styleClass\",\n    style: \"style\"\n  },\n  ngContentSelectors: _c0,\n  decls: 2,\n  vars: 4,\n  consts: [[3, \"ngClass\", \"ngStyle\"]],\n  template: function AvatarGroup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-avatar-group p-component\")(\"ngStyle\", ctx.style);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgStyle],\n  styles: [\".p-avatar-group p-avatar+p-avatar{margin-left:-1rem}.p-avatar-group{display:flex;align-items:center}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarGroup, [{\n    type: Component,\n    args: [{\n      selector: 'p-avatarGroup',\n      template: `\n        <div [ngClass]=\"'p-avatar-group p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-avatar-group p-avatar+p-avatar{margin-left:-1rem}.p-avatar-group{display:flex;align-items:center}\\n\"]\n    }]\n  }], null, {\n    styleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }]\n  });\n})();\n\nclass AvatarGroupModule {}\n\nAvatarGroupModule.ɵfac = function AvatarGroupModule_Factory(t) {\n  return new (t || AvatarGroupModule)();\n};\n\nAvatarGroupModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AvatarGroupModule\n});\nAvatarGroupModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarGroupModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [AvatarGroup],\n      declarations: [AvatarGroup]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { AvatarGroup, AvatarGroupModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "AvatarGroup", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "style", "AvatarGroupModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-avatargroup.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass AvatarGroup {\n}\nAvatarGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroup, deps: [], target: i0.ɵɵFactoryTarget.Component });\nAvatarGroup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: AvatarGroup, selector: \"p-avatarGroup\", inputs: { styleClass: \"styleClass\", style: \"style\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"'p-avatar-group p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, styles: [\".p-avatar-group p-avatar+p-avatar{margin-left:-1rem}.p-avatar-group{display:flex;align-items:center}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-avatarGroup', template: `\n        <div [ngClass]=\"'p-avatar-group p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-avatar-group p-avatar+p-avatar{margin-left:-1rem}.p-avatar-group{display:flex;align-items:center}\\n\"] }]\n        }], propDecorators: { styleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }] } });\nclass AvatarGroupModule {\n}\nAvatarGroupModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroupModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAvatarGroupModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroupModule, declarations: [AvatarGroup], imports: [CommonModule], exports: [AvatarGroup] });\nAvatarGroupModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroupModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarGroupModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [AvatarGroup],\n                    declarations: [AvatarGroup]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AvatarGroup, AvatarGroupModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;AAEA,MAAMC,WAAN,CAAkB;;AAElBA,WAAW,CAACC,IAAZ;EAAA,iBAAwGD,WAAxG;AAAA;;AACAA,WAAW,CAACE,IAAZ,kBAD8FV,EAC9F;EAAA,MAA4FQ,WAA5F;EAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD8FR,EAC9F;MAD8FA,EAEtF,4BADR;MAD8FA,EAGlF,gBAFZ;MAD8FA,EAItF,eAHR;IAAA;;IAAA;MAD8FA,EAExC,2BADtD;MAD8FA,EAEjF,0EADb;IAAA;EAAA;EAAA,eAIqLM,EAAE,CAACK,OAJxL,EAImRL,EAAE,CAACM,OAJtR;EAAA;EAAA;EAAA;AAAA;;AAKA;EAAA,mDAN8FZ,EAM9F,mBAA2FQ,WAA3F,EAAoH,CAAC;IACzGK,IAAI,EAAEZ,SADmG;IAEzGa,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6BC,QAAQ,EAAG;AAC3D;AACA;AACA;AACA,KAJmB;MAIZC,eAAe,EAAEf,uBAAuB,CAACgB,MAJ7B;MAIqCC,aAAa,EAAEhB,iBAAiB,CAACiB,IAJtE;MAI4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAJlF;MAMIC,MAAM,EAAE,CAAC,wGAAD;IANZ,CAAD;EAFmG,CAAD,CAApH,QAS4B;IAAEC,UAAU,EAAE,CAAC;MAC3BV,IAAI,EAAET;IADqB,CAAD,CAAd;IAEZoB,KAAK,EAAE,CAAC;MACRX,IAAI,EAAET;IADE,CAAD;EAFK,CAT5B;AAAA;;AAcA,MAAMqB,iBAAN,CAAwB;;AAExBA,iBAAiB,CAAChB,IAAlB;EAAA,iBAA8GgB,iBAA9G;AAAA;;AACAA,iBAAiB,CAACC,IAAlB,kBAvB8F1B,EAuB9F;EAAA,MAA+GyB;AAA/G;AACAA,iBAAiB,CAACE,IAAlB,kBAxB8F3B,EAwB9F;EAAA,UAA4IO,YAA5I;AAAA;;AACA;EAAA,mDAzB8FP,EAyB9F,mBAA2FyB,iBAA3F,EAA0H,CAAC;IAC/GZ,IAAI,EAAER,QADyG;IAE/GS,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAACrB,YAAD,CADV;MAECsB,OAAO,EAAE,CAACrB,WAAD,CAFV;MAGCsB,YAAY,EAAE,CAACtB,WAAD;IAHf,CAAD;EAFyG,CAAD,CAA1H;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,WAAT,EAAsBiB,iBAAtB"}, "metadata": {}, "sourceType": "module"}