{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { UniqueComponentId } from 'primeng/utils';\nconst _c0 = [\"itemsContainer\"];\n\nfunction Carousel_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Carousel_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Carousel_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\n\nconst _c1 = function (a1) {\n  return {\n    \"p-carousel-prev p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c2 = function (a1, a2) {\n  return {\n    \"p-carousel-prev-icon pi\": true,\n    \"pi-chevron-left\": a1,\n    \"pi-chevron-up\": a2\n  };\n};\n\nfunction Carousel_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function Carousel_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.navBackward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c1, ctx_r1.isBackwardNavDisabled()))(\"disabled\", ctx_r1.isBackwardNavDisabled());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c2, !ctx_r1.isVertical(), ctx_r1.isVertical()));\n  }\n}\n\nfunction Carousel_div_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c3 = function (a1, a2, a3) {\n  return {\n    \"p-carousel-item p-carousel-item-cloned\": true,\n    \"p-carousel-item-active\": a1,\n    \"p-carousel-item-start\": a2,\n    \"p-carousel-item-end\": a3\n  };\n};\n\nconst _c4 = function (a0) {\n  return {\n    $implicit: a0\n  };\n};\n\nfunction Carousel_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, Carousel_div_8_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const index_r13 = ctx.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c3, ctx_r3.totalShiftedItems * -1 === ctx_r3.value.length, 0 === index_r13, ctx_r3.clonedItemsForStarting.length - 1 === index_r13));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c4, item_r12));\n  }\n}\n\nfunction Carousel_div_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c5 = function (a1, a2, a3) {\n  return {\n    \"p-carousel-item\": true,\n    \"p-carousel-item-active\": a1,\n    \"p-carousel-item-start\": a2,\n    \"p-carousel-item-end\": a3\n  };\n};\n\nfunction Carousel_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, Carousel_div_9_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    const index_r16 = ctx.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c5, ctx_r4.firstIndex() <= index_r16 && ctx_r4.lastIndex() >= index_r16, ctx_r4.firstIndex() === index_r16, ctx_r4.lastIndex() === index_r16));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c4, item_r15));\n  }\n}\n\nfunction Carousel_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Carousel_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, Carousel_div_10_ng_container_1_Template, 1, 0, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r18 = ctx.$implicit;\n    const index_r19 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c3, ctx_r5.totalShiftedItems * -1 === ctx_r5.numVisible, 0 === index_r19, ctx_r5.clonedItemsForFinishing.length - 1 === index_r19));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r5.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(7, _c4, item_r18));\n  }\n}\n\nconst _c6 = function (a1) {\n  return {\n    \"p-carousel-next p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c7 = function (a1, a2) {\n  return {\n    \"p-carousel-prev-icon pi\": true,\n    \"pi-chevron-right\": a1,\n    \"pi-chevron-down\": a2\n  };\n};\n\nfunction Carousel_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function Carousel_button_11_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.navForward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c6, ctx_r6.isForwardNavDisabled()))(\"disabled\", ctx_r6.isForwardNavDisabled());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c7, !ctx_r6.isVertical(), ctx_r6.isVertical()));\n  }\n}\n\nconst _c8 = function (a1) {\n  return {\n    \"p-carousel-indicator\": true,\n    \"p-highlight\": a1\n  };\n};\n\nfunction Carousel_ul_12_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 2)(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Carousel_ul_12_li_1_Template_button_click_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const i_r25 = restoredCtx.index;\n      const ctx_r26 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r26.onDotClick($event, i_r25));\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const i_r25 = ctx.index;\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c8, ctx_r23._page === i_r25));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r23.indicatorStyleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-link\")(\"ngStyle\", ctx_r23.indicatorStyle);\n  }\n}\n\nfunction Carousel_ul_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 0);\n    i0.ɵɵtemplate(1, Carousel_ul_12_li_1_Template, 2, 7, \"li\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r7.indicatorsContentClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-carousel-indicators p-reset\")(\"ngStyle\", ctx_r7.indicatorsContentStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.totalDotsArray());\n  }\n}\n\nfunction Carousel_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Carousel_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵtemplate(2, Carousel_div_13_ng_container_2_Template, 1, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r8.footerTemplate);\n  }\n}\n\nconst _c9 = [[[\"p-header\"]], [[\"p-footer\"]]];\n\nconst _c10 = function (a1, a2) {\n  return {\n    \"p-carousel p-component\": true,\n    \"p-carousel-vertical\": a1,\n    \"p-carousel-horizontal\": a2\n  };\n};\n\nconst _c11 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nconst _c12 = [\"p-header\", \"p-footer\"];\n\nclass Carousel {\n  constructor(el, zone, cd) {\n    this.el = el;\n    this.zone = zone;\n    this.cd = cd;\n    this.orientation = \"horizontal\";\n    this.verticalViewPortHeight = \"300px\";\n    this.contentClass = \"\";\n    this.indicatorsContentClass = \"\";\n    this.indicatorStyleClass = \"\";\n    this.circular = false;\n    this.showIndicators = true;\n    this.showNavigators = true;\n    this.autoplayInterval = 0;\n    this.onPage = new EventEmitter();\n    this._numVisible = 1;\n    this._numScroll = 1;\n    this._oldNumScroll = 0;\n    this.prevState = {\n      numScroll: 0,\n      numVisible: 0,\n      value: []\n    };\n    this.defaultNumScroll = 1;\n    this.defaultNumVisible = 1;\n    this._page = 0;\n    this.isRemainingItemsAdded = false;\n    this.remainingItems = 0;\n    this.swipeThreshold = 20;\n    this.totalShiftedItems = this.page * this.numScroll * -1;\n  }\n\n  get page() {\n    return this._page;\n  }\n\n  set page(val) {\n    if (this.isCreated && val !== this._page) {\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n        this.allowAutoplay = false;\n      }\n\n      if (val > this._page && val <= this.totalDots() - 1) {\n        this.step(-1, val);\n      } else if (val < this._page) {\n        this.step(1, val);\n      }\n    }\n\n    this._page = val;\n  }\n\n  get numVisible() {\n    return this._numVisible;\n  }\n\n  set numVisible(val) {\n    this._numVisible = val;\n  }\n\n  get numScroll() {\n    return this._numVisible;\n  }\n\n  set numScroll(val) {\n    this._numScroll = val;\n  }\n\n  get value() {\n    return this._value;\n  }\n\n  set value(val) {\n    this._value = val;\n  }\n\n  ngOnChanges(simpleChange) {\n    if (simpleChange.value) {\n      if (this.circular && this._value) {\n        this.setCloneItems();\n      }\n    }\n\n    if (this.isCreated) {\n      if (simpleChange.numVisible) {\n        if (this.responsiveOptions) {\n          this.defaultNumVisible = this.numVisible;\n        }\n\n        if (this.isCircular()) {\n          this.setCloneItems();\n        }\n\n        this.createStyle();\n        this.calculatePosition();\n      }\n\n      if (simpleChange.numScroll) {\n        if (this.responsiveOptions) {\n          this.defaultNumScroll = this.numScroll;\n        }\n      }\n    }\n  }\n\n  ngAfterContentInit() {\n    this.id = UniqueComponentId();\n    this.allowAutoplay = !!this.autoplayInterval;\n\n    if (this.circular) {\n      this.setCloneItems();\n    }\n\n    if (this.responsiveOptions) {\n      this.defaultNumScroll = this._numScroll;\n      this.defaultNumVisible = this._numVisible;\n    }\n\n    this.createStyle();\n    this.calculatePosition();\n\n    if (this.responsiveOptions) {\n      this.bindDocumentListeners();\n    }\n\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'footer':\n          this.footerTemplate = item.template;\n          break;\n\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  ngAfterContentChecked() {\n    const isCircular = this.isCircular();\n    let totalShiftedItems = this.totalShiftedItems;\n\n    if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n      if (this.autoplayInterval) {\n        this.stopAutoplay();\n      }\n\n      this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n      let page = this._page;\n\n      if (this.totalDots() !== 0 && page >= this.totalDots()) {\n        page = this.totalDots() - 1;\n        this._page = page;\n        this.onPage.emit({\n          page: this.page\n        });\n      }\n\n      totalShiftedItems = page * this._numScroll * -1;\n\n      if (isCircular) {\n        totalShiftedItems -= this._numVisible;\n      }\n\n      if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n        totalShiftedItems += -1 * this.remainingItems + this._numScroll;\n        this.isRemainingItemsAdded = true;\n      } else {\n        this.isRemainingItemsAdded = false;\n      }\n\n      if (totalShiftedItems !== this.totalShiftedItems) {\n        this.totalShiftedItems = totalShiftedItems;\n      }\n\n      this._oldNumScroll = this._numScroll;\n      this.prevState.numScroll = this._numScroll;\n      this.prevState.numVisible = this._numVisible;\n      this.prevState.value = [...this._value];\n\n      if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      }\n\n      this.isCreated = true;\n\n      if (this.autoplayInterval && this.isAutoplay()) {\n        this.startAutoplay();\n      }\n    }\n\n    if (isCircular) {\n      if (this.page === 0) {\n        totalShiftedItems = -1 * this._numVisible;\n      } else if (totalShiftedItems === 0) {\n        totalShiftedItems = -1 * this.value.length;\n\n        if (this.remainingItems > 0) {\n          this.isRemainingItemsAdded = true;\n        }\n      }\n\n      if (totalShiftedItems !== this.totalShiftedItems) {\n        this.totalShiftedItems = totalShiftedItems;\n      }\n    }\n  }\n\n  createStyle() {\n    if (!this.carouselStyle) {\n      this.carouselStyle = document.createElement('style');\n      this.carouselStyle.type = 'text/css';\n      document.body.appendChild(this.carouselStyle);\n    }\n\n    let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${100 / this.numVisible}%\n\t\t\t}\n        `;\n\n    if (this.responsiveOptions) {\n      this.responsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n\n      for (let i = 0; i < this.responsiveOptions.length; i++) {\n        let res = this.responsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n\n    this.carouselStyle.innerHTML = innerHTML;\n  }\n\n  calculatePosition() {\n    if (this.responsiveOptions) {\n      let windowWidth = window.innerWidth;\n      let matchedResponsiveData = {\n        numVisible: this.defaultNumVisible,\n        numScroll: this.defaultNumScroll\n      };\n\n      for (let i = 0; i < this.responsiveOptions.length; i++) {\n        let res = this.responsiveOptions[i];\n\n        if (parseInt(res.breakpoint, 10) >= windowWidth) {\n          matchedResponsiveData = res;\n        }\n      }\n\n      if (this._numScroll !== matchedResponsiveData.numScroll) {\n        let page = this._page;\n        page = Math.floor(page * this._numScroll / matchedResponsiveData.numScroll);\n        let totalShiftedItems = matchedResponsiveData.numScroll * this.page * -1;\n\n        if (this.isCircular()) {\n          totalShiftedItems -= matchedResponsiveData.numVisible;\n        }\n\n        this.totalShiftedItems = totalShiftedItems;\n        this._numScroll = matchedResponsiveData.numScroll;\n        this._page = page;\n        this.onPage.emit({\n          page: this.page\n        });\n      }\n\n      if (this._numVisible !== matchedResponsiveData.numVisible) {\n        this._numVisible = matchedResponsiveData.numVisible;\n        this.setCloneItems();\n      }\n\n      this.cd.markForCheck();\n    }\n  }\n\n  setCloneItems() {\n    this.clonedItemsForStarting = [];\n    this.clonedItemsForFinishing = [];\n\n    if (this.isCircular()) {\n      this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n      this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n    }\n  }\n\n  firstIndex() {\n    return this.isCircular() ? -1 * (this.totalShiftedItems + this.numVisible) : this.totalShiftedItems * -1;\n  }\n\n  lastIndex() {\n    return this.firstIndex() + this.numVisible - 1;\n  }\n\n  totalDots() {\n    return this.value ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n  }\n\n  totalDotsArray() {\n    const totalDots = this.totalDots();\n    return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n  }\n\n  isVertical() {\n    return this.orientation === 'vertical';\n  }\n\n  isCircular() {\n    return this.circular && this.value && this.value.length >= this.numVisible;\n  }\n\n  isAutoplay() {\n    return this.autoplayInterval && this.allowAutoplay;\n  }\n\n  isForwardNavDisabled() {\n    return this.isEmpty() || this._page >= this.totalDots() - 1 && !this.isCircular();\n  }\n\n  isBackwardNavDisabled() {\n    return this.isEmpty() || this._page <= 0 && !this.isCircular();\n  }\n\n  isEmpty() {\n    return !this.value || this.value.length === 0;\n  }\n\n  navForward(e, index) {\n    if (this.isCircular() || this._page < this.totalDots() - 1) {\n      this.step(-1, index);\n    }\n\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n      this.allowAutoplay = false;\n    }\n\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  navBackward(e, index) {\n    if (this.isCircular() || this._page !== 0) {\n      this.step(1, index);\n    }\n\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n      this.allowAutoplay = false;\n    }\n\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  onDotClick(e, index) {\n    let page = this._page;\n\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n      this.allowAutoplay = false;\n    }\n\n    if (index > page) {\n      this.navForward(e, index);\n    } else if (index < page) {\n      this.navBackward(e, index);\n    }\n  }\n\n  step(dir, page) {\n    let totalShiftedItems = this.totalShiftedItems;\n    const isCircular = this.isCircular();\n\n    if (page != null) {\n      totalShiftedItems = this._numScroll * page * -1;\n\n      if (isCircular) {\n        totalShiftedItems -= this._numVisible;\n      }\n\n      this.isRemainingItemsAdded = false;\n    } else {\n      totalShiftedItems += this._numScroll * dir;\n\n      if (this.isRemainingItemsAdded) {\n        totalShiftedItems += this.remainingItems - this._numScroll * dir;\n        this.isRemainingItemsAdded = false;\n      }\n\n      let originalShiftedItems = isCircular ? totalShiftedItems + this._numVisible : totalShiftedItems;\n      page = Math.abs(Math.floor(originalShiftedItems / this._numScroll));\n    }\n\n    if (isCircular && this.page === this.totalDots() - 1 && dir === -1) {\n      totalShiftedItems = -1 * (this.value.length + this._numVisible);\n      page = 0;\n    } else if (isCircular && this.page === 0 && dir === 1) {\n      totalShiftedItems = 0;\n      page = this.totalDots() - 1;\n    } else if (page === this.totalDots() - 1 && this.remainingItems > 0) {\n      totalShiftedItems += this.remainingItems * -1 - this._numScroll * dir;\n      this.isRemainingItemsAdded = true;\n    }\n\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n\n    this.totalShiftedItems = totalShiftedItems;\n    this._page = page;\n    this.onPage.emit({\n      page: this.page\n    });\n  }\n\n  startAutoplay() {\n    this.interval = setInterval(() => {\n      if (this.totalDots() > 0) {\n        if (this.page === this.totalDots() - 1) {\n          this.step(-1, 0);\n        } else {\n          this.step(-1, this.page + 1);\n        }\n      }\n    }, this.autoplayInterval);\n  }\n\n  stopAutoplay() {\n    if (this.interval) {\n      clearInterval(this.interval);\n    }\n  }\n\n  onTransitionEnd() {\n    if (this.itemsContainer) {\n      this.itemsContainer.nativeElement.style.transition = '';\n\n      if ((this.page === 0 || this.page === this.totalDots() - 1) && this.isCircular()) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n      }\n    }\n  }\n\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n\n    if (this.isVertical()) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n\n  changePageOnTouch(e, diff) {\n    if (Math.abs(diff) > this.swipeThreshold) {\n      if (diff < 0) {\n        this.navForward(e);\n      } else {\n        this.navBackward(e);\n      }\n    }\n  }\n\n  bindDocumentListeners() {\n    if (!this.documentResizeListener) {\n      this.documentResizeListener = e => {\n        this.calculatePosition();\n      };\n\n      window.addEventListener('resize', this.documentResizeListener);\n    }\n  }\n\n  unbindDocumentListeners() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n\n    if (this.autoplayInterval) {\n      this.stopAutoplay();\n    }\n  }\n\n}\n\nCarousel.ɵfac = function Carousel_Factory(t) {\n  return new (t || Carousel)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nCarousel.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Carousel,\n  selectors: [[\"p-carousel\"]],\n  contentQueries: function Carousel_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, Header, 5);\n      i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footerFacet = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Carousel_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    page: \"page\",\n    numVisible: \"numVisible\",\n    numScroll: \"numScroll\",\n    responsiveOptions: \"responsiveOptions\",\n    orientation: \"orientation\",\n    verticalViewPortHeight: \"verticalViewPortHeight\",\n    contentClass: \"contentClass\",\n    indicatorsContentClass: \"indicatorsContentClass\",\n    indicatorsContentStyle: \"indicatorsContentStyle\",\n    indicatorStyleClass: \"indicatorStyleClass\",\n    indicatorStyle: \"indicatorStyle\",\n    value: \"value\",\n    circular: \"circular\",\n    showIndicators: \"showIndicators\",\n    showNavigators: \"showNavigators\",\n    autoplayInterval: \"autoplayInterval\",\n    style: \"style\",\n    styleClass: \"styleClass\"\n  },\n  outputs: {\n    onPage: \"onPage\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c12,\n  decls: 14,\n  vars: 22,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-carousel-header\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-carousel-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-carousel-items-content\", 3, \"ngStyle\"], [1, \"p-carousel-items-container\", 3, \"transitionend\", \"touchend\", \"touchstart\", \"touchmove\"], [\"itemsContainer\", \"\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-carousel-footer\", 4, \"ngIf\"], [1, \"p-carousel-header\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", 3, \"ngClass\", \"ngStyle\", \"click\"], [1, \"p-carousel-footer\"]],\n  template: function Carousel_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c9);\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, Carousel_div_1_Template, 3, 1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n      i0.ɵɵtemplate(4, Carousel_button_4_Template, 2, 8, \"button\", 4);\n      i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6, 7);\n      i0.ɵɵlistener(\"transitionend\", function Carousel_Template_div_transitionend_6_listener() {\n        return ctx.onTransitionEnd();\n      })(\"touchend\", function Carousel_Template_div_touchend_6_listener($event) {\n        return ctx.onTouchEnd($event);\n      })(\"touchstart\", function Carousel_Template_div_touchstart_6_listener($event) {\n        return ctx.onTouchStart($event);\n      })(\"touchmove\", function Carousel_Template_div_touchmove_6_listener($event) {\n        return ctx.onTouchMove($event);\n      });\n      i0.ɵɵtemplate(8, Carousel_div_8_Template, 2, 9, \"div\", 8);\n      i0.ɵɵtemplate(9, Carousel_div_9_Template, 2, 9, \"div\", 8);\n      i0.ɵɵtemplate(10, Carousel_div_10_Template, 2, 9, \"div\", 8);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(11, Carousel_button_11_Template, 2, 8, \"button\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(12, Carousel_ul_12_Template, 2, 5, \"ul\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(13, Carousel_div_13_Template, 3, 1, \"div\", 10);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c10, ctx.isVertical(), !ctx.isVertical()))(\"ngStyle\", ctx.style);\n      i0.ɵɵattribute(\"id\", ctx.id);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.headerFacet || ctx.headerTemplate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵclassMap(ctx.contentClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-carousel-content\");\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c11, ctx.isVertical() ? ctx.verticalViewPortHeight : \"auto\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForStarting);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.clonedItemsForFinishing);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showNavigators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.footerFacet || ctx.footerTemplate);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n  styles: [\".p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Carousel, [{\n    type: Component,\n    args: [{\n      selector: 'p-carousel',\n      template: `\n\t\t<div [attr.id]=\"id\" [ngClass]=\"{'p-carousel p-component':true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical()}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n\t\t\t<div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t\t<div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n\t\t\t\t<div class=\"p-carousel-container\">\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-prev p-link':true, 'p-disabled': isBackwardNavDisabled()}\" [disabled]=\"isBackwardNavDisabled()\" (click)=\"navBackward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-left': !isVertical(), 'pi-chevron-up': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<div class=\"p-carousel-items-content\" [ngStyle]=\"{'height': isVertical() ? verticalViewPortHeight : 'auto'}\">\n\t\t\t\t\t\t<div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div *ngFor=\"let item of clonedItemsForStarting; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': (totalShiftedItems * -1) === (value.length),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForStarting.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of value; let index = index\" [ngClass]= \"{'p-carousel-item': true,\n                                'p-carousel-item-active': (firstIndex() <= index && lastIndex() >= index),\n\t\t\t\t\t\t\t    'p-carousel-item-start': firstIndex() === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': lastIndex() === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of clonedItemsForFinishing; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': ((totalShiftedItems *-1) === numVisible),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForFinishing.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled()}\" [disabled]=\"isForwardNavDisabled()\" (click)=\"navForward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-right': !isVertical(), 'pi-chevron-down': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t\t<ul [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\">\n\t\t\t\t\t<li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{'p-carousel-indicator':true,'p-highlight': _page === i}\">\n\t\t\t\t\t\t<button type=\"button\" [ngClass]=\"'p-link'\" (click)=\"onDotClick($event, i)\" [class]=\"indicatorStyleClass\" [ngStyle]=\"indicatorStyle\"></button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t\t<div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t</div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    page: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    numScroll: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    verticalViewPortHeight: [{\n      type: Input\n    }],\n    contentClass: [{\n      type: Input\n    }],\n    indicatorsContentClass: [{\n      type: Input\n    }],\n    indicatorsContentStyle: [{\n      type: Input\n    }],\n    indicatorStyleClass: [{\n      type: Input\n    }],\n    indicatorStyle: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    showNavigators: [{\n      type: Input\n    }],\n    autoplayInterval: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    onPage: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    headerFacet: [{\n      type: ContentChild,\n      args: [Header]\n    }],\n    footerFacet: [{\n      type: ContentChild,\n      args: [Footer]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass CarouselModule {}\n\nCarouselModule.ɵfac = function CarouselModule_Factory(t) {\n  return new (t || CarouselModule)();\n};\n\nCarouselModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: CarouselModule\n});\nCarouselModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule, RippleModule, CommonModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CarouselModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule],\n      exports: [CommonModule, Carousel, SharedModule],\n      declarations: [Carousel]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Carousel, CarouselModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ViewChild", "ContentChild", "ContentChildren", "NgModule", "Header", "Footer", "PrimeTemplate", "SharedModule", "i2", "RippleModule", "i1", "CommonModule", "UniqueComponentId", "Carousel", "constructor", "el", "zone", "cd", "orientation", "verticalViewPortHeight", "contentClass", "indicatorsContentClass", "indicatorStyleClass", "circular", "showIndicators", "showNavigators", "autoplayInterval", "onPage", "_numVisible", "_numScroll", "_oldNumScroll", "prevState", "numScroll", "numVisible", "value", "defaultNumScroll", "defaultNumVisible", "_page", "isRemainingItemsAdded", "remainingItems", "swipe<PERSON><PERSON><PERSON><PERSON>", "totalShiftedItems", "page", "val", "isCreated", "stopAutoplay", "allowAutoplay", "totalDots", "step", "_value", "ngOnChanges", "simpleChange", "setCloneItems", "responsiveOptions", "isCircular", "createStyle", "calculatePosition", "ngAfterContentInit", "id", "bindDocumentListeners", "templates", "for<PERSON>ach", "item", "getType", "itemTemplate", "template", "headerTemplate", "footerTemplate", "ngAfterContentChecked", "itemsContainer", "length", "emit", "nativeElement", "style", "transform", "isVertical", "isAutoplay", "startAutoplay", "carouselStyle", "document", "createElement", "type", "body", "append<PERSON><PERSON><PERSON>", "innerHTML", "sort", "data1", "data2", "value1", "breakpoint", "value2", "result", "localeCompare", "undefined", "numeric", "i", "res", "windowWidth", "window", "innerWidth", "matchedResponsiveData", "parseInt", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clonedItemsForStarting", "clonedItemsForFinishing", "push", "slice", "firstIndex", "lastIndex", "ceil", "totalDotsArray", "Array", "fill", "isForwardNavDisabled", "isEmpty", "isBackwardNavDisabled", "navForward", "e", "index", "cancelable", "preventDefault", "navBackward", "onDotClick", "dir", "originalShiftedItems", "abs", "transition", "interval", "setInterval", "clearInterval", "onTransitionEnd", "onTouchStart", "<PERSON><PERSON><PERSON>", "changedTouches", "startPos", "x", "pageX", "y", "pageY", "onTouchMove", "onTouchEnd", "changePageOnTouch", "diff", "documentResizeListener", "addEventListener", "unbindDocumentListeners", "removeEventListener", "ngOnDestroy", "ɵfac", "ElementRef", "NgZone", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "args", "selector", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "indicatorsContentStyle", "indicatorStyle", "styleClass", "headerFacet", "footer<PERSON><PERSON><PERSON>", "CarouselModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-carousel.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { Header, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { UniqueComponentId } from 'primeng/utils';\n\nclass Carousel {\n    constructor(el, zone, cd) {\n        this.el = el;\n        this.zone = zone;\n        this.cd = cd;\n        this.orientation = \"horizontal\";\n        this.verticalViewPortHeight = \"300px\";\n        this.contentClass = \"\";\n        this.indicatorsContentClass = \"\";\n        this.indicatorStyleClass = \"\";\n        this.circular = false;\n        this.showIndicators = true;\n        this.showNavigators = true;\n        this.autoplayInterval = 0;\n        this.onPage = new EventEmitter();\n        this._numVisible = 1;\n        this._numScroll = 1;\n        this._oldNumScroll = 0;\n        this.prevState = {\n            numScroll: 0,\n            numVisible: 0,\n            value: []\n        };\n        this.defaultNumScroll = 1;\n        this.defaultNumVisible = 1;\n        this._page = 0;\n        this.isRemainingItemsAdded = false;\n        this.remainingItems = 0;\n        this.swipeThreshold = 20;\n        this.totalShiftedItems = this.page * this.numScroll * -1;\n    }\n    get page() {\n        return this._page;\n    }\n    set page(val) {\n        if (this.isCreated && val !== this._page) {\n            if (this.autoplayInterval) {\n                this.stopAutoplay();\n                this.allowAutoplay = false;\n            }\n            if (val > this._page && val <= (this.totalDots() - 1)) {\n                this.step(-1, val);\n            }\n            else if (val < this._page) {\n                this.step(1, val);\n            }\n        }\n        this._page = val;\n    }\n    get numVisible() {\n        return this._numVisible;\n    }\n    set numVisible(val) {\n        this._numVisible = val;\n    }\n    get numScroll() {\n        return this._numVisible;\n    }\n    set numScroll(val) {\n        this._numScroll = val;\n    }\n    get value() {\n        return this._value;\n    }\n    ;\n    set value(val) {\n        this._value = val;\n    }\n    ngOnChanges(simpleChange) {\n        if (simpleChange.value) {\n            if (this.circular && this._value) {\n                this.setCloneItems();\n            }\n        }\n        if (this.isCreated) {\n            if (simpleChange.numVisible) {\n                if (this.responsiveOptions) {\n                    this.defaultNumVisible = this.numVisible;\n                }\n                if (this.isCircular()) {\n                    this.setCloneItems();\n                }\n                this.createStyle();\n                this.calculatePosition();\n            }\n            if (simpleChange.numScroll) {\n                if (this.responsiveOptions) {\n                    this.defaultNumScroll = this.numScroll;\n                }\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.id = UniqueComponentId();\n        this.allowAutoplay = !!this.autoplayInterval;\n        if (this.circular) {\n            this.setCloneItems();\n        }\n        if (this.responsiveOptions) {\n            this.defaultNumScroll = this._numScroll;\n            this.defaultNumVisible = this._numVisible;\n        }\n        this.createStyle();\n        this.calculatePosition();\n        if (this.responsiveOptions) {\n            this.bindDocumentListeners();\n        }\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'footer':\n                    this.footerTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterContentChecked() {\n        const isCircular = this.isCircular();\n        let totalShiftedItems = this.totalShiftedItems;\n        if (this.value && this.itemsContainer && (this.prevState.numScroll !== this._numScroll || this.prevState.numVisible !== this._numVisible || this.prevState.value.length !== this.value.length)) {\n            if (this.autoplayInterval) {\n                this.stopAutoplay();\n            }\n            this.remainingItems = (this.value.length - this._numVisible) % this._numScroll;\n            let page = this._page;\n            if (this.totalDots() !== 0 && page >= this.totalDots()) {\n                page = this.totalDots() - 1;\n                this._page = page;\n                this.onPage.emit({\n                    page: this.page\n                });\n            }\n            totalShiftedItems = (page * this._numScroll) * -1;\n            if (isCircular) {\n                totalShiftedItems -= this._numVisible;\n            }\n            if (page === (this.totalDots() - 1) && this.remainingItems > 0) {\n                totalShiftedItems += (-1 * this.remainingItems) + this._numScroll;\n                this.isRemainingItemsAdded = true;\n            }\n            else {\n                this.isRemainingItemsAdded = false;\n            }\n            if (totalShiftedItems !== this.totalShiftedItems) {\n                this.totalShiftedItems = totalShiftedItems;\n            }\n            this._oldNumScroll = this._numScroll;\n            this.prevState.numScroll = this._numScroll;\n            this.prevState.numVisible = this._numVisible;\n            this.prevState.value = [...this._value];\n            if (this.totalDots() > 0 && this.itemsContainer.nativeElement) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            }\n            this.isCreated = true;\n            if (this.autoplayInterval && this.isAutoplay()) {\n                this.startAutoplay();\n            }\n        }\n        if (isCircular) {\n            if (this.page === 0) {\n                totalShiftedItems = -1 * this._numVisible;\n            }\n            else if (totalShiftedItems === 0) {\n                totalShiftedItems = -1 * this.value.length;\n                if (this.remainingItems > 0) {\n                    this.isRemainingItemsAdded = true;\n                }\n            }\n            if (totalShiftedItems !== this.totalShiftedItems) {\n                this.totalShiftedItems = totalShiftedItems;\n            }\n        }\n    }\n    createStyle() {\n        if (!this.carouselStyle) {\n            this.carouselStyle = document.createElement('style');\n            this.carouselStyle.type = 'text/css';\n            document.body.appendChild(this.carouselStyle);\n        }\n        let innerHTML = `\n            #${this.id} .p-carousel-item {\n\t\t\t\tflex: 1 0 ${(100 / this.numVisible)}%\n\t\t\t}\n        `;\n        if (this.responsiveOptions) {\n            this.responsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n                if (value1 == null && value2 != null)\n                    result = -1;\n                else if (value1 != null && value2 == null)\n                    result = 1;\n                else if (value1 == null && value2 == null)\n                    result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string')\n                    result = value1.localeCompare(value2, undefined, { numeric: true });\n                else\n                    result = (value1 < value2) ? -1 : (value1 > value2) ? 1 : 0;\n                return -1 * result;\n            });\n            for (let i = 0; i < this.responsiveOptions.length; i++) {\n                let res = this.responsiveOptions[i];\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.id} .p-carousel-item {\n                            flex: 1 0 ${(100 / res.numVisible)}%\n                        }\n                    }\n                `;\n            }\n        }\n        this.carouselStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n        if (this.responsiveOptions) {\n            let windowWidth = window.innerWidth;\n            let matchedResponsiveData = {\n                numVisible: this.defaultNumVisible,\n                numScroll: this.defaultNumScroll\n            };\n            for (let i = 0; i < this.responsiveOptions.length; i++) {\n                let res = this.responsiveOptions[i];\n                if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                    matchedResponsiveData = res;\n                }\n            }\n            if (this._numScroll !== matchedResponsiveData.numScroll) {\n                let page = this._page;\n                page = Math.floor((page * this._numScroll) / matchedResponsiveData.numScroll);\n                let totalShiftedItems = (matchedResponsiveData.numScroll * this.page) * -1;\n                if (this.isCircular()) {\n                    totalShiftedItems -= matchedResponsiveData.numVisible;\n                }\n                this.totalShiftedItems = totalShiftedItems;\n                this._numScroll = matchedResponsiveData.numScroll;\n                this._page = page;\n                this.onPage.emit({\n                    page: this.page\n                });\n            }\n            if (this._numVisible !== matchedResponsiveData.numVisible) {\n                this._numVisible = matchedResponsiveData.numVisible;\n                this.setCloneItems();\n            }\n            this.cd.markForCheck();\n        }\n    }\n    setCloneItems() {\n        this.clonedItemsForStarting = [];\n        this.clonedItemsForFinishing = [];\n        if (this.isCircular()) {\n            this.clonedItemsForStarting.push(...this.value.slice(-1 * this._numVisible));\n            this.clonedItemsForFinishing.push(...this.value.slice(0, this._numVisible));\n        }\n    }\n    firstIndex() {\n        return this.isCircular() ? (-1 * (this.totalShiftedItems + this.numVisible)) : (this.totalShiftedItems * -1);\n    }\n    lastIndex() {\n        return this.firstIndex() + this.numVisible - 1;\n    }\n    totalDots() {\n        return this.value ? Math.ceil((this.value.length - this._numVisible) / this._numScroll) + 1 : 0;\n    }\n    totalDotsArray() {\n        const totalDots = this.totalDots();\n        return totalDots <= 0 ? [] : Array(totalDots).fill(0);\n    }\n    isVertical() {\n        return this.orientation === 'vertical';\n    }\n    isCircular() {\n        return this.circular && this.value && this.value.length >= this.numVisible;\n    }\n    isAutoplay() {\n        return this.autoplayInterval && this.allowAutoplay;\n    }\n    isForwardNavDisabled() {\n        return this.isEmpty() || (this._page >= (this.totalDots() - 1) && !this.isCircular());\n    }\n    isBackwardNavDisabled() {\n        return this.isEmpty() || (this._page <= 0 && !this.isCircular());\n    }\n    isEmpty() {\n        return !this.value || this.value.length === 0;\n    }\n    navForward(e, index) {\n        if (this.isCircular() || this._page < (this.totalDots() - 1)) {\n            this.step(-1, index);\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n            this.allowAutoplay = false;\n        }\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e, index) {\n        if (this.isCircular() || this._page !== 0) {\n            this.step(1, index);\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n            this.allowAutoplay = false;\n        }\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onDotClick(e, index) {\n        let page = this._page;\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n            this.allowAutoplay = false;\n        }\n        if (index > page) {\n            this.navForward(e, index);\n        }\n        else if (index < page) {\n            this.navBackward(e, index);\n        }\n    }\n    step(dir, page) {\n        let totalShiftedItems = this.totalShiftedItems;\n        const isCircular = this.isCircular();\n        if (page != null) {\n            totalShiftedItems = (this._numScroll * page) * -1;\n            if (isCircular) {\n                totalShiftedItems -= this._numVisible;\n            }\n            this.isRemainingItemsAdded = false;\n        }\n        else {\n            totalShiftedItems += (this._numScroll * dir);\n            if (this.isRemainingItemsAdded) {\n                totalShiftedItems += this.remainingItems - (this._numScroll * dir);\n                this.isRemainingItemsAdded = false;\n            }\n            let originalShiftedItems = isCircular ? (totalShiftedItems + this._numVisible) : totalShiftedItems;\n            page = Math.abs(Math.floor((originalShiftedItems / this._numScroll)));\n        }\n        if (isCircular && this.page === (this.totalDots() - 1) && dir === -1) {\n            totalShiftedItems = -1 * (this.value.length + this._numVisible);\n            page = 0;\n        }\n        else if (isCircular && this.page === 0 && dir === 1) {\n            totalShiftedItems = 0;\n            page = (this.totalDots() - 1);\n        }\n        else if (page === (this.totalDots() - 1) && this.remainingItems > 0) {\n            totalShiftedItems += ((this.remainingItems * -1) - (this._numScroll * dir));\n            this.isRemainingItemsAdded = true;\n        }\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n        this.totalShiftedItems = totalShiftedItems;\n        this._page = page;\n        this.onPage.emit({\n            page: this.page\n        });\n    }\n    startAutoplay() {\n        this.interval = setInterval(() => {\n            if (this.totalDots() > 0) {\n                if (this.page === (this.totalDots() - 1)) {\n                    this.step(-1, 0);\n                }\n                else {\n                    this.step(-1, this.page + 1);\n                }\n            }\n        }, this.autoplayInterval);\n    }\n    stopAutoplay() {\n        if (this.interval) {\n            clearInterval(this.interval);\n        }\n    }\n    onTransitionEnd() {\n        if (this.itemsContainer) {\n            this.itemsContainer.nativeElement.style.transition = '';\n            if ((this.page === 0 || this.page === (this.totalDots() - 1)) && this.isCircular()) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical() ? `translate3d(0, ${this.totalShiftedItems * (100 / this._numVisible)}%, 0)` : `translate3d(${this.totalShiftedItems * (100 / this._numVisible)}%, 0, 0)`;\n            }\n        }\n    }\n    onTouchStart(e) {\n        let touchobj = e.changedTouches[0];\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n    onTouchMove(e) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onTouchEnd(e) {\n        let touchobj = e.changedTouches[0];\n        if (this.isVertical()) {\n            this.changePageOnTouch(e, (touchobj.pageY - this.startPos.y));\n        }\n        else {\n            this.changePageOnTouch(e, (touchobj.pageX - this.startPos.x));\n        }\n    }\n    changePageOnTouch(e, diff) {\n        if (Math.abs(diff) > this.swipeThreshold) {\n            if (diff < 0) {\n                this.navForward(e);\n            }\n            else {\n                this.navBackward(e);\n            }\n        }\n    }\n    bindDocumentListeners() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = (e) => {\n                this.calculatePosition();\n            };\n            window.addEventListener('resize', this.documentResizeListener);\n        }\n    }\n    unbindDocumentListeners() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n        if (this.autoplayInterval) {\n            this.stopAutoplay();\n        }\n    }\n}\nCarousel.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Carousel, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nCarousel.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Carousel, selector: \"p-carousel\", inputs: { page: \"page\", numVisible: \"numVisible\", numScroll: \"numScroll\", responsiveOptions: \"responsiveOptions\", orientation: \"orientation\", verticalViewPortHeight: \"verticalViewPortHeight\", contentClass: \"contentClass\", indicatorsContentClass: \"indicatorsContentClass\", indicatorsContentStyle: \"indicatorsContentStyle\", indicatorStyleClass: \"indicatorStyleClass\", indicatorStyle: \"indicatorStyle\", value: \"value\", circular: \"circular\", showIndicators: \"showIndicators\", showNavigators: \"showNavigators\", autoplayInterval: \"autoplayInterval\", style: \"style\", styleClass: \"styleClass\" }, outputs: { onPage: \"onPage\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", first: true, predicate: Header, descendants: true }, { propertyName: \"footerFacet\", first: true, predicate: Footer, descendants: true }, { propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"itemsContainer\", first: true, predicate: [\"itemsContainer\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n\t\t<div [attr.id]=\"id\" [ngClass]=\"{'p-carousel p-component':true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical()}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n\t\t\t<div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t\t<div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n\t\t\t\t<div class=\"p-carousel-container\">\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-prev p-link':true, 'p-disabled': isBackwardNavDisabled()}\" [disabled]=\"isBackwardNavDisabled()\" (click)=\"navBackward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-left': !isVertical(), 'pi-chevron-up': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<div class=\"p-carousel-items-content\" [ngStyle]=\"{'height': isVertical() ? verticalViewPortHeight : 'auto'}\">\n\t\t\t\t\t\t<div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div *ngFor=\"let item of clonedItemsForStarting; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': (totalShiftedItems * -1) === (value.length),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForStarting.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of value; let index = index\" [ngClass]= \"{'p-carousel-item': true,\n                                'p-carousel-item-active': (firstIndex() <= index && lastIndex() >= index),\n\t\t\t\t\t\t\t    'p-carousel-item-start': firstIndex() === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': lastIndex() === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of clonedItemsForFinishing; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': ((totalShiftedItems *-1) === numVisible),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForFinishing.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled()}\" [disabled]=\"isForwardNavDisabled()\" (click)=\"navForward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-right': !isVertical(), 'pi-chevron-down': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t\t<ul [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\">\n\t\t\t\t\t<li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{'p-carousel-indicator':true,'p-highlight': _page === i}\">\n\t\t\t\t\t\t<button type=\"button\" [ngClass]=\"'p-link'\" (click)=\"onDotClick($event, i)\" [class]=\"indicatorStyleClass\" [ngStyle]=\"indicatorStyle\"></button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t\t<div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t</div>\n    `, isInline: true, styles: [\".p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.Ripple, selector: \"[pRipple]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Carousel, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-carousel', template: `\n\t\t<div [attr.id]=\"id\" [ngClass]=\"{'p-carousel p-component':true, 'p-carousel-vertical': isVertical(), 'p-carousel-horizontal': !isVertical()}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n\t\t\t<div class=\"p-carousel-header\" *ngIf=\"headerFacet || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t\t<div [class]=\"contentClass\" [ngClass]=\"'p-carousel-content'\">\n\t\t\t\t<div class=\"p-carousel-container\">\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-prev p-link':true, 'p-disabled': isBackwardNavDisabled()}\" [disabled]=\"isBackwardNavDisabled()\" (click)=\"navBackward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-left': !isVertical(), 'pi-chevron-up': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t\t<div class=\"p-carousel-items-content\" [ngStyle]=\"{'height': isVertical() ? verticalViewPortHeight : 'auto'}\">\n\t\t\t\t\t\t<div #itemsContainer class=\"p-carousel-items-container\" (transitionend)=\"onTransitionEnd()\" (touchend)=\"onTouchEnd($event)\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\">\n                            <div *ngFor=\"let item of clonedItemsForStarting; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': (totalShiftedItems * -1) === (value.length),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForStarting.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of value; let index = index\" [ngClass]= \"{'p-carousel-item': true,\n                                'p-carousel-item-active': (firstIndex() <= index && lastIndex() >= index),\n\t\t\t\t\t\t\t    'p-carousel-item-start': firstIndex() === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': lastIndex() === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n                            <div *ngFor=\"let item of clonedItemsForFinishing; let index = index\" [ngClass]= \"{'p-carousel-item p-carousel-item-cloned': true,\n                                'p-carousel-item-active': ((totalShiftedItems *-1) === numVisible),\n\t\t\t\t\t\t\t    'p-carousel-item-start': 0 === index,\n\t\t\t\t\t\t\t    'p-carousel-item-end': (clonedItemsForFinishing.length - 1) === index}\">\n\t\t\t\t\t\t\t\t<ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item}\"></ng-container>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<button type=\"button\" *ngIf=\"showNavigators\" [ngClass]=\"{'p-carousel-next p-link': true, 'p-disabled': isForwardNavDisabled()}\" [disabled]=\"isForwardNavDisabled()\" (click)=\"navForward($event)\" pRipple>\n\t\t\t\t\t\t<span [ngClass]=\"{'p-carousel-prev-icon pi': true, 'pi-chevron-right': !isVertical(), 'pi-chevron-down': isVertical()}\"></span>\n\t\t\t\t\t</button>\n\t\t\t\t</div>\n\t\t\t\t<ul [ngClass]=\"'p-carousel-indicators p-reset'\" [class]=\"indicatorsContentClass\" [ngStyle]=\"indicatorsContentStyle\" *ngIf=\"showIndicators\">\n\t\t\t\t\t<li *ngFor=\"let totalDot of totalDotsArray(); let i = index\" [ngClass]=\"{'p-carousel-indicator':true,'p-highlight': _page === i}\">\n\t\t\t\t\t\t<button type=\"button\" [ngClass]=\"'p-link'\" (click)=\"onDotClick($event, i)\" [class]=\"indicatorStyleClass\" [ngStyle]=\"indicatorStyle\"></button>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t\t<div class=\"p-carousel-footer\" *ngIf=\"footerFacet || footerTemplate\">\n                <ng-content select=\"p-footer\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"footerTemplate\"></ng-container>\n\t\t\t</div>\n\t\t</div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-carousel{display:flex;flex-direction:column}.p-carousel-content{display:flex;flex-direction:column;overflow:auto}.p-carousel-prev,.p-carousel-next{align-self:center;flex-grow:0;flex-shrink:0;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-carousel-container{display:flex;flex-direction:row}.p-carousel-items-content{overflow:hidden;width:100%}.p-carousel-items-container{display:flex;flex-direction:row}.p-carousel-indicators{display:flex;flex-direction:row;justify-content:center;flex-wrap:wrap}.p-carousel-indicator>button{display:flex;align-items:center;justify-content:center}.p-carousel-vertical .p-carousel-container{flex-direction:column}.p-carousel-vertical .p-carousel-items-container{flex-direction:column;height:100%}.p-items-hidden .p-carousel-item{visibility:hidden}.p-items-hidden .p-carousel-item.p-carousel-item-active{visibility:visible}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { page: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], numScroll: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], verticalViewPortHeight: [{\n                type: Input\n            }], contentClass: [{\n                type: Input\n            }], indicatorsContentClass: [{\n                type: Input\n            }], indicatorsContentStyle: [{\n                type: Input\n            }], indicatorStyleClass: [{\n                type: Input\n            }], indicatorStyle: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], showNavigators: [{\n                type: Input\n            }], autoplayInterval: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], onPage: [{\n                type: Output\n            }], itemsContainer: [{\n                type: ViewChild,\n                args: ['itemsContainer']\n            }], headerFacet: [{\n                type: ContentChild,\n                args: [Header]\n            }], footerFacet: [{\n                type: ContentChild,\n                args: [Footer]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass CarouselModule {\n}\nCarouselModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CarouselModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nCarouselModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: CarouselModule, declarations: [Carousel], imports: [CommonModule, SharedModule, RippleModule], exports: [CommonModule, Carousel, SharedModule] });\nCarouselModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CarouselModule, imports: [CommonModule, SharedModule, RippleModule, CommonModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: CarouselModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, RippleModule],\n                    exports: [CommonModule, Carousel, SharedModule],\n                    declarations: [Carousel]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Carousel, CarouselModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,SAA7F,EAAwGC,YAAxG,EAAsHC,eAAtH,EAAuIC,QAAvI,QAAuJ,eAAvJ;AACA,SAASC,MAAT,EAAiBC,MAAjB,EAAyBC,aAAzB,EAAwCC,YAAxC,QAA4D,aAA5D;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,iBAAT,QAAkC,eAAlC;;;;;IAsc2FnB,EAK3E,sB;;;;;;IAL2EA,EAGxF,6B;IAHwFA,EAI3E,gB;IAJ2EA,EAK3E,gF;IAL2EA,EAMxF,e;;;;mBANwFA,E;IAAAA,EAK5D,a;IAL4DA,EAK5D,sD;;;;;;;;;;;;;;;;;;;;;iBAL4DA,E;;IAAAA,EAStF,gC;IATsFA,EAS+E;MAT/EA,EAS+E;MAAA,gBAT/EA,EAS+E;MAAA,OAT/EA,EASwF,yCAAT;IAAA,E;IAT/EA,EAUrF,wB;IAVqFA,EAWtF,e;;;;mBAXsFA,E;IAAAA,EASzC,uBATyCA,EASzC,qG;IATyCA,EAU/E,a;IAV+EA,EAU/E,uBAV+EA,EAU/E,oE;;;;;;IAV+EA,EAkBnF,sB;;;;;;;;;;;;;;;;;;;;;IAlBmFA,EAc/D,4B;IAd+DA,EAkBnF,gF;IAlBmFA,EAmBpF,e;;;;;;mBAnBoFA,E;IAAAA,EAcK,uBAdLA,EAcK,yJ;IAdLA,EAkBpE,a;IAlBoEA,EAkBpE,gFAlBoEA,EAkBpE,mC;;;;;;IAlBoEA,EAwBnF,sB;;;;;;;;;;;;;;;IAxBmFA,EAoB/D,4B;IApB+DA,EAwBnF,gF;IAxBmFA,EAyBpF,e;;;;;;mBAzBoFA,E;IAAAA,EAoBZ,uBApBYA,EAoBZ,mK;IApBYA,EAwBpE,a;IAxBoEA,EAwBpE,gFAxBoEA,EAwBpE,mC;;;;;;IAxBoEA,EA8BnF,sB;;;;;;IA9BmFA,EA0B/D,4B;IA1B+DA,EA8BnF,iF;IA9BmFA,EA+BpF,e;;;;;;mBA/BoFA,E;IAAAA,EA0BM,uBA1BNA,EA0BM,wJ;IA1BNA,EA8BpE,a;IA9BoEA,EA8BpE,gFA9BoEA,EA8BpE,mC;;;;;;;;;;;;;;;;;;;;;iBA9BoEA,E;;IAAAA,EAkCtF,gC;IAlCsFA,EAkC8E;MAlC9EA,EAkC8E;MAAA,gBAlC9EA,EAkC8E;MAAA,OAlC9EA,EAkCuF,wCAAT;IAAA,E;IAlC9EA,EAmCrF,wB;IAnCqFA,EAoCtF,e;;;;mBApCsFA,E;IAAAA,EAkCzC,uBAlCyCA,EAkCzC,mG;IAlCyCA,EAmC/E,a;IAnC+EA,EAmC/E,uBAnC+EA,EAmC/E,oE;;;;;;;;;;;;;iBAnC+EA,E;;IAAAA,EAuCtF,4C;IAvCsFA,EAwC1C;MAAA,oBAxC0CA,EAwC1C;MAAA;MAAA,gBAxC0CA,EAwC1C;MAAA,OAxC0CA,EAwCjC,+CAAT;IAAA,E;IAxC0CA,EAwC+C,iB;;;;;oBAxC/CA,E;IAAAA,EAuCzB,uBAvCyBA,EAuCzB,kD;IAvCyBA,EAwCV,a;IAxCUA,EAwCV,wC;IAxCUA,EAwC/D,mE;;;;;;IAxC+DA,EAsCvF,2B;IAtCuFA,EAuCtF,2D;IAvCsFA,EA0CvF,e;;;;mBA1CuFA,E;IAAAA,EAsCvC,0C;IAtCuCA,EAsCnF,iG;IAtCmFA,EAuC7D,a;IAvC6DA,EAuC7D,+C;;;;;;IAvC6DA,EA8C3E,sB;;;;;;IA9C2EA,EA4CxF,6B;IA5CwFA,EA6C3E,mB;IA7C2EA,EA8C3E,iF;IA9C2EA,EA+CxF,e;;;;mBA/CwFA,E;IAAAA,EA8C5D,a;IA9C4DA,EA8C5D,sD;;;;;;;;;;;;;;;;;;;;;;AAlf/B,MAAMoB,QAAN,CAAe;EACXC,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAWC,EAAX,EAAe;IACtB,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,WAAL,GAAmB,YAAnB;IACA,KAAKC,sBAAL,GAA8B,OAA9B;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,sBAAL,GAA8B,EAA9B;IACA,KAAKC,mBAAL,GAA2B,EAA3B;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA,KAAKC,gBAAL,GAAwB,CAAxB;IACA,KAAKC,MAAL,GAAc,IAAIjC,YAAJ,EAAd;IACA,KAAKkC,WAAL,GAAmB,CAAnB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,aAAL,GAAqB,CAArB;IACA,KAAKC,SAAL,GAAiB;MACbC,SAAS,EAAE,CADE;MAEbC,UAAU,EAAE,CAFC;MAGbC,KAAK,EAAE;IAHM,CAAjB;IAKA,KAAKC,gBAAL,GAAwB,CAAxB;IACA,KAAKC,iBAAL,GAAyB,CAAzB;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAKC,qBAAL,GAA6B,KAA7B;IACA,KAAKC,cAAL,GAAsB,CAAtB;IACA,KAAKC,cAAL,GAAsB,EAAtB;IACA,KAAKC,iBAAL,GAAyB,KAAKC,IAAL,GAAY,KAAKV,SAAjB,GAA6B,CAAC,CAAvD;EACH;;EACO,IAAJU,IAAI,GAAG;IACP,OAAO,KAAKL,KAAZ;EACH;;EACO,IAAJK,IAAI,CAACC,GAAD,EAAM;IACV,IAAI,KAAKC,SAAL,IAAkBD,GAAG,KAAK,KAAKN,KAAnC,EAA0C;MACtC,IAAI,KAAKX,gBAAT,EAA2B;QACvB,KAAKmB,YAAL;QACA,KAAKC,aAAL,GAAqB,KAArB;MACH;;MACD,IAAIH,GAAG,GAAG,KAAKN,KAAX,IAAoBM,GAAG,IAAK,KAAKI,SAAL,KAAmB,CAAnD,EAAuD;QACnD,KAAKC,IAAL,CAAU,CAAC,CAAX,EAAcL,GAAd;MACH,CAFD,MAGK,IAAIA,GAAG,GAAG,KAAKN,KAAf,EAAsB;QACvB,KAAKW,IAAL,CAAU,CAAV,EAAaL,GAAb;MACH;IACJ;;IACD,KAAKN,KAAL,GAAaM,GAAb;EACH;;EACa,IAAVV,UAAU,GAAG;IACb,OAAO,KAAKL,WAAZ;EACH;;EACa,IAAVK,UAAU,CAACU,GAAD,EAAM;IAChB,KAAKf,WAAL,GAAmBe,GAAnB;EACH;;EACY,IAATX,SAAS,GAAG;IACZ,OAAO,KAAKJ,WAAZ;EACH;;EACY,IAATI,SAAS,CAACW,GAAD,EAAM;IACf,KAAKd,UAAL,GAAkBc,GAAlB;EACH;;EACQ,IAALT,KAAK,GAAG;IACR,OAAO,KAAKe,MAAZ;EACH;;EAEQ,IAALf,KAAK,CAACS,GAAD,EAAM;IACX,KAAKM,MAAL,GAAcN,GAAd;EACH;;EACDO,WAAW,CAACC,YAAD,EAAe;IACtB,IAAIA,YAAY,CAACjB,KAAjB,EAAwB;MACpB,IAAI,KAAKX,QAAL,IAAiB,KAAK0B,MAA1B,EAAkC;QAC9B,KAAKG,aAAL;MACH;IACJ;;IACD,IAAI,KAAKR,SAAT,EAAoB;MAChB,IAAIO,YAAY,CAAClB,UAAjB,EAA6B;QACzB,IAAI,KAAKoB,iBAAT,EAA4B;UACxB,KAAKjB,iBAAL,GAAyB,KAAKH,UAA9B;QACH;;QACD,IAAI,KAAKqB,UAAL,EAAJ,EAAuB;UACnB,KAAKF,aAAL;QACH;;QACD,KAAKG,WAAL;QACA,KAAKC,iBAAL;MACH;;MACD,IAAIL,YAAY,CAACnB,SAAjB,EAA4B;QACxB,IAAI,KAAKqB,iBAAT,EAA4B;UACxB,KAAKlB,gBAAL,GAAwB,KAAKH,SAA7B;QACH;MACJ;IACJ;EACJ;;EACDyB,kBAAkB,GAAG;IACjB,KAAKC,EAAL,GAAU9C,iBAAiB,EAA3B;IACA,KAAKkC,aAAL,GAAqB,CAAC,CAAC,KAAKpB,gBAA5B;;IACA,IAAI,KAAKH,QAAT,EAAmB;MACf,KAAK6B,aAAL;IACH;;IACD,IAAI,KAAKC,iBAAT,EAA4B;MACxB,KAAKlB,gBAAL,GAAwB,KAAKN,UAA7B;MACA,KAAKO,iBAAL,GAAyB,KAAKR,WAA9B;IACH;;IACD,KAAK2B,WAAL;IACA,KAAKC,iBAAL;;IACA,IAAI,KAAKH,iBAAT,EAA4B;MACxB,KAAKM,qBAAL;IACH;;IACD,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,MAAL;UACI,KAAKC,YAAL,GAAoBF,IAAI,CAACG,QAAzB;UACA;;QACJ,KAAK,QAAL;UACI,KAAKC,cAAL,GAAsBJ,IAAI,CAACG,QAA3B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKE,cAAL,GAAsBL,IAAI,CAACG,QAA3B;UACA;;QACJ;UACI,KAAKD,YAAL,GAAoBF,IAAI,CAACG,QAAzB;UACA;MAZR;IAcH,CAfD;EAgBH;;EACDG,qBAAqB,GAAG;IACpB,MAAMd,UAAU,GAAG,KAAKA,UAAL,EAAnB;IACA,IAAIb,iBAAiB,GAAG,KAAKA,iBAA7B;;IACA,IAAI,KAAKP,KAAL,IAAc,KAAKmC,cAAnB,KAAsC,KAAKtC,SAAL,CAAeC,SAAf,KAA6B,KAAKH,UAAlC,IAAgD,KAAKE,SAAL,CAAeE,UAAf,KAA8B,KAAKL,WAAnF,IAAkG,KAAKG,SAAL,CAAeG,KAAf,CAAqBoC,MAArB,KAAgC,KAAKpC,KAAL,CAAWoC,MAAnL,CAAJ,EAAgM;MAC5L,IAAI,KAAK5C,gBAAT,EAA2B;QACvB,KAAKmB,YAAL;MACH;;MACD,KAAKN,cAAL,GAAsB,CAAC,KAAKL,KAAL,CAAWoC,MAAX,GAAoB,KAAK1C,WAA1B,IAAyC,KAAKC,UAApE;MACA,IAAIa,IAAI,GAAG,KAAKL,KAAhB;;MACA,IAAI,KAAKU,SAAL,OAAqB,CAArB,IAA0BL,IAAI,IAAI,KAAKK,SAAL,EAAtC,EAAwD;QACpDL,IAAI,GAAG,KAAKK,SAAL,KAAmB,CAA1B;QACA,KAAKV,KAAL,GAAaK,IAAb;QACA,KAAKf,MAAL,CAAY4C,IAAZ,CAAiB;UACb7B,IAAI,EAAE,KAAKA;QADE,CAAjB;MAGH;;MACDD,iBAAiB,GAAIC,IAAI,GAAG,KAAKb,UAAb,GAA2B,CAAC,CAAhD;;MACA,IAAIyB,UAAJ,EAAgB;QACZb,iBAAiB,IAAI,KAAKb,WAA1B;MACH;;MACD,IAAIc,IAAI,KAAM,KAAKK,SAAL,KAAmB,CAA7B,IAAmC,KAAKR,cAAL,GAAsB,CAA7D,EAAgE;QAC5DE,iBAAiB,IAAK,CAAC,CAAD,GAAK,KAAKF,cAAX,GAA6B,KAAKV,UAAvD;QACA,KAAKS,qBAAL,GAA6B,IAA7B;MACH,CAHD,MAIK;QACD,KAAKA,qBAAL,GAA6B,KAA7B;MACH;;MACD,IAAIG,iBAAiB,KAAK,KAAKA,iBAA/B,EAAkD;QAC9C,KAAKA,iBAAL,GAAyBA,iBAAzB;MACH;;MACD,KAAKX,aAAL,GAAqB,KAAKD,UAA1B;MACA,KAAKE,SAAL,CAAeC,SAAf,GAA2B,KAAKH,UAAhC;MACA,KAAKE,SAAL,CAAeE,UAAf,GAA4B,KAAKL,WAAjC;MACA,KAAKG,SAAL,CAAeG,KAAf,GAAuB,CAAC,GAAG,KAAKe,MAAT,CAAvB;;MACA,IAAI,KAAKF,SAAL,KAAmB,CAAnB,IAAwB,KAAKsB,cAAL,CAAoBG,aAAhD,EAA+D;QAC3D,KAAKH,cAAL,CAAoBG,aAApB,CAAkCC,KAAlC,CAAwCC,SAAxC,GAAoD,KAAKC,UAAL,KAAqB,kBAAiBlC,iBAAiB,IAAI,MAAM,KAAKb,WAAf,CAA4B,OAAnF,GAA6F,eAAca,iBAAiB,IAAI,MAAM,KAAKb,WAAf,CAA4B,UAA5M;MACH;;MACD,KAAKgB,SAAL,GAAiB,IAAjB;;MACA,IAAI,KAAKlB,gBAAL,IAAyB,KAAKkD,UAAL,EAA7B,EAAgD;QAC5C,KAAKC,aAAL;MACH;IACJ;;IACD,IAAIvB,UAAJ,EAAgB;MACZ,IAAI,KAAKZ,IAAL,KAAc,CAAlB,EAAqB;QACjBD,iBAAiB,GAAG,CAAC,CAAD,GAAK,KAAKb,WAA9B;MACH,CAFD,MAGK,IAAIa,iBAAiB,KAAK,CAA1B,EAA6B;QAC9BA,iBAAiB,GAAG,CAAC,CAAD,GAAK,KAAKP,KAAL,CAAWoC,MAApC;;QACA,IAAI,KAAK/B,cAAL,GAAsB,CAA1B,EAA6B;UACzB,KAAKD,qBAAL,GAA6B,IAA7B;QACH;MACJ;;MACD,IAAIG,iBAAiB,KAAK,KAAKA,iBAA/B,EAAkD;QAC9C,KAAKA,iBAAL,GAAyBA,iBAAzB;MACH;IACJ;EACJ;;EACDc,WAAW,GAAG;IACV,IAAI,CAAC,KAAKuB,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqBC,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAArB;MACA,KAAKF,aAAL,CAAmBG,IAAnB,GAA0B,UAA1B;MACAF,QAAQ,CAACG,IAAT,CAAcC,WAAd,CAA0B,KAAKL,aAA/B;IACH;;IACD,IAAIM,SAAS,GAAI;AACzB,eAAe,KAAK1B,EAAG;AACvB,gBAAiB,MAAM,KAAKzB,UAAY;AACxC;AACA,SAJQ;;IAKA,IAAI,KAAKoB,iBAAT,EAA4B;MACxB,KAAKA,iBAAL,CAAuBgC,IAAvB,CAA4B,CAACC,KAAD,EAAQC,KAAR,KAAkB;QAC1C,MAAMC,MAAM,GAAGF,KAAK,CAACG,UAArB;QACA,MAAMC,MAAM,GAAGH,KAAK,CAACE,UAArB;QACA,IAAIE,MAAM,GAAG,IAAb;QACA,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACIC,MAAM,GAAG,CAAC,CAAV,CADJ,KAEK,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAI,OAAOH,MAAP,KAAkB,QAAlB,IAA8B,OAAOE,MAAP,KAAkB,QAApD,EACDC,MAAM,GAAGH,MAAM,CAACI,aAAP,CAAqBF,MAArB,EAA6BG,SAA7B,EAAwC;UAAEC,OAAO,EAAE;QAAX,CAAxC,CAAT,CADC,KAGDH,MAAM,GAAIH,MAAM,GAAGE,MAAV,GAAoB,CAAC,CAArB,GAA0BF,MAAM,GAAGE,MAAV,GAAoB,CAApB,GAAwB,CAA1D;QACJ,OAAO,CAAC,CAAD,GAAKC,MAAZ;MACH,CAfD;;MAgBA,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1C,iBAAL,CAAuBiB,MAA3C,EAAmDyB,CAAC,EAApD,EAAwD;QACpD,IAAIC,GAAG,GAAG,KAAK3C,iBAAL,CAAuB0C,CAAvB,CAAV;QACAX,SAAS,IAAK;AAC9B,oDAAoDY,GAAG,CAACP,UAAW;AACnE,2BAA2B,KAAK/B,EAAG;AACnC,wCAAyC,MAAMsC,GAAG,CAAC/D,UAAY;AAC/D;AACA;AACA,iBANgB;MAOH;IACJ;;IACD,KAAK6C,aAAL,CAAmBM,SAAnB,GAA+BA,SAA/B;EACH;;EACD5B,iBAAiB,GAAG;IAChB,IAAI,KAAKH,iBAAT,EAA4B;MACxB,IAAI4C,WAAW,GAAGC,MAAM,CAACC,UAAzB;MACA,IAAIC,qBAAqB,GAAG;QACxBnE,UAAU,EAAE,KAAKG,iBADO;QAExBJ,SAAS,EAAE,KAAKG;MAFQ,CAA5B;;MAIA,KAAK,IAAI4D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK1C,iBAAL,CAAuBiB,MAA3C,EAAmDyB,CAAC,EAApD,EAAwD;QACpD,IAAIC,GAAG,GAAG,KAAK3C,iBAAL,CAAuB0C,CAAvB,CAAV;;QACA,IAAIM,QAAQ,CAACL,GAAG,CAACP,UAAL,EAAiB,EAAjB,CAAR,IAAgCQ,WAApC,EAAiD;UAC7CG,qBAAqB,GAAGJ,GAAxB;QACH;MACJ;;MACD,IAAI,KAAKnE,UAAL,KAAoBuE,qBAAqB,CAACpE,SAA9C,EAAyD;QACrD,IAAIU,IAAI,GAAG,KAAKL,KAAhB;QACAK,IAAI,GAAG4D,IAAI,CAACC,KAAL,CAAY7D,IAAI,GAAG,KAAKb,UAAb,GAA2BuE,qBAAqB,CAACpE,SAA5D,CAAP;QACA,IAAIS,iBAAiB,GAAI2D,qBAAqB,CAACpE,SAAtB,GAAkC,KAAKU,IAAxC,GAAgD,CAAC,CAAzE;;QACA,IAAI,KAAKY,UAAL,EAAJ,EAAuB;UACnBb,iBAAiB,IAAI2D,qBAAqB,CAACnE,UAA3C;QACH;;QACD,KAAKQ,iBAAL,GAAyBA,iBAAzB;QACA,KAAKZ,UAAL,GAAkBuE,qBAAqB,CAACpE,SAAxC;QACA,KAAKK,KAAL,GAAaK,IAAb;QACA,KAAKf,MAAL,CAAY4C,IAAZ,CAAiB;UACb7B,IAAI,EAAE,KAAKA;QADE,CAAjB;MAGH;;MACD,IAAI,KAAKd,WAAL,KAAqBwE,qBAAqB,CAACnE,UAA/C,EAA2D;QACvD,KAAKL,WAAL,GAAmBwE,qBAAqB,CAACnE,UAAzC;QACA,KAAKmB,aAAL;MACH;;MACD,KAAKnC,EAAL,CAAQuF,YAAR;IACH;EACJ;;EACDpD,aAAa,GAAG;IACZ,KAAKqD,sBAAL,GAA8B,EAA9B;IACA,KAAKC,uBAAL,GAA+B,EAA/B;;IACA,IAAI,KAAKpD,UAAL,EAAJ,EAAuB;MACnB,KAAKmD,sBAAL,CAA4BE,IAA5B,CAAiC,GAAG,KAAKzE,KAAL,CAAW0E,KAAX,CAAiB,CAAC,CAAD,GAAK,KAAKhF,WAA3B,CAApC;MACA,KAAK8E,uBAAL,CAA6BC,IAA7B,CAAkC,GAAG,KAAKzE,KAAL,CAAW0E,KAAX,CAAiB,CAAjB,EAAoB,KAAKhF,WAAzB,CAArC;IACH;EACJ;;EACDiF,UAAU,GAAG;IACT,OAAO,KAAKvD,UAAL,KAAqB,CAAC,CAAD,IAAM,KAAKb,iBAAL,GAAyB,KAAKR,UAApC,CAArB,GAAyE,KAAKQ,iBAAL,GAAyB,CAAC,CAA1G;EACH;;EACDqE,SAAS,GAAG;IACR,OAAO,KAAKD,UAAL,KAAoB,KAAK5E,UAAzB,GAAsC,CAA7C;EACH;;EACDc,SAAS,GAAG;IACR,OAAO,KAAKb,KAAL,GAAaoE,IAAI,CAACS,IAAL,CAAU,CAAC,KAAK7E,KAAL,CAAWoC,MAAX,GAAoB,KAAK1C,WAA1B,IAAyC,KAAKC,UAAxD,IAAsE,CAAnF,GAAuF,CAA9F;EACH;;EACDmF,cAAc,GAAG;IACb,MAAMjE,SAAS,GAAG,KAAKA,SAAL,EAAlB;IACA,OAAOA,SAAS,IAAI,CAAb,GAAiB,EAAjB,GAAsBkE,KAAK,CAAClE,SAAD,CAAL,CAAiBmE,IAAjB,CAAsB,CAAtB,CAA7B;EACH;;EACDvC,UAAU,GAAG;IACT,OAAO,KAAKzD,WAAL,KAAqB,UAA5B;EACH;;EACDoC,UAAU,GAAG;IACT,OAAO,KAAK/B,QAAL,IAAiB,KAAKW,KAAtB,IAA+B,KAAKA,KAAL,CAAWoC,MAAX,IAAqB,KAAKrC,UAAhE;EACH;;EACD2C,UAAU,GAAG;IACT,OAAO,KAAKlD,gBAAL,IAAyB,KAAKoB,aAArC;EACH;;EACDqE,oBAAoB,GAAG;IACnB,OAAO,KAAKC,OAAL,MAAmB,KAAK/E,KAAL,IAAe,KAAKU,SAAL,KAAmB,CAAlC,IAAwC,CAAC,KAAKO,UAAL,EAAnE;EACH;;EACD+D,qBAAqB,GAAG;IACpB,OAAO,KAAKD,OAAL,MAAmB,KAAK/E,KAAL,IAAc,CAAd,IAAmB,CAAC,KAAKiB,UAAL,EAA9C;EACH;;EACD8D,OAAO,GAAG;IACN,OAAO,CAAC,KAAKlF,KAAN,IAAe,KAAKA,KAAL,CAAWoC,MAAX,KAAsB,CAA5C;EACH;;EACDgD,UAAU,CAACC,CAAD,EAAIC,KAAJ,EAAW;IACjB,IAAI,KAAKlE,UAAL,MAAqB,KAAKjB,KAAL,GAAc,KAAKU,SAAL,KAAmB,CAA1D,EAA8D;MAC1D,KAAKC,IAAL,CAAU,CAAC,CAAX,EAAcwE,KAAd;IACH;;IACD,IAAI,KAAK9F,gBAAT,EAA2B;MACvB,KAAKmB,YAAL;MACA,KAAKC,aAAL,GAAqB,KAArB;IACH;;IACD,IAAIyE,CAAC,IAAIA,CAAC,CAACE,UAAX,EAAuB;MACnBF,CAAC,CAACG,cAAF;IACH;EACJ;;EACDC,WAAW,CAACJ,CAAD,EAAIC,KAAJ,EAAW;IAClB,IAAI,KAAKlE,UAAL,MAAqB,KAAKjB,KAAL,KAAe,CAAxC,EAA2C;MACvC,KAAKW,IAAL,CAAU,CAAV,EAAawE,KAAb;IACH;;IACD,IAAI,KAAK9F,gBAAT,EAA2B;MACvB,KAAKmB,YAAL;MACA,KAAKC,aAAL,GAAqB,KAArB;IACH;;IACD,IAAIyE,CAAC,IAAIA,CAAC,CAACE,UAAX,EAAuB;MACnBF,CAAC,CAACG,cAAF;IACH;EACJ;;EACDE,UAAU,CAACL,CAAD,EAAIC,KAAJ,EAAW;IACjB,IAAI9E,IAAI,GAAG,KAAKL,KAAhB;;IACA,IAAI,KAAKX,gBAAT,EAA2B;MACvB,KAAKmB,YAAL;MACA,KAAKC,aAAL,GAAqB,KAArB;IACH;;IACD,IAAI0E,KAAK,GAAG9E,IAAZ,EAAkB;MACd,KAAK4E,UAAL,CAAgBC,CAAhB,EAAmBC,KAAnB;IACH,CAFD,MAGK,IAAIA,KAAK,GAAG9E,IAAZ,EAAkB;MACnB,KAAKiF,WAAL,CAAiBJ,CAAjB,EAAoBC,KAApB;IACH;EACJ;;EACDxE,IAAI,CAAC6E,GAAD,EAAMnF,IAAN,EAAY;IACZ,IAAID,iBAAiB,GAAG,KAAKA,iBAA7B;IACA,MAAMa,UAAU,GAAG,KAAKA,UAAL,EAAnB;;IACA,IAAIZ,IAAI,IAAI,IAAZ,EAAkB;MACdD,iBAAiB,GAAI,KAAKZ,UAAL,GAAkBa,IAAnB,GAA2B,CAAC,CAAhD;;MACA,IAAIY,UAAJ,EAAgB;QACZb,iBAAiB,IAAI,KAAKb,WAA1B;MACH;;MACD,KAAKU,qBAAL,GAA6B,KAA7B;IACH,CAND,MAOK;MACDG,iBAAiB,IAAK,KAAKZ,UAAL,GAAkBgG,GAAxC;;MACA,IAAI,KAAKvF,qBAAT,EAAgC;QAC5BG,iBAAiB,IAAI,KAAKF,cAAL,GAAuB,KAAKV,UAAL,GAAkBgG,GAA9D;QACA,KAAKvF,qBAAL,GAA6B,KAA7B;MACH;;MACD,IAAIwF,oBAAoB,GAAGxE,UAAU,GAAIb,iBAAiB,GAAG,KAAKb,WAA7B,GAA4Ca,iBAAjF;MACAC,IAAI,GAAG4D,IAAI,CAACyB,GAAL,CAASzB,IAAI,CAACC,KAAL,CAAYuB,oBAAoB,GAAG,KAAKjG,UAAxC,CAAT,CAAP;IACH;;IACD,IAAIyB,UAAU,IAAI,KAAKZ,IAAL,KAAe,KAAKK,SAAL,KAAmB,CAAhD,IAAsD8E,GAAG,KAAK,CAAC,CAAnE,EAAsE;MAClEpF,iBAAiB,GAAG,CAAC,CAAD,IAAM,KAAKP,KAAL,CAAWoC,MAAX,GAAoB,KAAK1C,WAA/B,CAApB;MACAc,IAAI,GAAG,CAAP;IACH,CAHD,MAIK,IAAIY,UAAU,IAAI,KAAKZ,IAAL,KAAc,CAA5B,IAAiCmF,GAAG,KAAK,CAA7C,EAAgD;MACjDpF,iBAAiB,GAAG,CAApB;MACAC,IAAI,GAAI,KAAKK,SAAL,KAAmB,CAA3B;IACH,CAHI,MAIA,IAAIL,IAAI,KAAM,KAAKK,SAAL,KAAmB,CAA7B,IAAmC,KAAKR,cAAL,GAAsB,CAA7D,EAAgE;MACjEE,iBAAiB,IAAM,KAAKF,cAAL,GAAsB,CAAC,CAAxB,GAA8B,KAAKV,UAAL,GAAkBgG,GAAtE;MACA,KAAKvF,qBAAL,GAA6B,IAA7B;IACH;;IACD,IAAI,KAAK+B,cAAT,EAAyB;MACrB,KAAKA,cAAL,CAAoBG,aAApB,CAAkCC,KAAlC,CAAwCC,SAAxC,GAAoD,KAAKC,UAAL,KAAqB,kBAAiBlC,iBAAiB,IAAI,MAAM,KAAKb,WAAf,CAA4B,OAAnF,GAA6F,eAAca,iBAAiB,IAAI,MAAM,KAAKb,WAAf,CAA4B,UAA5M;MACA,KAAKyC,cAAL,CAAoBG,aAApB,CAAkCC,KAAlC,CAAwCuD,UAAxC,GAAqD,yBAArD;IACH;;IACD,KAAKvF,iBAAL,GAAyBA,iBAAzB;IACA,KAAKJ,KAAL,GAAaK,IAAb;IACA,KAAKf,MAAL,CAAY4C,IAAZ,CAAiB;MACb7B,IAAI,EAAE,KAAKA;IADE,CAAjB;EAGH;;EACDmC,aAAa,GAAG;IACZ,KAAKoD,QAAL,GAAgBC,WAAW,CAAC,MAAM;MAC9B,IAAI,KAAKnF,SAAL,KAAmB,CAAvB,EAA0B;QACtB,IAAI,KAAKL,IAAL,KAAe,KAAKK,SAAL,KAAmB,CAAtC,EAA0C;UACtC,KAAKC,IAAL,CAAU,CAAC,CAAX,EAAc,CAAd;QACH,CAFD,MAGK;UACD,KAAKA,IAAL,CAAU,CAAC,CAAX,EAAc,KAAKN,IAAL,GAAY,CAA1B;QACH;MACJ;IACJ,CAT0B,EASxB,KAAKhB,gBATmB,CAA3B;EAUH;;EACDmB,YAAY,GAAG;IACX,IAAI,KAAKoF,QAAT,EAAmB;MACfE,aAAa,CAAC,KAAKF,QAAN,CAAb;IACH;EACJ;;EACDG,eAAe,GAAG;IACd,IAAI,KAAK/D,cAAT,EAAyB;MACrB,KAAKA,cAAL,CAAoBG,aAApB,CAAkCC,KAAlC,CAAwCuD,UAAxC,GAAqD,EAArD;;MACA,IAAI,CAAC,KAAKtF,IAAL,KAAc,CAAd,IAAmB,KAAKA,IAAL,KAAe,KAAKK,SAAL,KAAmB,CAAtD,KAA6D,KAAKO,UAAL,EAAjE,EAAoF;QAChF,KAAKe,cAAL,CAAoBG,aAApB,CAAkCC,KAAlC,CAAwCC,SAAxC,GAAoD,KAAKC,UAAL,KAAqB,kBAAiB,KAAKlC,iBAAL,IAA0B,MAAM,KAAKb,WAArC,CAAkD,OAAxF,GAAkG,eAAc,KAAKa,iBAAL,IAA0B,MAAM,KAAKb,WAArC,CAAkD,UAAtN;MACH;IACJ;EACJ;;EACDyG,YAAY,CAACd,CAAD,EAAI;IACZ,IAAIe,QAAQ,GAAGf,CAAC,CAACgB,cAAF,CAAiB,CAAjB,CAAf;IACA,KAAKC,QAAL,GAAgB;MACZC,CAAC,EAAEH,QAAQ,CAACI,KADA;MAEZC,CAAC,EAAEL,QAAQ,CAACM;IAFA,CAAhB;EAIH;;EACDC,WAAW,CAACtB,CAAD,EAAI;IACX,IAAIA,CAAC,CAACE,UAAN,EAAkB;MACdF,CAAC,CAACG,cAAF;IACH;EACJ;;EACDoB,UAAU,CAACvB,CAAD,EAAI;IACV,IAAIe,QAAQ,GAAGf,CAAC,CAACgB,cAAF,CAAiB,CAAjB,CAAf;;IACA,IAAI,KAAK5D,UAAL,EAAJ,EAAuB;MACnB,KAAKoE,iBAAL,CAAuBxB,CAAvB,EAA2Be,QAAQ,CAACM,KAAT,GAAiB,KAAKJ,QAAL,CAAcG,CAA1D;IACH,CAFD,MAGK;MACD,KAAKI,iBAAL,CAAuBxB,CAAvB,EAA2Be,QAAQ,CAACI,KAAT,GAAiB,KAAKF,QAAL,CAAcC,CAA1D;IACH;EACJ;;EACDM,iBAAiB,CAACxB,CAAD,EAAIyB,IAAJ,EAAU;IACvB,IAAI1C,IAAI,CAACyB,GAAL,CAASiB,IAAT,IAAiB,KAAKxG,cAA1B,EAA0C;MACtC,IAAIwG,IAAI,GAAG,CAAX,EAAc;QACV,KAAK1B,UAAL,CAAgBC,CAAhB;MACH,CAFD,MAGK;QACD,KAAKI,WAAL,CAAiBJ,CAAjB;MACH;IACJ;EACJ;;EACD5D,qBAAqB,GAAG;IACpB,IAAI,CAAC,KAAKsF,sBAAV,EAAkC;MAC9B,KAAKA,sBAAL,GAA+B1B,CAAD,IAAO;QACjC,KAAK/D,iBAAL;MACH,CAFD;;MAGA0C,MAAM,CAACgD,gBAAP,CAAwB,QAAxB,EAAkC,KAAKD,sBAAvC;IACH;EACJ;;EACDE,uBAAuB,GAAG;IACtB,IAAI,KAAKF,sBAAT,EAAiC;MAC7B/C,MAAM,CAACkD,mBAAP,CAA2B,QAA3B,EAAqC,KAAKH,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDI,WAAW,GAAG;IACV,IAAI,KAAKhG,iBAAT,EAA4B;MACxB,KAAK8F,uBAAL;IACH;;IACD,IAAI,KAAKzH,gBAAT,EAA2B;MACvB,KAAKmB,YAAL;IACH;EACJ;;AAlcU;;AAocfhC,QAAQ,CAACyI,IAAT;EAAA,iBAAqGzI,QAArG,EAA2FpB,EAA3F,mBAA+HA,EAAE,CAAC8J,UAAlI,GAA2F9J,EAA3F,mBAAyJA,EAAE,CAAC+J,MAA5J,GAA2F/J,EAA3F,mBAA+KA,EAAE,CAACgK,iBAAlL;AAAA;;AACA5I,QAAQ,CAAC6I,IAAT,kBAD2FjK,EAC3F;EAAA,MAAyFoB,QAAzF;EAAA;EAAA;IAAA;MAD2FpB,EAC3F,0BAA80BW,MAA90B;MAD2FX,EAC3F,0BAAk6BY,MAAl6B;MAD2FZ,EAC3F,0BAAu+Ba,aAAv+B;IAAA;;IAAA;MAAA;;MAD2Fb,EAC3F,qBAD2FA,EAC3F;MAD2FA,EAC3F,qBAD2FA,EAC3F;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;IAAA;;IAAA;MAAA;;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAD2FA,EAC3F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;MAD2FA,EAEzF,4BADF;MAD2FA,EAGxF,uDAFH;MAD2FA,EAOxF,yCANH;MAD2FA,EAStF,6DARL;MAD2FA,EAYtF,4CAXL;MAD2FA,EAa7B;QAAA,OAAiB,qBAAjB;MAAA;QAAA,OAAgD,sBAAhD;MAAA;QAAA,OAAkF,wBAAlF;MAAA;QAAA,OAAqH,uBAArH;MAAA,EAZ9D;MAD2FA,EAc/D,uDAb5B;MAD2FA,EAoB/D,uDAnB5B;MAD2FA,EA0B/D,yDAzB5B;MAD2FA,EAgCrF,iBA/BN;MAD2FA,EAkCtF,+DAjCL;MAD2FA,EAqCvF,eApCJ;MAD2FA,EAsCvF,uDArCJ;MAD2FA,EA2CxF,eA1CH;MAD2FA,EA4CxF,0DA3CH;MAD2FA,EAgDzF,eA/CF;IAAA;;IAAA;MAD2FA,EAEsE,2BADjK;MAD2FA,EAErE,uBAFqEA,EAErE,sFADtB;MAD2FA,EAEpF,0BADP;MAD2FA,EAGxD,aAFnC;MAD2FA,EAGxD,0DAFnC;MAD2FA,EAOnF,aANR;MAD2FA,EAOnF,6BANR;MAD2FA,EAO5D,4CAN/B;MAD2FA,EAS/D,aAR5B;MAD2FA,EAS/D,uCAR5B;MAD2FA,EAYhD,aAX3C;MAD2FA,EAYhD,uBAZgDA,EAYhD,mFAX3C;MAD2FA,EAczC,aAblD;MAD2FA,EAczC,kDAblD;MAD2FA,EAoBzC,aAnBlD;MAD2FA,EAoBzC,iCAnBlD;MAD2FA,EA0BzC,aAzBlD;MAD2FA,EA0BzC,mDAzBlD;MAD2FA,EAkC/D,aAjC5B;MAD2FA,EAkC/D,uCAjC5B;MAD2FA,EAsC8B,aArCzH;MAD2FA,EAsC8B,uCArCzH;MAD2FA,EA4CxD,aA3CnC;MAD2FA,EA4CxD,0DA3CnC;IAAA;EAAA;EAAA,eAgDs9BiB,EAAE,CAACiJ,OAhDz9B,EAgDojCjJ,EAAE,CAACkJ,OAhDvjC,EAgDirClJ,EAAE,CAACmJ,IAhDprC,EAgDqxCnJ,EAAE,CAACoJ,gBAhDxxC,EAgD47CpJ,EAAE,CAACqJ,OAhD/7C,EAgDihDvJ,EAAE,CAACwJ,MAhDphD;EAAA;EAAA;EAAA;AAAA;;AAiDA;EAAA,mDAlD2FvK,EAkD3F,mBAA2FoB,QAA3F,EAAiH,CAAC;IACtGoE,IAAI,EAAEtF,SADgG;IAEtGsK,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BjG,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAhDmB;MAgDZkG,eAAe,EAAEvK,uBAAuB,CAACwK,MAhD7B;MAgDqCC,aAAa,EAAExK,iBAAiB,CAACyK,IAhDtE;MAgD4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAhDlF;MAkDIC,MAAM,EAAE,CAAC,y4BAAD;IAlDZ,CAAD;EAFgG,CAAD,CAAjH,EAqD4B,YAAY;IAAE,OAAO,CAAC;MAAEvF,IAAI,EAAExF,EAAE,CAAC8J;IAAX,CAAD,EAA0B;MAAEtE,IAAI,EAAExF,EAAE,CAAC+J;IAAX,CAA1B,EAA+C;MAAEvE,IAAI,EAAExF,EAAE,CAACgK;IAAX,CAA/C,CAAP;EAAwF,CArDlI,EAqDoJ;IAAE/G,IAAI,EAAE,CAAC;MAC7IuC,IAAI,EAAEnF;IADuI,CAAD,CAAR;IAEpImC,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAEnF;IADO,CAAD,CAFwH;IAIpIkC,SAAS,EAAE,CAAC;MACZiD,IAAI,EAAEnF;IADM,CAAD,CAJyH;IAMpIuD,iBAAiB,EAAE,CAAC;MACpB4B,IAAI,EAAEnF;IADc,CAAD,CANiH;IAQpIoB,WAAW,EAAE,CAAC;MACd+D,IAAI,EAAEnF;IADQ,CAAD,CARuH;IAUpIqB,sBAAsB,EAAE,CAAC;MACzB8D,IAAI,EAAEnF;IADmB,CAAD,CAV4G;IAYpIsB,YAAY,EAAE,CAAC;MACf6D,IAAI,EAAEnF;IADS,CAAD,CAZsH;IAcpIuB,sBAAsB,EAAE,CAAC;MACzB4D,IAAI,EAAEnF;IADmB,CAAD,CAd4G;IAgBpI2K,sBAAsB,EAAE,CAAC;MACzBxF,IAAI,EAAEnF;IADmB,CAAD,CAhB4G;IAkBpIwB,mBAAmB,EAAE,CAAC;MACtB2D,IAAI,EAAEnF;IADgB,CAAD,CAlB+G;IAoBpI4K,cAAc,EAAE,CAAC;MACjBzF,IAAI,EAAEnF;IADW,CAAD,CApBoH;IAsBpIoC,KAAK,EAAE,CAAC;MACR+C,IAAI,EAAEnF;IADE,CAAD,CAtB6H;IAwBpIyB,QAAQ,EAAE,CAAC;MACX0D,IAAI,EAAEnF;IADK,CAAD,CAxB0H;IA0BpI0B,cAAc,EAAE,CAAC;MACjByD,IAAI,EAAEnF;IADW,CAAD,CA1BoH;IA4BpI2B,cAAc,EAAE,CAAC;MACjBwD,IAAI,EAAEnF;IADW,CAAD,CA5BoH;IA8BpI4B,gBAAgB,EAAE,CAAC;MACnBuD,IAAI,EAAEnF;IADa,CAAD,CA9BkH;IAgCpI2E,KAAK,EAAE,CAAC;MACRQ,IAAI,EAAEnF;IADE,CAAD,CAhC6H;IAkCpI6K,UAAU,EAAE,CAAC;MACb1F,IAAI,EAAEnF;IADO,CAAD,CAlCwH;IAoCpI6B,MAAM,EAAE,CAAC;MACTsD,IAAI,EAAElF;IADG,CAAD,CApC4H;IAsCpIsE,cAAc,EAAE,CAAC;MACjBY,IAAI,EAAEjF,SADW;MAEjBiK,IAAI,EAAE,CAAC,gBAAD;IAFW,CAAD,CAtCoH;IAyCpIW,WAAW,EAAE,CAAC;MACd3F,IAAI,EAAEhF,YADQ;MAEdgK,IAAI,EAAE,CAAC7J,MAAD;IAFQ,CAAD,CAzCuH;IA4CpIyK,WAAW,EAAE,CAAC;MACd5F,IAAI,EAAEhF,YADQ;MAEdgK,IAAI,EAAE,CAAC5J,MAAD;IAFQ,CAAD,CA5CuH;IA+CpIuD,SAAS,EAAE,CAAC;MACZqB,IAAI,EAAE/E,eADM;MAEZ+J,IAAI,EAAE,CAAC3J,aAAD;IAFM,CAAD;EA/CyH,CArDpJ;AAAA;;AAwGA,MAAMwK,cAAN,CAAqB;;AAErBA,cAAc,CAACxB,IAAf;EAAA,iBAA2GwB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBA7J2FtL,EA6J3F;EAAA,MAA4GqL;AAA5G;AACAA,cAAc,CAACE,IAAf,kBA9J2FvL,EA8J3F;EAAA,UAAsIkB,YAAtI,EAAoJJ,YAApJ,EAAkKE,YAAlK,EAAgLE,YAAhL,EAA8LJ,YAA9L;AAAA;;AACA;EAAA,mDA/J2Fd,EA+J3F,mBAA2FqL,cAA3F,EAAuH,CAAC;IAC5G7F,IAAI,EAAE9E,QADsG;IAE5G8J,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAACtK,YAAD,EAAeJ,YAAf,EAA6BE,YAA7B,CADV;MAECyK,OAAO,EAAE,CAACvK,YAAD,EAAeE,QAAf,EAAyBN,YAAzB,CAFV;MAGC4K,YAAY,EAAE,CAACtK,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmBiK,cAAnB"}, "metadata": {}, "sourceType": "module"}