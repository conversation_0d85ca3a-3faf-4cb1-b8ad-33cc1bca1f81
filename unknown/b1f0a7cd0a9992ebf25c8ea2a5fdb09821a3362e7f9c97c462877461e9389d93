{"ast": null, "code": "import { config } from '../config';\nlet context = null;\nexport function errorContext(cb) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    const isRoot = !context;\n\n    if (isRoot) {\n      context = {\n        errorThrown: false,\n        error: null\n      };\n    }\n\n    cb();\n\n    if (isRoot) {\n      const {\n        errorThrown,\n        error\n      } = context;\n      context = null;\n\n      if (errorThrown) {\n        throw error;\n      }\n    }\n  } else {\n    cb();\n  }\n}\nexport function captureError(err) {\n  if (config.useDeprecatedSynchronousErrorHandling && context) {\n    context.errorThrown = true;\n    context.error = err;\n  }\n}", "map": {"version": 3, "names": ["config", "context", "errorContext", "cb", "useDeprecatedSynchronousErrorHandling", "isRoot", "errorThrown", "error", "captureError", "err"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/errorContext.js"], "sourcesContent": ["import { config } from '../config';\nlet context = null;\nexport function errorContext(cb) {\n    if (config.useDeprecatedSynchronousErrorHandling) {\n        const isRoot = !context;\n        if (isRoot) {\n            context = { errorThrown: false, error: null };\n        }\n        cb();\n        if (isRoot) {\n            const { errorThrown, error } = context;\n            context = null;\n            if (errorThrown) {\n                throw error;\n            }\n        }\n    }\n    else {\n        cb();\n    }\n}\nexport function captureError(err) {\n    if (config.useDeprecatedSynchronousErrorHandling && context) {\n        context.errorThrown = true;\n        context.error = err;\n    }\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,WAAvB;AACA,IAAIC,OAAO,GAAG,IAAd;AACA,OAAO,SAASC,YAAT,CAAsBC,EAAtB,EAA0B;EAC7B,IAAIH,MAAM,CAACI,qCAAX,EAAkD;IAC9C,MAAMC,MAAM,GAAG,CAACJ,OAAhB;;IACA,IAAII,MAAJ,EAAY;MACRJ,OAAO,GAAG;QAAEK,WAAW,EAAE,KAAf;QAAsBC,KAAK,EAAE;MAA7B,CAAV;IACH;;IACDJ,EAAE;;IACF,IAAIE,MAAJ,EAAY;MACR,MAAM;QAAEC,WAAF;QAAeC;MAAf,IAAyBN,OAA/B;MACAA,OAAO,GAAG,IAAV;;MACA,IAAIK,WAAJ,EAAiB;QACb,MAAMC,KAAN;MACH;IACJ;EACJ,CAbD,MAcK;IACDJ,EAAE;EACL;AACJ;AACD,OAAO,SAASK,YAAT,CAAsBC,GAAtB,EAA2B;EAC9B,IAAIT,MAAM,CAACI,qCAAP,IAAgDH,OAApD,EAA6D;IACzDA,OAAO,CAACK,WAAR,GAAsB,IAAtB;IACAL,OAAO,CAACM,KAAR,GAAgBE,GAAhB;EACH;AACJ"}, "metadata": {}, "sourceType": "module"}