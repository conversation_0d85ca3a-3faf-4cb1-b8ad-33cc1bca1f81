{"ast": null, "code": "import { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat(...args) {\n  const scheduler = popScheduler(args);\n  return operate((source, subscriber) => {\n    concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "concatAll", "popScheduler", "from", "concat", "args", "scheduler", "source", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/concat.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat(...args) {\n    const scheduler = popScheduler(args);\n    return operate((source, subscriber) => {\n        concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,YAAT,QAA6B,cAA7B;AACA,SAASC,IAAT,QAAqB,oBAArB;AACA,OAAO,SAASC,MAAT,CAAgB,GAAGC,IAAnB,EAAyB;EAC5B,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAD,CAA9B;EACA,OAAOL,OAAO,CAAC,CAACO,MAAD,EAASC,UAAT,KAAwB;IACnCP,SAAS,GAAGE,IAAI,CAAC,CAACI,MAAD,EAAS,GAAGF,IAAZ,CAAD,EAAoBC,SAApB,CAAP,CAAT,CAAgDG,SAAhD,CAA0DD,UAA1D;EACH,CAFa,CAAd;AAGH"}, "metadata": {}, "sourceType": "module"}