{"ast": null, "code": "import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n  return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}", "map": {"version": 3, "names": ["bindCallbackInternals", "bindNodeCallback", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/bindNodeCallback.js"], "sourcesContent": ["import { bindCallbackInternals } from './bindCallbackInternals';\nexport function bindNodeCallback(callbackFunc, resultSelector, scheduler) {\n    return bindCallbackInternals(true, callbackFunc, resultSelector, scheduler);\n}\n"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,yBAAtC;AACA,OAAO,SAASC,gBAAT,CAA0BC,YAA1B,EAAwCC,cAAxC,EAAwDC,SAAxD,EAAmE;EACtE,OAAOJ,qBAAqB,CAAC,IAAD,EAAOE,YAAP,EAAqBC,cAArB,EAAqCC,SAArC,CAA5B;AACH"}, "metadata": {}, "sourceType": "module"}