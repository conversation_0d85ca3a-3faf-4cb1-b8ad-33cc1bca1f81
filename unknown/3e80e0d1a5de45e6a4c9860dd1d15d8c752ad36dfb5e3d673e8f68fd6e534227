{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n  return operate((source, subscriber) => {\n    let connection = null;\n    source._refCount++;\n    const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n      if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n        connection = null;\n        return;\n      }\n\n      const sharedConnection = source._connection;\n      const conn = connection;\n      connection = null;\n\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n\n      subscriber.unsubscribe();\n    });\n    source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      connection = source.connect();\n    }\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "refCount", "source", "subscriber", "connection", "_refCount", "refCounter", "undefined", "sharedConnection", "_connection", "conn", "unsubscribe", "subscribe", "closed", "connect"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/refCount.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n    return operate((source, subscriber) => {\n        let connection = null;\n        source._refCount++;\n        const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n            if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n                connection = null;\n                return;\n            }\n            const sharedConnection = source._connection;\n            const conn = connection;\n            connection = null;\n            if (sharedConnection && (!conn || sharedConnection === conn)) {\n                sharedConnection.unsubscribe();\n            }\n            subscriber.unsubscribe();\n        });\n        source.subscribe(refCounter);\n        if (!refCounter.closed) {\n            connection = source.connect();\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,QAAT,GAAoB;EACvB,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,UAAU,GAAG,IAAjB;IACAF,MAAM,CAACG,SAAP;IACA,MAAMC,UAAU,GAAGN,wBAAwB,CAACG,UAAD,EAAaI,SAAb,EAAwBA,SAAxB,EAAmCA,SAAnC,EAA8C,MAAM;MAC3F,IAAI,CAACL,MAAD,IAAWA,MAAM,CAACG,SAAP,IAAoB,CAA/B,IAAoC,IAAI,EAAEH,MAAM,CAACG,SAArD,EAAgE;QAC5DD,UAAU,GAAG,IAAb;QACA;MACH;;MACD,MAAMI,gBAAgB,GAAGN,MAAM,CAACO,WAAhC;MACA,MAAMC,IAAI,GAAGN,UAAb;MACAA,UAAU,GAAG,IAAb;;MACA,IAAII,gBAAgB,KAAK,CAACE,IAAD,IAASF,gBAAgB,KAAKE,IAAnC,CAApB,EAA8D;QAC1DF,gBAAgB,CAACG,WAAjB;MACH;;MACDR,UAAU,CAACQ,WAAX;IACH,CAZ0C,CAA3C;IAaAT,MAAM,CAACU,SAAP,CAAiBN,UAAjB;;IACA,IAAI,CAACA,UAAU,CAACO,MAAhB,EAAwB;MACpBT,UAAU,GAAGF,MAAM,CAACY,OAAP,EAAb;IACH;EACJ,CApBa,CAAd;AAqBH"}, "metadata": {}, "sourceType": "module"}