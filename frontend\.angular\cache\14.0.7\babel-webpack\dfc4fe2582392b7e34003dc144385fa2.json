{"ast": null, "code": "import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n  if (resultSelector) {\n    return source => source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n  }\n\n  return operate((source, subscriber) => {\n    let index = 0;\n    let innerSub = null;\n    let isComplete = false;\n    source.subscribe(createOperatorSubscriber(subscriber, outerValue => {\n      if (!innerSub) {\n        innerSub = createOperatorSubscriber(subscriber, undefined, () => {\n          innerSub = null;\n          isComplete && subscriber.complete();\n        });\n        innerFrom(project(outerValue, index++)).subscribe(innerSub);\n      }\n    }, () => {\n      isComplete = true;\n      !innerSub && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["map", "innerFrom", "operate", "createOperatorSubscriber", "exhaustMap", "project", "resultSelector", "source", "pipe", "a", "i", "b", "ii", "subscriber", "index", "innerSub", "isComplete", "subscribe", "outerValue", "undefined", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/exhaustMap.js"], "sourcesContent": ["import { map } from './map';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function exhaustMap(project, resultSelector) {\n    if (resultSelector) {\n        return (source) => source.pipe(exhaustMap((a, i) => innerFrom(project(a, i)).pipe(map((b, ii) => resultSelector(a, b, i, ii)))));\n    }\n    return operate((source, subscriber) => {\n        let index = 0;\n        let innerSub = null;\n        let isComplete = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (outerValue) => {\n            if (!innerSub) {\n                innerSub = createOperatorSubscriber(subscriber, undefined, () => {\n                    innerSub = null;\n                    isComplete && subscriber.complete();\n                });\n                innerFrom(project(outerValue, index++)).subscribe(innerSub);\n            }\n        }, () => {\n            isComplete = true;\n            !innerSub && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,GAAT,QAAoB,OAApB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,UAAT,CAAoBC,OAApB,EAA6BC,cAA7B,EAA6C;EAChD,IAAIA,cAAJ,EAAoB;IAChB,OAAQC,MAAD,IAAYA,MAAM,CAACC,IAAP,CAAYJ,UAAU,CAAC,CAACK,CAAD,EAAIC,CAAJ,KAAUT,SAAS,CAACI,OAAO,CAACI,CAAD,EAAIC,CAAJ,CAAR,CAAT,CAAyBF,IAAzB,CAA8BR,GAAG,CAAC,CAACW,CAAD,EAAIC,EAAJ,KAAWN,cAAc,CAACG,CAAD,EAAIE,CAAJ,EAAOD,CAAP,EAAUE,EAAV,CAA1B,CAAjC,CAAX,CAAtB,CAAnB;EACH;;EACD,OAAOV,OAAO,CAAC,CAACK,MAAD,EAASM,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAG,CAAZ;IACA,IAAIC,QAAQ,GAAG,IAAf;IACA,IAAIC,UAAU,GAAG,KAAjB;IACAT,MAAM,CAACU,SAAP,CAAiBd,wBAAwB,CAACU,UAAD,EAAcK,UAAD,IAAgB;MAClE,IAAI,CAACH,QAAL,EAAe;QACXA,QAAQ,GAAGZ,wBAAwB,CAACU,UAAD,EAAaM,SAAb,EAAwB,MAAM;UAC7DJ,QAAQ,GAAG,IAAX;UACAC,UAAU,IAAIH,UAAU,CAACO,QAAX,EAAd;QACH,CAHkC,CAAnC;QAIAnB,SAAS,CAACI,OAAO,CAACa,UAAD,EAAaJ,KAAK,EAAlB,CAAR,CAAT,CAAwCG,SAAxC,CAAkDF,QAAlD;MACH;IACJ,CARwC,EAQtC,MAAM;MACLC,UAAU,GAAG,IAAb;MACA,CAACD,QAAD,IAAaF,UAAU,CAACO,QAAX,EAAb;IACH,CAXwC,CAAzC;EAYH,CAhBa,CAAd;AAiBH"}, "metadata": {}, "sourceType": "module"}