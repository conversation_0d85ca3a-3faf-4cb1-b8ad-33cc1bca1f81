{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => {\n      if (!hasValue) {\n        subscriber.next(defaultValue);\n      }\n\n      subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "defaultIfEmpty", "defaultValue", "source", "subscriber", "hasValue", "subscribe", "value", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/defaultIfEmpty.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function defaultIfEmpty(defaultValue) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            subscriber.next(value);\n        }, () => {\n            if (!hasValue) {\n                subscriber.next(defaultValue);\n            }\n            subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,cAAT,CAAwBC,YAAxB,EAAsC;EACzC,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAQ,GAAG,KAAf;IACAF,MAAM,CAACG,SAAP,CAAiBN,wBAAwB,CAACI,UAAD,EAAcG,KAAD,IAAW;MAC7DF,QAAQ,GAAG,IAAX;MACAD,UAAU,CAACI,IAAX,CAAgBD,KAAhB;IACH,CAHwC,EAGtC,MAAM;MACL,IAAI,CAACF,QAAL,EAAe;QACXD,UAAU,CAACI,IAAX,CAAgBN,YAAhB;MACH;;MACDE,UAAU,CAACK,QAAX;IACH,CARwC,CAAzC;EASH,CAXa,CAAd;AAYH"}, "metadata": {}, "sourceType": "module"}