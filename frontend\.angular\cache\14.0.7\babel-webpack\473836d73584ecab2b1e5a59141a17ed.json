{"ast": null, "code": "/* global __resourceQuery WorkerGlobalScope */\n// Send messages to the outside, so plugins can consume it.\n\n/**\n * @param {string} type\n * @param {any} [data]\n */\nfunction sendMsg(type, data) {\n  if (typeof self !== \"undefined\" && (typeof WorkerGlobalScope === \"undefined\" || !(self instanceof WorkerGlobalScope))) {\n    self.postMessage({\n      type: \"webpack\".concat(type),\n      data: data\n    }, \"*\");\n  }\n}\n\nexport default sendMsg;", "map": {"version": 3, "names": ["sendMsg", "type", "data", "self", "WorkerGlobalScope", "postMessage", "concat"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/webpack-dev-server/client/utils/sendMessage.js"], "sourcesContent": ["/* global __resourceQuery WorkerGlobalScope */\n// Send messages to the outside, so plugins can consume it.\n\n/**\n * @param {string} type\n * @param {any} [data]\n */\nfunction sendMsg(type, data) {\n  if (typeof self !== \"undefined\" && (typeof WorkerGlobalScope === \"undefined\" || !(self instanceof WorkerGlobalScope))) {\n    self.postMessage({\n      type: \"webpack\".concat(type),\n      data: data\n    }, \"*\");\n  }\n}\n\nexport default sendMsg;"], "mappings": "AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,OAAT,CAAiBC,IAAjB,EAAuBC,IAAvB,EAA6B;EAC3B,IAAI,OAAOC,IAAP,KAAgB,WAAhB,KAAgC,OAAOC,iBAAP,KAA6B,WAA7B,IAA4C,EAAED,IAAI,YAAYC,iBAAlB,CAA5E,CAAJ,EAAuH;IACrHD,IAAI,CAACE,WAAL,CAAiB;MACfJ,IAAI,EAAE,UAAUK,MAAV,CAAiBL,IAAjB,CADS;MAEfC,IAAI,EAAEA;IAFS,CAAjB,EAGG,GAHH;EAID;AACF;;AAED,eAAeF,OAAf"}, "metadata": {}, "sourceType": "module"}