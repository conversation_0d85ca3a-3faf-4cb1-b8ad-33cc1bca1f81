{"ast": null, "code": "import { filter } from './filter';\nexport function skip(count) {\n  return filter((_, index) => count <= index);\n}", "map": {"version": 3, "names": ["filter", "skip", "count", "_", "index"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/skip.js"], "sourcesContent": ["import { filter } from './filter';\nexport function skip(count) {\n    return filter((_, index) => count <= index);\n}\n"], "mappings": "AAAA,SAASA,MAAT,QAAuB,UAAvB;AACA,OAAO,SAASC,IAAT,CAAcC,KAAd,EAAqB;EACxB,OAAOF,MAAM,CAAC,CAACG,CAAD,EAAIC,KAAJ,KAAcF,KAAK,IAAIE,KAAxB,CAAb;AACH"}, "metadata": {}, "sourceType": "module"}