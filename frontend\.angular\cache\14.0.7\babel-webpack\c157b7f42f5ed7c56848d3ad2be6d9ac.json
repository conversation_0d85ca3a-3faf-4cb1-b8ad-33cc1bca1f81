{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/auth.service\";\nimport * as i2 from \"../../../../services/statistics.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/inputtext\";\nimport * as i8 from \"primeng/toast\";\n\nfunction ProfileComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵelement(1, \"i\", 7);\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵtext(3, \"Loading profile...\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ProfileComponent_div_6_button_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_6_button_61_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.toggleEditMode());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction ProfileComponent_div_6_button_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_6_button_62_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.saveProfile());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"loading\", ctx_r4.saving);\n  }\n}\n\nfunction ProfileComponent_div_6_button_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_6_button_63_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.cancelEdit());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function () {\n  return {\n    width: \"4rem\",\n    height: \"4rem\"\n  };\n};\n\nfunction ProfileComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 9)(3, \"div\", 10);\n    i0.ɵɵelement(4, \"i\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"h3\", 12);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 13);\n    i0.ɵɵtext(9, \"Administrator\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 14)(11, \"div\", 2)(12, \"h6\");\n    i0.ɵɵtext(13, \"Personal Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 0)(15, \"div\", 1)(16, \"label\", 15);\n    i0.ɵɵtext(17, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function ProfileComponent_div_6_Template_input_ngModelChange_18_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.userProfile.first_name = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 1)(20, \"label\", 15);\n    i0.ɵɵtext(21, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function ProfileComponent_div_6_Template_input_ngModelChange_22_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.userProfile.last_name = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 1)(24, \"label\", 15);\n    i0.ɵɵtext(25, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function ProfileComponent_div_6_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.userProfile.email = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 1)(28, \"label\", 15);\n    i0.ɵɵtext(29, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"input\", 17);\n    i0.ɵɵlistener(\"ngModelChange\", function ProfileComponent_div_6_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.userProfile.phone = $event);\n    });\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(31, \"div\", 14)(32, \"div\", 2)(33, \"h6\");\n    i0.ɵɵtext(34, \"Account Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 0)(36, \"div\", 1)(37, \"label\", 15);\n    i0.ɵɵtext(38, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(39, \"input\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 1)(41, \"label\", 15);\n    i0.ɵɵtext(42, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 19);\n    i0.ɵɵtext(44, \"Active\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 1)(46, \"label\", 15);\n    i0.ɵɵtext(47, \"Member Since\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"input\", 20);\n    i0.ɵɵpipe(49, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 1)(51, \"label\", 15);\n    i0.ɵɵtext(52, \"Last Updated\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(53, \"input\", 20);\n    i0.ɵɵpipe(54, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(55, \"div\", 1)(56, \"div\", 2)(57, \"div\", 21)(58, \"h6\");\n    i0.ɵɵtext(59, \"Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\");\n    i0.ɵɵtemplate(61, ProfileComponent_div_6_button_61_Template, 1, 0, \"button\", 22);\n    i0.ɵɵtemplate(62, ProfileComponent_div_6_button_62_Template, 1, 1, \"button\", 23);\n    i0.ɵɵtemplate(63, ProfileComponent_div_6_button_63_Template, 1, 0, \"button\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(64, \"div\", 1)(65, \"div\", 2)(66, \"h6\");\n    i0.ɵɵtext(67, \"Admin Statistics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 0)(69, \"div\", 25)(70, \"div\", 26)(71, \"div\", 27);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 28);\n    i0.ɵɵtext(74, \"Total Formations\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(75, \"div\", 25)(76, \"div\", 26)(77, \"div\", 29);\n    i0.ɵɵtext(78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(79, \"div\", 28);\n    i0.ɵɵtext(80, \"Total Employees\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(81, \"div\", 25)(82, \"div\", 26)(83, \"div\", 30);\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"div\", 28);\n    i0.ɵɵtext(86, \"Total Trainers\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(87, \"div\", 25)(88, \"div\", 26)(89, \"div\", 31);\n    i0.ɵɵtext(90);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 28);\n    i0.ɵɵtext(92, \"Total Teams\");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(26, _c0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.userProfile.first_name, \" \", ctx_r1.userProfile.last_name, \"\");\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.userProfile.first_name)(\"disabled\", !ctx_r1.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.userProfile.last_name)(\"disabled\", !ctx_r1.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.userProfile.email)(\"disabled\", !ctx_r1.editMode);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.userProfile.phone)(\"disabled\", !ctx_r1.editMode);\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind2(49, 20, ctx_r1.userProfile.created_at, \"mediumDate\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind2(54, 23, ctx_r1.userProfile.updated_at, \"medium\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editMode);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.editMode);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.stats.totalFormations);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.stats.totalEmployees);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.stats.totalTrainers);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.stats.totalTeams);\n  }\n}\n\nfunction ProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵelementStart(2, \"h5\");\n    i0.ɵɵtext(3, \"Unable to load profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 28);\n    i0.ɵɵtext(5, \"Please try refreshing the page or contact support.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_7_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.loadProfile());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\n\nexport class ProfileComponent {\n  constructor(authService, statisticsService, messageService) {\n    this.authService = authService;\n    this.statisticsService = statisticsService;\n    this.messageService = messageService;\n    this.userProfile = null;\n    this.originalProfile = null;\n    this.editMode = false;\n    this.loading = true;\n    this.saving = false;\n    this.stats = {\n      totalFormations: 0,\n      totalEmployees: 0,\n      totalTrainers: 0,\n      totalTeams: 0\n    };\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this.loadProfile();\n      yield _this.loadStats();\n    })();\n  }\n\n  loadProfile() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.loading = true;\n        console.log('Loading user profile...'); // Get current user profile\n\n        _this2.userProfile = yield lastValueFrom(_this2.authService.getProfile());\n        _this2.originalProfile = Object.assign({}, _this2.userProfile);\n        console.log('Profile loaded:', _this2.userProfile);\n      } catch (error) {\n        console.error('Error loading profile:', error);\n\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load profile information'\n        });\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  loadStats() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const dashboardStats = yield lastValueFrom(_this3.statisticsService.getDashboardStats());\n        _this3.stats = {\n          totalFormations: dashboardStats.totalFormations || 0,\n          totalEmployees: dashboardStats.totalEmployees || 0,\n          totalTrainers: dashboardStats.totalTrainers || 0,\n          totalTeams: dashboardStats.totalTeams || 0\n        };\n      } catch (error) {\n        console.error('Error loading stats:', error); // Don't show error for stats, just keep zeros\n      }\n    })();\n  }\n\n  toggleEditMode() {\n    this.editMode = !this.editMode;\n\n    if (this.editMode) {\n      this.originalProfile = Object.assign({}, this.userProfile);\n    }\n  }\n\n  saveProfile() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this4.saving = true; // Update profile via API\n\n        const updatedProfile = yield lastValueFrom(_this4.authService.updateProfile(_this4.userProfile));\n        _this4.userProfile = updatedProfile;\n        _this4.originalProfile = Object.assign({}, updatedProfile);\n        _this4.editMode = false;\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Profile updated successfully'\n        });\n      } catch (error) {\n        console.error('Error saving profile:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to update profile'\n        });\n      } finally {\n        _this4.saving = false;\n      }\n    })();\n  }\n\n  cancelEdit() {\n    this.userProfile = Object.assign({}, this.originalProfile);\n    this.editMode = false;\n    this.messageService.add({\n      severity: 'info',\n      summary: 'Cancelled',\n      detail: 'Changes cancelled'\n    });\n  }\n\n}\n\nProfileComponent.ɵfac = function ProfileComponent_Factory(t) {\n  return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.StatisticsService), i0.ɵɵdirectiveInject(i3.MessageService));\n};\n\nProfileComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ProfileComponent,\n  selectors: [[\"app-profile\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 9,\n  vars: 3,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [\"class\", \"flex align-items-center justify-content-center\", \"style\", \"height: 200px;\", 4, \"ngIf\"], [\"class\", \"grid\", 4, \"ngIf\"], [\"class\", \"text-center p-4\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"200px\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\", 2, \"font-size\", \"2rem\"], [1, \"ml-2\"], [1, \"flex\", \"align-items-center\", \"mb-4\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", \"mr-3\", 3, \"ngStyle\"], [1, \"pi\", \"pi-user\", \"text-blue-500\", \"text-2xl\"], [1, \"m-0\", \"text-900\"], [1, \"m-0\", \"text-600\"], [1, \"col-12\", \"md:col-6\"], [1, \"block\", \"text-900\", \"font-medium\", \"mb-2\"], [\"pInputText\", \"\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"pInputText\", \"\", \"placeholder\", \"Enter phone number\", 1, \"w-full\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"pInputText\", \"\", \"value\", \"Administrator\", \"disabled\", \"\", 1, \"w-full\"], [1, \"p-tag\", \"p-tag-success\"], [\"pInputText\", \"\", \"disabled\", \"\", 1, \"w-full\", 3, \"value\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", \"class\", \"p-button-outlined mr-2\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Save Changes\", \"icon\", \"pi pi-check\", \"class\", \"p-button-success mr-2\", 3, \"loading\", \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", \"class\", \"p-button-secondary\", 3, \"click\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-3\"], [1, \"text-center\"], [1, \"text-2xl\", \"font-bold\", \"text-blue-500\"], [1, \"text-600\"], [1, \"text-2xl\", \"font-bold\", \"text-green-500\"], [1, \"text-2xl\", \"font-bold\", \"text-orange-500\"], [1, \"text-2xl\", \"font-bold\", \"text-purple-500\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Edit Profile\", \"icon\", \"pi pi-pencil\", 1, \"p-button-outlined\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Save Changes\", \"icon\", \"pi pi-check\", 1, \"p-button-success\", \"mr-2\", 3, \"loading\", \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-secondary\", 3, \"click\"], [1, \"text-center\", \"p-4\"], [1, \"pi\", \"pi-exclamation-triangle\", \"text-orange-500\", \"text-4xl\", \"mb-3\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Retry\", \"icon\", \"pi pi-refresh\", 3, \"click\"]],\n  template: function ProfileComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n      i0.ɵɵtext(4, \"Admin Profile\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, ProfileComponent_div_5_Template, 4, 0, \"div\", 3);\n      i0.ɵɵtemplate(6, ProfileComponent_div_6_Template, 93, 27, \"div\", 4);\n      i0.ɵɵtemplate(7, ProfileComponent_div_7_Template, 7, 0, \"div\", 5);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelement(8, \"p-toast\");\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.userProfile);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.userProfile);\n    }\n  },\n  dependencies: [i4.NgIf, i4.NgStyle, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.ButtonDirective, i7.InputText, i8.Toast, i4.DatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAGA,SAASA,cAAT,QAA+B,aAA/B;AACA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;ICEYC;IACIA;IACAA;IAAmBA;IAAkBA;;;;;;;;IAiGrBA;IAKQA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAyB,CAAzB;IACRA;;;;;;;;IACAA;IAKQA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAsB,CAAtB;IAERA;;;;;IADQA;;;;;;;;IAERA;IAKQA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IACRA;;;;;;;;;;;;;;;IAlHpBA,+BAAkD,CAAlD,EAAkD,KAAlD,EAAkD,CAAlD,EAAkD,CAAlD,EAAkD,KAAlD,EAAkD,CAAlD,EAAkD,CAAlD,EAAkD,KAAlD,EAAkD,EAAlD;IAMgBA;IACJA;IACAA,4BAAK,CAAL,EAAK,IAAL,EAAK,EAAL;IAC6BA;IAAoDA;IAC7EA;IAAwBA;IAAaA;IAMjDA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B;IAEYA;IAAoBA;IACxBA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,OAAlB,EAAkB,EAAlB;IAEuDA;IAAUA;IACzDA;IACOA;MAAAA;MAAA;MAAA,OAAaA,uDAAb;IAClC,CADkC;IADPA;IAKJA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAASA;IACxDA;IACOA;MAAAA;MAAA;MAAA,OAAaA,sDAAb;IAClC,CADkC;IADPA;IAKJA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAAKA;IACpDA;IACOA;MAAAA;MAAA;MAAA,OAAaA,kDAAb;IAClC,CADkC;IADPA;IAKJA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAAKA;IACpDA;IACOA;MAAAA;MAAA;MAAA,OAAaA,kDAAb;IAClC,CADkC;IADPA;IAWhBA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B;IAEYA;IAAmBA;IACvBA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,OAAlB,EAAkB,EAAlB;IAEuDA;IAAIA;IACnDA;IAIJA;IACAA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAAMA;IACrDA;IAAkCA;IAAMA;IAE5CA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAAYA;IAC3DA;;IAIJA;IACAA,gCAAoB,EAApB,EAAoB,OAApB,EAAoB,EAApB;IACmDA;IAAYA;IAC3DA;;IAIJA;IAMZA,gCAAoB,EAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,EAApB,EAAoB,KAApB,EAAoB,EAApB,EAAoB,EAApB,EAAoB,IAApB;IAGgBA;IAAOA;IACXA;IACIA;IAOAA;IAQAA;IAOJA;IAMZA,gCAAoB,EAApB,EAAoB,KAApB,EAAoB,CAApB,EAAoB,EAApB,EAAoB,IAApB;IAEYA;IAAgBA;IACpBA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB;IAG0DA;IAAyBA;IACvEA;IAAsBA;IAAgBA;IAG9CA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B;IAEuDA;IAAwBA;IACvEA;IAAsBA;IAAeA;IAG7CA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B;IAEwDA;IAAuBA;IACvEA;IAAsBA;IAAcA;IAG5CA,iCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B,EAA6B,EAA7B,EAA6B,KAA7B,EAA6B,EAA7B;IAEwDA;IAAoBA;IACpEA;IAAsBA;IAAWA;;;;;IA7IxCA;IAAAA;IAIwBA;IAAAA;IAcdA;IAAAA,wDAAoC,UAApC,EAAoC,gBAApC;IAOAA;IAAAA,uDAAmC,UAAnC,EAAmC,gBAAnC;IAOAA;IAAAA,mDAA+B,UAA/B,EAA+B,gBAA/B;IAOAA;IAAAA,mDAA+B,UAA/B,EAA+B,gBAA/B;IA4BAA;IAAAA;IAOAA;IAAAA;IAcEA;IAAAA;IAOAA;IAAAA;IAQAA;IAAAA;IAmByCA;IAAAA;IAMCA;IAAAA;IAMCA;IAAAA;IAMAA;IAAAA;;;;;;;;IAUxEA;IACIA;IACAA;IAAIA;IAAsBA;IAC1BA;IAAoBA;IAAkDA;IACtEA;IAGQA;MAAAA;MAAA;MAAA,OAASA,qCAAT;IAAsB,CAAtB;IACRA;;;;ADpKhB,OAAM,MAAOC,gBAAP,CAAuB;EAczBC,YACYC,WADZ,EAEYC,iBAFZ,EAGYC,cAHZ,EAG0C;IAF9B;IACA;IACA;IAhBZ,mBAAmB,IAAnB;IACA,uBAAuB,IAAvB;IACA,gBAAoB,KAApB;IACA,eAAmB,IAAnB;IACA,cAAkB,KAAlB;IAEA,aAAQ;MACJC,eAAe,EAAE,CADb;MAEJC,cAAc,EAAE,CAFZ;MAGJC,aAAa,EAAE,CAHX;MAIJC,UAAU,EAAE;IAJR,CAAR;EAWI;;EAEEC,QAAQ;IAAA;;IAAA;MACV,MAAM,KAAI,CAACC,WAAL,EAAN;MACA,MAAM,KAAI,CAACC,SAAL,EAAN;IAFU;EAGb;;EAEKD,WAAW;IAAA;;IAAA;MACb,IAAI;QACA,MAAI,CAACE,OAAL,GAAe,IAAf;QACAC,OAAO,CAACC,GAAR,CAAY,yBAAZ,EAFA,CAIA;;QACA,MAAI,CAACC,WAAL,SAAyBjB,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBc,UAAjB,EAAD,CAAtC;QACA,MAAI,CAACC,eAAL,GAAoBC,kBAAQ,MAAI,CAACH,WAAb,CAApB;QAEAF,OAAO,CAACC,GAAR,CAAY,iBAAZ,EAA+B,MAAI,CAACC,WAApC;MACH,CATD,CASE,OAAOI,KAAP,EAAc;QACZN,OAAO,CAACM,KAAR,CAAc,wBAAd,EAAwCA,KAAxC;;QACA,MAAI,CAACf,cAAL,CAAoBgB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE;QAHY,CAAxB;MAKH,CAhBD,SAgBU;QACN,MAAI,CAACX,OAAL,GAAe,KAAf;MACH;IAnBY;EAoBhB;;EAEKD,SAAS;IAAA;;IAAA;MACX,IAAI;QACA,MAAMa,cAAc,SAAS1B,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBsB,iBAAvB,EAAD,CAA1C;QACA,MAAI,CAACC,KAAL,GAAa;UACTrB,eAAe,EAAEmB,cAAc,CAACnB,eAAf,IAAkC,CAD1C;UAETC,cAAc,EAAEkB,cAAc,CAAClB,cAAf,IAAiC,CAFxC;UAGTC,aAAa,EAAEiB,cAAc,CAACjB,aAAf,IAAgC,CAHtC;UAITC,UAAU,EAAEgB,cAAc,CAAChB,UAAf,IAA6B;QAJhC,CAAb;MAMH,CARD,CAQE,OAAOW,KAAP,EAAc;QACZN,OAAO,CAACM,KAAR,CAAc,sBAAd,EAAsCA,KAAtC,EADY,CAEZ;MACH;IAZU;EAad;;EAEDQ,cAAc;IACV,KAAKC,QAAL,GAAgB,CAAC,KAAKA,QAAtB;;IACA,IAAI,KAAKA,QAAT,EAAmB;MACf,KAAKX,eAAL,GAAoBC,kBAAQ,KAAKH,WAAb,CAApB;IACH;EACJ;;EAEKc,WAAW;IAAA;;IAAA;MACb,IAAI;QACA,MAAI,CAACC,MAAL,GAAc,IAAd,CADA,CAGA;;QACA,MAAMC,cAAc,SAASjC,aAAa,CACtC,MAAI,CAACI,WAAL,CAAiB8B,aAAjB,CAA+B,MAAI,CAACjB,WAApC,CADsC,CAA1C;QAIA,MAAI,CAACA,WAAL,GAAmBgB,cAAnB;QACA,MAAI,CAACd,eAAL,GAAoBC,kBAAQa,cAAR,CAApB;QACA,MAAI,CAACH,QAAL,GAAgB,KAAhB;;QAEA,MAAI,CAACxB,cAAL,CAAoBgB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,SAFW;UAGpBC,MAAM,EAAE;QAHY,CAAxB;MAMH,CAlBD,CAkBE,OAAOJ,KAAP,EAAc;QACZN,OAAO,CAACM,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;;QACA,MAAI,CAACf,cAAL,CAAoBgB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE;QAHY,CAAxB;MAKH,CAzBD,SAyBU;QACN,MAAI,CAACO,MAAL,GAAc,KAAd;MACH;IA5BY;EA6BhB;;EAEDG,UAAU;IACN,KAAKlB,WAAL,GAAgBG,kBAAQ,KAAKD,eAAb,CAAhB;IACA,KAAKW,QAAL,GAAgB,KAAhB;IAEA,KAAKxB,cAAL,CAAoBgB,GAApB,CAAwB;MACpBC,QAAQ,EAAE,MADU;MAEpBC,OAAO,EAAE,WAFW;MAGpBC,MAAM,EAAE;IAHY,CAAxB;EAKH;;AA7GwB;;;mBAAhBvB,kBAAgBD;AAAA;;;QAAhBC;EAAgBkC;EAAAC,iCAFd,CAACtC,cAAD,CAEc;EAFEuC;EAAAC;EAAAC;EAAAC;IAAA;MCT/BxC,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,IAAlB;MAGgBA;MAAaA;MAGjBA;MAMAA;MA2JAA;MAUJA;MAIRA;;;;MA/KkBA;MAAAA;MAMAA;MAAAA;MA2JAA;MAAAA", "names": ["MessageService", "lastValueFrom", "i0", "ProfileComponent", "constructor", "authService", "statisticsService", "messageService", "totalFormations", "totalEmployees", "totalTrainers", "totalTeams", "ngOnInit", "loadProfile", "loadStats", "loading", "console", "log", "userProfile", "getProfile", "originalProfile", "Object", "error", "add", "severity", "summary", "detail", "dashboardStats", "getDashboardStats", "stats", "toggleEditMode", "editMode", "saveProfile", "saving", "updatedProfile", "updateProfile", "cancelEdit", "selectors", "features", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\profile\\profile.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../../services/auth.service';\nimport { StatisticsService } from '../../../../services/statistics.service';\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\n\n@Component({\n    selector: 'app-profile',\n    templateUrl: './profile.component.html',\n    providers: [MessageService]\n})\nexport class ProfileComponent implements OnInit {\n    userProfile: any = null;\n    originalProfile: any = null;\n    editMode: boolean = false;\n    loading: boolean = true;\n    saving: boolean = false;\n    \n    stats = {\n        totalFormations: 0,\n        totalEmployees: 0,\n        totalTrainers: 0,\n        totalTeams: 0\n    };\n\n    constructor(\n        private authService: AuthService,\n        private statisticsService: StatisticsService,\n        private messageService: MessageService\n    ) {}\n\n    async ngOnInit() {\n        await this.loadProfile();\n        await this.loadStats();\n    }\n\n    async loadProfile() {\n        try {\n            this.loading = true;\n            console.log('Loading user profile...');\n            \n            // Get current user profile\n            this.userProfile = await lastValueFrom(this.authService.getProfile());\n            this.originalProfile = { ...this.userProfile };\n            \n            console.log('Profile loaded:', this.userProfile);\n        } catch (error) {\n            console.error('Error loading profile:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to load profile information'\n            });\n        } finally {\n            this.loading = false;\n        }\n    }\n\n    async loadStats() {\n        try {\n            const dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            this.stats = {\n                totalFormations: dashboardStats.totalFormations || 0,\n                totalEmployees: dashboardStats.totalEmployees || 0,\n                totalTrainers: dashboardStats.totalTrainers || 0,\n                totalTeams: dashboardStats.totalTeams || 0\n            };\n        } catch (error) {\n            console.error('Error loading stats:', error);\n            // Don't show error for stats, just keep zeros\n        }\n    }\n\n    toggleEditMode() {\n        this.editMode = !this.editMode;\n        if (this.editMode) {\n            this.originalProfile = { ...this.userProfile };\n        }\n    }\n\n    async saveProfile() {\n        try {\n            this.saving = true;\n            \n            // Update profile via API\n            const updatedProfile = await lastValueFrom(\n                this.authService.updateProfile(this.userProfile)\n            );\n            \n            this.userProfile = updatedProfile;\n            this.originalProfile = { ...updatedProfile };\n            this.editMode = false;\n            \n            this.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'Profile updated successfully'\n            });\n            \n        } catch (error) {\n            console.error('Error saving profile:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to update profile'\n            });\n        } finally {\n            this.saving = false;\n        }\n    }\n\n    cancelEdit() {\n        this.userProfile = { ...this.originalProfile };\n        this.editMode = false;\n        \n        this.messageService.add({\n            severity: 'info',\n            summary: 'Cancelled',\n            detail: 'Changes cancelled'\n        });\n    }\n}\n", "<div class=\"grid\">\n    <div class=\"col-12\">\n        <div class=\"card\">\n            <h5>Admin Profile</h5>\n            \n            <!-- Loading State -->\n            <div *ngIf=\"loading\" class=\"flex align-items-center justify-content-center\" style=\"height: 200px;\">\n                <i class=\"pi pi-spin pi-spinner\" style=\"font-size: 2rem;\"></i>\n                <span class=\"ml-2\">Loading profile...</span>\n            </div>\n\n            <!-- Profile Content -->\n            <div *ngIf=\"!loading && userProfile\" class=\"grid\">\n                <!-- Profile Header -->\n                <div class=\"col-12\">\n                    <div class=\"flex align-items-center mb-4\">\n                        <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round mr-3\" \n                             [ngStyle]=\"{width: '4rem', height: '4rem'}\">\n                            <i class=\"pi pi-user text-blue-500 text-2xl\"></i>\n                        </div>\n                        <div>\n                            <h3 class=\"m-0 text-900\">{{userProfile.first_name}} {{userProfile.last_name}}</h3>\n                            <p class=\"m-0 text-600\">Administrator</p>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Profile Information -->\n                <div class=\"col-12 md:col-6\">\n                    <div class=\"card\">\n                        <h6>Personal Information</h6>\n                        <div class=\"grid\">\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">First Name</label>\n                                <input pInputText \n                                       [(ngModel)]=\"userProfile.first_name\" \n                                       [disabled]=\"!editMode\"\n                                       class=\"w-full\" />\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Last Name</label>\n                                <input pInputText \n                                       [(ngModel)]=\"userProfile.last_name\" \n                                       [disabled]=\"!editMode\"\n                                       class=\"w-full\" />\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Email</label>\n                                <input pInputText \n                                       [(ngModel)]=\"userProfile.email\" \n                                       [disabled]=\"!editMode\"\n                                       class=\"w-full\" />\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Phone</label>\n                                <input pInputText \n                                       [(ngModel)]=\"userProfile.phone\" \n                                       [disabled]=\"!editMode\"\n                                       placeholder=\"Enter phone number\"\n                                       class=\"w-full\" />\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Account Information -->\n                <div class=\"col-12 md:col-6\">\n                    <div class=\"card\">\n                        <h6>Account Information</h6>\n                        <div class=\"grid\">\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Role</label>\n                                <input pInputText \n                                       value=\"Administrator\" \n                                       disabled\n                                       class=\"w-full\" />\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Status</label>\n                                <span class=\"p-tag p-tag-success\">Active</span>\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Member Since</label>\n                                <input pInputText \n                                       [value]=\"userProfile.created_at | date:'mediumDate'\" \n                                       disabled\n                                       class=\"w-full\" />\n                            </div>\n                            <div class=\"col-12\">\n                                <label class=\"block text-900 font-medium mb-2\">Last Updated</label>\n                                <input pInputText \n                                       [value]=\"userProfile.updated_at | date:'medium'\" \n                                       disabled\n                                       class=\"w-full\" />\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Actions -->\n                <div class=\"col-12\">\n                    <div class=\"card\">\n                        <div class=\"flex justify-content-between align-items-center\">\n                            <h6>Actions</h6>\n                            <div>\n                                <button *ngIf=\"!editMode\" \n                                        pButton pRipple \n                                        label=\"Edit Profile\" \n                                        icon=\"pi pi-pencil\" \n                                        class=\"p-button-outlined mr-2\"\n                                        (click)=\"toggleEditMode()\">\n                                </button>\n                                <button *ngIf=\"editMode\" \n                                        pButton pRipple \n                                        label=\"Save Changes\" \n                                        icon=\"pi pi-check\" \n                                        class=\"p-button-success mr-2\"\n                                        (click)=\"saveProfile()\"\n                                        [loading]=\"saving\">\n                                </button>\n                                <button *ngIf=\"editMode\" \n                                        pButton pRipple \n                                        label=\"Cancel\" \n                                        icon=\"pi pi-times\" \n                                        class=\"p-button-secondary\"\n                                        (click)=\"cancelEdit()\">\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n\n                <!-- Statistics -->\n                <div class=\"col-12\">\n                    <div class=\"card\">\n                        <h6>Admin Statistics</h6>\n                        <div class=\"grid\">\n                            <div class=\"col-12 md:col-3\">\n                                <div class=\"text-center\">\n                                    <div class=\"text-2xl font-bold text-blue-500\">{{stats.totalFormations}}</div>\n                                    <div class=\"text-600\">Total Formations</div>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-3\">\n                                <div class=\"text-center\">\n                                    <div class=\"text-2xl font-bold text-green-500\">{{stats.totalEmployees}}</div>\n                                    <div class=\"text-600\">Total Employees</div>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-3\">\n                                <div class=\"text-center\">\n                                    <div class=\"text-2xl font-bold text-orange-500\">{{stats.totalTrainers}}</div>\n                                    <div class=\"text-600\">Total Trainers</div>\n                                </div>\n                            </div>\n                            <div class=\"col-12 md:col-3\">\n                                <div class=\"text-center\">\n                                    <div class=\"text-2xl font-bold text-purple-500\">{{stats.totalTeams}}</div>\n                                    <div class=\"text-600\">Total Teams</div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- Error State -->\n            <div *ngIf=\"!loading && !userProfile\" class=\"text-center p-4\">\n                <i class=\"pi pi-exclamation-triangle text-orange-500 text-4xl mb-3\"></i>\n                <h5>Unable to load profile</h5>\n                <p class=\"text-600\">Please try refreshing the page or contact support.</p>\n                <button pButton pRipple \n                        label=\"Retry\" \n                        icon=\"pi pi-refresh\" \n                        (click)=\"loadProfile()\">\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n\n<p-toast></p-toast>\n"]}, "metadata": {}, "sourceType": "module"}