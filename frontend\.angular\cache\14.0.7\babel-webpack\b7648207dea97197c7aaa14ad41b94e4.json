{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { lastValueFrom } from 'rxjs';\nlet DashboardComponent = class DashboardComponent {\n  constructor(productService, statisticsService, layoutService) {\n    this.productService = productService;\n    this.statisticsService = statisticsService;\n    this.layoutService = layoutService; // Statistics data\n\n    this.dashboardStats = null;\n    this.monthlyFormations = [];\n    this.loading = true;\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initChart();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initChart();\n\n      _this.productService.getProductsSmall().then(data => _this.products = data);\n\n      _this.items = [{\n        label: 'Add New',\n        icon: 'pi pi-fw pi-plus'\n      }, {\n        label: 'Remove',\n        icon: 'pi pi-fw pi-minus'\n      }]; // Load statistics data\n\n      yield _this.loadDashboardData();\n    })();\n  }\n\n  loadDashboardData() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 Loading dashboard data...');\n        _this2.loading = true; // Load dashboard stats\n\n        console.log('📊 Loading dashboard stats...');\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats());\n        console.log('✅ Dashboard stats loaded:', _this2.dashboardStats); // Load monthly formations data\n\n        console.log('📅 Loading monthly formations...');\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations());\n        console.log('✅ Monthly formations loaded:', _this2.monthlyFormations); // Update chart with real data\n\n        _this2.updateFormationsChart();\n      } catch (error) {\n        console.error('❌ Error loading dashboard data:', error);\n      } finally {\n        _this2.loading = false;\n        console.log('✅ Dashboard loading complete');\n      }\n    })();\n  }\n\n  initChart() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.chartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      datasets: [{\n        label: 'Formations',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate (%)',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4,\n        yAxisID: 'y1'\n      }]\n    };\n    this.chartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          type: 'linear',\n          display: true,\n          position: 'left',\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          ticks: {\n            color: textColorSecondary,\n            max: 100\n          },\n          grid: {\n            drawOnChartArea: false\n          }\n        }\n      }\n    };\n  }\n\n  updateFormationsChart() {\n    if (!this.monthlyFormations.length) return;\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const formationsData = new Array(12).fill(0);\n    const attendanceData = new Array(12).fill(0);\n    this.monthlyFormations.forEach(item => {\n      const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n      formationsData[monthIndex] = item.count;\n      attendanceData[monthIndex] = item.attendanceRate;\n    });\n    this.chartData = Object.assign(Object.assign({}, this.chartData), {\n      datasets: [Object.assign(Object.assign({}, this.chartData.datasets[0]), {\n        data: formationsData\n      }), Object.assign(Object.assign({}, this.chartData.datasets[1]), {\n        data: attendanceData\n      })]\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n};\nDashboardComponent = __decorate([Component({\n  templateUrl: './dashboard.component.html'\n})], DashboardComponent);\nexport { DashboardComponent };", "map": {"version": 3, "mappings": ";;AAAA,SAASA,SAAT,QAA6C,eAA7C;AAKA,SAAuBC,aAAvB,QAA4C,MAA5C;AAMA,IAAaC,kBAAkB,GAA/B,MAAaA,kBAAb,CAA+B;EAiB3BC,YACYC,cADZ,EAEYC,iBAFZ,EAGWC,aAHX,EAGuC;IAF3B;IACA;IACD,mCAA4B,CARvC;;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC;IACA,eAAmB,IAAnB;IAOI,KAAKC,YAAL,GAAoB,KAAKD,aAAL,CAAmBE,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,SAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,SAAL;;MACA,KAAI,CAACN,cAAL,CAAoBQ,gBAApB,GAAuCC,IAAvC,CAA4CC,IAAI,IAAI,KAAI,CAACC,QAAL,GAAgBD,IAApE;;MAEA,KAAI,CAACE,KAAL,GAAa,CACT;QAAEC,KAAK,EAAE,SAAT;QAAoBC,IAAI,EAAE;MAA1B,CADS,EAET;QAAED,KAAK,EAAE,QAAT;QAAmBC,IAAI,EAAE;MAAzB,CAFS,CAAb,CAJU,CASV;;MACA,MAAM,KAAI,CAACC,iBAAL,EAAN;IAVU;EAWb;;EAEKA,iBAAiB;IAAA;;IAAA;MACnB,IAAI;QACAC,OAAO,CAACC,GAAR,CAAY,8BAAZ;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CAFA,CAIA;;QACAF,OAAO,CAACC,GAAR,CAAY,+BAAZ;QACA,MAAI,CAACE,cAAL,SAA4BtB,aAAa,CAAC,MAAI,CAACI,iBAAL,CAAuBmB,iBAAvB,EAAD,CAAzC;QACAJ,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyC,MAAI,CAACE,cAA9C,EAPA,CASA;;QACAH,OAAO,CAACC,GAAR,CAAY,kCAAZ;QACA,MAAI,CAACI,iBAAL,SAA+BxB,aAAa,CAAC,MAAI,CAACI,iBAAL,CAAuBqB,oBAAvB,EAAD,CAA5C;QACAN,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4C,MAAI,CAACI,iBAAjD,EAZA,CAcA;;QACA,MAAI,CAACE,qBAAL;MAEH,CAjBD,CAiBE,OAAOC,KAAP,EAAc;QACZR,OAAO,CAACQ,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;MACH,CAnBD,SAmBU;QACN,MAAI,CAACN,OAAL,GAAe,KAAf;QACAF,OAAO,CAACC,GAAR,CAAY,8BAAZ;MACH;IAvBkB;EAwBtB;;EAEDX,SAAS;IACL,MAAMmB,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB;IAEA,KAAKG,SAAL,GAAiB;MACbC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CADK;MAEbC,QAAQ,EAAE,CACN;QACItB,KAAK,EAAE,YADX;QAEIH,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGI0B,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMIS,OAAO,EAAE;MANb,CADM,EASN;QACI1B,KAAK,EAAE,qBADX;QAEIH,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGI0B,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMIS,OAAO,EAAE,EANb;QAOIC,OAAO,EAAE;MAPb,CATM;IAFG,CAAjB;IAuBA,KAAKC,YAAL,GAAoB;MAChBC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJT,MAAM,EAAE;YACJU,KAAK,EAAEf;UADH;QADJ;MADH,CADO;MAQhBgB,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEb;UADJ,CADR;UAICiB,IAAI,EAAE;YACFJ,KAAK,EAAEZ,aADL;YAEFiB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCC,IAAI,EAAE,QADP;UAECC,OAAO,EAAE,IAFV;UAGCC,QAAQ,EAAE,MAHX;UAICN,KAAK,EAAE;YACHH,KAAK,EAAEb;UADJ,CAJR;UAOCiB,IAAI,EAAE;YACFJ,KAAK,EAAEZ,aADL;YAEFiB,UAAU,EAAE;UAFV;QAPP,CAVC;QAsBJK,EAAE,EAAE;UACAH,IAAI,EAAE,QADN;UAEAC,OAAO,EAAE,IAFT;UAGAC,QAAQ,EAAE,OAHV;UAIAN,KAAK,EAAE;YACHH,KAAK,EAAEb,kBADJ;YAEHwB,GAAG,EAAE;UAFF,CAJP;UAQAP,IAAI,EAAE;YACFQ,eAAe,EAAE;UADf;QARN;MAtBA;IARQ,CAApB;EA4CH;;EAEDjC,qBAAqB;IACjB,IAAI,CAAC,KAAKF,iBAAL,CAAuBoC,MAA5B,EAAoC;IAEpC,MAAMC,MAAM,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CAAf;IACA,MAAMC,cAAc,GAAG,IAAIC,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IACA,MAAMyB,cAAc,GAAG,IAAID,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IAEA,KAAKf,iBAAL,CAAuByC,OAAvB,CAA+BC,IAAI,IAAG;MAClC,MAAMC,UAAU,GAAG,IAAIC,IAAJ,CAASF,IAAI,CAACG,KAAL,GAAa,UAAtB,EAAkCC,QAAlC,EAAnB;MACAR,cAAc,CAACK,UAAD,CAAd,GAA6BD,IAAI,CAACK,KAAlC;MACAP,cAAc,CAACG,UAAD,CAAd,GAA6BD,IAAI,CAACM,cAAlC;IACH,CAJD;IAMA,KAAKpC,SAAL,GAAcqC,gCACP,KAAKrC,SADE,GACO;MACjBE,QAAQ,EAAE,iCAEC,KAAKF,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BzB,IAAI,EAAEiD;MADuB,EAF3B,kCAMC,KAAK1B,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BzB,IAAI,EAAEmD;MADuB,EAN3B;IADO,CADP,CAAd;EAaH;;EAEDU,WAAW;IACP,IAAI,KAAKpE,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBqE,WAAlB;IACH;EACJ;;AA7K0B,CAA/B;AAAa1E,kBAAkB,eAH9BF,SAAS,CAAC;EACP6E,WAAW,EAAE;AADN,CAAD,CAGqB,GAAlB3E,kBAAkB,CAAlB;SAAAA", "names": ["Component", "lastValueFrom", "DashboardComponent", "constructor", "productService", "statisticsService", "layoutService", "subscription", "configUpdate$", "subscribe", "initChart", "ngOnInit", "getProductsSmall", "then", "data", "products", "items", "label", "icon", "loadDashboardData", "console", "log", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "updateFormationsChart", "error", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "chartData", "labels", "datasets", "fill", "backgroundColor", "borderColor", "tension", "yAxisID", "chartOptions", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "drawBorder", "y", "type", "display", "position", "y1", "max", "drawOnChartArea", "length", "months", "formationsData", "Array", "attendanceData", "for<PERSON>ach", "item", "monthIndex", "Date", "month", "getMonth", "count", "attendanceRate", "Object", "ngOnDestroy", "unsubscribe", "templateUrl"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { MenuItem } from 'primeng/api';\nimport { Product } from '../../api/product';\nimport { ProductService } from '../../service/product.service';\nimport { StatisticsService, DashboardStats, MonthlyFormations } from '../../../services/statistics.service';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\n\n@Component({\n    templateUrl: './dashboard.component.html',\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n\n    items!: MenuItem[];\n\n    products!: Product[];\n\n    chartData: any;\n\n    chartOptions: any;\n\n    subscription!: Subscription;\n\n    // Statistics data\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n    loading: boolean = true;\n\n    constructor(\n        private productService: ProductService,\n        private statisticsService: StatisticsService,\n        public layoutService: LayoutService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initChart();\n        });\n    }\n\n    async ngOnInit() {\n        this.initChart();\n        this.productService.getProductsSmall().then(data => this.products = data);\n\n        this.items = [\n            { label: 'Add New', icon: 'pi pi-fw pi-plus' },\n            { label: 'Remove', icon: 'pi pi-fw pi-minus' }\n        ];\n\n        // Load statistics data\n        await this.loadDashboardData();\n    }\n\n    async loadDashboardData() {\n        try {\n            console.log('🔄 Loading dashboard data...');\n            this.loading = true;\n\n            // Load dashboard stats\n            console.log('📊 Loading dashboard stats...');\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            console.log('✅ Dashboard stats loaded:', this.dashboardStats);\n\n            // Load monthly formations data\n            console.log('📅 Loading monthly formations...');\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n            console.log('✅ Monthly formations loaded:', this.monthlyFormations);\n\n            // Update chart with real data\n            this.updateFormationsChart();\n\n        } catch (error) {\n            console.error('❌ Error loading dashboard data:', error);\n        } finally {\n            this.loading = false;\n            console.log('✅ Dashboard loading complete');\n        }\n    }\n\n    initChart() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        this.chartData = {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate (%)',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4,\n                    yAxisID: 'y1'\n                }\n            ]\n        };\n\n        this.chartOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    type: 'linear',\n                    display: true,\n                    position: 'left',\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    ticks: {\n                        color: textColorSecondary,\n                        max: 100\n                    },\n                    grid: {\n                        drawOnChartArea: false,\n                    },\n                }\n            }\n        };\n    }\n\n    updateFormationsChart() {\n        if (!this.monthlyFormations.length) return;\n\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formationsData = new Array(12).fill(0);\n        const attendanceData = new Array(12).fill(0);\n\n        this.monthlyFormations.forEach(item => {\n            const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n            formationsData[monthIndex] = item.count;\n            attendanceData[monthIndex] = item.attendanceRate;\n        });\n\n        this.chartData = {\n            ...this.chartData,\n            datasets: [\n                {\n                    ...this.chartData.datasets[0],\n                    data: formationsData\n                },\n                {\n                    ...this.chartData.datasets[1],\n                    data: attendanceData\n                }\n            ]\n        };\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}