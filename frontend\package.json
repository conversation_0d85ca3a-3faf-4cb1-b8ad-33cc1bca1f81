{"name": "sakai-ng", "version": "14.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~14.0.0", "@angular/cdk": "~14.0.0", "@angular/common": "~14.0.0", "@angular/compiler": "~14.0.0", "@angular/core": "~14.0.0", "@angular/forms": "~14.0.0", "@angular/platform-browser": "~14.0.0", "@angular/platform-browser-dynamic": "~14.0.0", "@angular/router": "~14.0.0", "chart.js": "^3.3.2", "primeflex": "^3.2.0", "primeicons": "5.0.0", "primeng": "14.0.0", "prismjs": "1.9.0", "rxjs": "~7.5.0", "tslib": "^2.3.0", "web-animations-js": "^2.3.2", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~14.0.3", "@angular/cli": "~14.0.3", "@angular/compiler-cli": "~14.0.0", "@types/jasmine": "~3.10.0", "@types/jasminewd2": "~2.0.8", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.10.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.7.2"}}