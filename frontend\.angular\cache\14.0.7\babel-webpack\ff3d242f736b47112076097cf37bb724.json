{"ast": null, "code": "import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n  return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\n\nfunction animationFramesFactory(timestampProvider) {\n  return new Observable(subscriber => {\n    const provider = timestampProvider || performanceTimestampProvider;\n    const start = provider.now();\n    let id = 0;\n\n    const run = () => {\n      if (!subscriber.closed) {\n        id = animationFrameProvider.requestAnimationFrame(timestamp => {\n          id = 0;\n          const now = provider.now();\n          subscriber.next({\n            timestamp: timestampProvider ? now : timestamp,\n            elapsed: now - start\n          });\n          run();\n        });\n      }\n    };\n\n    run();\n    return () => {\n      if (id) {\n        animationFrameProvider.cancelAnimationFrame(id);\n      }\n    };\n  });\n}\n\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();", "map": {"version": 3, "names": ["Observable", "performanceTimestampProvider", "animationFrameProvider", "animationFrames", "timestampProvider", "animationFramesFactory", "DEFAULT_ANIMATION_FRAMES", "subscriber", "provider", "start", "now", "id", "run", "closed", "requestAnimationFrame", "timestamp", "next", "elapsed", "cancelAnimationFrame"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/dom/animationFrames.js"], "sourcesContent": ["import { Observable } from '../../Observable';\nimport { performanceTimestampProvider } from '../../scheduler/performanceTimestampProvider';\nimport { animationFrameProvider } from '../../scheduler/animationFrameProvider';\nexport function animationFrames(timestampProvider) {\n    return timestampProvider ? animationFramesFactory(timestampProvider) : DEFAULT_ANIMATION_FRAMES;\n}\nfunction animationFramesFactory(timestampProvider) {\n    return new Observable((subscriber) => {\n        const provider = timestampProvider || performanceTimestampProvider;\n        const start = provider.now();\n        let id = 0;\n        const run = () => {\n            if (!subscriber.closed) {\n                id = animationFrameProvider.requestAnimationFrame((timestamp) => {\n                    id = 0;\n                    const now = provider.now();\n                    subscriber.next({\n                        timestamp: timestampProvider ? now : timestamp,\n                        elapsed: now - start,\n                    });\n                    run();\n                });\n            }\n        };\n        run();\n        return () => {\n            if (id) {\n                animationFrameProvider.cancelAnimationFrame(id);\n            }\n        };\n    });\n}\nconst DEFAULT_ANIMATION_FRAMES = animationFramesFactory();\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,kBAA3B;AACA,SAASC,4BAAT,QAA6C,8CAA7C;AACA,SAASC,sBAAT,QAAuC,wCAAvC;AACA,OAAO,SAASC,eAAT,CAAyBC,iBAAzB,EAA4C;EAC/C,OAAOA,iBAAiB,GAAGC,sBAAsB,CAACD,iBAAD,CAAzB,GAA+CE,wBAAvE;AACH;;AACD,SAASD,sBAAT,CAAgCD,iBAAhC,EAAmD;EAC/C,OAAO,IAAIJ,UAAJ,CAAgBO,UAAD,IAAgB;IAClC,MAAMC,QAAQ,GAAGJ,iBAAiB,IAAIH,4BAAtC;IACA,MAAMQ,KAAK,GAAGD,QAAQ,CAACE,GAAT,EAAd;IACA,IAAIC,EAAE,GAAG,CAAT;;IACA,MAAMC,GAAG,GAAG,MAAM;MACd,IAAI,CAACL,UAAU,CAACM,MAAhB,EAAwB;QACpBF,EAAE,GAAGT,sBAAsB,CAACY,qBAAvB,CAA8CC,SAAD,IAAe;UAC7DJ,EAAE,GAAG,CAAL;UACA,MAAMD,GAAG,GAAGF,QAAQ,CAACE,GAAT,EAAZ;UACAH,UAAU,CAACS,IAAX,CAAgB;YACZD,SAAS,EAAEX,iBAAiB,GAAGM,GAAH,GAASK,SADzB;YAEZE,OAAO,EAAEP,GAAG,GAAGD;UAFH,CAAhB;UAIAG,GAAG;QACN,CARI,CAAL;MASH;IACJ,CAZD;;IAaAA,GAAG;IACH,OAAO,MAAM;MACT,IAAID,EAAJ,EAAQ;QACJT,sBAAsB,CAACgB,oBAAvB,CAA4CP,EAA5C;MACH;IACJ,CAJD;EAKH,CAvBM,CAAP;AAwBH;;AACD,MAAML,wBAAwB,GAAGD,sBAAsB,EAAvD"}, "metadata": {}, "sourceType": "module"}