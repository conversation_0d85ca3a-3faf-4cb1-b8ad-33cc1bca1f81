{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      hasValue = true;\n      subscriber.next(value);\n    }, () => hasValue ? subscriber.complete() : subscriber.error(errorFactory())));\n  });\n}\n\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}", "map": {"version": 3, "names": ["EmptyError", "operate", "createOperatorSubscriber", "throwIfEmpty", "errorFactory", "defaultErrorFactory", "source", "subscriber", "hasValue", "subscribe", "value", "next", "complete", "error"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/throwIfEmpty.js"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory = defaultErrorFactory) {\n    return operate((source, subscriber) => {\n        let hasValue = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            hasValue = true;\n            subscriber.next(value);\n        }, () => (hasValue ? subscriber.complete() : subscriber.error(errorFactory()))));\n    });\n}\nfunction defaultErrorFactory() {\n    return new EmptyError();\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,oBAA3B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,YAAT,CAAsBC,YAAY,GAAGC,mBAArC,EAA0D;EAC7D,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,QAAQ,GAAG,KAAf;IACAF,MAAM,CAACG,SAAP,CAAiBP,wBAAwB,CAACK,UAAD,EAAcG,KAAD,IAAW;MAC7DF,QAAQ,GAAG,IAAX;MACAD,UAAU,CAACI,IAAX,CAAgBD,KAAhB;IACH,CAHwC,EAGtC,MAAOF,QAAQ,GAAGD,UAAU,CAACK,QAAX,EAAH,GAA2BL,UAAU,CAACM,KAAX,CAAiBT,YAAY,EAA7B,CAHJ,CAAzC;EAIH,CANa,CAAd;AAOH;;AACD,SAASC,mBAAT,GAA+B;EAC3B,OAAO,IAAIL,UAAJ,EAAP;AACH"}, "metadata": {}, "sourceType": "module"}