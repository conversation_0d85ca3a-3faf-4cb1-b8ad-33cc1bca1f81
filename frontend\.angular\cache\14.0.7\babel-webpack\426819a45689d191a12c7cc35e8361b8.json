{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n  return operate((source, subscriber) => {\n    let innerSubscriber = null;\n    let index = 0;\n    let isComplete = false;\n\n    const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n      let innerIndex = 0;\n      const outerIndex = index++;\n      innerFrom(project(value, outerIndex)).subscribe(innerSubscriber = createOperatorSubscriber(subscriber, innerValue => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue), () => {\n        innerSubscriber = null;\n        checkComplete();\n      }));\n    }, () => {\n      isComplete = true;\n      checkComplete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "operate", "createOperatorSubscriber", "switchMap", "project", "resultSelector", "source", "subscriber", "innerSubscriber", "index", "isComplete", "checkComplete", "complete", "subscribe", "value", "unsubscribe", "innerIndex", "outerIndex", "innerValue", "next"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/switchMap.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n    return operate((source, subscriber) => {\n        let innerSubscriber = null;\n        let index = 0;\n        let isComplete = false;\n        const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n            let innerIndex = 0;\n            const outerIndex = index++;\n            innerFrom(project(value, outerIndex)).subscribe((innerSubscriber = createOperatorSubscriber(subscriber, (innerValue) => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue), () => {\n                innerSubscriber = null;\n                checkComplete();\n            })));\n        }, () => {\n            isComplete = true;\n            checkComplete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,yBAA1B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,SAAT,CAAmBC,OAAnB,EAA4BC,cAA5B,EAA4C;EAC/C,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,eAAe,GAAG,IAAtB;IACA,IAAIC,KAAK,GAAG,CAAZ;IACA,IAAIC,UAAU,GAAG,KAAjB;;IACA,MAAMC,aAAa,GAAG,MAAMD,UAAU,IAAI,CAACF,eAAf,IAAkCD,UAAU,CAACK,QAAX,EAA9D;;IACAN,MAAM,CAACO,SAAP,CAAiBX,wBAAwB,CAACK,UAAD,EAAcO,KAAD,IAAW;MAC7DN,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,CAACO,WAAhB,EAAlE;MACA,IAAIC,UAAU,GAAG,CAAjB;MACA,MAAMC,UAAU,GAAGR,KAAK,EAAxB;MACAT,SAAS,CAACI,OAAO,CAACU,KAAD,EAAQG,UAAR,CAAR,CAAT,CAAsCJ,SAAtC,CAAiDL,eAAe,GAAGN,wBAAwB,CAACK,UAAD,EAAcW,UAAD,IAAgBX,UAAU,CAACY,IAAX,CAAgBd,cAAc,GAAGA,cAAc,CAACS,KAAD,EAAQI,UAAR,EAAoBD,UAApB,EAAgCD,UAAU,EAA1C,CAAjB,GAAiEE,UAA/F,CAA7B,EAAyI,MAAM;QACtOV,eAAe,GAAG,IAAlB;QACAG,aAAa;MAChB,CAH0F,CAA3F;IAIH,CARwC,EAQtC,MAAM;MACLD,UAAU,GAAG,IAAb;MACAC,aAAa;IAChB,CAXwC,CAAzC;EAYH,CAjBa,CAAd;AAkBH"}, "metadata": {}, "sourceType": "module"}