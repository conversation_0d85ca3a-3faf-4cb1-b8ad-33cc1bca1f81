{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UserService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = '/api/users';\n  }\n\n  getUsers() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        return yield lastValueFrom(_this.http.get(_this.apiUrl));\n      } catch (error) {\n        console.error('Error fetching users:', error);\n        return [];\n      }\n    })();\n  }\n\n  getUsersByRole(role) {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        return yield lastValueFrom(_this2.http.get(`${_this2.apiUrl}?role=${role}`));\n      } catch (error) {\n        console.error('Error fetching users by role:', error);\n        return [];\n      }\n    })();\n  }\n\n  createUser(user) {\n    return this.http.post(this.apiUrl, user);\n  }\n\n  updateUser(id, user) {\n    return this.http.put(`${this.apiUrl}/${id}`, user);\n  }\n\n  deleteUser(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n\n  updateUserTeam(userId, teamId) {\n    return this.http.put(`${this.apiUrl}/${userId}/team`, {\n      team_id: teamId\n    });\n  }\n\n}\n\nUserService.ɵfac = function UserService_Factory(t) {\n  return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n};\n\nUserService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: UserService,\n  factory: UserService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AAEA,SAAqBA,aAArB,QAA0C,MAA1C;;;AAIA,OAAM,MAAOC,WAAP,CAAkB;EAGpBC,YAAoBC,IAApB,EAAoC;IAAhB;IAFZ,cAAS,YAAT;EAEgC;;EAElCC,QAAQ;IAAA;;IAAA;MACV,IAAI;QACA,aAAaJ,aAAa,CAAC,KAAI,CAACG,IAAL,CAAUE,GAAV,CAAsB,KAAI,CAACC,MAA3B,CAAD,CAA1B;MACH,CAFD,CAEE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;QACA,OAAO,EAAP;MACH;IANS;EAOb;;EAEKE,cAAc,CAACC,IAAD,EAAa;IAAA;;IAAA;MAC7B,IAAI;QACA,aAAaV,aAAa,CAAC,MAAI,CAACG,IAAL,CAAUE,GAAV,CAAsB,GAAG,MAAI,CAACC,MAAM,SAASI,IAAI,EAAjD,CAAD,CAA1B;MACH,CAFD,CAEE,OAAOH,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;QACA,OAAO,EAAP;MACH;IAN4B;EAOhC;;EAEDI,UAAU,CAACC,IAAD,EAAoB;IAC1B,OAAO,KAAKT,IAAL,CAAUU,IAAV,CAAqB,KAAKP,MAA1B,EAAkCM,IAAlC,CAAP;EACH;;EAEDE,UAAU,CAACC,EAAD,EAAaH,IAAb,EAAgC;IACtC,OAAO,KAAKT,IAAL,CAAUa,GAAV,CAAoB,GAAG,KAAKV,MAAM,IAAIS,EAAE,EAAxC,EAA4CH,IAA5C,CAAP;EACH;;EAEDK,UAAU,CAACF,EAAD,EAAW;IACjB,OAAO,KAAKZ,IAAL,CAAUe,MAAV,CAAiB,GAAG,KAAKZ,MAAM,IAAIS,EAAE,EAArC,CAAP;EACH;;EAEDI,cAAc,CAACC,MAAD,EAAiBC,MAAjB,EAA+B;IACzC,OAAO,KAAKlB,IAAL,CAAUa,GAAV,CAAoB,GAAG,KAAKV,MAAM,IAAIc,MAAM,OAA5C,EAAqD;MAAEE,OAAO,EAAED;IAAX,CAArD,CAAP;EACH;;AArCmB;;;mBAAXpB,aAAWsB;AAAA;;;SAAXtB;EAAWuB,SAAXvB,WAAW;EAAAwB,YADE", "names": ["lastValueFrom", "UserService", "constructor", "http", "getUsers", "get", "apiUrl", "error", "console", "getUsersByRole", "role", "createUser", "user", "post", "updateUser", "id", "put", "deleteUser", "delete", "updateUserTeam", "userId", "teamId", "team_id", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\service\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Observable, lastValueFrom } from 'rxjs';\r\nimport { User, CreateUserRequest, UpdateUserRequest } from '../../models/user.model';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class UserService {\r\n    private apiUrl = '/api/users';\r\n\r\n    constructor(private http: HttpClient) {}\r\n\r\n    async getUsers(): Promise<User[]> {\r\n        try {\r\n            return await lastValueFrom(this.http.get<User[]>(this.apiUrl));\r\n        } catch (error) {\r\n            console.error('Error fetching users:', error);\r\n            return [];\r\n        }\r\n    }\r\n\r\n    async getUsersByRole(role: string): Promise<User[]> {\r\n        try {\r\n            return await lastValueFrom(this.http.get<User[]>(`${this.apiUrl}?role=${role}`));\r\n        } catch (error) {\r\n            console.error('Error fetching users by role:', error);\r\n            return [];\r\n        }\r\n    }\r\n\r\n    createUser(user: Partial<User>): Observable<User> {\r\n        return this.http.post<User>(this.apiUrl, user);\r\n    }\r\n\r\n    updateUser(id: number, user: Partial<User>): Observable<User> {\r\n        return this.http.put<User>(`${this.apiUrl}/${id}`, user);\r\n    }\r\n\r\n    deleteUser(id: number): Observable<any> {\r\n        return this.http.delete(`${this.apiUrl}/${id}`);\r\n    }\r\n\r\n    updateUserTeam(userId: number, teamId: number): Observable<User> {\r\n        return this.http.put<User>(`${this.apiUrl}/${userId}/team`, { team_id: teamId });\r\n    }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}