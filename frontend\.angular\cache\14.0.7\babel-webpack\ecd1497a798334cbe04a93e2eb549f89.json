{"ast": null, "code": "import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\nexport function race(...args) {\n  return raceWith(...argsOrArgArray(args));\n}", "map": {"version": 3, "names": ["argsOrArgArray", "raceWith", "race", "args"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/race.js"], "sourcesContent": ["import { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\nexport function race(...args) {\n    return raceWith(...argsOrArgArray(args));\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,wBAA/B;AACA,SAASC,QAAT,QAAyB,YAAzB;AACA,OAAO,SAASC,IAAT,CAAc,GAAGC,IAAjB,EAAuB;EAC1B,OAAOF,QAAQ,CAAC,GAAGD,cAAc,CAACG,IAAD,CAAlB,CAAf;AACH"}, "metadata": {}, "sourceType": "module"}