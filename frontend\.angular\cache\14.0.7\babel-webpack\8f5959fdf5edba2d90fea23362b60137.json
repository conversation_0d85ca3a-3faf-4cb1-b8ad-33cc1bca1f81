{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction UIMessage_div_0_div_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r4.text, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction UIMessage_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, UIMessage_div_0_div_2_span_1_Template, 1, 1, \"span\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.escape);\n  }\n}\n\nfunction UIMessage_div_0_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r5.text);\n  }\n}\n\nfunction UIMessage_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, UIMessage_div_0_ng_template_3_span_0_Template, 2, 1, \"span\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.escape);\n  }\n}\n\nconst _c0 = function (a0, a1, a2, a3, a4) {\n  return {\n    \"p-inline-message-info\": a0,\n    \"p-inline-message-warn\": a1,\n    \"p-inline-message-error\": a2,\n    \"p-inline-message-success\": a3,\n    \"p-inline-message-icon-only\": a4\n  };\n};\n\nfunction UIMessage_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵtemplate(2, UIMessage_div_0_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵtemplate(3, UIMessage_div_0_ng_template_3_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(4);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.style)(\"ngClass\", i0.ɵɵpureFunction5(7, _c0, ctx_r0.severity === \"info\", ctx_r0.severity === \"warn\", ctx_r0.severity === \"error\", ctx_r0.severity === \"success\", ctx_r0.text == null));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.escape)(\"ngIfElse\", _r2);\n  }\n}\n\nclass UIMessage {\n  constructor() {\n    this.escape = true;\n  }\n\n  get icon() {\n    let icon = null;\n\n    if (this.severity) {\n      switch (this.severity) {\n        case 'success':\n          icon = 'pi pi-check';\n          break;\n\n        case 'info':\n          icon = 'pi pi-info-circle';\n          break;\n\n        case 'error':\n          icon = 'pi pi-times-circle';\n          break;\n\n        case 'warn':\n          icon = 'pi pi-exclamation-triangle';\n          break;\n\n        default:\n          icon = 'pi pi-info-circle';\n          break;\n      }\n    }\n\n    return icon;\n  }\n\n}\n\nUIMessage.ɵfac = function UIMessage_Factory(t) {\n  return new (t || UIMessage)();\n};\n\nUIMessage.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: UIMessage,\n  selectors: [[\"p-message\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    severity: \"severity\",\n    text: \"text\",\n    escape: \"escape\",\n    style: \"style\",\n    styleClass: \"styleClass\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[\"aria-live\", \"polite\", \"class\", \"p-inline-message p-component p-inline-message\", 3, \"ngStyle\", \"class\", \"ngClass\", 4, \"ngIf\"], [\"aria-live\", \"polite\", 1, \"p-inline-message\", \"p-component\", \"p-inline-message\", 3, \"ngStyle\", \"ngClass\"], [1, \"p-inline-message-icon\", 3, \"ngClass\"], [4, \"ngIf\", \"ngIfElse\"], [\"escapeOut\", \"\"], [\"class\", \"p-inline-message-text\", 3, \"innerHTML\", 4, \"ngIf\"], [1, \"p-inline-message-text\", 3, \"innerHTML\"], [\"class\", \"p-inline-message-text\", 4, \"ngIf\"], [1, \"p-inline-message-text\"]],\n  template: function UIMessage_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, UIMessage_div_0_Template, 5, 13, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.severity);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n  styles: [\".p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UIMessage, [{\n    type: Component,\n    args: [{\n      selector: 'p-message',\n      template: `\n        <div aria-live=\"polite\" class=\"p-inline-message p-component p-inline-message\" *ngIf=\"severity\" [ngStyle]=\"style\" [class]=\"styleClass\"\n        [ngClass]=\"{'p-inline-message-info': (severity === 'info'),\n                'p-inline-message-warn': (severity === 'warn'),\n                'p-inline-message-error': (severity === 'error'),\n                'p-inline-message-success': (severity === 'success'),\n                'p-inline-message-icon-only': this.text == null}\">\n            <span class=\"p-inline-message-icon\" [ngClass]=\"icon\"></span>\n            <div *ngIf=\"!escape; else escapeOut\">\n                <span *ngIf=\"!escape\" class=\"p-inline-message-text\" [innerHTML]=\"text\"></span>\n            </div>\n            <ng-template #escapeOut>\n                <span *ngIf=\"escape\" class=\"p-inline-message-text\">{{text}}</span>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}\\n\"]\n    }]\n  }], null, {\n    severity: [{\n      type: Input\n    }],\n    text: [{\n      type: Input\n    }],\n    escape: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\nclass MessageModule {}\n\nMessageModule.ɵfac = function MessageModule_Factory(t) {\n  return new (t || MessageModule)();\n};\n\nMessageModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MessageModule\n});\nMessageModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MessageModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [UIMessage],\n      declarations: [UIMessage]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MessageModule, UIMessage };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "UIMessage", "constructor", "escape", "icon", "severity", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "text", "style", "styleClass", "MessageModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-message.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass UIMessage {\n    constructor() {\n        this.escape = true;\n    }\n    get icon() {\n        let icon = null;\n        if (this.severity) {\n            switch (this.severity) {\n                case 'success':\n                    icon = 'pi pi-check';\n                    break;\n                case 'info':\n                    icon = 'pi pi-info-circle';\n                    break;\n                case 'error':\n                    icon = 'pi pi-times-circle';\n                    break;\n                case 'warn':\n                    icon = 'pi pi-exclamation-triangle';\n                    break;\n                default:\n                    icon = 'pi pi-info-circle';\n                    break;\n            }\n        }\n        return icon;\n    }\n}\nUIMessage.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UIMessage, deps: [], target: i0.ɵɵFactoryTarget.Component });\nUIMessage.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: UIMessage, selector: \"p-message\", inputs: { severity: \"severity\", text: \"text\", escape: \"escape\", style: \"style\", styleClass: \"styleClass\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div aria-live=\"polite\" class=\"p-inline-message p-component p-inline-message\" *ngIf=\"severity\" [ngStyle]=\"style\" [class]=\"styleClass\"\n        [ngClass]=\"{'p-inline-message-info': (severity === 'info'),\n                'p-inline-message-warn': (severity === 'warn'),\n                'p-inline-message-error': (severity === 'error'),\n                'p-inline-message-success': (severity === 'success'),\n                'p-inline-message-icon-only': this.text == null}\">\n            <span class=\"p-inline-message-icon\" [ngClass]=\"icon\"></span>\n            <div *ngIf=\"!escape; else escapeOut\">\n                <span *ngIf=\"!escape\" class=\"p-inline-message-text\" [innerHTML]=\"text\"></span>\n            </div>\n            <ng-template #escapeOut>\n                <span *ngIf=\"escape\" class=\"p-inline-message-text\">{{text}}</span>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\".p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UIMessage, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-message', template: `\n        <div aria-live=\"polite\" class=\"p-inline-message p-component p-inline-message\" *ngIf=\"severity\" [ngStyle]=\"style\" [class]=\"styleClass\"\n        [ngClass]=\"{'p-inline-message-info': (severity === 'info'),\n                'p-inline-message-warn': (severity === 'warn'),\n                'p-inline-message-error': (severity === 'error'),\n                'p-inline-message-success': (severity === 'success'),\n                'p-inline-message-icon-only': this.text == null}\">\n            <span class=\"p-inline-message-icon\" [ngClass]=\"icon\"></span>\n            <div *ngIf=\"!escape; else escapeOut\">\n                <span *ngIf=\"!escape\" class=\"p-inline-message-text\" [innerHTML]=\"text\"></span>\n            </div>\n            <ng-template #escapeOut>\n                <span *ngIf=\"escape\" class=\"p-inline-message-text\">{{text}}</span>\n            </ng-template>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-inline-message{display:inline-flex;align-items:center;justify-content:center;vertical-align:top}.p-inline-message-icon-only .p-inline-message-text{visibility:hidden;width:0}.p-fluid .p-inline-message{display:flex}\\n\"] }]\n        }], propDecorators: { severity: [{\n                type: Input\n            }], text: [{\n                type: Input\n            }], escape: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }] } });\nclass MessageModule {\n}\nMessageModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MessageModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMessageModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: MessageModule, declarations: [UIMessage], imports: [CommonModule], exports: [UIMessage] });\nMessageModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MessageModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MessageModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [UIMessage],\n                    declarations: [UIMessage]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MessageModule, UIMessage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;IA8B4FP,EAU5E,wB;;;;mBAV4EA,E;IAAAA,EAUxB,sCAVwBA,EAUxB,gB;;;;;;IAVwBA,EAShF,yB;IATgFA,EAU5E,sE;IAV4EA,EAWhF,e;;;;mBAXgFA,E;IAAAA,EAUrE,a;IAVqEA,EAUrE,mC;;;;;;IAVqEA,EAa5E,6B;IAb4EA,EAazB,U;IAbyBA,EAajB,e;;;;mBAbiBA,E;IAAAA,EAazB,a;IAbyBA,EAazB,+B;;;;;;IAbyBA,EAa5E,8E;;;;mBAb4EA,E;IAAAA,EAarE,kC;;;;;;;;;;;;;;;;IAbqEA,EAEpF,4B;IAFoFA,EAQhF,wB;IARgFA,EAShF,8D;IATgFA,EAYhF,qFAZgFA,EAYhF,wB;IAZgFA,EAepF,e;;;;gBAfoFA,E;;mBAAAA,E;IAAAA,EAE6B,8B;IAF7BA,EAEW,gDAFXA,EAEW,kK;IAFXA,EAQ5C,a;IAR4CA,EAQ5C,mC;IAR4CA,EAS1E,a;IAT0EA,EAS1E,oD;;;;AArClB,MAAMQ,SAAN,CAAgB;EACZC,WAAW,GAAG;IACV,KAAKC,MAAL,GAAc,IAAd;EACH;;EACO,IAAJC,IAAI,GAAG;IACP,IAAIA,IAAI,GAAG,IAAX;;IACA,IAAI,KAAKC,QAAT,EAAmB;MACf,QAAQ,KAAKA,QAAb;QACI,KAAK,SAAL;UACID,IAAI,GAAG,aAAP;UACA;;QACJ,KAAK,MAAL;UACIA,IAAI,GAAG,mBAAP;UACA;;QACJ,KAAK,OAAL;UACIA,IAAI,GAAG,oBAAP;UACA;;QACJ,KAAK,MAAL;UACIA,IAAI,GAAG,4BAAP;UACA;;QACJ;UACIA,IAAI,GAAG,mBAAP;UACA;MAfR;IAiBH;;IACD,OAAOA,IAAP;EACH;;AA1BW;;AA4BhBH,SAAS,CAACK,IAAV;EAAA,iBAAsGL,SAAtG;AAAA;;AACAA,SAAS,CAACM,IAAV,kBAD4Fd,EAC5F;EAAA,MAA0FQ,SAA1F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD4FR,EAEpF,yDADR;IAAA;;IAAA;MAD4FA,EAEL,iCADvF;IAAA;EAAA;EAAA,eAeySM,EAAE,CAACS,OAf5S,EAeuYT,EAAE,CAACU,IAf1Y,EAe2eV,EAAE,CAACW,OAf9e;EAAA;EAAA;EAAA;AAAA;;AAgBA;EAAA,mDAjB4FjB,EAiB5F,mBAA2FQ,SAA3F,EAAkH,CAAC;IACvGU,IAAI,EAAEjB,SADiG;IAEvGkB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAZ;MAAyBC,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAfmB;MAeZC,eAAe,EAAEpB,uBAAuB,CAACqB,MAf7B;MAeqCC,aAAa,EAAErB,iBAAiB,CAACsB,IAftE;MAe4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAflF;MAiBIC,MAAM,EAAE,CAAC,4NAAD;IAjBZ,CAAD;EAFiG,CAAD,CAAlH,QAoB4B;IAAEf,QAAQ,EAAE,CAAC;MACzBM,IAAI,EAAEd;IADmB,CAAD,CAAZ;IAEZwB,IAAI,EAAE,CAAC;MACPV,IAAI,EAAEd;IADC,CAAD,CAFM;IAIZM,MAAM,EAAE,CAAC;MACTQ,IAAI,EAAEd;IADG,CAAD,CAJI;IAMZyB,KAAK,EAAE,CAAC;MACRX,IAAI,EAAEd;IADE,CAAD,CANK;IAQZ0B,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEd;IADO,CAAD;EARA,CApB5B;AAAA;;AA+BA,MAAM2B,aAAN,CAAoB;;AAEpBA,aAAa,CAAClB,IAAd;EAAA,iBAA0GkB,aAA1G;AAAA;;AACAA,aAAa,CAACC,IAAd,kBAnD4FhC,EAmD5F;EAAA,MAA2G+B;AAA3G;AACAA,aAAa,CAACE,IAAd,kBApD4FjC,EAoD5F;EAAA,UAAoIO,YAApI;AAAA;;AACA;EAAA,mDArD4FP,EAqD5F,mBAA2F+B,aAA3F,EAAsH,CAAC;IAC3Gb,IAAI,EAAEb,QADqG;IAE3Gc,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC3B,YAAD,CADV;MAEC4B,OAAO,EAAE,CAAC3B,SAAD,CAFV;MAGC4B,YAAY,EAAE,CAAC5B,SAAD;IAHf,CAAD;EAFqG,CAAD,CAAtH;AAAA;AASA;AACA;AACA;;;AAEA,SAASuB,aAAT,EAAwBvB,SAAxB"}, "metadata": {}, "sourceType": "module"}