{"ast": null, "code": "export var fromCodePoint = String.fromCodePoint || function (astralCodePoint) {\n  return String.fromCharCode(Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800, (astralCodePoint - 0x10000) % 0x400 + 0xdc00);\n}; // @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\n\nexport var getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n};\nexport var highSurrogateFrom = 0xd800;\nexport var highSurrogateTo = 0xdbff;", "map": {"version": 3, "names": ["fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/html-entities/dist/esm/surrogate-pairs.js"], "sourcesContent": ["export var fromCodePoint = String.fromCodePoint ||\n    function (astralCodePoint) {\n        return String.fromCharCode(Math.floor((astralCodePoint - 0x10000) / 0x400) + 0xd800, ((astralCodePoint - 0x10000) % 0x400) + 0xdc00);\n    };\n// @ts-expect-error - String.prototype.codePointAt might not exist in older node versions\nexport var getCodePoint = String.prototype.codePointAt\n    ? function (input, position) {\n        return input.codePointAt(position);\n    }\n    : function (input, position) {\n        return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n    };\nexport var highSurrogateFrom = 0xd800;\nexport var highSurrogateTo = 0xdbff;\n"], "mappings": "AAAA,OAAO,IAAIA,aAAa,GAAGC,MAAM,CAACD,aAAP,IACvB,UAAUE,eAAV,EAA2B;EACvB,OAAOD,MAAM,CAACE,YAAP,CAAoBC,IAAI,CAACC,KAAL,CAAW,CAACH,eAAe,GAAG,OAAnB,IAA8B,KAAzC,IAAkD,MAAtE,EAA+E,CAACA,eAAe,GAAG,OAAnB,IAA8B,KAA/B,GAAwC,MAAtH,CAAP;AACH,CAHE,C,CAIP;;AACA,OAAO,IAAII,YAAY,GAAGL,MAAM,CAACM,SAAP,CAAiBC,WAAjB,GACpB,UAAUC,KAAV,EAAiBC,QAAjB,EAA2B;EACzB,OAAOD,KAAK,CAACD,WAAN,CAAkBE,QAAlB,CAAP;AACH,CAHqB,GAIpB,UAAUD,KAAV,EAAiBC,QAAjB,EAA2B;EACzB,OAAO,CAACD,KAAK,CAACE,UAAN,CAAiBD,QAAjB,IAA6B,MAA9B,IAAwC,KAAxC,GAAgDD,KAAK,CAACE,UAAN,CAAiBD,QAAQ,GAAG,CAA5B,CAAhD,GAAiF,MAAjF,GAA0F,OAAjG;AACH,CANE;AAOP,OAAO,IAAIE,iBAAiB,GAAG,MAAxB;AACP,OAAO,IAAIC,eAAe,GAAG,MAAtB"}, "metadata": {}, "sourceType": "module"}