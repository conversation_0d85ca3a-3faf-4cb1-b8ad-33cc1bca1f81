{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/statistics.service\";\nimport * as i2 from \"src/app/layout/service/app.layout.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/chart\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/button\";\n\nfunction DashboardComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6);\n    i0.ɵɵelement(3, \"i\", 7);\n    i0.ɵɵelementStart(4, \"span\", 8);\n    i0.ɵɵtext(5, \"Loading dashboard...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nconst _c0 = function () {\n  return {\n    width: \"2.5rem\",\n    height: \"2.5rem\"\n  };\n};\n\nfunction DashboardComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\")(4, \"span\", 12);\n    i0.ɵɵtext(5, \"Total Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 14);\n    i0.ɵɵelement(9, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 17);\n    i0.ɵɵtext(13, \"formations\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardStats.totalFormations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.dashboardStats.upcomingFormations, \" upcoming \");\n  }\n}\n\nfunction DashboardComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\")(4, \"span\", 12);\n    i0.ɵɵtext(5, \"Total Employees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 18);\n    i0.ɵɵelement(9, \"i\", 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 17);\n    i0.ɵɵtext(13, \"organized\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totalEmployees);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.dashboardStats.totalTeams, \" teams \");\n  }\n}\n\nfunction DashboardComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\")(4, \"span\", 12);\n    i0.ɵɵtext(5, \"Trainers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 20);\n    i0.ɵɵelement(9, \"i\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11, \"Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 17);\n    i0.ɵɵtext(13, \"trainers available\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.dashboardStats.totalTrainers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\n\nfunction DashboardComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\")(4, \"span\", 12);\n    i0.ɵɵtext(5, \"Attendance Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 22);\n    i0.ɵɵelement(9, \"i\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 16);\n    i0.ɵɵtext(11, \"Global \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 17);\n    i0.ɵɵtext(13, \"attendance rate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.dashboardStats.globalAttendanceRate, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\n\nconst _c1 = function () {\n  return {\n    height: \"8px\"\n  };\n};\n\nconst _c2 = function () {\n  return {\n    width: \"100%\"\n  };\n};\n\nconst _c3 = function () {\n  return {\n    width: \"80%\"\n  };\n};\n\nconst _c4 = function () {\n  return {\n    width: \"90%\"\n  };\n};\n\nfunction DashboardComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 5)(2, \"h5\");\n    i0.ɵɵtext(3, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 0)(5, \"div\", 25);\n    i0.ɵɵelement(6, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 25);\n    i0.ɵɵelement(8, \"button\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 25);\n    i0.ɵɵelement(10, \"button\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 25);\n    i0.ɵɵelement(12, \"button\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 5)(14, \"div\", 30)(15, \"h5\");\n    i0.ɵɵtext(16, \"Recent Activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"ul\", 31)(18, \"li\", 32)(19, \"div\")(20, \"span\", 33);\n    i0.ɵɵtext(21, \"Formation Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 34);\n    i0.ɵɵtext(23, \"Web Development Training\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 35)(25, \"div\", 36);\n    i0.ɵɵelement(26, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 38);\n    i0.ɵɵtext(28, \"Active\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"li\", 32)(30, \"div\")(31, \"span\", 33);\n    i0.ɵɵtext(32, \"Employee Registered\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 34);\n    i0.ɵɵtext(34, \"New team member added\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 39)(36, \"div\", 36);\n    i0.ɵɵelement(37, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 41);\n    i0.ɵɵtext(39, \"Recent\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(40, \"li\", 32)(41, \"div\")(42, \"span\", 33);\n    i0.ɵɵtext(43, \"Training Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 34);\n    i0.ɵɵtext(45, \"UI/UX Design Workshop\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 39)(47, \"div\", 36);\n    i0.ɵɵelement(48, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\", 43);\n    i0.ɵɵtext(50, \"90% Attendance\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    i0.ɵɵadvance(25);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(6, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(7, _c2));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(9, _c3));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(10, _c1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(11, _c4));\n  }\n}\n\nfunction DashboardComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 5)(2, \"h5\");\n    i0.ɵɵtext(3, \"Formations Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"p-chart\", 44);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r6.chartData)(\"options\", ctx_r6.chartOptions);\n  }\n}\n\nexport class DashboardComponent {\n  constructor(statisticsService, layoutService) {\n    this.statisticsService = statisticsService;\n    this.layoutService = layoutService; // Statistics data\n\n    this.dashboardStats = null;\n    this.monthlyFormations = [];\n    this.loading = true;\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initChart();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initChart(); // Load statistics data\n\n\n      yield _this.loadDashboardData();\n    })();\n  }\n\n  loadDashboardData() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 Loading dashboard data...');\n        _this2.loading = true; // Load dashboard stats\n\n        console.log('📊 Loading dashboard stats...');\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats());\n        console.log('✅ Dashboard stats loaded:', _this2.dashboardStats); // Load monthly formations data\n\n        console.log('📅 Loading monthly formations...');\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations());\n        console.log('✅ Monthly formations loaded:', _this2.monthlyFormations); // Update chart with real data\n\n        _this2.updateFormationsChart();\n      } catch (error) {\n        console.error('❌ Error loading dashboard data:', error);\n      } finally {\n        _this2.loading = false;\n        console.log('✅ Dashboard loading complete');\n      }\n    })();\n  }\n\n  initChart() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.chartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      datasets: [{\n        label: 'Formations',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate (%)',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4,\n        yAxisID: 'y1'\n      }]\n    };\n    this.chartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          type: 'linear',\n          display: true,\n          position: 'left',\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          ticks: {\n            color: textColorSecondary,\n            max: 100\n          },\n          grid: {\n            drawOnChartArea: false\n          }\n        }\n      }\n    };\n  }\n\n  updateFormationsChart() {\n    if (!this.monthlyFormations.length) return;\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const formationsData = new Array(12).fill(0);\n    const attendanceData = new Array(12).fill(0);\n    this.monthlyFormations.forEach(item => {\n      const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n      formationsData[monthIndex] = item.count;\n      attendanceData[monthIndex] = item.attendanceRate;\n    });\n    this.chartData = Object.assign(Object.assign({}, this.chartData), {\n      datasets: [Object.assign(Object.assign({}, this.chartData.datasets[0]), {\n        data: formationsData\n      }), Object.assign(Object.assign({}, this.chartData.datasets[1]), {\n        data: attendanceData\n      })]\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nDashboardComponent.ɵfac = function DashboardComponent_Factory(t) {\n  return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.StatisticsService), i0.ɵɵdirectiveInject(i2.LayoutService));\n};\n\nDashboardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DashboardComponent,\n  selectors: [[\"ng-component\"]],\n  decls: 8,\n  vars: 7,\n  consts: [[1, \"grid\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12 lg:col-6 xl:col-3\", 4, \"ngIf\"], [\"class\", \"col-12 xl:col-6\", 4, \"ngIf\"], [1, \"col-12\"], [1, \"card\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"200px\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\", 2, \"font-size\", \"2rem\"], [1, \"ml-2\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-3\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-calendar\", \"text-blue-500\", \"text-xl\"], [1, \"text-green-500\", \"font-medium\"], [1, \"text-500\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-users\", \"text-orange-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-cyan-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-user\", \"text-cyan-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-chart-line\", \"text-purple-500\", \"text-xl\"], [1, \"col-12\", \"xl:col-6\"], [1, \"col-6\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"New Formation\", \"icon\", \"pi pi-plus\", \"routerLink\", \"/uikit/crud/formations\", 1, \"p-button-outlined\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Manage Users\", \"icon\", \"pi pi-users\", \"routerLink\", \"/uikit/crud/employees\", 1, \"p-button-outlined\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"View Statistics\", \"icon\", \"pi pi-chart-bar\", \"routerLink\", \"/uikit/charts\", 1, \"p-button-outlined\", \"w-full\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Manage Teams\", \"icon\", \"pi pi-sitemap\", \"routerLink\", \"/uikit/crud/teams\", 1, \"p-button-outlined\", \"w-full\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-5\"], [1, \"list-none\", \"p-0\", \"m-0\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"md:justify-content-between\", \"mb-4\"], [1, \"text-900\", \"font-medium\", \"mr-2\", \"mb-1\", \"md:mb-0\"], [1, \"mt-1\", \"text-600\"], [1, \"mt-2\", \"md:mt-0\", \"flex\", \"align-items-center\"], [1, \"surface-300\", \"border-round\", \"overflow-hidden\", \"w-10rem\", \"lg:w-6rem\", 3, \"ngStyle\"], [1, \"bg-green-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-green-500\", \"ml-3\", \"font-medium\"], [1, \"mt-2\", \"md:mt-0\", \"ml-0\", \"md:ml-8\", \"flex\", \"align-items-center\"], [1, \"bg-cyan-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-cyan-500\", \"ml-3\", \"font-medium\"], [1, \"bg-pink-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-pink-500\", \"ml-3\", \"font-medium\"], [\"type\", \"line\", 3, \"data\", \"options\"]],\n  template: function DashboardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, DashboardComponent_div_1_Template, 6, 0, \"div\", 1);\n      i0.ɵɵtemplate(2, DashboardComponent_div_2_Template, 14, 4, \"div\", 2);\n      i0.ɵɵtemplate(3, DashboardComponent_div_3_Template, 14, 4, \"div\", 2);\n      i0.ɵɵtemplate(4, DashboardComponent_div_4_Template, 14, 3, \"div\", 2);\n      i0.ɵɵtemplate(5, DashboardComponent_div_5_Template, 14, 3, \"div\", 2);\n      i0.ɵɵtemplate(6, DashboardComponent_div_6_Template, 51, 12, \"div\", 3);\n      i0.ɵɵtemplate(7, DashboardComponent_div_7_Template, 5, 2, \"div\", 3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n    }\n  },\n  dependencies: [i3.NgIf, i3.NgStyle, i4.UIChart, i5.RouterLink, i6.ButtonDirective],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAEA,SAAuBA,aAAvB,QAA4C,MAA5C;;;;;;;;;;;ICAQC,+BAAoC,CAApC,EAAoC,KAApC,EAAoC,CAApC,EAAoC,CAApC,EAAoC,KAApC,EAAoC,CAApC;IAGYA;IACAA;IAAmBA;IAAoBA;;;;;;;;;;;;;IAMnDA,+BAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAgBA;IAC9DA;IAA0CA;IAAkCA;IAEhFA;IACIA;IACJA;IAEJA;IAAyCA;IAA+CA;IACxFA;IAAuBA;IAAUA;;;;;IAPiBA;IAAAA;IAEuCA;IAAAA;IAIhDA;IAAAA;;;;;;IAIjDA,+BAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAiCA;IAE/EA;IACIA;IACJA;IAEJA;IAAyCA;IAAoCA;IAC7EA;IAAuBA;IAASA;;;;;IAPkBA;IAAAA;IAEyCA;IAAAA;IAIlDA;IAAAA;;;;;;IAIjDA,+BAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAQA;IACtDA;IAA0CA;IAAgCA;IAE9EA;IACIA;IACJA;IAEJA;IAAyCA;IAAOA;IAChDA;IAAuBA;IAAkBA;;;;;IAPSA;IAAAA;IAEuCA;IAAAA;;;;;;IAQjGA,+BAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAwCA;IAEtFA;IACIA;IACJA;IAEJA;IAAyCA;IAAOA;IAChDA;IAAuBA;IAAeA;;;;;IAPYA;IAAAA;IAEyCA;IAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnGA,gCAAgE,CAAhE,EAAgE,KAAhE,EAAgE,CAAhE,EAAgE,CAAhE,EAAgE,IAAhE;IAEYA;IAAaA;IACjBA,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB;IAEQA;IAMJA;IACAA;IACIA;IAMJA;IACAA;IACIA;IAMJA;IACAA;IACIA;IAMJA;IAGRA,gCAAkB,EAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,EAAlB,EAAkB,IAAlB;IAEYA;IAAeA;IAEvBA,gCAA8B,EAA9B,EAA8B,IAA9B,EAA8B,EAA9B,EAA8B,EAA9B,EAA8B,KAA9B,EAA8B,EAA9B,EAA8B,MAA9B,EAA8B,EAA9B;IAGiEA;IAAiBA;IACtEA;IAA2BA;IAAwBA;IAEvDA,iCAAkD,EAAlD,EAAkD,KAAlD,EAAkD,EAAlD;IAEQA;IACJA;IACAA;IAA8CA;IAAMA;IAG5DA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;IAE6DA;IAAmBA;IACxEA;IAA2BA;IAAqBA;IAEpDA,iCAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;IAEQA;IACJA;IACAA;IAA6CA;IAAMA;IAG3DA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;IAE6DA;IAAkBA;IACvEA;IAA2BA;IAAqBA;IAEpDA,iCAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;IAEQA;IACJA;IACAA;IAA6CA;IAAcA;;;;IA3BaA;IAAAA;IACnCA;IAAAA;IAWmCA;IAAAA;IACpCA;IAAAA;IAWoCA;IAAAA;IACpCA;IAAAA;;;;;;IAUxDA,gCAA8C,CAA9C,EAA8C,KAA9C,EAA8C,CAA9C,EAA8C,CAA9C,EAA8C,IAA9C;IAEYA;IAAmBA;IACvBA;IACJA;;;;;IADyBA;IAAAA,wCAAkB,SAAlB,EAAkBC,mBAAlB;;;;ADzJrC,OAAM,MAAOC,kBAAP,CAAyB;EAW3BC,YACYC,iBADZ,EAEWC,aAFX,EAEuC;IAD3B;IACD,mCAA4B,CAPvC;;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC;IACA,eAAmB,IAAnB;IAMI,KAAKC,YAAL,GAAoB,KAAKD,aAAL,CAAmBE,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,SAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,SAAL,GADU,CAEV;;;MACA,MAAM,KAAI,CAACE,iBAAL,EAAN;IAHU;EAIb;;EAEKA,iBAAiB;IAAA;;IAAA;MACnB,IAAI;QACAC,OAAO,CAACC,GAAR,CAAY,8BAAZ;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CAFA,CAIA;;QACAF,OAAO,CAACC,GAAR,CAAY,+BAAZ;QACA,MAAI,CAACE,cAAL,SAA4BhB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBY,iBAAvB,EAAD,CAAzC;QACAJ,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyC,MAAI,CAACE,cAA9C,EAPA,CASA;;QACAH,OAAO,CAACC,GAAR,CAAY,kCAAZ;QACA,MAAI,CAACI,iBAAL,SAA+BlB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBc,oBAAvB,EAAD,CAA5C;QACAN,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4C,MAAI,CAACI,iBAAjD,EAZA,CAcA;;QACA,MAAI,CAACE,qBAAL;MAEH,CAjBD,CAiBE,OAAOC,KAAP,EAAc;QACZR,OAAO,CAACQ,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;MACH,CAnBD,SAmBU;QACN,MAAI,CAACN,OAAL,GAAe,KAAf;QACAF,OAAO,CAACC,GAAR,CAAY,8BAAZ;MACH;IAvBkB;EAwBtB;;EAEDJ,SAAS;IACL,MAAMY,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB;IAEA,KAAKG,SAAL,GAAiB;MACbC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CADK;MAEbC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,YADX;QAEIC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIC,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEd,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIU,WAAW,EAAEf,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMIW,OAAO,EAAE;MANb,CADM,EASN;QACIL,KAAK,EAAE,qBADX;QAEIC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIC,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEd,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIU,WAAW,EAAEf,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMIW,OAAO,EAAE,EANb;QAOIC,OAAO,EAAE;MAPb,CATM;IAFG,CAAjB;IAuBA,KAAKC,YAAL,GAAoB;MAChBC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJX,MAAM,EAAE;YACJY,KAAK,EAAEjB;UADH;QADJ;MADH,CADO;MAQhBkB,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEf;UADJ,CADR;UAICmB,IAAI,EAAE;YACFJ,KAAK,EAAEd,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCC,IAAI,EAAE,QADP;UAECC,OAAO,EAAE,IAFV;UAGCC,QAAQ,EAAE,MAHX;UAICN,KAAK,EAAE;YACHH,KAAK,EAAEf;UADJ,CAJR;UAOCmB,IAAI,EAAE;YACFJ,KAAK,EAAEd,aADL;YAEFmB,UAAU,EAAE;UAFV;QAPP,CAVC;QAsBJK,EAAE,EAAE;UACAH,IAAI,EAAE,QADN;UAEAC,OAAO,EAAE,IAFT;UAGAC,QAAQ,EAAE,OAHV;UAIAN,KAAK,EAAE;YACHH,KAAK,EAAEf,kBADJ;YAEH0B,GAAG,EAAE;UAFF,CAJP;UAQAP,IAAI,EAAE;YACFQ,eAAe,EAAE;UADf;QARN;MAtBA;IARQ,CAApB;EA4CH;;EAEDnC,qBAAqB;IACjB,IAAI,CAAC,KAAKF,iBAAL,CAAuBsC,MAA5B,EAAoC;IAEpC,MAAMC,MAAM,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CAAf;IACA,MAAMC,cAAc,GAAG,IAAIC,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IACA,MAAMyB,cAAc,GAAG,IAAID,KAAJ,CAAU,EAAV,EAAcxB,IAAd,CAAmB,CAAnB,CAAvB;IAEA,KAAKjB,iBAAL,CAAuB2C,OAAvB,CAA+BC,IAAI,IAAG;MAClC,MAAMC,UAAU,GAAG,IAAIC,IAAJ,CAASF,IAAI,CAACG,KAAL,GAAa,UAAtB,EAAkCC,QAAlC,EAAnB;MACAR,cAAc,CAACK,UAAD,CAAd,GAA6BD,IAAI,CAACK,KAAlC;MACAP,cAAc,CAACG,UAAD,CAAd,GAA6BD,IAAI,CAACM,cAAlC;IACH,CAJD;IAMA,KAAKtC,SAAL,GAAcuC,gCACP,KAAKvC,SADE,GACO;MACjBE,QAAQ,EAAE,iCAEC,KAAKF,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BE,IAAI,EAAEwB;MADuB,EAF3B,kCAMC,KAAK5B,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BE,IAAI,EAAE0B;MADuB,EAN3B;IADO,CADP,CAAd;EAaH;;EAEDU,WAAW;IACP,IAAI,KAAK/D,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBgE,WAAlB;IACH;EACJ;;AA/J0B;;;mBAAlBpE,oBAAkBF;AAAA;;;QAAlBE;EAAkBqE;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCR3B3E;MAEIA;MAUAA;MAeAA;MAeAA;MAeAA;MAiBAA;MAoFAA;MA9JJA;;;;MAEUA;MAAAA;MAUAA;MAAAA;MAeAA;MAAAA;MAeAA;MAAAA;MAeAA;MAAAA;MAiBAA;MAAAA;MAoFAA;MAAAA", "names": ["lastValueFrom", "i0", "ctx_r6", "DashboardComponent", "constructor", "statisticsService", "layoutService", "subscription", "configUpdate$", "subscribe", "initChart", "ngOnInit", "loadDashboardData", "console", "log", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "updateFormationsChart", "error", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "chartData", "labels", "datasets", "label", "data", "fill", "backgroundColor", "borderColor", "tension", "yAxisID", "chartOptions", "plugins", "legend", "color", "scales", "x", "ticks", "grid", "drawBorder", "y", "type", "display", "position", "y1", "max", "drawOnChartArea", "length", "months", "formationsData", "Array", "attendanceData", "for<PERSON>ach", "item", "monthIndex", "Date", "month", "getMonth", "count", "attendanceRate", "Object", "ngOnDestroy", "unsubscribe", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { StatisticsService, DashboardStats, MonthlyFormations } from '../../../services/statistics.service';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\n\n@Component({\n    templateUrl: './dashboard.component.html',\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n\n    chartData: any;\n    chartOptions: any;\n    subscription!: Subscription;\n\n    // Statistics data\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n    loading: boolean = true;\n\n    constructor(\n        private statisticsService: StatisticsService,\n        public layoutService: LayoutService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initChart();\n        });\n    }\n\n    async ngOnInit() {\n        this.initChart();\n        // Load statistics data\n        await this.loadDashboardData();\n    }\n\n    async loadDashboardData() {\n        try {\n            console.log('🔄 Loading dashboard data...');\n            this.loading = true;\n\n            // Load dashboard stats\n            console.log('📊 Loading dashboard stats...');\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            console.log('✅ Dashboard stats loaded:', this.dashboardStats);\n\n            // Load monthly formations data\n            console.log('📅 Loading monthly formations...');\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n            console.log('✅ Monthly formations loaded:', this.monthlyFormations);\n\n            // Update chart with real data\n            this.updateFormationsChart();\n\n        } catch (error) {\n            console.error('❌ Error loading dashboard data:', error);\n        } finally {\n            this.loading = false;\n            console.log('✅ Dashboard loading complete');\n        }\n    }\n\n    initChart() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        this.chartData = {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate (%)',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4,\n                    yAxisID: 'y1'\n                }\n            ]\n        };\n\n        this.chartOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    type: 'linear',\n                    display: true,\n                    position: 'left',\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    ticks: {\n                        color: textColorSecondary,\n                        max: 100\n                    },\n                    grid: {\n                        drawOnChartArea: false,\n                    },\n                }\n            }\n        };\n    }\n\n    updateFormationsChart() {\n        if (!this.monthlyFormations.length) return;\n\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formationsData = new Array(12).fill(0);\n        const attendanceData = new Array(12).fill(0);\n\n        this.monthlyFormations.forEach(item => {\n            const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n            formationsData[monthIndex] = item.count;\n            attendanceData[monthIndex] = item.attendanceRate;\n        });\n\n        this.chartData = {\n            ...this.chartData,\n            datasets: [\n                {\n                    ...this.chartData.datasets[0],\n                    data: formationsData\n                },\n                {\n                    ...this.chartData.datasets[1],\n                    data: attendanceData\n                }\n            ]\n        };\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n", "    <div class=\"grid\">\n        <!-- Loading State -->\n        <div *ngIf=\"loading\" class=\"col-12\">\n            <div class=\"card\">\n                <div class=\"flex align-items-center justify-content-center\" style=\"height: 200px;\">\n                    <i class=\"pi pi-spin pi-spinner\" style=\"font-size: 2rem;\"></i>\n                    <span class=\"ml-2\">Loading dashboard...</span>\n                </div>\n            </div>\n        </div>\n\n        <!-- Statistics Cards -->\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Total Formations</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalFormations}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-calendar text-blue-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">{{dashboardStats.upcomingFormations}} upcoming </span>\n                <span class=\"text-500\">formations</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Total Employees</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalEmployees}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-orange-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-users text-orange-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">{{dashboardStats.totalTeams}} teams </span>\n                <span class=\"text-500\">organized</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Trainers</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalTrainers}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-cyan-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-user text-cyan-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">Active </span>\n                <span class=\"text-500\">trainers available</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Attendance Rate</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.globalAttendanceRate}}%</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-purple-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-chart-line text-purple-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">Global </span>\n                <span class=\"text-500\">attendance rate</span>\n            </div>\n        </div>\n\n        <!-- Upcoming Formations -->\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 xl:col-6\">\n            <div class=\"card\">\n                <h5>Quick Actions</h5>\n                <div class=\"grid\">\n                    <div class=\"col-6\">\n                        <button pButton pRipple\n                                label=\"New Formation\"\n                                icon=\"pi pi-plus\"\n                                class=\"p-button-outlined w-full\"\n                                routerLink=\"/uikit/crud/formations\">\n                        </button>\n                    </div>\n                    <div class=\"col-6\">\n                        <button pButton pRipple\n                                label=\"Manage Users\"\n                                icon=\"pi pi-users\"\n                                class=\"p-button-outlined w-full\"\n                                routerLink=\"/uikit/crud/employees\">\n                        </button>\n                    </div>\n                    <div class=\"col-6\">\n                        <button pButton pRipple\n                                label=\"View Statistics\"\n                                icon=\"pi pi-chart-bar\"\n                                class=\"p-button-outlined w-full\"\n                                routerLink=\"/uikit/charts\">\n                        </button>\n                    </div>\n                    <div class=\"col-6\">\n                        <button pButton pRipple\n                                label=\"Manage Teams\"\n                                icon=\"pi pi-sitemap\"\n                                class=\"p-button-outlined w-full\"\n                                routerLink=\"/uikit/crud/teams\">\n                        </button>\n                    </div>\n                </div>\n            </div>\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center mb-5\">\n                    <h5>Recent Activity</h5>\n                </div>\n                <ul class=\"list-none p-0 m-0\">\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Formation Created</span>\n                            <div class=\"mt-1 text-600\">Web Development Training</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-green-500 h-full\" [ngStyle]=\"{width: '100%'}\"></div>\n                            </div>\n                            <span class=\"text-green-500 ml-3 font-medium\">Active</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Employee Registered</span>\n                            <div class=\"mt-1 text-600\">New team member added</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-cyan-500 h-full\" [ngStyle]=\"{width: '80%'}\"></div>\n                            </div>\n                            <span class=\"text-cyan-500 ml-3 font-medium\">Recent</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Training Completed</span>\n                            <div class=\"mt-1 text-600\">UI/UX Design Workshop</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-pink-500 h-full\" [ngStyle]=\"{width: '90%'}\"></div>\n                            </div>\n                            <span class=\"text-pink-500 ml-3 font-medium\">90% Attendance</span>\n                        </div>\n                    </li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- Chart Section -->\n        <div *ngIf=\"!loading\" class=\"col-12 xl:col-6\">\n            <div class=\"card\">\n                <h5>Formations Overview</h5>\n                <p-chart type=\"line\" [data]=\"chartData\" [options]=\"chartOptions\"></p-chart>\n            </div>\n        </div>\n\n\n"]}, "metadata": {}, "sourceType": "module"}