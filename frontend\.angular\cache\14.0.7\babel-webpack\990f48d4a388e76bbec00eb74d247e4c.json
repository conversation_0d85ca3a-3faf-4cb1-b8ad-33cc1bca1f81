{"ast": null, "code": "import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n  return new Observable(subscriber => subscribable.subscribe(subscriber));\n}", "map": {"version": 3, "names": ["Observable", "fromSubscribable", "subscribable", "subscriber", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/fromSubscribable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n    return new Observable((subscriber) => subscribable.subscribe(subscriber));\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,OAAO,SAASC,gBAAT,CAA0BC,YAA1B,EAAwC;EAC3C,OAAO,IAAIF,UAAJ,CAAgBG,UAAD,IAAgBD,YAAY,CAACE,SAAb,CAAuBD,UAAvB,CAA/B,CAAP;AACH"}, "metadata": {}, "sourceType": "module"}