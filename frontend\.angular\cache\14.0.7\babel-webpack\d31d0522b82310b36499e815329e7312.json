{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n  if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n    timestampProvider = selectorOrScheduler;\n  }\n\n  const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n  return source => multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n}", "map": {"version": 3, "names": ["ReplaySubject", "multicast", "isFunction", "publishReplay", "bufferSize", "windowTime", "selectorOrScheduler", "timestampProvider", "selector", "undefined", "source"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/publishReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n    if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n        timestampProvider = selectorOrScheduler;\n    }\n    const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n    return (source) => multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,kBAA9B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,OAAO,SAASC,aAAT,CAAuBC,UAAvB,EAAmCC,UAAnC,EAA+CC,mBAA/C,EAAoEC,iBAApE,EAAuF;EAC1F,IAAID,mBAAmB,IAAI,CAACJ,UAAU,CAACI,mBAAD,CAAtC,EAA6D;IACzDC,iBAAiB,GAAGD,mBAApB;EACH;;EACD,MAAME,QAAQ,GAAGN,UAAU,CAACI,mBAAD,CAAV,GAAkCA,mBAAlC,GAAwDG,SAAzE;EACA,OAAQC,MAAD,IAAYT,SAAS,CAAC,IAAID,aAAJ,CAAkBI,UAAlB,EAA8BC,UAA9B,EAA0CE,iBAA1C,CAAD,EAA+DC,QAA/D,CAAT,CAAkFE,MAAlF,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}