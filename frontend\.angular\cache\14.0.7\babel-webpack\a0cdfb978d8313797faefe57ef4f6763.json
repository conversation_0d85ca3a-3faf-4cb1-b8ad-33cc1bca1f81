{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function distinct(keySelector, flushes) {\n  return operate((source, subscriber) => {\n    const distinctKeys = new Set();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const key = keySelector ? keySelector(value) : value;\n\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes === null || flushes === void 0 ? void 0 : flushes.subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "noop", "distinct", "keySelector", "flushes", "source", "subscriber", "distinctKeys", "Set", "subscribe", "value", "key", "has", "add", "next", "clear"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/distinct.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function distinct(keySelector, flushes) {\n    return operate((source, subscriber) => {\n        const distinctKeys = new Set();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const key = keySelector ? keySelector(value) : value;\n            if (!distinctKeys.has(key)) {\n                distinctKeys.add(key);\n                subscriber.next(value);\n            }\n        }));\n        flushes === null || flushes === void 0 ? void 0 : flushes.subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,SAASC,QAAT,CAAkBC,WAAlB,EAA+BC,OAA/B,EAAwC;EAC3C,OAAOL,OAAO,CAAC,CAACM,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,YAAY,GAAG,IAAIC,GAAJ,EAArB;IACAH,MAAM,CAACI,SAAP,CAAiBT,wBAAwB,CAACM,UAAD,EAAcI,KAAD,IAAW;MAC7D,MAAMC,GAAG,GAAGR,WAAW,GAAGA,WAAW,CAACO,KAAD,CAAd,GAAwBA,KAA/C;;MACA,IAAI,CAACH,YAAY,CAACK,GAAb,CAAiBD,GAAjB,CAAL,EAA4B;QACxBJ,YAAY,CAACM,GAAb,CAAiBF,GAAjB;QACAL,UAAU,CAACQ,IAAX,CAAgBJ,KAAhB;MACH;IACJ,CANwC,CAAzC;IAOAN,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACK,SAAR,CAAkBT,wBAAwB,CAACM,UAAD,EAAa,MAAMC,YAAY,CAACQ,KAAb,EAAnB,EAAyCd,IAAzC,CAA1C,CAAlD;EACH,CAVa,CAAd;AAWH"}, "metadata": {}, "sourceType": "module"}