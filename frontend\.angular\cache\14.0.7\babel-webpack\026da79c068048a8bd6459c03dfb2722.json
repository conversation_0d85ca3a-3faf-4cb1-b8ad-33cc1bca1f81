{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    var _a;\n\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n\n    const {\n      actions\n    } = scheduler;\n\n    if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      animationFrameProvider.cancelAnimationFrame(id);\n      scheduler._scheduled = undefined;\n    }\n\n    return undefined;\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "animationFrameProvider", "AnimationFrameAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "requestAnimationFrame", "flush", "undefined", "recycleAsyncId", "_a", "length", "cancelAnimationFrame"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/AnimationFrameAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { animationFrameProvider } from './animationFrameProvider';\nexport class AnimationFrameAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = animationFrameProvider.requestAnimationFrame(() => scheduler.flush(undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            animationFrameProvider.cancelAnimationFrame(id);\n            scheduler._scheduled = undefined;\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,sBAAT,QAAuC,0BAAvC;AACA,OAAO,MAAMC,oBAAN,SAAmCF,WAAnC,CAA+C;EAClDG,WAAW,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACzB,MAAMD,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;EACH;;EACDC,cAAc,CAACF,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIA,KAAK,KAAK,IAAV,IAAkBA,KAAK,GAAG,CAA9B,EAAiC;MAC7B,OAAO,MAAMF,cAAN,CAAqBF,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACDJ,SAAS,CAACK,OAAV,CAAkBC,IAAlB,CAAuB,IAAvB;IACA,OAAON,SAAS,CAACO,UAAV,KAAyBP,SAAS,CAACO,UAAV,GAAuBV,sBAAsB,CAACW,qBAAvB,CAA6C,MAAMR,SAAS,CAACS,KAAV,CAAgBC,SAAhB,CAAnD,CAAhD,CAAP;EACH;;EACDC,cAAc,CAACX,SAAD,EAAYG,EAAZ,EAAgBC,KAAK,GAAG,CAAxB,EAA2B;IACrC,IAAIQ,EAAJ;;IACA,IAAIR,KAAK,IAAI,IAAT,GAAgBA,KAAK,GAAG,CAAxB,GAA4B,KAAKA,KAAL,GAAa,CAA7C,EAAgD;MAC5C,OAAO,MAAMO,cAAN,CAAqBX,SAArB,EAAgCG,EAAhC,EAAoCC,KAApC,CAAP;IACH;;IACD,MAAM;MAAEC;IAAF,IAAcL,SAApB;;IACA,IAAIG,EAAE,IAAI,IAAN,IAAc,CAAC,CAACS,EAAE,GAAGP,OAAO,CAACA,OAAO,CAACQ,MAAR,GAAiB,CAAlB,CAAb,MAAuC,IAAvC,IAA+CD,EAAE,KAAK,KAAK,CAA3D,GAA+D,KAAK,CAApE,GAAwEA,EAAE,CAACT,EAA5E,MAAoFA,EAAtG,EAA0G;MACtGN,sBAAsB,CAACiB,oBAAvB,CAA4CX,EAA5C;MACAH,SAAS,CAACO,UAAV,GAAuBG,SAAvB;IACH;;IACD,OAAOA,SAAP;EACH;;AAxBiD"}, "metadata": {}, "sourceType": "module"}