{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n  const hasConfig = typeof config === 'object';\n  return new Promise((resolve, reject) => {\n    let _hasValue = false;\n\n    let _value;\n\n    source.subscribe({\n      next: value => {\n        _value = value;\n        _hasValue = true;\n      },\n      error: reject,\n      complete: () => {\n        if (_hasValue) {\n          resolve(_value);\n        } else if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "lastValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "_hasValue", "_value", "subscribe", "next", "value", "error", "complete", "defaultValue"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/lastValueFrom.js"], "sourcesContent": ["import { EmptyError } from './util/EmptyError';\nexport function lastValueFrom(source, config) {\n    const hasConfig = typeof config === 'object';\n    return new Promise((resolve, reject) => {\n        let _hasValue = false;\n        let _value;\n        source.subscribe({\n            next: (value) => {\n                _value = value;\n                _hasValue = true;\n            },\n            error: reject,\n            complete: () => {\n                if (_hasValue) {\n                    resolve(_value);\n                }\n                else if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,OAAO,SAASC,aAAT,CAAuBC,MAAvB,EAA+BC,MAA/B,EAAuC;EAC1C,MAAMC,SAAS,GAAG,OAAOD,MAAP,KAAkB,QAApC;EACA,OAAO,IAAIE,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;IACpC,IAAIC,SAAS,GAAG,KAAhB;;IACA,IAAIC,MAAJ;;IACAP,MAAM,CAACQ,SAAP,CAAiB;MACbC,IAAI,EAAGC,KAAD,IAAW;QACbH,MAAM,GAAGG,KAAT;QACAJ,SAAS,GAAG,IAAZ;MACH,CAJY;MAKbK,KAAK,EAAEN,MALM;MAMbO,QAAQ,EAAE,MAAM;QACZ,IAAIN,SAAJ,EAAe;UACXF,OAAO,CAACG,MAAD,CAAP;QACH,CAFD,MAGK,IAAIL,SAAJ,EAAe;UAChBE,OAAO,CAACH,MAAM,CAACY,YAAR,CAAP;QACH,CAFI,MAGA;UACDR,MAAM,CAAC,IAAIP,UAAJ,EAAD,CAAN;QACH;MACJ;IAhBY,CAAjB;EAkBH,CArBM,CAAP;AAsBH"}, "metadata": {}, "sourceType": "module"}