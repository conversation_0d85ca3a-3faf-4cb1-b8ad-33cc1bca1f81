<div class="grid">
    <div class="col-12">
        <div class="card">
            <h5>Statistics Dashboard</h5>
            
            <!-- Date Filter -->
            <div class="flex align-items-center gap-3 mb-4">
                <div class="field">
                    <label for="startDate">Start Date</label>
                    <p-calendar 
                        id="startDate" 
                        [(ngModel)]="startDate" 
                        dateFormat="yy-mm-dd"
                        placeholder="Select start date">
                    </p-calendar>
                </div>
                <div class="field">
                    <label for="endDate">End Date</label>
                    <p-calendar 
                        id="endDate" 
                        [(ngModel)]="endDate" 
                        dateFormat="yy-mm-dd"
                        placeholder="Select end date">
                    </p-calendar>
                </div>
                <p-button 
                    label="Apply Filter" 
                    icon="pi pi-filter" 
                    (onClick)="applyDateFilter()"
                    [disabled]="!startDate || !endDate">
                </p-button>
                <p-button 
                    label="Clear" 
                    icon="pi pi-times" 
                    class="p-button-secondary"
                    (onClick)="clearDateFilter()">
                </p-button>
            </div>

            <!-- Tabs -->
            <p-tabView [(activeIndex)]="selectedTab">
                
                <!-- Formation Statistics Tab -->
                <p-tabPanel header="Formation Statistics" leftIcon="pi pi-chart-bar">
                    <div class="card">
                        <div class="flex justify-content-between align-items-center mb-4">
                            <h6>Formation Attendance Statistics</h6>
                            <div class="flex gap-2">
                                <p-button 
                                    label="Export PDF" 
                                    icon="pi pi-file-pdf" 
                                    class="p-button-danger p-button-sm"
                                    (onClick)="exportToPDF('formations')">
                                </p-button>
                                <p-button 
                                    label="Export CSV" 
                                    icon="pi pi-file-excel" 
                                    class="p-button-success p-button-sm"
                                    (onClick)="exportToCSV('formations')">
                                </p-button>
                            </div>
                        </div>
                        
                        <p-table 
                            [value]="formationStats" 
                            [loading]="loadingFormations"
                            [paginator]="true" 
                            [rows]="10"
                            [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                            [rowsPerPageOptions]="[10,25,50]"
                            styleClass="p-datatable-gridlines">
                            
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Formation</th>
                                    <th>Date</th>
                                    <th>Team</th>
                                    <th>Trainer</th>
                                    <th>Total Participants</th>
                                    <th>Present</th>
                                    <th>Absent</th>
                                    <th>Attendance Rate</th>
                                </tr>
                            </ng-template>
                            
                            <ng-template pTemplate="body" let-stat>
                                <tr>
                                    <td>{{stat.name}}</td>
                                    <td>{{stat.date | date:'short'}}</td>
                                    <td>{{stat.team}}</td>
                                    <td>{{stat.trainer}}</td>
                                    <td>{{stat.totalParticipants}}</td>
                                    <td>
                                        <span class="text-green-600 font-semibold">{{stat.presentCount}}</span>
                                    </td>
                                    <td>
                                        <span class="text-red-600 font-semibold">{{stat.absentCount}}</span>
                                    </td>
                                    <td>
                                        <p-progressBar 
                                            [value]="stat.attendanceRate" 
                                            [showValue]="true"
                                            [style]="{'height': '20px'}"
                                            [styleClass]="stat.attendanceRate >= 80 ? 'progress-success' : stat.attendanceRate >= 60 ? 'progress-warning' : 'progress-danger'">
                                        </p-progressBar>
                                    </td>
                                </tr>
                            </ng-template>
                            
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="8" class="text-center">No formation statistics found</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </p-tabPanel>

                <!-- Team Statistics Tab -->
                <p-tabPanel header="Team Statistics" leftIcon="pi pi-users">
                    <div class="card">
                        <div class="flex justify-content-between align-items-center mb-4">
                            <h6>Team Performance Statistics</h6>
                            <div class="flex gap-2">
                                <p-button 
                                    label="Export PDF" 
                                    icon="pi pi-file-pdf" 
                                    class="p-button-danger p-button-sm"
                                    (onClick)="exportToPDF('teams')">
                                </p-button>
                                <p-button 
                                    label="Export CSV" 
                                    icon="pi pi-file-excel" 
                                    class="p-button-success p-button-sm"
                                    (onClick)="exportToCSV('teams')">
                                </p-button>
                            </div>
                        </div>
                        
                        <p-table 
                            [value]="teamStats" 
                            [loading]="loadingTeams"
                            [paginator]="true" 
                            [rows]="10"
                            styleClass="p-datatable-gridlines">
                            
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Team</th>
                                    <th>Speciality</th>
                                    <th>Total Employees</th>
                                    <th>Total Formations</th>
                                    <th>Attendance Rate</th>
                                    <th>Avg Formations/Employee</th>
                                </tr>
                            </ng-template>
                            
                            <ng-template pTemplate="body" let-team>
                                <tr>
                                    <td>{{team.name}}</td>
                                    <td>{{team.speciality || 'N/A'}}</td>
                                    <td>{{team.totalEmployees}}</td>
                                    <td>{{team.totalFormations}}</td>
                                    <td>
                                        <p-progressBar 
                                            [value]="team.attendanceRate" 
                                            [showValue]="true"
                                            [style]="{'height': '20px'}">
                                        </p-progressBar>
                                    </td>
                                    <td>{{team.averageFormationsPerEmployee}}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </p-tabPanel>

                <!-- Employee Statistics Tab -->
                <p-tabPanel header="Employee Statistics" leftIcon="pi pi-user">
                    <div class="card">
                        <div class="flex justify-content-between align-items-center mb-4">
                            <h6>Employee Attendance Statistics</h6>
                            <div class="flex gap-2">
                                <p-button 
                                    label="Export PDF" 
                                    icon="pi pi-file-pdf" 
                                    class="p-button-danger p-button-sm"
                                    (onClick)="exportToPDF('employees')">
                                </p-button>
                                <p-button 
                                    label="Export CSV" 
                                    icon="pi pi-file-excel" 
                                    class="p-button-success p-button-sm"
                                    (onClick)="exportToCSV('employees')">
                                </p-button>
                            </div>
                        </div>
                        
                        <p-table 
                            [value]="employeeStats" 
                            [loading]="loadingEmployees"
                            [paginator]="true" 
                            [rows]="20"
                            styleClass="p-datatable-gridlines">
                            
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Employee</th>
                                    <th>Email</th>
                                    <th>Team</th>
                                    <th>Total Formations</th>
                                    <th>Present</th>
                                    <th>Absent</th>
                                    <th>Attendance Rate</th>
                                </tr>
                            </ng-template>
                            
                            <ng-template pTemplate="body" let-employee>
                                <tr>
                                    <td>{{employee.name}}</td>
                                    <td>{{employee.email}}</td>
                                    <td>{{employee.team}}</td>
                                    <td>{{employee.totalFormations}}</td>
                                    <td>
                                        <span class="text-green-600 font-semibold">{{employee.presentCount}}</span>
                                    </td>
                                    <td>
                                        <span class="text-red-600 font-semibold">{{employee.absentCount}}</span>
                                    </td>
                                    <td>
                                        <p-progressBar 
                                            [value]="employee.attendanceRate" 
                                            [showValue]="true"
                                            [style]="{'height': '20px'}"
                                            [styleClass]="employee.attendanceRate >= 80 ? 'progress-success' : employee.attendanceRate >= 60 ? 'progress-warning' : 'progress-danger'">
                                        </p-progressBar>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>
                    </div>
                </p-tabPanel>

            </p-tabView>
        </div>
    </div>
</div>

<!-- Attendance Chart -->
<div class="grid" *ngIf="attendanceChartData">
    <div class="col-12">
        <div class="card">
            <h5>Attendance Trends</h5>
            <p-chart type="line" [data]="attendanceChartData" [options]="attendanceChartOptions"></p-chart>
        </div>
    </div>
</div>
