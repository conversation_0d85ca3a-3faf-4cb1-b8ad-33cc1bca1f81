{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class UserService {\n  constructor() {\n    // Replace with real API calls\n    this.users = [{\n      id: 1,\n      name: 'Admin',\n      email: '<EMAIL>',\n      role: 'admin'\n    }, {\n      id: 2,\n      name: 'Formateur',\n      email: '<EMAIL>',\n      role: 'formateur'\n    }, {\n      id: 3,\n      name: 'Employ<PERSON>',\n      email: '<EMAIL>',\n      role: 'employe'\n    }];\n  }\n\n  getUsers() {\n    // Simulate async API\n    return Promise.resolve(this.users);\n  }\n\n}\n\nUserService.ɵfac = function UserService_Factory(t) {\n  return new (t || UserService)();\n};\n\nUserService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: UserService,\n  factory: UserService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AAKA,OAAM,MAAOA,WAAP,CAAkB;EADxBC;IAEI;IACQ,aAAgB,CACpB;MAAEC,EAAE,EAAE,CAAN;MAASC,IAAI,EAAE,OAAf;MAAwBC,KAAK,EAAE,mBAA/B;MAAoDC,IAAI,EAAE;IAA1D,CADoB,EAEpB;MAAEH,EAAE,EAAE,CAAN;MAASC,IAAI,EAAE,WAAf;MAA4BC,KAAK,EAAE,uBAAnC;MAA4DC,IAAI,EAAE;IAAlE,CAFoB,EAGpB;MAAEH,EAAE,EAAE,CAAN;MAASC,IAAI,EAAE,SAAf;MAA0BC,KAAK,EAAE,qBAAjC;MAAwDC,IAAI,EAAE;IAA9D,CAHoB,CAAhB;EAYX;;EANGC,QAAQ;IACJ;IACA,OAAOC,OAAO,CAACC,OAAR,CAAgB,KAAKC,KAArB,CAAP;EACH;;AAXmB;;;mBAAXT;AAAW;;;SAAXA;EAAWU,SAAXV,WAAW;EAAAW,YADE", "names": ["UserService", "constructor", "id", "name", "email", "role", "getUsers", "Promise", "resolve", "users", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\service\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { User } from '../models/user.model';\r\nimport { Observable, of } from 'rxjs';\r\n\r\n@Injectable({ providedIn: 'root' })\r\nexport class UserService {\r\n    // Replace with real API calls\r\n    private users: User[] = [\r\n        { id: 1, name: 'Admin', email: '<EMAIL>', role: 'admin' },\r\n        { id: 2, name: 'Formateur', email: '<EMAIL>', role: 'formateur' },\r\n        { id: 3, name: 'Employé', email: '<EMAIL>', role: 'employe' }\r\n    ];\r\n\r\n    getUsers(): Promise<User[]> {\r\n        // Simulate async API\r\n        return Promise.resolve(this.users);\r\n    }\r\n\r\n    // Add more CRUD methods as needed\r\n}\r\n"]}, "metadata": {}, "sourceType": "module"}