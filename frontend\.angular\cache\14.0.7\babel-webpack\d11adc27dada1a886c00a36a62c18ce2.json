{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n  const buffer = [];\n  let active = 0;\n  let index = 0;\n  let isComplete = false;\n\n  const checkComplete = () => {\n    if (isComplete && !buffer.length && !active) {\n      subscriber.complete();\n    }\n  };\n\n  const outerNext = value => active < concurrent ? doInnerSub(value) : buffer.push(value);\n\n  const doInnerSub = value => {\n    expand && subscriber.next(value);\n    active++;\n    let innerComplete = false;\n    innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, innerValue => {\n      onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n\n      if (expand) {\n        outerNext(innerValue);\n      } else {\n        subscriber.next(innerValue);\n      }\n    }, () => {\n      innerComplete = true;\n    }, undefined, () => {\n      if (innerComplete) {\n        try {\n          active--;\n\n          while (buffer.length && active < concurrent) {\n            const bufferedValue = buffer.shift();\n\n            if (innerSubScheduler) {\n              executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n            } else {\n              doInnerSub(bufferedValue);\n            }\n          }\n\n          checkComplete();\n        } catch (err) {\n          subscriber.error(err);\n        }\n      }\n    }));\n  };\n\n  source.subscribe(createOperatorSubscriber(subscriber, outerNext, () => {\n    isComplete = true;\n    checkComplete();\n  }));\n  return () => {\n    additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n  };\n}", "map": {"version": 3, "names": ["innerFrom", "executeSchedule", "createOperatorSubscriber", "mergeInternals", "source", "subscriber", "project", "concurrent", "onBeforeNext", "expand", "innerSubScheduler", "additionalFinalizer", "buffer", "active", "index", "isComplete", "checkComplete", "length", "complete", "outerNext", "value", "doInnerSub", "push", "next", "innerComplete", "subscribe", "innerValue", "undefined", "bufferedValue", "shift", "err", "error"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/mergeInternals.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { executeSchedule } from '../util/executeSchedule';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function mergeInternals(source, subscriber, project, concurrent, onBeforeNext, expand, innerSubScheduler, additionalFinalizer) {\n    const buffer = [];\n    let active = 0;\n    let index = 0;\n    let isComplete = false;\n    const checkComplete = () => {\n        if (isComplete && !buffer.length && !active) {\n            subscriber.complete();\n        }\n    };\n    const outerNext = (value) => (active < concurrent ? doInnerSub(value) : buffer.push(value));\n    const doInnerSub = (value) => {\n        expand && subscriber.next(value);\n        active++;\n        let innerComplete = false;\n        innerFrom(project(value, index++)).subscribe(createOperatorSubscriber(subscriber, (innerValue) => {\n            onBeforeNext === null || onBeforeNext === void 0 ? void 0 : onBeforeNext(innerValue);\n            if (expand) {\n                outerNext(innerValue);\n            }\n            else {\n                subscriber.next(innerValue);\n            }\n        }, () => {\n            innerComplete = true;\n        }, undefined, () => {\n            if (innerComplete) {\n                try {\n                    active--;\n                    while (buffer.length && active < concurrent) {\n                        const bufferedValue = buffer.shift();\n                        if (innerSubScheduler) {\n                            executeSchedule(subscriber, innerSubScheduler, () => doInnerSub(bufferedValue));\n                        }\n                        else {\n                            doInnerSub(bufferedValue);\n                        }\n                    }\n                    checkComplete();\n                }\n                catch (err) {\n                    subscriber.error(err);\n                }\n            }\n        }));\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, outerNext, () => {\n        isComplete = true;\n        checkComplete();\n    }));\n    return () => {\n        additionalFinalizer === null || additionalFinalizer === void 0 ? void 0 : additionalFinalizer();\n    };\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,yBAA1B;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,UAAhC,EAA4CC,OAA5C,EAAqDC,UAArD,EAAiEC,YAAjE,EAA+EC,MAA/E,EAAuFC,iBAAvF,EAA0GC,mBAA1G,EAA+H;EAClI,MAAMC,MAAM,GAAG,EAAf;EACA,IAAIC,MAAM,GAAG,CAAb;EACA,IAAIC,KAAK,GAAG,CAAZ;EACA,IAAIC,UAAU,GAAG,KAAjB;;EACA,MAAMC,aAAa,GAAG,MAAM;IACxB,IAAID,UAAU,IAAI,CAACH,MAAM,CAACK,MAAtB,IAAgC,CAACJ,MAArC,EAA6C;MACzCR,UAAU,CAACa,QAAX;IACH;EACJ,CAJD;;EAKA,MAAMC,SAAS,GAAIC,KAAD,IAAYP,MAAM,GAAGN,UAAT,GAAsBc,UAAU,CAACD,KAAD,CAAhC,GAA0CR,MAAM,CAACU,IAAP,CAAYF,KAAZ,CAAxE;;EACA,MAAMC,UAAU,GAAID,KAAD,IAAW;IAC1BX,MAAM,IAAIJ,UAAU,CAACkB,IAAX,CAAgBH,KAAhB,CAAV;IACAP,MAAM;IACN,IAAIW,aAAa,GAAG,KAApB;IACAxB,SAAS,CAACM,OAAO,CAACc,KAAD,EAAQN,KAAK,EAAb,CAAR,CAAT,CAAmCW,SAAnC,CAA6CvB,wBAAwB,CAACG,UAAD,EAAcqB,UAAD,IAAgB;MAC9FlB,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAACkB,UAAD,CAAxE;;MACA,IAAIjB,MAAJ,EAAY;QACRU,SAAS,CAACO,UAAD,CAAT;MACH,CAFD,MAGK;QACDrB,UAAU,CAACkB,IAAX,CAAgBG,UAAhB;MACH;IACJ,CARoE,EAQlE,MAAM;MACLF,aAAa,GAAG,IAAhB;IACH,CAVoE,EAUlEG,SAVkE,EAUvD,MAAM;MAChB,IAAIH,aAAJ,EAAmB;QACf,IAAI;UACAX,MAAM;;UACN,OAAOD,MAAM,CAACK,MAAP,IAAiBJ,MAAM,GAAGN,UAAjC,EAA6C;YACzC,MAAMqB,aAAa,GAAGhB,MAAM,CAACiB,KAAP,EAAtB;;YACA,IAAInB,iBAAJ,EAAuB;cACnBT,eAAe,CAACI,UAAD,EAAaK,iBAAb,EAAgC,MAAMW,UAAU,CAACO,aAAD,CAAhD,CAAf;YACH,CAFD,MAGK;cACDP,UAAU,CAACO,aAAD,CAAV;YACH;UACJ;;UACDZ,aAAa;QAChB,CAZD,CAaA,OAAOc,GAAP,EAAY;UACRzB,UAAU,CAAC0B,KAAX,CAAiBD,GAAjB;QACH;MACJ;IACJ,CA7BoE,CAArE;EA8BH,CAlCD;;EAmCA1B,MAAM,CAACqB,SAAP,CAAiBvB,wBAAwB,CAACG,UAAD,EAAac,SAAb,EAAwB,MAAM;IACnEJ,UAAU,GAAG,IAAb;IACAC,aAAa;EAChB,CAHwC,CAAzC;EAIA,OAAO,MAAM;IACTL,mBAAmB,KAAK,IAAxB,IAAgCA,mBAAmB,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,mBAAmB,EAA7F;EACH,CAFD;AAGH"}, "metadata": {}, "sourceType": "module"}