{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/statistics.service\";\nimport * as i2 from \"../../../services/formation.service\";\nimport * as i3 from \"src/app/layout/service/app.layout.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"primeng/chart\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/api\";\nimport * as i9 from \"primeng/button\";\n\nfunction DashboardComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"i\", 15);\n    i0.ɵɵelementStart(4, \"span\", 16);\n    i0.ɵɵtext(5, \"Loading dashboard...\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nconst _c0 = function () {\n  return {\n    width: \"2.5rem\",\n    height: \"2.5rem\"\n  };\n};\n\nfunction DashboardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\")(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Total Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 22);\n    i0.ɵɵelement(9, \"i\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 24);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"formations\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.dashboardStats.totalFormations);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.dashboardStats.upcomingFormations, \" upcoming \");\n  }\n}\n\nfunction DashboardComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\")(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Total Employees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 26);\n    i0.ɵɵelement(9, \"i\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 24);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"organized\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.dashboardStats.totalEmployees);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(3, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.dashboardStats.totalTeams, \" teams \");\n  }\n}\n\nfunction DashboardComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\")(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Trainers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 28);\n    i0.ɵɵelement(9, \"i\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 24);\n    i0.ɵɵtext(11, \"Active \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"trainers available\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r3.dashboardStats.totalTrainers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\n\nfunction DashboardComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18)(2, \"div\", 19)(3, \"div\")(4, \"span\", 20);\n    i0.ɵɵtext(5, \"Attendance Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 30);\n    i0.ɵɵelement(9, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"span\", 24);\n    i0.ɵɵtext(11, \"Global \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 25);\n    i0.ɵɵtext(13, \"attendance rate\");\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.dashboardStats.globalAttendanceRate, \"%\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(2, _c0));\n  }\n}\n\nfunction DashboardComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h5\");\n    i0.ɵɵtext(3, \"Quick Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 0)(5, \"div\", 32);\n    i0.ɵɵelement(6, \"button\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32);\n    i0.ɵɵelement(8, \"button\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 32);\n    i0.ɵɵelement(10, \"button\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 32);\n    i0.ɵɵelement(12, \"button\", 36);\n    i0.ɵɵelementEnd()()()();\n  }\n}\n\nfunction DashboardComponent_div_19_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Formation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Trainer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c1 = function (a0, a1, a2) {\n  return {\n    \"p-tag-success\": a0,\n    \"p-tag-warning\": a1,\n    \"p-tag-danger\": a2\n  };\n};\n\nfunction DashboardComponent_div_19_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"span\", 44);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const formation_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formation_r12.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 6, formation_r12.date, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(formation_r12.team);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formation_r12.trainer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c1, formation_r12.status === \"completed\", formation_r12.status === \"scheduled\", formation_r12.status === \"cancelled\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", formation_r12.status, \" \");\n  }\n}\n\nfunction DashboardComponent_div_19_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵtext(2, \"No formations found\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction DashboardComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 2)(2, \"div\", 38)(3, \"h5\");\n    i0.ɵɵtext(4, \"Recent Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"button\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p-table\", 40);\n    i0.ɵɵtemplate(7, DashboardComponent_div_19_ng_template_7_Template, 11, 0, \"ng-template\", 41);\n    i0.ɵɵtemplate(8, DashboardComponent_div_19_ng_template_8_Template, 13, 13, \"ng-template\", 42);\n    i0.ɵɵtemplate(9, DashboardComponent_div_19_ng_template_9_Template, 3, 0, \"ng-template\", 43);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"value\", ctx_r6.recentFormations)(\"loading\", ctx_r6.loadingFormations);\n  }\n}\n\nfunction DashboardComponent_div_20_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 49)(1, \"div\")(2, \"span\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 51);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 61);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 52);\n    i0.ɵɵelement(9, \"i\", 62);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const activity_r14 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r14.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r14.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(activity_r14.time);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", activity_r14.color);\n    i0.ɵɵproperty(\"ngClass\", activity_r14.icon);\n  }\n}\n\nconst _c2 = function () {\n  return {\n    height: \"8px\"\n  };\n};\n\nconst _c3 = function () {\n  return {\n    width: \"100%\"\n  };\n};\n\nconst _c4 = function () {\n  return {\n    width: \"80%\"\n  };\n};\n\nconst _c5 = function () {\n  return {\n    width: \"90%\"\n  };\n};\n\nfunction DashboardComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 2)(2, \"div\", 38)(3, \"h5\");\n    i0.ɵɵtext(4, \"Recent Activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"ul\", 47);\n    i0.ɵɵtemplate(6, DashboardComponent_div_20_li_6_Template, 10, 6, \"li\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 2)(8, \"div\", 38)(9, \"h5\");\n    i0.ɵɵtext(10, \"Recent Activity\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"ul\", 47)(12, \"li\", 49)(13, \"div\")(14, \"span\", 50);\n    i0.ɵɵtext(15, \"Formation Created\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 51);\n    i0.ɵɵtext(17, \"Web Development Training\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 52)(19, \"div\", 53);\n    i0.ɵɵelement(20, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 55);\n    i0.ɵɵtext(22, \"Active\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"li\", 49)(24, \"div\")(25, \"span\", 50);\n    i0.ɵɵtext(26, \"Employee Registered\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 51);\n    i0.ɵɵtext(28, \"New team member added\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 56)(30, \"div\", 53);\n    i0.ɵɵelement(31, \"div\", 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 58);\n    i0.ɵɵtext(33, \"Recent\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"li\", 49)(35, \"div\")(36, \"span\", 50);\n    i0.ɵɵtext(37, \"Training Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 51);\n    i0.ɵɵtext(39, \"UI/UX Design Workshop\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 56)(41, \"div\", 53);\n    i0.ɵɵelement(42, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"span\", 60);\n    i0.ɵɵtext(44, \"90% Attendance\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r7.recentActivities);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(7, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(8, _c3));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(9, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(10, _c4));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(11, _c2));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(12, _c5));\n  }\n}\n\nfunction DashboardComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 2)(2, \"h5\");\n    i0.ɵɵtext(3, \"Formations Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"p-chart\", 64);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r8.chartData)(\"options\", ctx_r8.chartOptions);\n  }\n}\n\nexport class DashboardComponent {\n  constructor(statisticsService, formationService, layoutService) {\n    this.statisticsService = statisticsService;\n    this.formationService = formationService;\n    this.layoutService = layoutService; // Statistics data\n\n    this.dashboardStats = null;\n    this.monthlyFormations = [];\n    this.loading = true;\n    this.loadingFormations = true; // Dashboard data\n\n    this.recentFormations = [];\n    this.recentActivities = [];\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initChart();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initChart(); // Load statistics data\n\n\n      yield _this.loadDashboardData();\n    })();\n  }\n\n  loadDashboardData() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        console.log('🔄 Loading dashboard data...');\n        _this2.loading = true; // Load dashboard stats\n\n        console.log('📊 Loading dashboard stats...');\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats());\n        console.log('✅ Dashboard stats loaded:', _this2.dashboardStats); // Load monthly formations data\n\n        console.log('📅 Loading monthly formations...');\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations());\n        console.log('✅ Monthly formations loaded:', _this2.monthlyFormations); // Load recent formations\n\n        yield _this2.loadRecentFormations(); // Load recent activities\n\n        _this2.loadRecentActivities(); // Update chart with real data\n\n\n        _this2.updateFormationsChart();\n      } catch (error) {\n        console.error('❌ Error loading dashboard data:', error);\n      } finally {\n        _this2.loading = false;\n        console.log('✅ Dashboard loading complete');\n      }\n    })();\n  }\n\n  loadRecentFormations() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this3.loadingFormations = true;\n        const formations = yield _this3.formationService.getFormations(); // Get the 5 most recent formations\n\n        _this3.recentFormations = formations.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()).slice(0, 5).map(formation => {\n          var _a;\n\n          return Object.assign(Object.assign({}, formation), {\n            team: ((_a = formation.equipe) === null || _a === void 0 ? void 0 : _a.name) || 'No team',\n            trainer: formation.formateur ? `${formation.formateur.first_name} ${formation.formateur.last_name}` : 'No trainer',\n            status: _this3.getFormationStatus(formation)\n          });\n        });\n      } catch (error) {\n        console.error('Error loading recent formations:', error);\n        _this3.recentFormations = [];\n      } finally {\n        _this3.loadingFormations = false;\n      }\n    })();\n  }\n\n  loadRecentActivities() {\n    // Mock recent activities - in a real app, this would come from an API\n    this.recentActivities = [{\n      title: 'Formation Created',\n      description: 'Web Development Training scheduled',\n      time: '2 hours ago',\n      icon: 'pi-plus-circle',\n      color: '#10b981'\n    }, {\n      title: 'Employee Registered',\n      description: 'New team member added to Development team',\n      time: '4 hours ago',\n      icon: 'pi-user-plus',\n      color: '#3b82f6'\n    }, {\n      title: 'Training Completed',\n      description: 'UI/UX Design Workshop finished',\n      time: '1 day ago',\n      icon: 'pi-check-circle',\n      color: '#8b5cf6'\n    }, {\n      title: 'Team Updated',\n      description: 'Marketing team speciality changed',\n      time: '2 days ago',\n      icon: 'pi-users',\n      color: '#f59e0b'\n    }];\n  }\n\n  getFormationStatus(formation) {\n    const now = new Date();\n    const formationDate = new Date(formation.date);\n    if (formation.status === 'cancelled') return 'cancelled';\n    if (formation.status === 'completed') return 'completed';\n    if (formationDate > now) return 'scheduled';\n    return 'completed';\n  }\n\n  getCurrentDate() {\n    return new Date().toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  }\n\n  initChart() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n    this.chartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n      datasets: [{\n        label: 'Formations',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate (%)',\n        data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4,\n        yAxisID: 'y1'\n      }]\n    };\n    this.chartOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          type: 'linear',\n          display: true,\n          position: 'left',\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y1: {\n          type: 'linear',\n          display: true,\n          position: 'right',\n          ticks: {\n            color: textColorSecondary,\n            max: 100\n          },\n          grid: {\n            drawOnChartArea: false\n          }\n        }\n      }\n    };\n  }\n\n  updateFormationsChart() {\n    if (!this.monthlyFormations.length) return;\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const formationsData = new Array(12).fill(0);\n    const attendanceData = new Array(12).fill(0);\n    this.monthlyFormations.forEach(item => {\n      const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n      formationsData[monthIndex] = item.count;\n      attendanceData[monthIndex] = item.attendanceRate;\n    });\n    this.chartData = Object.assign(Object.assign({}, this.chartData), {\n      datasets: [Object.assign(Object.assign({}, this.chartData.datasets[0]), {\n        data: formationsData\n      }), Object.assign(Object.assign({}, this.chartData.datasets[1]), {\n        data: attendanceData\n      })]\n    });\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nDashboardComponent.ɵfac = function DashboardComponent_Factory(t) {\n  return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.StatisticsService), i0.ɵɵdirectiveInject(i2.FormationService), i0.ɵɵdirectiveInject(i3.LayoutService));\n};\n\nDashboardComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: DashboardComponent,\n  selectors: [[\"ng-component\"]],\n  decls: 22,\n  vars: 10,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-900\", \"font-medium\", \"text-3xl\", \"mb-2\"], [1, \"text-600\", \"mb-0\"], [1, \"flex\", \"align-items-center\"], [1, \"pi\", \"pi-calendar\", \"text-blue-500\", \"text-2xl\", \"mr-2\"], [1, \"text-900\", \"font-medium\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12 lg:col-6 xl:col-3\", 4, \"ngIf\"], [\"class\", \"col-12 xl:col-8\", 4, \"ngIf\"], [\"class\", \"col-12 xl:col-4\", 4, \"ngIf\"], [\"class\", \"col-12 xl:col-6\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"height\", \"200px\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\", 2, \"font-size\", \"2rem\"], [1, \"ml-2\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-3\"], [1, \"card\", \"mb-0\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [1, \"block\", \"text-500\", \"font-medium\", \"mb-3\"], [1, \"text-900\", \"font-medium\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-calendar\", \"text-blue-500\", \"text-xl\"], [1, \"text-green-500\", \"font-medium\"], [1, \"text-500\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-users\", \"text-orange-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-cyan-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-user\", \"text-cyan-500\", \"text-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-purple-100\", \"border-round\", 3, \"ngStyle\"], [1, \"pi\", \"pi-chart-line\", \"text-purple-500\", \"text-xl\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"New Formation\", \"icon\", \"pi pi-plus\", \"routerLink\", \"/uikit/formlayout\", 1, \"p-button-success\", \"w-full\", \"mb-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Manage Users\", \"icon\", \"pi pi-users\", \"routerLink\", \"/uikit/crud/employees\", 1, \"p-button-info\", \"w-full\", \"mb-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"View Statistics\", \"icon\", \"pi pi-chart-bar\", \"routerLink\", \"/uikit/statistics\", 1, \"p-button-warning\", \"w-full\", \"mb-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Manage Teams\", \"icon\", \"pi pi-sitemap\", \"routerLink\", \"/uikit/crud/teams\", 1, \"p-button-help\", \"w-full\", \"mb-2\"], [1, \"col-12\", \"xl:col-8\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-5\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"View All\", \"icon\", \"pi pi-arrow-right\", \"routerLink\", \"/admin/list-train\", 1, \"p-button-text\"], [\"responsiveLayout\", \"scroll\", 3, \"value\", \"loading\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [1, \"p-tag\", 3, \"ngClass\"], [\"colspan\", \"5\", 1, \"text-center\"], [1, \"col-12\", \"xl:col-4\"], [1, \"list-none\", \"p-0\", \"m-0\"], [\"class\", \"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"md:justify-content-between\", \"mb-4\"], [1, \"text-900\", \"font-medium\", \"mr-2\", \"mb-1\", \"md:mb-0\"], [1, \"mt-1\", \"text-600\"], [1, \"mt-2\", \"md:mt-0\", \"flex\", \"align-items-center\"], [1, \"surface-300\", \"border-round\", \"overflow-hidden\", \"w-10rem\", \"lg:w-6rem\", 3, \"ngStyle\"], [1, \"bg-green-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-green-500\", \"ml-3\", \"font-medium\"], [1, \"mt-2\", \"md:mt-0\", \"ml-0\", \"md:ml-8\", \"flex\", \"align-items-center\"], [1, \"bg-cyan-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-cyan-500\", \"ml-3\", \"font-medium\"], [1, \"bg-pink-500\", \"h-full\", 3, \"ngStyle\"], [1, \"text-pink-500\", \"ml-3\", \"font-medium\"], [1, \"mt-1\", \"text-500\", \"text-sm\"], [1, \"pi\", 3, \"ngClass\"], [1, \"col-12\", \"xl:col-6\"], [\"type\", \"line\", 3, \"data\", \"options\"]],\n  template: function DashboardComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\")(5, \"h2\", 4);\n      i0.ɵɵtext(6, \"Formation Management Dashboard\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"p\", 5);\n      i0.ɵɵtext(8, \"Welcome to your training management system overview\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(9, \"div\", 6);\n      i0.ɵɵelement(10, \"i\", 7);\n      i0.ɵɵelementStart(11, \"span\", 8);\n      i0.ɵɵtext(12);\n      i0.ɵɵelementEnd()()()()();\n      i0.ɵɵtemplate(13, DashboardComponent_div_13_Template, 6, 0, \"div\", 9);\n      i0.ɵɵtemplate(14, DashboardComponent_div_14_Template, 14, 4, \"div\", 10);\n      i0.ɵɵtemplate(15, DashboardComponent_div_15_Template, 14, 4, \"div\", 10);\n      i0.ɵɵtemplate(16, DashboardComponent_div_16_Template, 14, 3, \"div\", 10);\n      i0.ɵɵtemplate(17, DashboardComponent_div_17_Template, 14, 3, \"div\", 10);\n      i0.ɵɵtemplate(18, DashboardComponent_div_18_Template, 13, 0, \"div\", 9);\n      i0.ɵɵtemplate(19, DashboardComponent_div_19_Template, 10, 2, \"div\", 11);\n      i0.ɵɵtemplate(20, DashboardComponent_div_20_Template, 45, 13, \"div\", 12);\n      i0.ɵɵtemplate(21, DashboardComponent_div_21_Template, 5, 2, \"div\", 13);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(12);\n      i0.ɵɵtextInterpolate(ctx.getCurrentDate());\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.dashboardStats);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n    }\n  },\n  dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i4.NgStyle, i5.RouterLink, i6.UIChart, i7.Table, i8.PrimeTemplate, i9.ButtonDirective, i4.DatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAGA,SAAuBA,aAAvB,QAA4C,MAA5C;;;;;;;;;;;;;;ICeQC,+BAAoC,CAApC,EAAoC,KAApC,EAAoC,CAApC,EAAoC,CAApC,EAAoC,KAApC,EAAoC,EAApC;IAGYA;IACAA;IAAmBA;IAAoBA;;;;;;;;;;;;;IAMnDA,gCAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAgBA;IAC9DA;IAA0CA;IAAkCA;IAEhFA;IACIA;IACJA;IAEJA;IAAyCA;IAA+CA;IACxFA;IAAuBA;IAAUA;;;;;IAPiBA;IAAAA;IAEuCA;IAAAA;IAIhDA;IAAAA;;;;;;IAIjDA,gCAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAiCA;IAE/EA;IACIA;IACJA;IAEJA;IAAyCA;IAAoCA;IAC7EA;IAAuBA;IAASA;;;;;IAPkBA;IAAAA;IAEyCA;IAAAA;IAIlDA;IAAAA;;;;;;IAIjDA,gCAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAQA;IACtDA;IAA0CA;IAAgCA;IAE9EA;IACIA;IACJA;IAEJA;IAAyCA;IAAOA;IAChDA;IAAuBA;IAAkBA;;;;;IAPSA;IAAAA;IAEuCA;IAAAA;;;;;;IAQjGA,gCAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,EAAzE,EAAyE,CAAzE,EAAyE,KAAzE,EAAyE,CAAzE,EAAyE,MAAzE,EAAyE,EAAzE;IAI8DA;IAAeA;IAC7DA;IAA0CA;IAAwCA;IAEtFA;IACIA;IACJA;IAEJA;IAAyCA;IAAOA;IAChDA;IAAuBA;IAAeA;;;;;IAPYA;IAAAA;IAEyCA;IAAAA;;;;;;IAUnGA,+BAAqC,CAArC,EAAqC,KAArC,EAAqC,CAArC,EAAqC,CAArC,EAAqC,IAArC;IAEYA;IAAaA;IACjBA,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB;IAEQA;IAMJA;IACAA;IACIA;IAMJA;IACAA;IACIA;IAMJA;IACAA;IACIA;IAMJA;;;;;;IAmBIA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAASA;IACbA;IAAIA;IAAIA;IACRA;IAAIA;IAAIA;IACRA;IAAIA;IAAOA;IACXA;IAAIA;IAAMA;;;;;;;;;;;;;;IAIdA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAkBA;IACtBA;IAAIA;;IAAiCA;IACrCA;IAAIA;IAAkBA;IACtBA;IAAIA;IAAqBA;IACzBA,4BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAOQA;IACJA;;;;;IAZAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAGMA;IAAAA;IAKFA;IAAAA;;;;;;IAMZA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IACwCA;IAAmBA;;;;;;IAzC3EA,gCAA8C,CAA9C,EAA8C,KAA9C,EAA8C,CAA9C,EAA8C,CAA9C,EAA8C,KAA9C,EAA8C,EAA9C,EAA8C,CAA9C,EAA8C,IAA9C;IAGgBA;IAAiBA;IACrBA;IAMJA;IACAA;IACIA;IASAA;IAkBAA;IAKJA;;;;;IAjCSA;IAAAA,gDAA0B,SAA1B,EAA0BC,wBAA1B;;;;;;IA4CLD,+BAAyI,CAAzI,EAAyI,KAAzI,EAAyI,CAAzI,EAAyI,MAAzI,EAAyI,EAAzI;IAE6DA;IAAkBA;IACvEA;IAA2BA;IAAwBA;IACnDA;IAAmCA;IAAiBA;IAExDA;IACIA;IACJA;;;;;IANyDA;IAAAA;IAC1BA;IAAAA;IACQA;IAAAA;IAGKA;IAAAA;IAA1BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAblCA,gCAA8C,CAA9C,EAA8C,KAA9C,EAA8C,CAA9C,EAA8C,CAA9C,EAA8C,KAA9C,EAA8C,EAA9C,EAA8C,CAA9C,EAA8C,IAA9C;IAGgBA;IAAeA;IAEvBA;IACIA;IAUJA;IAEJA,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,EAAlB,EAAkB,CAAlB,EAAkB,IAAlB;IAEYA;IAAeA;IAEvBA,gCAA8B,EAA9B,EAA8B,IAA9B,EAA8B,EAA9B,EAA8B,EAA9B,EAA8B,KAA9B,EAA8B,EAA9B,EAA8B,MAA9B,EAA8B,EAA9B;IAGiEA;IAAiBA;IACtEA;IAA2BA;IAAwBA;IAEvDA,iCAAkD,EAAlD,EAAkD,KAAlD,EAAkD,EAAlD;IAEQA;IACJA;IACAA;IAA8CA;IAAMA;IAG5DA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;IAE6DA;IAAmBA;IACxEA;IAA2BA;IAAqBA;IAEpDA,iCAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;IAEQA;IACJA;IACAA;IAA6CA;IAAMA;IAG3DA,gCAA+F,EAA/F,EAA+F,KAA/F,EAA+F,EAA/F,EAA+F,MAA/F,EAA+F,EAA/F;IAE6DA;IAAkBA;IACvEA;IAA2BA;IAAqBA;IAEpDA,iCAA+D,EAA/D,EAA+D,KAA/D,EAA+D,EAA/D;IAEQA;IACJA;IACAA;IAA6CA;IAAcA;;;;;IAlD1CA;IAAAA;IAuBuDA;IAAAA;IACnCA;IAAAA;IAWmCA;IAAAA;IACpCA;IAAAA;IAWoCA;IAAAA;IACpCA;IAAAA;;;;;;IAUxDA,gCAA8C,CAA9C,EAA8C,KAA9C,EAA8C,CAA9C,EAA8C,CAA9C,EAA8C,IAA9C;IAEYA;IAAmBA;IACvBA;IACJA;;;;;IADyBA;IAAAA,wCAAkB,SAAlB,EAAkBE,mBAAlB;;;;AD9OrC,OAAM,MAAOC,kBAAP,CAAyB;EAgB3BC,YACYC,iBADZ,EAEYC,gBAFZ,EAGWC,aAHX,EAGuC;IAF3B;IACA;IACD,mCAA4B,CAbvC;;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC;IACA,eAAmB,IAAnB;IACA,yBAA6B,IAA7B,CASuC,CAPvC;;IACA,wBAA0B,EAA1B;IACA,wBAA0B,EAA1B;IAOI,KAAKC,YAAL,GAAoB,KAAKD,aAAL,CAAmBE,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,SAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,SAAL,GADU,CAEV;;;MACA,MAAM,KAAI,CAACE,iBAAL,EAAN;IAHU;EAIb;;EAEKA,iBAAiB;IAAA;;IAAA;MACnB,IAAI;QACAC,OAAO,CAACC,GAAR,CAAY,8BAAZ;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CAFA,CAIA;;QACAF,OAAO,CAACC,GAAR,CAAY,+BAAZ;QACA,MAAI,CAACE,cAAL,SAA4BlB,aAAa,CAAC,MAAI,CAACM,iBAAL,CAAuBa,iBAAvB,EAAD,CAAzC;QACAJ,OAAO,CAACC,GAAR,CAAY,2BAAZ,EAAyC,MAAI,CAACE,cAA9C,EAPA,CASA;;QACAH,OAAO,CAACC,GAAR,CAAY,kCAAZ;QACA,MAAI,CAACI,iBAAL,SAA+BpB,aAAa,CAAC,MAAI,CAACM,iBAAL,CAAuBe,oBAAvB,EAAD,CAA5C;QACAN,OAAO,CAACC,GAAR,CAAY,8BAAZ,EAA4C,MAAI,CAACI,iBAAjD,EAZA,CAcA;;QACA,MAAM,MAAI,CAACE,oBAAL,EAAN,CAfA,CAiBA;;QACA,MAAI,CAACC,oBAAL,GAlBA,CAoBA;;;QACA,MAAI,CAACC,qBAAL;MAEH,CAvBD,CAuBE,OAAOC,KAAP,EAAc;QACZV,OAAO,CAACU,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;MACH,CAzBD,SAyBU;QACN,MAAI,CAACR,OAAL,GAAe,KAAf;QACAF,OAAO,CAACC,GAAR,CAAY,8BAAZ;MACH;IA7BkB;EA8BtB;;EAEKM,oBAAoB;IAAA;;IAAA;MACtB,IAAI;QACA,MAAI,CAACI,iBAAL,GAAyB,IAAzB;QACA,MAAMC,UAAU,SAAS,MAAI,CAACpB,gBAAL,CAAsBqB,aAAtB,EAAzB,CAFA,CAGA;;QACA,MAAI,CAACC,gBAAL,GAAwBF,UAAU,CAC7BG,IADmB,CACd,CAACC,CAAD,EAAIC,CAAJ,KAAU,IAAIC,IAAJ,CAASD,CAAC,CAACE,IAAX,EAAiBC,OAAjB,KAA6B,IAAIF,IAAJ,CAASF,CAAC,CAACG,IAAX,EAAiBC,OAAjB,EADzB,EAEnBC,KAFmB,CAEb,CAFa,EAEV,CAFU,EAGnBC,GAHmB,CAGfC,SAAS,IAAG;;;UAAC,uCACXA,SADW,GACF;YACZC,IAAI,EAAE,gBAAS,CAACC,MAAV,MAAgB,IAAhB,IAAgBC,aAAhB,GAAgB,MAAhB,GAAgBA,GAAEC,IAAlB,KAA0B,SADpB;YAEZC,OAAO,EAAEL,SAAS,CAACM,SAAV,GACL,GAAGN,SAAS,CAACM,SAAV,CAAoBC,UAAU,IAAIP,SAAS,CAACM,SAAV,CAAoBE,SAAS,EAD7D,GAEL,YAJQ;YAKZC,MAAM,EAAE,MAAI,CAACC,kBAAL,CAAwBV,SAAxB;UALI,CADE;QAOhB,CAVkB,CAAxB;MAWH,CAfD,CAeE,OAAOb,KAAP,EAAc;QACZV,OAAO,CAACU,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;QACA,MAAI,CAACI,gBAAL,GAAwB,EAAxB;MACH,CAlBD,SAkBU;QACN,MAAI,CAACH,iBAAL,GAAyB,KAAzB;MACH;IArBqB;EAsBzB;;EAEDH,oBAAoB;IAChB;IACA,KAAK0B,gBAAL,GAAwB,CACpB;MACIC,KAAK,EAAE,mBADX;MAEIC,WAAW,EAAE,oCAFjB;MAGIC,IAAI,EAAE,aAHV;MAIIC,IAAI,EAAE,gBAJV;MAKIC,KAAK,EAAE;IALX,CADoB,EAQpB;MACIJ,KAAK,EAAE,qBADX;MAEIC,WAAW,EAAE,2CAFjB;MAGIC,IAAI,EAAE,aAHV;MAIIC,IAAI,EAAE,cAJV;MAKIC,KAAK,EAAE;IALX,CARoB,EAepB;MACIJ,KAAK,EAAE,oBADX;MAEIC,WAAW,EAAE,gCAFjB;MAGIC,IAAI,EAAE,WAHV;MAIIC,IAAI,EAAE,iBAJV;MAKIC,KAAK,EAAE;IALX,CAfoB,EAsBpB;MACIJ,KAAK,EAAE,cADX;MAEIC,WAAW,EAAE,mCAFjB;MAGIC,IAAI,EAAE,YAHV;MAIIC,IAAI,EAAE,UAJV;MAKIC,KAAK,EAAE;IALX,CAtBoB,CAAxB;EA8BH;;EAEDN,kBAAkB,CAACV,SAAD,EAAe;IAC7B,MAAMiB,GAAG,GAAG,IAAItB,IAAJ,EAAZ;IACA,MAAMuB,aAAa,GAAG,IAAIvB,IAAJ,CAASK,SAAS,CAACJ,IAAnB,CAAtB;IAEA,IAAII,SAAS,CAACS,MAAV,KAAqB,WAAzB,EAAsC,OAAO,WAAP;IACtC,IAAIT,SAAS,CAACS,MAAV,KAAqB,WAAzB,EAAsC,OAAO,WAAP;IACtC,IAAIS,aAAa,GAAGD,GAApB,EAAyB,OAAO,WAAP;IACzB,OAAO,WAAP;EACH;;EAEDE,cAAc;IACV,OAAO,IAAIxB,IAAJ,GAAWyB,kBAAX,CAA8B,OAA9B,EAAuC;MAC1CC,OAAO,EAAE,MADiC;MAE1CC,IAAI,EAAE,SAFoC;MAG1CC,KAAK,EAAE,MAHmC;MAI1CC,GAAG,EAAE;IAJqC,CAAvC,CAAP;EAMH;;EAEDlD,SAAS;IACL,MAAMmD,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB;IAEA,KAAKG,SAAL,GAAiB;MACbC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CADK;MAEbC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,YADX;QAEIC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIC,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEd,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIU,WAAW,EAAEf,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMIW,OAAO,EAAE;MANb,CADM,EASN;QACIL,KAAK,EAAE,qBADX;QAEIC,IAAI,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,CAFV;QAGIC,IAAI,EAAE,KAHV;QAIIC,eAAe,EAAEd,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIU,WAAW,EAAEf,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMIW,OAAO,EAAE,EANb;QAOIC,OAAO,EAAE;MAPb,CATM;IAFG,CAAjB;IAuBA,KAAKC,YAAL,GAAoB;MAChBC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJX,MAAM,EAAE;YACJlB,KAAK,EAAEa;UADH;QADJ;MADH,CADO;MAQhBiB,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHhC,KAAK,EAAEe;UADJ,CADR;UAICkB,IAAI,EAAE;YACFjC,KAAK,EAAEgB,aADL;YAEFkB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCC,IAAI,EAAE,QADP;UAECC,OAAO,EAAE,IAFV;UAGCC,QAAQ,EAAE,MAHX;UAICN,KAAK,EAAE;YACHhC,KAAK,EAAEe;UADJ,CAJR;UAOCkB,IAAI,EAAE;YACFjC,KAAK,EAAEgB,aADL;YAEFkB,UAAU,EAAE;UAFV;QAPP,CAVC;QAsBJK,EAAE,EAAE;UACAH,IAAI,EAAE,QADN;UAEAC,OAAO,EAAE,IAFT;UAGAC,QAAQ,EAAE,OAHV;UAIAN,KAAK,EAAE;YACHhC,KAAK,EAAEe,kBADJ;YAEHyB,GAAG,EAAE;UAFF,CAJP;UAQAP,IAAI,EAAE;YACFQ,eAAe,EAAE;UADf;QARN;MAtBA;IARQ,CAApB;EA4CH;;EAEDvE,qBAAqB;IACjB,IAAI,CAAC,KAAKJ,iBAAL,CAAuB4E,MAA5B,EAAoC;IAEpC,MAAMC,MAAM,GAAG,CAAC,KAAD,EAAQ,KAAR,EAAe,KAAf,EAAsB,KAAtB,EAA6B,KAA7B,EAAoC,KAApC,EAA2C,KAA3C,EAAkD,KAAlD,EAAyD,KAAzD,EAAgE,KAAhE,EAAuE,KAAvE,EAA8E,KAA9E,CAAf;IACA,MAAMC,cAAc,GAAG,IAAIC,KAAJ,CAAU,EAAV,EAAcvB,IAAd,CAAmB,CAAnB,CAAvB;IACA,MAAMwB,cAAc,GAAG,IAAID,KAAJ,CAAU,EAAV,EAAcvB,IAAd,CAAmB,CAAnB,CAAvB;IAEA,KAAKxD,iBAAL,CAAuBiF,OAAvB,CAA+BC,IAAI,IAAG;MAClC,MAAMC,UAAU,GAAG,IAAItE,IAAJ,CAASqE,IAAI,CAACzC,KAAL,GAAa,UAAtB,EAAkC2C,QAAlC,EAAnB;MACAN,cAAc,CAACK,UAAD,CAAd,GAA6BD,IAAI,CAACG,KAAlC;MACAL,cAAc,CAACG,UAAD,CAAd,GAA6BD,IAAI,CAACI,cAAlC;IACH,CAJD;IAMA,KAAKnC,SAAL,GAAcoC,gCACP,KAAKpC,SADE,GACO;MACjBE,QAAQ,EAAE,iCAEC,KAAKF,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BE,IAAI,EAAEuB;MADuB,EAF3B,kCAMC,KAAK3B,SAAL,CAAeE,QAAf,CAAwB,CAAxB,IAA0B;QAC7BE,IAAI,EAAEyB;MADuB,EAN3B;IADO,CADP,CAAd;EAaH;;EAEDQ,WAAW;IACP,IAAI,KAAKnG,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBoG,WAAlB;IACH;EACJ;;AAxP0B;;;mBAAlBzG,oBAAkBH;AAAA;;;QAAlBG;EAAkB0G;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;MCT3BjH,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,IAAlB,EAAkB,CAAlB;MAMmEA;MAA8BA;MAC7EA;MAAyBA;MAAmDA;MAEhFA;MACIA;MACAA;MAAmCA;MAAoBA;MAOvEA;MAUAA;MAeAA;MAeAA;MAeAA;MAiBAA;MAyCAA;MAiDAA;MAgEAA;MApPJA;;;;MAWuDA;MAAAA;MAO7CA;MAAAA;MAUAA;MAAAA;MAeAA;MAAAA;MAeAA;MAAAA;MAeAA;MAAAA;MAiBAA;MAAAA;MAyCAA;MAAAA;MAiDAA;MAAAA;MAgEAA;MAAAA", "names": ["lastValueFrom", "i0", "ctx_r6", "ctx_r8", "DashboardComponent", "constructor", "statisticsService", "formationService", "layoutService", "subscription", "configUpdate$", "subscribe", "initChart", "ngOnInit", "loadDashboardData", "console", "log", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "loadRecentFormations", "loadRecentActivities", "updateFormationsChart", "error", "loadingFormations", "formations", "getFormations", "recentFormations", "sort", "a", "b", "Date", "date", "getTime", "slice", "map", "formation", "team", "equipe", "_a", "name", "trainer", "formateur", "first_name", "last_name", "status", "getFormationStatus", "recentActivities", "title", "description", "time", "icon", "color", "now", "formationDate", "getCurrentDate", "toLocaleDateString", "weekday", "year", "month", "day", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "chartData", "labels", "datasets", "label", "data", "fill", "backgroundColor", "borderColor", "tension", "yAxisID", "chartOptions", "plugins", "legend", "scales", "x", "ticks", "grid", "drawBorder", "y", "type", "display", "position", "y1", "max", "drawOnChartArea", "length", "months", "formationsData", "Array", "attendanceData", "for<PERSON>ach", "item", "monthIndex", "getMonth", "count", "attendanceRate", "Object", "ngOnDestroy", "unsubscribe", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { StatisticsService, DashboardStats, MonthlyFormations } from '../../../services/statistics.service';\nimport { FormationService } from '../../../services/formation.service';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\n\n@Component({\n    templateUrl: './dashboard.component.html',\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n\n    chartData: any;\n    chartOptions: any;\n    subscription!: Subscription;\n\n    // Statistics data\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n    loading: boolean = true;\n    loadingFormations: boolean = true;\n\n    // Dashboard data\n    recentFormations: any[] = [];\n    recentActivities: any[] = [];\n\n    constructor(\n        private statisticsService: StatisticsService,\n        private formationService: FormationService,\n        public layoutService: LayoutService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initChart();\n        });\n    }\n\n    async ngOnInit() {\n        this.initChart();\n        // Load statistics data\n        await this.loadDashboardData();\n    }\n\n    async loadDashboardData() {\n        try {\n            console.log('🔄 Loading dashboard data...');\n            this.loading = true;\n\n            // Load dashboard stats\n            console.log('📊 Loading dashboard stats...');\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            console.log('✅ Dashboard stats loaded:', this.dashboardStats);\n\n            // Load monthly formations data\n            console.log('📅 Loading monthly formations...');\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n            console.log('✅ Monthly formations loaded:', this.monthlyFormations);\n\n            // Load recent formations\n            await this.loadRecentFormations();\n\n            // Load recent activities\n            this.loadRecentActivities();\n\n            // Update chart with real data\n            this.updateFormationsChart();\n\n        } catch (error) {\n            console.error('❌ Error loading dashboard data:', error);\n        } finally {\n            this.loading = false;\n            console.log('✅ Dashboard loading complete');\n        }\n    }\n\n    async loadRecentFormations() {\n        try {\n            this.loadingFormations = true;\n            const formations = await this.formationService.getFormations();\n            // Get the 5 most recent formations\n            this.recentFormations = formations\n                .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())\n                .slice(0, 5)\n                .map(formation => ({\n                    ...formation,\n                    team: formation.equipe?.name || 'No team',\n                    trainer: formation.formateur ?\n                        `${formation.formateur.first_name} ${formation.formateur.last_name}` :\n                        'No trainer',\n                    status: this.getFormationStatus(formation)\n                }));\n        } catch (error) {\n            console.error('Error loading recent formations:', error);\n            this.recentFormations = [];\n        } finally {\n            this.loadingFormations = false;\n        }\n    }\n\n    loadRecentActivities() {\n        // Mock recent activities - in a real app, this would come from an API\n        this.recentActivities = [\n            {\n                title: 'Formation Created',\n                description: 'Web Development Training scheduled',\n                time: '2 hours ago',\n                icon: 'pi-plus-circle',\n                color: '#10b981'\n            },\n            {\n                title: 'Employee Registered',\n                description: 'New team member added to Development team',\n                time: '4 hours ago',\n                icon: 'pi-user-plus',\n                color: '#3b82f6'\n            },\n            {\n                title: 'Training Completed',\n                description: 'UI/UX Design Workshop finished',\n                time: '1 day ago',\n                icon: 'pi-check-circle',\n                color: '#8b5cf6'\n            },\n            {\n                title: 'Team Updated',\n                description: 'Marketing team speciality changed',\n                time: '2 days ago',\n                icon: 'pi-users',\n                color: '#f59e0b'\n            }\n        ];\n    }\n\n    getFormationStatus(formation: any): string {\n        const now = new Date();\n        const formationDate = new Date(formation.date);\n\n        if (formation.status === 'cancelled') return 'cancelled';\n        if (formation.status === 'completed') return 'completed';\n        if (formationDate > now) return 'scheduled';\n        return 'completed';\n    }\n\n    getCurrentDate(): string {\n        return new Date().toLocaleDateString('en-US', {\n            weekday: 'long',\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n    }\n\n    initChart() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        this.chartData = {\n            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate (%)',\n                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4,\n                    yAxisID: 'y1'\n                }\n            ]\n        };\n\n        this.chartOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    type: 'linear',\n                    display: true,\n                    position: 'left',\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y1: {\n                    type: 'linear',\n                    display: true,\n                    position: 'right',\n                    ticks: {\n                        color: textColorSecondary,\n                        max: 100\n                    },\n                    grid: {\n                        drawOnChartArea: false,\n                    },\n                }\n            }\n        };\n    }\n\n    updateFormationsChart() {\n        if (!this.monthlyFormations.length) return;\n\n        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n        const formationsData = new Array(12).fill(0);\n        const attendanceData = new Array(12).fill(0);\n\n        this.monthlyFormations.forEach(item => {\n            const monthIndex = new Date(item.month + ' 1, 2025').getMonth();\n            formationsData[monthIndex] = item.count;\n            attendanceData[monthIndex] = item.attendanceRate;\n        });\n\n        this.chartData = {\n            ...this.chartData,\n            datasets: [\n                {\n                    ...this.chartData.datasets[0],\n                    data: formationsData\n                },\n                {\n                    ...this.chartData.datasets[1],\n                    data: attendanceData\n                }\n            ]\n        };\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n", "    <div class=\"grid\">\n        <!-- Header -->\n        <div class=\"col-12\">\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center\">\n                    <div>\n                        <h2 class=\"text-900 font-medium text-3xl mb-2\">Formation Management Dashboard</h2>\n                        <p class=\"text-600 mb-0\">Welcome to your training management system overview</p>\n                    </div>\n                    <div class=\"flex align-items-center\">\n                        <i class=\"pi pi-calendar text-blue-500 text-2xl mr-2\"></i>\n                        <span class=\"text-900 font-medium\">{{getCurrentDate()}}</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Loading State -->\n        <div *ngIf=\"loading\" class=\"col-12\">\n            <div class=\"card\">\n                <div class=\"flex align-items-center justify-content-center\" style=\"height: 200px;\">\n                    <i class=\"pi pi-spin pi-spinner\" style=\"font-size: 2rem;\"></i>\n                    <span class=\"ml-2\">Loading dashboard...</span>\n                </div>\n            </div>\n        </div>\n\n        <!-- Statistics Cards -->\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Total Formations</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalFormations}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-blue-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-calendar text-blue-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">{{dashboardStats.upcomingFormations}} upcoming </span>\n                <span class=\"text-500\">formations</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Total Employees</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalEmployees}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-orange-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-users text-orange-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">{{dashboardStats.totalTeams}} teams </span>\n                <span class=\"text-500\">organized</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Trainers</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.totalTrainers}}</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-cyan-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-user text-cyan-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">Active </span>\n                <span class=\"text-500\">trainers available</span>\n            </div>\n        </div>\n        <div *ngIf=\"!loading && dashboardStats\" class=\"col-12 lg:col-6 xl:col-3\">\n            <div class=\"card mb-0\">\n                <div class=\"flex justify-content-between mb-3\">\n                    <div>\n                        <span class=\"block text-500 font-medium mb-3\">Attendance Rate</span>\n                        <div class=\"text-900 font-medium text-xl\">{{dashboardStats.globalAttendanceRate}}%</div>\n                    </div>\n                    <div class=\"flex align-items-center justify-content-center bg-purple-100 border-round\" [ngStyle]=\"{width: '2.5rem', height: '2.5rem'}\">\n                        <i class=\"pi pi-chart-line text-purple-500 text-xl\"></i>\n                    </div>\n                </div>\n                <span class=\"text-green-500 font-medium\">Global </span>\n                <span class=\"text-500\">attendance rate</span>\n            </div>\n        </div>\n\n        <!-- Quick Actions -->\n        <div *ngIf=\"!loading\" class=\"col-12\">\n            <div class=\"card\">\n                <h5>Quick Actions</h5>\n                <div class=\"grid\">\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <button pButton pRipple\n                                label=\"New Formation\"\n                                icon=\"pi pi-plus\"\n                                class=\"p-button-success w-full mb-2\"\n                                routerLink=\"/uikit/formlayout\">\n                        </button>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <button pButton pRipple\n                                label=\"Manage Users\"\n                                icon=\"pi pi-users\"\n                                class=\"p-button-info w-full mb-2\"\n                                routerLink=\"/uikit/crud/employees\">\n                        </button>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <button pButton pRipple\n                                label=\"View Statistics\"\n                                icon=\"pi pi-chart-bar\"\n                                class=\"p-button-warning w-full mb-2\"\n                                routerLink=\"/uikit/statistics\">\n                        </button>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <button pButton pRipple\n                                label=\"Manage Teams\"\n                                icon=\"pi pi-sitemap\"\n                                class=\"p-button-help w-full mb-2\"\n                                routerLink=\"/uikit/crud/teams\">\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n\n        <!-- Recent Formations Table -->\n        <div *ngIf=\"!loading\" class=\"col-12 xl:col-8\">\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center mb-5\">\n                    <h5>Recent Formations</h5>\n                    <button pButton pRipple\n                            label=\"View All\"\n                            icon=\"pi pi-arrow-right\"\n                            class=\"p-button-text\"\n                            routerLink=\"/admin/list-train\">\n                    </button>\n                </div>\n                <p-table [value]=\"recentFormations\" [loading]=\"loadingFormations\" responsiveLayout=\"scroll\">\n                    <ng-template pTemplate=\"header\">\n                        <tr>\n                            <th>Formation</th>\n                            <th>Date</th>\n                            <th>Team</th>\n                            <th>Trainer</th>\n                            <th>Status</th>\n                        </tr>\n                    </ng-template>\n                    <ng-template pTemplate=\"body\" let-formation>\n                        <tr>\n                            <td>{{formation.name}}</td>\n                            <td>{{formation.date | date:'short'}}</td>\n                            <td>{{formation.team}}</td>\n                            <td>{{formation.trainer}}</td>\n                            <td>\n                                <span class=\"p-tag\"\n                                      [ngClass]=\"{\n                                        'p-tag-success': formation.status === 'completed',\n                                        'p-tag-warning': formation.status === 'scheduled',\n                                        'p-tag-danger': formation.status === 'cancelled'\n                                      }\">\n                                    {{formation.status}}\n                                </span>\n                            </td>\n                        </tr>\n                    </ng-template>\n                    <ng-template pTemplate=\"emptymessage\">\n                        <tr>\n                            <td colspan=\"5\" class=\"text-center\">No formations found</td>\n                        </tr>\n                    </ng-template>\n                </p-table>\n            </div>\n        </div>\n\n        <!-- Recent Activity -->\n        <div *ngIf=\"!loading\" class=\"col-12 xl:col-4\">\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center mb-5\">\n                    <h5>Recent Activity</h5>\n                </div>\n                <ul class=\"list-none p-0 m-0\">\n                    <li *ngFor=\"let activity of recentActivities\" class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">{{activity.title}}</span>\n                            <div class=\"mt-1 text-600\">{{activity.description}}</div>\n                            <div class=\"mt-1 text-500 text-sm\">{{activity.time}}</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 flex align-items-center\">\n                            <i class=\"pi\" [ngClass]=\"activity.icon\" [style.color]=\"activity.color\"></i>\n                        </div>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"card\">\n                <div class=\"flex justify-content-between align-items-center mb-5\">\n                    <h5>Recent Activity</h5>\n                </div>\n                <ul class=\"list-none p-0 m-0\">\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Formation Created</span>\n                            <div class=\"mt-1 text-600\">Web Development Training</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-green-500 h-full\" [ngStyle]=\"{width: '100%'}\"></div>\n                            </div>\n                            <span class=\"text-green-500 ml-3 font-medium\">Active</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Employee Registered</span>\n                            <div class=\"mt-1 text-600\">New team member added</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-cyan-500 h-full\" [ngStyle]=\"{width: '80%'}\"></div>\n                            </div>\n                            <span class=\"text-cyan-500 ml-3 font-medium\">Recent</span>\n                        </div>\n                    </li>\n                    <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\n                        <div>\n                            <span class=\"text-900 font-medium mr-2 mb-1 md:mb-0\">Training Completed</span>\n                            <div class=\"mt-1 text-600\">UI/UX Design Workshop</div>\n                        </div>\n                        <div class=\"mt-2 md:mt-0 ml-0 md:ml-8 flex align-items-center\">\n                            <div class=\"surface-300 border-round overflow-hidden w-10rem lg:w-6rem\" [ngStyle]=\"{height: '8px'}\">\n                                <div class=\"bg-pink-500 h-full\" [ngStyle]=\"{width: '90%'}\"></div>\n                            </div>\n                            <span class=\"text-pink-500 ml-3 font-medium\">90% Attendance</span>\n                        </div>\n                    </li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- Chart Section -->\n        <div *ngIf=\"!loading\" class=\"col-12 xl:col-6\">\n            <div class=\"card\">\n                <h5>Formations Overview</h5>\n                <p-chart type=\"line\" [data]=\"chartData\" [options]=\"chartOptions\"></p-chart>\n            </div>\n        </div>\n\n\n"]}, "metadata": {}, "sourceType": "module"}