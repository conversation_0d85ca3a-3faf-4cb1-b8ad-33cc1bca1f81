{"ast": null, "code": "import { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\nexport function timestamp(timestampProvider = dateTimestampProvider) {\n  return map(value => ({\n    value,\n    timestamp: timestampProvider.now()\n  }));\n}", "map": {"version": 3, "names": ["dateTimestampProvider", "map", "timestamp", "timestampProvider", "value", "now"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/timestamp.js"], "sourcesContent": ["import { dateTimestampProvider } from '../scheduler/dateTimestampProvider';\nimport { map } from './map';\nexport function timestamp(timestampProvider = dateTimestampProvider) {\n    return map((value) => ({ value, timestamp: timestampProvider.now() }));\n}\n"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,oCAAtC;AACA,SAASC,GAAT,QAAoB,OAApB;AACA,OAAO,SAASC,SAAT,CAAmBC,iBAAiB,GAAGH,qBAAvC,EAA8D;EACjE,OAAOC,GAAG,CAAEG,KAAD,KAAY;IAAEA,KAAF;IAASF,SAAS,EAAEC,iBAAiB,CAACE,GAAlB;EAApB,CAAZ,CAAD,CAAV;AACH"}, "metadata": {}, "sourceType": "module"}