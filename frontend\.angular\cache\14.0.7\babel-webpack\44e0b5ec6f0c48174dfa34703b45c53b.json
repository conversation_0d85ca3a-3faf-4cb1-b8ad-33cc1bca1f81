{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport * as i5 from 'primeng/api';\nimport { ContextMenuService } from 'primeng/api';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nconst _c0 = [\"sublist\"];\nconst _c1 = [\"menuitem\"];\n\nconst _c2 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\n\nfunction ContextMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 5, 6);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c2, child_r2.visible === false));\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.icon)(\"ngStyle\", child_r2.iconStyle);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.label);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r2.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.badge);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n}\n\nconst _c3 = function (a1) {\n  return {\n    \"p-menuitem-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction ContextMenuSub_ng_template_2_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 11);\n    i0.ɵɵlistener(\"click\", function ContextMenuSub_ng_template_2_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      i0.ɵɵnextContext();\n\n      const _r8 = i0.ɵɵreference(1);\n\n      const ctx_r23 = i0.ɵɵnextContext();\n      const child_r2 = ctx_r23.$implicit;\n      const index_r3 = ctx_r23.index;\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onItemClick($event, child_r2, _r8, ctx_r22.getKey(index_r3)));\n    });\n    i0.ɵɵtemplate(1, ContextMenuSub_ng_template_2_li_1_a_2_span_1_Template, 1, 2, \"span\", 12);\n    i0.ɵɵtemplate(2, ContextMenuSub_ng_template_2_li_1_a_2_span_2_Template, 2, 1, \"span\", 13);\n    i0.ɵɵtemplate(3, ContextMenuSub_ng_template_2_li_1_a_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, ContextMenuSub_ng_template_2_li_1_a_2_span_5_Template, 2, 2, \"span\", 15);\n    i0.ɵɵtemplate(6, ContextMenuSub_ng_template_2_li_1_a_2_span_6_Template, 1, 0, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r14 = i0.ɵɵreference(4);\n\n    const ctx_r25 = i0.ɵɵnextContext(2);\n    const child_r2 = ctx_r25.$implicit;\n    const index_r3 = ctx_r25.index;\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", child_r2.target)(\"ngClass\", i0.ɵɵpureFunction1(13, _c3, child_r2.disabled));\n    i0.ɵɵattribute(\"href\", child_r2.url ? child_r2.url : null, i0.ɵɵsanitizeUrl)(\"title\", child_r2.title)(\"id\", child_r2.id)(\"tabindex\", child_r2.disabled ? null : \"0\")(\"aria-haspopup\", ctx_r9.item.items != null)(\"aria-expanded\", ctx_r9.isActive(ctx_r9.getKey(index_r3)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.escape !== false)(\"ngIfElse\", _r14);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r2.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.icon)(\"ngStyle\", child_r2.iconStyle);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.label);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r2.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r2 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r2.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r2.badge);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n}\n\nconst _c4 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction ContextMenuSub_ng_template_2_li_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function ContextMenuSub_ng_template_2_li_1_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      i0.ɵɵnextContext();\n\n      const _r8 = i0.ɵɵreference(1);\n\n      const ctx_r37 = i0.ɵɵnextContext();\n      const child_r2 = ctx_r37.$implicit;\n      const index_r3 = ctx_r37.index;\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onItemClick($event, child_r2, _r8, ctx_r36.getKey(index_r3)));\n    });\n    i0.ɵɵtemplate(1, ContextMenuSub_ng_template_2_li_1_a_3_span_1_Template, 1, 2, \"span\", 12);\n    i0.ɵɵtemplate(2, ContextMenuSub_ng_template_2_li_1_a_3_span_2_Template, 2, 1, \"span\", 13);\n    i0.ɵɵtemplate(3, ContextMenuSub_ng_template_2_li_1_a_3_ng_template_3_Template, 1, 1, \"ng-template\", null, 23, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, ContextMenuSub_ng_template_2_li_1_a_3_span_5_Template, 2, 2, \"span\", 15);\n    i0.ɵɵtemplate(6, ContextMenuSub_ng_template_2_li_1_a_3_span_6_Template, 1, 0, \"span\", 16);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r28 = i0.ɵɵreference(4);\n\n    const child_r2 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"routerLink\", child_r2.routerLink)(\"queryParams\", child_r2.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", child_r2.routerLinkActiveOptions || i0.ɵɵpureFunction0(20, _c4))(\"target\", child_r2.target)(\"ngClass\", i0.ɵɵpureFunction1(21, _c3, child_r2.disabled))(\"fragment\", child_r2.fragment)(\"queryParamsHandling\", child_r2.queryParamsHandling)(\"preserveFragment\", child_r2.preserveFragment)(\"skipLocationChange\", child_r2.skipLocationChange)(\"replaceUrl\", child_r2.replaceUrl)(\"state\", child_r2.state);\n    i0.ɵɵattribute(\"title\", child_r2.title)(\"id\", child_r2.id)(\"tabindex\", child_r2.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.escape !== false)(\"ngIfElse\", _r28);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r2.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_li_1_p_contextMenuSub_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-contextMenuSub\", 24);\n    i0.ɵɵlistener(\"leafClick\", function ContextMenuSub_ng_template_2_li_1_p_contextMenuSub_4_Template_p_contextMenuSub_leafClick_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.onLeafClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext(2);\n    const index_r3 = ctx_r42.index;\n    const child_r2 = ctx_r42.$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"parentItemKey\", ctx_r11.getKey(index_r3))(\"item\", child_r2);\n  }\n}\n\nconst _c5 = function (a1, a2) {\n  return {\n    \"p-menuitem\": true,\n    \"p-menuitem-active\": a1,\n    \"p-hidden\": a2\n  };\n};\n\nfunction ContextMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 7, 6);\n    i0.ɵɵlistener(\"mouseenter\", function ContextMenuSub_ng_template_2_li_1_Template_li_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const ctx_r44 = i0.ɵɵnextContext();\n      const child_r2 = ctx_r44.$implicit;\n      const index_r3 = ctx_r44.index;\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.onItemMouseEnter($event, child_r2, ctx_r43.getKey(index_r3)));\n    })(\"mouseleave\", function ContextMenuSub_ng_template_2_li_1_Template_li_mouseleave_0_listener($event) {\n      i0.ɵɵrestoreView(_r45);\n      const child_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.onItemMouseLeave($event, child_r2));\n    });\n    i0.ɵɵtemplate(2, ContextMenuSub_ng_template_2_li_1_a_2_Template, 7, 15, \"a\", 8);\n    i0.ɵɵtemplate(3, ContextMenuSub_ng_template_2_li_1_a_3_Template, 7, 23, \"a\", 9);\n    i0.ɵɵtemplate(4, ContextMenuSub_ng_template_2_li_1_p_contextMenuSub_4_Template, 1, 2, \"p-contextMenuSub\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r48 = i0.ɵɵnextContext();\n    const child_r2 = ctx_r48.$implicit;\n    const index_r3 = ctx_r48.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(child_r2.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(9, _c5, ctx_r5.isActive(ctx_r5.getKey(index_r3)), child_r2.visible === false))(\"ngStyle\", child_r2.style)(\"tooltipOptions\", child_r2.tooltipOptions);\n    i0.ɵɵattribute(\"data-ik\", ctx_r5.getKey(index_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !child_r2.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r2.items);\n  }\n}\n\nfunction ContextMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ContextMenuSub_ng_template_2_li_0_Template, 2, 3, \"li\", 3);\n    i0.ɵɵtemplate(1, ContextMenuSub_ng_template_2_li_1_Template, 5, 12, \"li\", 4);\n  }\n\n  if (rf & 2) {\n    const child_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", child_r2.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !child_r2.separator);\n  }\n}\n\nconst _c6 = function (a0) {\n  return {\n    \"p-submenu-list\": a0\n  };\n};\n\nconst _c7 = [\"container\"];\n\nclass ContextMenuSub {\n  constructor(contextMenu) {\n    this.leafClick = new EventEmitter();\n    this.contextMenu = contextMenu;\n  }\n\n  ngOnInit() {\n    this.activeItemKeyChangeSubscription = this.contextMenu.contextMenuService.activeItemKeyChange$.pipe(takeUntil(this.contextMenu.ngDestroy$)).subscribe(activeItemKey => {\n      this.activeItemKey = activeItemKey;\n\n      if (this.isActive(this.parentItemKey) && DomHandler.hasClass(this.sublistViewChild.nativeElement, 'p-submenu-list-active')) {\n        this.contextMenu.positionSubmenu(this.sublistViewChild.nativeElement);\n      }\n\n      this.contextMenu.cd.markForCheck();\n    });\n  }\n\n  onItemMouseEnter(event, item, key) {\n    if (this.hideTimeout) {\n      clearTimeout(this.hideTimeout);\n      this.hideTimeout = null;\n    }\n\n    if (item.disabled) {\n      this.activeItemKey = null;\n      return;\n    }\n\n    if (item.items) {\n      let childSublist = DomHandler.findSingle(event.currentTarget, '.p-submenu-list');\n      DomHandler.addClass(childSublist, 'p-submenu-list-active');\n    }\n\n    this.contextMenu.contextMenuService.changeKey(key);\n  }\n\n  onItemMouseLeave(event, item) {\n    if (item.disabled) {\n      return;\n    }\n\n    if (this.contextMenu.el.nativeElement.contains(event.toElement)) {\n      if (item.items) {\n        this.contextMenu.removeActiveFromSubLists(event.currentTarget);\n      }\n\n      if (!this.root) {\n        this.contextMenu.contextMenuService.changeKey(this.parentItemKey);\n      }\n    }\n  }\n\n  onItemClick(event, item, menuitem, key) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (item.items) {\n      let childSublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n\n      if (childSublist) {\n        if (this.isActive(key) && DomHandler.hasClass(childSublist, 'p-submenu-list-active')) {\n          this.contextMenu.removeActiveFromSubLists(menuitem);\n        } else {\n          DomHandler.addClass(childSublist, 'p-submenu-list-active');\n        }\n\n        this.contextMenu.contextMenuService.changeKey(key);\n      }\n    }\n\n    if (!item.items) {\n      this.onLeafClick();\n    }\n  }\n\n  onLeafClick() {\n    if (this.root) {\n      this.contextMenu.hide();\n    }\n\n    this.leafClick.emit();\n  }\n\n  getKey(index) {\n    return this.root ? String(index) : this.parentItemKey + '_' + index;\n  }\n\n  isActive(key) {\n    return this.activeItemKey && (this.activeItemKey.startsWith(key + '_') || this.activeItemKey === key);\n  }\n\n}\n\nContextMenuSub.ɵfac = function ContextMenuSub_Factory(t) {\n  return new (t || ContextMenuSub)(i0.ɵɵdirectiveInject(forwardRef(() => ContextMenu)));\n};\n\nContextMenuSub.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ContextMenuSub,\n  selectors: [[\"p-contextMenuSub\"]],\n  viewQuery: function ContextMenuSub_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sublistViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuitemViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    item: \"item\",\n    root: \"root\",\n    parentItemKey: \"parentItemKey\"\n  },\n  outputs: {\n    leafClick: \"leafClick\"\n  },\n  decls: 3,\n  vars: 4,\n  consts: [[3, \"ngClass\"], [\"sublist\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menu-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"pTooltip\", \"\", \"role\", \"none\", 3, \"ngClass\", \"ngStyle\", \"class\", \"tooltipOptions\", \"mouseenter\", \"mouseleave\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menu-separator\", 3, \"ngClass\"], [\"menuitem\", \"\"], [\"pTooltip\", \"\", \"role\", \"none\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\", \"mouseenter\", \"mouseleave\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [3, \"parentItemKey\", \"item\", \"leafClick\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-icon pi pi-angle-right\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-submenu-icon\", \"pi\", \"pi-angle-right\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\"], [\"htmlRouteLabel\", \"\"], [3, \"parentItemKey\", \"item\", \"leafClick\"]],\n  template: function ContextMenuSub_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ul\", 0, 1);\n      i0.ɵɵtemplate(2, ContextMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c6, !ctx.root));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.root ? ctx.item : ctx.item.items);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, ContextMenuSub],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-contextMenuSub',\n      template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list':!root}\">\n            <ng-template ngFor let-child let-index=\"index\" [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" #menuitem class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #menuitem [ngClass]=\"{'p-menuitem':true,'p-menuitem-active': isActive(getKey(index)),'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\"\n                    (mouseenter)=\"onItemMouseEnter($event,child,getKey(index))\" (mouseleave)=\"onItemMouseLeave($event,child)\" role=\"none\" [attr.data-ik]=\"getKey(index)\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url ? child.url : null\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                        [attr.tabindex]=\"child.disabled ? null : '0'\" (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" pRipple\n                        [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"isActive(getKey(index))\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" role=\"menuitem\"\n                        [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\"\n                        (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         pRipple [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-contextMenuSub [parentItemKey]=\"getKey(index)\" [item]=\"child\" *ngIf=\"child.items\" (leafClick)=\"onLeafClick()\"></p-contextMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => ContextMenu)]\n      }]\n    }];\n  }, {\n    item: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    parentItemKey: [{\n      type: Input\n    }],\n    leafClick: [{\n      type: Output\n    }],\n    sublistViewChild: [{\n      type: ViewChild,\n      args: ['sublist']\n    }],\n    menuitemViewChild: [{\n      type: ViewChild,\n      args: ['menuitem']\n    }]\n  });\n})();\n\nclass ContextMenu {\n  constructor(el, renderer, cd, zone, contextMenuService, config) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n    this.contextMenuService = contextMenuService;\n    this.config = config;\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.triggerEvent = 'contextmenu';\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.ngDestroy$ = new Subject();\n    this.preventDocumentDefault = false;\n  }\n\n  ngAfterViewInit() {\n    if (this.global) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.triggerEventListener = this.renderer.listen(documentTarget, this.triggerEvent, event => {\n        this.show(event);\n        event.preventDefault();\n      });\n    } else if (this.target) {\n      this.triggerEventListener = this.renderer.listen(this.target, this.triggerEvent, event => {\n        this.show(event);\n        event.preventDefault();\n      });\n    }\n\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.containerViewChild.nativeElement);else DomHandler.appendChild(this.containerViewChild.nativeElement, this.appendTo);\n    }\n  }\n\n  show(event) {\n    this.clearActiveItem();\n    this.position(event);\n    this.moveOnTop();\n    this.containerViewChild.nativeElement.style.display = 'block';\n    this.preventDocumentDefault = true;\n    DomHandler.fadeIn(this.containerViewChild.nativeElement, 250);\n    this.bindGlobalListeners();\n\n    if (event) {\n      event.preventDefault();\n    }\n\n    this.onShow.emit();\n  }\n\n  hide() {\n    this.containerViewChild.nativeElement.style.display = 'none';\n\n    if (this.autoZIndex) {\n      ZIndexUtils.clear(this.containerViewChild.nativeElement);\n    }\n\n    this.clearActiveItem();\n    this.unbindGlobalListeners();\n    this.onHide.emit();\n  }\n\n  moveOnTop() {\n    if (this.autoZIndex && this.containerViewChild && this.containerViewChild.nativeElement.style.display !== 'block') {\n      ZIndexUtils.set('menu', this.containerViewChild.nativeElement, this.baseZIndex + this.config.zIndex.menu);\n    }\n  }\n\n  toggle(event) {\n    if (this.containerViewChild.nativeElement.offsetParent) this.hide();else this.show(event);\n  }\n\n  position(event) {\n    if (event) {\n      let left = event.pageX + 1;\n      let top = event.pageY + 1;\n      let width = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetWidth : DomHandler.getHiddenElementOuterWidth(this.containerViewChild.nativeElement);\n      let height = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetHeight : DomHandler.getHiddenElementOuterHeight(this.containerViewChild.nativeElement);\n      let viewport = DomHandler.getViewport(); //flip\n\n      if (left + width - document.body.scrollLeft > viewport.width) {\n        left -= width;\n      } //flip\n\n\n      if (top + height - document.body.scrollTop > viewport.height) {\n        top -= height;\n      } //fit\n\n\n      if (left < document.body.scrollLeft) {\n        left = document.body.scrollLeft;\n      } //fit\n\n\n      if (top < document.body.scrollTop) {\n        top = document.body.scrollTop;\n      }\n\n      this.containerViewChild.nativeElement.style.left = left + 'px';\n      this.containerViewChild.nativeElement.style.top = top + 'px';\n    }\n  }\n\n  positionSubmenu(sublist) {\n    let parentMenuItem = sublist.parentElement.parentElement;\n    let viewport = DomHandler.getViewport();\n    let sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n    let sublistHeight = sublist.offsetHeight ? sublist.offsetHeight : DomHandler.getHiddenElementOuterHeight(sublist);\n    let itemOuterWidth = DomHandler.getOuterWidth(parentMenuItem.children[0]);\n    let itemOuterHeight = DomHandler.getOuterHeight(parentMenuItem.children[0]);\n    let containerOffset = DomHandler.getOffset(parentMenuItem.parentElement);\n    sublist.style.zIndex = ++DomHandler.zindex;\n\n    if (parseInt(containerOffset.top) + itemOuterHeight + sublistHeight > viewport.height - DomHandler.calculateScrollbarHeight()) {\n      sublist.style.removeProperty('top');\n      sublist.style.bottom = '0px';\n    } else {\n      sublist.style.removeProperty('bottom');\n      sublist.style.top = '0px';\n    }\n\n    if (parseInt(containerOffset.left) + itemOuterWidth + sublistWidth > viewport.width - DomHandler.calculateScrollbarWidth()) {\n      sublist.style.left = -sublistWidth + 'px';\n    } else {\n      sublist.style.left = itemOuterWidth + 'px';\n    }\n  }\n\n  isItemMatched(menuitem) {\n    return DomHandler.hasClass(menuitem, 'p-menuitem') && !DomHandler.hasClass(menuitem.children[0], 'p-disabled');\n  }\n\n  findNextItem(menuitem, isRepeated) {\n    let nextMenuitem = menuitem.nextElementSibling;\n\n    if (nextMenuitem) {\n      return this.isItemMatched(nextMenuitem) ? nextMenuitem : this.findNextItem(nextMenuitem, isRepeated);\n    } else {\n      let firstItem = menuitem.parentElement.children[0];\n      return this.isItemMatched(firstItem) ? firstItem : !isRepeated ? this.findNextItem(firstItem, true) : null;\n    }\n  }\n\n  findPrevItem(menuitem, isRepeated) {\n    let prevMenuitem = menuitem.previousElementSibling;\n\n    if (prevMenuitem) {\n      return this.isItemMatched(prevMenuitem) ? prevMenuitem : this.findPrevItem(prevMenuitem, isRepeated);\n    } else {\n      let lastItem = menuitem.parentElement.children[menuitem.parentElement.children.length - 1];\n      return this.isItemMatched(lastItem) ? lastItem : !isRepeated ? this.findPrevItem(lastItem, true) : null;\n    }\n  }\n\n  getActiveItem() {\n    let activeItemKey = this.contextMenuService.activeItemKey;\n    return activeItemKey == null ? null : DomHandler.findSingle(this.containerViewChild.nativeElement, '.p-menuitem[data-ik=\"' + activeItemKey + '\"]');\n  }\n\n  clearActiveItem() {\n    if (this.contextMenuService.activeItemKey) {\n      this.removeActiveFromSubLists(this.containerViewChild.nativeElement);\n      this.contextMenuService.reset();\n    }\n  }\n\n  removeActiveFromSubLists(el) {\n    let sublists = DomHandler.find(el, '.p-submenu-list-active');\n\n    for (let sublist of sublists) {\n      DomHandler.removeClass(sublist, 'p-submenu-list-active');\n    }\n  }\n\n  removeActiveFromSublist(menuitem) {\n    if (menuitem) {\n      let sublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n\n      if (sublist && DomHandler.hasClass(menuitem, 'p-submenu-list-active')) {\n        DomHandler.removeClass(menuitem, 'p-submenu-list-active');\n      }\n    }\n  }\n\n  bindGlobalListeners() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', event => {\n        if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !event.ctrlKey && event.button !== 2) {\n          this.hide();\n        }\n      });\n      this.documentTriggerListener = this.renderer.listen(documentTarget, this.triggerEvent, event => {\n        if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !this.preventDocumentDefault) {\n          this.hide();\n        }\n\n        this.preventDocumentDefault = false;\n      });\n    }\n\n    this.zone.runOutsideAngular(() => {\n      if (!this.windowResizeListener) {\n        this.windowResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.windowResizeListener);\n      }\n    });\n\n    if (!this.documentKeydownListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentKeydownListener = this.renderer.listen(documentTarget, 'keydown', event => {\n        let activeItem = this.getActiveItem();\n\n        switch (event.key) {\n          case 'ArrowDown':\n            if (activeItem) {\n              this.removeActiveFromSublist(activeItem);\n              activeItem = this.findNextItem(activeItem);\n            } else {\n              let firstItem = DomHandler.findSingle(this.containerViewChild.nativeElement, '.p-menuitem-link').parentElement;\n              activeItem = this.isItemMatched(firstItem) ? firstItem : this.findNextItem(firstItem);\n            }\n\n            if (activeItem) {\n              this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n            }\n\n            event.preventDefault();\n            break;\n\n          case 'ArrowUp':\n            if (activeItem) {\n              this.removeActiveFromSublist(activeItem);\n              activeItem = this.findPrevItem(activeItem);\n            } else {\n              let sublist = DomHandler.findSingle(this.containerViewChild.nativeElement, 'ul');\n              let lastItem = sublist.children[sublist.children.length - 1];\n              activeItem = this.isItemMatched(lastItem) ? lastItem : this.findPrevItem(lastItem);\n            }\n\n            if (activeItem) {\n              this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n            }\n\n            event.preventDefault();\n            break;\n\n          case 'ArrowRight':\n            if (activeItem) {\n              let sublist = DomHandler.findSingle(activeItem, '.p-submenu-list');\n\n              if (sublist) {\n                DomHandler.addClass(sublist, 'p-submenu-list-active');\n                activeItem = DomHandler.findSingle(sublist, '.p-menuitem-link:not(.p-disabled)').parentElement;\n\n                if (activeItem) {\n                  this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                }\n              }\n            }\n\n            event.preventDefault();\n            break;\n\n          case 'ArrowLeft':\n            if (activeItem) {\n              let sublist = activeItem.parentElement;\n\n              if (sublist && DomHandler.hasClass(sublist, 'p-submenu-list-active')) {\n                DomHandler.removeClass(sublist, 'p-submenu-list-active');\n                activeItem = sublist.parentElement.parentElement;\n\n                if (activeItem) {\n                  this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                }\n              }\n            }\n\n            event.preventDefault();\n            break;\n\n          case 'Escape':\n            this.hide();\n            event.preventDefault();\n            break;\n\n          case 'Enter':\n            if (activeItem) {\n              this.handleItemClick(event, this.findModelItemFromKey(this.contextMenuService.activeItemKey), activeItem);\n            }\n\n            event.preventDefault();\n            break;\n\n          default:\n            break;\n        }\n      });\n    }\n  }\n\n  findModelItemFromKey(key) {\n    if (key == null || !this.model) {\n      return null;\n    }\n\n    let indexes = key.split('_');\n    return indexes.reduce((item, currentIndex) => {\n      return item ? item.items[currentIndex] : this.model[currentIndex];\n    }, null);\n  }\n\n  handleItemClick(event, item, menuitem) {\n    if (!item || item.disabled) {\n      return;\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (item.items) {\n      let childSublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n\n      if (childSublist) {\n        if (DomHandler.hasClass(childSublist, 'p-submenu-list-active')) {\n          this.removeActiveFromSubLists(menuitem);\n        } else {\n          DomHandler.addClass(childSublist, 'p-submenu-list-active');\n          this.positionSubmenu(childSublist);\n        }\n      }\n    }\n\n    if (!item.items) {\n      this.hide();\n    }\n  }\n\n  unbindGlobalListeners() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n\n    if (this.documentTriggerListener) {\n      this.documentTriggerListener();\n      this.documentTriggerListener = null;\n    }\n\n    if (this.windowResizeListener) {\n      window.removeEventListener('resize', this.windowResizeListener);\n      this.windowResizeListener = null;\n    }\n\n    if (this.documentKeydownListener) {\n      this.documentKeydownListener();\n      this.documentKeydownListener = null;\n    }\n  }\n\n  onWindowResize(event) {\n    if (this.containerViewChild.nativeElement.offsetParent) {\n      this.hide();\n    }\n  }\n\n  isOutsideClicked(event) {\n    return !(this.containerViewChild.nativeElement.isSameNode(event.target) || this.containerViewChild.nativeElement.contains(event.target));\n  }\n\n  ngOnDestroy() {\n    this.unbindGlobalListeners();\n\n    if (this.triggerEventListener) {\n      this.triggerEventListener();\n    }\n\n    if (this.containerViewChild && this.autoZIndex) {\n      ZIndexUtils.clear(this.containerViewChild.nativeElement);\n    }\n\n    if (this.appendTo) {\n      this.el.nativeElement.appendChild(this.containerViewChild.nativeElement);\n    }\n\n    this.ngDestroy$.next(true);\n    this.ngDestroy$.complete();\n  }\n\n}\n\nContextMenu.ɵfac = function ContextMenu_Factory(t) {\n  return new (t || ContextMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i5.ContextMenuService), i0.ɵɵdirectiveInject(i5.PrimeNGConfig));\n};\n\nContextMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ContextMenu,\n  selectors: [[\"p-contextMenu\"]],\n  viewQuery: function ContextMenu_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c7, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    global: \"global\",\n    target: \"target\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    appendTo: \"appendTo\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    triggerEvent: \"triggerEvent\"\n  },\n  outputs: {\n    onShow: \"onShow\",\n    onHide: \"onHide\"\n  },\n  decls: 3,\n  vars: 6,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [3, \"item\", \"root\"]],\n  template: function ContextMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵelement(2, \"p-contextMenuSub\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-contextmenu p-component\")(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"item\", ctx.model)(\"root\", true);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgStyle, ContextMenuSub],\n  styles: [\".p-contextmenu{position:absolute;display:none}.p-contextmenu ul{margin:0;padding:0;list-style:none}.p-contextmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-contextmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-contextmenu .p-menuitem-text{line-height:1}.p-contextmenu .p-menuitem{position:relative}.p-contextmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-contextmenu .p-menuitem-active>p-contextmenusub>.p-submenu-list.p-submenu-list-active{display:block!important}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-contextMenu',\n      template: `\n        <div #container [ngClass]=\"'p-contextmenu p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <p-contextMenuSub [item]=\"model\" [root]=\"true\"></p-contextMenuSub>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-contextmenu{position:absolute;display:none}.p-contextmenu ul{margin:0;padding:0;list-style:none}.p-contextmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-contextmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-contextmenu .p-menuitem-text{line-height:1}.p-contextmenu .p-menuitem{position:relative}.p-contextmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-contextmenu .p-menuitem-active>p-contextmenusub>.p-submenu-list.p-submenu-list-active{display:block!important}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i5.ContextMenuService\n    }, {\n      type: i5.PrimeNGConfig\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    global: [{\n      type: Input\n    }],\n    target: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    triggerEvent: [{\n      type: Input\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }]\n  });\n})();\n\nclass ContextMenuModule {}\n\nContextMenuModule.ɵfac = function ContextMenuModule_Factory(t) {\n  return new (t || ContextMenuModule)();\n};\n\nContextMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ContextMenuModule\n});\nContextMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [ContextMenuService],\n  imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContextMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n      exports: [ContextMenu, RouterModule, TooltipModule],\n      declarations: [ContextMenu, ContextMenuSub],\n      providers: [ContextMenuService]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ContextMenu, ContextMenuModule, ContextMenuSub };", "map": {"version": 3, "names": ["i0", "EventEmitter", "forwardRef", "Component", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ChangeDetectionStrategy", "NgModule", "i1", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "i5", "ContextMenuService", "i3", "RippleModule", "ZIndexUtils", "i2", "RouterModule", "Subject", "takeUntil", "i4", "TooltipModule", "ContextMenuSub", "constructor", "contextMenu", "leafClick", "ngOnInit", "activeItemKeyChangeSubscription", "contextMenuService", "activeItemKeyChange$", "pipe", "ngDestroy$", "subscribe", "activeItemKey", "isActive", "parentItemKey", "hasClass", "sublist<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "positionSubmenu", "cd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onItemMouseEnter", "event", "item", "key", "hideTimeout", "clearTimeout", "disabled", "items", "childSublist", "findSingle", "currentTarget", "addClass", "change<PERSON>ey", "onItemMouseLeave", "el", "contains", "toElement", "removeActiveFromSubLists", "root", "onItemClick", "menuitem", "preventDefault", "url", "routerLink", "command", "originalEvent", "onLeafClick", "hide", "emit", "<PERSON><PERSON><PERSON>", "index", "String", "startsWith", "ɵfac", "ContextMenu", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "encapsulation", "None", "host", "undefined", "decorators", "menuitemViewChild", "renderer", "zone", "config", "autoZIndex", "baseZIndex", "triggerEvent", "onShow", "onHide", "preventDocumentDefault", "ngAfterViewInit", "global", "documentTarget", "ownerDocument", "triggerEventListener", "listen", "show", "target", "appendTo", "document", "body", "append<PERSON><PERSON><PERSON>", "containerViewChild", "clearActiveItem", "position", "moveOnTop", "style", "display", "fadeIn", "bindGlobalListeners", "clear", "unbindGlobalListeners", "set", "zIndex", "menu", "toggle", "offsetParent", "left", "pageX", "top", "pageY", "width", "offsetWidth", "getHiddenElementOuterWidth", "height", "offsetHeight", "getHiddenElementOuterHeight", "viewport", "getViewport", "scrollLeft", "scrollTop", "sublist", "parentMenuItem", "parentElement", "sublist<PERSON><PERSON><PERSON>", "sublistHeight", "itemOuterWidth", "getOuterWidth", "children", "itemOuterHeight", "getOuterHeight", "containerOffset", "getOffset", "zindex", "parseInt", "calculateScrollbarHeight", "removeProperty", "bottom", "calculateScrollbarWidth", "isItemMatched", "findNextItem", "isRepeated", "nextMenuitem", "nextElement<PERSON><PERSON>ling", "firstItem", "findPrevItem", "prevMenuitem", "previousElementSibling", "lastItem", "length", "getActiveItem", "reset", "sublists", "find", "removeClass", "removeActiveFromSublist", "documentClickListener", "isOutsideClicked", "ctrl<PERSON>ey", "button", "documentTriggerListener", "runOutsideAngular", "windowResizeListener", "onWindowResize", "bind", "window", "addEventListener", "documentKeydownListener", "activeItem", "getAttribute", "handleItemClick", "findModelItemFromKey", "model", "indexes", "split", "reduce", "currentIndex", "removeEventListener", "isSameNode", "ngOnDestroy", "next", "complete", "ElementRef", "Renderer2", "ChangeDetectorRef", "NgZone", "PrimeNGConfig", "changeDetection", "OnPush", "styles", "styleClass", "ContextMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations", "providers"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-contextmenu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\nimport * as i5 from 'primeng/api';\nimport { ContextMenuService } from 'primeng/api';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nclass ContextMenuSub {\n    constructor(contextMenu) {\n        this.leafClick = new EventEmitter();\n        this.contextMenu = contextMenu;\n    }\n    ngOnInit() {\n        this.activeItemKeyChangeSubscription = this.contextMenu.contextMenuService.activeItemKeyChange$.pipe(takeUntil(this.contextMenu.ngDestroy$)).subscribe((activeItemKey) => {\n            this.activeItemKey = activeItemKey;\n            if (this.isActive(this.parentItemKey) && DomHandler.hasClass(this.sublistViewChild.nativeElement, 'p-submenu-list-active')) {\n                this.contextMenu.positionSubmenu(this.sublistViewChild.nativeElement);\n            }\n            this.contextMenu.cd.markForCheck();\n        });\n    }\n    onItemMouseEnter(event, item, key) {\n        if (this.hideTimeout) {\n            clearTimeout(this.hideTimeout);\n            this.hideTimeout = null;\n        }\n        if (item.disabled) {\n            this.activeItemKey = null;\n            return;\n        }\n        if (item.items) {\n            let childSublist = DomHandler.findSingle(event.currentTarget, '.p-submenu-list');\n            DomHandler.addClass(childSublist, 'p-submenu-list-active');\n        }\n        this.contextMenu.contextMenuService.changeKey(key);\n    }\n    onItemMouseLeave(event, item) {\n        if (item.disabled) {\n            return;\n        }\n        if (this.contextMenu.el.nativeElement.contains(event.toElement)) {\n            if (item.items) {\n                this.contextMenu.removeActiveFromSubLists(event.currentTarget);\n            }\n            if (!this.root) {\n                this.contextMenu.contextMenuService.changeKey(this.parentItemKey);\n            }\n        }\n    }\n    onItemClick(event, item, menuitem, key) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (item.items) {\n            let childSublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n            if (childSublist) {\n                if (this.isActive(key) && DomHandler.hasClass(childSublist, 'p-submenu-list-active')) {\n                    this.contextMenu.removeActiveFromSubLists(menuitem);\n                }\n                else {\n                    DomHandler.addClass(childSublist, 'p-submenu-list-active');\n                }\n                this.contextMenu.contextMenuService.changeKey(key);\n            }\n        }\n        if (!item.items) {\n            this.onLeafClick();\n        }\n    }\n    onLeafClick() {\n        if (this.root) {\n            this.contextMenu.hide();\n        }\n        this.leafClick.emit();\n    }\n    getKey(index) {\n        return this.root ? String(index) : this.parentItemKey + '_' + index;\n    }\n    isActive(key) {\n        return (this.activeItemKey && (this.activeItemKey.startsWith(key + '_') || this.activeItemKey === key));\n    }\n}\nContextMenuSub.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuSub, deps: [{ token: forwardRef(() => ContextMenu) }], target: i0.ɵɵFactoryTarget.Component });\nContextMenuSub.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: ContextMenuSub, selector: \"p-contextMenuSub\", inputs: { item: \"item\", root: \"root\", parentItemKey: \"parentItemKey\" }, outputs: { leafClick: \"leafClick\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"sublistViewChild\", first: true, predicate: [\"sublist\"], descendants: true }, { propertyName: \"menuitemViewChild\", first: true, predicate: [\"menuitem\"], descendants: true }], ngImport: i0, template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list':!root}\">\n            <ng-template ngFor let-child let-index=\"index\" [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" #menuitem class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #menuitem [ngClass]=\"{'p-menuitem':true,'p-menuitem-active': isActive(getKey(index)),'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\"\n                    (mouseenter)=\"onItemMouseEnter($event,child,getKey(index))\" (mouseleave)=\"onItemMouseLeave($event,child)\" role=\"none\" [attr.data-ik]=\"getKey(index)\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url ? child.url : null\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                        [attr.tabindex]=\"child.disabled ? null : '0'\" (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" pRipple\n                        [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"isActive(getKey(index))\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" role=\"menuitem\"\n                        [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\"\n                        (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         pRipple [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-contextMenuSub [parentItemKey]=\"getKey(index)\" [item]=\"child\" *ngIf=\"child.items\" (leafClick)=\"onLeafClick()\"></p-contextMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }, { kind: \"directive\", type: i4.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: ContextMenuSub, selector: \"p-contextMenuSub\", inputs: [\"item\", \"root\", \"parentItemKey\"], outputs: [\"leafClick\"] }], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-contextMenuSub',\n                    template: `\n        <ul #sublist [ngClass]=\"{'p-submenu-list':!root}\">\n            <ng-template ngFor let-child let-index=\"index\" [ngForOf]=\"(root ? item : item.items)\">\n                <li *ngIf=\"child.separator\" #menuitem class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': child.visible === false}\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" #menuitem [ngClass]=\"{'p-menuitem':true,'p-menuitem-active': isActive(getKey(index)),'p-hidden': child.visible === false}\" [ngStyle]=\"child.style\" [class]=\"child.styleClass\" pTooltip [tooltipOptions]=\"child.tooltipOptions\"\n                    (mouseenter)=\"onItemMouseEnter($event,child,getKey(index))\" (mouseleave)=\"onItemMouseLeave($event,child)\" role=\"none\" [attr.data-ik]=\"getKey(index)\">\n                    <a *ngIf=\"!child.routerLink\" [attr.href]=\"child.url ? child.url : null\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\"\n                        [attr.tabindex]=\"child.disabled ? null : '0'\" (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\" pRipple\n                        [attr.aria-haspopup]=\"item.items != null\" [attr.aria-expanded]=\"isActive(getKey(index))\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" role=\"menuitem\"\n                        [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" [target]=\"child.target\" [attr.title]=\"child.title\" [attr.id]=\"child.id\" [attr.tabindex]=\"child.disabled ? null : '0'\"\n                        (click)=\"onItemClick($event, child, menuitem, getKey(index))\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':child.disabled}\"\n                         pRipple [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-menuitem-icon\" *ngIf=\"child.icon\" [ngClass]=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                        <span class=\"p-submenu-icon pi pi-angle-right\" *ngIf=\"child.items\"></span>\n                    </a>\n                    <p-contextMenuSub [parentItemKey]=\"getKey(index)\" [item]=\"child\" *ngIf=\"child.items\" (leafClick)=\"onLeafClick()\"></p-contextMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [forwardRef(() => ContextMenu)]\n                    }] }];\n    }, propDecorators: { item: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], parentItemKey: [{\n                type: Input\n            }], leafClick: [{\n                type: Output\n            }], sublistViewChild: [{\n                type: ViewChild,\n                args: ['sublist']\n            }], menuitemViewChild: [{\n                type: ViewChild,\n                args: ['menuitem']\n            }] } });\nclass ContextMenu {\n    constructor(el, renderer, cd, zone, contextMenuService, config) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n        this.contextMenuService = contextMenuService;\n        this.config = config;\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.triggerEvent = 'contextmenu';\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.ngDestroy$ = new Subject();\n        this.preventDocumentDefault = false;\n    }\n    ngAfterViewInit() {\n        if (this.global) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.triggerEventListener = this.renderer.listen(documentTarget, this.triggerEvent, (event) => {\n                this.show(event);\n                event.preventDefault();\n            });\n        }\n        else if (this.target) {\n            this.triggerEventListener = this.renderer.listen(this.target, this.triggerEvent, (event) => {\n                this.show(event);\n                event.preventDefault();\n            });\n        }\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.containerViewChild.nativeElement);\n            else\n                DomHandler.appendChild(this.containerViewChild.nativeElement, this.appendTo);\n        }\n    }\n    show(event) {\n        this.clearActiveItem();\n        this.position(event);\n        this.moveOnTop();\n        this.containerViewChild.nativeElement.style.display = 'block';\n        this.preventDocumentDefault = true;\n        DomHandler.fadeIn(this.containerViewChild.nativeElement, 250);\n        this.bindGlobalListeners();\n        if (event) {\n            event.preventDefault();\n        }\n        this.onShow.emit();\n    }\n    hide() {\n        this.containerViewChild.nativeElement.style.display = 'none';\n        if (this.autoZIndex) {\n            ZIndexUtils.clear(this.containerViewChild.nativeElement);\n        }\n        this.clearActiveItem();\n        this.unbindGlobalListeners();\n        this.onHide.emit();\n    }\n    moveOnTop() {\n        if (this.autoZIndex && this.containerViewChild && this.containerViewChild.nativeElement.style.display !== 'block') {\n            ZIndexUtils.set('menu', this.containerViewChild.nativeElement, this.baseZIndex + this.config.zIndex.menu);\n        }\n    }\n    toggle(event) {\n        if (this.containerViewChild.nativeElement.offsetParent)\n            this.hide();\n        else\n            this.show(event);\n    }\n    position(event) {\n        if (event) {\n            let left = event.pageX + 1;\n            let top = event.pageY + 1;\n            let width = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetWidth : DomHandler.getHiddenElementOuterWidth(this.containerViewChild.nativeElement);\n            let height = this.containerViewChild.nativeElement.offsetParent ? this.containerViewChild.nativeElement.offsetHeight : DomHandler.getHiddenElementOuterHeight(this.containerViewChild.nativeElement);\n            let viewport = DomHandler.getViewport();\n            //flip\n            if (left + width - document.body.scrollLeft > viewport.width) {\n                left -= width;\n            }\n            //flip\n            if (top + height - document.body.scrollTop > viewport.height) {\n                top -= height;\n            }\n            //fit\n            if (left < document.body.scrollLeft) {\n                left = document.body.scrollLeft;\n            }\n            //fit\n            if (top < document.body.scrollTop) {\n                top = document.body.scrollTop;\n            }\n            this.containerViewChild.nativeElement.style.left = left + 'px';\n            this.containerViewChild.nativeElement.style.top = top + 'px';\n        }\n    }\n    positionSubmenu(sublist) {\n        let parentMenuItem = sublist.parentElement.parentElement;\n        let viewport = DomHandler.getViewport();\n        let sublistWidth = sublist.offsetParent ? sublist.offsetWidth : DomHandler.getHiddenElementOuterWidth(sublist);\n        let sublistHeight = sublist.offsetHeight ? sublist.offsetHeight : DomHandler.getHiddenElementOuterHeight(sublist);\n        let itemOuterWidth = DomHandler.getOuterWidth(parentMenuItem.children[0]);\n        let itemOuterHeight = DomHandler.getOuterHeight(parentMenuItem.children[0]);\n        let containerOffset = DomHandler.getOffset(parentMenuItem.parentElement);\n        sublist.style.zIndex = ++DomHandler.zindex;\n        if ((parseInt(containerOffset.top) + itemOuterHeight + sublistHeight) > (viewport.height - DomHandler.calculateScrollbarHeight())) {\n            sublist.style.removeProperty('top');\n            sublist.style.bottom = '0px';\n        }\n        else {\n            sublist.style.removeProperty('bottom');\n            sublist.style.top = '0px';\n        }\n        if ((parseInt(containerOffset.left) + itemOuterWidth + sublistWidth) > (viewport.width - DomHandler.calculateScrollbarWidth())) {\n            sublist.style.left = -sublistWidth + 'px';\n        }\n        else {\n            sublist.style.left = itemOuterWidth + 'px';\n        }\n    }\n    isItemMatched(menuitem) {\n        return DomHandler.hasClass(menuitem, 'p-menuitem') && !DomHandler.hasClass(menuitem.children[0], 'p-disabled');\n    }\n    findNextItem(menuitem, isRepeated) {\n        let nextMenuitem = menuitem.nextElementSibling;\n        if (nextMenuitem) {\n            return this.isItemMatched(nextMenuitem) ? nextMenuitem : this.findNextItem(nextMenuitem, isRepeated);\n        }\n        else {\n            let firstItem = menuitem.parentElement.children[0];\n            return this.isItemMatched(firstItem) ? firstItem : (!isRepeated ? this.findNextItem(firstItem, true) : null);\n        }\n    }\n    findPrevItem(menuitem, isRepeated) {\n        let prevMenuitem = menuitem.previousElementSibling;\n        if (prevMenuitem) {\n            return this.isItemMatched(prevMenuitem) ? prevMenuitem : this.findPrevItem(prevMenuitem, isRepeated);\n        }\n        else {\n            let lastItem = menuitem.parentElement.children[menuitem.parentElement.children.length - 1];\n            return this.isItemMatched(lastItem) ? lastItem : (!isRepeated ? this.findPrevItem(lastItem, true) : null);\n        }\n    }\n    getActiveItem() {\n        let activeItemKey = this.contextMenuService.activeItemKey;\n        return activeItemKey == null ? null : DomHandler.findSingle(this.containerViewChild.nativeElement, '.p-menuitem[data-ik=\"' + activeItemKey + '\"]');\n    }\n    clearActiveItem() {\n        if (this.contextMenuService.activeItemKey) {\n            this.removeActiveFromSubLists(this.containerViewChild.nativeElement);\n            this.contextMenuService.reset();\n        }\n    }\n    removeActiveFromSubLists(el) {\n        let sublists = DomHandler.find(el, '.p-submenu-list-active');\n        for (let sublist of sublists) {\n            DomHandler.removeClass(sublist, 'p-submenu-list-active');\n        }\n    }\n    removeActiveFromSublist(menuitem) {\n        if (menuitem) {\n            let sublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n            if (sublist && DomHandler.hasClass(menuitem, 'p-submenu-list-active')) {\n                DomHandler.removeClass(menuitem, 'p-submenu-list-active');\n            }\n        }\n    }\n    bindGlobalListeners() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', (event) => {\n                if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !event.ctrlKey && event.button !== 2) {\n                    this.hide();\n                }\n            });\n            this.documentTriggerListener = this.renderer.listen(documentTarget, this.triggerEvent, (event) => {\n                if (this.containerViewChild.nativeElement.offsetParent && this.isOutsideClicked(event) && !this.preventDocumentDefault) {\n                    this.hide();\n                }\n                this.preventDocumentDefault = false;\n            });\n        }\n        this.zone.runOutsideAngular(() => {\n            if (!this.windowResizeListener) {\n                this.windowResizeListener = this.onWindowResize.bind(this);\n                window.addEventListener('resize', this.windowResizeListener);\n            }\n        });\n        if (!this.documentKeydownListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentKeydownListener = this.renderer.listen(documentTarget, 'keydown', (event) => {\n                let activeItem = this.getActiveItem();\n                switch (event.key) {\n                    case 'ArrowDown':\n                        if (activeItem) {\n                            this.removeActiveFromSublist(activeItem);\n                            activeItem = this.findNextItem(activeItem);\n                        }\n                        else {\n                            let firstItem = DomHandler.findSingle(this.containerViewChild.nativeElement, '.p-menuitem-link').parentElement;\n                            activeItem = this.isItemMatched(firstItem) ? firstItem : this.findNextItem(firstItem);\n                        }\n                        if (activeItem) {\n                            this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                        }\n                        event.preventDefault();\n                        break;\n                    case 'ArrowUp':\n                        if (activeItem) {\n                            this.removeActiveFromSublist(activeItem);\n                            activeItem = this.findPrevItem(activeItem);\n                        }\n                        else {\n                            let sublist = DomHandler.findSingle(this.containerViewChild.nativeElement, 'ul');\n                            let lastItem = sublist.children[sublist.children.length - 1];\n                            activeItem = this.isItemMatched(lastItem) ? lastItem : this.findPrevItem(lastItem);\n                        }\n                        if (activeItem) {\n                            this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                        }\n                        event.preventDefault();\n                        break;\n                    case 'ArrowRight':\n                        if (activeItem) {\n                            let sublist = DomHandler.findSingle(activeItem, '.p-submenu-list');\n                            if (sublist) {\n                                DomHandler.addClass(sublist, 'p-submenu-list-active');\n                                activeItem = DomHandler.findSingle(sublist, '.p-menuitem-link:not(.p-disabled)').parentElement;\n                                if (activeItem) {\n                                    this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                                }\n                            }\n                        }\n                        event.preventDefault();\n                        break;\n                    case 'ArrowLeft':\n                        if (activeItem) {\n                            let sublist = activeItem.parentElement;\n                            if (sublist && DomHandler.hasClass(sublist, 'p-submenu-list-active')) {\n                                DomHandler.removeClass(sublist, 'p-submenu-list-active');\n                                activeItem = sublist.parentElement.parentElement;\n                                if (activeItem) {\n                                    this.contextMenuService.changeKey(activeItem.getAttribute('data-ik'));\n                                }\n                            }\n                        }\n                        event.preventDefault();\n                        break;\n                    case 'Escape':\n                        this.hide();\n                        event.preventDefault();\n                        break;\n                    case 'Enter':\n                        if (activeItem) {\n                            this.handleItemClick(event, this.findModelItemFromKey(this.contextMenuService.activeItemKey), activeItem);\n                        }\n                        event.preventDefault();\n                        break;\n                    default:\n                        break;\n                }\n            });\n        }\n    }\n    findModelItemFromKey(key) {\n        if (key == null || !this.model) {\n            return null;\n        }\n        let indexes = key.split('_');\n        return indexes.reduce((item, currentIndex) => {\n            return item ? item.items[currentIndex] : this.model[currentIndex];\n        }, null);\n    }\n    handleItemClick(event, item, menuitem) {\n        if (!item || item.disabled) {\n            return;\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (item.items) {\n            let childSublist = DomHandler.findSingle(menuitem, '.p-submenu-list');\n            if (childSublist) {\n                if (DomHandler.hasClass(childSublist, 'p-submenu-list-active')) {\n                    this.removeActiveFromSubLists(menuitem);\n                }\n                else {\n                    DomHandler.addClass(childSublist, 'p-submenu-list-active');\n                    this.positionSubmenu(childSublist);\n                }\n            }\n        }\n        if (!item.items) {\n            this.hide();\n        }\n    }\n    unbindGlobalListeners() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n        if (this.documentTriggerListener) {\n            this.documentTriggerListener();\n            this.documentTriggerListener = null;\n        }\n        if (this.windowResizeListener) {\n            window.removeEventListener('resize', this.windowResizeListener);\n            this.windowResizeListener = null;\n        }\n        if (this.documentKeydownListener) {\n            this.documentKeydownListener();\n            this.documentKeydownListener = null;\n        }\n    }\n    onWindowResize(event) {\n        if (this.containerViewChild.nativeElement.offsetParent) {\n            this.hide();\n        }\n    }\n    isOutsideClicked(event) {\n        return !(this.containerViewChild.nativeElement.isSameNode(event.target) || this.containerViewChild.nativeElement.contains(event.target));\n    }\n    ngOnDestroy() {\n        this.unbindGlobalListeners();\n        if (this.triggerEventListener) {\n            this.triggerEventListener();\n        }\n        if (this.containerViewChild && this.autoZIndex) {\n            ZIndexUtils.clear(this.containerViewChild.nativeElement);\n        }\n        if (this.appendTo) {\n            this.el.nativeElement.appendChild(this.containerViewChild.nativeElement);\n        }\n        this.ngDestroy$.next(true);\n        this.ngDestroy$.complete();\n    }\n}\nContextMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenu, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i5.ContextMenuService }, { token: i5.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\nContextMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: ContextMenu, selector: \"p-contextMenu\", inputs: { model: \"model\", global: \"global\", target: \"target\", style: \"style\", styleClass: \"styleClass\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", triggerEvent: \"triggerEvent\" }, outputs: { onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div #container [ngClass]=\"'p-contextmenu p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <p-contextMenuSub [item]=\"model\" [root]=\"true\"></p-contextMenuSub>\n        </div>\n    `, isInline: true, styles: [\".p-contextmenu{position:absolute;display:none}.p-contextmenu ul{margin:0;padding:0;list-style:none}.p-contextmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-contextmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-contextmenu .p-menuitem-text{line-height:1}.p-contextmenu .p-menuitem{position:relative}.p-contextmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-contextmenu .p-menuitem-active>p-contextmenusub>.p-submenu-list.p-submenu-list-active{display:block!important}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: ContextMenuSub, selector: \"p-contextMenuSub\", inputs: [\"item\", \"root\", \"parentItemKey\"], outputs: [\"leafClick\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-contextMenu', template: `\n        <div #container [ngClass]=\"'p-contextmenu p-component'\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <p-contextMenuSub [item]=\"model\" [root]=\"true\"></p-contextMenuSub>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-contextmenu{position:absolute;display:none}.p-contextmenu ul{margin:0;padding:0;list-style:none}.p-contextmenu .p-submenu-list{position:absolute;min-width:100%;z-index:1;display:none}.p-contextmenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-contextmenu .p-menuitem-text{line-height:1}.p-contextmenu .p-menuitem{position:relative}.p-contextmenu .p-menuitem-link .p-submenu-icon{margin-left:auto}.p-contextmenu .p-menuitem-active>p-contextmenusub>.p-submenu-list.p-submenu-list-active{display:block!important}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i5.ContextMenuService }, { type: i5.PrimeNGConfig }]; }, propDecorators: { model: [{\n                type: Input\n            }], global: [{\n                type: Input\n            }], target: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], triggerEvent: [{\n                type: Input\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }] } });\nclass ContextMenuModule {\n}\nContextMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nContextMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuModule, declarations: [ContextMenu, ContextMenuSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule], exports: [ContextMenu, RouterModule, TooltipModule] });\nContextMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuModule, providers: [ContextMenuService], imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ContextMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n                    exports: [ContextMenu, RouterModule, TooltipModule],\n                    declarations: [ContextMenu, ContextMenuSub],\n                    providers: [ContextMenuService]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ContextMenu, ContextMenuModule, ContextMenuSub };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,UAAvB,EAAmCC,SAAnC,EAA8CC,iBAA9C,EAAiEC,MAAjE,EAAyEC,KAAzE,EAAgFC,MAAhF,EAAwFC,SAAxF,EAAmGC,uBAAnG,EAA4HC,QAA5H,QAA4I,eAA5I;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,kBAAT,QAAmC,aAAnC;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,OAAT,QAAwB,MAAxB;AACA,SAASC,SAAT,QAA0B,gBAA1B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;;;;;;;;;;;;IAuFiGxB,EAIjF,yB;;;;qBAJiFA,E;IAAAA,EAIlB,uBAJkBA,EAIlB,qD;;;;;;IAJkBA,EAUzE,yB;;;;qBAVyEA,E;IAAAA,EAUxB,oE;;;;;;IAVwBA,EAWzE,8B;IAXyEA,EAWI,U;IAXJA,EAWmB,e;;;;qBAXnBA,E;IAAAA,EAWI,a;IAXJA,EAWI,kC;;;;;;IAXJA,EAYjD,yB;;;;qBAZiDA,E;IAAAA,EAYnB,yCAZmBA,EAYnB,gB;;;;;;IAZmBA,EAazE,8B;IAbyEA,EAaY,U;IAbZA,EAa2B,e;;;;qBAb3BA,E;IAAAA,EAatB,gD;IAbsBA,EAaY,a;IAbZA,EAaY,kC;;;;;;IAbZA,EAczE,yB;;;;;;;;;;;;;iBAdyEA,E;;IAAAA,EAO7E,2B;IAP6EA,EAQ3B;MAR2BA,EAQ3B;MAR2BA,EAQ3B;;MAAA,YAR2BA,EAQ3B;;MAAA,gBAR2BA,EAQ3B;MAAA;MAAA;MAAA,gBAR2BA,EAQ3B;MAAA,OAR2BA,EAQlB,wDAAqC,wBAArC,EAAT;IAAA,E;IAR2BA,EAUzE,uF;IAVyEA,EAWzE,uF;IAXyEA,EAYzE,4GAZyEA,EAYzE,wB;IAZyEA,EAazE,uF;IAbyEA,EAczE,uF;IAdyEA,EAe7E,e;;;;iBAf6EA,E;;oBAAAA,E;;;mBAAAA,E;IAAAA,EAOL,kDAPKA,EAOL,6C;IAPKA,EAOhD,yDAPgDA,EAOhD,8M;IAPgDA,EAU1C,a;IAV0CA,EAU1C,kC;IAV0CA,EAW1C,a;IAX0CA,EAW1C,gE;IAX0CA,EAazC,a;IAbyCA,EAazC,mC;IAbyCA,EAczB,a;IAdyBA,EAczB,mC;;;;;;IAdyBA,EAoBzE,yB;;;;qBApByEA,E;IAAAA,EAoBxB,oE;;;;;;IApBwBA,EAqBzE,8B;IArByEA,EAqBS,U;IArBTA,EAqBwB,e;;;;qBArBxBA,E;IAAAA,EAqBS,a;IArBTA,EAqBS,kC;;;;;;IArBTA,EAsB5C,yB;;;;qBAtB4CA,E;IAAAA,EAsBd,yCAtBcA,EAsBd,gB;;;;;;IAtBcA,EAuBzE,8B;IAvByEA,EAuBY,U;IAvBZA,EAuB2B,e;;;;qBAvB3BA,E;IAAAA,EAuBtB,gD;IAvBsBA,EAuBY,a;IAvBZA,EAuBY,kC;;;;;;IAvBZA,EAwBzE,yB;;;;;;;;;;;;iBAxByEA,E;;IAAAA,EAgB7E,2B;IAhB6EA,EAkBzE;MAlByEA,EAkBzE;MAlByEA,EAkBzE;;MAAA,YAlByEA,EAkBzE;;MAAA,gBAlByEA,EAkBzE;MAAA;MAAA;MAAA,gBAlByEA,EAkBzE;MAAA,OAlByEA,EAkBhE,wDAAqC,wBAArC,EAAT;IAAA,E;IAlByEA,EAoBzE,uF;IApByEA,EAqBzE,uF;IArByEA,EAsBzE,4GAtByEA,EAsBzE,wB;IAtByEA,EAuBzE,uF;IAvByEA,EAwBzE,uF;IAxByEA,EAyB7E,e;;;;iBAzB6EA,E;;qBAAAA,E;IAAAA,EAgBjD,iMAhBiDA,EAgBjD,iEAhBiDA,EAgBjD,+R;IAhBiDA,EAiBwB,oG;IAjBxBA,EAoB1C,a;IApB0CA,EAoB1C,kC;IApB0CA,EAqB1C,a;IArB0CA,EAqB1C,gE;IArB0CA,EAuBzC,a;IAvByCA,EAuBzC,mC;IAvByCA,EAwBzB,a;IAxByBA,EAwBzB,mC;;;;;;iBAxByBA,E;;IAAAA,EA0B7E,0C;IA1B6EA,EA0BQ;MA1BRA,EA0BQ;MAAA,gBA1BRA,EA0BQ;MAAA,OA1BRA,EA0BqB,mCAAb;IAAA,E;IA1BRA,EA0BoC,e;;;;oBA1BpCA,E;;;oBAAAA,E;IAAAA,EA0B3D,wE;;;;;;;;;;;;;;iBA1B2DA,E;;IAAAA,EAKjF,8B;IALiFA,EAM7E;MAN6EA,EAM7E;MAAA,gBAN6EA,EAM7E;MAAA;MAAA;MAAA,gBAN6EA,EAM7E;MAAA,OAN6EA,EAM/D,wDAA8B,wBAA9B,EAAd;IAAA;MAN6EA,EAM7E;MAAA,iBAN6EA,EAM7E;MAAA,gBAN6EA,EAM7E;MAAA,OAN6EA,EAMH,wDAA1E;IAAA,E;IAN6EA,EAO7E,6E;IAP6EA,EAgB7E,6E;IAhB6EA,EA0B7E,2G;IA1B6EA,EA2BjF,e;;;;oBA3BiFA,E;;;mBAAAA,E;IAAAA,EAK+F,gC;IAL/FA,EAK1C,uBAL0CA,EAK1C,qK;IAL0CA,EAMyC,gD;IANzCA,EAOzE,a;IAPyEA,EAOzE,yC;IAPyEA,EAgBzE,a;IAhByEA,EAgBzE,wC;IAhByEA,EA0BX,a;IA1BWA,EA0BX,mC;;;;;;IA1BWA,EAIjF,yE;IAJiFA,EAKjF,0E;;;;;IALiFA,EAI5E,uC;IAJ4EA,EAK5E,a;IAL4EA,EAK5E,wC;;;;;;;;;;;;AA1FrB,MAAMyB,cAAN,CAAqB;EACjBC,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKC,SAAL,GAAiB,IAAI3B,YAAJ,EAAjB;IACA,KAAK0B,WAAL,GAAmBA,WAAnB;EACH;;EACDE,QAAQ,GAAG;IACP,KAAKC,+BAAL,GAAuC,KAAKH,WAAL,CAAiBI,kBAAjB,CAAoCC,oBAApC,CAAyDC,IAAzD,CAA8DX,SAAS,CAAC,KAAKK,WAAL,CAAiBO,UAAlB,CAAvE,EAAsGC,SAAtG,CAAiHC,aAAD,IAAmB;MACtK,KAAKA,aAAL,GAAqBA,aAArB;;MACA,IAAI,KAAKC,QAAL,CAAc,KAAKC,aAAnB,KAAqCzB,UAAU,CAAC0B,QAAX,CAAoB,KAAKC,gBAAL,CAAsBC,aAA1C,EAAyD,uBAAzD,CAAzC,EAA4H;QACxH,KAAKd,WAAL,CAAiBe,eAAjB,CAAiC,KAAKF,gBAAL,CAAsBC,aAAvD;MACH;;MACD,KAAKd,WAAL,CAAiBgB,EAAjB,CAAoBC,YAApB;IACH,CANsC,CAAvC;EAOH;;EACDC,gBAAgB,CAACC,KAAD,EAAQC,IAAR,EAAcC,GAAd,EAAmB;IAC/B,IAAI,KAAKC,WAAT,EAAsB;MAClBC,YAAY,CAAC,KAAKD,WAAN,CAAZ;MACA,KAAKA,WAAL,GAAmB,IAAnB;IACH;;IACD,IAAIF,IAAI,CAACI,QAAT,EAAmB;MACf,KAAKf,aAAL,GAAqB,IAArB;MACA;IACH;;IACD,IAAIW,IAAI,CAACK,KAAT,EAAgB;MACZ,IAAIC,YAAY,GAAGxC,UAAU,CAACyC,UAAX,CAAsBR,KAAK,CAACS,aAA5B,EAA2C,iBAA3C,CAAnB;MACA1C,UAAU,CAAC2C,QAAX,CAAoBH,YAApB,EAAkC,uBAAlC;IACH;;IACD,KAAK1B,WAAL,CAAiBI,kBAAjB,CAAoC0B,SAApC,CAA8CT,GAA9C;EACH;;EACDU,gBAAgB,CAACZ,KAAD,EAAQC,IAAR,EAAc;IAC1B,IAAIA,IAAI,CAACI,QAAT,EAAmB;MACf;IACH;;IACD,IAAI,KAAKxB,WAAL,CAAiBgC,EAAjB,CAAoBlB,aAApB,CAAkCmB,QAAlC,CAA2Cd,KAAK,CAACe,SAAjD,CAAJ,EAAiE;MAC7D,IAAId,IAAI,CAACK,KAAT,EAAgB;QACZ,KAAKzB,WAAL,CAAiBmC,wBAAjB,CAA0ChB,KAAK,CAACS,aAAhD;MACH;;MACD,IAAI,CAAC,KAAKQ,IAAV,EAAgB;QACZ,KAAKpC,WAAL,CAAiBI,kBAAjB,CAAoC0B,SAApC,CAA8C,KAAKnB,aAAnD;MACH;IACJ;EACJ;;EACD0B,WAAW,CAAClB,KAAD,EAAQC,IAAR,EAAckB,QAAd,EAAwBjB,GAAxB,EAA6B;IACpC,IAAID,IAAI,CAACI,QAAT,EAAmB;MACfL,KAAK,CAACoB,cAAN;MACA;IACH;;IACD,IAAI,CAACnB,IAAI,CAACoB,GAAN,IAAa,CAACpB,IAAI,CAACqB,UAAvB,EAAmC;MAC/BtB,KAAK,CAACoB,cAAN;IACH;;IACD,IAAInB,IAAI,CAACsB,OAAT,EAAkB;MACdtB,IAAI,CAACsB,OAAL,CAAa;QACTC,aAAa,EAAExB,KADN;QAETC,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,IAAIA,IAAI,CAACK,KAAT,EAAgB;MACZ,IAAIC,YAAY,GAAGxC,UAAU,CAACyC,UAAX,CAAsBW,QAAtB,EAAgC,iBAAhC,CAAnB;;MACA,IAAIZ,YAAJ,EAAkB;QACd,IAAI,KAAKhB,QAAL,CAAcW,GAAd,KAAsBnC,UAAU,CAAC0B,QAAX,CAAoBc,YAApB,EAAkC,uBAAlC,CAA1B,EAAsF;UAClF,KAAK1B,WAAL,CAAiBmC,wBAAjB,CAA0CG,QAA1C;QACH,CAFD,MAGK;UACDpD,UAAU,CAAC2C,QAAX,CAAoBH,YAApB,EAAkC,uBAAlC;QACH;;QACD,KAAK1B,WAAL,CAAiBI,kBAAjB,CAAoC0B,SAApC,CAA8CT,GAA9C;MACH;IACJ;;IACD,IAAI,CAACD,IAAI,CAACK,KAAV,EAAiB;MACb,KAAKmB,WAAL;IACH;EACJ;;EACDA,WAAW,GAAG;IACV,IAAI,KAAKR,IAAT,EAAe;MACX,KAAKpC,WAAL,CAAiB6C,IAAjB;IACH;;IACD,KAAK5C,SAAL,CAAe6C,IAAf;EACH;;EACDC,MAAM,CAACC,KAAD,EAAQ;IACV,OAAO,KAAKZ,IAAL,GAAYa,MAAM,CAACD,KAAD,CAAlB,GAA4B,KAAKrC,aAAL,GAAqB,GAArB,GAA2BqC,KAA9D;EACH;;EACDtC,QAAQ,CAACW,GAAD,EAAM;IACV,OAAQ,KAAKZ,aAAL,KAAuB,KAAKA,aAAL,CAAmByC,UAAnB,CAA8B7B,GAAG,GAAG,GAApC,KAA4C,KAAKZ,aAAL,KAAuBY,GAA1F,CAAR;EACH;;AAnFgB;;AAqFrBvB,cAAc,CAACqD,IAAf;EAAA,iBAA2GrD,cAA3G,EAAiGzB,EAAjG,mBAA2IE,UAAU,CAAC,MAAM6E,WAAP,CAArJ;AAAA;;AACAtD,cAAc,CAACuD,IAAf,kBADiGhF,EACjG;EAAA,MAA+FyB,cAA/F;EAAA;EAAA;IAAA;MADiGzB,EACjG;MADiGA,EACjG;IAAA;;IAAA;MAAA;;MADiGA,EACjG,qBADiGA,EACjG;MADiGA,EACjG,qBADiGA,EACjG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADiGA,EAEzF,8BADR;MADiGA,EAGrF,6EAFZ;MADiGA,EA6BzF,eA5BR;IAAA;;IAAA;MADiGA,EAE5E,uBAF4EA,EAE5E,oCADrB;MADiGA,EAGtC,aAF3D;MADiGA,EAGtC,4DAF3D;IAAA;EAAA;EAAA,eA6BiEW,EAAE,CAACsE,OA7BpE,EA6B+JtE,EAAE,CAACuE,OA7BlK,EA6B4RvE,EAAE,CAACwE,IA7B/R,EA6BgYxE,EAAE,CAACyE,OA7BnY,EA6BqdjE,EAAE,CAACkE,kBA7Bxd,EA6BwtBlE,EAAE,CAACmE,gBA7B3tB,EA6By7BtE,EAAE,CAACuE,MA7B57B,EA6Bw/BhE,EAAE,CAACiE,OA7B3/B,EA6BmzC/D,cA7BnzC;EAAA;AAAA;;AA8BA;EAAA,mDA/BiGzB,EA+BjG,mBAA2FyB,cAA3F,EAAuH,CAAC;IAC5GgE,IAAI,EAAEtF,SADsG;IAE5GuF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA/BmB;MAgCCC,aAAa,EAAEzF,iBAAiB,CAAC0F,IAhClC;MAiCCC,IAAI,EAAE;QACF,SAAS;MADP;IAjCP,CAAD;EAFsG,CAAD,CAAvH,EAuC4B,YAAY;IAChC,OAAO,CAAC;MAAEN,IAAI,EAAEO,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBR,IAAI,EAAEpF,MADkB;QAExBqF,IAAI,EAAE,CAACxF,UAAU,CAAC,MAAM6E,WAAP,CAAX;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CA5CL,EA4CuB;IAAEhC,IAAI,EAAE,CAAC;MAChB0C,IAAI,EAAEnF;IADU,CAAD,CAAR;IAEPyD,IAAI,EAAE,CAAC;MACP0B,IAAI,EAAEnF;IADC,CAAD,CAFC;IAIPgC,aAAa,EAAE,CAAC;MAChBmD,IAAI,EAAEnF;IADU,CAAD,CAJR;IAMPsB,SAAS,EAAE,CAAC;MACZ6D,IAAI,EAAElF;IADM,CAAD,CANJ;IAQPiC,gBAAgB,EAAE,CAAC;MACnBiD,IAAI,EAAEjF,SADa;MAEnBkF,IAAI,EAAE,CAAC,SAAD;IAFa,CAAD,CARX;IAWPQ,iBAAiB,EAAE,CAAC;MACpBT,IAAI,EAAEjF,SADc;MAEpBkF,IAAI,EAAE,CAAC,UAAD;IAFc,CAAD;EAXZ,CA5CvB;AAAA;;AA2DA,MAAMX,WAAN,CAAkB;EACdrD,WAAW,CAACiC,EAAD,EAAKwC,QAAL,EAAexD,EAAf,EAAmByD,IAAnB,EAAyBrE,kBAAzB,EAA6CsE,MAA7C,EAAqD;IAC5D,KAAK1C,EAAL,GAAUA,EAAV;IACA,KAAKwC,QAAL,GAAgBA,QAAhB;IACA,KAAKxD,EAAL,GAAUA,EAAV;IACA,KAAKyD,IAAL,GAAYA,IAAZ;IACA,KAAKrE,kBAAL,GAA0BA,kBAA1B;IACA,KAAKsE,MAAL,GAAcA,MAAd;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,YAAL,GAAoB,aAApB;IACA,KAAKC,MAAL,GAAc,IAAIxG,YAAJ,EAAd;IACA,KAAKyG,MAAL,GAAc,IAAIzG,YAAJ,EAAd;IACA,KAAKiC,UAAL,GAAkB,IAAIb,OAAJ,EAAlB;IACA,KAAKsF,sBAAL,GAA8B,KAA9B;EACH;;EACDC,eAAe,GAAG;IACd,IAAI,KAAKC,MAAT,EAAiB;MACb,MAAMC,cAAc,GAAG,KAAKnD,EAAL,GAAU,KAAKA,EAAL,CAAQlB,aAAR,CAAsBsE,aAAhC,GAAgD,UAAvE;MACA,KAAKC,oBAAL,GAA4B,KAAKb,QAAL,CAAcc,MAAd,CAAqBH,cAArB,EAAqC,KAAKN,YAA1C,EAAyD1D,KAAD,IAAW;QAC3F,KAAKoE,IAAL,CAAUpE,KAAV;QACAA,KAAK,CAACoB,cAAN;MACH,CAH2B,CAA5B;IAIH,CAND,MAOK,IAAI,KAAKiD,MAAT,EAAiB;MAClB,KAAKH,oBAAL,GAA4B,KAAKb,QAAL,CAAcc,MAAd,CAAqB,KAAKE,MAA1B,EAAkC,KAAKX,YAAvC,EAAsD1D,KAAD,IAAW;QACxF,KAAKoE,IAAL,CAAUpE,KAAV;QACAA,KAAK,CAACoB,cAAN;MACH,CAH2B,CAA5B;IAIH;;IACD,IAAI,KAAKkD,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACIC,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKC,kBAAL,CAAwB/E,aAAlD,EADJ,KAGI5B,UAAU,CAAC0G,WAAX,CAAuB,KAAKC,kBAAL,CAAwB/E,aAA/C,EAA8D,KAAK2E,QAAnE;IACP;EACJ;;EACDF,IAAI,CAACpE,KAAD,EAAQ;IACR,KAAK2E,eAAL;IACA,KAAKC,QAAL,CAAc5E,KAAd;IACA,KAAK6E,SAAL;IACA,KAAKH,kBAAL,CAAwB/E,aAAxB,CAAsCmF,KAAtC,CAA4CC,OAA5C,GAAsD,OAAtD;IACA,KAAKlB,sBAAL,GAA8B,IAA9B;IACA9F,UAAU,CAACiH,MAAX,CAAkB,KAAKN,kBAAL,CAAwB/E,aAA1C,EAAyD,GAAzD;IACA,KAAKsF,mBAAL;;IACA,IAAIjF,KAAJ,EAAW;MACPA,KAAK,CAACoB,cAAN;IACH;;IACD,KAAKuC,MAAL,CAAYhC,IAAZ;EACH;;EACDD,IAAI,GAAG;IACH,KAAKgD,kBAAL,CAAwB/E,aAAxB,CAAsCmF,KAAtC,CAA4CC,OAA5C,GAAsD,MAAtD;;IACA,IAAI,KAAKvB,UAAT,EAAqB;MACjBpF,WAAW,CAAC8G,KAAZ,CAAkB,KAAKR,kBAAL,CAAwB/E,aAA1C;IACH;;IACD,KAAKgF,eAAL;IACA,KAAKQ,qBAAL;IACA,KAAKvB,MAAL,CAAYjC,IAAZ;EACH;;EACDkD,SAAS,GAAG;IACR,IAAI,KAAKrB,UAAL,IAAmB,KAAKkB,kBAAxB,IAA8C,KAAKA,kBAAL,CAAwB/E,aAAxB,CAAsCmF,KAAtC,CAA4CC,OAA5C,KAAwD,OAA1G,EAAmH;MAC/G3G,WAAW,CAACgH,GAAZ,CAAgB,MAAhB,EAAwB,KAAKV,kBAAL,CAAwB/E,aAAhD,EAA+D,KAAK8D,UAAL,GAAkB,KAAKF,MAAL,CAAY8B,MAAZ,CAAmBC,IAApG;IACH;EACJ;;EACDC,MAAM,CAACvF,KAAD,EAAQ;IACV,IAAI,KAAK0E,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAA1C,EACI,KAAK9D,IAAL,GADJ,KAGI,KAAK0C,IAAL,CAAUpE,KAAV;EACP;;EACD4E,QAAQ,CAAC5E,KAAD,EAAQ;IACZ,IAAIA,KAAJ,EAAW;MACP,IAAIyF,IAAI,GAAGzF,KAAK,CAAC0F,KAAN,GAAc,CAAzB;MACA,IAAIC,GAAG,GAAG3F,KAAK,CAAC4F,KAAN,GAAc,CAAxB;MACA,IAAIC,KAAK,GAAG,KAAKnB,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAAtC,GAAqD,KAAKd,kBAAL,CAAwB/E,aAAxB,CAAsCmG,WAA3F,GAAyG/H,UAAU,CAACgI,0BAAX,CAAsC,KAAKrB,kBAAL,CAAwB/E,aAA9D,CAArH;MACA,IAAIqG,MAAM,GAAG,KAAKtB,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAAtC,GAAqD,KAAKd,kBAAL,CAAwB/E,aAAxB,CAAsCsG,YAA3F,GAA0GlI,UAAU,CAACmI,2BAAX,CAAuC,KAAKxB,kBAAL,CAAwB/E,aAA/D,CAAvH;MACA,IAAIwG,QAAQ,GAAGpI,UAAU,CAACqI,WAAX,EAAf,CALO,CAMP;;MACA,IAAIX,IAAI,GAAGI,KAAP,GAAetB,QAAQ,CAACC,IAAT,CAAc6B,UAA7B,GAA0CF,QAAQ,CAACN,KAAvD,EAA8D;QAC1DJ,IAAI,IAAII,KAAR;MACH,CATM,CAUP;;;MACA,IAAIF,GAAG,GAAGK,MAAN,GAAezB,QAAQ,CAACC,IAAT,CAAc8B,SAA7B,GAAyCH,QAAQ,CAACH,MAAtD,EAA8D;QAC1DL,GAAG,IAAIK,MAAP;MACH,CAbM,CAcP;;;MACA,IAAIP,IAAI,GAAGlB,QAAQ,CAACC,IAAT,CAAc6B,UAAzB,EAAqC;QACjCZ,IAAI,GAAGlB,QAAQ,CAACC,IAAT,CAAc6B,UAArB;MACH,CAjBM,CAkBP;;;MACA,IAAIV,GAAG,GAAGpB,QAAQ,CAACC,IAAT,CAAc8B,SAAxB,EAAmC;QAC/BX,GAAG,GAAGpB,QAAQ,CAACC,IAAT,CAAc8B,SAApB;MACH;;MACD,KAAK5B,kBAAL,CAAwB/E,aAAxB,CAAsCmF,KAAtC,CAA4CW,IAA5C,GAAmDA,IAAI,GAAG,IAA1D;MACA,KAAKf,kBAAL,CAAwB/E,aAAxB,CAAsCmF,KAAtC,CAA4Ca,GAA5C,GAAkDA,GAAG,GAAG,IAAxD;IACH;EACJ;;EACD/F,eAAe,CAAC2G,OAAD,EAAU;IACrB,IAAIC,cAAc,GAAGD,OAAO,CAACE,aAAR,CAAsBA,aAA3C;IACA,IAAIN,QAAQ,GAAGpI,UAAU,CAACqI,WAAX,EAAf;IACA,IAAIM,YAAY,GAAGH,OAAO,CAACf,YAAR,GAAuBe,OAAO,CAACT,WAA/B,GAA6C/H,UAAU,CAACgI,0BAAX,CAAsCQ,OAAtC,CAAhE;IACA,IAAII,aAAa,GAAGJ,OAAO,CAACN,YAAR,GAAuBM,OAAO,CAACN,YAA/B,GAA8ClI,UAAU,CAACmI,2BAAX,CAAuCK,OAAvC,CAAlE;IACA,IAAIK,cAAc,GAAG7I,UAAU,CAAC8I,aAAX,CAAyBL,cAAc,CAACM,QAAf,CAAwB,CAAxB,CAAzB,CAArB;IACA,IAAIC,eAAe,GAAGhJ,UAAU,CAACiJ,cAAX,CAA0BR,cAAc,CAACM,QAAf,CAAwB,CAAxB,CAA1B,CAAtB;IACA,IAAIG,eAAe,GAAGlJ,UAAU,CAACmJ,SAAX,CAAqBV,cAAc,CAACC,aAApC,CAAtB;IACAF,OAAO,CAACzB,KAAR,CAAcO,MAAd,GAAuB,EAAEtH,UAAU,CAACoJ,MAApC;;IACA,IAAKC,QAAQ,CAACH,eAAe,CAACtB,GAAjB,CAAR,GAAgCoB,eAAhC,GAAkDJ,aAAnD,GAAqER,QAAQ,CAACH,MAAT,GAAkBjI,UAAU,CAACsJ,wBAAX,EAA3F,EAAmI;MAC/Hd,OAAO,CAACzB,KAAR,CAAcwC,cAAd,CAA6B,KAA7B;MACAf,OAAO,CAACzB,KAAR,CAAcyC,MAAd,GAAuB,KAAvB;IACH,CAHD,MAIK;MACDhB,OAAO,CAACzB,KAAR,CAAcwC,cAAd,CAA6B,QAA7B;MACAf,OAAO,CAACzB,KAAR,CAAca,GAAd,GAAoB,KAApB;IACH;;IACD,IAAKyB,QAAQ,CAACH,eAAe,CAACxB,IAAjB,CAAR,GAAiCmB,cAAjC,GAAkDF,YAAnD,GAAoEP,QAAQ,CAACN,KAAT,GAAiB9H,UAAU,CAACyJ,uBAAX,EAAzF,EAAgI;MAC5HjB,OAAO,CAACzB,KAAR,CAAcW,IAAd,GAAqB,CAACiB,YAAD,GAAgB,IAArC;IACH,CAFD,MAGK;MACDH,OAAO,CAACzB,KAAR,CAAcW,IAAd,GAAqBmB,cAAc,GAAG,IAAtC;IACH;EACJ;;EACDa,aAAa,CAACtG,QAAD,EAAW;IACpB,OAAOpD,UAAU,CAAC0B,QAAX,CAAoB0B,QAApB,EAA8B,YAA9B,KAA+C,CAACpD,UAAU,CAAC0B,QAAX,CAAoB0B,QAAQ,CAAC2F,QAAT,CAAkB,CAAlB,CAApB,EAA0C,YAA1C,CAAvD;EACH;;EACDY,YAAY,CAACvG,QAAD,EAAWwG,UAAX,EAAuB;IAC/B,IAAIC,YAAY,GAAGzG,QAAQ,CAAC0G,kBAA5B;;IACA,IAAID,YAAJ,EAAkB;MACd,OAAO,KAAKH,aAAL,CAAmBG,YAAnB,IAAmCA,YAAnC,GAAkD,KAAKF,YAAL,CAAkBE,YAAlB,EAAgCD,UAAhC,CAAzD;IACH,CAFD,MAGK;MACD,IAAIG,SAAS,GAAG3G,QAAQ,CAACsF,aAAT,CAAuBK,QAAvB,CAAgC,CAAhC,CAAhB;MACA,OAAO,KAAKW,aAAL,CAAmBK,SAAnB,IAAgCA,SAAhC,GAA6C,CAACH,UAAD,GAAc,KAAKD,YAAL,CAAkBI,SAAlB,EAA6B,IAA7B,CAAd,GAAmD,IAAvG;IACH;EACJ;;EACDC,YAAY,CAAC5G,QAAD,EAAWwG,UAAX,EAAuB;IAC/B,IAAIK,YAAY,GAAG7G,QAAQ,CAAC8G,sBAA5B;;IACA,IAAID,YAAJ,EAAkB;MACd,OAAO,KAAKP,aAAL,CAAmBO,YAAnB,IAAmCA,YAAnC,GAAkD,KAAKD,YAAL,CAAkBC,YAAlB,EAAgCL,UAAhC,CAAzD;IACH,CAFD,MAGK;MACD,IAAIO,QAAQ,GAAG/G,QAAQ,CAACsF,aAAT,CAAuBK,QAAvB,CAAgC3F,QAAQ,CAACsF,aAAT,CAAuBK,QAAvB,CAAgCqB,MAAhC,GAAyC,CAAzE,CAAf;MACA,OAAO,KAAKV,aAAL,CAAmBS,QAAnB,IAA+BA,QAA/B,GAA2C,CAACP,UAAD,GAAc,KAAKI,YAAL,CAAkBG,QAAlB,EAA4B,IAA5B,CAAd,GAAkD,IAApG;IACH;EACJ;;EACDE,aAAa,GAAG;IACZ,IAAI9I,aAAa,GAAG,KAAKL,kBAAL,CAAwBK,aAA5C;IACA,OAAOA,aAAa,IAAI,IAAjB,GAAwB,IAAxB,GAA+BvB,UAAU,CAACyC,UAAX,CAAsB,KAAKkE,kBAAL,CAAwB/E,aAA9C,EAA6D,0BAA0BL,aAA1B,GAA0C,IAAvG,CAAtC;EACH;;EACDqF,eAAe,GAAG;IACd,IAAI,KAAK1F,kBAAL,CAAwBK,aAA5B,EAA2C;MACvC,KAAK0B,wBAAL,CAA8B,KAAK0D,kBAAL,CAAwB/E,aAAtD;MACA,KAAKV,kBAAL,CAAwBoJ,KAAxB;IACH;EACJ;;EACDrH,wBAAwB,CAACH,EAAD,EAAK;IACzB,IAAIyH,QAAQ,GAAGvK,UAAU,CAACwK,IAAX,CAAgB1H,EAAhB,EAAoB,wBAApB,CAAf;;IACA,KAAK,IAAI0F,OAAT,IAAoB+B,QAApB,EAA8B;MAC1BvK,UAAU,CAACyK,WAAX,CAAuBjC,OAAvB,EAAgC,uBAAhC;IACH;EACJ;;EACDkC,uBAAuB,CAACtH,QAAD,EAAW;IAC9B,IAAIA,QAAJ,EAAc;MACV,IAAIoF,OAAO,GAAGxI,UAAU,CAACyC,UAAX,CAAsBW,QAAtB,EAAgC,iBAAhC,CAAd;;MACA,IAAIoF,OAAO,IAAIxI,UAAU,CAAC0B,QAAX,CAAoB0B,QAApB,EAA8B,uBAA9B,CAAf,EAAuE;QACnEpD,UAAU,CAACyK,WAAX,CAAuBrH,QAAvB,EAAiC,uBAAjC;MACH;IACJ;EACJ;;EACD8D,mBAAmB,GAAG;IAClB,IAAI,CAAC,KAAKyD,qBAAV,EAAiC;MAC7B,MAAM1E,cAAc,GAAG,KAAKnD,EAAL,GAAU,KAAKA,EAAL,CAAQlB,aAAR,CAAsBsE,aAAhC,GAAgD,UAAvE;MACA,KAAKyE,qBAAL,GAA6B,KAAKrF,QAAL,CAAcc,MAAd,CAAqBH,cAArB,EAAqC,OAArC,EAA+ChE,KAAD,IAAW;QAClF,IAAI,KAAK0E,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAAtC,IAAsD,KAAKmD,gBAAL,CAAsB3I,KAAtB,CAAtD,IAAsF,CAACA,KAAK,CAAC4I,OAA7F,IAAwG5I,KAAK,CAAC6I,MAAN,KAAiB,CAA7H,EAAgI;UAC5H,KAAKnH,IAAL;QACH;MACJ,CAJ4B,CAA7B;MAKA,KAAKoH,uBAAL,GAA+B,KAAKzF,QAAL,CAAcc,MAAd,CAAqBH,cAArB,EAAqC,KAAKN,YAA1C,EAAyD1D,KAAD,IAAW;QAC9F,IAAI,KAAK0E,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAAtC,IAAsD,KAAKmD,gBAAL,CAAsB3I,KAAtB,CAAtD,IAAsF,CAAC,KAAK6D,sBAAhG,EAAwH;UACpH,KAAKnC,IAAL;QACH;;QACD,KAAKmC,sBAAL,GAA8B,KAA9B;MACH,CAL8B,CAA/B;IAMH;;IACD,KAAKP,IAAL,CAAUyF,iBAAV,CAA4B,MAAM;MAC9B,IAAI,CAAC,KAAKC,oBAAV,EAAgC;QAC5B,KAAKA,oBAAL,GAA4B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA5B;QACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKJ,oBAAvC;MACH;IACJ,CALD;;IAMA,IAAI,CAAC,KAAKK,uBAAV,EAAmC;MAC/B,MAAMrF,cAAc,GAAG,KAAKnD,EAAL,GAAU,KAAKA,EAAL,CAAQlB,aAAR,CAAsBsE,aAAhC,GAAgD,UAAvE;MACA,KAAKoF,uBAAL,GAA+B,KAAKhG,QAAL,CAAcc,MAAd,CAAqBH,cAArB,EAAqC,SAArC,EAAiDhE,KAAD,IAAW;QACtF,IAAIsJ,UAAU,GAAG,KAAKlB,aAAL,EAAjB;;QACA,QAAQpI,KAAK,CAACE,GAAd;UACI,KAAK,WAAL;YACI,IAAIoJ,UAAJ,EAAgB;cACZ,KAAKb,uBAAL,CAA6Ba,UAA7B;cACAA,UAAU,GAAG,KAAK5B,YAAL,CAAkB4B,UAAlB,CAAb;YACH,CAHD,MAIK;cACD,IAAIxB,SAAS,GAAG/J,UAAU,CAACyC,UAAX,CAAsB,KAAKkE,kBAAL,CAAwB/E,aAA9C,EAA6D,kBAA7D,EAAiF8G,aAAjG;cACA6C,UAAU,GAAG,KAAK7B,aAAL,CAAmBK,SAAnB,IAAgCA,SAAhC,GAA4C,KAAKJ,YAAL,CAAkBI,SAAlB,CAAzD;YACH;;YACD,IAAIwB,UAAJ,EAAgB;cACZ,KAAKrK,kBAAL,CAAwB0B,SAAxB,CAAkC2I,UAAU,CAACC,YAAX,CAAwB,SAAxB,CAAlC;YACH;;YACDvJ,KAAK,CAACoB,cAAN;YACA;;UACJ,KAAK,SAAL;YACI,IAAIkI,UAAJ,EAAgB;cACZ,KAAKb,uBAAL,CAA6Ba,UAA7B;cACAA,UAAU,GAAG,KAAKvB,YAAL,CAAkBuB,UAAlB,CAAb;YACH,CAHD,MAIK;cACD,IAAI/C,OAAO,GAAGxI,UAAU,CAACyC,UAAX,CAAsB,KAAKkE,kBAAL,CAAwB/E,aAA9C,EAA6D,IAA7D,CAAd;cACA,IAAIuI,QAAQ,GAAG3B,OAAO,CAACO,QAAR,CAAiBP,OAAO,CAACO,QAAR,CAAiBqB,MAAjB,GAA0B,CAA3C,CAAf;cACAmB,UAAU,GAAG,KAAK7B,aAAL,CAAmBS,QAAnB,IAA+BA,QAA/B,GAA0C,KAAKH,YAAL,CAAkBG,QAAlB,CAAvD;YACH;;YACD,IAAIoB,UAAJ,EAAgB;cACZ,KAAKrK,kBAAL,CAAwB0B,SAAxB,CAAkC2I,UAAU,CAACC,YAAX,CAAwB,SAAxB,CAAlC;YACH;;YACDvJ,KAAK,CAACoB,cAAN;YACA;;UACJ,KAAK,YAAL;YACI,IAAIkI,UAAJ,EAAgB;cACZ,IAAI/C,OAAO,GAAGxI,UAAU,CAACyC,UAAX,CAAsB8I,UAAtB,EAAkC,iBAAlC,CAAd;;cACA,IAAI/C,OAAJ,EAAa;gBACTxI,UAAU,CAAC2C,QAAX,CAAoB6F,OAApB,EAA6B,uBAA7B;gBACA+C,UAAU,GAAGvL,UAAU,CAACyC,UAAX,CAAsB+F,OAAtB,EAA+B,mCAA/B,EAAoEE,aAAjF;;gBACA,IAAI6C,UAAJ,EAAgB;kBACZ,KAAKrK,kBAAL,CAAwB0B,SAAxB,CAAkC2I,UAAU,CAACC,YAAX,CAAwB,SAAxB,CAAlC;gBACH;cACJ;YACJ;;YACDvJ,KAAK,CAACoB,cAAN;YACA;;UACJ,KAAK,WAAL;YACI,IAAIkI,UAAJ,EAAgB;cACZ,IAAI/C,OAAO,GAAG+C,UAAU,CAAC7C,aAAzB;;cACA,IAAIF,OAAO,IAAIxI,UAAU,CAAC0B,QAAX,CAAoB8G,OAApB,EAA6B,uBAA7B,CAAf,EAAsE;gBAClExI,UAAU,CAACyK,WAAX,CAAuBjC,OAAvB,EAAgC,uBAAhC;gBACA+C,UAAU,GAAG/C,OAAO,CAACE,aAAR,CAAsBA,aAAnC;;gBACA,IAAI6C,UAAJ,EAAgB;kBACZ,KAAKrK,kBAAL,CAAwB0B,SAAxB,CAAkC2I,UAAU,CAACC,YAAX,CAAwB,SAAxB,CAAlC;gBACH;cACJ;YACJ;;YACDvJ,KAAK,CAACoB,cAAN;YACA;;UACJ,KAAK,QAAL;YACI,KAAKM,IAAL;YACA1B,KAAK,CAACoB,cAAN;YACA;;UACJ,KAAK,OAAL;YACI,IAAIkI,UAAJ,EAAgB;cACZ,KAAKE,eAAL,CAAqBxJ,KAArB,EAA4B,KAAKyJ,oBAAL,CAA0B,KAAKxK,kBAAL,CAAwBK,aAAlD,CAA5B,EAA8FgK,UAA9F;YACH;;YACDtJ,KAAK,CAACoB,cAAN;YACA;;UACJ;YACI;QAnER;MAqEH,CAvE8B,CAA/B;IAwEH;EACJ;;EACDqI,oBAAoB,CAACvJ,GAAD,EAAM;IACtB,IAAIA,GAAG,IAAI,IAAP,IAAe,CAAC,KAAKwJ,KAAzB,EAAgC;MAC5B,OAAO,IAAP;IACH;;IACD,IAAIC,OAAO,GAAGzJ,GAAG,CAAC0J,KAAJ,CAAU,GAAV,CAAd;IACA,OAAOD,OAAO,CAACE,MAAR,CAAe,CAAC5J,IAAD,EAAO6J,YAAP,KAAwB;MAC1C,OAAO7J,IAAI,GAAGA,IAAI,CAACK,KAAL,CAAWwJ,YAAX,CAAH,GAA8B,KAAKJ,KAAL,CAAWI,YAAX,CAAzC;IACH,CAFM,EAEJ,IAFI,CAAP;EAGH;;EACDN,eAAe,CAACxJ,KAAD,EAAQC,IAAR,EAAckB,QAAd,EAAwB;IACnC,IAAI,CAAClB,IAAD,IAASA,IAAI,CAACI,QAAlB,EAA4B;MACxB;IACH;;IACD,IAAIJ,IAAI,CAACsB,OAAT,EAAkB;MACdtB,IAAI,CAACsB,OAAL,CAAa;QACTC,aAAa,EAAExB,KADN;QAETC,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,IAAIA,IAAI,CAACK,KAAT,EAAgB;MACZ,IAAIC,YAAY,GAAGxC,UAAU,CAACyC,UAAX,CAAsBW,QAAtB,EAAgC,iBAAhC,CAAnB;;MACA,IAAIZ,YAAJ,EAAkB;QACd,IAAIxC,UAAU,CAAC0B,QAAX,CAAoBc,YAApB,EAAkC,uBAAlC,CAAJ,EAAgE;UAC5D,KAAKS,wBAAL,CAA8BG,QAA9B;QACH,CAFD,MAGK;UACDpD,UAAU,CAAC2C,QAAX,CAAoBH,YAApB,EAAkC,uBAAlC;UACA,KAAKX,eAAL,CAAqBW,YAArB;QACH;MACJ;IACJ;;IACD,IAAI,CAACN,IAAI,CAACK,KAAV,EAAiB;MACb,KAAKoB,IAAL;IACH;EACJ;;EACDyD,qBAAqB,GAAG;IACpB,IAAI,KAAKuD,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;;IACD,IAAI,KAAKI,uBAAT,EAAkC;MAC9B,KAAKA,uBAAL;MACA,KAAKA,uBAAL,GAA+B,IAA/B;IACH;;IACD,IAAI,KAAKE,oBAAT,EAA+B;MAC3BG,MAAM,CAACY,mBAAP,CAA2B,QAA3B,EAAqC,KAAKf,oBAA1C;MACA,KAAKA,oBAAL,GAA4B,IAA5B;IACH;;IACD,IAAI,KAAKK,uBAAT,EAAkC;MAC9B,KAAKA,uBAAL;MACA,KAAKA,uBAAL,GAA+B,IAA/B;IACH;EACJ;;EACDJ,cAAc,CAACjJ,KAAD,EAAQ;IAClB,IAAI,KAAK0E,kBAAL,CAAwB/E,aAAxB,CAAsC6F,YAA1C,EAAwD;MACpD,KAAK9D,IAAL;IACH;EACJ;;EACDiH,gBAAgB,CAAC3I,KAAD,EAAQ;IACpB,OAAO,EAAE,KAAK0E,kBAAL,CAAwB/E,aAAxB,CAAsCqK,UAAtC,CAAiDhK,KAAK,CAACqE,MAAvD,KAAkE,KAAKK,kBAAL,CAAwB/E,aAAxB,CAAsCmB,QAAtC,CAA+Cd,KAAK,CAACqE,MAArD,CAApE,CAAP;EACH;;EACD4F,WAAW,GAAG;IACV,KAAK9E,qBAAL;;IACA,IAAI,KAAKjB,oBAAT,EAA+B;MAC3B,KAAKA,oBAAL;IACH;;IACD,IAAI,KAAKQ,kBAAL,IAA2B,KAAKlB,UAApC,EAAgD;MAC5CpF,WAAW,CAAC8G,KAAZ,CAAkB,KAAKR,kBAAL,CAAwB/E,aAA1C;IACH;;IACD,IAAI,KAAK2E,QAAT,EAAmB;MACf,KAAKzD,EAAL,CAAQlB,aAAR,CAAsB8E,WAAtB,CAAkC,KAAKC,kBAAL,CAAwB/E,aAA1D;IACH;;IACD,KAAKP,UAAL,CAAgB8K,IAAhB,CAAqB,IAArB;IACA,KAAK9K,UAAL,CAAgB+K,QAAhB;EACH;;AAnVa;;AAqVlBlI,WAAW,CAACD,IAAZ;EAAA,iBAAwGC,WAAxG,EA/aiG/E,EA+ajG,mBAAqIA,EAAE,CAACkN,UAAxI,GA/aiGlN,EA+ajG,mBAA+JA,EAAE,CAACmN,SAAlK,GA/aiGnN,EA+ajG,mBAAwLA,EAAE,CAACoN,iBAA3L,GA/aiGpN,EA+ajG,mBAAyNA,EAAE,CAACqN,MAA5N,GA/aiGrN,EA+ajG,mBAA+Oc,EAAE,CAACC,kBAAlP,GA/aiGf,EA+ajG,mBAAiRc,EAAE,CAACwM,aAApR;AAAA;;AACAvI,WAAW,CAACC,IAAZ,kBAhbiGhF,EAgbjG;EAAA,MAA4F+E,WAA5F;EAAA;EAAA;IAAA;MAhbiG/E,EAgbjG;IAAA;;IAAA;MAAA;;MAhbiGA,EAgbjG,qBAhbiGA,EAgbjG;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAhbiGA,EAibzF,+BADR;MAhbiGA,EAkbrF,oCAFZ;MAhbiGA,EAmbzF,eAHR;IAAA;;IAAA;MAhbiGA,EAibjC,2BADhE;MAhbiGA,EAibzE,yEADxB;MAhbiGA,EAkbnE,aAF9B;MAhbiGA,EAkbnE,4CAF9B;IAAA;EAAA;EAAA,eAI8pBW,EAAE,CAACsE,OAJjqB,EAI4vBtE,EAAE,CAACyE,OAJ/vB,EAIi1B3D,cAJj1B;EAAA;EAAA;EAAA;AAAA;;AAKA;EAAA,mDArbiGzB,EAqbjG,mBAA2F+E,WAA3F,EAAoH,CAAC;IACzGU,IAAI,EAAEtF,SADmG;IAEzGuF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6BC,QAAQ,EAAG;AAC3D;AACA;AACA;AACA,KAJmB;MAIZ2H,eAAe,EAAE9M,uBAAuB,CAAC+M,MAJ7B;MAIqC3H,aAAa,EAAEzF,iBAAiB,CAAC0F,IAJtE;MAI4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAJlF;MAMI0H,MAAM,EAAE,CAAC,ilBAAD;IANZ,CAAD;EAFmG,CAAD,CAApH,EAS4B,YAAY;IAAE,OAAO,CAAC;MAAEhI,IAAI,EAAEzF,EAAE,CAACkN;IAAX,CAAD,EAA0B;MAAEzH,IAAI,EAAEzF,EAAE,CAACmN;IAAX,CAA1B,EAAkD;MAAE1H,IAAI,EAAEzF,EAAE,CAACoN;IAAX,CAAlD,EAAkF;MAAE3H,IAAI,EAAEzF,EAAE,CAACqN;IAAX,CAAlF,EAAuG;MAAE5H,IAAI,EAAE3E,EAAE,CAACC;IAAX,CAAvG,EAAwI;MAAE0E,IAAI,EAAE3E,EAAE,CAACwM;IAAX,CAAxI,CAAP;EAA6K,CATvN,EASyO;IAAEd,KAAK,EAAE,CAAC;MACnO/G,IAAI,EAAEnF;IAD6N,CAAD,CAAT;IAEzNuG,MAAM,EAAE,CAAC;MACTpB,IAAI,EAAEnF;IADG,CAAD,CAFiN;IAIzN6G,MAAM,EAAE,CAAC;MACT1B,IAAI,EAAEnF;IADG,CAAD,CAJiN;IAMzNsH,KAAK,EAAE,CAAC;MACRnC,IAAI,EAAEnF;IADE,CAAD,CANkN;IAQzNoN,UAAU,EAAE,CAAC;MACbjI,IAAI,EAAEnF;IADO,CAAD,CAR6M;IAUzN8G,QAAQ,EAAE,CAAC;MACX3B,IAAI,EAAEnF;IADK,CAAD,CAV+M;IAYzNgG,UAAU,EAAE,CAAC;MACbb,IAAI,EAAEnF;IADO,CAAD,CAZ6M;IAczNiG,UAAU,EAAE,CAAC;MACbd,IAAI,EAAEnF;IADO,CAAD,CAd6M;IAgBzNkG,YAAY,EAAE,CAAC;MACff,IAAI,EAAEnF;IADS,CAAD,CAhB2M;IAkBzNmG,MAAM,EAAE,CAAC;MACThB,IAAI,EAAElF;IADG,CAAD,CAlBiN;IAoBzNmG,MAAM,EAAE,CAAC;MACTjB,IAAI,EAAElF;IADG,CAAD,CApBiN;IAsBzNiH,kBAAkB,EAAE,CAAC;MACrB/B,IAAI,EAAEjF,SADe;MAErBkF,IAAI,EAAE,CAAC,WAAD;IAFe,CAAD;EAtBqM,CATzO;AAAA;;AAmCA,MAAMiI,iBAAN,CAAwB;;AAExBA,iBAAiB,CAAC7I,IAAlB;EAAA,iBAA8G6I,iBAA9G;AAAA;;AACAA,iBAAiB,CAACC,IAAlB,kBA3diG5N,EA2djG;EAAA,MAA+G2N;AAA/G;AACAA,iBAAiB,CAACE,IAAlB,kBA5diG7N,EA4djG;EAAA,WAA6I,CAACe,kBAAD,CAA7I;EAAA,UAA6KH,YAA7K,EAA2LQ,YAA3L,EAAyMH,YAAzM,EAAuNO,aAAvN,EAAsOJ,YAAtO,EAAoPI,aAApP;AAAA;;AACA;EAAA,mDA7diGxB,EA6djG,mBAA2F2N,iBAA3F,EAA0H,CAAC;IAC/GlI,IAAI,EAAE/E,QADyG;IAE/GgF,IAAI,EAAE,CAAC;MACCoI,OAAO,EAAE,CAAClN,YAAD,EAAeQ,YAAf,EAA6BH,YAA7B,EAA2CO,aAA3C,CADV;MAECuM,OAAO,EAAE,CAAChJ,WAAD,EAAc3D,YAAd,EAA4BI,aAA5B,CAFV;MAGCwM,YAAY,EAAE,CAACjJ,WAAD,EAActD,cAAd,CAHf;MAICwM,SAAS,EAAE,CAAClN,kBAAD;IAJZ,CAAD;EAFyG,CAAD,CAA1H;AAAA;AAUA;AACA;AACA;;;AAEA,SAASgE,WAAT,EAAsB4I,iBAAtB,EAAyClM,cAAzC"}, "metadata": {}, "sourceType": "module"}