{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class FormationService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = '/api/formations';\n  }\n\n  getFormations() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        return yield lastValueFrom(_this.http.get(_this.apiUrl));\n      } catch (error) {\n        console.error('Error fetching formations:', error);\n        return [];\n      }\n    })();\n  }\n\n  getFormation(id) {\n    return this.http.get(`${this.apiUrl}/${id}`);\n  }\n\n  createFormation(formation) {\n    return this.http.post(this.apiUrl, formation);\n  }\n\n  updateFormation(id, formation) {\n    return this.http.put(`${this.apiUrl}/${id}`, formation);\n  }\n\n  deleteFormation(id) {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n\n}\n\nFormationService.ɵfac = function FormationService_Factory(t) {\n  return new (t || FormationService)(i0.ɵɵinject(i1.HttpClient));\n};\n\nFormationService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: FormationService,\n  factory: FormationService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AAEA,SAAqBA,aAArB,QAA0C,MAA1C;;;AAIA,OAAM,MAAOC,gBAAP,CAAuB;EAG3BC,YAAoBC,IAApB,EAAoC;IAAhB;IAFZ,cAAS,iBAAT;EAEgC;;EAElCC,aAAa;IAAA;;IAAA;MACjB,IAAI;QACF,aAAaJ,aAAa,CAAC,KAAI,CAACG,IAAL,CAAUE,GAAV,CAA2B,KAAI,CAACC,MAAhC,CAAD,CAA1B;MACD,CAFD,CAEE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;QACA,OAAO,EAAP;MACD;IANgB;EAOlB;;EAEDE,YAAY,CAACC,EAAD,EAAW;IACrB,OAAO,KAAKP,IAAL,CAAUE,GAAV,CAAyB,GAAG,KAAKC,MAAM,IAAII,EAAE,EAA7C,CAAP;EACD;;EAEDC,eAAe,CAACC,SAAD,EAA8B;IAC3C,OAAO,KAAKT,IAAL,CAAUU,IAAV,CAA0B,KAAKP,MAA/B,EAAuCM,SAAvC,CAAP;EACD;;EAEDE,eAAe,CAACJ,EAAD,EAAaE,SAAb,EAA0C;IACvD,OAAO,KAAKT,IAAL,CAAUY,GAAV,CAAyB,GAAG,KAAKT,MAAM,IAAII,EAAE,EAA7C,EAAiDE,SAAjD,CAAP;EACD;;EAEDI,eAAe,CAACN,EAAD,EAAW;IACxB,OAAO,KAAKP,IAAL,CAAUc,MAAV,CAAiB,GAAG,KAAKX,MAAM,IAAII,EAAE,EAArC,CAAP;EACD;;AA5B0B;;;mBAAhBT,kBAAgBiB;AAAA;;;SAAhBjB;EAAgBkB,SAAhBlB,gBAAgB;EAAAmB,YADH", "names": ["lastValueFrom", "FormationService", "constructor", "http", "getFormations", "get", "apiUrl", "error", "console", "getFormation", "id", "createFormation", "formation", "post", "updateFormation", "put", "deleteFormation", "delete", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\services\\formation.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, lastValueFrom } from 'rxjs';\nimport { Formation } from '../models/formation.model';\n\n@Injectable({ providedIn: 'root' })\nexport class FormationService {\n  private apiUrl = '/api/formations';\n\n  constructor(private http: HttpClient) {}\n\n  async getFormations(): Promise<Formation[]> {\n    try {\n      return await lastValueFrom(this.http.get<Formation[]>(this.apiUrl));\n    } catch (error) {\n      console.error('Error fetching formations:', error);\n      return [];\n    }\n  }\n\n  getFormation(id: number): Observable<Formation> {\n    return this.http.get<Formation>(`${this.apiUrl}/${id}`);\n  }\n\n  createFormation(formation: Partial<Formation>): Observable<Formation> {\n    return this.http.post<Formation>(this.apiUrl, formation);\n  }\n\n  updateFormation(id: number, formation: Partial<Formation>): Observable<Formation> {\n    return this.http.put<Formation>(`${this.apiUrl}/${id}`, formation);\n  }\n\n  deleteFormation(id: number): Observable<any> {\n    return this.http.delete(`${this.apiUrl}/${id}`);\n  }\n}\n"]}, "metadata": {}, "sourceType": "module"}