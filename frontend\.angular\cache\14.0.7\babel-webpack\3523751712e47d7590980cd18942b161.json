{"ast": null, "code": "import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n  const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n\n  if (isFunction(selector)) {\n    return connect(selector, {\n      connector: subjectFactory\n    });\n  }\n\n  return source => new ConnectableObservable(source, subjectFactory);\n}", "map": {"version": 3, "names": ["ConnectableObservable", "isFunction", "connect", "multicast", "subjectOrSubjectFactory", "selector", "subjectFactory", "connector", "source"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/multicast.js"], "sourcesContent": ["import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { isFunction } from '../util/isFunction';\nimport { connect } from './connect';\nexport function multicast(subjectOrSubjectFactory, selector) {\n    const subjectFactory = isFunction(subjectOrSubjectFactory) ? subjectOrSubjectFactory : () => subjectOrSubjectFactory;\n    if (isFunction(selector)) {\n        return connect(selector, {\n            connector: subjectFactory,\n        });\n    }\n    return (source) => new ConnectableObservable(source, subjectFactory);\n}\n"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,qCAAtC;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,OAAT,QAAwB,WAAxB;AACA,OAAO,SAASC,SAAT,CAAmBC,uBAAnB,EAA4CC,QAA5C,EAAsD;EACzD,MAAMC,cAAc,GAAGL,UAAU,CAACG,uBAAD,CAAV,GAAsCA,uBAAtC,GAAgE,MAAMA,uBAA7F;;EACA,IAAIH,UAAU,CAACI,QAAD,CAAd,EAA0B;IACtB,OAAOH,OAAO,CAACG,QAAD,EAAW;MACrBE,SAAS,EAAED;IADU,CAAX,CAAd;EAGH;;EACD,OAAQE,MAAD,IAAY,IAAIR,qBAAJ,CAA0BQ,MAA1B,EAAkCF,cAAlC,CAAnB;AACH"}, "metadata": {}, "sourceType": "module"}