{"ast": null, "code": "import { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nconst DEFAULT_CONFIG = {\n  connector: () => new Subject()\n};\nexport function connect(selector, config = DEFAULT_CONFIG) {\n  const {\n    connector\n  } = config;\n  return operate((source, subscriber) => {\n    const subject = connector();\n    innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n}", "map": {"version": 3, "names": ["Subject", "innerFrom", "operate", "fromSubscribable", "DEFAULT_CONFIG", "connector", "connect", "selector", "config", "source", "subscriber", "subject", "subscribe", "add"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/connect.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nconst DEFAULT_CONFIG = {\n    connector: () => new Subject(),\n};\nexport function connect(selector, config = DEFAULT_CONFIG) {\n    const { connector } = config;\n    return operate((source, subscriber) => {\n        const subject = connector();\n        innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n        subscriber.add(source.subscribe(subject));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,gBAAT,QAAiC,gCAAjC;AACA,MAAMC,cAAc,GAAG;EACnBC,SAAS,EAAE,MAAM,IAAIL,OAAJ;AADE,CAAvB;AAGA,OAAO,SAASM,OAAT,CAAiBC,QAAjB,EAA2BC,MAAM,GAAGJ,cAApC,EAAoD;EACvD,MAAM;IAAEC;EAAF,IAAgBG,MAAtB;EACA,OAAON,OAAO,CAAC,CAACO,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,OAAO,GAAGN,SAAS,EAAzB;IACAJ,SAAS,CAACM,QAAQ,CAACJ,gBAAgB,CAACQ,OAAD,CAAjB,CAAT,CAAT,CAA+CC,SAA/C,CAAyDF,UAAzD;IACAA,UAAU,CAACG,GAAX,CAAeJ,MAAM,CAACG,SAAP,CAAiBD,OAAjB,CAAf;EACH,CAJa,CAAd;AAKH"}, "metadata": {}, "sourceType": "module"}