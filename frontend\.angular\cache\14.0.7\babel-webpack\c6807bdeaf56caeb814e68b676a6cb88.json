{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n  return operate((source, subscriber) => {\n    let taking = false;\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "source", "subscriber", "taking", "index", "subscribe", "value", "next"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/skipWhile.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipWhile(predicate) {\n    return operate((source, subscriber) => {\n        let taking = false;\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => (taking || (taking = !predicate(value, index++))) && subscriber.next(value)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAnB,EAA8B;EACjC,OAAOH,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,MAAM,GAAG,KAAb;IACA,IAAIC,KAAK,GAAG,CAAZ;IACAH,MAAM,CAACI,SAAP,CAAiBP,wBAAwB,CAACI,UAAD,EAAcI,KAAD,IAAW,CAACH,MAAM,KAAKA,MAAM,GAAG,CAACH,SAAS,CAACM,KAAD,EAAQF,KAAK,EAAb,CAAxB,CAAP,KAAqDF,UAAU,CAACK,IAAX,CAAgBD,KAAhB,CAA7E,CAAzC;EACH,CAJa,CAAd;AAKH"}, "metadata": {}, "sourceType": "module"}