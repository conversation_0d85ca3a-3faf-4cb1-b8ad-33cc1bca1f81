{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/demo/service/customer.service\";\nimport * as i2 from \"src/app/demo/service/product.service\";\nimport * as i3 from \"src/app/services/formation.service\";\nimport * as i4 from \"src/app/services/team.service\";\nimport * as i5 from \"src/app/demo/service/user.service\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/api\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dropdown\";\nimport * as i12 from \"@angular/common\";\nconst _c0 = [\"filter\"];\n\nfunction TableDemoComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function TableDemoComponent_ng_template_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(6);\n\n      return i0.ɵɵresetView(ctx_r7.clear(_r0));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 12);\n    i0.ɵɵelement(3, \"i\", 13);\n    i0.ɵɵelementStart(4, \"input\", 14, 15);\n    i0.ɵɵlistener(\"input\", function TableDemoComponent_ng_template_7_Template_input_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n\n      const _r0 = i0.ɵɵreference(6);\n\n      return i0.ɵɵresetView(ctx_r9.onGlobalFilter(_r0, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction TableDemoComponent_ng_template_8_ng_template_22_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const option_r14 = ctx.$implicit;\n    i0.ɵɵclassMap(\"formation-badge status-\" + option_r14.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(option_r14.label);\n  }\n}\n\nconst _c1 = function () {\n  return {\n    \"min-width\": \"12rem\"\n  };\n};\n\nfunction TableDemoComponent_ng_template_8_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-dropdown\", 24);\n    i0.ɵɵlistener(\"onChange\", function TableDemoComponent_ng_template_8_ng_template_22_Template_p_dropdown_onChange_0_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const filter_r12 = restoredCtx.filterCallback;\n      return i0.ɵɵresetView(filter_r12($event.value));\n    });\n    i0.ɵɵtemplate(1, TableDemoComponent_ng_template_8_ng_template_22_ng_template_1_Template, 2, 3, \"ng-template\", 25);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const value_r11 = ctx.$implicit;\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c1));\n    i0.ɵɵproperty(\"ngModel\", value_r11)(\"options\", ctx_r10.statuses);\n  }\n}\n\nfunction TableDemoComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Name \");\n    i0.ɵɵelement(3, \"p-columnFilter\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"th\");\n    i0.ɵɵtext(5, \"Trainer \");\n    i0.ɵɵelement(6, \"p-columnFilter\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Date \");\n    i0.ɵɵelement(9, \"p-columnFilter\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Duration \");\n    i0.ɵɵelement(12, \"p-columnFilter\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Team \");\n    i0.ɵɵelement(15, \"p-columnFilter\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Room \");\n    i0.ɵɵelement(18, \"p-columnFilter\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\");\n    i0.ɵɵtext(20, \"Status \");\n    i0.ɵɵelementStart(21, \"p-columnFilter\", 22);\n    i0.ɵɵtemplate(22, TableDemoComponent_ng_template_8_ng_template_22_Template, 2, 5, \"ng-template\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"th\");\n    i0.ɵɵtext(24, \"Description\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction TableDemoComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13, \"-\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\")(15, \"span\", 26);\n    i0.ɵɵtext(16, \"Active\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const formation_r17 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(formation_r17.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTrainerName(formation_r17.formateur_id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 6, formation_r17.date, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", formation_r17.duree, \"h\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r3.getTeamName(formation_r17.equipe_id));\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(formation_r17.description);\n  }\n}\n\nfunction TableDemoComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵtext(2, \"No formations found.\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction TableDemoComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 27);\n    i0.ɵɵtext(2, \"Loading formations data. Please wait.\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c2 = function () {\n  return [\"name\", \"trainer\", \"status\", \"room\", \"team\"];\n};\n\nexport class TableDemoComponent {\n  constructor(customerService, productService, formationService, teamService, userService) {\n    this.customerService = customerService;\n    this.productService = productService;\n    this.formationService = formationService;\n    this.teamService = teamService;\n    this.userService = userService;\n    this.formations = [];\n    this.teams = [];\n    this.trainers = [];\n    this.customers1 = [];\n    this.customers2 = [];\n    this.customers3 = [];\n    this.selectedCustomers1 = [];\n    this.selectedCustomer = {};\n    this.representatives = [];\n    this.statuses = [];\n    this.products = [];\n    this.expandedRows = {};\n    this.activityValues = [0, 100];\n    this.isExpanded = false;\n    this.idFrozen = false;\n    this.loading = true;\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // Load formations\n        _this.formations = yield _this.formationService.getFormations();\n        console.log('Loaded formations:', _this.formations); // Load teams for display\n\n        _this.teams = yield lastValueFrom(_this.teamService.getTeams()); // Load trainers for display\n\n        const trainersData = yield _this.userService.getUsersByRole('formateur');\n        _this.trainers = trainersData.map(trainer => Object.assign(Object.assign({}, trainer), {\n          name: `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`\n        }));\n        _this.loading = false;\n      } catch (error) {\n        console.error('Error loading formations:', error);\n        _this.loading = false;\n      } // Keep existing demo data loading for other tables\n\n\n      _this.customerService.getCustomersLarge().then(customers => {\n        _this.customers1 = customers; // @ts-ignore\n\n        _this.customers1.forEach(customer => customer.date = new Date(customer.date));\n      });\n\n      _this.customerService.getCustomersMedium().then(customers => _this.customers2 = customers);\n\n      _this.customerService.getCustomersLarge().then(customers => _this.customers3 = customers);\n\n      _this.productService.getProductsWithOrdersSmall().then(data => _this.products = data);\n\n      _this.representatives = [{\n        name: 'Amy Elsner',\n        image: 'amyelsner.png'\n      }, {\n        name: 'Anna Fali',\n        image: 'annafali.png'\n      }, {\n        name: 'Asiya Javayant',\n        image: 'asiyajavayant.png'\n      }, {\n        name: 'Bernardo Dominic',\n        image: 'bernardodominic.png'\n      }, {\n        name: 'Elwin Sharvill',\n        image: 'elwinsharvill.png'\n      }, {\n        name: 'Ioni Bowcher',\n        image: 'ionibowcher.png'\n      }, {\n        name: 'Ivan Magalhaes',\n        image: 'ivanmagalhaes.png'\n      }, {\n        name: 'Onyama Limba',\n        image: 'onyamalimba.png'\n      }, {\n        name: 'Stephen Shaw',\n        image: 'stephenshaw.png'\n      }, {\n        name: 'XuXue Feng',\n        image: 'xuxuefeng.png'\n      }];\n      _this.statuses = [{\n        label: 'Unqualified',\n        value: 'unqualified'\n      }, {\n        label: 'Qualified',\n        value: 'qualified'\n      }, {\n        label: 'New',\n        value: 'new'\n      }, {\n        label: 'Negotiation',\n        value: 'negotiation'\n      }, {\n        label: 'Renewal',\n        value: 'renewal'\n      }, {\n        label: 'Proposal',\n        value: 'proposal'\n      }];\n    })();\n  }\n\n  onSort() {\n    this.updateRowGroupMetaData();\n  }\n\n  updateRowGroupMetaData() {\n    var _a, _b;\n\n    this.rowGroupMetadata = {};\n\n    if (this.customers3) {\n      for (let i = 0; i < this.customers3.length; i++) {\n        const rowData = this.customers3[i];\n        const representativeName = ((_a = rowData === null || rowData === void 0 ? void 0 : rowData.representative) === null || _a === void 0 ? void 0 : _a.name) || '';\n\n        if (i === 0) {\n          this.rowGroupMetadata[representativeName] = {\n            index: 0,\n            size: 1\n          };\n        } else {\n          const previousRowData = this.customers3[i - 1];\n          const previousRowGroup = (_b = previousRowData === null || previousRowData === void 0 ? void 0 : previousRowData.representative) === null || _b === void 0 ? void 0 : _b.name;\n\n          if (representativeName === previousRowGroup) {\n            this.rowGroupMetadata[representativeName].size++;\n          } else {\n            this.rowGroupMetadata[representativeName] = {\n              index: i,\n              size: 1\n            };\n          }\n        }\n      }\n    }\n  }\n\n  expandAll() {\n    if (!this.isExpanded) {\n      this.products.forEach(product => product && product.name ? this.expandedRows[product.name] = true : '');\n    } else {\n      this.expandedRows = {};\n    }\n\n    this.isExpanded = !this.isExpanded;\n  }\n\n  formatCurrency(value) {\n    return value.toLocaleString('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    });\n  }\n\n  onGlobalFilter(table, event) {\n    table.filterGlobal(event.target.value, 'contains');\n  }\n\n  clear(table) {\n    table.clear();\n    this.filter.nativeElement.value = '';\n  } // Helper methods for displaying team and trainer names\n\n\n  getTeamName(teamId) {\n    const team = this.teams.find(t => t.id === teamId);\n    return team ? team.name : 'Unknown Team';\n  }\n\n  getTrainerName(trainerId) {\n    const trainer = this.trainers.find(t => t.id === trainerId);\n    return trainer ? trainer.name || 'Unknown Trainer' : 'Unknown Trainer';\n  }\n\n}\n\nTableDemoComponent.ɵfac = function TableDemoComponent_Factory(t) {\n  return new (t || TableDemoComponent)(i0.ɵɵdirectiveInject(i1.CustomerService), i0.ɵɵdirectiveInject(i2.ProductService), i0.ɵɵdirectiveInject(i3.FormationService), i0.ɵɵdirectiveInject(i4.TeamService), i0.ɵɵdirectiveInject(i5.UserService));\n};\n\nTableDemoComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TableDemoComponent,\n  selectors: [[\"ng-component\"]],\n  viewQuery: function TableDemoComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n    }\n  },\n  features: [i0.ɵɵProvidersFeature([MessageService, ConfirmationService])],\n  decls: 12,\n  vars: 7,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [\"dataKey\", \"id\", \"styleClass\", \"p-datatable-gridlines\", \"responsiveLayout\", \"scroll\", 3, \"value\", \"rows\", \"loading\", \"rowHover\", \"paginator\", \"globalFilterFields\"], [\"dt1\", \"\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"pTemplate\", \"loadingbody\"], [1, \"flex\", \"justify-content-between\", \"flex-column\", \"sm:flex-row\"], [\"pButton\", \"\", \"label\", \"Clear\", \"icon\", \"pi pi-filter-slash\", 1, \"p-button-outlined\", \"mb-2\", 3, \"click\"], [1, \"p-input-icon-left\", \"mb-2\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search Keyword\", 1, \"w-full\", 3, \"input\"], [\"filter\", \"\"], [\"type\", \"text\", \"field\", \"name\", \"display\", \"menu\", \"placeholder\", \"Search by name\"], [\"type\", \"text\", \"field\", \"trainer\", \"display\", \"menu\", \"placeholder\", \"Search by trainer\"], [\"type\", \"date\", \"field\", \"date\", \"display\", \"menu\", \"placeholder\", \"mm/dd/yyyy\"], [\"type\", \"text\", \"field\", \"duration\", \"display\", \"menu\", \"placeholder\", \"Search by duration\"], [\"type\", \"text\", \"field\", \"team\", \"display\", \"menu\", \"placeholder\", \"Search by team\"], [\"type\", \"text\", \"field\", \"room\", \"display\", \"menu\", \"placeholder\", \"Search by room\"], [\"field\", \"status\", \"matchMode\", \"equals\", \"display\", \"menu\"], [\"pTemplate\", \"filter\"], [\"placeholder\", \"Any\", 3, \"ngModel\", \"options\", \"onChange\"], [\"pTemplate\", \"item\"], [1, \"formation-badge\", \"status-active\"], [\"colspan\", \"8\"]],\n  template: function TableDemoComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n      i0.ɵɵtext(4, \"Training List\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"p-table\", 3, 4);\n      i0.ɵɵtemplate(7, TableDemoComponent_ng_template_7_Template, 6, 0, \"ng-template\", 5);\n      i0.ɵɵtemplate(8, TableDemoComponent_ng_template_8_Template, 25, 0, \"ng-template\", 6);\n      i0.ɵɵtemplate(9, TableDemoComponent_ng_template_9_Template, 19, 9, \"ng-template\", 7);\n      i0.ɵɵtemplate(10, TableDemoComponent_ng_template_10_Template, 3, 0, \"ng-template\", 8);\n      i0.ɵɵtemplate(11, TableDemoComponent_ng_template_11_Template, 3, 0, \"ng-template\", 9);\n      i0.ɵɵelementEnd()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"value\", ctx.formations)(\"rows\", 10)(\"loading\", ctx.loading)(\"rowHover\", true)(\"paginator\", true)(\"globalFilterFields\", i0.ɵɵpureFunction0(6, _c2));\n    }\n  },\n  dependencies: [i6.NgControlStatus, i6.NgModel, i7.Table, i8.PrimeTemplate, i7.ColumnFilter, i9.ButtonDirective, i10.InputText, i11.Dropdown, i12.DatePipe],\n  styles: [\"[_nghost-%COMP%]     .p-frozen-column {\\n  font-weight: bold;\\n}\\n\\n[_nghost-%COMP%]     .p-datatable-frozen-tbody {\\n  font-weight: bold;\\n}\\n\\n[_nghost-%COMP%]     .p-progressbar {\\n  height: 0.5rem;\\n}\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInRhYmxlZGVtby5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ1E7RUFDSSxpQkFBQTtBQUFaOztBQUdRO0VBQ0ksaUJBQUE7QUFBWjs7QUFHUTtFQUNJLGNBQUE7QUFBWiIsImZpbGUiOiJ0YWJsZWRlbW8uY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgICAgIDpob3N0IDo6bmctZGVlcCAgLnAtZnJvemVuLWNvbHVtbiB7XG4gICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDtcbiAgICAgICAgfVxuXG4gICAgICAgIDpob3N0IDo6bmctZGVlcCAucC1kYXRhdGFibGUtZnJvemVuLXRib2R5IHtcbiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgICB9XG5cbiAgICAgICAgOmhvc3QgOjpuZy1kZWVwIC5wLXByb2dyZXNzYmFyIHtcbiAgICAgICAgICAgIGhlaWdodDouNXJlbTtcbiAgICAgICAgfVxuICAgICJdfQ== */\"]\n});", "map": {"version": 3, "mappings": ";AAEA,SAASA,cAAT,EAAyBC,mBAAzB,QAAoD,aAApD;AAWA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;;;;;;;;ICPIC,gCAAkE,CAAlE,EAAkE,QAAlE,EAAkE,EAAlE;IACsFA;MAAAA;MAAA;;MAAA;;MAAA,OAASA,iCAAT;IAAmB,CAAnB;IAAqBA;IAC5GA;IACEA;IACAA;IAAsCA;MAAAA;MAAA;;MAAA;;MAAA,OAASA,kDAAT;IAAoC,CAApC;IAAtCA;;;;;;IAgBAA;IAAyDA;IAAgBA;;;;;IAAnEA;IAAmDA;IAAAA;;;;;;;;;;;;;;IAF1DA;IAAmDA;MAAA;MAAA;MAAA,OAAYA,wCAAZ;IAAgC,CAAhC;IACjDA;IAGFA;;;;;;IAJuGA;IAA3FA,oCAAiB,SAAjB,EAAiBC,gBAAjB;;;;;;IATZD,2BAAI,CAAJ,EAAI,IAAJ;IACGA;IAAKA;IAAsGA;IAC/GA;IAAIA;IAAQA;IAA4GA;IACxHA;IAAIA;IAAKA;IAAkGA;IAC3GA;IAAIA;IAASA;IAA8GA;IAC3HA;IAAIA;IAAKA;IAAsGA;IAC/GA;IAAIA;IAAKA;IAAsGA;IAC/GA;IAAIA;IAAOA;IACTA;IAOFA;IACAA;IAAIA;IAAWA;;;;;;IAIdA,2BAAI,CAAJ,EAAI,IAAJ;IACGA;IAAkBA;IACtBA;IAAIA;IAA0CA;IAC9CA;IAAIA;;IAAuCA;IAC3CA;IAAIA;IAAoBA;IACxBA;IAAIA;IAAoCA;IACxCA;IAAIA;IAACA;IACLA,4BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAAgDA;IAAMA;IACtDA;IAAIA;IAAyBA;;;;;;IAPzBA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAGAA;IAAAA;;;;;;IAIHA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IACeA;IAAoBA;;;;;;IAInCA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IACeA;IAAqCA;;;;;;;;ADjBxD,OAAM,MAAOE,kBAAP,CAAyB;EAmC3BC,YACYC,eADZ,EAEYC,cAFZ,EAGYC,gBAHZ,EAIYC,WAJZ,EAKYC,WALZ,EAKoC;IAJxB;IACA;IACA;IACA;IACA;IAvCZ,kBAA0B,EAA1B;IACA,aAAgB,EAAhB;IACA,gBAAmB,EAAnB;IAEA,kBAAyB,EAAzB;IAEA,kBAAyB,EAAzB;IAEA,kBAAyB,EAAzB;IAEA,0BAAiC,EAAjC;IAEA,wBAA6B,EAA7B;IAEA,uBAAoC,EAApC;IAEA,gBAAkB,EAAlB;IAEA,gBAAsB,EAAtB;IAIA,oBAA6B,EAA7B;IAEA,sBAA2B,CAAC,CAAD,EAAI,GAAJ,CAA3B;IAEA,kBAAsB,KAAtB;IAEA,gBAAoB,KAApB;IAEA,eAAmB,IAAnB;EAUK;;EAECC,QAAQ;IAAA;;IAAA;MACV,IAAI;QACA;QACA,KAAI,CAACC,UAAL,SAAwB,KAAI,CAACJ,gBAAL,CAAsBK,aAAtB,EAAxB;QACAC,OAAO,CAACC,GAAR,CAAY,oBAAZ,EAAkC,KAAI,CAACH,UAAvC,EAHA,CAKA;;QACA,KAAI,CAACI,KAAL,SAAmBf,aAAa,CAAC,KAAI,CAACQ,WAAL,CAAiBQ,QAAjB,EAAD,CAAhC,CANA,CAQA;;QACA,MAAMC,YAAY,SAAS,KAAI,CAACR,WAAL,CAAiBS,cAAjB,CAAgC,WAAhC,CAA3B;QACA,KAAI,CAACC,QAAL,GAAgBF,YAAY,CAACG,GAAb,CAAiBC,OAAO,IAAIC,gCACrCD,OADqC,GAC9B;UACVE,IAAI,EAAE,GAAGF,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACI,SAAS,GAAGJ,OAAO,CAACK,UAAR,GAAqB,OAAOL,OAAO,CAACK,UAAf,GAA4B,GAAjD,GAAuD,EAAE;QADlG,CAD8B,CAA5B,CAAhB;QAKA,KAAI,CAACC,OAAL,GAAe,KAAf;MACH,CAhBD,CAgBE,OAAOC,KAAP,EAAc;QACZf,OAAO,CAACe,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;QACA,KAAI,CAACD,OAAL,GAAe,KAAf;MACH,CApBS,CAsBV;;;MACA,KAAI,CAACtB,eAAL,CAAqBwB,iBAArB,GAAyCC,IAAzC,CAA8CC,SAAS,IAAG;QACtD,KAAI,CAACC,UAAL,GAAkBD,SAAlB,CADsD,CAEtD;;QACA,KAAI,CAACC,UAAL,CAAgBC,OAAhB,CAAwBC,QAAQ,IAAIA,QAAQ,CAACC,IAAT,GAAgB,IAAIC,IAAJ,CAASF,QAAQ,CAACC,IAAlB,CAApD;MACH,CAJD;;MAKA,KAAI,CAAC9B,eAAL,CAAqBgC,kBAArB,GAA0CP,IAA1C,CAA+CC,SAAS,IAAI,KAAI,CAACO,UAAL,GAAkBP,SAA9E;;MACA,KAAI,CAAC1B,eAAL,CAAqBwB,iBAArB,GAAyCC,IAAzC,CAA8CC,SAAS,IAAI,KAAI,CAACQ,UAAL,GAAkBR,SAA7E;;MACA,KAAI,CAACzB,cAAL,CAAoBkC,0BAApB,GAAiDV,IAAjD,CAAsDW,IAAI,IAAI,KAAI,CAACC,QAAL,GAAgBD,IAA9E;;MAEA,KAAI,CAACE,eAAL,GAAuB,CACnB;QAAEpB,IAAI,EAAE,YAAR;QAAsBqB,KAAK,EAAE;MAA7B,CADmB,EAEnB;QAAErB,IAAI,EAAE,WAAR;QAAqBqB,KAAK,EAAE;MAA5B,CAFmB,EAGnB;QAAErB,IAAI,EAAE,gBAAR;QAA0BqB,KAAK,EAAE;MAAjC,CAHmB,EAInB;QAAErB,IAAI,EAAE,kBAAR;QAA4BqB,KAAK,EAAE;MAAnC,CAJmB,EAKnB;QAAErB,IAAI,EAAE,gBAAR;QAA0BqB,KAAK,EAAE;MAAjC,CALmB,EAMnB;QAAErB,IAAI,EAAE,cAAR;QAAwBqB,KAAK,EAAE;MAA/B,CANmB,EAOnB;QAAErB,IAAI,EAAE,gBAAR;QAA0BqB,KAAK,EAAE;MAAjC,CAPmB,EAQnB;QAAErB,IAAI,EAAE,cAAR;QAAwBqB,KAAK,EAAE;MAA/B,CARmB,EASnB;QAAErB,IAAI,EAAE,cAAR;QAAwBqB,KAAK,EAAE;MAA/B,CATmB,EAUnB;QAAErB,IAAI,EAAE,YAAR;QAAsBqB,KAAK,EAAE;MAA7B,CAVmB,CAAvB;MAaA,KAAI,CAACC,QAAL,GAAgB,CACZ;QAAEC,KAAK,EAAE,aAAT;QAAwBC,KAAK,EAAE;MAA/B,CADY,EAEZ;QAAED,KAAK,EAAE,WAAT;QAAsBC,KAAK,EAAE;MAA7B,CAFY,EAGZ;QAAED,KAAK,EAAE,KAAT;QAAgBC,KAAK,EAAE;MAAvB,CAHY,EAIZ;QAAED,KAAK,EAAE,aAAT;QAAwBC,KAAK,EAAE;MAA/B,CAJY,EAKZ;QAAED,KAAK,EAAE,SAAT;QAAoBC,KAAK,EAAE;MAA3B,CALY,EAMZ;QAAED,KAAK,EAAE,UAAT;QAAqBC,KAAK,EAAE;MAA5B,CANY,CAAhB;IA7CU;EAqDb;;EAEDC,MAAM;IACF,KAAKC,sBAAL;EACH;;EAEDA,sBAAsB;;;IAClB,KAAKC,gBAAL,GAAwB,EAAxB;;IAEA,IAAI,KAAKX,UAAT,EAAqB;MACjB,KAAK,IAAIY,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKZ,UAAL,CAAgBa,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;QAC7C,MAAME,OAAO,GAAG,KAAKd,UAAL,CAAgBY,CAAhB,CAAhB;QACA,MAAMG,kBAAkB,GAAG,cAAO,SAAP,WAAO,WAAP,GAAO,MAAP,UAAO,CAAEC,cAAT,MAAuB,IAAvB,IAAuBC,aAAvB,GAAuB,MAAvB,GAAuBA,GAAEjC,IAAzB,KAAiC,EAA5D;;QAEA,IAAI4B,CAAC,KAAK,CAAV,EAAa;UACT,KAAKD,gBAAL,CAAsBI,kBAAtB,IAA4C;YAAEG,KAAK,EAAE,CAAT;YAAYC,IAAI,EAAE;UAAlB,CAA5C;QACH,CAFD,MAGK;UACD,MAAMC,eAAe,GAAG,KAAKpB,UAAL,CAAgBY,CAAC,GAAG,CAApB,CAAxB;UACA,MAAMS,gBAAgB,GAAG,qBAAe,SAAf,mBAAe,WAAf,GAAe,MAAf,kBAAe,CAAEL,cAAjB,MAA+B,IAA/B,IAA+BM,aAA/B,GAA+B,MAA/B,GAA+BA,GAAEtC,IAA1D;;UACA,IAAI+B,kBAAkB,KAAKM,gBAA3B,EAA6C;YACzC,KAAKV,gBAAL,CAAsBI,kBAAtB,EAA0CI,IAA1C;UACH,CAFD,MAGK;YACD,KAAKR,gBAAL,CAAsBI,kBAAtB,IAA4C;cAAEG,KAAK,EAAEN,CAAT;cAAYO,IAAI,EAAE;YAAlB,CAA5C;UACH;QACJ;MACJ;IACJ;EACJ;;EAEDI,SAAS;IACL,IAAI,CAAC,KAAKC,UAAV,EAAsB;MAClB,KAAKrB,QAAL,CAAcT,OAAd,CAAsB+B,OAAO,IAAIA,OAAO,IAAIA,OAAO,CAACzC,IAAnB,GAA0B,KAAK0C,YAAL,CAAkBD,OAAO,CAACzC,IAA1B,IAAkC,IAA5D,GAAmE,EAApG;IAEH,CAHD,MAGO;MACH,KAAK0C,YAAL,GAAoB,EAApB;IACH;;IACD,KAAKF,UAAL,GAAkB,CAAC,KAAKA,UAAxB;EACH;;EAEDG,cAAc,CAACnB,KAAD,EAAc;IACxB,OAAOA,KAAK,CAACoB,cAAN,CAAqB,OAArB,EAA8B;MAAEC,KAAK,EAAE,UAAT;MAAqBC,QAAQ,EAAE;IAA/B,CAA9B,CAAP;EACH;;EAEDC,cAAc,CAACC,KAAD,EAAeC,KAAf,EAA2B;IACrCD,KAAK,CAACE,YAAN,CAAoBD,KAAK,CAACE,MAAN,CAAkC3B,KAAtD,EAA6D,UAA7D;EACH;;EAED4B,KAAK,CAACJ,KAAD,EAAa;IACdA,KAAK,CAACI,KAAN;IACA,KAAKC,MAAL,CAAYC,aAAZ,CAA0B9B,KAA1B,GAAkC,EAAlC;EACH,CApJ0B,CAsJ3B;;;EACA+B,WAAW,CAACC,MAAD,EAAe;IACtB,MAAMC,IAAI,GAAG,KAAKjE,KAAL,CAAWkE,IAAX,CAAgBC,CAAC,IAAIA,CAAC,CAACC,EAAF,KAASJ,MAA9B,CAAb;IACA,OAAOC,IAAI,GAAGA,IAAI,CAACzD,IAAR,GAAe,cAA1B;EACH;;EAED6D,cAAc,CAACC,SAAD,EAAkB;IAC5B,MAAMhE,OAAO,GAAG,KAAKF,QAAL,CAAc8D,IAAd,CAAmBC,CAAC,IAAIA,CAAC,CAACC,EAAF,KAASE,SAAjC,CAAhB;IACA,OAAOhE,OAAO,GAAIA,OAAO,CAACE,IAAR,IAAgB,iBAApB,GAAyC,iBAAvD;EACH;;AA/J0B;;;mBAAlBpB,oBAAkBF;AAAA;;;QAAlBE;EAAkBmF;EAAAC;IAAA;;;;;;;;;;mCAfhB,CAACzF,cAAD,EAAiBC,mBAAjB;EAAqCyF;EAAAC;EAAAC;EAAAC;IAAA;MCrBpD1F,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,IAAlB;MAGOA;MAAaA;MACjBA;MACDA;MASAA;MAoBAA;MAYAA;MAKAA;MAKCA;;;;MApDcA;MAAAA,uCAAoB,MAApB,EAAoB,EAApB,EAAoB,SAApB,EAAoB2F,WAApB,EAAoB,UAApB,EAAoB,IAApB,EAAoB,WAApB,EAAoB,IAApB,EAAoB,oBAApB,EAAoB3F,0BAApB", "names": ["MessageService", "ConfirmationService", "lastValueFrom", "i0", "ctx_r10", "TableDemoComponent", "constructor", "customerService", "productService", "formationService", "teamService", "userService", "ngOnInit", "formations", "getFormations", "console", "log", "teams", "getTeams", "trainersData", "getUsersByRole", "trainers", "map", "trainer", "Object", "name", "first_name", "last_name", "specialite", "loading", "error", "getCustomersLarge", "then", "customers", "customers1", "for<PERSON>ach", "customer", "date", "Date", "getCustomersMedium", "customers2", "customers3", "getProductsWithOrdersSmall", "data", "products", "representatives", "image", "statuses", "label", "value", "onSort", "updateRowGroupMetaData", "rowGroupMetadata", "i", "length", "rowData", "<PERSON><PERSON><PERSON>", "representative", "_a", "index", "size", "previousRowData", "previousRowGroup", "_b", "expandAll", "isExpanded", "product", "expandedRows", "formatCurrency", "toLocaleString", "style", "currency", "onGlobalFilter", "table", "event", "filterGlobal", "target", "clear", "filter", "nativeElement", "getTeamName", "teamId", "team", "find", "t", "id", "getTrainerName", "trainerId", "selectors", "viewQuery", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\ListTrain\\tabledemo.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\ListTrain\\tabledemo.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';\nimport { Table } from 'primeng/table';\nimport { MessageService, ConfirmationService } from 'primeng/api';\nimport { Customer, Representative } from 'src/app/demo/api/customer';\nimport { Product } from 'src/app/demo/api/product';\nimport { CustomerService } from 'src/app/demo/service/customer.service';\nimport { ProductService } from 'src/app/demo/service/product.service';\nimport { FormationService } from 'src/app/services/formation.service';\nimport { TeamService } from 'src/app/services/team.service';\nimport { UserService } from 'src/app/demo/service/user.service';\nimport { Formation } from 'src/app/models/formation.model';\nimport { Team } from 'src/app/models/team.model';\nimport { User } from 'src/app/models/user.model';\nimport { lastValueFrom } from 'rxjs';\n\ninterface expandedRows {\n    [key: string]: boolean;\n}\n\n@Component({\n    templateUrl: './tabledemo.component.html',\n    providers: [MessageService, ConfirmationService],\n    styles: [`\n        :host ::ng-deep  .p-frozen-column {\n            font-weight: bold;\n        }\n\n        :host ::ng-deep .p-datatable-frozen-tbody {\n            font-weight: bold;\n        }\n\n        :host ::ng-deep .p-progressbar {\n            height:.5rem;\n        }\n    `]\n})\nexport class TableDemoComponent implements OnInit {\n    formations: Formation[] = [];\n    teams: Team[] = [];\n    trainers: User[] = [];\n\n    customers1: Customer[] = [];\n\n    customers2: Customer[] = [];\n\n    customers3: Customer[] = [];\n\n    selectedCustomers1: Customer[] = [];\n\n    selectedCustomer: Customer = {};\n\n    representatives: Representative[] = [];\n\n    statuses: any[] = [];\n\n    products: Product[] = [];\n\n    rowGroupMetadata: any;\n\n    expandedRows: expandedRows = {};\n\n    activityValues: number[] = [0, 100];\n\n    isExpanded: boolean = false;\n\n    idFrozen: boolean = false;\n\n    loading: boolean = true;\n\n    @ViewChild('filter') filter!: ElementRef;\n\n    constructor(\n        private customerService: CustomerService,\n        private productService: ProductService,\n        private formationService: FormationService,\n        private teamService: TeamService,\n        private userService: UserService\n    ) { }\n\n    async ngOnInit() {\n        try {\n            // Load formations\n            this.formations = await this.formationService.getFormations();\n            console.log('Loaded formations:', this.formations);\n\n            // Load teams for display\n            this.teams = await lastValueFrom(this.teamService.getTeams());\n\n            // Load trainers for display\n            const trainersData = await this.userService.getUsersByRole('formateur');\n            this.trainers = trainersData.map(trainer => ({\n                ...trainer,\n                name: `${trainer.first_name} ${trainer.last_name}${trainer.specialite ? ' (' + trainer.specialite + ')' : ''}`\n            }));\n\n            this.loading = false;\n        } catch (error) {\n            console.error('Error loading formations:', error);\n            this.loading = false;\n        }\n\n        // Keep existing demo data loading for other tables\n        this.customerService.getCustomersLarge().then(customers => {\n            this.customers1 = customers;\n            // @ts-ignore\n            this.customers1.forEach(customer => customer.date = new Date(customer.date));\n        });\n        this.customerService.getCustomersMedium().then(customers => this.customers2 = customers);\n        this.customerService.getCustomersLarge().then(customers => this.customers3 = customers);\n        this.productService.getProductsWithOrdersSmall().then(data => this.products = data);\n\n        this.representatives = [\n            { name: 'Amy Elsner', image: 'amyelsner.png' },\n            { name: 'Anna Fali', image: 'annafali.png' },\n            { name: 'Asiya Javayant', image: 'asiyajavayant.png' },\n            { name: 'Bernardo Dominic', image: 'bernardodominic.png' },\n            { name: 'Elwin Sharvill', image: 'elwinsharvill.png' },\n            { name: 'Ioni Bowcher', image: 'ionibowcher.png' },\n            { name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png' },\n            { name: 'Onyama Limba', image: 'onyamalimba.png' },\n            { name: 'Stephen Shaw', image: 'stephenshaw.png' },\n            { name: 'XuXue Feng', image: 'xuxuefeng.png' }\n        ];\n\n        this.statuses = [\n            { label: 'Unqualified', value: 'unqualified' },\n            { label: 'Qualified', value: 'qualified' },\n            { label: 'New', value: 'new' },\n            { label: 'Negotiation', value: 'negotiation' },\n            { label: 'Renewal', value: 'renewal' },\n            { label: 'Proposal', value: 'proposal' }\n        ];\n    }\n\n    onSort() {\n        this.updateRowGroupMetaData();\n    }\n\n    updateRowGroupMetaData() {\n        this.rowGroupMetadata = {};\n\n        if (this.customers3) {\n            for (let i = 0; i < this.customers3.length; i++) {\n                const rowData = this.customers3[i];\n                const representativeName = rowData?.representative?.name || '';\n\n                if (i === 0) {\n                    this.rowGroupMetadata[representativeName] = { index: 0, size: 1 };\n                }\n                else {\n                    const previousRowData = this.customers3[i - 1];\n                    const previousRowGroup = previousRowData?.representative?.name;\n                    if (representativeName === previousRowGroup) {\n                        this.rowGroupMetadata[representativeName].size++;\n                    }\n                    else {\n                        this.rowGroupMetadata[representativeName] = { index: i, size: 1 };\n                    }\n                }\n            }\n        }\n    }\n\n    expandAll() {\n        if (!this.isExpanded) {\n            this.products.forEach(product => product && product.name ? this.expandedRows[product.name] = true : '');\n\n        } else {\n            this.expandedRows = {};\n        }\n        this.isExpanded = !this.isExpanded;\n    }\n\n    formatCurrency(value: number) {\n        return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });\n    }\n\n    onGlobalFilter(table: Table, event: Event) {\n        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');\n    }\n\n    clear(table: Table) {\n        table.clear();\n        this.filter.nativeElement.value = '';\n    }\n\n    // Helper methods for displaying team and trainer names\n    getTeamName(teamId: number): string {\n        const team = this.teams.find(t => t.id === teamId);\n        return team ? team.name : 'Unknown Team';\n    }\n\n    getTrainerName(trainerId: number): string {\n        const trainer = this.trainers.find(t => t.id === trainerId);\n        return trainer ? (trainer.name || 'Unknown Trainer') : 'Unknown Trainer';\n    }\n}\n", "<div class=\"grid\">\n  <div class=\"col-12\">\n\t<div class=\"card\">\n\t  <h5>Training List</h5>\n\t  <p-table #dt1 [value]=\"formations\" dataKey=\"id\" [rows]=\"10\" [loading]=\"loading\" [rowHover]=\"true\" styleClass=\"p-datatable-gridlines\" [paginator]=\"true\" [globalFilterFields]=\"['name','trainer','status','room','team']\" responsiveLayout=\"scroll\">\n\t\t<ng-template pTemplate=\"caption\">\n\t\t  <div class=\"flex justify-content-between flex-column sm:flex-row\">\n\t\t\t<button pButton label=\"Clear\" class=\"p-button-outlined mb-2\" icon=\"pi pi-filter-slash\" (click)=\"clear(dt1)\"></button>\n\t\t\t<span class=\"p-input-icon-left mb-2\">\n\t\t\t  <i class=\"pi pi-search\"></i>\n\t\t\t  <input pInputText type=\"text\" #filter (input)=\"onGlobalFilter(dt1, $event)\" placeholder=\"Search Keyword\" class=\"w-full\"/>\n\t\t\t</span>\n\t\t  </div>\n\t\t</ng-template>\n\t\t<ng-template pTemplate=\"header\">\n\t\t  <tr>\n\t\t\t<th>Name <p-columnFilter type=\"text\" field=\"name\" display=\"menu\" placeholder=\"Search by name\"></p-columnFilter></th>\n\t\t\t<th>Trainer <p-columnFilter type=\"text\" field=\"trainer\" display=\"menu\" placeholder=\"Search by trainer\"></p-columnFilter></th>\n\t\t\t<th>Date <p-columnFilter type=\"date\" field=\"date\" display=\"menu\" placeholder=\"mm/dd/yyyy\"></p-columnFilter></th>\n\t\t\t<th>Duration <p-columnFilter type=\"text\" field=\"duration\" display=\"menu\" placeholder=\"Search by duration\"></p-columnFilter></th>\n\t\t\t<th>Team <p-columnFilter type=\"text\" field=\"team\" display=\"menu\" placeholder=\"Search by team\"></p-columnFilter></th>\n\t\t\t<th>Room <p-columnFilter type=\"text\" field=\"room\" display=\"menu\" placeholder=\"Search by room\"></p-columnFilter></th>\n\t\t\t<th>Status <p-columnFilter field=\"status\" matchMode=\"equals\" display=\"menu\">\n\t\t\t  <ng-template pTemplate=\"filter\" let-value let-filter=\"filterCallback\">\n\t\t\t\t<p-dropdown [ngModel]=\"value\" [options]=\"statuses\" (onChange)=\"filter($event.value)\" placeholder=\"Any\" [style]=\"{'min-width': '12rem'}\" >\n\t\t\t\t  <ng-template let-option pTemplate=\"item\">\n\t\t\t\t\t<span [class]=\"'formation-badge status-' + option.value\">{{option.label}}</span>\n\t\t\t\t  </ng-template>\n\t\t\t\t</p-dropdown>\n\t\t\t  </ng-template>\n\t\t\t</p-columnFilter></th>\n\t\t\t<th>Description</th>\n\t\t  </tr>\n\t\t</ng-template>\n\t\t<ng-template pTemplate=\"body\" let-formation>\n\t\t  <tr>\n\t\t\t<td>{{formation.name}}</td>\n\t\t\t<td>{{getTrainerName(formation.formateur_id)}}</td>\n\t\t\t<td>{{formation.date | date: 'MM/dd/yyyy'}}</td>\n\t\t\t<td>{{formation.duree}}h</td>\n\t\t\t<td>{{getTeamName(formation.equipe_id)}}</td>\n\t\t\t<td>-</td>\n\t\t\t<td><span class=\"formation-badge status-active\">Active</span></td>\n\t\t\t<td>{{formation.description}}</td>\n\t\t  </tr>\n\t\t</ng-template>\n\t\t<ng-template pTemplate=\"emptymessage\">\n\t\t  <tr>\n\t\t\t<td colspan=\"8\">No formations found.</td>\n\t\t  </tr>\n\t\t</ng-template>\n\t\t<ng-template pTemplate=\"loadingbody\">\n\t\t  <tr>\n\t\t\t<td colspan=\"8\">Loading formations data. Please wait.</td>\n\t\t  </tr>\n\t\t</ng-template>\n\t  </p-table>\n\t<!-- End of Training List Card -->\n  </div>\n<!-- Removed extra closing div -->\n</div>\n\n\t<!-- All obsolete formation tables/views removed. Only main Training List remains. -->\n</div>\n"]}, "metadata": {}, "sourceType": "module"}