{"ast": null, "code": "import { scheduled } from '../scheduled/scheduled';\nimport { innerFrom } from './innerFrom';\nexport function from(input, scheduler) {\n  return scheduler ? scheduled(input, scheduler) : innerFrom(input);\n}", "map": {"version": 3, "names": ["scheduled", "innerFrom", "from", "input", "scheduler"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/from.js"], "sourcesContent": ["import { scheduled } from '../scheduled/scheduled';\nimport { innerFrom } from './innerFrom';\nexport function from(input, scheduler) {\n    return scheduler ? scheduled(input, scheduler) : innerFrom(input);\n}\n"], "mappings": "AAAA,SAASA,SAAT,QAA0B,wBAA1B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,OAAO,SAASC,IAAT,CAAcC,KAAd,EAAqBC,SAArB,EAAgC;EACnC,OAAOA,SAAS,GAAGJ,SAAS,CAACG,KAAD,EAAQC,SAAR,CAAZ,GAAiCH,SAAS,CAACE,KAAD,CAA1D;AACH"}, "metadata": {}, "sourceType": "module"}