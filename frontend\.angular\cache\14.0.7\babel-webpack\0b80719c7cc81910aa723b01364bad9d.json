{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function mergeScan(accumulator, seed, concurrent = Infinity) {\n  return operate((source, subscriber) => {\n    let state = seed;\n    return mergeInternals(source, subscriber, (value, index) => accumulator(state, value, index), concurrent, value => {\n      state = value;\n    }, false, undefined, () => state = null);\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "mergeScan", "accumulator", "seed", "concurrent", "Infinity", "source", "subscriber", "state", "value", "index", "undefined"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/mergeScan.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function mergeScan(accumulator, seed, concurrent = Infinity) {\n    return operate((source, subscriber) => {\n        let state = seed;\n        return mergeInternals(source, subscriber, (value, index) => accumulator(state, value, index), concurrent, (value) => {\n            state = value;\n        }, false, undefined, () => (state = null));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,SAASC,SAAT,CAAmBC,WAAnB,EAAgCC,IAAhC,EAAsCC,UAAU,GAAGC,QAAnD,EAA6D;EAChE,OAAON,OAAO,CAAC,CAACO,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAGL,IAAZ;IACA,OAAOH,cAAc,CAACM,MAAD,EAASC,UAAT,EAAqB,CAACE,KAAD,EAAQC,KAAR,KAAkBR,WAAW,CAACM,KAAD,EAAQC,KAAR,EAAeC,KAAf,CAAlD,EAAyEN,UAAzE,EAAsFK,KAAD,IAAW;MACjHD,KAAK,GAAGC,KAAR;IACH,CAFoB,EAElB,KAFkB,EAEXE,SAFW,EAEA,MAAOH,KAAK,GAAG,IAFf,CAArB;EAGH,CALa,CAAd;AAMH"}, "metadata": {}, "sourceType": "module"}