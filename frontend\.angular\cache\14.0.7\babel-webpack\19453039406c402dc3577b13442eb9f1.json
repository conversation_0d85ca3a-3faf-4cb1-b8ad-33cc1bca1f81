{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\n\nfunction AccordionTab_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.header, \" \");\n  }\n}\n\nfunction AccordionTab_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AccordionTab_ng_content_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\n\nfunction AccordionTab_ng_container_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction AccordionTab_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_10_ng_container_1_Template, 1, 0, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.contentTemplate);\n  }\n}\n\nconst _c0 = [\"*\", [[\"p-header\"]]];\n\nconst _c1 = function (a0) {\n  return {\n    \"p-accordion-tab-active\": a0\n  };\n};\n\nconst _c2 = function (a0, a1) {\n  return {\n    \"p-highlight\": a0,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c3 = function (a0) {\n  return {\n    transitionParams: a0\n  };\n};\n\nconst _c4 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nconst _c5 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\n\nconst _c6 = [\"*\", \"p-header\"];\nconst _c7 = [\"*\"];\nlet idx = 0;\n\nclass AccordionTab {\n  constructor(accordion, changeDetector) {\n    this.changeDetector = changeDetector;\n    this.cache = true;\n    this.selectedChange = new EventEmitter();\n    this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    this.id = `p-accordiontab-${idx++}`;\n    this.accordion = accordion;\n  }\n\n  get selected() {\n    return this._selected;\n  }\n\n  set selected(val) {\n    this._selected = val;\n\n    if (!this.loaded) {\n      if (this._selected && this.cache) {\n        this.loaded = true;\n      }\n\n      this.changeDetector.detectChanges();\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  toggle(event) {\n    if (this.disabled) {\n      return false;\n    }\n\n    let index = this.findTabIndex();\n\n    if (this.selected) {\n      this.selected = false;\n      this.accordion.onClose.emit({\n        originalEvent: event,\n        index: index\n      });\n    } else {\n      if (!this.accordion.multiple) {\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n          if (this.accordion.tabs[i].selected) {\n            this.accordion.tabs[i].selected = false;\n            this.accordion.tabs[i].selectedChange.emit(false);\n            this.accordion.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n\n      this.selected = true;\n      this.loaded = true;\n      this.accordion.onOpen.emit({\n        originalEvent: event,\n        index: index\n      });\n    }\n\n    this.selectedChange.emit(this.selected);\n    this.accordion.updateActiveIndex();\n    this.changeDetector.markForCheck();\n    event.preventDefault();\n  }\n\n  findTabIndex() {\n    let index = -1;\n\n    for (var i = 0; i < this.accordion.tabs.length; i++) {\n      if (this.accordion.tabs[i] == this) {\n        index = i;\n        break;\n      }\n    }\n\n    return index;\n  }\n\n  get hasHeaderFacet() {\n    return this.headerFacet && this.headerFacet.length > 0;\n  }\n\n  onKeydown(event) {\n    if (event.which === 32 || event.which === 13) {\n      this.toggle(event);\n      event.preventDefault();\n    }\n  }\n\n  ngOnDestroy() {\n    this.accordion.tabs.splice(this.findTabIndex(), 1);\n  }\n\n}\n\nAccordionTab.ɵfac = function AccordionTab_Factory(t) {\n  return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nAccordionTab.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: AccordionTab,\n  selectors: [[\"p-accordionTab\"]],\n  contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, Header, 4);\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    header: \"header\",\n    disabled: \"disabled\",\n    cache: \"cache\",\n    transitionOptions: \"transitionOptions\",\n    selected: \"selected\"\n  },\n  outputs: {\n    selectedChange: \"selectedChange\"\n  },\n  ngContentSelectors: _c6,\n  decls: 11,\n  vars: 28,\n  consts: [[1, \"p-accordion-tab\", 3, \"ngClass\"], [1, \"p-accordion-header\", 3, \"ngClass\"], [\"role\", \"tab\", 1, \"p-accordion-header-link\", 3, \"click\", \"keydown\"], [1, \"p-accordion-toggle-icon\", 3, \"ngClass\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [4, \"ngIf\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\"], [1, \"p-accordion-header-text\"]],\n  template: function AccordionTab_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c0);\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n      i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n        return ctx.toggle($event);\n      })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n        return ctx.onKeydown($event);\n      });\n      i0.ɵɵelement(3, \"span\", 3);\n      i0.ɵɵtemplate(4, AccordionTab_span_4_Template, 2, 1, \"span\", 4);\n      i0.ɵɵtemplate(5, AccordionTab_ng_container_5_Template, 1, 0, \"ng-container\", 5);\n      i0.ɵɵtemplate(6, AccordionTab_ng_content_6_Template, 1, 0, \"ng-content\", 6);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8);\n      i0.ɵɵprojection(9);\n      i0.ɵɵtemplate(10, AccordionTab_ng_container_10_Template, 2, 1, \"ng-container\", 6);\n      i0.ɵɵelementEnd()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c1, ctx.selected));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c2, ctx.selected, ctx.disabled));\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.id)(\"aria-controls\", ctx.id + \"-content\")(\"aria-expanded\", ctx.selected);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngClass\", ctx.selected ? ctx.accordion.collapseIcon : ctx.accordion.expandIcon);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(22, _c4, i0.ɵɵpureFunction1(20, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(26, _c5, i0.ɵɵpureFunction1(24, _c3, ctx.transitionOptions)));\n      i0.ɵɵattribute(\"id\", ctx.id + \"-content\")(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.id);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet],\n  styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion-tab-active .p-toggleable-content:not(.ng-animating){overflow:visible}.p-accordion .p-toggleable-content{overflow:hidden}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('tabContent', [state('hidden', style({\n      height: '0'\n    })), state('visible', style({\n      height: '*'\n    })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionTab, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordionTab',\n      template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('tabContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion-tab-active .p-toggleable-content:not(.ng-animating){overflow:visible}.p-accordion .p-toggleable-content{overflow:hidden}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [forwardRef(() => Accordion)]\n      }]\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    header: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    cache: [{\n      type: Input\n    }],\n    selectedChange: [{\n      type: Output\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    headerFacet: [{\n      type: ContentChildren,\n      args: [Header]\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    selected: [{\n      type: Input\n    }]\n  });\n})();\n\nclass Accordion {\n  constructor(el, changeDetector) {\n    this.el = el;\n    this.changeDetector = changeDetector;\n    this.onClose = new EventEmitter();\n    this.onOpen = new EventEmitter();\n    this.expandIcon = 'pi pi-fw pi-chevron-right';\n    this.collapseIcon = 'pi pi-fw pi-chevron-down';\n    this.activeIndexChange = new EventEmitter();\n    this.tabs = [];\n  }\n\n  ngAfterContentInit() {\n    this.initTabs();\n    this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n      this.initTabs();\n    });\n  }\n\n  initTabs() {\n    this.tabs = this.tabList.toArray();\n    this.updateSelectionState();\n    this.changeDetector.markForCheck();\n  }\n\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(val) {\n    this._activeIndex = val;\n\n    if (this.preventActiveIndexPropagation) {\n      this.preventActiveIndexPropagation = false;\n      return;\n    }\n\n    this.updateSelectionState();\n  }\n\n  updateSelectionState() {\n    if (this.tabs && this.tabs.length && this._activeIndex != null) {\n      for (let i = 0; i < this.tabs.length; i++) {\n        let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n        let changed = selected !== this.tabs[i].selected;\n\n        if (changed) {\n          this.tabs[i].selected = selected;\n          this.tabs[i].selectedChange.emit(selected);\n          this.tabs[i].changeDetector.markForCheck();\n        }\n      }\n    }\n  }\n\n  updateActiveIndex() {\n    let index = this.multiple ? [] : null;\n    this.tabs.forEach((tab, i) => {\n      if (tab.selected) {\n        if (this.multiple) {\n          index.push(i);\n        } else {\n          index = i;\n          return;\n        }\n      }\n    });\n    this.preventActiveIndexPropagation = true;\n    this.activeIndexChange.emit(index);\n  }\n\n  ngOnDestroy() {\n    if (this.tabListSubscription) {\n      this.tabListSubscription.unsubscribe();\n    }\n  }\n\n}\n\nAccordion.ɵfac = function Accordion_Factory(t) {\n  return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nAccordion.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Accordion,\n  selectors: [[\"p-accordion\"]],\n  contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, AccordionTab, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    multiple: \"multiple\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    expandIcon: \"expandIcon\",\n    collapseIcon: \"collapseIcon\",\n    activeIndex: \"activeIndex\"\n  },\n  outputs: {\n    onClose: \"onClose\",\n    onOpen: \"onOpen\",\n    activeIndexChange: \"activeIndexChange\"\n  },\n  ngContentSelectors: _c7,\n  decls: 2,\n  vars: 4,\n  consts: [[\"role\", \"tablist\", 3, \"ngClass\", \"ngStyle\"]],\n  template: function Accordion_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgStyle],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Accordion, [{\n    type: Component,\n    args: [{\n      selector: 'p-accordion',\n      template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    multiple: [{\n      type: Input\n    }],\n    onClose: [{\n      type: Output\n    }],\n    onOpen: [{\n      type: Output\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    expandIcon: [{\n      type: Input\n    }],\n    collapseIcon: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    tabList: [{\n      type: ContentChildren,\n      args: [AccordionTab]\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\n\nclass AccordionModule {}\n\nAccordionModule.ɵfac = function AccordionModule_Factory(t) {\n  return new (t || AccordionModule)();\n};\n\nAccordionModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AccordionModule\n});\nAccordionModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Accordion, AccordionTab, SharedModule],\n      declarations: [Accordion, AccordionTab]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Accordion, AccordionModule, AccordionTab };", "map": {"version": 3, "names": ["i0", "EventEmitter", "forwardRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "NgModule", "trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "Header", "PrimeTemplate", "SharedModule", "idx", "AccordionTab", "constructor", "accordion", "changeDetector", "cache", "<PERSON><PERSON><PERSON><PERSON>", "transitionOptions", "id", "selected", "_selected", "val", "loaded", "detectChanges", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "contentTemplate", "template", "headerTemplate", "toggle", "event", "disabled", "index", "findTabIndex", "onClose", "emit", "originalEvent", "multiple", "i", "tabs", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOpen", "updateActiveIndex", "preventDefault", "hasHeaderFacet", "headerFacet", "onKeydown", "which", "ngOnDestroy", "splice", "ɵfac", "Accordion", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "height", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "undefined", "decorators", "header", "el", "expandIcon", "collapseIcon", "activeIndexChange", "initTabs", "tabListSubscription", "tabList", "changes", "subscribe", "_", "toArray", "updateSelectionState", "getBlockableElement", "nativeElement", "children", "activeIndex", "_activeIndex", "preventActiveIndexPropagation", "includes", "changed", "tab", "push", "unsubscribe", "ElementRef", "NgStyle", "styleClass", "AccordionModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-accordion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\n\nlet idx = 0;\nclass AccordionTab {\n    constructor(accordion, changeDetector) {\n        this.changeDetector = changeDetector;\n        this.cache = true;\n        this.selectedChange = new EventEmitter();\n        this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n        this.id = `p-accordiontab-${idx++}`;\n        this.accordion = accordion;\n    }\n    get selected() {\n        return this._selected;\n    }\n    set selected(val) {\n        this._selected = val;\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n            this.changeDetector.detectChanges();\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.disabled) {\n            return false;\n        }\n        let index = this.findTabIndex();\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        }\n        else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n        event.preventDefault();\n    }\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n    get hasHeaderFacet() {\n        return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n        if (event.which === 32 || event.which === 13) {\n            this.toggle(event);\n            event.preventDefault();\n        }\n    }\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n}\nAccordionTab.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionTab, deps: [{ token: forwardRef(() => Accordion) }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nAccordionTab.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: AccordionTab, selector: \"p-accordionTab\", inputs: { header: \"header\", disabled: \"disabled\", cache: \"cache\", transitionOptions: \"transitionOptions\", selected: \"selected\" }, outputs: { selectedChange: \"selectedChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", predicate: Header }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion-tab-active .p-toggleable-content:not(.ng-animating){overflow:visible}.p-accordion .p-toggleable-content{overflow:hidden}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], animations: [\n        trigger('tabContent', [\n            state('hidden', style({\n                height: '0'\n            })),\n            state('visible', style({\n                height: '*'\n            })),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-accordionTab', template: `\n        <div class=\"p-accordion-tab\" [ngClass]=\"{'p-accordion-tab-active': selected}\">\n            <div class=\"p-accordion-header\" [ngClass]=\"{'p-highlight': selected, 'p-disabled': disabled}\">\n                <a role=\"tab\" class=\"p-accordion-header-link\" (click)=\"toggle($event)\" (keydown)=\"onKeydown($event)\" [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"id\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"selected\">\n                    <span class=\"p-accordion-toggle-icon\" [ngClass]=\"selected ? accordion.collapseIcon : accordion.expandIcon\"></span>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{header}}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@tabContent]=\"selected ? {value: 'visible', params: {transitionParams: transitionOptions}} : {value: 'hidden', params: {transitionParams: transitionOptions}}\"\n                role=\"region\" [attr.aria-hidden]=\"!selected\" [attr.aria-labelledby]=\"id\">\n                <div class=\"p-accordion-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('tabContent', [\n                            state('hidden', style({\n                                height: '0'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion-tab-active .p-toggleable-content:not(.ng-animating){overflow:visible}.p-accordion .p-toggleable-content{overflow:hidden}\\n\"] }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [forwardRef(() => Accordion)]\n                    }] }, { type: i0.ChangeDetectorRef }];\n    }, propDecorators: { header: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], cache: [{\n                type: Input\n            }], selectedChange: [{\n                type: Output\n            }], transitionOptions: [{\n                type: Input\n            }], headerFacet: [{\n                type: ContentChildren,\n                args: [Header]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], selected: [{\n                type: Input\n            }] } });\nclass Accordion {\n    constructor(el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n        this.onClose = new EventEmitter();\n        this.onOpen = new EventEmitter();\n        this.expandIcon = 'pi pi-fw pi-chevron-right';\n        this.collapseIcon = 'pi pi-fw pi-chevron-down';\n        this.activeIndexChange = new EventEmitter();\n        this.tabs = [];\n    }\n    ngAfterContentInit() {\n        this.initTabs();\n        this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n            this.initTabs();\n        });\n    }\n    initTabs() {\n        this.tabs = this.tabList.toArray();\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(val) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n        this.updateSelectionState();\n    }\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : (i === this._activeIndex);\n                let changed = selected !== this.tabs[i].selected;\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n    updateActiveIndex() {\n        let index = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    index.push(i);\n                }\n                else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n}\nAccordion.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Accordion, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nAccordion.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Accordion, selector: \"p-accordion\", inputs: { multiple: \"multiple\", style: \"style\", styleClass: \"styleClass\", expandIcon: \"expandIcon\", collapseIcon: \"collapseIcon\", activeIndex: \"activeIndex\" }, outputs: { onClose: \"onClose\", onOpen: \"onOpen\", activeIndexChange: \"activeIndexChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"tabList\", predicate: AccordionTab }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Accordion, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordion',\n                    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" role=\"tablist\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { multiple: [{\n                type: Input\n            }], onClose: [{\n                type: Output\n            }], onOpen: [{\n                type: Output\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], expandIcon: [{\n                type: Input\n            }], collapseIcon: [{\n                type: Input\n            }], activeIndexChange: [{\n                type: Output\n            }], tabList: [{\n                type: ContentChildren,\n                args: [AccordionTab]\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass AccordionModule {\n}\nAccordionModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAccordionModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionModule, declarations: [Accordion, AccordionTab], imports: [CommonModule], exports: [Accordion, AccordionTab, SharedModule] });\nAccordionModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionModule, imports: [CommonModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Accordion, AccordionTab, SharedModule],\n                    declarations: [Accordion, AccordionTab]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,UAAvB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,MAA1F,EAAkGC,KAAlG,EAAyGC,MAAzG,EAAiHC,eAAjH,EAAkIC,QAAlI,QAAkJ,eAAlJ;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,MAAT,EAAiBC,aAAjB,EAAgCC,YAAhC,QAAoD,aAApD;;;;IA0F+FpB,EAO3E,6B;IAP2EA,EAQvE,U;IARuEA,EAS3E,e;;;;mBAT2EA,E;IAAAA,EAQvE,a;IARuEA,EAQvE,4C;;;;;;IARuEA,EAU3E,sB;;;;;;IAV2EA,EAW3E,gD;;;;;;IAX2EA,EAmBvE,sB;;;;;;IAnBuEA,EAkB3E,2B;IAlB2EA,EAmBvE,6F;IAnBuEA,EAoB3E,wB;;;;mBApB2EA,E;IAAAA,EAmBxD,a;IAnBwDA,EAmBxD,uD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3GvC,IAAIqB,GAAG,GAAG,CAAV;;AACA,MAAMC,YAAN,CAAmB;EACfC,WAAW,CAACC,SAAD,EAAYC,cAAZ,EAA4B;IACnC,KAAKA,cAAL,GAAsBA,cAAtB;IACA,KAAKC,KAAL,GAAa,IAAb;IACA,KAAKC,cAAL,GAAsB,IAAI1B,YAAJ,EAAtB;IACA,KAAK2B,iBAAL,GAAyB,sCAAzB;IACA,KAAKC,EAAL,GAAW,kBAAiBR,GAAG,EAAG,EAAlC;IACA,KAAKG,SAAL,GAAiBA,SAAjB;EACH;;EACW,IAARM,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACE,GAAD,EAAM;IACd,KAAKD,SAAL,GAAiBC,GAAjB;;IACA,IAAI,CAAC,KAAKC,MAAV,EAAkB;MACd,IAAI,KAAKF,SAAL,IAAkB,KAAKL,KAA3B,EAAkC;QAC9B,KAAKO,MAAL,GAAc,IAAd;MACH;;MACD,KAAKR,cAAL,CAAoBS,aAApB;IACH;EACJ;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;;QACJ,KAAK,QAAL;UACI,KAAKC,cAAL,GAAsBJ,IAAI,CAACG,QAA3B;UACA;;QACJ;UACI,KAAKD,eAAL,GAAuBF,IAAI,CAACG,QAA5B;UACA;MATR;IAWH,CAZD;EAaH;;EACDE,MAAM,CAACC,KAAD,EAAQ;IACV,IAAI,KAAKC,QAAT,EAAmB;MACf,OAAO,KAAP;IACH;;IACD,IAAIC,KAAK,GAAG,KAAKC,YAAL,EAAZ;;IACA,IAAI,KAAKjB,QAAT,EAAmB;MACf,KAAKA,QAAL,GAAgB,KAAhB;MACA,KAAKN,SAAL,CAAewB,OAAf,CAAuBC,IAAvB,CAA4B;QAAEC,aAAa,EAAEN,KAAjB;QAAwBE,KAAK,EAAEA;MAA/B,CAA5B;IACH,CAHD,MAIK;MACD,IAAI,CAAC,KAAKtB,SAAL,CAAe2B,QAApB,EAA8B;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBC,MAAxC,EAAgDF,CAAC,EAAjD,EAAqD;UACjD,IAAI,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBtB,QAA3B,EAAqC;YACjC,KAAKN,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBtB,QAAvB,GAAkC,KAAlC;YACA,KAAKN,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuBzB,cAAvB,CAAsCsB,IAAtC,CAA2C,KAA3C;YACA,KAAKzB,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,EAAuB3B,cAAvB,CAAsC8B,YAAtC;UACH;QACJ;MACJ;;MACD,KAAKzB,QAAL,GAAgB,IAAhB;MACA,KAAKG,MAAL,GAAc,IAAd;MACA,KAAKT,SAAL,CAAegC,MAAf,CAAsBP,IAAtB,CAA2B;QAAEC,aAAa,EAAEN,KAAjB;QAAwBE,KAAK,EAAEA;MAA/B,CAA3B;IACH;;IACD,KAAKnB,cAAL,CAAoBsB,IAApB,CAAyB,KAAKnB,QAA9B;IACA,KAAKN,SAAL,CAAeiC,iBAAf;IACA,KAAKhC,cAAL,CAAoB8B,YAApB;IACAX,KAAK,CAACc,cAAN;EACH;;EACDX,YAAY,GAAG;IACX,IAAID,KAAK,GAAG,CAAC,CAAb;;IACA,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBC,MAAxC,EAAgDF,CAAC,EAAjD,EAAqD;MACjD,IAAI,KAAK5B,SAAL,CAAe6B,IAAf,CAAoBD,CAApB,KAA0B,IAA9B,EAAoC;QAChCN,KAAK,GAAGM,CAAR;QACA;MACH;IACJ;;IACD,OAAON,KAAP;EACH;;EACiB,IAAda,cAAc,GAAG;IACjB,OAAO,KAAKC,WAAL,IAAoB,KAAKA,WAAL,CAAiBN,MAAjB,GAA0B,CAArD;EACH;;EACDO,SAAS,CAACjB,KAAD,EAAQ;IACb,IAAIA,KAAK,CAACkB,KAAN,KAAgB,EAAhB,IAAsBlB,KAAK,CAACkB,KAAN,KAAgB,EAA1C,EAA8C;MAC1C,KAAKnB,MAAL,CAAYC,KAAZ;MACAA,KAAK,CAACc,cAAN;IACH;EACJ;;EACDK,WAAW,GAAG;IACV,KAAKvC,SAAL,CAAe6B,IAAf,CAAoBW,MAApB,CAA2B,KAAKjB,YAAL,EAA3B,EAAgD,CAAhD;EACH;;AArFc;;AAuFnBzB,YAAY,CAAC2C,IAAb;EAAA,iBAAyG3C,YAAzG,EAA+FtB,EAA/F,mBAAuIE,UAAU,CAAC,MAAMgE,SAAP,CAAjJ,GAA+FlE,EAA/F,mBAA+KA,EAAE,CAACmE,iBAAlL;AAAA;;AACA7C,YAAY,CAAC8C,IAAb,kBAD+FpE,EAC/F;EAAA,MAA6FsB,YAA7F;EAAA;EAAA;IAAA;MAD+FtB,EAC/F,0BAAmZkB,MAAnZ;MAD+FlB,EAC/F,0BAAqcmB,aAArc;IAAA;;IAAA;MAAA;;MAD+FnB,EAC/F,qBAD+FA,EAC/F;MAD+FA,EAC/F,qBAD+FA,EAC/F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FA,EAC/F;MAD+FA,EAEvF,oDADR;MAD+FA,EAIjC;QAAA,OAAS,kBAAT;MAAA;QAAA,OAAoC,qBAApC;MAAA,EAH9D;MAD+FA,EAM3E,wBALpB;MAD+FA,EAO3E,6DANpB;MAD+FA,EAU3E,6EATpB;MAD+FA,EAW3E,yEAVpB;MAD+FA,EAY/E,iBAXhB;MAD+FA,EAcnF,yCAbZ;MAD+FA,EAiB3E,gBAhBpB;MAD+FA,EAkB3E,+EAjBpB;MAD+FA,EAqB/E,mBApBhB;IAAA;;IAAA;MAD+FA,EAE1D,uBAF0DA,EAE1D,wCADrC;MAD+FA,EAGnD,aAF5C;MAD+FA,EAGnD,uBAHmDA,EAGnD,sDAF5C;MAD+FA,EAIsB,aAHrH;MAD+FA,EAIsB,oIAHrH;MAD+FA,EAMrC,aAL1D;MAD+FA,EAMrC,4FAL1D;MAD+FA,EAOpC,aAN3D;MAD+FA,EAOpC,wCAN3D;MAD+FA,EAU5D,aATnC;MAD+FA,EAU5D,mDATnC;MAD+FA,EAW5C,aAVnD;MAD+FA,EAW5C,uCAVnD;MAD+FA,EAcrB,aAb1E;MAD+FA,EAcrB,0CAdqBA,EAcrB,0BAdqBA,EAcrB,oDAdqBA,EAcrB,0BAdqBA,EAcrB,kDAb1E;MAD+FA,EAc9E,gGAbjB;MAD+FA,EAkB5D,aAjBnC;MAD+FA,EAkB5D,mFAjBnC;IAAA;EAAA;EAAA,eAuB+bgB,EAAE,CAACqD,OAvBlc,EAuB6hBrD,EAAE,CAACsD,IAvBhiB,EAuBioBtD,EAAE,CAACuD,gBAvBpoB;EAAA;EAAA;EAAA;IAAA,WAuB0xB,CAClxB5D,OAAO,CAAC,YAAD,EAAe,CAClBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;MAClB2D,MAAM,EAAE;IADU,CAAD,CAAhB,CADa,EAIlB5D,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;MACnB2D,MAAM,EAAE;IADW,CAAD,CAAjB,CAJa,EAOlB1D,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPQ,EAQlBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARQ,CAAf,CAD2wB;EAvB1xB;EAAA;AAAA;;AAmCA;EAAA,mDApC+Ff,EAoC/F,mBAA2FsB,YAA3F,EAAqH,CAAC;IAC1GmD,IAAI,EAAEtE,SADoG;IAE1GuE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAZ;MAA8BlC,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAvBmB;MAuBZmC,UAAU,EAAE,CACKjE,OAAO,CAAC,YAAD,EAAe,CAClBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;QAClB2D,MAAM,EAAE;MADU,CAAD,CAAhB,CADa,EAIlB5D,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;QACnB2D,MAAM,EAAE;MADW,CAAD,CAAjB,CAJa,EAOlB1D,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPQ,EAQlBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARQ,CAAf,CADZ,CAvBA;MAkCI8D,eAAe,EAAEzE,uBAAuB,CAAC0E,MAlC7C;MAkCqDC,aAAa,EAAE1E,iBAAiB,CAAC2E,IAlCtF;MAkC4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CAlClG;MAoCIC,MAAM,EAAE,CAAC,kXAAD;IApCZ,CAAD;EAFoG,CAAD,CAArH,EAuC4B,YAAY;IAChC,OAAO,CAAC;MAAET,IAAI,EAAEU,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBX,IAAI,EAAEnE,MADkB;QAExBoE,IAAI,EAAE,CAACxE,UAAU,CAAC,MAAMgE,SAAP,CAAX;MAFkB,CAAD;IAA/B,CAAD,EAGW;MAAEO,IAAI,EAAEzE,EAAE,CAACmE;IAAX,CAHX,CAAP;EAIH,CA5CL,EA4CuB;IAAEkB,MAAM,EAAE,CAAC;MAClBZ,IAAI,EAAElE;IADY,CAAD,CAAV;IAEPsC,QAAQ,EAAE,CAAC;MACX4B,IAAI,EAAElE;IADK,CAAD,CAFH;IAIPmB,KAAK,EAAE,CAAC;MACR+C,IAAI,EAAElE;IADE,CAAD,CAJA;IAMPoB,cAAc,EAAE,CAAC;MACjB8C,IAAI,EAAEjE;IADW,CAAD,CANT;IAQPoB,iBAAiB,EAAE,CAAC;MACpB6C,IAAI,EAAElE;IADc,CAAD,CARZ;IAUPqD,WAAW,EAAE,CAAC;MACda,IAAI,EAAEhE,eADQ;MAEdiE,IAAI,EAAE,CAACxD,MAAD;IAFQ,CAAD,CAVN;IAaPkB,SAAS,EAAE,CAAC;MACZqC,IAAI,EAAEhE,eADM;MAEZiE,IAAI,EAAE,CAACvD,aAAD;IAFM,CAAD,CAbJ;IAgBPW,QAAQ,EAAE,CAAC;MACX2C,IAAI,EAAElE;IADK,CAAD;EAhBH,CA5CvB;AAAA;;AA+DA,MAAM2D,SAAN,CAAgB;EACZ3C,WAAW,CAAC+D,EAAD,EAAK7D,cAAL,EAAqB;IAC5B,KAAK6D,EAAL,GAAUA,EAAV;IACA,KAAK7D,cAAL,GAAsBA,cAAtB;IACA,KAAKuB,OAAL,GAAe,IAAI/C,YAAJ,EAAf;IACA,KAAKuD,MAAL,GAAc,IAAIvD,YAAJ,EAAd;IACA,KAAKsF,UAAL,GAAkB,2BAAlB;IACA,KAAKC,YAAL,GAAoB,0BAApB;IACA,KAAKC,iBAAL,GAAyB,IAAIxF,YAAJ,EAAzB;IACA,KAAKoD,IAAL,GAAY,EAAZ;EACH;;EACDlB,kBAAkB,GAAG;IACjB,KAAKuD,QAAL;IACA,KAAKC,mBAAL,GAA2B,KAAKC,OAAL,CAAaC,OAAb,CAAqBC,SAArB,CAA+BC,CAAC,IAAI;MAC3D,KAAKL,QAAL;IACH,CAF0B,CAA3B;EAGH;;EACDA,QAAQ,GAAG;IACP,KAAKrC,IAAL,GAAY,KAAKuC,OAAL,CAAaI,OAAb,EAAZ;IACA,KAAKC,oBAAL;IACA,KAAKxE,cAAL,CAAoB8B,YAApB;EACH;;EACD2C,mBAAmB,GAAG;IAClB,OAAO,KAAKZ,EAAL,CAAQa,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,CAAP;EACH;;EACc,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKC,YAAZ;EACH;;EACc,IAAXD,WAAW,CAACrE,GAAD,EAAM;IACjB,KAAKsE,YAAL,GAAoBtE,GAApB;;IACA,IAAI,KAAKuE,6BAAT,EAAwC;MACpC,KAAKA,6BAAL,GAAqC,KAArC;MACA;IACH;;IACD,KAAKN,oBAAL;EACH;;EACDA,oBAAoB,GAAG;IACnB,IAAI,KAAK5C,IAAL,IAAa,KAAKA,IAAL,CAAUC,MAAvB,IAAiC,KAAKgD,YAAL,IAAqB,IAA1D,EAAgE;MAC5D,KAAK,IAAIlD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,IAAL,CAAUC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;QACvC,IAAItB,QAAQ,GAAG,KAAKqB,QAAL,GAAgB,KAAKmD,YAAL,CAAkBE,QAAlB,CAA2BpD,CAA3B,CAAhB,GAAiDA,CAAC,KAAK,KAAKkD,YAA3E;QACA,IAAIG,OAAO,GAAG3E,QAAQ,KAAK,KAAKuB,IAAL,CAAUD,CAAV,EAAatB,QAAxC;;QACA,IAAI2E,OAAJ,EAAa;UACT,KAAKpD,IAAL,CAAUD,CAAV,EAAatB,QAAb,GAAwBA,QAAxB;UACA,KAAKuB,IAAL,CAAUD,CAAV,EAAazB,cAAb,CAA4BsB,IAA5B,CAAiCnB,QAAjC;UACA,KAAKuB,IAAL,CAAUD,CAAV,EAAa3B,cAAb,CAA4B8B,YAA5B;QACH;MACJ;IACJ;EACJ;;EACDE,iBAAiB,GAAG;IAChB,IAAIX,KAAK,GAAG,KAAKK,QAAL,GAAgB,EAAhB,GAAqB,IAAjC;IACA,KAAKE,IAAL,CAAUhB,OAAV,CAAkB,CAACqE,GAAD,EAAMtD,CAAN,KAAY;MAC1B,IAAIsD,GAAG,CAAC5E,QAAR,EAAkB;QACd,IAAI,KAAKqB,QAAT,EAAmB;UACfL,KAAK,CAAC6D,IAAN,CAAWvD,CAAX;QACH,CAFD,MAGK;UACDN,KAAK,GAAGM,CAAR;UACA;QACH;MACJ;IACJ,CAVD;IAWA,KAAKmD,6BAAL,GAAqC,IAArC;IACA,KAAKd,iBAAL,CAAuBxC,IAAvB,CAA4BH,KAA5B;EACH;;EACDiB,WAAW,GAAG;IACV,IAAI,KAAK4B,mBAAT,EAA8B;MAC1B,KAAKA,mBAAL,CAAyBiB,WAAzB;IACH;EACJ;;AArEW;;AAuEhB1C,SAAS,CAACD,IAAV;EAAA,iBAAsGC,SAAtG,EA1K+FlE,EA0K/F,mBAAiIA,EAAE,CAAC6G,UAApI,GA1K+F7G,EA0K/F,mBAA2JA,EAAE,CAACmE,iBAA9J;AAAA;;AACAD,SAAS,CAACE,IAAV,kBA3K+FpE,EA2K/F;EAAA,MAA0FkE,SAA1F;EAAA;EAAA;IAAA;MA3K+FlE,EA2K/F,0BAAgdsB,YAAhd;IAAA;;IAAA;MAAA;;MA3K+FtB,EA2K/F,qBA3K+FA,EA2K/F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA3K+FA,EA2K/F;MA3K+FA,EA4KvF,4BADR;MA3K+FA,EA6KnF,gBAFZ;MA3K+FA,EA8KvF,eAHR;IAAA;;IAAA;MA3K+FA,EA4K1B,2BADrE;MA3K+FA,EA4KlF,uEADb;IAAA;EAAA;EAAA,eAIiEgB,EAAE,CAACqD,OAJpE,EAI+JrD,EAAE,CAAC8F,OAJlK;EAAA;EAAA;AAAA;;AAKA;EAAA,mDAhL+F9G,EAgL/F,mBAA2FkE,SAA3F,EAAkH,CAAC;IACvGO,IAAI,EAAEtE,SADiG;IAEvGuE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,aADX;MAEClC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KANmB;MAOCoC,eAAe,EAAEzE,uBAAuB,CAAC0E,MAP1C;MAQCG,IAAI,EAAE;QACF,SAAS;MADP;IARP,CAAD;EAFiG,CAAD,CAAlH,EAc4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAEzE,EAAE,CAAC6G;IAAX,CAAD,EAA0B;MAAEpC,IAAI,EAAEzE,EAAE,CAACmE;IAAX,CAA1B,CAAP;EAAmE,CAd7G,EAc+H;IAAEhB,QAAQ,EAAE,CAAC;MAC5HsB,IAAI,EAAElE;IADsH,CAAD,CAAZ;IAE/GyC,OAAO,EAAE,CAAC;MACVyB,IAAI,EAAEjE;IADI,CAAD,CAFsG;IAI/GgD,MAAM,EAAE,CAAC;MACTiB,IAAI,EAAEjE;IADG,CAAD,CAJuG;IAM/GK,KAAK,EAAE,CAAC;MACR4D,IAAI,EAAElE;IADE,CAAD,CANwG;IAQ/GwG,UAAU,EAAE,CAAC;MACbtC,IAAI,EAAElE;IADO,CAAD,CARmG;IAU/GgF,UAAU,EAAE,CAAC;MACbd,IAAI,EAAElE;IADO,CAAD,CAVmG;IAY/GiF,YAAY,EAAE,CAAC;MACff,IAAI,EAAElE;IADS,CAAD,CAZiG;IAc/GkF,iBAAiB,EAAE,CAAC;MACpBhB,IAAI,EAAEjE;IADc,CAAD,CAd4F;IAgB/GoF,OAAO,EAAE,CAAC;MACVnB,IAAI,EAAEhE,eADI;MAEViE,IAAI,EAAE,CAACpD,YAAD;IAFI,CAAD,CAhBsG;IAmB/G+E,WAAW,EAAE,CAAC;MACd5B,IAAI,EAAElE;IADQ,CAAD;EAnBkG,CAd/H;AAAA;;AAoCA,MAAMyG,eAAN,CAAsB;;AAEtBA,eAAe,CAAC/C,IAAhB;EAAA,iBAA4G+C,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAvN+FjH,EAuN/F;EAAA,MAA6GgH;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBAxN+FlH,EAwN/F;EAAA,UAAwIiB,YAAxI,EAAsJG,YAAtJ;AAAA;;AACA;EAAA,mDAzN+FpB,EAyN/F,mBAA2FgH,eAA3F,EAAwH,CAAC;IAC7GvC,IAAI,EAAE/D,QADuG;IAE7GgE,IAAI,EAAE,CAAC;MACCyC,OAAO,EAAE,CAAClG,YAAD,CADV;MAECmG,OAAO,EAAE,CAAClD,SAAD,EAAY5C,YAAZ,EAA0BF,YAA1B,CAFV;MAGCiG,YAAY,EAAE,CAACnD,SAAD,EAAY5C,YAAZ;IAHf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;;;AAEA,SAAS4C,SAAT,EAAoB8C,eAApB,EAAqC1F,YAArC"}, "metadata": {}, "sourceType": "module"}