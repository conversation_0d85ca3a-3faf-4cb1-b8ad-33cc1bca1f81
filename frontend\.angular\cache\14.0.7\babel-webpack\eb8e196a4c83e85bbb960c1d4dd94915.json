{"ast": null, "code": "import { AsyncScheduler } from './AsyncScheduler';\nexport class QueueScheduler extends AsyncScheduler {}", "map": {"version": 3, "names": ["AsyncScheduler", "QueueScheduler"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/QueueScheduler.js"], "sourcesContent": ["import { AsyncScheduler } from './AsyncScheduler';\nexport class QueueScheduler extends AsyncScheduler {\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,cAAN,SAA6BD,cAA7B,CAA4C"}, "metadata": {}, "sourceType": "module"}