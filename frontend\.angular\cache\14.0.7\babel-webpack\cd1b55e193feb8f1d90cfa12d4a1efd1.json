{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  let bufferSize;\n  let refCount = false;\n\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    ({\n      bufferSize = Infinity,\n      windowTime = Infinity,\n      refCount = false,\n      scheduler\n    } = configOrBufferSize);\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n\n  return share({\n    connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n}", "map": {"version": 3, "names": ["ReplaySubject", "share", "shareReplay", "configOrBufferSize", "windowTime", "scheduler", "bufferSize", "refCount", "Infinity", "connector", "resetOnError", "resetOnComplete", "resetOnRefCountZero"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/shareReplay.js"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n    let bufferSize;\n    let refCount = false;\n    if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n        ({ bufferSize = Infinity, windowTime = Infinity, refCount = false, scheduler } = configOrBufferSize);\n    }\n    else {\n        bufferSize = (configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity);\n    }\n    return share({\n        connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n        resetOnError: true,\n        resetOnComplete: false,\n        resetOnRefCountZero: refCount,\n    });\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,kBAA9B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,OAAO,SAASC,WAAT,CAAqBC,kBAArB,EAAyCC,UAAzC,EAAqDC,SAArD,EAAgE;EACnE,IAAIC,UAAJ;EACA,IAAIC,QAAQ,GAAG,KAAf;;EACA,IAAIJ,kBAAkB,IAAI,OAAOA,kBAAP,KAA8B,QAAxD,EAAkE;IAC9D,CAAC;MAAEG,UAAU,GAAGE,QAAf;MAAyBJ,UAAU,GAAGI,QAAtC;MAAgDD,QAAQ,GAAG,KAA3D;MAAkEF;IAAlE,IAAgFF,kBAAjF;EACH,CAFD,MAGK;IACDG,UAAU,GAAIH,kBAAkB,KAAK,IAAvB,IAA+BA,kBAAkB,KAAK,KAAK,CAA3D,GAA+DA,kBAA/D,GAAoFK,QAAlG;EACH;;EACD,OAAOP,KAAK,CAAC;IACTQ,SAAS,EAAE,MAAM,IAAIT,aAAJ,CAAkBM,UAAlB,EAA8BF,UAA9B,EAA0CC,SAA1C,CADR;IAETK,YAAY,EAAE,IAFL;IAGTC,eAAe,EAAE,KAHR;IAITC,mBAAmB,EAAEL;EAJZ,CAAD,CAAZ;AAMH"}, "metadata": {}, "sourceType": "module"}