{"ast": null, "code": "'use strict';\n/**\n * @license Angular v14.2.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n(function (global) {\n  const performance = global['performance'];\n\n  function mark(name) {\n    performance && performance['mark'] && performance['mark'](name);\n  }\n\n  function performanceMeasure(name, label) {\n    performance && performance['measure'] && performance['measure'](name, label);\n  }\n\n  mark('Zone'); // Initialize before it's accessed below.\n  // __Zone_symbol_prefix global can be used to override the default zone\n  // symbol prefix with a custom one if needed.\n\n  const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n\n  function __symbol__(name) {\n    return symbolPrefix + name;\n  }\n\n  const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n\n  if (global['Zone']) {\n    // if global['Zone'] already exists (maybe zone.js was already loaded or\n    // some other lib also registered a global object named Zone), we may need\n    // to throw an error, but sometimes user may not want this error.\n    // For example,\n    // we have two web pages, page1 includes zone.js, page2 doesn't.\n    // and the 1st time user load page1 and page2, everything work fine,\n    // but when user load page2 again, error occurs because global['Zone'] already exists.\n    // so we add a flag to let user choose whether to throw this error or not.\n    // By default, if existing Zone is from zone.js, we will not throw the error.\n    if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n      throw new Error('Zone already loaded.');\n    } else {\n      return global['Zone'];\n    }\n  }\n\n  class Zone {\n    constructor(parent, zoneSpec) {\n      this._parent = parent;\n      this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n      this._properties = zoneSpec && zoneSpec.properties || {};\n      this._zoneDelegate = new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n    }\n\n    static assertZonePatched() {\n      if (global['Promise'] !== patches['ZoneAwarePromise']) {\n        throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' + 'has been overwritten.\\n' + 'Most likely cause is that a Promise polyfill has been loaded ' + 'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' + 'If you must load one, do so before loading zone.js.)');\n      }\n    }\n\n    static get root() {\n      let zone = Zone.current;\n\n      while (zone.parent) {\n        zone = zone.parent;\n      }\n\n      return zone;\n    }\n\n    static get current() {\n      return _currentZoneFrame.zone;\n    }\n\n    static get currentTask() {\n      return _currentTask;\n    } // tslint:disable-next-line:require-internal-with-underscore\n\n\n    static __load_patch(name, fn, ignoreDuplicate = false) {\n      if (patches.hasOwnProperty(name)) {\n        // `checkDuplicate` option is defined from global variable\n        // so it works for all modules.\n        // `ignoreDuplicate` can work for the specified module\n        if (!ignoreDuplicate && checkDuplicate) {\n          throw Error('Already loaded patch: ' + name);\n        }\n      } else if (!global['__Zone_disable_' + name]) {\n        const perfName = 'Zone:' + name;\n        mark(perfName);\n        patches[name] = fn(global, Zone, _api);\n        performanceMeasure(perfName, perfName);\n      }\n    }\n\n    get parent() {\n      return this._parent;\n    }\n\n    get name() {\n      return this._name;\n    }\n\n    get(key) {\n      const zone = this.getZoneWith(key);\n      if (zone) return zone._properties[key];\n    }\n\n    getZoneWith(key) {\n      let current = this;\n\n      while (current) {\n        if (current._properties.hasOwnProperty(key)) {\n          return current;\n        }\n\n        current = current._parent;\n      }\n\n      return null;\n    }\n\n    fork(zoneSpec) {\n      if (!zoneSpec) throw new Error('ZoneSpec required!');\n      return this._zoneDelegate.fork(this, zoneSpec);\n    }\n\n    wrap(callback, source) {\n      if (typeof callback !== 'function') {\n        throw new Error('Expecting function got: ' + callback);\n      }\n\n      const _callback = this._zoneDelegate.intercept(this, callback, source);\n\n      const zone = this;\n      return function () {\n        return zone.runGuarded(_callback, this, arguments, source);\n      };\n    }\n\n    run(callback, applyThis, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n\n    runGuarded(callback, applyThis = null, applyArgs, source) {\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        try {\n          return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        _currentZoneFrame = _currentZoneFrame.parent;\n      }\n    }\n\n    runTask(task, applyThis, applyArgs) {\n      if (task.zone != this) {\n        throw new Error('A task can only be run in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n      } // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n      // will run in notScheduled(canceled) state, we should not try to\n      // run such kind of task but just return\n\n\n      if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n        return;\n      }\n\n      const reEntryGuard = task.state != running;\n      reEntryGuard && task._transitionTo(running, scheduled);\n      task.runCount++;\n      const previousTask = _currentTask;\n      _currentTask = task;\n      _currentZoneFrame = {\n        parent: _currentZoneFrame,\n        zone: this\n      };\n\n      try {\n        if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n          task.cancelFn = undefined;\n        }\n\n        try {\n          return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n        } catch (error) {\n          if (this._zoneDelegate.handleError(this, error)) {\n            throw error;\n          }\n        }\n      } finally {\n        // if the task's state is notScheduled or unknown, then it has already been cancelled\n        // we should not reset the state to scheduled\n        if (task.state !== notScheduled && task.state !== unknown) {\n          if (task.type == eventTask || task.data && task.data.isPeriodic) {\n            reEntryGuard && task._transitionTo(scheduled, running);\n          } else {\n            task.runCount = 0;\n\n            this._updateTaskCount(task, -1);\n\n            reEntryGuard && task._transitionTo(notScheduled, running, notScheduled);\n          }\n        }\n\n        _currentZoneFrame = _currentZoneFrame.parent;\n        _currentTask = previousTask;\n      }\n    }\n\n    scheduleTask(task) {\n      if (task.zone && task.zone !== this) {\n        // check if the task was rescheduled, the newZone\n        // should not be the children of the original zone\n        let newZone = this;\n\n        while (newZone) {\n          if (newZone === task.zone) {\n            throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n          }\n\n          newZone = newZone.parent;\n        }\n      }\n\n      task._transitionTo(scheduling, notScheduled);\n\n      const zoneDelegates = [];\n      task._zoneDelegates = zoneDelegates;\n      task._zone = this;\n\n      try {\n        task = this._zoneDelegate.scheduleTask(this, task);\n      } catch (err) {\n        // should set task's state to unknown when scheduleTask throw error\n        // because the err may from reschedule, so the fromState maybe notScheduled\n        task._transitionTo(unknown, scheduling, notScheduled); // TODO: @JiaLiPassion, should we check the result from handleError?\n\n\n        this._zoneDelegate.handleError(this, err);\n\n        throw err;\n      }\n\n      if (task._zoneDelegates === zoneDelegates) {\n        // we have to check because internally the delegate can reschedule the task.\n        this._updateTaskCount(task, 1);\n      }\n\n      if (task.state == scheduling) {\n        task._transitionTo(scheduled, scheduling);\n      }\n\n      return task;\n    }\n\n    scheduleMicroTask(source, callback, data, customSchedule) {\n      return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n    }\n\n    scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n    }\n\n    scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n      return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n    }\n\n    cancelTask(task) {\n      if (task.zone != this) throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' + (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n\n      task._transitionTo(canceling, scheduled, running);\n\n      try {\n        this._zoneDelegate.cancelTask(this, task);\n      } catch (err) {\n        // if error occurs when cancelTask, transit the state to unknown\n        task._transitionTo(unknown, canceling);\n\n        this._zoneDelegate.handleError(this, err);\n\n        throw err;\n      }\n\n      this._updateTaskCount(task, -1);\n\n      task._transitionTo(notScheduled, canceling);\n\n      task.runCount = 0;\n      return task;\n    }\n\n    _updateTaskCount(task, count) {\n      const zoneDelegates = task._zoneDelegates;\n\n      if (count == -1) {\n        task._zoneDelegates = null;\n      }\n\n      for (let i = 0; i < zoneDelegates.length; i++) {\n        zoneDelegates[i]._updateTaskCount(task.type, count);\n      }\n    }\n\n  } // tslint:disable-next-line:require-internal-with-underscore\n\n\n  Zone.__symbol__ = __symbol__;\n  const DELEGATE_ZS = {\n    name: '',\n    onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n    onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n    onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n    onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n  };\n\n  class _ZoneDelegate {\n    constructor(zone, parentDelegate, zoneSpec) {\n      this._taskCounts = {\n        'microTask': 0,\n        'macroTask': 0,\n        'eventTask': 0\n      };\n      this.zone = zone;\n      this._parentDelegate = parentDelegate;\n      this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n      this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n      this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n      this._interceptZS = zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n      this._interceptDlgt = zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n      this._interceptCurrZone = zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n      this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n      this._invokeDlgt = zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n      this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n      this._handleErrorZS = zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n      this._handleErrorDlgt = zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n      this._handleErrorCurrZone = zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n      this._scheduleTaskZS = zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n      this._scheduleTaskDlgt = zoneSpec && (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n      this._scheduleTaskCurrZone = zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n      this._invokeTaskZS = zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n      this._invokeTaskDlgt = zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n      this._invokeTaskCurrZone = zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n      this._cancelTaskZS = zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n      this._cancelTaskDlgt = zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n      this._cancelTaskCurrZone = zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n      this._hasTaskZS = null;\n      this._hasTaskDlgt = null;\n      this._hasTaskDlgtOwner = null;\n      this._hasTaskCurrZone = null;\n      const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n      const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n\n      if (zoneSpecHasTask || parentHasTask) {\n        // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n        // a case all task related interceptors must go through this ZD. We can't short circuit it.\n        this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n        this._hasTaskDlgt = parentDelegate;\n        this._hasTaskDlgtOwner = this;\n        this._hasTaskCurrZone = zone;\n\n        if (!zoneSpec.onScheduleTask) {\n          this._scheduleTaskZS = DELEGATE_ZS;\n          this._scheduleTaskDlgt = parentDelegate;\n          this._scheduleTaskCurrZone = this.zone;\n        }\n\n        if (!zoneSpec.onInvokeTask) {\n          this._invokeTaskZS = DELEGATE_ZS;\n          this._invokeTaskDlgt = parentDelegate;\n          this._invokeTaskCurrZone = this.zone;\n        }\n\n        if (!zoneSpec.onCancelTask) {\n          this._cancelTaskZS = DELEGATE_ZS;\n          this._cancelTaskDlgt = parentDelegate;\n          this._cancelTaskCurrZone = this.zone;\n        }\n      }\n    }\n\n    fork(targetZone, zoneSpec) {\n      return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) : new Zone(targetZone, zoneSpec);\n    }\n\n    intercept(targetZone, callback, source) {\n      return this._interceptZS ? this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) : callback;\n    }\n\n    invoke(targetZone, callback, applyThis, applyArgs, source) {\n      return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) : callback.apply(applyThis, applyArgs);\n    }\n\n    handleError(targetZone, error) {\n      return this._handleErrorZS ? this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) : true;\n    }\n\n    scheduleTask(targetZone, task) {\n      let returnTask = task;\n\n      if (this._scheduleTaskZS) {\n        if (this._hasTaskZS) {\n          returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n        } // clang-format off\n\n\n        returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task); // clang-format on\n\n        if (!returnTask) returnTask = task;\n      } else {\n        if (task.scheduleFn) {\n          task.scheduleFn(task);\n        } else if (task.type == microTask) {\n          scheduleMicroTask(task);\n        } else {\n          throw new Error('Task is missing scheduleFn.');\n        }\n      }\n\n      return returnTask;\n    }\n\n    invokeTask(targetZone, task, applyThis, applyArgs) {\n      return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) : task.callback.apply(applyThis, applyArgs);\n    }\n\n    cancelTask(targetZone, task) {\n      let value;\n\n      if (this._cancelTaskZS) {\n        value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n      } else {\n        if (!task.cancelFn) {\n          throw Error('Task is not cancelable');\n        }\n\n        value = task.cancelFn(task);\n      }\n\n      return value;\n    }\n\n    hasTask(targetZone, isEmpty) {\n      // hasTask should not throw error so other ZoneDelegate\n      // can still trigger hasTask callback\n      try {\n        this._hasTaskZS && this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n      } catch (err) {\n        this.handleError(targetZone, err);\n      }\n    } // tslint:disable-next-line:require-internal-with-underscore\n\n\n    _updateTaskCount(type, count) {\n      const counts = this._taskCounts;\n      const prev = counts[type];\n      const next = counts[type] = prev + count;\n\n      if (next < 0) {\n        throw new Error('More tasks executed then were scheduled.');\n      }\n\n      if (prev == 0 || next == 0) {\n        const isEmpty = {\n          microTask: counts['microTask'] > 0,\n          macroTask: counts['macroTask'] > 0,\n          eventTask: counts['eventTask'] > 0,\n          change: type\n        };\n        this.hasTask(this.zone, isEmpty);\n      }\n    }\n\n  }\n\n  class ZoneTask {\n    constructor(type, source, callback, options, scheduleFn, cancelFn) {\n      // tslint:disable-next-line:require-internal-with-underscore\n      this._zone = null;\n      this.runCount = 0; // tslint:disable-next-line:require-internal-with-underscore\n\n      this._zoneDelegates = null; // tslint:disable-next-line:require-internal-with-underscore\n\n      this._state = 'notScheduled';\n      this.type = type;\n      this.source = source;\n      this.data = options;\n      this.scheduleFn = scheduleFn;\n      this.cancelFn = cancelFn;\n\n      if (!callback) {\n        throw new Error('callback is not defined');\n      }\n\n      this.callback = callback;\n      const self = this; // TODO: @JiaLiPassion options should have interface\n\n      if (type === eventTask && options && options.useG) {\n        this.invoke = ZoneTask.invokeTask;\n      } else {\n        this.invoke = function () {\n          return ZoneTask.invokeTask.call(global, self, this, arguments);\n        };\n      }\n    }\n\n    static invokeTask(task, target, args) {\n      if (!task) {\n        task = this;\n      }\n\n      _numberOfNestedTaskFrames++;\n\n      try {\n        task.runCount++;\n        return task.zone.runTask(task, target, args);\n      } finally {\n        if (_numberOfNestedTaskFrames == 1) {\n          drainMicroTaskQueue();\n        }\n\n        _numberOfNestedTaskFrames--;\n      }\n    }\n\n    get zone() {\n      return this._zone;\n    }\n\n    get state() {\n      return this._state;\n    }\n\n    cancelScheduleRequest() {\n      this._transitionTo(notScheduled, scheduling);\n    } // tslint:disable-next-line:require-internal-with-underscore\n\n\n    _transitionTo(toState, fromState1, fromState2) {\n      if (this._state === fromState1 || this._state === fromState2) {\n        this._state = toState;\n\n        if (toState == notScheduled) {\n          this._zoneDelegates = null;\n        }\n      } else {\n        throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n      }\n    }\n\n    toString() {\n      if (this.data && typeof this.data.handleId !== 'undefined') {\n        return this.data.handleId.toString();\n      } else {\n        return Object.prototype.toString.call(this);\n      }\n    } // add toJSON method to prevent cyclic error when\n    // call JSON.stringify(zoneTask)\n\n\n    toJSON() {\n      return {\n        type: this.type,\n        state: this.state,\n        source: this.source,\n        zone: this.zone.name,\n        runCount: this.runCount\n      };\n    }\n\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  MICROTASK QUEUE\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const symbolSetTimeout = __symbol__('setTimeout');\n\n  const symbolPromise = __symbol__('Promise');\n\n  const symbolThen = __symbol__('then');\n\n  let _microTaskQueue = [];\n  let _isDrainingMicrotaskQueue = false;\n  let nativeMicroTaskQueuePromise;\n\n  function nativeScheduleMicroTask(func) {\n    if (!nativeMicroTaskQueuePromise) {\n      if (global[symbolPromise]) {\n        nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n      }\n    }\n\n    if (nativeMicroTaskQueuePromise) {\n      let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n\n      if (!nativeThen) {\n        // native Promise is not patchable, we need to use `then` directly\n        // issue 1078\n        nativeThen = nativeMicroTaskQueuePromise['then'];\n      }\n\n      nativeThen.call(nativeMicroTaskQueuePromise, func);\n    } else {\n      global[symbolSetTimeout](func, 0);\n    }\n  }\n\n  function scheduleMicroTask(task) {\n    // if we are not running in any task, and there has not been anything scheduled\n    // we must bootstrap the initial task creation by manually scheduling the drain\n    if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n      // We are not running in Task, so we need to kickstart the microtask queue.\n      nativeScheduleMicroTask(drainMicroTaskQueue);\n    }\n\n    task && _microTaskQueue.push(task);\n  }\n\n  function drainMicroTaskQueue() {\n    if (!_isDrainingMicrotaskQueue) {\n      _isDrainingMicrotaskQueue = true;\n\n      while (_microTaskQueue.length) {\n        const queue = _microTaskQueue;\n        _microTaskQueue = [];\n\n        for (let i = 0; i < queue.length; i++) {\n          const task = queue[i];\n\n          try {\n            task.zone.runTask(task, null, null);\n          } catch (error) {\n            _api.onUnhandledError(error);\n          }\n        }\n      }\n\n      _api.microtaskDrainDone();\n\n      _isDrainingMicrotaskQueue = false;\n    }\n  } //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n  ///  BOOTSTRAP\n  //////////////////////////////////////////////////////\n  //////////////////////////////////////////////////////\n\n\n  const NO_ZONE = {\n    name: 'NO ZONE'\n  };\n  const notScheduled = 'notScheduled',\n        scheduling = 'scheduling',\n        scheduled = 'scheduled',\n        running = 'running',\n        canceling = 'canceling',\n        unknown = 'unknown';\n  const microTask = 'microTask',\n        macroTask = 'macroTask',\n        eventTask = 'eventTask';\n  const patches = {};\n  const _api = {\n    symbol: __symbol__,\n    currentZoneFrame: () => _currentZoneFrame,\n    onUnhandledError: noop,\n    microtaskDrainDone: noop,\n    scheduleMicroTask: scheduleMicroTask,\n    showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n    patchEventTarget: () => [],\n    patchOnProperties: noop,\n    patchMethod: () => noop,\n    bindArguments: () => [],\n    patchThen: () => noop,\n    patchMacroTask: () => noop,\n    patchEventPrototype: () => noop,\n    isIEOrEdge: () => false,\n    getGlobalObjects: () => undefined,\n    ObjectDefineProperty: () => noop,\n    ObjectGetOwnPropertyDescriptor: () => undefined,\n    ObjectCreate: () => undefined,\n    ArraySlice: () => [],\n    patchClass: () => noop,\n    wrapWithCurrentZone: () => noop,\n    filterProperties: () => [],\n    attachOriginToPatched: () => noop,\n    _redefineProperty: () => noop,\n    patchCallbacks: () => noop,\n    nativeScheduleMicroTask: nativeScheduleMicroTask\n  };\n  let _currentZoneFrame = {\n    parent: null,\n    zone: new Zone(null, null)\n  };\n  let _currentTask = null;\n  let _numberOfNestedTaskFrames = 0;\n\n  function noop() {}\n\n  performanceMeasure('Zone', 'Zone');\n  return global['Zone'] = Zone;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n\n/** Object.getOwnPropertyDescriptor */\n\n\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\n\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\n\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\n\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\n\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\n\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\n\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\n\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\n\n\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\n\n\nconst TRUE_STR = 'true';\n/** false string const */\n\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\n\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\n\nfunction wrapWithCurrentZone(callback, source) {\n  return Zone.current.wrap(callback, source);\n}\n\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n  return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\n\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\n\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\n\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\n\nfunction bindArguments(args, source) {\n  for (let i = args.length - 1; i >= 0; i--) {\n    if (typeof args[i] === 'function') {\n      args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n    }\n  }\n\n  return args;\n}\n\nfunction patchPrototype(prototype, fnNames) {\n  const source = prototype.constructor['name'];\n\n  for (let i = 0; i < fnNames.length; i++) {\n    const name = fnNames[i];\n    const delegate = prototype[name];\n\n    if (delegate) {\n      const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n\n      if (!isPropertyWritable(prototypeDesc)) {\n        continue;\n      }\n\n      prototype[name] = (delegate => {\n        const patched = function () {\n          return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n        };\n\n        attachOriginToPatched(patched, delegate);\n        return patched;\n      })(delegate);\n    }\n  }\n}\n\nfunction isPropertyWritable(propertyDesc) {\n  if (!propertyDesc) {\n    return true;\n  }\n\n  if (propertyDesc.writable === false) {\n    return false;\n  }\n\n  return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\n\nconst isWebWorker = typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope; // Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isNode = !('nw' in _global) && typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]';\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']); // we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\n\nconst isMix = typeof _global.process !== 'undefined' && {}.toString.call(_global.process) === '[object process]' && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\n\nconst wrapFn = function (event) {\n  // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n  // event will be undefined, so we need to use window.event\n  event = event || _global.event;\n\n  if (!event) {\n    return;\n  }\n\n  let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n  }\n\n  const target = this || event.target || _global;\n  const listener = target[eventNameSymbol];\n  let result;\n\n  if (isBrowser && target === internalWindow && event.type === 'error') {\n    // window.onerror have different signature\n    // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n    // and onerror callback will prevent default when callback return true\n    const errorEvent = event;\n    result = listener && listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n\n    if (result === true) {\n      event.preventDefault();\n    }\n  } else {\n    result = listener && listener.apply(this, arguments);\n\n    if (result != undefined && !result) {\n      event.preventDefault();\n    }\n  }\n\n  return result;\n};\n\nfunction patchProperty(obj, prop, prototype) {\n  let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n\n  if (!desc && prototype) {\n    // when patch window object, use prototype to check prop exist or not\n    const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n\n    if (prototypeDesc) {\n      desc = {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  } // if the descriptor not exists or is not configurable\n  // just return\n\n\n  if (!desc || !desc.configurable) {\n    return;\n  }\n\n  const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n\n  if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n    return;\n  } // A property descriptor cannot have getter/setter and be writable\n  // deleting the writable and value properties avoids this error:\n  //\n  // TypeError: property descriptors must not specify a value or be writable when a\n  // getter or setter has been specified\n\n\n  delete desc.writable;\n  delete desc.value;\n  const originalDescGet = desc.get;\n  const originalDescSet = desc.set; // slice(2) cuz 'onclick' -> 'click', etc\n\n  const eventName = prop.slice(2);\n  let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n\n  if (!eventNameSymbol) {\n    eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n  }\n\n  desc.set = function (newValue) {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return;\n    }\n\n    const previousValue = target[eventNameSymbol];\n\n    if (typeof previousValue === 'function') {\n      target.removeEventListener(eventName, wrapFn);\n    } // issue #978, when onload handler was added before loading zone.js\n    // we should remove it with originalDescSet\n\n\n    originalDescSet && originalDescSet.call(target, null);\n    target[eventNameSymbol] = newValue;\n\n    if (typeof newValue === 'function') {\n      target.addEventListener(eventName, wrapFn, false);\n    }\n  }; // The getter would return undefined for unassigned properties but the default value of an\n  // unassigned property is null\n\n\n  desc.get = function () {\n    // in some of windows's onproperty callback, this is undefined\n    // so we need to check it\n    let target = this;\n\n    if (!target && obj === _global) {\n      target = _global;\n    }\n\n    if (!target) {\n      return null;\n    }\n\n    const listener = target[eventNameSymbol];\n\n    if (listener) {\n      return listener;\n    } else if (originalDescGet) {\n      // result will be null when use inline event attribute,\n      // such as <button onclick=\"func();\">OK</button>\n      // because the onclick function is internal raw uncompiled handler\n      // the onclick will be evaluated when first time event was triggered or\n      // the property is accessed, https://github.com/angular/zone.js/issues/525\n      // so we should use original native get to retrieve the handler\n      let value = originalDescGet.call(this);\n\n      if (value) {\n        desc.set.call(this, value);\n\n        if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n          target.removeAttribute(prop);\n        }\n\n        return value;\n      }\n    }\n\n    return null;\n  };\n\n  ObjectDefineProperty(obj, prop, desc);\n  obj[onPropPatchedSymbol] = true;\n}\n\nfunction patchOnProperties(obj, properties, prototype) {\n  if (properties) {\n    for (let i = 0; i < properties.length; i++) {\n      patchProperty(obj, 'on' + properties[i], prototype);\n    }\n  } else {\n    const onProperties = [];\n\n    for (const prop in obj) {\n      if (prop.slice(0, 2) == 'on') {\n        onProperties.push(prop);\n      }\n    }\n\n    for (let j = 0; j < onProperties.length; j++) {\n      patchProperty(obj, onProperties[j], prototype);\n    }\n  }\n}\n\nconst originalInstanceKey = zoneSymbol('originalInstance'); // wrap some native API on `window`\n\nfunction patchClass(className) {\n  const OriginalClass = _global[className];\n  if (!OriginalClass) return; // keep original class in global\n\n  _global[zoneSymbol(className)] = OriginalClass;\n\n  _global[className] = function () {\n    const a = bindArguments(arguments, className);\n\n    switch (a.length) {\n      case 0:\n        this[originalInstanceKey] = new OriginalClass();\n        break;\n\n      case 1:\n        this[originalInstanceKey] = new OriginalClass(a[0]);\n        break;\n\n      case 2:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n        break;\n\n      case 3:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n        break;\n\n      case 4:\n        this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n        break;\n\n      default:\n        throw new Error('Arg list too long.');\n    }\n  }; // attach original delegate to patched function\n\n\n  attachOriginToPatched(_global[className], OriginalClass);\n  const instance = new OriginalClass(function () {});\n  let prop;\n\n  for (prop in instance) {\n    // https://bugs.webkit.org/show_bug.cgi?id=44721\n    if (className === 'XMLHttpRequest' && prop === 'responseBlob') continue;\n\n    (function (prop) {\n      if (typeof instance[prop] === 'function') {\n        _global[className].prototype[prop] = function () {\n          return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n        };\n      } else {\n        ObjectDefineProperty(_global[className].prototype, prop, {\n          set: function (fn) {\n            if (typeof fn === 'function') {\n              this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop); // keep callback in wrapped function so we can\n              // use it in Function.prototype.toString to return\n              // the native one.\n\n              attachOriginToPatched(this[originalInstanceKey][prop], fn);\n            } else {\n              this[originalInstanceKey][prop] = fn;\n            }\n          },\n          get: function () {\n            return this[originalInstanceKey][prop];\n          }\n        });\n      }\n    })(prop);\n  }\n\n  for (prop in OriginalClass) {\n    if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n      _global[className][prop] = OriginalClass[prop];\n    }\n  }\n}\n\nfunction patchMethod(target, name, patchFn) {\n  let proto = target;\n\n  while (proto && !proto.hasOwnProperty(name)) {\n    proto = ObjectGetPrototypeOf(proto);\n  }\n\n  if (!proto && target[name]) {\n    // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n    proto = target;\n  }\n\n  const delegateName = zoneSymbol(name);\n  let delegate = null;\n\n  if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n    delegate = proto[delegateName] = proto[name]; // check whether proto[name] is writable\n    // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n\n    const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n\n    if (isPropertyWritable(desc)) {\n      const patchDelegate = patchFn(delegate, delegateName, name);\n\n      proto[name] = function () {\n        return patchDelegate(this, arguments);\n      };\n\n      attachOriginToPatched(proto[name], delegate);\n    }\n  }\n\n  return delegate;\n} // TODO: @JiaLiPassion, support cancel task later if necessary\n\n\nfunction patchMacroTask(obj, funcName, metaCreator) {\n  let setNative = null;\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[data.cbIdx] = function () {\n      task.invoke.apply(this, arguments);\n    };\n\n    setNative.apply(data.target, data.args);\n    return task;\n  }\n\n  setNative = patchMethod(obj, funcName, delegate => function (self, args) {\n    const meta = metaCreator(self, args);\n\n    if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n      return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(self, args);\n    }\n  });\n}\n\nfunction attachOriginToPatched(patched, original) {\n  patched[zoneSymbol('OriginalDelegate')] = original;\n}\n\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\n\nfunction isIE() {\n  try {\n    const ua = internalWindow.navigator.userAgent;\n\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n      return true;\n    }\n  } catch (error) {}\n\n  return false;\n}\n\nfunction isIEOrEdge() {\n  if (isDetectedIEOrEdge) {\n    return ieOrEdge;\n  }\n\n  isDetectedIEOrEdge = true;\n\n  try {\n    const ua = internalWindow.navigator.userAgent;\n\n    if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n      ieOrEdge = true;\n    }\n  } catch (error) {}\n\n  return ieOrEdge;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n  const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  const ObjectDefineProperty = Object.defineProperty;\n\n  function readableObjectToString(obj) {\n    if (obj && obj.toString === Object.prototype.toString) {\n      const className = obj.constructor && obj.constructor.name;\n      return (className ? className : '') + ': ' + JSON.stringify(obj);\n    }\n\n    return obj ? obj.toString() : Object.prototype.toString.call(obj);\n  }\n\n  const __symbol__ = api.symbol;\n  const _uncaughtPromiseErrors = [];\n  const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n\n  const symbolPromise = __symbol__('Promise');\n\n  const symbolThen = __symbol__('then');\n\n  const creationTrace = '__creationTrace__';\n\n  api.onUnhandledError = e => {\n    if (api.showUncaughtError()) {\n      const rejection = e && e.rejection;\n\n      if (rejection) {\n        console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n      } else {\n        console.error(e);\n      }\n    }\n  };\n\n  api.microtaskDrainDone = () => {\n    while (_uncaughtPromiseErrors.length) {\n      const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n\n      try {\n        uncaughtPromiseError.zone.runGuarded(() => {\n          if (uncaughtPromiseError.throwOriginal) {\n            throw uncaughtPromiseError.rejection;\n          }\n\n          throw uncaughtPromiseError;\n        });\n      } catch (error) {\n        handleUnhandledRejection(error);\n      }\n    }\n  };\n\n  const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n\n  function handleUnhandledRejection(e) {\n    api.onUnhandledError(e);\n\n    try {\n      const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n\n      if (typeof handler === 'function') {\n        handler.call(this, e);\n      }\n    } catch (err) {}\n  }\n\n  function isThenable(value) {\n    return value && value.then;\n  }\n\n  function forwardResolution(value) {\n    return value;\n  }\n\n  function forwardRejection(rejection) {\n    return ZoneAwarePromise.reject(rejection);\n  }\n\n  const symbolState = __symbol__('state');\n\n  const symbolValue = __symbol__('value');\n\n  const symbolFinally = __symbol__('finally');\n\n  const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n\n  const symbolParentPromiseState = __symbol__('parentPromiseState');\n\n  const source = 'Promise.then';\n  const UNRESOLVED = null;\n  const RESOLVED = true;\n  const REJECTED = false;\n  const REJECTED_NO_CATCH = 0;\n\n  function makeResolver(promise, state) {\n    return v => {\n      try {\n        resolvePromise(promise, state, v);\n      } catch (err) {\n        resolvePromise(promise, false, err);\n      } // Do not return value or you will break the Promise spec.\n\n    };\n  }\n\n  const once = function () {\n    let wasCalled = false;\n    return function wrapper(wrappedFunction) {\n      return function () {\n        if (wasCalled) {\n          return;\n        }\n\n        wasCalled = true;\n        wrappedFunction.apply(null, arguments);\n      };\n    };\n  };\n\n  const TYPE_ERROR = 'Promise resolved with itself';\n\n  const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace'); // Promise Resolution\n\n\n  function resolvePromise(promise, state, value) {\n    const onceWrapper = once();\n\n    if (promise === value) {\n      throw new TypeError(TYPE_ERROR);\n    }\n\n    if (promise[symbolState] === UNRESOLVED) {\n      // should only get value.then once based on promise spec.\n      let then = null;\n\n      try {\n        if (typeof value === 'object' || typeof value === 'function') {\n          then = value && value.then;\n        }\n      } catch (err) {\n        onceWrapper(() => {\n          resolvePromise(promise, false, err);\n        })();\n        return promise;\n      } // if (value instanceof ZoneAwarePromise) {\n\n\n      if (state !== REJECTED && value instanceof ZoneAwarePromise && value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) && value[symbolState] !== UNRESOLVED) {\n        clearRejectedNoCatch(value);\n        resolvePromise(promise, value[symbolState], value[symbolValue]);\n      } else if (state !== REJECTED && typeof then === 'function') {\n        try {\n          then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n        } catch (err) {\n          onceWrapper(() => {\n            resolvePromise(promise, false, err);\n          })();\n        }\n      } else {\n        promise[symbolState] = state;\n        const queue = promise[symbolValue];\n        promise[symbolValue] = value;\n\n        if (promise[symbolFinally] === symbolFinally) {\n          // the promise is generated by Promise.prototype.finally\n          if (state === RESOLVED) {\n            // the state is resolved, should ignore the value\n            // and use parent promise value\n            promise[symbolState] = promise[symbolParentPromiseState];\n            promise[symbolValue] = promise[symbolParentPromiseValue];\n          }\n        } // record task information in value when error occurs, so we can\n        // do some additional work such as render longStackTrace\n\n\n        if (state === REJECTED && value instanceof Error) {\n          // check if longStackTraceZone is here\n          const trace = Zone.currentTask && Zone.currentTask.data && Zone.currentTask.data[creationTrace];\n\n          if (trace) {\n            // only keep the long stack trace into error when in longStackTraceZone\n            ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, {\n              configurable: true,\n              enumerable: false,\n              writable: true,\n              value: trace\n            });\n          }\n        }\n\n        for (let i = 0; i < queue.length;) {\n          scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n        }\n\n        if (queue.length == 0 && state == REJECTED) {\n          promise[symbolState] = REJECTED_NO_CATCH;\n          let uncaughtPromiseError = value;\n\n          try {\n            // Here we throws a new Error to print more readable error log\n            // and if the value is not an error, zone.js builds an `Error`\n            // Object here to attach the stack information.\n            throw new Error('Uncaught (in promise): ' + readableObjectToString(value) + (value && value.stack ? '\\n' + value.stack : ''));\n          } catch (err) {\n            uncaughtPromiseError = err;\n          }\n\n          if (isDisableWrappingUncaughtPromiseRejection) {\n            // If disable wrapping uncaught promise reject\n            // use the value instead of wrapping it.\n            uncaughtPromiseError.throwOriginal = true;\n          }\n\n          uncaughtPromiseError.rejection = value;\n          uncaughtPromiseError.promise = promise;\n          uncaughtPromiseError.zone = Zone.current;\n          uncaughtPromiseError.task = Zone.currentTask;\n\n          _uncaughtPromiseErrors.push(uncaughtPromiseError);\n\n          api.scheduleMicroTask(); // to make sure that it is running\n        }\n      }\n    } // Resolving an already resolved promise is a noop.\n\n\n    return promise;\n  }\n\n  const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n\n  function clearRejectedNoCatch(promise) {\n    if (promise[symbolState] === REJECTED_NO_CATCH) {\n      // if the promise is rejected no catch status\n      // and queue.length > 0, means there is a error handler\n      // here to handle the rejected promise, we should trigger\n      // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n      // eventHandler\n      try {\n        const handler = Zone[REJECTION_HANDLED_HANDLER];\n\n        if (handler && typeof handler === 'function') {\n          handler.call(this, {\n            rejection: promise[symbolValue],\n            promise: promise\n          });\n        }\n      } catch (err) {}\n\n      promise[symbolState] = REJECTED;\n\n      for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n        if (promise === _uncaughtPromiseErrors[i].promise) {\n          _uncaughtPromiseErrors.splice(i, 1);\n        }\n      }\n    }\n  }\n\n  function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n    clearRejectedNoCatch(promise);\n    const promiseState = promise[symbolState];\n    const delegate = promiseState ? typeof onFulfilled === 'function' ? onFulfilled : forwardResolution : typeof onRejected === 'function' ? onRejected : forwardRejection;\n    zone.scheduleMicroTask(source, () => {\n      try {\n        const parentPromiseValue = promise[symbolValue];\n        const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n\n        if (isFinallyPromise) {\n          // if the promise is generated from finally call, keep parent promise's state and value\n          chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n          chainPromise[symbolParentPromiseState] = promiseState;\n        } // should not pass value to finally callback\n\n\n        const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ? [] : [parentPromiseValue]);\n        resolvePromise(chainPromise, true, value);\n      } catch (error) {\n        // if error occurs, should always return this error\n        resolvePromise(chainPromise, false, error);\n      }\n    }, chainPromise);\n  }\n\n  const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n\n  const noop = function () {};\n\n  const AggregateError = global.AggregateError;\n\n  class ZoneAwarePromise {\n    static toString() {\n      return ZONE_AWARE_PROMISE_TO_STRING;\n    }\n\n    static resolve(value) {\n      return resolvePromise(new this(null), RESOLVED, value);\n    }\n\n    static reject(error) {\n      return resolvePromise(new this(null), REJECTED, error);\n    }\n\n    static any(values) {\n      if (!values || typeof values[Symbol.iterator] !== 'function') {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n\n      const promises = [];\n      let count = 0;\n\n      try {\n        for (let v of values) {\n          count++;\n          promises.push(ZoneAwarePromise.resolve(v));\n        }\n      } catch (err) {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n\n      if (count === 0) {\n        return Promise.reject(new AggregateError([], 'All promises were rejected'));\n      }\n\n      let finished = false;\n      const errors = [];\n      return new ZoneAwarePromise((resolve, reject) => {\n        for (let i = 0; i < promises.length; i++) {\n          promises[i].then(v => {\n            if (finished) {\n              return;\n            }\n\n            finished = true;\n            resolve(v);\n          }, err => {\n            errors.push(err);\n            count--;\n\n            if (count === 0) {\n              finished = true;\n              reject(new AggregateError(errors, 'All promises were rejected'));\n            }\n          });\n        }\n      });\n    }\n\n    static race(values) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n\n      function onResolve(value) {\n        resolve(value);\n      }\n\n      function onReject(error) {\n        reject(error);\n      }\n\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n\n        value.then(onResolve, onReject);\n      }\n\n      return promise;\n    }\n\n    static all(values) {\n      return ZoneAwarePromise.allWithCallback(values);\n    }\n\n    static allSettled(values) {\n      const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n      return P.allWithCallback(values, {\n        thenCallback: value => ({\n          status: 'fulfilled',\n          value\n        }),\n        errorCallback: err => ({\n          status: 'rejected',\n          reason: err\n        })\n      });\n    }\n\n    static allWithCallback(values, callback) {\n      let resolve;\n      let reject;\n      let promise = new this((res, rej) => {\n        resolve = res;\n        reject = rej;\n      }); // Start at 2 to prevent prematurely resolving if .then is called immediately.\n\n      let unresolvedCount = 2;\n      let valueIndex = 0;\n      const resolvedValues = [];\n\n      for (let value of values) {\n        if (!isThenable(value)) {\n          value = this.resolve(value);\n        }\n\n        const curValueIndex = valueIndex;\n\n        try {\n          value.then(value => {\n            resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n            unresolvedCount--;\n\n            if (unresolvedCount === 0) {\n              resolve(resolvedValues);\n            }\n          }, err => {\n            if (!callback) {\n              reject(err);\n            } else {\n              resolvedValues[curValueIndex] = callback.errorCallback(err);\n              unresolvedCount--;\n\n              if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n              }\n            }\n          });\n        } catch (thenErr) {\n          reject(thenErr);\n        }\n\n        unresolvedCount++;\n        valueIndex++;\n      } // Make the unresolvedCount zero-based again.\n\n\n      unresolvedCount -= 2;\n\n      if (unresolvedCount === 0) {\n        resolve(resolvedValues);\n      }\n\n      return promise;\n    }\n\n    constructor(executor) {\n      const promise = this;\n\n      if (!(promise instanceof ZoneAwarePromise)) {\n        throw new Error('Must be an instanceof Promise.');\n      }\n\n      promise[symbolState] = UNRESOLVED;\n      promise[symbolValue] = []; // queue;\n\n      try {\n        const onceWrapper = once();\n        executor && executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n      } catch (error) {\n        resolvePromise(promise, false, error);\n      }\n    }\n\n    get [Symbol.toStringTag]() {\n      return 'Promise';\n    }\n\n    get [Symbol.species]() {\n      return ZoneAwarePromise;\n    }\n\n    then(onFulfilled, onRejected) {\n      var _a; // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n      // may be an object without a prototype (created through `Object.create(null)`); thus\n      // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n      // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n      // object and copies promise properties into that object (within the `getOrCreateLoad`\n      // function). The zone.js then checks if the resolved value has the `then` method and invokes\n      // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n      // properties of undefined (reading 'Symbol(Symbol.species)')`.\n\n\n      let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n\n      if (!C || typeof C !== 'function') {\n        C = this.constructor || ZoneAwarePromise;\n      }\n\n      const chainPromise = new C(noop);\n      const zone = Zone.current;\n\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n      }\n\n      return chainPromise;\n    }\n\n    catch(onRejected) {\n      return this.then(null, onRejected);\n    }\n\n    finally(onFinally) {\n      var _a; // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n\n\n      let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n\n      if (!C || typeof C !== 'function') {\n        C = ZoneAwarePromise;\n      }\n\n      const chainPromise = new C(noop);\n      chainPromise[symbolFinally] = symbolFinally;\n      const zone = Zone.current;\n\n      if (this[symbolState] == UNRESOLVED) {\n        this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n      } else {\n        scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n      }\n\n      return chainPromise;\n    }\n\n  } // Protect against aggressive optimizers dropping seemingly unused properties.\n  // E.g. Closure Compiler in advanced mode.\n\n\n  ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n  ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n  ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n  ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n  const NativePromise = global[symbolPromise] = global['Promise'];\n  global['Promise'] = ZoneAwarePromise;\n\n  const symbolThenPatched = __symbol__('thenPatched');\n\n  function patchThen(Ctor) {\n    const proto = Ctor.prototype;\n    const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n\n    if (prop && (prop.writable === false || !prop.configurable)) {\n      // check Ctor.prototype.then propertyDescriptor is writable or not\n      // in meteor env, writable is false, we should ignore such case\n      return;\n    }\n\n    const originalThen = proto.then; // Keep a reference to the original method.\n\n    proto[symbolThen] = originalThen;\n\n    Ctor.prototype.then = function (onResolve, onReject) {\n      const wrapped = new ZoneAwarePromise((resolve, reject) => {\n        originalThen.call(this, resolve, reject);\n      });\n      return wrapped.then(onResolve, onReject);\n    };\n\n    Ctor[symbolThenPatched] = true;\n  }\n\n  api.patchThen = patchThen;\n\n  function zoneify(fn) {\n    return function (self, args) {\n      let resultPromise = fn.apply(self, args);\n\n      if (resultPromise instanceof ZoneAwarePromise) {\n        return resultPromise;\n      }\n\n      let ctor = resultPromise.constructor;\n\n      if (!ctor[symbolThenPatched]) {\n        patchThen(ctor);\n      }\n\n      return resultPromise;\n    };\n  }\n\n  if (NativePromise) {\n    patchThen(NativePromise);\n    patchMethod(global, 'fetch', delegate => zoneify(delegate));\n  } // This is not part of public API, but it is useful for tests, so we expose it.\n\n\n  Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n  return ZoneAwarePromise;\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\n\n\nZone.__load_patch('toString', global => {\n  // patch Func.prototype.toString to let them look like native\n  const originalFunctionToString = Function.prototype.toString;\n  const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n  const PROMISE_SYMBOL = zoneSymbol('Promise');\n  const ERROR_SYMBOL = zoneSymbol('Error');\n\n  const newFunctionToString = function toString() {\n    if (typeof this === 'function') {\n      const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n\n      if (originalDelegate) {\n        if (typeof originalDelegate === 'function') {\n          return originalFunctionToString.call(originalDelegate);\n        } else {\n          return Object.prototype.toString.call(originalDelegate);\n        }\n      }\n\n      if (this === Promise) {\n        const nativePromise = global[PROMISE_SYMBOL];\n\n        if (nativePromise) {\n          return originalFunctionToString.call(nativePromise);\n        }\n      }\n\n      if (this === Error) {\n        const nativeError = global[ERROR_SYMBOL];\n\n        if (nativeError) {\n          return originalFunctionToString.call(nativeError);\n        }\n      }\n    }\n\n    return originalFunctionToString.call(this);\n  };\n\n  newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n  Function.prototype.toString = newFunctionToString; // patch Object.prototype.toString to let them look like native\n\n  const originalObjectToString = Object.prototype.toString;\n  const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n\n  Object.prototype.toString = function () {\n    if (typeof Promise === 'function' && this instanceof Promise) {\n      return PROMISE_OBJECT_TO_STRING;\n    }\n\n    return originalObjectToString.call(this);\n  };\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet passiveSupported = false;\n\nif (typeof window !== 'undefined') {\n  try {\n    const options = Object.defineProperty({}, 'passive', {\n      get: function () {\n        passiveSupported = true;\n      }\n    }); // Note: We pass the `options` object as the event handler too. This is not compatible with the\n    // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n    // without an actual handler.\n\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, options);\n  } catch (err) {\n    passiveSupported = false;\n  }\n} // an identifier to tell ZoneTask do not create a new invoke closure\n\n\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n  useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\n\nfunction prepareEventNames(eventName, eventNameToString) {\n  const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n  const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n  const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n  const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n  zoneSymbolEventNames[eventName] = {};\n  zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n  zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\n\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n  const ADD_EVENT_LISTENER = patchOptions && patchOptions.add || ADD_EVENT_LISTENER_STR;\n  const REMOVE_EVENT_LISTENER = patchOptions && patchOptions.rm || REMOVE_EVENT_LISTENER_STR;\n  const LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.listeners || 'eventListeners';\n  const REMOVE_ALL_LISTENERS_EVENT_LISTENER = patchOptions && patchOptions.rmAll || 'removeAllListeners';\n  const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n  const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n  const PREPEND_EVENT_LISTENER = 'prependListener';\n  const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n\n  const invokeTask = function (task, target, event) {\n    // for better performance, check isRemoved which is set\n    // by removeEventListener\n    if (task.isRemoved) {\n      return;\n    }\n\n    const delegate = task.callback;\n\n    if (typeof delegate === 'object' && delegate.handleEvent) {\n      // create the bind version of handleEvent when invoke\n      task.callback = event => delegate.handleEvent(event);\n\n      task.originalDelegate = delegate;\n    } // invoke static task.invoke\n    // need to try/catch error here, otherwise, the error in one event listener\n    // will break the executions of the other event listeners. Also error will\n    // not remove the event listener when `once` options is true.\n\n\n    let error;\n\n    try {\n      task.invoke(task, target, [event]);\n    } catch (err) {\n      error = err;\n    }\n\n    const options = task.options;\n\n    if (options && typeof options === 'object' && options.once) {\n      // if options.once is true, after invoke once remove listener here\n      // only browser need to do this, nodejs eventEmitter will cal removeListener\n      // inside EventEmitter.once\n      const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n      target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n    }\n\n    return error;\n  };\n\n  function globalCallback(context, event, isCapture) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n\n    if (!event) {\n      return;\n    } // event.target is needed for Samsung TV and SourceBuffer\n    // || global is needed https://github.com/angular/zone.js/issues/190\n\n\n    const target = context || event.target || _global;\n    const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n\n    if (tasks) {\n      const errors = []; // invoke all tasks which attached to current target with given event.type and capture = false\n      // for performance concern, if task.length === 1, just invoke\n\n      if (tasks.length === 1) {\n        const err = invokeTask(tasks[0], target, event);\n        err && errors.push(err);\n      } else {\n        // https://github.com/angular/zone.js/issues/836\n        // copy the tasks array before invoke, to avoid\n        // the callback will remove itself or other listener\n        const copyTasks = tasks.slice();\n\n        for (let i = 0; i < copyTasks.length; i++) {\n          if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n            break;\n          }\n\n          const err = invokeTask(copyTasks[i], target, event);\n          err && errors.push(err);\n        }\n      } // Since there is only one error, we don't need to schedule microTask\n      // to throw the error.\n\n\n      if (errors.length === 1) {\n        throw errors[0];\n      } else {\n        for (let i = 0; i < errors.length; i++) {\n          const err = errors[i];\n          api.nativeScheduleMicroTask(() => {\n            throw err;\n          });\n        }\n      }\n    }\n  } // global shared zoneAwareCallback to handle all event callback with capture = false\n\n\n  const globalZoneAwareCallback = function (event) {\n    return globalCallback(this, event, false);\n  }; // global shared zoneAwareCallback to handle all event callback with capture = true\n\n\n  const globalZoneAwareCaptureCallback = function (event) {\n    return globalCallback(this, event, true);\n  };\n\n  function patchEventTargetMethods(obj, patchOptions) {\n    if (!obj) {\n      return false;\n    }\n\n    let useGlobalCallback = true;\n\n    if (patchOptions && patchOptions.useG !== undefined) {\n      useGlobalCallback = patchOptions.useG;\n    }\n\n    const validateHandler = patchOptions && patchOptions.vh;\n    let checkDuplicate = true;\n\n    if (patchOptions && patchOptions.chkDup !== undefined) {\n      checkDuplicate = patchOptions.chkDup;\n    }\n\n    let returnTarget = false;\n\n    if (patchOptions && patchOptions.rt !== undefined) {\n      returnTarget = patchOptions.rt;\n    }\n\n    let proto = obj;\n\n    while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n      proto = ObjectGetPrototypeOf(proto);\n    }\n\n    if (!proto && obj[ADD_EVENT_LISTENER]) {\n      // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n      proto = obj;\n    }\n\n    if (!proto) {\n      return false;\n    }\n\n    if (proto[zoneSymbolAddEventListener]) {\n      return false;\n    }\n\n    const eventNameToString = patchOptions && patchOptions.eventNameToString; // a shared global taskData to pass data for scheduleEventTask\n    // so we do not need to create a new object just for pass some data\n\n    const taskData = {};\n    const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n    const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] = proto[REMOVE_EVENT_LISTENER];\n    const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] = proto[LISTENERS_EVENT_LISTENER];\n    const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] = proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n    let nativePrependEventListener;\n\n    if (patchOptions && patchOptions.prepend) {\n      nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] = proto[patchOptions.prepend];\n    }\n    /**\n     * This util function will build an option object with passive option\n     * to handle all possible input from the user.\n     */\n\n\n    function buildEventListenerOptions(options, passive) {\n      if (!passiveSupported && typeof options === 'object' && options) {\n        // doesn't support passive but user want to pass an object as options.\n        // this will not work on some old browser, so we just pass a boolean\n        // as useCapture parameter\n        return !!options.capture;\n      }\n\n      if (!passiveSupported || !passive) {\n        return options;\n      }\n\n      if (typeof options === 'boolean') {\n        return {\n          capture: options,\n          passive: true\n        };\n      }\n\n      if (!options) {\n        return {\n          passive: true\n        };\n      }\n\n      if (typeof options === 'object' && options.passive !== false) {\n        return Object.assign(Object.assign({}, options), {\n          passive: true\n        });\n      }\n\n      return options;\n    }\n\n    const customScheduleGlobal = function (task) {\n      // if there is already a task for the eventName + capture,\n      // just return, because we use the shared globalZoneAwareCallback here.\n      if (taskData.isExisting) {\n        return;\n      }\n\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n    };\n\n    const customCancelGlobal = function (task) {\n      // if task is not marked as isRemoved, this call is directly\n      // from Zone.prototype.cancelTask, we should remove the task\n      // from tasksList of target first\n      if (!task.isRemoved) {\n        const symbolEventNames = zoneSymbolEventNames[task.eventName];\n        let symbolEventName;\n\n        if (symbolEventNames) {\n          symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n        }\n\n        const existingTasks = symbolEventName && task.target[symbolEventName];\n\n        if (existingTasks) {\n          for (let i = 0; i < existingTasks.length; i++) {\n            const existingTask = existingTasks[i];\n\n            if (existingTask === task) {\n              existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n              task.isRemoved = true;\n\n              if (existingTasks.length === 0) {\n                // all tasks for the eventName + capture have gone,\n                // remove globalZoneAwareCallback and remove the task cache from target\n                task.allRemoved = true;\n                task.target[symbolEventName] = null;\n              }\n\n              break;\n            }\n          }\n        }\n      } // if all tasks for the eventName + capture have gone,\n      // we will really remove the global event callback,\n      // if not, return\n\n\n      if (!task.allRemoved) {\n        return;\n      }\n\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n    };\n\n    const customScheduleNonGlobal = function (task) {\n      return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customSchedulePrepend = function (task) {\n      return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n    };\n\n    const customCancelNonGlobal = function (task) {\n      return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n    };\n\n    const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n    const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n\n    const compareTaskCallbackVsDelegate = function (task, delegate) {\n      const typeOfDelegate = typeof delegate;\n      return typeOfDelegate === 'function' && task.callback === delegate || typeOfDelegate === 'object' && task.originalDelegate === delegate;\n    };\n\n    const compare = patchOptions && patchOptions.diff ? patchOptions.diff : compareTaskCallbackVsDelegate;\n    const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n\n    const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n\n    const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n      return function () {\n        const target = this || _global;\n        let eventName = arguments[0];\n\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        let delegate = arguments[1];\n\n        if (!delegate) {\n          return nativeListener.apply(this, arguments);\n        }\n\n        if (isNode && eventName === 'uncaughtException') {\n          // don't patch uncaughtException of nodejs to prevent endless loop\n          return nativeListener.apply(this, arguments);\n        } // don't create the bind delegate function for handleEvent\n        // case here to improve addEventListener performance\n        // we will create the bind delegate when invoke\n\n\n        let isHandleEvent = false;\n\n        if (typeof delegate !== 'function') {\n          if (!delegate.handleEvent) {\n            return nativeListener.apply(this, arguments);\n          }\n\n          isHandleEvent = true;\n        }\n\n        if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n          return;\n        }\n\n        const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n        const options = buildEventListenerOptions(arguments[2], passive);\n\n        if (unpatchedEvents) {\n          // check unpatched list\n          for (let i = 0; i < unpatchedEvents.length; i++) {\n            if (eventName === unpatchedEvents[i]) {\n              if (passive) {\n                return nativeListener.call(target, eventName, delegate, options);\n              } else {\n                return nativeListener.apply(this, arguments);\n              }\n            }\n          }\n        }\n\n        const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n        const once = options && typeof options === 'object' ? options.once : false;\n        const zone = Zone.current;\n        let symbolEventNames = zoneSymbolEventNames[eventName];\n\n        if (!symbolEventNames) {\n          prepareEventNames(eventName, eventNameToString);\n          symbolEventNames = zoneSymbolEventNames[eventName];\n        }\n\n        const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n        let existingTasks = target[symbolEventName];\n        let isExisting = false;\n\n        if (existingTasks) {\n          // already have task registered\n          isExisting = true;\n\n          if (checkDuplicate) {\n            for (let i = 0; i < existingTasks.length; i++) {\n              if (compare(existingTasks[i], delegate)) {\n                // same callback, same capture, same event name, just return\n                return;\n              }\n            }\n          }\n        } else {\n          existingTasks = target[symbolEventName] = [];\n        }\n\n        let source;\n        const constructorName = target.constructor['name'];\n        const targetSource = globalSources[constructorName];\n\n        if (targetSource) {\n          source = targetSource[eventName];\n        }\n\n        if (!source) {\n          source = constructorName + addSource + (eventNameToString ? eventNameToString(eventName) : eventName);\n        } // do not create a new object as task.data to pass those things\n        // just use the global shared one\n\n\n        taskData.options = options;\n\n        if (once) {\n          // if addEventListener with once options, we don't pass it to\n          // native addEventListener, instead we keep the once setting\n          // and handle ourselves.\n          taskData.options.once = false;\n        }\n\n        taskData.target = target;\n        taskData.capture = capture;\n        taskData.eventName = eventName;\n        taskData.isExisting = isExisting;\n        const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined; // keep taskData into data to allow onScheduleEventTask to access the task information\n\n        if (data) {\n          data.taskData = taskData;\n        }\n\n        const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn); // should clear taskData.target to avoid memory leak\n        // issue, https://github.com/angular/angular/issues/20442\n\n        taskData.target = null; // need to clear up taskData because it is a global object\n\n        if (data) {\n          data.taskData = null;\n        } // have to save those information to task in case\n        // application may call task.zone.cancelTask() directly\n\n\n        if (once) {\n          options.once = true;\n        }\n\n        if (!(!passiveSupported && typeof task.options === 'boolean')) {\n          // if not support passive, and we pass an option object\n          // to addEventListener, we should save the options to task\n          task.options = options;\n        }\n\n        task.target = target;\n        task.capture = capture;\n        task.eventName = eventName;\n\n        if (isHandleEvent) {\n          // save original delegate for compare to check duplicate\n          task.originalDelegate = delegate;\n        }\n\n        if (!prepend) {\n          existingTasks.push(task);\n        } else {\n          existingTasks.unshift(task);\n        }\n\n        if (returnTarget) {\n          return target;\n        }\n      };\n    };\n\n    proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n\n    if (nativePrependEventListener) {\n      proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n    }\n\n    proto[REMOVE_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const options = arguments[2];\n      const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n      const delegate = arguments[1];\n\n      if (!delegate) {\n        return nativeRemoveEventListener.apply(this, arguments);\n      }\n\n      if (validateHandler && !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n        return;\n      }\n\n      const symbolEventNames = zoneSymbolEventNames[eventName];\n      let symbolEventName;\n\n      if (symbolEventNames) {\n        symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n      }\n\n      const existingTasks = symbolEventName && target[symbolEventName];\n\n      if (existingTasks) {\n        for (let i = 0; i < existingTasks.length; i++) {\n          const existingTask = existingTasks[i];\n\n          if (compare(existingTask, delegate)) {\n            existingTasks.splice(i, 1); // set isRemoved to data for faster invokeTask check\n\n            existingTask.isRemoved = true;\n\n            if (existingTasks.length === 0) {\n              // all tasks for the eventName + capture have gone,\n              // remove globalZoneAwareCallback and remove the task cache from target\n              existingTask.allRemoved = true;\n              target[symbolEventName] = null; // in the target, we have an event listener which is added by on_property\n              // such as target.onclick = function() {}, so we need to clear this internal\n              // property too if all delegates all removed\n\n              if (typeof eventName === 'string') {\n                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                target[onPropertySymbol] = null;\n              }\n            }\n\n            existingTask.zone.cancelTask(existingTask);\n\n            if (returnTarget) {\n              return target;\n            }\n\n            return;\n          }\n        }\n      } // issue 930, didn't find the event name or callback\n      // from zone kept existingTasks, the callback maybe\n      // added outside of zone, we need to call native removeEventListener\n      // to try to remove it.\n\n\n      return nativeRemoveEventListener.apply(this, arguments);\n    };\n\n    proto[LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (patchOptions && patchOptions.transferEventName) {\n        eventName = patchOptions.transferEventName(eventName);\n      }\n\n      const listeners = [];\n      const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n\n      for (let i = 0; i < tasks.length; i++) {\n        const task = tasks[i];\n        let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n        listeners.push(delegate);\n      }\n\n      return listeners;\n    };\n\n    proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n      const target = this || _global;\n      let eventName = arguments[0];\n\n      if (!eventName) {\n        const keys = Object.keys(target);\n\n        for (let i = 0; i < keys.length; i++) {\n          const prop = keys[i];\n          const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n          let evtName = match && match[1]; // in nodejs EventEmitter, removeListener event is\n          // used for monitoring the removeListener call,\n          // so just keep removeListener eventListener until\n          // all other eventListeners are removed\n\n          if (evtName && evtName !== 'removeListener') {\n            this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n          }\n        } // remove removeListener listener finally\n\n\n        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n      } else {\n        if (patchOptions && patchOptions.transferEventName) {\n          eventName = patchOptions.transferEventName(eventName);\n        }\n\n        const symbolEventNames = zoneSymbolEventNames[eventName];\n\n        if (symbolEventNames) {\n          const symbolEventName = symbolEventNames[FALSE_STR];\n          const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n          const tasks = target[symbolEventName];\n          const captureTasks = target[symbolCaptureEventName];\n\n          if (tasks) {\n            const removeTasks = tasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n\n          if (captureTasks) {\n            const removeTasks = captureTasks.slice();\n\n            for (let i = 0; i < removeTasks.length; i++) {\n              const task = removeTasks[i];\n              let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n              this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n            }\n          }\n        }\n      }\n\n      if (returnTarget) {\n        return this;\n      }\n    }; // for native toString patch\n\n\n    attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n    attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n\n    if (nativeRemoveAllListeners) {\n      attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n    }\n\n    if (nativeListeners) {\n      attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n    }\n\n    return true;\n  }\n\n  let results = [];\n\n  for (let i = 0; i < apis.length; i++) {\n    results[i] = patchEventTargetMethods(apis[i], patchOptions);\n  }\n\n  return results;\n}\n\nfunction findEventTasks(target, eventName) {\n  if (!eventName) {\n    const foundTasks = [];\n\n    for (let prop in target) {\n      const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n      let evtName = match && match[1];\n\n      if (evtName && (!eventName || evtName === eventName)) {\n        const tasks = target[prop];\n\n        if (tasks) {\n          for (let i = 0; i < tasks.length; i++) {\n            foundTasks.push(tasks[i]);\n          }\n        }\n      }\n    }\n\n    return foundTasks;\n  }\n\n  let symbolEventName = zoneSymbolEventNames[eventName];\n\n  if (!symbolEventName) {\n    prepareEventNames(eventName);\n    symbolEventName = zoneSymbolEventNames[eventName];\n  }\n\n  const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n  const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n\n  if (!captureFalseTasks) {\n    return captureTrueTasks ? captureTrueTasks.slice() : [];\n  } else {\n    return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) : captureFalseTasks.slice();\n  }\n}\n\nfunction patchEventPrototype(global, api) {\n  const Event = global['Event'];\n\n  if (Event && Event.prototype) {\n    api.patchMethod(Event.prototype, 'stopImmediatePropagation', delegate => function (self, args) {\n      self[IMMEDIATE_PROPAGATION_SYMBOL] = true; // we need to call the native stopImmediatePropagation\n      // in case in some hybrid application, some part of\n      // application will be controlled by zone, some are not\n\n      delegate && delegate.apply(self, args);\n    });\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n  const symbol = Zone.__symbol__(method);\n\n  if (target[symbol]) {\n    return;\n  }\n\n  const nativeDelegate = target[symbol] = target[method];\n\n  target[method] = function (name, opts, options) {\n    if (opts && opts.prototype) {\n      callbacks.forEach(function (callback) {\n        const source = `${targetName}.${method}::` + callback;\n        const prototype = opts.prototype; // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n        // `customElements.define`. We explicitly wrap the patching code into try-catch since\n        // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n        // make those properties non-writable. This means that patching callback will throw an error\n        // `cannot assign to read-only property`. See this code as an example:\n        // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n        // We don't want to stop the application rendering if we couldn't patch some\n        // callback, e.g. `attributeChangedCallback`.\n\n        try {\n          if (prototype.hasOwnProperty(callback)) {\n            const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n\n            if (descriptor && descriptor.value) {\n              descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n\n              api._redefineProperty(opts.prototype, callback, descriptor);\n            } else if (prototype[callback]) {\n              prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n            }\n          } else if (prototype[callback]) {\n            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n          }\n        } catch (_a) {// Note: we leave the catch block empty since there's no way to handle the error related\n          // to non-writable property.\n        }\n      });\n    }\n\n    return nativeDelegate.call(target, name, opts, options);\n  };\n\n  api.attachOriginToPatched(target[method], nativeDelegate);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction filterProperties(target, onProperties, ignoreProperties) {\n  if (!ignoreProperties || ignoreProperties.length === 0) {\n    return onProperties;\n  }\n\n  const tip = ignoreProperties.filter(ip => ip.target === target);\n\n  if (!tip || tip.length === 0) {\n    return onProperties;\n  }\n\n  const targetIgnoreProperties = tip[0].ignoreProperties;\n  return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\n\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n  // check whether target is available, sometimes target will be undefined\n  // because different browser or some 3rd party plugin.\n  if (!target) {\n    return;\n  }\n\n  const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n  patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\n\n\nfunction getOnEventNames(target) {\n  return Object.getOwnPropertyNames(target).filter(name => name.startsWith('on') && name.length > 2).map(name => name.substring(2));\n}\n\nfunction propertyDescriptorPatch(api, _global) {\n  if (isNode && !isMix) {\n    return;\n  }\n\n  if (Zone[api.symbol('patchEvents')]) {\n    // events are already been patched by legacy patch.\n    return;\n  }\n\n  const ignoreProperties = _global['__Zone_ignore_on_properties']; // for browsers that we can patch the descriptor:  Chrome & Firefox\n\n  let patchTargets = [];\n\n  if (isBrowser) {\n    const internalWindow = window;\n    patchTargets = patchTargets.concat(['Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement', 'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker']);\n    const ignoreErrorProperties = isIE() ? [{\n      target: internalWindow,\n      ignoreProperties: ['error']\n    }] : []; // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n    // so we need to pass WindowPrototype to check onProp exist or not\n\n    patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n  }\n\n  patchTargets = patchTargets.concat(['XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest', 'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket']);\n\n  for (let i = 0; i < patchTargets.length; i++) {\n    const target = _global[patchTargets[i]];\n    target && target.prototype && patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('util', (global, Zone, api) => {\n  // Collect native event names by looking at properties\n  // on the global namespace, e.g. 'onclick'.\n  const eventNames = getOnEventNames(global);\n  api.patchOnProperties = patchOnProperties;\n  api.patchMethod = patchMethod;\n  api.bindArguments = bindArguments;\n  api.patchMacroTask = patchMacroTask; // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n  // define which events will not be patched by `Zone.js`.\n  // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n  // the name consistent with angular repo.\n  // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n  // backwards compatibility.\n\n  const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n\n  const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n\n  if (global[SYMBOL_UNPATCHED_EVENTS]) {\n    global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n  }\n\n  if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n    Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] = global[SYMBOL_BLACK_LISTED_EVENTS];\n  }\n\n  api.patchEventPrototype = patchEventPrototype;\n  api.patchEventTarget = patchEventTarget;\n  api.isIEOrEdge = isIEOrEdge;\n  api.ObjectDefineProperty = ObjectDefineProperty;\n  api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n  api.ObjectCreate = ObjectCreate;\n  api.ArraySlice = ArraySlice;\n  api.patchClass = patchClass;\n  api.wrapWithCurrentZone = wrapWithCurrentZone;\n  api.filterProperties = filterProperties;\n  api.attachOriginToPatched = attachOriginToPatched;\n  api._redefineProperty = Object.defineProperty;\n  api.patchCallbacks = patchCallbacks;\n\n  api.getGlobalObjects = () => ({\n    globalSources,\n    zoneSymbolEventNames,\n    eventNames,\n    isBrowser,\n    isMix,\n    isNode,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX,\n    ADD_EVENT_LISTENER_STR,\n    REMOVE_EVENT_LISTENER_STR\n  });\n});\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst taskSymbol = zoneSymbol('zoneTask');\n\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n  let setNative = null;\n  let clearNative = null;\n  setName += nameSuffix;\n  cancelName += nameSuffix;\n  const tasksByHandleId = {};\n\n  function scheduleTask(task) {\n    const data = task.data;\n\n    data.args[0] = function () {\n      return task.invoke.apply(this, arguments);\n    };\n\n    data.handleId = setNative.apply(window, data.args);\n    return task;\n  }\n\n  function clearTask(task) {\n    return clearNative.call(window, task.data.handleId);\n  }\n\n  setNative = patchMethod(window, setName, delegate => function (self, args) {\n    if (typeof args[0] === 'function') {\n      const options = {\n        isPeriodic: nameSuffix === 'Interval',\n        delay: nameSuffix === 'Timeout' || nameSuffix === 'Interval' ? args[1] || 0 : undefined,\n        args: args\n      };\n      const callback = args[0];\n\n      args[0] = function timer() {\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          // issue-934, task will be cancelled\n          // even it is a periodic task such as\n          // setInterval\n          // https://github.com/angular/angular/issues/40387\n          // Cleanup tasksByHandleId should be handled before scheduleTask\n          // Since some zoneSpec may intercept and doesn't trigger\n          // scheduleFn(scheduleTask) provided here.\n          if (!options.isPeriodic) {\n            if (typeof options.handleId === 'number') {\n              // in non-nodejs env, we remove timerId\n              // from local cache\n              delete tasksByHandleId[options.handleId];\n            } else if (options.handleId) {\n              // Node returns complex objects as handleIds\n              // we remove task reference from timer object\n              options.handleId[taskSymbol] = null;\n            }\n          }\n        }\n      };\n\n      const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n\n      if (!task) {\n        return task;\n      } // Node.js must additionally support the ref and unref functions.\n\n\n      const handle = task.data.handleId;\n\n      if (typeof handle === 'number') {\n        // for non nodejs env, we save handleId: task\n        // mapping in local cache for clearTimeout\n        tasksByHandleId[handle] = task;\n      } else if (handle) {\n        // for nodejs env, we save task\n        // reference in timerId Object for clearTimeout\n        handle[taskSymbol] = task;\n      } // check whether handle is null, because some polyfill or browser\n      // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n\n\n      if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' && typeof handle.unref === 'function') {\n        task.ref = handle.ref.bind(handle);\n        task.unref = handle.unref.bind(handle);\n      }\n\n      if (typeof handle === 'number' || handle) {\n        return handle;\n      }\n\n      return task;\n    } else {\n      // cause an error by calling it directly.\n      return delegate.apply(window, args);\n    }\n  });\n  clearNative = patchMethod(window, cancelName, delegate => function (self, args) {\n    const id = args[0];\n    let task;\n\n    if (typeof id === 'number') {\n      // non nodejs env.\n      task = tasksByHandleId[id];\n    } else {\n      // nodejs env.\n      task = id && id[taskSymbol]; // other environments.\n\n      if (!task) {\n        task = id;\n      }\n    }\n\n    if (task && typeof task.type === 'string') {\n      if (task.state !== 'notScheduled' && (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n        if (typeof id === 'number') {\n          delete tasksByHandleId[id];\n        } else if (id) {\n          id[taskSymbol] = null;\n        } // Do not cancel already canceled functions\n\n\n        task.zone.cancelTask(task);\n      }\n    } else {\n      // cause an error by calling it directly.\n      delegate.apply(window, args);\n    }\n  });\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction patchCustomElements(_global, api) {\n  const {\n    isBrowser,\n    isMix\n  } = api.getGlobalObjects();\n\n  if (!isBrowser && !isMix || !_global['customElements'] || !('customElements' in _global)) {\n    return;\n  }\n\n  const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n  api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction eventTargetPatch(_global, api) {\n  if (Zone[api.symbol('patchEventTarget')]) {\n    // EventTarget is already patched.\n    return;\n  }\n\n  const {\n    eventNames,\n    zoneSymbolEventNames,\n    TRUE_STR,\n    FALSE_STR,\n    ZONE_SYMBOL_PREFIX\n  } = api.getGlobalObjects(); //  predefine all __zone_symbol__ + eventName + true/false string\n\n  for (let i = 0; i < eventNames.length; i++) {\n    const eventName = eventNames[i];\n    const falseEventName = eventName + FALSE_STR;\n    const trueEventName = eventName + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n  }\n\n  const EVENT_TARGET = _global['EventTarget'];\n\n  if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n    return;\n  }\n\n  api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n  return true;\n}\n\nfunction patchEvent(global, api) {\n  api.patchEventPrototype(global, api);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nZone.__load_patch('legacy', global => {\n  const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n\n  if (legacyPatch) {\n    legacyPatch();\n  }\n});\n\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n  api.patchMethod(global, 'queueMicrotask', delegate => {\n    return function (self, args) {\n      Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n    };\n  });\n});\n\nZone.__load_patch('timers', global => {\n  const set = 'set';\n  const clear = 'clear';\n  patchTimer(global, set, clear, 'Timeout');\n  patchTimer(global, set, clear, 'Interval');\n  patchTimer(global, set, clear, 'Immediate');\n});\n\nZone.__load_patch('requestAnimationFrame', global => {\n  patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n  patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n  patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\n\nZone.__load_patch('blocking', (global, Zone) => {\n  const blockingMethods = ['alert', 'prompt', 'confirm'];\n\n  for (let i = 0; i < blockingMethods.length; i++) {\n    const name = blockingMethods[i];\n    patchMethod(global, name, (delegate, symbol, name) => {\n      return function (s, args) {\n        return Zone.current.run(delegate, global, args, name);\n      };\n    });\n  }\n});\n\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n  patchEvent(global, api);\n  eventTargetPatch(global, api); // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n\n  const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n\n  if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n    api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n  }\n});\n\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n  patchClass('MutationObserver');\n  patchClass('WebKitMutationObserver');\n});\n\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n  patchClass('IntersectionObserver');\n});\n\nZone.__load_patch('FileReader', (global, Zone, api) => {\n  patchClass('FileReader');\n});\n\nZone.__load_patch('on_property', (global, Zone, api) => {\n  propertyDescriptorPatch(api, global);\n});\n\nZone.__load_patch('customElements', (global, Zone, api) => {\n  patchCustomElements(global, api);\n});\n\nZone.__load_patch('XHR', (global, Zone) => {\n  // Treat XMLHttpRequest as a macrotask.\n  patchXHR(global);\n  const XHR_TASK = zoneSymbol('xhrTask');\n  const XHR_SYNC = zoneSymbol('xhrSync');\n  const XHR_LISTENER = zoneSymbol('xhrListener');\n  const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n  const XHR_URL = zoneSymbol('xhrURL');\n  const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n\n  function patchXHR(window) {\n    const XMLHttpRequest = window['XMLHttpRequest'];\n\n    if (!XMLHttpRequest) {\n      // XMLHttpRequest is not available in service worker\n      return;\n    }\n\n    const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n\n    function findPendingTask(target) {\n      return target[XHR_TASK];\n    }\n\n    let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n    let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n\n    if (!oriAddListener) {\n      const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n\n      if (XMLHttpRequestEventTarget) {\n        const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n        oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n    }\n\n    const READY_STATE_CHANGE = 'readystatechange';\n    const SCHEDULED = 'scheduled';\n\n    function scheduleTask(task) {\n      const data = task.data;\n      const target = data.target;\n      target[XHR_SCHEDULED] = false;\n      target[XHR_ERROR_BEFORE_SCHEDULED] = false; // remove existing event listener\n\n      const listener = target[XHR_LISTENER];\n\n      if (!oriAddListener) {\n        oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n      }\n\n      if (listener) {\n        oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n      }\n\n      const newListener = target[XHR_LISTENER] = () => {\n        if (target.readyState === target.DONE) {\n          // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n          // readyState=4 multiple times, so we need to check task state here\n          if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n            // check whether the xhr has registered onload listener\n            // if that is the case, the task should invoke after all\n            // onload listeners finish.\n            // Also if the request failed without response (status = 0), the load event handler\n            // will not be triggered, in that case, we should also invoke the placeholder callback\n            // to close the XMLHttpRequest::send macroTask.\n            // https://github.com/angular/angular/issues/38795\n            const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n            if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n              const oriInvoke = task.invoke;\n\n              task.invoke = function () {\n                // need to load the tasks again, because in other\n                // load listener, they may remove themselves\n                const loadTasks = target[Zone.__symbol__('loadfalse')];\n\n                for (let i = 0; i < loadTasks.length; i++) {\n                  if (loadTasks[i] === task) {\n                    loadTasks.splice(i, 1);\n                  }\n                }\n\n                if (!data.aborted && task.state === SCHEDULED) {\n                  oriInvoke.call(task);\n                }\n              };\n\n              loadTasks.push(task);\n            } else {\n              task.invoke();\n            }\n          } else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n            // error occurs when xhr.send()\n            target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n          }\n        }\n      };\n\n      oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n      const storedTask = target[XHR_TASK];\n\n      if (!storedTask) {\n        target[XHR_TASK] = task;\n      }\n\n      sendNative.apply(target, data.args);\n      target[XHR_SCHEDULED] = true;\n      return task;\n    }\n\n    function placeholderCallback() {}\n\n    function clearTask(task) {\n      const data = task.data; // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n      // to prevent it from firing. So instead, we store info for the event listener.\n\n      data.aborted = true;\n      return abortNative.apply(data.target, data.args);\n    }\n\n    const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n      self[XHR_SYNC] = args[2] == false;\n      self[XHR_URL] = args[1];\n      return openNative.apply(self, args);\n    });\n    const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n    const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n    const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n    const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n      if (Zone.current[fetchTaskScheduling] === true) {\n        // a fetch is scheduling, so we are using xhr to polyfill fetch\n        // and because we already schedule macroTask for fetch, we should\n        // not schedule a macroTask for xhr again\n        return sendNative.apply(self, args);\n      }\n\n      if (self[XHR_SYNC]) {\n        // if the XHR is sync there is no task to schedule, just execute the code.\n        return sendNative.apply(self, args);\n      } else {\n        const options = {\n          target: self,\n          url: self[XHR_URL],\n          isPeriodic: false,\n          args: args,\n          aborted: false\n        };\n        const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n\n        if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted && task.state === SCHEDULED) {\n          // xhr request throw error when send\n          // we should invoke task instead of leaving a scheduled\n          // pending macroTask\n          task.invoke();\n        }\n      }\n    });\n    const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n      const task = findPendingTask(self);\n\n      if (task && typeof task.type == 'string') {\n        // If the XHR has already completed, do nothing.\n        // If the XHR has already been aborted, do nothing.\n        // Fix #569, call abort multiple times before done will cause\n        // macroTask task count be negative number\n        if (task.cancelFn == null || task.data && task.data.aborted) {\n          return;\n        }\n\n        task.zone.cancelTask(task);\n      } else if (Zone.current[fetchTaskAborting] === true) {\n        // the abort is called from fetch polyfill, we need to call native abort of XHR.\n        return abortNative.apply(self, args);\n      } // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n      // task\n      // to cancel. Do nothing.\n\n    });\n  }\n});\n\nZone.__load_patch('geolocation', global => {\n  /// GEO_LOCATION\n  if (global['navigator'] && global['navigator'].geolocation) {\n    patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n  }\n});\n\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n  // handle unhandled promise rejection\n  function findPromiseRejectionHandler(evtName) {\n    return function (e) {\n      const eventTasks = findEventTasks(global, evtName);\n      eventTasks.forEach(eventTask => {\n        // windows has added unhandledrejection event listener\n        // trigger the event listener\n        const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n\n        if (PromiseRejectionEvent) {\n          const evt = new PromiseRejectionEvent(evtName, {\n            promise: e.promise,\n            reason: e.rejection\n          });\n          eventTask.invoke(evt);\n        }\n      });\n    };\n  }\n\n  if (global['PromiseRejectionEvent']) {\n    Zone[zoneSymbol('unhandledPromiseRejectionHandler')] = findPromiseRejectionHandler('unhandledrejection');\n    Zone[zoneSymbol('rejectionHandledHandler')] = findPromiseRejectionHandler('rejectionhandled');\n  }\n});", "map": {"version": 3, "names": ["global", "performance", "mark", "name", "performanceMeasure", "label", "symbolPrefix", "__symbol__", "checkDuplicate", "Error", "Zone", "constructor", "parent", "zoneSpec", "_parent", "_name", "_properties", "properties", "_zoneDelegate", "_ZoneDelegate", "assertZonePatched", "patches", "root", "zone", "current", "_currentZoneFrame", "currentTask", "_currentTask", "__load_patch", "fn", "ignoreDuplicate", "hasOwnProperty", "perfName", "_api", "get", "key", "getZoneWith", "fork", "wrap", "callback", "source", "_callback", "intercept", "runGuarded", "arguments", "run", "applyThis", "applyArgs", "invoke", "error", "handleError", "runTask", "task", "NO_ZONE", "state", "notScheduled", "type", "eventTask", "macroTask", "re<PERSON><PERSON><PERSON><PERSON><PERSON>", "running", "_transitionTo", "scheduled", "runCount", "previousTask", "data", "isPeriodic", "cancelFn", "undefined", "invokeTask", "unknown", "_updateTaskCount", "scheduleTask", "newZone", "scheduling", "zoneDelegates", "_zoneDelegates", "_zone", "err", "scheduleMicroTask", "customSchedule", "ZoneTask", "microTask", "scheduleMacroTask", "customCancel", "scheduleEventTask", "cancelTask", "canceling", "count", "i", "length", "DELEGATE_ZS", "onHasTask", "delegate", "_", "target", "hasTaskState", "hasTask", "onScheduleTask", "onInvokeTask", "onCancelTask", "parentDelegate", "_taskCounts", "_parentDelegate", "_forkZS", "onFork", "_forkDlgt", "_forkCurrZone", "_interceptZS", "onIntercept", "_interceptDlgt", "_interceptCurrZone", "_invokeZS", "onInvoke", "_invokeDlgt", "_invokeCurrZone", "_handleErrorZS", "onHandleError", "_handleErrorDlgt", "_handleErrorCurrZone", "_scheduleTaskZS", "_scheduleTaskDlgt", "_scheduleTaskCurrZone", "_invokeTaskZS", "_invokeTaskDlgt", "_invokeTaskCurrZone", "_cancelTaskZS", "_cancelTaskDlgt", "_cancelTaskCurrZone", "_hasTaskZS", "_hasTaskDlgt", "_hasTaskDlgtOwner", "_hasTaskCurrZone", "zoneSpecHasTask", "parentHasTask", "targetZone", "apply", "returnTask", "push", "scheduleFn", "value", "isEmpty", "counts", "prev", "next", "change", "options", "_state", "self", "useG", "call", "args", "_numberOfNestedTaskFrames", "drainMicroTaskQueue", "cancelScheduleRequest", "toState", "fromState1", "fromState2", "toString", "handleId", "Object", "prototype", "toJSON", "symbolSetTimeout", "symbolPromise", "symbolThen", "_microTaskQueue", "_isDrainingMicrotaskQueue", "nativeMicroTaskQueuePromise", "nativeScheduleMicroTask", "func", "resolve", "nativeThen", "queue", "onUnhandledError", "microtaskDrainDone", "symbol", "currentZoneFrame", "noop", "showUncaughtError", "patchEventTarget", "patchOnProperties", "patchMethod", "bindArguments", "patchThen", "patchMacroTask", "patchEventPrototype", "isIEOrEdge", "getGlobalObjects", "ObjectDefineProperty", "ObjectGetOwnPropertyDescriptor", "ObjectCreate", "ArraySlice", "patchClass", "wrapWithCurrentZone", "filterProperties", "attachOriginToPatched", "_redefineProperty", "patchCallbacks", "window", "getOwnPropertyDescriptor", "defineProperty", "ObjectGetPrototypeOf", "getPrototypeOf", "create", "Array", "slice", "ADD_EVENT_LISTENER_STR", "REMOVE_EVENT_LISTENER_STR", "ZONE_SYMBOL_ADD_EVENT_LISTENER", "ZONE_SYMBOL_REMOVE_EVENT_LISTENER", "TRUE_STR", "FALSE_STR", "ZONE_SYMBOL_PREFIX", "scheduleMacroTaskWithCurrentZone", "zoneSymbol", "isWindowExists", "internalWindow", "_global", "REMOVE_ATTRIBUTE", "patchPrototype", "fnNames", "prototypeDesc", "isPropertyWritable", "patched", "propertyDesc", "writable", "set", "isWebWorker", "WorkerGlobalScope", "isNode", "process", "<PERSON><PERSON><PERSON><PERSON>", "isMix", "zoneSymbolEventNames$1", "wrapFn", "event", "eventNameSymbol", "listener", "result", "errorEvent", "message", "filename", "lineno", "colno", "preventDefault", "patchProperty", "obj", "prop", "desc", "enumerable", "configurable", "onPropPatchedSymbol", "originalDescGet", "originalDescSet", "eventName", "newValue", "previousValue", "removeEventListener", "addEventListener", "removeAttribute", "onProperties", "j", "originalInstanceKey", "className", "OriginalClass", "a", "instance", "patchFn", "proto", "<PERSON><PERSON><PERSON>", "patchDelegate", "funcName", "metaCreator", "setNative", "cbIdx", "meta", "original", "isDetectedIEOrEdge", "ieOrEdge", "isIE", "ua", "navigator", "userAgent", "indexOf", "api", "readableObjectToString", "JSON", "stringify", "_uncaughtPromiseErrors", "isDisableWrappingUncaughtPromiseRejection", "creationTrace", "e", "rejection", "console", "stack", "uncaughtPromiseError", "shift", "throwOriginal", "handleUnhandledRejection", "UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL", "handler", "isThenable", "then", "forwardResolution", "forwardRejection", "ZoneAwarePromise", "reject", "symbolState", "symbolValue", "symbolFinally", "symbolParentPromiseValue", "symbolParentPromiseState", "UNRESOLVED", "RESOLVED", "REJECTED", "REJECTED_NO_CATCH", "makeResolver", "promise", "v", "resolvePromise", "once", "wasCalled", "wrapper", "wrappedFunction", "TYPE_ERROR", "CURRENT_TASK_TRACE_SYMBOL", "onceWrapper", "TypeError", "clearRejectedNoCatch", "trace", "scheduleResolveOrReject", "REJECTION_HANDLED_HANDLER", "splice", "chainPromise", "onFulfilled", "onRejected", "promiseState", "parentPromiseValue", "isFinallyPromise", "ZONE_AWARE_PROMISE_TO_STRING", "AggregateError", "any", "values", "Symbol", "iterator", "Promise", "promises", "finished", "errors", "race", "res", "rej", "onResolve", "onReject", "all", "allWithCallback", "allSettled", "P", "then<PERSON>allback", "status", "<PERSON><PERSON><PERSON><PERSON>", "reason", "unresolvedCount", "valueIndex", "resolvedV<PERSON>ues", "curValueIndex", "thenErr", "executor", "toStringTag", "species", "_a", "C", "catch", "finally", "onFinally", "NativePromise", "symbolThenPatched", "Ctor", "originalThen", "wrapped", "zoneify", "resultPromise", "ctor", "originalFunctionToString", "Function", "ORIGINAL_DELEGATE_SYMBOL", "PROMISE_SYMBOL", "ERROR_SYMBOL", "newFunctionToString", "originalDelegate", "nativePromise", "nativeError", "originalObjectToString", "PROMISE_OBJECT_TO_STRING", "passiveSupported", "OPTIMIZED_ZONE_EVENT_TASK_DATA", "zoneSymbolEventNames", "globalSources", "EVENT_NAME_SYMBOL_REGX", "RegExp", "IMMEDIATE_PROPAGATION_SYMBOL", "prepareEventNames", "eventNameToString", "falseEventName", "trueEventName", "symbolCapture", "apis", "patchOptions", "ADD_EVENT_LISTENER", "add", "REMOVE_EVENT_LISTENER", "rm", "LISTENERS_EVENT_LISTENER", "listeners", "REMOVE_ALL_LISTENERS_EVENT_LISTENER", "rmAll", "zoneSymbolAddEventListener", "ADD_EVENT_LISTENER_SOURCE", "PREPEND_EVENT_LISTENER", "PREPEND_EVENT_LISTENER_SOURCE", "isRemoved", "handleEvent", "globalCallback", "context", "isCapture", "tasks", "copyTasks", "globalZoneAwareCallback", "globalZoneAwareCaptureCallback", "patchEventTargetMethods", "useGlobalCallback", "validate<PERSON><PERSON><PERSON>", "vh", "chkDup", "<PERSON><PERSON><PERSON><PERSON>", "rt", "taskData", "nativeAddEventListener", "nativeRemoveEventListener", "nativeListeners", "nativeRemoveAllListeners", "nativePrependEventListener", "prepend", "buildEventListenerOptions", "passive", "capture", "assign", "customScheduleGlobal", "isExisting", "customCancelGlobal", "symbolEventNames", "symbolEventName", "existingTasks", "existingTask", "allRemoved", "customScheduleNonGlobal", "customSchedulePrepend", "customCancelNonGlobal", "compareTaskCallbackVsDelegate", "typeOfDelegate", "compare", "diff", "unpatchedEvents", "passiveEvents", "makeAddListener", "nativeListener", "addSource", "customScheduleFn", "customCancelFn", "transferEventName", "isHandleEvent", "constructorName", "targetSource", "unshift", "onPropertySymbol", "findEventTasks", "keys", "match", "exec", "evtName", "symbolCaptureEventName", "captureTasks", "removeTasks", "results", "foundTasks", "captureFalseTasks", "captureTrueTasks", "concat", "Event", "targetName", "method", "callbacks", "nativeDelegate", "opts", "for<PERSON>ach", "descriptor", "ignoreProperties", "tip", "filter", "ip", "targetIgnoreProperties", "op", "patchFilteredProperties", "filteredProperties", "getOnEventNames", "getOwnPropertyNames", "startsWith", "map", "substring", "propertyDescriptorPatch", "patchTargets", "ignoreErrorProperties", "eventNames", "SYMBOL_BLACK_LISTED_EVENTS", "SYMBOL_UNPATCHED_EVENTS", "taskSymbol", "patchTimer", "setName", "cancelName", "nameSuffix", "clearNative", "tasksByHandleId", "clearTask", "delay", "timer", "handle", "ref", "unref", "bind", "id", "patchCustomElements", "customElements", "eventTargetPatch", "EVENT_TARGET", "patchEvent", "legacyPatch", "clear", "blockingMethods", "s", "XMLHttpRequestEventTarget", "patchXHR", "XHR_TASK", "XHR_SYNC", "XHR_LISTENER", "XHR_SCHEDULED", "XHR_URL", "XHR_ERROR_BEFORE_SCHEDULED", "XMLHttpRequest", "XMLHttpRequestPrototype", "findPendingTask", "oriAddListener", "oriRemoveListener", "XMLHttpRequestEventTargetPrototype", "READY_STATE_CHANGE", "SCHEDULED", "newListener", "readyState", "DONE", "aborted", "loadTasks", "oriInvoke", "storedTask", "sendNative", "placeholder<PERSON><PERSON><PERSON>", "abortNative", "openNative", "XMLHTTPREQUEST_SOURCE", "fetchTaskAborting", "fetchTaskScheduling", "url", "geolocation", "findPromiseRejectionHandler", "eventTasks", "PromiseRejectionEvent", "evt"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/zone.js/fesm2015/zone.js"], "sourcesContent": ["'use strict';\n/**\n * @license Angular v14.2.0-next.0\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n((function (global) {\n    const performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    // Initialize before it's accessed below.\n    // __Zone_symbol_prefix global can be used to override the default zone\n    // symbol prefix with a custom one if needed.\n    const symbolPrefix = global['__Zone_symbol_prefix'] || '__zone_symbol__';\n    function __symbol__(name) {\n        return symbolPrefix + name;\n    }\n    const checkDuplicate = global[__symbol__('forceDuplicateZoneCheck')] === true;\n    if (global['Zone']) {\n        // if global['Zone'] already exists (maybe zone.js was already loaded or\n        // some other lib also registered a global object named Zone), we may need\n        // to throw an error, but sometimes user may not want this error.\n        // For example,\n        // we have two web pages, page1 includes zone.js, page2 doesn't.\n        // and the 1st time user load page1 and page2, everything work fine,\n        // but when user load page2 again, error occurs because global['Zone'] already exists.\n        // so we add a flag to let user choose whether to throw this error or not.\n        // By default, if existing Zone is from zone.js, we will not throw the error.\n        if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n            throw new Error('Zone already loaded.');\n        }\n        else {\n            return global['Zone'];\n        }\n    }\n    class Zone {\n        constructor(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = zoneSpec && zoneSpec.properties || {};\n            this._zoneDelegate =\n                new _ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        static assertZonePatched() {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        }\n        static get root() {\n            let zone = Zone.current;\n            while (zone.parent) {\n                zone = zone.parent;\n            }\n            return zone;\n        }\n        static get current() {\n            return _currentZoneFrame.zone;\n        }\n        static get currentTask() {\n            return _currentTask;\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        static __load_patch(name, fn, ignoreDuplicate = false) {\n            if (patches.hasOwnProperty(name)) {\n                // `checkDuplicate` option is defined from global variable\n                // so it works for all modules.\n                // `ignoreDuplicate` can work for the specified module\n                if (!ignoreDuplicate && checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                const perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, Zone, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        }\n        get parent() {\n            return this._parent;\n        }\n        get name() {\n            return this._name;\n        }\n        get(key) {\n            const zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        }\n        getZoneWith(key) {\n            let current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        }\n        fork(zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        }\n        wrap(callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            const _callback = this._zoneDelegate.intercept(this, callback, source);\n            const zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        }\n        run(callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runGuarded(callback, applyThis = null, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        }\n        runTask(task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            const reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            const previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        }\n        scheduleTask(task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                let newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${task.zone.name}`);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            const zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        }\n        scheduleMicroTask(source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        }\n        scheduleMacroTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        }\n        scheduleEventTask(source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        }\n        cancelTask(task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        }\n        _updateTaskCount(task, count) {\n            const zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (let i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        }\n    }\n    // tslint:disable-next-line:require-internal-with-underscore\n    Zone.__symbol__ = __symbol__;\n    const DELEGATE_ZS = {\n        name: '',\n        onHasTask: (delegate, _, target, hasTaskState) => delegate.hasTask(target, hasTaskState),\n        onScheduleTask: (delegate, _, target, task) => delegate.scheduleTask(target, task),\n        onInvokeTask: (delegate, _, target, task, applyThis, applyArgs) => delegate.invokeTask(target, task, applyThis, applyArgs),\n        onCancelTask: (delegate, _, target, task) => delegate.cancelTask(target, task)\n    };\n    class _ZoneDelegate {\n        constructor(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = { 'microTask': 0, 'macroTask': 0, 'eventTask': 0 };\n            this.zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone =\n                zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate._forkCurrZone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate._interceptCurrZone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone =\n                zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate._invokeCurrZone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate._handleErrorCurrZone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt = zoneSpec &&\n                (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate._scheduleTaskCurrZone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate._invokeTaskCurrZone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate._cancelTaskCurrZone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            const zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            const parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this.zone;\n                }\n            }\n        }\n        fork(targetZone, zoneSpec) {\n            return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) :\n                new Zone(targetZone, zoneSpec);\n        }\n        intercept(targetZone, callback, source) {\n            return this._interceptZS ?\n                this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) :\n                callback;\n        }\n        invoke(targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) :\n                callback.apply(applyThis, applyArgs);\n        }\n        handleError(targetZone, error) {\n            return this._handleErrorZS ?\n                this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) :\n                true;\n        }\n        scheduleTask(targetZone, task) {\n            let returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                // clang-format off\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                // clang-format on\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        }\n        invokeTask(targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) :\n                task.callback.apply(applyThis, applyArgs);\n        }\n        cancelTask(targetZone, task) {\n            let value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        }\n        hasTask(targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _updateTaskCount(type, count) {\n            const counts = this._taskCounts;\n            const prev = counts[type];\n            const next = counts[type] = prev + count;\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                const isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type\n                };\n                this.hasTask(this.zone, isEmpty);\n            }\n        }\n    }\n    class ZoneTask {\n        constructor(type, source, callback, options, scheduleFn, cancelFn) {\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zone = null;\n            this.runCount = 0;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._zoneDelegates = null;\n            // tslint:disable-next-line:require-internal-with-underscore\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            if (!callback) {\n                throw new Error('callback is not defined');\n            }\n            this.callback = callback;\n            const self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        static invokeTask(task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        }\n        get zone() {\n            return this._zone;\n        }\n        get state() {\n            return this._state;\n        }\n        cancelScheduleRequest() {\n            this._transitionTo(notScheduled, scheduling);\n        }\n        // tslint:disable-next-line:require-internal-with-underscore\n        _transitionTo(toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(`${this.type} '${this.source}': can not transition to '${toState}', expecting state '${fromState1}'${fromState2 ? ' or \\'' + fromState2 + '\\'' : ''}, was '${this._state}'.`);\n            }\n        }\n        toString() {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        }\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        toJSON() {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount\n            };\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const symbolSetTimeout = __symbol__('setTimeout');\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    let _microTaskQueue = [];\n    let _isDrainingMicrotaskQueue = false;\n    let nativeMicroTaskQueuePromise;\n    function nativeScheduleMicroTask(func) {\n        if (!nativeMicroTaskQueuePromise) {\n            if (global[symbolPromise]) {\n                nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n            }\n        }\n        if (nativeMicroTaskQueuePromise) {\n            let nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n            if (!nativeThen) {\n                // native Promise is not patchable, we need to use `then` directly\n                // issue 1078\n                nativeThen = nativeMicroTaskQueuePromise['then'];\n            }\n            nativeThen.call(nativeMicroTaskQueuePromise, func);\n        }\n        else {\n            global[symbolSetTimeout](func, 0);\n        }\n    }\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            nativeScheduleMicroTask(drainMicroTaskQueue);\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                const queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (let i = 0; i < queue.length; i++) {\n                    const task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    const NO_ZONE = { name: 'NO ZONE' };\n    const notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    const microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    const patches = {};\n    const _api = {\n        symbol: __symbol__,\n        currentZoneFrame: () => _currentZoneFrame,\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: () => !Zone[__symbol__('ignoreConsoleErrorUncaughtError')],\n        patchEventTarget: () => [],\n        patchOnProperties: noop,\n        patchMethod: () => noop,\n        bindArguments: () => [],\n        patchThen: () => noop,\n        patchMacroTask: () => noop,\n        patchEventPrototype: () => noop,\n        isIEOrEdge: () => false,\n        getGlobalObjects: () => undefined,\n        ObjectDefineProperty: () => noop,\n        ObjectGetOwnPropertyDescriptor: () => undefined,\n        ObjectCreate: () => undefined,\n        ArraySlice: () => [],\n        patchClass: () => noop,\n        wrapWithCurrentZone: () => noop,\n        filterProperties: () => [],\n        attachOriginToPatched: () => noop,\n        _redefineProperty: () => noop,\n        patchCallbacks: () => noop,\n        nativeScheduleMicroTask: nativeScheduleMicroTask\n    };\n    let _currentZoneFrame = { parent: null, zone: new Zone(null, null) };\n    let _currentTask = null;\n    let _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    performanceMeasure('Zone', 'Zone');\n    return global['Zone'] = Zone;\n}))(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n/// <reference types=\"node\"/>\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nconst ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nconst ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nconst ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nconst ObjectCreate = Object.create;\n/** Array.prototype.slice */\nconst ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nconst ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nconst REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nconst ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nconst ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nconst TRUE_STR = 'true';\n/** false string const */\nconst FALSE_STR = 'false';\n/** Zone symbol prefix string const. */\nconst ZONE_SYMBOL_PREFIX = Zone.__symbol__('');\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nconst zoneSymbol = Zone.__symbol__;\nconst isWindowExists = typeof window !== 'undefined';\nconst internalWindow = isWindowExists ? window : undefined;\nconst _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\nconst REMOVE_ATTRIBUTE = 'removeAttribute';\nfunction bindArguments(args, source) {\n    for (let i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    const source = prototype.constructor['name'];\n    for (let i = 0; i < fnNames.length; i++) {\n        const name = fnNames[i];\n        const delegate = prototype[name];\n        if (delegate) {\n            const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name);\n            if (!isPropertyWritable(prototypeDesc)) {\n                continue;\n            }\n            prototype[name] = ((delegate) => {\n                const patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nconst isWebWorker = (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope);\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isNode = (!('nw' in _global) && typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]');\nconst isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nconst isMix = typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]' && !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nconst zoneSymbolEventNames$1 = {};\nconst wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    let eventNameSymbol = zoneSymbolEventNames$1[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    const target = this || event.target || _global;\n    const listener = target[eventNameSymbol];\n    let result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signature\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        const errorEvent = event;\n        result = listener &&\n            listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    let desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        const prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    const onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    const originalDescGet = desc.get;\n    const originalDescSet = desc.set;\n    // slice(2) cuz 'onclick' -> 'click', etc\n    const eventName = prop.slice(2);\n    let eventNameSymbol = zoneSymbolEventNames$1[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames$1[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        const previousValue = target[eventNameSymbol];\n        if (typeof previousValue === 'function') {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        originalDescSet && originalDescSet.call(target, null);\n        target[eventNameSymbol] = newValue;\n        if (typeof newValue === 'function') {\n            target.addEventListener(eventName, wrapFn, false);\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        let target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        const listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            let value = originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (let i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        const onProperties = [];\n        for (const prop in obj) {\n            if (prop.slice(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (let j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nconst originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    const OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        const a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    const instance = new OriginalClass(function () { });\n    let prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    }\n                });\n            }\n        }(prop));\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction patchMethod(target, name, patchFn) {\n    let proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    const delegateName = zoneSymbol(name);\n    let delegate = null;\n    if (proto && (!(delegate = proto[delegateName]) || !proto.hasOwnProperty(delegateName))) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        const desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            const patchDelegate = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    let setNative = null;\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, (delegate) => function (self, args) {\n        const meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    });\n}\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nlet isDetectedIEOrEdge = false;\nlet ieOrEdge = false;\nfunction isIE() {\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) {\n    }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        const ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) {\n    }\n    return ieOrEdge;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('ZoneAwarePromise', (global, Zone, api) => {\n    const ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    const ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n        if (obj && obj.toString === Object.prototype.toString) {\n            const className = obj.constructor && obj.constructor.name;\n            return (className ? className : '') + ': ' + JSON.stringify(obj);\n        }\n        return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    const __symbol__ = api.symbol;\n    const _uncaughtPromiseErrors = [];\n    const isDisableWrappingUncaughtPromiseRejection = global[__symbol__('DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION')] === true;\n    const symbolPromise = __symbol__('Promise');\n    const symbolThen = __symbol__('then');\n    const creationTrace = '__creationTrace__';\n    api.onUnhandledError = (e) => {\n        if (api.showUncaughtError()) {\n            const rejection = e && e.rejection;\n            if (rejection) {\n                console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n            }\n            else {\n                console.error(e);\n            }\n        }\n    };\n    api.microtaskDrainDone = () => {\n        while (_uncaughtPromiseErrors.length) {\n            const uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n            try {\n                uncaughtPromiseError.zone.runGuarded(() => {\n                    if (uncaughtPromiseError.throwOriginal) {\n                        throw uncaughtPromiseError.rejection;\n                    }\n                    throw uncaughtPromiseError;\n                });\n            }\n            catch (error) {\n                handleUnhandledRejection(error);\n            }\n        }\n    };\n    const UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n        api.onUnhandledError(e);\n        try {\n            const handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n            if (typeof handler === 'function') {\n                handler.call(this, e);\n            }\n        }\n        catch (err) {\n        }\n    }\n    function isThenable(value) {\n        return value && value.then;\n    }\n    function forwardResolution(value) {\n        return value;\n    }\n    function forwardRejection(rejection) {\n        return ZoneAwarePromise.reject(rejection);\n    }\n    const symbolState = __symbol__('state');\n    const symbolValue = __symbol__('value');\n    const symbolFinally = __symbol__('finally');\n    const symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    const symbolParentPromiseState = __symbol__('parentPromiseState');\n    const source = 'Promise.then';\n    const UNRESOLVED = null;\n    const RESOLVED = true;\n    const REJECTED = false;\n    const REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n        return (v) => {\n            try {\n                resolvePromise(promise, state, v);\n            }\n            catch (err) {\n                resolvePromise(promise, false, err);\n            }\n            // Do not return value or you will break the Promise spec.\n        };\n    }\n    const once = function () {\n        let wasCalled = false;\n        return function wrapper(wrappedFunction) {\n            return function () {\n                if (wasCalled) {\n                    return;\n                }\n                wasCalled = true;\n                wrappedFunction.apply(null, arguments);\n            };\n        };\n    };\n    const TYPE_ERROR = 'Promise resolved with itself';\n    const CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n        const onceWrapper = once();\n        if (promise === value) {\n            throw new TypeError(TYPE_ERROR);\n        }\n        if (promise[symbolState] === UNRESOLVED) {\n            // should only get value.then once based on promise spec.\n            let then = null;\n            try {\n                if (typeof value === 'object' || typeof value === 'function') {\n                    then = value && value.then;\n                }\n            }\n            catch (err) {\n                onceWrapper(() => {\n                    resolvePromise(promise, false, err);\n                })();\n                return promise;\n            }\n            // if (value instanceof ZoneAwarePromise) {\n            if (state !== REJECTED && value instanceof ZoneAwarePromise &&\n                value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) &&\n                value[symbolState] !== UNRESOLVED) {\n                clearRejectedNoCatch(value);\n                resolvePromise(promise, value[symbolState], value[symbolValue]);\n            }\n            else if (state !== REJECTED && typeof then === 'function') {\n                try {\n                    then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                }\n                catch (err) {\n                    onceWrapper(() => {\n                        resolvePromise(promise, false, err);\n                    })();\n                }\n            }\n            else {\n                promise[symbolState] = state;\n                const queue = promise[symbolValue];\n                promise[symbolValue] = value;\n                if (promise[symbolFinally] === symbolFinally) {\n                    // the promise is generated by Promise.prototype.finally\n                    if (state === RESOLVED) {\n                        // the state is resolved, should ignore the value\n                        // and use parent promise value\n                        promise[symbolState] = promise[symbolParentPromiseState];\n                        promise[symbolValue] = promise[symbolParentPromiseValue];\n                    }\n                }\n                // record task information in value when error occurs, so we can\n                // do some additional work such as render longStackTrace\n                if (state === REJECTED && value instanceof Error) {\n                    // check if longStackTraceZone is here\n                    const trace = Zone.currentTask && Zone.currentTask.data &&\n                        Zone.currentTask.data[creationTrace];\n                    if (trace) {\n                        // only keep the long stack trace into error when in longStackTraceZone\n                        ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, { configurable: true, enumerable: false, writable: true, value: trace });\n                    }\n                }\n                for (let i = 0; i < queue.length;) {\n                    scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                }\n                if (queue.length == 0 && state == REJECTED) {\n                    promise[symbolState] = REJECTED_NO_CATCH;\n                    let uncaughtPromiseError = value;\n                    try {\n                        // Here we throws a new Error to print more readable error log\n                        // and if the value is not an error, zone.js builds an `Error`\n                        // Object here to attach the stack information.\n                        throw new Error('Uncaught (in promise): ' + readableObjectToString(value) +\n                            (value && value.stack ? '\\n' + value.stack : ''));\n                    }\n                    catch (err) {\n                        uncaughtPromiseError = err;\n                    }\n                    if (isDisableWrappingUncaughtPromiseRejection) {\n                        // If disable wrapping uncaught promise reject\n                        // use the value instead of wrapping it.\n                        uncaughtPromiseError.throwOriginal = true;\n                    }\n                    uncaughtPromiseError.rejection = value;\n                    uncaughtPromiseError.promise = promise;\n                    uncaughtPromiseError.zone = Zone.current;\n                    uncaughtPromiseError.task = Zone.currentTask;\n                    _uncaughtPromiseErrors.push(uncaughtPromiseError);\n                    api.scheduleMicroTask(); // to make sure that it is running\n                }\n            }\n        }\n        // Resolving an already resolved promise is a noop.\n        return promise;\n    }\n    const REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n        if (promise[symbolState] === REJECTED_NO_CATCH) {\n            // if the promise is rejected no catch status\n            // and queue.length > 0, means there is a error handler\n            // here to handle the rejected promise, we should trigger\n            // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n            // eventHandler\n            try {\n                const handler = Zone[REJECTION_HANDLED_HANDLER];\n                if (handler && typeof handler === 'function') {\n                    handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                }\n            }\n            catch (err) {\n            }\n            promise[symbolState] = REJECTED;\n            for (let i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                if (promise === _uncaughtPromiseErrors[i].promise) {\n                    _uncaughtPromiseErrors.splice(i, 1);\n                }\n            }\n        }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n        clearRejectedNoCatch(promise);\n        const promiseState = promise[symbolState];\n        const delegate = promiseState ?\n            (typeof onFulfilled === 'function') ? onFulfilled : forwardResolution :\n            (typeof onRejected === 'function') ? onRejected :\n                forwardRejection;\n        zone.scheduleMicroTask(source, () => {\n            try {\n                const parentPromiseValue = promise[symbolValue];\n                const isFinallyPromise = !!chainPromise && symbolFinally === chainPromise[symbolFinally];\n                if (isFinallyPromise) {\n                    // if the promise is generated from finally call, keep parent promise's state and value\n                    chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                    chainPromise[symbolParentPromiseState] = promiseState;\n                }\n                // should not pass value to finally callback\n                const value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ?\n                    [] :\n                    [parentPromiseValue]);\n                resolvePromise(chainPromise, true, value);\n            }\n            catch (error) {\n                // if error occurs, should always return this error\n                resolvePromise(chainPromise, false, error);\n            }\n        }, chainPromise);\n    }\n    const ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    const noop = function () { };\n    const AggregateError = global.AggregateError;\n    class ZoneAwarePromise {\n        static toString() {\n            return ZONE_AWARE_PROMISE_TO_STRING;\n        }\n        static resolve(value) {\n            return resolvePromise(new this(null), RESOLVED, value);\n        }\n        static reject(error) {\n            return resolvePromise(new this(null), REJECTED, error);\n        }\n        static any(values) {\n            if (!values || typeof values[Symbol.iterator] !== 'function') {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            const promises = [];\n            let count = 0;\n            try {\n                for (let v of values) {\n                    count++;\n                    promises.push(ZoneAwarePromise.resolve(v));\n                }\n            }\n            catch (err) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            if (count === 0) {\n                return Promise.reject(new AggregateError([], 'All promises were rejected'));\n            }\n            let finished = false;\n            const errors = [];\n            return new ZoneAwarePromise((resolve, reject) => {\n                for (let i = 0; i < promises.length; i++) {\n                    promises[i].then(v => {\n                        if (finished) {\n                            return;\n                        }\n                        finished = true;\n                        resolve(v);\n                    }, err => {\n                        errors.push(err);\n                        count--;\n                        if (count === 0) {\n                            finished = true;\n                            reject(new AggregateError(errors, 'All promises were rejected'));\n                        }\n                    });\n                }\n            });\n        }\n        ;\n        static race(values) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            function onResolve(value) {\n                resolve(value);\n            }\n            function onReject(error) {\n                reject(error);\n            }\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                value.then(onResolve, onReject);\n            }\n            return promise;\n        }\n        static all(values) {\n            return ZoneAwarePromise.allWithCallback(values);\n        }\n        static allSettled(values) {\n            const P = this && this.prototype instanceof ZoneAwarePromise ? this : ZoneAwarePromise;\n            return P.allWithCallback(values, {\n                thenCallback: (value) => ({ status: 'fulfilled', value }),\n                errorCallback: (err) => ({ status: 'rejected', reason: err })\n            });\n        }\n        static allWithCallback(values, callback) {\n            let resolve;\n            let reject;\n            let promise = new this((res, rej) => {\n                resolve = res;\n                reject = rej;\n            });\n            // Start at 2 to prevent prematurely resolving if .then is called immediately.\n            let unresolvedCount = 2;\n            let valueIndex = 0;\n            const resolvedValues = [];\n            for (let value of values) {\n                if (!isThenable(value)) {\n                    value = this.resolve(value);\n                }\n                const curValueIndex = valueIndex;\n                try {\n                    value.then((value) => {\n                        resolvedValues[curValueIndex] = callback ? callback.thenCallback(value) : value;\n                        unresolvedCount--;\n                        if (unresolvedCount === 0) {\n                            resolve(resolvedValues);\n                        }\n                    }, (err) => {\n                        if (!callback) {\n                            reject(err);\n                        }\n                        else {\n                            resolvedValues[curValueIndex] = callback.errorCallback(err);\n                            unresolvedCount--;\n                            if (unresolvedCount === 0) {\n                                resolve(resolvedValues);\n                            }\n                        }\n                    });\n                }\n                catch (thenErr) {\n                    reject(thenErr);\n                }\n                unresolvedCount++;\n                valueIndex++;\n            }\n            // Make the unresolvedCount zero-based again.\n            unresolvedCount -= 2;\n            if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n            }\n            return promise;\n        }\n        constructor(executor) {\n            const promise = this;\n            if (!(promise instanceof ZoneAwarePromise)) {\n                throw new Error('Must be an instanceof Promise.');\n            }\n            promise[symbolState] = UNRESOLVED;\n            promise[symbolValue] = []; // queue;\n            try {\n                const onceWrapper = once();\n                executor &&\n                    executor(onceWrapper(makeResolver(promise, RESOLVED)), onceWrapper(makeResolver(promise, REJECTED)));\n            }\n            catch (error) {\n                resolvePromise(promise, false, error);\n            }\n        }\n        get [Symbol.toStringTag]() {\n            return 'Promise';\n        }\n        get [Symbol.species]() {\n            return ZoneAwarePromise;\n        }\n        then(onFulfilled, onRejected) {\n            var _a;\n            // We must read `Symbol.species` safely because `this` may be anything. For instance, `this`\n            // may be an object without a prototype (created through `Object.create(null)`); thus\n            // `this.constructor` will be undefined. One of the use cases is SystemJS creating\n            // prototype-less objects (modules) via `Object.create(null)`. The SystemJS creates an empty\n            // object and copies promise properties into that object (within the `getOrCreateLoad`\n            // function). The zone.js then checks if the resolved value has the `then` method and invokes\n            // it with the `value` context. Otherwise, this will throw an error: `TypeError: Cannot read\n            // properties of undefined (reading 'Symbol(Symbol.species)')`.\n            let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = this.constructor || ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n            }\n            return chainPromise;\n        }\n        catch(onRejected) {\n            return this.then(null, onRejected);\n        }\n        finally(onFinally) {\n            var _a;\n            // See comment on the call to `then` about why thee `Symbol.species` is safely accessed.\n            let C = (_a = this.constructor) === null || _a === void 0 ? void 0 : _a[Symbol.species];\n            if (!C || typeof C !== 'function') {\n                C = ZoneAwarePromise;\n            }\n            const chainPromise = new C(noop);\n            chainPromise[symbolFinally] = symbolFinally;\n            const zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n            }\n            return chainPromise;\n        }\n    }\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    const NativePromise = global[symbolPromise] = global['Promise'];\n    global['Promise'] = ZoneAwarePromise;\n    const symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n        const proto = Ctor.prototype;\n        const prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n        if (prop && (prop.writable === false || !prop.configurable)) {\n            // check Ctor.prototype.then propertyDescriptor is writable or not\n            // in meteor env, writable is false, we should ignore such case\n            return;\n        }\n        const originalThen = proto.then;\n        // Keep a reference to the original method.\n        proto[symbolThen] = originalThen;\n        Ctor.prototype.then = function (onResolve, onReject) {\n            const wrapped = new ZoneAwarePromise((resolve, reject) => {\n                originalThen.call(this, resolve, reject);\n            });\n            return wrapped.then(onResolve, onReject);\n        };\n        Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n        return function (self, args) {\n            let resultPromise = fn.apply(self, args);\n            if (resultPromise instanceof ZoneAwarePromise) {\n                return resultPromise;\n            }\n            let ctor = resultPromise.constructor;\n            if (!ctor[symbolThenPatched]) {\n                patchThen(ctor);\n            }\n            return resultPromise;\n        };\n    }\n    if (NativePromise) {\n        patchThen(NativePromise);\n        patchMethod(global, 'fetch', delegate => zoneify(delegate));\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\nZone.__load_patch('toString', (global) => {\n    // patch Func.prototype.toString to let them look like native\n    const originalFunctionToString = Function.prototype.toString;\n    const ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    const PROMISE_SYMBOL = zoneSymbol('Promise');\n    const ERROR_SYMBOL = zoneSymbol('Error');\n    const newFunctionToString = function toString() {\n        if (typeof this === 'function') {\n            const originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n            if (originalDelegate) {\n                if (typeof originalDelegate === 'function') {\n                    return originalFunctionToString.call(originalDelegate);\n                }\n                else {\n                    return Object.prototype.toString.call(originalDelegate);\n                }\n            }\n            if (this === Promise) {\n                const nativePromise = global[PROMISE_SYMBOL];\n                if (nativePromise) {\n                    return originalFunctionToString.call(nativePromise);\n                }\n            }\n            if (this === Error) {\n                const nativeError = global[ERROR_SYMBOL];\n                if (nativeError) {\n                    return originalFunctionToString.call(nativeError);\n                }\n            }\n        }\n        return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    const originalObjectToString = Object.prototype.toString;\n    const PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n        if (typeof Promise === 'function' && this instanceof Promise) {\n            return PROMISE_OBJECT_TO_STRING;\n        }\n        return originalObjectToString.call(this);\n    };\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        const options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            }\n        });\n        // Note: We pass the `options` object as the event handler too. This is not compatible with the\n        // signature of `addEventListener` or `removeEventListener` but enables us to remove the handler\n        // without an actual handler.\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nconst OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true\n};\nconst zoneSymbolEventNames = {};\nconst globalSources = {};\nconst EVENT_NAME_SYMBOL_REGX = new RegExp('^' + ZONE_SYMBOL_PREFIX + '(\\\\w+)(true|false)$');\nconst IMMEDIATE_PROPAGATION_SYMBOL = zoneSymbol('propagationStopped');\nfunction prepareEventNames(eventName, eventNameToString) {\n    const falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n    const trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n    const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n    const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n    zoneSymbolEventNames[eventName] = {};\n    zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n    zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n}\nfunction patchEventTarget(_global, api, apis, patchOptions) {\n    const ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    const REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    const LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    const REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    const zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    const ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    const PREPEND_EVENT_LISTENER = 'prependListener';\n    const PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    const invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        const delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = (event) => delegate.handleEvent(event);\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        // need to try/catch error here, otherwise, the error in one event listener\n        // will break the executions of the other event listeners. Also error will\n        // not remove the event listener when `once` options is true.\n        let error;\n        try {\n            task.invoke(task, target, [event]);\n        }\n        catch (err) {\n            error = err;\n        }\n        const options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            const delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate, options);\n        }\n        return error;\n    };\n    function globalCallback(context, event, isCapture) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        const target = context || event.target || _global;\n        const tasks = target[zoneSymbolEventNames[event.type][isCapture ? TRUE_STR : FALSE_STR]];\n        if (tasks) {\n            const errors = [];\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                const err = invokeTask(tasks[0], target, event);\n                err && errors.push(err);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                const copyTasks = tasks.slice();\n                for (let i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    const err = invokeTask(copyTasks[i], target, event);\n                    err && errors.push(err);\n                }\n            }\n            // Since there is only one error, we don't need to schedule microTask\n            // to throw the error.\n            if (errors.length === 1) {\n                throw errors[0];\n            }\n            else {\n                for (let i = 0; i < errors.length; i++) {\n                    const err = errors[i];\n                    api.nativeScheduleMicroTask(() => {\n                        throw err;\n                    });\n                }\n            }\n        }\n    }\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    const globalZoneAwareCallback = function (event) {\n        return globalCallback(this, event, false);\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    const globalZoneAwareCaptureCallback = function (event) {\n        return globalCallback(this, event, true);\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        let useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        const validateHandler = patchOptions && patchOptions.vh;\n        let checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        let returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        let proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        const eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // a shared global taskData to pass data for scheduleEventTask\n        // so we do not need to create a new object just for pass some data\n        const taskData = {};\n        const nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n        const nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER];\n        const nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER];\n        const nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n        let nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        /**\n         * This util function will build an option object with passive option\n         * to handle all possible input from the user.\n         */\n        function buildEventListenerOptions(options, passive) {\n            if (!passiveSupported && typeof options === 'object' && options) {\n                // doesn't support passive but user want to pass an object as options.\n                // this will not work on some old browser, so we just pass a boolean\n                // as useCapture parameter\n                return !!options.capture;\n            }\n            if (!passiveSupported || !passive) {\n                return options;\n            }\n            if (typeof options === 'boolean') {\n                return { capture: options, passive: true };\n            }\n            if (!options) {\n                return { passive: true };\n            }\n            if (typeof options === 'object' && options.passive !== false) {\n                return Object.assign(Object.assign({}, options), { passive: true });\n            }\n            return options;\n        }\n        const customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        const customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                const symbolEventNames = zoneSymbolEventNames[task.eventName];\n                let symbolEventName;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                const existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (let i = 0; i < existingTasks.length; i++) {\n                        const existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        const customScheduleNonGlobal = function (task) {\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        const customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        const customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        const customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        const compareTaskCallbackVsDelegate = function (task, delegate) {\n            const typeOfDelegate = typeof delegate;\n            return (typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate);\n        };\n        const compare = (patchOptions && patchOptions.diff) ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        const unpatchedEvents = Zone[zoneSymbol('UNPATCHED_EVENTS')];\n        const passiveEvents = _global[zoneSymbol('PASSIVE_EVENTS')];\n        const makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget = false, prepend = false) {\n            return function () {\n                const target = this || _global;\n                let eventName = arguments[0];\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                let delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                let isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                const passive = passiveSupported && !!passiveEvents && passiveEvents.indexOf(eventName) !== -1;\n                const options = buildEventListenerOptions(arguments[2], passive);\n                if (unpatchedEvents) {\n                    // check unpatched list\n                    for (let i = 0; i < unpatchedEvents.length; i++) {\n                        if (eventName === unpatchedEvents[i]) {\n                            if (passive) {\n                                return nativeListener.call(target, eventName, delegate, options);\n                            }\n                            else {\n                                return nativeListener.apply(this, arguments);\n                            }\n                        }\n                    }\n                }\n                const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n                const once = options && typeof options === 'object' ? options.once : false;\n                const zone = Zone.current;\n                let symbolEventNames = zoneSymbolEventNames[eventName];\n                if (!symbolEventNames) {\n                    prepareEventNames(eventName, eventNameToString);\n                    symbolEventNames = zoneSymbolEventNames[eventName];\n                }\n                const symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                let existingTasks = target[symbolEventName];\n                let isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (let i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                let source;\n                const constructorName = target.constructor['name'];\n                const targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source = constructorName + addSource +\n                        (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // do not create a new object as task.data to pass those things\n                // just use the global shared one\n                taskData.options = options;\n                if (once) {\n                    // if addEventListener with once options, we don't pass it to\n                    // native addEventListener, instead we keep the once setting\n                    // and handle ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                const data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                const task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const options = arguments[2];\n            const capture = !options ? false : typeof options === 'boolean' ? true : options.capture;\n            const delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            const symbolEventNames = zoneSymbolEventNames[eventName];\n            let symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            const existingTasks = symbolEventName && target[symbolEventName];\n            if (existingTasks) {\n                for (let i = 0; i < existingTasks.length; i++) {\n                    const existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                            // in the target, we have an event listener which is added by on_property\n                            // such as target.onclick = function() {}, so we need to clear this internal\n                            // property too if all delegates all removed\n                            if (typeof eventName === 'string') {\n                                const onPropertySymbol = ZONE_SYMBOL_PREFIX + 'ON_PROPERTY' + eventName;\n                                target[onPropertySymbol] = null;\n                            }\n                        }\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // issue 930, didn't find the event name or callback\n            // from zone kept existingTasks, the callback maybe\n            // added outside of zone, we need to call native removeEventListener\n            // to try to remove it.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (patchOptions && patchOptions.transferEventName) {\n                eventName = patchOptions.transferEventName(eventName);\n            }\n            const listeners = [];\n            const tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (let i = 0; i < tasks.length; i++) {\n                const task = tasks[i];\n                let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            const target = this || _global;\n            let eventName = arguments[0];\n            if (!eventName) {\n                const keys = Object.keys(target);\n                for (let i = 0; i < keys.length; i++) {\n                    const prop = keys[i];\n                    const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    let evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                if (patchOptions && patchOptions.transferEventName) {\n                    eventName = patchOptions.transferEventName(eventName);\n                }\n                const symbolEventNames = zoneSymbolEventNames[eventName];\n                if (symbolEventNames) {\n                    const symbolEventName = symbolEventNames[FALSE_STR];\n                    const symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    const tasks = target[symbolEventName];\n                    const captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        const removeTasks = tasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        const removeTasks = captureTasks.slice();\n                        for (let i = 0; i < removeTasks.length; i++) {\n                            const task = removeTasks[i];\n                            let delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    let results = [];\n    for (let i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    if (!eventName) {\n        const foundTasks = [];\n        for (let prop in target) {\n            const match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n            let evtName = match && match[1];\n            if (evtName && (!eventName || evtName === eventName)) {\n                const tasks = target[prop];\n                if (tasks) {\n                    for (let i = 0; i < tasks.length; i++) {\n                        foundTasks.push(tasks[i]);\n                    }\n                }\n            }\n        }\n        return foundTasks;\n    }\n    let symbolEventName = zoneSymbolEventNames[eventName];\n    if (!symbolEventName) {\n        prepareEventNames(eventName);\n        symbolEventName = zoneSymbolEventNames[eventName];\n    }\n    const captureFalseTasks = target[symbolEventName[FALSE_STR]];\n    const captureTrueTasks = target[symbolEventName[TRUE_STR]];\n    if (!captureFalseTasks) {\n        return captureTrueTasks ? captureTrueTasks.slice() : [];\n    }\n    else {\n        return captureTrueTasks ? captureFalseTasks.concat(captureTrueTasks) :\n            captureFalseTasks.slice();\n    }\n}\nfunction patchEventPrototype(global, api) {\n    const Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', (delegate) => function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        });\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    const symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    const nativeDelegate = target[symbol] = target[method];\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                const source = `${targetName}.${method}::` + callback;\n                const prototype = opts.prototype;\n                // Note: the `patchCallbacks` is used for patching the `document.registerElement` and\n                // `customElements.define`. We explicitly wrap the patching code into try-catch since\n                // callbacks may be already patched by other web components frameworks (e.g. LWC), and they\n                // make those properties non-writable. This means that patching callback will throw an error\n                // `cannot assign to read-only property`. See this code as an example:\n                // https://github.com/salesforce/lwc/blob/master/packages/@lwc/engine-core/src/framework/base-bridge-element.ts#L180-L186\n                // We don't want to stop the application rendering if we couldn't patch some\n                // callback, e.g. `attributeChangedCallback`.\n                try {\n                    if (prototype.hasOwnProperty(callback)) {\n                        const descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                        if (descriptor && descriptor.value) {\n                            descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                            api._redefineProperty(opts.prototype, callback, descriptor);\n                        }\n                        else if (prototype[callback]) {\n                            prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                        }\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                catch (_a) {\n                    // Note: we leave the catch block empty since there's no way to handle the error related\n                    // to non-writable property.\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    const tip = ignoreProperties.filter(ip => ip.target === target);\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    const targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter(op => targetIgnoreProperties.indexOf(op) === -1);\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    const filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\n/**\n * Get all event name properties which the event name startsWith `on`\n * from the target object itself, inherited properties are not considered.\n */\nfunction getOnEventNames(target) {\n    return Object.getOwnPropertyNames(target)\n        .filter(name => name.startsWith('on') && name.length > 2)\n        .map(name => name.substring(2));\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    const ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    let patchTargets = [];\n    if (isBrowser) {\n        const internalWindow = window;\n        patchTargets = patchTargets.concat([\n            'Document', 'SVGElement', 'Element', 'HTMLElement', 'HTMLBodyElement', 'HTMLMediaElement',\n            'HTMLFrameSetElement', 'HTMLFrameElement', 'HTMLIFrameElement', 'HTMLMarqueeElement', 'Worker'\n        ]);\n        const ignoreErrorProperties = isIE() ? [{ target: internalWindow, ignoreProperties: ['error'] }] : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, getOnEventNames(internalWindow), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n    }\n    patchTargets = patchTargets.concat([\n        'XMLHttpRequest', 'XMLHttpRequestEventTarget', 'IDBIndex', 'IDBRequest', 'IDBOpenDBRequest',\n        'IDBDatabase', 'IDBTransaction', 'IDBCursor', 'WebSocket'\n    ]);\n    for (let i = 0; i < patchTargets.length; i++) {\n        const target = _global[patchTargets[i]];\n        target && target.prototype &&\n            patchFilteredProperties(target.prototype, getOnEventNames(target.prototype), ignoreProperties);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('util', (global, Zone, api) => {\n    // Collect native event names by looking at properties\n    // on the global namespace, e.g. 'onclick'.\n    const eventNames = getOnEventNames(global);\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n    // define which events will not be patched by `Zone.js`.\n    // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n    // the name consistent with angular repo.\n    // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n    // backwards compatibility.\n    const SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    const SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n        global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n        Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n            global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = Object.defineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = () => ({\n        globalSources,\n        zoneSymbolEventNames,\n        eventNames,\n        isBrowser,\n        isMix,\n        isNode,\n        TRUE_STR,\n        FALSE_STR,\n        ZONE_SYMBOL_PREFIX,\n        ADD_EVENT_LISTENER_STR,\n        REMOVE_EVENT_LISTENER_STR\n    });\n});\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    let setNative = null;\n    let clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    const tasksByHandleId = {};\n    function scheduleTask(task) {\n        const data = task.data;\n        data.args[0] = function () {\n            return task.invoke.apply(this, arguments);\n        };\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative.call(window, task.data.handleId);\n    }\n    setNative =\n        patchMethod(window, setName, (delegate) => function (self, args) {\n            if (typeof args[0] === 'function') {\n                const options = {\n                    isPeriodic: nameSuffix === 'Interval',\n                    delay: (nameSuffix === 'Timeout' || nameSuffix === 'Interval') ? args[1] || 0 :\n                        undefined,\n                    args: args\n                };\n                const callback = args[0];\n                args[0] = function timer() {\n                    try {\n                        return callback.apply(this, arguments);\n                    }\n                    finally {\n                        // issue-934, task will be cancelled\n                        // even it is a periodic task such as\n                        // setInterval\n                        // https://github.com/angular/angular/issues/40387\n                        // Cleanup tasksByHandleId should be handled before scheduleTask\n                        // Since some zoneSpec may intercept and doesn't trigger\n                        // scheduleFn(scheduleTask) provided here.\n                        if (!(options.isPeriodic)) {\n                            if (typeof options.handleId === 'number') {\n                                // in non-nodejs env, we remove timerId\n                                // from local cache\n                                delete tasksByHandleId[options.handleId];\n                            }\n                            else if (options.handleId) {\n                                // Node returns complex objects as handleIds\n                                // we remove task reference from timer object\n                                options.handleId[taskSymbol] = null;\n                            }\n                        }\n                    }\n                };\n                const task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n                if (!task) {\n                    return task;\n                }\n                // Node.js must additionally support the ref and unref functions.\n                const handle = task.data.handleId;\n                if (typeof handle === 'number') {\n                    // for non nodejs env, we save handleId: task\n                    // mapping in local cache for clearTimeout\n                    tasksByHandleId[handle] = task;\n                }\n                else if (handle) {\n                    // for nodejs env, we save task\n                    // reference in timerId Object for clearTimeout\n                    handle[taskSymbol] = task;\n                }\n                // check whether handle is null, because some polyfill or browser\n                // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n                if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' &&\n                    typeof handle.unref === 'function') {\n                    task.ref = handle.ref.bind(handle);\n                    task.unref = handle.unref.bind(handle);\n                }\n                if (typeof handle === 'number' || handle) {\n                    return handle;\n                }\n                return task;\n            }\n            else {\n                // cause an error by calling it directly.\n                return delegate.apply(window, args);\n            }\n        });\n    clearNative =\n        patchMethod(window, cancelName, (delegate) => function (self, args) {\n            const id = args[0];\n            let task;\n            if (typeof id === 'number') {\n                // non nodejs env.\n                task = tasksByHandleId[id];\n            }\n            else {\n                // nodejs env.\n                task = id && id[taskSymbol];\n                // other environments.\n                if (!task) {\n                    task = id;\n                }\n            }\n            if (task && typeof task.type === 'string') {\n                if (task.state !== 'notScheduled' &&\n                    (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n                    if (typeof id === 'number') {\n                        delete tasksByHandleId[id];\n                    }\n                    else if (id) {\n                        id[taskSymbol] = null;\n                    }\n                    // Do not cancel already canceled functions\n                    task.zone.cancelTask(task);\n                }\n            }\n            else {\n                // cause an error by calling it directly.\n                delegate.apply(window, args);\n            }\n        });\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCustomElements(_global, api) {\n    const { isBrowser, isMix } = api.getGlobalObjects();\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    const callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    const { eventNames, zoneSymbolEventNames, TRUE_STR, FALSE_STR, ZONE_SYMBOL_PREFIX } = api.getGlobalObjects();\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (let i = 0; i < eventNames.length; i++) {\n        const eventName = eventNames[i];\n        const falseEventName = eventName + FALSE_STR;\n        const trueEventName = eventName + TRUE_STR;\n        const symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        const symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    const EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, api, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('legacy', (global) => {\n    const legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n        legacyPatch();\n    }\n});\nZone.__load_patch('queueMicrotask', (global, Zone, api) => {\n    api.patchMethod(global, 'queueMicrotask', delegate => {\n        return function (self, args) {\n            Zone.current.scheduleMicroTask('queueMicrotask', args[0]);\n        };\n    });\n});\nZone.__load_patch('timers', (global) => {\n    const set = 'set';\n    const clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n});\nZone.__load_patch('requestAnimationFrame', (global) => {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\nZone.__load_patch('blocking', (global, Zone) => {\n    const blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (let i = 0; i < blockingMethods.length; i++) {\n        const name = blockingMethods[i];\n        patchMethod(global, name, (delegate, symbol, name) => {\n            return function (s, args) {\n                return Zone.current.run(delegate, global, args, name);\n            };\n        });\n    }\n});\nZone.__load_patch('EventTarget', (global, Zone, api) => {\n    patchEvent(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    const XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n        api.patchEventTarget(global, api, [XMLHttpRequestEventTarget.prototype]);\n    }\n});\nZone.__load_patch('MutationObserver', (global, Zone, api) => {\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n});\nZone.__load_patch('IntersectionObserver', (global, Zone, api) => {\n    patchClass('IntersectionObserver');\n});\nZone.__load_patch('FileReader', (global, Zone, api) => {\n    patchClass('FileReader');\n});\nZone.__load_patch('on_property', (global, Zone, api) => {\n    propertyDescriptorPatch(api, global);\n});\nZone.__load_patch('customElements', (global, Zone, api) => {\n    patchCustomElements(global, api);\n});\nZone.__load_patch('XHR', (global, Zone) => {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    const XHR_TASK = zoneSymbol('xhrTask');\n    const XHR_SYNC = zoneSymbol('xhrSync');\n    const XHR_LISTENER = zoneSymbol('xhrListener');\n    const XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    const XHR_URL = zoneSymbol('xhrURL');\n    const XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n        const XMLHttpRequest = window['XMLHttpRequest'];\n        if (!XMLHttpRequest) {\n            // XMLHttpRequest is not available in service worker\n            return;\n        }\n        const XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n        function findPendingTask(target) {\n            return target[XHR_TASK];\n        }\n        let oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        let oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        if (!oriAddListener) {\n            const XMLHttpRequestEventTarget = window['XMLHttpRequestEventTarget'];\n            if (XMLHttpRequestEventTarget) {\n                const XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget.prototype;\n                oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n        }\n        const READY_STATE_CHANGE = 'readystatechange';\n        const SCHEDULED = 'scheduled';\n        function scheduleTask(task) {\n            const data = task.data;\n            const target = data.target;\n            target[XHR_SCHEDULED] = false;\n            target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n            // remove existing event listener\n            const listener = target[XHR_LISTENER];\n            if (!oriAddListener) {\n                oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n            if (listener) {\n                oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n            }\n            const newListener = target[XHR_LISTENER] = () => {\n                if (target.readyState === target.DONE) {\n                    // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                    // readyState=4 multiple times, so we need to check task state here\n                    if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                        // check whether the xhr has registered onload listener\n                        // if that is the case, the task should invoke after all\n                        // onload listeners finish.\n                        // Also if the request failed without response (status = 0), the load event handler\n                        // will not be triggered, in that case, we should also invoke the placeholder callback\n                        // to close the XMLHttpRequest::send macroTask.\n                        // https://github.com/angular/angular/issues/38795\n                        const loadTasks = target[Zone.__symbol__('loadfalse')];\n                        if (target.status !== 0 && loadTasks && loadTasks.length > 0) {\n                            const oriInvoke = task.invoke;\n                            task.invoke = function () {\n                                // need to load the tasks again, because in other\n                                // load listener, they may remove themselves\n                                const loadTasks = target[Zone.__symbol__('loadfalse')];\n                                for (let i = 0; i < loadTasks.length; i++) {\n                                    if (loadTasks[i] === task) {\n                                        loadTasks.splice(i, 1);\n                                    }\n                                }\n                                if (!data.aborted && task.state === SCHEDULED) {\n                                    oriInvoke.call(task);\n                                }\n                            };\n                            loadTasks.push(task);\n                        }\n                        else {\n                            task.invoke();\n                        }\n                    }\n                    else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                        // error occurs when xhr.send()\n                        target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                    }\n                }\n            };\n            oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n            const storedTask = target[XHR_TASK];\n            if (!storedTask) {\n                target[XHR_TASK] = task;\n            }\n            sendNative.apply(target, data.args);\n            target[XHR_SCHEDULED] = true;\n            return task;\n        }\n        function placeholderCallback() { }\n        function clearTask(task) {\n            const data = task.data;\n            // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n            // to prevent it from firing. So instead, we store info for the event listener.\n            data.aborted = true;\n            return abortNative.apply(data.target, data.args);\n        }\n        const openNative = patchMethod(XMLHttpRequestPrototype, 'open', () => function (self, args) {\n            self[XHR_SYNC] = args[2] == false;\n            self[XHR_URL] = args[1];\n            return openNative.apply(self, args);\n        });\n        const XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n        const fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n        const fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n        const sendNative = patchMethod(XMLHttpRequestPrototype, 'send', () => function (self, args) {\n            if (Zone.current[fetchTaskScheduling] === true) {\n                // a fetch is scheduling, so we are using xhr to polyfill fetch\n                // and because we already schedule macroTask for fetch, we should\n                // not schedule a macroTask for xhr again\n                return sendNative.apply(self, args);\n            }\n            if (self[XHR_SYNC]) {\n                // if the XHR is sync there is no task to schedule, just execute the code.\n                return sendNative.apply(self, args);\n            }\n            else {\n                const options = { target: self, url: self[XHR_URL], isPeriodic: false, args: args, aborted: false };\n                const task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted &&\n                    task.state === SCHEDULED) {\n                    // xhr request throw error when send\n                    // we should invoke task instead of leaving a scheduled\n                    // pending macroTask\n                    task.invoke();\n                }\n            }\n        });\n        const abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', () => function (self, args) {\n            const task = findPendingTask(self);\n            if (task && typeof task.type == 'string') {\n                // If the XHR has already completed, do nothing.\n                // If the XHR has already been aborted, do nothing.\n                // Fix #569, call abort multiple times before done will cause\n                // macroTask task count be negative number\n                if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                    return;\n                }\n                task.zone.cancelTask(task);\n            }\n            else if (Zone.current[fetchTaskAborting] === true) {\n                // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                return abortNative.apply(self, args);\n            }\n            // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n            // task\n            // to cancel. Do nothing.\n        });\n    }\n});\nZone.__load_patch('geolocation', (global) => {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n        patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n});\nZone.__load_patch('PromiseRejectionEvent', (global, Zone) => {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n        return function (e) {\n            const eventTasks = findEventTasks(global, evtName);\n            eventTasks.forEach(eventTask => {\n                // windows has added unhandledrejection event listener\n                // trigger the event listener\n                const PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                if (PromiseRejectionEvent) {\n                    const evt = new PromiseRejectionEvent(evtName, { promise: e.promise, reason: e.rejection });\n                    eventTask.invoke(evt);\n                }\n            });\n        };\n    }\n    if (global['PromiseRejectionEvent']) {\n        Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n            findPromiseRejectionHandler('unhandledrejection');\n        Zone[zoneSymbol('rejectionHandledHandler')] =\n            findPromiseRejectionHandler('rejectionhandled');\n    }\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,CAAE,UAAUA,MAAV,EAAkB;EAChB,MAAMC,WAAW,GAAGD,MAAM,CAAC,aAAD,CAA1B;;EACA,SAASE,IAAT,CAAcC,IAAd,EAAoB;IAChBF,WAAW,IAAIA,WAAW,CAAC,MAAD,CAA1B,IAAsCA,WAAW,CAAC,MAAD,CAAX,CAAoBE,IAApB,CAAtC;EACH;;EACD,SAASC,kBAAT,CAA4BD,IAA5B,EAAkCE,KAAlC,EAAyC;IACrCJ,WAAW,IAAIA,WAAW,CAAC,SAAD,CAA1B,IAAyCA,WAAW,CAAC,SAAD,CAAX,CAAuBE,IAAvB,EAA6BE,KAA7B,CAAzC;EACH;;EACDH,IAAI,CAAC,MAAD,CAAJ,CARgB,CAShB;EACA;EACA;;EACA,MAAMI,YAAY,GAAGN,MAAM,CAAC,sBAAD,CAAN,IAAkC,iBAAvD;;EACA,SAASO,UAAT,CAAoBJ,IAApB,EAA0B;IACtB,OAAOG,YAAY,GAAGH,IAAtB;EACH;;EACD,MAAMK,cAAc,GAAGR,MAAM,CAACO,UAAU,CAAC,yBAAD,CAAX,CAAN,KAAkD,IAAzE;;EACA,IAAIP,MAAM,CAAC,MAAD,CAAV,EAAoB;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIQ,cAAc,IAAI,OAAOR,MAAM,CAAC,MAAD,CAAN,CAAeO,UAAtB,KAAqC,UAA3D,EAAuE;MACnE,MAAM,IAAIE,KAAJ,CAAU,sBAAV,CAAN;IACH,CAFD,MAGK;MACD,OAAOT,MAAM,CAAC,MAAD,CAAb;IACH;EACJ;;EACD,MAAMU,IAAN,CAAW;IACPC,WAAW,CAACC,MAAD,EAASC,QAAT,EAAmB;MAC1B,KAAKC,OAAL,GAAeF,MAAf;MACA,KAAKG,KAAL,GAAaF,QAAQ,GAAGA,QAAQ,CAACV,IAAT,IAAiB,SAApB,GAAgC,QAArD;MACA,KAAKa,WAAL,GAAmBH,QAAQ,IAAIA,QAAQ,CAACI,UAArB,IAAmC,EAAtD;MACA,KAAKC,aAAL,GACI,IAAIC,aAAJ,CAAkB,IAAlB,EAAwB,KAAKL,OAAL,IAAgB,KAAKA,OAAL,CAAaI,aAArD,EAAoEL,QAApE,CADJ;IAEH;;IACuB,OAAjBO,iBAAiB,GAAG;MACvB,IAAIpB,MAAM,CAAC,SAAD,CAAN,KAAsBqB,OAAO,CAAC,kBAAD,CAAjC,EAAuD;QACnD,MAAM,IAAIZ,KAAJ,CAAU,0EACZ,yBADY,GAEZ,+DAFY,GAGZ,kFAHY,GAIZ,sDAJE,CAAN;MAKH;IACJ;;IACc,WAAJa,IAAI,GAAG;MACd,IAAIC,IAAI,GAAGb,IAAI,CAACc,OAAhB;;MACA,OAAOD,IAAI,CAACX,MAAZ,EAAoB;QAChBW,IAAI,GAAGA,IAAI,CAACX,MAAZ;MACH;;MACD,OAAOW,IAAP;IACH;;IACiB,WAAPC,OAAO,GAAG;MACjB,OAAOC,iBAAiB,CAACF,IAAzB;IACH;;IACqB,WAAXG,WAAW,GAAG;MACrB,OAAOC,YAAP;IACH,CA7BM,CA8BP;;;IACmB,OAAZC,YAAY,CAACzB,IAAD,EAAO0B,EAAP,EAAWC,eAAe,GAAG,KAA7B,EAAoC;MACnD,IAAIT,OAAO,CAACU,cAAR,CAAuB5B,IAAvB,CAAJ,EAAkC;QAC9B;QACA;QACA;QACA,IAAI,CAAC2B,eAAD,IAAoBtB,cAAxB,EAAwC;UACpC,MAAMC,KAAK,CAAC,2BAA2BN,IAA5B,CAAX;QACH;MACJ,CAPD,MAQK,IAAI,CAACH,MAAM,CAAC,oBAAoBG,IAArB,CAAX,EAAuC;QACxC,MAAM6B,QAAQ,GAAG,UAAU7B,IAA3B;QACAD,IAAI,CAAC8B,QAAD,CAAJ;QACAX,OAAO,CAAClB,IAAD,CAAP,GAAgB0B,EAAE,CAAC7B,MAAD,EAASU,IAAT,EAAeuB,IAAf,CAAlB;QACA7B,kBAAkB,CAAC4B,QAAD,EAAWA,QAAX,CAAlB;MACH;IACJ;;IACS,IAANpB,MAAM,GAAG;MACT,OAAO,KAAKE,OAAZ;IACH;;IACO,IAAJX,IAAI,GAAG;MACP,OAAO,KAAKY,KAAZ;IACH;;IACDmB,GAAG,CAACC,GAAD,EAAM;MACL,MAAMZ,IAAI,GAAG,KAAKa,WAAL,CAAiBD,GAAjB,CAAb;MACA,IAAIZ,IAAJ,EACI,OAAOA,IAAI,CAACP,WAAL,CAAiBmB,GAAjB,CAAP;IACP;;IACDC,WAAW,CAACD,GAAD,EAAM;MACb,IAAIX,OAAO,GAAG,IAAd;;MACA,OAAOA,OAAP,EAAgB;QACZ,IAAIA,OAAO,CAACR,WAAR,CAAoBe,cAApB,CAAmCI,GAAnC,CAAJ,EAA6C;UACzC,OAAOX,OAAP;QACH;;QACDA,OAAO,GAAGA,OAAO,CAACV,OAAlB;MACH;;MACD,OAAO,IAAP;IACH;;IACDuB,IAAI,CAACxB,QAAD,EAAW;MACX,IAAI,CAACA,QAAL,EACI,MAAM,IAAIJ,KAAJ,CAAU,oBAAV,CAAN;MACJ,OAAO,KAAKS,aAAL,CAAmBmB,IAAnB,CAAwB,IAAxB,EAA8BxB,QAA9B,CAAP;IACH;;IACDyB,IAAI,CAACC,QAAD,EAAWC,MAAX,EAAmB;MACnB,IAAI,OAAOD,QAAP,KAAoB,UAAxB,EAAoC;QAChC,MAAM,IAAI9B,KAAJ,CAAU,6BAA6B8B,QAAvC,CAAN;MACH;;MACD,MAAME,SAAS,GAAG,KAAKvB,aAAL,CAAmBwB,SAAnB,CAA6B,IAA7B,EAAmCH,QAAnC,EAA6CC,MAA7C,CAAlB;;MACA,MAAMjB,IAAI,GAAG,IAAb;MACA,OAAO,YAAY;QACf,OAAOA,IAAI,CAACoB,UAAL,CAAgBF,SAAhB,EAA2B,IAA3B,EAAiCG,SAAjC,EAA4CJ,MAA5C,CAAP;MACH,CAFD;IAGH;;IACDK,GAAG,CAACN,QAAD,EAAWO,SAAX,EAAsBC,SAAtB,EAAiCP,MAAjC,EAAyC;MACxCf,iBAAiB,GAAG;QAAEb,MAAM,EAAEa,iBAAV;QAA6BF,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,OAAO,KAAKL,aAAL,CAAmB8B,MAAnB,CAA0B,IAA1B,EAAgCT,QAAhC,EAA0CO,SAA1C,EAAqDC,SAArD,EAAgEP,MAAhE,CAAP;MACH,CAFD,SAGQ;QACJf,iBAAiB,GAAGA,iBAAiB,CAACb,MAAtC;MACH;IACJ;;IACD+B,UAAU,CAACJ,QAAD,EAAWO,SAAS,GAAG,IAAvB,EAA6BC,SAA7B,EAAwCP,MAAxC,EAAgD;MACtDf,iBAAiB,GAAG;QAAEb,MAAM,EAAEa,iBAAV;QAA6BF,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,IAAI;UACA,OAAO,KAAKL,aAAL,CAAmB8B,MAAnB,CAA0B,IAA1B,EAAgCT,QAAhC,EAA0CO,SAA1C,EAAqDC,SAArD,EAAgEP,MAAhE,CAAP;QACH,CAFD,CAGA,OAAOS,KAAP,EAAc;UACV,IAAI,KAAK/B,aAAL,CAAmBgC,WAAnB,CAA+B,IAA/B,EAAqCD,KAArC,CAAJ,EAAiD;YAC7C,MAAMA,KAAN;UACH;QACJ;MACJ,CATD,SAUQ;QACJxB,iBAAiB,GAAGA,iBAAiB,CAACb,MAAtC;MACH;IACJ;;IACDuC,OAAO,CAACC,IAAD,EAAON,SAAP,EAAkBC,SAAlB,EAA6B;MAChC,IAAIK,IAAI,CAAC7B,IAAL,IAAa,IAAjB,EAAuB;QACnB,MAAM,IAAId,KAAJ,CAAU,gEACZ,CAAC2C,IAAI,CAAC7B,IAAL,IAAa8B,OAAd,EAAuBlD,IADX,GACkB,eADlB,GACoC,KAAKA,IADzC,GACgD,GAD1D,CAAN;MAEH,CAJ+B,CAKhC;MACA;MACA;;;MACA,IAAIiD,IAAI,CAACE,KAAL,KAAeC,YAAf,KAAgCH,IAAI,CAACI,IAAL,KAAcC,SAAd,IAA2BL,IAAI,CAACI,IAAL,KAAcE,SAAzE,CAAJ,EAAyF;QACrF;MACH;;MACD,MAAMC,YAAY,GAAGP,IAAI,CAACE,KAAL,IAAcM,OAAnC;MACAD,YAAY,IAAIP,IAAI,CAACS,aAAL,CAAmBD,OAAnB,EAA4BE,SAA5B,CAAhB;MACAV,IAAI,CAACW,QAAL;MACA,MAAMC,YAAY,GAAGrC,YAArB;MACAA,YAAY,GAAGyB,IAAf;MACA3B,iBAAiB,GAAG;QAAEb,MAAM,EAAEa,iBAAV;QAA6BF,IAAI,EAAE;MAAnC,CAApB;;MACA,IAAI;QACA,IAAI6B,IAAI,CAACI,IAAL,IAAaE,SAAb,IAA0BN,IAAI,CAACa,IAA/B,IAAuC,CAACb,IAAI,CAACa,IAAL,CAAUC,UAAtD,EAAkE;UAC9Dd,IAAI,CAACe,QAAL,GAAgBC,SAAhB;QACH;;QACD,IAAI;UACA,OAAO,KAAKlD,aAAL,CAAmBmD,UAAnB,CAA8B,IAA9B,EAAoCjB,IAApC,EAA0CN,SAA1C,EAAqDC,SAArD,CAAP;QACH,CAFD,CAGA,OAAOE,KAAP,EAAc;UACV,IAAI,KAAK/B,aAAL,CAAmBgC,WAAnB,CAA+B,IAA/B,EAAqCD,KAArC,CAAJ,EAAiD;YAC7C,MAAMA,KAAN;UACH;QACJ;MACJ,CAZD,SAaQ;QACJ;QACA;QACA,IAAIG,IAAI,CAACE,KAAL,KAAeC,YAAf,IAA+BH,IAAI,CAACE,KAAL,KAAegB,OAAlD,EAA2D;UACvD,IAAIlB,IAAI,CAACI,IAAL,IAAaC,SAAb,IAA2BL,IAAI,CAACa,IAAL,IAAab,IAAI,CAACa,IAAL,CAAUC,UAAtD,EAAmE;YAC/DP,YAAY,IAAIP,IAAI,CAACS,aAAL,CAAmBC,SAAnB,EAA8BF,OAA9B,CAAhB;UACH,CAFD,MAGK;YACDR,IAAI,CAACW,QAAL,GAAgB,CAAhB;;YACA,KAAKQ,gBAAL,CAAsBnB,IAAtB,EAA4B,CAAC,CAA7B;;YACAO,YAAY,IACRP,IAAI,CAACS,aAAL,CAAmBN,YAAnB,EAAiCK,OAAjC,EAA0CL,YAA1C,CADJ;UAEH;QACJ;;QACD9B,iBAAiB,GAAGA,iBAAiB,CAACb,MAAtC;QACAe,YAAY,GAAGqC,YAAf;MACH;IACJ;;IACDQ,YAAY,CAACpB,IAAD,EAAO;MACf,IAAIA,IAAI,CAAC7B,IAAL,IAAa6B,IAAI,CAAC7B,IAAL,KAAc,IAA/B,EAAqC;QACjC;QACA;QACA,IAAIkD,OAAO,GAAG,IAAd;;QACA,OAAOA,OAAP,EAAgB;UACZ,IAAIA,OAAO,KAAKrB,IAAI,CAAC7B,IAArB,EAA2B;YACvB,MAAMd,KAAK,CAAE,8BAA6B,KAAKN,IAAK,8CAA6CiD,IAAI,CAAC7B,IAAL,CAAUpB,IAAK,EAArG,CAAX;UACH;;UACDsE,OAAO,GAAGA,OAAO,CAAC7D,MAAlB;QACH;MACJ;;MACDwC,IAAI,CAACS,aAAL,CAAmBa,UAAnB,EAA+BnB,YAA/B;;MACA,MAAMoB,aAAa,GAAG,EAAtB;MACAvB,IAAI,CAACwB,cAAL,GAAsBD,aAAtB;MACAvB,IAAI,CAACyB,KAAL,GAAa,IAAb;;MACA,IAAI;QACAzB,IAAI,GAAG,KAAKlC,aAAL,CAAmBsD,YAAnB,CAAgC,IAAhC,EAAsCpB,IAAtC,CAAP;MACH,CAFD,CAGA,OAAO0B,GAAP,EAAY;QACR;QACA;QACA1B,IAAI,CAACS,aAAL,CAAmBS,OAAnB,EAA4BI,UAA5B,EAAwCnB,YAAxC,EAHQ,CAIR;;;QACA,KAAKrC,aAAL,CAAmBgC,WAAnB,CAA+B,IAA/B,EAAqC4B,GAArC;;QACA,MAAMA,GAAN;MACH;;MACD,IAAI1B,IAAI,CAACwB,cAAL,KAAwBD,aAA5B,EAA2C;QACvC;QACA,KAAKJ,gBAAL,CAAsBnB,IAAtB,EAA4B,CAA5B;MACH;;MACD,IAAIA,IAAI,CAACE,KAAL,IAAcoB,UAAlB,EAA8B;QAC1BtB,IAAI,CAACS,aAAL,CAAmBC,SAAnB,EAA8BY,UAA9B;MACH;;MACD,OAAOtB,IAAP;IACH;;IACD2B,iBAAiB,CAACvC,MAAD,EAASD,QAAT,EAAmB0B,IAAnB,EAAyBe,cAAzB,EAAyC;MACtD,OAAO,KAAKR,YAAL,CAAkB,IAAIS,QAAJ,CAAaC,SAAb,EAAwB1C,MAAxB,EAAgCD,QAAhC,EAA0C0B,IAA1C,EAAgDe,cAAhD,EAAgEZ,SAAhE,CAAlB,CAAP;IACH;;IACDe,iBAAiB,CAAC3C,MAAD,EAASD,QAAT,EAAmB0B,IAAnB,EAAyBe,cAAzB,EAAyCI,YAAzC,EAAuD;MACpE,OAAO,KAAKZ,YAAL,CAAkB,IAAIS,QAAJ,CAAavB,SAAb,EAAwBlB,MAAxB,EAAgCD,QAAhC,EAA0C0B,IAA1C,EAAgDe,cAAhD,EAAgEI,YAAhE,CAAlB,CAAP;IACH;;IACDC,iBAAiB,CAAC7C,MAAD,EAASD,QAAT,EAAmB0B,IAAnB,EAAyBe,cAAzB,EAAyCI,YAAzC,EAAuD;MACpE,OAAO,KAAKZ,YAAL,CAAkB,IAAIS,QAAJ,CAAaxB,SAAb,EAAwBjB,MAAxB,EAAgCD,QAAhC,EAA0C0B,IAA1C,EAAgDe,cAAhD,EAAgEI,YAAhE,CAAlB,CAAP;IACH;;IACDE,UAAU,CAAClC,IAAD,EAAO;MACb,IAAIA,IAAI,CAAC7B,IAAL,IAAa,IAAjB,EACI,MAAM,IAAId,KAAJ,CAAU,sEACZ,CAAC2C,IAAI,CAAC7B,IAAL,IAAa8B,OAAd,EAAuBlD,IADX,GACkB,eADlB,GACoC,KAAKA,IADzC,GACgD,GAD1D,CAAN;;MAEJiD,IAAI,CAACS,aAAL,CAAmB0B,SAAnB,EAA8BzB,SAA9B,EAAyCF,OAAzC;;MACA,IAAI;QACA,KAAK1C,aAAL,CAAmBoE,UAAnB,CAA8B,IAA9B,EAAoClC,IAApC;MACH,CAFD,CAGA,OAAO0B,GAAP,EAAY;QACR;QACA1B,IAAI,CAACS,aAAL,CAAmBS,OAAnB,EAA4BiB,SAA5B;;QACA,KAAKrE,aAAL,CAAmBgC,WAAnB,CAA+B,IAA/B,EAAqC4B,GAArC;;QACA,MAAMA,GAAN;MACH;;MACD,KAAKP,gBAAL,CAAsBnB,IAAtB,EAA4B,CAAC,CAA7B;;MACAA,IAAI,CAACS,aAAL,CAAmBN,YAAnB,EAAiCgC,SAAjC;;MACAnC,IAAI,CAACW,QAAL,GAAgB,CAAhB;MACA,OAAOX,IAAP;IACH;;IACDmB,gBAAgB,CAACnB,IAAD,EAAOoC,KAAP,EAAc;MAC1B,MAAMb,aAAa,GAAGvB,IAAI,CAACwB,cAA3B;;MACA,IAAIY,KAAK,IAAI,CAAC,CAAd,EAAiB;QACbpC,IAAI,CAACwB,cAAL,GAAsB,IAAtB;MACH;;MACD,KAAK,IAAIa,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGd,aAAa,CAACe,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;QAC3Cd,aAAa,CAACc,CAAD,CAAb,CAAiBlB,gBAAjB,CAAkCnB,IAAI,CAACI,IAAvC,EAA6CgC,KAA7C;MACH;IACJ;;EApOM,CAlCK,CAwQhB;;;EACA9E,IAAI,CAACH,UAAL,GAAkBA,UAAlB;EACA,MAAMoF,WAAW,GAAG;IAChBxF,IAAI,EAAE,EADU;IAEhByF,SAAS,EAAE,CAACC,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsBC,YAAtB,KAAuCH,QAAQ,CAACI,OAAT,CAAiBF,MAAjB,EAAyBC,YAAzB,CAFlC;IAGhBE,cAAc,EAAE,CAACL,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB3C,IAAtB,KAA+ByC,QAAQ,CAACrB,YAAT,CAAsBuB,MAAtB,EAA8B3C,IAA9B,CAH/B;IAIhB+C,YAAY,EAAE,CAACN,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB3C,IAAtB,EAA4BN,SAA5B,EAAuCC,SAAvC,KAAqD8C,QAAQ,CAACxB,UAAT,CAAoB0B,MAApB,EAA4B3C,IAA5B,EAAkCN,SAAlC,EAA6CC,SAA7C,CAJnD;IAKhBqD,YAAY,EAAE,CAACP,QAAD,EAAWC,CAAX,EAAcC,MAAd,EAAsB3C,IAAtB,KAA+ByC,QAAQ,CAACP,UAAT,CAAoBS,MAApB,EAA4B3C,IAA5B;EAL7B,CAApB;;EAOA,MAAMjC,aAAN,CAAoB;IAChBR,WAAW,CAACY,IAAD,EAAO8E,cAAP,EAAuBxF,QAAvB,EAAiC;MACxC,KAAKyF,WAAL,GAAmB;QAAE,aAAa,CAAf;QAAkB,aAAa,CAA/B;QAAkC,aAAa;MAA/C,CAAnB;MACA,KAAK/E,IAAL,GAAYA,IAAZ;MACA,KAAKgF,eAAL,GAAuBF,cAAvB;MACA,KAAKG,OAAL,GAAe3F,QAAQ,KAAKA,QAAQ,IAAIA,QAAQ,CAAC4F,MAArB,GAA8B5F,QAA9B,GAAyCwF,cAAc,CAACG,OAA7D,CAAvB;MACA,KAAKE,SAAL,GAAiB7F,QAAQ,KAAKA,QAAQ,CAAC4F,MAAT,GAAkBJ,cAAlB,GAAmCA,cAAc,CAACK,SAAvD,CAAzB;MACA,KAAKC,aAAL,GACI9F,QAAQ,KAAKA,QAAQ,CAAC4F,MAAT,GAAkB,KAAKlF,IAAvB,GAA8B8E,cAAc,CAACM,aAAlD,CADZ;MAEA,KAAKC,YAAL,GACI/F,QAAQ,KAAKA,QAAQ,CAACgG,WAAT,GAAuBhG,QAAvB,GAAkCwF,cAAc,CAACO,YAAtD,CADZ;MAEA,KAAKE,cAAL,GACIjG,QAAQ,KAAKA,QAAQ,CAACgG,WAAT,GAAuBR,cAAvB,GAAwCA,cAAc,CAACS,cAA5D,CADZ;MAEA,KAAKC,kBAAL,GACIlG,QAAQ,KAAKA,QAAQ,CAACgG,WAAT,GAAuB,KAAKtF,IAA5B,GAAmC8E,cAAc,CAACU,kBAAvD,CADZ;MAEA,KAAKC,SAAL,GAAiBnG,QAAQ,KAAKA,QAAQ,CAACoG,QAAT,GAAoBpG,QAApB,GAA+BwF,cAAc,CAACW,SAAnD,CAAzB;MACA,KAAKE,WAAL,GACIrG,QAAQ,KAAKA,QAAQ,CAACoG,QAAT,GAAoBZ,cAApB,GAAqCA,cAAc,CAACa,WAAzD,CADZ;MAEA,KAAKC,eAAL,GACItG,QAAQ,KAAKA,QAAQ,CAACoG,QAAT,GAAoB,KAAK1F,IAAzB,GAAgC8E,cAAc,CAACc,eAApD,CADZ;MAEA,KAAKC,cAAL,GACIvG,QAAQ,KAAKA,QAAQ,CAACwG,aAAT,GAAyBxG,QAAzB,GAAoCwF,cAAc,CAACe,cAAxD,CADZ;MAEA,KAAKE,gBAAL,GACIzG,QAAQ,KAAKA,QAAQ,CAACwG,aAAT,GAAyBhB,cAAzB,GAA0CA,cAAc,CAACiB,gBAA9D,CADZ;MAEA,KAAKC,oBAAL,GACI1G,QAAQ,KAAKA,QAAQ,CAACwG,aAAT,GAAyB,KAAK9F,IAA9B,GAAqC8E,cAAc,CAACkB,oBAAzD,CADZ;MAEA,KAAKC,eAAL,GACI3G,QAAQ,KAAKA,QAAQ,CAACqF,cAAT,GAA0BrF,QAA1B,GAAqCwF,cAAc,CAACmB,eAAzD,CADZ;MAEA,KAAKC,iBAAL,GAAyB5G,QAAQ,KAC5BA,QAAQ,CAACqF,cAAT,GAA0BG,cAA1B,GAA2CA,cAAc,CAACoB,iBAD9B,CAAjC;MAEA,KAAKC,qBAAL,GACI7G,QAAQ,KAAKA,QAAQ,CAACqF,cAAT,GAA0B,KAAK3E,IAA/B,GAAsC8E,cAAc,CAACqB,qBAA1D,CADZ;MAEA,KAAKC,aAAL,GACI9G,QAAQ,KAAKA,QAAQ,CAACsF,YAAT,GAAwBtF,QAAxB,GAAmCwF,cAAc,CAACsB,aAAvD,CADZ;MAEA,KAAKC,eAAL,GACI/G,QAAQ,KAAKA,QAAQ,CAACsF,YAAT,GAAwBE,cAAxB,GAAyCA,cAAc,CAACuB,eAA7D,CADZ;MAEA,KAAKC,mBAAL,GACIhH,QAAQ,KAAKA,QAAQ,CAACsF,YAAT,GAAwB,KAAK5E,IAA7B,GAAoC8E,cAAc,CAACwB,mBAAxD,CADZ;MAEA,KAAKC,aAAL,GACIjH,QAAQ,KAAKA,QAAQ,CAACuF,YAAT,GAAwBvF,QAAxB,GAAmCwF,cAAc,CAACyB,aAAvD,CADZ;MAEA,KAAKC,eAAL,GACIlH,QAAQ,KAAKA,QAAQ,CAACuF,YAAT,GAAwBC,cAAxB,GAAyCA,cAAc,CAAC0B,eAA7D,CADZ;MAEA,KAAKC,mBAAL,GACInH,QAAQ,KAAKA,QAAQ,CAACuF,YAAT,GAAwB,KAAK7E,IAA7B,GAAoC8E,cAAc,CAAC2B,mBAAxD,CADZ;MAEA,KAAKC,UAAL,GAAkB,IAAlB;MACA,KAAKC,YAAL,GAAoB,IAApB;MACA,KAAKC,iBAAL,GAAyB,IAAzB;MACA,KAAKC,gBAAL,GAAwB,IAAxB;MACA,MAAMC,eAAe,GAAGxH,QAAQ,IAAIA,QAAQ,CAAC+E,SAA7C;MACA,MAAM0C,aAAa,GAAGjC,cAAc,IAAIA,cAAc,CAAC4B,UAAvD;;MACA,IAAII,eAAe,IAAIC,aAAvB,EAAsC;QAClC;QACA;QACA,KAAKL,UAAL,GAAkBI,eAAe,GAAGxH,QAAH,GAAc8E,WAA/C;QACA,KAAKuC,YAAL,GAAoB7B,cAApB;QACA,KAAK8B,iBAAL,GAAyB,IAAzB;QACA,KAAKC,gBAAL,GAAwB7G,IAAxB;;QACA,IAAI,CAACV,QAAQ,CAACqF,cAAd,EAA8B;UAC1B,KAAKsB,eAAL,GAAuB7B,WAAvB;UACA,KAAK8B,iBAAL,GAAyBpB,cAAzB;UACA,KAAKqB,qBAAL,GAA6B,KAAKnG,IAAlC;QACH;;QACD,IAAI,CAACV,QAAQ,CAACsF,YAAd,EAA4B;UACxB,KAAKwB,aAAL,GAAqBhC,WAArB;UACA,KAAKiC,eAAL,GAAuBvB,cAAvB;UACA,KAAKwB,mBAAL,GAA2B,KAAKtG,IAAhC;QACH;;QACD,IAAI,CAACV,QAAQ,CAACuF,YAAd,EAA4B;UACxB,KAAK0B,aAAL,GAAqBnC,WAArB;UACA,KAAKoC,eAAL,GAAuB1B,cAAvB;UACA,KAAK2B,mBAAL,GAA2B,KAAKzG,IAAhC;QACH;MACJ;IACJ;;IACDc,IAAI,CAACkG,UAAD,EAAa1H,QAAb,EAAuB;MACvB,OAAO,KAAK2F,OAAL,GAAe,KAAKA,OAAL,CAAaC,MAAb,CAAoB,KAAKC,SAAzB,EAAoC,KAAKnF,IAAzC,EAA+CgH,UAA/C,EAA2D1H,QAA3D,CAAf,GACH,IAAIH,IAAJ,CAAS6H,UAAT,EAAqB1H,QAArB,CADJ;IAEH;;IACD6B,SAAS,CAAC6F,UAAD,EAAahG,QAAb,EAAuBC,MAAvB,EAA+B;MACpC,OAAO,KAAKoE,YAAL,GACH,KAAKA,YAAL,CAAkBC,WAAlB,CAA8B,KAAKC,cAAnC,EAAmD,KAAKC,kBAAxD,EAA4EwB,UAA5E,EAAwFhG,QAAxF,EAAkGC,MAAlG,CADG,GAEHD,QAFJ;IAGH;;IACDS,MAAM,CAACuF,UAAD,EAAahG,QAAb,EAAuBO,SAAvB,EAAkCC,SAAlC,EAA6CP,MAA7C,EAAqD;MACvD,OAAO,KAAKwE,SAAL,GAAiB,KAAKA,SAAL,CAAeC,QAAf,CAAwB,KAAKC,WAA7B,EAA0C,KAAKC,eAA/C,EAAgEoB,UAAhE,EAA4EhG,QAA5E,EAAsFO,SAAtF,EAAiGC,SAAjG,EAA4GP,MAA5G,CAAjB,GACHD,QAAQ,CAACiG,KAAT,CAAe1F,SAAf,EAA0BC,SAA1B,CADJ;IAEH;;IACDG,WAAW,CAACqF,UAAD,EAAatF,KAAb,EAAoB;MAC3B,OAAO,KAAKmE,cAAL,GACH,KAAKA,cAAL,CAAoBC,aAApB,CAAkC,KAAKC,gBAAvC,EAAyD,KAAKC,oBAA9D,EAAoFgB,UAApF,EAAgGtF,KAAhG,CADG,GAEH,IAFJ;IAGH;;IACDuB,YAAY,CAAC+D,UAAD,EAAanF,IAAb,EAAmB;MAC3B,IAAIqF,UAAU,GAAGrF,IAAjB;;MACA,IAAI,KAAKoE,eAAT,EAA0B;QACtB,IAAI,KAAKS,UAAT,EAAqB;UACjBQ,UAAU,CAAC7D,cAAX,CAA0B8D,IAA1B,CAA+B,KAAKP,iBAApC;QACH,CAHqB,CAItB;;;QACAM,UAAU,GAAG,KAAKjB,eAAL,CAAqBtB,cAArB,CAAoC,KAAKuB,iBAAzC,EAA4D,KAAKC,qBAAjE,EAAwFa,UAAxF,EAAoGnF,IAApG,CAAb,CALsB,CAMtB;;QACA,IAAI,CAACqF,UAAL,EACIA,UAAU,GAAGrF,IAAb;MACP,CATD,MAUK;QACD,IAAIA,IAAI,CAACuF,UAAT,EAAqB;UACjBvF,IAAI,CAACuF,UAAL,CAAgBvF,IAAhB;QACH,CAFD,MAGK,IAAIA,IAAI,CAACI,IAAL,IAAa0B,SAAjB,EAA4B;UAC7BH,iBAAiB,CAAC3B,IAAD,CAAjB;QACH,CAFI,MAGA;UACD,MAAM,IAAI3C,KAAJ,CAAU,6BAAV,CAAN;QACH;MACJ;;MACD,OAAOgI,UAAP;IACH;;IACDpE,UAAU,CAACkE,UAAD,EAAanF,IAAb,EAAmBN,SAAnB,EAA8BC,SAA9B,EAAyC;MAC/C,OAAO,KAAK4E,aAAL,GAAqB,KAAKA,aAAL,CAAmBxB,YAAnB,CAAgC,KAAKyB,eAArC,EAAsD,KAAKC,mBAA3D,EAAgFU,UAAhF,EAA4FnF,IAA5F,EAAkGN,SAAlG,EAA6GC,SAA7G,CAArB,GACHK,IAAI,CAACb,QAAL,CAAciG,KAAd,CAAoB1F,SAApB,EAA+BC,SAA/B,CADJ;IAEH;;IACDuC,UAAU,CAACiD,UAAD,EAAanF,IAAb,EAAmB;MACzB,IAAIwF,KAAJ;;MACA,IAAI,KAAKd,aAAT,EAAwB;QACpBc,KAAK,GAAG,KAAKd,aAAL,CAAmB1B,YAAnB,CAAgC,KAAK2B,eAArC,EAAsD,KAAKC,mBAA3D,EAAgFO,UAAhF,EAA4FnF,IAA5F,CAAR;MACH,CAFD,MAGK;QACD,IAAI,CAACA,IAAI,CAACe,QAAV,EAAoB;UAChB,MAAM1D,KAAK,CAAC,wBAAD,CAAX;QACH;;QACDmI,KAAK,GAAGxF,IAAI,CAACe,QAAL,CAAcf,IAAd,CAAR;MACH;;MACD,OAAOwF,KAAP;IACH;;IACD3C,OAAO,CAACsC,UAAD,EAAaM,OAAb,EAAsB;MACzB;MACA;MACA,IAAI;QACA,KAAKZ,UAAL,IACI,KAAKA,UAAL,CAAgBrC,SAAhB,CAA0B,KAAKsC,YAA/B,EAA6C,KAAKE,gBAAlD,EAAoEG,UAApE,EAAgFM,OAAhF,CADJ;MAEH,CAHD,CAIA,OAAO/D,GAAP,EAAY;QACR,KAAK5B,WAAL,CAAiBqF,UAAjB,EAA6BzD,GAA7B;MACH;IACJ,CAhJe,CAiJhB;;;IACAP,gBAAgB,CAACf,IAAD,EAAOgC,KAAP,EAAc;MAC1B,MAAMsD,MAAM,GAAG,KAAKxC,WAApB;MACA,MAAMyC,IAAI,GAAGD,MAAM,CAACtF,IAAD,CAAnB;MACA,MAAMwF,IAAI,GAAGF,MAAM,CAACtF,IAAD,CAAN,GAAeuF,IAAI,GAAGvD,KAAnC;;MACA,IAAIwD,IAAI,GAAG,CAAX,EAAc;QACV,MAAM,IAAIvI,KAAJ,CAAU,0CAAV,CAAN;MACH;;MACD,IAAIsI,IAAI,IAAI,CAAR,IAAaC,IAAI,IAAI,CAAzB,EAA4B;QACxB,MAAMH,OAAO,GAAG;UACZ3D,SAAS,EAAE4D,MAAM,CAAC,WAAD,CAAN,GAAsB,CADrB;UAEZpF,SAAS,EAAEoF,MAAM,CAAC,WAAD,CAAN,GAAsB,CAFrB;UAGZrF,SAAS,EAAEqF,MAAM,CAAC,WAAD,CAAN,GAAsB,CAHrB;UAIZG,MAAM,EAAEzF;QAJI,CAAhB;QAMA,KAAKyC,OAAL,CAAa,KAAK1E,IAAlB,EAAwBsH,OAAxB;MACH;IACJ;;EAlKe;;EAoKpB,MAAM5D,QAAN,CAAe;IACXtE,WAAW,CAAC6C,IAAD,EAAOhB,MAAP,EAAeD,QAAf,EAAyB2G,OAAzB,EAAkCP,UAAlC,EAA8CxE,QAA9C,EAAwD;MAC/D;MACA,KAAKU,KAAL,GAAa,IAAb;MACA,KAAKd,QAAL,GAAgB,CAAhB,CAH+D,CAI/D;;MACA,KAAKa,cAAL,GAAsB,IAAtB,CAL+D,CAM/D;;MACA,KAAKuE,MAAL,GAAc,cAAd;MACA,KAAK3F,IAAL,GAAYA,IAAZ;MACA,KAAKhB,MAAL,GAAcA,MAAd;MACA,KAAKyB,IAAL,GAAYiF,OAAZ;MACA,KAAKP,UAAL,GAAkBA,UAAlB;MACA,KAAKxE,QAAL,GAAgBA,QAAhB;;MACA,IAAI,CAAC5B,QAAL,EAAe;QACX,MAAM,IAAI9B,KAAJ,CAAU,yBAAV,CAAN;MACH;;MACD,KAAK8B,QAAL,GAAgBA,QAAhB;MACA,MAAM6G,IAAI,GAAG,IAAb,CAjB+D,CAkB/D;;MACA,IAAI5F,IAAI,KAAKC,SAAT,IAAsByF,OAAtB,IAAiCA,OAAO,CAACG,IAA7C,EAAmD;QAC/C,KAAKrG,MAAL,GAAciC,QAAQ,CAACZ,UAAvB;MACH,CAFD,MAGK;QACD,KAAKrB,MAAL,GAAc,YAAY;UACtB,OAAOiC,QAAQ,CAACZ,UAAT,CAAoBiF,IAApB,CAAyBtJ,MAAzB,EAAiCoJ,IAAjC,EAAuC,IAAvC,EAA6CxG,SAA7C,CAAP;QACH,CAFD;MAGH;IACJ;;IACgB,OAAVyB,UAAU,CAACjB,IAAD,EAAO2C,MAAP,EAAewD,IAAf,EAAqB;MAClC,IAAI,CAACnG,IAAL,EAAW;QACPA,IAAI,GAAG,IAAP;MACH;;MACDoG,yBAAyB;;MACzB,IAAI;QACApG,IAAI,CAACW,QAAL;QACA,OAAOX,IAAI,CAAC7B,IAAL,CAAU4B,OAAV,CAAkBC,IAAlB,EAAwB2C,MAAxB,EAAgCwD,IAAhC,CAAP;MACH,CAHD,SAIQ;QACJ,IAAIC,yBAAyB,IAAI,CAAjC,EAAoC;UAChCC,mBAAmB;QACtB;;QACDD,yBAAyB;MAC5B;IACJ;;IACO,IAAJjI,IAAI,GAAG;MACP,OAAO,KAAKsD,KAAZ;IACH;;IACQ,IAALvB,KAAK,GAAG;MACR,OAAO,KAAK6F,MAAZ;IACH;;IACDO,qBAAqB,GAAG;MACpB,KAAK7F,aAAL,CAAmBN,YAAnB,EAAiCmB,UAAjC;IACH,CArDU,CAsDX;;;IACAb,aAAa,CAAC8F,OAAD,EAAUC,UAAV,EAAsBC,UAAtB,EAAkC;MAC3C,IAAI,KAAKV,MAAL,KAAgBS,UAAhB,IAA8B,KAAKT,MAAL,KAAgBU,UAAlD,EAA8D;QAC1D,KAAKV,MAAL,GAAcQ,OAAd;;QACA,IAAIA,OAAO,IAAIpG,YAAf,EAA6B;UACzB,KAAKqB,cAAL,GAAsB,IAAtB;QACH;MACJ,CALD,MAMK;QACD,MAAM,IAAInE,KAAJ,CAAW,GAAE,KAAK+C,IAAK,KAAI,KAAKhB,MAAO,6BAA4BmH,OAAQ,uBAAsBC,UAAW,IAAGC,UAAU,GAAG,WAAWA,UAAX,GAAwB,IAA3B,GAAkC,EAAG,UAAS,KAAKV,MAAO,IAAnL,CAAN;MACH;IACJ;;IACDW,QAAQ,GAAG;MACP,IAAI,KAAK7F,IAAL,IAAa,OAAO,KAAKA,IAAL,CAAU8F,QAAjB,KAA8B,WAA/C,EAA4D;QACxD,OAAO,KAAK9F,IAAL,CAAU8F,QAAV,CAAmBD,QAAnB,EAAP;MACH,CAFD,MAGK;QACD,OAAOE,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BR,IAA1B,CAA+B,IAA/B,CAAP;MACH;IACJ,CAzEU,CA0EX;IACA;;;IACAY,MAAM,GAAG;MACL,OAAO;QACH1G,IAAI,EAAE,KAAKA,IADR;QAEHF,KAAK,EAAE,KAAKA,KAFT;QAGHd,MAAM,EAAE,KAAKA,MAHV;QAIHjB,IAAI,EAAE,KAAKA,IAAL,CAAUpB,IAJb;QAKH4D,QAAQ,EAAE,KAAKA;MALZ,CAAP;IAOH;;EApFU,CArbC,CA2gBhB;EACA;EACA;EACA;EACA;;;EACA,MAAMoG,gBAAgB,GAAG5J,UAAU,CAAC,YAAD,CAAnC;;EACA,MAAM6J,aAAa,GAAG7J,UAAU,CAAC,SAAD,CAAhC;;EACA,MAAM8J,UAAU,GAAG9J,UAAU,CAAC,MAAD,CAA7B;;EACA,IAAI+J,eAAe,GAAG,EAAtB;EACA,IAAIC,yBAAyB,GAAG,KAAhC;EACA,IAAIC,2BAAJ;;EACA,SAASC,uBAAT,CAAiCC,IAAjC,EAAuC;IACnC,IAAI,CAACF,2BAAL,EAAkC;MAC9B,IAAIxK,MAAM,CAACoK,aAAD,CAAV,EAA2B;QACvBI,2BAA2B,GAAGxK,MAAM,CAACoK,aAAD,CAAN,CAAsBO,OAAtB,CAA8B,CAA9B,CAA9B;MACH;IACJ;;IACD,IAAIH,2BAAJ,EAAiC;MAC7B,IAAII,UAAU,GAAGJ,2BAA2B,CAACH,UAAD,CAA5C;;MACA,IAAI,CAACO,UAAL,EAAiB;QACb;QACA;QACAA,UAAU,GAAGJ,2BAA2B,CAAC,MAAD,CAAxC;MACH;;MACDI,UAAU,CAACtB,IAAX,CAAgBkB,2BAAhB,EAA6CE,IAA7C;IACH,CARD,MASK;MACD1K,MAAM,CAACmK,gBAAD,CAAN,CAAyBO,IAAzB,EAA+B,CAA/B;IACH;EACJ;;EACD,SAAS3F,iBAAT,CAA2B3B,IAA3B,EAAiC;IAC7B;IACA;IACA,IAAIoG,yBAAyB,KAAK,CAA9B,IAAmCc,eAAe,CAAC5E,MAAhB,KAA2B,CAAlE,EAAqE;MACjE;MACA+E,uBAAuB,CAAChB,mBAAD,CAAvB;IACH;;IACDrG,IAAI,IAAIkH,eAAe,CAAC5B,IAAhB,CAAqBtF,IAArB,CAAR;EACH;;EACD,SAASqG,mBAAT,GAA+B;IAC3B,IAAI,CAACc,yBAAL,EAAgC;MAC5BA,yBAAyB,GAAG,IAA5B;;MACA,OAAOD,eAAe,CAAC5E,MAAvB,EAA+B;QAC3B,MAAMmF,KAAK,GAAGP,eAAd;QACAA,eAAe,GAAG,EAAlB;;QACA,KAAK,IAAI7E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoF,KAAK,CAACnF,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;UACnC,MAAMrC,IAAI,GAAGyH,KAAK,CAACpF,CAAD,CAAlB;;UACA,IAAI;YACArC,IAAI,CAAC7B,IAAL,CAAU4B,OAAV,CAAkBC,IAAlB,EAAwB,IAAxB,EAA8B,IAA9B;UACH,CAFD,CAGA,OAAOH,KAAP,EAAc;YACVhB,IAAI,CAAC6I,gBAAL,CAAsB7H,KAAtB;UACH;QACJ;MACJ;;MACDhB,IAAI,CAAC8I,kBAAL;;MACAR,yBAAyB,GAAG,KAA5B;IACH;EACJ,CArkBe,CAskBhB;EACA;EACA;EACA;EACA;;;EACA,MAAMlH,OAAO,GAAG;IAAElD,IAAI,EAAE;EAAR,CAAhB;EACA,MAAMoD,YAAY,GAAG,cAArB;EAAA,MAAqCmB,UAAU,GAAG,YAAlD;EAAA,MAAgEZ,SAAS,GAAG,WAA5E;EAAA,MAAyFF,OAAO,GAAG,SAAnG;EAAA,MAA8G2B,SAAS,GAAG,WAA1H;EAAA,MAAuIjB,OAAO,GAAG,SAAjJ;EACA,MAAMY,SAAS,GAAG,WAAlB;EAAA,MAA+BxB,SAAS,GAAG,WAA3C;EAAA,MAAwDD,SAAS,GAAG,WAApE;EACA,MAAMpC,OAAO,GAAG,EAAhB;EACA,MAAMY,IAAI,GAAG;IACT+I,MAAM,EAAEzK,UADC;IAET0K,gBAAgB,EAAE,MAAMxJ,iBAFf;IAGTqJ,gBAAgB,EAAEI,IAHT;IAITH,kBAAkB,EAAEG,IAJX;IAKTnG,iBAAiB,EAAEA,iBALV;IAMToG,iBAAiB,EAAE,MAAM,CAACzK,IAAI,CAACH,UAAU,CAAC,iCAAD,CAAX,CANrB;IAOT6K,gBAAgB,EAAE,MAAM,EAPf;IAQTC,iBAAiB,EAAEH,IARV;IASTI,WAAW,EAAE,MAAMJ,IATV;IAUTK,aAAa,EAAE,MAAM,EAVZ;IAWTC,SAAS,EAAE,MAAMN,IAXR;IAYTO,cAAc,EAAE,MAAMP,IAZb;IAaTQ,mBAAmB,EAAE,MAAMR,IAblB;IAcTS,UAAU,EAAE,MAAM,KAdT;IAeTC,gBAAgB,EAAE,MAAMxH,SAff;IAgBTyH,oBAAoB,EAAE,MAAMX,IAhBnB;IAiBTY,8BAA8B,EAAE,MAAM1H,SAjB7B;IAkBT2H,YAAY,EAAE,MAAM3H,SAlBX;IAmBT4H,UAAU,EAAE,MAAM,EAnBT;IAoBTC,UAAU,EAAE,MAAMf,IApBT;IAqBTgB,mBAAmB,EAAE,MAAMhB,IArBlB;IAsBTiB,gBAAgB,EAAE,MAAM,EAtBf;IAuBTC,qBAAqB,EAAE,MAAMlB,IAvBpB;IAwBTmB,iBAAiB,EAAE,MAAMnB,IAxBhB;IAyBToB,cAAc,EAAE,MAAMpB,IAzBb;IA0BTT,uBAAuB,EAAEA;EA1BhB,CAAb;EA4BA,IAAIhJ,iBAAiB,GAAG;IAAEb,MAAM,EAAE,IAAV;IAAgBW,IAAI,EAAE,IAAIb,IAAJ,CAAS,IAAT,EAAe,IAAf;EAAtB,CAAxB;EACA,IAAIiB,YAAY,GAAG,IAAnB;EACA,IAAI6H,yBAAyB,GAAG,CAAhC;;EACA,SAAS0B,IAAT,GAAgB,CAAG;;EACnB9K,kBAAkB,CAAC,MAAD,EAAS,MAAT,CAAlB;EACA,OAAOJ,MAAM,CAAC,MAAD,CAAN,GAAiBU,IAAxB;AACH,CAjnBD,EAinBI,OAAO6L,MAAP,KAAkB,WAAlB,IAAiCA,MAAjC,IAA2C,OAAOnD,IAAP,KAAgB,WAAhB,IAA+BA,IAA1E,IAAkFpJ,MAjnBtF;AAmnBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAM8L,8BAA8B,GAAG9B,MAAM,CAACwC,wBAA9C;AACA;;AACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAApC;AACA;;AACA,MAAMC,oBAAoB,GAAG1C,MAAM,CAAC2C,cAApC;AACA;;AACA,MAAMZ,YAAY,GAAG/B,MAAM,CAAC4C,MAA5B;AACA;;AACA,MAAMZ,UAAU,GAAGa,KAAK,CAAC5C,SAAN,CAAgB6C,KAAnC;AACA;;AACA,MAAMC,sBAAsB,GAAG,kBAA/B;AACA;;AACA,MAAMC,yBAAyB,GAAG,qBAAlC;AACA;;AACA,MAAMC,8BAA8B,GAAGvM,IAAI,CAACH,UAAL,CAAgBwM,sBAAhB,CAAvC;AACA;;;AACA,MAAMG,iCAAiC,GAAGxM,IAAI,CAACH,UAAL,CAAgByM,yBAAhB,CAA1C;AACA;;;AACA,MAAMG,QAAQ,GAAG,MAAjB;AACA;;AACA,MAAMC,SAAS,GAAG,OAAlB;AACA;;AACA,MAAMC,kBAAkB,GAAG3M,IAAI,CAACH,UAAL,CAAgB,EAAhB,CAA3B;;AACA,SAAS2L,mBAAT,CAA6B3J,QAA7B,EAAuCC,MAAvC,EAA+C;EAC3C,OAAO9B,IAAI,CAACc,OAAL,CAAac,IAAb,CAAkBC,QAAlB,EAA4BC,MAA5B,CAAP;AACH;;AACD,SAAS8K,gCAAT,CAA0C9K,MAA1C,EAAkDD,QAAlD,EAA4D0B,IAA5D,EAAkEe,cAAlE,EAAkFI,YAAlF,EAAgG;EAC5F,OAAO1E,IAAI,CAACc,OAAL,CAAa2D,iBAAb,CAA+B3C,MAA/B,EAAuCD,QAAvC,EAAiD0B,IAAjD,EAAuDe,cAAvD,EAAuEI,YAAvE,CAAP;AACH;;AACD,MAAMmI,UAAU,GAAG7M,IAAI,CAACH,UAAxB;AACA,MAAMiN,cAAc,GAAG,OAAOjB,MAAP,KAAkB,WAAzC;AACA,MAAMkB,cAAc,GAAGD,cAAc,GAAGjB,MAAH,GAAYnI,SAAjD;;AACA,MAAMsJ,OAAO,GAAGF,cAAc,IAAIC,cAAlB,IAAoC,OAAOrE,IAAP,KAAgB,QAAhB,IAA4BA,IAAhE,IAAwEpJ,MAAxF;;AACA,MAAM2N,gBAAgB,GAAG,iBAAzB;;AACA,SAASpC,aAAT,CAAuBhC,IAAvB,EAA6B/G,MAA7B,EAAqC;EACjC,KAAK,IAAIiD,CAAC,GAAG8D,IAAI,CAAC7D,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;IACvC,IAAI,OAAO8D,IAAI,CAAC9D,CAAD,CAAX,KAAmB,UAAvB,EAAmC;MAC/B8D,IAAI,CAAC9D,CAAD,CAAJ,GAAUyG,mBAAmB,CAAC3C,IAAI,CAAC9D,CAAD,CAAL,EAAUjD,MAAM,GAAG,GAAT,GAAeiD,CAAzB,CAA7B;IACH;EACJ;;EACD,OAAO8D,IAAP;AACH;;AACD,SAASqE,cAAT,CAAwB3D,SAAxB,EAAmC4D,OAAnC,EAA4C;EACxC,MAAMrL,MAAM,GAAGyH,SAAS,CAACtJ,WAAV,CAAsB,MAAtB,CAAf;;EACA,KAAK,IAAI8E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoI,OAAO,CAACnI,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;IACrC,MAAMtF,IAAI,GAAG0N,OAAO,CAACpI,CAAD,CAApB;IACA,MAAMI,QAAQ,GAAGoE,SAAS,CAAC9J,IAAD,CAA1B;;IACA,IAAI0F,QAAJ,EAAc;MACV,MAAMiI,aAAa,GAAGhC,8BAA8B,CAAC7B,SAAD,EAAY9J,IAAZ,CAApD;;MACA,IAAI,CAAC4N,kBAAkB,CAACD,aAAD,CAAvB,EAAwC;QACpC;MACH;;MACD7D,SAAS,CAAC9J,IAAD,CAAT,GAAkB,CAAE0F,QAAD,IAAc;QAC7B,MAAMmI,OAAO,GAAG,YAAY;UACxB,OAAOnI,QAAQ,CAAC2C,KAAT,CAAe,IAAf,EAAqB+C,aAAa,CAAC3I,SAAD,EAAYJ,MAAM,GAAG,GAAT,GAAerC,IAA3B,CAAlC,CAAP;QACH,CAFD;;QAGAiM,qBAAqB,CAAC4B,OAAD,EAAUnI,QAAV,CAArB;QACA,OAAOmI,OAAP;MACH,CANiB,EAMfnI,QANe,CAAlB;IAOH;EACJ;AACJ;;AACD,SAASkI,kBAAT,CAA4BE,YAA5B,EAA0C;EACtC,IAAI,CAACA,YAAL,EAAmB;IACf,OAAO,IAAP;EACH;;EACD,IAAIA,YAAY,CAACC,QAAb,KAA0B,KAA9B,EAAqC;IACjC,OAAO,KAAP;EACH;;EACD,OAAO,EAAE,OAAOD,YAAY,CAAC/L,GAApB,KAA4B,UAA5B,IAA0C,OAAO+L,YAAY,CAACE,GAApB,KAA4B,WAAxE,CAAP;AACH;;AACD,MAAMC,WAAW,GAAI,OAAOC,iBAAP,KAA6B,WAA7B,IAA4CjF,IAAI,YAAYiF,iBAAjF,C,CACA;AACA;;AACA,MAAMC,MAAM,GAAI,EAAE,QAAQZ,OAAV,KAAsB,OAAOA,OAAO,CAACa,OAAf,KAA2B,WAAjD,IACZ,GAAGzE,QAAH,CAAYR,IAAZ,CAAiBoE,OAAO,CAACa,OAAzB,MAAsC,kBAD1C;AAEA,MAAMC,SAAS,GAAG,CAACF,MAAD,IAAW,CAACF,WAAZ,IAA2B,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAD,CAAlC,CAA9C,C,CACA;AACA;AACA;;AACA,MAAMgB,KAAK,GAAG,OAAOf,OAAO,CAACa,OAAf,KAA2B,WAA3B,IACV,GAAGzE,QAAH,CAAYR,IAAZ,CAAiBoE,OAAO,CAACa,OAAzB,MAAsC,kBAD5B,IACkD,CAACH,WADnD,IAEV,CAAC,EAAEZ,cAAc,IAAIC,cAAc,CAAC,aAAD,CAAlC,CAFL;AAGA,MAAMiB,sBAAsB,GAAG,EAA/B;;AACA,MAAMC,MAAM,GAAG,UAAUC,KAAV,EAAiB;EAC5B;EACA;EACAA,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAzB;;EACA,IAAI,CAACA,KAAL,EAAY;IACR;EACH;;EACD,IAAIC,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACpL,IAAP,CAA5C;;EACA,IAAI,CAACqL,eAAL,EAAsB;IAClBA,eAAe,GAAGH,sBAAsB,CAACE,KAAK,CAACpL,IAAP,CAAtB,GAAqC+J,UAAU,CAAC,gBAAgBqB,KAAK,CAACpL,IAAvB,CAAjE;EACH;;EACD,MAAMuC,MAAM,GAAG,QAAQ6I,KAAK,CAAC7I,MAAd,IAAwB2H,OAAvC;EACA,MAAMoB,QAAQ,GAAG/I,MAAM,CAAC8I,eAAD,CAAvB;EACA,IAAIE,MAAJ;;EACA,IAAIP,SAAS,IAAIzI,MAAM,KAAK0H,cAAxB,IAA0CmB,KAAK,CAACpL,IAAN,KAAe,OAA7D,EAAsE;IAClE;IACA;IACA;IACA,MAAMwL,UAAU,GAAGJ,KAAnB;IACAG,MAAM,GAAGD,QAAQ,IACbA,QAAQ,CAACxF,IAAT,CAAc,IAAd,EAAoB0F,UAAU,CAACC,OAA/B,EAAwCD,UAAU,CAACE,QAAnD,EAA6DF,UAAU,CAACG,MAAxE,EAAgFH,UAAU,CAACI,KAA3F,EAAkGJ,UAAU,CAAC/L,KAA7G,CADJ;;IAEA,IAAI8L,MAAM,KAAK,IAAf,EAAqB;MACjBH,KAAK,CAACS,cAAN;IACH;EACJ,CAVD,MAWK;IACDN,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACtG,KAAT,CAAe,IAAf,EAAqB5F,SAArB,CAArB;;IACA,IAAImM,MAAM,IAAI3K,SAAV,IAAuB,CAAC2K,MAA5B,EAAoC;MAChCH,KAAK,CAACS,cAAN;IACH;EACJ;;EACD,OAAON,MAAP;AACH,CAhCD;;AAiCA,SAASO,aAAT,CAAuBC,GAAvB,EAA4BC,IAA5B,EAAkCvF,SAAlC,EAA6C;EACzC,IAAIwF,IAAI,GAAG3D,8BAA8B,CAACyD,GAAD,EAAMC,IAAN,CAAzC;;EACA,IAAI,CAACC,IAAD,IAASxF,SAAb,EAAwB;IACpB;IACA,MAAM6D,aAAa,GAAGhC,8BAA8B,CAAC7B,SAAD,EAAYuF,IAAZ,CAApD;;IACA,IAAI1B,aAAJ,EAAmB;MACf2B,IAAI,GAAG;QAAEC,UAAU,EAAE,IAAd;QAAoBC,YAAY,EAAE;MAAlC,CAAP;IACH;EACJ,CARwC,CASzC;EACA;;;EACA,IAAI,CAACF,IAAD,IAAS,CAACA,IAAI,CAACE,YAAnB,EAAiC;IAC7B;EACH;;EACD,MAAMC,mBAAmB,GAAGrC,UAAU,CAAC,OAAOiC,IAAP,GAAc,SAAf,CAAtC;;EACA,IAAID,GAAG,CAACxN,cAAJ,CAAmB6N,mBAAnB,KAA2CL,GAAG,CAACK,mBAAD,CAAlD,EAAyE;IACrE;EACH,CAjBwC,CAkBzC;EACA;EACA;EACA;EACA;;;EACA,OAAOH,IAAI,CAACvB,QAAZ;EACA,OAAOuB,IAAI,CAAC7G,KAAZ;EACA,MAAMiH,eAAe,GAAGJ,IAAI,CAACvN,GAA7B;EACA,MAAM4N,eAAe,GAAGL,IAAI,CAACtB,GAA7B,CA1ByC,CA2BzC;;EACA,MAAM4B,SAAS,GAAGP,IAAI,CAAC1C,KAAL,CAAW,CAAX,CAAlB;EACA,IAAI+B,eAAe,GAAGH,sBAAsB,CAACqB,SAAD,CAA5C;;EACA,IAAI,CAAClB,eAAL,EAAsB;IAClBA,eAAe,GAAGH,sBAAsB,CAACqB,SAAD,CAAtB,GAAoCxC,UAAU,CAAC,gBAAgBwC,SAAjB,CAAhE;EACH;;EACDN,IAAI,CAACtB,GAAL,GAAW,UAAU6B,QAAV,EAAoB;IAC3B;IACA;IACA,IAAIjK,MAAM,GAAG,IAAb;;IACA,IAAI,CAACA,MAAD,IAAWwJ,GAAG,KAAK7B,OAAvB,EAAgC;MAC5B3H,MAAM,GAAG2H,OAAT;IACH;;IACD,IAAI,CAAC3H,MAAL,EAAa;MACT;IACH;;IACD,MAAMkK,aAAa,GAAGlK,MAAM,CAAC8I,eAAD,CAA5B;;IACA,IAAI,OAAOoB,aAAP,KAAyB,UAA7B,EAAyC;MACrClK,MAAM,CAACmK,mBAAP,CAA2BH,SAA3B,EAAsCpB,MAAtC;IACH,CAb0B,CAc3B;IACA;;;IACAmB,eAAe,IAAIA,eAAe,CAACxG,IAAhB,CAAqBvD,MAArB,EAA6B,IAA7B,CAAnB;IACAA,MAAM,CAAC8I,eAAD,CAAN,GAA0BmB,QAA1B;;IACA,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;MAChCjK,MAAM,CAACoK,gBAAP,CAAwBJ,SAAxB,EAAmCpB,MAAnC,EAA2C,KAA3C;IACH;EACJ,CArBD,CAjCyC,CAuDzC;EACA;;;EACAc,IAAI,CAACvN,GAAL,GAAW,YAAY;IACnB;IACA;IACA,IAAI6D,MAAM,GAAG,IAAb;;IACA,IAAI,CAACA,MAAD,IAAWwJ,GAAG,KAAK7B,OAAvB,EAAgC;MAC5B3H,MAAM,GAAG2H,OAAT;IACH;;IACD,IAAI,CAAC3H,MAAL,EAAa;MACT,OAAO,IAAP;IACH;;IACD,MAAM+I,QAAQ,GAAG/I,MAAM,CAAC8I,eAAD,CAAvB;;IACA,IAAIC,QAAJ,EAAc;MACV,OAAOA,QAAP;IACH,CAFD,MAGK,IAAIe,eAAJ,EAAqB;MACtB;MACA;MACA;MACA;MACA;MACA;MACA,IAAIjH,KAAK,GAAGiH,eAAe,CAACvG,IAAhB,CAAqB,IAArB,CAAZ;;MACA,IAAIV,KAAJ,EAAW;QACP6G,IAAI,CAACtB,GAAL,CAAS7E,IAAT,CAAc,IAAd,EAAoBV,KAApB;;QACA,IAAI,OAAO7C,MAAM,CAAC4H,gBAAD,CAAb,KAAoC,UAAxC,EAAoD;UAChD5H,MAAM,CAACqK,eAAP,CAAuBZ,IAAvB;QACH;;QACD,OAAO5G,KAAP;MACH;IACJ;;IACD,OAAO,IAAP;EACH,CA/BD;;EAgCAiD,oBAAoB,CAAC0D,GAAD,EAAMC,IAAN,EAAYC,IAAZ,CAApB;EACAF,GAAG,CAACK,mBAAD,CAAH,GAA2B,IAA3B;AACH;;AACD,SAASvE,iBAAT,CAA2BkE,GAA3B,EAAgCtO,UAAhC,EAA4CgJ,SAA5C,EAAuD;EACnD,IAAIhJ,UAAJ,EAAgB;IACZ,KAAK,IAAIwE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGxE,UAAU,CAACyE,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;MACxC6J,aAAa,CAACC,GAAD,EAAM,OAAOtO,UAAU,CAACwE,CAAD,CAAvB,EAA4BwE,SAA5B,CAAb;IACH;EACJ,CAJD,MAKK;IACD,MAAMoG,YAAY,GAAG,EAArB;;IACA,KAAK,MAAMb,IAAX,IAAmBD,GAAnB,EAAwB;MACpB,IAAIC,IAAI,CAAC1C,KAAL,CAAW,CAAX,EAAc,CAAd,KAAoB,IAAxB,EAA8B;QAC1BuD,YAAY,CAAC3H,IAAb,CAAkB8G,IAAlB;MACH;IACJ;;IACD,KAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,YAAY,CAAC3K,MAAjC,EAAyC4K,CAAC,EAA1C,EAA8C;MAC1ChB,aAAa,CAACC,GAAD,EAAMc,YAAY,CAACC,CAAD,CAAlB,EAAuBrG,SAAvB,CAAb;IACH;EACJ;AACJ;;AACD,MAAMsG,mBAAmB,GAAGhD,UAAU,CAAC,kBAAD,CAAtC,C,CACA;;AACA,SAAStB,UAAT,CAAoBuE,SAApB,EAA+B;EAC3B,MAAMC,aAAa,GAAG/C,OAAO,CAAC8C,SAAD,CAA7B;EACA,IAAI,CAACC,aAAL,EACI,OAHuB,CAI3B;;EACA/C,OAAO,CAACH,UAAU,CAACiD,SAAD,CAAX,CAAP,GAAiCC,aAAjC;;EACA/C,OAAO,CAAC8C,SAAD,CAAP,GAAqB,YAAY;IAC7B,MAAME,CAAC,GAAGnF,aAAa,CAAC3I,SAAD,EAAY4N,SAAZ,CAAvB;;IACA,QAAQE,CAAC,CAAChL,MAAV;MACI,KAAK,CAAL;QACI,KAAK6K,mBAAL,IAA4B,IAAIE,aAAJ,EAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKF,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,EAA8BA,CAAC,CAAC,CAAD,CAA/B,CAA5B;QACA;;MACJ,KAAK,CAAL;QACI,KAAKH,mBAAL,IAA4B,IAAIE,aAAJ,CAAkBC,CAAC,CAAC,CAAD,CAAnB,EAAwBA,CAAC,CAAC,CAAD,CAAzB,EAA8BA,CAAC,CAAC,CAAD,CAA/B,EAAoCA,CAAC,CAAC,CAAD,CAArC,CAA5B;QACA;;MACJ;QACI,MAAM,IAAIjQ,KAAJ,CAAU,oBAAV,CAAN;IAjBR;EAmBH,CArBD,CAN2B,CA4B3B;;;EACA2L,qBAAqB,CAACsB,OAAO,CAAC8C,SAAD,CAAR,EAAqBC,aAArB,CAArB;EACA,MAAME,QAAQ,GAAG,IAAIF,aAAJ,CAAkB,YAAY,CAAG,CAAjC,CAAjB;EACA,IAAIjB,IAAJ;;EACA,KAAKA,IAAL,IAAamB,QAAb,EAAuB;IACnB;IACA,IAAIH,SAAS,KAAK,gBAAd,IAAkChB,IAAI,KAAK,cAA/C,EACI;;IACH,WAAUA,IAAV,EAAgB;MACb,IAAI,OAAOmB,QAAQ,CAACnB,IAAD,CAAf,KAA0B,UAA9B,EAA0C;QACtC9B,OAAO,CAAC8C,SAAD,CAAP,CAAmBvG,SAAnB,CAA6BuF,IAA7B,IAAqC,YAAY;UAC7C,OAAO,KAAKe,mBAAL,EAA0Bf,IAA1B,EAAgChH,KAAhC,CAAsC,KAAK+H,mBAAL,CAAtC,EAAiE3N,SAAjE,CAAP;QACH,CAFD;MAGH,CAJD,MAKK;QACDiJ,oBAAoB,CAAC6B,OAAO,CAAC8C,SAAD,CAAP,CAAmBvG,SAApB,EAA+BuF,IAA/B,EAAqC;UACrDrB,GAAG,EAAE,UAAUtM,EAAV,EAAc;YACf,IAAI,OAAOA,EAAP,KAAc,UAAlB,EAA8B;cAC1B,KAAK0O,mBAAL,EAA0Bf,IAA1B,IAAkCtD,mBAAmB,CAACrK,EAAD,EAAK2O,SAAS,GAAG,GAAZ,GAAkBhB,IAAvB,CAArD,CAD0B,CAE1B;cACA;cACA;;cACApD,qBAAqB,CAAC,KAAKmE,mBAAL,EAA0Bf,IAA1B,CAAD,EAAkC3N,EAAlC,CAArB;YACH,CAND,MAOK;cACD,KAAK0O,mBAAL,EAA0Bf,IAA1B,IAAkC3N,EAAlC;YACH;UACJ,CAZoD;UAarDK,GAAG,EAAE,YAAY;YACb,OAAO,KAAKqO,mBAAL,EAA0Bf,IAA1B,CAAP;UACH;QAfoD,CAArC,CAApB;MAiBH;IACJ,CAzBA,EAyBCA,IAzBD,CAAD;EA0BH;;EACD,KAAKA,IAAL,IAAaiB,aAAb,EAA4B;IACxB,IAAIjB,IAAI,KAAK,WAAT,IAAwBiB,aAAa,CAAC1O,cAAd,CAA6ByN,IAA7B,CAA5B,EAAgE;MAC5D9B,OAAO,CAAC8C,SAAD,CAAP,CAAmBhB,IAAnB,IAA2BiB,aAAa,CAACjB,IAAD,CAAxC;IACH;EACJ;AACJ;;AACD,SAASlE,WAAT,CAAqBvF,MAArB,EAA6B5F,IAA7B,EAAmCyQ,OAAnC,EAA4C;EACxC,IAAIC,KAAK,GAAG9K,MAAZ;;EACA,OAAO8K,KAAK,IAAI,CAACA,KAAK,CAAC9O,cAAN,CAAqB5B,IAArB,CAAjB,EAA6C;IACzC0Q,KAAK,GAAGnE,oBAAoB,CAACmE,KAAD,CAA5B;EACH;;EACD,IAAI,CAACA,KAAD,IAAU9K,MAAM,CAAC5F,IAAD,CAApB,EAA4B;IACxB;IACA0Q,KAAK,GAAG9K,MAAR;EACH;;EACD,MAAM+K,YAAY,GAAGvD,UAAU,CAACpN,IAAD,CAA/B;EACA,IAAI0F,QAAQ,GAAG,IAAf;;EACA,IAAIgL,KAAK,KAAK,EAAEhL,QAAQ,GAAGgL,KAAK,CAACC,YAAD,CAAlB,KAAqC,CAACD,KAAK,CAAC9O,cAAN,CAAqB+O,YAArB,CAA3C,CAAT,EAAyF;IACrFjL,QAAQ,GAAGgL,KAAK,CAACC,YAAD,CAAL,GAAsBD,KAAK,CAAC1Q,IAAD,CAAtC,CADqF,CAErF;IACA;;IACA,MAAMsP,IAAI,GAAGoB,KAAK,IAAI/E,8BAA8B,CAAC+E,KAAD,EAAQ1Q,IAAR,CAApD;;IACA,IAAI4N,kBAAkB,CAAC0B,IAAD,CAAtB,EAA8B;MAC1B,MAAMsB,aAAa,GAAGH,OAAO,CAAC/K,QAAD,EAAWiL,YAAX,EAAyB3Q,IAAzB,CAA7B;;MACA0Q,KAAK,CAAC1Q,IAAD,CAAL,GAAc,YAAY;QACtB,OAAO4Q,aAAa,CAAC,IAAD,EAAOnO,SAAP,CAApB;MACH,CAFD;;MAGAwJ,qBAAqB,CAACyE,KAAK,CAAC1Q,IAAD,CAAN,EAAc0F,QAAd,CAArB;IACH;EACJ;;EACD,OAAOA,QAAP;AACH,C,CACD;;;AACA,SAAS4F,cAAT,CAAwB8D,GAAxB,EAA6ByB,QAA7B,EAAuCC,WAAvC,EAAoD;EAChD,IAAIC,SAAS,GAAG,IAAhB;;EACA,SAAS1M,YAAT,CAAsBpB,IAAtB,EAA4B;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAlB;;IACAA,IAAI,CAACsF,IAAL,CAAUtF,IAAI,CAACkN,KAAf,IAAwB,YAAY;MAChC/N,IAAI,CAACJ,MAAL,CAAYwF,KAAZ,CAAkB,IAAlB,EAAwB5F,SAAxB;IACH,CAFD;;IAGAsO,SAAS,CAAC1I,KAAV,CAAgBvE,IAAI,CAAC8B,MAArB,EAA6B9B,IAAI,CAACsF,IAAlC;IACA,OAAOnG,IAAP;EACH;;EACD8N,SAAS,GAAG5F,WAAW,CAACiE,GAAD,EAAMyB,QAAN,EAAiBnL,QAAD,IAAc,UAAUuD,IAAV,EAAgBG,IAAhB,EAAsB;IACvE,MAAM6H,IAAI,GAAGH,WAAW,CAAC7H,IAAD,EAAOG,IAAP,CAAxB;;IACA,IAAI6H,IAAI,CAACD,KAAL,IAAc,CAAd,IAAmB,OAAO5H,IAAI,CAAC6H,IAAI,CAACD,KAAN,CAAX,KAA4B,UAAnD,EAA+D;MAC3D,OAAO7D,gCAAgC,CAAC8D,IAAI,CAACjR,IAAN,EAAYoJ,IAAI,CAAC6H,IAAI,CAACD,KAAN,CAAhB,EAA8BC,IAA9B,EAAoC5M,YAApC,CAAvC;IACH,CAFD,MAGK;MACD;MACA,OAAOqB,QAAQ,CAAC2C,KAAT,CAAeY,IAAf,EAAqBG,IAArB,CAAP;IACH;EACJ,CATsB,CAAvB;AAUH;;AACD,SAAS6C,qBAAT,CAA+B4B,OAA/B,EAAwCqD,QAAxC,EAAkD;EAC9CrD,OAAO,CAACT,UAAU,CAAC,kBAAD,CAAX,CAAP,GAA0C8D,QAA1C;AACH;;AACD,IAAIC,kBAAkB,GAAG,KAAzB;AACA,IAAIC,QAAQ,GAAG,KAAf;;AACA,SAASC,IAAT,GAAgB;EACZ,IAAI;IACA,MAAMC,EAAE,GAAGhE,cAAc,CAACiE,SAAf,CAAyBC,SAApC;;IACA,IAAIF,EAAE,CAACG,OAAH,CAAW,OAAX,MAAwB,CAAC,CAAzB,IAA8BH,EAAE,CAACG,OAAH,CAAW,UAAX,MAA2B,CAAC,CAA9D,EAAiE;MAC7D,OAAO,IAAP;IACH;EACJ,CALD,CAMA,OAAO3O,KAAP,EAAc,CACb;;EACD,OAAO,KAAP;AACH;;AACD,SAAS0I,UAAT,GAAsB;EAClB,IAAI2F,kBAAJ,EAAwB;IACpB,OAAOC,QAAP;EACH;;EACDD,kBAAkB,GAAG,IAArB;;EACA,IAAI;IACA,MAAMG,EAAE,GAAGhE,cAAc,CAACiE,SAAf,CAAyBC,SAApC;;IACA,IAAIF,EAAE,CAACG,OAAH,CAAW,OAAX,MAAwB,CAAC,CAAzB,IAA8BH,EAAE,CAACG,OAAH,CAAW,UAAX,MAA2B,CAAC,CAA1D,IAA+DH,EAAE,CAACG,OAAH,CAAW,OAAX,MAAwB,CAAC,CAA5F,EAA+F;MAC3FL,QAAQ,GAAG,IAAX;IACH;EACJ,CALD,CAMA,OAAOtO,KAAP,EAAc,CACb;;EACD,OAAOsO,QAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA7Q,IAAI,CAACkB,YAAL,CAAkB,kBAAlB,EAAsC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACzD,MAAM/F,8BAA8B,GAAG9B,MAAM,CAACwC,wBAA9C;EACA,MAAMX,oBAAoB,GAAG7B,MAAM,CAACyC,cAApC;;EACA,SAASqF,sBAAT,CAAgCvC,GAAhC,EAAqC;IACjC,IAAIA,GAAG,IAAIA,GAAG,CAACzF,QAAJ,KAAiBE,MAAM,CAACC,SAAP,CAAiBH,QAA7C,EAAuD;MACnD,MAAM0G,SAAS,GAAGjB,GAAG,CAAC5O,WAAJ,IAAmB4O,GAAG,CAAC5O,WAAJ,CAAgBR,IAArD;MACA,OAAO,CAACqQ,SAAS,GAAGA,SAAH,GAAe,EAAzB,IAA+B,IAA/B,GAAsCuB,IAAI,CAACC,SAAL,CAAezC,GAAf,CAA7C;IACH;;IACD,OAAOA,GAAG,GAAGA,GAAG,CAACzF,QAAJ,EAAH,GAAoBE,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BR,IAA1B,CAA+BiG,GAA/B,CAA9B;EACH;;EACD,MAAMhP,UAAU,GAAGsR,GAAG,CAAC7G,MAAvB;EACA,MAAMiH,sBAAsB,GAAG,EAA/B;EACA,MAAMC,yCAAyC,GAAGlS,MAAM,CAACO,UAAU,CAAC,6CAAD,CAAX,CAAN,KAAsE,IAAxH;;EACA,MAAM6J,aAAa,GAAG7J,UAAU,CAAC,SAAD,CAAhC;;EACA,MAAM8J,UAAU,GAAG9J,UAAU,CAAC,MAAD,CAA7B;;EACA,MAAM4R,aAAa,GAAG,mBAAtB;;EACAN,GAAG,CAAC/G,gBAAJ,GAAwBsH,CAAD,IAAO;IAC1B,IAAIP,GAAG,CAAC1G,iBAAJ,EAAJ,EAA6B;MACzB,MAAMkH,SAAS,GAAGD,CAAC,IAAIA,CAAC,CAACC,SAAzB;;MACA,IAAIA,SAAJ,EAAe;QACXC,OAAO,CAACrP,KAAR,CAAc,8BAAd,EAA8CoP,SAAS,YAAY5R,KAArB,GAA6B4R,SAAS,CAACpD,OAAvC,GAAiDoD,SAA/F,EAA0G,SAA1G,EAAqHD,CAAC,CAAC7Q,IAAF,CAAOpB,IAA5H,EAAkI,SAAlI,EAA6IiS,CAAC,CAAChP,IAAF,IAAUgP,CAAC,CAAChP,IAAF,CAAOZ,MAA9J,EAAsK,UAAtK,EAAkL6P,SAAlL,EAA6LA,SAAS,YAAY5R,KAArB,GAA6B4R,SAAS,CAACE,KAAvC,GAA+CnO,SAA5O;MACH,CAFD,MAGK;QACDkO,OAAO,CAACrP,KAAR,CAAcmP,CAAd;MACH;IACJ;EACJ,CAVD;;EAWAP,GAAG,CAAC9G,kBAAJ,GAAyB,MAAM;IAC3B,OAAOkH,sBAAsB,CAACvM,MAA9B,EAAsC;MAClC,MAAM8M,oBAAoB,GAAGP,sBAAsB,CAACQ,KAAvB,EAA7B;;MACA,IAAI;QACAD,oBAAoB,CAACjR,IAArB,CAA0BoB,UAA1B,CAAqC,MAAM;UACvC,IAAI6P,oBAAoB,CAACE,aAAzB,EAAwC;YACpC,MAAMF,oBAAoB,CAACH,SAA3B;UACH;;UACD,MAAMG,oBAAN;QACH,CALD;MAMH,CAPD,CAQA,OAAOvP,KAAP,EAAc;QACV0P,wBAAwB,CAAC1P,KAAD,CAAxB;MACH;IACJ;EACJ,CAfD;;EAgBA,MAAM2P,0CAA0C,GAAGrS,UAAU,CAAC,kCAAD,CAA7D;;EACA,SAASoS,wBAAT,CAAkCP,CAAlC,EAAqC;IACjCP,GAAG,CAAC/G,gBAAJ,CAAqBsH,CAArB;;IACA,IAAI;MACA,MAAMS,OAAO,GAAGnS,IAAI,CAACkS,0CAAD,CAApB;;MACA,IAAI,OAAOC,OAAP,KAAmB,UAAvB,EAAmC;QAC/BA,OAAO,CAACvJ,IAAR,CAAa,IAAb,EAAmB8I,CAAnB;MACH;IACJ,CALD,CAMA,OAAOtN,GAAP,EAAY,CACX;EACJ;;EACD,SAASgO,UAAT,CAAoBlK,KAApB,EAA2B;IACvB,OAAOA,KAAK,IAAIA,KAAK,CAACmK,IAAtB;EACH;;EACD,SAASC,iBAAT,CAA2BpK,KAA3B,EAAkC;IAC9B,OAAOA,KAAP;EACH;;EACD,SAASqK,gBAAT,CAA0BZ,SAA1B,EAAqC;IACjC,OAAOa,gBAAgB,CAACC,MAAjB,CAAwBd,SAAxB,CAAP;EACH;;EACD,MAAMe,WAAW,GAAG7S,UAAU,CAAC,OAAD,CAA9B;;EACA,MAAM8S,WAAW,GAAG9S,UAAU,CAAC,OAAD,CAA9B;;EACA,MAAM+S,aAAa,GAAG/S,UAAU,CAAC,SAAD,CAAhC;;EACA,MAAMgT,wBAAwB,GAAGhT,UAAU,CAAC,oBAAD,CAA3C;;EACA,MAAMiT,wBAAwB,GAAGjT,UAAU,CAAC,oBAAD,CAA3C;;EACA,MAAMiC,MAAM,GAAG,cAAf;EACA,MAAMiR,UAAU,GAAG,IAAnB;EACA,MAAMC,QAAQ,GAAG,IAAjB;EACA,MAAMC,QAAQ,GAAG,KAAjB;EACA,MAAMC,iBAAiB,GAAG,CAA1B;;EACA,SAASC,YAAT,CAAsBC,OAAtB,EAA+BxQ,KAA/B,EAAsC;IAClC,OAAQyQ,CAAD,IAAO;MACV,IAAI;QACAC,cAAc,CAACF,OAAD,EAAUxQ,KAAV,EAAiByQ,CAAjB,CAAd;MACH,CAFD,CAGA,OAAOjP,GAAP,EAAY;QACRkP,cAAc,CAACF,OAAD,EAAU,KAAV,EAAiBhP,GAAjB,CAAd;MACH,CANS,CAOV;;IACH,CARD;EASH;;EACD,MAAMmP,IAAI,GAAG,YAAY;IACrB,IAAIC,SAAS,GAAG,KAAhB;IACA,OAAO,SAASC,OAAT,CAAiBC,eAAjB,EAAkC;MACrC,OAAO,YAAY;QACf,IAAIF,SAAJ,EAAe;UACX;QACH;;QACDA,SAAS,GAAG,IAAZ;QACAE,eAAe,CAAC5L,KAAhB,CAAsB,IAAtB,EAA4B5F,SAA5B;MACH,CAND;IAOH,CARD;EASH,CAXD;;EAYA,MAAMyR,UAAU,GAAG,8BAAnB;;EACA,MAAMC,yBAAyB,GAAG/T,UAAU,CAAC,kBAAD,CAA5C,CAlGyD,CAmGzD;;;EACA,SAASyT,cAAT,CAAwBF,OAAxB,EAAiCxQ,KAAjC,EAAwCsF,KAAxC,EAA+C;IAC3C,MAAM2L,WAAW,GAAGN,IAAI,EAAxB;;IACA,IAAIH,OAAO,KAAKlL,KAAhB,EAAuB;MACnB,MAAM,IAAI4L,SAAJ,CAAcH,UAAd,CAAN;IACH;;IACD,IAAIP,OAAO,CAACV,WAAD,CAAP,KAAyBK,UAA7B,EAAyC;MACrC;MACA,IAAIV,IAAI,GAAG,IAAX;;MACA,IAAI;QACA,IAAI,OAAOnK,KAAP,KAAiB,QAAjB,IAA6B,OAAOA,KAAP,KAAiB,UAAlD,EAA8D;UAC1DmK,IAAI,GAAGnK,KAAK,IAAIA,KAAK,CAACmK,IAAtB;QACH;MACJ,CAJD,CAKA,OAAOjO,GAAP,EAAY;QACRyP,WAAW,CAAC,MAAM;UACdP,cAAc,CAACF,OAAD,EAAU,KAAV,EAAiBhP,GAAjB,CAAd;QACH,CAFU,CAAX;QAGA,OAAOgP,OAAP;MACH,CAboC,CAcrC;;;MACA,IAAIxQ,KAAK,KAAKqQ,QAAV,IAAsB/K,KAAK,YAAYsK,gBAAvC,IACAtK,KAAK,CAAC7G,cAAN,CAAqBqR,WAArB,CADA,IACqCxK,KAAK,CAAC7G,cAAN,CAAqBsR,WAArB,CADrC,IAEAzK,KAAK,CAACwK,WAAD,CAAL,KAAuBK,UAF3B,EAEuC;QACnCgB,oBAAoB,CAAC7L,KAAD,CAApB;QACAoL,cAAc,CAACF,OAAD,EAAUlL,KAAK,CAACwK,WAAD,CAAf,EAA8BxK,KAAK,CAACyK,WAAD,CAAnC,CAAd;MACH,CALD,MAMK,IAAI/P,KAAK,KAAKqQ,QAAV,IAAsB,OAAOZ,IAAP,KAAgB,UAA1C,EAAsD;QACvD,IAAI;UACAA,IAAI,CAACzJ,IAAL,CAAUV,KAAV,EAAiB2L,WAAW,CAACV,YAAY,CAACC,OAAD,EAAUxQ,KAAV,CAAb,CAA5B,EAA4DiR,WAAW,CAACV,YAAY,CAACC,OAAD,EAAU,KAAV,CAAb,CAAvE;QACH,CAFD,CAGA,OAAOhP,GAAP,EAAY;UACRyP,WAAW,CAAC,MAAM;YACdP,cAAc,CAACF,OAAD,EAAU,KAAV,EAAiBhP,GAAjB,CAAd;UACH,CAFU,CAAX;QAGH;MACJ,CATI,MAUA;QACDgP,OAAO,CAACV,WAAD,CAAP,GAAuB9P,KAAvB;QACA,MAAMuH,KAAK,GAAGiJ,OAAO,CAACT,WAAD,CAArB;QACAS,OAAO,CAACT,WAAD,CAAP,GAAuBzK,KAAvB;;QACA,IAAIkL,OAAO,CAACR,aAAD,CAAP,KAA2BA,aAA/B,EAA8C;UAC1C;UACA,IAAIhQ,KAAK,KAAKoQ,QAAd,EAAwB;YACpB;YACA;YACAI,OAAO,CAACV,WAAD,CAAP,GAAuBU,OAAO,CAACN,wBAAD,CAA9B;YACAM,OAAO,CAACT,WAAD,CAAP,GAAuBS,OAAO,CAACP,wBAAD,CAA9B;UACH;QACJ,CAZA,CAaD;QACA;;;QACA,IAAIjQ,KAAK,KAAKqQ,QAAV,IAAsB/K,KAAK,YAAYnI,KAA3C,EAAkD;UAC9C;UACA,MAAMiU,KAAK,GAAGhU,IAAI,CAACgB,WAAL,IAAoBhB,IAAI,CAACgB,WAAL,CAAiBuC,IAArC,IACVvD,IAAI,CAACgB,WAAL,CAAiBuC,IAAjB,CAAsBkO,aAAtB,CADJ;;UAEA,IAAIuC,KAAJ,EAAW;YACP;YACA7I,oBAAoB,CAACjD,KAAD,EAAQ0L,yBAAR,EAAmC;cAAE3E,YAAY,EAAE,IAAhB;cAAsBD,UAAU,EAAE,KAAlC;cAAyCxB,QAAQ,EAAE,IAAnD;cAAyDtF,KAAK,EAAE8L;YAAhE,CAAnC,CAApB;UACH;QACJ;;QACD,KAAK,IAAIjP,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoF,KAAK,CAACnF,MAA1B,GAAmC;UAC/BiP,uBAAuB,CAACb,OAAD,EAAUjJ,KAAK,CAACpF,CAAC,EAAF,CAAf,EAAsBoF,KAAK,CAACpF,CAAC,EAAF,CAA3B,EAAkCoF,KAAK,CAACpF,CAAC,EAAF,CAAvC,EAA8CoF,KAAK,CAACpF,CAAC,EAAF,CAAnD,CAAvB;QACH;;QACD,IAAIoF,KAAK,CAACnF,MAAN,IAAgB,CAAhB,IAAqBpC,KAAK,IAAIqQ,QAAlC,EAA4C;UACxCG,OAAO,CAACV,WAAD,CAAP,GAAuBQ,iBAAvB;UACA,IAAIpB,oBAAoB,GAAG5J,KAA3B;;UACA,IAAI;YACA;YACA;YACA;YACA,MAAM,IAAInI,KAAJ,CAAU,4BAA4BqR,sBAAsB,CAAClJ,KAAD,CAAlD,IACXA,KAAK,IAAIA,KAAK,CAAC2J,KAAf,GAAuB,OAAO3J,KAAK,CAAC2J,KAApC,GAA4C,EADjC,CAAV,CAAN;UAEH,CAND,CAOA,OAAOzN,GAAP,EAAY;YACR0N,oBAAoB,GAAG1N,GAAvB;UACH;;UACD,IAAIoN,yCAAJ,EAA+C;YAC3C;YACA;YACAM,oBAAoB,CAACE,aAArB,GAAqC,IAArC;UACH;;UACDF,oBAAoB,CAACH,SAArB,GAAiCzJ,KAAjC;UACA4J,oBAAoB,CAACsB,OAArB,GAA+BA,OAA/B;UACAtB,oBAAoB,CAACjR,IAArB,GAA4Bb,IAAI,CAACc,OAAjC;UACAgR,oBAAoB,CAACpP,IAArB,GAA4B1C,IAAI,CAACgB,WAAjC;;UACAuQ,sBAAsB,CAACvJ,IAAvB,CAA4B8J,oBAA5B;;UACAX,GAAG,CAAC9M,iBAAJ,GAvBwC,CAuBf;QAC5B;MACJ;IACJ,CAzF0C,CA0F3C;;;IACA,OAAO+O,OAAP;EACH;;EACD,MAAMc,yBAAyB,GAAGrU,UAAU,CAAC,yBAAD,CAA5C;;EACA,SAASkU,oBAAT,CAA8BX,OAA9B,EAAuC;IACnC,IAAIA,OAAO,CAACV,WAAD,CAAP,KAAyBQ,iBAA7B,EAAgD;MAC5C;MACA;MACA;MACA;MACA;MACA,IAAI;QACA,MAAMf,OAAO,GAAGnS,IAAI,CAACkU,yBAAD,CAApB;;QACA,IAAI/B,OAAO,IAAI,OAAOA,OAAP,KAAmB,UAAlC,EAA8C;UAC1CA,OAAO,CAACvJ,IAAR,CAAa,IAAb,EAAmB;YAAE+I,SAAS,EAAEyB,OAAO,CAACT,WAAD,CAApB;YAAmCS,OAAO,EAAEA;UAA5C,CAAnB;QACH;MACJ,CALD,CAMA,OAAOhP,GAAP,EAAY,CACX;;MACDgP,OAAO,CAACV,WAAD,CAAP,GAAuBO,QAAvB;;MACA,KAAK,IAAIlO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwM,sBAAsB,CAACvM,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;QACpD,IAAIqO,OAAO,KAAK7B,sBAAsB,CAACxM,CAAD,CAAtB,CAA0BqO,OAA1C,EAAmD;UAC/C7B,sBAAsB,CAAC4C,MAAvB,CAA8BpP,CAA9B,EAAiC,CAAjC;QACH;MACJ;IACJ;EACJ;;EACD,SAASkP,uBAAT,CAAiCb,OAAjC,EAA0CvS,IAA1C,EAAgDuT,YAAhD,EAA8DC,WAA9D,EAA2EC,UAA3E,EAAuF;IACnFP,oBAAoB,CAACX,OAAD,CAApB;IACA,MAAMmB,YAAY,GAAGnB,OAAO,CAACV,WAAD,CAA5B;IACA,MAAMvN,QAAQ,GAAGoP,YAAY,GACxB,OAAOF,WAAP,KAAuB,UAAxB,GAAsCA,WAAtC,GAAoD/B,iBAD3B,GAExB,OAAOgC,UAAP,KAAsB,UAAvB,GAAqCA,UAArC,GACI/B,gBAHR;IAIA1R,IAAI,CAACwD,iBAAL,CAAuBvC,MAAvB,EAA+B,MAAM;MACjC,IAAI;QACA,MAAM0S,kBAAkB,GAAGpB,OAAO,CAACT,WAAD,CAAlC;QACA,MAAM8B,gBAAgB,GAAG,CAAC,CAACL,YAAF,IAAkBxB,aAAa,KAAKwB,YAAY,CAACxB,aAAD,CAAzE;;QACA,IAAI6B,gBAAJ,EAAsB;UAClB;UACAL,YAAY,CAACvB,wBAAD,CAAZ,GAAyC2B,kBAAzC;UACAJ,YAAY,CAACtB,wBAAD,CAAZ,GAAyCyB,YAAzC;QACH,CAPD,CAQA;;;QACA,MAAMrM,KAAK,GAAGrH,IAAI,CAACsB,GAAL,CAASgD,QAAT,EAAmBzB,SAAnB,EAA8B+Q,gBAAgB,IAAItP,QAAQ,KAAKoN,gBAAjC,IAAqDpN,QAAQ,KAAKmN,iBAAlE,GACxC,EADwC,GAExC,CAACkC,kBAAD,CAFU,CAAd;QAGAlB,cAAc,CAACc,YAAD,EAAe,IAAf,EAAqBlM,KAArB,CAAd;MACH,CAbD,CAcA,OAAO3F,KAAP,EAAc;QACV;QACA+Q,cAAc,CAACc,YAAD,EAAe,KAAf,EAAsB7R,KAAtB,CAAd;MACH;IACJ,CAnBD,EAmBG6R,YAnBH;EAoBH;;EACD,MAAMM,4BAA4B,GAAG,+CAArC;;EACA,MAAMlK,IAAI,GAAG,YAAY,CAAG,CAA5B;;EACA,MAAMmK,cAAc,GAAGrV,MAAM,CAACqV,cAA9B;;EACA,MAAMnC,gBAAN,CAAuB;IACJ,OAARpJ,QAAQ,GAAG;MACd,OAAOsL,4BAAP;IACH;;IACa,OAAPzK,OAAO,CAAC/B,KAAD,EAAQ;MAClB,OAAOoL,cAAc,CAAC,IAAI,IAAJ,CAAS,IAAT,CAAD,EAAiBN,QAAjB,EAA2B9K,KAA3B,CAArB;IACH;;IACY,OAANuK,MAAM,CAAClQ,KAAD,EAAQ;MACjB,OAAO+Q,cAAc,CAAC,IAAI,IAAJ,CAAS,IAAT,CAAD,EAAiBL,QAAjB,EAA2B1Q,KAA3B,CAArB;IACH;;IACS,OAAHqS,GAAG,CAACC,MAAD,EAAS;MACf,IAAI,CAACA,MAAD,IAAW,OAAOA,MAAM,CAACC,MAAM,CAACC,QAAR,CAAb,KAAmC,UAAlD,EAA8D;QAC1D,OAAOC,OAAO,CAACvC,MAAR,CAAe,IAAIkC,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;MACH;;MACD,MAAMM,QAAQ,GAAG,EAAjB;MACA,IAAInQ,KAAK,GAAG,CAAZ;;MACA,IAAI;QACA,KAAK,IAAIuO,CAAT,IAAcwB,MAAd,EAAsB;UAClB/P,KAAK;UACLmQ,QAAQ,CAACjN,IAAT,CAAcwK,gBAAgB,CAACvI,OAAjB,CAAyBoJ,CAAzB,CAAd;QACH;MACJ,CALD,CAMA,OAAOjP,GAAP,EAAY;QACR,OAAO4Q,OAAO,CAACvC,MAAR,CAAe,IAAIkC,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;MACH;;MACD,IAAI7P,KAAK,KAAK,CAAd,EAAiB;QACb,OAAOkQ,OAAO,CAACvC,MAAR,CAAe,IAAIkC,cAAJ,CAAmB,EAAnB,EAAuB,4BAAvB,CAAf,CAAP;MACH;;MACD,IAAIO,QAAQ,GAAG,KAAf;MACA,MAAMC,MAAM,GAAG,EAAf;MACA,OAAO,IAAI3C,gBAAJ,CAAqB,CAACvI,OAAD,EAAUwI,MAAV,KAAqB;QAC7C,KAAK,IAAI1N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkQ,QAAQ,CAACjQ,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;UACtCkQ,QAAQ,CAAClQ,CAAD,CAAR,CAAYsN,IAAZ,CAAiBgB,CAAC,IAAI;YAClB,IAAI6B,QAAJ,EAAc;cACV;YACH;;YACDA,QAAQ,GAAG,IAAX;YACAjL,OAAO,CAACoJ,CAAD,CAAP;UACH,CAND,EAMGjP,GAAG,IAAI;YACN+Q,MAAM,CAACnN,IAAP,CAAY5D,GAAZ;YACAU,KAAK;;YACL,IAAIA,KAAK,KAAK,CAAd,EAAiB;cACboQ,QAAQ,GAAG,IAAX;cACAzC,MAAM,CAAC,IAAIkC,cAAJ,CAAmBQ,MAAnB,EAA2B,4BAA3B,CAAD,CAAN;YACH;UACJ,CAbD;QAcH;MACJ,CAjBM,CAAP;IAkBH;;IAEU,OAAJC,IAAI,CAACP,MAAD,EAAS;MAChB,IAAI5K,OAAJ;MACA,IAAIwI,MAAJ;MACA,IAAIW,OAAO,GAAG,IAAI,IAAJ,CAAS,CAACiC,GAAD,EAAMC,GAAN,KAAc;QACjCrL,OAAO,GAAGoL,GAAV;QACA5C,MAAM,GAAG6C,GAAT;MACH,CAHa,CAAd;;MAIA,SAASC,SAAT,CAAmBrN,KAAnB,EAA0B;QACtB+B,OAAO,CAAC/B,KAAD,CAAP;MACH;;MACD,SAASsN,QAAT,CAAkBjT,KAAlB,EAAyB;QACrBkQ,MAAM,CAAClQ,KAAD,CAAN;MACH;;MACD,KAAK,IAAI2F,KAAT,IAAkB2M,MAAlB,EAA0B;QACtB,IAAI,CAACzC,UAAU,CAAClK,KAAD,CAAf,EAAwB;UACpBA,KAAK,GAAG,KAAK+B,OAAL,CAAa/B,KAAb,CAAR;QACH;;QACDA,KAAK,CAACmK,IAAN,CAAWkD,SAAX,EAAsBC,QAAtB;MACH;;MACD,OAAOpC,OAAP;IACH;;IACS,OAAHqC,GAAG,CAACZ,MAAD,EAAS;MACf,OAAOrC,gBAAgB,CAACkD,eAAjB,CAAiCb,MAAjC,CAAP;IACH;;IACgB,OAAVc,UAAU,CAACd,MAAD,EAAS;MACtB,MAAMe,CAAC,GAAG,QAAQ,KAAKrM,SAAL,YAA0BiJ,gBAAlC,GAAqD,IAArD,GAA4DA,gBAAtE;MACA,OAAOoD,CAAC,CAACF,eAAF,CAAkBb,MAAlB,EAA0B;QAC7BgB,YAAY,EAAG3N,KAAD,KAAY;UAAE4N,MAAM,EAAE,WAAV;UAAuB5N;QAAvB,CAAZ,CADe;QAE7B6N,aAAa,EAAG3R,GAAD,KAAU;UAAE0R,MAAM,EAAE,UAAV;UAAsBE,MAAM,EAAE5R;QAA9B,CAAV;MAFc,CAA1B,CAAP;IAIH;;IACqB,OAAfsR,eAAe,CAACb,MAAD,EAAShT,QAAT,EAAmB;MACrC,IAAIoI,OAAJ;MACA,IAAIwI,MAAJ;MACA,IAAIW,OAAO,GAAG,IAAI,IAAJ,CAAS,CAACiC,GAAD,EAAMC,GAAN,KAAc;QACjCrL,OAAO,GAAGoL,GAAV;QACA5C,MAAM,GAAG6C,GAAT;MACH,CAHa,CAAd,CAHqC,CAOrC;;MACA,IAAIW,eAAe,GAAG,CAAtB;MACA,IAAIC,UAAU,GAAG,CAAjB;MACA,MAAMC,cAAc,GAAG,EAAvB;;MACA,KAAK,IAAIjO,KAAT,IAAkB2M,MAAlB,EAA0B;QACtB,IAAI,CAACzC,UAAU,CAAClK,KAAD,CAAf,EAAwB;UACpBA,KAAK,GAAG,KAAK+B,OAAL,CAAa/B,KAAb,CAAR;QACH;;QACD,MAAMkO,aAAa,GAAGF,UAAtB;;QACA,IAAI;UACAhO,KAAK,CAACmK,IAAN,CAAYnK,KAAD,IAAW;YAClBiO,cAAc,CAACC,aAAD,CAAd,GAAgCvU,QAAQ,GAAGA,QAAQ,CAACgU,YAAT,CAAsB3N,KAAtB,CAAH,GAAkCA,KAA1E;YACA+N,eAAe;;YACf,IAAIA,eAAe,KAAK,CAAxB,EAA2B;cACvBhM,OAAO,CAACkM,cAAD,CAAP;YACH;UACJ,CAND,EAMI/R,GAAD,IAAS;YACR,IAAI,CAACvC,QAAL,EAAe;cACX4Q,MAAM,CAACrO,GAAD,CAAN;YACH,CAFD,MAGK;cACD+R,cAAc,CAACC,aAAD,CAAd,GAAgCvU,QAAQ,CAACkU,aAAT,CAAuB3R,GAAvB,CAAhC;cACA6R,eAAe;;cACf,IAAIA,eAAe,KAAK,CAAxB,EAA2B;gBACvBhM,OAAO,CAACkM,cAAD,CAAP;cACH;YACJ;UACJ,CAjBD;QAkBH,CAnBD,CAoBA,OAAOE,OAAP,EAAgB;UACZ5D,MAAM,CAAC4D,OAAD,CAAN;QACH;;QACDJ,eAAe;QACfC,UAAU;MACb,CAzCoC,CA0CrC;;;MACAD,eAAe,IAAI,CAAnB;;MACA,IAAIA,eAAe,KAAK,CAAxB,EAA2B;QACvBhM,OAAO,CAACkM,cAAD,CAAP;MACH;;MACD,OAAO/C,OAAP;IACH;;IACDnT,WAAW,CAACqW,QAAD,EAAW;MAClB,MAAMlD,OAAO,GAAG,IAAhB;;MACA,IAAI,EAAEA,OAAO,YAAYZ,gBAArB,CAAJ,EAA4C;QACxC,MAAM,IAAIzS,KAAJ,CAAU,gCAAV,CAAN;MACH;;MACDqT,OAAO,CAACV,WAAD,CAAP,GAAuBK,UAAvB;MACAK,OAAO,CAACT,WAAD,CAAP,GAAuB,EAAvB,CANkB,CAMS;;MAC3B,IAAI;QACA,MAAMkB,WAAW,GAAGN,IAAI,EAAxB;QACA+C,QAAQ,IACJA,QAAQ,CAACzC,WAAW,CAACV,YAAY,CAACC,OAAD,EAAUJ,QAAV,CAAb,CAAZ,EAA+Ca,WAAW,CAACV,YAAY,CAACC,OAAD,EAAUH,QAAV,CAAb,CAA1D,CADZ;MAEH,CAJD,CAKA,OAAO1Q,KAAP,EAAc;QACV+Q,cAAc,CAACF,OAAD,EAAU,KAAV,EAAiB7Q,KAAjB,CAAd;MACH;IACJ;;IACsB,KAAlBuS,MAAM,CAACyB,WAAW,IAAI;MACvB,OAAO,SAAP;IACH;;IACkB,KAAdzB,MAAM,CAAC0B,OAAO,IAAI;MACnB,OAAOhE,gBAAP;IACH;;IACDH,IAAI,CAACgC,WAAD,EAAcC,UAAd,EAA0B;MAC1B,IAAImC,EAAJ,CAD0B,CAE1B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAIC,CAAC,GAAG,CAACD,EAAE,GAAG,KAAKxW,WAAX,MAA4B,IAA5B,IAAoCwW,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAC3B,MAAM,CAAC0B,OAAR,CAAvE;;MACA,IAAI,CAACE,CAAD,IAAM,OAAOA,CAAP,KAAa,UAAvB,EAAmC;QAC/BA,CAAC,GAAG,KAAKzW,WAAL,IAAoBuS,gBAAxB;MACH;;MACD,MAAM4B,YAAY,GAAG,IAAIsC,CAAJ,CAAMlM,IAAN,CAArB;MACA,MAAM3J,IAAI,GAAGb,IAAI,CAACc,OAAlB;;MACA,IAAI,KAAK4R,WAAL,KAAqBK,UAAzB,EAAqC;QACjC,KAAKJ,WAAL,EAAkB3K,IAAlB,CAAuBnH,IAAvB,EAA6BuT,YAA7B,EAA2CC,WAA3C,EAAwDC,UAAxD;MACH,CAFD,MAGK;QACDL,uBAAuB,CAAC,IAAD,EAAOpT,IAAP,EAAauT,YAAb,EAA2BC,WAA3B,EAAwCC,UAAxC,CAAvB;MACH;;MACD,OAAOF,YAAP;IACH;;IACDuC,KAAK,CAACrC,UAAD,EAAa;MACd,OAAO,KAAKjC,IAAL,CAAU,IAAV,EAAgBiC,UAAhB,CAAP;IACH;;IACDsC,OAAO,CAACC,SAAD,EAAY;MACf,IAAIJ,EAAJ,CADe,CAEf;;;MACA,IAAIC,CAAC,GAAG,CAACD,EAAE,GAAG,KAAKxW,WAAX,MAA4B,IAA5B,IAAoCwW,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAAC3B,MAAM,CAAC0B,OAAR,CAAvE;;MACA,IAAI,CAACE,CAAD,IAAM,OAAOA,CAAP,KAAa,UAAvB,EAAmC;QAC/BA,CAAC,GAAGlE,gBAAJ;MACH;;MACD,MAAM4B,YAAY,GAAG,IAAIsC,CAAJ,CAAMlM,IAAN,CAArB;MACA4J,YAAY,CAACxB,aAAD,CAAZ,GAA8BA,aAA9B;MACA,MAAM/R,IAAI,GAAGb,IAAI,CAACc,OAAlB;;MACA,IAAI,KAAK4R,WAAL,KAAqBK,UAAzB,EAAqC;QACjC,KAAKJ,WAAL,EAAkB3K,IAAlB,CAAuBnH,IAAvB,EAA6BuT,YAA7B,EAA2CyC,SAA3C,EAAsDA,SAAtD;MACH,CAFD,MAGK;QACD5C,uBAAuB,CAAC,IAAD,EAAOpT,IAAP,EAAauT,YAAb,EAA2ByC,SAA3B,EAAsCA,SAAtC,CAAvB;MACH;;MACD,OAAOzC,YAAP;IACH;;EApMkB,CAxPkC,CA8bzD;EACA;;;EACA5B,gBAAgB,CAAC,SAAD,CAAhB,GAA8BA,gBAAgB,CAACvI,OAA/C;EACAuI,gBAAgB,CAAC,QAAD,CAAhB,GAA6BA,gBAAgB,CAACC,MAA9C;EACAD,gBAAgB,CAAC,MAAD,CAAhB,GAA2BA,gBAAgB,CAAC4C,IAA5C;EACA5C,gBAAgB,CAAC,KAAD,CAAhB,GAA0BA,gBAAgB,CAACiD,GAA3C;EACA,MAAMqB,aAAa,GAAGxX,MAAM,CAACoK,aAAD,CAAN,GAAwBpK,MAAM,CAAC,SAAD,CAApD;EACAA,MAAM,CAAC,SAAD,CAAN,GAAoBkT,gBAApB;;EACA,MAAMuE,iBAAiB,GAAGlX,UAAU,CAAC,aAAD,CAApC;;EACA,SAASiL,SAAT,CAAmBkM,IAAnB,EAAyB;IACrB,MAAM7G,KAAK,GAAG6G,IAAI,CAACzN,SAAnB;IACA,MAAMuF,IAAI,GAAG1D,8BAA8B,CAAC+E,KAAD,EAAQ,MAAR,CAA3C;;IACA,IAAIrB,IAAI,KAAKA,IAAI,CAACtB,QAAL,KAAkB,KAAlB,IAA2B,CAACsB,IAAI,CAACG,YAAtC,CAAR,EAA6D;MACzD;MACA;MACA;IACH;;IACD,MAAMgI,YAAY,GAAG9G,KAAK,CAACkC,IAA3B,CARqB,CASrB;;IACAlC,KAAK,CAACxG,UAAD,CAAL,GAAoBsN,YAApB;;IACAD,IAAI,CAACzN,SAAL,CAAe8I,IAAf,GAAsB,UAAUkD,SAAV,EAAqBC,QAArB,EAA+B;MACjD,MAAM0B,OAAO,GAAG,IAAI1E,gBAAJ,CAAqB,CAACvI,OAAD,EAAUwI,MAAV,KAAqB;QACtDwE,YAAY,CAACrO,IAAb,CAAkB,IAAlB,EAAwBqB,OAAxB,EAAiCwI,MAAjC;MACH,CAFe,CAAhB;MAGA,OAAOyE,OAAO,CAAC7E,IAAR,CAAakD,SAAb,EAAwBC,QAAxB,CAAP;IACH,CALD;;IAMAwB,IAAI,CAACD,iBAAD,CAAJ,GAA0B,IAA1B;EACH;;EACD5F,GAAG,CAACrG,SAAJ,GAAgBA,SAAhB;;EACA,SAASqM,OAAT,CAAiBhW,EAAjB,EAAqB;IACjB,OAAO,UAAUuH,IAAV,EAAgBG,IAAhB,EAAsB;MACzB,IAAIuO,aAAa,GAAGjW,EAAE,CAAC2G,KAAH,CAASY,IAAT,EAAeG,IAAf,CAApB;;MACA,IAAIuO,aAAa,YAAY5E,gBAA7B,EAA+C;QAC3C,OAAO4E,aAAP;MACH;;MACD,IAAIC,IAAI,GAAGD,aAAa,CAACnX,WAAzB;;MACA,IAAI,CAACoX,IAAI,CAACN,iBAAD,CAAT,EAA8B;QAC1BjM,SAAS,CAACuM,IAAD,CAAT;MACH;;MACD,OAAOD,aAAP;IACH,CAVD;EAWH;;EACD,IAAIN,aAAJ,EAAmB;IACfhM,SAAS,CAACgM,aAAD,CAAT;IACAlM,WAAW,CAACtL,MAAD,EAAS,OAAT,EAAkB6F,QAAQ,IAAIgS,OAAO,CAAChS,QAAD,CAArC,CAAX;EACH,CA3ewD,CA4ezD;;;EACA6P,OAAO,CAAChV,IAAI,CAACH,UAAL,CAAgB,uBAAhB,CAAD,CAAP,GAAoD0R,sBAApD;EACA,OAAOiB,gBAAP;AACH,CA/eD;AAifA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAxS,IAAI,CAACkB,YAAL,CAAkB,UAAlB,EAA+B5B,MAAD,IAAY;EACtC;EACA,MAAMgY,wBAAwB,GAAGC,QAAQ,CAAChO,SAAT,CAAmBH,QAApD;EACA,MAAMoO,wBAAwB,GAAG3K,UAAU,CAAC,kBAAD,CAA3C;EACA,MAAM4K,cAAc,GAAG5K,UAAU,CAAC,SAAD,CAAjC;EACA,MAAM6K,YAAY,GAAG7K,UAAU,CAAC,OAAD,CAA/B;;EACA,MAAM8K,mBAAmB,GAAG,SAASvO,QAAT,GAAoB;IAC5C,IAAI,OAAO,IAAP,KAAgB,UAApB,EAAgC;MAC5B,MAAMwO,gBAAgB,GAAG,KAAKJ,wBAAL,CAAzB;;MACA,IAAII,gBAAJ,EAAsB;QAClB,IAAI,OAAOA,gBAAP,KAA4B,UAAhC,EAA4C;UACxC,OAAON,wBAAwB,CAAC1O,IAAzB,CAA8BgP,gBAA9B,CAAP;QACH,CAFD,MAGK;UACD,OAAOtO,MAAM,CAACC,SAAP,CAAiBH,QAAjB,CAA0BR,IAA1B,CAA+BgP,gBAA/B,CAAP;QACH;MACJ;;MACD,IAAI,SAAS5C,OAAb,EAAsB;QAClB,MAAM6C,aAAa,GAAGvY,MAAM,CAACmY,cAAD,CAA5B;;QACA,IAAII,aAAJ,EAAmB;UACf,OAAOP,wBAAwB,CAAC1O,IAAzB,CAA8BiP,aAA9B,CAAP;QACH;MACJ;;MACD,IAAI,SAAS9X,KAAb,EAAoB;QAChB,MAAM+X,WAAW,GAAGxY,MAAM,CAACoY,YAAD,CAA1B;;QACA,IAAII,WAAJ,EAAiB;UACb,OAAOR,wBAAwB,CAAC1O,IAAzB,CAA8BkP,WAA9B,CAAP;QACH;MACJ;IACJ;;IACD,OAAOR,wBAAwB,CAAC1O,IAAzB,CAA8B,IAA9B,CAAP;EACH,CAzBD;;EA0BA+O,mBAAmB,CAACH,wBAAD,CAAnB,GAAgDF,wBAAhD;EACAC,QAAQ,CAAChO,SAAT,CAAmBH,QAAnB,GAA8BuO,mBAA9B,CAjCsC,CAkCtC;;EACA,MAAMI,sBAAsB,GAAGzO,MAAM,CAACC,SAAP,CAAiBH,QAAhD;EACA,MAAM4O,wBAAwB,GAAG,kBAAjC;;EACA1O,MAAM,CAACC,SAAP,CAAiBH,QAAjB,GAA4B,YAAY;IACpC,IAAI,OAAO4L,OAAP,KAAmB,UAAnB,IAAiC,gBAAgBA,OAArD,EAA8D;MAC1D,OAAOgD,wBAAP;IACH;;IACD,OAAOD,sBAAsB,CAACnP,IAAvB,CAA4B,IAA5B,CAAP;EACH,CALD;AAMH,CA3CD;AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAIqP,gBAAgB,GAAG,KAAvB;;AACA,IAAI,OAAOpM,MAAP,KAAkB,WAAtB,EAAmC;EAC/B,IAAI;IACA,MAAMrD,OAAO,GAAGc,MAAM,CAACyC,cAAP,CAAsB,EAAtB,EAA0B,SAA1B,EAAqC;MACjDvK,GAAG,EAAE,YAAY;QACbyW,gBAAgB,GAAG,IAAnB;MACH;IAHgD,CAArC,CAAhB,CADA,CAMA;IACA;IACA;;IACApM,MAAM,CAAC4D,gBAAP,CAAwB,MAAxB,EAAgCjH,OAAhC,EAAyCA,OAAzC;IACAqD,MAAM,CAAC2D,mBAAP,CAA2B,MAA3B,EAAmChH,OAAnC,EAA4CA,OAA5C;EACH,CAXD,CAYA,OAAOpE,GAAP,EAAY;IACR6T,gBAAgB,GAAG,KAAnB;EACH;AACJ,C,CACD;;;AACA,MAAMC,8BAA8B,GAAG;EACnCvP,IAAI,EAAE;AAD6B,CAAvC;AAGA,MAAMwP,oBAAoB,GAAG,EAA7B;AACA,MAAMC,aAAa,GAAG,EAAtB;AACA,MAAMC,sBAAsB,GAAG,IAAIC,MAAJ,CAAW,MAAM3L,kBAAN,GAA2B,qBAAtC,CAA/B;AACA,MAAM4L,4BAA4B,GAAG1L,UAAU,CAAC,oBAAD,CAA/C;;AACA,SAAS2L,iBAAT,CAA2BnJ,SAA3B,EAAsCoJ,iBAAtC,EAAyD;EACrD,MAAMC,cAAc,GAAG,CAACD,iBAAiB,GAAGA,iBAAiB,CAACpJ,SAAD,CAApB,GAAkCA,SAApD,IAAiE3C,SAAxF;EACA,MAAMiM,aAAa,GAAG,CAACF,iBAAiB,GAAGA,iBAAiB,CAACpJ,SAAD,CAApB,GAAkCA,SAApD,IAAiE5C,QAAvF;EACA,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAG+L,cAApC;EACA,MAAME,aAAa,GAAGjM,kBAAkB,GAAGgM,aAA3C;EACAR,oBAAoB,CAAC9I,SAAD,CAApB,GAAkC,EAAlC;EACA8I,oBAAoB,CAAC9I,SAAD,CAApB,CAAgC3C,SAAhC,IAA6CpC,MAA7C;EACA6N,oBAAoB,CAAC9I,SAAD,CAApB,CAAgC5C,QAAhC,IAA4CmM,aAA5C;AACH;;AACD,SAASlO,gBAAT,CAA0BsC,OAA1B,EAAmCmE,GAAnC,EAAwC0H,IAAxC,EAA8CC,YAA9C,EAA4D;EACxD,MAAMC,kBAAkB,GAAID,YAAY,IAAIA,YAAY,CAACE,GAA9B,IAAsC3M,sBAAjE;EACA,MAAM4M,qBAAqB,GAAIH,YAAY,IAAIA,YAAY,CAACI,EAA9B,IAAqC5M,yBAAnE;EACA,MAAM6M,wBAAwB,GAAIL,YAAY,IAAIA,YAAY,CAACM,SAA9B,IAA4C,gBAA7E;EACA,MAAMC,mCAAmC,GAAIP,YAAY,IAAIA,YAAY,CAACQ,KAA9B,IAAwC,oBAApF;EACA,MAAMC,0BAA0B,GAAG1M,UAAU,CAACkM,kBAAD,CAA7C;EACA,MAAMS,yBAAyB,GAAG,MAAMT,kBAAN,GAA2B,GAA7D;EACA,MAAMU,sBAAsB,GAAG,iBAA/B;EACA,MAAMC,6BAA6B,GAAG,MAAMD,sBAAN,GAA+B,GAArE;;EACA,MAAM9V,UAAU,GAAG,UAAUjB,IAAV,EAAgB2C,MAAhB,EAAwB6I,KAAxB,EAA+B;IAC9C;IACA;IACA,IAAIxL,IAAI,CAACiX,SAAT,EAAoB;MAChB;IACH;;IACD,MAAMxU,QAAQ,GAAGzC,IAAI,CAACb,QAAtB;;IACA,IAAI,OAAOsD,QAAP,KAAoB,QAApB,IAAgCA,QAAQ,CAACyU,WAA7C,EAA0D;MACtD;MACAlX,IAAI,CAACb,QAAL,GAAiBqM,KAAD,IAAW/I,QAAQ,CAACyU,WAAT,CAAqB1L,KAArB,CAA3B;;MACAxL,IAAI,CAACkV,gBAAL,GAAwBzS,QAAxB;IACH,CAX6C,CAY9C;IACA;IACA;IACA;;;IACA,IAAI5C,KAAJ;;IACA,IAAI;MACAG,IAAI,CAACJ,MAAL,CAAYI,IAAZ,EAAkB2C,MAAlB,EAA0B,CAAC6I,KAAD,CAA1B;IACH,CAFD,CAGA,OAAO9J,GAAP,EAAY;MACR7B,KAAK,GAAG6B,GAAR;IACH;;IACD,MAAMoE,OAAO,GAAG9F,IAAI,CAAC8F,OAArB;;IACA,IAAIA,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAA9B,IAA0CA,OAAO,CAAC+K,IAAtD,EAA4D;MACxD;MACA;MACA;MACA,MAAMpO,QAAQ,GAAGzC,IAAI,CAACkV,gBAAL,GAAwBlV,IAAI,CAACkV,gBAA7B,GAAgDlV,IAAI,CAACb,QAAtE;MACAwD,MAAM,CAAC4T,qBAAD,CAAN,CAA8BrQ,IAA9B,CAAmCvD,MAAnC,EAA2C6I,KAAK,CAACpL,IAAjD,EAAuDqC,QAAvD,EAAiEqD,OAAjE;IACH;;IACD,OAAOjG,KAAP;EACH,CAhCD;;EAiCA,SAASsX,cAAT,CAAwBC,OAAxB,EAAiC5L,KAAjC,EAAwC6L,SAAxC,EAAmD;IAC/C;IACA;IACA7L,KAAK,GAAGA,KAAK,IAAIlB,OAAO,CAACkB,KAAzB;;IACA,IAAI,CAACA,KAAL,EAAY;MACR;IACH,CAN8C,CAO/C;IACA;;;IACA,MAAM7I,MAAM,GAAGyU,OAAO,IAAI5L,KAAK,CAAC7I,MAAjB,IAA2B2H,OAA1C;IACA,MAAMgN,KAAK,GAAG3U,MAAM,CAAC8S,oBAAoB,CAACjK,KAAK,CAACpL,IAAP,CAApB,CAAiCiX,SAAS,GAAGtN,QAAH,GAAcC,SAAxD,CAAD,CAApB;;IACA,IAAIsN,KAAJ,EAAW;MACP,MAAM7E,MAAM,GAAG,EAAf,CADO,CAEP;MACA;;MACA,IAAI6E,KAAK,CAAChV,MAAN,KAAiB,CAArB,EAAwB;QACpB,MAAMZ,GAAG,GAAGT,UAAU,CAACqW,KAAK,CAAC,CAAD,CAAN,EAAW3U,MAAX,EAAmB6I,KAAnB,CAAtB;QACA9J,GAAG,IAAI+Q,MAAM,CAACnN,IAAP,CAAY5D,GAAZ,CAAP;MACH,CAHD,MAIK;QACD;QACA;QACA;QACA,MAAM6V,SAAS,GAAGD,KAAK,CAAC5N,KAAN,EAAlB;;QACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkV,SAAS,CAACjV,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;UACvC,IAAImJ,KAAK,IAAIA,KAAK,CAACqK,4BAAD,CAAL,KAAwC,IAArD,EAA2D;YACvD;UACH;;UACD,MAAMnU,GAAG,GAAGT,UAAU,CAACsW,SAAS,CAAClV,CAAD,CAAV,EAAeM,MAAf,EAAuB6I,KAAvB,CAAtB;UACA9J,GAAG,IAAI+Q,MAAM,CAACnN,IAAP,CAAY5D,GAAZ,CAAP;QACH;MACJ,CApBM,CAqBP;MACA;;;MACA,IAAI+Q,MAAM,CAACnQ,MAAP,KAAkB,CAAtB,EAAyB;QACrB,MAAMmQ,MAAM,CAAC,CAAD,CAAZ;MACH,CAFD,MAGK;QACD,KAAK,IAAIpQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoQ,MAAM,CAACnQ,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;UACpC,MAAMX,GAAG,GAAG+Q,MAAM,CAACpQ,CAAD,CAAlB;UACAoM,GAAG,CAACpH,uBAAJ,CAA4B,MAAM;YAC9B,MAAM3F,GAAN;UACH,CAFD;QAGH;MACJ;IACJ;EACJ,CAxFuD,CAyFxD;;;EACA,MAAM8V,uBAAuB,GAAG,UAAUhM,KAAV,EAAiB;IAC7C,OAAO2L,cAAc,CAAC,IAAD,EAAO3L,KAAP,EAAc,KAAd,CAArB;EACH,CAFD,CA1FwD,CA6FxD;;;EACA,MAAMiM,8BAA8B,GAAG,UAAUjM,KAAV,EAAiB;IACpD,OAAO2L,cAAc,CAAC,IAAD,EAAO3L,KAAP,EAAc,IAAd,CAArB;EACH,CAFD;;EAGA,SAASkM,uBAAT,CAAiCvL,GAAjC,EAAsCiK,YAAtC,EAAoD;IAChD,IAAI,CAACjK,GAAL,EAAU;MACN,OAAO,KAAP;IACH;;IACD,IAAIwL,iBAAiB,GAAG,IAAxB;;IACA,IAAIvB,YAAY,IAAIA,YAAY,CAACnQ,IAAb,KAAsBjF,SAA1C,EAAqD;MACjD2W,iBAAiB,GAAGvB,YAAY,CAACnQ,IAAjC;IACH;;IACD,MAAM2R,eAAe,GAAGxB,YAAY,IAAIA,YAAY,CAACyB,EAArD;IACA,IAAIza,cAAc,GAAG,IAArB;;IACA,IAAIgZ,YAAY,IAAIA,YAAY,CAAC0B,MAAb,KAAwB9W,SAA5C,EAAuD;MACnD5D,cAAc,GAAGgZ,YAAY,CAAC0B,MAA9B;IACH;;IACD,IAAIC,YAAY,GAAG,KAAnB;;IACA,IAAI3B,YAAY,IAAIA,YAAY,CAAC4B,EAAb,KAAoBhX,SAAxC,EAAmD;MAC/C+W,YAAY,GAAG3B,YAAY,CAAC4B,EAA5B;IACH;;IACD,IAAIvK,KAAK,GAAGtB,GAAZ;;IACA,OAAOsB,KAAK,IAAI,CAACA,KAAK,CAAC9O,cAAN,CAAqB0X,kBAArB,CAAjB,EAA2D;MACvD5I,KAAK,GAAGnE,oBAAoB,CAACmE,KAAD,CAA5B;IACH;;IACD,IAAI,CAACA,KAAD,IAAUtB,GAAG,CAACkK,kBAAD,CAAjB,EAAuC;MACnC;MACA5I,KAAK,GAAGtB,GAAR;IACH;;IACD,IAAI,CAACsB,KAAL,EAAY;MACR,OAAO,KAAP;IACH;;IACD,IAAIA,KAAK,CAACoJ,0BAAD,CAAT,EAAuC;MACnC,OAAO,KAAP;IACH;;IACD,MAAMd,iBAAiB,GAAGK,YAAY,IAAIA,YAAY,CAACL,iBAAvD,CA/BgD,CAgChD;IACA;;IACA,MAAMkC,QAAQ,GAAG,EAAjB;IACA,MAAMC,sBAAsB,GAAGzK,KAAK,CAACoJ,0BAAD,CAAL,GAAoCpJ,KAAK,CAAC4I,kBAAD,CAAxE;IACA,MAAM8B,yBAAyB,GAAG1K,KAAK,CAACtD,UAAU,CAACoM,qBAAD,CAAX,CAAL,GAC9B9I,KAAK,CAAC8I,qBAAD,CADT;IAEA,MAAM6B,eAAe,GAAG3K,KAAK,CAACtD,UAAU,CAACsM,wBAAD,CAAX,CAAL,GACpBhJ,KAAK,CAACgJ,wBAAD,CADT;IAEA,MAAM4B,wBAAwB,GAAG5K,KAAK,CAACtD,UAAU,CAACwM,mCAAD,CAAX,CAAL,GAC7BlJ,KAAK,CAACkJ,mCAAD,CADT;IAEA,IAAI2B,0BAAJ;;IACA,IAAIlC,YAAY,IAAIA,YAAY,CAACmC,OAAjC,EAA0C;MACtCD,0BAA0B,GAAG7K,KAAK,CAACtD,UAAU,CAACiM,YAAY,CAACmC,OAAd,CAAX,CAAL,GACzB9K,KAAK,CAAC2I,YAAY,CAACmC,OAAd,CADT;IAEH;IACD;AACR;AACA;AACA;;;IACQ,SAASC,yBAAT,CAAmC1S,OAAnC,EAA4C2S,OAA5C,EAAqD;MACjD,IAAI,CAAClD,gBAAD,IAAqB,OAAOzP,OAAP,KAAmB,QAAxC,IAAoDA,OAAxD,EAAiE;QAC7D;QACA;QACA;QACA,OAAO,CAAC,CAACA,OAAO,CAAC4S,OAAjB;MACH;;MACD,IAAI,CAACnD,gBAAD,IAAqB,CAACkD,OAA1B,EAAmC;QAC/B,OAAO3S,OAAP;MACH;;MACD,IAAI,OAAOA,OAAP,KAAmB,SAAvB,EAAkC;QAC9B,OAAO;UAAE4S,OAAO,EAAE5S,OAAX;UAAoB2S,OAAO,EAAE;QAA7B,CAAP;MACH;;MACD,IAAI,CAAC3S,OAAL,EAAc;QACV,OAAO;UAAE2S,OAAO,EAAE;QAAX,CAAP;MACH;;MACD,IAAI,OAAO3S,OAAP,KAAmB,QAAnB,IAA+BA,OAAO,CAAC2S,OAAR,KAAoB,KAAvD,EAA8D;QAC1D,OAAO7R,MAAM,CAAC+R,MAAP,CAAc/R,MAAM,CAAC+R,MAAP,CAAc,EAAd,EAAkB7S,OAAlB,CAAd,EAA0C;UAAE2S,OAAO,EAAE;QAAX,CAA1C,CAAP;MACH;;MACD,OAAO3S,OAAP;IACH;;IACD,MAAM8S,oBAAoB,GAAG,UAAU5Y,IAAV,EAAgB;MACzC;MACA;MACA,IAAIiY,QAAQ,CAACY,UAAb,EAAyB;QACrB;MACH;;MACD,OAAOX,sBAAsB,CAAChS,IAAvB,CAA4B+R,QAAQ,CAACtV,MAArC,EAA6CsV,QAAQ,CAACtL,SAAtD,EAAiEsL,QAAQ,CAACS,OAAT,GAAmBjB,8BAAnB,GAAoDD,uBAArH,EAA8IS,QAAQ,CAACnS,OAAvJ,CAAP;IACH,CAPD;;IAQA,MAAMgT,kBAAkB,GAAG,UAAU9Y,IAAV,EAAgB;MACvC;MACA;MACA;MACA,IAAI,CAACA,IAAI,CAACiX,SAAV,EAAqB;QACjB,MAAM8B,gBAAgB,GAAGtD,oBAAoB,CAACzV,IAAI,CAAC2M,SAAN,CAA7C;QACA,IAAIqM,eAAJ;;QACA,IAAID,gBAAJ,EAAsB;UAClBC,eAAe,GAAGD,gBAAgB,CAAC/Y,IAAI,CAAC0Y,OAAL,GAAe3O,QAAf,GAA0BC,SAA3B,CAAlC;QACH;;QACD,MAAMiP,aAAa,GAAGD,eAAe,IAAIhZ,IAAI,CAAC2C,MAAL,CAAYqW,eAAZ,CAAzC;;QACA,IAAIC,aAAJ,EAAmB;UACf,KAAK,IAAI5W,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4W,aAAa,CAAC3W,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;YAC3C,MAAM6W,YAAY,GAAGD,aAAa,CAAC5W,CAAD,CAAlC;;YACA,IAAI6W,YAAY,KAAKlZ,IAArB,EAA2B;cACvBiZ,aAAa,CAACxH,MAAd,CAAqBpP,CAArB,EAAwB,CAAxB,EADuB,CAEvB;;cACArC,IAAI,CAACiX,SAAL,GAAiB,IAAjB;;cACA,IAAIgC,aAAa,CAAC3W,MAAd,KAAyB,CAA7B,EAAgC;gBAC5B;gBACA;gBACAtC,IAAI,CAACmZ,UAAL,GAAkB,IAAlB;gBACAnZ,IAAI,CAAC2C,MAAL,CAAYqW,eAAZ,IAA+B,IAA/B;cACH;;cACD;YACH;UACJ;QACJ;MACJ,CA5BsC,CA6BvC;MACA;MACA;;;MACA,IAAI,CAAChZ,IAAI,CAACmZ,UAAV,EAAsB;QAClB;MACH;;MACD,OAAOhB,yBAAyB,CAACjS,IAA1B,CAA+BlG,IAAI,CAAC2C,MAApC,EAA4C3C,IAAI,CAAC2M,SAAjD,EAA4D3M,IAAI,CAAC0Y,OAAL,GAAejB,8BAAf,GAAgDD,uBAA5G,EAAqIxX,IAAI,CAAC8F,OAA1I,CAAP;IACH,CApCD;;IAqCA,MAAMsT,uBAAuB,GAAG,UAAUpZ,IAAV,EAAgB;MAC5C,OAAOkY,sBAAsB,CAAChS,IAAvB,CAA4B+R,QAAQ,CAACtV,MAArC,EAA6CsV,QAAQ,CAACtL,SAAtD,EAAiE3M,IAAI,CAACJ,MAAtE,EAA8EqY,QAAQ,CAACnS,OAAvF,CAAP;IACH,CAFD;;IAGA,MAAMuT,qBAAqB,GAAG,UAAUrZ,IAAV,EAAgB;MAC1C,OAAOsY,0BAA0B,CAACpS,IAA3B,CAAgC+R,QAAQ,CAACtV,MAAzC,EAAiDsV,QAAQ,CAACtL,SAA1D,EAAqE3M,IAAI,CAACJ,MAA1E,EAAkFqY,QAAQ,CAACnS,OAA3F,CAAP;IACH,CAFD;;IAGA,MAAMwT,qBAAqB,GAAG,UAAUtZ,IAAV,EAAgB;MAC1C,OAAOmY,yBAAyB,CAACjS,IAA1B,CAA+BlG,IAAI,CAAC2C,MAApC,EAA4C3C,IAAI,CAAC2M,SAAjD,EAA4D3M,IAAI,CAACJ,MAAjE,EAAyEI,IAAI,CAAC8F,OAA9E,CAAP;IACH,CAFD;;IAGA,MAAMlE,cAAc,GAAG+V,iBAAiB,GAAGiB,oBAAH,GAA0BQ,uBAAlE;IACA,MAAMpX,YAAY,GAAG2V,iBAAiB,GAAGmB,kBAAH,GAAwBQ,qBAA9D;;IACA,MAAMC,6BAA6B,GAAG,UAAUvZ,IAAV,EAAgByC,QAAhB,EAA0B;MAC5D,MAAM+W,cAAc,GAAG,OAAO/W,QAA9B;MACA,OAAQ+W,cAAc,KAAK,UAAnB,IAAiCxZ,IAAI,CAACb,QAAL,KAAkBsD,QAApD,IACF+W,cAAc,KAAK,QAAnB,IAA+BxZ,IAAI,CAACkV,gBAAL,KAA0BzS,QAD9D;IAEH,CAJD;;IAKA,MAAMgX,OAAO,GAAIrD,YAAY,IAAIA,YAAY,CAACsD,IAA9B,GAAsCtD,YAAY,CAACsD,IAAnD,GAA0DH,6BAA1E;IACA,MAAMI,eAAe,GAAGrc,IAAI,CAAC6M,UAAU,CAAC,kBAAD,CAAX,CAA5B;;IACA,MAAMyP,aAAa,GAAGtP,OAAO,CAACH,UAAU,CAAC,gBAAD,CAAX,CAA7B;;IACA,MAAM0P,eAAe,GAAG,UAAUC,cAAV,EAA0BC,SAA1B,EAAqCC,gBAArC,EAAuDC,cAAvD,EAAuElC,YAAY,GAAG,KAAtF,EAA6FQ,OAAO,GAAG,KAAvG,EAA8G;MAClI,OAAO,YAAY;QACf,MAAM5V,MAAM,GAAG,QAAQ2H,OAAvB;QACA,IAAIqC,SAAS,GAAGnN,SAAS,CAAC,CAAD,CAAzB;;QACA,IAAI4W,YAAY,IAAIA,YAAY,CAAC8D,iBAAjC,EAAoD;UAChDvN,SAAS,GAAGyJ,YAAY,CAAC8D,iBAAb,CAA+BvN,SAA/B,CAAZ;QACH;;QACD,IAAIlK,QAAQ,GAAGjD,SAAS,CAAC,CAAD,CAAxB;;QACA,IAAI,CAACiD,QAAL,EAAe;UACX,OAAOqX,cAAc,CAAC1U,KAAf,CAAqB,IAArB,EAA2B5F,SAA3B,CAAP;QACH;;QACD,IAAI0L,MAAM,IAAIyB,SAAS,KAAK,mBAA5B,EAAiD;UAC7C;UACA,OAAOmN,cAAc,CAAC1U,KAAf,CAAqB,IAArB,EAA2B5F,SAA3B,CAAP;QACH,CAbc,CAcf;QACA;QACA;;;QACA,IAAI2a,aAAa,GAAG,KAApB;;QACA,IAAI,OAAO1X,QAAP,KAAoB,UAAxB,EAAoC;UAChC,IAAI,CAACA,QAAQ,CAACyU,WAAd,EAA2B;YACvB,OAAO4C,cAAc,CAAC1U,KAAf,CAAqB,IAArB,EAA2B5F,SAA3B,CAAP;UACH;;UACD2a,aAAa,GAAG,IAAhB;QACH;;QACD,IAAIvC,eAAe,IAAI,CAACA,eAAe,CAACkC,cAAD,EAAiBrX,QAAjB,EAA2BE,MAA3B,EAAmCnD,SAAnC,CAAvC,EAAsF;UAClF;QACH;;QACD,MAAMiZ,OAAO,GAAGlD,gBAAgB,IAAI,CAAC,CAACqE,aAAtB,IAAuCA,aAAa,CAACpL,OAAd,CAAsB7B,SAAtB,MAAqC,CAAC,CAA7F;QACA,MAAM7G,OAAO,GAAG0S,yBAAyB,CAAChZ,SAAS,CAAC,CAAD,CAAV,EAAeiZ,OAAf,CAAzC;;QACA,IAAIkB,eAAJ,EAAqB;UACjB;UACA,KAAK,IAAItX,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsX,eAAe,CAACrX,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;YAC7C,IAAIsK,SAAS,KAAKgN,eAAe,CAACtX,CAAD,CAAjC,EAAsC;cAClC,IAAIoW,OAAJ,EAAa;gBACT,OAAOqB,cAAc,CAAC5T,IAAf,CAAoBvD,MAApB,EAA4BgK,SAA5B,EAAuClK,QAAvC,EAAiDqD,OAAjD,CAAP;cACH,CAFD,MAGK;gBACD,OAAOgU,cAAc,CAAC1U,KAAf,CAAqB,IAArB,EAA2B5F,SAA3B,CAAP;cACH;YACJ;UACJ;QACJ;;QACD,MAAMkZ,OAAO,GAAG,CAAC5S,OAAD,GAAW,KAAX,GAAmB,OAAOA,OAAP,KAAmB,SAAnB,GAA+B,IAA/B,GAAsCA,OAAO,CAAC4S,OAAjF;QACA,MAAM7H,IAAI,GAAG/K,OAAO,IAAI,OAAOA,OAAP,KAAmB,QAA9B,GAAyCA,OAAO,CAAC+K,IAAjD,GAAwD,KAArE;QACA,MAAM1S,IAAI,GAAGb,IAAI,CAACc,OAAlB;QACA,IAAI2a,gBAAgB,GAAGtD,oBAAoB,CAAC9I,SAAD,CAA3C;;QACA,IAAI,CAACoM,gBAAL,EAAuB;UACnBjD,iBAAiB,CAACnJ,SAAD,EAAYoJ,iBAAZ,CAAjB;UACAgD,gBAAgB,GAAGtD,oBAAoB,CAAC9I,SAAD,CAAvC;QACH;;QACD,MAAMqM,eAAe,GAAGD,gBAAgB,CAACL,OAAO,GAAG3O,QAAH,GAAcC,SAAtB,CAAxC;QACA,IAAIiP,aAAa,GAAGtW,MAAM,CAACqW,eAAD,CAA1B;QACA,IAAIH,UAAU,GAAG,KAAjB;;QACA,IAAII,aAAJ,EAAmB;UACf;UACAJ,UAAU,GAAG,IAAb;;UACA,IAAIzb,cAAJ,EAAoB;YAChB,KAAK,IAAIiF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4W,aAAa,CAAC3W,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;cAC3C,IAAIoX,OAAO,CAACR,aAAa,CAAC5W,CAAD,CAAd,EAAmBI,QAAnB,CAAX,EAAyC;gBACrC;gBACA;cACH;YACJ;UACJ;QACJ,CAXD,MAYK;UACDwW,aAAa,GAAGtW,MAAM,CAACqW,eAAD,CAAN,GAA0B,EAA1C;QACH;;QACD,IAAI5Z,MAAJ;QACA,MAAMgb,eAAe,GAAGzX,MAAM,CAACpF,WAAP,CAAmB,MAAnB,CAAxB;QACA,MAAM8c,YAAY,GAAG3E,aAAa,CAAC0E,eAAD,CAAlC;;QACA,IAAIC,YAAJ,EAAkB;UACdjb,MAAM,GAAGib,YAAY,CAAC1N,SAAD,CAArB;QACH;;QACD,IAAI,CAACvN,MAAL,EAAa;UACTA,MAAM,GAAGgb,eAAe,GAAGL,SAAlB,IACJhE,iBAAiB,GAAGA,iBAAiB,CAACpJ,SAAD,CAApB,GAAkCA,SAD/C,CAAT;QAEH,CA7Ec,CA8Ef;QACA;;;QACAsL,QAAQ,CAACnS,OAAT,GAAmBA,OAAnB;;QACA,IAAI+K,IAAJ,EAAU;UACN;UACA;UACA;UACAoH,QAAQ,CAACnS,OAAT,CAAiB+K,IAAjB,GAAwB,KAAxB;QACH;;QACDoH,QAAQ,CAACtV,MAAT,GAAkBA,MAAlB;QACAsV,QAAQ,CAACS,OAAT,GAAmBA,OAAnB;QACAT,QAAQ,CAACtL,SAAT,GAAqBA,SAArB;QACAsL,QAAQ,CAACY,UAAT,GAAsBA,UAAtB;QACA,MAAMhY,IAAI,GAAG8W,iBAAiB,GAAGnC,8BAAH,GAAoCxU,SAAlE,CA3Fe,CA4Ff;;QACA,IAAIH,IAAJ,EAAU;UACNA,IAAI,CAACoX,QAAL,GAAgBA,QAAhB;QACH;;QACD,MAAMjY,IAAI,GAAG7B,IAAI,CAAC8D,iBAAL,CAAuB7C,MAAvB,EAA+BqD,QAA/B,EAAyC5B,IAAzC,EAA+CmZ,gBAA/C,EAAiEC,cAAjE,CAAb,CAhGe,CAiGf;QACA;;QACAhC,QAAQ,CAACtV,MAAT,GAAkB,IAAlB,CAnGe,CAoGf;;QACA,IAAI9B,IAAJ,EAAU;UACNA,IAAI,CAACoX,QAAL,GAAgB,IAAhB;QACH,CAvGc,CAwGf;QACA;;;QACA,IAAIpH,IAAJ,EAAU;UACN/K,OAAO,CAAC+K,IAAR,GAAe,IAAf;QACH;;QACD,IAAI,EAAE,CAAC0E,gBAAD,IAAqB,OAAOvV,IAAI,CAAC8F,OAAZ,KAAwB,SAA/C,CAAJ,EAA+D;UAC3D;UACA;UACA9F,IAAI,CAAC8F,OAAL,GAAeA,OAAf;QACH;;QACD9F,IAAI,CAAC2C,MAAL,GAAcA,MAAd;QACA3C,IAAI,CAAC0Y,OAAL,GAAeA,OAAf;QACA1Y,IAAI,CAAC2M,SAAL,GAAiBA,SAAjB;;QACA,IAAIwN,aAAJ,EAAmB;UACf;UACAna,IAAI,CAACkV,gBAAL,GAAwBzS,QAAxB;QACH;;QACD,IAAI,CAAC8V,OAAL,EAAc;UACVU,aAAa,CAAC3T,IAAd,CAAmBtF,IAAnB;QACH,CAFD,MAGK;UACDiZ,aAAa,CAACqB,OAAd,CAAsBta,IAAtB;QACH;;QACD,IAAI+X,YAAJ,EAAkB;UACd,OAAOpV,MAAP;QACH;MACJ,CAlID;IAmIH,CApID;;IAqIA8K,KAAK,CAAC4I,kBAAD,CAAL,GAA4BwD,eAAe,CAAC3B,sBAAD,EAAyBpB,yBAAzB,EAAoDlV,cAApD,EAAoEI,YAApE,EAAkF+V,YAAlF,CAA3C;;IACA,IAAIO,0BAAJ,EAAgC;MAC5B7K,KAAK,CAACsJ,sBAAD,CAAL,GAAgC8C,eAAe,CAACvB,0BAAD,EAA6BtB,6BAA7B,EAA4DqC,qBAA5D,EAAmFrX,YAAnF,EAAiG+V,YAAjG,EAA+G,IAA/G,CAA/C;IACH;;IACDtK,KAAK,CAAC8I,qBAAD,CAAL,GAA+B,YAAY;MACvC,MAAM5T,MAAM,GAAG,QAAQ2H,OAAvB;MACA,IAAIqC,SAAS,GAAGnN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAI4W,YAAY,IAAIA,YAAY,CAAC8D,iBAAjC,EAAoD;QAChDvN,SAAS,GAAGyJ,YAAY,CAAC8D,iBAAb,CAA+BvN,SAA/B,CAAZ;MACH;;MACD,MAAM7G,OAAO,GAAGtG,SAAS,CAAC,CAAD,CAAzB;MACA,MAAMkZ,OAAO,GAAG,CAAC5S,OAAD,GAAW,KAAX,GAAmB,OAAOA,OAAP,KAAmB,SAAnB,GAA+B,IAA/B,GAAsCA,OAAO,CAAC4S,OAAjF;MACA,MAAMjW,QAAQ,GAAGjD,SAAS,CAAC,CAAD,CAA1B;;MACA,IAAI,CAACiD,QAAL,EAAe;QACX,OAAO0V,yBAAyB,CAAC/S,KAA1B,CAAgC,IAAhC,EAAsC5F,SAAtC,CAAP;MACH;;MACD,IAAIoY,eAAe,IACf,CAACA,eAAe,CAACO,yBAAD,EAA4B1V,QAA5B,EAAsCE,MAAtC,EAA8CnD,SAA9C,CADpB,EAC8E;QAC1E;MACH;;MACD,MAAMuZ,gBAAgB,GAAGtD,oBAAoB,CAAC9I,SAAD,CAA7C;MACA,IAAIqM,eAAJ;;MACA,IAAID,gBAAJ,EAAsB;QAClBC,eAAe,GAAGD,gBAAgB,CAACL,OAAO,GAAG3O,QAAH,GAAcC,SAAtB,CAAlC;MACH;;MACD,MAAMiP,aAAa,GAAGD,eAAe,IAAIrW,MAAM,CAACqW,eAAD,CAA/C;;MACA,IAAIC,aAAJ,EAAmB;QACf,KAAK,IAAI5W,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4W,aAAa,CAAC3W,MAAlC,EAA0CD,CAAC,EAA3C,EAA+C;UAC3C,MAAM6W,YAAY,GAAGD,aAAa,CAAC5W,CAAD,CAAlC;;UACA,IAAIoX,OAAO,CAACP,YAAD,EAAezW,QAAf,CAAX,EAAqC;YACjCwW,aAAa,CAACxH,MAAd,CAAqBpP,CAArB,EAAwB,CAAxB,EADiC,CAEjC;;YACA6W,YAAY,CAACjC,SAAb,GAAyB,IAAzB;;YACA,IAAIgC,aAAa,CAAC3W,MAAd,KAAyB,CAA7B,EAAgC;cAC5B;cACA;cACA4W,YAAY,CAACC,UAAb,GAA0B,IAA1B;cACAxW,MAAM,CAACqW,eAAD,CAAN,GAA0B,IAA1B,CAJ4B,CAK5B;cACA;cACA;;cACA,IAAI,OAAOrM,SAAP,KAAqB,QAAzB,EAAmC;gBAC/B,MAAM4N,gBAAgB,GAAGtQ,kBAAkB,GAAG,aAArB,GAAqC0C,SAA9D;gBACAhK,MAAM,CAAC4X,gBAAD,CAAN,GAA2B,IAA3B;cACH;YACJ;;YACDrB,YAAY,CAAC/a,IAAb,CAAkB+D,UAAlB,CAA6BgX,YAA7B;;YACA,IAAInB,YAAJ,EAAkB;cACd,OAAOpV,MAAP;YACH;;YACD;UACH;QACJ;MACJ,CAjDsC,CAkDvC;MACA;MACA;MACA;;;MACA,OAAOwV,yBAAyB,CAAC/S,KAA1B,CAAgC,IAAhC,EAAsC5F,SAAtC,CAAP;IACH,CAvDD;;IAwDAiO,KAAK,CAACgJ,wBAAD,CAAL,GAAkC,YAAY;MAC1C,MAAM9T,MAAM,GAAG,QAAQ2H,OAAvB;MACA,IAAIqC,SAAS,GAAGnN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAI4W,YAAY,IAAIA,YAAY,CAAC8D,iBAAjC,EAAoD;QAChDvN,SAAS,GAAGyJ,YAAY,CAAC8D,iBAAb,CAA+BvN,SAA/B,CAAZ;MACH;;MACD,MAAM+J,SAAS,GAAG,EAAlB;MACA,MAAMY,KAAK,GAAGkD,cAAc,CAAC7X,MAAD,EAASoT,iBAAiB,GAAGA,iBAAiB,CAACpJ,SAAD,CAApB,GAAkCA,SAA5D,CAA5B;;MACA,KAAK,IAAItK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiV,KAAK,CAAChV,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;QACnC,MAAMrC,IAAI,GAAGsX,KAAK,CAACjV,CAAD,CAAlB;QACA,IAAII,QAAQ,GAAGzC,IAAI,CAACkV,gBAAL,GAAwBlV,IAAI,CAACkV,gBAA7B,GAAgDlV,IAAI,CAACb,QAApE;QACAuX,SAAS,CAACpR,IAAV,CAAe7C,QAAf;MACH;;MACD,OAAOiU,SAAP;IACH,CAdD;;IAeAjJ,KAAK,CAACkJ,mCAAD,CAAL,GAA6C,YAAY;MACrD,MAAMhU,MAAM,GAAG,QAAQ2H,OAAvB;MACA,IAAIqC,SAAS,GAAGnN,SAAS,CAAC,CAAD,CAAzB;;MACA,IAAI,CAACmN,SAAL,EAAgB;QACZ,MAAM8N,IAAI,GAAG7T,MAAM,CAAC6T,IAAP,CAAY9X,MAAZ,CAAb;;QACA,KAAK,IAAIN,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoY,IAAI,CAACnY,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;UAClC,MAAM+J,IAAI,GAAGqO,IAAI,CAACpY,CAAD,CAAjB;UACA,MAAMqY,KAAK,GAAG/E,sBAAsB,CAACgF,IAAvB,CAA4BvO,IAA5B,CAAd;UACA,IAAIwO,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAD,CAA5B,CAHkC,CAIlC;UACA;UACA;UACA;;UACA,IAAIE,OAAO,IAAIA,OAAO,KAAK,gBAA3B,EAA6C;YACzC,KAAKjE,mCAAL,EAA0CzQ,IAA1C,CAA+C,IAA/C,EAAqD0U,OAArD;UACH;QACJ,CAbW,CAcZ;;;QACA,KAAKjE,mCAAL,EAA0CzQ,IAA1C,CAA+C,IAA/C,EAAqD,gBAArD;MACH,CAhBD,MAiBK;QACD,IAAIkQ,YAAY,IAAIA,YAAY,CAAC8D,iBAAjC,EAAoD;UAChDvN,SAAS,GAAGyJ,YAAY,CAAC8D,iBAAb,CAA+BvN,SAA/B,CAAZ;QACH;;QACD,MAAMoM,gBAAgB,GAAGtD,oBAAoB,CAAC9I,SAAD,CAA7C;;QACA,IAAIoM,gBAAJ,EAAsB;UAClB,MAAMC,eAAe,GAAGD,gBAAgB,CAAC/O,SAAD,CAAxC;UACA,MAAM6Q,sBAAsB,GAAG9B,gBAAgB,CAAChP,QAAD,CAA/C;UACA,MAAMuN,KAAK,GAAG3U,MAAM,CAACqW,eAAD,CAApB;UACA,MAAM8B,YAAY,GAAGnY,MAAM,CAACkY,sBAAD,CAA3B;;UACA,IAAIvD,KAAJ,EAAW;YACP,MAAMyD,WAAW,GAAGzD,KAAK,CAAC5N,KAAN,EAApB;;YACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0Y,WAAW,CAACzY,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;cACzC,MAAMrC,IAAI,GAAG+a,WAAW,CAAC1Y,CAAD,CAAxB;cACA,IAAII,QAAQ,GAAGzC,IAAI,CAACkV,gBAAL,GAAwBlV,IAAI,CAACkV,gBAA7B,GAAgDlV,IAAI,CAACb,QAApE;cACA,KAAKoX,qBAAL,EAA4BrQ,IAA5B,CAAiC,IAAjC,EAAuCyG,SAAvC,EAAkDlK,QAAlD,EAA4DzC,IAAI,CAAC8F,OAAjE;YACH;UACJ;;UACD,IAAIgV,YAAJ,EAAkB;YACd,MAAMC,WAAW,GAAGD,YAAY,CAACpR,KAAb,EAApB;;YACA,KAAK,IAAIrH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG0Y,WAAW,CAACzY,MAAhC,EAAwCD,CAAC,EAAzC,EAA6C;cACzC,MAAMrC,IAAI,GAAG+a,WAAW,CAAC1Y,CAAD,CAAxB;cACA,IAAII,QAAQ,GAAGzC,IAAI,CAACkV,gBAAL,GAAwBlV,IAAI,CAACkV,gBAA7B,GAAgDlV,IAAI,CAACb,QAApE;cACA,KAAKoX,qBAAL,EAA4BrQ,IAA5B,CAAiC,IAAjC,EAAuCyG,SAAvC,EAAkDlK,QAAlD,EAA4DzC,IAAI,CAAC8F,OAAjE;YACH;UACJ;QACJ;MACJ;;MACD,IAAIiS,YAAJ,EAAkB;QACd,OAAO,IAAP;MACH;IACJ,CAnDD,CAxVgD,CA4YhD;;;IACA/O,qBAAqB,CAACyE,KAAK,CAAC4I,kBAAD,CAAN,EAA4B6B,sBAA5B,CAArB;IACAlP,qBAAqB,CAACyE,KAAK,CAAC8I,qBAAD,CAAN,EAA+B4B,yBAA/B,CAArB;;IACA,IAAIE,wBAAJ,EAA8B;MAC1BrP,qBAAqB,CAACyE,KAAK,CAACkJ,mCAAD,CAAN,EAA6C0B,wBAA7C,CAArB;IACH;;IACD,IAAID,eAAJ,EAAqB;MACjBpP,qBAAqB,CAACyE,KAAK,CAACgJ,wBAAD,CAAN,EAAkC2B,eAAlC,CAArB;IACH;;IACD,OAAO,IAAP;EACH;;EACD,IAAI4C,OAAO,GAAG,EAAd;;EACA,KAAK,IAAI3Y,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG8T,IAAI,CAAC7T,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;IAClC2Y,OAAO,CAAC3Y,CAAD,CAAP,GAAaqV,uBAAuB,CAACvB,IAAI,CAAC9T,CAAD,CAAL,EAAU+T,YAAV,CAApC;EACH;;EACD,OAAO4E,OAAP;AACH;;AACD,SAASR,cAAT,CAAwB7X,MAAxB,EAAgCgK,SAAhC,EAA2C;EACvC,IAAI,CAACA,SAAL,EAAgB;IACZ,MAAMsO,UAAU,GAAG,EAAnB;;IACA,KAAK,IAAI7O,IAAT,IAAiBzJ,MAAjB,EAAyB;MACrB,MAAM+X,KAAK,GAAG/E,sBAAsB,CAACgF,IAAvB,CAA4BvO,IAA5B,CAAd;MACA,IAAIwO,OAAO,GAAGF,KAAK,IAAIA,KAAK,CAAC,CAAD,CAA5B;;MACA,IAAIE,OAAO,KAAK,CAACjO,SAAD,IAAciO,OAAO,KAAKjO,SAA/B,CAAX,EAAsD;QAClD,MAAM2K,KAAK,GAAG3U,MAAM,CAACyJ,IAAD,CAApB;;QACA,IAAIkL,KAAJ,EAAW;UACP,KAAK,IAAIjV,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGiV,KAAK,CAAChV,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;YACnC4Y,UAAU,CAAC3V,IAAX,CAAgBgS,KAAK,CAACjV,CAAD,CAArB;UACH;QACJ;MACJ;IACJ;;IACD,OAAO4Y,UAAP;EACH;;EACD,IAAIjC,eAAe,GAAGvD,oBAAoB,CAAC9I,SAAD,CAA1C;;EACA,IAAI,CAACqM,eAAL,EAAsB;IAClBlD,iBAAiB,CAACnJ,SAAD,CAAjB;IACAqM,eAAe,GAAGvD,oBAAoB,CAAC9I,SAAD,CAAtC;EACH;;EACD,MAAMuO,iBAAiB,GAAGvY,MAAM,CAACqW,eAAe,CAAChP,SAAD,CAAhB,CAAhC;EACA,MAAMmR,gBAAgB,GAAGxY,MAAM,CAACqW,eAAe,CAACjP,QAAD,CAAhB,CAA/B;;EACA,IAAI,CAACmR,iBAAL,EAAwB;IACpB,OAAOC,gBAAgB,GAAGA,gBAAgB,CAACzR,KAAjB,EAAH,GAA8B,EAArD;EACH,CAFD,MAGK;IACD,OAAOyR,gBAAgB,GAAGD,iBAAiB,CAACE,MAAlB,CAAyBD,gBAAzB,CAAH,GACnBD,iBAAiB,CAACxR,KAAlB,EADJ;EAEH;AACJ;;AACD,SAASpB,mBAAT,CAA6B1L,MAA7B,EAAqC6R,GAArC,EAA0C;EACtC,MAAM4M,KAAK,GAAGze,MAAM,CAAC,OAAD,CAApB;;EACA,IAAIye,KAAK,IAAIA,KAAK,CAACxU,SAAnB,EAA8B;IAC1B4H,GAAG,CAACvG,WAAJ,CAAgBmT,KAAK,CAACxU,SAAtB,EAAiC,0BAAjC,EAA8DpE,QAAD,IAAc,UAAUuD,IAAV,EAAgBG,IAAhB,EAAsB;MAC7FH,IAAI,CAAC6P,4BAAD,CAAJ,GAAqC,IAArC,CAD6F,CAE7F;MACA;MACA;;MACApT,QAAQ,IAAIA,QAAQ,CAAC2C,KAAT,CAAeY,IAAf,EAAqBG,IAArB,CAAZ;IACH,CAND;EAOH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS+C,cAAT,CAAwBuF,GAAxB,EAA6B9L,MAA7B,EAAqC2Y,UAArC,EAAiDC,MAAjD,EAAyDC,SAAzD,EAAoE;EAChE,MAAM5T,MAAM,GAAGtK,IAAI,CAACH,UAAL,CAAgBoe,MAAhB,CAAf;;EACA,IAAI5Y,MAAM,CAACiF,MAAD,CAAV,EAAoB;IAChB;EACH;;EACD,MAAM6T,cAAc,GAAG9Y,MAAM,CAACiF,MAAD,CAAN,GAAiBjF,MAAM,CAAC4Y,MAAD,CAA9C;;EACA5Y,MAAM,CAAC4Y,MAAD,CAAN,GAAiB,UAAUxe,IAAV,EAAgB2e,IAAhB,EAAsB5V,OAAtB,EAA+B;IAC5C,IAAI4V,IAAI,IAAIA,IAAI,CAAC7U,SAAjB,EAA4B;MACxB2U,SAAS,CAACG,OAAV,CAAkB,UAAUxc,QAAV,EAAoB;QAClC,MAAMC,MAAM,GAAI,GAAEkc,UAAW,IAAGC,MAAO,IAAxB,GAA8Bpc,QAA7C;QACA,MAAM0H,SAAS,GAAG6U,IAAI,CAAC7U,SAAvB,CAFkC,CAGlC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAI;UACA,IAAIA,SAAS,CAAClI,cAAV,CAAyBQ,QAAzB,CAAJ,EAAwC;YACpC,MAAMyc,UAAU,GAAGnN,GAAG,CAAC/F,8BAAJ,CAAmC7B,SAAnC,EAA8C1H,QAA9C,CAAnB;;YACA,IAAIyc,UAAU,IAAIA,UAAU,CAACpW,KAA7B,EAAoC;cAChCoW,UAAU,CAACpW,KAAX,GAAmBiJ,GAAG,CAAC3F,mBAAJ,CAAwB8S,UAAU,CAACpW,KAAnC,EAA0CpG,MAA1C,CAAnB;;cACAqP,GAAG,CAACxF,iBAAJ,CAAsByS,IAAI,CAAC7U,SAA3B,EAAsC1H,QAAtC,EAAgDyc,UAAhD;YACH,CAHD,MAIK,IAAI/U,SAAS,CAAC1H,QAAD,CAAb,EAAyB;cAC1B0H,SAAS,CAAC1H,QAAD,CAAT,GAAsBsP,GAAG,CAAC3F,mBAAJ,CAAwBjC,SAAS,CAAC1H,QAAD,CAAjC,EAA6CC,MAA7C,CAAtB;YACH;UACJ,CATD,MAUK,IAAIyH,SAAS,CAAC1H,QAAD,CAAb,EAAyB;YAC1B0H,SAAS,CAAC1H,QAAD,CAAT,GAAsBsP,GAAG,CAAC3F,mBAAJ,CAAwBjC,SAAS,CAAC1H,QAAD,CAAjC,EAA6CC,MAA7C,CAAtB;UACH;QACJ,CAdD,CAeA,OAAO2U,EAAP,EAAW,CACP;UACA;QACH;MACJ,CA9BD;IA+BH;;IACD,OAAO0H,cAAc,CAACvV,IAAf,CAAoBvD,MAApB,EAA4B5F,IAA5B,EAAkC2e,IAAlC,EAAwC5V,OAAxC,CAAP;EACH,CAnCD;;EAoCA2I,GAAG,CAACzF,qBAAJ,CAA0BrG,MAAM,CAAC4Y,MAAD,CAAhC,EAA0CE,cAA1C;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS1S,gBAAT,CAA0BpG,MAA1B,EAAkCsK,YAAlC,EAAgD4O,gBAAhD,EAAkE;EAC9D,IAAI,CAACA,gBAAD,IAAqBA,gBAAgB,CAACvZ,MAAjB,KAA4B,CAArD,EAAwD;IACpD,OAAO2K,YAAP;EACH;;EACD,MAAM6O,GAAG,GAAGD,gBAAgB,CAACE,MAAjB,CAAwBC,EAAE,IAAIA,EAAE,CAACrZ,MAAH,KAAcA,MAA5C,CAAZ;;EACA,IAAI,CAACmZ,GAAD,IAAQA,GAAG,CAACxZ,MAAJ,KAAe,CAA3B,EAA8B;IAC1B,OAAO2K,YAAP;EACH;;EACD,MAAMgP,sBAAsB,GAAGH,GAAG,CAAC,CAAD,CAAH,CAAOD,gBAAtC;EACA,OAAO5O,YAAY,CAAC8O,MAAb,CAAoBG,EAAE,IAAID,sBAAsB,CAACzN,OAAvB,CAA+B0N,EAA/B,MAAuC,CAAC,CAAlE,CAAP;AACH;;AACD,SAASC,uBAAT,CAAiCxZ,MAAjC,EAAyCsK,YAAzC,EAAuD4O,gBAAvD,EAAyEhV,SAAzE,EAAoF;EAChF;EACA;EACA,IAAI,CAAClE,MAAL,EAAa;IACT;EACH;;EACD,MAAMyZ,kBAAkB,GAAGrT,gBAAgB,CAACpG,MAAD,EAASsK,YAAT,EAAuB4O,gBAAvB,CAA3C;EACA5T,iBAAiB,CAACtF,MAAD,EAASyZ,kBAAT,EAA6BvV,SAA7B,CAAjB;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASwV,eAAT,CAAyB1Z,MAAzB,EAAiC;EAC7B,OAAOiE,MAAM,CAAC0V,mBAAP,CAA2B3Z,MAA3B,EACFoZ,MADE,CACKhf,IAAI,IAAIA,IAAI,CAACwf,UAAL,CAAgB,IAAhB,KAAyBxf,IAAI,CAACuF,MAAL,GAAc,CADpD,EAEFka,GAFE,CAEEzf,IAAI,IAAIA,IAAI,CAAC0f,SAAL,CAAe,CAAf,CAFV,CAAP;AAGH;;AACD,SAASC,uBAAT,CAAiCjO,GAAjC,EAAsCnE,OAAtC,EAA+C;EAC3C,IAAIY,MAAM,IAAI,CAACG,KAAf,EAAsB;IAClB;EACH;;EACD,IAAI/N,IAAI,CAACmR,GAAG,CAAC7G,MAAJ,CAAW,aAAX,CAAD,CAAR,EAAqC;IACjC;IACA;EACH;;EACD,MAAMiU,gBAAgB,GAAGvR,OAAO,CAAC,6BAAD,CAAhC,CAR2C,CAS3C;;EACA,IAAIqS,YAAY,GAAG,EAAnB;;EACA,IAAIvR,SAAJ,EAAe;IACX,MAAMf,cAAc,GAAGlB,MAAvB;IACAwT,YAAY,GAAGA,YAAY,CAACvB,MAAb,CAAoB,CAC/B,UAD+B,EACnB,YADmB,EACL,SADK,EACM,aADN,EACqB,iBADrB,EACwC,kBADxC,EAE/B,qBAF+B,EAER,kBAFQ,EAEY,mBAFZ,EAEiC,oBAFjC,EAEuD,QAFvD,CAApB,CAAf;IAIA,MAAMwB,qBAAqB,GAAGxO,IAAI,KAAK,CAAC;MAAEzL,MAAM,EAAE0H,cAAV;MAA0BwR,gBAAgB,EAAE,CAAC,OAAD;IAA5C,CAAD,CAAL,GAAiE,EAAnG,CANW,CAOX;IACA;;IACAM,uBAAuB,CAAC9R,cAAD,EAAiBgS,eAAe,CAAChS,cAAD,CAAhC,EAAkDwR,gBAAgB,GAAGA,gBAAgB,CAACT,MAAjB,CAAwBwB,qBAAxB,CAAH,GAAoDf,gBAAtH,EAAwIvS,oBAAoB,CAACe,cAAD,CAA5J,CAAvB;EACH;;EACDsS,YAAY,GAAGA,YAAY,CAACvB,MAAb,CAAoB,CAC/B,gBAD+B,EACb,2BADa,EACgB,UADhB,EAC4B,YAD5B,EAC0C,kBAD1C,EAE/B,aAF+B,EAEhB,gBAFgB,EAEE,WAFF,EAEe,WAFf,CAApB,CAAf;;EAIA,KAAK,IAAI/Y,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsa,YAAY,CAACra,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;IAC1C,MAAMM,MAAM,GAAG2H,OAAO,CAACqS,YAAY,CAACta,CAAD,CAAb,CAAtB;IACAM,MAAM,IAAIA,MAAM,CAACkE,SAAjB,IACIsV,uBAAuB,CAACxZ,MAAM,CAACkE,SAAR,EAAmBwV,eAAe,CAAC1Z,MAAM,CAACkE,SAAR,CAAlC,EAAsDgV,gBAAtD,CAD3B;EAEH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAve,IAAI,CAACkB,YAAL,CAAkB,MAAlB,EAA0B,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EAC7C;EACA;EACA,MAAMoO,UAAU,GAAGR,eAAe,CAACzf,MAAD,CAAlC;EACA6R,GAAG,CAACxG,iBAAJ,GAAwBA,iBAAxB;EACAwG,GAAG,CAACvG,WAAJ,GAAkBA,WAAlB;EACAuG,GAAG,CAACtG,aAAJ,GAAoBA,aAApB;EACAsG,GAAG,CAACpG,cAAJ,GAAqBA,cAArB,CAP6C,CAQ7C;EACA;EACA;EACA;EACA;EACA;;EACA,MAAMyU,0BAA0B,GAAGxf,IAAI,CAACH,UAAL,CAAgB,qBAAhB,CAAnC;;EACA,MAAM4f,uBAAuB,GAAGzf,IAAI,CAACH,UAAL,CAAgB,kBAAhB,CAAhC;;EACA,IAAIP,MAAM,CAACmgB,uBAAD,CAAV,EAAqC;IACjCngB,MAAM,CAACkgB,0BAAD,CAAN,GAAqClgB,MAAM,CAACmgB,uBAAD,CAA3C;EACH;;EACD,IAAIngB,MAAM,CAACkgB,0BAAD,CAAV,EAAwC;IACpCxf,IAAI,CAACwf,0BAAD,CAAJ,GAAmCxf,IAAI,CAACyf,uBAAD,CAAJ,GAC/BngB,MAAM,CAACkgB,0BAAD,CADV;EAEH;;EACDrO,GAAG,CAACnG,mBAAJ,GAA0BA,mBAA1B;EACAmG,GAAG,CAACzG,gBAAJ,GAAuBA,gBAAvB;EACAyG,GAAG,CAAClG,UAAJ,GAAiBA,UAAjB;EACAkG,GAAG,CAAChG,oBAAJ,GAA2BA,oBAA3B;EACAgG,GAAG,CAAC/F,8BAAJ,GAAqCA,8BAArC;EACA+F,GAAG,CAAC9F,YAAJ,GAAmBA,YAAnB;EACA8F,GAAG,CAAC7F,UAAJ,GAAiBA,UAAjB;EACA6F,GAAG,CAAC5F,UAAJ,GAAiBA,UAAjB;EACA4F,GAAG,CAAC3F,mBAAJ,GAA0BA,mBAA1B;EACA2F,GAAG,CAAC1F,gBAAJ,GAAuBA,gBAAvB;EACA0F,GAAG,CAACzF,qBAAJ,GAA4BA,qBAA5B;EACAyF,GAAG,CAACxF,iBAAJ,GAAwBrC,MAAM,CAACyC,cAA/B;EACAoF,GAAG,CAACvF,cAAJ,GAAqBA,cAArB;;EACAuF,GAAG,CAACjG,gBAAJ,GAAuB,OAAO;IAC1BkN,aAD0B;IAE1BD,oBAF0B;IAG1BoH,UAH0B;IAI1BzR,SAJ0B;IAK1BC,KAL0B;IAM1BH,MAN0B;IAO1BnB,QAP0B;IAQ1BC,SAR0B;IAS1BC,kBAT0B;IAU1BN,sBAV0B;IAW1BC;EAX0B,CAAP,CAAvB;AAaH,CAjDD;AAmDA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoT,UAAU,GAAG7S,UAAU,CAAC,UAAD,CAA7B;;AACA,SAAS8S,UAAT,CAAoB9T,MAApB,EAA4B+T,OAA5B,EAAqCC,UAArC,EAAiDC,UAAjD,EAA6D;EACzD,IAAItP,SAAS,GAAG,IAAhB;EACA,IAAIuP,WAAW,GAAG,IAAlB;EACAH,OAAO,IAAIE,UAAX;EACAD,UAAU,IAAIC,UAAd;EACA,MAAME,eAAe,GAAG,EAAxB;;EACA,SAASlc,YAAT,CAAsBpB,IAAtB,EAA4B;IACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAlB;;IACAA,IAAI,CAACsF,IAAL,CAAU,CAAV,IAAe,YAAY;MACvB,OAAOnG,IAAI,CAACJ,MAAL,CAAYwF,KAAZ,CAAkB,IAAlB,EAAwB5F,SAAxB,CAAP;IACH,CAFD;;IAGAqB,IAAI,CAAC8F,QAAL,GAAgBmH,SAAS,CAAC1I,KAAV,CAAgB+D,MAAhB,EAAwBtI,IAAI,CAACsF,IAA7B,CAAhB;IACA,OAAOnG,IAAP;EACH;;EACD,SAASud,SAAT,CAAmBvd,IAAnB,EAAyB;IACrB,OAAOqd,WAAW,CAACnX,IAAZ,CAAiBiD,MAAjB,EAAyBnJ,IAAI,CAACa,IAAL,CAAU8F,QAAnC,CAAP;EACH;;EACDmH,SAAS,GACL5F,WAAW,CAACiB,MAAD,EAAS+T,OAAT,EAAmBza,QAAD,IAAc,UAAUuD,IAAV,EAAgBG,IAAhB,EAAsB;IAC7D,IAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,UAAvB,EAAmC;MAC/B,MAAML,OAAO,GAAG;QACZhF,UAAU,EAAEsc,UAAU,KAAK,UADf;QAEZI,KAAK,EAAGJ,UAAU,KAAK,SAAf,IAA4BA,UAAU,KAAK,UAA5C,GAA0DjX,IAAI,CAAC,CAAD,CAAJ,IAAW,CAArE,GACHnF,SAHQ;QAIZmF,IAAI,EAAEA;MAJM,CAAhB;MAMA,MAAMhH,QAAQ,GAAGgH,IAAI,CAAC,CAAD,CAArB;;MACAA,IAAI,CAAC,CAAD,CAAJ,GAAU,SAASsX,KAAT,GAAiB;QACvB,IAAI;UACA,OAAOte,QAAQ,CAACiG,KAAT,CAAe,IAAf,EAAqB5F,SAArB,CAAP;QACH,CAFD,SAGQ;UACJ;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAI,CAAEsG,OAAO,CAAChF,UAAd,EAA2B;YACvB,IAAI,OAAOgF,OAAO,CAACa,QAAf,KAA4B,QAAhC,EAA0C;cACtC;cACA;cACA,OAAO2W,eAAe,CAACxX,OAAO,CAACa,QAAT,CAAtB;YACH,CAJD,MAKK,IAAIb,OAAO,CAACa,QAAZ,EAAsB;cACvB;cACA;cACAb,OAAO,CAACa,QAAR,CAAiBqW,UAAjB,IAA+B,IAA/B;YACH;UACJ;QACJ;MACJ,CAzBD;;MA0BA,MAAMhd,IAAI,GAAGkK,gCAAgC,CAACgT,OAAD,EAAU/W,IAAI,CAAC,CAAD,CAAd,EAAmBL,OAAnB,EAA4B1E,YAA5B,EAA0Cmc,SAA1C,CAA7C;;MACA,IAAI,CAACvd,IAAL,EAAW;QACP,OAAOA,IAAP;MACH,CArC8B,CAsC/B;;;MACA,MAAM0d,MAAM,GAAG1d,IAAI,CAACa,IAAL,CAAU8F,QAAzB;;MACA,IAAI,OAAO+W,MAAP,KAAkB,QAAtB,EAAgC;QAC5B;QACA;QACAJ,eAAe,CAACI,MAAD,CAAf,GAA0B1d,IAA1B;MACH,CAJD,MAKK,IAAI0d,MAAJ,EAAY;QACb;QACA;QACAA,MAAM,CAACV,UAAD,CAAN,GAAqBhd,IAArB;MACH,CAjD8B,CAkD/B;MACA;;;MACA,IAAI0d,MAAM,IAAIA,MAAM,CAACC,GAAjB,IAAwBD,MAAM,CAACE,KAA/B,IAAwC,OAAOF,MAAM,CAACC,GAAd,KAAsB,UAA9D,IACA,OAAOD,MAAM,CAACE,KAAd,KAAwB,UAD5B,EACwC;QACpC5d,IAAI,CAAC2d,GAAL,GAAWD,MAAM,CAACC,GAAP,CAAWE,IAAX,CAAgBH,MAAhB,CAAX;QACA1d,IAAI,CAAC4d,KAAL,GAAaF,MAAM,CAACE,KAAP,CAAaC,IAAb,CAAkBH,MAAlB,CAAb;MACH;;MACD,IAAI,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAlC,EAA0C;QACtC,OAAOA,MAAP;MACH;;MACD,OAAO1d,IAAP;IACH,CA7DD,MA8DK;MACD;MACA,OAAOyC,QAAQ,CAAC2C,KAAT,CAAe+D,MAAf,EAAuBhD,IAAvB,CAAP;IACH;EACJ,CAnEU,CADf;EAqEAkX,WAAW,GACPnV,WAAW,CAACiB,MAAD,EAASgU,UAAT,EAAsB1a,QAAD,IAAc,UAAUuD,IAAV,EAAgBG,IAAhB,EAAsB;IAChE,MAAM2X,EAAE,GAAG3X,IAAI,CAAC,CAAD,CAAf;IACA,IAAInG,IAAJ;;IACA,IAAI,OAAO8d,EAAP,KAAc,QAAlB,EAA4B;MACxB;MACA9d,IAAI,GAAGsd,eAAe,CAACQ,EAAD,CAAtB;IACH,CAHD,MAIK;MACD;MACA9d,IAAI,GAAG8d,EAAE,IAAIA,EAAE,CAACd,UAAD,CAAf,CAFC,CAGD;;MACA,IAAI,CAAChd,IAAL,EAAW;QACPA,IAAI,GAAG8d,EAAP;MACH;IACJ;;IACD,IAAI9d,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAZ,KAAqB,QAAjC,EAA2C;MACvC,IAAIJ,IAAI,CAACE,KAAL,KAAe,cAAf,KACCF,IAAI,CAACe,QAAL,IAAiBf,IAAI,CAACa,IAAL,CAAUC,UAA3B,IAAyCd,IAAI,CAACW,QAAL,KAAkB,CAD5D,CAAJ,EACoE;QAChE,IAAI,OAAOmd,EAAP,KAAc,QAAlB,EAA4B;UACxB,OAAOR,eAAe,CAACQ,EAAD,CAAtB;QACH,CAFD,MAGK,IAAIA,EAAJ,EAAQ;UACTA,EAAE,CAACd,UAAD,CAAF,GAAiB,IAAjB;QACH,CAN+D,CAOhE;;;QACAhd,IAAI,CAAC7B,IAAL,CAAU+D,UAAV,CAAqBlC,IAArB;MACH;IACJ,CAZD,MAaK;MACD;MACAyC,QAAQ,CAAC2C,KAAT,CAAe+D,MAAf,EAAuBhD,IAAvB;IACH;EACJ,CAhCU,CADf;AAkCH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS4X,mBAAT,CAA6BzT,OAA7B,EAAsCmE,GAAtC,EAA2C;EACvC,MAAM;IAAErD,SAAF;IAAaC;EAAb,IAAuBoD,GAAG,CAACjG,gBAAJ,EAA7B;;EACA,IAAK,CAAC4C,SAAD,IAAc,CAACC,KAAhB,IAA0B,CAACf,OAAO,CAAC,gBAAD,CAAlC,IAAwD,EAAE,oBAAoBA,OAAtB,CAA5D,EAA4F;IACxF;EACH;;EACD,MAAMkR,SAAS,GAAG,CAAC,mBAAD,EAAsB,sBAAtB,EAA8C,iBAA9C,EAAiE,0BAAjE,CAAlB;EACA/M,GAAG,CAACvF,cAAJ,CAAmBuF,GAAnB,EAAwBnE,OAAO,CAAC0T,cAAhC,EAAgD,gBAAhD,EAAkE,QAAlE,EAA4ExC,SAA5E;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASyC,gBAAT,CAA0B3T,OAA1B,EAAmCmE,GAAnC,EAAwC;EACpC,IAAInR,IAAI,CAACmR,GAAG,CAAC7G,MAAJ,CAAW,kBAAX,CAAD,CAAR,EAA0C;IACtC;IACA;EACH;;EACD,MAAM;IAAEiV,UAAF;IAAcpH,oBAAd;IAAoC1L,QAApC;IAA8CC,SAA9C;IAAyDC;EAAzD,IAAgFwE,GAAG,CAACjG,gBAAJ,EAAtF,CALoC,CAMpC;;EACA,KAAK,IAAInG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwa,UAAU,CAACva,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;IACxC,MAAMsK,SAAS,GAAGkQ,UAAU,CAACxa,CAAD,CAA5B;IACA,MAAM2T,cAAc,GAAGrJ,SAAS,GAAG3C,SAAnC;IACA,MAAMiM,aAAa,GAAGtJ,SAAS,GAAG5C,QAAlC;IACA,MAAMnC,MAAM,GAAGqC,kBAAkB,GAAG+L,cAApC;IACA,MAAME,aAAa,GAAGjM,kBAAkB,GAAGgM,aAA3C;IACAR,oBAAoB,CAAC9I,SAAD,CAApB,GAAkC,EAAlC;IACA8I,oBAAoB,CAAC9I,SAAD,CAApB,CAAgC3C,SAAhC,IAA6CpC,MAA7C;IACA6N,oBAAoB,CAAC9I,SAAD,CAApB,CAAgC5C,QAAhC,IAA4CmM,aAA5C;EACH;;EACD,MAAMgI,YAAY,GAAG5T,OAAO,CAAC,aAAD,CAA5B;;EACA,IAAI,CAAC4T,YAAD,IAAiB,CAACA,YAAY,CAACrX,SAAnC,EAA8C;IAC1C;EACH;;EACD4H,GAAG,CAACzG,gBAAJ,CAAqBsC,OAArB,EAA8BmE,GAA9B,EAAmC,CAACyP,YAAY,IAAIA,YAAY,CAACrX,SAA9B,CAAnC;EACA,OAAO,IAAP;AACH;;AACD,SAASsX,UAAT,CAAoBvhB,MAApB,EAA4B6R,GAA5B,EAAiC;EAC7BA,GAAG,CAACnG,mBAAJ,CAAwB1L,MAAxB,EAAgC6R,GAAhC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAnR,IAAI,CAACkB,YAAL,CAAkB,QAAlB,EAA6B5B,MAAD,IAAY;EACpC,MAAMwhB,WAAW,GAAGxhB,MAAM,CAACU,IAAI,CAACH,UAAL,CAAgB,aAAhB,CAAD,CAA1B;;EACA,IAAIihB,WAAJ,EAAiB;IACbA,WAAW;EACd;AACJ,CALD;;AAMA9gB,IAAI,CAACkB,YAAL,CAAkB,gBAAlB,EAAoC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACvDA,GAAG,CAACvG,WAAJ,CAAgBtL,MAAhB,EAAwB,gBAAxB,EAA0C6F,QAAQ,IAAI;IAClD,OAAO,UAAUuD,IAAV,EAAgBG,IAAhB,EAAsB;MACzB7I,IAAI,CAACc,OAAL,CAAauD,iBAAb,CAA+B,gBAA/B,EAAiDwE,IAAI,CAAC,CAAD,CAArD;IACH,CAFD;EAGH,CAJD;AAKH,CAND;;AAOA7I,IAAI,CAACkB,YAAL,CAAkB,QAAlB,EAA6B5B,MAAD,IAAY;EACpC,MAAMmO,GAAG,GAAG,KAAZ;EACA,MAAMsT,KAAK,GAAG,OAAd;EACApB,UAAU,CAACrgB,MAAD,EAASmO,GAAT,EAAcsT,KAAd,EAAqB,SAArB,CAAV;EACApB,UAAU,CAACrgB,MAAD,EAASmO,GAAT,EAAcsT,KAAd,EAAqB,UAArB,CAAV;EACApB,UAAU,CAACrgB,MAAD,EAASmO,GAAT,EAAcsT,KAAd,EAAqB,WAArB,CAAV;AACH,CAND;;AAOA/gB,IAAI,CAACkB,YAAL,CAAkB,uBAAlB,EAA4C5B,MAAD,IAAY;EACnDqgB,UAAU,CAACrgB,MAAD,EAAS,SAAT,EAAoB,QAApB,EAA8B,gBAA9B,CAAV;EACAqgB,UAAU,CAACrgB,MAAD,EAAS,YAAT,EAAuB,WAAvB,EAAoC,gBAApC,CAAV;EACAqgB,UAAU,CAACrgB,MAAD,EAAS,eAAT,EAA0B,cAA1B,EAA0C,gBAA1C,CAAV;AACH,CAJD;;AAKAU,IAAI,CAACkB,YAAL,CAAkB,UAAlB,EAA8B,CAAC5B,MAAD,EAASU,IAAT,KAAkB;EAC5C,MAAMghB,eAAe,GAAG,CAAC,OAAD,EAAU,QAAV,EAAoB,SAApB,CAAxB;;EACA,KAAK,IAAIjc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGic,eAAe,CAAChc,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;IAC7C,MAAMtF,IAAI,GAAGuhB,eAAe,CAACjc,CAAD,CAA5B;IACA6F,WAAW,CAACtL,MAAD,EAASG,IAAT,EAAe,CAAC0F,QAAD,EAAWmF,MAAX,EAAmB7K,IAAnB,KAA4B;MAClD,OAAO,UAAUwhB,CAAV,EAAapY,IAAb,EAAmB;QACtB,OAAO7I,IAAI,CAACc,OAAL,CAAaqB,GAAb,CAAiBgD,QAAjB,EAA2B7F,MAA3B,EAAmCuJ,IAAnC,EAAyCpJ,IAAzC,CAAP;MACH,CAFD;IAGH,CAJU,CAAX;EAKH;AACJ,CAVD;;AAWAO,IAAI,CAACkB,YAAL,CAAkB,aAAlB,EAAiC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACpD0P,UAAU,CAACvhB,MAAD,EAAS6R,GAAT,CAAV;EACAwP,gBAAgB,CAACrhB,MAAD,EAAS6R,GAAT,CAAhB,CAFoD,CAGpD;;EACA,MAAM+P,yBAAyB,GAAG5hB,MAAM,CAAC,2BAAD,CAAxC;;EACA,IAAI4hB,yBAAyB,IAAIA,yBAAyB,CAAC3X,SAA3D,EAAsE;IAClE4H,GAAG,CAACzG,gBAAJ,CAAqBpL,MAArB,EAA6B6R,GAA7B,EAAkC,CAAC+P,yBAAyB,CAAC3X,SAA3B,CAAlC;EACH;AACJ,CARD;;AASAvJ,IAAI,CAACkB,YAAL,CAAkB,kBAAlB,EAAsC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACzD5F,UAAU,CAAC,kBAAD,CAAV;EACAA,UAAU,CAAC,wBAAD,CAAV;AACH,CAHD;;AAIAvL,IAAI,CAACkB,YAAL,CAAkB,sBAAlB,EAA0C,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EAC7D5F,UAAU,CAAC,sBAAD,CAAV;AACH,CAFD;;AAGAvL,IAAI,CAACkB,YAAL,CAAkB,YAAlB,EAAgC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACnD5F,UAAU,CAAC,YAAD,CAAV;AACH,CAFD;;AAGAvL,IAAI,CAACkB,YAAL,CAAkB,aAAlB,EAAiC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACpDiO,uBAAuB,CAACjO,GAAD,EAAM7R,MAAN,CAAvB;AACH,CAFD;;AAGAU,IAAI,CAACkB,YAAL,CAAkB,gBAAlB,EAAoC,CAAC5B,MAAD,EAASU,IAAT,EAAemR,GAAf,KAAuB;EACvDsP,mBAAmB,CAACnhB,MAAD,EAAS6R,GAAT,CAAnB;AACH,CAFD;;AAGAnR,IAAI,CAACkB,YAAL,CAAkB,KAAlB,EAAyB,CAAC5B,MAAD,EAASU,IAAT,KAAkB;EACvC;EACAmhB,QAAQ,CAAC7hB,MAAD,CAAR;EACA,MAAM8hB,QAAQ,GAAGvU,UAAU,CAAC,SAAD,CAA3B;EACA,MAAMwU,QAAQ,GAAGxU,UAAU,CAAC,SAAD,CAA3B;EACA,MAAMyU,YAAY,GAAGzU,UAAU,CAAC,aAAD,CAA/B;EACA,MAAM0U,aAAa,GAAG1U,UAAU,CAAC,cAAD,CAAhC;EACA,MAAM2U,OAAO,GAAG3U,UAAU,CAAC,QAAD,CAA1B;EACA,MAAM4U,0BAA0B,GAAG5U,UAAU,CAAC,yBAAD,CAA7C;;EACA,SAASsU,QAAT,CAAkBtV,MAAlB,EAA0B;IACtB,MAAM6V,cAAc,GAAG7V,MAAM,CAAC,gBAAD,CAA7B;;IACA,IAAI,CAAC6V,cAAL,EAAqB;MACjB;MACA;IACH;;IACD,MAAMC,uBAAuB,GAAGD,cAAc,CAACnY,SAA/C;;IACA,SAASqY,eAAT,CAAyBvc,MAAzB,EAAiC;MAC7B,OAAOA,MAAM,CAAC+b,QAAD,CAAb;IACH;;IACD,IAAIS,cAAc,GAAGF,uBAAuB,CAACpV,8BAAD,CAA5C;IACA,IAAIuV,iBAAiB,GAAGH,uBAAuB,CAACnV,iCAAD,CAA/C;;IACA,IAAI,CAACqV,cAAL,EAAqB;MACjB,MAAMX,yBAAyB,GAAGrV,MAAM,CAAC,2BAAD,CAAxC;;MACA,IAAIqV,yBAAJ,EAA+B;QAC3B,MAAMa,kCAAkC,GAAGb,yBAAyB,CAAC3X,SAArE;QACAsY,cAAc,GAAGE,kCAAkC,CAACxV,8BAAD,CAAnD;QACAuV,iBAAiB,GAAGC,kCAAkC,CAACvV,iCAAD,CAAtD;MACH;IACJ;;IACD,MAAMwV,kBAAkB,GAAG,kBAA3B;IACA,MAAMC,SAAS,GAAG,WAAlB;;IACA,SAASne,YAAT,CAAsBpB,IAAtB,EAA4B;MACxB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAlB;MACA,MAAM8B,MAAM,GAAG9B,IAAI,CAAC8B,MAApB;MACAA,MAAM,CAACkc,aAAD,CAAN,GAAwB,KAAxB;MACAlc,MAAM,CAACoc,0BAAD,CAAN,GAAqC,KAArC,CAJwB,CAKxB;;MACA,MAAMrT,QAAQ,GAAG/I,MAAM,CAACic,YAAD,CAAvB;;MACA,IAAI,CAACO,cAAL,EAAqB;QACjBA,cAAc,GAAGxc,MAAM,CAACkH,8BAAD,CAAvB;QACAuV,iBAAiB,GAAGzc,MAAM,CAACmH,iCAAD,CAA1B;MACH;;MACD,IAAI4B,QAAJ,EAAc;QACV0T,iBAAiB,CAAClZ,IAAlB,CAAuBvD,MAAvB,EAA+B2c,kBAA/B,EAAmD5T,QAAnD;MACH;;MACD,MAAM8T,WAAW,GAAG7c,MAAM,CAACic,YAAD,CAAN,GAAuB,MAAM;QAC7C,IAAIjc,MAAM,CAAC8c,UAAP,KAAsB9c,MAAM,CAAC+c,IAAjC,EAAuC;UACnC;UACA;UACA,IAAI,CAAC7e,IAAI,CAAC8e,OAAN,IAAiBhd,MAAM,CAACkc,aAAD,CAAvB,IAA0C7e,IAAI,CAACE,KAAL,KAAeqf,SAA7D,EAAwE;YACpE;YACA;YACA;YACA;YACA;YACA;YACA;YACA,MAAMK,SAAS,GAAGjd,MAAM,CAACrF,IAAI,CAACH,UAAL,CAAgB,WAAhB,CAAD,CAAxB;;YACA,IAAIwF,MAAM,CAACyQ,MAAP,KAAkB,CAAlB,IAAuBwM,SAAvB,IAAoCA,SAAS,CAACtd,MAAV,GAAmB,CAA3D,EAA8D;cAC1D,MAAMud,SAAS,GAAG7f,IAAI,CAACJ,MAAvB;;cACAI,IAAI,CAACJ,MAAL,GAAc,YAAY;gBACtB;gBACA;gBACA,MAAMggB,SAAS,GAAGjd,MAAM,CAACrF,IAAI,CAACH,UAAL,CAAgB,WAAhB,CAAD,CAAxB;;gBACA,KAAK,IAAIkF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGud,SAAS,CAACtd,MAA9B,EAAsCD,CAAC,EAAvC,EAA2C;kBACvC,IAAIud,SAAS,CAACvd,CAAD,CAAT,KAAiBrC,IAArB,EAA2B;oBACvB4f,SAAS,CAACnO,MAAV,CAAiBpP,CAAjB,EAAoB,CAApB;kBACH;gBACJ;;gBACD,IAAI,CAACxB,IAAI,CAAC8e,OAAN,IAAiB3f,IAAI,CAACE,KAAL,KAAeqf,SAApC,EAA+C;kBAC3CM,SAAS,CAAC3Z,IAAV,CAAelG,IAAf;gBACH;cACJ,CAZD;;cAaA4f,SAAS,CAACta,IAAV,CAAetF,IAAf;YACH,CAhBD,MAiBK;cACDA,IAAI,CAACJ,MAAL;YACH;UACJ,CA7BD,MA8BK,IAAI,CAACiB,IAAI,CAAC8e,OAAN,IAAiBhd,MAAM,CAACkc,aAAD,CAAN,KAA0B,KAA/C,EAAsD;YACvD;YACAlc,MAAM,CAACoc,0BAAD,CAAN,GAAqC,IAArC;UACH;QACJ;MACJ,CAvCD;;MAwCAI,cAAc,CAACjZ,IAAf,CAAoBvD,MAApB,EAA4B2c,kBAA5B,EAAgDE,WAAhD;MACA,MAAMM,UAAU,GAAGnd,MAAM,CAAC+b,QAAD,CAAzB;;MACA,IAAI,CAACoB,UAAL,EAAiB;QACbnd,MAAM,CAAC+b,QAAD,CAAN,GAAmB1e,IAAnB;MACH;;MACD+f,UAAU,CAAC3a,KAAX,CAAiBzC,MAAjB,EAAyB9B,IAAI,CAACsF,IAA9B;MACAxD,MAAM,CAACkc,aAAD,CAAN,GAAwB,IAAxB;MACA,OAAO7e,IAAP;IACH;;IACD,SAASggB,mBAAT,GAA+B,CAAG;;IAClC,SAASzC,SAAT,CAAmBvd,IAAnB,EAAyB;MACrB,MAAMa,IAAI,GAAGb,IAAI,CAACa,IAAlB,CADqB,CAErB;MACA;;MACAA,IAAI,CAAC8e,OAAL,GAAe,IAAf;MACA,OAAOM,WAAW,CAAC7a,KAAZ,CAAkBvE,IAAI,CAAC8B,MAAvB,EAA+B9B,IAAI,CAACsF,IAApC,CAAP;IACH;;IACD,MAAM+Z,UAAU,GAAGhY,WAAW,CAAC+W,uBAAD,EAA0B,MAA1B,EAAkC,MAAM,UAAUjZ,IAAV,EAAgBG,IAAhB,EAAsB;MACxFH,IAAI,CAAC2Y,QAAD,CAAJ,GAAiBxY,IAAI,CAAC,CAAD,CAAJ,IAAW,KAA5B;MACAH,IAAI,CAAC8Y,OAAD,CAAJ,GAAgB3Y,IAAI,CAAC,CAAD,CAApB;MACA,OAAO+Z,UAAU,CAAC9a,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;IACH,CAJ6B,CAA9B;IAKA,MAAMga,qBAAqB,GAAG,qBAA9B;IACA,MAAMC,iBAAiB,GAAGjW,UAAU,CAAC,mBAAD,CAApC;IACA,MAAMkW,mBAAmB,GAAGlW,UAAU,CAAC,qBAAD,CAAtC;IACA,MAAM4V,UAAU,GAAG7X,WAAW,CAAC+W,uBAAD,EAA0B,MAA1B,EAAkC,MAAM,UAAUjZ,IAAV,EAAgBG,IAAhB,EAAsB;MACxF,IAAI7I,IAAI,CAACc,OAAL,CAAaiiB,mBAAb,MAAsC,IAA1C,EAAgD;QAC5C;QACA;QACA;QACA,OAAON,UAAU,CAAC3a,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;MACH;;MACD,IAAIH,IAAI,CAAC2Y,QAAD,CAAR,EAAoB;QAChB;QACA,OAAOoB,UAAU,CAAC3a,KAAX,CAAiBY,IAAjB,EAAuBG,IAAvB,CAAP;MACH,CAHD,MAIK;QACD,MAAML,OAAO,GAAG;UAAEnD,MAAM,EAAEqD,IAAV;UAAgBsa,GAAG,EAAEta,IAAI,CAAC8Y,OAAD,CAAzB;UAAoChe,UAAU,EAAE,KAAhD;UAAuDqF,IAAI,EAAEA,IAA7D;UAAmEwZ,OAAO,EAAE;QAA5E,CAAhB;QACA,MAAM3f,IAAI,GAAGkK,gCAAgC,CAACiW,qBAAD,EAAwBH,mBAAxB,EAA6Cla,OAA7C,EAAsD1E,YAAtD,EAAoEmc,SAApE,CAA7C;;QACA,IAAIvX,IAAI,IAAIA,IAAI,CAAC+Y,0BAAD,CAAJ,KAAqC,IAA7C,IAAqD,CAACjZ,OAAO,CAAC6Z,OAA9D,IACA3f,IAAI,CAACE,KAAL,KAAeqf,SADnB,EAC8B;UAC1B;UACA;UACA;UACAvf,IAAI,CAACJ,MAAL;QACH;MACJ;IACJ,CAtB6B,CAA9B;IAuBA,MAAMqgB,WAAW,GAAG/X,WAAW,CAAC+W,uBAAD,EAA0B,OAA1B,EAAmC,MAAM,UAAUjZ,IAAV,EAAgBG,IAAhB,EAAsB;MAC1F,MAAMnG,IAAI,GAAGkf,eAAe,CAAClZ,IAAD,CAA5B;;MACA,IAAIhG,IAAI,IAAI,OAAOA,IAAI,CAACI,IAAZ,IAAoB,QAAhC,EAA0C;QACtC;QACA;QACA;QACA;QACA,IAAIJ,IAAI,CAACe,QAAL,IAAiB,IAAjB,IAA0Bf,IAAI,CAACa,IAAL,IAAab,IAAI,CAACa,IAAL,CAAU8e,OAArD,EAA+D;UAC3D;QACH;;QACD3f,IAAI,CAAC7B,IAAL,CAAU+D,UAAV,CAAqBlC,IAArB;MACH,CATD,MAUK,IAAI1C,IAAI,CAACc,OAAL,CAAagiB,iBAAb,MAAoC,IAAxC,EAA8C;QAC/C;QACA,OAAOH,WAAW,CAAC7a,KAAZ,CAAkBY,IAAlB,EAAwBG,IAAxB,CAAP;MACH,CAfyF,CAgB1F;MACA;MACA;;IACH,CAnB8B,CAA/B;EAoBH;AACJ,CA1JD;;AA2JA7I,IAAI,CAACkB,YAAL,CAAkB,aAAlB,EAAkC5B,MAAD,IAAY;EACzC;EACA,IAAIA,MAAM,CAAC,WAAD,CAAN,IAAuBA,MAAM,CAAC,WAAD,CAAN,CAAoB2jB,WAA/C,EAA4D;IACxD/V,cAAc,CAAC5N,MAAM,CAAC,WAAD,CAAN,CAAoB2jB,WAArB,EAAkC,CAAC,oBAAD,EAAuB,eAAvB,CAAlC,CAAd;EACH;AACJ,CALD;;AAMAjjB,IAAI,CAACkB,YAAL,CAAkB,uBAAlB,EAA2C,CAAC5B,MAAD,EAASU,IAAT,KAAkB;EACzD;EACA,SAASkjB,2BAAT,CAAqC5F,OAArC,EAA8C;IAC1C,OAAO,UAAU5L,CAAV,EAAa;MAChB,MAAMyR,UAAU,GAAGjG,cAAc,CAAC5d,MAAD,EAASge,OAAT,CAAjC;MACA6F,UAAU,CAAC9E,OAAX,CAAmBtb,SAAS,IAAI;QAC5B;QACA;QACA,MAAMqgB,qBAAqB,GAAG9jB,MAAM,CAAC,uBAAD,CAApC;;QACA,IAAI8jB,qBAAJ,EAA2B;UACvB,MAAMC,GAAG,GAAG,IAAID,qBAAJ,CAA0B9F,OAA1B,EAAmC;YAAElK,OAAO,EAAE1B,CAAC,CAAC0B,OAAb;YAAsB4C,MAAM,EAAEtE,CAAC,CAACC;UAAhC,CAAnC,CAAZ;UACA5O,SAAS,CAACT,MAAV,CAAiB+gB,GAAjB;QACH;MACJ,CARD;IASH,CAXD;EAYH;;EACD,IAAI/jB,MAAM,CAAC,uBAAD,CAAV,EAAqC;IACjCU,IAAI,CAAC6M,UAAU,CAAC,kCAAD,CAAX,CAAJ,GACIqW,2BAA2B,CAAC,oBAAD,CAD/B;IAEAljB,IAAI,CAAC6M,UAAU,CAAC,yBAAD,CAAX,CAAJ,GACIqW,2BAA2B,CAAC,kBAAD,CAD/B;EAEH;AACJ,CAtBD"}, "metadata": {}, "sourceType": "script"}