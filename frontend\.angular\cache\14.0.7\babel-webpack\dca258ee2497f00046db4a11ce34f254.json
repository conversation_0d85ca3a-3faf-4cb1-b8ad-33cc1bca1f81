{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../services/reports.service\";\nimport * as i2 from \"../../../../services/team.service\";\nimport * as i3 from \"../../../service/user.service\";\nimport * as i4 from \"../../../../services/formation.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"primeng/toast\";\nimport * as i13 from \"primeng/progressbar\";\nimport * as i14 from \"primeng/tag\";\nimport * as i15 from \"@angular/common\";\n\nfunction ReportsComponent_ng_template_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Employee\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Formation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Trainer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Attendance Rate\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c0 = function () {\n  return {\n    \"height\": \"20px\"\n  };\n};\n\nfunction ReportsComponent_ng_template_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵelement(15, \"p-tag\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵelement(17, \"p-progressBar\", 30);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const report_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r8.employeeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r8.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r8.team);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r8.formationName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 12, report_r8.formationDate, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r8.trainer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", report_r8.status)(\"severity\", ctx_r1.getStatusSeverity(report_r8.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(15, _c0));\n    i0.ɵɵproperty(\"value\", report_r8.attendanceRate)(\"showValue\", true);\n  }\n}\n\nfunction ReportsComponent_ng_template_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 31);\n    i0.ɵɵtext(2, \"No attendance data found\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ReportsComponent_ng_template_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Formation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Trainer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Total Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Present\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Absent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\");\n    i0.ɵɵtext(16, \"Attendance Rate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\");\n    i0.ɵɵtext(18, \"Status\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ReportsComponent_ng_template_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 32);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 33);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵelement(19, \"p-progressBar\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\");\n    i0.ɵɵelement(21, \"p-tag\", 29);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const report_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 13, report_r9.date, \"short\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r9.trainer);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r9.team);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r9.totalParticipants);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r9.presentCount);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r9.absentCount);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(16, _c0));\n    i0.ɵɵproperty(\"value\", report_r9.attendanceRate)(\"showValue\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", report_r9.status)(\"severity\", ctx_r4.getFormationStatusSeverity(report_r9.status));\n  }\n}\n\nfunction ReportsComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\");\n    i0.ɵɵtext(2, \"Team\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Speciality\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Total Employees\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Total Formations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\");\n    i0.ɵɵtext(12, \"Upcoming\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\");\n    i0.ɵɵtext(14, \"Avg Attendance Rate\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nfunction ReportsComponent_ng_template_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 32);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 34);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵelement(16, \"p-progressBar\", 30);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const report_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r10.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r10.speciality || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r10.totalEmployees);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r10.totalFormations);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r10.completedFormations);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r10.upcomingFormations);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c0));\n    i0.ɵɵproperty(\"value\", report_r10.averageAttendanceRate)(\"showValue\", true);\n  }\n}\n\nfunction ReportsComponent_ng_template_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 35);\n    i0.ɵɵtext(2, \"No team data found\");\n    i0.ɵɵelementEnd()();\n  }\n}\n\nconst _c1 = function () {\n  return [10, 20, 50];\n};\n\nexport class ReportsComponent {\n  constructor(reportsService, teamService, userService, formationService, messageService) {\n    this.reportsService = reportsService;\n    this.teamService = teamService;\n    this.userService = userService;\n    this.formationService = formationService;\n    this.messageService = messageService; // Data\n\n    this.attendanceReports = [];\n    this.formationReports = [];\n    this.teamReports = []; // Filter options\n\n    this.teams = [];\n    this.trainers = [];\n    this.formations = []; // Loading states\n\n    this.loadingAttendance = false;\n    this.loadingFormations = false;\n    this.loadingTeams = false; // Filters\n\n    this.filters = {};\n    this.selectedTab = 0; // Date range\n\n    this.startDate = null;\n    this.endDate = null;\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      yield _this.loadFilterOptions();\n      yield _this.loadAllReports();\n    })();\n  }\n\n  loadFilterOptions() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        // Load teams\n        _this2.teams = yield lastValueFrom(_this2.teamService.getTeams()); // Load trainers\n\n        const trainersData = yield _this2.userService.getUsersByRole('formateur');\n        _this2.trainers = trainersData.map(trainer => Object.assign(Object.assign({}, trainer), {\n          name: `${trainer.first_name} ${trainer.last_name}`\n        })); // Load formations\n\n        _this2.formations = yield _this2.formationService.getFormations();\n      } catch (error) {\n        console.error('Error loading filter options:', error);\n\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load filter options',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  loadAllReports() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      yield Promise.all([_this3.loadAttendanceReport(), _this3.loadFormationReport(), _this3.loadTeamReport()]);\n    })();\n  }\n\n  loadAttendanceReport() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      _this4.loadingAttendance = true;\n\n      try {\n        _this4.attendanceReports = yield lastValueFrom(_this4.reportsService.getAttendanceReport(_this4.filters));\n      } catch (error) {\n        console.error('Error loading attendance report:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load attendance report',\n          life: 3000\n        });\n      } finally {\n        _this4.loadingAttendance = false;\n      }\n    })();\n  }\n\n  loadFormationReport() {\n    var _this5 = this;\n\n    return _asyncToGenerator(function* () {\n      _this5.loadingFormations = true;\n\n      try {\n        _this5.formationReports = yield lastValueFrom(_this5.reportsService.getFormationReport(_this5.filters));\n      } catch (error) {\n        console.error('Error loading formation report:', error);\n\n        _this5.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load formation report',\n          life: 3000\n        });\n      } finally {\n        _this5.loadingFormations = false;\n      }\n    })();\n  }\n\n  loadTeamReport() {\n    var _this6 = this;\n\n    return _asyncToGenerator(function* () {\n      _this6.loadingTeams = true;\n\n      try {\n        _this6.teamReports = yield lastValueFrom(_this6.reportsService.getTeamReport(_this6.filters));\n      } catch (error) {\n        console.error('Error loading team report:', error);\n\n        _this6.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load team report',\n          life: 3000\n        });\n      } finally {\n        _this6.loadingTeams = false;\n      }\n    })();\n  }\n\n  applyFilters() {\n    // Update filters object\n    this.filters = {\n      startDate: this.startDate ? this.startDate.toISOString().split('T')[0] : undefined,\n      endDate: this.endDate ? this.endDate.toISOString().split('T')[0] : undefined,\n      teamId: this.filters.teamId,\n      trainerId: this.filters.trainerId,\n      formationId: this.filters.formationId,\n      status: this.filters.status\n    }; // Reload reports with new filters\n\n    this.loadAllReports();\n  }\n\n  clearFilters() {\n    this.filters = {};\n    this.startDate = null;\n    this.endDate = null;\n    this.loadAllReports();\n  } // Export methods\n\n\n  exportAttendancePDF() {\n    var _this7 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this7.reportsService.exportAttendancePDF(_this7.filters));\n\n        _this7.downloadFile(blob, 'attendance-report.pdf');\n\n        _this7.showSuccessMessage('PDF exported successfully');\n      } catch (error) {\n        _this7.showErrorMessage('Failed to export PDF');\n      }\n    })();\n  }\n\n  exportFormationPDF() {\n    var _this8 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this8.reportsService.exportFormationPDF(_this8.filters));\n\n        _this8.downloadFile(blob, 'formation-report.pdf');\n\n        _this8.showSuccessMessage('PDF exported successfully');\n      } catch (error) {\n        _this8.showErrorMessage('Failed to export PDF');\n      }\n    })();\n  }\n\n  exportTeamPDF() {\n    var _this9 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this9.reportsService.exportTeamPDF(_this9.filters));\n\n        _this9.downloadFile(blob, 'team-report.pdf');\n\n        _this9.showSuccessMessage('PDF exported successfully');\n      } catch (error) {\n        _this9.showErrorMessage('Failed to export PDF');\n      }\n    })();\n  }\n\n  exportAttendanceCSV() {\n    var _this0 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this0.reportsService.exportAttendanceCSV(_this0.filters));\n\n        _this0.downloadFile(blob, 'attendance-report.csv');\n\n        _this0.showSuccessMessage('CSV exported successfully');\n      } catch (error) {\n        _this0.showErrorMessage('Failed to export CSV');\n      }\n    })();\n  }\n\n  exportFormationCSV() {\n    var _this1 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this1.reportsService.exportFormationCSV(_this1.filters));\n\n        _this1.downloadFile(blob, 'formation-report.csv');\n\n        _this1.showSuccessMessage('CSV exported successfully');\n      } catch (error) {\n        _this1.showErrorMessage('Failed to export CSV');\n      }\n    })();\n  }\n\n  exportTeamCSV() {\n    var _this10 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this10.reportsService.exportTeamCSV(_this10.filters));\n\n        _this10.downloadFile(blob, 'team-report.csv');\n\n        _this10.showSuccessMessage('CSV exported successfully');\n      } catch (error) {\n        _this10.showErrorMessage('Failed to export CSV');\n      }\n    })();\n  }\n\n  downloadFile(blob, filename) {\n    const url = window.URL.createObjectURL(blob);\n    const link = window.document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n\n  showSuccessMessage(detail) {\n    this.messageService.add({\n      severity: 'success',\n      summary: 'Success',\n      detail,\n      life: 3000\n    });\n  }\n\n  showErrorMessage(detail) {\n    this.messageService.add({\n      severity: 'error',\n      summary: 'Error',\n      detail,\n      life: 3000\n    });\n  }\n\n  getStatusSeverity(status) {\n    switch (status) {\n      case 'present':\n        return 'success';\n\n      case 'absent':\n        return 'danger';\n\n      case 'pending':\n        return 'warning';\n\n      default:\n        return 'info';\n    }\n  }\n\n  getFormationStatusSeverity(status) {\n    switch (status) {\n      case 'completed':\n        return 'success';\n\n      case 'active':\n        return 'info';\n\n      case 'cancelled':\n        return 'danger';\n\n      case 'pending':\n        return 'warning';\n\n      default:\n        return 'info';\n    }\n  }\n\n}\n\nReportsComponent.ɵfac = function ReportsComponent_Factory(t) {\n  return new (t || ReportsComponent)(i0.ɵɵdirectiveInject(i1.ReportsService), i0.ɵɵdirectiveInject(i2.TeamService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.FormationService), i0.ɵɵdirectiveInject(i5.MessageService));\n};\n\nReportsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ReportsComponent,\n  selectors: [[\"app-reports\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 69,\n  vars: 24,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\"], [1, \"card\", \"mb-4\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\"], [1, \"field\"], [\"for\", \"startDate\"], [\"id\", \"startDate\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Select start date\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"endDate\"], [\"id\", \"endDate\", \"dateFormat\", \"yy-mm-dd\", \"placeholder\", \"Select end date\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"teamFilter\"], [\"id\", \"teamFilter\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select team\", 3, \"ngModel\", \"options\", \"showClear\", \"ngModelChange\"], [\"for\", \"trainerFilter\"], [\"id\", \"trainerFilter\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select trainer\", 3, \"ngModel\", \"options\", \"showClear\", \"ngModelChange\"], [1, \"flex\", \"gap-2\"], [\"label\", \"Apply Filters\", \"icon\", \"pi pi-filter\", 3, \"onClick\"], [\"label\", \"Clear Filters\", \"icon\", \"pi pi-times\", 1, \"p-button-secondary\", 3, \"onClick\"], [3, \"activeIndex\", \"activeIndexChange\"], [\"header\", \"Attendance Report\", \"leftIcon\", \"pi pi-check-circle\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [\"label\", \"Export PDF\", \"icon\", \"pi pi-file-pdf\", 1, \"p-button-danger\", \"p-button-sm\", 3, \"onClick\"], [\"label\", \"Export CSV\", \"icon\", \"pi pi-file-excel\", 1, \"p-button-success\", \"p-button-sm\", 3, \"onClick\"], [\"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} entries\", \"styleClass\", \"p-datatable-gridlines\", 3, \"value\", \"loading\", \"paginator\", \"rows\", \"showCurrentPageReport\", \"rowsPerPageOptions\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"pTemplate\", \"emptymessage\"], [\"header\", \"Formation Report\", \"leftIcon\", \"pi pi-book\"], [\"styleClass\", \"p-datatable-gridlines\", 3, \"value\", \"loading\", \"paginator\", \"rows\"], [\"header\", \"Team Report\", \"leftIcon\", \"pi pi-users\"], [3, \"value\", \"severity\"], [3, \"value\", \"showValue\"], [\"colspan\", \"8\", 1, \"text-center\"], [1, \"text-green-600\", \"font-semibold\"], [1, \"text-red-600\", \"font-semibold\"], [1, \"text-blue-600\", \"font-semibold\"], [\"colspan\", \"7\", 1, \"text-center\"]],\n  template: function ReportsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n      i0.ɵɵtext(4, \"Reports Dashboard\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(5, \"p-toast\");\n      i0.ɵɵelementStart(6, \"div\", 3)(7, \"h6\");\n      i0.ɵɵtext(8, \"Report Filters\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(9, \"div\", 0)(10, \"div\", 4)(11, \"div\", 5)(12, \"label\", 6);\n      i0.ɵɵtext(13, \"Start Date\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"p-calendar\", 7);\n      i0.ɵɵlistener(\"ngModelChange\", function ReportsComponent_Template_p_calendar_ngModelChange_14_listener($event) {\n        return ctx.startDate = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(15, \"div\", 4)(16, \"div\", 5)(17, \"label\", 8);\n      i0.ɵɵtext(18, \"End Date\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"p-calendar\", 9);\n      i0.ɵɵlistener(\"ngModelChange\", function ReportsComponent_Template_p_calendar_ngModelChange_19_listener($event) {\n        return ctx.endDate = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(20, \"div\", 4)(21, \"div\", 5)(22, \"label\", 10);\n      i0.ɵɵtext(23, \"Team\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(24, \"p-dropdown\", 11);\n      i0.ɵɵlistener(\"ngModelChange\", function ReportsComponent_Template_p_dropdown_ngModelChange_24_listener($event) {\n        return ctx.filters.teamId = $event;\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(25, \"div\", 4)(26, \"div\", 5)(27, \"label\", 12);\n      i0.ɵɵtext(28, \"Trainer\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(29, \"p-dropdown\", 13);\n      i0.ɵɵlistener(\"ngModelChange\", function ReportsComponent_Template_p_dropdown_ngModelChange_29_listener($event) {\n        return ctx.filters.trainerId = $event;\n      });\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(30, \"div\", 14)(31, \"p-button\", 15);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_31_listener() {\n        return ctx.applyFilters();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(32, \"p-button\", 16);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_32_listener() {\n        return ctx.clearFilters();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(33, \"p-tabView\", 17);\n      i0.ɵɵlistener(\"activeIndexChange\", function ReportsComponent_Template_p_tabView_activeIndexChange_33_listener($event) {\n        return ctx.selectedTab = $event;\n      });\n      i0.ɵɵelementStart(34, \"p-tabPanel\", 18)(35, \"div\", 2)(36, \"div\", 19)(37, \"h6\");\n      i0.ɵɵtext(38, \"Employee Attendance Report\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(39, \"div\", 14)(40, \"p-button\", 20);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_40_listener() {\n        return ctx.exportAttendancePDF();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(41, \"p-button\", 21);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_41_listener() {\n        return ctx.exportAttendanceCSV();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(42, \"p-table\", 22);\n      i0.ɵɵtemplate(43, ReportsComponent_ng_template_43_Template, 17, 0, \"ng-template\", 23);\n      i0.ɵɵtemplate(44, ReportsComponent_ng_template_44_Template, 18, 16, \"ng-template\", 24);\n      i0.ɵɵtemplate(45, ReportsComponent_ng_template_45_Template, 3, 0, \"ng-template\", 25);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(46, \"p-tabPanel\", 26)(47, \"div\", 2)(48, \"div\", 19)(49, \"h6\");\n      i0.ɵɵtext(50, \"Formation Performance Report\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(51, \"div\", 14)(52, \"p-button\", 20);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_52_listener() {\n        return ctx.exportFormationPDF();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(53, \"p-button\", 21);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_53_listener() {\n        return ctx.exportFormationCSV();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(54, \"p-table\", 27);\n      i0.ɵɵtemplate(55, ReportsComponent_ng_template_55_Template, 19, 0, \"ng-template\", 23);\n      i0.ɵɵtemplate(56, ReportsComponent_ng_template_56_Template, 22, 17, \"ng-template\", 24);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(57, \"p-tabPanel\", 28)(58, \"div\", 2)(59, \"div\", 19)(60, \"h6\");\n      i0.ɵɵtext(61, \"Team Performance Report\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(62, \"div\", 14)(63, \"p-button\", 20);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_63_listener() {\n        return ctx.exportTeamPDF();\n      });\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(64, \"p-button\", 21);\n      i0.ɵɵlistener(\"onClick\", function ReportsComponent_Template_p_button_onClick_64_listener() {\n        return ctx.exportTeamCSV();\n      });\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(65, \"p-table\", 27);\n      i0.ɵɵtemplate(66, ReportsComponent_ng_template_66_Template, 15, 0, \"ng-template\", 23);\n      i0.ɵɵtemplate(67, ReportsComponent_ng_template_67_Template, 17, 11, \"ng-template\", 24);\n      i0.ɵɵtemplate(68, ReportsComponent_ng_template_68_Template, 3, 0, \"ng-template\", 25);\n      i0.ɵɵelementEnd()()()()()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(14);\n      i0.ɵɵproperty(\"ngModel\", ctx.startDate);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.endDate);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.filters.teamId)(\"options\", ctx.teams)(\"showClear\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"ngModel\", ctx.filters.trainerId)(\"options\", ctx.trainers)(\"showClear\", true);\n      i0.ɵɵadvance(4);\n      i0.ɵɵproperty(\"activeIndex\", ctx.selectedTab);\n      i0.ɵɵadvance(9);\n      i0.ɵɵproperty(\"value\", ctx.attendanceReports)(\"loading\", ctx.loadingAttendance)(\"paginator\", true)(\"rows\", 20)(\"showCurrentPageReport\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c1));\n      i0.ɵɵadvance(12);\n      i0.ɵɵproperty(\"value\", ctx.formationReports)(\"loading\", ctx.loadingFormations)(\"paginator\", true)(\"rows\", 15);\n      i0.ɵɵadvance(11);\n      i0.ɵɵproperty(\"value\", ctx.teamReports)(\"loading\", ctx.loadingTeams)(\"paginator\", true)(\"rows\", 10);\n    }\n  },\n  dependencies: [i6.NgControlStatus, i6.NgModel, i7.Table, i5.PrimeTemplate, i8.Button, i9.Dropdown, i10.Calendar, i11.TabView, i11.TabPanel, i12.Toast, i13.ProgressBar, i14.Tag, i15.DatePipe],\n  styles: [\"\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiJyZXBvcnRzLmNvbXBvbmVudC5zY3NzIn0= */\"]\n});", "map": {"version": 3, "mappings": ";AACA,SAASA,cAAT,QAA+B,aAA/B;AACA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;;;;;;;;IC6GgCC,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAQA;IACZA;IAAIA;IAAKA;IACTA;IAAIA;IAAIA;IACRA;IAAIA;IAASA;IACbA;IAAIA;IAAIA;IACRA;IAAIA;IAAOA;IACXA;IAAIA;IAAMA;IACVA;IAAIA;IAAeA;;;;;;;;;;;;IAKvBA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAuBA;IAC3BA;IAAIA;IAAgBA;IACpBA;IAAIA;IAAeA;IACnBA;IAAIA;IAAwBA;IAC5BA;IAAIA;;IAAuCA;IAC3CA;IAAIA;IAAkBA;IACtBA;IACIA;IAIJA;IACAA;IACIA;IAKJA;;;;;;IAlBIA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAGIA;IAAAA,yCAAuB,UAAvB,EAAuBC,0CAAvB;IAQAD;IAAAA;IAFAA,iDAA+B,WAA/B,EAA+B,IAA/B;;;;;;IASZA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IACwCA;IAAwBA;;;;;;IAoChEA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAASA;IACbA;IAAIA;IAAIA;IACRA;IAAIA;IAAOA;IACXA;IAAIA;IAAIA;IACRA;IAAIA;IAAkBA;IACtBA;IAAIA;IAAOA;IACXA;IAAIA;IAAMA;IACVA;IAAIA;IAAeA;IACnBA;IAAIA;IAAMA;;;;;;IAKdA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAeA;IACnBA;IAAIA;;IAA8BA;IAClCA;IAAIA;IAAkBA;IACtBA;IAAIA;IAAeA;IACnBA;IAAIA;IAA4BA;IAChCA,4BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAC+CA;IAAuBA;IAEtEA,4BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAC6CA;IAAsBA;IAEnEA;IACIA;IAKJA;IACAA;IACIA;IAIJA;;;;;;IAvBIA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAE2CA;IAAAA;IAGFA;IAAAA;IAMrCA;IAAAA;IAFAA,iDAA+B,WAA/B,EAA+B,IAA/B;IAOAA;IAAAA,yCAAuB,UAAvB,EAAuBE,mDAAvB;;;;;;IAuCZF,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAIA;IACRA;IAAIA;IAAUA;IACdA;IAAIA;IAAeA;IACnBA;IAAIA;IAAgBA;IACpBA;IAAIA;IAASA;IACbA;IAAIA;IAAQA;IACZA;IAAIA;IAAmBA;;;;;;IAK3BA,2BAAI,CAAJ,EAAI,IAAJ;IACQA;IAAeA;IACnBA;IAAIA;IAA8BA;IAClCA;IAAIA;IAAyBA;IAC7BA;IAAIA;IAA0BA;IAC9BA,2BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAC+CA;IAA8BA;IAE7EA,4BAAI,EAAJ,EAAI,MAAJ,EAAI,EAAJ;IAC8CA;IAA6BA;IAE3EA;IACIA;IAKJA;;;;;IAhBIA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;IAE2CA;IAAAA;IAGDA;IAAAA;IAMtCA;IAAAA;IAFAA,yDAAsC,WAAtC,EAAsC,IAAtC;;;;;;IASZA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IACwCA;IAAkBA;;;;;;;;ADrR1F,OAAM,MAAOG,gBAAP,CAAuB;EAyB3BC,YACUC,cADV,EAEUC,WAFV,EAGUC,WAHV,EAIUC,gBAJV,EAKUC,cALV,EAKwC;IAJ9B;IACA;IACA;IACA;IACA,qCAA8B,CA5BxC;;IACA,yBAAwC,EAAxC;IACA,wBAAsC,EAAtC;IACA,mBAA4B,EAA5B,CAyBwC,CAvBxC;;IACA,aAAgB,EAAhB;IACA,gBAAmB,EAAnB;IACA,kBAA0B,EAA1B,CAoBwC,CAlBxC;;IACA,yBAA6B,KAA7B;IACA,yBAA6B,KAA7B;IACA,oBAAwB,KAAxB,CAewC,CAbxC;;IACA,eAAwB,EAAxB;IACA,mBAAsB,CAAtB,CAWwC,CATxC;;IACA,iBAAyB,IAAzB;IACA,eAAuB,IAAvB;EAQK;;EAECC,QAAQ;IAAA;;IAAA;MACZ,MAAM,KAAI,CAACC,iBAAL,EAAN;MACA,MAAM,KAAI,CAACC,cAAL,EAAN;IAFY;EAGb;;EAEKD,iBAAiB;IAAA;;IAAA;MACrB,IAAI;QACF;QACA,MAAI,CAACE,KAAL,SAAmBd,aAAa,CAAC,MAAI,CAACO,WAAL,CAAiBQ,QAAjB,EAAD,CAAhC,CAFE,CAIF;;QACA,MAAMC,YAAY,SAAS,MAAI,CAACR,WAAL,CAAiBS,cAAjB,CAAgC,WAAhC,CAA3B;QACA,MAAI,CAACC,QAAL,GAAgBF,YAAY,CAACG,GAAb,CAAiBC,OAAO,IAAIC,gCACvCD,OADuC,GAChC;UACVE,IAAI,EAAE,GAAGF,OAAO,CAACG,UAAU,IAAIH,OAAO,CAACI,SAAS;QADtC,CADgC,CAA5B,CAAhB,CANE,CAWF;;QACA,MAAI,CAACC,UAAL,SAAwB,MAAI,CAAChB,gBAAL,CAAsBiB,aAAtB,EAAxB;MAED,CAdD,CAcE,OAAOC,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,+BAAd,EAA+CA,KAA/C;;QACA,MAAI,CAACjB,cAAL,CAAoBmB,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,+BAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD;IAvBoB;EAwBtB;;EAEKpB,cAAc;IAAA;;IAAA;MAClB,MAAMqB,OAAO,CAACC,GAAR,CAAY,CAChB,MAAI,CAACC,oBAAL,EADgB,EAEhB,MAAI,CAACC,mBAAL,EAFgB,EAGhB,MAAI,CAACC,cAAL,EAHgB,CAAZ,CAAN;IADkB;EAMnB;;EAEKF,oBAAoB;IAAA;;IAAA;MACxB,MAAI,CAACG,iBAAL,GAAyB,IAAzB;;MACA,IAAI;QACF,MAAI,CAACC,iBAAL,SAA+BxC,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoBmC,mBAApB,CAAwC,MAAI,CAACC,OAA7C,CAAD,CAA5C;MACD,CAFD,CAEE,OAAOf,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,kCAAd,EAAkDA,KAAlD;;QACA,MAAI,CAACjB,cAAL,CAAoBmB,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,kCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,SAUU;QACR,MAAI,CAACM,iBAAL,GAAyB,KAAzB;MACD;IAduB;EAezB;;EAEKF,mBAAmB;IAAA;;IAAA;MACvB,MAAI,CAACM,iBAAL,GAAyB,IAAzB;;MACA,IAAI;QACF,MAAI,CAACC,gBAAL,SAA8B5C,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoBuC,kBAApB,CAAuC,MAAI,CAACH,OAA5C,CAAD,CAA3C;MACD,CAFD,CAEE,OAAOf,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;;QACA,MAAI,CAACjB,cAAL,CAAoBmB,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,iCAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,SAUU;QACR,MAAI,CAACU,iBAAL,GAAyB,KAAzB;MACD;IAdsB;EAexB;;EAEKL,cAAc;IAAA;;IAAA;MAClB,MAAI,CAACQ,YAAL,GAAoB,IAApB;;MACA,IAAI;QACF,MAAI,CAACC,WAAL,SAAyB/C,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoB0C,aAApB,CAAkC,MAAI,CAACN,OAAvC,CAAD,CAAtC;MACD,CAFD,CAEE,OAAOf,KAAP,EAAc;QACdC,OAAO,CAACD,KAAR,CAAc,4BAAd,EAA4CA,KAA5C;;QACA,MAAI,CAACjB,cAAL,CAAoBmB,GAApB,CAAwB;UACtBC,QAAQ,EAAE,OADY;UAEtBC,OAAO,EAAE,OAFa;UAGtBC,MAAM,EAAE,4BAHc;UAItBC,IAAI,EAAE;QAJgB,CAAxB;MAMD,CAVD,SAUU;QACR,MAAI,CAACa,YAAL,GAAoB,KAApB;MACD;IAdiB;EAenB;;EAEDG,YAAY;IACV;IACA,KAAKP,OAAL,GAAe;MACbQ,SAAS,EAAE,KAAKA,SAAL,GAAiB,KAAKA,SAAL,CAAeC,WAAf,GAA6BC,KAA7B,CAAmC,GAAnC,EAAwC,CAAxC,CAAjB,GAA8DC,SAD5D;MAEbC,OAAO,EAAE,KAAKA,OAAL,GAAe,KAAKA,OAAL,CAAaH,WAAb,GAA2BC,KAA3B,CAAiC,GAAjC,EAAsC,CAAtC,CAAf,GAA0DC,SAFtD;MAGbE,MAAM,EAAE,KAAKb,OAAL,CAAaa,MAHR;MAIbC,SAAS,EAAE,KAAKd,OAAL,CAAac,SAJX;MAKbC,WAAW,EAAE,KAAKf,OAAL,CAAae,WALb;MAMbC,MAAM,EAAE,KAAKhB,OAAL,CAAagB;IANR,CAAf,CAFU,CAWV;;IACA,KAAK7C,cAAL;EACD;;EAED8C,YAAY;IACV,KAAKjB,OAAL,GAAe,EAAf;IACA,KAAKQ,SAAL,GAAiB,IAAjB;IACA,KAAKI,OAAL,GAAe,IAAf;IACA,KAAKzC,cAAL;EACD,CA/I0B,CAiJ3B;;;EACM+C,mBAAmB;IAAA;;IAAA;MACvB,IAAI;QACF,MAAMC,IAAI,SAAS7D,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoBsD,mBAApB,CAAwC,MAAI,CAAClB,OAA7C,CAAD,CAAhC;;QACA,MAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,uBAAxB;;QACA,MAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,MAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPsB;EAQxB;;EAEKC,kBAAkB;IAAA;;IAAA;MACtB,IAAI;QACF,MAAMJ,IAAI,SAAS7D,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoB2D,kBAApB,CAAuC,MAAI,CAACvB,OAA5C,CAAD,CAAhC;;QACA,MAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,sBAAxB;;QACA,MAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,MAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPqB;EAQvB;;EAEKE,aAAa;IAAA;;IAAA;MACjB,IAAI;QACF,MAAML,IAAI,SAAS7D,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoB4D,aAApB,CAAkC,MAAI,CAACxB,OAAvC,CAAD,CAAhC;;QACA,MAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,iBAAxB;;QACA,MAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,MAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPgB;EAQlB;;EAEKG,mBAAmB;IAAA;;IAAA;MACvB,IAAI;QACF,MAAMN,IAAI,SAAS7D,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoB6D,mBAApB,CAAwC,MAAI,CAACzB,OAA7C,CAAD,CAAhC;;QACA,MAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,uBAAxB;;QACA,MAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,MAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPsB;EAQxB;;EAEKI,kBAAkB;IAAA;;IAAA;MACtB,IAAI;QACF,MAAMP,IAAI,SAAS7D,aAAa,CAAC,MAAI,CAACM,cAAL,CAAoB8D,kBAApB,CAAuC,MAAI,CAAC1B,OAA5C,CAAD,CAAhC;;QACA,MAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,sBAAxB;;QACA,MAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,MAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPqB;EAQvB;;EAEKK,aAAa;IAAA;;IAAA;MACjB,IAAI;QACF,MAAMR,IAAI,SAAS7D,aAAa,CAAC,OAAI,CAACM,cAAL,CAAoB+D,aAApB,CAAkC,OAAI,CAAC3B,OAAvC,CAAD,CAAhC;;QACA,OAAI,CAACoB,YAAL,CAAkBD,IAAlB,EAAwB,iBAAxB;;QACA,OAAI,CAACE,kBAAL,CAAwB,2BAAxB;MACD,CAJD,CAIE,OAAOpC,KAAP,EAAc;QACd,OAAI,CAACqC,gBAAL,CAAsB,sBAAtB;MACD;IAPgB;EAQlB;;EAEOF,YAAY,CAACD,IAAD,EAAaS,QAAb,EAA6B;IAC/C,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2Bb,IAA3B,CAAZ;IACA,MAAMc,IAAI,GAAGH,MAAM,CAACI,QAAP,CAAgBC,aAAhB,CAA8B,GAA9B,CAAb;IACAF,IAAI,CAACG,IAAL,GAAYP,GAAZ;IACAI,IAAI,CAACI,QAAL,GAAgBT,QAAhB;IACAK,IAAI,CAACK,KAAL;IACAR,MAAM,CAACC,GAAP,CAAWQ,eAAX,CAA2BV,GAA3B;EACD;;EAEOR,kBAAkB,CAAC/B,MAAD,EAAe;IACvC,KAAKtB,cAAL,CAAoBmB,GAApB,CAAwB;MACtBC,QAAQ,EAAE,SADY;MAEtBC,OAAO,EAAE,SAFa;MAGtBC,MAHsB;MAItBC,IAAI,EAAE;IAJgB,CAAxB;EAMD;;EAEO+B,gBAAgB,CAAChC,MAAD,EAAe;IACrC,KAAKtB,cAAL,CAAoBmB,GAApB,CAAwB;MACtBC,QAAQ,EAAE,OADY;MAEtBC,OAAO,EAAE,OAFa;MAGtBC,MAHsB;MAItBC,IAAI,EAAE;IAJgB,CAAxB;EAMD;;EAEDiD,iBAAiB,CAACxB,MAAD,EAAe;IAC9B,QAAQA,MAAR;MACE,KAAK,SAAL;QAAgB,OAAO,SAAP;;MAChB,KAAK,QAAL;QAAe,OAAO,QAAP;;MACf,KAAK,SAAL;QAAgB,OAAO,SAAP;;MAChB;QAAS,OAAO,MAAP;IAJX;EAMD;;EAEDyB,0BAA0B,CAACzB,MAAD,EAAe;IACvC,QAAQA,MAAR;MACE,KAAK,WAAL;QAAkB,OAAO,SAAP;;MAClB,KAAK,QAAL;QAAe,OAAO,MAAP;;MACf,KAAK,WAAL;QAAkB,OAAO,QAAP;;MAClB,KAAK,SAAL;QAAgB,OAAO,SAAP;;MAChB;QAAS,OAAO,MAAP;IALX;EAOD;;AA1P0B;;;mBAAhBtD,kBAAgBH;AAAA;;;QAAhBG;EAAgBgF;EAAAC,iCAFhB,CAACtF,cAAD,CAEgB;EAFAuF;EAAAC;EAAAC;EAAAC;IAAA;MCf7BxF,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,IAAlB;MAGgBA;MAAiBA;MACrBA;MAGAA,+BAAuB,CAAvB,EAAuB,IAAvB;MACQA;MAAcA;MAClBA,+BAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,EAAlB,EAAkB,OAAlB,EAAkB,CAAlB;MAGmCA;MAAUA;MACjCA;MAEIA;QAAA;MAAA;MAGJA;MAGRA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,OAAtC,EAAsC,CAAtC;MAE6BA;MAAQA;MAC7BA;MAEIA;QAAA;MAAA;MAGJA;MAGRA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,OAAtC,EAAsC,EAAtC;MAEgCA;MAAIA;MAC5BA;MAEIA;QAAA;MAAA;MAMJA;MAGRA,gCAAsC,EAAtC,EAAsC,KAAtC,EAAsC,CAAtC,EAAsC,EAAtC,EAAsC,OAAtC,EAAsC,EAAtC;MAEmCA;MAAOA;MAClCA;MAEIA;QAAA;MAAA;MAMJA;MAIZA,iCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,EAAxB;MAIQA;QAAA,OAAWyF,kBAAX;MAAyB,CAAzB;MACJzF;MACAA;MAIIA;QAAA,OAAWyF,kBAAX;MAAyB,CAAzB;MACJzF;MAKRA;MAAWA;QAAA;MAAA;MAGPA,wCAAqE,EAArE,EAAqE,KAArE,EAAqE,CAArE,EAAqE,EAArE,EAAqE,KAArE,EAAqE,EAArE,EAAqE,EAArE,EAAqE,IAArE;MAGgBA;MAA0BA;MAC9BA,iCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,EAAxB;MAKQA;QAAA,OAAWyF,yBAAX;MAAgC,CAAhC;MACJzF;MACAA;MAIIA;QAAA,OAAWyF,yBAAX;MAAgC,CAAhC;MACJzF;MAIRA;MAUIA;MAaAA;MAwBAA;MAKJA;MAKRA,wCAA4D,EAA5D,EAA4D,KAA5D,EAA4D,CAA5D,EAA4D,EAA5D,EAA4D,KAA5D,EAA4D,EAA5D,EAA4D,EAA5D,EAA4D,IAA5D;MAGgBA;MAA4BA;MAChCA,iCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,EAAxB;MAKQA;QAAA,OAAWyF,wBAAX;MAA+B,CAA/B;MACJzF;MACAA;MAIIA;QAAA,OAAWyF,wBAAX;MAA+B,CAA/B;MACJzF;MAIRA;MAOIA;MAcAA;MA4BJA;MAKRA,wCAAwD,EAAxD,EAAwD,KAAxD,EAAwD,CAAxD,EAAwD,EAAxD,EAAwD,KAAxD,EAAwD,EAAxD,EAAwD,EAAxD,EAAwD,IAAxD;MAGgBA;MAAuBA;MAC3BA,iCAAwB,EAAxB,EAAwB,UAAxB,EAAwB,EAAxB;MAKQA;QAAA,OAAWyF,mBAAX;MAA0B,CAA1B;MACJzF;MACAA;MAIIA;QAAA,OAAWyF,mBAAX;MAA0B,CAA1B;MACJzF;MAIRA;MAOIA;MAYAA;MAsBAA;MAKJA;;;;MA1RQA;MAAAA;MAWAA;MAAAA;MAWAA;MAAAA,6CAA4B,SAA5B,EAA4ByF,SAA5B,EAA4B,WAA5B,EAA4B,IAA5B;MAcAzF;MAAAA,gDAA+B,SAA/B,EAA+ByF,YAA/B,EAA+B,WAA/B,EAA+B,IAA/B;MA0BTzF;MAAAA;MAwBKA;MAAAA,8CAA2B,SAA3B,EAA2ByF,qBAA3B,EAA2B,WAA3B,EAA2B,IAA3B,EAA2B,MAA3B,EAA2B,EAA3B,EAA2B,uBAA3B,EAA2B,IAA3B,EAA2B,oBAA3B,EAA2BzF,2BAA3B;MA6EAA;MAAAA,6CAA0B,SAA1B,EAA0ByF,qBAA1B,EAA0B,WAA1B,EAA0B,IAA1B,EAA0B,MAA1B,EAA0B,EAA1B;MA0EAzF;MAAAA,wCAAqB,SAArB,EAAqByF,gBAArB,EAAqB,WAArB,EAAqB,IAArB,EAAqB,MAArB,EAAqB,EAArB", "names": ["MessageService", "lastValueFrom", "i0", "ctx_r1", "ctx_r4", "ReportsComponent", "constructor", "reportsService", "teamService", "userService", "formationService", "messageService", "ngOnInit", "loadFilterOptions", "loadAllReports", "teams", "getTeams", "trainersData", "getUsersByRole", "trainers", "map", "trainer", "Object", "name", "first_name", "last_name", "formations", "getFormations", "error", "console", "add", "severity", "summary", "detail", "life", "Promise", "all", "loadAttendanceReport", "loadFormationReport", "loadTeamReport", "loadingAttendance", "attendanceReports", "getAttendanceReport", "filters", "loadingFormations", "formationReports", "getFormationReport", "loadingTeams", "teamReports", "getTeamReport", "applyFilters", "startDate", "toISOString", "split", "undefined", "endDate", "teamId", "trainerId", "formationId", "status", "clearFilters", "exportAttendancePDF", "blob", "downloadFile", "showSuccessMessage", "showErrorMessage", "exportFormationPDF", "exportTeamPDF", "exportAttendanceCSV", "exportFormationCSV", "exportTeamCSV", "filename", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "click", "revokeObjectURL", "getStatusSeverity", "getFormationStatusSeverity", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\reports\\reports.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\reports\\reports.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport { ReportsService, ReportFilter, AttendanceReport, FormationReport, TeamReport } from '../../../../services/reports.service';\nimport { TeamService } from '../../../../services/team.service';\nimport { UserService } from '../../../service/user.service';\nimport { FormationService } from '../../../../services/formation.service';\nimport { Team } from '../../../../models/team.model';\nimport { User } from '../../../../models/user.model';\nimport { Formation } from '../../../../models/formation.model';\n\n@Component({\n  selector: 'app-reports',\n  templateUrl: './reports.component.html',\n  styleUrls: ['./reports.component.scss'],\n  providers: [MessageService]\n})\nexport class ReportsComponent implements OnInit {\n\n  // Data\n  attendanceReports: AttendanceReport[] = [];\n  formationReports: FormationReport[] = [];\n  teamReports: TeamReport[] = [];\n\n  // Filter options\n  teams: Team[] = [];\n  trainers: User[] = [];\n  formations: Formation[] = [];\n\n  // Loading states\n  loadingAttendance: boolean = false;\n  loadingFormations: boolean = false;\n  loadingTeams: boolean = false;\n\n  // Filters\n  filters: ReportFilter = {};\n  selectedTab: number = 0;\n\n  // Date range\n  startDate: Date | null = null;\n  endDate: Date | null = null;\n\n  constructor(\n    private reportsService: ReportsService,\n    private teamService: TeamService,\n    private userService: UserService,\n    private formationService: FormationService,\n    private messageService: MessageService\n  ) { }\n\n  async ngOnInit() {\n    await this.loadFilterOptions();\n    await this.loadAllReports();\n  }\n\n  async loadFilterOptions() {\n    try {\n      // Load teams\n      this.teams = await lastValueFrom(this.teamService.getTeams());\n\n      // Load trainers\n      const trainersData = await this.userService.getUsersByRole('formateur');\n      this.trainers = trainersData.map(trainer => ({\n        ...trainer,\n        name: `${trainer.first_name} ${trainer.last_name}`\n      }));\n\n      // Load formations\n      this.formations = await this.formationService.getFormations();\n\n    } catch (error) {\n      console.error('Error loading filter options:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to load filter options',\n        life: 3000\n      });\n    }\n  }\n\n  async loadAllReports() {\n    await Promise.all([\n      this.loadAttendanceReport(),\n      this.loadFormationReport(),\n      this.loadTeamReport()\n    ]);\n  }\n\n  async loadAttendanceReport() {\n    this.loadingAttendance = true;\n    try {\n      this.attendanceReports = await lastValueFrom(this.reportsService.getAttendanceReport(this.filters));\n    } catch (error) {\n      console.error('Error loading attendance report:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to load attendance report',\n        life: 3000\n      });\n    } finally {\n      this.loadingAttendance = false;\n    }\n  }\n\n  async loadFormationReport() {\n    this.loadingFormations = true;\n    try {\n      this.formationReports = await lastValueFrom(this.reportsService.getFormationReport(this.filters));\n    } catch (error) {\n      console.error('Error loading formation report:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to load formation report',\n        life: 3000\n      });\n    } finally {\n      this.loadingFormations = false;\n    }\n  }\n\n  async loadTeamReport() {\n    this.loadingTeams = true;\n    try {\n      this.teamReports = await lastValueFrom(this.reportsService.getTeamReport(this.filters));\n    } catch (error) {\n      console.error('Error loading team report:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to load team report',\n        life: 3000\n      });\n    } finally {\n      this.loadingTeams = false;\n    }\n  }\n\n  applyFilters() {\n    // Update filters object\n    this.filters = {\n      startDate: this.startDate ? this.startDate.toISOString().split('T')[0] : undefined,\n      endDate: this.endDate ? this.endDate.toISOString().split('T')[0] : undefined,\n      teamId: this.filters.teamId,\n      trainerId: this.filters.trainerId,\n      formationId: this.filters.formationId,\n      status: this.filters.status\n    };\n\n    // Reload reports with new filters\n    this.loadAllReports();\n  }\n\n  clearFilters() {\n    this.filters = {};\n    this.startDate = null;\n    this.endDate = null;\n    this.loadAllReports();\n  }\n\n  // Export methods\n  async exportAttendancePDF() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportAttendancePDF(this.filters));\n      this.downloadFile(blob, 'attendance-report.pdf');\n      this.showSuccessMessage('PDF exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export PDF');\n    }\n  }\n\n  async exportFormationPDF() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportFormationPDF(this.filters));\n      this.downloadFile(blob, 'formation-report.pdf');\n      this.showSuccessMessage('PDF exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export PDF');\n    }\n  }\n\n  async exportTeamPDF() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportTeamPDF(this.filters));\n      this.downloadFile(blob, 'team-report.pdf');\n      this.showSuccessMessage('PDF exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export PDF');\n    }\n  }\n\n  async exportAttendanceCSV() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportAttendanceCSV(this.filters));\n      this.downloadFile(blob, 'attendance-report.csv');\n      this.showSuccessMessage('CSV exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export CSV');\n    }\n  }\n\n  async exportFormationCSV() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportFormationCSV(this.filters));\n      this.downloadFile(blob, 'formation-report.csv');\n      this.showSuccessMessage('CSV exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export CSV');\n    }\n  }\n\n  async exportTeamCSV() {\n    try {\n      const blob = await lastValueFrom(this.reportsService.exportTeamCSV(this.filters));\n      this.downloadFile(blob, 'team-report.csv');\n      this.showSuccessMessage('CSV exported successfully');\n    } catch (error) {\n      this.showErrorMessage('Failed to export CSV');\n    }\n  }\n\n  private downloadFile(blob: Blob, filename: string) {\n    const url = window.URL.createObjectURL(blob);\n    const link = window.document.createElement('a');\n    link.href = url;\n    link.download = filename;\n    link.click();\n    window.URL.revokeObjectURL(url);\n  }\n\n  private showSuccessMessage(detail: string) {\n    this.messageService.add({\n      severity: 'success',\n      summary: 'Success',\n      detail,\n      life: 3000\n    });\n  }\n\n  private showErrorMessage(detail: string) {\n    this.messageService.add({\n      severity: 'error',\n      summary: 'Error',\n      detail,\n      life: 3000\n    });\n  }\n\n  getStatusSeverity(status: string): string {\n    switch (status) {\n      case 'present': return 'success';\n      case 'absent': return 'danger';\n      case 'pending': return 'warning';\n      default: return 'info';\n    }\n  }\n\n  getFormationStatusSeverity(status: string): string {\n    switch (status) {\n      case 'completed': return 'success';\n      case 'active': return 'info';\n      case 'cancelled': return 'danger';\n      case 'pending': return 'warning';\n      default: return 'info';\n    }\n  }\n}\n", "<div class=\"grid\">\n    <div class=\"col-12\">\n        <div class=\"card\">\n            <h5>Reports Dashboard</h5>\n            <p-toast></p-toast>\n\n            <!-- Filters Section -->\n            <div class=\"card mb-4\">\n                <h6>Report Filters</h6>\n                <div class=\"grid\">\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <div class=\"field\">\n                            <label for=\"startDate\">Start Date</label>\n                            <p-calendar\n                                id=\"startDate\"\n                                [(ngModel)]=\"startDate\"\n                                dateFormat=\"yy-mm-dd\"\n                                placeholder=\"Select start date\">\n                            </p-calendar>\n                        </div>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <div class=\"field\">\n                            <label for=\"endDate\">End Date</label>\n                            <p-calendar\n                                id=\"endDate\"\n                                [(ngModel)]=\"endDate\"\n                                dateFormat=\"yy-mm-dd\"\n                                placeholder=\"Select end date\">\n                            </p-calendar>\n                        </div>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <div class=\"field\">\n                            <label for=\"teamFilter\">Team</label>\n                            <p-dropdown\n                                id=\"teamFilter\"\n                                [(ngModel)]=\"filters.teamId\"\n                                [options]=\"teams\"\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"Select team\"\n                                [showClear]=\"true\">\n                            </p-dropdown>\n                        </div>\n                    </div>\n                    <div class=\"col-12 md:col-6 lg:col-3\">\n                        <div class=\"field\">\n                            <label for=\"trainerFilter\">Trainer</label>\n                            <p-dropdown\n                                id=\"trainerFilter\"\n                                [(ngModel)]=\"filters.trainerId\"\n                                [options]=\"trainers\"\n                                optionLabel=\"name\"\n                                optionValue=\"id\"\n                                placeholder=\"Select trainer\"\n                                [showClear]=\"true\">\n                            </p-dropdown>\n                        </div>\n                    </div>\n                </div>\n                <div class=\"flex gap-2\">\n                    <p-button\n                        label=\"Apply Filters\"\n                        icon=\"pi pi-filter\"\n                        (onClick)=\"applyFilters()\">\n                    </p-button>\n                    <p-button\n                        label=\"Clear Filters\"\n                        icon=\"pi pi-times\"\n                        class=\"p-button-secondary\"\n                        (onClick)=\"clearFilters()\">\n                    </p-button>\n                </div>\n            </div>\n\n            <!-- Reports Tabs -->\n            <p-tabView [(activeIndex)]=\"selectedTab\">\n\n                <!-- Attendance Report Tab -->\n                <p-tabPanel header=\"Attendance Report\" leftIcon=\"pi pi-check-circle\">\n                    <div class=\"card\">\n                        <div class=\"flex justify-content-between align-items-center mb-4\">\n                            <h6>Employee Attendance Report</h6>\n                            <div class=\"flex gap-2\">\n                                <p-button\n                                    label=\"Export PDF\"\n                                    icon=\"pi pi-file-pdf\"\n                                    class=\"p-button-danger p-button-sm\"\n                                    (onClick)=\"exportAttendancePDF()\">\n                                </p-button>\n                                <p-button\n                                    label=\"Export CSV\"\n                                    icon=\"pi pi-file-excel\"\n                                    class=\"p-button-success p-button-sm\"\n                                    (onClick)=\"exportAttendanceCSV()\">\n                                </p-button>\n                            </div>\n                        </div>\n\n                        <p-table\n                            [value]=\"attendanceReports\"\n                            [loading]=\"loadingAttendance\"\n                            [paginator]=\"true\"\n                            [rows]=\"20\"\n                            [showCurrentPageReport]=\"true\"\n                            currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} entries\"\n                            [rowsPerPageOptions]=\"[10,20,50]\"\n                            styleClass=\"p-datatable-gridlines\">\n\n                            <ng-template pTemplate=\"header\">\n                                <tr>\n                                    <th>Employee</th>\n                                    <th>Email</th>\n                                    <th>Team</th>\n                                    <th>Formation</th>\n                                    <th>Date</th>\n                                    <th>Trainer</th>\n                                    <th>Status</th>\n                                    <th>Attendance Rate</th>\n                                </tr>\n                            </ng-template>\n\n                            <ng-template pTemplate=\"body\" let-report>\n                                <tr>\n                                    <td>{{report.employeeName}}</td>\n                                    <td>{{report.email}}</td>\n                                    <td>{{report.team}}</td>\n                                    <td>{{report.formationName}}</td>\n                                    <td>{{report.formationDate | date:'short'}}</td>\n                                    <td>{{report.trainer}}</td>\n                                    <td>\n                                        <p-tag\n                                            [value]=\"report.status\"\n                                            [severity]=\"getStatusSeverity(report.status)\">\n                                        </p-tag>\n                                    </td>\n                                    <td>\n                                        <p-progressBar\n                                            [value]=\"report.attendanceRate\"\n                                            [showValue]=\"true\"\n                                            [style]=\"{'height': '20px'}\">\n                                        </p-progressBar>\n                                    </td>\n                                </tr>\n                            </ng-template>\n\n                            <ng-template pTemplate=\"emptymessage\">\n                                <tr>\n                                    <td colspan=\"8\" class=\"text-center\">No attendance data found</td>\n                                </tr>\n                            </ng-template>\n                        </p-table>\n                    </div>\n                </p-tabPanel>\n\n                <!-- Formation Report Tab -->\n                <p-tabPanel header=\"Formation Report\" leftIcon=\"pi pi-book\">\n                    <div class=\"card\">\n                        <div class=\"flex justify-content-between align-items-center mb-4\">\n                            <h6>Formation Performance Report</h6>\n                            <div class=\"flex gap-2\">\n                                <p-button\n                                    label=\"Export PDF\"\n                                    icon=\"pi pi-file-pdf\"\n                                    class=\"p-button-danger p-button-sm\"\n                                    (onClick)=\"exportFormationPDF()\">\n                                </p-button>\n                                <p-button\n                                    label=\"Export CSV\"\n                                    icon=\"pi pi-file-excel\"\n                                    class=\"p-button-success p-button-sm\"\n                                    (onClick)=\"exportFormationCSV()\">\n                                </p-button>\n                            </div>\n                        </div>\n\n                        <p-table\n                            [value]=\"formationReports\"\n                            [loading]=\"loadingFormations\"\n                            [paginator]=\"true\"\n                            [rows]=\"15\"\n                            styleClass=\"p-datatable-gridlines\">\n\n                            <ng-template pTemplate=\"header\">\n                                <tr>\n                                    <th>Formation</th>\n                                    <th>Date</th>\n                                    <th>Trainer</th>\n                                    <th>Team</th>\n                                    <th>Total Participants</th>\n                                    <th>Present</th>\n                                    <th>Absent</th>\n                                    <th>Attendance Rate</th>\n                                    <th>Status</th>\n                                </tr>\n                            </ng-template>\n\n                            <ng-template pTemplate=\"body\" let-report>\n                                <tr>\n                                    <td>{{report.name}}</td>\n                                    <td>{{report.date | date:'short'}}</td>\n                                    <td>{{report.trainer}}</td>\n                                    <td>{{report.team}}</td>\n                                    <td>{{report.totalParticipants}}</td>\n                                    <td>\n                                        <span class=\"text-green-600 font-semibold\">{{report.presentCount}}</span>\n                                    </td>\n                                    <td>\n                                        <span class=\"text-red-600 font-semibold\">{{report.absentCount}}</span>\n                                    </td>\n                                    <td>\n                                        <p-progressBar\n                                            [value]=\"report.attendanceRate\"\n                                            [showValue]=\"true\"\n                                            [style]=\"{'height': '20px'}\">\n                                        </p-progressBar>\n                                    </td>\n                                    <td>\n                                        <p-tag\n                                            [value]=\"report.status\"\n                                            [severity]=\"getFormationStatusSeverity(report.status)\">\n                                        </p-tag>\n                                    </td>\n                                </tr>\n                            </ng-template>\n                        </p-table>\n                    </div>\n                </p-tabPanel>\n\n                <!-- Team Report Tab -->\n                <p-tabPanel header=\"Team Report\" leftIcon=\"pi pi-users\">\n                    <div class=\"card\">\n                        <div class=\"flex justify-content-between align-items-center mb-4\">\n                            <h6>Team Performance Report</h6>\n                            <div class=\"flex gap-2\">\n                                <p-button\n                                    label=\"Export PDF\"\n                                    icon=\"pi pi-file-pdf\"\n                                    class=\"p-button-danger p-button-sm\"\n                                    (onClick)=\"exportTeamPDF()\">\n                                </p-button>\n                                <p-button\n                                    label=\"Export CSV\"\n                                    icon=\"pi pi-file-excel\"\n                                    class=\"p-button-success p-button-sm\"\n                                    (onClick)=\"exportTeamCSV()\">\n                                </p-button>\n                            </div>\n                        </div>\n\n                        <p-table\n                            [value]=\"teamReports\"\n                            [loading]=\"loadingTeams\"\n                            [paginator]=\"true\"\n                            [rows]=\"10\"\n                            styleClass=\"p-datatable-gridlines\">\n\n                            <ng-template pTemplate=\"header\">\n                                <tr>\n                                    <th>Team</th>\n                                    <th>Speciality</th>\n                                    <th>Total Employees</th>\n                                    <th>Total Formations</th>\n                                    <th>Completed</th>\n                                    <th>Upcoming</th>\n                                    <th>Avg Attendance Rate</th>\n                                </tr>\n                            </ng-template>\n\n                            <ng-template pTemplate=\"body\" let-report>\n                                <tr>\n                                    <td>{{report.name}}</td>\n                                    <td>{{report.speciality || 'N/A'}}</td>\n                                    <td>{{report.totalEmployees}}</td>\n                                    <td>{{report.totalFormations}}</td>\n                                    <td>\n                                        <span class=\"text-green-600 font-semibold\">{{report.completedFormations}}</span>\n                                    </td>\n                                    <td>\n                                        <span class=\"text-blue-600 font-semibold\">{{report.upcomingFormations}}</span>\n                                    </td>\n                                    <td>\n                                        <p-progressBar\n                                            [value]=\"report.averageAttendanceRate\"\n                                            [showValue]=\"true\"\n                                            [style]=\"{'height': '20px'}\">\n                                        </p-progressBar>\n                                    </td>\n                                </tr>\n                            </ng-template>\n\n                            <ng-template pTemplate=\"emptymessage\">\n                                <tr>\n                                    <td colspan=\"7\" class=\"text-center\">No team data found</td>\n                                </tr>\n                            </ng-template>\n                        </p-table>\n                    </div>\n                </p-tabPanel>\n\n            </p-tabView>\n        </div>\n    </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}