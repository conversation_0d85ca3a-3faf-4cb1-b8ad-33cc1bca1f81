{"ast": null, "code": "var logLevel = \"info\";\n\nfunction dummy() {}\n\nfunction shouldLog(level) {\n  var shouldLog = logLevel === \"info\" && level === \"info\" || [\"info\", \"warning\"].indexOf(logLevel) >= 0 && level === \"warning\" || [\"info\", \"warning\", \"error\"].indexOf(logLevel) >= 0 && level === \"error\";\n  return shouldLog;\n}\n\nfunction logGroup(logFn) {\n  return function (level, msg) {\n    if (shouldLog(level)) {\n      logFn(msg);\n    }\n  };\n}\n\nmodule.exports = function (level, msg) {\n  if (shouldLog(level)) {\n    if (level === \"info\") {\n      console.log(msg);\n    } else if (level === \"warning\") {\n      console.warn(msg);\n    } else if (level === \"error\") {\n      console.error(msg);\n    }\n  }\n};\n/* eslint-disable node/no-unsupported-features/node-builtins */\n\n\nvar group = console.group || dummy;\nvar groupCollapsed = console.groupCollapsed || dummy;\nvar groupEnd = console.groupEnd || dummy;\n/* eslint-enable node/no-unsupported-features/node-builtins */\n\nmodule.exports.group = logGroup(group);\nmodule.exports.groupCollapsed = logGroup(groupCollapsed);\nmodule.exports.groupEnd = logGroup(groupEnd);\n\nmodule.exports.setLogLevel = function (level) {\n  logLevel = level;\n};\n\nmodule.exports.formatError = function (err) {\n  var message = err.message;\n  var stack = err.stack;\n\n  if (!stack) {\n    return message;\n  } else if (stack.indexOf(message) < 0) {\n    return message + \"\\n\" + stack;\n  } else {\n    return stack;\n  }\n};", "map": {"version": 3, "names": ["logLevel", "dummy", "shouldLog", "level", "indexOf", "logGroup", "logFn", "msg", "module", "exports", "console", "log", "warn", "error", "group", "groupCollapsed", "groupEnd", "setLogLevel", "formatError", "err", "message", "stack"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/webpack/hot/log.js"], "sourcesContent": ["var logLevel = \"info\";\n\nfunction dummy() {}\n\nfunction shouldLog(level) {\n\tvar shouldLog =\n\t\t(logLevel === \"info\" && level === \"info\") ||\n\t\t([\"info\", \"warning\"].indexOf(logLevel) >= 0 && level === \"warning\") ||\n\t\t([\"info\", \"warning\", \"error\"].indexOf(logLevel) >= 0 && level === \"error\");\n\treturn shouldLog;\n}\n\nfunction logGroup(logFn) {\n\treturn function (level, msg) {\n\t\tif (shouldLog(level)) {\n\t\t\tlogFn(msg);\n\t\t}\n\t};\n}\n\nmodule.exports = function (level, msg) {\n\tif (shouldLog(level)) {\n\t\tif (level === \"info\") {\n\t\t\tconsole.log(msg);\n\t\t} else if (level === \"warning\") {\n\t\t\tconsole.warn(msg);\n\t\t} else if (level === \"error\") {\n\t\t\tconsole.error(msg);\n\t\t}\n\t}\n};\n\n/* eslint-disable node/no-unsupported-features/node-builtins */\nvar group = console.group || dummy;\nvar groupCollapsed = console.groupCollapsed || dummy;\nvar groupEnd = console.groupEnd || dummy;\n/* eslint-enable node/no-unsupported-features/node-builtins */\n\nmodule.exports.group = logGroup(group);\n\nmodule.exports.groupCollapsed = logGroup(groupCollapsed);\n\nmodule.exports.groupEnd = logGroup(groupEnd);\n\nmodule.exports.setLogLevel = function (level) {\n\tlogLevel = level;\n};\n\nmodule.exports.formatError = function (err) {\n\tvar message = err.message;\n\tvar stack = err.stack;\n\tif (!stack) {\n\t\treturn message;\n\t} else if (stack.indexOf(message) < 0) {\n\t\treturn message + \"\\n\" + stack;\n\t} else {\n\t\treturn stack;\n\t}\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,MAAf;;AAEA,SAASC,KAAT,GAAiB,CAAE;;AAEnB,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;EACzB,IAAID,SAAS,GACXF,QAAQ,KAAK,MAAb,IAAuBG,KAAK,KAAK,MAAlC,IACC,CAAC,MAAD,EAAS,SAAT,EAAoBC,OAApB,CAA4BJ,QAA5B,KAAyC,CAAzC,IAA8CG,KAAK,KAAK,SADzD,IAEC,CAAC,MAAD,EAAS,SAAT,EAAoB,OAApB,EAA6BC,OAA7B,CAAqCJ,QAArC,KAAkD,CAAlD,IAAuDG,KAAK,KAAK,OAHnE;EAIA,OAAOD,SAAP;AACA;;AAED,SAASG,QAAT,CAAkBC,KAAlB,EAAyB;EACxB,OAAO,UAAUH,KAAV,EAAiBI,GAAjB,EAAsB;IAC5B,IAAIL,SAAS,CAACC,KAAD,CAAb,EAAsB;MACrBG,KAAK,CAACC,GAAD,CAAL;IACA;EACD,CAJD;AAKA;;AAEDC,MAAM,CAACC,OAAP,GAAiB,UAAUN,KAAV,EAAiBI,GAAjB,EAAsB;EACtC,IAAIL,SAAS,CAACC,KAAD,CAAb,EAAsB;IACrB,IAAIA,KAAK,KAAK,MAAd,EAAsB;MACrBO,OAAO,CAACC,GAAR,CAAYJ,GAAZ;IACA,CAFD,MAEO,IAAIJ,KAAK,KAAK,SAAd,EAAyB;MAC/BO,OAAO,CAACE,IAAR,CAAaL,GAAb;IACA,CAFM,MAEA,IAAIJ,KAAK,KAAK,OAAd,EAAuB;MAC7BO,OAAO,CAACG,KAAR,CAAcN,GAAd;IACA;EACD;AACD,CAVD;AAYA;;;AACA,IAAIO,KAAK,GAAGJ,OAAO,CAACI,KAAR,IAAiBb,KAA7B;AACA,IAAIc,cAAc,GAAGL,OAAO,CAACK,cAAR,IAA0Bd,KAA/C;AACA,IAAIe,QAAQ,GAAGN,OAAO,CAACM,QAAR,IAAoBf,KAAnC;AACA;;AAEAO,MAAM,CAACC,OAAP,CAAeK,KAAf,GAAuBT,QAAQ,CAACS,KAAD,CAA/B;AAEAN,MAAM,CAACC,OAAP,CAAeM,cAAf,GAAgCV,QAAQ,CAACU,cAAD,CAAxC;AAEAP,MAAM,CAACC,OAAP,CAAeO,QAAf,GAA0BX,QAAQ,CAACW,QAAD,CAAlC;;AAEAR,MAAM,CAACC,OAAP,CAAeQ,WAAf,GAA6B,UAAUd,KAAV,EAAiB;EAC7CH,QAAQ,GAAGG,KAAX;AACA,CAFD;;AAIAK,MAAM,CAACC,OAAP,CAAeS,WAAf,GAA6B,UAAUC,GAAV,EAAe;EAC3C,IAAIC,OAAO,GAAGD,GAAG,CAACC,OAAlB;EACA,IAAIC,KAAK,GAAGF,GAAG,CAACE,KAAhB;;EACA,IAAI,CAACA,KAAL,EAAY;IACX,OAAOD,OAAP;EACA,CAFD,MAEO,IAAIC,KAAK,CAACjB,OAAN,CAAcgB,OAAd,IAAyB,CAA7B,EAAgC;IACtC,OAAOA,OAAO,GAAG,IAAV,GAAiBC,KAAxB;EACA,CAFM,MAEA;IACN,OAAOA,KAAP;EACA;AACD,CAVD"}, "metadata": {}, "sourceType": "script"}