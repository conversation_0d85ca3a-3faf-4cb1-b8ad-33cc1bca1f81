{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount = Infinity) {\n  let config;\n\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount\n    };\n  }\n\n  const {\n    count = Infinity,\n    delay,\n    resetOnSuccess = false\n  } = config;\n  return count <= 0 ? identity : operate((source, subscriber) => {\n    let soFar = 0;\n    let innerSub;\n\n    const subscribeForRetry = () => {\n      let syncUnsub = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, value => {\n        if (resetOnSuccess) {\n          soFar = 0;\n        }\n\n        subscriber.next(value);\n      }, undefined, err => {\n        if (soFar++ < count) {\n          const resub = () => {\n            if (innerSub) {\n              innerSub.unsubscribe();\n              innerSub = null;\n              subscribeForRetry();\n            } else {\n              syncUnsub = true;\n            }\n          };\n\n          if (delay != null) {\n            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n              notifierSubscriber.unsubscribe();\n              resub();\n            }, () => {\n              subscriber.complete();\n            });\n            notifier.subscribe(notifierSubscriber);\n          } else {\n            resub();\n          }\n        } else {\n          subscriber.error(err);\n        }\n      }));\n\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRetry();\n      }\n    };\n\n    subscribeForRetry();\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "identity", "timer", "innerFrom", "retry", "config<PERSON>r<PERSON>ount", "Infinity", "config", "count", "delay", "resetOnSuccess", "source", "subscriber", "soFar", "innerSub", "subscribeForRetry", "syncUnsub", "subscribe", "value", "next", "undefined", "err", "resub", "unsubscribe", "notifier", "notifierSubscriber", "complete", "error"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/retry.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount = Infinity) {\n    let config;\n    if (configOrCount && typeof configOrCount === 'object') {\n        config = configOrCount;\n    }\n    else {\n        config = {\n            count: configOrCount,\n        };\n    }\n    const { count = Infinity, delay, resetOnSuccess: resetOnSuccess = false } = config;\n    return count <= 0\n        ? identity\n        : operate((source, subscriber) => {\n            let soFar = 0;\n            let innerSub;\n            const subscribeForRetry = () => {\n                let syncUnsub = false;\n                innerSub = source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                    if (resetOnSuccess) {\n                        soFar = 0;\n                    }\n                    subscriber.next(value);\n                }, undefined, (err) => {\n                    if (soFar++ < count) {\n                        const resub = () => {\n                            if (innerSub) {\n                                innerSub.unsubscribe();\n                                innerSub = null;\n                                subscribeForRetry();\n                            }\n                            else {\n                                syncUnsub = true;\n                            }\n                        };\n                        if (delay != null) {\n                            const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n                            const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n                                notifierSubscriber.unsubscribe();\n                                resub();\n                            }, () => {\n                                subscriber.complete();\n                            });\n                            notifier.subscribe(notifierSubscriber);\n                        }\n                        else {\n                            resub();\n                        }\n                    }\n                    else {\n                        subscriber.error(err);\n                    }\n                }));\n                if (syncUnsub) {\n                    innerSub.unsubscribe();\n                    innerSub = null;\n                    subscribeForRetry();\n                }\n            };\n            subscribeForRetry();\n        });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,QAAT,QAAyB,kBAAzB;AACA,SAASC,KAAT,QAAsB,qBAAtB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,OAAO,SAASC,KAAT,CAAeC,aAAa,GAAGC,QAA/B,EAAyC;EAC5C,IAAIC,MAAJ;;EACA,IAAIF,aAAa,IAAI,OAAOA,aAAP,KAAyB,QAA9C,EAAwD;IACpDE,MAAM,GAAGF,aAAT;EACH,CAFD,MAGK;IACDE,MAAM,GAAG;MACLC,KAAK,EAAEH;IADF,CAAT;EAGH;;EACD,MAAM;IAAEG,KAAK,GAAGF,QAAV;IAAoBG,KAApB;IAA2CC,cAAc,GAAG;EAA5D,IAAsEH,MAA5E;EACA,OAAOC,KAAK,IAAI,CAAT,GACDP,QADC,GAEDF,OAAO,CAAC,CAACY,MAAD,EAASC,UAAT,KAAwB;IAC9B,IAAIC,KAAK,GAAG,CAAZ;IACA,IAAIC,QAAJ;;IACA,MAAMC,iBAAiB,GAAG,MAAM;MAC5B,IAAIC,SAAS,GAAG,KAAhB;MACAF,QAAQ,GAAGH,MAAM,CAACM,SAAP,CAAiBjB,wBAAwB,CAACY,UAAD,EAAcM,KAAD,IAAW;QACxE,IAAIR,cAAJ,EAAoB;UAChBG,KAAK,GAAG,CAAR;QACH;;QACDD,UAAU,CAACO,IAAX,CAAgBD,KAAhB;MACH,CALmD,EAKjDE,SALiD,EAKrCC,GAAD,IAAS;QACnB,IAAIR,KAAK,KAAKL,KAAd,EAAqB;UACjB,MAAMc,KAAK,GAAG,MAAM;YAChB,IAAIR,QAAJ,EAAc;cACVA,QAAQ,CAACS,WAAT;cACAT,QAAQ,GAAG,IAAX;cACAC,iBAAiB;YACpB,CAJD,MAKK;cACDC,SAAS,GAAG,IAAZ;YACH;UACJ,CATD;;UAUA,IAAIP,KAAK,IAAI,IAAb,EAAmB;YACf,MAAMe,QAAQ,GAAG,OAAOf,KAAP,KAAiB,QAAjB,GAA4BP,KAAK,CAACO,KAAD,CAAjC,GAA2CN,SAAS,CAACM,KAAK,CAACY,GAAD,EAAMR,KAAN,CAAN,CAArE;YACA,MAAMY,kBAAkB,GAAGzB,wBAAwB,CAACY,UAAD,EAAa,MAAM;cAClEa,kBAAkB,CAACF,WAAnB;cACAD,KAAK;YACR,CAHkD,EAGhD,MAAM;cACLV,UAAU,CAACc,QAAX;YACH,CALkD,CAAnD;YAMAF,QAAQ,CAACP,SAAT,CAAmBQ,kBAAnB;UACH,CATD,MAUK;YACDH,KAAK;UACR;QACJ,CAxBD,MAyBK;UACDV,UAAU,CAACe,KAAX,CAAiBN,GAAjB;QACH;MACJ,CAlCmD,CAAzC,CAAX;;MAmCA,IAAIL,SAAJ,EAAe;QACXF,QAAQ,CAACS,WAAT;QACAT,QAAQ,GAAG,IAAX;QACAC,iBAAiB;MACpB;IACJ,CA1CD;;IA2CAA,iBAAiB;EACpB,CA/CQ,CAFb;AAkDH"}, "metadata": {}, "sourceType": "module"}