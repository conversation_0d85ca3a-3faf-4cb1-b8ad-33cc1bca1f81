{"ast": null, "code": "/**\n * @license Angular v14.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalBootstrapApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n\n/* tslint:disable:requireParameterType no-console */\n\n\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener, false); // Needed to follow Dart's subscription semantic, until fix of\n    // https://code.google.com/p/dart/issues/detail?id=17406\n\n    return () => {\n      el.removeEventListener(evt, listener, false);\n    };\n  }\n\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n\n  remove(node) {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n\n  getDefaultDocument() {\n    return document;\n  }\n\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n\n\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n\n    if (target === 'document') {\n      return doc;\n    }\n\n    if (target === 'body') {\n      return doc.body;\n    }\n\n    return null;\n  }\n\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n\n  resetBaseElement() {\n    baseElement = null;\n  }\n\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n\n}\n\nlet baseElement = null;\n\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n} // based on urlUtils.js in AngularJS 1\n\n\nlet urlParsingNode;\n\nfunction relativePath(url) {\n  urlParsingNode = urlParsingNode || document.createElement('a');\n  urlParsingNode.setAttribute('href', url);\n  const pathName = urlParsingNode.pathname;\n  return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\n\n\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\n\nfunction appInitializerFactory(transitionId, document, injector) {\n  return () => {\n    // Wait for all application initializers to be completed before removing the styles set by\n    // the server.\n    injector.get(ApplicationInitStatus).donePromise.then(() => {\n      const dom = ɵgetDOM();\n      const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n\n      for (let i = 0; i < styles.length; i++) {\n        dom.remove(styles[i]);\n      }\n    });\n  };\n}\n\nconst SERVER_TRANSITION_PROVIDERS = [{\n  provide: APP_INITIALIZER,\n  useFactory: appInitializerFactory,\n  deps: [TRANSITION_ID, DOCUMENT, Injector],\n  multi: true\n}];\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n\n      if (testability == null) {\n        throw new Error('Could not find testability for element.');\n      }\n\n      return testability;\n    };\n\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback\n    /** TODO #9100 */\n    ) => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      let didWork = false;\n\n      const decrement = function (didWork_\n      /** TODO #9100 */\n      ) {\n        didWork = didWork || didWork_;\n        count--;\n\n        if (count == 0) {\n          callback(didWork);\n        }\n      };\n\n      testabilities.forEach(function (testability\n      /** TODO #9100 */\n      ) {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n\n    const t = registry.getTestability(elem);\n\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n\n}\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n\n\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n\n}\n\nBrowserXhr.ɵfac = function BrowserXhr_Factory(t) {\n  return new (t || BrowserXhr)();\n};\n\nBrowserXhr.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: BrowserXhr,\n  factory: BrowserXhr.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\n\n\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(p => p.manager = this);\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Registers a global handler for an event in a target view.\n   *\n   * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns A callback function that can be used to remove the handler.\n   * @deprecated No longer being used in Ivy code. To be removed in version 14.\n   */\n\n\n  addGlobalEventListener(target, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n\n    return plugin.addGlobalEventListener(target, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n\n\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n\n\n  _findPluginFor(eventName) {\n    const plugin = this._eventNameToPlugin.get(eventName);\n\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n\n    for (let i = 0; i < plugins.length; i++) {\n      const plugin = plugins[i];\n\n      if (plugin.supports(eventName)) {\n        this._eventNameToPlugin.set(eventName, plugin);\n\n        return plugin;\n      }\n    }\n\n    throw new Error(`No event manager plugin found for event ${eventName}`);\n  }\n\n}\n\nEventManager.ɵfac = function EventManager_Factory(t) {\n  return new (t || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n};\n\nEventManager.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: EventManager,\n  factory: EventManager.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [EVENT_MANAGER_PLUGINS]\n      }]\n    }, {\n      type: i0.NgZone\n    }];\n  }, null);\n})();\n\nclass EventManagerPlugin {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n\n  addGlobalEventListener(element, eventName, handler) {\n    const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n\n    if (!target) {\n      throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n    }\n\n    return this.addEventListener(target, eventName, handler);\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass SharedStylesHost {\n  constructor() {\n    /** @internal */\n    this._stylesSet = new Set();\n  }\n\n  addStyles(styles) {\n    const additions = new Set();\n    styles.forEach(style => {\n      if (!this._stylesSet.has(style)) {\n        this._stylesSet.add(style);\n\n        additions.add(style);\n      }\n    });\n    this.onStylesAdded(additions);\n  }\n\n  onStylesAdded(additions) {}\n\n  getAllStyles() {\n    return Array.from(this._stylesSet);\n  }\n\n}\n\nSharedStylesHost.ɵfac = function SharedStylesHost_Factory(t) {\n  return new (t || SharedStylesHost)();\n};\n\nSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: SharedStylesHost,\n  factory: SharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], null, null);\n})();\n\nclass DomSharedStylesHost extends SharedStylesHost {\n  constructor(_doc) {\n    super();\n    this._doc = _doc; // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n\n    this._hostNodes = new Map();\n\n    this._hostNodes.set(_doc.head, []);\n  }\n\n  _addStylesToHost(styles, host, styleNodes) {\n    styles.forEach(style => {\n      const styleEl = this._doc.createElement('style');\n\n      styleEl.textContent = style;\n      styleNodes.push(host.appendChild(styleEl));\n    });\n  }\n\n  addHost(hostNode) {\n    const styleNodes = [];\n\n    this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n\n    this._hostNodes.set(hostNode, styleNodes);\n  }\n\n  removeHost(hostNode) {\n    const styleNodes = this._hostNodes.get(hostNode);\n\n    if (styleNodes) {\n      styleNodes.forEach(removeStyle);\n    }\n\n    this._hostNodes.delete(hostNode);\n  }\n\n  onStylesAdded(additions) {\n    this._hostNodes.forEach((styleNodes, hostNode) => {\n      this._addStylesToHost(additions, hostNode, styleNodes);\n    });\n  }\n\n  ngOnDestroy() {\n    this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n  }\n\n}\n\nDomSharedStylesHost.ɵfac = function DomSharedStylesHost_Factory(t) {\n  return new (t || DomSharedStylesHost)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSharedStylesHost.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSharedStylesHost,\n  factory: DomSharedStylesHost.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSharedStylesHost, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nfunction removeStyle(styleNode) {\n  ɵgetDOM().remove(styleNode);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nfunction flattenStyles(compId, styles, target) {\n  for (let i = 0; i < styles.length; i++) {\n    let style = styles[i];\n\n    if (Array.isArray(style)) {\n      flattenStyles(compId, style, target);\n    } else {\n      style = style.replace(COMPONENT_REGEX, compId);\n      target.push(style);\n    }\n  }\n\n  return target;\n}\n\nfunction decoratePreventDefault(eventHandler) {\n  // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n  // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n  // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n  // the listener (see below).\n  return event => {\n    // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n    // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n    // can inspect the listener toString contents for the existence of this special token. Because\n    // the token is a string literal, it is ensured to not be modified by compiled code.\n    if (event === '__ngUnwrap__') {\n      return eventHandler;\n    }\n\n    const allowDefaultBehavior = eventHandler(event);\n\n    if (allowDefaultBehavior === false) {\n      // TODO(tbosch): move preventDefault into event plugins...\n      event.preventDefault();\n      event.returnValue = false;\n    }\n\n    return undefined;\n  };\n}\n\nlet hasLoggedNativeEncapsulationWarning = false;\n\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.rendererByCompId = new Map();\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n  }\n\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n\n    switch (type.encapsulation) {\n      case ViewEncapsulation.Emulated:\n        {\n          let renderer = this.rendererByCompId.get(type.id);\n\n          if (!renderer) {\n            renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n            this.rendererByCompId.set(type.id, renderer);\n          }\n\n          renderer.applyToHost(element);\n          return renderer;\n        }\n      // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n      // value that is not known (but previously was the value for ViewEncapsulation.Native)\n\n      case 1:\n      case ViewEncapsulation.ShadowDom:\n        // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n        // enum value that is not known (but previously was the value for\n        // ViewEncapsulation.Native)\n        !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n          hasLoggedNativeEncapsulationWarning = true;\n          console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n        }\n\n        return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n\n      default:\n        {\n          if (!this.rendererByCompId.has(type.id)) {\n            const styles = flattenStyles(type.id, type.styles, []);\n            this.sharedStylesHost.addStyles(styles);\n            this.rendererByCompId.set(type.id, this.defaultRenderer);\n          }\n\n          return this.defaultRenderer;\n        }\n    }\n  }\n\n  begin() {}\n\n  end() {}\n\n}\n\nDomRendererFactory2.ɵfac = function DomRendererFactory2_Factory(t) {\n  return new (t || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(DomSharedStylesHost), i0.ɵɵinject(APP_ID));\n};\n\nDomRendererFactory2.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomRendererFactory2,\n  factory: DomRendererFactory2.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: EventManager\n    }, {\n      type: DomSharedStylesHost\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [APP_ID]\n      }]\n    }];\n  }, null);\n})();\n\nclass DefaultDomRenderer2 {\n  constructor(eventManager) {\n    this.eventManager = eventManager;\n    this.data = Object.create(null);\n    this.destroyNode = null;\n  }\n\n  destroy() {}\n\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return document.createElement(name);\n  }\n\n  createComment(value) {\n    return document.createComment(value);\n  }\n\n  createText(value) {\n    return document.createTextNode(value);\n  }\n\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(parent, oldChild) {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) : selectorOrNode;\n\n    if (!el) {\n      throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n\n    return el;\n  }\n\n  parentNode(node) {\n    return node.parentNode;\n  }\n\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      el.style.removeProperty(style);\n    } else {\n      // IE requires '' instead of null\n      // see https://github.com/angular/angular/issues/7916\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el, name, value) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n\n  listen(target, event, callback) {\n    NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n\n    if (typeof target === 'string') {\n      return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n    }\n\n    return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n  }\n\n}\n\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\n\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\n\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, appId) {\n    super(eventManager);\n    this.component = component;\n    const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n    sharedStylesHost.addStyles(styles);\n    this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n    this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n  }\n\n  applyToHost(element) {\n    super.setAttribute(element, this.hostAttr, '');\n  }\n\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component) {\n    super(eventManager);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = flattenStyles(component.id, component.styles, []);\n\n    for (let i = 0; i < styles.length; i++) {\n      const styleEl = document.createElement('style');\n      styleEl.textContent = styles[i];\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n\n  removeChild(parent, oldChild) {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  } // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n\n\n  supports(eventName) {\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n\n}\n\nDomEventsPlugin.ɵfac = function DomEventsPlugin_Factory(t) {\n  return new (t || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomEventsPlugin,\n  factory: DomEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Defines supported modifiers for key events.\n */\n\n\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst DOM_KEY_LOCATION_NUMPAD = 3; // Map to convert some key or keyIdentifier values to what will be returned by getEventKey\n\nconst _keyMap = {\n  // The following values are here for cross-browser compatibility and to match the W3C standard\n  // cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n}; // There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\n\nconst _chromeNumKeyPadMap = {\n  'A': '1',\n  'B': '2',\n  'C': '3',\n  'D': '4',\n  'E': '5',\n  'F': '6',\n  'G': '7',\n  'H': '8',\n  'I': '9',\n  'J': '*',\n  'K': '+',\n  'M': '-',\n  'N': '.',\n  'O': '/',\n  '\\x60': '0',\n  '\\x90': 'NumLock'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\n\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n\n\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n\n\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n\n    let fullKey = '';\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    } // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n\n\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n\n  static getEventFullKey(event) {\n    let fullKey = '';\n    let key = getEventKey(event);\n    key = key.toLowerCase();\n\n    if (key === ' ') {\n      key = 'space'; // for readability\n    } else if (key === '.') {\n      key = 'dot'; // because '.' is used as a separator in event names\n    }\n\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName != key) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n\n        if (modifierGetter(event)) {\n          fullKey += modifierName + '.';\n        }\n      }\n    });\n    fullKey += key;\n    return fullKey;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n\n\n  static eventCallback(fullKey, handler, zone) {\n    return (event\n    /** TODO #9100 */\n    ) => {\n      if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n\n\n  static _normalizeKey(keyName) {\n    // TODO: switch to a Map if the mapping grows too much\n    switch (keyName) {\n      case 'esc':\n        return 'escape';\n\n      default:\n        return keyName;\n    }\n  }\n\n}\n\nKeyEventsPlugin.ɵfac = function KeyEventsPlugin_Factory(t) {\n  return new (t || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n};\n\nKeyEventsPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: KeyEventsPlugin,\n  factory: KeyEventsPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n\nfunction getEventKey(event) {\n  let key = event.key;\n\n  if (key == null) {\n    key = event.keyIdentifier; // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n    // Safari cf\n    // https://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n\n    if (key == null) {\n      return 'Unidentified';\n    }\n\n    if (key.startsWith('U+')) {\n      key = String.fromCharCode(parseInt(key.substring(2), 16));\n\n      if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n        // There is a bug in Chrome for numeric keypad keys:\n        // https://code.google.com/p/chromium/issues/detail?id=155654\n        // 1, 2, 3 ... are reported as A, B, C ...\n        key = _chromeNumKeyPadMap[key];\n      }\n    }\n  }\n\n  return _keyMap[key] || key;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\n\nfunction bootstrapApplication(rootComponent, options) {\n  var _a;\n\n  return ɵinternalBootstrapApplication({\n    rootComponent,\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...((_a = options === null || options === void 0 ? void 0 : options.providers) !== null && _a !== void 0 ? _a : [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  });\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @developerPreview\n * @publicApi\n */\n\n\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n  return [...TESTABILITY_PROVIDERS];\n}\n\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nfunction errorHandler() {\n  return new ErrorHandler();\n}\n\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\n\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\n\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, {\n  provide: DomRendererFactory2,\n  useClass: DomRendererFactory2,\n  deps: [EventManager, DomSharedStylesHost, APP_ID]\n}, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: SharedStylesHost,\n  useExisting: DomSharedStylesHost\n}, {\n  provide: DomSharedStylesHost,\n  useClass: DomSharedStylesHost,\n  deps: [DOCUMENT]\n}, {\n  provide: EventManager,\n  useClass: EventManager,\n  deps: [EVENT_MANAGER_PLUGINS, NgZone]\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, NG_DEV_MODE ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if (NG_DEV_MODE && providersAlreadyPresent) {\n      throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   */\n\n\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }, {\n        provide: TRANSITION_ID,\n        useExisting: APP_ID\n      }, SERVER_TRANSITION_PROVIDERS]\n    };\n  }\n\n}\n\nBrowserModule.ɵfac = function BrowserModule_Factory(t) {\n  return new (t || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n};\n\nBrowserModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserModule\n});\nBrowserModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  imports: [CommonModule, ApplicationModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\n\n\nfunction createMeta() {\n  return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n\n\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n\n\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n\n\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n\n\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n\n\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n\n    const list\n    /*NodeList*/\n    = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n\n\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n\n\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n\n\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta); // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n\n\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n\n    const element = this._dom.createElement('meta');\n\n    this._setMetaElementAttributes(meta, element);\n\n    const head = this._doc.getElementsByTagName('head')[0];\n\n    head.appendChild(element);\n    return element;\n  }\n\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n\n}\n\nMeta.ɵfac = function Meta_Factory(t) {\n  return new (t || Meta)(i0.ɵɵinject(DOCUMENT));\n};\n\nMeta.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Meta,\n  factory: function Meta_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createMeta();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createMeta,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\n\n\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory to create Title service.\n */\n\nfunction createTitle() {\n  return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n\n\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n\n\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n\n\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n\n}\n\nTitle.ɵfac = function Title_Factory(t) {\n  return new (t || Title)(i0.ɵɵinject(DOCUMENT));\n};\n\nTitle.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Title,\n  factory: function Title_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = createTitle();\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: createTitle,\n      deps: []\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\n\nfunction camelCaseToDashCase(input) {\n  return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\n\nfunction dashCaseToCamelCase(input) {\n  return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\n\n\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst win = typeof window !== 'undefined' && window || {};\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\n\n\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  } // tslint:disable:no-console\n\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n\n\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection'; // Profiler is not available in Android browsers without dev tools opened\n\n    const isProfilerAvailable = win.console.profile != null;\n\n    if (record && isProfilerAvailable) {\n      win.console.profile(profileName);\n    }\n\n    const start = performanceNow();\n    let numTicks = 0;\n\n    while (numTicks < 5 || performanceNow() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n\n    const end = performanceNow();\n\n    if (record && isProfilerAvailable) {\n      win.console.profileEnd(profileName);\n    }\n\n    const msPerTick = (end - start) / numTicks;\n    win.console.log(`ran ${numTicks} change detection cycles`);\n    win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n\n}\n\nfunction performanceNow() {\n  return win.performance && win.performance.now ? win.performance.now() : new Date().getTime();\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\n\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\n\n\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nfunction escapeHtml(text) {\n  const escapedText = {\n    '&': '&a;',\n    '\"': '&q;',\n    '\\'': '&s;',\n    '<': '&l;',\n    '>': '&g;'\n  };\n  return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\n\nfunction unescapeHtml(text) {\n  const unescapedText = {\n    '&a;': '&',\n    '&q;': '\"',\n    '&s;': '\\'',\n    '&l;': '<',\n    '&g;': '>'\n  };\n  return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\n\n\nfunction makeStateKey(key) {\n  return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * `TransferState` will be available as an injectable token. To use it import\n * `ServerTransferStateModule` on the server and `BrowserTransferStateModule` on the client.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\n\n\nclass TransferState {\n  constructor() {\n    this.store = {};\n    this.onSerializeCallbacks = {};\n  }\n  /** @internal */\n\n\n  static init(initState) {\n    const transferState = new TransferState();\n    transferState.store = initState;\n    return transferState;\n  }\n  /**\n   * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n   */\n\n\n  get(key, defaultValue) {\n    return this.store[key] !== undefined ? this.store[key] : defaultValue;\n  }\n  /**\n   * Set the value corresponding to a key.\n   */\n\n\n  set(key, value) {\n    this.store[key] = value;\n  }\n  /**\n   * Remove a key from the store.\n   */\n\n\n  remove(key) {\n    delete this.store[key];\n  }\n  /**\n   * Test whether a key exists in the store.\n   */\n\n\n  hasKey(key) {\n    return this.store.hasOwnProperty(key);\n  }\n  /**\n   * Register a callback to provide the value for a key when `toJson` is called.\n   */\n\n\n  onSerialize(key, callback) {\n    this.onSerializeCallbacks[key] = callback;\n  }\n  /**\n   * Serialize the current state of the store to JSON.\n   */\n\n\n  toJson() {\n    // Call the onSerialize callbacks and put those values into the store.\n    for (const key in this.onSerializeCallbacks) {\n      if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n        try {\n          this.store[key] = this.onSerializeCallbacks[key]();\n        } catch (e) {\n          console.warn('Exception in onSerialize callback: ', e);\n        }\n      }\n    }\n\n    return JSON.stringify(this.store);\n  }\n\n}\n\nTransferState.ɵfac = function TransferState_Factory(t) {\n  return new (t || TransferState)();\n};\n\nTransferState.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: TransferState,\n  factory: TransferState.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TransferState, [{\n    type: Injectable\n  }], null, null);\n})();\n\nfunction initTransferState(doc, appId) {\n  // Locate the script tag with the JSON data transferred from the server.\n  // The id of the script tag is set to the Angular appId + 'state'.\n  const script = doc.getElementById(appId + '-state');\n  let initialState = {};\n\n  if (script && script.textContent) {\n    try {\n      // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n      initialState = JSON.parse(unescapeHtml(script.textContent));\n    } catch (e) {\n      console.warn('Exception while restoring TransferState for app ' + appId, e);\n    }\n  }\n\n  return TransferState.init(initialState);\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n */\n\n\nclass BrowserTransferStateModule {}\n\nBrowserTransferStateModule.ɵfac = function BrowserTransferStateModule_Factory(t) {\n  return new (t || BrowserTransferStateModule)();\n};\n\nBrowserTransferStateModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: BrowserTransferStateModule\n});\nBrowserTransferStateModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: TransferState,\n    useFactory: initTransferState,\n    deps: [DOCUMENT, APP_ID]\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserTransferStateModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: TransferState,\n        useFactory: initTransferState,\n        deps: [DOCUMENT, APP_ID]\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\n\n\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n\n\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n\n\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n\n}\n\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n\n  return false;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Supported HammerJS recognizer event names.\n */\n\n\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\n\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\n\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\n\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n\n\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\n\n}\n\nHammerGestureConfig.ɵfac = function HammerGestureConfig_Factory(t) {\n  return new (t || HammerGestureConfig)();\n};\n\nHammerGestureConfig.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGestureConfig,\n  factory: HammerGestureConfig.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n\n\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n\n      return false;\n    }\n\n    return true;\n  }\n\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase(); // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader()); // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n\n      let cancelRegistration = false;\n\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n\n          deregister = () => {};\n\n          return;\n        }\n\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n\n        deregister = () => {};\n      })); // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n\n      return () => {\n        deregister();\n      };\n    }\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback); // destroy mc to prevent memory leak\n\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n\n}\n\nHammerGesturesPlugin.ɵfac = function HammerGesturesPlugin_Factory(t) {\n  return new (t || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n};\n\nHammerGesturesPlugin.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: HammerGesturesPlugin,\n  factory: HammerGesturesPlugin.ɵfac\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: HammerGestureConfig,\n      decorators: [{\n        type: Inject,\n        args: [HAMMER_GESTURE_CONFIG]\n      }]\n    }, {\n      type: i0.ɵConsole\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [HAMMER_LOADER]\n      }]\n    }];\n  }, null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\n\n\nclass HammerModule {}\n\nHammerModule.ɵfac = function HammerModule_Factory(t) {\n  return new (t || HammerModule)();\n};\n\nHammerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: HammerModule\n});\nHammerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [{\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: HammerGesturesPlugin,\n    multi: true,\n    deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n  }, {\n    provide: HAMMER_GESTURE_CONFIG,\n    useClass: HammerGestureConfig,\n    deps: []\n  }]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n\n\nclass DomSanitizer {}\n\nDomSanitizer.ɵfac = function DomSanitizer_Factory(t) {\n  return new (t || DomSanitizer)();\n};\n\nDomSanitizer.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizer,\n  factory: function DomSanitizer_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new (t || DomSanitizer)();\n    } else {\n      r = i0.ɵɵinject(DomSanitizerImpl);\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\n\nfunction domSanitizerImplFactory(injector) {\n  return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\n\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n\n  sanitize(ctx, value) {\n    if (value == null) return null;\n\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\"\n        /* BypassType.Html */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\"\n        /* BypassType.Style */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return value;\n\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\"\n        /* BypassType.Script */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a script context');\n\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\"\n        /* BypassType.Url */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        return ɵ_sanitizeUrl(String(value));\n\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\"\n        /* BypassType.ResourceUrl */\n        )) {\n          return ɵunwrapSafeValue(value);\n        }\n\n        throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n\n      default:\n        throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n    }\n  }\n\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n\n}\n\nDomSanitizerImpl.ɵfac = function DomSanitizerImpl_Factory(t) {\n  return new (t || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n};\n\nDomSanitizerImpl.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DomSanitizerImpl,\n  factory: function DomSanitizerImpl_Factory(t) {\n    let r = null;\n\n    if (t) {\n      r = new t();\n    } else {\n      r = domSanitizerImplFactory(i0.ɵɵinject(Injector));\n    }\n\n    return r;\n  },\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useFactory: domSanitizerImplFactory,\n      deps: [Injector]\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @publicApi\n */\n\n\nconst VERSION = new Version('14.0.7');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, bootstrapApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "InjectionToken", "ApplicationInitStatus", "APP_INITIALIZER", "Injector", "ɵglobal", "Injectable", "Inject", "ViewEncapsulation", "APP_ID", "RendererStyleFlags2", "ɵinternalBootstrapApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_ID", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "Optional", "SkipSelf", "ɵɵinject", "ApplicationRef", "ɵConsole", "forwardRef", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "Version", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "urlParsingNode", "url", "setAttribute", "pathName", "pathname", "char<PERSON>t", "TRANSITION_ID", "appInitializerFactory", "transitionId", "injector", "get", "donePromise", "then", "dom", "styles", "querySelectorAll", "i", "length", "SERVER_TRANSITION_PROVIDERS", "provide", "useFactory", "deps", "multi", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "Error", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "didWork", "decrement", "didWork_", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "ɵprov", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "p", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "plugin", "_findPluginFor", "addGlobalEventListener", "getZone", "supports", "set", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "SharedStylesHost", "_stylesSet", "Set", "addStyles", "additions", "style", "has", "add", "onStylesAdded", "getAllStyles", "Array", "from", "DomSharedStylesHost", "_hostNodes", "head", "_addStylesToHost", "styleNodes", "styleEl", "textContent", "append<PERSON><PERSON><PERSON>", "addHost", "hostNode", "removeHost", "removeStyle", "delete", "ngOnDestroy", "styleNode", "NAMESPACE_URIS", "COMPONENT_REGEX", "NG_DEV_MODE$1", "ngDevMode", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "flattenStyles", "compId", "isArray", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "event", "allowDefaultBehavior", "preventDefault", "returnValue", "hasLoggedNativeEncapsulationWarning", "DomRendererFactory2", "eventManager", "sharedStylesHost", "appId", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "Emulated", "renderer", "id", "EmulatedEncapsulationDomRenderer2", "applyToHost", "ShadowDom", "console", "warn", "ShadowDom<PERSON><PERSON><PERSON>", "begin", "end", "data", "Object", "create", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttribute", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "component", "contentAttr", "hostAttr", "hostEl", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "DomEventsPlugin", "MODIFIER_KEYS", "DOM_KEY_LOCATION_NUMPAD", "_keyMap", "_chromeNumKeyPadMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "modifierName", "index", "indexOf", "splice", "result", "getEventFullKey", "getEventKey", "modifierGetter", "zone", "runGuarded", "keyName", "keyIdentifier", "startsWith", "String", "fromCharCode", "parseInt", "substring", "location", "hasOwnProperty", "NG_DEV_MODE", "bootstrapApplication", "rootComponent", "options", "_a", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "useValue", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "ɵmod", "ɵinj", "exports", "createMeta", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "keys", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "providedIn", "httpEquiv", "createTitle", "Title", "getTitle", "title", "setTitle", "newTitle", "CAMEL_CASE_REGEXP", "DASH_CASE_REGEXP", "camelCaseToDashCase", "input", "m", "dashCaseToCamelCase", "toUpperCase", "exportNgVar", "COMPILED", "ng", "win", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "timeChangeDetection", "config", "record", "profileName", "isProfilerAvailable", "profile", "start", "performanceNow", "tick", "profileEnd", "log", "toFixed", "performance", "now", "Date", "getTime", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "escapeHtml", "text", "escapedText", "s", "unescapeHtml", "unescapedText", "makeStateKey", "TransferState", "store", "onSerializeCallbacks", "init", "initState", "transferState", "defaultValue", "<PERSON><PERSON><PERSON>", "onSerialize", "to<PERSON><PERSON>", "e", "JSON", "stringify", "initTransferState", "script", "getElementById", "initialState", "parse", "BrowserTransferStateModule", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "isCustomEvent", "cancelRegistration", "deregister", "catch", "eventObj", "on", "off", "HammerModule", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizerImpl", "domSanitizerImplFactory", "sanitize", "ctx", "NONE", "HTML", "toString", "STYLE", "SCRIPT", "URL", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵDomSharedStylesHost", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵNAMESPACE_URIS", "ɵSharedStylesHost", "ɵTRANSITION_ID", "ɵescapeHtml", "ɵflattenStyles", "ɵinitDomAdapter", "ɵshimContentAttribute", "ɵshimHostAttribute"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/platform-browser/fesm2015/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v14.0.7\n * (c) 2010-2022 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, ApplicationInitStatus, APP_INITIALIZER, Injector, ɵglobal, Injectable, Inject, ViewEncapsulation, APP_ID, RendererStyleFlags2, ɵinternalBootstrapApplication, ErrorHandler, ɵsetDocument, PLATFORM_ID, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, Optional, SkipSelf, ɵɵinject, ApplicationRef, ɵConsole, forwardRef, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, Version } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener, false);\n        // Needed to follow Dart's subscription semantic, until fix of\n        // https://code.google.com/p/dart/issues/detail?id=17406\n        return () => {\n            el.removeEventListener(evt, listener, false);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        if (node.parentNode) {\n            node.parentNode.removeChild(node);\n        }\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\n// based on urlUtils.js in AngularJS 1\nlet urlParsingNode;\nfunction relativePath(url) {\n    urlParsingNode = urlParsingNode || document.createElement('a');\n    urlParsingNode.setAttribute('href', url);\n    const pathName = urlParsingNode.pathname;\n    return pathName.charAt(0) === '/' ? pathName : `/${pathName}`;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * An id that identifies a particular application being bootstrapped, that should\n * match across the client/server boundary.\n */\nconst TRANSITION_ID = new InjectionToken('TRANSITION_ID');\nfunction appInitializerFactory(transitionId, document, injector) {\n    return () => {\n        // Wait for all application initializers to be completed before removing the styles set by\n        // the server.\n        injector.get(ApplicationInitStatus).donePromise.then(() => {\n            const dom = ɵgetDOM();\n            const styles = document.querySelectorAll(`style[ng-transition=\"${transitionId}\"]`);\n            for (let i = 0; i < styles.length; i++) {\n                dom.remove(styles[i]);\n            }\n        });\n    };\n}\nconst SERVER_TRANSITION_PROVIDERS = [\n    {\n        provide: APP_INITIALIZER,\n        useFactory: appInitializerFactory,\n        deps: [TRANSITION_ID, DOCUMENT, Injector],\n        multi: true\n    },\n];\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new Error('Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback /** TODO #9100 */) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            let didWork = false;\n            const decrement = function (didWork_ /** TODO #9100 */) {\n                didWork = didWork || didWork_;\n                count--;\n                if (count == 0) {\n                    callback(didWork);\n                }\n            };\n            testabilities.forEach(function (testability /** TODO #9100 */) {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n}\nBrowserXhr.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nBrowserXhr.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserXhr });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * The injection token for the event-manager plug-in service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken('EventManagerPlugins');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach(p => p.manager = this);\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Registers a global handler for an event in a target view.\n     *\n     * @param target A target for global event notifications. One of \"window\", \"document\", or \"body\".\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns A callback function that can be used to remove the handler.\n     * @deprecated No longer being used in Ivy code. To be removed in version 14.\n     */\n    addGlobalEventListener(target, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addGlobalEventListener(target, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        const plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        for (let i = 0; i < plugins.length; i++) {\n            const plugin = plugins[i];\n            if (plugin.supports(eventName)) {\n                this._eventNameToPlugin.set(eventName, plugin);\n                return plugin;\n            }\n        }\n        throw new Error(`No event manager plugin found for event ${eventName}`);\n    }\n}\nEventManager.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\nEventManager.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: EventManager });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [EVENT_MANAGER_PLUGINS]\n                    }] }, { type: i0.NgZone }];\n    } });\nclass EventManagerPlugin {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    addGlobalEventListener(element, eventName, handler) {\n        const target = ɵgetDOM().getGlobalEventTarget(this._doc, element);\n        if (!target) {\n            throw new Error(`Unsupported event target ${target} for event ${eventName}`);\n        }\n        return this.addEventListener(target, eventName, handler);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass SharedStylesHost {\n    constructor() {\n        /** @internal */\n        this._stylesSet = new Set();\n    }\n    addStyles(styles) {\n        const additions = new Set();\n        styles.forEach(style => {\n            if (!this._stylesSet.has(style)) {\n                this._stylesSet.add(style);\n                additions.add(style);\n            }\n        });\n        this.onStylesAdded(additions);\n    }\n    onStylesAdded(additions) { }\n    getAllStyles() {\n        return Array.from(this._stylesSet);\n    }\n}\nSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: SharedStylesHost, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: SharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }] });\nclass DomSharedStylesHost extends SharedStylesHost {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this._hostNodes = new Map();\n        this._hostNodes.set(_doc.head, []);\n    }\n    _addStylesToHost(styles, host, styleNodes) {\n        styles.forEach((style) => {\n            const styleEl = this._doc.createElement('style');\n            styleEl.textContent = style;\n            styleNodes.push(host.appendChild(styleEl));\n        });\n    }\n    addHost(hostNode) {\n        const styleNodes = [];\n        this._addStylesToHost(this._stylesSet, hostNode, styleNodes);\n        this._hostNodes.set(hostNode, styleNodes);\n    }\n    removeHost(hostNode) {\n        const styleNodes = this._hostNodes.get(hostNode);\n        if (styleNodes) {\n            styleNodes.forEach(removeStyle);\n        }\n        this._hostNodes.delete(hostNode);\n    }\n    onStylesAdded(additions) {\n        this._hostNodes.forEach((styleNodes, hostNode) => {\n            this._addStylesToHost(additions, hostNode, styleNodes);\n        });\n    }\n    ngOnDestroy() {\n        this._hostNodes.forEach(styleNodes => styleNodes.forEach(removeStyle));\n    }\n}\nDomSharedStylesHost.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSharedStylesHost, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSharedStylesHost.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSharedStylesHost });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\nfunction removeStyle(styleNode) {\n    ɵgetDOM().remove(styleNode);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/MathML/',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst NG_DEV_MODE$1 = typeof ngDevMode === 'undefined' || !!ngDevMode;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction flattenStyles(compId, styles, target) {\n    for (let i = 0; i < styles.length; i++) {\n        let style = styles[i];\n        if (Array.isArray(style)) {\n            flattenStyles(compId, style, target);\n        }\n        else {\n            style = style.replace(COMPONENT_REGEX, compId);\n            target.push(style);\n        }\n    }\n    return target;\n}\nfunction decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle the\n    // two differently. In the first case, the special '__ngUnwrap__' token is passed to the unwrap\n    // the listener (see below).\n    return (event) => {\n        // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n        // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The debug_node\n        // can inspect the listener toString contents for the existence of this special token. Because\n        // the token is a string literal, it is ensured to not be modified by compiled code.\n        if (event === '__ngUnwrap__') {\n            return eventHandler;\n        }\n        const allowDefaultBehavior = eventHandler(event);\n        if (allowDefaultBehavior === false) {\n            // TODO(tbosch): move preventDefault into event plugins...\n            event.preventDefault();\n            event.returnValue = false;\n        }\n        return undefined;\n    };\n}\nlet hasLoggedNativeEncapsulationWarning = false;\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.rendererByCompId = new Map();\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        switch (type.encapsulation) {\n            case ViewEncapsulation.Emulated: {\n                let renderer = this.rendererByCompId.get(type.id);\n                if (!renderer) {\n                    renderer = new EmulatedEncapsulationDomRenderer2(this.eventManager, this.sharedStylesHost, type, this.appId);\n                    this.rendererByCompId.set(type.id, renderer);\n                }\n                renderer.applyToHost(element);\n                return renderer;\n            }\n            // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an enum\n            // value that is not known (but previously was the value for ViewEncapsulation.Native)\n            case 1:\n            case ViewEncapsulation.ShadowDom:\n                // TODO(FW-2290): remove the `case 1:` fallback logic and the warning in v12.\n                if ((typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    // @ts-ignore TODO: Remove as part of FW-2290. TS complains about us dealing with an\n                    // enum value that is not known (but previously was the value for\n                    // ViewEncapsulation.Native)\n                    !hasLoggedNativeEncapsulationWarning && type.encapsulation === 1) {\n                    hasLoggedNativeEncapsulationWarning = true;\n                    console.warn('ViewEncapsulation.Native is no longer supported. Falling back to ViewEncapsulation.ShadowDom. The fallback will be removed in v12.');\n                }\n                return new ShadowDomRenderer(this.eventManager, this.sharedStylesHost, element, type);\n            default: {\n                if (!this.rendererByCompId.has(type.id)) {\n                    const styles = flattenStyles(type.id, type.styles, []);\n                    this.sharedStylesHost.addStyles(styles);\n                    this.rendererByCompId.set(type.id, this.defaultRenderer);\n                }\n                return this.defaultRenderer;\n            }\n        }\n    }\n    begin() { }\n    end() { }\n}\nDomRendererFactory2.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: DomSharedStylesHost }, { token: APP_ID }], target: i0.ɵɵFactoryTarget.Injectable });\nDomRendererFactory2.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomRendererFactory2 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: EventManager }, { type: DomSharedStylesHost }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [APP_ID]\n                    }] }];\n    } });\nclass DefaultDomRenderer2 {\n    constructor(eventManager) {\n        this.eventManager = eventManager;\n        this.data = Object.create(null);\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return document.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return document.createElement(name);\n    }\n    createComment(value) {\n        return document.createComment(value);\n    }\n    createText(value) {\n        return document.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(parent, oldChild) {\n        if (parent) {\n            parent.removeChild(oldChild);\n        }\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? document.querySelector(selectorOrNode) :\n            selectorOrNode;\n        if (!el) {\n            throw new Error(`The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            el.style.removeProperty(style);\n        }\n        else {\n            // IE requires '' instead of null\n            // see https://github.com/angular/angular/issues/7916\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        NG_DEV_MODE$1 && checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            return this.eventManager.addGlobalEventListener(target, event, decoratePreventDefault(callback));\n        }\n        return this.eventManager.addEventListener(target, event, decoratePreventDefault(callback));\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new Error(`Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass EmulatedEncapsulationDomRenderer2 extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, appId) {\n        super(eventManager);\n        this.component = component;\n        const styles = flattenStyles(appId + '-' + component.id, component.styles, []);\n        sharedStylesHost.addStyles(styles);\n        this.contentAttr = shimContentAttribute(appId + '-' + component.id);\n        this.hostAttr = shimHostAttribute(appId + '-' + component.id);\n    }\n    applyToHost(element) {\n        super.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component) {\n        super(eventManager);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = flattenStyles(component.id, component.styles, []);\n        for (let i = 0; i < styles.length; i++) {\n            const styleEl = document.createElement('style');\n            styleEl.textContent = styles[i];\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(parent, oldChild) {\n        return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n}\nDomEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\nconst DOM_KEY_LOCATION_NUMPAD = 3;\n// Map to convert some key or keyIdentifier values to what will be returned by getEventKey\nconst _keyMap = {\n    // The following values are here for cross-browser compatibility and to match the W3C standard\n    // cf https://www.w3.org/TR/DOM-Level-3-Events-key/\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS'\n};\n// There is a bug in Chrome for numeric keypad keys:\n// https://code.google.com/p/chromium/issues/detail?id=155654\n// 1, 2, 3 ... are reported as A, B, C ...\nconst _chromeNumKeyPadMap = {\n    'A': '1',\n    'B': '2',\n    'C': '3',\n    'D': '4',\n    'E': '5',\n    'F': '6',\n    'G': '7',\n    'H': '8',\n    'I': '9',\n    'J': '*',\n    'K': '+',\n    'M': '-',\n    'N': '.',\n    'O': '/',\n    '\\x60': '0',\n    '\\x90': 'NumLock'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey\n};\n/**\n * @publicApi\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        MODIFIER_KEYS.forEach(modifierName => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    static getEventFullKey(event) {\n        let fullKey = '';\n        let key = getEventKey(event);\n        key = key.toLowerCase();\n        if (key === ' ') {\n            key = 'space'; // for readability\n        }\n        else if (key === '.') {\n            key = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach(modifierName => {\n            if (modifierName != key) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    fullKey += modifierName + '.';\n                }\n            }\n        });\n        fullKey += key;\n        return fullKey;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event /** TODO #9100 */) => {\n            if (KeyEventsPlugin.getEventFullKey(event) === fullKey) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        // TODO: switch to a Map if the mapping grows too much\n        switch (keyName) {\n            case 'esc':\n                return 'escape';\n            default:\n                return keyName;\n        }\n    }\n}\nKeyEventsPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nKeyEventsPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: KeyEventsPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\nfunction getEventKey(event) {\n    let key = event.key;\n    if (key == null) {\n        key = event.keyIdentifier;\n        // keyIdentifier is defined in the old draft of DOM Level 3 Events implemented by Chrome and\n        // Safari cf\n        // https://www.w3.org/TR/2007/WD-DOM-Level-3-Events-20071221/events.html#Events-KeyboardEvents-Interfaces\n        if (key == null) {\n            return 'Unidentified';\n        }\n        if (key.startsWith('U+')) {\n            key = String.fromCharCode(parseInt(key.substring(2), 16));\n            if (event.location === DOM_KEY_LOCATION_NUMPAD && _chromeNumKeyPadMap.hasOwnProperty(key)) {\n                // There is a bug in Chrome for numeric keypad keys:\n                // https://code.google.com/p/chromium/issues/detail?id=155654\n                // 1, 2, 3 ... are reported as A, B, C ...\n                key = _chromeNumKeyPadMap[key];\n            }\n        }\n    }\n    return _keyMap[key] || key;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst NG_DEV_MODE = typeof ngDevMode === 'undefined' || !!ngDevMode;\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n * @developerPreview\n */\nfunction bootstrapApplication(rootComponent, options) {\n    var _a;\n    return ɵinternalBootstrapApplication({\n        rootComponent,\n        appProviders: [\n            ...BROWSER_MODULE_PROVIDERS,\n            ...((_a = options === null || options === void 0 ? void 0 : options.providers) !== null && _a !== void 0 ? _a : []),\n        ],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    });\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @developerPreview\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(NG_DEV_MODE ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    },\n    {\n        provide: Testability,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n    }\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] }, {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID]\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] }, {\n        provide: DomRendererFactory2,\n        useClass: DomRendererFactory2,\n        deps: [EventManager, DomSharedStylesHost, APP_ID]\n    },\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: SharedStylesHost, useExisting: DomSharedStylesHost },\n    { provide: DomSharedStylesHost, useClass: DomSharedStylesHost, deps: [DOCUMENT] },\n    { provide: EventManager, useClass: EventManager, deps: [EVENT_MANAGER_PLUGINS, NgZone] },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    NG_DEV_MODE ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true } : []\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if (NG_DEV_MODE && providersAlreadyPresent) {\n            throw new Error(`Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [\n                { provide: APP_ID, useValue: params.appId },\n                { provide: TRANSITION_ID, useExisting: APP_ID },\n                SERVER_TRANSITION_PROVIDERS,\n            ],\n        };\n    }\n}\nBrowserModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] });\nBrowserModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserModule, providers: [\n        ...BROWSER_MODULE_PROVIDERS,\n        ...TESTABILITY_PROVIDERS\n    ], imports: [CommonModule, ApplicationModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        ...BROWSER_MODULE_PROVIDERS,\n                        ...TESTABILITY_PROVIDERS\n                    ],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: SkipSelf\n                    }, {\n                        type: Inject,\n                        args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create a `Meta` service instance for the current DOM document.\n */\nfunction createMeta() {\n    return new Meta(ɵɵinject(DOCUMENT));\n}\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n}\nMeta.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nMeta.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Meta, providedIn: 'root', useFactory: createMeta, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createMeta, deps: [] }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv'\n};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory to create Title service.\n */\nfunction createTitle() {\n    return new Title(ɵɵinject(DOCUMENT));\n}\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n}\nTitle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nTitle.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Title, providedIn: 'root', useFactory: createTitle, deps: [] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: createTitle, deps: [] }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst CAMEL_CASE_REGEXP = /([A-Z])/g;\nconst DASH_CASE_REGEXP = /-([a-z])/g;\nfunction camelCaseToDashCase(input) {\n    return input.replace(CAMEL_CASE_REGEXP, (...m) => '-' + m[1].toLowerCase());\n}\nfunction dashCaseToCamelCase(input) {\n    return input.replace(DASH_CASE_REGEXP, (...m) => m[1].toUpperCase());\n}\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n        ng[name] = value;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst win = typeof window !== 'undefined' && window || {};\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        const isProfilerAvailable = win.console.profile != null;\n        if (record && isProfilerAvailable) {\n            win.console.profile(profileName);\n        }\n        const start = performanceNow();\n        let numTicks = 0;\n        while (numTicks < 5 || (performanceNow() - start) < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performanceNow();\n        if (record && isProfilerAvailable) {\n            win.console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        win.console.log(`ran ${numTicks} change detection cycles`);\n        win.console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\nfunction performanceNow() {\n    return win.performance && win.performance.now ? win.performance.now() :\n        new Date().getTime();\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction escapeHtml(text) {\n    const escapedText = {\n        '&': '&a;',\n        '\"': '&q;',\n        '\\'': '&s;',\n        '<': '&l;',\n        '>': '&g;',\n    };\n    return text.replace(/[&\"'<>]/g, s => escapedText[s]);\n}\nfunction unescapeHtml(text) {\n    const unescapedText = {\n        '&a;': '&',\n        '&q;': '\"',\n        '&s;': '\\'',\n        '&l;': '<',\n        '&g;': '>',\n    };\n    return text.replace(/&[^;]+;/g, s => unescapedText[s]);\n}\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n */\nfunction makeStateKey(key) {\n    return key;\n}\n/**\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * `TransferState` will be available as an injectable token. To use it import\n * `ServerTransferStateModule` on the server and `BrowserTransferStateModule` on the client.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n */\nclass TransferState {\n    constructor() {\n        this.store = {};\n        this.onSerializeCallbacks = {};\n    }\n    /** @internal */\n    static init(initState) {\n        const transferState = new TransferState();\n        transferState.store = initState;\n        return transferState;\n    }\n    /**\n     * Get the value corresponding to a key. Return `defaultValue` if key is not found.\n     */\n    get(key, defaultValue) {\n        return this.store[key] !== undefined ? this.store[key] : defaultValue;\n    }\n    /**\n     * Set the value corresponding to a key.\n     */\n    set(key, value) {\n        this.store[key] = value;\n    }\n    /**\n     * Remove a key from the store.\n     */\n    remove(key) {\n        delete this.store[key];\n    }\n    /**\n     * Test whether a key exists in the store.\n     */\n    hasKey(key) {\n        return this.store.hasOwnProperty(key);\n    }\n    /**\n     * Register a callback to provide the value for a key when `toJson` is called.\n     */\n    onSerialize(key, callback) {\n        this.onSerializeCallbacks[key] = callback;\n    }\n    /**\n     * Serialize the current state of the store to JSON.\n     */\n    toJson() {\n        // Call the onSerialize callbacks and put those values into the store.\n        for (const key in this.onSerializeCallbacks) {\n            if (this.onSerializeCallbacks.hasOwnProperty(key)) {\n                try {\n                    this.store[key] = this.onSerializeCallbacks[key]();\n                }\n                catch (e) {\n                    console.warn('Exception in onSerialize callback: ', e);\n                }\n            }\n        }\n        return JSON.stringify(this.store);\n    }\n}\nTransferState.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: TransferState, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nTransferState.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: TransferState });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: TransferState, decorators: [{\n            type: Injectable\n        }] });\nfunction initTransferState(doc, appId) {\n    // Locate the script tag with the JSON data transferred from the server.\n    // The id of the script tag is set to the Angular appId + 'state'.\n    const script = doc.getElementById(appId + '-state');\n    let initialState = {};\n    if (script && script.textContent) {\n        try {\n            // Avoid using any here as it triggers lint errors in google3 (any is not allowed).\n            initialState = JSON.parse(unescapeHtml(script.textContent));\n        }\n        catch (e) {\n            console.warn('Exception while restoring TransferState for app ' + appId, e);\n        }\n    }\n    return TransferState.init(initialState);\n}\n/**\n * NgModule to install on the client side while using the `TransferState` to transfer state from\n * server to client.\n *\n * @publicApi\n */\nclass BrowserTransferStateModule {\n}\nBrowserTransferStateModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserTransferStateModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nBrowserTransferStateModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserTransferStateModule });\nBrowserTransferStateModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserTransferStateModule, providers: [{ provide: TransferState, useFactory: initTransferState, deps: [DOCUMENT, APP_ID] }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: BrowserTransferStateModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [{ provide: TransferState, useFactory: initTransferState, deps: [DOCUMENT, APP_ID] }],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null ?\n                elementMatches(debugElement.nativeElement, selector) :\n                false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return n.matches && n.matches(selector) ||\n            n.msMatchesSelector && n.msMatchesSelector(selector) ||\n            n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n    }\n    return false;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see `HammerGestureConfig`\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n}\nHammerGestureConfig.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGestureConfig.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGestureConfig });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise\n                .then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            })\n                .catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n}\nHammerGesturesPlugin.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nHammerGesturesPlugin.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGesturesPlugin });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }, { type: HammerGestureConfig, decorators: [{\n                        type: Inject,\n                        args: [HAMMER_GESTURE_CONFIG]\n                    }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [HAMMER_LOADER]\n                    }] }];\n    } });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's EventManager.\n *\n * @publicApi\n */\nclass HammerModule {\n}\nHammerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nHammerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerModule });\nHammerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerModule, providers: [\n        {\n            provide: EVENT_MANAGER_PLUGINS,\n            useClass: HammerGesturesPlugin,\n            multi: true,\n            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n        },\n        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ]\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n}\nDomSanitizer.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizer.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(function () { return DomSanitizerImpl; }) });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nfunction domSanitizerImplFactory(injector) {\n    return new DomSanitizerImpl(injector.get(DOCUMENT));\n}\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new Error('unsafe value used in a resource URL context (see https://g.co/ng/security#xss)');\n            default:\n                throw new Error(`Unexpected SecurityContext ${ctx} (see https://g.co/ng/security#xss)`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n}\nDomSanitizerImpl.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDomSanitizerImpl.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [{ token: Injector }] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.7\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useFactory: domSanitizerImplFactory, deps: [Injector] }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('14.0.7');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, BrowserTransferStateModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, Meta, Title, TransferState, VERSION, bootstrapApplication, disableDebugTools, enableDebugTools, makeStateKey, platformBrowser, provideProtractorTestingSupport, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, DomSharedStylesHost as ɵDomSharedStylesHost, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, NAMESPACE_URIS as ɵNAMESPACE_URIS, SharedStylesHost as ɵSharedStylesHost, TRANSITION_ID as ɵTRANSITION_ID, escapeHtml as ɵescapeHtml, flattenStyles as ɵflattenStyles, initDomAdapter as ɵinitDomAdapter, shimContentAttribute as ɵshimContentAttribute, shimHostAttribute as ɵshimHostAttribute };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SAASA,WAAT,EAAsBC,kBAAtB,EAA0CC,iBAA1C,EAA6DC,OAA7D,EAAsEC,QAAtE,EAAgFC,oBAAhF,EAAsGC,UAAtG,EAAkHC,YAAlH,QAAsI,iBAAtI;AACA,SAASJ,OAAT,QAAwB,iBAAxB;AACA,OAAO,KAAKK,EAAZ,MAAoB,eAApB;AACA,SAASC,cAAT,EAAyBC,qBAAzB,EAAgDC,eAAhD,EAAiEC,QAAjE,EAA2EC,OAA3E,EAAoFC,UAApF,EAAgGC,MAAhG,EAAwGC,iBAAxG,EAA2HC,MAA3H,EAAmIC,mBAAnI,EAAwJC,6BAAxJ,EAAuLC,YAAvL,EAAqMC,YAArM,EAAmNC,WAAnN,EAAgOC,oBAAhO,EAAsPC,qBAAtP,EAA6QC,YAA7Q,EAA2RC,mBAA3R,EAAgTC,YAAhT,EAA8TC,WAA9T,EAA2UC,MAA3U,EAAmVC,mBAAnV,EAAwWC,eAAxW,EAAyXC,gBAAzX,EAA2YC,iBAA3Y,EAA8ZC,QAA9Z,EAAwaC,QAAxa,EAAkbC,QAAlb,EAA4bC,QAA5b,EAAscC,cAAtc,EAAsdC,QAAtd,EAAgeC,UAAhe,EAA4eC,eAA5e,EAA6fC,gCAA7f,EAA+hBC,gBAA/hB,EAAijBC,aAAjjB,EAAgkBC,cAAhkB,EAAglBC,4BAAhlB,EAA8mBC,6BAA9mB,EAA6oBC,8BAA7oB,EAA6qBC,2BAA7qB,EAA0sBC,mCAA1sB,EAA+uBC,OAA/uB,QAA8vB,eAA9vB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,wBAAN,SAAuCpD,WAAvC,CAAmD;EAC/CqD,WAAW,GAAG;IACV,MAAM,GAAGC,SAAT;IACA,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;AAJ8C;AAOnD;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,iBAAN,SAAgCJ,wBAAhC,CAAyD;EACnC,OAAXK,WAAW,GAAG;IACjBxD,kBAAkB,CAAC,IAAIuD,iBAAJ,EAAD,CAAlB;EACH;;EACDE,WAAW,CAACC,EAAD,EAAKC,GAAL,EAAUC,QAAV,EAAoB;IAC3BF,EAAE,CAACG,gBAAH,CAAoBF,GAApB,EAAyBC,QAAzB,EAAmC,KAAnC,EAD2B,CAE3B;IACA;;IACA,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAH,CAAuBH,GAAvB,EAA4BC,QAA5B,EAAsC,KAAtC;IACH,CAFD;EAGH;;EACDG,aAAa,CAACL,EAAD,EAAKC,GAAL,EAAU;IACnBD,EAAE,CAACK,aAAH,CAAiBJ,GAAjB;EACH;;EACDK,MAAM,CAACC,IAAD,EAAO;IACT,IAAIA,IAAI,CAACC,UAAT,EAAqB;MACjBD,IAAI,CAACC,UAAL,CAAgBC,WAAhB,CAA4BF,IAA5B;IACH;EACJ;;EACDG,aAAa,CAACC,OAAD,EAAUC,GAAV,EAAe;IACxBA,GAAG,GAAGA,GAAG,IAAI,KAAKC,kBAAL,EAAb;IACA,OAAOD,GAAG,CAACF,aAAJ,CAAkBC,OAAlB,CAAP;EACH;;EACDG,kBAAkB,GAAG;IACjB,OAAOC,QAAQ,CAACC,cAAT,CAAwBC,kBAAxB,CAA2C,WAA3C,CAAP;EACH;;EACDJ,kBAAkB,GAAG;IACjB,OAAOE,QAAP;EACH;;EACDG,aAAa,CAACX,IAAD,EAAO;IAChB,OAAOA,IAAI,CAACY,QAAL,KAAkBC,IAAI,CAACC,YAA9B;EACH;;EACDC,YAAY,CAACf,IAAD,EAAO;IACf,OAAOA,IAAI,YAAYgB,gBAAvB;EACH;EACD;;;EACAC,oBAAoB,CAACZ,GAAD,EAAMa,MAAN,EAAc;IAC9B,IAAIA,MAAM,KAAK,QAAf,EAAyB;MACrB,OAAOC,MAAP;IACH;;IACD,IAAID,MAAM,KAAK,UAAf,EAA2B;MACvB,OAAOb,GAAP;IACH;;IACD,IAAIa,MAAM,KAAK,MAAf,EAAuB;MACnB,OAAOb,GAAG,CAACe,IAAX;IACH;;IACD,OAAO,IAAP;EACH;;EACDC,WAAW,CAAChB,GAAD,EAAM;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,EAA/B;IACA,OAAOD,IAAI,IAAI,IAAR,GAAe,IAAf,GAAsBE,YAAY,CAACF,IAAD,CAAzC;EACH;;EACDG,gBAAgB,GAAG;IACfC,WAAW,GAAG,IAAd;EACH;;EACDC,YAAY,GAAG;IACX,OAAOR,MAAM,CAACS,SAAP,CAAiBC,SAAxB;EACH;;EACDC,SAAS,CAACC,IAAD,EAAO;IACZ,OAAO/F,iBAAiB,CAACwE,QAAQ,CAACwB,MAAV,EAAkBD,IAAlB,CAAxB;EACH;;AA7DoD;;AA+DzD,IAAIL,WAAW,GAAG,IAAlB;;AACA,SAASH,kBAAT,GAA8B;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAT,CAAuB,MAAvB,CAA7B;EACA,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAZ,CAAyB,MAAzB,CAAH,GAAsC,IAAxD;AACH,C,CACD;;;AACA,IAAIC,cAAJ;;AACA,SAASX,YAAT,CAAsBY,GAAtB,EAA2B;EACvBD,cAAc,GAAGA,cAAc,IAAI3B,QAAQ,CAACL,aAAT,CAAuB,GAAvB,CAAnC;EACAgC,cAAc,CAACE,YAAf,CAA4B,MAA5B,EAAoCD,GAApC;EACA,MAAME,QAAQ,GAAGH,cAAc,CAACI,QAAhC;EACA,OAAOD,QAAQ,CAACE,MAAT,CAAgB,CAAhB,MAAuB,GAAvB,GAA6BF,QAA7B,GAAyC,IAAGA,QAAS,EAA5D;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMG,aAAa,GAAG,IAAIlG,cAAJ,CAAmB,eAAnB,CAAtB;;AACA,SAASmG,qBAAT,CAA+BC,YAA/B,EAA6CnC,QAA7C,EAAuDoC,QAAvD,EAAiE;EAC7D,OAAO,MAAM;IACT;IACA;IACAA,QAAQ,CAACC,GAAT,CAAarG,qBAAb,EAAoCsG,WAApC,CAAgDC,IAAhD,CAAqD,MAAM;MACvD,MAAMC,GAAG,GAAG/G,OAAO,EAAnB;MACA,MAAMgH,MAAM,GAAGzC,QAAQ,CAAC0C,gBAAT,CAA2B,wBAAuBP,YAAa,IAA/D,CAAf;;MACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;QACpCH,GAAG,CAACjD,MAAJ,CAAWkD,MAAM,CAACE,CAAD,CAAjB;MACH;IACJ,CAND;EAOH,CAVD;AAWH;;AACD,MAAME,2BAA2B,GAAG,CAChC;EACIC,OAAO,EAAE7G,eADb;EAEI8G,UAAU,EAAEb,qBAFhB;EAGIc,IAAI,EAAE,CAACf,aAAD,EAAgBvG,QAAhB,EAA0BQ,QAA1B,CAHV;EAII+G,KAAK,EAAE;AAJX,CADgC,CAApC;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAN,CAA4B;EACxBC,WAAW,CAACC,QAAD,EAAW;IAClBjH,OAAO,CAAC,uBAAD,CAAP,GAAmC,CAACkH,IAAD,EAAOC,eAAe,GAAG,IAAzB,KAAkC;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAT,CAA+BH,IAA/B,EAAqCC,eAArC,CAApB;;MACA,IAAIC,WAAW,IAAI,IAAnB,EAAyB;QACrB,MAAM,IAAIE,KAAJ,CAAU,yCAAV,CAAN;MACH;;MACD,OAAOF,WAAP;IACH,CAND;;IAOApH,OAAO,CAAC,4BAAD,CAAP,GAAwC,MAAMiH,QAAQ,CAACM,mBAAT,EAA9C;;IACAvH,OAAO,CAAC,2BAAD,CAAP,GAAuC,MAAMiH,QAAQ,CAACO,kBAAT,EAA7C;;IACA,MAAMC,aAAa,GAAG,CAACC;IAAS;IAAV,KAAgC;MAClD,MAAMC,aAAa,GAAG3H,OAAO,CAAC,4BAAD,CAAP,EAAtB;MACA,IAAI4H,KAAK,GAAGD,aAAa,CAAClB,MAA1B;MACA,IAAIoB,OAAO,GAAG,KAAd;;MACA,MAAMC,SAAS,GAAG,UAAUC;MAAS;MAAnB,EAAsC;QACpDF,OAAO,GAAGA,OAAO,IAAIE,QAArB;QACAH,KAAK;;QACL,IAAIA,KAAK,IAAI,CAAb,EAAgB;UACZF,QAAQ,CAACG,OAAD,CAAR;QACH;MACJ,CAND;;MAOAF,aAAa,CAACK,OAAd,CAAsB,UAAUZ;MAAY;MAAtB,EAAyC;QAC3DA,WAAW,CAACa,UAAZ,CAAuBH,SAAvB;MACH,CAFD;IAGH,CAdD;;IAeA,IAAI,CAAC9H,OAAO,CAAC,sBAAD,CAAZ,EAAsC;MAClCA,OAAO,CAAC,sBAAD,CAAP,GAAkC,EAAlC;IACH;;IACDA,OAAO,CAAC,sBAAD,CAAP,CAAgCkI,IAAhC,CAAqCT,aAArC;EACH;;EACDJ,qBAAqB,CAACJ,QAAD,EAAWC,IAAX,EAAiBC,eAAjB,EAAkC;IACnD,IAAID,IAAI,IAAI,IAAZ,EAAkB;MACd,OAAO,IAAP;IACH;;IACD,MAAMiB,CAAC,GAAGlB,QAAQ,CAACmB,cAAT,CAAwBlB,IAAxB,CAAV;;IACA,IAAIiB,CAAC,IAAI,IAAT,EAAe;MACX,OAAOA,CAAP;IACH,CAFD,MAGK,IAAI,CAAChB,eAAL,EAAsB;MACvB,OAAO,IAAP;IACH;;IACD,IAAI7H,OAAO,GAAG8E,YAAV,CAAuB8C,IAAvB,CAAJ,EAAkC;MAC9B,OAAO,KAAKG,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACmB,IAA1C,EAAgD,IAAhD,CAAP;IACH;;IACD,OAAO,KAAKhB,qBAAL,CAA2BJ,QAA3B,EAAqCC,IAAI,CAACoB,aAA1C,EAAyD,IAAzD,CAAP;EACH;;AA9CuB;AAiD5B;AACA;AACA;;;AACA,MAAMC,UAAN,CAAiB;EACbC,KAAK,GAAG;IACJ,OAAO,IAAIC,cAAJ,EAAP;EACH;;AAHY;;AAKjBF,UAAU,CAACG,IAAX;EAAA,iBAAuGH,UAAvG;AAAA;;AACAA,UAAU,CAACI,KAAX,kBAD6FhJ,EAC7F;EAAA,OAA2G4I,UAA3G;EAAA,SAA2GA,UAA3G;AAAA;;AACA;EAAA,mDAF6F5I,EAE7F,mBAA2F4I,UAA3F,EAAmH,CAAC;IACxGK,IAAI,EAAE3I;EADkG,CAAD,CAAnH;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4I,qBAAqB,GAAG,IAAIjJ,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMkJ,YAAN,CAAmB;EACf;AACJ;AACA;EACItG,WAAW,CAACuG,OAAD,EAAUC,KAAV,EAAiB;IACxB,KAAKA,KAAL,GAAaA,KAAb;IACA,KAAKC,kBAAL,GAA0B,IAAIC,GAAJ,EAA1B;IACAH,OAAO,CAACf,OAAR,CAAgBmB,CAAC,IAAIA,CAAC,CAACC,OAAF,GAAY,IAAjC;IACA,KAAKC,QAAL,GAAgBN,OAAO,CAACO,KAAR,GAAgBC,OAAhB,EAAhB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACItG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;IACA,OAAOE,MAAM,CAAC1G,gBAAP,CAAwBuG,OAAxB,EAAiCC,SAAjC,EAA4CC,OAA5C,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIG,sBAAsB,CAACtF,MAAD,EAASkF,SAAT,EAAoBC,OAApB,EAA6B;IAC/C,MAAMC,MAAM,GAAG,KAAKC,cAAL,CAAoBH,SAApB,CAAf;;IACA,OAAOE,MAAM,CAACE,sBAAP,CAA8BtF,MAA9B,EAAsCkF,SAAtC,EAAiDC,OAAjD,CAAP;EACH;EACD;AACJ;AACA;;;EACII,OAAO,GAAG;IACN,OAAO,KAAKd,KAAZ;EACH;EACD;;;EACAY,cAAc,CAACH,SAAD,EAAY;IACtB,MAAME,MAAM,GAAG,KAAKV,kBAAL,CAAwB/C,GAAxB,CAA4BuD,SAA5B,CAAf;;IACA,IAAIE,MAAJ,EAAY;MACR,OAAOA,MAAP;IACH;;IACD,MAAMZ,OAAO,GAAG,KAAKM,QAArB;;IACA,KAAK,IAAI7C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,OAAO,CAACtC,MAA5B,EAAoCD,CAAC,EAArC,EAAyC;MACrC,MAAMmD,MAAM,GAAGZ,OAAO,CAACvC,CAAD,CAAtB;;MACA,IAAImD,MAAM,CAACI,QAAP,CAAgBN,SAAhB,CAAJ,EAAgC;QAC5B,KAAKR,kBAAL,CAAwBe,GAAxB,CAA4BP,SAA5B,EAAuCE,MAAvC;;QACA,OAAOA,MAAP;MACH;IACJ;;IACD,MAAM,IAAIrC,KAAJ,CAAW,2CAA0CmC,SAAU,EAA/D,CAAN;EACH;;AA1Dc;;AA4DnBX,YAAY,CAACJ,IAAb;EAAA,iBAAyGI,YAAzG,EArF6FnJ,EAqF7F,UAAuIkJ,qBAAvI,GArF6FlJ,EAqF7F,UAAyKA,EAAE,CAACqB,MAA5K;AAAA;;AACA8H,YAAY,CAACH,KAAb,kBAtF6FhJ,EAsF7F;EAAA,OAA6GmJ,YAA7G;EAAA,SAA6GA,YAA7G;AAAA;;AACA;EAAA,mDAvF6FnJ,EAuF7F,mBAA2FmJ,YAA3F,EAAqH,CAAC;IAC1GF,IAAI,EAAE3I;EADoG,CAAD,CAArH,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAACtB,qBAAD;MAFkB,CAAD;IAA/B,CAAD,EAGW;MAAED,IAAI,EAAEjJ,EAAE,CAACqB;IAAX,CAHX,CAAP;EAIH,CAPL;AAAA;;AAQA,MAAMoJ,kBAAN,CAAyB;EACrB5H,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACDR,sBAAsB,CAACL,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAChD,MAAMnF,MAAM,GAAGjF,OAAO,GAAGgF,oBAAV,CAA+B,KAAK+F,IAApC,EAA0Cb,OAA1C,CAAf;;IACA,IAAI,CAACjF,MAAL,EAAa;MACT,MAAM,IAAI+C,KAAJ,CAAW,4BAA2B/C,MAAO,cAAakF,SAAU,EAApE,CAAN;IACH;;IACD,OAAO,KAAKxG,gBAAL,CAAsBsB,MAAtB,EAA8BkF,SAA9B,EAAyCC,OAAzC,CAAP;EACH;;AAVoB;AAazB;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMY,gBAAN,CAAuB;EACnB9H,WAAW,GAAG;IACV;IACA,KAAK+H,UAAL,GAAkB,IAAIC,GAAJ,EAAlB;EACH;;EACDC,SAAS,CAACnE,MAAD,EAAS;IACd,MAAMoE,SAAS,GAAG,IAAIF,GAAJ,EAAlB;IACAlE,MAAM,CAAC0B,OAAP,CAAe2C,KAAK,IAAI;MACpB,IAAI,CAAC,KAAKJ,UAAL,CAAgBK,GAAhB,CAAoBD,KAApB,CAAL,EAAiC;QAC7B,KAAKJ,UAAL,CAAgBM,GAAhB,CAAoBF,KAApB;;QACAD,SAAS,CAACG,GAAV,CAAcF,KAAd;MACH;IACJ,CALD;IAMA,KAAKG,aAAL,CAAmBJ,SAAnB;EACH;;EACDI,aAAa,CAACJ,SAAD,EAAY,CAAG;;EAC5BK,YAAY,GAAG;IACX,OAAOC,KAAK,CAACC,IAAN,CAAW,KAAKV,UAAhB,CAAP;EACH;;AAlBkB;;AAoBvBD,gBAAgB,CAAC5B,IAAjB;EAAA,iBAA6G4B,gBAA7G;AAAA;;AACAA,gBAAgB,CAAC3B,KAAjB,kBAxI6FhJ,EAwI7F;EAAA,OAAiH2K,gBAAjH;EAAA,SAAiHA,gBAAjH;AAAA;;AACA;EAAA,mDAzI6F3K,EAyI7F,mBAA2F2K,gBAA3F,EAAyH,CAAC;IAC9G1B,IAAI,EAAE3I;EADwG,CAAD,CAAzH;AAAA;;AAGA,MAAMiL,mBAAN,SAAkCZ,gBAAlC,CAAmD;EAC/C9H,WAAW,CAAC6H,IAAD,EAAO;IACd;IACA,KAAKA,IAAL,GAAYA,IAAZ,CAFc,CAGd;;IACA,KAAKc,UAAL,GAAkB,IAAIjC,GAAJ,EAAlB;;IACA,KAAKiC,UAAL,CAAgBnB,GAAhB,CAAoBK,IAAI,CAACe,IAAzB,EAA+B,EAA/B;EACH;;EACDC,gBAAgB,CAAC/E,MAAD,EAAS+B,IAAT,EAAeiD,UAAf,EAA2B;IACvChF,MAAM,CAAC0B,OAAP,CAAgB2C,KAAD,IAAW;MACtB,MAAMY,OAAO,GAAG,KAAKlB,IAAL,CAAU7G,aAAV,CAAwB,OAAxB,CAAhB;;MACA+H,OAAO,CAACC,WAAR,GAAsBb,KAAtB;MACAW,UAAU,CAACpD,IAAX,CAAgBG,IAAI,CAACoD,WAAL,CAAiBF,OAAjB,CAAhB;IACH,CAJD;EAKH;;EACDG,OAAO,CAACC,QAAD,EAAW;IACd,MAAML,UAAU,GAAG,EAAnB;;IACA,KAAKD,gBAAL,CAAsB,KAAKd,UAA3B,EAAuCoB,QAAvC,EAAiDL,UAAjD;;IACA,KAAKH,UAAL,CAAgBnB,GAAhB,CAAoB2B,QAApB,EAA8BL,UAA9B;EACH;;EACDM,UAAU,CAACD,QAAD,EAAW;IACjB,MAAML,UAAU,GAAG,KAAKH,UAAL,CAAgBjF,GAAhB,CAAoByF,QAApB,CAAnB;;IACA,IAAIL,UAAJ,EAAgB;MACZA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB;IACH;;IACD,KAAKV,UAAL,CAAgBW,MAAhB,CAAuBH,QAAvB;EACH;;EACDb,aAAa,CAACJ,SAAD,EAAY;IACrB,KAAKS,UAAL,CAAgBnD,OAAhB,CAAwB,CAACsD,UAAD,EAAaK,QAAb,KAA0B;MAC9C,KAAKN,gBAAL,CAAsBX,SAAtB,EAAiCiB,QAAjC,EAA2CL,UAA3C;IACH,CAFD;EAGH;;EACDS,WAAW,GAAG;IACV,KAAKZ,UAAL,CAAgBnD,OAAhB,CAAwBsD,UAAU,IAAIA,UAAU,CAACtD,OAAX,CAAmB6D,WAAnB,CAAtC;EACH;;AAlC8C;;AAoCnDX,mBAAmB,CAACxC,IAApB;EAAA,iBAAgHwC,mBAAhH,EAhL6FvL,EAgL7F,UAAqJJ,QAArJ;AAAA;;AACA2L,mBAAmB,CAACvC,KAApB,kBAjL6FhJ,EAiL7F;EAAA,OAAoHuL,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDAlL6FvL,EAkL7F,mBAA2FuL,mBAA3F,EAA4H,CAAC;IACjHtC,IAAI,EAAE3I;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CAPL;AAAA;;AAQA,SAASsM,WAAT,CAAqBG,SAArB,EAAgC;EAC5B1M,OAAO,GAAG8D,MAAV,CAAiB4I,SAAjB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,cAAc,GAAG;EACnB,OAAO,4BADY;EAEnB,SAAS,8BAFU;EAGnB,SAAS,8BAHU;EAInB,OAAO,sCAJY;EAKnB,SAAS,+BALU;EAMnB,QAAQ;AANW,CAAvB;AAQA,MAAMC,eAAe,GAAG,SAAxB;AACA,MAAMC,aAAa,GAAG,OAAOC,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAA5D;AACA,MAAMC,kBAAkB,GAAG,QAA3B;AACA,MAAMC,SAAS,GAAI,WAAUD,kBAAmB,EAAhD;AACA,MAAME,YAAY,GAAI,cAAaF,kBAAmB,EAAtD;;AACA,SAASG,oBAAT,CAA8BC,gBAA9B,EAAgD;EAC5C,OAAOF,YAAY,CAACG,OAAb,CAAqBR,eAArB,EAAsCO,gBAAtC,CAAP;AACH;;AACD,SAASE,iBAAT,CAA2BF,gBAA3B,EAA6C;EACzC,OAAOH,SAAS,CAACI,OAAV,CAAkBR,eAAlB,EAAmCO,gBAAnC,CAAP;AACH;;AACD,SAASG,aAAT,CAAuBC,MAAvB,EAA+BvG,MAA/B,EAAuC/B,MAAvC,EAA+C;EAC3C,KAAK,IAAIiC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;IACpC,IAAImE,KAAK,GAAGrE,MAAM,CAACE,CAAD,CAAlB;;IACA,IAAIwE,KAAK,CAAC8B,OAAN,CAAcnC,KAAd,CAAJ,EAA0B;MACtBiC,aAAa,CAACC,MAAD,EAASlC,KAAT,EAAgBpG,MAAhB,CAAb;IACH,CAFD,MAGK;MACDoG,KAAK,GAAGA,KAAK,CAAC+B,OAAN,CAAcR,eAAd,EAA+BW,MAA/B,CAAR;MACAtI,MAAM,CAAC2D,IAAP,CAAYyC,KAAZ;IACH;EACJ;;EACD,OAAOpG,MAAP;AACH;;AACD,SAASwI,sBAAT,CAAgCC,YAAhC,EAA8C;EAC1C;EACA;EACA;EACA;EACA,OAAQC,KAAD,IAAW;IACd;IACA;IACA;IACA;IACA,IAAIA,KAAK,KAAK,cAAd,EAA8B;MAC1B,OAAOD,YAAP;IACH;;IACD,MAAME,oBAAoB,GAAGF,YAAY,CAACC,KAAD,CAAzC;;IACA,IAAIC,oBAAoB,KAAK,KAA7B,EAAoC;MAChC;MACAD,KAAK,CAACE,cAAN;MACAF,KAAK,CAACG,WAAN,GAAoB,KAApB;IACH;;IACD,OAAOnD,SAAP;EACH,CAfD;AAgBH;;AACD,IAAIoD,mCAAmC,GAAG,KAA1C;;AACA,MAAMC,mBAAN,CAA0B;EACtB9K,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiCC,KAAjC,EAAwC;IAC/C,KAAKF,YAAL,GAAoBA,YAApB;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,gBAAL,GAAwB,IAAIxE,GAAJ,EAAxB;IACA,KAAKyE,eAAL,GAAuB,IAAIC,mBAAJ,CAAwBL,YAAxB,CAAvB;EACH;;EACDM,cAAc,CAACrE,OAAD,EAAUZ,IAAV,EAAgB;IAC1B,IAAI,CAACY,OAAD,IAAY,CAACZ,IAAjB,EAAuB;MACnB,OAAO,KAAK+E,eAAZ;IACH;;IACD,QAAQ/E,IAAI,CAACkF,aAAb;MACI,KAAK3N,iBAAiB,CAAC4N,QAAvB;QAAiC;UAC7B,IAAIC,QAAQ,GAAG,KAAKN,gBAAL,CAAsBxH,GAAtB,CAA0B0C,IAAI,CAACqF,EAA/B,CAAf;;UACA,IAAI,CAACD,QAAL,EAAe;YACXA,QAAQ,GAAG,IAAIE,iCAAJ,CAAsC,KAAKX,YAA3C,EAAyD,KAAKC,gBAA9D,EAAgF5E,IAAhF,EAAsF,KAAK6E,KAA3F,CAAX;YACA,KAAKC,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmCD,QAAnC;UACH;;UACDA,QAAQ,CAACG,WAAT,CAAqB3E,OAArB;UACA,OAAOwE,QAAP;QACH;MACD;MACA;;MACA,KAAK,CAAL;MACA,KAAK7N,iBAAiB,CAACiO,SAAvB;QACI;QACA,IAAI,CAAC,OAAOhC,SAAP,KAAqB,WAArB,IAAoCA,SAArC,KACA;QACA;QACA;QACA,CAACiB,mCAJD,IAIwCzE,IAAI,CAACkF,aAAL,KAAuB,CAJnE,EAIsE;UAClET,mCAAmC,GAAG,IAAtC;UACAgB,OAAO,CAACC,IAAR,CAAa,oIAAb;QACH;;QACD,OAAO,IAAIC,iBAAJ,CAAsB,KAAKhB,YAA3B,EAAyC,KAAKC,gBAA9C,EAAgEhE,OAAhE,EAAyEZ,IAAzE,CAAP;;MACJ;QAAS;UACL,IAAI,CAAC,KAAK8E,gBAAL,CAAsB9C,GAAtB,CAA0BhC,IAAI,CAACqF,EAA/B,CAAL,EAAyC;YACrC,MAAM3H,MAAM,GAAGsG,aAAa,CAAChE,IAAI,CAACqF,EAAN,EAAUrF,IAAI,CAACtC,MAAf,EAAuB,EAAvB,CAA5B;YACA,KAAKkH,gBAAL,CAAsB/C,SAAtB,CAAgCnE,MAAhC;YACA,KAAKoH,gBAAL,CAAsB1D,GAAtB,CAA0BpB,IAAI,CAACqF,EAA/B,EAAmC,KAAKN,eAAxC;UACH;;UACD,OAAO,KAAKA,eAAZ;QACH;IA/BL;EAiCH;;EACDa,KAAK,GAAG,CAAG;;EACXC,GAAG,GAAG,CAAG;;AA/Ca;;AAiD1BnB,mBAAmB,CAAC5E,IAApB;EAAA,iBAAgH4E,mBAAhH,EA7S6F3N,EA6S7F,UAAqJmJ,YAArJ,GA7S6FnJ,EA6S7F,UAA8KuL,mBAA9K,GA7S6FvL,EA6S7F,UAA8MS,MAA9M;AAAA;;AACAkN,mBAAmB,CAAC3E,KAApB,kBA9S6FhJ,EA8S7F;EAAA,OAAoH2N,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDA/S6F3N,EA+S7F,mBAA2F2N,mBAA3F,EAA4H,CAAC;IACjH1E,IAAI,EAAE3I;EAD2G,CAAD,CAA5H,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEE;IAAR,CAAD,EAAyB;MAAEF,IAAI,EAAEsC;IAAR,CAAzB,EAAwD;MAAEtC,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QAC/EtB,IAAI,EAAE1I,MADyE;QAE/EiK,IAAI,EAAE,CAAC/J,MAAD;MAFyE,CAAD;IAA/B,CAAxD,CAAP;EAIH,CAPL;AAAA;;AAQA,MAAMwN,mBAAN,CAA0B;EACtBpL,WAAW,CAAC+K,YAAD,EAAe;IACtB,KAAKA,YAAL,GAAoBA,YAApB;IACA,KAAKmB,IAAL,GAAYC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAZ;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,OAAO,GAAG,CAAG;;EACbtL,aAAa,CAAC4B,IAAD,EAAO2J,SAAP,EAAkB;IAC3B,IAAIA,SAAJ,EAAe;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAOlL,QAAQ,CAACmL,eAAT,CAAyB/C,cAAc,CAAC8C,SAAD,CAAd,IAA6BA,SAAtD,EAAiE3J,IAAjE,CAAP;IACH;;IACD,OAAOvB,QAAQ,CAACL,aAAT,CAAuB4B,IAAvB,CAAP;EACH;;EACD6J,aAAa,CAACC,KAAD,EAAQ;IACjB,OAAOrL,QAAQ,CAACoL,aAAT,CAAuBC,KAAvB,CAAP;EACH;;EACDC,UAAU,CAACD,KAAD,EAAQ;IACd,OAAOrL,QAAQ,CAACuL,cAAT,CAAwBF,KAAxB,CAAP;EACH;;EACDzD,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAD,CAAd,GAAyBA,MAAM,CAACI,OAAhC,GAA0CJ,MAA/D;IACAE,YAAY,CAAC9D,WAAb,CAAyB6D,QAAzB;EACH;;EACDI,YAAY,CAACL,MAAD,EAASC,QAAT,EAAmBK,QAAnB,EAA6B;IACrC,IAAIN,MAAJ,EAAY;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAD,CAAd,GAAyBA,MAAM,CAACI,OAAhC,GAA0CJ,MAA/D;MACAE,YAAY,CAACG,YAAb,CAA0BJ,QAA1B,EAAoCK,QAApC;IACH;EACJ;;EACDpM,WAAW,CAAC8L,MAAD,EAASO,QAAT,EAAmB;IAC1B,IAAIP,MAAJ,EAAY;MACRA,MAAM,CAAC9L,WAAP,CAAmBqM,QAAnB;IACH;EACJ;;EACDC,iBAAiB,CAACC,cAAD,EAAiBC,eAAjB,EAAkC;IAC/C,IAAIjN,EAAE,GAAG,OAAOgN,cAAP,KAA0B,QAA1B,GAAqCjM,QAAQ,CAACyB,aAAT,CAAuBwK,cAAvB,CAArC,GACLA,cADJ;;IAEA,IAAI,CAAChN,EAAL,EAAS;MACL,MAAM,IAAIwE,KAAJ,CAAW,iBAAgBwI,cAAe,8BAA1C,CAAN;IACH;;IACD,IAAI,CAACC,eAAL,EAAsB;MAClBjN,EAAE,CAAC0I,WAAH,GAAiB,EAAjB;IACH;;IACD,OAAO1I,EAAP;EACH;;EACDQ,UAAU,CAACD,IAAD,EAAO;IACb,OAAOA,IAAI,CAACC,UAAZ;EACH;;EACD0M,WAAW,CAAC3M,IAAD,EAAO;IACd,OAAOA,IAAI,CAAC2M,WAAZ;EACH;;EACDtK,YAAY,CAAC5C,EAAD,EAAKsC,IAAL,EAAW8J,KAAX,EAAkBH,SAAlB,EAA6B;IACrC,IAAIA,SAAJ,EAAe;MACX3J,IAAI,GAAG2J,SAAS,GAAG,GAAZ,GAAkB3J,IAAzB;MACA,MAAM6K,YAAY,GAAGhE,cAAc,CAAC8C,SAAD,CAAnC;;MACA,IAAIkB,YAAJ,EAAkB;QACdnN,EAAE,CAACoN,cAAH,CAAkBD,YAAlB,EAAgC7K,IAAhC,EAAsC8J,KAAtC;MACH,CAFD,MAGK;QACDpM,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB8J,KAAtB;MACH;IACJ,CATD,MAUK;MACDpM,EAAE,CAAC4C,YAAH,CAAgBN,IAAhB,EAAsB8J,KAAtB;IACH;EACJ;;EACDiB,eAAe,CAACrN,EAAD,EAAKsC,IAAL,EAAW2J,SAAX,EAAsB;IACjC,IAAIA,SAAJ,EAAe;MACX,MAAMkB,YAAY,GAAGhE,cAAc,CAAC8C,SAAD,CAAnC;;MACA,IAAIkB,YAAJ,EAAkB;QACdnN,EAAE,CAACsN,iBAAH,CAAqBH,YAArB,EAAmC7K,IAAnC;MACH,CAFD,MAGK;QACDtC,EAAE,CAACqN,eAAH,CAAoB,GAAEpB,SAAU,IAAG3J,IAAK,EAAxC;MACH;IACJ,CARD,MASK;MACDtC,EAAE,CAACqN,eAAH,CAAmB/K,IAAnB;IACH;EACJ;;EACDiL,QAAQ,CAACvN,EAAD,EAAKsC,IAAL,EAAW;IACftC,EAAE,CAACwN,SAAH,CAAazF,GAAb,CAAiBzF,IAAjB;EACH;;EACDmL,WAAW,CAACzN,EAAD,EAAKsC,IAAL,EAAW;IAClBtC,EAAE,CAACwN,SAAH,CAAalN,MAAb,CAAoBgC,IAApB;EACH;;EACDoL,QAAQ,CAAC1N,EAAD,EAAK6H,KAAL,EAAYuE,KAAZ,EAAmBuB,KAAnB,EAA0B;IAC9B,IAAIA,KAAK,IAAIpQ,mBAAmB,CAACqQ,QAApB,GAA+BrQ,mBAAmB,CAACsQ,SAAvD,CAAT,EAA4E;MACxE7N,EAAE,CAAC6H,KAAH,CAASiG,WAAT,CAAqBjG,KAArB,EAA4BuE,KAA5B,EAAmCuB,KAAK,GAAGpQ,mBAAmB,CAACsQ,SAA5B,GAAwC,WAAxC,GAAsD,EAAzF;IACH,CAFD,MAGK;MACD7N,EAAE,CAAC6H,KAAH,CAASA,KAAT,IAAkBuE,KAAlB;IACH;EACJ;;EACDrD,WAAW,CAAC/I,EAAD,EAAK6H,KAAL,EAAY8F,KAAZ,EAAmB;IAC1B,IAAIA,KAAK,GAAGpQ,mBAAmB,CAACqQ,QAAhC,EAA0C;MACtC5N,EAAE,CAAC6H,KAAH,CAASkG,cAAT,CAAwBlG,KAAxB;IACH,CAFD,MAGK;MACD;MACA;MACA7H,EAAE,CAAC6H,KAAH,CAASA,KAAT,IAAkB,EAAlB;IACH;EACJ;;EACDiG,WAAW,CAAC9N,EAAD,EAAKsC,IAAL,EAAW8J,KAAX,EAAkB;IACzB/C,aAAa,IAAI2E,oBAAoB,CAAC1L,IAAD,EAAO,UAAP,CAArC;IACAtC,EAAE,CAACsC,IAAD,CAAF,GAAW8J,KAAX;EACH;;EACD6B,QAAQ,CAAC1N,IAAD,EAAO6L,KAAP,EAAc;IAClB7L,IAAI,CAAC2N,SAAL,GAAiB9B,KAAjB;EACH;;EACD+B,MAAM,CAAC1M,MAAD,EAAS0I,KAAT,EAAgBvF,QAAhB,EAA0B;IAC5ByE,aAAa,IAAI2E,oBAAoB,CAAC7D,KAAD,EAAQ,UAAR,CAArC;;IACA,IAAI,OAAO1I,MAAP,KAAkB,QAAtB,EAAgC;MAC5B,OAAO,KAAKgJ,YAAL,CAAkB1D,sBAAlB,CAAyCtF,MAAzC,EAAiD0I,KAAjD,EAAwDF,sBAAsB,CAACrF,QAAD,CAA9E,CAAP;IACH;;IACD,OAAO,KAAK6F,YAAL,CAAkBtK,gBAAlB,CAAmCsB,MAAnC,EAA2C0I,KAA3C,EAAkDF,sBAAsB,CAACrF,QAAD,CAAxE,CAAP;EACH;;AA9HqB;;AAgI1B,MAAMwJ,WAAW,GAAG,CAAC,MAAM,IAAIC,UAAJ,CAAe,CAAf,CAAP,GAApB;;AACA,SAASL,oBAAT,CAA8B1L,IAA9B,EAAoCgM,QAApC,EAA8C;EAC1C,IAAIhM,IAAI,CAAC+L,UAAL,CAAgB,CAAhB,MAAuBD,WAA3B,EAAwC;IACpC,MAAM,IAAI5J,KAAJ,CAAW,wBAAuB8J,QAAS,IAAGhM,IAAK;AACjE;AACA,qEAAqEA,IAAK,gIAF5D,CAAN;EAGH;AACJ;;AACD,SAASoK,cAAT,CAAwBnM,IAAxB,EAA8B;EAC1B,OAAOA,IAAI,CAACI,OAAL,KAAiB,UAAjB,IAA+BJ,IAAI,CAACoM,OAAL,KAAiBxF,SAAvD;AACH;;AACD,MAAMiE,iCAAN,SAAgDN,mBAAhD,CAAoE;EAChEpL,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiC6D,SAAjC,EAA4C5D,KAA5C,EAAmD;IAC1D,MAAMF,YAAN;IACA,KAAK8D,SAAL,GAAiBA,SAAjB;IACA,MAAM/K,MAAM,GAAGsG,aAAa,CAACa,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,EAA6BoD,SAAS,CAAC/K,MAAvC,EAA+C,EAA/C,CAA5B;IACAkH,gBAAgB,CAAC/C,SAAjB,CAA2BnE,MAA3B;IACA,KAAKgL,WAAL,GAAmB9E,oBAAoB,CAACiB,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,CAAvC;IACA,KAAKsD,QAAL,GAAgB5E,iBAAiB,CAACc,KAAK,GAAG,GAAR,GAAc4D,SAAS,CAACpD,EAAzB,CAAjC;EACH;;EACDE,WAAW,CAAC3E,OAAD,EAAU;IACjB,MAAM9D,YAAN,CAAmB8D,OAAnB,EAA4B,KAAK+H,QAAjC,EAA2C,EAA3C;EACH;;EACD/N,aAAa,CAAC6L,MAAD,EAASjK,IAAT,EAAe;IACxB,MAAMtC,EAAE,GAAG,MAAMU,aAAN,CAAoB6L,MAApB,EAA4BjK,IAA5B,CAAX;IACA,MAAMM,YAAN,CAAmB5C,EAAnB,EAAuB,KAAKwO,WAA5B,EAAyC,EAAzC;IACA,OAAOxO,EAAP;EACH;;AAhB+D;;AAkBpE,MAAMyL,iBAAN,SAAgCX,mBAAhC,CAAoD;EAChDpL,WAAW,CAAC+K,YAAD,EAAeC,gBAAf,EAAiCgE,MAAjC,EAAyCH,SAAzC,EAAoD;IAC3D,MAAM9D,YAAN;IACA,KAAKC,gBAAL,GAAwBA,gBAAxB;IACA,KAAKgE,MAAL,GAAcA,MAAd;IACA,KAAKC,UAAL,GAAkBD,MAAM,CAACE,YAAP,CAAoB;MAAEC,IAAI,EAAE;IAAR,CAApB,CAAlB;IACA,KAAKnE,gBAAL,CAAsB9B,OAAtB,CAA8B,KAAK+F,UAAnC;IACA,MAAMnL,MAAM,GAAGsG,aAAa,CAACyE,SAAS,CAACpD,EAAX,EAAeoD,SAAS,CAAC/K,MAAzB,EAAiC,EAAjC,CAA5B;;IACA,KAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,MAAM,CAACG,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;MACpC,MAAM+E,OAAO,GAAG1H,QAAQ,CAACL,aAAT,CAAuB,OAAvB,CAAhB;MACA+H,OAAO,CAACC,WAAR,GAAsBlF,MAAM,CAACE,CAAD,CAA5B;MACA,KAAKiL,UAAL,CAAgBhG,WAAhB,CAA4BF,OAA5B;IACH;EACJ;;EACDqG,gBAAgB,CAACvO,IAAD,EAAO;IACnB,OAAOA,IAAI,KAAK,KAAKmO,MAAd,GAAuB,KAAKC,UAA5B,GAAyCpO,IAAhD;EACH;;EACDyL,OAAO,GAAG;IACN,KAAKtB,gBAAL,CAAsB5B,UAAtB,CAAiC,KAAK6F,UAAtC;EACH;;EACDhG,WAAW,CAAC4D,MAAD,EAASC,QAAT,EAAmB;IAC1B,OAAO,MAAM7D,WAAN,CAAkB,KAAKmG,gBAAL,CAAsBvC,MAAtB,CAAlB,EAAiDC,QAAjD,CAAP;EACH;;EACDI,YAAY,CAACL,MAAD,EAASC,QAAT,EAAmBK,QAAnB,EAA6B;IACrC,OAAO,MAAMD,YAAN,CAAmB,KAAKkC,gBAAL,CAAsBvC,MAAtB,CAAnB,EAAkDC,QAAlD,EAA4DK,QAA5D,CAAP;EACH;;EACDpM,WAAW,CAAC8L,MAAD,EAASO,QAAT,EAAmB;IAC1B,OAAO,MAAMrM,WAAN,CAAkB,KAAKqO,gBAAL,CAAsBvC,MAAtB,CAAlB,EAAiDO,QAAjD,CAAP;EACH;;EACDtM,UAAU,CAACD,IAAD,EAAO;IACb,OAAO,KAAKuO,gBAAL,CAAsB,MAAMtO,UAAN,CAAiB,KAAKsO,gBAAL,CAAsBvO,IAAtB,CAAjB,CAAtB,CAAP;EACH;;AA/B+C;AAkCpD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMwO,eAAN,SAA8BzH,kBAA9B,CAAiD;EAC7C5H,WAAW,CAACkB,GAAD,EAAM;IACb,MAAMA,GAAN;EACH,CAH4C,CAI7C;EACA;;;EACAqG,QAAQ,CAACN,SAAD,EAAY;IAChB,OAAO,IAAP;EACH;;EACDxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1CF,OAAO,CAACvG,gBAAR,CAAyBwG,SAAzB,EAAoCC,OAApC,EAA6C,KAA7C;IACA,OAAO,MAAM,KAAKxG,mBAAL,CAAyBsG,OAAzB,EAAkCC,SAAlC,EAA6CC,OAA7C,CAAb;EACH;;EACDxG,mBAAmB,CAACqB,MAAD,EAASkF,SAAT,EAAoB/B,QAApB,EAA8B;IAC7C,OAAOnD,MAAM,CAACrB,mBAAP,CAA2BuG,SAA3B,EAAsC/B,QAAtC,CAAP;EACH;;AAf4C;;AAiBjDmK,eAAe,CAACnJ,IAAhB;EAAA,iBAA4GmJ,eAA5G,EA9gB6FlS,EA8gB7F,UAA6IJ,QAA7I;AAAA;;AACAsS,eAAe,CAAClJ,KAAhB,kBA/gB6FhJ,EA+gB7F;EAAA,OAAgHkS,eAAhH;EAAA,SAAgHA,eAAhH;AAAA;;AACA;EAAA,mDAhhB6FlS,EAghB7F,mBAA2FkS,eAA3F,EAAwH,CAAC;IAC7GjJ,IAAI,EAAE3I;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CAPL;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMuS,aAAa,GAAG,CAAC,KAAD,EAAQ,SAAR,EAAmB,MAAnB,EAA2B,OAA3B,CAAtB;AACA,MAAMC,uBAAuB,GAAG,CAAhC,C,CACA;;AACA,MAAMC,OAAO,GAAG;EACZ;EACA;EACA,MAAM,WAHM;EAIZ,MAAM,KAJM;EAKZ,QAAQ,QALI;EAMZ,QAAQ,QANI;EAOZ,OAAO,QAPK;EAQZ,OAAO,QARK;EASZ,QAAQ,WATI;EAUZ,SAAS,YAVG;EAWZ,MAAM,SAXM;EAYZ,QAAQ,WAZI;EAaZ,QAAQ,aAbI;EAcZ,UAAU,YAdE;EAeZ,OAAO;AAfK,CAAhB,C,CAiBA;AACA;AACA;;AACA,MAAMC,mBAAmB,GAAG;EACxB,KAAK,GADmB;EAExB,KAAK,GAFmB;EAGxB,KAAK,GAHmB;EAIxB,KAAK,GAJmB;EAKxB,KAAK,GALmB;EAMxB,KAAK,GANmB;EAOxB,KAAK,GAPmB;EAQxB,KAAK,GARmB;EASxB,KAAK,GATmB;EAUxB,KAAK,GAVmB;EAWxB,KAAK,GAXmB;EAYxB,KAAK,GAZmB;EAaxB,KAAK,GAbmB;EAcxB,KAAK,GAdmB;EAexB,QAAQ,GAfgB;EAgBxB,QAAQ;AAhBgB,CAA5B;AAkBA;AACA;AACA;;AACA,MAAMC,oBAAoB,GAAG;EACzB,OAAQjF,KAAD,IAAWA,KAAK,CAACkF,MADC;EAEzB,WAAYlF,KAAD,IAAWA,KAAK,CAACmF,OAFH;EAGzB,QAASnF,KAAD,IAAWA,KAAK,CAACoF,OAHA;EAIzB,SAAUpF,KAAD,IAAWA,KAAK,CAACqF;AAJD,CAA7B;AAMA;AACA;AACA;AACA;;AACA,MAAMC,eAAN,SAA8BnI,kBAA9B,CAAiD;EAC7C;AACJ;AACA;AACA;EACI5H,WAAW,CAACkB,GAAD,EAAM;IACb,MAAMA,GAAN;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIqG,QAAQ,CAACN,SAAD,EAAY;IAChB,OAAO8I,eAAe,CAACC,cAAhB,CAA+B/I,SAA/B,KAA6C,IAApD;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAM+I,WAAW,GAAGF,eAAe,CAACC,cAAhB,CAA+B/I,SAA/B,CAApB;IACA,MAAMiJ,cAAc,GAAGH,eAAe,CAACI,aAAhB,CAA8BF,WAAW,CAAC,SAAD,CAAzC,EAAsD/I,OAAtD,EAA+D,KAAKN,OAAL,CAAaU,OAAb,EAA/D,CAAvB;IACA,OAAO,KAAKV,OAAL,CAAaU,OAAb,GAAuB8I,iBAAvB,CAAyC,MAAM;MAClD,OAAOtT,OAAO,GAAGuD,WAAV,CAAsB2G,OAAtB,EAA+BiJ,WAAW,CAAC,cAAD,CAA1C,EAA4DC,cAA5D,CAAP;IACH,CAFM,CAAP;EAGH;;EACoB,OAAdF,cAAc,CAAC/I,SAAD,EAAY;IAC7B,MAAMoJ,KAAK,GAAGpJ,SAAS,CAACqJ,WAAV,GAAwBC,KAAxB,CAA8B,GAA9B,CAAd;IACA,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAN,EAArB;;IACA,IAAKJ,KAAK,CAACpM,MAAN,KAAiB,CAAlB,IAAwB,EAAEuM,YAAY,KAAK,SAAjB,IAA8BA,YAAY,KAAK,OAAjD,CAA5B,EAAuF;MACnF,OAAO,IAAP;IACH;;IACD,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAhB,CAA8BN,KAAK,CAACO,GAAN,EAA9B,CAAZ;;IACA,IAAIC,OAAO,GAAG,EAAd;IACAvB,aAAa,CAAC9J,OAAd,CAAsBsL,YAAY,IAAI;MAClC,MAAMC,KAAK,GAAGV,KAAK,CAACW,OAAN,CAAcF,YAAd,CAAd;;MACA,IAAIC,KAAK,GAAG,CAAC,CAAb,EAAgB;QACZV,KAAK,CAACY,MAAN,CAAaF,KAAb,EAAoB,CAApB;QACAF,OAAO,IAAIC,YAAY,GAAG,GAA1B;MACH;IACJ,CAND;IAOAD,OAAO,IAAIH,GAAX;;IACA,IAAIL,KAAK,CAACpM,MAAN,IAAgB,CAAhB,IAAqByM,GAAG,CAACzM,MAAJ,KAAe,CAAxC,EAA2C;MACvC;MACA,OAAO,IAAP;IACH,CAnB4B,CAoB7B;IACA;IACA;;;IACA,MAAMiN,MAAM,GAAG,EAAf;IACAA,MAAM,CAAC,cAAD,CAAN,GAAyBV,YAAzB;IACAU,MAAM,CAAC,SAAD,CAAN,GAAoBL,OAApB;IACA,OAAOK,MAAP;EACH;;EACqB,OAAfC,eAAe,CAAC1G,KAAD,EAAQ;IAC1B,IAAIoG,OAAO,GAAG,EAAd;IACA,IAAIH,GAAG,GAAGU,WAAW,CAAC3G,KAAD,CAArB;IACAiG,GAAG,GAAGA,GAAG,CAACJ,WAAJ,EAAN;;IACA,IAAII,GAAG,KAAK,GAAZ,EAAiB;MACbA,GAAG,GAAG,OAAN,CADa,CACE;IAClB,CAFD,MAGK,IAAIA,GAAG,KAAK,GAAZ,EAAiB;MAClBA,GAAG,GAAG,KAAN,CADkB,CACL;IAChB;;IACDpB,aAAa,CAAC9J,OAAd,CAAsBsL,YAAY,IAAI;MAClC,IAAIA,YAAY,IAAIJ,GAApB,EAAyB;QACrB,MAAMW,cAAc,GAAG3B,oBAAoB,CAACoB,YAAD,CAA3C;;QACA,IAAIO,cAAc,CAAC5G,KAAD,CAAlB,EAA2B;UACvBoG,OAAO,IAAIC,YAAY,GAAG,GAA1B;QACH;MACJ;IACJ,CAPD;IAQAD,OAAO,IAAIH,GAAX;IACA,OAAOG,OAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACwB,OAAbV,aAAa,CAACU,OAAD,EAAU3J,OAAV,EAAmBoK,IAAnB,EAAyB;IACzC,OAAO,CAAC7G;IAAM;IAAP,KAA6B;MAChC,IAAIsF,eAAe,CAACoB,eAAhB,CAAgC1G,KAAhC,MAA2CoG,OAA/C,EAAwD;QACpDS,IAAI,CAACC,UAAL,CAAgB,MAAMrK,OAAO,CAACuD,KAAD,CAA7B;MACH;IACJ,CAJD;EAKH;EACD;;;EACoB,OAAbkG,aAAa,CAACa,OAAD,EAAU;IAC1B;IACA,QAAQA,OAAR;MACI,KAAK,KAAL;QACI,OAAO,QAAP;;MACJ;QACI,OAAOA,OAAP;IAJR;EAMH;;AAvG4C;;AAyGjDzB,eAAe,CAAC7J,IAAhB;EAAA,iBAA4G6J,eAA5G,EAlsB6F5S,EAksB7F,UAA6IJ,QAA7I;AAAA;;AACAgT,eAAe,CAAC5J,KAAhB,kBAnsB6FhJ,EAmsB7F;EAAA,OAAgH4S,eAAhH;EAAA,SAAgHA,eAAhH;AAAA;;AACA;EAAA,mDApsB6F5S,EAosB7F,mBAA2F4S,eAA3F,EAAwH,CAAC;IAC7G3J,IAAI,EAAE3I;EADuG,CAAD,CAAxH,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CAPL;AAAA;;AAQA,SAASqU,WAAT,CAAqB3G,KAArB,EAA4B;EACxB,IAAIiG,GAAG,GAAGjG,KAAK,CAACiG,GAAhB;;EACA,IAAIA,GAAG,IAAI,IAAX,EAAiB;IACbA,GAAG,GAAGjG,KAAK,CAACgH,aAAZ,CADa,CAEb;IACA;IACA;;IACA,IAAIf,GAAG,IAAI,IAAX,EAAiB;MACb,OAAO,cAAP;IACH;;IACD,IAAIA,GAAG,CAACgB,UAAJ,CAAe,IAAf,CAAJ,EAA0B;MACtBhB,GAAG,GAAGiB,MAAM,CAACC,YAAP,CAAoBC,QAAQ,CAACnB,GAAG,CAACoB,SAAJ,CAAc,CAAd,CAAD,EAAmB,EAAnB,CAA5B,CAAN;;MACA,IAAIrH,KAAK,CAACsH,QAAN,KAAmBxC,uBAAnB,IAA8CE,mBAAmB,CAACuC,cAApB,CAAmCtB,GAAnC,CAAlD,EAA2F;QACvF;QACA;QACA;QACAA,GAAG,GAAGjB,mBAAmB,CAACiB,GAAD,CAAzB;MACH;IACJ;EACJ;;EACD,OAAOlB,OAAO,CAACkB,GAAD,CAAP,IAAgBA,GAAvB;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMuB,WAAW,GAAG,OAAOrI,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAA1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASsI,oBAAT,CAA8BC,aAA9B,EAA6CC,OAA7C,EAAsD;EAClD,IAAIC,EAAJ;;EACA,OAAOvU,6BAA6B,CAAC;IACjCqU,aADiC;IAEjCG,YAAY,EAAE,CACV,GAAGC,wBADO,EAEV,IAAI,CAACF,EAAE,GAAGD,OAAO,KAAK,IAAZ,IAAoBA,OAAO,KAAK,KAAK,CAArC,GAAyC,KAAK,CAA9C,GAAkDA,OAAO,CAACI,SAAhE,MAA+E,IAA/E,IAAuFH,EAAE,KAAK,KAAK,CAAnG,GAAuGA,EAAvG,GAA4G,EAAhH,CAFU,CAFmB;IAMjCI,iBAAiB,EAAEC;EANc,CAAD,CAApC;AAQH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,+BAAT,GAA2C;EACvC;EACA;EACA,OAAO,CAAC,GAAGC,qBAAJ,CAAP;AACH;;AACD,SAASC,cAAT,GAA0B;EACtB1S,iBAAiB,CAACC,WAAlB;AACH;;AACD,SAAS0S,YAAT,GAAwB;EACpB,OAAO,IAAI/U,YAAJ,EAAP;AACH;;AACD,SAASgV,SAAT,GAAqB;EACjB;EACA/U,YAAY,CAACqD,QAAD,CAAZ;EACA,OAAOA,QAAP;AACH;;AACD,MAAMqR,mCAAmC,GAAG,CACxC;EAAEvO,OAAO,EAAElG,WAAX;EAAwB+U,QAAQ,EAAEhW;AAAlC,CADwC,EAExC;EAAEmH,OAAO,EAAEjG,oBAAX;EAAiC8U,QAAQ,EAAEH,cAA3C;EAA2DvO,KAAK,EAAE;AAAlE,CAFwC,EAGxC;EAAEH,OAAO,EAAEpH,QAAX;EAAqBqH,UAAU,EAAE2O,SAAjC;EAA4C1O,IAAI,EAAE;AAAlD,CAHwC,CAA5C;AAKA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM4O,eAAe,GAAG9U,qBAAqB,CAACC,YAAD,EAAe,SAAf,EAA0BsU,mCAA1B,CAA7C;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMQ,+BAA+B,GAAG,IAAI9V,cAAJ,CAAmB6U,WAAW,GAAG,gCAAH,GAAsC,EAApE,CAAxC;AACA,MAAMW,qBAAqB,GAAG,CAC1B;EACIzO,OAAO,EAAE9F,mBADb;EAEI8U,QAAQ,EAAE5O,qBAFd;EAGIF,IAAI,EAAE;AAHV,CAD0B,EAM1B;EACIF,OAAO,EAAE7F,YADb;EAEI6U,QAAQ,EAAE5U,WAFd;EAGI8F,IAAI,EAAE,CAAC7F,MAAD,EAASC,mBAAT,EAA8BJ,mBAA9B;AAHV,CAN0B,EAW1B;EACI8F,OAAO,EAAE5F,WADb;EAEI4U,QAAQ,EAAE5U,WAFd;EAGI8F,IAAI,EAAE,CAAC7F,MAAD,EAASC,mBAAT,EAA8BJ,mBAA9B;AAHV,CAX0B,CAA9B;AAiBA,MAAMkU,wBAAwB,GAAG,CAC7B;EAAEpO,OAAO,EAAEzF,eAAX;EAA4BsU,QAAQ,EAAE;AAAtC,CAD6B,EAE7B;EAAE7O,OAAO,EAAEpG,YAAX;EAAyBqG,UAAU,EAAE0O,YAArC;EAAmDzO,IAAI,EAAE;AAAzD,CAF6B,EAEkC;EAC3DF,OAAO,EAAEkC,qBADkD;EAE3D8M,QAAQ,EAAE9D,eAFiD;EAG3D/K,KAAK,EAAE,IAHoD;EAI3DD,IAAI,EAAE,CAACtH,QAAD,EAAWyB,MAAX,EAAmBP,WAAnB;AAJqD,CAFlC,EAQ7B;EAAEkG,OAAO,EAAEkC,qBAAX;EAAkC8M,QAAQ,EAAEpD,eAA5C;EAA6DzL,KAAK,EAAE,IAApE;EAA0ED,IAAI,EAAE,CAACtH,QAAD;AAAhF,CAR6B,EAQiE;EAC1FoH,OAAO,EAAE2G,mBADiF;EAE1FqI,QAAQ,EAAErI,mBAFgF;EAG1FzG,IAAI,EAAE,CAACiC,YAAD,EAAeoC,mBAAf,EAAoC9K,MAApC;AAHoF,CARjE,EAa7B;EAAEuG,OAAO,EAAExF,gBAAX;EAA6ByU,WAAW,EAAEtI;AAA1C,CAb6B,EAc7B;EAAE3G,OAAO,EAAE2D,gBAAX;EAA6BsL,WAAW,EAAE1K;AAA1C,CAd6B,EAe7B;EAAEvE,OAAO,EAAEuE,mBAAX;EAAgCyK,QAAQ,EAAEzK,mBAA1C;EAA+DrE,IAAI,EAAE,CAACtH,QAAD;AAArE,CAf6B,EAgB7B;EAAEoH,OAAO,EAAEmC,YAAX;EAAyB6M,QAAQ,EAAE7M,YAAnC;EAAiDjC,IAAI,EAAE,CAACgC,qBAAD,EAAwB7H,MAAxB;AAAvD,CAhB6B,EAiB7B;EAAE2F,OAAO,EAAElH,UAAX;EAAuBkW,QAAQ,EAAEpN,UAAjC;EAA6C1B,IAAI,EAAE;AAAnD,CAjB6B,EAkB7B4N,WAAW,GAAG;EAAE9N,OAAO,EAAE+O,+BAAX;EAA4CF,QAAQ,EAAE;AAAtD,CAAH,GAAkE,EAlBhD,CAAjC;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMK,aAAN,CAAoB;EAChBrT,WAAW,CAACsT,uBAAD,EAA0B;IACjC,IAAIrB,WAAW,IAAIqB,uBAAnB,EAA4C;MACxC,MAAM,IAAIxO,KAAJ,CAAW,oFAAD,GACX,mFADC,CAAN;IAEH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EAC+B,OAApByO,oBAAoB,CAACC,MAAD,EAAS;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aADP;MAEHb,SAAS,EAAE,CACP;QAAErO,OAAO,EAAEvG,MAAX;QAAmBoV,QAAQ,EAAEQ,MAAM,CAACvI;MAApC,CADO,EAEP;QAAE9G,OAAO,EAAEb,aAAX;QAA0B8P,WAAW,EAAExV;MAAvC,CAFO,EAGPsG,2BAHO;IAFR,CAAP;EAQH;;AAxBe;;AA0BpBmP,aAAa,CAACnN,IAAd;EAAA,iBAA0GmN,aAA1G,EAz6B6FlW,EAy6B7F,UAAyI+V,+BAAzI;AAAA;;AACAG,aAAa,CAACK,IAAd,kBA16B6FvW,EA06B7F;EAAA,MAA2GkW;AAA3G;AACAA,aAAa,CAACM,IAAd,kBA36B6FxW,EA26B7F;EAAA,WAAqI,CAC7H,GAAGoV,wBAD0H,EAE7H,GAAGK,qBAF0H,CAArI;EAAA,UAGiB1V,YAHjB,EAG+B0B,iBAH/B;AAAA;;AAIA;EAAA,mDA/6B6FzB,EA+6B7F,mBAA2FkW,aAA3F,EAAsH,CAAC;IAC3GjN,IAAI,EAAEvH,QADqG;IAE3G8I,IAAI,EAAE,CAAC;MACC6K,SAAS,EAAE,CACP,GAAGD,wBADI,EAEP,GAAGK,qBAFI,CADZ;MAKCgB,OAAO,EAAE,CAAC1W,YAAD,EAAe0B,iBAAf;IALV,CAAD;EAFqG,CAAD,CAAtH,EAS4B,YAAY;IAChC,OAAO,CAAC;MAAEwH,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAEtH;MADkB,CAAD,EAExB;QACCsH,IAAI,EAAErH;MADP,CAFwB,EAIxB;QACCqH,IAAI,EAAE1I,MADP;QAECiK,IAAI,EAAE,CAACuL,+BAAD;MAFP,CAJwB;IAA/B,CAAD,CAAP;EAQH,CAlBL;AAAA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,SAASW,UAAT,GAAsB;EAClB,OAAO,IAAIC,IAAJ,CAAS9U,QAAQ,CAACjC,QAAD,CAAjB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+W,IAAN,CAAW;EACP9T,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAKkM,IAAL,GAAYjX,OAAO,EAAnB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIkX,MAAM,CAACC,GAAD,EAAMC,aAAa,GAAG,KAAtB,EAA6B;IAC/B,IAAI,CAACD,GAAL,EACI,OAAO,IAAP;IACJ,OAAO,KAAKE,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,OAAO,CAACC,IAAD,EAAOH,aAAa,GAAG,KAAvB,EAA8B;IACjC,IAAI,CAACG,IAAL,EACI,OAAO,EAAP;IACJ,OAAOA,IAAI,CAACC,MAAL,CAAY,CAACpD,MAAD,EAAS+C,GAAT,KAAiB;MAChC,IAAIA,GAAJ,EAAS;QACL/C,MAAM,CAACxL,IAAP,CAAY,KAAKyO,mBAAL,CAAyBF,GAAzB,EAA8BC,aAA9B,CAAZ;MACH;;MACD,OAAOhD,MAAP;IACH,CALM,EAKJ,EALI,CAAP;EAMH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIqD,MAAM,CAACC,YAAD,EAAe;IACjB,IAAI,CAACA,YAAL,EACI,OAAO,IAAP;IACJ,OAAO,KAAK3M,IAAL,CAAU/E,aAAV,CAAyB,QAAO0R,YAAa,GAA7C,KAAoD,IAA3D;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,OAAO,CAACD,YAAD,EAAe;IAClB,IAAI,CAACA,YAAL,EACI,OAAO,EAAP;;IACJ,MAAME;IAAK;IAAD,EAAgB,KAAK7M,IAAL,CAAU9D,gBAAV,CAA4B,QAAOyQ,YAAa,GAAhD,CAA1B;;IACA,OAAOE,IAAI,GAAG,GAAG5N,KAAH,CAAS6N,IAAT,CAAcD,IAAd,CAAH,GAAyB,EAApC;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIE,SAAS,CAACX,GAAD,EAAMY,QAAN,EAAgB;IACrB,IAAI,CAACZ,GAAL,EACI,OAAO,IAAP;IACJY,QAAQ,GAAGA,QAAQ,IAAI,KAAKC,cAAL,CAAoBb,GAApB,CAAvB;IACA,MAAMc,IAAI,GAAG,KAAKR,MAAL,CAAYM,QAAZ,CAAb;;IACA,IAAIE,IAAJ,EAAU;MACN,OAAO,KAAKC,yBAAL,CAA+Bf,GAA/B,EAAoCc,IAApC,CAAP;IACH;;IACD,OAAO,KAAKZ,mBAAL,CAAyBF,GAAzB,EAA8B,IAA9B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIgB,SAAS,CAACT,YAAD,EAAe;IACpB,KAAKU,gBAAL,CAAsB,KAAKX,MAAL,CAAYC,YAAZ,CAAtB;EACH;EACD;AACJ;AACA;AACA;;;EACIU,gBAAgB,CAACH,IAAD,EAAO;IACnB,IAAIA,IAAJ,EAAU;MACN,KAAKhB,IAAL,CAAUnT,MAAV,CAAiBmU,IAAjB;IACH;EACJ;;EACDZ,mBAAmB,CAACY,IAAD,EAAOb,aAAa,GAAG,KAAvB,EAA8B;IAC7C,IAAI,CAACA,aAAL,EAAoB;MAChB,MAAMW,QAAQ,GAAG,KAAKC,cAAL,CAAoBC,IAApB,CAAjB,CADgB,CAEhB;MACA;MACA;;;MACA,MAAMrQ,IAAI,GAAG,KAAK+P,OAAL,CAAaI,QAAb,EAAuBM,MAAvB,CAA8BzQ,IAAI,IAAI,KAAK0Q,mBAAL,CAAyBL,IAAzB,EAA+BrQ,IAA/B,CAAtC,EAA4E,CAA5E,CAAb;MACA,IAAIA,IAAI,KAAK+C,SAAb,EACI,OAAO/C,IAAP;IACP;;IACD,MAAMsC,OAAO,GAAG,KAAK+M,IAAL,CAAU/S,aAAV,CAAwB,MAAxB,CAAhB;;IACA,KAAKgU,yBAAL,CAA+BD,IAA/B,EAAqC/N,OAArC;;IACA,MAAM4B,IAAI,GAAG,KAAKf,IAAL,CAAUwN,oBAAV,CAA+B,MAA/B,EAAuC,CAAvC,CAAb;;IACAzM,IAAI,CAACK,WAAL,CAAiBjC,OAAjB;IACA,OAAOA,OAAP;EACH;;EACDgO,yBAAyB,CAACf,GAAD,EAAM3T,EAAN,EAAU;IAC/B6L,MAAM,CAACmJ,IAAP,CAAYrB,GAAZ,EAAiBzO,OAAjB,CAA0B+P,IAAD,IAAUjV,EAAE,CAAC4C,YAAH,CAAgB,KAAKsS,cAAL,CAAoBD,IAApB,CAAhB,EAA2CtB,GAAG,CAACsB,IAAD,CAA9C,CAAnC;IACA,OAAOjV,EAAP;EACH;;EACDwU,cAAc,CAACb,GAAD,EAAM;IAChB,MAAMwB,IAAI,GAAGxB,GAAG,CAACrR,IAAJ,GAAW,MAAX,GAAoB,UAAjC;IACA,OAAQ,GAAE6S,IAAK,KAAIxB,GAAG,CAACwB,IAAD,CAAO,GAA7B;EACH;;EACDL,mBAAmB,CAACnB,GAAD,EAAMvP,IAAN,EAAY;IAC3B,OAAOyH,MAAM,CAACmJ,IAAP,CAAYrB,GAAZ,EAAiByB,KAAjB,CAAwBhF,GAAD,IAAShM,IAAI,CAAC3B,YAAL,CAAkB,KAAKyS,cAAL,CAAoB9E,GAApB,CAAlB,MAAgDuD,GAAG,CAACvD,GAAD,CAAnF,CAAP;EACH;;EACD8E,cAAc,CAACD,IAAD,EAAO;IACjB,OAAOI,aAAa,CAACJ,IAAD,CAAb,IAAuBA,IAA9B;EACH;;AA9HM;;AAgIXzB,IAAI,CAAC5N,IAAL;EAAA,iBAAiG4N,IAAjG,EAtmC6F3W,EAsmC7F,UAAuHJ,QAAvH;AAAA;;AACA+W,IAAI,CAAC3N,KAAL,kBAvmC6FhJ,EAumC7F;EAAA,OAAqG2W,IAArG;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAA2ID,UAA3I;IAAA;;IAAA;EAAA;EAAA,YAAuH;AAAvH;;AACA;EAAA,mDAxmC6F1W,EAwmC7F,mBAA2F2W,IAA3F,EAA6G,CAAC;IAClG1N,IAAI,EAAE3I,UAD4F;IAElGkK,IAAI,EAAE,CAAC;MAAEiO,UAAU,EAAE,MAAd;MAAsBxR,UAAU,EAAEyP,UAAlC;MAA8CxP,IAAI,EAAE;IAApD,CAAD;EAF4F,CAAD,CAA7G,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAE+B,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CARL;AAAA;AASA;AACA;AACA;;;AACA,MAAM4Y,aAAa,GAAG;EAClBE,SAAS,EAAE;AADO,CAAtB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;AACA,SAASC,WAAT,GAAuB;EACnB,OAAO,IAAIC,KAAJ,CAAU/W,QAAQ,CAACjC,QAAD,CAAlB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgZ,KAAN,CAAY;EACR/V,WAAW,CAAC6H,IAAD,EAAO;IACd,KAAKA,IAAL,GAAYA,IAAZ;EACH;EACD;AACJ;AACA;;;EACImO,QAAQ,GAAG;IACP,OAAO,KAAKnO,IAAL,CAAUoO,KAAjB;EACH;EACD;AACJ;AACA;AACA;;;EACIC,QAAQ,CAACC,QAAD,EAAW;IACf,KAAKtO,IAAL,CAAUoO,KAAV,GAAkBE,QAAQ,IAAI,EAA9B;EACH;;AAhBO;;AAkBZJ,KAAK,CAAC7P,IAAN;EAAA,iBAAkG6P,KAAlG,EAjqC6F5Y,EAiqC7F,UAAyHJ,QAAzH;AAAA;;AACAgZ,KAAK,CAAC5P,KAAN,kBAlqC6FhJ,EAkqC7F;EAAA,OAAsG4Y,KAAtG;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAA6ID,WAA7I;IAAA;;IAAA;EAAA;EAAA,YAAyH;AAAzH;;AACA;EAAA,mDAnqC6F3Y,EAmqC7F,mBAA2F4Y,KAA3F,EAA8G,CAAC;IACnG3P,IAAI,EAAE3I,UAD6F;IAEnGkK,IAAI,EAAE,CAAC;MAAEiO,UAAU,EAAE,MAAd;MAAsBxR,UAAU,EAAE0R,WAAlC;MAA+CzR,IAAI,EAAE;IAArD,CAAD;EAF6F,CAAD,CAA9G,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAE+B,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CARL;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqZ,iBAAiB,GAAG,UAA1B;AACA,MAAMC,gBAAgB,GAAG,WAAzB;;AACA,SAASC,mBAAT,CAA6BC,KAA7B,EAAoC;EAChC,OAAOA,KAAK,CAACrM,OAAN,CAAckM,iBAAd,EAAiC,CAAC,GAAGI,CAAJ,KAAU,MAAMA,CAAC,CAAC,CAAD,CAAD,CAAKlG,WAAL,EAAjD,CAAP;AACH;;AACD,SAASmG,mBAAT,CAA6BF,KAA7B,EAAoC;EAChC,OAAOA,KAAK,CAACrM,OAAN,CAAcmM,gBAAd,EAAgC,CAAC,GAAGG,CAAJ,KAAUA,CAAC,CAAC,CAAD,CAAD,CAAKE,WAAL,EAA1C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,WAAT,CAAqB/T,IAArB,EAA2B8J,KAA3B,EAAkC;EAC9B,IAAI,OAAOkK,QAAP,KAAoB,WAApB,IAAmC,CAACA,QAAxC,EAAkD;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAGrZ,OAAO,CAAC,IAAD,CAAP,GAAgBA,OAAO,CAAC,IAAD,CAAP,IAAiB,EAA5C;IACAqZ,EAAE,CAACjU,IAAD,CAAF,GAAW8J,KAAX;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMoK,GAAG,GAAG,OAAO9U,MAAP,KAAkB,WAAlB,IAAiCA,MAAjC,IAA2C,EAAvD;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM+U,yBAAN,CAAgC;EAC5B/W,WAAW,CAACgX,SAAD,EAAYC,QAAZ,EAAsB;IAC7B,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;AAJ2B;AAMhC;AACA;AACA;AACA;;;AACA,MAAMC,eAAN,CAAsB;EAClBlX,WAAW,CAACmX,GAAD,EAAM;IACb,KAAKC,MAAL,GAAcD,GAAG,CAAC1T,QAAJ,CAAaC,GAAb,CAAiBzE,cAAjB,CAAd;EACH,CAHiB,CAIlB;;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIoY,mBAAmB,CAACC,MAAD,EAAS;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAD,CAA/B;IACA,MAAME,WAAW,GAAG,kBAApB,CAFwB,CAGxB;;IACA,MAAMC,mBAAmB,GAAGX,GAAG,CAACjL,OAAJ,CAAY6L,OAAZ,IAAuB,IAAnD;;IACA,IAAIH,MAAM,IAAIE,mBAAd,EAAmC;MAC/BX,GAAG,CAACjL,OAAJ,CAAY6L,OAAZ,CAAoBF,WAApB;IACH;;IACD,MAAMG,KAAK,GAAGC,cAAc,EAA5B;IACA,IAAIX,QAAQ,GAAG,CAAf;;IACA,OAAOA,QAAQ,GAAG,CAAX,IAAiBW,cAAc,KAAKD,KAApB,GAA6B,GAApD,EAAyD;MACrD,KAAKP,MAAL,CAAYS,IAAZ;MACAZ,QAAQ;IACX;;IACD,MAAMhL,GAAG,GAAG2L,cAAc,EAA1B;;IACA,IAAIL,MAAM,IAAIE,mBAAd,EAAmC;MAC/BX,GAAG,CAACjL,OAAJ,CAAYiM,UAAZ,CAAuBN,WAAvB;IACH;;IACD,MAAMR,SAAS,GAAG,CAAC/K,GAAG,GAAG0L,KAAP,IAAgBV,QAAlC;IACAH,GAAG,CAACjL,OAAJ,CAAYkM,GAAZ,CAAiB,OAAMd,QAAS,0BAAhC;IACAH,GAAG,CAACjL,OAAJ,CAAYkM,GAAZ,CAAiB,GAAEf,SAAS,CAACgB,OAAV,CAAkB,CAAlB,CAAqB,eAAxC;IACA,OAAO,IAAIjB,yBAAJ,CAA8BC,SAA9B,EAAyCC,QAAzC,CAAP;EACH;;AA3CiB;;AA6CtB,SAASW,cAAT,GAA0B;EACtB,OAAOd,GAAG,CAACmB,WAAJ,IAAmBnB,GAAG,CAACmB,WAAJ,CAAgBC,GAAnC,GAAyCpB,GAAG,CAACmB,WAAJ,CAAgBC,GAAhB,EAAzC,GACH,IAAIC,IAAJ,GAAWC,OAAX,EADJ;AAEH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,oBAAoB,GAAG,UAA7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,gBAAT,CAA0BnB,GAA1B,EAA+B;EAC3BR,WAAW,CAAC0B,oBAAD,EAAuB,IAAInB,eAAJ,CAAoBC,GAApB,CAAvB,CAAX;EACA,OAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASoB,iBAAT,GAA6B;EACzB5B,WAAW,CAAC0B,oBAAD,EAAuB,IAAvB,CAAX;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,UAAT,CAAoBC,IAApB,EAA0B;EACtB,MAAMC,WAAW,GAAG;IAChB,KAAK,KADW;IAEhB,KAAK,KAFW;IAGhB,MAAM,KAHU;IAIhB,KAAK,KAJW;IAKhB,KAAK;EALW,CAApB;EAOA,OAAOD,IAAI,CAACvO,OAAL,CAAa,UAAb,EAAyByO,CAAC,IAAID,WAAW,CAACC,CAAD,CAAzC,CAAP;AACH;;AACD,SAASC,YAAT,CAAsBH,IAAtB,EAA4B;EACxB,MAAMI,aAAa,GAAG;IAClB,OAAO,GADW;IAElB,OAAO,GAFW;IAGlB,OAAO,IAHW;IAIlB,OAAO,GAJW;IAKlB,OAAO;EALW,CAAtB;EAOA,OAAOJ,IAAI,CAACvO,OAAL,CAAa,UAAb,EAAyByO,CAAC,IAAIE,aAAa,CAACF,CAAD,CAA3C,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASG,YAAT,CAAsBpI,GAAtB,EAA2B;EACvB,OAAOA,GAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqI,aAAN,CAAoB;EAChB/Y,WAAW,GAAG;IACV,KAAKgZ,KAAL,GAAa,EAAb;IACA,KAAKC,oBAAL,GAA4B,EAA5B;EACH;EACD;;;EACW,OAAJC,IAAI,CAACC,SAAD,EAAY;IACnB,MAAMC,aAAa,GAAG,IAAIL,aAAJ,EAAtB;IACAK,aAAa,CAACJ,KAAd,GAAsBG,SAAtB;IACA,OAAOC,aAAP;EACH;EACD;AACJ;AACA;;;EACI1V,GAAG,CAACgN,GAAD,EAAM2I,YAAN,EAAoB;IACnB,OAAO,KAAKL,KAAL,CAAWtI,GAAX,MAAoBjJ,SAApB,GAAgC,KAAKuR,KAAL,CAAWtI,GAAX,CAAhC,GAAkD2I,YAAzD;EACH;EACD;AACJ;AACA;;;EACI7R,GAAG,CAACkJ,GAAD,EAAMhE,KAAN,EAAa;IACZ,KAAKsM,KAAL,CAAWtI,GAAX,IAAkBhE,KAAlB;EACH;EACD;AACJ;AACA;;;EACI9L,MAAM,CAAC8P,GAAD,EAAM;IACR,OAAO,KAAKsI,KAAL,CAAWtI,GAAX,CAAP;EACH;EACD;AACJ;AACA;;;EACI4I,MAAM,CAAC5I,GAAD,EAAM;IACR,OAAO,KAAKsI,KAAL,CAAWhH,cAAX,CAA0BtB,GAA1B,CAAP;EACH;EACD;AACJ;AACA;;;EACI6I,WAAW,CAAC7I,GAAD,EAAMxL,QAAN,EAAgB;IACvB,KAAK+T,oBAAL,CAA0BvI,GAA1B,IAAiCxL,QAAjC;EACH;EACD;AACJ;AACA;;;EACIsU,MAAM,GAAG;IACL;IACA,KAAK,MAAM9I,GAAX,IAAkB,KAAKuI,oBAAvB,EAA6C;MACzC,IAAI,KAAKA,oBAAL,CAA0BjH,cAA1B,CAAyCtB,GAAzC,CAAJ,EAAmD;QAC/C,IAAI;UACA,KAAKsI,KAAL,CAAWtI,GAAX,IAAkB,KAAKuI,oBAAL,CAA0BvI,GAA1B,GAAlB;QACH,CAFD,CAGA,OAAO+I,CAAP,EAAU;UACN5N,OAAO,CAACC,IAAR,CAAa,qCAAb,EAAoD2N,CAApD;QACH;MACJ;IACJ;;IACD,OAAOC,IAAI,CAACC,SAAL,CAAe,KAAKX,KAApB,CAAP;EACH;;AAzDe;;AA2DpBD,aAAa,CAAC7S,IAAd;EAAA,iBAA0G6S,aAA1G;AAAA;;AACAA,aAAa,CAAC5S,KAAd,kBAj7C6FhJ,EAi7C7F;EAAA,OAA8G4b,aAA9G;EAAA,SAA8GA,aAA9G;AAAA;;AACA;EAAA,mDAl7C6F5b,EAk7C7F,mBAA2F4b,aAA3F,EAAsH,CAAC;IAC3G3S,IAAI,EAAE3I;EADqG,CAAD,CAAtH;AAAA;;AAGA,SAASmc,iBAAT,CAA2B1Y,GAA3B,EAAgC+J,KAAhC,EAAuC;EACnC;EACA;EACA,MAAM4O,MAAM,GAAG3Y,GAAG,CAAC4Y,cAAJ,CAAmB7O,KAAK,GAAG,QAA3B,CAAf;EACA,IAAI8O,YAAY,GAAG,EAAnB;;EACA,IAAIF,MAAM,IAAIA,MAAM,CAAC7Q,WAArB,EAAkC;IAC9B,IAAI;MACA;MACA+Q,YAAY,GAAGL,IAAI,CAACM,KAAL,CAAWpB,YAAY,CAACiB,MAAM,CAAC7Q,WAAR,CAAvB,CAAf;IACH,CAHD,CAIA,OAAOyQ,CAAP,EAAU;MACN5N,OAAO,CAACC,IAAR,CAAa,qDAAqDb,KAAlE,EAAyEwO,CAAzE;IACH;EACJ;;EACD,OAAOV,aAAa,CAACG,IAAd,CAAmBa,YAAnB,CAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAME,0BAAN,CAAiC;;AAEjCA,0BAA0B,CAAC/T,IAA3B;EAAA,iBAAuH+T,0BAAvH;AAAA;;AACAA,0BAA0B,CAACvG,IAA3B,kBA98C6FvW,EA88C7F;EAAA,MAAwH8c;AAAxH;AACAA,0BAA0B,CAACtG,IAA3B,kBA/8C6FxW,EA+8C7F;EAAA,WAA+J,CAAC;IAAEgH,OAAO,EAAE4U,aAAX;IAA0B3U,UAAU,EAAEwV,iBAAtC;IAAyDvV,IAAI,EAAE,CAACtH,QAAD,EAAWa,MAAX;EAA/D,CAAD;AAA/J;;AACA;EAAA,mDAh9C6FT,EAg9C7F,mBAA2F8c,0BAA3F,EAAmI,CAAC;IACxH7T,IAAI,EAAEvH,QADkH;IAExH8I,IAAI,EAAE,CAAC;MACC6K,SAAS,EAAE,CAAC;QAAErO,OAAO,EAAE4U,aAAX;QAA0B3U,UAAU,EAAEwV,iBAAtC;QAAyDvV,IAAI,EAAE,CAACtH,QAAD,EAAWa,MAAX;MAA/D,CAAD;IADZ,CAAD;EAFkH,CAAD,CAAnI;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMsc,EAAN,CAAS;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACc,OAAHC,GAAG,GAAG;IACT,OAAO,MAAM,IAAb;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACc,OAAHC,GAAG,CAACvF,QAAD,EAAW;IACjB,OAAQwF,YAAD,IAAkB;MACrB,OAAOA,YAAY,CAACC,aAAb,IAA8B,IAA9B,GACHC,cAAc,CAACF,YAAY,CAACC,aAAd,EAA6BzF,QAA7B,CADX,GAEH,KAFJ;IAGH,CAJD;EAKH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACoB,OAAT2F,SAAS,CAACpU,IAAD,EAAO;IACnB,OAAQqU,SAAD,IAAeA,SAAS,CAACC,cAAV,CAAyB1J,OAAzB,CAAiC5K,IAAjC,MAA2C,CAAC,CAAlE;EACH;;AArCI;;AAuCT,SAASmU,cAAT,CAAwBI,CAAxB,EAA2B9F,QAA3B,EAAqC;EACjC,IAAI/X,OAAO,GAAG0E,aAAV,CAAwBmZ,CAAxB,CAAJ,EAAgC;IAC5B,OAAOA,CAAC,CAACC,OAAF,IAAaD,CAAC,CAACC,OAAF,CAAU/F,QAAV,CAAb,IACH8F,CAAC,CAACE,iBAAF,IAAuBF,CAAC,CAACE,iBAAF,CAAoBhG,QAApB,CADpB,IAEH8F,CAAC,CAACG,qBAAF,IAA2BH,CAAC,CAACG,qBAAF,CAAwBjG,QAAxB,CAF/B;EAGH;;EACD,OAAO,KAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMkG,WAAW,GAAG;EAChB;EACA,OAAO,IAFS;EAGhB,YAAY,IAHI;EAIhB,WAAW,IAJK;EAKhB,UAAU,IALM;EAMhB,aAAa,IANG;EAOhB,WAAW,IAPK;EAQhB,YAAY,IARI;EAShB,SAAS,IATO;EAUhB,WAAW,IAVK;EAWhB;EACA,SAAS,IAZO;EAahB,cAAc,IAbE;EAchB,aAAa,IAdG;EAehB,YAAY,IAfI;EAgBhB,eAAe,IAhBC;EAiBhB,WAAW,IAjBK;EAkBhB,YAAY,IAlBI;EAmBhB;EACA,SAAS,IApBO;EAqBhB,WAAW,IArBK;EAsBhB;EACA,UAAU,IAvBM;EAwBhB,eAAe,IAxBC;EAyBhB,cAAc,IAzBE;EA0BhB,aAAa,IA1BG;EA2BhB,gBAAgB,IA3BA;EA4BhB;EACA,SAAS,IA7BO;EA8BhB,aAAa,IA9BG;EA+BhB,cAAc,IA/BE;EAgChB,WAAW,IAhCK;EAiChB,aAAa,IAjCG;EAkChB;EACA,OAAO,IAnCS;EAoChB,aAAa;AApCG,CAApB;AAsCA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,qBAAqB,GAAG,IAAI5d,cAAJ,CAAmB,qBAAnB,CAA9B;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM6d,aAAa,GAAG,IAAI7d,cAAJ,CAAmB,cAAnB,CAAtB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM8d,mBAAN,CAA0B;EACtBlb,WAAW,GAAG;IACV;AACR;AACA;AACA;AACA;IACQ,KAAKmb,MAAL,GAAc,EAAd;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,SAAL,GAAiB,EAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIC,WAAW,CAACrU,OAAD,EAAU;IACjB,MAAMsU,EAAE,GAAG,IAAIC,MAAJ,CAAWvU,OAAX,EAAoB,KAAKoL,OAAzB,CAAX;IACAkJ,EAAE,CAAC5X,GAAH,CAAO,OAAP,EAAgB8D,GAAhB,CAAoB;MAAEgU,MAAM,EAAE;IAAV,CAApB;IACAF,EAAE,CAAC5X,GAAH,CAAO,QAAP,EAAiB8D,GAAjB,CAAqB;MAAEgU,MAAM,EAAE;IAAV,CAArB;;IACA,KAAK,MAAMvU,SAAX,IAAwB,KAAKmU,SAA7B,EAAwC;MACpCE,EAAE,CAAC5X,GAAH,CAAOuD,SAAP,EAAkBO,GAAlB,CAAsB,KAAK4T,SAAL,CAAenU,SAAf,CAAtB;IACH;;IACD,OAAOqU,EAAP;EACH;;AAxCqB;;AA0C1BJ,mBAAmB,CAAChV,IAApB;EAAA,iBAAgHgV,mBAAhH;AAAA;;AACAA,mBAAmB,CAAC/U,KAApB,kBAjoD6FhJ,EAioD7F;EAAA,OAAoH+d,mBAApH;EAAA,SAAoHA,mBAApH;AAAA;;AACA;EAAA,mDAloD6F/d,EAkoD7F,mBAA2F+d,mBAA3F,EAA4H,CAAC;IACjH9U,IAAI,EAAE3I;EAD2G,CAAD,CAA5H;AAAA;AAGA;AACA;AACA;AACA;AACA;;;AACA,MAAMge,oBAAN,SAAmC7T,kBAAnC,CAAsD;EAClD5H,WAAW,CAACkB,GAAD,EAAMwa,OAAN,EAAe7P,OAAf,EAAwB8P,MAAxB,EAAgC;IACvC,MAAMza,GAAN;IACA,KAAKwa,OAAL,GAAeA,OAAf;IACA,KAAK7P,OAAL,GAAeA,OAAf;IACA,KAAK8P,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsB,IAAtB;EACH;;EACDrU,QAAQ,CAACN,SAAD,EAAY;IAChB,IAAI,CAAC8T,WAAW,CAAC/I,cAAZ,CAA2B/K,SAAS,CAACqJ,WAAV,EAA3B,CAAD,IAAwD,CAAC,KAAKuL,aAAL,CAAmB5U,SAAnB,CAA7D,EAA4F;MACxF,OAAO,KAAP;IACH;;IACD,IAAI,CAACjF,MAAM,CAACuZ,MAAR,IAAkB,CAAC,KAAKI,MAA5B,EAAoC;MAChC,IAAI,OAAO/R,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;QAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,mDAAlB,GACb,iDADL;MAEH;;MACD,OAAO,KAAP;IACH;;IACD,OAAO,IAAP;EACH;;EACDxG,gBAAgB,CAACuG,OAAD,EAAUC,SAAV,EAAqBC,OAArB,EAA8B;IAC1C,MAAMoK,IAAI,GAAG,KAAK1K,OAAL,CAAaU,OAAb,EAAb;IACAL,SAAS,GAAGA,SAAS,CAACqJ,WAAV,EAAZ,CAF0C,CAG1C;IACA;;IACA,IAAI,CAACtO,MAAM,CAACuZ,MAAR,IAAkB,KAAKI,MAA3B,EAAmC;MAC/B,KAAKC,cAAL,GAAsB,KAAKA,cAAL,IAAuBtK,IAAI,CAAClB,iBAAL,CAAuB,MAAM,KAAKuL,MAAL,EAA7B,CAA7C,CAD+B,CAE/B;MACA;MACA;;MACA,IAAIG,kBAAkB,GAAG,KAAzB;;MACA,IAAIC,UAAU,GAAG,MAAM;QACnBD,kBAAkB,GAAG,IAArB;MACH,CAFD;;MAGAxK,IAAI,CAAClB,iBAAL,CAAuB,MAAM,KAAKwL,cAAL,CACxBhY,IADwB,CACnB,MAAM;QACZ;QACA,IAAI,CAAC5B,MAAM,CAACuZ,MAAZ,EAAoB;UAChB,IAAI,OAAO3R,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;YAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,mEAAnB;UACH;;UACDiQ,UAAU,GAAG,MAAM,CAAG,CAAtB;;UACA;QACH;;QACD,IAAI,CAACD,kBAAL,EAAyB;UACrB;UACA;UACA;UACAC,UAAU,GAAG,KAAKtb,gBAAL,CAAsBuG,OAAtB,EAA+BC,SAA/B,EAA0CC,OAA1C,CAAb;QACH;MACJ,CAhB4B,EAiBxB8U,KAjBwB,CAiBlB,MAAM;QACb,IAAI,OAAOpS,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;UAC/C,KAAKiC,OAAL,CAAaC,IAAb,CAAmB,QAAO7E,SAAU,6CAAlB,GACb,0BADL;QAEH;;QACD8U,UAAU,GAAG,MAAM,CAAG,CAAtB;MACH,CAvB4B,CAA7B,EAT+B,CAiC/B;MACA;MACA;;MACA,OAAO,MAAM;QACTA,UAAU;MACb,CAFD;IAGH;;IACD,OAAOzK,IAAI,CAAClB,iBAAL,CAAuB,MAAM;MAChC;MACA,MAAMkL,EAAE,GAAG,KAAKI,OAAL,CAAaL,WAAb,CAAyBrU,OAAzB,CAAX;;MACA,MAAM9B,QAAQ,GAAG,UAAU+W,QAAV,EAAoB;QACjC3K,IAAI,CAACC,UAAL,CAAgB,YAAY;UACxBrK,OAAO,CAAC+U,QAAD,CAAP;QACH,CAFD;MAGH,CAJD;;MAKAX,EAAE,CAACY,EAAH,CAAMjV,SAAN,EAAiB/B,QAAjB;MACA,OAAO,MAAM;QACToW,EAAE,CAACa,GAAH,CAAOlV,SAAP,EAAkB/B,QAAlB,EADS,CAET;;QACA,IAAI,OAAOoW,EAAE,CAAChP,OAAV,KAAsB,UAA1B,EAAsC;UAClCgP,EAAE,CAAChP,OAAH;QACH;MACJ,CAND;IAOH,CAhBM,CAAP;EAiBH;;EACDuP,aAAa,CAAC5U,SAAD,EAAY;IACrB,OAAO,KAAKyU,OAAL,CAAaP,MAAb,CAAoBnK,OAApB,CAA4B/J,SAA5B,IAAyC,CAAC,CAAjD;EACH;;AAtFiD;;AAwFtDwU,oBAAoB,CAACvV,IAArB;EAAA,iBAAiHuV,oBAAjH,EAluD6Fte,EAkuD7F,UAAuJJ,QAAvJ,GAluD6FI,EAkuD7F,UAA4K6d,qBAA5K,GAluD6F7d,EAkuD7F,UAA8MA,EAAE,CAAC+B,QAAjN,GAluD6F/B,EAkuD7F,UAAsO8d,aAAtO;AAAA;;AACAQ,oBAAoB,CAACtV,KAArB,kBAnuD6FhJ,EAmuD7F;EAAA,OAAqHse,oBAArH;EAAA,SAAqHA,oBAArH;AAAA;;AACA;EAAA,mDApuD6Fte,EAouD7F,mBAA2Fse,oBAA3F,EAA6H,CAAC;IAClHrV,IAAI,EAAE3I;EAD4G,CAAD,CAA7H,EAE4B,YAAY;IAChC,OAAO,CAAC;MAAE2I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,EAGW;MAAEqJ,IAAI,EAAE8U,mBAAR;MAA6BxT,UAAU,EAAE,CAAC;QAC5CtB,IAAI,EAAE1I,MADsC;QAE5CiK,IAAI,EAAE,CAACqT,qBAAD;MAFsC,CAAD;IAAzC,CAHX,EAMW;MAAE5U,IAAI,EAAEjJ,EAAE,CAAC+B;IAAX,CANX,EAMkC;MAAEkH,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACzDtB,IAAI,EAAEtH;MADmD,CAAD,EAEzD;QACCsH,IAAI,EAAE1I,MADP;QAECiK,IAAI,EAAE,CAACsT,aAAD;MAFP,CAFyD;IAA/B,CANlC,CAAP;EAYH,CAfL;AAAA;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmB,YAAN,CAAmB;;AAEnBA,YAAY,CAAClW,IAAb;EAAA,iBAAyGkW,YAAzG;AAAA;;AACAA,YAAY,CAAC1I,IAAb,kBAlwD6FvW,EAkwD7F;EAAA,MAA0Gif;AAA1G;AACAA,YAAY,CAACzI,IAAb,kBAnwD6FxW,EAmwD7F;EAAA,WAAmI,CAC3H;IACIgH,OAAO,EAAEkC,qBADb;IAEI8M,QAAQ,EAAEsI,oBAFd;IAGInX,KAAK,EAAE,IAHX;IAIID,IAAI,EAAE,CAACtH,QAAD,EAAWie,qBAAX,EAAkC9b,QAAlC,EAA4C,CAAC,IAAIJ,QAAJ,EAAD,EAAiBmc,aAAjB,CAA5C;EAJV,CAD2H,EAO3H;IAAE9W,OAAO,EAAE6W,qBAAX;IAAkC7H,QAAQ,EAAE+H,mBAA5C;IAAiE7W,IAAI,EAAE;EAAvE,CAP2H;AAAnI;;AASA;EAAA,mDA5wD6FlH,EA4wD7F,mBAA2Fif,YAA3F,EAAqH,CAAC;IAC1GhW,IAAI,EAAEvH,QADoG;IAE1G8I,IAAI,EAAE,CAAC;MACC6K,SAAS,EAAE,CACP;QACIrO,OAAO,EAAEkC,qBADb;QAEI8M,QAAQ,EAAEsI,oBAFd;QAGInX,KAAK,EAAE,IAHX;QAIID,IAAI,EAAE,CAACtH,QAAD,EAAWie,qBAAX,EAAkC9b,QAAlC,EAA4C,CAAC,IAAIJ,QAAJ,EAAD,EAAiBmc,aAAjB,CAA5C;MAJV,CADO,EAOP;QAAE9W,OAAO,EAAE6W,qBAAX;QAAkC7H,QAAQ,EAAE+H,mBAA5C;QAAiE7W,IAAI,EAAE;MAAvE,CAPO;IADZ,CAAD;EAFoG,CAAD,CAArH;AAAA;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMgY,YAAN,CAAmB;;AAEnBA,YAAY,CAACnW,IAAb;EAAA,iBAAyGmW,YAAzG;AAAA;;AACAA,YAAY,CAAClW,KAAb,kBAp0D6FhJ,EAo0D7F;EAAA,OAA6Gkf,YAA7G;EAAA;IAAA;;IAAA;MAAA,cAA6GA,YAA7G;IAAA;MAAA,IAp0D6Flf,EAo0D7F,UAA+Lmf,gBAA/L;IAAA;;IAAA;EAAA;EAAA,YAAuI;AAAvI;;AACA;EAAA,mDAr0D6Fnf,EAq0D7F,mBAA2Fkf,YAA3F,EAAqH,CAAC;IAC1GjW,IAAI,EAAE3I,UADoG;IAE1GkK,IAAI,EAAE,CAAC;MAAEiO,UAAU,EAAE,MAAd;MAAsBxC,WAAW,EAAEjU,UAAU,CAAC,MAAMmd,gBAAP;IAA7C,CAAD;EAFoG,CAAD,CAArH;AAAA;;AAIA,SAASC,uBAAT,CAAiC9Y,QAAjC,EAA2C;EACvC,OAAO,IAAI6Y,gBAAJ,CAAqB7Y,QAAQ,CAACC,GAAT,CAAa3G,QAAb,CAArB,CAAP;AACH;;AACD,MAAMuf,gBAAN,SAA+BD,YAA/B,CAA4C;EACxCrc,WAAW,CAAC6H,IAAD,EAAO;IACd;IACA,KAAKA,IAAL,GAAYA,IAAZ;EACH;;EACD2U,QAAQ,CAACC,GAAD,EAAM/P,KAAN,EAAa;IACjB,IAAIA,KAAK,IAAI,IAAb,EACI,OAAO,IAAP;;IACJ,QAAQ+P,GAAR;MACI,KAAKrd,eAAe,CAACsd,IAArB;QACI,OAAOhQ,KAAP;;MACJ,KAAKtN,eAAe,CAACud,IAArB;QACI,IAAItd,gCAAgC,CAACqN,KAAD,EAAQ;QAAO;QAAf,CAApC,EAA2E;UACvE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOlN,cAAc,CAAC,KAAKqI,IAAN,EAAY8J,MAAM,CAACjF,KAAD,CAAlB,CAAd,CAAyCkQ,QAAzC,EAAP;;MACJ,KAAKxd,eAAe,CAACyd,KAArB;QACI,IAAIxd,gCAAgC,CAACqN,KAAD,EAAQ;QAAQ;QAAhB,CAApC,EAA6E;UACzE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOA,KAAP;;MACJ,KAAKtN,eAAe,CAAC0d,MAArB;QACI,IAAIzd,gCAAgC,CAACqN,KAAD,EAAQ;QAAS;QAAjB,CAApC,EAA+E;UAC3E,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,MAAM,IAAI5H,KAAJ,CAAU,uCAAV,CAAN;;MACJ,KAAK1F,eAAe,CAAC2d,GAArB;QACI,IAAI1d,gCAAgC,CAACqN,KAAD,EAAQ;QAAM;QAAd,CAApC,EAAyE;UACrE,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,OAAOnN,aAAa,CAACoS,MAAM,CAACjF,KAAD,CAAP,CAApB;;MACJ,KAAKtN,eAAe,CAAC4d,YAArB;QACI,IAAI3d,gCAAgC,CAACqN,KAAD,EAAQ;QAAc;QAAtB,CAApC,EAAyF;UACrF,OAAOpN,gBAAgB,CAACoN,KAAD,CAAvB;QACH;;QACD,MAAM,IAAI5H,KAAJ,CAAU,gFAAV,CAAN;;MACJ;QACI,MAAM,IAAIA,KAAJ,CAAW,8BAA6B2X,GAAI,qCAA5C,CAAN;IA7BR;EA+BH;;EACDQ,uBAAuB,CAACvQ,KAAD,EAAQ;IAC3B,OAAOjN,4BAA4B,CAACiN,KAAD,CAAnC;EACH;;EACDwQ,wBAAwB,CAACxQ,KAAD,EAAQ;IAC5B,OAAOhN,6BAA6B,CAACgN,KAAD,CAApC;EACH;;EACDyQ,yBAAyB,CAACzQ,KAAD,EAAQ;IAC7B,OAAO/M,8BAA8B,CAAC+M,KAAD,CAArC;EACH;;EACD0Q,sBAAsB,CAAC1Q,KAAD,EAAQ;IAC1B,OAAO9M,2BAA2B,CAAC8M,KAAD,CAAlC;EACH;;EACD2Q,8BAA8B,CAAC3Q,KAAD,EAAQ;IAClC,OAAO7M,mCAAmC,CAAC6M,KAAD,CAA1C;EACH;;AAtDuC;;AAwD5C4P,gBAAgB,CAACpW,IAAjB;EAAA,iBAA6GoW,gBAA7G,EAp4D6Fnf,EAo4D7F,UAA+IJ,QAA/I;AAAA;;AACAuf,gBAAgB,CAACnW,KAAjB,kBAr4D6FhJ,EAq4D7F;EAAA,OAAiHmf,gBAAjH;EAAA;IAAA;;IAAA;MAAA;IAAA;MAAA,IAAmKC,uBAAnK,CAr4D6Fpf,EAq4D7F,UAA4MI,QAA5M;IAAA;;IAAA;EAAA;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAt4D6FJ,EAs4D7F,mBAA2Fmf,gBAA3F,EAAyH,CAAC;IAC9GlW,IAAI,EAAE3I,UADwG;IAE9GkK,IAAI,EAAE,CAAC;MAAEiO,UAAU,EAAE,MAAd;MAAsBxR,UAAU,EAAEmY,uBAAlC;MAA2DlY,IAAI,EAAE,CAAC9G,QAAD;IAAjE,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAE6I,IAAI,EAAEqB,SAAR;MAAmBC,UAAU,EAAE,CAAC;QACxBtB,IAAI,EAAE1I,MADkB;QAExBiK,IAAI,EAAE,CAAC5K,QAAD;MAFkB,CAAD;IAA/B,CAAD,CAAP;EAIH,CARL;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMugB,OAAO,GAAG,IAAIxd,OAAJ,CAAY,QAAZ,CAAhB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASuT,aAAT,EAAwB4G,0BAAxB,EAAoDC,EAApD,EAAwDmC,YAAxD,EAAsEhW,qBAAtE,EAA6FC,YAA7F,EAA2G0U,qBAA3G,EAAkIC,aAAlI,EAAiJC,mBAAjJ,EAAsKkB,YAAtK,EAAoLtI,IAApL,EAA0LiC,KAA1L,EAAiMgD,aAAjM,EAAgNuE,OAAhN,EAAyNpL,oBAAzN,EAA+OqG,iBAA/O,EAAkQD,gBAAlQ,EAAoRQ,YAApR,EAAkS7F,eAAlS,EAAmTN,+BAAnT,EAAoVxS,iBAAiB,IAAIod,kBAAzW,EAA6XhZ,qBAAqB,IAAIiZ,sBAAtZ,EAA8anO,eAAe,IAAIoO,gBAAjc,EAAmd3S,mBAAmB,IAAI4S,oBAA1e,EAAggBpB,gBAAgB,IAAIqB,iBAAphB,EAAuiBjV,mBAAmB,IAAIkV,oBAA9jB,EAAolBnC,oBAAoB,IAAIoC,qBAA5mB,EAAmoBnL,mCAAmC,IAAIoL,oCAA1qB,EAAgtB/N,eAAe,IAAIgO,gBAAnuB,EAAqvBtU,cAAc,IAAIuU,eAAvwB,EAAwxBlW,gBAAgB,IAAImW,iBAA5yB,EAA+zB3a,aAAa,IAAI4a,cAAh1B,EAAg2B1F,UAAU,IAAI2F,WAA92B,EAA23B/T,aAAa,IAAIgU,cAA54B,EAA45BvL,cAAc,IAAIwL,eAA96B,EAA+7BrU,oBAAoB,IAAIsU,qBAAv9B,EAA8+BnU,iBAAiB,IAAIoU,kBAAngC"}, "metadata": {}, "sourceType": "module"}