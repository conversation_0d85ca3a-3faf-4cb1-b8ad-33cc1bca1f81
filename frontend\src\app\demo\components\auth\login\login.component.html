<div class="surface-0 flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
    <div class="grid justify-content-center p-2 lg:p-0" style="min-width:80%">
        <div class="col-12 mt-5 xl:mt-0 text-center">
            <img src="assets/layout/images/{{layoutService.config.colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.svg" alt="Formation logo" class="mb-5" style="width:81px; height:60px;">
        </div>
        <div class="col-12 xl:col-6" style="border-radius:56px; padding:0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%);">
            <div class="h-full w-full m-0 py-7 px-4" style="border-radius:53px; background: linear-gradient(180deg, var(--surface-50) 38.9%, var(--surface-0));">
                <div class="text-center mb-5">
                    <i class="pi pi-shield text-6xl text-primary mb-3"></i>
                    <div class="text-900 text-3xl font-medium mb-3">Formation Management</div>
                    <span class="text-600 font-medium">Sign in with Keycloak</span>
                </div>

                <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="w-full md:w-10 mx-auto">
                    <!-- Demo Credentials Buttons -->
                    <div class="mb-4">
                        <div class="text-center mb-3">
                            <span class="text-600 text-sm">Quick Login (Demo):</span>
                        </div>
                        <div class="flex gap-2 justify-content-center flex-wrap">
                            <p-button
                                label="Admin"
                                icon="pi pi-user-plus"
                                class="p-button-sm p-button-outlined p-button-danger"
                                (click)="fillDemoCredentials('admin')"
                                type="button">
                            </p-button>
                            <p-button
                                label="Trainer"
                                icon="pi pi-graduation-cap"
                                class="p-button-sm p-button-outlined p-button-info"
                                (click)="fillDemoCredentials('trainer')"
                                type="button">
                            </p-button>
                            <p-button
                                label="Employee"
                                icon="pi pi-users"
                                class="p-button-sm p-button-outlined p-button-success"
                                (click)="fillDemoCredentials('employee')"
                                type="button">
                            </p-button>
                        </div>
                    </div>

                    <p-divider></p-divider>

                    <!-- Username Field -->
                    <div class="field mb-4">
                        <label for="username" class="block text-900 text-xl font-medium mb-2">Username</label>
                        <input
                            id="username"
                            type="text"
                            placeholder="Enter username"
                            pInputText
                            formControlName="username"
                            class="w-full"
                            [class.p-invalid]="isFieldInvalid('username')"
                            style="padding:1rem;">
                        <small
                            class="p-error block mt-1"
                            *ngIf="isFieldInvalid('username')">
                            {{getFieldError('username')}}
                        </small>
                    </div>

                    <!-- Password Field -->
                    <div class="field mb-4">
                        <label for="password" class="block text-900 font-medium text-xl mb-2">Password</label>
                        <p-password
                            id="password"
                            formControlName="password"
                            placeholder="Enter password"
                            [toggleMask]="true"
                            styleClass="w-full"
                            [class.p-invalid]="isFieldInvalid('password')"
                            [feedback]="false">
                        </p-password>
                        <small
                            class="p-error block mt-1"
                            *ngIf="isFieldInvalid('password')">
                            {{getFieldError('password')}}
                        </small>
                    </div>

                    <!-- Submit Button -->
                    <p-button
                        label="Sign In"
                        icon="pi pi-sign-in"
                        class="w-full p-3 text-xl"
                        type="submit"
                        [loading]="loading"
                        [disabled]="loginForm.invalid || loading">
                    </p-button>
                </form>

                <!-- Additional Info -->
                <div class="text-center mt-4">
                    <small class="text-500">
                        Powered by Keycloak Authentication
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<p-toast></p-toast>
