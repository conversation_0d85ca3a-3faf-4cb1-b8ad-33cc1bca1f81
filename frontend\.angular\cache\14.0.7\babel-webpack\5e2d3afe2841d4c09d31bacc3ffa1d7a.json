{"ast": null, "code": "import { combineLatest } from '../observable/combineLatest';\nimport { joinAllInternals } from './joinAllInternals';\nexport function combineLatestAll(project) {\n  return joinAllInternals(combineLatest, project);\n}", "map": {"version": 3, "names": ["combineLatest", "joinAllInternals", "combineLatestAll", "project"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/combineLatestAll.js"], "sourcesContent": ["import { combineLatest } from '../observable/combineLatest';\nimport { joinAllInternals } from './joinAllInternals';\nexport function combineLatestAll(project) {\n    return joinAllInternals(combineLatest, project);\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,6BAA9B;AACA,SAASC,gBAAT,QAAiC,oBAAjC;AACA,OAAO,SAASC,gBAAT,CAA0BC,OAA1B,EAAmC;EACtC,OAAOF,gBAAgB,CAACD,aAAD,EAAgBG,OAAhB,CAAvB;AACH"}, "metadata": {}, "sourceType": "module"}