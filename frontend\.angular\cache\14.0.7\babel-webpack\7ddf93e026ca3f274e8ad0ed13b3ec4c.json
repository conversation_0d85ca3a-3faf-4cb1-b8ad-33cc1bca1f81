{"ast": null, "code": "import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race(...sources) {\n  sources = argsOrArgArray(sources);\n  return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n  return subscriber => {\n    let subscriptions = [];\n\n    for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n      subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        if (subscriptions) {\n          for (let s = 0; s < subscriptions.length; s++) {\n            s !== i && subscriptions[s].unsubscribe();\n          }\n\n          subscriptions = null;\n        }\n\n        subscriber.next(value);\n      })));\n    }\n  };\n}", "map": {"version": 3, "names": ["Observable", "innerFrom", "argsOrArgArray", "createOperatorSubscriber", "race", "sources", "length", "raceInit", "subscriber", "subscriptions", "i", "closed", "push", "subscribe", "value", "s", "unsubscribe", "next"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/race.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from './innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from '../operators/OperatorSubscriber';\nexport function race(...sources) {\n    sources = argsOrArgArray(sources);\n    return sources.length === 1 ? innerFrom(sources[0]) : new Observable(raceInit(sources));\n}\nexport function raceInit(sources) {\n    return (subscriber) => {\n        let subscriptions = [];\n        for (let i = 0; subscriptions && !subscriber.closed && i < sources.length; i++) {\n            subscriptions.push(innerFrom(sources[i]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                if (subscriptions) {\n                    for (let s = 0; s < subscriptions.length; s++) {\n                        s !== i && subscriptions[s].unsubscribe();\n                    }\n                    subscriptions = null;\n                }\n                subscriber.next(value);\n            })));\n        }\n    };\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,eAA3B;AACA,SAASC,SAAT,QAA0B,aAA1B;AACA,SAASC,cAAT,QAA+B,wBAA/B;AACA,SAASC,wBAAT,QAAyC,iCAAzC;AACA,OAAO,SAASC,IAAT,CAAc,GAAGC,OAAjB,EAA0B;EAC7BA,OAAO,GAAGH,cAAc,CAACG,OAAD,CAAxB;EACA,OAAOA,OAAO,CAACC,MAAR,KAAmB,CAAnB,GAAuBL,SAAS,CAACI,OAAO,CAAC,CAAD,CAAR,CAAhC,GAA+C,IAAIL,UAAJ,CAAeO,QAAQ,CAACF,OAAD,CAAvB,CAAtD;AACH;AACD,OAAO,SAASE,QAAT,CAAkBF,OAAlB,EAA2B;EAC9B,OAAQG,UAAD,IAAgB;IACnB,IAAIC,aAAa,GAAG,EAApB;;IACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBD,aAAa,IAAI,CAACD,UAAU,CAACG,MAA7B,IAAuCD,CAAC,GAAGL,OAAO,CAACC,MAAnE,EAA2EI,CAAC,EAA5E,EAAgF;MAC5ED,aAAa,CAACG,IAAd,CAAmBX,SAAS,CAACI,OAAO,CAACK,CAAD,CAAR,CAAT,CAAsBG,SAAtB,CAAgCV,wBAAwB,CAACK,UAAD,EAAcM,KAAD,IAAW;QAC/F,IAAIL,aAAJ,EAAmB;UACf,KAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,aAAa,CAACH,MAAlC,EAA0CS,CAAC,EAA3C,EAA+C;YAC3CA,CAAC,KAAKL,CAAN,IAAWD,aAAa,CAACM,CAAD,CAAb,CAAiBC,WAAjB,EAAX;UACH;;UACDP,aAAa,GAAG,IAAhB;QACH;;QACDD,UAAU,CAACS,IAAX,CAAgBH,KAAhB;MACH,CAR0E,CAAxD,CAAnB;IASH;EACJ,CAbD;AAcH"}, "metadata": {}, "sourceType": "module"}