{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"pi-minus\": a0,\n    \"pi-plus\": a1\n  };\n};\n\nfunction Fieldset_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c0, !ctx_r4.collapsed, ctx_r4.collapsed));\n  }\n}\n\nfunction Fieldset_ng_container_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Fieldset_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function Fieldset_ng_container_2_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.toggle($event));\n    })(\"keydown.enter\", function Fieldset_ng_container_2_Template_a_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.toggle($event));\n    });\n    i0.ɵɵtemplate(2, Fieldset_ng_container_2_span_2_Template, 1, 4, \"span\", 8);\n    i0.ɵɵtemplate(3, Fieldset_ng_container_2_ng_container_3_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n\n    const _r1 = i0.ɵɵreference(4);\n\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"aria-controls\", ctx_r0.id + \"-content\")(\"aria-expanded\", !ctx_r0.collapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.toggleable);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", _r1);\n  }\n}\n\nfunction Fieldset_ng_template_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Fieldset_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 10);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵprojection(2, 1);\n    i0.ɵɵtemplate(3, Fieldset_ng_template_3_ng_container_3_Template, 1, 0, \"ng-container\", 6);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.legend);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.headerTemplate);\n  }\n}\n\nfunction Fieldset_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c1 = [\"*\", [[\"p-header\"]]];\n\nconst _c2 = function (a1, a2) {\n  return {\n    \"p-fieldset p-component\": true,\n    \"p-fieldset-toggleable\": a1,\n    \"p-fieldset-expanded\": a2\n  };\n};\n\nconst _c3 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"0\"\n  };\n};\n\nconst _c4 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\n\nconst _c5 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"*\"\n  };\n};\n\nconst _c6 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nconst _c7 = [\"*\", \"p-header\"];\nlet idx = 0;\n\nclass Fieldset {\n  constructor(el) {\n    this.el = el;\n    this.collapsed = false;\n    this.collapsedChange = new EventEmitter();\n    this.onBeforeToggle = new EventEmitter();\n    this.onAfterToggle = new EventEmitter();\n    this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    this.id = `p-fieldset-${idx++}`;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerTemplate = item.template;\n          break;\n\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  toggle(event) {\n    if (this.animating) {\n      return false;\n    }\n\n    this.animating = true;\n    this.onBeforeToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n    if (this.collapsed) this.expand(event);else this.collapse(event);\n    this.onAfterToggle.emit({\n      originalEvent: event,\n      collapsed: this.collapsed\n    });\n    event.preventDefault();\n  }\n\n  expand(event) {\n    this.collapsed = false;\n    this.collapsedChange.emit(this.collapsed);\n  }\n\n  collapse(event) {\n    this.collapsed = true;\n    this.collapsedChange.emit(this.collapsed);\n  }\n\n  getBlockableElement() {\n    return this.el.nativeElement.children[0];\n  }\n\n  onToggleDone() {\n    this.animating = false;\n  }\n\n}\n\nFieldset.ɵfac = function Fieldset_Factory(t) {\n  return new (t || Fieldset)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nFieldset.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Fieldset,\n  selectors: [[\"p-fieldset\"]],\n  contentQueries: function Fieldset_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    legend: \"legend\",\n    toggleable: \"toggleable\",\n    collapsed: \"collapsed\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    transitionOptions: \"transitionOptions\"\n  },\n  outputs: {\n    collapsedChange: \"collapsedChange\",\n    onBeforeToggle: \"onBeforeToggle\",\n    onAfterToggle: \"onAfterToggle\"\n  },\n  ngContentSelectors: _c7,\n  decls: 9,\n  vars: 23,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [1, \"p-fieldset-legend\"], [4, \"ngIf\", \"ngIfElse\"], [\"legendContent\", \"\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-fieldset-content\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"pRipple\", \"\", 3, \"click\", \"keydown.enter\"], [\"class\", \"p-fieldset-toggler pi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-fieldset-toggler\", \"pi\", 3, \"ngClass\"], [1, \"p-fieldset-legend-text\"]],\n  template: function Fieldset_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef(_c1);\n      i0.ɵɵelementStart(0, \"fieldset\", 0)(1, \"legend\", 1);\n      i0.ɵɵtemplate(2, Fieldset_ng_container_2_Template, 4, 4, \"ng-container\", 2);\n      i0.ɵɵtemplate(3, Fieldset_ng_template_3_Template, 4, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(5, \"div\", 4);\n      i0.ɵɵlistener(\"@fieldsetContent.done\", function Fieldset_Template_div_animation_fieldsetContent_done_5_listener() {\n        return ctx.onToggleDone();\n      });\n      i0.ɵɵelementStart(6, \"div\", 5);\n      i0.ɵɵprojection(7);\n      i0.ɵɵtemplate(8, Fieldset_ng_container_8_Template, 1, 0, \"ng-container\", 6);\n      i0.ɵɵelementEnd()()();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(4);\n\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(12, _c2, ctx.toggleable, !ctx.collapsed && ctx.toggleable))(\"ngStyle\", ctx.style);\n      i0.ɵɵattribute(\"id\", ctx.id);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.toggleable)(\"ngIfElse\", _r1);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"@fieldsetContent\", ctx.collapsed ? i0.ɵɵpureFunction1(17, _c4, i0.ɵɵpureFunction1(15, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(21, _c6, i0.ɵɵpureFunction1(19, _c5, ctx.animating ? ctx.transitionOptions : \"0ms\")));\n      i0.ɵɵattribute(\"id\", ctx.id + \"-content\")(\"aria-labelledby\", ctx.id)(\"aria-hidden\", ctx.collapsed);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.Ripple],\n  styles: [\".p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded .p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('fieldsetContent', [state('hidden', style({\n      height: '0'\n    })), state('visible', style({\n      height: '*'\n    })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Fieldset, [{\n    type: Component,\n    args: [{\n      selector: 'p-fieldset',\n      template: `\n        <fieldset [attr.id]=\"id\" [ngClass]=\"{'p-fieldset p-component': true, 'p-fieldset-toggleable': toggleable, 'p-fieldset-expanded': !collapsed && toggleable}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <legend class=\"p-fieldset-legend\">\n                <ng-container *ngIf=\"toggleable; else legendContent\">\n                    <a tabindex=\"0\" (click)=\"toggle($event)\" (keydown.enter)=\"toggle($event)\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"!collapsed\" pRipple>\n                        <span class=\"p-fieldset-toggler pi\" *ngIf=\"toggleable\" [ngClass]=\"{'pi-minus': !collapsed,'pi-plus':collapsed}\"></span>\n                        <ng-container *ngTemplateOutlet=\"legendContent\"></ng-container>\n                    </a>\n                </ng-container>\n                <ng-template #legendContent>\n                    <span class=\"p-fieldset-legend-text\">{{legend}}</span>\n                    <ng-content select=\"p-header\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </ng-template>\n            </legend>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@fieldsetContent]=\"collapsed ? {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}} : {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}}\"\n                        [attr.aria-labelledby]=\"id\" [attr.aria-hidden]=\"collapsed\"\n                         (@fieldsetContent.done)=\"onToggleDone()\" role=\"region\">\n                <div class=\"p-fieldset-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n        </fieldset>\n    `,\n      animations: [trigger('fieldsetContent', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded .p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    legend: [{\n      type: Input\n    }],\n    toggleable: [{\n      type: Input\n    }],\n    collapsed: [{\n      type: Input\n    }],\n    collapsedChange: [{\n      type: Output\n    }],\n    onBeforeToggle: [{\n      type: Output\n    }],\n    onAfterToggle: [{\n      type: Output\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass FieldsetModule {}\n\nFieldsetModule.ɵfac = function FieldsetModule_Factory(t) {\n  return new (t || FieldsetModule)();\n};\n\nFieldsetModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FieldsetModule\n});\nFieldsetModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RippleModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FieldsetModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RippleModule],\n      exports: [Fieldset, SharedModule],\n      declarations: [Fieldset]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Fieldset, FieldsetModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ContentChildren", "NgModule", "trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "PrimeTemplate", "SharedModule", "i2", "RippleModule", "idx", "<PERSON><PERSON>", "constructor", "el", "collapsed", "collapsedChange", "onBeforeToggle", "onAfterToggle", "transitionOptions", "id", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "headerTemplate", "template", "contentTemplate", "toggle", "event", "animating", "emit", "originalEvent", "expand", "collapse", "preventDefault", "getBlockableElement", "nativeElement", "children", "onToggleDone", "ɵfac", "ElementRef", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "<PERSON><PERSON><PERSON>", "height", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "legend", "toggleable", "styleClass", "FieldsetModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-fieldset.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i2 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\n\nlet idx = 0;\nclass Fieldset {\n    constructor(el) {\n        this.el = el;\n        this.collapsed = false;\n        this.collapsedChange = new EventEmitter();\n        this.onBeforeToggle = new EventEmitter();\n        this.onAfterToggle = new EventEmitter();\n        this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n        this.id = `p-fieldset-${idx++}`;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.animating) {\n            return false;\n        }\n        this.animating = true;\n        this.onBeforeToggle.emit({ originalEvent: event, collapsed: this.collapsed });\n        if (this.collapsed)\n            this.expand(event);\n        else\n            this.collapse(event);\n        this.onAfterToggle.emit({ originalEvent: event, collapsed: this.collapsed });\n        event.preventDefault();\n    }\n    expand(event) {\n        this.collapsed = false;\n        this.collapsedChange.emit(this.collapsed);\n    }\n    collapse(event) {\n        this.collapsed = true;\n        this.collapsedChange.emit(this.collapsed);\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    onToggleDone() {\n        this.animating = false;\n    }\n}\nFieldset.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Fieldset, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nFieldset.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Fieldset, selector: \"p-fieldset\", inputs: { legend: \"legend\", toggleable: \"toggleable\", collapsed: \"collapsed\", style: \"style\", styleClass: \"styleClass\", transitionOptions: \"transitionOptions\" }, outputs: { collapsedChange: \"collapsedChange\", onBeforeToggle: \"onBeforeToggle\", onAfterToggle: \"onAfterToggle\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <fieldset [attr.id]=\"id\" [ngClass]=\"{'p-fieldset p-component': true, 'p-fieldset-toggleable': toggleable, 'p-fieldset-expanded': !collapsed && toggleable}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <legend class=\"p-fieldset-legend\">\n                <ng-container *ngIf=\"toggleable; else legendContent\">\n                    <a tabindex=\"0\" (click)=\"toggle($event)\" (keydown.enter)=\"toggle($event)\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"!collapsed\" pRipple>\n                        <span class=\"p-fieldset-toggler pi\" *ngIf=\"toggleable\" [ngClass]=\"{'pi-minus': !collapsed,'pi-plus':collapsed}\"></span>\n                        <ng-container *ngTemplateOutlet=\"legendContent\"></ng-container>\n                    </a>\n                </ng-container>\n                <ng-template #legendContent>\n                    <span class=\"p-fieldset-legend-text\">{{legend}}</span>\n                    <ng-content select=\"p-header\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </ng-template>\n            </legend>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@fieldsetContent]=\"collapsed ? {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}} : {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}}\"\n                        [attr.aria-labelledby]=\"id\" [attr.aria-hidden]=\"collapsed\"\n                         (@fieldsetContent.done)=\"onToggleDone()\" role=\"region\">\n                <div class=\"p-fieldset-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n        </fieldset>\n    `, isInline: true, styles: [\".p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded .p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.Ripple, selector: \"[pRipple]\" }], animations: [\n        trigger('fieldsetContent', [\n            state('hidden', style({\n                height: '0'\n            })),\n            state('visible', style({\n                height: '*'\n            })),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Fieldset, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-fieldset', template: `\n        <fieldset [attr.id]=\"id\" [ngClass]=\"{'p-fieldset p-component': true, 'p-fieldset-toggleable': toggleable, 'p-fieldset-expanded': !collapsed && toggleable}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <legend class=\"p-fieldset-legend\">\n                <ng-container *ngIf=\"toggleable; else legendContent\">\n                    <a tabindex=\"0\" (click)=\"toggle($event)\" (keydown.enter)=\"toggle($event)\" [attr.aria-controls]=\"id + '-content'\" [attr.aria-expanded]=\"!collapsed\" pRipple>\n                        <span class=\"p-fieldset-toggler pi\" *ngIf=\"toggleable\" [ngClass]=\"{'pi-minus': !collapsed,'pi-plus':collapsed}\"></span>\n                        <ng-container *ngTemplateOutlet=\"legendContent\"></ng-container>\n                    </a>\n                </ng-container>\n                <ng-template #legendContent>\n                    <span class=\"p-fieldset-legend-text\">{{legend}}</span>\n                    <ng-content select=\"p-header\"></ng-content>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                </ng-template>\n            </legend>\n            <div [attr.id]=\"id + '-content'\" class=\"p-toggleable-content\" [@fieldsetContent]=\"collapsed ? {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}} : {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}}\"\n                        [attr.aria-labelledby]=\"id\" [attr.aria-hidden]=\"collapsed\"\n                         (@fieldsetContent.done)=\"onToggleDone()\" role=\"region\">\n                <div class=\"p-fieldset-content\">\n                    <ng-content></ng-content>\n                    <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                </div>\n            </div>\n        </fieldset>\n    `, animations: [\n                        trigger('fieldsetContent', [\n                            state('hidden', style({\n                                height: '0'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-fieldset-legend>a,.p-fieldset-legend>span{display:flex;align-items:center;justify-content:center}.p-fieldset-toggleable .p-fieldset-legend a{cursor:pointer;-webkit-user-select:none;user-select:none;overflow:hidden;position:relative}.p-fieldset-legend-text{line-height:1}.p-fieldset-toggleable.p-fieldset-expanded .p-toggleable-content:not(.ng-animating){overflow:visible}.p-fieldset-toggleable .p-toggleable-content{overflow:hidden}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { legend: [{\n                type: Input\n            }], toggleable: [{\n                type: Input\n            }], collapsed: [{\n                type: Input\n            }], collapsedChange: [{\n                type: Output\n            }], onBeforeToggle: [{\n                type: Output\n            }], onAfterToggle: [{\n                type: Output\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass FieldsetModule {\n}\nFieldsetModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FieldsetModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nFieldsetModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: FieldsetModule, declarations: [Fieldset], imports: [CommonModule, RippleModule], exports: [Fieldset, SharedModule] });\nFieldsetModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FieldsetModule, imports: [CommonModule, RippleModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FieldsetModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RippleModule],\n                    exports: [Fieldset, SharedModule],\n                    declarations: [Fieldset]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Fieldset, FieldsetModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,eAA7F,EAA8GC,QAA9G,QAA8H,eAA9H;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,aAA5C;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;;;;;;;;;;;IAqD2FnB,EAMnE,wB;;;;mBANmEA,E;IAAAA,EAMZ,uBANYA,EAMZ,8D;;;;;;IANYA,EAOnE,sB;;;;;;gBAPmEA,E;;IAAAA,EAI3E,2B;IAJ2EA,EAKvE,0B;IALuEA,EAKvD;MALuDA,EAKvD;MAAA,eALuDA,EAKvD;MAAA,OALuDA,EAK9C,mCAAT;IAAA;MALuDA,EAKvD;MAAA,eALuDA,EAKvD;MAAA,OALuDA,EAKb,mCAA1C;IAAA,E;IALuDA,EAMnE,wE;IANmEA,EAOnE,wF;IAPmEA,EAQvE,e;IARuEA,EAS3E,wB;;;;mBAT2EA,E;;gBAAAA,E;;IAAAA,EAKG,a;IALHA,EAKG,yF;IALHA,EAM9B,a;IAN8BA,EAM9B,sC;IAN8BA,EAOpD,a;IAPoDA,EAOpD,oC;;;;;;IAPoDA,EAavE,sB;;;;;;IAbuEA,EAWvE,8B;IAXuEA,EAWlC,U;IAXkCA,EAWxB,e;IAXwBA,EAYvE,mB;IAZuEA,EAavE,uF;;;;mBAbuEA,E;IAAAA,EAWlC,a;IAXkCA,EAWlC,iC;IAXkCA,EAaxD,a;IAbwDA,EAaxD,sD;;;;;;IAbwDA,EAqBvE,sB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxEpB,IAAIoB,GAAG,GAAG,CAAV;;AACA,MAAMC,QAAN,CAAe;EACXC,WAAW,CAACC,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,eAAL,GAAuB,IAAIxB,YAAJ,EAAvB;IACA,KAAKyB,cAAL,GAAsB,IAAIzB,YAAJ,EAAtB;IACA,KAAK0B,aAAL,GAAqB,IAAI1B,YAAJ,EAArB;IACA,KAAK2B,iBAAL,GAAyB,sCAAzB;IACA,KAAKC,EAAL,GAAW,cAAaT,GAAG,EAAG,EAA9B;EACH;;EACDU,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,QAAL;UACI,KAAKC,cAAL,GAAsBF,IAAI,CAACG,QAA3B;UACA;;QACJ,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBJ,IAAI,CAACG,QAA5B;UACA;MANR;IAQH,CATD;EAUH;;EACDE,MAAM,CAACC,KAAD,EAAQ;IACV,IAAI,KAAKC,SAAT,EAAoB;MAChB,OAAO,KAAP;IACH;;IACD,KAAKA,SAAL,GAAiB,IAAjB;IACA,KAAKd,cAAL,CAAoBe,IAApB,CAAyB;MAAEC,aAAa,EAAEH,KAAjB;MAAwBf,SAAS,EAAE,KAAKA;IAAxC,CAAzB;IACA,IAAI,KAAKA,SAAT,EACI,KAAKmB,MAAL,CAAYJ,KAAZ,EADJ,KAGI,KAAKK,QAAL,CAAcL,KAAd;IACJ,KAAKZ,aAAL,CAAmBc,IAAnB,CAAwB;MAAEC,aAAa,EAAEH,KAAjB;MAAwBf,SAAS,EAAE,KAAKA;IAAxC,CAAxB;IACAe,KAAK,CAACM,cAAN;EACH;;EACDF,MAAM,CAACJ,KAAD,EAAQ;IACV,KAAKf,SAAL,GAAiB,KAAjB;IACA,KAAKC,eAAL,CAAqBgB,IAArB,CAA0B,KAAKjB,SAA/B;EACH;;EACDoB,QAAQ,CAACL,KAAD,EAAQ;IACZ,KAAKf,SAAL,GAAiB,IAAjB;IACA,KAAKC,eAAL,CAAqBgB,IAArB,CAA0B,KAAKjB,SAA/B;EACH;;EACDsB,mBAAmB,GAAG;IAClB,OAAO,KAAKvB,EAAL,CAAQwB,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,CAAP;EACH;;EACDC,YAAY,GAAG;IACX,KAAKT,SAAL,GAAiB,KAAjB;EACH;;AAhDU;;AAkDfnB,QAAQ,CAAC6B,IAAT;EAAA,iBAAqG7B,QAArG,EAA2FrB,EAA3F,mBAA+HA,EAAE,CAACmD,UAAlI;AAAA;;AACA9B,QAAQ,CAAC+B,IAAT,kBAD2FpD,EAC3F;EAAA,MAAyFqB,QAAzF;EAAA;EAAA;IAAA;MAD2FrB,EAC3F,0BAAyegB,aAAze;IAAA;;IAAA;MAAA;;MAD2FhB,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;MAD2FA,EAEnF,iDADR;MAD2FA,EAI3E,yEAHhB;MAD2FA,EAU3E,8EAV2EA,EAU3E,wBAThB;MAD2FA,EAe/E,eAdZ;MAD2FA,EAgB/E,4BAfZ;MAD2FA,EAkBlE;QAAA,OAAyB,kBAAzB;MAAA,EAjBzB;MAD2FA,EAmB3E,4BAlBhB;MAD2FA,EAoBvE,gBAnBpB;MAD2FA,EAqBvE,yEApBpB;MAD2FA,EAsB3E,mBArBhB;IAAA;;IAAA;MAAA,YAD2FA,EAC3F;;MAD2FA,EAE2F,2BADtL;MAD2FA,EAE1D,uBAF0DA,EAE1D,kGADjC;MAD2FA,EAEzE,0BADlB;MAD2FA,EAI5D,aAH/B;MAD2FA,EAI5D,oDAH/B;MAD2FA,EAgBjB,aAf1E;MAD2FA,EAgBjB,gDAhBiBA,EAgBjB,0BAhBiBA,EAgBjB,oDAhBiBA,EAgBjB,0BAhBiBA,EAgBjB,0EAf1E;MAD2FA,EAgB1E,gGAfjB;MAD2FA,EAqBxD,aApBnC;MAD2FA,EAqBxD,oDApBnC;IAAA;EAAA;EAAA,eAwBogBc,EAAE,CAACuC,OAxBvgB,EAwBkmBvC,EAAE,CAACwC,IAxBrmB,EAwBssBxC,EAAE,CAACyC,gBAxBzsB,EAwB62BzC,EAAE,CAAC0C,OAxBh3B,EAwBk8BtC,EAAE,CAACuC,MAxBr8B;EAAA;EAAA;EAAA;IAAA,WAwBm/B,CAC3+BhD,OAAO,CAAC,iBAAD,EAAoB,CACvBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;MAClB+C,MAAM,EAAE;IADU,CAAD,CAAhB,CADkB,EAIvBhD,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;MACnB+C,MAAM,EAAE;IADW,CAAD,CAAjB,CAJkB,EAOvB9C,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPa,EAQvBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARa,CAApB,CADo+B;EAxBn/B;EAAA;AAAA;;AAoCA;EAAA,mDArC2Fb,EAqC3F,mBAA2FqB,QAA3F,EAAiH,CAAC;IACtGsC,IAAI,EAAEzD,SADgG;IAEtG0D,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BzB,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAxBmB;MAwBZ0B,UAAU,EAAE,CACKrD,OAAO,CAAC,iBAAD,EAAoB,CACvBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;QAClB+C,MAAM,EAAE;MADU,CAAD,CAAhB,CADkB,EAIvBhD,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;QACnB+C,MAAM,EAAE;MADW,CAAD,CAAjB,CAJkB,EAOvB9C,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPa,EAQvBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARa,CAApB,CADZ,CAxBA;MAmCIkD,eAAe,EAAE5D,uBAAuB,CAAC6D,MAnC7C;MAmCqDC,aAAa,EAAE7D,iBAAiB,CAAC8D,IAnCtF;MAmC4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CAnClG;MAqCIC,MAAM,EAAE,CAAC,ubAAD;IArCZ,CAAD;EAFgG,CAAD,CAAjH,EAwC4B,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAE3D,EAAE,CAACmD;IAAX,CAAD,CAAP;EAAmC,CAxC7E,EAwC+F;IAAEkB,MAAM,EAAE,CAAC;MAC1FV,IAAI,EAAEtD;IADoF,CAAD,CAAV;IAE/EiE,UAAU,EAAE,CAAC;MACbX,IAAI,EAAEtD;IADO,CAAD,CAFmE;IAI/EmB,SAAS,EAAE,CAAC;MACZmC,IAAI,EAAEtD;IADM,CAAD,CAJoE;IAM/EoB,eAAe,EAAE,CAAC;MAClBkC,IAAI,EAAErD;IADY,CAAD,CAN8D;IAQ/EoB,cAAc,EAAE,CAAC;MACjBiC,IAAI,EAAErD;IADW,CAAD,CAR+D;IAU/EqB,aAAa,EAAE,CAAC;MAChBgC,IAAI,EAAErD;IADU,CAAD,CAVgE;IAY/EK,KAAK,EAAE,CAAC;MACRgD,IAAI,EAAEtD;IADE,CAAD,CAZwE;IAc/EkE,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEtD;IADO,CAAD,CAdmE;IAgB/EuB,iBAAiB,EAAE,CAAC;MACpB+B,IAAI,EAAEtD;IADc,CAAD,CAhB4D;IAkB/E0B,SAAS,EAAE,CAAC;MACZ4B,IAAI,EAAEpD,eADM;MAEZqD,IAAI,EAAE,CAAC5C,aAAD;IAFM,CAAD;EAlBoE,CAxC/F;AAAA;;AA8DA,MAAMwD,cAAN,CAAqB;;AAErBA,cAAc,CAACtB,IAAf;EAAA,iBAA2GsB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAtG2FzE,EAsG3F;EAAA,MAA4GwE;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAvG2F1E,EAuG3F;EAAA,UAAsIe,YAAtI,EAAoJI,YAApJ,EAAkKF,YAAlK;AAAA;;AACA;EAAA,mDAxG2FjB,EAwG3F,mBAA2FwE,cAA3F,EAAuH,CAAC;IAC5Gb,IAAI,EAAEnD,QADsG;IAE5GoD,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC5D,YAAD,EAAeI,YAAf,CADV;MAECyD,OAAO,EAAE,CAACvD,QAAD,EAAWJ,YAAX,CAFV;MAGC4D,YAAY,EAAE,CAACxD,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmBmD,cAAnB"}, "metadata": {}, "sourceType": "module"}