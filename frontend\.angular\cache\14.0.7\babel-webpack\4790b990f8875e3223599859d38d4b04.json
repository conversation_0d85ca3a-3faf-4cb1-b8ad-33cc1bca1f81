{"ast": null, "code": "import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function onErrorResumeNext(...sources) {\n  const nextSources = argsOrArgArray(sources);\n  return operate((source, subscriber) => {\n    const remaining = [source, ...nextSources];\n\n    const subscribeNext = () => {\n      if (!subscriber.closed) {\n        if (remaining.length > 0) {\n          let nextSource;\n\n          try {\n            nextSource = innerFrom(remaining.shift());\n          } catch (err) {\n            subscribeNext();\n            return;\n          }\n\n          const innerSub = createOperatorSubscriber(subscriber, undefined, noop, noop);\n          nextSource.subscribe(innerSub);\n          innerSub.add(subscribeNext);\n        } else {\n          subscriber.complete();\n        }\n      }\n    };\n\n    subscribeNext();\n  });\n}", "map": {"version": 3, "names": ["operate", "innerFrom", "argsOrArgArray", "createOperatorSubscriber", "noop", "onErrorResumeNext", "sources", "nextSources", "source", "subscriber", "remaining", "subscribeNext", "closed", "length", "nextSource", "shift", "err", "innerSub", "undefined", "subscribe", "add", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/onErrorResumeNext.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function onErrorResumeNext(...sources) {\n    const nextSources = argsOrArgArray(sources);\n    return operate((source, subscriber) => {\n        const remaining = [source, ...nextSources];\n        const subscribeNext = () => {\n            if (!subscriber.closed) {\n                if (remaining.length > 0) {\n                    let nextSource;\n                    try {\n                        nextSource = innerFrom(remaining.shift());\n                    }\n                    catch (err) {\n                        subscribeNext();\n                        return;\n                    }\n                    const innerSub = createOperatorSubscriber(subscriber, undefined, noop, noop);\n                    nextSource.subscribe(innerSub);\n                    innerSub.add(subscribeNext);\n                }\n                else {\n                    subscriber.complete();\n                }\n            }\n        };\n        subscribeNext();\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,SAAT,QAA0B,yBAA1B;AACA,SAASC,cAAT,QAA+B,wBAA/B;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,SAASC,iBAAT,CAA2B,GAAGC,OAA9B,EAAuC;EAC1C,MAAMC,WAAW,GAAGL,cAAc,CAACI,OAAD,CAAlC;EACA,OAAON,OAAO,CAAC,CAACQ,MAAD,EAASC,UAAT,KAAwB;IACnC,MAAMC,SAAS,GAAG,CAACF,MAAD,EAAS,GAAGD,WAAZ,CAAlB;;IACA,MAAMI,aAAa,GAAG,MAAM;MACxB,IAAI,CAACF,UAAU,CAACG,MAAhB,EAAwB;QACpB,IAAIF,SAAS,CAACG,MAAV,GAAmB,CAAvB,EAA0B;UACtB,IAAIC,UAAJ;;UACA,IAAI;YACAA,UAAU,GAAGb,SAAS,CAACS,SAAS,CAACK,KAAV,EAAD,CAAtB;UACH,CAFD,CAGA,OAAOC,GAAP,EAAY;YACRL,aAAa;YACb;UACH;;UACD,MAAMM,QAAQ,GAAGd,wBAAwB,CAACM,UAAD,EAAaS,SAAb,EAAwBd,IAAxB,EAA8BA,IAA9B,CAAzC;UACAU,UAAU,CAACK,SAAX,CAAqBF,QAArB;UACAA,QAAQ,CAACG,GAAT,CAAaT,aAAb;QACH,CAZD,MAaK;UACDF,UAAU,CAACY,QAAX;QACH;MACJ;IACJ,CAnBD;;IAoBAV,aAAa;EAChB,CAvBa,CAAd;AAwBH"}, "metadata": {}, "sourceType": "module"}