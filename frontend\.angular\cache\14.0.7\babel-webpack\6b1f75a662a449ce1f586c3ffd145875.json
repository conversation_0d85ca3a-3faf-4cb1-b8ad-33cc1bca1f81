{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class KeycloakAuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.apiUrl = 'http://localhost:8080/api';\n    this.accessTokenKey = 'keycloak_access_token';\n    this.refreshTokenKey = 'keycloak_refresh_token';\n    this.userKey = 'keycloak_user';\n    this.currentUserSubject = new BehaviorSubject(this.getUserFromStorage());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(this.hasValidToken());\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.keycloakConfig = null;\n    /**\r\n     * Handle HTTP errors\r\n     */\n\n    this.handleError = error => {\n      console.error('Keycloak Auth Service Error:', error);\n\n      if (error.status === 401) {\n        this.clearSession();\n      }\n\n      return throwError(error);\n    };\n\n    this.loadKeycloakConfig();\n  }\n  /**\r\n   * Load Keycloak configuration from backend\r\n   */\n\n\n  loadKeycloakConfig() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const response = yield _this.http.get(`${_this.apiUrl}/auth/config`).toPromise();\n\n        if (response.success) {\n          _this.keycloakConfig = response.data;\n        }\n      } catch (error) {\n        console.error('Failed to load Keycloak config:', error);\n      }\n    })();\n  }\n  /**\r\n   * Login with username and password\r\n   */\n\n\n  login(credentials) {\n    return this.http.post(`${this.apiUrl}/auth/login`, credentials).pipe(tap(response => {\n      if (response.success) {\n        this.setSession(response.data);\n      }\n    }), catchError(this.handleError));\n  }\n  /**\r\n   * Refresh access token\r\n   */\n\n\n  refreshToken() {\n    const refreshToken = this.getRefreshToken();\n\n    if (!refreshToken) {\n      return throwError('No refresh token available');\n    }\n\n    return this.http.post(`${this.apiUrl}/auth/refresh`, {\n      refresh_token: refreshToken\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.setTokens(response.data.access_token, response.data.refresh_token);\n      }\n    }), catchError(error => {\n      this.clearSession();\n      return throwError(error);\n    }));\n  }\n  /**\r\n   * Logout user\r\n   */\n\n\n  logout() {\n    const refreshToken = this.getRefreshToken();\n    const logoutRequest = refreshToken ? this.http.post(`${this.apiUrl}/auth/logout`, {\n      refresh_token: refreshToken\n    }) : new Observable(observer => observer.next({}));\n    return logoutRequest.pipe(tap(() => this.clearSession()), catchError(() => {\n      this.clearSession();\n      return throwError('Logout failed');\n    }));\n  }\n  /**\r\n   * Get current user info from server\r\n   */\n\n\n  getCurrentUser() {\n    return this.http.get(`${this.apiUrl}/auth/me`, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.updateUser(response.data.user);\n      }\n    }), catchError(this.handleError));\n  }\n  /**\r\n   * Validate current token\r\n   */\n\n\n  validateToken() {\n    return this.http.post(`${this.apiUrl}/auth/validate`, {}, {\n      headers: this.getAuthHeaders()\n    }).pipe(tap(response => {\n      if (response.success) {\n        this.updateUser(response.data.user);\n      }\n    }), catchError(error => {\n      this.clearSession();\n      return throwError(error);\n    }));\n  }\n  /**\r\n   * Check if user is authenticated\r\n   */\n\n\n  isAuthenticated() {\n    return this.hasValidToken();\n  }\n  /**\r\n   * Get current user\r\n   */\n\n\n  getCurrentUserValue() {\n    return this.currentUserSubject.value;\n  }\n  /**\r\n   * Check if user has specific role\r\n   */\n\n\n  hasRole(role) {\n    const user = this.getCurrentUserValue();\n    return user ? user.roles.includes(role) : false;\n  }\n  /**\r\n   * Check if user has any of the specified roles\r\n   */\n\n\n  hasAnyRole(roles) {\n    const user = this.getCurrentUserValue();\n    return user ? roles.some(role => user.roles.includes(role)) : false;\n  }\n  /**\r\n   * Check if user is admin\r\n   */\n\n\n  isAdmin() {\n    return this.hasRole('admin');\n  }\n  /**\r\n   * Check if user is trainer\r\n   */\n\n\n  isTrainer() {\n    return this.hasRole('trainer');\n  }\n  /**\r\n   * Check if user is employee\r\n   */\n\n\n  isEmployee() {\n    return this.hasRole('employee');\n  }\n  /**\r\n   * Get access token\r\n   */\n\n\n  getAccessToken() {\n    return localStorage.getItem(this.accessTokenKey);\n  }\n  /**\r\n   * Get refresh token\r\n   */\n\n\n  getRefreshToken() {\n    return localStorage.getItem(this.refreshTokenKey);\n  }\n  /**\r\n   * Get Keycloak configuration\r\n   */\n\n\n  getKeycloakConfig() {\n    return this.keycloakConfig;\n  }\n  /**\r\n   * Set session data\r\n   */\n\n\n  setSession(authData) {\n    localStorage.setItem(this.accessTokenKey, authData.access_token);\n    localStorage.setItem(this.refreshTokenKey, authData.refresh_token);\n    localStorage.setItem(this.userKey, JSON.stringify(authData.user));\n    this.currentUserSubject.next(authData.user);\n    this.isAuthenticatedSubject.next(true);\n  }\n  /**\r\n   * Set tokens only\r\n   */\n\n\n  setTokens(accessToken, refreshToken) {\n    localStorage.setItem(this.accessTokenKey, accessToken);\n    localStorage.setItem(this.refreshTokenKey, refreshToken);\n  }\n  /**\r\n   * Update user data\r\n   */\n\n\n  updateUser(user) {\n    localStorage.setItem(this.userKey, JSON.stringify(user));\n    this.currentUserSubject.next(user);\n  }\n  /**\r\n   * Clear session\r\n   */\n\n\n  clearSession() {\n    localStorage.removeItem(this.accessTokenKey);\n    localStorage.removeItem(this.refreshTokenKey);\n    localStorage.removeItem(this.userKey);\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n    this.router.navigate(['/auth/login']);\n  }\n  /**\r\n   * Get user from storage\r\n   */\n\n\n  getUserFromStorage() {\n    const userStr = localStorage.getItem(this.userKey);\n    return userStr ? JSON.parse(userStr) : null;\n  }\n  /**\r\n   * Check if token exists\r\n   */\n\n\n  hasValidToken() {\n    const token = this.getAccessToken();\n    return !!token;\n  }\n  /**\r\n   * Get authorization headers\r\n   */\n\n\n  getAuthHeaders() {\n    const token = this.getAccessToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  /**\r\n   * Redirect user based on role after login\r\n   */\n\n\n  redirectAfterLogin() {\n    const user = this.getCurrentUserValue();\n    if (!user) return;\n\n    if (user.roles.includes('admin')) {\n      this.router.navigate(['/']);\n    } else if (user.roles.includes('trainer')) {\n      this.router.navigate(['/trainer/dashboard']);\n    } else if (user.roles.includes('employee')) {\n      this.router.navigate(['/employee/dashboard']);\n    } else {\n      this.router.navigate(['/']);\n    }\n  }\n\n}\n\nKeycloakAuthService.ɵfac = function KeycloakAuthService_Factory(t) {\n  return new (t || KeycloakAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n};\n\nKeycloakAuthService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: KeycloakAuthService,\n  factory: KeycloakAuthService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";AACA,SAAqBA,WAArB,QAAwC,sBAAxC;AACA,SAASC,eAAT,EAA0BC,UAA1B,EAAsCC,UAAtC,QAAwD,MAAxD;AACA,SAAcC,UAAd,EAA0BC,GAA1B,QAAqC,gBAArC;;;;AA8CA,OAAM,MAAOC,mBAAP,CAA0B;EAc5BC,YACYC,IADZ,EAEYC,MAFZ,EAE0B;IADd;IACA;IAfJ,cAAS,2BAAT;IACA,sBAAiB,uBAAjB;IACA,uBAAkB,wBAAlB;IACA,eAAU,eAAV;IAEA,0BAAqB,IAAIR,eAAJ,CAAyC,KAAKS,kBAAL,EAAzC,CAArB;IACD,oBAAe,KAAKC,kBAAL,CAAwBC,YAAxB,EAAf;IAEC,8BAAyB,IAAIX,eAAJ,CAA6B,KAAKY,aAAL,EAA7B,CAAzB;IACD,wBAAmB,KAAKC,sBAAL,CAA4BF,YAA5B,EAAnB;IAEC,sBAAwC,IAAxC;IA+PR;;;;IAGQ,mBAAeG,KAAD,IAAe;MACjCC,OAAO,CAACD,KAAR,CAAc,8BAAd,EAA8CA,KAA9C;;MAEA,IAAIA,KAAK,CAACE,MAAN,KAAiB,GAArB,EAA0B;QACtB,KAAKC,YAAL;MACH;;MAED,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CARO;;IA5PJ,KAAKI,kBAAL;EACH;EAED;;;;;EAGcA,kBAAkB;IAAA;;IAAA;MAC5B,IAAI;QACA,MAAMC,QAAQ,SAAS,KAAI,CAACZ,IAAL,CAAUa,GAAV,CAAmB,GAAG,KAAI,CAACC,MAAM,cAAjC,EAAiDC,SAAjD,EAAvB;;QACA,IAAIH,QAAQ,CAACI,OAAb,EAAsB;UAClB,KAAI,CAACC,cAAL,GAAsBL,QAAQ,CAACM,IAA/B;QACH;MACJ,CALD,CAKE,OAAOX,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,iCAAd,EAAiDA,KAAjD;MACH;IAR2B;EAS/B;EAED;;;;;EAGAY,KAAK,CAACC,WAAD,EAA0B;IAC3B,OAAO,KAAKpB,IAAL,CAAUqB,IAAV,CAA8B,GAAG,KAAKP,MAAM,aAA5C,EAA2DM,WAA3D,EACFE,IADE,CAECzB,GAAG,CAACe,QAAQ,IAAG;MACX,IAAIA,QAAQ,CAACI,OAAb,EAAsB;QAClB,KAAKO,UAAL,CAAgBX,QAAQ,CAACM,IAAzB;MACH;IACJ,CAJE,CAFJ,EAOCtB,UAAU,CAAC,KAAK4B,WAAN,CAPX,CAAP;EASH;EAED;;;;;EAGAC,YAAY;IACR,MAAMA,YAAY,GAAG,KAAKC,eAAL,EAArB;;IAEA,IAAI,CAACD,YAAL,EAAmB;MACf,OAAO9B,UAAU,CAAC,4BAAD,CAAjB;IACH;;IAED,OAAO,KAAKK,IAAL,CAAUqB,IAAV,CAAe,GAAG,KAAKP,MAAM,eAA7B,EAA8C;MACjDa,aAAa,EAAEF;IADkC,CAA9C,EAEJH,IAFI,CAGHzB,GAAG,CAAEe,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACI,OAAb,EAAsB;QAClB,KAAKY,SAAL,CAAehB,QAAQ,CAACM,IAAT,CAAcW,YAA7B,EAA2CjB,QAAQ,CAACM,IAAT,CAAcS,aAAzD;MACH;IACJ,CAJE,CAHA,EAQH/B,UAAU,CAACW,KAAK,IAAG;MACf,KAAKG,YAAL;MACA,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CAHS,CARP,CAAP;EAaH;EAED;;;;;EAGAuB,MAAM;IACF,MAAML,YAAY,GAAG,KAAKC,eAAL,EAArB;IAEA,MAAMK,aAAa,GAAGN,YAAY,GAC9B,KAAKzB,IAAL,CAAUqB,IAAV,CAAe,GAAG,KAAKP,MAAM,cAA7B,EAA6C;MAAEa,aAAa,EAAEF;IAAjB,CAA7C,CAD8B,GAE9B,IAAI/B,UAAJ,CAAesC,QAAQ,IAAIA,QAAQ,CAACC,IAAT,CAAc,EAAd,CAA3B,CAFJ;IAIA,OAAOF,aAAa,CAACT,IAAd,CACHzB,GAAG,CAAC,MAAM,KAAKa,YAAL,EAAP,CADA,EAEHd,UAAU,CAAC,MAAK;MACZ,KAAKc,YAAL;MACA,OAAOf,UAAU,CAAC,eAAD,CAAjB;IACH,CAHS,CAFP,CAAP;EAOH;EAED;;;;;EAGAuC,cAAc;IACV,OAAO,KAAKlC,IAAL,CAAUa,GAAV,CAAc,GAAG,KAAKC,MAAM,UAA5B,EAAwC;MAC3CqB,OAAO,EAAE,KAAKC,cAAL;IADkC,CAAxC,EAEJd,IAFI,CAGHzB,GAAG,CAAEe,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACI,OAAb,EAAsB;QAClB,KAAKqB,UAAL,CAAgBzB,QAAQ,CAACM,IAAT,CAAcoB,IAA9B;MACH;IACJ,CAJE,CAHA,EAQH1C,UAAU,CAAC,KAAK4B,WAAN,CARP,CAAP;EAUH;EAED;;;;;EAGAe,aAAa;IACT,OAAO,KAAKvC,IAAL,CAAUqB,IAAV,CAAe,GAAG,KAAKP,MAAM,gBAA7B,EAA+C,EAA/C,EAAmD;MACtDqB,OAAO,EAAE,KAAKC,cAAL;IAD6C,CAAnD,EAEJd,IAFI,CAGHzB,GAAG,CAAEe,QAAD,IAAkB;MAClB,IAAIA,QAAQ,CAACI,OAAb,EAAsB;QAClB,KAAKqB,UAAL,CAAgBzB,QAAQ,CAACM,IAAT,CAAcoB,IAA9B;MACH;IACJ,CAJE,CAHA,EAQH1C,UAAU,CAACW,KAAK,IAAG;MACf,KAAKG,YAAL;MACA,OAAOf,UAAU,CAACY,KAAD,CAAjB;IACH,CAHS,CARP,CAAP;EAaH;EAED;;;;;EAGAiC,eAAe;IACX,OAAO,KAAKnC,aAAL,EAAP;EACH;EAED;;;;;EAGAoC,mBAAmB;IACf,OAAO,KAAKtC,kBAAL,CAAwBuC,KAA/B;EACH;EAED;;;;;EAGAC,OAAO,CAACC,IAAD,EAAa;IAChB,MAAMN,IAAI,GAAG,KAAKG,mBAAL,EAAb;IACA,OAAOH,IAAI,GAAGA,IAAI,CAACO,KAAL,CAAWC,QAAX,CAAoBF,IAApB,CAAH,GAA+B,KAA1C;EACH;EAED;;;;;EAGAG,UAAU,CAACF,KAAD,EAAgB;IACtB,MAAMP,IAAI,GAAG,KAAKG,mBAAL,EAAb;IACA,OAAOH,IAAI,GAAGO,KAAK,CAACG,IAAN,CAAWJ,IAAI,IAAIN,IAAI,CAACO,KAAL,CAAWC,QAAX,CAAoBF,IAApB,CAAnB,CAAH,GAAmD,KAA9D;EACH;EAED;;;;;EAGAK,OAAO;IACH,OAAO,KAAKN,OAAL,CAAa,OAAb,CAAP;EACH;EAED;;;;;EAGAO,SAAS;IACL,OAAO,KAAKP,OAAL,CAAa,SAAb,CAAP;EACH;EAED;;;;;EAGAQ,UAAU;IACN,OAAO,KAAKR,OAAL,CAAa,UAAb,CAAP;EACH;EAED;;;;;EAGAS,cAAc;IACV,OAAOC,YAAY,CAACC,OAAb,CAAqB,KAAKC,cAA1B,CAAP;EACH;EAED;;;;;EAGA7B,eAAe;IACX,OAAO2B,YAAY,CAACC,OAAb,CAAqB,KAAKE,eAA1B,CAAP;EACH;EAED;;;;;EAGAC,iBAAiB;IACb,OAAO,KAAKxC,cAAZ;EACH;EAED;;;;;EAGQM,UAAU,CAACmC,QAAD,EAAc;IAC5BL,YAAY,CAACM,OAAb,CAAqB,KAAKJ,cAA1B,EAA0CG,QAAQ,CAAC7B,YAAnD;IACAwB,YAAY,CAACM,OAAb,CAAqB,KAAKH,eAA1B,EAA2CE,QAAQ,CAAC/B,aAApD;IACA0B,YAAY,CAACM,OAAb,CAAqB,KAAKC,OAA1B,EAAmCC,IAAI,CAACC,SAAL,CAAeJ,QAAQ,CAACpB,IAAxB,CAAnC;IACA,KAAKnC,kBAAL,CAAwB8B,IAAxB,CAA6ByB,QAAQ,CAACpB,IAAtC;IACA,KAAKhC,sBAAL,CAA4B2B,IAA5B,CAAiC,IAAjC;EACH;EAED;;;;;EAGQL,SAAS,CAACmC,WAAD,EAAsBtC,YAAtB,EAA0C;IACvD4B,YAAY,CAACM,OAAb,CAAqB,KAAKJ,cAA1B,EAA0CQ,WAA1C;IACAV,YAAY,CAACM,OAAb,CAAqB,KAAKH,eAA1B,EAA2C/B,YAA3C;EACH;EAED;;;;;EAGQY,UAAU,CAACC,IAAD,EAAmB;IACjCe,YAAY,CAACM,OAAb,CAAqB,KAAKC,OAA1B,EAAmCC,IAAI,CAACC,SAAL,CAAexB,IAAf,CAAnC;IACA,KAAKnC,kBAAL,CAAwB8B,IAAxB,CAA6BK,IAA7B;EACH;EAED;;;;;EAGQ5B,YAAY;IAChB2C,YAAY,CAACW,UAAb,CAAwB,KAAKT,cAA7B;IACAF,YAAY,CAACW,UAAb,CAAwB,KAAKR,eAA7B;IACAH,YAAY,CAACW,UAAb,CAAwB,KAAKJ,OAA7B;IACA,KAAKzD,kBAAL,CAAwB8B,IAAxB,CAA6B,IAA7B;IACA,KAAK3B,sBAAL,CAA4B2B,IAA5B,CAAiC,KAAjC;IACA,KAAKhC,MAAL,CAAYgE,QAAZ,CAAqB,CAAC,aAAD,CAArB;EACH;EAED;;;;;EAGQ/D,kBAAkB;IACtB,MAAMgE,OAAO,GAAGb,YAAY,CAACC,OAAb,CAAqB,KAAKM,OAA1B,CAAhB;IACA,OAAOM,OAAO,GAAGL,IAAI,CAACM,KAAL,CAAWD,OAAX,CAAH,GAAyB,IAAvC;EACH;EAED;;;;;EAGQ7D,aAAa;IACjB,MAAM+D,KAAK,GAAG,KAAKhB,cAAL,EAAd;IACA,OAAO,CAAC,CAACgB,KAAT;EACH;EAED;;;;;EAGQhC,cAAc;IAClB,MAAMgC,KAAK,GAAG,KAAKhB,cAAL,EAAd;IACA,OAAO,IAAI5D,WAAJ,CAAgB;MACnB,iBAAiB,UAAU4E,KAAK,EADb;MAEnB,gBAAgB;IAFG,CAAhB,CAAP;EAIH;EAeD;;;;;EAGAC,kBAAkB;IACd,MAAM/B,IAAI,GAAG,KAAKG,mBAAL,EAAb;IACA,IAAI,CAACH,IAAL,EAAW;;IAEX,IAAIA,IAAI,CAACO,KAAL,CAAWC,QAAX,CAAoB,OAApB,CAAJ,EAAkC;MAC9B,KAAK7C,MAAL,CAAYgE,QAAZ,CAAqB,CAAC,GAAD,CAArB;IACH,CAFD,MAEO,IAAI3B,IAAI,CAACO,KAAL,CAAWC,QAAX,CAAoB,SAApB,CAAJ,EAAoC;MACvC,KAAK7C,MAAL,CAAYgE,QAAZ,CAAqB,CAAC,oBAAD,CAArB;IACH,CAFM,MAEA,IAAI3B,IAAI,CAACO,KAAL,CAAWC,QAAX,CAAoB,UAApB,CAAJ,EAAqC;MACxC,KAAK7C,MAAL,CAAYgE,QAAZ,CAAqB,CAAC,qBAAD,CAArB;IACH,CAFM,MAEA;MACH,KAAKhE,MAAL,CAAYgE,QAAZ,CAAqB,CAAC,GAAD,CAArB;IACH;EACJ;;AAxS2B;;;mBAAnBnE,qBAAmBwE;AAAA;;;SAAnBxE;EAAmByE,SAAnBzE,mBAAmB;EAAA0E,YAFhB", "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "catchError", "tap", "KeycloakAuthService", "constructor", "http", "router", "getUserFromStorage", "currentUserSubject", "asObservable", "hasValidToken", "isAuthenticatedSubject", "error", "console", "status", "clearSession", "loadKeycloakConfig", "response", "get", "apiUrl", "to<PERSON>romise", "success", "keycloakConfig", "data", "login", "credentials", "post", "pipe", "setSession", "handleError", "refreshToken", "getRefreshToken", "refresh_token", "setTokens", "access_token", "logout", "logoutRequest", "observer", "next", "getCurrentUser", "headers", "getAuthHeaders", "updateUser", "user", "validateToken", "isAuthenticated", "getCurrentUserValue", "value", "hasRole", "role", "roles", "includes", "hasAnyRole", "some", "isAdmin", "isTrainer", "isEmployee", "getAccessToken", "localStorage", "getItem", "accessTokenKey", "refreshT<PERSON><PERSON><PERSON>", "getKeycloakConfig", "authData", "setItem", "<PERSON><PERSON><PERSON>", "JSON", "stringify", "accessToken", "removeItem", "navigate", "userStr", "parse", "token", "redirectAfterLogin", "i0", "factory", "providedIn"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\services\\keycloak-auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\n\nexport interface KeycloakUser {\n    id: string;\n    username: string;\n    email: string;\n    name: string;\n    first_name: string;\n    last_name: string;\n    roles: string[];\n    team?: string;\n    phone?: string;\n    specialite?: string;\n}\n\nexport interface LoginRequest {\n    username: string;\n    password: string;\n}\n\nexport interface LoginResponse {\n    success: boolean;\n    message: string;\n    data: {\n        access_token: string;\n        refresh_token: string;\n        expires_in: number;\n        token_type: string;\n        user: KeycloakUser;\n    };\n}\n\nexport interface KeycloakConfig {\n    keycloak_url: string;\n    realm: string;\n    client_id: string;\n    auth_url: string;\n    token_url: string;\n    userinfo_url: string;\n    logout_url: string;\n}\n\n@Injectable({\n    providedIn: 'root'\n})\nexport class KeycloakAuthService {\n    private apiUrl = 'http://localhost:8080/api';\n    private accessTokenKey = 'keycloak_access_token';\n    private refreshTokenKey = 'keycloak_refresh_token';\n    private userKey = 'keycloak_user';\n\n    private currentUserSubject = new BehaviorSubject<KeycloakUser | null>(this.getUserFromStorage());\n    public currentUser$ = this.currentUserSubject.asObservable();\n\n    private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasValidToken());\n    public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n    private keycloakConfig: KeycloakConfig | null = null;\n\n    constructor(\n        private http: HttpClient,\n        private router: Router\n    ) {\n        this.loadKeycloakConfig();\n    }\n\n    /**\n     * Load Keycloak configuration from backend\n     */\n    private async loadKeycloakConfig(): Promise<void> {\n        try {\n            const response = await this.http.get<any>(`${this.apiUrl}/auth/config`).toPromise();\n            if (response.success) {\n                this.keycloakConfig = response.data;\n            }\n        } catch (error) {\n            console.error('Failed to load Keycloak config:', error);\n        }\n    }\n\n    /**\n     * Login with username and password\n     */\n    login(credentials: LoginRequest): Observable<LoginResponse> {\n        return this.http.post<LoginResponse>(`${this.apiUrl}/auth/login`, credentials)\n            .pipe(\n                tap(response => {\n                    if (response.success) {\n                        this.setSession(response.data);\n                    }\n                }),\n                catchError(this.handleError)\n            );\n    }\n\n    /**\n     * Refresh access token\n     */\n    refreshToken(): Observable<any> {\n        const refreshToken = this.getRefreshToken();\n        \n        if (!refreshToken) {\n            return throwError('No refresh token available');\n        }\n\n        return this.http.post(`${this.apiUrl}/auth/refresh`, {\n            refresh_token: refreshToken\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.setTokens(response.data.access_token, response.data.refresh_token);\n                }\n            }),\n            catchError(error => {\n                this.clearSession();\n                return throwError(error);\n            })\n        );\n    }\n\n    /**\n     * Logout user\n     */\n    logout(): Observable<any> {\n        const refreshToken = this.getRefreshToken();\n        \n        const logoutRequest = refreshToken ? \n            this.http.post(`${this.apiUrl}/auth/logout`, { refresh_token: refreshToken }) :\n            new Observable(observer => observer.next({}));\n\n        return logoutRequest.pipe(\n            tap(() => this.clearSession()),\n            catchError(() => {\n                this.clearSession();\n                return throwError('Logout failed');\n            })\n        );\n    }\n\n    /**\n     * Get current user info from server\n     */\n    getCurrentUser(): Observable<any> {\n        return this.http.get(`${this.apiUrl}/auth/me`, {\n            headers: this.getAuthHeaders()\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.updateUser(response.data.user);\n                }\n            }),\n            catchError(this.handleError)\n        );\n    }\n\n    /**\n     * Validate current token\n     */\n    validateToken(): Observable<any> {\n        return this.http.post(`${this.apiUrl}/auth/validate`, {}, {\n            headers: this.getAuthHeaders()\n        }).pipe(\n            tap((response: any) => {\n                if (response.success) {\n                    this.updateUser(response.data.user);\n                }\n            }),\n            catchError(error => {\n                this.clearSession();\n                return throwError(error);\n            })\n        );\n    }\n\n    /**\n     * Check if user is authenticated\n     */\n    isAuthenticated(): boolean {\n        return this.hasValidToken();\n    }\n\n    /**\n     * Get current user\n     */\n    getCurrentUserValue(): KeycloakUser | null {\n        return this.currentUserSubject.value;\n    }\n\n    /**\n     * Check if user has specific role\n     */\n    hasRole(role: string): boolean {\n        const user = this.getCurrentUserValue();\n        return user ? user.roles.includes(role) : false;\n    }\n\n    /**\n     * Check if user has any of the specified roles\n     */\n    hasAnyRole(roles: string[]): boolean {\n        const user = this.getCurrentUserValue();\n        return user ? roles.some(role => user.roles.includes(role)) : false;\n    }\n\n    /**\n     * Check if user is admin\n     */\n    isAdmin(): boolean {\n        return this.hasRole('admin');\n    }\n\n    /**\n     * Check if user is trainer\n     */\n    isTrainer(): boolean {\n        return this.hasRole('trainer');\n    }\n\n    /**\n     * Check if user is employee\n     */\n    isEmployee(): boolean {\n        return this.hasRole('employee');\n    }\n\n    /**\n     * Get access token\n     */\n    getAccessToken(): string | null {\n        return localStorage.getItem(this.accessTokenKey);\n    }\n\n    /**\n     * Get refresh token\n     */\n    getRefreshToken(): string | null {\n        return localStorage.getItem(this.refreshTokenKey);\n    }\n\n    /**\n     * Get Keycloak configuration\n     */\n    getKeycloakConfig(): KeycloakConfig | null {\n        return this.keycloakConfig;\n    }\n\n    /**\n     * Set session data\n     */\n    private setSession(authData: any): void {\n        localStorage.setItem(this.accessTokenKey, authData.access_token);\n        localStorage.setItem(this.refreshTokenKey, authData.refresh_token);\n        localStorage.setItem(this.userKey, JSON.stringify(authData.user));\n        this.currentUserSubject.next(authData.user);\n        this.isAuthenticatedSubject.next(true);\n    }\n\n    /**\n     * Set tokens only\n     */\n    private setTokens(accessToken: string, refreshToken: string): void {\n        localStorage.setItem(this.accessTokenKey, accessToken);\n        localStorage.setItem(this.refreshTokenKey, refreshToken);\n    }\n\n    /**\n     * Update user data\n     */\n    private updateUser(user: KeycloakUser): void {\n        localStorage.setItem(this.userKey, JSON.stringify(user));\n        this.currentUserSubject.next(user);\n    }\n\n    /**\n     * Clear session\n     */\n    private clearSession(): void {\n        localStorage.removeItem(this.accessTokenKey);\n        localStorage.removeItem(this.refreshTokenKey);\n        localStorage.removeItem(this.userKey);\n        this.currentUserSubject.next(null);\n        this.isAuthenticatedSubject.next(false);\n        this.router.navigate(['/auth/login']);\n    }\n\n    /**\n     * Get user from storage\n     */\n    private getUserFromStorage(): KeycloakUser | null {\n        const userStr = localStorage.getItem(this.userKey);\n        return userStr ? JSON.parse(userStr) : null;\n    }\n\n    /**\n     * Check if token exists\n     */\n    private hasValidToken(): boolean {\n        const token = this.getAccessToken();\n        return !!token;\n    }\n\n    /**\n     * Get authorization headers\n     */\n    private getAuthHeaders(): HttpHeaders {\n        const token = this.getAccessToken();\n        return new HttpHeaders({\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json'\n        });\n    }\n\n    /**\n     * Handle HTTP errors\n     */\n    private handleError = (error: any) => {\n        console.error('Keycloak Auth Service Error:', error);\n        \n        if (error.status === 401) {\n            this.clearSession();\n        }\n        \n        return throwError(error);\n    };\n\n    /**\n     * Redirect user based on role after login\n     */\n    redirectAfterLogin(): void {\n        const user = this.getCurrentUserValue();\n        if (!user) return;\n\n        if (user.roles.includes('admin')) {\n            this.router.navigate(['/']);\n        } else if (user.roles.includes('trainer')) {\n            this.router.navigate(['/trainer/dashboard']);\n        } else if (user.roles.includes('employee')) {\n            this.router.navigate(['/employee/dashboard']);\n        } else {\n            this.router.navigate(['/']);\n        }\n    }\n}\n"]}, "metadata": {}, "sourceType": "module"}