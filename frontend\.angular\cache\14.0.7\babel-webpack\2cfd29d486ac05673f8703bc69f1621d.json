{"ast": null, "code": "import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n  const hasConfig = typeof config === 'object';\n  return new Promise((resolve, reject) => {\n    const subscriber = new SafeSubscriber({\n      next: value => {\n        resolve(value);\n        subscriber.unsubscribe();\n      },\n      error: reject,\n      complete: () => {\n        if (hasConfig) {\n          resolve(config.defaultValue);\n        } else {\n          reject(new EmptyError());\n        }\n      }\n    });\n    source.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["EmptyError", "SafeSubscriber", "firstValueFrom", "source", "config", "hasConfig", "Promise", "resolve", "reject", "subscriber", "next", "value", "unsubscribe", "error", "complete", "defaultValue", "subscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/firstValueFrom.js"], "sourcesContent": ["import { EmptyError } from './util/EmptyError';\nimport { SafeSubscriber } from './Subscriber';\nexport function firstValueFrom(source, config) {\n    const hasConfig = typeof config === 'object';\n    return new Promise((resolve, reject) => {\n        const subscriber = new SafeSubscriber({\n            next: (value) => {\n                resolve(value);\n                subscriber.unsubscribe();\n            },\n            error: reject,\n            complete: () => {\n                if (hasConfig) {\n                    resolve(config.defaultValue);\n                }\n                else {\n                    reject(new EmptyError());\n                }\n            },\n        });\n        source.subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,mBAA3B;AACA,SAASC,cAAT,QAA+B,cAA/B;AACA,OAAO,SAASC,cAAT,CAAwBC,MAAxB,EAAgCC,MAAhC,EAAwC;EAC3C,MAAMC,SAAS,GAAG,OAAOD,MAAP,KAAkB,QAApC;EACA,OAAO,IAAIE,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;IACpC,MAAMC,UAAU,GAAG,IAAIR,cAAJ,CAAmB;MAClCS,IAAI,EAAGC,KAAD,IAAW;QACbJ,OAAO,CAACI,KAAD,CAAP;QACAF,UAAU,CAACG,WAAX;MACH,CAJiC;MAKlCC,KAAK,EAAEL,MAL2B;MAMlCM,QAAQ,EAAE,MAAM;QACZ,IAAIT,SAAJ,EAAe;UACXE,OAAO,CAACH,MAAM,CAACW,YAAR,CAAP;QACH,CAFD,MAGK;UACDP,MAAM,CAAC,IAAIR,UAAJ,EAAD,CAAN;QACH;MACJ;IAbiC,CAAnB,CAAnB;IAeAG,MAAM,CAACa,SAAP,CAAiBP,UAAjB;EACH,CAjBM,CAAP;AAkBH"}, "metadata": {}, "sourceType": "module"}