<!DOCTYPE html>
<html>
<head>
    <title>Test Trainer API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Test Trainer API</h1>
    
    <div class="test-section">
        <h3>1. Test GET /api/users?role=formateur</h3>
        <button onclick="testGetTrainers()">Test Get Trainers</button>
        <div id="getResult"></div>
    </div>

    <div class="test-section">
        <h3>2. Test POST /api/users (Create Trainer)</h3>
        <button onclick="testCreateTrainer()">Test Create Trainer</button>
        <div id="createResult"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Database Structure</h3>
        <button onclick="testDatabaseStructure()">Test DB Structure</button>
        <div id="dbResult"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000/api';

        async function testGetTrainers() {
            const resultDiv = document.getElementById('getResult');
            try {
                const response = await fetch(`${baseUrl}/users?role=formateur`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Success</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error ${response.status}</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function testCreateTrainer() {
            const resultDiv = document.getElementById('createResult');
            const testTrainer = {
                first_name: 'Test',
                last_name: 'Trainer',
                email: '<EMAIL>',
                password: 'password123',
                role: 'formateur',
                phone: '1234567890',
                specialite: 'Web Development'
            };

            try {
                const response = await fetch(`${baseUrl}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testTrainer)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Trainer Created Successfully</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Error ${response.status}</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        async function testDatabaseStructure() {
            const resultDiv = document.getElementById('dbResult');
            try {
                // Test if we can create a user with specialite field
                const testData = {
                    first_name: 'DB',
                    last_name: 'Test',
                    email: '<EMAIL>',
                    password: 'password123',
                    role: 'formateur',
                    specialite: 'Database Test'
                };

                const response = await fetch(`${baseUrl}/users`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Database structure OK - specialite field exists</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                    
                    // Clean up - delete the test user
                    if (data.id) {
                        await fetch(`${baseUrl}/users/${data.id}`, { method: 'DELETE' });
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Database issue detected</div><pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }

        // Auto-run tests on page load
        window.onload = function() {
            console.log('Testing API endpoints...');
            testGetTrainers();
        };
    </script>
</body>
</html>
