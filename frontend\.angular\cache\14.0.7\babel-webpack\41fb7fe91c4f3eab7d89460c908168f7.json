{"ast": null, "code": "import { combineLatest } from './combineLatest';\nexport function combineLatestWith(...otherSources) {\n  return combineLatest(...otherSources);\n}", "map": {"version": 3, "names": ["combineLatest", "combineLatestWith", "otherSources"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/combineLatestWith.js"], "sourcesContent": ["import { combineLatest } from './combineLatest';\nexport function combineLatestWith(...otherSources) {\n    return combineLatest(...otherSources);\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,OAAO,SAASC,iBAAT,CAA2B,GAAGC,YAA9B,EAA4C;EAC/C,OAAOF,aAAa,CAAC,GAAGE,YAAJ,CAApB;AACH"}, "metadata": {}, "sourceType": "module"}