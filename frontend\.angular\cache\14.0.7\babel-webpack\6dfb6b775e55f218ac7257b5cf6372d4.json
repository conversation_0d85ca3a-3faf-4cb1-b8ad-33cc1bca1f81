{"ast": null, "code": "import { scanInternals } from './scanInternals';\nimport { operate } from '../util/lift';\nexport function reduce(accumulator, seed) {\n  return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}", "map": {"version": 3, "names": ["scanInternals", "operate", "reduce", "accumulator", "seed", "arguments", "length"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/reduce.js"], "sourcesContent": ["import { scanInternals } from './scanInternals';\nimport { operate } from '../util/lift';\nexport function reduce(accumulator, seed) {\n    return operate(scanInternals(accumulator, seed, arguments.length >= 2, false, true));\n}\n"], "mappings": "AAAA,SAASA,aAAT,QAA8B,iBAA9B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,OAAO,SAASC,MAAT,CAAgBC,WAAhB,EAA6BC,IAA7B,EAAmC;EACtC,OAAOH,OAAO,CAACD,aAAa,CAACG,WAAD,EAAcC,IAAd,EAAoBC,SAAS,CAACC,MAAV,IAAoB,CAAxC,EAA2C,KAA3C,EAAkD,IAAlD,CAAd,CAAd;AACH"}, "metadata": {}, "sourceType": "module"}