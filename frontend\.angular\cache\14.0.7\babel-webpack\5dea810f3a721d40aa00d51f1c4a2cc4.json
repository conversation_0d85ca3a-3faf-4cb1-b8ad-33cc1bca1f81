{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeWhile(predicate, inclusive = false) {\n  return operate((source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const result = predicate(value, index++);\n      (result || inclusive) && subscriber.next(value);\n      !result && subscriber.complete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "<PERSON><PERSON><PERSON><PERSON>", "predicate", "inclusive", "source", "subscriber", "index", "subscribe", "value", "result", "next", "complete"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/takeWhile.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeWhile(predicate, inclusive = false) {\n    return operate((source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const result = predicate(value, index++);\n            (result || inclusive) && subscriber.next(value);\n            !result && subscriber.complete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,SAAT,CAAmBC,SAAnB,EAA8BC,SAAS,GAAG,KAA1C,EAAiD;EACpD,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,KAAK,GAAG,CAAZ;IACAF,MAAM,CAACG,SAAP,CAAiBP,wBAAwB,CAACK,UAAD,EAAcG,KAAD,IAAW;MAC7D,MAAMC,MAAM,GAAGP,SAAS,CAACM,KAAD,EAAQF,KAAK,EAAb,CAAxB;MACA,CAACG,MAAM,IAAIN,SAAX,KAAyBE,UAAU,CAACK,IAAX,CAAgBF,KAAhB,CAAzB;MACA,CAACC,MAAD,IAAWJ,UAAU,CAACM,QAAX,EAAX;IACH,CAJwC,CAAzC;EAKH,CAPa,CAAd;AAQH"}, "metadata": {}, "sourceType": "module"}