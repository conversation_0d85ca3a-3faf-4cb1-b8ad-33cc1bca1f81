{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\n\nclass FocusTrap {\n  constructor(el) {\n    this.el = el;\n  }\n\n  onkeydown(e) {\n    if (this.pFocusTrapDisabled !== true) {\n      e.preventDefault();\n      let focusableElements = DomHandler.getFocusableElements(this.el.nativeElement);\n\n      if (focusableElements && focusableElements.length > 0) {\n        if (!focusableElements[0].ownerDocument.activeElement) {\n          focusableElements[0].focus();\n        } else {\n          let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n\n          if (e.shiftKey) {\n            if (focusedIndex == -1 || focusedIndex === 0) focusableElements[focusableElements.length - 1].focus();else focusableElements[focusedIndex - 1].focus();\n          } else {\n            if (focusedIndex == -1 || focusedIndex === focusableElements.length - 1) focusableElements[0].focus();else focusableElements[focusedIndex + 1].focus();\n          }\n        }\n      }\n    }\n  }\n\n}\n\nFocusTrap.ɵfac = function FocusTrap_Factory(t) {\n  return new (t || FocusTrap)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nFocusTrap.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: FocusTrap,\n  selectors: [[\"\", \"pFocusTrap\", \"\"]],\n  hostAttrs: [1, \"p-element\"],\n  hostBindings: function FocusTrap_HostBindings(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵlistener(\"keydown.tab\", function FocusTrap_keydown_tab_HostBindingHandler($event) {\n        return ctx.onkeydown($event);\n      })(\"keydown.shift.tab\", function FocusTrap_keydown_shift_tab_HostBindingHandler($event) {\n        return ctx.onkeydown($event);\n      });\n    }\n  },\n  inputs: {\n    pFocusTrapDisabled: \"pFocusTrapDisabled\"\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrap, [{\n    type: Directive,\n    args: [{\n      selector: '[pFocusTrap]',\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    pFocusTrapDisabled: [{\n      type: Input\n    }],\n    onkeydown: [{\n      type: HostListener,\n      args: ['keydown.tab', ['$event']]\n    }, {\n      type: HostListener,\n      args: ['keydown.shift.tab', ['$event']]\n    }]\n  });\n})();\n\nclass FocusTrapModule {}\n\nFocusTrapModule.ɵfac = function FocusTrapModule_Factory(t) {\n  return new (t || FocusTrapModule)();\n};\n\nFocusTrapModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: FocusTrapModule\n});\nFocusTrapModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FocusTrapModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [FocusTrap],\n      declarations: [FocusTrap]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { FocusTrap, FocusTrapModule };", "map": {"version": 3, "names": ["i0", "Directive", "Input", "HostListener", "NgModule", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "FocusTrap", "constructor", "el", "onkeydown", "e", "pFocusTrapDisabled", "preventDefault", "focusableElements", "getFocusableElements", "nativeElement", "length", "ownerDocument", "activeElement", "focus", "focusedIndex", "indexOf", "shift<PERSON>ey", "ɵfac", "ElementRef", "ɵdir", "type", "args", "selector", "host", "FocusTrapModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-focustrap.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, Input, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\n\nclass FocusTrap {\n    constructor(el) {\n        this.el = el;\n    }\n    onkeydown(e) {\n        if (this.pFocusTrapDisabled !== true) {\n            e.preventDefault();\n            let focusableElements = DomHandler.getFocusableElements(this.el.nativeElement);\n            if (focusableElements && focusableElements.length > 0) {\n                if (!focusableElements[0].ownerDocument.activeElement) {\n                    focusableElements[0].focus();\n                }\n                else {\n                    let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n                    if (e.shiftKey) {\n                        if (focusedIndex == -1 || focusedIndex === 0)\n                            focusableElements[focusableElements.length - 1].focus();\n                        else\n                            focusableElements[focusedIndex - 1].focus();\n                    }\n                    else {\n                        if (focusedIndex == -1 || focusedIndex === (focusableElements.length - 1))\n                            focusableElements[0].focus();\n                        else\n                            focusableElements[focusedIndex + 1].focus();\n                    }\n                }\n            }\n        }\n    }\n}\nFocusTrap.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrap, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive });\nFocusTrap.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.6\", type: FocusTrap, selector: \"[pFocusTrap]\", inputs: { pFocusTrapDisabled: \"pFocusTrapDisabled\" }, host: { listeners: { \"keydown.tab\": \"onkeydown($event)\", \"keydown.shift.tab\": \"onkeydown($event)\" }, classAttribute: \"p-element\" }, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrap, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pFocusTrap]',\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { pFocusTrapDisabled: [{\n                type: Input\n            }], onkeydown: [{\n                type: HostListener,\n                args: ['keydown.tab', ['$event']]\n            }, {\n                type: HostListener,\n                args: ['keydown.shift.tab', ['$event']]\n            }] } });\nclass FocusTrapModule {\n}\nFocusTrapModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrapModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nFocusTrapModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrapModule, declarations: [FocusTrap], imports: [CommonModule], exports: [FocusTrap] });\nFocusTrapModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrapModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: FocusTrapModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [FocusTrap],\n                    declarations: [FocusTrap]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FocusTrap, FocusTrapModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,KAApB,EAA2BC,YAA3B,EAAyCC,QAAzC,QAAyD,eAAzD;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;;AAEA,MAAMC,SAAN,CAAgB;EACZC,WAAW,CAACC,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;EACH;;EACDC,SAAS,CAACC,CAAD,EAAI;IACT,IAAI,KAAKC,kBAAL,KAA4B,IAAhC,EAAsC;MAClCD,CAAC,CAACE,cAAF;MACA,IAAIC,iBAAiB,GAAGR,UAAU,CAACS,oBAAX,CAAgC,KAAKN,EAAL,CAAQO,aAAxC,CAAxB;;MACA,IAAIF,iBAAiB,IAAIA,iBAAiB,CAACG,MAAlB,GAA2B,CAApD,EAAuD;QACnD,IAAI,CAACH,iBAAiB,CAAC,CAAD,CAAjB,CAAqBI,aAArB,CAAmCC,aAAxC,EAAuD;UACnDL,iBAAiB,CAAC,CAAD,CAAjB,CAAqBM,KAArB;QACH,CAFD,MAGK;UACD,IAAIC,YAAY,GAAGP,iBAAiB,CAACQ,OAAlB,CAA0BR,iBAAiB,CAAC,CAAD,CAAjB,CAAqBI,aAArB,CAAmCC,aAA7D,CAAnB;;UACA,IAAIR,CAAC,CAACY,QAAN,EAAgB;YACZ,IAAIF,YAAY,IAAI,CAAC,CAAjB,IAAsBA,YAAY,KAAK,CAA3C,EACIP,iBAAiB,CAACA,iBAAiB,CAACG,MAAlB,GAA2B,CAA5B,CAAjB,CAAgDG,KAAhD,GADJ,KAGIN,iBAAiB,CAACO,YAAY,GAAG,CAAhB,CAAjB,CAAoCD,KAApC;UACP,CALD,MAMK;YACD,IAAIC,YAAY,IAAI,CAAC,CAAjB,IAAsBA,YAAY,KAAMP,iBAAiB,CAACG,MAAlB,GAA2B,CAAvE,EACIH,iBAAiB,CAAC,CAAD,CAAjB,CAAqBM,KAArB,GADJ,KAGIN,iBAAiB,CAACO,YAAY,GAAG,CAAhB,CAAjB,CAAoCD,KAApC;UACP;QACJ;MACJ;IACJ;EACJ;;AA7BW;;AA+BhBb,SAAS,CAACiB,IAAV;EAAA,iBAAsGjB,SAAtG,EAA4FP,EAA5F,mBAAiIA,EAAE,CAACyB,UAApI;AAAA;;AACAlB,SAAS,CAACmB,IAAV,kBAD4F1B,EAC5F;EAAA,MAA0FO,SAA1F;EAAA;EAAA;EAAA;IAAA;MAD4FP,EAC5F;QAAA,OAA0F,qBAA1F;MAAA;QAAA,OAA0F,qBAA1F;MAAA;IAAA;EAAA;EAAA;IAAA;EAAA;AAAA;;AACA;EAAA,mDAF4FA,EAE5F,mBAA2FO,SAA3F,EAAkH,CAAC;IACvGoB,IAAI,EAAE1B,SADiG;IAEvG2B,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cADX;MAECC,IAAI,EAAE;QACF,SAAS;MADP;IAFP,CAAD;EAFiG,CAAD,CAAlH,EAQ4B,YAAY;IAAE,OAAO,CAAC;MAAEH,IAAI,EAAE3B,EAAE,CAACyB;IAAX,CAAD,CAAP;EAAmC,CAR7E,EAQ+F;IAAEb,kBAAkB,EAAE,CAAC;MACtGe,IAAI,EAAEzB;IADgG,CAAD,CAAtB;IAE/EQ,SAAS,EAAE,CAAC;MACZiB,IAAI,EAAExB,YADM;MAEZyB,IAAI,EAAE,CAAC,aAAD,EAAgB,CAAC,QAAD,CAAhB;IAFM,CAAD,EAGZ;MACCD,IAAI,EAAExB,YADP;MAECyB,IAAI,EAAE,CAAC,mBAAD,EAAsB,CAAC,QAAD,CAAtB;IAFP,CAHY;EAFoE,CAR/F;AAAA;;AAiBA,MAAMG,eAAN,CAAsB;;AAEtBA,eAAe,CAACP,IAAhB;EAAA,iBAA4GO,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBAtB4FhC,EAsB5F;EAAA,MAA6G+B;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBAvB4FjC,EAuB5F;EAAA,UAAwIK,YAAxI;AAAA;;AACA;EAAA,mDAxB4FL,EAwB5F,mBAA2F+B,eAA3F,EAAwH,CAAC;IAC7GJ,IAAI,EAAEvB,QADuG;IAE7GwB,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CAAC7B,YAAD,CADV;MAEC8B,OAAO,EAAE,CAAC5B,SAAD,CAFV;MAGC6B,YAAY,EAAE,CAAC7B,SAAD;IAHf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,SAAT,EAAoBwB,eAApB"}, "metadata": {}, "sourceType": "module"}