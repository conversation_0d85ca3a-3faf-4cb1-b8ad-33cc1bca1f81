{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PrimeTemplate } from 'primeng/api';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nfunction MegaMenu_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction MegaMenu_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtemplate(1, MegaMenu_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.startTemplate);\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"p-hidden\": a0\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 10);\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, category_r6.visible === false));\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", category_r6.icon);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(category_r6.label);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", category_r6.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", category_r6.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(category_r6.badge);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"pi-angle-down\": a0,\n    \"pi-angle-right\": a1\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_1_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 25);\n  }\n\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c1, ctx_r18.orientation == \"horizontal\", ctx_r18.orientation == \"vertical\"));\n  }\n}\n\nconst _c2 = function (a1) {\n  return {\n    \"p-menuitem-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵlistener(\"click\", function MegaMenu_ng_template_3_li_1_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const category_r6 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onCategoryClick($event, category_r6));\n    });\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_a_1_span_1_Template, 1, 1, \"span\", 16);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_a_1_span_2_Template, 2, 1, \"span\", 17);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_a_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 18, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MegaMenu_ng_template_3_li_1_a_1_span_5_Template, 2, 2, \"span\", 19);\n    i0.ɵɵtemplate(6, MegaMenu_ng_template_3_li_1_a_1_span_6_Template, 1, 4, \"span\", 20);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r15 = i0.ɵɵreference(4);\n\n    const category_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassMap(category_r6.styleClass);\n    i0.ɵɵproperty(\"href\", category_r6.url || \"#\", i0.ɵɵsanitizeUrl)(\"target\", category_r6.target)(\"ngClass\", i0.ɵɵpureFunction1(14, _c2, category_r6.disabled))(\"ngStyle\", category_r6.style);\n    i0.ɵɵattribute(\"title\", category_r6.title)(\"id\", category_r6.id)(\"tabindex\", category_r6.tabindex ? category_r6.tabindex : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.escape !== false)(\"ngIfElse\", _r15);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", category_r6.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.items);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", category_r6.icon);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(category_r6.label);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", category_r6.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", category_r6.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(category_r6.badge);\n  }\n}\n\nconst _c3 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r38 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵlistener(\"click\", function MegaMenu_ng_template_3_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r38);\n      const category_r6 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.onCategoryClick($event, category_r6));\n    });\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_a_2_span_1_Template, 1, 1, \"span\", 16);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_a_2_span_2_Template, 2, 1, \"span\", 17);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_a_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 27, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MegaMenu_ng_template_3_li_1_a_2_span_5_Template, 2, 2, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r29 = i0.ɵɵreference(4);\n\n    const category_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵclassMap(category_r6.styleClass);\n    i0.ɵɵproperty(\"routerLink\", category_r6.routerLink)(\"queryParams\", category_r6.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", category_r6.routerLinkActiveOptions || i0.ɵɵpureFunction0(22, _c3))(\"target\", category_r6.target)(\"ngClass\", i0.ɵɵpureFunction1(23, _c2, category_r6.disabled))(\"ngStyle\", category_r6.style)(\"fragment\", category_r6.fragment)(\"queryParamsHandling\", category_r6.queryParamsHandling)(\"preserveFragment\", category_r6.preserveFragment)(\"skipLocationChange\", category_r6.skipLocationChange)(\"replaceUrl\", category_r6.replaceUrl)(\"state\", category_r6.state);\n    i0.ɵɵattribute(\"tabindex\", category_r6.tabindex ? category_r6.tabindex : \"0\")(\"title\", category_r6.title)(\"id\", category_r6.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.escape !== false)(\"ngIfElse\", _r29);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", category_r6.badge);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const submenu_r43 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(submenu_r43.label);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 34);\n  }\n\n  if (rf & 2) {\n    const submenu_r43 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"innerHTML\", submenu_r43.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const submenu_r43 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", submenu_r43.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(submenu_r43.badge);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 37);\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, item_r52.visible === false));\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 44);\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r52.icon)(\"ngStyle\", item_r52.iconStyle);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r52.label);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r52.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r52.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r52.badge);\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r69 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 41);\n    i0.ɵɵlistener(\"click\", function MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r69);\n      const item_r52 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r67 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r67.itemClick($event, item_r52));\n    });\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_1_Template, 1, 2, \"span\", 42);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_2_Template, 2, 1, \"span\", 17);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 43, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_span_5_Template, 2, 2, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r60 = i0.ɵɵreference(4);\n\n    const item_r52 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", item_r52.url || \"#\", i0.ɵɵsanitizeUrl)(\"target\", item_r52.target)(\"ngClass\", i0.ɵɵpureFunction1(10, _c4, item_r52.disabled));\n    i0.ɵɵattribute(\"title\", item_r52.title)(\"id\", item_r52.id)(\"tabindex\", item_r52.tabindex ? item_r52.tabindex : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r52.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r52.escape !== false)(\"ngIfElse\", _r60);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r52.badge);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 44);\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r52.icon)(\"ngStyle\", item_r52.iconStyle);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r52.label);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r52.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r52.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r52.badge);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 45);\n    i0.ɵɵlistener(\"click\", function MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r82);\n      const item_r52 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r80 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r80.itemClick($event, item_r52));\n    });\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_1_Template, 1, 2, \"span\", 42);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_2_Template, 2, 1, \"span\", 17);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_ng_template_3_Template, 1, 1, \"ng-template\", null, 46, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_span_5_Template, 2, 2, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r73 = i0.ɵɵreference(4);\n\n    const item_r52 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r52.routerLink)(\"queryParams\", item_r52.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r52.routerLinkActiveOptions || i0.ɵɵpureFunction0(19, _c3))(\"target\", item_r52.target)(\"ngClass\", i0.ɵɵpureFunction1(20, _c4, item_r52.disabled))(\"fragment\", item_r52.fragment)(\"queryParamsHandling\", item_r52.queryParamsHandling)(\"preserveFragment\", item_r52.preserveFragment)(\"skipLocationChange\", item_r52.skipLocationChange)(\"replaceUrl\", item_r52.replaceUrl)(\"state\", item_r52.state);\n    i0.ɵɵattribute(\"tabindex\", item_r52.tabindex ? item_r52.tabindex : \"0\")(\"title\", item_r52.title)(\"id\", item_r52.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r52.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r52.escape !== false)(\"ngIfElse\", _r73);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r52.badge);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 38);\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_1_Template, 6, 12, \"a\", 39);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_a_2_Template, 6, 22, \"a\", 40);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r52 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, item_r52.visible === false))(\"tooltipOptions\", item_r52.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r52.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r52.routerLink);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_0_Template, 1, 3, \"li\", 35);\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_li_1_Template, 3, 6, \"li\", 36);\n  }\n\n  if (rf & 2) {\n    const item_r52 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", item_r52.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r52.separator);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 30)(1, \"li\", 31);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_span_2_Template, 2, 1, \"span\", 32);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_3_Template, 1, 1, \"ng-template\", null, 33, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(5, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_span_5_Template, 2, 2, \"span\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_ng_template_6_Template, 2, 2, \"ng-template\", 3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const submenu_r43 = ctx.$implicit;\n\n    const _r45 = i0.ɵɵreference(4);\n\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", submenu_r43.escape !== false)(\"ngIfElse\", _r45);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", submenu_r43.badge);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", submenu_r43.items);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_ng_template_1_Template, 7, 4, \"ng-template\", 3);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const column_r41 = ctx.$implicit;\n    const category_r6 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r40 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r40.getColumnClass(category_r6));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", column_r41);\n  }\n}\n\nfunction MegaMenu_ng_template_3_li_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_div_3_ng_template_2_Template, 2, 3, \"ng-template\", 3);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r6.items);\n  }\n}\n\nconst _c5 = function (a1, a2) {\n  return {\n    \"p-menuitem\": true,\n    \"p-menuitem-active\": a1,\n    \"p-hidden\": a2\n  };\n};\n\nfunction MegaMenu_ng_template_3_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r89 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 11);\n    i0.ɵɵlistener(\"mouseenter\", function MegaMenu_ng_template_3_li_1_Template_li_mouseenter_0_listener($event) {\n      i0.ɵɵrestoreView(_r89);\n      const category_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r87 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r87.onCategoryMouseEnter($event, category_r6));\n    });\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_a_1_Template, 7, 16, \"a\", 12);\n    i0.ɵɵtemplate(2, MegaMenu_ng_template_3_li_1_a_2_Template, 6, 25, \"a\", 13);\n    i0.ɵɵtemplate(3, MegaMenu_ng_template_3_li_1_div_3_Template, 3, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const category_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c5, category_r6 == ctx_r8.activeItem, category_r6.visible === false))(\"tooltipOptions\", category_r6.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !category_r6.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", category_r6.items);\n  }\n}\n\nfunction MegaMenu_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MegaMenu_ng_template_3_li_0_Template, 1, 3, \"li\", 8);\n    i0.ɵɵtemplate(1, MegaMenu_ng_template_3_li_1_Template, 4, 8, \"li\", 9);\n  }\n\n  if (rf & 2) {\n    const category_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", category_r6.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !category_r6.separator);\n  }\n}\n\nfunction MegaMenu_div_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction MegaMenu_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtemplate(1, MegaMenu_div_4_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.endTemplate);\n  }\n}\n\nfunction MegaMenu_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c6 = function (a1, a2) {\n  return {\n    \"p-megamenu p-component\": true,\n    \"p-megamenu-horizontal\": a1,\n    \"p-megamenu-vertical\": a2\n  };\n};\n\nconst _c7 = [\"*\"];\n\nclass MegaMenu {\n  constructor(el, renderer, cd) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.orientation = 'horizontal';\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  onCategoryMouseEnter(event, menuitem) {\n    if (menuitem.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (this.activeItem) {\n      this.activeItem = menuitem;\n    }\n  }\n\n  onCategoryClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    if (item.items) {\n      if (this.activeItem && this.activeItem === item) {\n        this.activeItem = null;\n        this.unbindDocumentClickListener();\n      } else {\n        this.activeItem = item;\n        this.bindDocumentClickListener();\n      }\n    }\n  }\n\n  itemClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n\n    this.activeItem = null;\n  }\n\n  getColumnClass(menuitem) {\n    let length = menuitem.items ? menuitem.items.length : 0;\n    let columnClass;\n\n    switch (length) {\n      case 2:\n        columnClass = 'p-megamenu-col-6';\n        break;\n\n      case 3:\n        columnClass = 'p-megamenu-col-4';\n        break;\n\n      case 4:\n        columnClass = 'p-megamenu-col-3';\n        break;\n\n      case 6:\n        columnClass = 'p-megamenu-col-2';\n        break;\n\n      default:\n        columnClass = 'p-megamenu-col-12';\n        break;\n    }\n\n    return columnClass;\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      this.documentClickListener = event => {\n        if (this.el && !this.el.nativeElement.contains(event.target)) {\n          this.activeItem = null;\n          this.unbindDocumentClickListener();\n          this.cd.markForCheck();\n        }\n      };\n\n      document.addEventListener('click', this.documentClickListener);\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      document.removeEventListener('click', this.documentClickListener);\n      this.documentClickListener = null;\n    }\n  }\n\n}\n\nMegaMenu.ɵfac = function MegaMenu_Factory(t) {\n  return new (t || MegaMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nMegaMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: MegaMenu,\n  selectors: [[\"p-megaMenu\"]],\n  contentQueries: function MegaMenu_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    orientation: \"orientation\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\"\n  },\n  ngContentSelectors: _c7,\n  decls: 7,\n  vars: 11,\n  consts: [[3, \"ngStyle\", \"ngClass\"], [\"class\", \"p-megamenu-start\", 4, \"ngIf\"], [\"role\", \"menubar\", 1, \"p-megamenu-root-list\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-megamenu-end\", 4, \"ngIf\", \"ngIfElse\"], [\"legacy\", \"\"], [1, \"p-megamenu-start\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-menu-separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"tooltipOptions\", \"mouseenter\", 4, \"ngIf\"], [1, \"p-menu-separator\", 3, \"ngClass\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"tooltipOptions\", \"mouseenter\"], [\"pRipple\", \"\", 3, \"href\", \"target\", \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"ngStyle\", \"class\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [\"class\", \"p-megamenu-panel\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"href\", \"target\", \"ngClass\", \"ngStyle\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"categoryHtmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-submenu-icon pi\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-submenu-icon\", \"pi\", 3, \"ngClass\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"ngStyle\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\"], [\"categoryHtmlRouteLabel\", \"\"], [1, \"p-megamenu-panel\"], [1, \"p-megamenu-grid\"], [\"role\", \"menu\", 1, \"p-megamenu-submenu\"], [1, \"p-megamenu-submenu-header\"], [4, \"ngIf\", \"ngIfElse\"], [\"submenuHtmlLabel\", \"\"], [3, \"innerHTML\"], [\"class\", \"p-menu-separator\", \"role\", \"separator\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-menuitem\", \"role\", \"none\", \"pTooltip\", \"\", 3, \"ngClass\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menu-separator\", 3, \"ngClass\"], [\"role\", \"none\", \"pTooltip\", \"\", 1, \"p-menuitem\", 3, \"ngClass\", \"tooltipOptions\"], [\"role\", \"menuitem\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"href\", \"target\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"class\", \"p-menuitem-link\", \"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"href\", \"target\", \"ngClass\", \"click\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"htmlLabel\", \"\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"menuitem\", \"pRipple\", \"\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\"], [\"htmlRouteLabel\", \"\"], [1, \"p-megamenu-end\"]],\n  template: function MegaMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, MegaMenu_div_1_Template, 2, 1, \"div\", 1);\n      i0.ɵɵelementStart(2, \"ul\", 2);\n      i0.ɵɵtemplate(3, MegaMenu_ng_template_3_Template, 2, 2, \"ng-template\", 3);\n      i0.ɵɵtemplate(4, MegaMenu_div_4_Template, 2, 1, \"div\", 4);\n      i0.ɵɵtemplate(5, MegaMenu_ng_template_5_Template, 2, 0, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      const _r3 = i0.ɵɵreference(6);\n\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction2(8, _c6, ctx.orientation == \"horizontal\", ctx.orientation == \"vertical\"));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.model);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", _r3);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Ripple, i4.Tooltip],\n  styles: [\".p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-submenu-header{display:flex;align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-link>.p-submenu-icon{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-megaMenu',\n      template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\"\n            [ngClass]=\"{'p-megamenu p-component':true,'p-megamenu-horizontal': orientation == 'horizontal','p-megamenu-vertical': orientation == 'vertical'}\">\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <ul class=\"p-megamenu-root-list\" role=\"menubar\">\n                <ng-template ngFor let-category [ngForOf]=\"model\">\n                    <li *ngIf=\"category.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': category.visible === false}\">\n                    <li *ngIf=\"!category.separator\" [ngClass]=\"{'p-menuitem':true,'p-menuitem-active':category==activeItem, 'p-hidden': category.visible === false}\" pTooltip [tooltipOptions]=\"category.tooltipOptions\"\n                        (mouseenter)=\"onCategoryMouseEnter($event, category)\">\n                        <a *ngIf=\"!category.routerLink\" [href]=\"category.url||'#'\" [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\" (click)=\"onCategoryClick($event, category)\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                            <span *ngIf=\"category.items\" class=\"p-submenu-icon pi\" [ngClass]=\"{'pi-angle-down':orientation=='horizontal','pi-angle-right':orientation=='vertical'}\"></span>\n                        </a>\n                        <a *ngIf=\"category.routerLink\" [routerLink]=\"category.routerLink\" [queryParams]=\"category.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"category.routerLinkActiveOptions||{exact:false}\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\"\n                            (click)=\"onCategoryClick($event, category)\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\"\n                            [fragment]=\"category.fragment\" [queryParamsHandling]=\"category.queryParamsHandling\" [preserveFragment]=\"category.preserveFragment\" [skipLocationChange]=\"category.skipLocationChange\" [replaceUrl]=\"category.replaceUrl\" [state]=\"category.state\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlRouteLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                        </a>\n                        <div class=\"p-megamenu-panel\" *ngIf=\"category.items\">\n                            <div class=\"p-megamenu-grid\">\n                                <ng-template ngFor let-column [ngForOf]=\"category.items\">\n                                    <div [class]=\"getColumnClass(category)\">\n                                        <ng-template ngFor let-submenu [ngForOf]=\"column\">\n                                            <ul class=\"p-megamenu-submenu\" role=\"menu\">\n                                                <li class=\"p-megamenu-submenu-header\">\n                                                    <span *ngIf=\"submenu.escape !== false; else submenuHtmlLabel\">{{submenu.label}}</span>\n                                                    <ng-template #submenuHtmlLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                                                    <span class=\"p-menuitem-badge\" *ngIf=\"submenu.badge\" [ngClass]=\"submenu.badgeStyleClass\">{{submenu.badge}}</span>\n                                                </li>\n                                                <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                                                    <li *ngIf=\"item.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\">\n                                                    <li *ngIf=\"!item.separator\" class=\"p-menuitem\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"none\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                                                        <a *ngIf=\"!item.routerLink\" role=\"menuitem\" [href]=\"item.url||'#'\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                        <a *ngIf=\"item.routerLink\" role=\"menuitem\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                                                             [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\"\n                                                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                    </li>\n                                                </ng-template>\n                                            </ul>\n                                        </ng-template>\n                                    </div>\n                                </ng-template>\n                            </div>\n                        </div>\n                    </li>\n                </ng-template>\n                <div class=\"p-megamenu-end\" *ngIf=\"endTemplate; else legacy\">\n                    <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n                </div>\n                <ng-template #legacy>\n                    <div class=\"p-megamenu-end\">\n                        <ng-content></ng-content>\n                    </div>\n                </ng-template>\n            </ul>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-submenu-header{display:flex;align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-link>.p-submenu-icon{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass MegaMenuModule {}\n\nMegaMenuModule.ɵfac = function MegaMenuModule_Factory(t) {\n  return new (t || MegaMenuModule)();\n};\n\nMegaMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: MegaMenuModule\n});\nMegaMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MegaMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n      exports: [MegaMenu, RouterModule, TooltipModule],\n      declarations: [MegaMenu]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { MegaMenu, MegaMenuModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ContentChildren", "NgModule", "i1", "CommonModule", "PrimeTemplate", "i2", "RouterModule", "i3", "RippleModule", "i4", "TooltipModule", "MegaMenu", "constructor", "el", "renderer", "cd", "orientation", "autoZIndex", "baseZIndex", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "startTemplate", "template", "endTemplate", "onCategoryMouseEnter", "event", "menuitem", "disabled", "preventDefault", "activeItem", "onCategoryClick", "url", "routerLink", "command", "originalEvent", "items", "unbindDocumentClickListener", "bindDocumentClickListener", "itemClick", "getColumnClass", "length", "columnClass", "documentClickListener", "nativeElement", "contains", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "document", "addEventListener", "removeEventListener", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "model", "style", "styleClass", "MegaMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-megamenu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { PrimeTemplate } from 'primeng/api';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nclass MegaMenu {\n    constructor(el, renderer, cd) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.orientation = 'horizontal';\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    onCategoryMouseEnter(event, menuitem) {\n        if (menuitem.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (this.activeItem) {\n            this.activeItem = menuitem;\n        }\n    }\n    onCategoryClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        if (item.items) {\n            if (this.activeItem && this.activeItem === item) {\n                this.activeItem = null;\n                this.unbindDocumentClickListener();\n            }\n            else {\n                this.activeItem = item;\n                this.bindDocumentClickListener();\n            }\n        }\n    }\n    itemClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n        this.activeItem = null;\n    }\n    getColumnClass(menuitem) {\n        let length = menuitem.items ? menuitem.items.length : 0;\n        let columnClass;\n        switch (length) {\n            case 2:\n                columnClass = 'p-megamenu-col-6';\n                break;\n            case 3:\n                columnClass = 'p-megamenu-col-4';\n                break;\n            case 4:\n                columnClass = 'p-megamenu-col-3';\n                break;\n            case 6:\n                columnClass = 'p-megamenu-col-2';\n                break;\n            default:\n                columnClass = 'p-megamenu-col-12';\n                break;\n        }\n        return columnClass;\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = (event) => {\n                if (this.el && !this.el.nativeElement.contains(event.target)) {\n                    this.activeItem = null;\n                    this.unbindDocumentClickListener();\n                    this.cd.markForCheck();\n                }\n            };\n            document.addEventListener('click', this.documentClickListener);\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            document.removeEventListener('click', this.documentClickListener);\n            this.documentClickListener = null;\n        }\n    }\n}\nMegaMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenu, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nMegaMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: MegaMenu, selector: \"p-megaMenu\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", orientation: \"orientation\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\"\n            [ngClass]=\"{'p-megamenu p-component':true,'p-megamenu-horizontal': orientation == 'horizontal','p-megamenu-vertical': orientation == 'vertical'}\">\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <ul class=\"p-megamenu-root-list\" role=\"menubar\">\n                <ng-template ngFor let-category [ngForOf]=\"model\">\n                    <li *ngIf=\"category.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': category.visible === false}\">\n                    <li *ngIf=\"!category.separator\" [ngClass]=\"{'p-menuitem':true,'p-menuitem-active':category==activeItem, 'p-hidden': category.visible === false}\" pTooltip [tooltipOptions]=\"category.tooltipOptions\"\n                        (mouseenter)=\"onCategoryMouseEnter($event, category)\">\n                        <a *ngIf=\"!category.routerLink\" [href]=\"category.url||'#'\" [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\" (click)=\"onCategoryClick($event, category)\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                            <span *ngIf=\"category.items\" class=\"p-submenu-icon pi\" [ngClass]=\"{'pi-angle-down':orientation=='horizontal','pi-angle-right':orientation=='vertical'}\"></span>\n                        </a>\n                        <a *ngIf=\"category.routerLink\" [routerLink]=\"category.routerLink\" [queryParams]=\"category.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"category.routerLinkActiveOptions||{exact:false}\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\"\n                            (click)=\"onCategoryClick($event, category)\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\"\n                            [fragment]=\"category.fragment\" [queryParamsHandling]=\"category.queryParamsHandling\" [preserveFragment]=\"category.preserveFragment\" [skipLocationChange]=\"category.skipLocationChange\" [replaceUrl]=\"category.replaceUrl\" [state]=\"category.state\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlRouteLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                        </a>\n                        <div class=\"p-megamenu-panel\" *ngIf=\"category.items\">\n                            <div class=\"p-megamenu-grid\">\n                                <ng-template ngFor let-column [ngForOf]=\"category.items\">\n                                    <div [class]=\"getColumnClass(category)\">\n                                        <ng-template ngFor let-submenu [ngForOf]=\"column\">\n                                            <ul class=\"p-megamenu-submenu\" role=\"menu\">\n                                                <li class=\"p-megamenu-submenu-header\">\n                                                    <span *ngIf=\"submenu.escape !== false; else submenuHtmlLabel\">{{submenu.label}}</span>\n                                                    <ng-template #submenuHtmlLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                                                    <span class=\"p-menuitem-badge\" *ngIf=\"submenu.badge\" [ngClass]=\"submenu.badgeStyleClass\">{{submenu.badge}}</span>\n                                                </li>\n                                                <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                                                    <li *ngIf=\"item.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\">\n                                                    <li *ngIf=\"!item.separator\" class=\"p-menuitem\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"none\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                                                        <a *ngIf=\"!item.routerLink\" role=\"menuitem\" [href]=\"item.url||'#'\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                        <a *ngIf=\"item.routerLink\" role=\"menuitem\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                                                             [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\"\n                                                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                    </li>\n                                                </ng-template>\n                                            </ul>\n                                        </ng-template>\n                                    </div>\n                                </ng-template>\n                            </div>\n                        </div>\n                    </li>\n                </ng-template>\n                <div class=\"p-megamenu-end\" *ngIf=\"endTemplate; else legacy\">\n                    <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n                </div>\n                <ng-template #legacy>\n                    <div class=\"p-megamenu-end\">\n                        <ng-content></ng-content>\n                    </div>\n                </ng-template>\n            </ul>\n        </div>\n    `, isInline: true, styles: [\".p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-submenu-header{display:flex;align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-link>.p-submenu-icon{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }, { kind: \"directive\", type: i4.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-megaMenu', template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\"\n            [ngClass]=\"{'p-megamenu p-component':true,'p-megamenu-horizontal': orientation == 'horizontal','p-megamenu-vertical': orientation == 'vertical'}\">\n            <div class=\"p-megamenu-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <ul class=\"p-megamenu-root-list\" role=\"menubar\">\n                <ng-template ngFor let-category [ngForOf]=\"model\">\n                    <li *ngIf=\"category.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': category.visible === false}\">\n                    <li *ngIf=\"!category.separator\" [ngClass]=\"{'p-menuitem':true,'p-menuitem-active':category==activeItem, 'p-hidden': category.visible === false}\" pTooltip [tooltipOptions]=\"category.tooltipOptions\"\n                        (mouseenter)=\"onCategoryMouseEnter($event, category)\">\n                        <a *ngIf=\"!category.routerLink\" [href]=\"category.url||'#'\" [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\" (click)=\"onCategoryClick($event, category)\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                            <span *ngIf=\"category.items\" class=\"p-submenu-icon pi\" [ngClass]=\"{'pi-angle-down':orientation=='horizontal','pi-angle-right':orientation=='vertical'}\"></span>\n                        </a>\n                        <a *ngIf=\"category.routerLink\" [routerLink]=\"category.routerLink\" [queryParams]=\"category.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"category.routerLinkActiveOptions||{exact:false}\" [attr.tabindex]=\"category.tabindex ? category.tabindex : '0'\"\n                            [target]=\"category.target\" [attr.title]=\"category.title\" [attr.id]=\"category.id\"\n                            (click)=\"onCategoryClick($event, category)\" [ngClass]=\"{'p-menuitem-link':true,'p-disabled':category.disabled}\" [ngStyle]=\"category.style\" [class]=\"category.styleClass\"\n                            [fragment]=\"category.fragment\" [queryParamsHandling]=\"category.queryParamsHandling\" [preserveFragment]=\"category.preserveFragment\" [skipLocationChange]=\"category.skipLocationChange\" [replaceUrl]=\"category.replaceUrl\" [state]=\"category.state\" pRipple>\n                            <span class=\"p-menuitem-icon\" *ngIf=\"category.icon\" [ngClass]=\"category.icon\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"category.escape !== false; else categoryHtmlRouteLabel\">{{category.label}}</span>\n                            <ng-template #categoryHtmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"category.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"category.badge\" [ngClass]=\"category.badgeStyleClass\">{{category.badge}}</span>\n                        </a>\n                        <div class=\"p-megamenu-panel\" *ngIf=\"category.items\">\n                            <div class=\"p-megamenu-grid\">\n                                <ng-template ngFor let-column [ngForOf]=\"category.items\">\n                                    <div [class]=\"getColumnClass(category)\">\n                                        <ng-template ngFor let-submenu [ngForOf]=\"column\">\n                                            <ul class=\"p-megamenu-submenu\" role=\"menu\">\n                                                <li class=\"p-megamenu-submenu-header\">\n                                                    <span *ngIf=\"submenu.escape !== false; else submenuHtmlLabel\">{{submenu.label}}</span>\n                                                    <ng-template #submenuHtmlLabel><span [innerHTML]=\"submenu.label\"></span></ng-template>\n                                                    <span class=\"p-menuitem-badge\" *ngIf=\"submenu.badge\" [ngClass]=\"submenu.badgeStyleClass\">{{submenu.badge}}</span>\n                                                </li>\n                                                <ng-template ngFor let-item [ngForOf]=\"submenu.items\">\n                                                    <li *ngIf=\"item.separator\" class=\"p-menu-separator\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"separator\">\n                                                    <li *ngIf=\"!item.separator\" class=\"p-menuitem\" [ngClass]=\"{'p-hidden': item.visible === false}\" role=\"none\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                                                        <a *ngIf=\"!item.routerLink\" role=\"menuitem\" [href]=\"item.url||'#'\" class=\"p-menuitem-link\" [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                        <a *ngIf=\"item.routerLink\" role=\"menuitem\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [attr.tabindex]=\"item.tabindex ? item.tabindex : '0'\"\n                                                            [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                                                             [target]=\"item.target\" [attr.title]=\"item.title\" [attr.id]=\"item.id\"\n                                                            [ngClass]=\"{'p-disabled':item.disabled}\" (click)=\"itemClick($event, item)\"\n                                                            [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\" pRipple>\n                                                            <span class=\"p-menuitem-icon\" *ngIf=\"item.icon\" [ngClass]=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                                                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                                                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                                                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                                                        </a>\n                                                    </li>\n                                                </ng-template>\n                                            </ul>\n                                        </ng-template>\n                                    </div>\n                                </ng-template>\n                            </div>\n                        </div>\n                    </li>\n                </ng-template>\n                <div class=\"p-megamenu-end\" *ngIf=\"endTemplate; else legacy\">\n                    <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n                </div>\n                <ng-template #legacy>\n                    <div class=\"p-megamenu-end\">\n                        <ng-content></ng-content>\n                    </div>\n                </ng-template>\n            </ul>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-megamenu-root-list{margin:0;padding:0;list-style:none}.p-megamenu-root-list>.p-menuitem{position:relative}.p-megamenu .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-megamenu .p-menuitem-text{line-height:1}.p-megamenu-panel{display:none;position:absolute;width:auto;z-index:1}.p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{display:block}.p-megamenu-submenu{margin:0;padding:0;list-style:none}.p-megamenu-submenu-header{display:flex;align-items:center}.p-megamenu-horizontal .p-megamenu-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-megamenu-vertical .p-megamenu-root-list{flex-direction:column}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem-active>.p-megamenu-panel{left:100%;top:0}.p-megamenu-vertical .p-megamenu-root-list>.p-menuitem>.p-menuitem-link>.p-submenu-icon{margin-left:auto}.p-megamenu-grid{display:flex}.p-megamenu-col-2,.p-megamenu-col-3,.p-megamenu-col-4,.p-megamenu-col-6,.p-megamenu-col-12{flex:0 0 auto;padding:.5rem}.p-megamenu-col-2{width:16.6667%}.p-megamenu-col-3{width:25%}.p-megamenu-col-4{width:33.3333%}.p-megamenu-col-6{width:50%}.p-megamenu-col-12{width:100%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass MegaMenuModule {\n}\nMegaMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nMegaMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenuModule, declarations: [MegaMenu], imports: [CommonModule, RouterModule, RippleModule, TooltipModule], exports: [MegaMenu, RouterModule, TooltipModule] });\nMegaMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenuModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: MegaMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule],\n                    exports: [MegaMenu, RouterModule, TooltipModule],\n                    declarations: [MegaMenu]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MegaMenu, MegaMenuModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,eAAvE,EAAwFC,QAAxF,QAAwG,eAAxG;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;;;;IAkH2Ff,EAK3E,sB;;;;;;IAL2EA,EAI/E,4B;IAJ+EA,EAK3E,+E;IAL2EA,EAM/E,e;;;;mBAN+EA,E;IAAAA,EAK5D,a;IAL4DA,EAK5D,qD;;;;;;;;;;;;IAL4DA,EASvE,uB;;;;wBATuEA,E;IAAAA,EASf,uBATeA,EASf,wD;;;;;;IATeA,EAc/D,yB;;;;wBAd+DA,E;IAAAA,EAcX,wC;;;;;;IAdWA,EAe/D,8B;IAf+DA,EAeyB,U;IAfzBA,EAe2C,e;;;;wBAf3CA,E;IAAAA,EAeyB,a;IAfzBA,EAeyB,qC;;;;;;IAfzBA,EAgB/B,yB;;;;wBAhB+BA,E;IAAAA,EAgBD,4CAhBCA,EAgBD,gB;;;;;;IAhBCA,EAiB/D,8B;IAjB+DA,EAiB4B,U;IAjB5BA,EAiB8C,e;;;;wBAjB9CA,E;IAAAA,EAiBT,mD;IAjBSA,EAiB4B,a;IAjB5BA,EAiB4B,qC;;;;;;;;;;;;;IAjB5BA,EAkB/D,yB;;;;oBAlB+DA,E;IAAAA,EAkBR,uBAlBQA,EAkBR,iG;;;;;;;;;;;;;iBAlBQA,E;;IAAAA,EAYnE,2B;IAZmEA,EAYyE;MAZzEA,EAYyE;MAAA,oBAZzEA,EAYyE;MAAA,gBAZzEA,EAYyE;MAAA,OAZzEA,EAYkF,0DAAT;IAAA,E;IAZzEA,EAc/D,iF;IAd+DA,EAe/D,iF;IAf+DA,EAgB/D,sGAhB+DA,EAgB/D,wB;IAhB+DA,EAiB/D,iF;IAjB+DA,EAkB/D,iF;IAlB+DA,EAmBnE,e;;;;iBAnBmEA,E;;wBAAAA,E;IAAAA,EAagC,mC;IAbhCA,EAYnC,4CAZmCA,EAYnC,yDAZmCA,EAYnC,8E;IAZmCA,EAYmB,6H;IAZnBA,EAchC,a;IAdgCA,EAchC,qC;IAdgCA,EAehC,a;IAfgCA,EAehC,mE;IAfgCA,EAiB/B,a;IAjB+BA,EAiB/B,sC;IAjB+BA,EAkBxD,a;IAlBwDA,EAkBxD,sC;;;;;;IAlBwDA,EAwB/D,yB;;;;wBAxB+DA,E;IAAAA,EAwBX,wC;;;;;;IAxBWA,EAyB/D,8B;IAzB+DA,EAyB8B,U;IAzB9BA,EAyBgD,e;;;;wBAzBhDA,E;IAAAA,EAyB8B,a;IAzB9BA,EAyB8B,qC;;;;;;IAzB9BA,EA0B1B,yB;;;;wBA1B0BA,E;IAAAA,EA0BI,4CA1BJA,EA0BI,gB;;;;;;IA1BJA,EA2B/D,8B;IA3B+DA,EA2B4B,U;IA3B5BA,EA2B8C,e;;;;wBA3B9CA,E;IAAAA,EA2BT,mD;IA3BSA,EA2B4B,a;IA3B5BA,EA2B4B,qC;;;;;;;;;;;;iBA3B5BA,E;;IAAAA,EAoBnE,2B;IApBmEA,EAsB/D;MAtB+DA,EAsB/D;MAAA,oBAtB+DA,EAsB/D;MAAA,gBAtB+DA,EAsB/D;MAAA,OAtB+DA,EAsBtD,0DAAT;IAAA,E;IAtB+DA,EAwB/D,iF;IAxB+DA,EAyB/D,iF;IAzB+DA,EA0B/D,sGA1B+DA,EA0B/D,wB;IA1B+DA,EA2B/D,iF;IA3B+DA,EA4BnE,e;;;;iBA5BmEA,E;;wBAAAA,E;IAAAA,EAsB4E,mC;IAtB5EA,EAoBpC,0MApBoCA,EAoBpC,oEApBoCA,EAoBpC,kV;IApBoCA,EAoB8J,6H;IApB9JA,EAwBhC,a;IAxBgCA,EAwBhC,qC;IAxBgCA,EAyBhC,a;IAzBgCA,EAyBhC,mE;IAzBgCA,EA2B/B,a;IA3B+BA,EA2B/B,sC;;;;;;IA3B+BA,EAoCvC,0B;IApCuCA,EAoCuB,U;IApCvBA,EAoCwC,e;;;;wBApCxCA,E;IAAAA,EAoCuB,a;IApCvBA,EAoCuB,qC;;;;;;IApCvBA,EAqCR,yB;;;;wBArCQA,E;IAAAA,EAqCF,4CArCEA,EAqCF,gB;;;;;;IArCEA,EAsCvC,8B;IAtCuCA,EAsCkD,U;IAtClDA,EAsCmE,e;;;;wBAtCnEA,E;IAAAA,EAsCc,mD;IAtCdA,EAsCkD,a;IAtClDA,EAsCkD,qC;;;;;;IAtClDA,EAyCvC,uB;;;;qBAzCuCA,E;IAAAA,EAyCa,uBAzCbA,EAyCa,qD;;;;;;IAzCbA,EA6C/B,yB;;;;qBA7C+BA,E;IAAAA,EA6CiB,oE;;;;;;IA7CjBA,EA8C/B,8B;IA9C+BA,EA8C6C,U;IA9C7CA,EA8C2D,e;;;;qBA9C3DA,E;IAAAA,EA8C6C,a;IA9C7CA,EA8C6C,kC;;;;;;IA9C7CA,EA+CP,yB;;;;qBA/COA,E;IAAAA,EA+CuB,yCA/CvBA,EA+CuB,gB;;;;;;IA/CvBA,EAgD/B,8B;IAhD+BA,EAgDoD,U;IAhDpDA,EAgDkE,e;;;;qBAhDlEA,E;IAAAA,EAgDmB,gD;IAhDnBA,EAgDoD,a;IAhDpDA,EAgDoD,kC;;;;;;;;;;;;iBAhDpDA,E;;IAAAA,EA2CnC,2B;IA3CmCA,EA4CU;MA5CVA,EA4CU;MAAA,iBA5CVA,EA4CU;MAAA,gBA5CVA,EA4CU;MAAA,OA5CVA,EA4CmB,iDAAT;IAAA,E;IA5CVA,EA6C/B,sI;IA7C+BA,EA8C/B,sI;IA9C+BA,EA+C/B,2JA/C+BA,EA+C/B,wB;IA/C+BA,EAgD/B,sI;IAhD+BA,EAiDnC,e;;;;iBAjDmCA,E;;qBAAAA,E;IAAAA,EA2CS,yCA3CTA,EA2CS,sDA3CTA,EA2CS,6C;IA3CTA,EA2C+E,iH;IA3C/EA,EA6CA,a;IA7CAA,EA6CA,kC;IA7CAA,EA8CA,a;IA9CAA,EA8CA,gE;IA9CAA,EAgDC,a;IAhDDA,EAgDC,mC;;;;;;IAhDDA,EAuD/B,yB;;;;qBAvD+BA,E;IAAAA,EAuDiB,oE;;;;;;IAvDjBA,EAwD/B,8B;IAxD+BA,EAwDkD,U;IAxDlDA,EAwDgE,e;;;;qBAxDhEA,E;IAAAA,EAwDkD,a;IAxDlDA,EAwDkD,kC;;;;;;IAxDlDA,EAyDF,yB;;;;qBAzDEA,E;IAAAA,EAyD4B,yCAzD5BA,EAyD4B,gB;;;;;;IAzD5BA,EA0D/B,8B;IA1D+BA,EA0DoD,U;IA1DpDA,EA0DkE,e;;;;qBA1DlEA,E;IAAAA,EA0DmB,gD;IA1DnBA,EA0DoD,a;IA1DpDA,EA0DoD,kC;;;;;;iBA1DpDA,E;;IAAAA,EAkDnC,2B;IAlDmCA,EAqDU;MArDVA,EAqDU;MAAA,iBArDVA,EAqDU;MAAA,gBArDVA,EAqDU;MAAA,OArDVA,EAqDmB,iDAAT;IAAA,E;IArDVA,EAuD/B,sI;IAvD+BA,EAwD/B,sI;IAxD+BA,EAyD/B,2JAzD+BA,EAyD/B,wB;IAzD+BA,EA0D/B,sI;IA1D+BA,EA2DnC,e;;;;iBA3DmCA,E;;qBAAAA,E;IAAAA,EAkDQ,iMAlDRA,EAkDQ,iEAlDRA,EAkDQ,+R;IAlDRA,EAkDsH,iH;IAlDtHA,EAuDA,a;IAvDAA,EAuDA,kC;IAvDAA,EAwDA,a;IAxDAA,EAwDA,gE;IAxDAA,EA0DC,a;IA1DDA,EA0DC,mC;;;;;;IA1DDA,EA0CvC,4B;IA1CuCA,EA2CnC,6H;IA3CmCA,EAkDnC,6H;IAlDmCA,EA4DvC,e;;;;qBA5DuCA,E;IAAAA,EA0CQ,uBA1CRA,EA0CQ,gG;IA1CRA,EA2C/B,a;IA3C+BA,EA2C/B,yC;IA3C+BA,EAkD/B,a;IAlD+BA,EAkD/B,wC;;;;;;IAlD+BA,EAyCvC,yH;IAzCuCA,EA0CvC,yH;;;;;IA1CuCA,EAyClC,uC;IAzCkCA,EA0ClC,a;IA1CkCA,EA0ClC,wC;;;;;;IA1CkCA,EAkC/C,yC;IAlC+CA,EAoCvC,+G;IApCuCA,EAqCvC,oIArCuCA,EAqCvC,wB;IArCuCA,EAsCvC,+G;IAtCuCA,EAuC3C,e;IAvC2CA,EAwC3C,4H;IAxC2CA,EA8D/C,e;;;;;;iBA9D+CA,E;;IAAAA,EAoChC,a;IApCgCA,EAoChC,mE;IApCgCA,EAsCP,a;IAtCOA,EAsCP,sC;IAtCOA,EAwCf,a;IAxCeA,EAwCf,yC;;;;;;IAxCeA,EAgCvD,yB;IAhCuDA,EAiCnD,8G;IAjCmDA,EAgEvD,e;;;;;wBAhEuDA,E;oBAAAA,E;IAAAA,EAgClD,gD;IAhCkDA,EAiCpB,a;IAjCoBA,EAiCpB,kC;;;;;;IAjCoBA,EA6BnE,2C;IA7BmEA,EA+B3D,gG;IA/B2DA,EAkE/D,iB;;;;wBAlE+DA,E;IAAAA,EA+B7B,a;IA/B6BA,EA+B7B,yC;;;;;;;;;;;;;;iBA/B6BA,E;;IAAAA,EAUvE,4B;IAVuEA,EAWnE;MAXmEA,EAWnE;MAAA,oBAXmEA,EAWnE;MAAA,gBAXmEA,EAWnE;MAAA,OAXmEA,EAWrD,+DAAd;IAAA,E;IAXmEA,EAYnE,wE;IAZmEA,EAoBnE,wE;IApBmEA,EA6BnE,2E;IA7BmEA,EAoEvE,e;;;;wBApEuEA,E;mBAAAA,E;IAAAA,EAUvC,uBAVuCA,EAUvC,wI;IAVuCA,EAY/D,a;IAZ+DA,EAY/D,4C;IAZ+DA,EAoB/D,a;IApB+DA,EAoB/D,2C;IApB+DA,EA6BpC,a;IA7BoCA,EA6BpC,sC;;;;;;IA7BoCA,EASvE,mE;IATuEA,EAUvE,mE;;;;;IAVuEA,EASlE,0C;IATkEA,EAUlE,a;IAVkEA,EAUlE,2C;;;;;;IAVkEA,EAuEvE,sB;;;;;;IAvEuEA,EAsE3E,6B;IAtE2EA,EAuEvE,+E;IAvEuEA,EAwE3E,e;;;;mBAxE2EA,E;IAAAA,EAuExD,a;IAvEwDA,EAuExD,mD;;;;;;IAvEwDA,EA0EvE,6B;IA1EuEA,EA2EnE,gB;IA3EmEA,EA4EvE,e;;;;;;;;;;;;;;AA5LpB,MAAMgB,QAAN,CAAe;EACXC,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmB;IAC1B,KAAKF,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,WAAL,GAAmB,YAAnB;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,OAAL;UACI,KAAKC,aAAL,GAAqBF,IAAI,CAACG,QAA1B;UACA;;QACJ,KAAK,KAAL;UACI,KAAKC,WAAL,GAAmBJ,IAAI,CAACG,QAAxB;UACA;MANR;IAQH,CATD;EAUH;;EACDE,oBAAoB,CAACC,KAAD,EAAQC,QAAR,EAAkB;IAClC,IAAIA,QAAQ,CAACC,QAAb,EAAuB;MACnBF,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,KAAKC,UAAT,EAAqB;MACjB,KAAKA,UAAL,GAAkBH,QAAlB;IACH;EACJ;;EACDI,eAAe,CAACL,KAAD,EAAQN,IAAR,EAAc;IACzB,IAAIA,IAAI,CAACQ,QAAT,EAAmB;MACfF,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,CAACT,IAAI,CAACY,GAAN,IAAa,CAACZ,IAAI,CAACa,UAAvB,EAAmC;MAC/BP,KAAK,CAACG,cAAN;IACH;;IACD,IAAIT,IAAI,CAACc,OAAT,EAAkB;MACdd,IAAI,CAACc,OAAL,CAAa;QACTC,aAAa,EAAET,KADN;QAETN,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,IAAIA,IAAI,CAACgB,KAAT,EAAgB;MACZ,IAAI,KAAKN,UAAL,IAAmB,KAAKA,UAAL,KAAoBV,IAA3C,EAAiD;QAC7C,KAAKU,UAAL,GAAkB,IAAlB;QACA,KAAKO,2BAAL;MACH,CAHD,MAIK;QACD,KAAKP,UAAL,GAAkBV,IAAlB;QACA,KAAKkB,yBAAL;MACH;IACJ;EACJ;;EACDC,SAAS,CAACb,KAAD,EAAQN,IAAR,EAAc;IACnB,IAAIA,IAAI,CAACQ,QAAT,EAAmB;MACfF,KAAK,CAACG,cAAN;MACA;IACH;;IACD,IAAI,CAACT,IAAI,CAACY,GAAN,IAAa,CAACZ,IAAI,CAACa,UAAvB,EAAmC;MAC/BP,KAAK,CAACG,cAAN;IACH;;IACD,IAAIT,IAAI,CAACc,OAAT,EAAkB;MACdd,IAAI,CAACc,OAAL,CAAa;QACTC,aAAa,EAAET,KADN;QAETN,IAAI,EAAEA;MAFG,CAAb;IAIH;;IACD,KAAKU,UAAL,GAAkB,IAAlB;EACH;;EACDU,cAAc,CAACb,QAAD,EAAW;IACrB,IAAIc,MAAM,GAAGd,QAAQ,CAACS,KAAT,GAAiBT,QAAQ,CAACS,KAAT,CAAeK,MAAhC,GAAyC,CAAtD;IACA,IAAIC,WAAJ;;IACA,QAAQD,MAAR;MACI,KAAK,CAAL;QACIC,WAAW,GAAG,kBAAd;QACA;;MACJ,KAAK,CAAL;QACIA,WAAW,GAAG,kBAAd;QACA;;MACJ,KAAK,CAAL;QACIA,WAAW,GAAG,kBAAd;QACA;;MACJ,KAAK,CAAL;QACIA,WAAW,GAAG,kBAAd;QACA;;MACJ;QACIA,WAAW,GAAG,mBAAd;QACA;IAfR;;IAiBA,OAAOA,WAAP;EACH;;EACDJ,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAKK,qBAAV,EAAiC;MAC7B,KAAKA,qBAAL,GAA8BjB,KAAD,IAAW;QACpC,IAAI,KAAKf,EAAL,IAAW,CAAC,KAAKA,EAAL,CAAQiC,aAAR,CAAsBC,QAAtB,CAA+BnB,KAAK,CAACoB,MAArC,CAAhB,EAA8D;UAC1D,KAAKhB,UAAL,GAAkB,IAAlB;UACA,KAAKO,2BAAL;UACA,KAAKxB,EAAL,CAAQkC,YAAR;QACH;MACJ,CAND;;MAOAC,QAAQ,CAACC,gBAAT,CAA0B,OAA1B,EAAmC,KAAKN,qBAAxC;IACH;EACJ;;EACDN,2BAA2B,GAAG;IAC1B,IAAI,KAAKM,qBAAT,EAAgC;MAC5BK,QAAQ,CAACE,mBAAT,CAA6B,OAA7B,EAAsC,KAAKP,qBAA3C;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;AA9GU;;AAgHflC,QAAQ,CAAC0C,IAAT;EAAA,iBAAqG1C,QAArG,EAA2FhB,EAA3F,mBAA+HA,EAAE,CAAC2D,UAAlI,GAA2F3D,EAA3F,mBAAyJA,EAAE,CAAC4D,SAA5J,GAA2F5D,EAA3F,mBAAkLA,EAAE,CAAC6D,iBAArL;AAAA;;AACA7C,QAAQ,CAAC8C,IAAT,kBAD2F9D,EAC3F;EAAA,MAAyFgB,QAAzF;EAAA;EAAA;IAAA;MAD2FhB,EAC3F,0BAA0WS,aAA1W;IAAA;;IAAA;MAAA;;MAD2FT,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;MAD2FA,EAEnF,4BADR;MAD2FA,EAI/E,uDAHZ;MAD2FA,EAO/E,2BANZ;MAD2FA,EAQ3E,uEAPhB;MAD2FA,EAsE3E,uDArEhB;MAD2FA,EAyE3E,8EAzE2EA,EAyE3E,wBAxEhB;MAD2FA,EA8E/E,iBA7EZ;IAAA;;IAAA;MAAA,YAD2FA,EAC3F;;MAD2FA,EAE9E,2BADb;MAD2FA,EAEzD,6CAFyDA,EAEzD,yFADlC;MAD2FA,EAIhD,aAH3C;MAD2FA,EAIhD,sCAH3C;MAD2FA,EAQ3C,aAPhD;MAD2FA,EAQ3C,iCAPhD;MAD2FA,EAsE9C,aArE7C;MAD2FA,EAsE9C,qDArE7C;IAAA;EAAA;EAAA,eA+EgwCO,EAAE,CAACwD,OA/EnwC,EA+E81CxD,EAAE,CAACyD,OA/Ej2C,EA+E29CzD,EAAE,CAAC0D,IA/E99C,EA+E+jD1D,EAAE,CAAC2D,gBA/ElkD,EA+EsuD3D,EAAE,CAAC4D,OA/EzuD,EA+E2zDzD,EAAE,CAAC0D,kBA/E9zD,EA+E8jE1D,EAAE,CAAC2D,gBA/EjkE,EA+E+xEzD,EAAE,CAAC0D,MA/ElyE,EA+E81ExD,EAAE,CAACyD,OA/Ej2E;EAAA;EAAA;EAAA;AAAA;;AAgFA;EAAA,mDAjF2FvE,EAiF3F,mBAA2FgB,QAA3F,EAAiH,CAAC;IACtGwD,IAAI,EAAEvE,SADgG;IAEtGwE,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0B5C,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA/EmB;MA+EZ6C,eAAe,EAAEzE,uBAAuB,CAAC0E,MA/E7B;MA+EqCC,aAAa,EAAE1E,iBAAiB,CAAC2E,IA/EtE;MA+E4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CA/ElF;MAiFIC,MAAM,EAAE,CAAC,mrCAAD;IAjFZ,CAAD;EAFgG,CAAD,CAAjH,EAoF4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAExE,EAAE,CAAC2D;IAAX,CAAD,EAA0B;MAAEa,IAAI,EAAExE,EAAE,CAAC4D;IAAX,CAA1B,EAAkD;MAAEY,IAAI,EAAExE,EAAE,CAAC6D;IAAX,CAAlD,CAAP;EAA2F,CApFrI,EAoFuJ;IAAEoB,KAAK,EAAE,CAAC;MACjJT,IAAI,EAAEpE;IAD2I,CAAD,CAAT;IAEvI8E,KAAK,EAAE,CAAC;MACRV,IAAI,EAAEpE;IADE,CAAD,CAFgI;IAIvI+E,UAAU,EAAE,CAAC;MACbX,IAAI,EAAEpE;IADO,CAAD,CAJ2H;IAMvIiB,WAAW,EAAE,CAAC;MACdmD,IAAI,EAAEpE;IADQ,CAAD,CAN0H;IAQvIkB,UAAU,EAAE,CAAC;MACbkD,IAAI,EAAEpE;IADO,CAAD,CAR2H;IAUvImB,UAAU,EAAE,CAAC;MACbiD,IAAI,EAAEpE;IADO,CAAD,CAV2H;IAYvIqB,SAAS,EAAE,CAAC;MACZ+C,IAAI,EAAEnE,eADM;MAEZoE,IAAI,EAAE,CAAChE,aAAD;IAFM,CAAD;EAZ4H,CApFvJ;AAAA;;AAoGA,MAAM2E,cAAN,CAAqB;;AAErBA,cAAc,CAAC1B,IAAf;EAAA,iBAA2G0B,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAxL2FrF,EAwL3F;EAAA,MAA4GoF;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAzL2FtF,EAyL3F;EAAA,UAAsIQ,YAAtI,EAAoJG,YAApJ,EAAkKE,YAAlK,EAAgLE,aAAhL,EAA+LJ,YAA/L,EAA6MI,aAA7M;AAAA;;AACA;EAAA,mDA1L2Ff,EA0L3F,mBAA2FoF,cAA3F,EAAuH,CAAC;IAC5GZ,IAAI,EAAElE,QADsG;IAE5GmE,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAAC/E,YAAD,EAAeG,YAAf,EAA6BE,YAA7B,EAA2CE,aAA3C,CADV;MAECyE,OAAO,EAAE,CAACxE,QAAD,EAAWL,YAAX,EAAyBI,aAAzB,CAFV;MAGC0E,YAAY,EAAE,CAACzE,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmBoE,cAAnB"}, "metadata": {}, "sourceType": "module"}