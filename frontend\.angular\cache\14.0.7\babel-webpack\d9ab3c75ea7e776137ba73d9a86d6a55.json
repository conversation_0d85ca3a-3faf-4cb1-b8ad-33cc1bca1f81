{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\n\nfunction PanelMenuSub_ng_template_1_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 4);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    \"pi-angle-right\": a0,\n    \"pi-angle-down\": a1\n  };\n};\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, !child_r1.expanded, child_r1.expanded))(\"ngStyle\", child_r1.iconStyle);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.icon)(\"ngStyle\", child_r1.iconStyle);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.label);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.badge);\n  }\n}\n\nconst _c1 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\n\nfunction PanelMenuSub_ng_template_1_li_1_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 9);\n    i0.ɵɵlistener(\"keydown\", function PanelMenuSub_ng_template_1_li_1_a_1_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.onItemKeyDown($event));\n    })(\"click\", function PanelMenuSub_ng_template_1_li_1_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.handleClick($event, child_r1));\n    });\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_1_li_1_a_1_span_1_Template, 1, 5, \"span\", 10);\n    i0.ɵɵtemplate(2, PanelMenuSub_ng_template_1_li_1_a_1_span_2_Template, 1, 2, \"span\", 11);\n    i0.ɵɵtemplate(3, PanelMenuSub_ng_template_1_li_1_a_1_span_3_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(4, PanelMenuSub_ng_template_1_li_1_a_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, PanelMenuSub_ng_template_1_li_1_a_1_span_6_Template, 2, 2, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r10 = i0.ɵɵreference(5);\n\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c1, child_r1.disabled))(\"target\", child_r1.target);\n    i0.ɵɵattribute(\"href\", child_r1.url, i0.ɵɵsanitizeUrl)(\"tabindex\", !ctx_r4.item.expanded || !ctx_r4.parentExpanded ? null : child_r1.disabled ? null : \"0\")(\"id\", child_r1.id)(\"aria-expanded\", child_r1.expanded)(\"title\", child_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.escape !== false)(\"ngIfElse\", _r10);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r1.badge);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c0, !child_r1.expanded, child_r1.expanded))(\"ngStyle\", child_r1.iconStyle);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.icon)(\"ngStyle\", child_r1.iconStyle);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.label);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 18);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", child_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", child_r1.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(child_r1.badge);\n  }\n}\n\nconst _c2 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction PanelMenuSub_ng_template_1_li_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 20);\n    i0.ɵɵlistener(\"keydown\", function PanelMenuSub_ng_template_1_li_1_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r34.onItemKeyDown($event));\n    })(\"click\", function PanelMenuSub_ng_template_1_li_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r35);\n      const child_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.handleClick($event, child_r1));\n    });\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_1_li_1_a_2_span_1_Template, 1, 5, \"span\", 10);\n    i0.ɵɵtemplate(2, PanelMenuSub_ng_template_1_li_1_a_2_span_2_Template, 1, 2, \"span\", 11);\n    i0.ɵɵtemplate(3, PanelMenuSub_ng_template_1_li_1_a_2_span_3_Template, 2, 1, \"span\", 12);\n    i0.ɵɵtemplate(4, PanelMenuSub_ng_template_1_li_1_a_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 21, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, PanelMenuSub_ng_template_1_li_1_a_2_span_6_Template, 2, 2, \"span\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r26 = i0.ɵɵreference(5);\n\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", child_r1.routerLink)(\"queryParams\", child_r1.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", child_r1.routerLinkActiveOptions || i0.ɵɵpureFunction0(21, _c2))(\"ngClass\", i0.ɵɵpureFunction1(22, _c1, child_r1.disabled))(\"target\", child_r1.target)(\"fragment\", child_r1.fragment)(\"queryParamsHandling\", child_r1.queryParamsHandling)(\"preserveFragment\", child_r1.preserveFragment)(\"skipLocationChange\", child_r1.skipLocationChange)(\"replaceUrl\", child_r1.replaceUrl)(\"state\", child_r1.state);\n    i0.ɵɵattribute(\"tabindex\", !ctx_r5.item.expanded || !ctx_r5.parentExpanded ? null : child_r1.disabled ? null : \"0\")(\"id\", child_r1.id)(\"aria-expanded\", child_r1.expanded)(\"title\", child_r1.title);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.escape !== false)(\"ngIfElse\", _r26);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", child_r1.badge);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_p_panelMenuSub_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-panelMenuSub\", 22);\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", child_r1)(\"parentExpanded\", ctx_r6.expanded && ctx_r6.parentExpanded)(\"expanded\", child_r1.expanded)(\"transitionOptions\", ctx_r6.transitionOptions);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 5);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_1_li_1_a_1_Template, 7, 14, \"a\", 6);\n    i0.ɵɵtemplate(2, PanelMenuSub_ng_template_1_li_1_a_2_Template, 7, 24, \"a\", 7);\n    i0.ɵɵtemplate(3, PanelMenuSub_ng_template_1_li_1_p_panelMenuSub_3_Template, 1, 4, \"p-panelMenuSub\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const child_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"p-hidden\", child_r1.visible === false);\n    i0.ɵɵproperty(\"ngClass\", child_r1.styleClass)(\"ngStyle\", child_r1.style)(\"tooltipOptions\", child_r1.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !child_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", child_r1.items);\n  }\n}\n\nfunction PanelMenuSub_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_1_li_0_Template, 1, 0, \"li\", 2);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_1_li_1_Template, 4, 8, \"li\", 3);\n  }\n\n  if (rf & 2) {\n    const child_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", child_r1.separator);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !child_r1.separator);\n  }\n}\n\nconst _c3 = function (a1, a2) {\n  return {\n    \"p-submenu-list\": true,\n    \"p-panelmenu-root-submenu\": a1,\n    \"p-submenu-expanded\": a2\n  };\n};\n\nconst _c4 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"*\"\n  };\n};\n\nconst _c5 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nconst _c6 = function (a0) {\n  return {\n    transitionParams: a0,\n    height: \"0\"\n  };\n};\n\nconst _c7 = function (a1) {\n  return {\n    value: \"hidden\",\n    params: a1\n  };\n};\n\nconst _c8 = function (a0, a1) {\n  return {\n    \"pi-chevron-right\": a0,\n    \"pi-chevron-down\": a1\n  };\n};\n\nfunction PanelMenu_ng_container_1_div_1_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c8, !item_r1.expanded, item_r1.expanded));\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r1.icon)(\"ngStyle\", item_r1.iconStyle);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r1.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.badge);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 8);\n    i0.ɵɵlistener(\"click\", function PanelMenu_ng_container_1_div_1_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const item_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.handleClick($event, item_r1));\n    })(\"keydown\", function PanelMenu_ng_container_1_div_1_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r22.onItemKeyDown($event));\n    });\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_1_div_1_a_2_span_1_Template, 1, 4, \"span\", 9);\n    i0.ɵɵtemplate(2, PanelMenu_ng_container_1_div_1_a_2_span_2_Template, 1, 2, \"span\", 10);\n    i0.ɵɵtemplate(3, PanelMenu_ng_container_1_div_1_a_2_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(4, PanelMenu_ng_container_1_div_1_a_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 12, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, PanelMenu_ng_container_1_div_1_a_2_span_6_Template, 2, 2, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r11 = i0.ɵɵreference(5);\n\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"target\", item_r1.target);\n    i0.ɵɵattribute(\"href\", item_r1.url, i0.ɵɵsanitizeUrl)(\"tabindex\", item_r1.disabled ? null : \"0\")(\"id\", item_r1.id)(\"title\", item_r1.title)(\"aria-expanded\", item_r1.expanded)(\"id\", item_r1.id + \"_header\")(\"aria-controls\", item_r1.id + \"_content\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.escape !== false)(\"ngIfElse\", _r11);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r1.badge);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c8, !item_r1.expanded, item_r1.expanded));\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 15);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r1.icon)(\"ngStyle\", item_r1.iconStyle);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 16);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 17);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"ngClass\", item_r1.badgeStyleClass);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.badge);\n  }\n}\n\nfunction PanelMenu_ng_container_1_div_1_a_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 19);\n    i0.ɵɵlistener(\"click\", function PanelMenu_ng_container_1_div_1_a_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const item_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r35 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r35.handleClick($event, item_r1));\n    })(\"keydown\", function PanelMenu_ng_container_1_div_1_a_3_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r38.onItemKeyDown($event));\n    });\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_1_div_1_a_3_span_1_Template, 1, 4, \"span\", 9);\n    i0.ɵɵtemplate(2, PanelMenu_ng_container_1_div_1_a_3_span_2_Template, 1, 2, \"span\", 10);\n    i0.ɵɵtemplate(3, PanelMenu_ng_container_1_div_1_a_3_span_3_Template, 2, 1, \"span\", 11);\n    i0.ɵɵtemplate(4, PanelMenu_ng_container_1_div_1_a_3_ng_template_4_Template, 1, 1, \"ng-template\", null, 20, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, PanelMenu_ng_container_1_div_1_a_3_span_6_Template, 2, 2, \"span\", 13);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r27 = i0.ɵɵreference(5);\n\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r1.routerLink)(\"queryParams\", item_r1.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r1.routerLinkActiveOptions || i0.ɵɵpureFunction0(19, _c2))(\"target\", item_r1.target)(\"fragment\", item_r1.fragment)(\"queryParamsHandling\", item_r1.queryParamsHandling)(\"preserveFragment\", item_r1.preserveFragment)(\"skipLocationChange\", item_r1.skipLocationChange)(\"replaceUrl\", item_r1.replaceUrl)(\"state\", item_r1.state);\n    i0.ɵɵattribute(\"title\", item_r1.title)(\"id\", item_r1.id)(\"tabindex\", item_r1.disabled ? null : \"0\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.items);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.escape !== false)(\"ngIfElse\", _r27);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", item_r1.badge);\n  }\n}\n\nconst _c9 = function (a0) {\n  return {\n    \"p-panelmenu-expanded\": a0\n  };\n};\n\nfunction PanelMenu_ng_container_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵlistener(\"@rootItem.done\", function PanelMenu_ng_container_1_div_1_div_4_Template_div_animation_rootItem_done_0_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r40.onToggleDone());\n    });\n    i0.ɵɵelementStart(1, \"div\", 22);\n    i0.ɵɵelement(2, \"p-panelMenuSub\", 23);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c9, item_r1.expanded))(\"@rootItem\", item_r1.expanded ? i0.ɵɵpureFunction1(13, _c5, i0.ɵɵpureFunction1(11, _c4, ctx_r7.animating ? ctx_r7.transitionOptions : \"0ms\")) : i0.ɵɵpureFunction1(17, _c7, i0.ɵɵpureFunction1(15, _c6, ctx_r7.transitionOptions)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"id\", item_r1.id + \"_content\")(\"aria-labelledby\", item_r1.id + \"_header\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", item_r1)(\"parentExpanded\", item_r1.expanded)(\"expanded\", true)(\"transitionOptions\", ctx_r7.transitionOptions)(\"root\", true);\n  }\n}\n\nconst _c10 = function (a1, a2) {\n  return {\n    \"p-component p-panelmenu-header\": true,\n    \"p-highlight\": a1,\n    \"p-disabled\": a2\n  };\n};\n\nfunction PanelMenu_ng_container_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, PanelMenu_ng_container_1_div_1_a_2_Template, 7, 13, \"a\", 5);\n    i0.ɵɵtemplate(3, PanelMenu_ng_container_1_div_1_a_3_Template, 7, 20, \"a\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, PanelMenu_ng_container_1_div_1_div_4_Template, 3, 19, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(item_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c10, item_r1.expanded, item_r1.disabled))(\"ngStyle\", item_r1.style)(\"tooltipOptions\", item_r1.tooltipOptions);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !item_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.routerLink);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.items);\n  }\n}\n\nfunction PanelMenu_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_1_div_1_Template, 5, 11, \"div\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.visible(item_r1));\n  }\n}\n\nclass BasePanelMenuItem {\n  constructor(ref) {\n    this.ref = ref;\n  }\n\n  handleClick(event, item) {\n    if (item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    item.expanded = !item.expanded;\n    this.ref.detectChanges();\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item\n      });\n    }\n  }\n\n}\n\nclass PanelMenuSub extends BasePanelMenuItem {\n  constructor(ref) {\n    super(ref);\n  }\n\n  onItemKeyDown(event) {\n    let listItem = event.currentTarget;\n\n    switch (event.code) {\n      case 'Space':\n      case 'Enter':\n        if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n          listItem.click();\n        }\n\n        event.preventDefault();\n        break;\n\n      default:\n        break;\n    }\n  }\n\n}\n\nPanelMenuSub.ɵfac = function PanelMenuSub_Factory(t) {\n  return new (t || PanelMenuSub)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nPanelMenuSub.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PanelMenuSub,\n  selectors: [[\"p-panelMenuSub\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    item: \"item\",\n    expanded: \"expanded\",\n    parentExpanded: \"parentExpanded\",\n    transitionOptions: \"transitionOptions\",\n    root: \"root\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 2,\n  vars: 14,\n  consts: [[\"role\", \"tree\", 3, \"ngClass\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menu-separator\", \"role\", \"separator\", 4, \"ngIf\"], [\"class\", \"p-menuitem\", \"pTooltip\", \"\", 3, \"ngClass\", \"p-hidden\", \"ngStyle\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menu-separator\"], [\"pTooltip\", \"\", 1, \"p-menuitem\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"class\", \"p-menuitem-link\", \"role\", \"treeitem\", 3, \"ngClass\", \"target\", \"keydown\", \"click\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", \"role\", \"treeitem\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\", 4, \"ngIf\"], [3, \"item\", \"parentExpanded\", \"expanded\", \"transitionOptions\", 4, \"ngIf\"], [\"role\", \"treeitem\", 1, \"p-menuitem-link\", 3, \"ngClass\", \"target\", \"keydown\", \"click\"], [\"class\", \"p-panelmenu-icon pi pi-fw\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-panelmenu-icon\", \"pi\", \"pi-fw\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [\"role\", \"treeitem\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"keydown\", \"click\"], [\"htmlRouteLabel\", \"\"], [3, \"item\", \"parentExpanded\", \"expanded\", \"transitionOptions\"]],\n  template: function PanelMenuSub_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"ul\", 0);\n      i0.ɵɵtemplate(1, PanelMenuSub_ng_template_1_Template, 2, 2, \"ng-template\", 1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c3, ctx.root, ctx.expanded))(\"@submenu\", ctx.expanded ? i0.ɵɵpureFunction1(8, _c5, i0.ɵɵpureFunction1(6, _c4, ctx.transitionOptions)) : i0.ɵɵpureFunction1(12, _c7, i0.ɵɵpureFunction1(10, _c6, ctx.transitionOptions)));\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.item.items);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Tooltip, PanelMenuSub],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('submenu', [state('hidden', style({\n      height: '0'\n    })), state('visible', style({\n      height: '*'\n    })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n  }\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenuSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-panelMenuSub',\n      template: `\n        <ul [ngClass]=\"{'p-submenu-list': true, 'p-panelmenu-root-submenu': root, 'p-submenu-expanded': expanded}\" [@submenu]=\"expanded ? {value: 'visible', params: {transitionParams: transitionOptions, height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\" role=\"tree\">\n            <ng-template ngFor let-child [ngForOf]=\"item.items\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" class=\"p-menuitem\" [ngClass]=\"child.styleClass\" [class.p-hidden]=\"child.visible === false\" [ngStyle]=\"child.style\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"child.url\" class=\"p-menuitem-link\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event)\"  [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <p-panelMenuSub [item]=\"child\" [parentExpanded]=\"expanded && parentExpanded\" [expanded]=\"child.expanded\" [transitionOptions]=\"transitionOptions\" *ngIf=\"child.items\"></p-panelMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      animations: [trigger('submenu', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    item: [{\n      type: Input\n    }],\n    expanded: [{\n      type: Input\n    }],\n    parentExpanded: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }]\n  });\n})();\n\nclass PanelMenu extends BasePanelMenuItem {\n  constructor(ref) {\n    super(ref);\n    this.multiple = true;\n    this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n  }\n\n  collapseAll() {\n    for (let item of this.model) {\n      if (item.expanded) {\n        item.expanded = false;\n      }\n    }\n  }\n\n  handleClick(event, item) {\n    if (!this.multiple) {\n      for (let modelItem of this.model) {\n        if (item !== modelItem && modelItem.expanded) {\n          modelItem.expanded = false;\n        }\n      }\n    }\n\n    this.animating = true;\n    super.handleClick(event, item);\n  }\n\n  onToggleDone() {\n    this.animating = false;\n  }\n\n  onItemKeyDown(event) {\n    let listItem = event.currentTarget;\n\n    switch (event.code) {\n      case 'Space':\n      case 'Enter':\n        if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n          listItem.click();\n        }\n\n        event.preventDefault();\n        break;\n\n      default:\n        break;\n    }\n  }\n\n  visible(item) {\n    return item.visible !== false;\n  }\n\n}\n\nPanelMenu.ɵfac = function PanelMenu_Factory(t) {\n  return new (t || PanelMenu)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nPanelMenu.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: PanelMenu,\n  selectors: [[\"p-panelMenu\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    model: \"model\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    multiple: \"multiple\",\n    transitionOptions: \"transitionOptions\"\n  },\n  features: [i0.ɵɵInheritDefinitionFeature],\n  decls: 2,\n  vars: 5,\n  consts: [[3, \"ngStyle\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-panelmenu-panel\", 4, \"ngIf\"], [1, \"p-panelmenu-panel\"], [\"pTooltip\", \"\", 3, \"ngClass\", \"ngStyle\", \"tooltipOptions\"], [\"class\", \"p-panelmenu-header-link\", 3, \"target\", \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-panelmenu-header-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-toggleable-content\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-panelmenu-header-link\", 3, \"target\", \"click\", \"keydown\"], [\"class\", \"p-panelmenu-icon pi\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-panelmenu-icon\", \"pi\", 3, \"ngClass\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-panelmenu-header-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown\"], [\"htmlRouteLabel\", \"\"], [1, \"p-toggleable-content\", 3, \"ngClass\"], [\"role\", \"region\", 1, \"p-panelmenu-content\"], [3, \"item\", \"parentExpanded\", \"expanded\", \"transitionOptions\", \"root\"]],\n  template: function PanelMenu_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵtemplate(1, PanelMenu_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-panelmenu p-component\");\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngForOf\", ctx.model);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgStyle, i2.RouterLinkWithHref, i2.RouterLinkActive, i3.Tooltip, PanelMenuSub],\n  styles: [\".p-panelmenu .p-panelmenu-header-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-link:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('rootItem', [state('hidden', style({\n      height: '0'\n    })), state('visible', style({\n      height: '*'\n    })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenu, [{\n    type: Component,\n    args: [{\n      selector: 'p-panelMenu',\n      template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-panelmenu p-component'\">\n            <ng-container *ngFor=\"let item of model;let f=first;let l=last;\">\n                <div class=\"p-panelmenu-panel\" *ngIf=\"visible(item)\">\n                    <div [ngClass]=\"{'p-component p-panelmenu-header':true, 'p-highlight':item.expanded,'p-disabled':item.disabled}\" [class]=\"item.styleClass\" [ngStyle]=\"item.style\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url\" (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.id]=\"item.id\"\n                           [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.aria-expanded]=\"item.expanded\" [attr.id]=\"item.id + '_header'\" [attr.aria-controls]=\"item.id +'_content'\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\"\n                           (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                           [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                    </div>\n                    <div *ngIf=\"item.items\" class=\"p-toggleable-content\" [ngClass]=\"{'p-panelmenu-expanded': item.expanded}\"  [@rootItem]=\"item.expanded ? {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\"  (@rootItem.done)=\"onToggleDone()\">\n                        <div class=\"p-panelmenu-content\" role=\"region\" [attr.id]=\"item.id +'_content' \" [attr.aria-labelledby]=\"item.id +'_header'\">\n                            <p-panelMenuSub [item]=\"item\" [parentExpanded]=\"item.expanded\" [expanded]=\"true\" [transitionOptions]=\"transitionOptions\" [root]=\"true\"></p-panelMenuSub>\n                        </div>\n                    </div>\n                </div>\n            </ng-container>\n        </div>\n    `,\n      animations: [trigger('rootItem', [state('hidden', style({\n        height: '0'\n      })), state('visible', style({\n        height: '*'\n      })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-panelmenu .p-panelmenu-header-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-link:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    multiple: [{\n      type: Input\n    }],\n    transitionOptions: [{\n      type: Input\n    }]\n  });\n})();\n\nclass PanelMenuModule {}\n\nPanelMenuModule.ɵfac = function PanelMenuModule_Factory(t) {\n  return new (t || PanelMenuModule)();\n};\n\nPanelMenuModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PanelMenuModule\n});\nPanelMenuModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PanelMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, TooltipModule],\n      exports: [PanelMenu, RouterModule, TooltipModule],\n      declarations: [PanelMenu, PanelMenuSub]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { BasePanelMenuItem, PanelMenu, PanelMenuModule, PanelMenuSub };", "map": {"version": 3, "names": ["i0", "Component", "ViewEncapsulation", "Input", "ChangeDetectionStrategy", "NgModule", "trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "i2", "RouterModule", "i3", "TooltipModule", "<PERSON><PERSON><PERSON><PERSON>", "BasePanelMenuItem", "constructor", "ref", "handleClick", "event", "item", "disabled", "preventDefault", "expanded", "detectChanges", "url", "routerLink", "command", "originalEvent", "PanelMenuSub", "onItemKeyDown", "listItem", "currentTarget", "code", "hasClass", "click", "ɵfac", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "height", "type", "args", "selector", "template", "animations", "encapsulation", "None", "host", "parentExpanded", "transitionOptions", "root", "PanelMenu", "multiple", "collapseAll", "model", "modelItem", "animating", "onToggleDone", "visible", "changeDetection", "OnPush", "styles", "styleClass", "PanelMenuModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-panelmenu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Input, ChangeDetectionStrategy, NgModule } from '@angular/core';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { DomHandler } from 'primeng/dom';\n\nclass BasePanelMenuItem {\n    constructor(ref) {\n        this.ref = ref;\n    }\n    handleClick(event, item) {\n        if (item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        item.expanded = !item.expanded;\n        this.ref.detectChanges();\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item\n            });\n        }\n    }\n}\nclass PanelMenuSub extends BasePanelMenuItem {\n    constructor(ref) {\n        super(ref);\n    }\n    onItemKeyDown(event) {\n        let listItem = event.currentTarget;\n        switch (event.code) {\n            case 'Space':\n            case 'Enter':\n                if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n                    listItem.click();\n                }\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n}\nPanelMenuSub.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuSub, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nPanelMenuSub.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: PanelMenuSub, selector: \"p-panelMenuSub\", inputs: { item: \"item\", expanded: \"expanded\", parentExpanded: \"parentExpanded\", transitionOptions: \"transitionOptions\", root: \"root\" }, host: { classAttribute: \"p-element\" }, usesInheritance: true, ngImport: i0, template: `\n        <ul [ngClass]=\"{'p-submenu-list': true, 'p-panelmenu-root-submenu': root, 'p-submenu-expanded': expanded}\" [@submenu]=\"expanded ? {value: 'visible', params: {transitionParams: transitionOptions, height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\" role=\"tree\">\n            <ng-template ngFor let-child [ngForOf]=\"item.items\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" class=\"p-menuitem\" [ngClass]=\"child.styleClass\" [class.p-hidden]=\"child.visible === false\" [ngStyle]=\"child.style\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"child.url\" class=\"p-menuitem-link\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event)\"  [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <p-panelMenuSub [item]=\"child\" [parentExpanded]=\"expanded && parentExpanded\" [expanded]=\"child.expanded\" [transitionOptions]=\"transitionOptions\" *ngIf=\"child.items\"></p-panelMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: PanelMenuSub, selector: \"p-panelMenuSub\", inputs: [\"item\", \"expanded\", \"parentExpanded\", \"transitionOptions\", \"root\"] }], animations: [\n        trigger('submenu', [\n            state('hidden', style({\n                height: '0'\n            })),\n            state('visible', style({\n                height: '*'\n            })),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ], encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-panelMenuSub',\n                    template: `\n        <ul [ngClass]=\"{'p-submenu-list': true, 'p-panelmenu-root-submenu': root, 'p-submenu-expanded': expanded}\" [@submenu]=\"expanded ? {value: 'visible', params: {transitionParams: transitionOptions, height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\" role=\"tree\">\n            <ng-template ngFor let-child [ngForOf]=\"item.items\">\n                <li *ngIf=\"child.separator\" class=\"p-menu-separator\" role=\"separator\">\n                <li *ngIf=\"!child.separator\" class=\"p-menuitem\" [ngClass]=\"child.styleClass\" [class.p-hidden]=\"child.visible === false\" [ngStyle]=\"child.style\" pTooltip [tooltipOptions]=\"child.tooltipOptions\">\n                    <a *ngIf=\"!child.routerLink\" (keydown)=\"onItemKeyDown($event)\" [attr.href]=\"child.url\" class=\"p-menuitem-link\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlLabel\">{{child.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <a *ngIf=\"child.routerLink\" (keydown)=\"onItemKeyDown($event)\"  [routerLink]=\"child.routerLink\" [queryParams]=\"child.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"child.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        [ngClass]=\"{'p-disabled':child.disabled}\" [attr.tabindex]=\"!item.expanded || !parentExpanded ? null : child.disabled ? null : '0'\" [attr.id]=\"child.id\" role=\"treeitem\" [attr.aria-expanded]=\"child.expanded\"\n                        (click)=\"handleClick($event,child)\" [target]=\"child.target\" [attr.title]=\"child.title\"\n                        [fragment]=\"child.fragment\" [queryParamsHandling]=\"child.queryParamsHandling\" [preserveFragment]=\"child.preserveFragment\" [skipLocationChange]=\"child.skipLocationChange\" [replaceUrl]=\"child.replaceUrl\" [state]=\"child.state\">\n                        <span class=\"p-panelmenu-icon pi pi-fw\" [ngClass]=\"{'pi-angle-right':!child.expanded,'pi-angle-down':child.expanded}\" *ngIf=\"child.items\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-icon\" [ngClass]=\"child.icon\" *ngIf=\"child.icon\" [ngStyle]=\"child.iconStyle\"></span>\n                        <span class=\"p-menuitem-text\" *ngIf=\"child.escape !== false; else htmlRouteLabel\">{{child.label}}</span>\n                        <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"child.label\"></span></ng-template>\n                        <span class=\"p-menuitem-badge\" *ngIf=\"child.badge\" [ngClass]=\"child.badgeStyleClass\">{{child.badge}}</span>\n                    </a>\n                    <p-panelMenuSub [item]=\"child\" [parentExpanded]=\"expanded && parentExpanded\" [expanded]=\"child.expanded\" [transitionOptions]=\"transitionOptions\" *ngIf=\"child.items\"></p-panelMenuSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    animations: [\n                        trigger('submenu', [\n                            state('hidden', style({\n                                height: '0'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ],\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; }, propDecorators: { item: [{\n                type: Input\n            }], expanded: [{\n                type: Input\n            }], parentExpanded: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }] } });\nclass PanelMenu extends BasePanelMenuItem {\n    constructor(ref) {\n        super(ref);\n        this.multiple = true;\n        this.transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    }\n    collapseAll() {\n        for (let item of this.model) {\n            if (item.expanded) {\n                item.expanded = false;\n            }\n        }\n    }\n    handleClick(event, item) {\n        if (!this.multiple) {\n            for (let modelItem of this.model) {\n                if (item !== modelItem && modelItem.expanded) {\n                    modelItem.expanded = false;\n                }\n            }\n        }\n        this.animating = true;\n        super.handleClick(event, item);\n    }\n    onToggleDone() {\n        this.animating = false;\n    }\n    onItemKeyDown(event) {\n        let listItem = event.currentTarget;\n        switch (event.code) {\n            case 'Space':\n            case 'Enter':\n                if (listItem && !DomHandler.hasClass(listItem, 'p-disabled')) {\n                    listItem.click();\n                }\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n    visible(item) {\n        return item.visible !== false;\n    }\n}\nPanelMenu.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenu, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nPanelMenu.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: PanelMenu, selector: \"p-panelMenu\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", multiple: \"multiple\", transitionOptions: \"transitionOptions\" }, host: { classAttribute: \"p-element\" }, usesInheritance: true, ngImport: i0, template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-panelmenu p-component'\">\n            <ng-container *ngFor=\"let item of model;let f=first;let l=last;\">\n                <div class=\"p-panelmenu-panel\" *ngIf=\"visible(item)\">\n                    <div [ngClass]=\"{'p-component p-panelmenu-header':true, 'p-highlight':item.expanded,'p-disabled':item.disabled}\" [class]=\"item.styleClass\" [ngStyle]=\"item.style\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url\" (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.id]=\"item.id\"\n                           [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.aria-expanded]=\"item.expanded\" [attr.id]=\"item.id + '_header'\" [attr.aria-controls]=\"item.id +'_content'\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\"\n                           (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                           [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                    </div>\n                    <div *ngIf=\"item.items\" class=\"p-toggleable-content\" [ngClass]=\"{'p-panelmenu-expanded': item.expanded}\"  [@rootItem]=\"item.expanded ? {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\"  (@rootItem.done)=\"onToggleDone()\">\n                        <div class=\"p-panelmenu-content\" role=\"region\" [attr.id]=\"item.id +'_content' \" [attr.aria-labelledby]=\"item.id +'_header'\">\n                            <p-panelMenuSub [item]=\"item\" [parentExpanded]=\"item.expanded\" [expanded]=\"true\" [transitionOptions]=\"transitionOptions\" [root]=\"true\"></p-panelMenuSub>\n                        </div>\n                    </div>\n                </div>\n            </ng-container>\n        </div>\n    `, isInline: true, styles: [\".p-panelmenu .p-panelmenu-header-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-link:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i2.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: PanelMenuSub, selector: \"p-panelMenuSub\", inputs: [\"item\", \"expanded\", \"parentExpanded\", \"transitionOptions\", \"root\"] }], animations: [\n        trigger('rootItem', [\n            state('hidden', style({\n                height: '0'\n            })),\n            state('visible', style({\n                height: '*'\n            })),\n            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n            transition('void => *', animate(0))\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenu, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-panelMenu', template: `\n        <div [class]=\"styleClass\" [ngStyle]=\"style\" [ngClass]=\"'p-panelmenu p-component'\">\n            <ng-container *ngFor=\"let item of model;let f=first;let l=last;\">\n                <div class=\"p-panelmenu-panel\" *ngIf=\"visible(item)\">\n                    <div [ngClass]=\"{'p-component p-panelmenu-header':true, 'p-highlight':item.expanded,'p-disabled':item.disabled}\" [class]=\"item.styleClass\" [ngStyle]=\"item.style\" pTooltip [tooltipOptions]=\"item.tooltipOptions\">\n                        <a *ngIf=\"!item.routerLink\" [attr.href]=\"item.url\" (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [attr.tabindex]=\"item.disabled ? null : '0'\" [attr.id]=\"item.id\"\n                           [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.aria-expanded]=\"item.expanded\" [attr.id]=\"item.id + '_header'\" [attr.aria-controls]=\"item.id +'_content'\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                            <ng-template #htmlLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                        <a *ngIf=\"item.routerLink\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\"\n                           (click)=\"handleClick($event,item)\" (keydown)=\"onItemKeyDown($event)\" [target]=\"item.target\" [attr.title]=\"item.title\" class=\"p-panelmenu-header-link\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled ? null : '0'\"\n                           [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                            <span *ngIf=\"item.items\" class=\"p-panelmenu-icon pi\" [ngClass]=\"{'pi-chevron-right':!item.expanded,'pi-chevron-down':item.expanded}\"></span>\n                            <span class=\"p-menuitem-icon\" [ngClass]=\"item.icon\" *ngIf=\"item.icon\" [ngStyle]=\"item.iconStyle\"></span>\n                            <span class=\"p-menuitem-text\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"item.label\"></span></ng-template>\n                            <span class=\"p-menuitem-badge\" *ngIf=\"item.badge\" [ngClass]=\"item.badgeStyleClass\">{{item.badge}}</span>\n                        </a>\n                    </div>\n                    <div *ngIf=\"item.items\" class=\"p-toggleable-content\" [ngClass]=\"{'p-panelmenu-expanded': item.expanded}\"  [@rootItem]=\"item.expanded ? {value: 'visible', params: {transitionParams: animating ? transitionOptions : '0ms', height: '*'}} : {value: 'hidden', params: {transitionParams: transitionOptions, height: '0'}}\"  (@rootItem.done)=\"onToggleDone()\">\n                        <div class=\"p-panelmenu-content\" role=\"region\" [attr.id]=\"item.id +'_content' \" [attr.aria-labelledby]=\"item.id +'_header'\">\n                            <p-panelMenuSub [item]=\"item\" [parentExpanded]=\"item.expanded\" [expanded]=\"true\" [transitionOptions]=\"transitionOptions\" [root]=\"true\"></p-panelMenuSub>\n                        </div>\n                    </div>\n                </div>\n            </ng-container>\n        </div>\n    `, animations: [\n                        trigger('rootItem', [\n                            state('hidden', style({\n                                height: '0'\n                            })),\n                            state('visible', style({\n                                height: '*'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-panelmenu .p-panelmenu-header-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-link:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; }, propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], multiple: [{\n                type: Input\n            }], transitionOptions: [{\n                type: Input\n            }] } });\nclass PanelMenuModule {\n}\nPanelMenuModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nPanelMenuModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuModule, declarations: [PanelMenu, PanelMenuSub], imports: [CommonModule, RouterModule, TooltipModule], exports: [PanelMenu, RouterModule, TooltipModule] });\nPanelMenuModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuModule, imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: PanelMenuModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, TooltipModule],\n                    exports: [PanelMenu, RouterModule, TooltipModule],\n                    declarations: [PanelMenu, PanelMenuSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BasePanelMenuItem, PanelMenu, PanelMenuModule, PanelMenuSub };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,iBAApB,EAAuCC,KAAvC,EAA8CC,uBAA9C,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;AACA,SAASC,UAAT,QAA2B,aAA3B;;;;IA2C+FjB,EAI/E,sB;;;;;;;;;;;;;IAJ+EA,EASvE,yB;;;;qBATuEA,E;IAAAA,EAS/B,uBAT+BA,EAS/B,+F;;;;;;IAT+BA,EAUvE,yB;;;;qBAVuEA,E;IAAAA,EAUzC,oE;;;;;;IAVyCA,EAWvE,8B;IAXuEA,EAWM,U;IAXNA,EAWqB,e;;;;qBAXrBA,E;IAAAA,EAWM,a;IAXNA,EAWM,kC;;;;;;IAXNA,EAY/C,yB;;;;qBAZ+CA,E;IAAAA,EAYjB,yCAZiBA,EAYjB,gB;;;;;;IAZiBA,EAavE,8B;IAbuEA,EAac,U;IAbdA,EAa6B,e;;;;qBAb7BA,E;IAAAA,EAapB,gD;IAboBA,EAac,a;IAbdA,EAac,kC;;;;;;;;;;;;iBAbdA,E;;IAAAA,EAM3E,0B;IAN2EA,EAM9C;MAN8CA,EAM9C;MAAA,gBAN8CA,EAM9C;MAAA,OAN8CA,EAMnC,2CAAX;IAAA;MAN8CA,EAM9C;MAAA,iBAN8CA,EAM9C;MAAA,gBAN8CA,EAM9C;MAAA,OAN8CA,EAQ9D,mDAFgB;IAAA,E;IAN8CA,EASvE,qF;IATuEA,EAUvE,qF;IAVuEA,EAWvE,qF;IAXuEA,EAYvE,0GAZuEA,EAYvE,wB;IAZuEA,EAavE,qF;IAbuEA,EAc3E,e;;;;iBAd2EA,E;;qBAAAA,E;mBAAAA,E;IAAAA,EAOvE,uBAPuEA,EAOvE,wE;IAPuEA,EAMZ,mCANYA,EAMZ,oM;IANYA,EASgD,a;IAThDA,EASgD,mC;IAThDA,EAUjB,a;IAViBA,EAUjB,kC;IAViBA,EAWxC,a;IAXwCA,EAWxC,gE;IAXwCA,EAavC,a;IAbuCA,EAavC,mC;;;;;;IAbuCA,EAmBvE,yB;;;;qBAnBuEA,E;IAAAA,EAmB/B,uBAnB+BA,EAmB/B,+F;;;;;;IAnB+BA,EAoBvE,yB;;;;qBApBuEA,E;IAAAA,EAoBzC,oE;;;;;;IApByCA,EAqBvE,8B;IArBuEA,EAqBW,U;IArBXA,EAqB0B,e;;;;qBArB1BA,E;IAAAA,EAqBW,a;IArBXA,EAqBW,kC;;;;;;IArBXA,EAsB1C,yB;;;;qBAtB0CA,E;IAAAA,EAsBZ,yCAtBYA,EAsBZ,gB;;;;;;IAtBYA,EAuBvE,8B;IAvBuEA,EAuBc,U;IAvBdA,EAuB6B,e;;;;qBAvB7BA,E;IAAAA,EAuBpB,gD;IAvBoBA,EAuBc,a;IAvBdA,EAuBc,kC;;;;;;;;;;;;iBAvBdA,E;;IAAAA,EAe3E,2B;IAf2EA,EAe/C;MAf+CA,EAe/C;MAAA,gBAf+CA,EAe/C;MAAA,OAf+CA,EAepC,2CAAX;IAAA;MAf+CA,EAe/C;MAAA,iBAf+CA,EAe/C;MAAA,gBAf+CA,EAe/C;MAAA,OAf+CA,EAiB9D,mDAFe;IAAA,E;IAf+CA,EAmBvE,qF;IAnBuEA,EAoBvE,qF;IApBuEA,EAqBvE,qF;IArBuEA,EAsBvE,0GAtBuEA,EAsBvE,wB;IAtBuEA,EAuBvE,qF;IAvBuEA,EAwB3E,e;;;;iBAxB2EA,E;;qBAAAA,E;mBAAAA,E;IAAAA,EAeZ,iMAfYA,EAeZ,sCAfYA,EAeZ,0T;IAfYA,EAgB7B,iM;IAhB6BA,EAmBgD,a;IAnBhDA,EAmBgD,mC;IAnBhDA,EAoBjB,a;IApBiBA,EAoBjB,kC;IApBiBA,EAqBxC,a;IArBwCA,EAqBxC,gE;IArBwCA,EAuBvC,a;IAvBuCA,EAuBvC,mC;;;;;;IAvBuCA,EAyB3E,mC;;;;qBAzB2EA,E;mBAAAA,E;IAAAA,EAyB3D,uK;;;;;;IAzB2DA,EAK/E,2B;IAL+EA,EAM3E,2E;IAN2EA,EAe3E,2E;IAf2EA,EAyB3E,oG;IAzB2EA,EA0B/E,e;;;;qBA1B+EA,E;IAAAA,EAKF,oD;IALEA,EAK/B,iH;IAL+BA,EAMvE,a;IANuEA,EAMvE,yC;IANuEA,EAevE,a;IAfuEA,EAevE,wC;IAfuEA,EAyBuE,a;IAzBvEA,EAyBuE,mC;;;;;;IAzBvEA,EAI/E,uE;IAJ+EA,EAK/E,uE;;;;;IAL+EA,EAI1E,uC;IAJ0EA,EAK1E,a;IAL0EA,EAK1E,wC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAL0EA,EA2JnE,yB;;;;oBA3JmEA,E;IAAAA,EA2Jd,uBA3JcA,EA2Jd,8D;;;;;;IA3JcA,EA4JnE,yB;;;;oBA5JmEA,E;IAAAA,EA4JrC,kE;;;;;;IA5JqCA,EA6JnE,8B;IA7JmEA,EA6JS,U;IA7JTA,EA6JuB,e;;;;oBA7JvBA,E;IAAAA,EA6JS,a;IA7JTA,EA6JS,iC;;;;;;IA7JTA,EA8J3C,yB;;;;oBA9J2CA,E;IAAAA,EA8Jb,wCA9JaA,EA8Jb,gB;;;;;;IA9JaA,EA+JnE,8B;IA/JmEA,EA+JgB,U;IA/JhBA,EA+J8B,e;;;;oBA/J9BA,E;IAAAA,EA+JjB,+C;IA/JiBA,EA+JgB,a;IA/JhBA,EA+JgB,iC;;;;;;iBA/JhBA,E;;IAAAA,EAyJvE,0B;IAzJuEA,EAyJpB;MAzJoBA,EAyJpB;MAAA,gBAzJoBA,EAyJpB;MAAA,gBAzJoBA,EAyJpB;MAAA,OAzJoBA,EAyJX,kDAAT;IAAA;MAzJoBA,EAyJpB;MAAA,gBAzJoBA,EAyJpB;MAAA,OAzJoBA,EAyJ0B,2CAA9C;IAAA,E;IAzJoBA,EA2JnE,mF;IA3JmEA,EA4JnE,oF;IA5JmEA,EA6JnE,oF;IA7JmEA,EA8JnE,yGA9JmEA,EA8JnE,wB;IA9JmEA,EA+JnE,oF;IA/JmEA,EAgKvE,e;;;;iBAhKuEA,E;;oBAAAA,E;IAAAA,EA0JpE,qC;IA1JoEA,EAyJ3C,kCAzJ2CA,EAyJ3C,+M;IAzJ2CA,EA2J5D,a;IA3J4DA,EA2J5D,kC;IA3J4DA,EA4Jd,a;IA5JcA,EA4Jd,iC;IA5JcA,EA6JpC,a;IA7JoCA,EA6JpC,+D;IA7JoCA,EA+JnC,a;IA/JmCA,EA+JnC,kC;;;;;;IA/JmCA,EAoKnE,yB;;;;oBApKmEA,E;IAAAA,EAoKd,uBApKcA,EAoKd,8D;;;;;;IApKcA,EAqKnE,yB;;;;oBArKmEA,E;IAAAA,EAqKrC,kE;;;;;;IArKqCA,EAsKnE,8B;IAtKmEA,EAsKc,U;IAtKdA,EAsK4B,e;;;;oBAtK5BA,E;IAAAA,EAsKc,a;IAtKdA,EAsKc,iC;;;;;;IAtKdA,EAuKtC,yB;;;;oBAvKsCA,E;IAAAA,EAuKR,wCAvKQA,EAuKR,gB;;;;;;IAvKQA,EAwKnE,8B;IAxKmEA,EAwKgB,U;IAxKhBA,EAwK8B,e;;;;oBAxK9BA,E;IAAAA,EAwKjB,+C;IAxKiBA,EAwKgB,a;IAxKhBA,EAwKgB,iC;;;;;;iBAxKhBA,E;;IAAAA,EAiKvE,2B;IAjKuEA,EAkKpE;MAlKoEA,EAkKpE;MAAA,gBAlKoEA,EAkKpE;MAAA,gBAlKoEA,EAkKpE;MAAA,OAlKoEA,EAkK3D,kDAAT;IAAA;MAlKoEA,EAkKpE;MAAA,gBAlKoEA,EAkKpE;MAAA,OAlKoEA,EAkKtB,2CAA9C;IAAA,E;IAlKoEA,EAoKnE,mF;IApKmEA,EAqKnE,oF;IArKmEA,EAsKnE,oF;IAtKmEA,EAuKnE,yGAvKmEA,EAuKnE,wB;IAvKmEA,EAwKnE,oF;IAxKmEA,EAyKvE,e;;;;iBAzKuEA,E;;oBAAAA,E;IAAAA,EAiK5C,8LAjK4CA,EAiK5C,gS;IAjK4CA,EAkKwB,iG;IAlKxBA,EAoK5D,a;IApK4DA,EAoK5D,kC;IApK4DA,EAqKd,a;IArKcA,EAqKd,iC;IArKcA,EAsKpC,a;IAtKoCA,EAsKpC,+D;IAtKoCA,EAwKnC,a;IAxKmCA,EAwKnC,kC;;;;;;;;;;;;iBAxKmCA,E;;IAAAA,EA2K3E,6B;IA3K2EA,EA2KiP;MA3KjPA,EA2KiP;MAAA,gBA3KjPA,EA2KiP;MAAA,OA3KjPA,EA2KmQ,oCAAlB;IAAA,E;IA3KjPA,EA4KvE,6B;IA5KuEA,EA6KnE,mC;IA7KmEA,EA8KvE,iB;;;;oBA9KuEA,E;mBAAAA,E;IAAAA,EA2KtB,uBA3KsBA,EA2KtB,4EA3KsBA,EA2KtB,0BA3KsBA,EA2KtB,kFA3KsBA,EA2KtB,0BA3KsBA,EA2KtB,qD;IA3KsBA,EA4KxB,a;IA5KwBA,EA4KxB,sF;IA5KwBA,EA6KnD,a;IA7KmDA,EA6KnD,+I;;;;;;;;;;;;;;IA7KmDA,EAuJ/E,yC;IAvJ+EA,EAyJvE,0E;IAzJuEA,EAiKvE,0E;IAjKuEA,EA0K3E,e;IA1K2EA,EA2K3E,8E;IA3K2EA,EAgL/E,e;;;;oBAhL+EA,E;IAAAA,EAwJsC,a;IAxJtCA,EAwJsC,+B;IAxJtCA,EAwJtE,uBAxJsEA,EAwJtE,kI;IAxJsEA,EAyJnE,a;IAzJmEA,EAyJnE,wC;IAzJmEA,EAiKnE,a;IAjKmEA,EAiKnE,uC;IAjKmEA,EA2KrE,a;IA3KqEA,EA2KrE,kC;;;;;;IA3KqEA,EAsJnF,2B;IAtJmFA,EAuJ/E,wE;IAvJ+EA,EAiLnF,wB;;;;;mBAjLmFA,E;IAAAA,EAuJ/C,a;IAvJ+CA,EAuJ/C,4C;;;;AAhMhD,MAAMkB,iBAAN,CAAwB;EACpBC,WAAW,CAACC,GAAD,EAAM;IACb,KAAKA,GAAL,GAAWA,GAAX;EACH;;EACDC,WAAW,CAACC,KAAD,EAAQC,IAAR,EAAc;IACrB,IAAIA,IAAI,CAACC,QAAT,EAAmB;MACfF,KAAK,CAACG,cAAN;MACA;IACH;;IACDF,IAAI,CAACG,QAAL,GAAgB,CAACH,IAAI,CAACG,QAAtB;IACA,KAAKN,GAAL,CAASO,aAAT;;IACA,IAAI,CAACJ,IAAI,CAACK,GAAN,IAAa,CAACL,IAAI,CAACM,UAAvB,EAAmC;MAC/BP,KAAK,CAACG,cAAN;IACH;;IACD,IAAIF,IAAI,CAACO,OAAT,EAAkB;MACdP,IAAI,CAACO,OAAL,CAAa;QACTC,aAAa,EAAET,KADN;QAETC,IAAI,EAAEA;MAFG,CAAb;IAIH;EACJ;;AApBmB;;AAsBxB,MAAMS,YAAN,SAA2Bd,iBAA3B,CAA6C;EACzCC,WAAW,CAACC,GAAD,EAAM;IACb,MAAMA,GAAN;EACH;;EACDa,aAAa,CAACX,KAAD,EAAQ;IACjB,IAAIY,QAAQ,GAAGZ,KAAK,CAACa,aAArB;;IACA,QAAQb,KAAK,CAACc,IAAd;MACI,KAAK,OAAL;MACA,KAAK,OAAL;QACI,IAAIF,QAAQ,IAAI,CAACjB,UAAU,CAACoB,QAAX,CAAoBH,QAApB,EAA8B,YAA9B,CAAjB,EAA8D;UAC1DA,QAAQ,CAACI,KAAT;QACH;;QACDhB,KAAK,CAACG,cAAN;QACA;;MACJ;QACI;IATR;EAWH;;AAjBwC;;AAmB7CO,YAAY,CAACO,IAAb;EAAA,iBAAyGP,YAAzG,EAA+FhC,EAA/F,mBAAuIA,EAAE,CAACwC,iBAA1I;AAAA;;AACAR,YAAY,CAACS,IAAb,kBAD+FzC,EAC/F;EAAA,MAA6FgC,YAA7F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD+FhC,EAC/F;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FA,EAEvF,2BADR;MAD+FA,EAGnF,2EAFZ;MAD+FA,EA4BvF,eA3BR;IAAA;;IAAA;MAD+FA,EAEnF,uBAFmFA,EAEnF,6EAFmFA,EAEnF,yBAFmFA,EAEnF,mDAFmFA,EAEnF,0BAFmFA,EAEnF,kDADZ;MAD+FA,EAGtD,aAFzC;MAD+FA,EAGtD,sCAFzC;IAAA;EAAA;EAAA,eA4BiEW,EAAE,CAAC+B,OA5BpE,EA4B+J/B,EAAE,CAACgC,OA5BlK,EA4B4RhC,EAAE,CAACiC,IA5B/R,EA4BgYjC,EAAE,CAACkC,OA5BnY,EA4BqdhC,EAAE,CAACiC,kBA5Bxd,EA4BwtBjC,EAAE,CAACkC,gBA5B3tB,EA4By7BhC,EAAE,CAACiC,OA5B57B,EA4BovChB,YA5BpvC;EAAA;EAAA;IAAA,WA4B03C,CACl3C1B,OAAO,CAAC,SAAD,EAAY,CACfC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;MAClByC,MAAM,EAAE;IADU,CAAD,CAAhB,CADU,EAIf1C,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;MACnByC,MAAM,EAAE;IADW,CAAD,CAAjB,CAJU,EAOfxC,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPK,EAQfD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARK,CAAZ,CAD22C;EA5B13C;AAAA;;AAwCA;EAAA,mDAzC+FV,EAyC/F,mBAA2FgC,YAA3F,EAAqH,CAAC;IAC1GkB,IAAI,EAAEjD,SADoG;IAE1GkD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA9BmB;MA+BCC,UAAU,EAAE,CACRhD,OAAO,CAAC,SAAD,EAAY,CACfC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;QAClByC,MAAM,EAAE;MADU,CAAD,CAAhB,CADU,EAIf1C,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;QACnByC,MAAM,EAAE;MADW,CAAD,CAAjB,CAJU,EAOfxC,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPK,EAQfD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARK,CAAZ,CADC,CA/Bb;MA2CC6C,aAAa,EAAErD,iBAAiB,CAACsD,IA3ClC;MA4CCC,IAAI,EAAE;QACF,SAAS;MADP;IA5CP,CAAD;EAFoG,CAAD,CAArH,EAkD4B,YAAY;IAAE,OAAO,CAAC;MAAEP,IAAI,EAAElD,EAAE,CAACwC;IAAX,CAAD,CAAP;EAA0C,CAlDpF,EAkDsG;IAAEjB,IAAI,EAAE,CAAC;MAC/F2B,IAAI,EAAE/C;IADyF,CAAD,CAAR;IAEtFuB,QAAQ,EAAE,CAAC;MACXwB,IAAI,EAAE/C;IADK,CAAD,CAF4E;IAItFuD,cAAc,EAAE,CAAC;MACjBR,IAAI,EAAE/C;IADW,CAAD,CAJsE;IAMtFwD,iBAAiB,EAAE,CAAC;MACpBT,IAAI,EAAE/C;IADc,CAAD,CANmE;IAQtFyD,IAAI,EAAE,CAAC;MACPV,IAAI,EAAE/C;IADC,CAAD;EARgF,CAlDtG;AAAA;;AA6DA,MAAM0D,SAAN,SAAwB3C,iBAAxB,CAA0C;EACtCC,WAAW,CAACC,GAAD,EAAM;IACb,MAAMA,GAAN;IACA,KAAK0C,QAAL,GAAgB,IAAhB;IACA,KAAKH,iBAAL,GAAyB,sCAAzB;EACH;;EACDI,WAAW,GAAG;IACV,KAAK,IAAIxC,IAAT,IAAiB,KAAKyC,KAAtB,EAA6B;MACzB,IAAIzC,IAAI,CAACG,QAAT,EAAmB;QACfH,IAAI,CAACG,QAAL,GAAgB,KAAhB;MACH;IACJ;EACJ;;EACDL,WAAW,CAACC,KAAD,EAAQC,IAAR,EAAc;IACrB,IAAI,CAAC,KAAKuC,QAAV,EAAoB;MAChB,KAAK,IAAIG,SAAT,IAAsB,KAAKD,KAA3B,EAAkC;QAC9B,IAAIzC,IAAI,KAAK0C,SAAT,IAAsBA,SAAS,CAACvC,QAApC,EAA8C;UAC1CuC,SAAS,CAACvC,QAAV,GAAqB,KAArB;QACH;MACJ;IACJ;;IACD,KAAKwC,SAAL,GAAiB,IAAjB;IACA,MAAM7C,WAAN,CAAkBC,KAAlB,EAAyBC,IAAzB;EACH;;EACD4C,YAAY,GAAG;IACX,KAAKD,SAAL,GAAiB,KAAjB;EACH;;EACDjC,aAAa,CAACX,KAAD,EAAQ;IACjB,IAAIY,QAAQ,GAAGZ,KAAK,CAACa,aAArB;;IACA,QAAQb,KAAK,CAACc,IAAd;MACI,KAAK,OAAL;MACA,KAAK,OAAL;QACI,IAAIF,QAAQ,IAAI,CAACjB,UAAU,CAACoB,QAAX,CAAoBH,QAApB,EAA8B,YAA9B,CAAjB,EAA8D;UAC1DA,QAAQ,CAACI,KAAT;QACH;;QACDhB,KAAK,CAACG,cAAN;QACA;;MACJ;QACI;IATR;EAWH;;EACD2C,OAAO,CAAC7C,IAAD,EAAO;IACV,OAAOA,IAAI,CAAC6C,OAAL,KAAiB,KAAxB;EACH;;AA3CqC;;AA6C1CP,SAAS,CAACtB,IAAV;EAAA,iBAAsGsB,SAAtG,EAnJ+F7D,EAmJ/F,mBAAiIA,EAAE,CAACwC,iBAApI;AAAA;;AACAqB,SAAS,CAACpB,IAAV,kBApJ+FzC,EAoJ/F;EAAA,MAA0F6D,SAA1F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WApJ+F7D,EAoJ/F;EAAA;EAAA;EAAA;EAAA;IAAA;MApJ+FA,EAqJvF,4BADR;MApJ+FA,EAsJnF,0EAFZ;MApJ+FA,EAkLvF,eA9BR;IAAA;;IAAA;MApJ+FA,EAqJlF,2BADb;MApJ+FA,EAqJ7D,uEADlC;MApJ+FA,EAsJpD,aAF3C;MApJ+FA,EAsJpD,iCAF3C;IAAA;EAAA;EAAA,eA+BwvBW,EAAE,CAAC+B,OA/B3vB,EA+Bs1B/B,EAAE,CAACgC,OA/Bz1B,EA+Bm9BhC,EAAE,CAACiC,IA/Bt9B,EA+BujCjC,EAAE,CAACkC,OA/B1jC,EA+B4oChC,EAAE,CAACiC,kBA/B/oC,EA+B+4CjC,EAAE,CAACkC,gBA/Bl5C,EA+BgnDhC,EAAE,CAACiC,OA/BnnD,EA+B26DhB,YA/B36D;EAAA;EAAA;EAAA;IAAA,WA+BijE,CACziE1B,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;MAClByC,MAAM,EAAE;IADU,CAAD,CAAhB,CADW,EAIhB1C,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;MACnByC,MAAM,EAAE;IADW,CAAD,CAAjB,CAJW,EAOhBxC,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPM,EAQhBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARM,CAAb,CADkiE;EA/BjjE;EAAA;AAAA;;AA2CA;EAAA,mDA/L+FV,EA+L/F,mBAA2F6D,SAA3F,EAAkH,CAAC;IACvGX,IAAI,EAAEjD,SADiG;IAEvGkD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,aAAZ;MAA2BC,QAAQ,EAAG;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA/BmB;MA+BZC,UAAU,EAAE,CACKhD,OAAO,CAAC,UAAD,EAAa,CAChBC,KAAK,CAAC,QAAD,EAAWC,KAAK,CAAC;QAClByC,MAAM,EAAE;MADU,CAAD,CAAhB,CADW,EAIhB1C,KAAK,CAAC,SAAD,EAAYC,KAAK,CAAC;QACnByC,MAAM,EAAE;MADW,CAAD,CAAjB,CAJW,EAOhBxC,UAAU,CAAC,oBAAD,EAAuB,CAACC,OAAO,CAAC,sBAAD,CAAR,CAAvB,CAPM,EAQhBD,UAAU,CAAC,WAAD,EAAcC,OAAO,CAAC,CAAD,CAArB,CARM,CAAb,CADZ,CA/BA;MA0CI2D,eAAe,EAAEjE,uBAAuB,CAACkE,MA1C7C;MA0CqDf,aAAa,EAAErD,iBAAiB,CAACsD,IA1CtF;MA0C4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CA1ClG;MA4CIc,MAAM,EAAE,CAAC,2qBAAD;IA5CZ,CAAD;EAFiG,CAAD,CAAlH,EA+C4B,YAAY;IAAE,OAAO,CAAC;MAAErB,IAAI,EAAElD,EAAE,CAACwC;IAAX,CAAD,CAAP;EAA0C,CA/CpF,EA+CsG;IAAEwB,KAAK,EAAE,CAAC;MAChGd,IAAI,EAAE/C;IAD0F,CAAD,CAAT;IAEtFK,KAAK,EAAE,CAAC;MACR0C,IAAI,EAAE/C;IADE,CAAD,CAF+E;IAItFqE,UAAU,EAAE,CAAC;MACbtB,IAAI,EAAE/C;IADO,CAAD,CAJ0E;IAMtF2D,QAAQ,EAAE,CAAC;MACXZ,IAAI,EAAE/C;IADK,CAAD,CAN4E;IAQtFwD,iBAAiB,EAAE,CAAC;MACpBT,IAAI,EAAE/C;IADc,CAAD;EARmE,CA/CtG;AAAA;;AA0DA,MAAMsE,eAAN,CAAsB;;AAEtBA,eAAe,CAAClC,IAAhB;EAAA,iBAA4GkC,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBA5P+F1E,EA4P/F;EAAA,MAA6GyE;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBA7P+F3E,EA6P/F;EAAA,UAAwIY,YAAxI,EAAsJE,YAAtJ,EAAoKE,aAApK,EAAmLF,YAAnL,EAAiME,aAAjM;AAAA;;AACA;EAAA,mDA9P+FhB,EA8P/F,mBAA2FyE,eAA3F,EAAwH,CAAC;IAC7GvB,IAAI,EAAE7C,QADuG;IAE7G8C,IAAI,EAAE,CAAC;MACCyB,OAAO,EAAE,CAAChE,YAAD,EAAeE,YAAf,EAA6BE,aAA7B,CADV;MAEC6D,OAAO,EAAE,CAAChB,SAAD,EAAY/C,YAAZ,EAA0BE,aAA1B,CAFV;MAGC8D,YAAY,EAAE,CAACjB,SAAD,EAAY7B,YAAZ;IAHf,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;;;AAEA,SAASd,iBAAT,EAA4B2C,SAA5B,EAAuCY,eAAvC,EAAwDzC,YAAxD"}, "metadata": {}, "sourceType": "module"}