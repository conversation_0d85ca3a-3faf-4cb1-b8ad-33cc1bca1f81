{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n  constructor(schedulerActionCtor = VirtualAction, maxFrames = Infinity) {\n    super(schedulerActionCtor, () => this.frame);\n    this.maxFrames = maxFrames;\n    this.frame = 0;\n    this.index = -1;\n  }\n\n  flush() {\n    const {\n      actions,\n      maxFrames\n    } = this;\n    let error;\n    let action;\n\n    while ((action = actions[0]) && action.delay <= maxFrames) {\n      actions.shift();\n      this.frame = action.delay;\n\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    }\n\n    if (error) {\n      while (action = actions.shift()) {\n        action.unsubscribe();\n      }\n\n      throw error;\n    }\n  }\n\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n  constructor(scheduler, work, index = scheduler.index += 1) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n    this.index = index;\n    this.active = true;\n    this.index = scheduler.index = index;\n  }\n\n  schedule(state, delay = 0) {\n    if (Number.isFinite(delay)) {\n      if (!this.id) {\n        return super.schedule(state, delay);\n      }\n\n      this.active = false;\n      const action = new VirtualAction(this.scheduler, this.work);\n      this.add(action);\n      return action.schedule(state, delay);\n    } else {\n      return Subscription.EMPTY;\n    }\n  }\n\n  requestAsyncId(scheduler, id, delay = 0) {\n    this.delay = scheduler.frame + delay;\n    const {\n      actions\n    } = scheduler;\n    actions.push(this);\n    actions.sort(VirtualAction.sortActions);\n    return 1;\n  }\n\n  recycleAsyncId(scheduler, id, delay = 0) {\n    return undefined;\n  }\n\n  _execute(state, delay) {\n    if (this.active === true) {\n      return super._execute(state, delay);\n    }\n  }\n\n  static sortActions(a, b) {\n    if (a.delay === b.delay) {\n      if (a.index === b.index) {\n        return 0;\n      } else if (a.index > b.index) {\n        return 1;\n      } else {\n        return -1;\n      }\n    } else if (a.delay > b.delay) {\n      return 1;\n    } else {\n      return -1;\n    }\n  }\n\n}", "map": {"version": 3, "names": ["AsyncAction", "Subscription", "AsyncScheduler", "VirtualTimeScheduler", "constructor", "schedulerActionCtor", "VirtualAction", "maxFrames", "Infinity", "frame", "index", "flush", "actions", "error", "action", "delay", "shift", "execute", "state", "unsubscribe", "frameTimeFactor", "scheduler", "work", "active", "schedule", "Number", "isFinite", "id", "add", "EMPTY", "requestAsyncId", "push", "sort", "sortActions", "recycleAsyncId", "undefined", "_execute", "a", "b"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/scheduler/VirtualTimeScheduler.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport class VirtualTimeScheduler extends AsyncScheduler {\n    constructor(schedulerActionCtor = VirtualAction, maxFrames = Infinity) {\n        super(schedulerActionCtor, () => this.frame);\n        this.maxFrames = maxFrames;\n        this.frame = 0;\n        this.index = -1;\n    }\n    flush() {\n        const { actions, maxFrames } = this;\n        let error;\n        let action;\n        while ((action = actions[0]) && action.delay <= maxFrames) {\n            actions.shift();\n            this.frame = action.delay;\n            if ((error = action.execute(action.state, action.delay))) {\n                break;\n            }\n        }\n        if (error) {\n            while ((action = actions.shift())) {\n                action.unsubscribe();\n            }\n            throw error;\n        }\n    }\n}\nVirtualTimeScheduler.frameTimeFactor = 10;\nexport class VirtualAction extends AsyncAction {\n    constructor(scheduler, work, index = (scheduler.index += 1)) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n        this.index = index;\n        this.active = true;\n        this.index = scheduler.index = index;\n    }\n    schedule(state, delay = 0) {\n        if (Number.isFinite(delay)) {\n            if (!this.id) {\n                return super.schedule(state, delay);\n            }\n            this.active = false;\n            const action = new VirtualAction(this.scheduler, this.work);\n            this.add(action);\n            return action.schedule(state, delay);\n        }\n        else {\n            return Subscription.EMPTY;\n        }\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        this.delay = scheduler.frame + delay;\n        const { actions } = scheduler;\n        actions.push(this);\n        actions.sort(VirtualAction.sortActions);\n        return 1;\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        return undefined;\n    }\n    _execute(state, delay) {\n        if (this.active === true) {\n            return super._execute(state, delay);\n        }\n    }\n    static sortActions(a, b) {\n        if (a.delay === b.delay) {\n            if (a.index === b.index) {\n                return 0;\n            }\n            else if (a.index > b.index) {\n                return 1;\n            }\n            else {\n                return -1;\n            }\n        }\n        else if (a.delay > b.delay) {\n            return 1;\n        }\n        else {\n            return -1;\n        }\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAT,QAA4B,eAA5B;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,OAAO,MAAMC,oBAAN,SAAmCD,cAAnC,CAAkD;EACrDE,WAAW,CAACC,mBAAmB,GAAGC,aAAvB,EAAsCC,SAAS,GAAGC,QAAlD,EAA4D;IACnE,MAAMH,mBAAN,EAA2B,MAAM,KAAKI,KAAtC;IACA,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKE,KAAL,GAAa,CAAb;IACA,KAAKC,KAAL,GAAa,CAAC,CAAd;EACH;;EACDC,KAAK,GAAG;IACJ,MAAM;MAAEC,OAAF;MAAWL;IAAX,IAAyB,IAA/B;IACA,IAAIM,KAAJ;IACA,IAAIC,MAAJ;;IACA,OAAO,CAACA,MAAM,GAAGF,OAAO,CAAC,CAAD,CAAjB,KAAyBE,MAAM,CAACC,KAAP,IAAgBR,SAAhD,EAA2D;MACvDK,OAAO,CAACI,KAAR;MACA,KAAKP,KAAL,GAAaK,MAAM,CAACC,KAApB;;MACA,IAAKF,KAAK,GAAGC,MAAM,CAACG,OAAP,CAAeH,MAAM,CAACI,KAAtB,EAA6BJ,MAAM,CAACC,KAApC,CAAb,EAA0D;QACtD;MACH;IACJ;;IACD,IAAIF,KAAJ,EAAW;MACP,OAAQC,MAAM,GAAGF,OAAO,CAACI,KAAR,EAAjB,EAAmC;QAC/BF,MAAM,CAACK,WAAP;MACH;;MACD,MAAMN,KAAN;IACH;EACJ;;AAxBoD;AA0BzDV,oBAAoB,CAACiB,eAArB,GAAuC,EAAvC;AACA,OAAO,MAAMd,aAAN,SAA4BN,WAA5B,CAAwC;EAC3CI,WAAW,CAACiB,SAAD,EAAYC,IAAZ,EAAkBZ,KAAK,GAAIW,SAAS,CAACX,KAAV,IAAmB,CAA9C,EAAkD;IACzD,MAAMW,SAAN,EAAiBC,IAAjB;IACA,KAAKD,SAAL,GAAiBA,SAAjB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKZ,KAAL,GAAaA,KAAb;IACA,KAAKa,MAAL,GAAc,IAAd;IACA,KAAKb,KAAL,GAAaW,SAAS,CAACX,KAAV,GAAkBA,KAA/B;EACH;;EACDc,QAAQ,CAACN,KAAD,EAAQH,KAAK,GAAG,CAAhB,EAAmB;IACvB,IAAIU,MAAM,CAACC,QAAP,CAAgBX,KAAhB,CAAJ,EAA4B;MACxB,IAAI,CAAC,KAAKY,EAAV,EAAc;QACV,OAAO,MAAMH,QAAN,CAAeN,KAAf,EAAsBH,KAAtB,CAAP;MACH;;MACD,KAAKQ,MAAL,GAAc,KAAd;MACA,MAAMT,MAAM,GAAG,IAAIR,aAAJ,CAAkB,KAAKe,SAAvB,EAAkC,KAAKC,IAAvC,CAAf;MACA,KAAKM,GAAL,CAASd,MAAT;MACA,OAAOA,MAAM,CAACU,QAAP,CAAgBN,KAAhB,EAAuBH,KAAvB,CAAP;IACH,CARD,MASK;MACD,OAAOd,YAAY,CAAC4B,KAApB;IACH;EACJ;;EACDC,cAAc,CAACT,SAAD,EAAYM,EAAZ,EAAgBZ,KAAK,GAAG,CAAxB,EAA2B;IACrC,KAAKA,KAAL,GAAaM,SAAS,CAACZ,KAAV,GAAkBM,KAA/B;IACA,MAAM;MAAEH;IAAF,IAAcS,SAApB;IACAT,OAAO,CAACmB,IAAR,CAAa,IAAb;IACAnB,OAAO,CAACoB,IAAR,CAAa1B,aAAa,CAAC2B,WAA3B;IACA,OAAO,CAAP;EACH;;EACDC,cAAc,CAACb,SAAD,EAAYM,EAAZ,EAAgBZ,KAAK,GAAG,CAAxB,EAA2B;IACrC,OAAOoB,SAAP;EACH;;EACDC,QAAQ,CAAClB,KAAD,EAAQH,KAAR,EAAe;IACnB,IAAI,KAAKQ,MAAL,KAAgB,IAApB,EAA0B;MACtB,OAAO,MAAMa,QAAN,CAAelB,KAAf,EAAsBH,KAAtB,CAAP;IACH;EACJ;;EACiB,OAAXkB,WAAW,CAACI,CAAD,EAAIC,CAAJ,EAAO;IACrB,IAAID,CAAC,CAACtB,KAAF,KAAYuB,CAAC,CAACvB,KAAlB,EAAyB;MACrB,IAAIsB,CAAC,CAAC3B,KAAF,KAAY4B,CAAC,CAAC5B,KAAlB,EAAyB;QACrB,OAAO,CAAP;MACH,CAFD,MAGK,IAAI2B,CAAC,CAAC3B,KAAF,GAAU4B,CAAC,CAAC5B,KAAhB,EAAuB;QACxB,OAAO,CAAP;MACH,CAFI,MAGA;QACD,OAAO,CAAC,CAAR;MACH;IACJ,CAVD,MAWK,IAAI2B,CAAC,CAACtB,KAAF,GAAUuB,CAAC,CAACvB,KAAhB,EAAuB;MACxB,OAAO,CAAP;IACH,CAFI,MAGA;MACD,OAAO,CAAC,CAAR;IACH;EACJ;;AAxD0C"}, "metadata": {}, "sourceType": "module"}