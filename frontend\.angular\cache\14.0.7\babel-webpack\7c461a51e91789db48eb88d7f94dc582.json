{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Input, EventEmitter, Optional, SkipSelf, Output, Self, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceArray, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, startWith, map, take, tap, switchMap } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/bidi';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\n\nfunction extendStyles(dest, source, importantProperties) {\n  for (let key in source) {\n    if (source.hasOwnProperty(key)) {\n      const value = source[key];\n\n      if (value) {\n        dest.setProperty(key, value, (importantProperties === null || importantProperties === void 0 ? void 0 : importantProperties.has(key)) ? 'important' : '');\n      } else {\n        dest.removeProperty(key);\n      }\n    }\n  }\n\n  return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\n\n\nfunction toggleNativeDragInteractions(element, enable) {\n  const userSelect = enable ? '' : 'none';\n  extendStyles(element.style, {\n    'touch-action': enable ? '' : 'none',\n    '-webkit-user-drag': enable ? '' : 'none',\n    '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n    'user-select': userSelect,\n    '-ms-user-select': userSelect,\n    '-webkit-user-select': userSelect,\n    '-moz-user-select': userSelect\n  });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\n\n\nfunction toggleVisibility(element, enable, importantProperties) {\n  extendStyles(element.style, {\n    position: enable ? '' : 'fixed',\n    top: enable ? '' : '0',\n    opacity: enable ? '' : '0',\n    left: enable ? '' : '-999em'\n  }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\n\n\nfunction combineTransforms(transform, initialTransform) {\n  return initialTransform && initialTransform != 'none' ? transform + ' ' + initialTransform : transform;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Parses a CSS time value to milliseconds. */\n\n\nfunction parseCssTimeUnitsToMs(value) {\n  // Some browsers will return it in seconds, whereas others will return milliseconds.\n  const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n  return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\n\n\nfunction getTransformTransitionDurationInMs(element) {\n  const computedStyle = getComputedStyle(element);\n  const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n  const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all'); // If there's no transition for `all` or `transform`, we shouldn't do anything.\n\n  if (!property) {\n    return 0;\n  } // Get the index of the property that we're interested in and match\n  // it up to the same index in `transition-delay` and `transition-duration`.\n\n\n  const propertyIndex = transitionedProperties.indexOf(property);\n  const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n  const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n  return parseCssTimeUnitsToMs(rawDurations[propertyIndex]) + parseCssTimeUnitsToMs(rawDelays[propertyIndex]);\n}\n/** Parses out multiple values from a computed style into an array. */\n\n\nfunction parseCssPropertyValue(computedStyle, name) {\n  const value = computedStyle.getPropertyValue(name);\n  return value.split(',').map(part => part.trim());\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets a mutable version of an element's bounding `ClientRect`. */\n\n\nfunction getMutableClientRect(element) {\n  const clientRect = element.getBoundingClientRect(); // We need to clone the `clientRect` here, because all the values on it are readonly\n  // and we need to be able to update them. Also we can't use a spread here, because\n  // the values on a `ClientRect` aren't own properties. See:\n  // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n\n  return {\n    top: clientRect.top,\n    right: clientRect.right,\n    bottom: clientRect.bottom,\n    left: clientRect.left,\n    width: clientRect.width,\n    height: clientRect.height,\n    x: clientRect.x,\n    y: clientRect.y\n  };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\n\n\nfunction isInsideClientRect(clientRect, x, y) {\n  const {\n    top,\n    bottom,\n    left,\n    right\n  } = clientRect;\n  return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\n\n\nfunction adjustClientRect(clientRect, top, left) {\n  clientRect.top += top;\n  clientRect.bottom = clientRect.top + clientRect.height;\n  clientRect.left += left;\n  clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\n\n\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n  const {\n    top,\n    right,\n    bottom,\n    left,\n    width,\n    height\n  } = rect;\n  const xThreshold = width * threshold;\n  const yThreshold = height * threshold;\n  return pointerY > top - yThreshold && pointerY < bottom + yThreshold && pointerX > left - xThreshold && pointerX < right + xThreshold;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\n\n\nclass ParentPositionTracker {\n  constructor(_document) {\n    this._document = _document;\n    /** Cached positions of the scrollable parent elements. */\n\n    this.positions = new Map();\n  }\n  /** Clears the cached positions. */\n\n\n  clear() {\n    this.positions.clear();\n  }\n  /** Caches the positions. Should be called at the beginning of a drag sequence. */\n\n\n  cache(elements) {\n    this.clear();\n    this.positions.set(this._document, {\n      scrollPosition: this.getViewportScrollPosition()\n    });\n    elements.forEach(element => {\n      this.positions.set(element, {\n        scrollPosition: {\n          top: element.scrollTop,\n          left: element.scrollLeft\n        },\n        clientRect: getMutableClientRect(element)\n      });\n    });\n  }\n  /** Handles scrolling while a drag is taking place. */\n\n\n  handleScroll(event) {\n    const target = _getEventTarget(event);\n\n    const cachedPosition = this.positions.get(target);\n\n    if (!cachedPosition) {\n      return null;\n    }\n\n    const scrollPosition = cachedPosition.scrollPosition;\n    let newTop;\n    let newLeft;\n\n    if (target === this._document) {\n      const viewportScrollPosition = this.getViewportScrollPosition();\n      newTop = viewportScrollPosition.top;\n      newLeft = viewportScrollPosition.left;\n    } else {\n      newTop = target.scrollTop;\n      newLeft = target.scrollLeft;\n    }\n\n    const topDifference = scrollPosition.top - newTop;\n    const leftDifference = scrollPosition.left - newLeft; // Go through and update the cached positions of the scroll\n    // parents that are inside the element that was scrolled.\n\n    this.positions.forEach((position, node) => {\n      if (position.clientRect && target !== node && target.contains(node)) {\n        adjustClientRect(position.clientRect, topDifference, leftDifference);\n      }\n    });\n    scrollPosition.top = newTop;\n    scrollPosition.left = newLeft;\n    return {\n      top: topDifference,\n      left: leftDifference\n    };\n  }\n  /**\n   * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n   * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n   * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n   * if the element is offset by something like the `BlockScrollStrategy`.\n   */\n\n\n  getViewportScrollPosition() {\n    return {\n      top: window.scrollY,\n      left: window.scrollX\n    };\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Creates a deep clone of an element. */\n\n\nfunction deepCloneNode(node) {\n  const clone = node.cloneNode(true);\n  const descendantsWithId = clone.querySelectorAll('[id]');\n  const nodeName = node.nodeName.toLowerCase(); // Remove the `id` to avoid having multiple elements with the same id on the page.\n\n  clone.removeAttribute('id');\n\n  for (let i = 0; i < descendantsWithId.length; i++) {\n    descendantsWithId[i].removeAttribute('id');\n  }\n\n  if (nodeName === 'canvas') {\n    transferCanvasData(node, clone);\n  } else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n    transferInputData(node, clone);\n  }\n\n  transferData('canvas', node, clone, transferCanvasData);\n  transferData('input, textarea, select', node, clone, transferInputData);\n  return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\n\n\nfunction transferData(selector, node, clone, callback) {\n  const descendantElements = node.querySelectorAll(selector);\n\n  if (descendantElements.length) {\n    const cloneElements = clone.querySelectorAll(selector);\n\n    for (let i = 0; i < descendantElements.length; i++) {\n      callback(descendantElements[i], cloneElements[i]);\n    }\n  }\n} // Counter for unique cloned radio button names.\n\n\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\n\nfunction transferInputData(source, clone) {\n  // Browsers throw an error when assigning the value of a file input programmatically.\n  if (clone.type !== 'file') {\n    clone.value = source.value;\n  } // Radio button `name` attributes must be unique for radio button groups\n  // otherwise original radio buttons can lose their checked state\n  // once the clone is inserted in the DOM.\n\n\n  if (clone.type === 'radio' && clone.name) {\n    clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n  }\n}\n/** Transfers the data of one canvas element to another. */\n\n\nfunction transferCanvasData(source, clone) {\n  const context = clone.getContext('2d');\n\n  if (context) {\n    // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n    // We can't do much about it so just ignore the error.\n    try {\n      context.drawImage(source, 0, 0);\n    } catch (_a) {}\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Options that can be used to bind a passive event listener. */\n\n\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/** Options that can be used to bind an active event listener. */\n\nconst activeEventListenerOptions = normalizePassiveListenerOptions({\n  passive: false\n});\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\n\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\n\nconst dragImportantProperties = new Set([// Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n'position']);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\n\nclass DragRef {\n  constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._config = _config;\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n    /**\n     * CSS `transform` applied to the element when it isn't being dragged. We need a\n     * passive transform in order for the dragged element to retain its new position\n     * after the user has stopped dragging and because we need to know the relative\n     * position in case they start dragging again. This corresponds to `element.style.transform`.\n     */\n\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n    /** CSS `transform` that is applied to the element while it's being dragged. */\n\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * Whether the dragging sequence has been started. Doesn't\n     * necessarily mean that the element has been moved.\n     */\n\n    this._hasStartedDragging = false;\n    /** Emits when the item is being moved. */\n\n    this._moveEvents = new Subject();\n    /** Subscription to pointer movement events. */\n\n    this._pointerMoveSubscription = Subscription.EMPTY;\n    /** Subscription to the event that is dispatched when the user lifts their pointer. */\n\n    this._pointerUpSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being scrolled. */\n\n    this._scrollSubscription = Subscription.EMPTY;\n    /** Subscription to the viewport being resized. */\n\n    this._resizeSubscription = Subscription.EMPTY;\n    /** Cached reference to the boundary element. */\n\n    this._boundaryElement = null;\n    /** Whether the native dragging interactions have been enabled on the root element. */\n\n    this._nativeInteractionsEnabled = true;\n    /** Elements that can be used to drag the draggable item. */\n\n    this._handles = [];\n    /** Registered handles that are currently disabled. */\n\n    this._disabledHandles = new Set();\n    /** Layout direction of the item. */\n\n    this._direction = 'ltr';\n    /**\n     * Amount of milliseconds to wait after the user has put their\n     * pointer down before starting to drag the element.\n     */\n\n    this.dragStartDelay = 0;\n    this._disabled = false;\n    /** Emits as the drag sequence is being prepared. */\n\n    this.beforeStarted = new Subject();\n    /** Emits when the user starts dragging the item. */\n\n    this.started = new Subject();\n    /** Emits when the user has released a drag item, before any animations have started. */\n\n    this.released = new Subject();\n    /** Emits when the user stops dragging an item in the container. */\n\n    this.ended = new Subject();\n    /** Emits when the user has moved the item into a new container. */\n\n    this.entered = new Subject();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n\n    this.exited = new Subject();\n    /** Emits when the user drops the item inside a container. */\n\n    this.dropped = new Subject();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n\n    this.moved = this._moveEvents;\n    /** Handler for the `mousedown`/`touchstart` events. */\n\n    this._pointerDown = event => {\n      this.beforeStarted.next(); // Delegate the event based on whether it started from a handle or the element itself.\n\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          this._initializeDragSequence(targetHandle, event);\n        }\n      } else if (!this.disabled) {\n        this._initializeDragSequence(this._rootElement, event);\n      }\n    };\n    /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n\n\n    this._pointerMove = event => {\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      if (!this._hasStartedDragging) {\n        const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n        const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n        const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold; // Only start dragging after the user has moved more than the minimum distance in either\n        // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n        // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n        // per pixel of movement (e.g. if the user moves their pointer quickly).\n\n        if (isOverThreshold) {\n          const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n\n          const container = this._dropContainer;\n\n          if (!isDelayElapsed) {\n            this._endDragSequence(event);\n\n            return;\n          } // Prevent other drag sequences from starting while something in the container is still\n          // being dragged. This can happen while we're waiting for the drop animation to finish\n          // and can cause errors, because some elements might still be moving around.\n\n\n          if (!container || !container.isDragging() && !container.isReceiving()) {\n            // Prevent the default action as soon as the dragging sequence is considered as\n            // \"started\" since waiting for the next event can allow the device to begin scrolling.\n            event.preventDefault();\n            this._hasStartedDragging = true;\n\n            this._ngZone.run(() => this._startDragSequence(event));\n          }\n        }\n\n        return;\n      } // We prevent the default action down here so that we know that dragging has started. This is\n      // important for touch devices where doing this too early can unnecessarily block scrolling,\n      // if there's a dragging delay.\n\n\n      event.preventDefault();\n\n      const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n\n      this._hasMoved = true;\n      this._lastKnownPointerPosition = pointerPosition;\n\n      this._updatePointerDirectionDelta(constrainedPointerPosition);\n\n      if (this._dropContainer) {\n        this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n      } else {\n        // If there's a position constraint function, we want the element's top/left to be at the\n        // specific position on the page. Use the initial position as a reference if that's the case.\n        const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n        const activeTransform = this._activeTransform;\n        activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n        activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n\n        this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n      } // Since this event gets fired for every pixel while dragging, we only\n      // want to fire it if the consumer opted into it. Also we have to\n      // re-enter the zone because we run all of the events on the outside.\n\n\n      if (this._moveEvents.observers.length) {\n        this._ngZone.run(() => {\n          this._moveEvents.next({\n            source: this,\n            pointerPosition: constrainedPointerPosition,\n            event,\n            distance: this._getDragDistance(constrainedPointerPosition),\n            delta: this._pointerDirectionDelta\n          });\n        });\n      }\n    };\n    /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n\n\n    this._pointerUp = event => {\n      this._endDragSequence(event);\n    };\n    /** Handles a native `dragstart` event. */\n\n\n    this._nativeDragStart = event => {\n      if (this._handles.length) {\n        const targetHandle = this._getTargetHandle(event);\n\n        if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n          event.preventDefault();\n        }\n      } else if (!this.disabled) {\n        // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n        // but some cases like dragging of links can slip through (see #24403).\n        event.preventDefault();\n      }\n    };\n\n    this.withRootElement(element).withParent(_config.parentDragRef || null);\n    this._parentPositions = new ParentPositionTracker(_document);\n\n    _dragDropRegistry.registerDragItem(this);\n  }\n  /** Whether starting to drag this element is disabled. */\n\n\n  get disabled() {\n    return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n  }\n\n  set disabled(value) {\n    const newValue = coerceBooleanProperty(value);\n\n    if (newValue !== this._disabled) {\n      this._disabled = newValue;\n\n      this._toggleNativeDragInteractions();\n\n      this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n    }\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n\n\n  getPlaceholderElement() {\n    return this._placeholder;\n  }\n  /** Returns the root draggable element. */\n\n\n  getRootElement() {\n    return this._rootElement;\n  }\n  /**\n   * Gets the currently-visible element that represents the drag item.\n   * While dragging this is the placeholder, otherwise it's the root element.\n   */\n\n\n  getVisibleElement() {\n    return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n  }\n  /** Registers the handles that can be used to drag the element. */\n\n\n  withHandles(handles) {\n    this._handles = handles.map(handle => coerceElement(handle));\n\n    this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n\n    this._toggleNativeDragInteractions(); // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n    // the set, rather than iterate over it and filter out the destroyed handles, because while\n    // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n    // use an array internally which may throw an error.\n\n\n    const disabledHandles = new Set();\n\n    this._disabledHandles.forEach(handle => {\n      if (this._handles.indexOf(handle) > -1) {\n        disabledHandles.add(handle);\n      }\n    });\n\n    this._disabledHandles = disabledHandles;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag preview.\n   * @param template Template that from which to stamp out the preview.\n   */\n\n\n  withPreviewTemplate(template) {\n    this._previewTemplate = template;\n    return this;\n  }\n  /**\n   * Registers the template that should be used for the drag placeholder.\n   * @param template Template that from which to stamp out the placeholder.\n   */\n\n\n  withPlaceholderTemplate(template) {\n    this._placeholderTemplate = template;\n    return this;\n  }\n  /**\n   * Sets an alternate drag root element. The root element is the element that will be moved as\n   * the user is dragging. Passing an alternate root element is useful when trying to enable\n   * dragging on an element that you might not have access to.\n   */\n\n\n  withRootElement(rootElement) {\n    const element = coerceElement(rootElement);\n\n    if (element !== this._rootElement) {\n      if (this._rootElement) {\n        this._removeRootElementListeners(this._rootElement);\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n      });\n\n      this._initialTransform = undefined;\n      this._rootElement = element;\n    }\n\n    if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n      this._ownerSVGElement = this._rootElement.ownerSVGElement;\n    }\n\n    return this;\n  }\n  /**\n   * Element to which the draggable's position will be constrained.\n   */\n\n\n  withBoundaryElement(boundaryElement) {\n    this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n\n    this._resizeSubscription.unsubscribe();\n\n    if (boundaryElement) {\n      this._resizeSubscription = this._viewportRuler.change(10).subscribe(() => this._containInsideBoundaryOnResize());\n    }\n\n    return this;\n  }\n  /** Sets the parent ref that the ref is nested in.  */\n\n\n  withParent(parent) {\n    this._parentDragRef = parent;\n    return this;\n  }\n  /** Removes the dragging functionality from the DOM element. */\n\n\n  dispose() {\n    var _a, _b;\n\n    this._removeRootElementListeners(this._rootElement); // Do this check before removing from the registry since it'll\n    // stop being considered as dragged once it is removed.\n\n\n    if (this.isDragging()) {\n      // Since we move out the element to the end of the body while it's being\n      // dragged, we have to make sure that it's removed if it gets destroyed.\n      (_a = this._rootElement) === null || _a === void 0 ? void 0 : _a.remove();\n    }\n\n    (_b = this._anchor) === null || _b === void 0 ? void 0 : _b.remove();\n\n    this._destroyPreview();\n\n    this._destroyPlaceholder();\n\n    this._dragDropRegistry.removeDragItem(this);\n\n    this._removeSubscriptions();\n\n    this.beforeStarted.complete();\n    this.started.complete();\n    this.released.complete();\n    this.ended.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n\n    this._moveEvents.complete();\n\n    this._handles = [];\n\n    this._disabledHandles.clear();\n\n    this._dropContainer = undefined;\n\n    this._resizeSubscription.unsubscribe();\n\n    this._parentPositions.clear();\n\n    this._boundaryElement = this._rootElement = this._ownerSVGElement = this._placeholderTemplate = this._previewTemplate = this._anchor = this._parentDragRef = null;\n  }\n  /** Checks whether the element is currently being dragged. */\n\n\n  isDragging() {\n    return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n  }\n  /** Resets a standalone drag item to its initial position. */\n\n\n  reset() {\n    this._rootElement.style.transform = this._initialTransform || '';\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform = {\n      x: 0,\n      y: 0\n    };\n  }\n  /**\n   * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n   * @param handle Handle element that should be disabled.\n   */\n\n\n  disableHandle(handle) {\n    if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n      this._disabledHandles.add(handle);\n\n      toggleNativeDragInteractions(handle, true);\n    }\n  }\n  /**\n   * Enables a handle, if it has been disabled.\n   * @param handle Handle element to be enabled.\n   */\n\n\n  enableHandle(handle) {\n    if (this._disabledHandles.has(handle)) {\n      this._disabledHandles.delete(handle);\n\n      toggleNativeDragInteractions(handle, this.disabled);\n    }\n  }\n  /** Sets the layout direction of the draggable item. */\n\n\n  withDirection(direction) {\n    this._direction = direction;\n    return this;\n  }\n  /** Sets the container that the item is part of. */\n\n\n  _withDropContainer(container) {\n    this._dropContainer = container;\n  }\n  /**\n   * Gets the current position in pixels the draggable outside of a drop container.\n   */\n\n\n  getFreeDragPosition() {\n    const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n    return {\n      x: position.x,\n      y: position.y\n    };\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n\n\n  setFreeDragPosition(value) {\n    this._activeTransform = {\n      x: 0,\n      y: 0\n    };\n    this._passiveTransform.x = value.x;\n    this._passiveTransform.y = value.y;\n\n    if (!this._dropContainer) {\n      this._applyRootElementTransform(value.x, value.y);\n    }\n\n    return this;\n  }\n  /**\n   * Sets the container into which to insert the preview element.\n   * @param value Container into which to insert the preview.\n   */\n\n\n  withPreviewContainer(value) {\n    this._previewContainer = value;\n    return this;\n  }\n  /** Updates the item's sort order based on the last-known pointer position. */\n\n\n  _sortFromLastPointerPosition() {\n    const position = this._lastKnownPointerPosition;\n\n    if (position && this._dropContainer) {\n      this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n    }\n  }\n  /** Unsubscribes from the global subscriptions. */\n\n\n  _removeSubscriptions() {\n    this._pointerMoveSubscription.unsubscribe();\n\n    this._pointerUpSubscription.unsubscribe();\n\n    this._scrollSubscription.unsubscribe();\n  }\n  /** Destroys the preview element and its ViewRef. */\n\n\n  _destroyPreview() {\n    var _a, _b;\n\n    (_a = this._preview) === null || _a === void 0 ? void 0 : _a.remove();\n    (_b = this._previewRef) === null || _b === void 0 ? void 0 : _b.destroy();\n    this._preview = this._previewRef = null;\n  }\n  /** Destroys the placeholder element and its ViewRef. */\n\n\n  _destroyPlaceholder() {\n    var _a, _b;\n\n    (_a = this._placeholder) === null || _a === void 0 ? void 0 : _a.remove();\n    (_b = this._placeholderRef) === null || _b === void 0 ? void 0 : _b.destroy();\n    this._placeholder = this._placeholderRef = null;\n  }\n  /**\n   * Clears subscriptions and stops the dragging sequence.\n   * @param event Browser event object that ended the sequence.\n   */\n\n\n  _endDragSequence(event) {\n    // Note that here we use `isDragging` from the service, rather than from `this`.\n    // The difference is that the one from the service reflects whether a dragging sequence\n    // has been initiated, whereas the one on `this` includes whether the user has passed\n    // the minimum dragging threshold.\n    if (!this._dragDropRegistry.isDragging(this)) {\n      return;\n    }\n\n    this._removeSubscriptions();\n\n    this._dragDropRegistry.stopDragging(this);\n\n    this._toggleNativeDragInteractions();\n\n    if (this._handles) {\n      this._rootElement.style.webkitTapHighlightColor = this._rootElementTapHighlight;\n    }\n\n    if (!this._hasStartedDragging) {\n      return;\n    }\n\n    this.released.next({\n      source: this,\n      event\n    });\n\n    if (this._dropContainer) {\n      // Stop scrolling immediately, instead of waiting for the animation to finish.\n      this._dropContainer._stopScrolling();\n\n      this._animatePreviewToPlaceholder().then(() => {\n        this._cleanupDragArtifacts(event);\n\n        this._cleanupCachedDimensions();\n\n        this._dragDropRegistry.stopDragging(this);\n      });\n    } else {\n      // Convert the active transform into a passive one. This means that next time\n      // the user starts dragging the item, its position will be calculated relatively\n      // to the new passive transform.\n      this._passiveTransform.x = this._activeTransform.x;\n\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      this._passiveTransform.y = this._activeTransform.y;\n\n      this._ngZone.run(() => {\n        this.ended.next({\n          source: this,\n          distance: this._getDragDistance(pointerPosition),\n          dropPoint: pointerPosition,\n          event\n        });\n      });\n\n      this._cleanupCachedDimensions();\n\n      this._dragDropRegistry.stopDragging(this);\n    }\n  }\n  /** Starts the dragging sequence. */\n\n\n  _startDragSequence(event) {\n    if (isTouchEvent(event)) {\n      this._lastTouchEventTime = Date.now();\n    }\n\n    this._toggleNativeDragInteractions();\n\n    const dropContainer = this._dropContainer;\n\n    if (dropContainer) {\n      const element = this._rootElement;\n      const parent = element.parentNode;\n\n      const placeholder = this._placeholder = this._createPlaceholderElement();\n\n      const anchor = this._anchor = this._anchor || this._document.createComment(''); // Needs to happen before the root element is moved.\n\n\n      const shadowRoot = this._getShadowRoot(); // Insert an anchor node so that we can restore the element's position in the DOM.\n\n\n      parent.insertBefore(anchor, element); // There's no risk of transforms stacking when inside a drop container so\n      // we can keep the initial transform up to date any time dragging starts.\n\n      this._initialTransform = element.style.transform || ''; // Create the preview after the initial transform has\n      // been cached, because it can be affected by the transform.\n\n      this._preview = this._createPreviewElement(); // We move the element out at the end of the body and we make it hidden, because keeping it in\n      // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n      // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n\n      toggleVisibility(element, false, dragImportantProperties);\n\n      this._document.body.appendChild(parent.replaceChild(placeholder, element));\n\n      this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n\n      this.started.next({\n        source: this,\n        event\n      }); // Emit before notifying the container.\n\n      dropContainer.start();\n      this._initialContainer = dropContainer;\n      this._initialIndex = dropContainer.getItemIndex(this);\n    } else {\n      this.started.next({\n        source: this,\n        event\n      });\n      this._initialContainer = this._initialIndex = undefined;\n    } // Important to run after we've called `start` on the parent container\n    // so that it has had time to resolve its scrollable parents.\n\n\n    this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n  }\n  /**\n   * Sets up the different variables and subscriptions\n   * that will be necessary for the dragging sequence.\n   * @param referenceElement Element that started the drag sequence.\n   * @param event Browser event object that started the sequence.\n   */\n\n\n  _initializeDragSequence(referenceElement, event) {\n    // Stop propagation if the item is inside another\n    // draggable so we don't start multiple drag sequences.\n    if (this._parentDragRef) {\n      event.stopPropagation();\n    }\n\n    const isDragging = this.isDragging();\n    const isTouchSequence = isTouchEvent(event);\n    const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n    const rootElement = this._rootElement;\n\n    const target = _getEventTarget(event);\n\n    const isSyntheticEvent = !isTouchSequence && this._lastTouchEventTime && this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n    const isFakeEvent = isTouchSequence ? isFakeTouchstartFromScreenReader(event) : isFakeMousedownFromScreenReader(event); // If the event started from an element with the native HTML drag&drop, it'll interfere\n    // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n    // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n    // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n    // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n    // events from firing on touch devices.\n\n    if (target && target.draggable && event.type === 'mousedown') {\n      event.preventDefault();\n    } // Abort if the user is already dragging or is using a mouse button other than the primary one.\n\n\n    if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n      return;\n    } // If we've got handles, we need to disable the tap highlight on the entire root element,\n    // otherwise iOS will still add it, even though all the drag interactions on the handle\n    // are disabled.\n\n\n    if (this._handles.length) {\n      const rootStyles = rootElement.style;\n      this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n      rootStyles.webkitTapHighlightColor = 'transparent';\n    }\n\n    this._hasStartedDragging = this._hasMoved = false; // Avoid multiple subscriptions and memory leaks when multi touch\n    // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n\n    this._removeSubscriptions();\n\n    this._initialClientRect = this._rootElement.getBoundingClientRect();\n    this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n    this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n    this._scrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n\n    if (this._boundaryElement) {\n      this._boundaryRect = getMutableClientRect(this._boundaryElement);\n    } // If we have a custom preview we can't know ahead of time how large it'll be so we position\n    // it next to the cursor. The exception is when the consumer has opted into making the preview\n    // the same size as the root element, in which case we do know the size.\n\n\n    const previewTemplate = this._previewTemplate;\n    this._pickupPositionInElement = previewTemplate && previewTemplate.template && !previewTemplate.matchSize ? {\n      x: 0,\n      y: 0\n    } : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n\n    const pointerPosition = this._pickupPositionOnPage = this._lastKnownPointerPosition = this._getPointerPositionOnPage(event);\n\n    this._pointerDirectionDelta = {\n      x: 0,\n      y: 0\n    };\n    this._pointerPositionAtLastDirectionChange = {\n      x: pointerPosition.x,\n      y: pointerPosition.y\n    };\n    this._dragStartTime = Date.now();\n\n    this._dragDropRegistry.startDragging(this, event);\n  }\n  /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n\n\n  _cleanupDragArtifacts(event) {\n    // Restore the element's visibility and insert it at its old position in the DOM.\n    // It's important that we maintain the position, because moving the element around in the DOM\n    // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n    // while moving the existing elements in all other cases.\n    toggleVisibility(this._rootElement, true, dragImportantProperties);\n\n    this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n\n    this._destroyPreview();\n\n    this._destroyPlaceholder();\n\n    this._initialClientRect = this._boundaryRect = this._previewRect = this._initialTransform = undefined; // Re-enter the NgZone since we bound `document` events on the outside.\n\n    this._ngZone.run(() => {\n      const container = this._dropContainer;\n      const currentIndex = container.getItemIndex(this);\n\n      const pointerPosition = this._getPointerPositionOnPage(event);\n\n      const distance = this._getDragDistance(pointerPosition);\n\n      const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n\n      this.ended.next({\n        source: this,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      this.dropped.next({\n        item: this,\n        currentIndex,\n        previousIndex: this._initialIndex,\n        container: container,\n        previousContainer: this._initialContainer,\n        isPointerOverContainer,\n        distance,\n        dropPoint: pointerPosition,\n        event\n      });\n      container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition);\n      this._dropContainer = this._initialContainer;\n    });\n  }\n  /**\n   * Updates the item's position in its drop container, or moves it\n   * into a new one, depending on its current drag position.\n   */\n\n\n  _updateActiveDropContainer({\n    x,\n    y\n  }, {\n    x: rawX,\n    y: rawY\n  }) {\n    // Drop container that draggable has been moved into.\n    let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y); // If we couldn't find a new container to move the item into, and the item has left its\n    // initial container, check whether the it's over the initial container. This handles the\n    // case where two containers are connected one way and the user tries to undo dragging an\n    // item into a new container.\n\n\n    if (!newContainer && this._dropContainer !== this._initialContainer && this._initialContainer._isOverContainer(x, y)) {\n      newContainer = this._initialContainer;\n    }\n\n    if (newContainer && newContainer !== this._dropContainer) {\n      this._ngZone.run(() => {\n        // Notify the old container that the item has left.\n        this.exited.next({\n          item: this,\n          container: this._dropContainer\n        });\n\n        this._dropContainer.exit(this); // Notify the new container that the item has entered.\n\n\n        this._dropContainer = newContainer;\n\n        this._dropContainer.enter(this, x, y, newContainer === this._initialContainer && // If we're re-entering the initial container and sorting is disabled,\n        // put item the into its starting index to begin with.\n        newContainer.sortingDisabled ? this._initialIndex : undefined);\n\n        this.entered.next({\n          item: this,\n          container: newContainer,\n          currentIndex: newContainer.getItemIndex(this)\n        });\n      });\n    } // Dragging may have been interrupted as a result of the events above.\n\n\n    if (this.isDragging()) {\n      this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n\n      this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n\n      if (this.constrainPosition) {\n        this._applyPreviewTransform(x, y);\n      } else {\n        this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n      }\n    }\n  }\n  /**\n   * Creates the element that will be rendered next to the user's pointer\n   * and will be used as a preview of the element that is being dragged.\n   */\n\n\n  _createPreviewElement() {\n    const previewConfig = this._previewTemplate;\n    const previewClass = this.previewClass;\n    const previewTemplate = previewConfig ? previewConfig.template : null;\n    let preview;\n\n    if (previewTemplate && previewConfig) {\n      // Measure the element before we've inserted the preview\n      // since the insertion could throw off the measurement.\n      const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n      const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n      viewRef.detectChanges();\n      preview = getRootNode(viewRef, this._document);\n      this._previewRef = viewRef;\n\n      if (previewConfig.matchSize) {\n        matchElementSize(preview, rootRect);\n      } else {\n        preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n      }\n    } else {\n      preview = deepCloneNode(this._rootElement);\n      matchElementSize(preview, this._initialClientRect);\n\n      if (this._initialTransform) {\n        preview.style.transform = this._initialTransform;\n      }\n    }\n\n    extendStyles(preview.style, {\n      // It's important that we disable the pointer events on the preview, because\n      // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n      'pointer-events': 'none',\n      // We have to reset the margin, because it can throw off positioning relative to the viewport.\n      'margin': '0',\n      'position': 'fixed',\n      'top': '0',\n      'left': '0',\n      'z-index': `${this._config.zIndex || 1000}`\n    }, dragImportantProperties);\n    toggleNativeDragInteractions(preview, false);\n    preview.classList.add('cdk-drag-preview');\n    preview.setAttribute('dir', this._direction);\n\n    if (previewClass) {\n      if (Array.isArray(previewClass)) {\n        previewClass.forEach(className => preview.classList.add(className));\n      } else {\n        preview.classList.add(previewClass);\n      }\n    }\n\n    return preview;\n  }\n  /**\n   * Animates the preview element from its current position to the location of the drop placeholder.\n   * @returns Promise that resolves when the animation completes.\n   */\n\n\n  _animatePreviewToPlaceholder() {\n    // If the user hasn't moved yet, the transitionend event won't fire.\n    if (!this._hasMoved) {\n      return Promise.resolve();\n    }\n\n    const placeholderRect = this._placeholder.getBoundingClientRect(); // Apply the class that adds a transition to the preview.\n\n\n    this._preview.classList.add('cdk-drag-animating'); // Move the preview to the placeholder position.\n\n\n    this._applyPreviewTransform(placeholderRect.left, placeholderRect.top); // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n    // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n    // apply its style, we take advantage of the available info to figure out whether we need to\n    // bind the event in the first place.\n\n\n    const duration = getTransformTransitionDurationInMs(this._preview);\n\n    if (duration === 0) {\n      return Promise.resolve();\n    }\n\n    return this._ngZone.runOutsideAngular(() => {\n      return new Promise(resolve => {\n        const handler = event => {\n          var _a;\n\n          if (!event || _getEventTarget(event) === this._preview && event.propertyName === 'transform') {\n            (_a = this._preview) === null || _a === void 0 ? void 0 : _a.removeEventListener('transitionend', handler);\n            resolve();\n            clearTimeout(timeout);\n          }\n        }; // If a transition is short enough, the browser might not fire the `transitionend` event.\n        // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n        // fire if the transition hasn't completed when it was supposed to.\n\n\n        const timeout = setTimeout(handler, duration * 1.5);\n\n        this._preview.addEventListener('transitionend', handler);\n      });\n    });\n  }\n  /** Creates an element that will be shown instead of the current element while dragging. */\n\n\n  _createPlaceholderElement() {\n    const placeholderConfig = this._placeholderTemplate;\n    const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n    let placeholder;\n\n    if (placeholderTemplate) {\n      this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n\n      this._placeholderRef.detectChanges();\n\n      placeholder = getRootNode(this._placeholderRef, this._document);\n    } else {\n      placeholder = deepCloneNode(this._rootElement);\n    } // Stop pointer events on the preview so the user can't\n    // interact with it while the preview is animating.\n\n\n    placeholder.style.pointerEvents = 'none';\n    placeholder.classList.add('cdk-drag-placeholder');\n    return placeholder;\n  }\n  /**\n   * Figures out the coordinates at which an element was picked up.\n   * @param referenceElement Element that initiated the dragging.\n   * @param event Event that initiated the dragging.\n   */\n\n\n  _getPointerPositionInElement(elementRect, referenceElement, event) {\n    const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n    const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n    const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n\n    const scrollPosition = this._getViewportScrollPosition();\n\n    const x = point.pageX - referenceRect.left - scrollPosition.left;\n    const y = point.pageY - referenceRect.top - scrollPosition.top;\n    return {\n      x: referenceRect.left - elementRect.left + x,\n      y: referenceRect.top - elementRect.top + y\n    };\n  }\n  /** Determines the point of the page that was touched by the user. */\n\n\n  _getPointerPositionOnPage(event) {\n    const scrollPosition = this._getViewportScrollPosition();\n\n    const point = isTouchEvent(event) ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n    // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n    // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n    // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n    // throwing an error. The value returned here will be incorrect, but since this only\n    // breaks inside a developer tool and the value is only used for secondary information,\n    // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n    event.touches[0] || event.changedTouches[0] || {\n      pageX: 0,\n      pageY: 0\n    } : event;\n    const x = point.pageX - scrollPosition.left;\n    const y = point.pageY - scrollPosition.top; // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n    // coordinate system\n\n    if (this._ownerSVGElement) {\n      const svgMatrix = this._ownerSVGElement.getScreenCTM();\n\n      if (svgMatrix) {\n        const svgPoint = this._ownerSVGElement.createSVGPoint();\n\n        svgPoint.x = x;\n        svgPoint.y = y;\n        return svgPoint.matrixTransform(svgMatrix.inverse());\n      }\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Gets the pointer position on the page, accounting for any position constraints. */\n\n\n  _getConstrainedPointerPosition(point) {\n    const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n    let {\n      x,\n      y\n    } = this.constrainPosition ? this.constrainPosition(point, this, this._initialClientRect) : point;\n\n    if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n      y = this._pickupPositionOnPage.y;\n    } else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n      x = this._pickupPositionOnPage.x;\n    }\n\n    if (this._boundaryRect) {\n      const {\n        x: pickupX,\n        y: pickupY\n      } = this._pickupPositionInElement;\n      const boundaryRect = this._boundaryRect;\n\n      const {\n        width: previewWidth,\n        height: previewHeight\n      } = this._getPreviewRect();\n\n      const minY = boundaryRect.top + pickupY;\n      const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n      const minX = boundaryRect.left + pickupX;\n      const maxX = boundaryRect.right - (previewWidth - pickupX);\n      x = clamp$1(x, minX, maxX);\n      y = clamp$1(y, minY, maxY);\n    }\n\n    return {\n      x,\n      y\n    };\n  }\n  /** Updates the current drag delta, based on the user's current pointer position on the page. */\n\n\n  _updatePointerDirectionDelta(pointerPositionOnPage) {\n    const {\n      x,\n      y\n    } = pointerPositionOnPage;\n    const delta = this._pointerDirectionDelta;\n    const positionSinceLastChange = this._pointerPositionAtLastDirectionChange; // Amount of pixels the user has dragged since the last time the direction changed.\n\n    const changeX = Math.abs(x - positionSinceLastChange.x);\n    const changeY = Math.abs(y - positionSinceLastChange.y); // Because we handle pointer events on a per-pixel basis, we don't want the delta\n    // to change for every pixel, otherwise anything that depends on it can look erratic.\n    // To make the delta more consistent, we track how much the user has moved since the last\n    // delta change and we only update it after it has reached a certain threshold.\n\n    if (changeX > this._config.pointerDirectionChangeThreshold) {\n      delta.x = x > positionSinceLastChange.x ? 1 : -1;\n      positionSinceLastChange.x = x;\n    }\n\n    if (changeY > this._config.pointerDirectionChangeThreshold) {\n      delta.y = y > positionSinceLastChange.y ? 1 : -1;\n      positionSinceLastChange.y = y;\n    }\n\n    return delta;\n  }\n  /** Toggles the native drag interactions, based on how many handles are registered. */\n\n\n  _toggleNativeDragInteractions() {\n    if (!this._rootElement || !this._handles) {\n      return;\n    }\n\n    const shouldEnable = this._handles.length > 0 || !this.isDragging();\n\n    if (shouldEnable !== this._nativeInteractionsEnabled) {\n      this._nativeInteractionsEnabled = shouldEnable;\n      toggleNativeDragInteractions(this._rootElement, shouldEnable);\n    }\n  }\n  /** Removes the manually-added event listeners from the root element. */\n\n\n  _removeRootElementListeners(element) {\n    element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n    element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n    element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n  }\n  /**\n   * Applies a `transform` to the root element, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n\n\n  _applyRootElementTransform(x, y) {\n    const transform = getTransform(x, y);\n    const styles = this._rootElement.style; // Cache the previous transform amount only after the first drag sequence, because\n    // we don't want our own transforms to stack on top of each other.\n    // Should be excluded none because none + translate3d(x, y, x) is invalid css\n\n    if (this._initialTransform == null) {\n      this._initialTransform = styles.transform && styles.transform != 'none' ? styles.transform : '';\n    } // Preserve the previous `transform` value, if there was one. Note that we apply our own\n    // transform before the user's, because things like rotation can affect which direction\n    // the element will be translated towards.\n\n\n    styles.transform = combineTransforms(transform, this._initialTransform);\n  }\n  /**\n   * Applies a `transform` to the preview, taking into account any existing transforms on it.\n   * @param x New transform value along the X axis.\n   * @param y New transform value along the Y axis.\n   */\n\n\n  _applyPreviewTransform(x, y) {\n    var _a; // Only apply the initial transform if the preview is a clone of the original element, otherwise\n    // it could be completely different and the transform might not make sense anymore.\n\n\n    const initialTransform = ((_a = this._previewTemplate) === null || _a === void 0 ? void 0 : _a.template) ? undefined : this._initialTransform;\n    const transform = getTransform(x, y);\n    this._preview.style.transform = combineTransforms(transform, initialTransform);\n  }\n  /**\n   * Gets the distance that the user has dragged during the current drag sequence.\n   * @param currentPosition Current position of the user's pointer.\n   */\n\n\n  _getDragDistance(currentPosition) {\n    const pickupPosition = this._pickupPositionOnPage;\n\n    if (pickupPosition) {\n      return {\n        x: currentPosition.x - pickupPosition.x,\n        y: currentPosition.y - pickupPosition.y\n      };\n    }\n\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n\n\n  _cleanupCachedDimensions() {\n    this._boundaryRect = this._previewRect = undefined;\n\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the element is still inside its boundary after the viewport has been resized.\n   * If not, the position is adjusted so that the element fits again.\n   */\n\n\n  _containInsideBoundaryOnResize() {\n    let {\n      x,\n      y\n    } = this._passiveTransform;\n\n    if (x === 0 && y === 0 || this.isDragging() || !this._boundaryElement) {\n      return;\n    } // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n\n\n    const elementRect = this._rootElement.getBoundingClientRect();\n\n    const boundaryRect = this._boundaryElement.getBoundingClientRect(); // It's possible that the element got hidden away after dragging (e.g. by switching to a\n    // different tab). Don't do anything in this case so we don't clear the user's position.\n\n\n    if (boundaryRect.width === 0 && boundaryRect.height === 0 || elementRect.width === 0 && elementRect.height === 0) {\n      return;\n    }\n\n    const leftOverflow = boundaryRect.left - elementRect.left;\n    const rightOverflow = elementRect.right - boundaryRect.right;\n    const topOverflow = boundaryRect.top - elementRect.top;\n    const bottomOverflow = elementRect.bottom - boundaryRect.bottom; // If the element has become wider than the boundary, we can't\n    // do much to make it fit so we just anchor it to the left.\n\n    if (boundaryRect.width > elementRect.width) {\n      if (leftOverflow > 0) {\n        x += leftOverflow;\n      }\n\n      if (rightOverflow > 0) {\n        x -= rightOverflow;\n      }\n    } else {\n      x = 0;\n    } // If the element has become taller than the boundary, we can't\n    // do much to make it fit so we just anchor it to the top.\n\n\n    if (boundaryRect.height > elementRect.height) {\n      if (topOverflow > 0) {\n        y += topOverflow;\n      }\n\n      if (bottomOverflow > 0) {\n        y -= bottomOverflow;\n      }\n    } else {\n      y = 0;\n    }\n\n    if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n      this.setFreeDragPosition({\n        y,\n        x\n      });\n    }\n  }\n  /** Gets the drag start delay, based on the event type. */\n\n\n  _getDragStartDelay(event) {\n    const value = this.dragStartDelay;\n\n    if (typeof value === 'number') {\n      return value;\n    } else if (isTouchEvent(event)) {\n      return value.touch;\n    }\n\n    return value ? value.mouse : 0;\n  }\n  /** Updates the internal state of the draggable element when scrolling has occurred. */\n\n\n  _updateOnScroll(event) {\n    const scrollDifference = this._parentPositions.handleScroll(event);\n\n    if (scrollDifference) {\n      const target = _getEventTarget(event); // ClientRect dimensions are based on the scroll position of the page and its parent\n      // node so we have to update the cached boundary ClientRect if the user has scrolled.\n\n\n      if (this._boundaryRect && target !== this._boundaryElement && target.contains(this._boundaryElement)) {\n        adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n      }\n\n      this._pickupPositionOnPage.x += scrollDifference.left;\n      this._pickupPositionOnPage.y += scrollDifference.top; // If we're in free drag mode, we have to update the active transform, because\n      // it isn't relative to the viewport like the preview inside a drop list.\n\n      if (!this._dropContainer) {\n        this._activeTransform.x -= scrollDifference.left;\n        this._activeTransform.y -= scrollDifference.top;\n\n        this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n      }\n    }\n  }\n  /** Gets the scroll position of the viewport. */\n\n\n  _getViewportScrollPosition() {\n    var _a;\n\n    return ((_a = this._parentPositions.positions.get(this._document)) === null || _a === void 0 ? void 0 : _a.scrollPosition) || this._parentPositions.getViewportScrollPosition();\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n\n\n  _getShadowRoot() {\n    if (this._cachedShadowRoot === undefined) {\n      this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n    }\n\n    return this._cachedShadowRoot;\n  }\n  /** Gets the element into which the drag preview should be inserted. */\n\n\n  _getPreviewInsertionPoint(initialParent, shadowRoot) {\n    const previewContainer = this._previewContainer || 'global';\n\n    if (previewContainer === 'parent') {\n      return initialParent;\n    }\n\n    if (previewContainer === 'global') {\n      const documentRef = this._document; // We can't use the body if the user is in fullscreen mode,\n      // because the preview will render under the fullscreen element.\n      // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n\n      return shadowRoot || documentRef.fullscreenElement || documentRef.webkitFullscreenElement || documentRef.mozFullScreenElement || documentRef.msFullscreenElement || documentRef.body;\n    }\n\n    return coerceElement(previewContainer);\n  }\n  /** Lazily resolves and returns the dimensions of the preview. */\n\n\n  _getPreviewRect() {\n    // Cache the preview element rect if we haven't cached it already or if\n    // we cached it too early before the element dimensions were computed.\n    if (!this._previewRect || !this._previewRect.width && !this._previewRect.height) {\n      this._previewRect = this._preview ? this._preview.getBoundingClientRect() : this._initialClientRect;\n    }\n\n    return this._previewRect;\n  }\n  /** Gets a handle that is the target of an event. */\n\n\n  _getTargetHandle(event) {\n    return this._handles.find(handle => {\n      return event.target && (event.target === handle || handle.contains(event.target));\n    });\n  }\n\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\n\n\nfunction getTransform(x, y) {\n  // Round the transforms since some browsers will\n  // blur the elements for sub-pixel transforms.\n  return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\n\n\nfunction clamp$1(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\n\n\nfunction isTouchEvent(event) {\n  // This function is called for every pixel that the user has dragged so we need it to be\n  // as fast as possible. Since we only bind mouse events and touch events, we can assume\n  // that if the event's name starts with `t`, it's a touch event.\n  return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\n\n\nfunction getRootNode(viewRef, _document) {\n  const rootNodes = viewRef.rootNodes;\n\n  if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n    return rootNodes[0];\n  }\n\n  const wrapper = _document.createElement('div');\n\n  rootNodes.forEach(node => wrapper.appendChild(node));\n  return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\n\n\nfunction matchElementSize(target, sourceRect) {\n  target.style.width = `${sourceRect.width}px`;\n  target.style.height = `${sourceRect.height}px`;\n  target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\n\n\nfunction moveItemInArray(array, fromIndex, toIndex) {\n  const from = clamp(fromIndex, array.length - 1);\n  const to = clamp(toIndex, array.length - 1);\n\n  if (from === to) {\n    return;\n  }\n\n  const target = array[from];\n  const delta = to < from ? -1 : 1;\n\n  for (let i = from; i !== to; i += delta) {\n    array[i] = array[i + delta];\n  }\n\n  array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\n\n\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const from = clamp(currentIndex, currentArray.length - 1);\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n  }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\n\n\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n  const to = clamp(targetIndex, targetArray.length);\n\n  if (currentArray.length) {\n    targetArray.splice(to, 0, currentArray[currentIndex]);\n  }\n}\n/** Clamps a number between zero and a maximum. */\n\n\nfunction clamp(value, max) {\n  return Math.max(0, Math.min(max, value));\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\n\n\nclass SingleAxisSortStrategy {\n  constructor(_element, _dragDropRegistry) {\n    this._element = _element;\n    this._dragDropRegistry = _dragDropRegistry;\n    /** Cache of the dimensions of all the items inside the container. */\n\n    this._itemPositions = [];\n    /** Direction in which the list is oriented. */\n\n    this.orientation = 'vertical';\n    /**\n     * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n     * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n     * overlap with the swapped item after the swapping occurred.\n     */\n\n    this._previousSwap = {\n      drag: null,\n      delta: 0,\n      overlaps: false\n    };\n  }\n  /**\n   * To be called when the drag sequence starts.\n   * @param items Items that are currently in the list.\n   */\n\n\n  start(items) {\n    this.withItems(items);\n  }\n  /**\n   * To be called when an item is being sorted.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n\n\n  sort(item, pointerX, pointerY, pointerDelta) {\n    const siblings = this._itemPositions;\n\n    const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n\n    if (newIndex === -1 && siblings.length > 0) {\n      return null;\n    }\n\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n    const siblingAtNewPosition = siblings[newIndex];\n    const currentPosition = siblings[currentIndex].clientRect;\n    const newPosition = siblingAtNewPosition.clientRect;\n    const delta = currentIndex > newIndex ? 1 : -1; // How many pixels the item's placeholder should be offset.\n\n    const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta); // How many pixels all the other items should be offset.\n\n\n    const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta); // Save the previous order of the items before moving the item to its new index.\n    // We use this to check whether an item has been moved as a result of the sorting.\n\n\n    const oldOrder = siblings.slice(); // Shuffle the array in place.\n\n    moveItemInArray(siblings, currentIndex, newIndex);\n    siblings.forEach((sibling, index) => {\n      // Don't do anything if the position hasn't changed.\n      if (oldOrder[index] === sibling) {\n        return;\n      }\n\n      const isDraggedItem = sibling.drag === item;\n      const offset = isDraggedItem ? itemOffset : siblingOffset;\n      const elementToOffset = isDraggedItem ? item.getPlaceholderElement() : sibling.drag.getRootElement(); // Update the offset to reflect the new position.\n\n      sibling.offset += offset; // Since we're moving the items with a `transform`, we need to adjust their cached\n      // client rects to reflect their new position, as well as swap their positions in the cache.\n      // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n      // elements may be mid-animation which will give us a wrong result.\n\n      if (isHorizontal) {\n        // Round the transforms since some browsers will\n        // blur the elements, for sub-pixel transforms.\n        elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, 0, offset);\n      } else {\n        elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n        adjustClientRect(sibling.clientRect, offset, 0);\n      }\n    }); // Note that it's important that we do this after the client rects have been adjusted.\n\n    this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n    this._previousSwap.drag = siblingAtNewPosition.drag;\n    this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n    return {\n      previousIndex: currentIndex,\n      currentIndex: newIndex\n    };\n  }\n  /**\n   * Called when an item is being moved into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n\n\n  enter(item, pointerX, pointerY, index) {\n    const newIndex = index == null || index < 0 ? // We use the coordinates of where the item entered the drop\n    // zone to figure out at which index it should be inserted.\n    this._getItemIndexFromPointerPosition(item, pointerX, pointerY) : index;\n    const activeDraggables = this._activeDraggables;\n    const currentIndex = activeDraggables.indexOf(item);\n    const placeholder = item.getPlaceholderElement();\n    let newPositionReference = activeDraggables[newIndex]; // If the item at the new position is the same as the item that is being dragged,\n    // it means that we're trying to restore the item to its initial position. In this\n    // case we should use the next item from the list as the reference.\n\n    if (newPositionReference === item) {\n      newPositionReference = activeDraggables[newIndex + 1];\n    } // If we didn't find a new position reference, it means that either the item didn't start off\n    // in this container, or that the item requested to be inserted at the end of the list.\n\n\n    if (!newPositionReference && (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) && this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n      newPositionReference = activeDraggables[0];\n    } // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n    // into another container and back again), we have to ensure that it isn't duplicated.\n\n\n    if (currentIndex > -1) {\n      activeDraggables.splice(currentIndex, 1);\n    } // Don't use items that are being dragged as a reference, because\n    // their element has been moved down to the bottom of the body.\n\n\n    if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n      const element = newPositionReference.getRootElement();\n      element.parentElement.insertBefore(placeholder, element);\n      activeDraggables.splice(newIndex, 0, item);\n    } else {\n      coerceElement(this._element).appendChild(placeholder);\n      activeDraggables.push(item);\n    } // The transform needs to be cleared so it doesn't throw off the measurements.\n\n\n    placeholder.style.transform = ''; // Note that usually `start` is called together with `enter` when an item goes into a new\n    // container. This will cache item positions, but we need to refresh them since the amount\n    // of items has changed.\n\n    this._cacheItemPositions();\n  }\n  /** Sets the items that are currently part of the list. */\n\n\n  withItems(items) {\n    this._activeDraggables = items.slice();\n\n    this._cacheItemPositions();\n  }\n  /** Assigns a sort predicate to the strategy. */\n\n\n  withSortPredicate(predicate) {\n    this._sortPredicate = predicate;\n  }\n  /** Resets the strategy to its initial state before dragging was started. */\n\n\n  reset() {\n    // TODO(crisbeto): may have to wait for the animations to finish.\n    this._activeDraggables.forEach(item => {\n      var _a;\n\n      const rootElement = item.getRootElement();\n\n      if (rootElement) {\n        const initialTransform = (_a = this._itemPositions.find(p => p.drag === item)) === null || _a === void 0 ? void 0 : _a.initialTransform;\n        rootElement.style.transform = initialTransform || '';\n      }\n    });\n\n    this._itemPositions = [];\n    this._activeDraggables = [];\n    this._previousSwap.drag = null;\n    this._previousSwap.delta = 0;\n    this._previousSwap.overlaps = false;\n  }\n  /**\n   * Gets a snapshot of items currently in the list.\n   * Can include items that we dragged in from another list.\n   */\n\n\n  getActiveItemsSnapshot() {\n    return this._activeDraggables;\n  }\n  /** Gets the index of a specific item. */\n\n\n  getItemIndex(item) {\n    // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n    // The rest of the logic still stands no matter what orientation we're in, however\n    // we need to invert the array when determining the index.\n    const items = this.orientation === 'horizontal' && this.direction === 'rtl' ? this._itemPositions.slice().reverse() : this._itemPositions;\n    return items.findIndex(currentItem => currentItem.drag === item);\n  }\n  /** Used to notify the strategy that the scroll position has changed. */\n\n\n  updateOnScroll(topDifference, leftDifference) {\n    // Since we know the amount that the user has scrolled we can shift all of the\n    // client rectangles ourselves. This is cheaper than re-measuring everything and\n    // we can avoid inconsistent behavior where we might be measuring the element before\n    // its position has changed.\n    this._itemPositions.forEach(({\n      clientRect\n    }) => {\n      adjustClientRect(clientRect, topDifference, leftDifference);\n    }); // We need two loops for this, because we want all of the cached\n    // positions to be up-to-date before we re-sort the item.\n\n\n    this._itemPositions.forEach(({\n      drag\n    }) => {\n      if (this._dragDropRegistry.isDragging(drag)) {\n        // We need to re-sort the item manually, because the pointer move\n        // events won't be dispatched while the user is scrolling.\n        drag._sortFromLastPointerPosition();\n      }\n    });\n  }\n  /** Refreshes the position cache of the items and sibling containers. */\n\n\n  _cacheItemPositions() {\n    const isHorizontal = this.orientation === 'horizontal';\n    this._itemPositions = this._activeDraggables.map(drag => {\n      const elementToMeasure = drag.getVisibleElement();\n      return {\n        drag,\n        offset: 0,\n        initialTransform: elementToMeasure.style.transform || '',\n        clientRect: getMutableClientRect(elementToMeasure)\n      };\n    }).sort((a, b) => {\n      return isHorizontal ? a.clientRect.left - b.clientRect.left : a.clientRect.top - b.clientRect.top;\n    });\n  }\n  /**\n   * Gets the offset in pixels by which the item that is being dragged should be moved.\n   * @param currentPosition Current position of the item.\n   * @param newPosition Position of the item where the current item should be moved.\n   * @param delta Direction in which the user is moving.\n   */\n\n\n  _getItemOffsetPx(currentPosition, newPosition, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    let itemOffset = isHorizontal ? newPosition.left - currentPosition.left : newPosition.top - currentPosition.top; // Account for differences in the item width/height.\n\n    if (delta === -1) {\n      itemOffset += isHorizontal ? newPosition.width - currentPosition.width : newPosition.height - currentPosition.height;\n    }\n\n    return itemOffset;\n  }\n  /**\n   * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n   * @param currentIndex Index of the item currently being dragged.\n   * @param siblings All of the items in the list.\n   * @param delta Direction in which the user is moving.\n   */\n\n\n  _getSiblingOffsetPx(currentIndex, siblings, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n    const currentPosition = siblings[currentIndex].clientRect;\n    const immediateSibling = siblings[currentIndex + delta * -1];\n    let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n\n    if (immediateSibling) {\n      const start = isHorizontal ? 'left' : 'top';\n      const end = isHorizontal ? 'right' : 'bottom'; // Get the spacing between the start of the current item and the end of the one immediately\n      // after it in the direction in which the user is dragging, or vice versa. We add it to the\n      // offset in order to push the element to where it will be when it's inline and is influenced\n      // by the `margin` of its siblings.\n\n      if (delta === -1) {\n        siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n      } else {\n        siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n      }\n    }\n\n    return siblingOffset;\n  }\n  /**\n   * Checks if pointer is entering in the first position\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   */\n\n\n  _shouldEnterAsFirstChild(pointerX, pointerY) {\n    if (!this._activeDraggables.length) {\n      return false;\n    }\n\n    const itemPositions = this._itemPositions;\n    const isHorizontal = this.orientation === 'horizontal'; // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n    // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n\n    const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n\n    if (reversed) {\n      const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n      return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n    } else {\n      const firstItemRect = itemPositions[0].clientRect;\n      return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n    }\n  }\n  /**\n   * Gets the index of an item in the drop container, based on the position of the user's pointer.\n   * @param item Item that is being sorted.\n   * @param pointerX Position of the user's pointer along the X axis.\n   * @param pointerY Position of the user's pointer along the Y axis.\n   * @param delta Direction in which the user is moving their pointer.\n   */\n\n\n  _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n    const isHorizontal = this.orientation === 'horizontal';\n\n    const index = this._itemPositions.findIndex(({\n      drag,\n      clientRect\n    }) => {\n      // Skip the item itself.\n      if (drag === item) {\n        return false;\n      }\n\n      if (delta) {\n        const direction = isHorizontal ? delta.x : delta.y; // If the user is still hovering over the same item as last time, their cursor hasn't left\n        // the item after we made the swap, and they didn't change the direction in which they're\n        // dragging, we don't consider it a direction swap.\n\n        if (drag === this._previousSwap.drag && this._previousSwap.overlaps && direction === this._previousSwap.delta) {\n          return false;\n        }\n      }\n\n      return isHorizontal ? // Round these down since most browsers report client rects with\n      // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n      pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right) : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n    });\n\n    return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\n\n\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\n\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\n\nclass DropListRef {\n  constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n    this._dragDropRegistry = _dragDropRegistry;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    /** Whether starting a dragging sequence from this container is disabled. */\n\n    this.disabled = false;\n    /** Whether sorting items within the list is disabled. */\n\n    this.sortingDisabled = false;\n    /**\n     * Whether auto-scrolling the view when the user\n     * moves their pointer close to the edges is disabled.\n     */\n\n    this.autoScrollDisabled = false;\n    /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n\n    this.autoScrollStep = 2;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n\n    this.enterPredicate = () => true;\n    /** Function that is used to determine whether an item can be sorted into a particular index. */\n\n\n    this.sortPredicate = () => true;\n    /** Emits right before dragging has started. */\n\n\n    this.beforeStarted = new Subject();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n\n    this.entered = new Subject();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n\n    this.exited = new Subject();\n    /** Emits when the user drops an item inside the container. */\n\n    this.dropped = new Subject();\n    /** Emits as the user is swapping items while actively dragging. */\n\n    this.sorted = new Subject();\n    /** Whether an item in the list is being dragged. */\n\n    this._isDragging = false;\n    /** Draggable items in the container. */\n\n    this._draggables = [];\n    /** Drop lists that are connected to the current one. */\n\n    this._siblings = [];\n    /** Connected siblings that currently have a dragged item. */\n\n    this._activeSiblings = new Set();\n    /** Subscription to the window being scrolled. */\n\n    this._viewportScrollSubscription = Subscription.EMPTY;\n    /** Vertical direction in which the list is currently scrolling. */\n\n    this._verticalScrollDirection = 0\n    /* AutoScrollVerticalDirection.NONE */\n    ;\n    /** Horizontal direction in which the list is currently scrolling. */\n\n    this._horizontalScrollDirection = 0\n    /* AutoScrollHorizontalDirection.NONE */\n    ;\n    /** Used to signal to the current auto-scroll sequence when to stop. */\n\n    this._stopScrollTimers = new Subject();\n    /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n\n    this._cachedShadowRoot = null;\n    /** Starts the interval that'll auto-scroll the element. */\n\n    this._startScrollInterval = () => {\n      this._stopScrolling();\n\n      interval(0, animationFrameScheduler).pipe(takeUntil(this._stopScrollTimers)).subscribe(() => {\n        const node = this._scrollNode;\n        const scrollStep = this.autoScrollStep;\n\n        if (this._verticalScrollDirection === 1\n        /* AutoScrollVerticalDirection.UP */\n        ) {\n          node.scrollBy(0, -scrollStep);\n        } else if (this._verticalScrollDirection === 2\n        /* AutoScrollVerticalDirection.DOWN */\n        ) {\n          node.scrollBy(0, scrollStep);\n        }\n\n        if (this._horizontalScrollDirection === 1\n        /* AutoScrollHorizontalDirection.LEFT */\n        ) {\n          node.scrollBy(-scrollStep, 0);\n        } else if (this._horizontalScrollDirection === 2\n        /* AutoScrollHorizontalDirection.RIGHT */\n        ) {\n          node.scrollBy(scrollStep, 0);\n        }\n      });\n    };\n\n    this.element = coerceElement(element);\n    this._document = _document;\n    this.withScrollableParents([this.element]);\n\n    _dragDropRegistry.registerDropContainer(this);\n\n    this._parentPositions = new ParentPositionTracker(_document);\n    this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n\n    this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n  }\n  /** Removes the drop list functionality from the DOM element. */\n\n\n  dispose() {\n    this._stopScrolling();\n\n    this._stopScrollTimers.complete();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this.beforeStarted.complete();\n    this.entered.complete();\n    this.exited.complete();\n    this.dropped.complete();\n    this.sorted.complete();\n\n    this._activeSiblings.clear();\n\n    this._scrollNode = null;\n\n    this._parentPositions.clear();\n\n    this._dragDropRegistry.removeDropContainer(this);\n  }\n  /** Whether an item from this list is currently being dragged. */\n\n\n  isDragging() {\n    return this._isDragging;\n  }\n  /** Starts dragging an item. */\n\n\n  start() {\n    this._draggingStarted();\n\n    this._notifyReceivingSiblings();\n  }\n  /**\n   * Attempts to move an item into the container.\n   * @param item Item that was moved into the container.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param index Index at which the item entered. If omitted, the container will try to figure it\n   *   out automatically.\n   */\n\n\n  enter(item, pointerX, pointerY, index) {\n    this._draggingStarted(); // If sorting is disabled, we want the item to return to its starting\n    // position if the user is returning it to its initial container.\n\n\n    if (index == null && this.sortingDisabled) {\n      index = this._draggables.indexOf(item);\n    }\n\n    this._sortStrategy.enter(item, pointerX, pointerY, index); // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n    // can change when the sort strategy moves the item around inside `enter`.\n\n\n    this._cacheParentPositions(); // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n\n\n    this._notifyReceivingSiblings();\n\n    this.entered.next({\n      item,\n      container: this,\n      currentIndex: this.getItemIndex(item)\n    });\n  }\n  /**\n   * Removes an item from the container after it was dragged into another container by the user.\n   * @param item Item that was dragged out.\n   */\n\n\n  exit(item) {\n    this._reset();\n\n    this.exited.next({\n      item,\n      container: this\n    });\n  }\n  /**\n   * Drops an item into this container.\n   * @param item Item being dropped into the container.\n   * @param currentIndex Index at which the item should be inserted.\n   * @param previousIndex Index of the item when dragging started.\n   * @param previousContainer Container from which the item got dragged in.\n   * @param isPointerOverContainer Whether the user's pointer was over the\n   *    container when the item was dropped.\n   * @param distance Distance the user has dragged since the start of the dragging sequence.\n   * @param event Event that triggered the dropping sequence.\n   *\n   * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n   */\n\n\n  drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n    this._reset();\n\n    this.dropped.next({\n      item,\n      currentIndex,\n      previousIndex,\n      container: this,\n      previousContainer,\n      isPointerOverContainer,\n      distance,\n      dropPoint,\n      event\n    });\n  }\n  /**\n   * Sets the draggable items that are a part of this list.\n   * @param items Items that are a part of this list.\n   */\n\n\n  withItems(items) {\n    const previousItems = this._draggables;\n    this._draggables = items;\n    items.forEach(item => item._withDropContainer(this));\n\n    if (this.isDragging()) {\n      const draggedItems = previousItems.filter(item => item.isDragging()); // If all of the items being dragged were removed\n      // from the list, abort the current drag sequence.\n\n      if (draggedItems.every(item => items.indexOf(item) === -1)) {\n        this._reset();\n      } else {\n        this._sortStrategy.withItems(this._draggables);\n      }\n    }\n\n    return this;\n  }\n  /** Sets the layout direction of the drop list. */\n\n\n  withDirection(direction) {\n    this._sortStrategy.direction = direction;\n    return this;\n  }\n  /**\n   * Sets the containers that are connected to this one. When two or more containers are\n   * connected, the user will be allowed to transfer items between them.\n   * @param connectedTo Other containers that the current containers should be connected to.\n   */\n\n\n  connectedTo(connectedTo) {\n    this._siblings = connectedTo.slice();\n    return this;\n  }\n  /**\n   * Sets the orientation of the container.\n   * @param orientation New orientation for the container.\n   */\n\n\n  withOrientation(orientation) {\n    // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n    // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n    this._sortStrategy.orientation = orientation;\n    return this;\n  }\n  /**\n   * Sets which parent elements are can be scrolled while the user is dragging.\n   * @param elements Elements that can be scrolled.\n   */\n\n\n  withScrollableParents(elements) {\n    const element = coerceElement(this.element); // We always allow the current element to be scrollable\n    // so we need to ensure that it's in the array.\n\n    this._scrollableElements = elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n    return this;\n  }\n  /** Gets the scrollable parents that are registered with this drop container. */\n\n\n  getScrollableParents() {\n    return this._scrollableElements;\n  }\n  /**\n   * Figures out the index of an item in the container.\n   * @param item Item whose index should be determined.\n   */\n\n\n  getItemIndex(item) {\n    return this._isDragging ? this._sortStrategy.getItemIndex(item) : this._draggables.indexOf(item);\n  }\n  /**\n   * Whether the list is able to receive the item that\n   * is currently being dragged inside a connected drop list.\n   */\n\n\n  isReceiving() {\n    return this._activeSiblings.size > 0;\n  }\n  /**\n   * Sorts an item inside the container based on its position.\n   * @param item Item to be sorted.\n   * @param pointerX Position of the item along the X axis.\n   * @param pointerY Position of the item along the Y axis.\n   * @param pointerDelta Direction in which the pointer is moving along each axis.\n   */\n\n\n  _sortItem(item, pointerX, pointerY, pointerDelta) {\n    // Don't sort the item if sorting is disabled or it's out of range.\n    if (this.sortingDisabled || !this._clientRect || !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n      return;\n    }\n\n    const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n\n    if (result) {\n      this.sorted.next({\n        previousIndex: result.previousIndex,\n        currentIndex: result.currentIndex,\n        container: this,\n        item\n      });\n    }\n  }\n  /**\n   * Checks whether the user's pointer is close to the edges of either the\n   * viewport or the drop list and starts the auto-scroll sequence.\n   * @param pointerX User's pointer position along the x axis.\n   * @param pointerY User's pointer position along the y axis.\n   */\n\n\n  _startScrollingIfNecessary(pointerX, pointerY) {\n    if (this.autoScrollDisabled) {\n      return;\n    }\n\n    let scrollNode;\n    let verticalScrollDirection = 0\n    /* AutoScrollVerticalDirection.NONE */\n    ;\n    let horizontalScrollDirection = 0\n    /* AutoScrollHorizontalDirection.NONE */\n    ; // Check whether we should start scrolling any of the parent containers.\n\n    this._parentPositions.positions.forEach((position, element) => {\n      // We have special handling for the `document` below. Also this would be\n      // nicer with a  for...of loop, but it requires changing a compiler flag.\n      if (element === this._document || !position.clientRect || scrollNode) {\n        return;\n      }\n\n      if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n        [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n\n        if (verticalScrollDirection || horizontalScrollDirection) {\n          scrollNode = element;\n        }\n      }\n    }); // Otherwise check if we can start scrolling the viewport.\n\n\n    if (!verticalScrollDirection && !horizontalScrollDirection) {\n      const {\n        width,\n        height\n      } = this._viewportRuler.getViewportSize();\n\n      const clientRect = {\n        width,\n        height,\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n      verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n      horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n      scrollNode = window;\n    }\n\n    if (scrollNode && (verticalScrollDirection !== this._verticalScrollDirection || horizontalScrollDirection !== this._horizontalScrollDirection || scrollNode !== this._scrollNode)) {\n      this._verticalScrollDirection = verticalScrollDirection;\n      this._horizontalScrollDirection = horizontalScrollDirection;\n      this._scrollNode = scrollNode;\n\n      if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n        this._ngZone.runOutsideAngular(this._startScrollInterval);\n      } else {\n        this._stopScrolling();\n      }\n    }\n  }\n  /** Stops any currently-running auto-scroll sequences. */\n\n\n  _stopScrolling() {\n    this._stopScrollTimers.next();\n  }\n  /** Starts the dragging sequence within the list. */\n\n\n  _draggingStarted() {\n    const styles = coerceElement(this.element).style;\n    this.beforeStarted.next();\n    this._isDragging = true; // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n    // scrolling. The browser seems to round the value based on the snapping points which means\n    // that we can't increment/decrement the scroll position.\n\n    this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n    styles.scrollSnapType = styles.msScrollSnapType = 'none';\n\n    this._sortStrategy.start(this._draggables);\n\n    this._cacheParentPositions();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this._listenToScrollEvents();\n  }\n  /** Caches the positions of the configured scrollable parents. */\n\n\n  _cacheParentPositions() {\n    const element = coerceElement(this.element);\n\n    this._parentPositions.cache(this._scrollableElements); // The list element is always in the `scrollableElements`\n    // so we can take advantage of the cached `ClientRect`.\n\n\n    this._clientRect = this._parentPositions.positions.get(element).clientRect;\n  }\n  /** Resets the container to its initial state. */\n\n\n  _reset() {\n    this._isDragging = false;\n    const styles = coerceElement(this.element).style;\n    styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n\n    this._siblings.forEach(sibling => sibling._stopReceiving(this));\n\n    this._sortStrategy.reset();\n\n    this._stopScrolling();\n\n    this._viewportScrollSubscription.unsubscribe();\n\n    this._parentPositions.clear();\n  }\n  /**\n   * Checks whether the user's pointer is positioned over the container.\n   * @param x Pointer position along the X axis.\n   * @param y Pointer position along the Y axis.\n   */\n\n\n  _isOverContainer(x, y) {\n    return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n  }\n  /**\n   * Figures out whether an item should be moved into a sibling\n   * drop container, based on its current position.\n   * @param item Drag item that is being moved.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n\n\n  _getSiblingContainerFromPosition(item, x, y) {\n    return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n  }\n  /**\n   * Checks whether the drop list can receive the passed-in item.\n   * @param item Item that is being dragged into the list.\n   * @param x Position of the item along the X axis.\n   * @param y Position of the item along the Y axis.\n   */\n\n\n  _canReceive(item, x, y) {\n    if (!this._clientRect || !isInsideClientRect(this._clientRect, x, y) || !this.enterPredicate(item, this)) {\n      return false;\n    }\n\n    const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y); // If there's no element at the pointer position, then\n    // the client rect is probably scrolled out of the view.\n\n\n    if (!elementFromPoint) {\n      return false;\n    }\n\n    const nativeElement = coerceElement(this.element); // The `ClientRect`, that we're using to find the container over which the user is\n    // hovering, doesn't give us any information on whether the element has been scrolled\n    // out of the view or whether it's overlapping with other containers. This means that\n    // we could end up transferring the item into a container that's invisible or is positioned\n    // below another one. We use the result from `elementFromPoint` to get the top-most element\n    // at the pointer position and to find whether it's one of the intersecting drop containers.\n\n    return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n  }\n  /**\n   * Called by one of the connected drop lists when a dragging sequence has started.\n   * @param sibling Sibling in which dragging has started.\n   */\n\n\n  _startReceiving(sibling, items) {\n    const activeSiblings = this._activeSiblings;\n\n    if (!activeSiblings.has(sibling) && items.every(item => {\n      // Note that we have to add an exception to the `enterPredicate` for items that started off\n      // in this drop list. The drag ref has logic that allows an item to return to its initial\n      // container, if it has left the initial container and none of the connected containers\n      // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n      return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n    })) {\n      activeSiblings.add(sibling);\n\n      this._cacheParentPositions();\n\n      this._listenToScrollEvents();\n    }\n  }\n  /**\n   * Called by a connected drop list when dragging has stopped.\n   * @param sibling Sibling whose dragging has stopped.\n   */\n\n\n  _stopReceiving(sibling) {\n    this._activeSiblings.delete(sibling);\n\n    this._viewportScrollSubscription.unsubscribe();\n  }\n  /**\n   * Starts listening to scroll events on the viewport.\n   * Used for updating the internal state of the list.\n   */\n\n\n  _listenToScrollEvents() {\n    this._viewportScrollSubscription = this._dragDropRegistry.scrolled(this._getShadowRoot()).subscribe(event => {\n      if (this.isDragging()) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n\n        if (scrollDifference) {\n          this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n        }\n      } else if (this.isReceiving()) {\n        this._cacheParentPositions();\n      }\n    });\n  }\n  /**\n   * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n   * than saving it in property directly on init, because we want to resolve it as late as possible\n   * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n   * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n   */\n\n\n  _getShadowRoot() {\n    if (!this._cachedShadowRoot) {\n      const shadowRoot = _getShadowRoot(coerceElement(this.element));\n\n      this._cachedShadowRoot = shadowRoot || this._document;\n    }\n\n    return this._cachedShadowRoot;\n  }\n  /** Notifies any siblings that may potentially receive the item. */\n\n\n  _notifyReceivingSiblings() {\n    const draggedItems = this._sortStrategy.getActiveItemsSnapshot().filter(item => item.isDragging());\n\n    this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n  }\n\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\n\n\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n  const {\n    top,\n    bottom,\n    height\n  } = clientRect;\n  const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n    return 1\n    /* AutoScrollVerticalDirection.UP */\n    ;\n  } else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n    return 2\n    /* AutoScrollVerticalDirection.DOWN */\n    ;\n  }\n\n  return 0\n  /* AutoScrollVerticalDirection.NONE */\n  ;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\n\n\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n  const {\n    left,\n    right,\n    width\n  } = clientRect;\n  const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n\n  if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n    return 1\n    /* AutoScrollHorizontalDirection.LEFT */\n    ;\n  } else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n    return 2\n    /* AutoScrollHorizontalDirection.RIGHT */\n    ;\n  }\n\n  return 0\n  /* AutoScrollHorizontalDirection.NONE */\n  ;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\n\n\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n  const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n  const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n  let verticalScrollDirection = 0\n  /* AutoScrollVerticalDirection.NONE */\n  ;\n  let horizontalScrollDirection = 0\n  /* AutoScrollHorizontalDirection.NONE */\n  ; // Note that we here we do some extra checks for whether the element is actually scrollable in\n  // a certain direction and we only assign the scroll direction if it is. We do this so that we\n  // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n  // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n\n  if (computedVertical) {\n    const scrollTop = element.scrollTop;\n\n    if (computedVertical === 1\n    /* AutoScrollVerticalDirection.UP */\n    ) {\n      if (scrollTop > 0) {\n        verticalScrollDirection = 1\n        /* AutoScrollVerticalDirection.UP */\n        ;\n      }\n    } else if (element.scrollHeight - scrollTop > element.clientHeight) {\n      verticalScrollDirection = 2\n      /* AutoScrollVerticalDirection.DOWN */\n      ;\n    }\n  }\n\n  if (computedHorizontal) {\n    const scrollLeft = element.scrollLeft;\n\n    if (computedHorizontal === 1\n    /* AutoScrollHorizontalDirection.LEFT */\n    ) {\n      if (scrollLeft > 0) {\n        horizontalScrollDirection = 1\n        /* AutoScrollHorizontalDirection.LEFT */\n        ;\n      }\n    } else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n      horizontalScrollDirection = 2\n      /* AutoScrollHorizontalDirection.RIGHT */\n      ;\n    }\n  }\n\n  return [verticalScrollDirection, horizontalScrollDirection];\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Event options that can be used to bind an active, capturing event. */\n\n\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n  passive: false,\n  capture: true\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\n\nclass DragDropRegistry {\n  constructor(_ngZone, _document) {\n    this._ngZone = _ngZone;\n    /** Registered drop container instances. */\n\n    this._dropInstances = new Set();\n    /** Registered drag item instances. */\n\n    this._dragInstances = new Set();\n    /** Drag item instances that are currently being dragged. */\n\n    this._activeDragInstances = [];\n    /** Keeps track of the event listeners that we've bound to the `document`. */\n\n    this._globalListeners = new Map();\n    /**\n     * Predicate function to check if an item is being dragged.  Moved out into a property,\n     * because it'll be called a lot and we don't want to create a new function every time.\n     */\n\n    this._draggingPredicate = item => item.isDragging();\n    /**\n     * Emits the `touchmove` or `mousemove` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n\n\n    this.pointerMove = new Subject();\n    /**\n     * Emits the `touchend` or `mouseup` events that are dispatched\n     * while the user is dragging a drag item instance.\n     */\n\n    this.pointerUp = new Subject();\n    /**\n     * Emits when the viewport has been scrolled while the user is dragging an item.\n     * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n     * @breaking-change 13.0.0\n     */\n\n    this.scroll = new Subject();\n    /**\n     * Event listener that will prevent the default browser action while the user is dragging.\n     * @param event Event whose default action should be prevented.\n     */\n\n    this._preventDefaultWhileDragging = event => {\n      if (this._activeDragInstances.length > 0) {\n        event.preventDefault();\n      }\n    };\n    /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n\n\n    this._persistentTouchmoveListener = event => {\n      if (this._activeDragInstances.length > 0) {\n        // Note that we only want to prevent the default action after dragging has actually started.\n        // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n        // but it could be pushed back if the user has set up a drag delay or threshold.\n        if (this._activeDragInstances.some(this._draggingPredicate)) {\n          event.preventDefault();\n        }\n\n        this.pointerMove.next(event);\n      }\n    };\n\n    this._document = _document;\n  }\n  /** Adds a drop container to the registry. */\n\n\n  registerDropContainer(drop) {\n    if (!this._dropInstances.has(drop)) {\n      this._dropInstances.add(drop);\n    }\n  }\n  /** Adds a drag item instance to the registry. */\n\n\n  registerDragItem(drag) {\n    this._dragInstances.add(drag); // The `touchmove` event gets bound once, ahead of time, because WebKit\n    // won't preventDefault on a dynamically-added `touchmove` listener.\n    // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n\n\n    if (this._dragInstances.size === 1) {\n      this._ngZone.runOutsideAngular(() => {\n        // The event handler has to be explicitly active,\n        // because newer browsers make it passive by default.\n        this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n      });\n    }\n  }\n  /** Removes a drop container from the registry. */\n\n\n  removeDropContainer(drop) {\n    this._dropInstances.delete(drop);\n  }\n  /** Removes a drag item instance from the registry. */\n\n\n  removeDragItem(drag) {\n    this._dragInstances.delete(drag);\n\n    this.stopDragging(drag);\n\n    if (this._dragInstances.size === 0) {\n      this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n    }\n  }\n  /**\n   * Starts the dragging sequence for a drag instance.\n   * @param drag Drag instance which is being dragged.\n   * @param event Event that initiated the dragging.\n   */\n\n\n  startDragging(drag, event) {\n    // Do not process the same drag twice to avoid memory leaks and redundant listeners\n    if (this._activeDragInstances.indexOf(drag) > -1) {\n      return;\n    }\n\n    this._activeDragInstances.push(drag);\n\n    if (this._activeDragInstances.length === 1) {\n      const isTouchEvent = event.type.startsWith('touch'); // We explicitly bind __active__ listeners here, because newer browsers will default to\n      // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n      // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n\n      this._globalListeners.set(isTouchEvent ? 'touchend' : 'mouseup', {\n        handler: e => this.pointerUp.next(e),\n        options: true\n      }).set('scroll', {\n        handler: e => this.scroll.next(e),\n        // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n        // the document. See https://github.com/angular/components/issues/17144.\n        options: true\n      }) // Preventing the default action on `mousemove` isn't enough to disable text selection\n      // on Safari so we need to prevent the selection event as well. Alternatively this can\n      // be done by setting `user-select: none` on the `body`, however it has causes a style\n      // recalculation which can be expensive on pages with a lot of elements.\n      .set('selectstart', {\n        handler: this._preventDefaultWhileDragging,\n        options: activeCapturingEventOptions\n      }); // We don't have to bind a move event for touch drag sequences, because\n      // we already have a persistent global one bound from `registerDragItem`.\n\n\n      if (!isTouchEvent) {\n        this._globalListeners.set('mousemove', {\n          handler: e => this.pointerMove.next(e),\n          options: activeCapturingEventOptions\n        });\n      }\n\n      this._ngZone.runOutsideAngular(() => {\n        this._globalListeners.forEach((config, name) => {\n          this._document.addEventListener(name, config.handler, config.options);\n        });\n      });\n    }\n  }\n  /** Stops dragging a drag item instance. */\n\n\n  stopDragging(drag) {\n    const index = this._activeDragInstances.indexOf(drag);\n\n    if (index > -1) {\n      this._activeDragInstances.splice(index, 1);\n\n      if (this._activeDragInstances.length === 0) {\n        this._clearGlobalListeners();\n      }\n    }\n  }\n  /** Gets whether a drag item instance is currently being dragged. */\n\n\n  isDragging(drag) {\n    return this._activeDragInstances.indexOf(drag) > -1;\n  }\n  /**\n   * Gets a stream that will emit when any element on the page is scrolled while an item is being\n   * dragged.\n   * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n   *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n   *   be used to include an additional top-level listener at the shadow root level.\n   */\n\n\n  scrolled(shadowRoot) {\n    const streams = [this.scroll];\n\n    if (shadowRoot && shadowRoot !== this._document) {\n      // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n      // because we want to guarantee that the event is bound outside of the `NgZone`. With\n      // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n      streams.push(new Observable(observer => {\n        return this._ngZone.runOutsideAngular(() => {\n          const eventOptions = true;\n\n          const callback = event => {\n            if (this._activeDragInstances.length) {\n              observer.next(event);\n            }\n          };\n\n          shadowRoot.addEventListener('scroll', callback, eventOptions);\n          return () => {\n            shadowRoot.removeEventListener('scroll', callback, eventOptions);\n          };\n        });\n      }));\n    }\n\n    return merge(...streams);\n  }\n\n  ngOnDestroy() {\n    this._dragInstances.forEach(instance => this.removeDragItem(instance));\n\n    this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n\n    this._clearGlobalListeners();\n\n    this.pointerMove.complete();\n    this.pointerUp.complete();\n  }\n  /** Clears out the global event listeners from the `document`. */\n\n\n  _clearGlobalListeners() {\n    this._globalListeners.forEach((config, name) => {\n      this._document.removeEventListener(name, config.handler, config.options);\n    });\n\n    this._globalListeners.clear();\n  }\n\n}\n\nDragDropRegistry.ɵfac = function DragDropRegistry_Factory(t) {\n  return new (t || DragDropRegistry)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT));\n};\n\nDragDropRegistry.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DragDropRegistry,\n  factory: DragDropRegistry.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: i0.NgZone\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Default configuration to be used when creating a `DragRef`. */\n\n\nconst DEFAULT_CONFIG = {\n  dragStartThreshold: 5,\n  pointerDirectionChangeThreshold: 5\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\n\nclass DragDrop {\n  constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n    this._document = _document;\n    this._ngZone = _ngZone;\n    this._viewportRuler = _viewportRuler;\n    this._dragDropRegistry = _dragDropRegistry;\n  }\n  /**\n   * Turns an element into a draggable item.\n   * @param element Element to which to attach the dragging functionality.\n   * @param config Object used to configure the dragging behavior.\n   */\n\n\n  createDrag(element, config = DEFAULT_CONFIG) {\n    return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n  }\n  /**\n   * Turns an element into a drop list.\n   * @param element Element to which to attach the drop list functionality.\n   */\n\n\n  createDropList(element) {\n    return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n  }\n\n}\n\nDragDrop.ɵfac = function DragDrop_Factory(t) {\n  return new (t || DragDrop)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ViewportRuler), i0.ɵɵinject(DragDropRegistry));\n};\n\nDragDrop.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: DragDrop,\n  factory: DragDrop.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDrop, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.ViewportRuler\n    }, {\n      type: DragDropRegistry\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\n\n\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\n\nclass CdkDropListGroup {\n  constructor() {\n    /** Drop lists registered inside the group. */\n    this._items = new Set();\n    this._disabled = false;\n  }\n  /** Whether starting a dragging sequence from inside this group is disabled. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n  }\n\n  ngOnDestroy() {\n    this._items.clear();\n  }\n\n}\n\nCdkDropListGroup.ɵfac = function CdkDropListGroup_Factory(t) {\n  return new (t || CdkDropListGroup)();\n};\n\nCdkDropListGroup.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDropListGroup,\n  selectors: [[\"\", \"cdkDropListGroup\", \"\"]],\n  inputs: {\n    disabled: [\"cdkDropListGroupDisabled\", \"disabled\"]\n  },\n  exportAs: [\"cdkDropListGroup\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DROP_LIST_GROUP,\n    useExisting: CdkDropListGroup\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropListGroup, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropListGroup]',\n      exportAs: 'cdkDropListGroup',\n      providers: [{\n        provide: CDK_DROP_LIST_GROUP,\n        useExisting: CdkDropListGroup\n      }]\n    }]\n  }], null, {\n    disabled: [{\n      type: Input,\n      args: ['cdkDropListGroupDisabled']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\n\n\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\n\nfunction assertElementNode(node, name) {\n  if (node.nodeType !== 1) {\n    throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n  }\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Counter used to generate unique ids for drop zones. */\n\n\nlet _uniqueIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Container that wraps a set of draggable items. */\n\nclass CdkDropList {\n  constructor(\n  /** Element that the drop list is attached to. */\n  element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n    this.element = element;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._scrollDispatcher = _scrollDispatcher;\n    this._dir = _dir;\n    this._group = _group;\n    /** Emits when the list has been destroyed. */\n\n    this._destroyed = new Subject();\n    /**\n     * Other draggable containers that this container is connected to and into which the\n     * container's items can be transferred. Can either be references to other drop containers,\n     * or their unique IDs.\n     */\n\n    this.connectedTo = [];\n    /**\n     * Unique ID for the drop zone. Can be used as a reference\n     * in the `connectedTo` of another `CdkDropList`.\n     */\n\n    this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n    /**\n     * Function that is used to determine whether an item\n     * is allowed to be moved into a drop container.\n     */\n\n    this.enterPredicate = () => true;\n    /** Functions that is used to determine whether an item can be sorted into a particular index. */\n\n\n    this.sortPredicate = () => true;\n    /** Emits when the user drops an item inside the container. */\n\n\n    this.dropped = new EventEmitter();\n    /**\n     * Emits when the user has moved a new drag item into this container.\n     */\n\n    this.entered = new EventEmitter();\n    /**\n     * Emits when the user removes an item from the container\n     * by dragging it into another container.\n     */\n\n    this.exited = new EventEmitter();\n    /** Emits as the user is swapping items while actively dragging. */\n\n    this.sorted = new EventEmitter();\n    /**\n     * Keeps track of the items that are registered with this container. Historically we used to\n     * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n     * well which means that we can't handle cases like dragging the headers of a `mat-table`\n     * correctly. What we do instead is to have the items register themselves with the container\n     * and then we sort them based on their position in the DOM.\n     */\n\n    this._unsortedItems = new Set();\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDropList');\n    }\n\n    this._dropListRef = dragDrop.createDropList(element);\n    this._dropListRef.data = this;\n\n    if (config) {\n      this._assignDefaults(config);\n    }\n\n    this._dropListRef.enterPredicate = (drag, drop) => {\n      return this.enterPredicate(drag.data, drop.data);\n    };\n\n    this._dropListRef.sortPredicate = (index, drag, drop) => {\n      return this.sortPredicate(index, drag.data, drop.data);\n    };\n\n    this._setupInputSyncSubscription(this._dropListRef);\n\n    this._handleEvents(this._dropListRef);\n\n    CdkDropList._dropLists.push(this);\n\n    if (_group) {\n      _group._items.add(this);\n    }\n  }\n  /** Whether starting a dragging sequence from this container is disabled. */\n\n\n  get disabled() {\n    return this._disabled || !!this._group && this._group.disabled;\n  }\n\n  set disabled(value) {\n    // Usually we sync the directive and ref state right before dragging starts, in order to have\n    // a single point of failure and to avoid having to use setters for everything. `disabled` is\n    // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n    // the user in a disabled state, so we also need to sync it as it's being set.\n    this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n  }\n  /** Registers an items with the drop list. */\n\n\n  addItem(item) {\n    this._unsortedItems.add(item);\n\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Removes an item from the drop list. */\n\n\n  removeItem(item) {\n    this._unsortedItems.delete(item);\n\n    if (this._dropListRef.isDragging()) {\n      this._syncItemsWithRef();\n    }\n  }\n  /** Gets the registered items in the list, sorted by their position in the DOM. */\n\n\n  getSortedItems() {\n    return Array.from(this._unsortedItems).sort((a, b) => {\n      const documentPosition = a._dragRef.getVisibleElement().compareDocumentPosition(b._dragRef.getVisibleElement()); // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n      // tslint:disable-next-line:no-bitwise\n\n\n      return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n    });\n  }\n\n  ngOnDestroy() {\n    const index = CdkDropList._dropLists.indexOf(this);\n\n    if (index > -1) {\n      CdkDropList._dropLists.splice(index, 1);\n    }\n\n    if (this._group) {\n      this._group._items.delete(this);\n    }\n\n    this._unsortedItems.clear();\n\n    this._dropListRef.dispose();\n\n    this._destroyed.next();\n\n    this._destroyed.complete();\n  }\n  /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n\n\n  _setupInputSyncSubscription(ref) {\n    if (this._dir) {\n      this._dir.change.pipe(startWith(this._dir.value), takeUntil(this._destroyed)).subscribe(value => ref.withDirection(value));\n    }\n\n    ref.beforeStarted.subscribe(() => {\n      const siblings = coerceArray(this.connectedTo).map(drop => {\n        if (typeof drop === 'string') {\n          const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n\n          if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n          }\n\n          return correspondingDropList;\n        }\n\n        return drop;\n      });\n\n      if (this._group) {\n        this._group._items.forEach(drop => {\n          if (siblings.indexOf(drop) === -1) {\n            siblings.push(drop);\n          }\n        });\n      } // Note that we resolve the scrollable parents here so that we delay the resolution\n      // as long as possible, ensuring that the element is in its final place in the DOM.\n\n\n      if (!this._scrollableParentsResolved) {\n        const scrollableParents = this._scrollDispatcher.getAncestorScrollContainers(this.element).map(scrollable => scrollable.getElementRef().nativeElement);\n\n        this._dropListRef.withScrollableParents(scrollableParents); // Only do this once since it involves traversing the DOM and the parents\n        // shouldn't be able to change without the drop list being destroyed.\n\n\n        this._scrollableParentsResolved = true;\n      }\n\n      ref.disabled = this.disabled;\n      ref.lockAxis = this.lockAxis;\n      ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n      ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n      ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n      ref.connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef)).withOrientation(this.orientation);\n    });\n  }\n  /** Handles events from the underlying DropListRef. */\n\n\n  _handleEvents(ref) {\n    ref.beforeStarted.subscribe(() => {\n      this._syncItemsWithRef();\n\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(event => {\n      this.entered.emit({\n        container: this,\n        item: event.item.data,\n        currentIndex: event.currentIndex\n      });\n    });\n    ref.exited.subscribe(event => {\n      this.exited.emit({\n        container: this,\n        item: event.item.data\n      });\n\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.sorted.subscribe(event => {\n      this.sorted.emit({\n        previousIndex: event.previousIndex,\n        currentIndex: event.currentIndex,\n        container: this,\n        item: event.item.data\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        item: dropEvent.item.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      }); // Mark for check since all of these events run outside of change\n      // detection and we're not guaranteed for something else to have triggered it.\n\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n\n\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      draggingDisabled,\n      sortingDisabled,\n      listAutoScrollDisabled,\n      listOrientation\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n    this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n    this.orientation = listOrientation || 'vertical';\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n  }\n  /** Syncs up the registered drag items with underlying drop list ref. */\n\n\n  _syncItemsWithRef() {\n    this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n  }\n\n}\n/** Keeps track of the drop lists that are currently on the page. */\n\n\nCdkDropList._dropLists = [];\n\nCdkDropList.ɵfac = function CdkDropList_Factory(t) {\n  return new (t || CdkDropList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.ScrollDispatcher), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(CDK_DROP_LIST_GROUP, 12), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8));\n};\n\nCdkDropList.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDropList,\n  selectors: [[\"\", \"cdkDropList\", \"\"], [\"cdk-drop-list\"]],\n  hostAttrs: [1, \"cdk-drop-list\"],\n  hostVars: 7,\n  hostBindings: function CdkDropList_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵattribute(\"id\", ctx.id);\n      i0.ɵɵclassProp(\"cdk-drop-list-disabled\", ctx.disabled)(\"cdk-drop-list-dragging\", ctx._dropListRef.isDragging())(\"cdk-drop-list-receiving\", ctx._dropListRef.isReceiving());\n    }\n  },\n  inputs: {\n    connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"],\n    data: [\"cdkDropListData\", \"data\"],\n    orientation: [\"cdkDropListOrientation\", \"orientation\"],\n    id: \"id\",\n    lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"],\n    disabled: [\"cdkDropListDisabled\", \"disabled\"],\n    sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"],\n    enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"],\n    sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"],\n    autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"],\n    autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"]\n  },\n  outputs: {\n    dropped: \"cdkDropListDropped\",\n    entered: \"cdkDropListEntered\",\n    exited: \"cdkDropListExited\",\n    sorted: \"cdkDropListSorted\"\n  },\n  exportAs: [\"cdkDropList\"],\n  features: [i0.ɵɵProvidersFeature([// Prevent child drop lists from picking up the same group as their parent.\n  {\n    provide: CDK_DROP_LIST_GROUP,\n    useValue: undefined\n  }, {\n    provide: CDK_DROP_LIST,\n    useExisting: CdkDropList\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDropList, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDropList], cdk-drop-list',\n      exportAs: 'cdkDropList',\n      providers: [// Prevent child drop lists from picking up the same group as their parent.\n      {\n        provide: CDK_DROP_LIST_GROUP,\n        useValue: undefined\n      }, {\n        provide: CDK_DROP_LIST,\n        useExisting: CdkDropList\n      }],\n      host: {\n        'class': 'cdk-drop-list',\n        '[attr.id]': 'id',\n        '[class.cdk-drop-list-disabled]': 'disabled',\n        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: DragDrop\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.ScrollDispatcher\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: CdkDropListGroup,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DROP_LIST_GROUP]\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_CONFIG]\n      }]\n    }];\n  }, {\n    connectedTo: [{\n      type: Input,\n      args: ['cdkDropListConnectedTo']\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDropListData']\n    }],\n    orientation: [{\n      type: Input,\n      args: ['cdkDropListOrientation']\n    }],\n    id: [{\n      type: Input\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDropListLockAxis']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkDropListDisabled']\n    }],\n    sortingDisabled: [{\n      type: Input,\n      args: ['cdkDropListSortingDisabled']\n    }],\n    enterPredicate: [{\n      type: Input,\n      args: ['cdkDropListEnterPredicate']\n    }],\n    sortPredicate: [{\n      type: Input,\n      args: ['cdkDropListSortPredicate']\n    }],\n    autoScrollDisabled: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollDisabled']\n    }],\n    autoScrollStep: [{\n      type: Input,\n      args: ['cdkDropListAutoScrollStep']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDropListDropped']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDropListEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDropListExited']\n    }],\n    sorted: [{\n      type: Output,\n      args: ['cdkDropListSorted']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\n\nclass CdkDragHandle {\n  constructor(element, parentDrag) {\n    this.element = element;\n    /** Emits when the state of the handle has changed. */\n\n    this._stateChanges = new Subject();\n    this._disabled = false;\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      assertElementNode(element.nativeElement, 'cdkDragHandle');\n    }\n\n    this._parentDrag = parentDrag;\n  }\n  /** Whether starting to drag through this handle is disabled. */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n\n    this._stateChanges.next(this);\n  }\n\n  ngOnDestroy() {\n    this._stateChanges.complete();\n  }\n\n}\n\nCdkDragHandle.ɵfac = function CdkDragHandle_Factory(t) {\n  return new (t || CdkDragHandle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n};\n\nCdkDragHandle.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragHandle,\n  selectors: [[\"\", \"cdkDragHandle\", \"\"]],\n  hostAttrs: [1, \"cdk-drag-handle\"],\n  inputs: {\n    disabled: [\"cdkDragHandleDisabled\", \"disabled\"]\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_HANDLE,\n    useExisting: CdkDragHandle\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragHandle, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDragHandle]',\n      host: {\n        'class': 'cdk-drag-handle'\n      },\n      providers: [{\n        provide: CDK_DRAG_HANDLE,\n        useExisting: CdkDragHandle\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_DRAG_PARENT]\n      }, {\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }];\n  }, {\n    disabled: [{\n      type: Input,\n      args: ['cdkDragHandleDisabled']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\n\nclass CdkDragPlaceholder {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n\n}\n\nCdkDragPlaceholder.ɵfac = function CdkDragPlaceholder_Factory(t) {\n  return new (t || CdkDragPlaceholder)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nCdkDragPlaceholder.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragPlaceholder,\n  selectors: [[\"ng-template\", \"cdkDragPlaceholder\", \"\"]],\n  inputs: {\n    data: \"data\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PLACEHOLDER,\n    useExisting: CdkDragPlaceholder\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPlaceholder, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPlaceholder]',\n      providers: [{\n        provide: CDK_DRAG_PLACEHOLDER,\n        useExisting: CdkDragPlaceholder\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    data: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\n\n\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\n\nclass CdkDragPreview {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n    this._matchSize = false;\n  }\n  /** Whether the preview should preserve the same size as the item that is being dragged. */\n\n\n  get matchSize() {\n    return this._matchSize;\n  }\n\n  set matchSize(value) {\n    this._matchSize = coerceBooleanProperty(value);\n  }\n\n}\n\nCdkDragPreview.ɵfac = function CdkDragPreview_Factory(t) {\n  return new (t || CdkDragPreview)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n};\n\nCdkDragPreview.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDragPreview,\n  selectors: [[\"ng-template\", \"cdkDragPreview\", \"\"]],\n  inputs: {\n    data: \"data\",\n    matchSize: \"matchSize\"\n  },\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PREVIEW,\n    useExisting: CdkDragPreview\n  }])]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDragPreview, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[cdkDragPreview]',\n      providers: [{\n        provide: CDK_DRAG_PREVIEW,\n        useExisting: CdkDragPreview\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.TemplateRef\n    }];\n  }, {\n    data: [{\n      type: Input\n    }],\n    matchSize: [{\n      type: Input\n    }]\n  });\n})();\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/** Element that can be moved inside a CdkDropList container. */\n\nclass CdkDrag {\n  constructor(\n  /** Element that the draggable is attached to. */\n  element,\n  /** Droppable container that the draggable is a part of. */\n  dropContainer,\n  /**\n   * @deprecated `_document` parameter no longer being used and will be removed.\n   * @breaking-change 12.0.0\n   */\n  _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n    this.element = element;\n    this.dropContainer = dropContainer;\n    this._ngZone = _ngZone;\n    this._viewContainerRef = _viewContainerRef;\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._selfHandle = _selfHandle;\n    this._parentDrag = _parentDrag;\n    this._destroyed = new Subject();\n    /** Emits when the user starts dragging the item. */\n\n    this.started = new EventEmitter();\n    /** Emits when the user has released a drag item, before any animations have started. */\n\n    this.released = new EventEmitter();\n    /** Emits when the user stops dragging an item in the container. */\n\n    this.ended = new EventEmitter();\n    /** Emits when the user has moved the item into a new container. */\n\n    this.entered = new EventEmitter();\n    /** Emits when the user removes the item its container by dragging it into another container. */\n\n    this.exited = new EventEmitter();\n    /** Emits when the user drops the item inside a container. */\n\n    this.dropped = new EventEmitter();\n    /**\n     * Emits as the user is dragging the item. Use with caution,\n     * because this event will fire for every pixel that the user has dragged.\n     */\n\n    this.moved = new Observable(observer => {\n      const subscription = this._dragRef.moved.pipe(map(movedEvent => ({\n        source: this,\n        pointerPosition: movedEvent.pointerPosition,\n        event: movedEvent.event,\n        delta: movedEvent.delta,\n        distance: movedEvent.distance\n      }))).subscribe(observer);\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    });\n    this._dragRef = dragDrop.createDrag(element, {\n      dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n      pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null ? config.pointerDirectionChangeThreshold : 5,\n      zIndex: config === null || config === void 0 ? void 0 : config.zIndex\n    });\n    this._dragRef.data = this; // We have to keep track of the drag instances in order to be able to match an element to\n    // a drag instance. We can't go through the global registry of `DragRef`, because the root\n    // element could be different.\n\n    CdkDrag._dragInstances.push(this);\n\n    if (config) {\n      this._assignDefaults(config);\n    } // Note that usually the container is assigned when the drop list is picks up the item, but in\n    // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n    // where there are no items on the first change detection pass, but the items get picked up as\n    // soon as the user triggers another pass by dragging. This is a problem, because the item would\n    // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n    // is too late since the two modes save different kinds of information. We work around it by\n    // assigning the drop container both from here and the list.\n\n\n    if (dropContainer) {\n      this._dragRef._withDropContainer(dropContainer._dropListRef);\n\n      dropContainer.addItem(this);\n    }\n\n    this._syncInputs(this._dragRef);\n\n    this._handleEvents(this._dragRef);\n  }\n  /** Whether starting to drag this element is disabled. */\n\n\n  get disabled() {\n    return this._disabled || this.dropContainer && this.dropContainer.disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._dragRef.disabled = this._disabled;\n  }\n  /**\n   * Returns the element that is being used as a placeholder\n   * while the current element is being dragged.\n   */\n\n\n  getPlaceholderElement() {\n    return this._dragRef.getPlaceholderElement();\n  }\n  /** Returns the root draggable element. */\n\n\n  getRootElement() {\n    return this._dragRef.getRootElement();\n  }\n  /** Resets a standalone drag item to its initial position. */\n\n\n  reset() {\n    this._dragRef.reset();\n  }\n  /**\n   * Gets the pixel coordinates of the draggable outside of a drop container.\n   */\n\n\n  getFreeDragPosition() {\n    return this._dragRef.getFreeDragPosition();\n  }\n  /**\n   * Sets the current position in pixels the draggable outside of a drop container.\n   * @param value New position to be set.\n   */\n\n\n  setFreeDragPosition(value) {\n    this._dragRef.setFreeDragPosition(value);\n  }\n\n  ngAfterViewInit() {\n    // Normally this isn't in the zone, but it can cause major performance regressions for apps\n    // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n    this._ngZone.runOutsideAngular(() => {\n      // We need to wait for the zone to stabilize, in order for the reference\n      // element to be in the proper place in the DOM. This is mostly relevant\n      // for draggable elements inside portals since they get stamped out in\n      // their original DOM position and then they get transferred to the portal.\n      this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n        this._updateRootElement();\n\n        this._setupHandlesListener();\n\n        if (this.freeDragPosition) {\n          this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n      });\n    });\n  }\n\n  ngOnChanges(changes) {\n    const rootSelectorChange = changes['rootElementSelector'];\n    const positionChange = changes['freeDragPosition']; // We don't have to react to the first change since it's being\n    // handled in `ngAfterViewInit` where it needs to be deferred.\n\n    if (rootSelectorChange && !rootSelectorChange.firstChange) {\n      this._updateRootElement();\n    } // Skip the first change since it's being handled in `ngAfterViewInit`.\n\n\n    if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n      this._dragRef.setFreeDragPosition(this.freeDragPosition);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.dropContainer) {\n      this.dropContainer.removeItem(this);\n    }\n\n    const index = CdkDrag._dragInstances.indexOf(this);\n\n    if (index > -1) {\n      CdkDrag._dragInstances.splice(index, 1);\n    } // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n\n\n    this._ngZone.runOutsideAngular(() => {\n      this._destroyed.next();\n\n      this._destroyed.complete();\n\n      this._dragRef.dispose();\n    });\n  }\n  /** Syncs the root element with the `DragRef`. */\n\n\n  _updateRootElement() {\n    var _a;\n\n    const element = this.element.nativeElement;\n    let rootElement = element;\n\n    if (this.rootElementSelector) {\n      rootElement = element.closest !== undefined ? element.closest(this.rootElementSelector) : // Comment tag doesn't have closest method, so use parent's one.\n      (_a = element.parentElement) === null || _a === void 0 ? void 0 : _a.closest(this.rootElementSelector);\n    }\n\n    if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      assertElementNode(rootElement, 'cdkDrag');\n    }\n\n    this._dragRef.withRootElement(rootElement || element);\n  }\n  /** Gets the boundary element, based on the `boundaryElement` value. */\n\n\n  _getBoundaryElement() {\n    const boundary = this.boundaryElement;\n\n    if (!boundary) {\n      return null;\n    }\n\n    if (typeof boundary === 'string') {\n      return this.element.nativeElement.closest(boundary);\n    }\n\n    return coerceElement(boundary);\n  }\n  /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n\n\n  _syncInputs(ref) {\n    ref.beforeStarted.subscribe(() => {\n      if (!ref.isDragging()) {\n        const dir = this._dir;\n        const dragStartDelay = this.dragStartDelay;\n        const placeholder = this._placeholderTemplate ? {\n          template: this._placeholderTemplate.templateRef,\n          context: this._placeholderTemplate.data,\n          viewContainer: this._viewContainerRef\n        } : null;\n        const preview = this._previewTemplate ? {\n          template: this._previewTemplate.templateRef,\n          context: this._previewTemplate.data,\n          matchSize: this._previewTemplate.matchSize,\n          viewContainer: this._viewContainerRef\n        } : null;\n        ref.disabled = this.disabled;\n        ref.lockAxis = this.lockAxis;\n        ref.dragStartDelay = typeof dragStartDelay === 'object' && dragStartDelay ? dragStartDelay : coerceNumberProperty(dragStartDelay);\n        ref.constrainPosition = this.constrainPosition;\n        ref.previewClass = this.previewClass;\n        ref.withBoundaryElement(this._getBoundaryElement()).withPlaceholderTemplate(placeholder).withPreviewTemplate(preview).withPreviewContainer(this.previewContainer || 'global');\n\n        if (dir) {\n          ref.withDirection(dir.value);\n        }\n      }\n    }); // This only needs to be resolved once.\n\n    ref.beforeStarted.pipe(take(1)).subscribe(() => {\n      var _a; // If we managed to resolve a parent through DI, use it.\n\n\n      if (this._parentDrag) {\n        ref.withParent(this._parentDrag._dragRef);\n        return;\n      } // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n      // the item was projected into another item by something like `ngTemplateOutlet`.\n\n\n      let parent = this.element.nativeElement.parentElement;\n\n      while (parent) {\n        if (parent.classList.contains(DRAG_HOST_CLASS)) {\n          ref.withParent(((_a = CdkDrag._dragInstances.find(drag => {\n            return drag.element.nativeElement === parent;\n          })) === null || _a === void 0 ? void 0 : _a._dragRef) || null);\n          break;\n        }\n\n        parent = parent.parentElement;\n      }\n    });\n  }\n  /** Handles the events from the underlying `DragRef`. */\n\n\n  _handleEvents(ref) {\n    ref.started.subscribe(startEvent => {\n      this.started.emit({\n        source: this,\n        event: startEvent.event\n      }); // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.released.subscribe(releaseEvent => {\n      this.released.emit({\n        source: this,\n        event: releaseEvent.event\n      });\n    });\n    ref.ended.subscribe(endEvent => {\n      this.ended.emit({\n        source: this,\n        distance: endEvent.distance,\n        dropPoint: endEvent.dropPoint,\n        event: endEvent.event\n      }); // Since all of these events run outside of change detection,\n      // we need to ensure that everything is marked correctly.\n\n      this._changeDetectorRef.markForCheck();\n    });\n    ref.entered.subscribe(enterEvent => {\n      this.entered.emit({\n        container: enterEvent.container.data,\n        item: this,\n        currentIndex: enterEvent.currentIndex\n      });\n    });\n    ref.exited.subscribe(exitEvent => {\n      this.exited.emit({\n        container: exitEvent.container.data,\n        item: this\n      });\n    });\n    ref.dropped.subscribe(dropEvent => {\n      this.dropped.emit({\n        previousIndex: dropEvent.previousIndex,\n        currentIndex: dropEvent.currentIndex,\n        previousContainer: dropEvent.previousContainer.data,\n        container: dropEvent.container.data,\n        isPointerOverContainer: dropEvent.isPointerOverContainer,\n        item: this,\n        distance: dropEvent.distance,\n        dropPoint: dropEvent.dropPoint,\n        event: dropEvent.event\n      });\n    });\n  }\n  /** Assigns the default input values based on a provided config object. */\n\n\n  _assignDefaults(config) {\n    const {\n      lockAxis,\n      dragStartDelay,\n      constrainPosition,\n      previewClass,\n      boundaryElement,\n      draggingDisabled,\n      rootElementSelector,\n      previewContainer\n    } = config;\n    this.disabled = draggingDisabled == null ? false : draggingDisabled;\n    this.dragStartDelay = dragStartDelay || 0;\n\n    if (lockAxis) {\n      this.lockAxis = lockAxis;\n    }\n\n    if (constrainPosition) {\n      this.constrainPosition = constrainPosition;\n    }\n\n    if (previewClass) {\n      this.previewClass = previewClass;\n    }\n\n    if (boundaryElement) {\n      this.boundaryElement = boundaryElement;\n    }\n\n    if (rootElementSelector) {\n      this.rootElementSelector = rootElementSelector;\n    }\n\n    if (previewContainer) {\n      this.previewContainer = previewContainer;\n    }\n  }\n  /** Sets up the listener that syncs the handles with the drag ref. */\n\n\n  _setupHandlesListener() {\n    // Listen for any newly-added handles.\n    this._handles.changes.pipe(startWith(this._handles), // Sync the new handles with the DragRef.\n    tap(handles => {\n      const childHandleElements = handles.filter(handle => handle._parentDrag === this).map(handle => handle.element); // Usually handles are only allowed to be a descendant of the drag element, but if\n      // the consumer defined a different drag root, we should allow the drag element\n      // itself to be a handle too.\n\n      if (this._selfHandle && this.rootElementSelector) {\n        childHandleElements.push(this.element);\n      }\n\n      this._dragRef.withHandles(childHandleElements);\n    }), // Listen if the state of any of the handles changes.\n    switchMap(handles => {\n      return merge(...handles.map(item => {\n        return item._stateChanges.pipe(startWith(item));\n      }));\n    }), takeUntil(this._destroyed)).subscribe(handleInstance => {\n      // Enabled/disable the handle that changed in the DragRef.\n      const dragRef = this._dragRef;\n      const handle = handleInstance.element.nativeElement;\n      handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n    });\n  }\n\n}\n\nCdkDrag._dragInstances = [];\n\nCdkDrag.ɵfac = function CdkDrag_Factory(t) {\n  return new (t || CdkDrag)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(CDK_DROP_LIST, 12), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(CDK_DRAG_CONFIG, 8), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(DragDrop), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(CDK_DRAG_HANDLE, 10), i0.ɵɵdirectiveInject(CDK_DRAG_PARENT, 12));\n};\n\nCdkDrag.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkDrag,\n  selectors: [[\"\", \"cdkDrag\", \"\"]],\n  contentQueries: function CdkDrag_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PREVIEW, 5);\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_PLACEHOLDER, 5);\n      i0.ɵɵcontentQuery(dirIndex, CDK_DRAG_HANDLE, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._previewTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._placeholderTemplate = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._handles = _t);\n    }\n  },\n  hostAttrs: [1, \"cdk-drag\"],\n  hostVars: 4,\n  hostBindings: function CdkDrag_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"cdk-drag-disabled\", ctx.disabled)(\"cdk-drag-dragging\", ctx._dragRef.isDragging());\n    }\n  },\n  inputs: {\n    data: [\"cdkDragData\", \"data\"],\n    lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"],\n    rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"],\n    boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"],\n    dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"],\n    freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"],\n    disabled: [\"cdkDragDisabled\", \"disabled\"],\n    constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"],\n    previewClass: [\"cdkDragPreviewClass\", \"previewClass\"],\n    previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"]\n  },\n  outputs: {\n    started: \"cdkDragStarted\",\n    released: \"cdkDragReleased\",\n    ended: \"cdkDragEnded\",\n    entered: \"cdkDragEntered\",\n    exited: \"cdkDragExited\",\n    dropped: \"cdkDragDropped\",\n    moved: \"cdkDragMoved\"\n  },\n  exportAs: [\"cdkDrag\"],\n  features: [i0.ɵɵProvidersFeature([{\n    provide: CDK_DRAG_PARENT,\n    useExisting: CdkDrag\n  }]), i0.ɵɵNgOnChangesFeature]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDrag, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkDrag]',\n      exportAs: 'cdkDrag',\n      host: {\n        'class': DRAG_HOST_CLASS,\n        '[class.cdk-drag-disabled]': 'disabled',\n        '[class.cdk-drag-dragging]': '_dragRef.isDragging()'\n      },\n      providers: [{\n        provide: CDK_DRAG_PARENT,\n        useExisting: CdkDrag\n      }]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [CDK_DROP_LIST]\n      }, {\n        type: Optional\n      }, {\n        type: SkipSelf\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [DOCUMENT]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i0.ViewContainerRef\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_CONFIG]\n      }]\n    }, {\n      type: i3.Directionality,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: DragDrop\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: CdkDragHandle,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Self\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_HANDLE]\n      }]\n    }, {\n      type: CdkDrag,\n      decorators: [{\n        type: Optional\n      }, {\n        type: SkipSelf\n      }, {\n        type: Inject,\n        args: [CDK_DRAG_PARENT]\n      }]\n    }];\n  }, {\n    _handles: [{\n      type: ContentChildren,\n      args: [CDK_DRAG_HANDLE, {\n        descendants: true\n      }]\n    }],\n    _previewTemplate: [{\n      type: ContentChild,\n      args: [CDK_DRAG_PREVIEW]\n    }],\n    _placeholderTemplate: [{\n      type: ContentChild,\n      args: [CDK_DRAG_PLACEHOLDER]\n    }],\n    data: [{\n      type: Input,\n      args: ['cdkDragData']\n    }],\n    lockAxis: [{\n      type: Input,\n      args: ['cdkDragLockAxis']\n    }],\n    rootElementSelector: [{\n      type: Input,\n      args: ['cdkDragRootElement']\n    }],\n    boundaryElement: [{\n      type: Input,\n      args: ['cdkDragBoundary']\n    }],\n    dragStartDelay: [{\n      type: Input,\n      args: ['cdkDragStartDelay']\n    }],\n    freeDragPosition: [{\n      type: Input,\n      args: ['cdkDragFreeDragPosition']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkDragDisabled']\n    }],\n    constrainPosition: [{\n      type: Input,\n      args: ['cdkDragConstrainPosition']\n    }],\n    previewClass: [{\n      type: Input,\n      args: ['cdkDragPreviewClass']\n    }],\n    previewContainer: [{\n      type: Input,\n      args: ['cdkDragPreviewContainer']\n    }],\n    started: [{\n      type: Output,\n      args: ['cdkDragStarted']\n    }],\n    released: [{\n      type: Output,\n      args: ['cdkDragReleased']\n    }],\n    ended: [{\n      type: Output,\n      args: ['cdkDragEnded']\n    }],\n    entered: [{\n      type: Output,\n      args: ['cdkDragEntered']\n    }],\n    exited: [{\n      type: Output,\n      args: ['cdkDragExited']\n    }],\n    dropped: [{\n      type: Output,\n      args: ['cdkDragDropped']\n    }],\n    moved: [{\n      type: Output,\n      args: ['cdkDragMoved']\n    }]\n  });\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass DragDropModule {}\n\nDragDropModule.ɵfac = function DragDropModule_Factory(t) {\n  return new (t || DragDropModule)();\n};\n\nDragDropModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: DragDropModule\n});\nDragDropModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DragDrop],\n  imports: [CdkScrollableModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DragDropModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n      exports: [CdkScrollableModule, CdkDropList, CdkDropListGroup, CdkDrag, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder],\n      providers: [DragDrop]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };", "map": {"version": 3, "names": ["i0", "Injectable", "Inject", "InjectionToken", "Directive", "Input", "EventEmitter", "Optional", "SkipSelf", "Output", "Self", "ContentChildren", "ContentChild", "NgModule", "DOCUMENT", "_getEventTarget", "normalizePassiveListenerOptions", "_getShadowRoot", "coerceBooleanProperty", "coerceElement", "coerce<PERSON><PERSON><PERSON>", "coerceNumberProperty", "isFakeTouchstartFromScreenReader", "isFakeMousedownFromScreenReader", "Subject", "Subscription", "interval", "animationFrameScheduler", "Observable", "merge", "takeUntil", "startWith", "map", "take", "tap", "switchMap", "i1", "CdkScrollableModule", "i3", "extendStyles", "dest", "source", "importantProperties", "key", "hasOwnProperty", "value", "setProperty", "has", "removeProperty", "toggleNativeDragInteractions", "element", "enable", "userSelect", "style", "toggleVisibility", "position", "top", "opacity", "left", "combineTransforms", "transform", "initialTransform", "parseCssTimeUnitsToMs", "multiplier", "toLowerCase", "indexOf", "parseFloat", "getTransformTransitionDurationInMs", "computedStyle", "getComputedStyle", "transitionedProperties", "parseCssPropertyValue", "property", "find", "prop", "propertyIndex", "rawDurations", "rawDelays", "name", "getPropertyValue", "split", "part", "trim", "getMutableClientRect", "clientRect", "getBoundingClientRect", "right", "bottom", "width", "height", "x", "y", "isInsideClientRect", "adjustClientRect", "isPointerNearClientRect", "rect", "threshold", "pointerX", "pointerY", "xThreshold", "yT<PERSON><PERSON>old", "ParentPositionTracker", "constructor", "_document", "positions", "Map", "clear", "cache", "elements", "set", "scrollPosition", "getViewportScrollPosition", "for<PERSON>ach", "scrollTop", "scrollLeft", "handleScroll", "event", "target", "cachedPosition", "get", "newTop", "newLeft", "viewportScrollPosition", "topDifference", "leftDifference", "node", "contains", "window", "scrollY", "scrollX", "deepCloneNode", "clone", "cloneNode", "descendantsWithId", "querySelectorAll", "nodeName", "removeAttribute", "i", "length", "transferCanvasData", "transferInputData", "transferData", "selector", "callback", "descendantElements", "cloneElements", "cloneUniqueId", "type", "context", "getContext", "drawImage", "_a", "passiveEventListenerOptions", "passive", "activeEventListenerOptions", "MOUSE_EVENT_IGNORE_TIME", "dragImportantProperties", "Set", "DragRef", "_config", "_ngZone", "_viewportRuler", "_dragDropRegistry", "_passiveTransform", "_activeTransform", "_hasStartedDragging", "_moveEvents", "_pointerMoveSubscription", "EMPTY", "_pointerUpSubscription", "_scrollSubscription", "_resizeSubscription", "_boundaryElement", "_nativeInteractionsEnabled", "_handles", "_<PERSON><PERSON><PERSON><PERSON>", "_direction", "dragStartDelay", "_disabled", "beforeStarted", "started", "released", "ended", "entered", "exited", "dropped", "moved", "_pointerDown", "next", "targetHandle", "_getTar<PERSON><PERSON><PERSON>le", "disabled", "_initializeDragSequence", "_rootElement", "_pointerMove", "pointerPosition", "_getPointerPositionOnPage", "distanceX", "Math", "abs", "_pickupPositionOnPage", "distanceY", "isOverThreshold", "dragStartThreshold", "isDelayElapsed", "Date", "now", "_dragStartTime", "_getDragStartDelay", "container", "_dropContainer", "_endDragSequence", "isDragging", "isReceiving", "preventDefault", "run", "_startDragSequence", "constrainedPointerPosition", "_getConstrainedPointerPosition", "_hasMoved", "_lastKnownPointerPosition", "_updatePointerDirectionDelta", "_updateActiveDropContainer", "offset", "constrainPosition", "_initialClientRect", "activeTransform", "_applyRootElementTransform", "observers", "distance", "_getDragDistance", "delta", "_pointerDirectionDelta", "_pointerUp", "_nativeDragStart", "withRootElement", "with<PERSON><PERSON>nt", "parentDragRef", "_parentPositions", "registerDragItem", "newValue", "_toggleNativeDragInteractions", "handle", "getPlaceholderElement", "_placeholder", "getRootElement", "getVisibleElement", "<PERSON><PERSON><PERSON><PERSON>", "handles", "<PERSON><PERSON><PERSON><PERSON>", "add", "withPreviewTemplate", "template", "_previewTemplate", "withPlaceholderTemplate", "_placeholderTemplate", "rootElement", "_removeRootElementListeners", "runOutsideAngular", "addEventListener", "_initialTransform", "undefined", "SVGElement", "_ownerSVGElement", "ownerSVGElement", "withBoundaryElement", "boundaryElement", "unsubscribe", "change", "subscribe", "_containInsideBoundaryOnResize", "parent", "_parentDragRef", "dispose", "_b", "remove", "_anchor", "_destroyPreview", "_destroyPlaceholder", "removeDragItem", "_removeSubscriptions", "complete", "reset", "disable<PERSON><PERSON><PERSON>", "enableHandle", "delete", "withDirection", "direction", "_withDropContainer", "getFreeDragPosition", "setFreeDragPosition", "withPreviewContainer", "_previewContainer", "_sortFromLastPointerPosition", "_preview", "_previewRef", "destroy", "_placeholderRef", "stopDragging", "webkitTapHighlightColor", "_rootElementTapHighlight", "_stopScrolling", "_animatePreviewToPlaceholder", "then", "_cleanupDragArtifacts", "_cleanupCachedDimensions", "dropPoint", "isTouchEvent", "_lastTouchEventTime", "dropContainer", "parentNode", "placeholder", "_createPlaceholderElement", "anchor", "createComment", "shadowRoot", "insertBefore", "_createPreviewElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_getPreviewInsertionPoint", "start", "_initialContainer", "_initialIndex", "getItemIndex", "getScrollableParents", "referenceElement", "stopPropagation", "isTouchSequence", "isAuxiliaryMouseButton", "button", "isSyntheticEvent", "isFakeEvent", "draggable", "rootStyles", "pointer<PERSON><PERSON>", "pointerUp", "scrolled", "scrollEvent", "_updateOnScroll", "_boundaryRect", "previewTemplate", "_pickupPositionInElement", "matchSize", "_getPointerPositionInElement", "_pointerPositionAtLastDirectionChange", "startDragging", "_previewRect", "currentIndex", "isPointerOverContainer", "_isOverContainer", "item", "previousIndex", "previousContainer", "drop", "rawX", "rawY", "newContainer", "_getSiblingContainerFromPosition", "exit", "enter", "sortingDisabled", "_startScrollingIfNecessary", "_sortItem", "_applyPreviewTransform", "previewConfig", "previewClass", "preview", "rootRect", "viewRef", "viewContainer", "createEmbeddedView", "detectChanges", "getRootNode", "matchElementSize", "getTransform", "zIndex", "classList", "setAttribute", "Array", "isArray", "className", "Promise", "resolve", "placeholder<PERSON><PERSON><PERSON>", "duration", "handler", "propertyName", "removeEventListener", "clearTimeout", "timeout", "setTimeout", "placeholder<PERSON><PERSON><PERSON>g", "placeholderTemplate", "pointerEvents", "elementRect", "handleElement", "referenceRect", "point", "targetTouches", "_getViewportScrollPosition", "pageX", "pageY", "touches", "changedTouches", "svgMatrix", "getScreenCTM", "svgPoint", "createSVGPoint", "matrixTransform", "inverse", "dropContainerLock", "lockAxis", "pickupX", "pickupY", "boundaryRect", "previewWidth", "previewHeight", "_getPreviewRect", "minY", "maxY", "minX", "maxX", "clamp$1", "pointerPositionOnPage", "positionSinceLastChange", "changeX", "changeY", "pointerDirectionChangeThreshold", "shouldEnable", "styles", "currentPosition", "pickupPosition", "leftOverflow", "rightOverflow", "topOverflow", "bottomOverflow", "touch", "mouse", "scrollDifference", "_cachedShadowRoot", "initialParent", "previewContainer", "documentRef", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "round", "min", "max", "rootNodes", "nodeType", "ELEMENT_NODE", "wrapper", "createElement", "sourceRect", "moveItemInArray", "array", "fromIndex", "toIndex", "from", "clamp", "to", "transferArrayItem", "currentArray", "targetArray", "targetIndex", "splice", "copyArrayItem", "SingleAxisSortStrategy", "_element", "_itemPositions", "orientation", "_previousSwap", "drag", "overlaps", "items", "withItems", "sort", "pointer<PERSON><PERSON><PERSON>", "siblings", "newIndex", "_getItemIndexFromPointerPosition", "isHorizontal", "findIndex", "currentItem", "siblingAtNewPosition", "newPosition", "itemOffset", "_getItemOffsetPx", "siblingOffset", "_getSiblingOffsetPx", "oldOrder", "slice", "sibling", "index", "isDraggedItem", "elementToOffset", "activeDraggables", "_activeDraggables", "newPositionReference", "_shouldEnterAsFirstChild", "parentElement", "push", "_cacheItemPositions", "withSortPredicate", "predicate", "_sortPredicate", "p", "getActiveItemsSnapshot", "reverse", "updateOnScroll", "elementToMeasure", "a", "b", "immediateSibling", "end", "itemPositions", "reversed", "lastItemRect", "firstItemRect", "floor", "DROP_PROXIMITY_THRESHOLD", "SCROLL_PROXIMITY_THRESHOLD", "DropListRef", "autoScrollDisabled", "autoScrollStep", "enterPredicate", "sortPredicate", "sorted", "_isDragging", "_draggables", "_siblings", "_activeSiblings", "_viewportScrollSubscription", "_verticalScrollDirection", "_horizontalScrollDirection", "_stopScrollTimers", "_startScrollInterval", "pipe", "_scrollNode", "scrollStep", "scrollBy", "withScrollableParents", "registerDropContainer", "_sortStrategy", "removeDropContainer", "_draggingStarted", "_notifyReceivingSiblings", "_cacheParentPositions", "_reset", "previousItems", "draggedItems", "filter", "every", "connectedTo", "withOrientation", "_scrollableElements", "size", "_clientRect", "result", "scrollNode", "verticalScrollDirection", "horizontalScrollDirection", "getElementScrollDirections", "getViewportSize", "getVerticalScrollDirection", "getHorizontalScrollDirection", "_initialScrollSnap", "msScrollSnapType", "scrollSnapType", "_listenToScrollEvents", "_stopReceiving", "_canReceive", "elementFromPoint", "nativeElement", "_startReceiving", "activeSiblings", "computedVertical", "computedHorizontal", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "activeCapturingEventOptions", "capture", "DragDropRegistry", "_dropInstances", "_dragInstances", "_activeDragInstances", "_globalListeners", "_draggingPredicate", "scroll", "_preventDefaultWhileDragging", "_persistentTouchmoveListener", "some", "startsWith", "e", "options", "config", "_clearGlobalListeners", "streams", "observer", "eventOptions", "ngOnDestroy", "instance", "ɵfac", "NgZone", "ɵprov", "args", "providedIn", "decorators", "DEFAULT_CONFIG", "DragDrop", "createDrag", "createDropList", "ViewportRuler", "CDK_DRAG_PARENT", "CDK_DROP_LIST_GROUP", "CdkDropListGroup", "_items", "ɵdir", "provide", "useExisting", "exportAs", "providers", "CDK_DRAG_CONFIG", "assertElementNode", "Error", "_uniqueIdCounter", "CDK_DROP_LIST", "CdkDropList", "dragDrop", "_changeDetectorRef", "_scrollDispatcher", "_dir", "_group", "_destroyed", "id", "_unsortedItems", "ngDevMode", "_dropListRef", "data", "_assignDefaults", "_setupInputSyncSubscription", "_handleEvents", "_dropLists", "addItem", "_syncItemsWithRef", "removeItem", "getSortedItems", "documentPosition", "_dragRef", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "ref", "correspondingDropList", "list", "console", "warn", "_scrollableParentsResolved", "scrollableParents", "getAncestorScrollContainers", "scrollable", "getElementRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emit", "dropEvent", "draggingDisabled", "listAutoScrollDisabled", "listOrientation", "ElementRef", "ChangeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Directionality", "useValue", "host", "CDK_DRAG_HANDLE", "CdkDragHandle", "parentDrag", "_stateChanges", "_parentDrag", "CDK_DRAG_PLACEHOLDER", "CdkDragPlaceholder", "templateRef", "TemplateRef", "CDK_DRAG_PREVIEW", "CdkDragPreview", "_matchSize", "DRAG_HOST_CLASS", "CdkDrag", "_viewContainerRef", "_selfHandle", "subscription", "movedEvent", "_syncInputs", "ngAfterViewInit", "onStable", "_updateRootElement", "_setupHandlesListener", "freeDragPosition", "ngOnChanges", "changes", "rootSelectorChange", "positionChange", "firstChange", "rootElementSelector", "closest", "_getBoundaryElement", "boundary", "dir", "startEvent", "releaseEvent", "endEvent", "enterEvent", "exitEvent", "childHandleElements", "handleInstance", "dragRef", "ViewContainerRef", "descendants", "DragDropModule", "ɵmod", "ɵinj", "declarations", "exports"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/drag-drop.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, Directive, Input, EventEmitter, Optional, SkipSelf, Output, Self, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { _getEventTarget, normalizePassiveListenerOptions, _getShadowRoot } from '@angular/cdk/platform';\nimport { coerceBooleanProperty, coerceElement, coerceArray, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { Subject, Subscription, interval, animationFrameScheduler, Observable, merge } from 'rxjs';\nimport { takeUntil, startWith, map, take, tap, switchMap } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i3 from '@angular/cdk/bidi';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Shallow-extends a stylesheet object with another stylesheet-like object.\n * Note that the keys in `source` have to be dash-cased.\n * @docs-private\n */\nfunction extendStyles(dest, source, importantProperties) {\n    for (let key in source) {\n        if (source.hasOwnProperty(key)) {\n            const value = source[key];\n            if (value) {\n                dest.setProperty(key, value, (importantProperties === null || importantProperties === void 0 ? void 0 : importantProperties.has(key)) ? 'important' : '');\n            }\n            else {\n                dest.removeProperty(key);\n            }\n        }\n    }\n    return dest;\n}\n/**\n * Toggles whether the native drag interactions should be enabled for an element.\n * @param element Element on which to toggle the drag interactions.\n * @param enable Whether the drag interactions should be enabled.\n * @docs-private\n */\nfunction toggleNativeDragInteractions(element, enable) {\n    const userSelect = enable ? '' : 'none';\n    extendStyles(element.style, {\n        'touch-action': enable ? '' : 'none',\n        '-webkit-user-drag': enable ? '' : 'none',\n        '-webkit-tap-highlight-color': enable ? '' : 'transparent',\n        'user-select': userSelect,\n        '-ms-user-select': userSelect,\n        '-webkit-user-select': userSelect,\n        '-moz-user-select': userSelect,\n    });\n}\n/**\n * Toggles whether an element is visible while preserving its dimensions.\n * @param element Element whose visibility to toggle\n * @param enable Whether the element should be visible.\n * @param importantProperties Properties to be set as `!important`.\n * @docs-private\n */\nfunction toggleVisibility(element, enable, importantProperties) {\n    extendStyles(element.style, {\n        position: enable ? '' : 'fixed',\n        top: enable ? '' : '0',\n        opacity: enable ? '' : '0',\n        left: enable ? '' : '-999em',\n    }, importantProperties);\n}\n/**\n * Combines a transform string with an optional other transform\n * that exited before the base transform was applied.\n */\nfunction combineTransforms(transform, initialTransform) {\n    return initialTransform && initialTransform != 'none'\n        ? transform + ' ' + initialTransform\n        : transform;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Parses a CSS time value to milliseconds. */\nfunction parseCssTimeUnitsToMs(value) {\n    // Some browsers will return it in seconds, whereas others will return milliseconds.\n    const multiplier = value.toLowerCase().indexOf('ms') > -1 ? 1 : 1000;\n    return parseFloat(value) * multiplier;\n}\n/** Gets the transform transition duration, including the delay, of an element in milliseconds. */\nfunction getTransformTransitionDurationInMs(element) {\n    const computedStyle = getComputedStyle(element);\n    const transitionedProperties = parseCssPropertyValue(computedStyle, 'transition-property');\n    const property = transitionedProperties.find(prop => prop === 'transform' || prop === 'all');\n    // If there's no transition for `all` or `transform`, we shouldn't do anything.\n    if (!property) {\n        return 0;\n    }\n    // Get the index of the property that we're interested in and match\n    // it up to the same index in `transition-delay` and `transition-duration`.\n    const propertyIndex = transitionedProperties.indexOf(property);\n    const rawDurations = parseCssPropertyValue(computedStyle, 'transition-duration');\n    const rawDelays = parseCssPropertyValue(computedStyle, 'transition-delay');\n    return (parseCssTimeUnitsToMs(rawDurations[propertyIndex]) +\n        parseCssTimeUnitsToMs(rawDelays[propertyIndex]));\n}\n/** Parses out multiple values from a computed style into an array. */\nfunction parseCssPropertyValue(computedStyle, name) {\n    const value = computedStyle.getPropertyValue(name);\n    return value.split(',').map(part => part.trim());\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets a mutable version of an element's bounding `ClientRect`. */\nfunction getMutableClientRect(element) {\n    const clientRect = element.getBoundingClientRect();\n    // We need to clone the `clientRect` here, because all the values on it are readonly\n    // and we need to be able to update them. Also we can't use a spread here, because\n    // the values on a `ClientRect` aren't own properties. See:\n    // https://developer.mozilla.org/en-US/docs/Web/API/Element/getBoundingClientRect#Notes\n    return {\n        top: clientRect.top,\n        right: clientRect.right,\n        bottom: clientRect.bottom,\n        left: clientRect.left,\n        width: clientRect.width,\n        height: clientRect.height,\n        x: clientRect.x,\n        y: clientRect.y,\n    };\n}\n/**\n * Checks whether some coordinates are within a `ClientRect`.\n * @param clientRect ClientRect that is being checked.\n * @param x Coordinates along the X axis.\n * @param y Coordinates along the Y axis.\n */\nfunction isInsideClientRect(clientRect, x, y) {\n    const { top, bottom, left, right } = clientRect;\n    return y >= top && y <= bottom && x >= left && x <= right;\n}\n/**\n * Updates the top/left positions of a `ClientRect`, as well as their bottom/right counterparts.\n * @param clientRect `ClientRect` that should be updated.\n * @param top Amount to add to the `top` position.\n * @param left Amount to add to the `left` position.\n */\nfunction adjustClientRect(clientRect, top, left) {\n    clientRect.top += top;\n    clientRect.bottom = clientRect.top + clientRect.height;\n    clientRect.left += left;\n    clientRect.right = clientRect.left + clientRect.width;\n}\n/**\n * Checks whether the pointer coordinates are close to a ClientRect.\n * @param rect ClientRect to check against.\n * @param threshold Threshold around the ClientRect.\n * @param pointerX Coordinates along the X axis.\n * @param pointerY Coordinates along the Y axis.\n */\nfunction isPointerNearClientRect(rect, threshold, pointerX, pointerY) {\n    const { top, right, bottom, left, width, height } = rect;\n    const xThreshold = width * threshold;\n    const yThreshold = height * threshold;\n    return (pointerY > top - yThreshold &&\n        pointerY < bottom + yThreshold &&\n        pointerX > left - xThreshold &&\n        pointerX < right + xThreshold);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Keeps track of the scroll position and dimensions of the parents of an element. */\nclass ParentPositionTracker {\n    constructor(_document) {\n        this._document = _document;\n        /** Cached positions of the scrollable parent elements. */\n        this.positions = new Map();\n    }\n    /** Clears the cached positions. */\n    clear() {\n        this.positions.clear();\n    }\n    /** Caches the positions. Should be called at the beginning of a drag sequence. */\n    cache(elements) {\n        this.clear();\n        this.positions.set(this._document, {\n            scrollPosition: this.getViewportScrollPosition(),\n        });\n        elements.forEach(element => {\n            this.positions.set(element, {\n                scrollPosition: { top: element.scrollTop, left: element.scrollLeft },\n                clientRect: getMutableClientRect(element),\n            });\n        });\n    }\n    /** Handles scrolling while a drag is taking place. */\n    handleScroll(event) {\n        const target = _getEventTarget(event);\n        const cachedPosition = this.positions.get(target);\n        if (!cachedPosition) {\n            return null;\n        }\n        const scrollPosition = cachedPosition.scrollPosition;\n        let newTop;\n        let newLeft;\n        if (target === this._document) {\n            const viewportScrollPosition = this.getViewportScrollPosition();\n            newTop = viewportScrollPosition.top;\n            newLeft = viewportScrollPosition.left;\n        }\n        else {\n            newTop = target.scrollTop;\n            newLeft = target.scrollLeft;\n        }\n        const topDifference = scrollPosition.top - newTop;\n        const leftDifference = scrollPosition.left - newLeft;\n        // Go through and update the cached positions of the scroll\n        // parents that are inside the element that was scrolled.\n        this.positions.forEach((position, node) => {\n            if (position.clientRect && target !== node && target.contains(node)) {\n                adjustClientRect(position.clientRect, topDifference, leftDifference);\n            }\n        });\n        scrollPosition.top = newTop;\n        scrollPosition.left = newLeft;\n        return { top: topDifference, left: leftDifference };\n    }\n    /**\n     * Gets the scroll position of the viewport. Note that we use the scrollX and scrollY directly,\n     * instead of going through the `ViewportRuler`, because the first value the ruler looks at is\n     * the top/left offset of the `document.documentElement` which works for most cases, but breaks\n     * if the element is offset by something like the `BlockScrollStrategy`.\n     */\n    getViewportScrollPosition() {\n        return { top: window.scrollY, left: window.scrollX };\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Creates a deep clone of an element. */\nfunction deepCloneNode(node) {\n    const clone = node.cloneNode(true);\n    const descendantsWithId = clone.querySelectorAll('[id]');\n    const nodeName = node.nodeName.toLowerCase();\n    // Remove the `id` to avoid having multiple elements with the same id on the page.\n    clone.removeAttribute('id');\n    for (let i = 0; i < descendantsWithId.length; i++) {\n        descendantsWithId[i].removeAttribute('id');\n    }\n    if (nodeName === 'canvas') {\n        transferCanvasData(node, clone);\n    }\n    else if (nodeName === 'input' || nodeName === 'select' || nodeName === 'textarea') {\n        transferInputData(node, clone);\n    }\n    transferData('canvas', node, clone, transferCanvasData);\n    transferData('input, textarea, select', node, clone, transferInputData);\n    return clone;\n}\n/** Matches elements between an element and its clone and allows for their data to be cloned. */\nfunction transferData(selector, node, clone, callback) {\n    const descendantElements = node.querySelectorAll(selector);\n    if (descendantElements.length) {\n        const cloneElements = clone.querySelectorAll(selector);\n        for (let i = 0; i < descendantElements.length; i++) {\n            callback(descendantElements[i], cloneElements[i]);\n        }\n    }\n}\n// Counter for unique cloned radio button names.\nlet cloneUniqueId = 0;\n/** Transfers the data of one input element to another. */\nfunction transferInputData(source, clone) {\n    // Browsers throw an error when assigning the value of a file input programmatically.\n    if (clone.type !== 'file') {\n        clone.value = source.value;\n    }\n    // Radio button `name` attributes must be unique for radio button groups\n    // otherwise original radio buttons can lose their checked state\n    // once the clone is inserted in the DOM.\n    if (clone.type === 'radio' && clone.name) {\n        clone.name = `mat-clone-${clone.name}-${cloneUniqueId++}`;\n    }\n}\n/** Transfers the data of one canvas element to another. */\nfunction transferCanvasData(source, clone) {\n    const context = clone.getContext('2d');\n    if (context) {\n        // In some cases `drawImage` can throw (e.g. if the canvas size is 0x0).\n        // We can't do much about it so just ignore the error.\n        try {\n            context.drawImage(source, 0, 0);\n        }\n        catch (_a) { }\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Options that can be used to bind a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({ passive: true });\n/** Options that can be used to bind an active event listener. */\nconst activeEventListenerOptions = normalizePassiveListenerOptions({ passive: false });\n/**\n * Time in milliseconds for which to ignore mouse events, after\n * receiving a touch event. Used to avoid doing double work for\n * touch devices where the browser fires fake mouse events, in\n * addition to touch events.\n */\nconst MOUSE_EVENT_IGNORE_TIME = 800;\n/** Inline styles to be set as `!important` while dragging. */\nconst dragImportantProperties = new Set([\n    // Needs to be important, because some `mat-table` sets `position: sticky !important`. See #22781.\n    'position',\n]);\n/**\n * Reference to a draggable item. Used to manipulate or dispose of the item.\n */\nclass DragRef {\n    constructor(element, _config, _document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._config = _config;\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n        /**\n         * CSS `transform` applied to the element when it isn't being dragged. We need a\n         * passive transform in order for the dragged element to retain its new position\n         * after the user has stopped dragging and because we need to know the relative\n         * position in case they start dragging again. This corresponds to `element.style.transform`.\n         */\n        this._passiveTransform = { x: 0, y: 0 };\n        /** CSS `transform` that is applied to the element while it's being dragged. */\n        this._activeTransform = { x: 0, y: 0 };\n        /**\n         * Whether the dragging sequence has been started. Doesn't\n         * necessarily mean that the element has been moved.\n         */\n        this._hasStartedDragging = false;\n        /** Emits when the item is being moved. */\n        this._moveEvents = new Subject();\n        /** Subscription to pointer movement events. */\n        this._pointerMoveSubscription = Subscription.EMPTY;\n        /** Subscription to the event that is dispatched when the user lifts their pointer. */\n        this._pointerUpSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being scrolled. */\n        this._scrollSubscription = Subscription.EMPTY;\n        /** Subscription to the viewport being resized. */\n        this._resizeSubscription = Subscription.EMPTY;\n        /** Cached reference to the boundary element. */\n        this._boundaryElement = null;\n        /** Whether the native dragging interactions have been enabled on the root element. */\n        this._nativeInteractionsEnabled = true;\n        /** Elements that can be used to drag the draggable item. */\n        this._handles = [];\n        /** Registered handles that are currently disabled. */\n        this._disabledHandles = new Set();\n        /** Layout direction of the item. */\n        this._direction = 'ltr';\n        /**\n         * Amount of milliseconds to wait after the user has put their\n         * pointer down before starting to drag the element.\n         */\n        this.dragStartDelay = 0;\n        this._disabled = false;\n        /** Emits as the drag sequence is being prepared. */\n        this.beforeStarted = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new Subject();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new Subject();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new Subject();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new Subject();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new Subject();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new Subject();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = this._moveEvents;\n        /** Handler for the `mousedown`/`touchstart` events. */\n        this._pointerDown = (event) => {\n            this.beforeStarted.next();\n            // Delegate the event based on whether it started from a handle or the element itself.\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    this._initializeDragSequence(targetHandle, event);\n                }\n            }\n            else if (!this.disabled) {\n                this._initializeDragSequence(this._rootElement, event);\n            }\n        };\n        /** Handler that is invoked when the user moves their pointer after they've initiated a drag. */\n        this._pointerMove = (event) => {\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            if (!this._hasStartedDragging) {\n                const distanceX = Math.abs(pointerPosition.x - this._pickupPositionOnPage.x);\n                const distanceY = Math.abs(pointerPosition.y - this._pickupPositionOnPage.y);\n                const isOverThreshold = distanceX + distanceY >= this._config.dragStartThreshold;\n                // Only start dragging after the user has moved more than the minimum distance in either\n                // direction. Note that this is preferable over doing something like `skip(minimumDistance)`\n                // in the `pointerMove` subscription, because we're not guaranteed to have one move event\n                // per pixel of movement (e.g. if the user moves their pointer quickly).\n                if (isOverThreshold) {\n                    const isDelayElapsed = Date.now() >= this._dragStartTime + this._getDragStartDelay(event);\n                    const container = this._dropContainer;\n                    if (!isDelayElapsed) {\n                        this._endDragSequence(event);\n                        return;\n                    }\n                    // Prevent other drag sequences from starting while something in the container is still\n                    // being dragged. This can happen while we're waiting for the drop animation to finish\n                    // and can cause errors, because some elements might still be moving around.\n                    if (!container || (!container.isDragging() && !container.isReceiving())) {\n                        // Prevent the default action as soon as the dragging sequence is considered as\n                        // \"started\" since waiting for the next event can allow the device to begin scrolling.\n                        event.preventDefault();\n                        this._hasStartedDragging = true;\n                        this._ngZone.run(() => this._startDragSequence(event));\n                    }\n                }\n                return;\n            }\n            // We prevent the default action down here so that we know that dragging has started. This is\n            // important for touch devices where doing this too early can unnecessarily block scrolling,\n            // if there's a dragging delay.\n            event.preventDefault();\n            const constrainedPointerPosition = this._getConstrainedPointerPosition(pointerPosition);\n            this._hasMoved = true;\n            this._lastKnownPointerPosition = pointerPosition;\n            this._updatePointerDirectionDelta(constrainedPointerPosition);\n            if (this._dropContainer) {\n                this._updateActiveDropContainer(constrainedPointerPosition, pointerPosition);\n            }\n            else {\n                // If there's a position constraint function, we want the element's top/left to be at the\n                // specific position on the page. Use the initial position as a reference if that's the case.\n                const offset = this.constrainPosition ? this._initialClientRect : this._pickupPositionOnPage;\n                const activeTransform = this._activeTransform;\n                activeTransform.x = constrainedPointerPosition.x - offset.x + this._passiveTransform.x;\n                activeTransform.y = constrainedPointerPosition.y - offset.y + this._passiveTransform.y;\n                this._applyRootElementTransform(activeTransform.x, activeTransform.y);\n            }\n            // Since this event gets fired for every pixel while dragging, we only\n            // want to fire it if the consumer opted into it. Also we have to\n            // re-enter the zone because we run all of the events on the outside.\n            if (this._moveEvents.observers.length) {\n                this._ngZone.run(() => {\n                    this._moveEvents.next({\n                        source: this,\n                        pointerPosition: constrainedPointerPosition,\n                        event,\n                        distance: this._getDragDistance(constrainedPointerPosition),\n                        delta: this._pointerDirectionDelta,\n                    });\n                });\n            }\n        };\n        /** Handler that is invoked when the user lifts their pointer up, after initiating a drag. */\n        this._pointerUp = (event) => {\n            this._endDragSequence(event);\n        };\n        /** Handles a native `dragstart` event. */\n        this._nativeDragStart = (event) => {\n            if (this._handles.length) {\n                const targetHandle = this._getTargetHandle(event);\n                if (targetHandle && !this._disabledHandles.has(targetHandle) && !this.disabled) {\n                    event.preventDefault();\n                }\n            }\n            else if (!this.disabled) {\n                // Usually this isn't necessary since the we prevent the default action in `pointerDown`,\n                // but some cases like dragging of links can slip through (see #24403).\n                event.preventDefault();\n            }\n        };\n        this.withRootElement(element).withParent(_config.parentDragRef || null);\n        this._parentPositions = new ParentPositionTracker(_document);\n        _dragDropRegistry.registerDragItem(this);\n    }\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || !!(this._dropContainer && this._dropContainer.disabled);\n    }\n    set disabled(value) {\n        const newValue = coerceBooleanProperty(value);\n        if (newValue !== this._disabled) {\n            this._disabled = newValue;\n            this._toggleNativeDragInteractions();\n            this._handles.forEach(handle => toggleNativeDragInteractions(handle, newValue));\n        }\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._placeholder;\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._rootElement;\n    }\n    /**\n     * Gets the currently-visible element that represents the drag item.\n     * While dragging this is the placeholder, otherwise it's the root element.\n     */\n    getVisibleElement() {\n        return this.isDragging() ? this.getPlaceholderElement() : this.getRootElement();\n    }\n    /** Registers the handles that can be used to drag the element. */\n    withHandles(handles) {\n        this._handles = handles.map(handle => coerceElement(handle));\n        this._handles.forEach(handle => toggleNativeDragInteractions(handle, this.disabled));\n        this._toggleNativeDragInteractions();\n        // Delete any lingering disabled handles that may have been destroyed. Note that we re-create\n        // the set, rather than iterate over it and filter out the destroyed handles, because while\n        // the ES spec allows for sets to be modified while they're being iterated over, some polyfills\n        // use an array internally which may throw an error.\n        const disabledHandles = new Set();\n        this._disabledHandles.forEach(handle => {\n            if (this._handles.indexOf(handle) > -1) {\n                disabledHandles.add(handle);\n            }\n        });\n        this._disabledHandles = disabledHandles;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag preview.\n     * @param template Template that from which to stamp out the preview.\n     */\n    withPreviewTemplate(template) {\n        this._previewTemplate = template;\n        return this;\n    }\n    /**\n     * Registers the template that should be used for the drag placeholder.\n     * @param template Template that from which to stamp out the placeholder.\n     */\n    withPlaceholderTemplate(template) {\n        this._placeholderTemplate = template;\n        return this;\n    }\n    /**\n     * Sets an alternate drag root element. The root element is the element that will be moved as\n     * the user is dragging. Passing an alternate root element is useful when trying to enable\n     * dragging on an element that you might not have access to.\n     */\n    withRootElement(rootElement) {\n        const element = coerceElement(rootElement);\n        if (element !== this._rootElement) {\n            if (this._rootElement) {\n                this._removeRootElementListeners(this._rootElement);\n            }\n            this._ngZone.runOutsideAngular(() => {\n                element.addEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n                element.addEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n                element.addEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n            });\n            this._initialTransform = undefined;\n            this._rootElement = element;\n        }\n        if (typeof SVGElement !== 'undefined' && this._rootElement instanceof SVGElement) {\n            this._ownerSVGElement = this._rootElement.ownerSVGElement;\n        }\n        return this;\n    }\n    /**\n     * Element to which the draggable's position will be constrained.\n     */\n    withBoundaryElement(boundaryElement) {\n        this._boundaryElement = boundaryElement ? coerceElement(boundaryElement) : null;\n        this._resizeSubscription.unsubscribe();\n        if (boundaryElement) {\n            this._resizeSubscription = this._viewportRuler\n                .change(10)\n                .subscribe(() => this._containInsideBoundaryOnResize());\n        }\n        return this;\n    }\n    /** Sets the parent ref that the ref is nested in.  */\n    withParent(parent) {\n        this._parentDragRef = parent;\n        return this;\n    }\n    /** Removes the dragging functionality from the DOM element. */\n    dispose() {\n        var _a, _b;\n        this._removeRootElementListeners(this._rootElement);\n        // Do this check before removing from the registry since it'll\n        // stop being considered as dragged once it is removed.\n        if (this.isDragging()) {\n            // Since we move out the element to the end of the body while it's being\n            // dragged, we have to make sure that it's removed if it gets destroyed.\n            (_a = this._rootElement) === null || _a === void 0 ? void 0 : _a.remove();\n        }\n        (_b = this._anchor) === null || _b === void 0 ? void 0 : _b.remove();\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._dragDropRegistry.removeDragItem(this);\n        this._removeSubscriptions();\n        this.beforeStarted.complete();\n        this.started.complete();\n        this.released.complete();\n        this.ended.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this._moveEvents.complete();\n        this._handles = [];\n        this._disabledHandles.clear();\n        this._dropContainer = undefined;\n        this._resizeSubscription.unsubscribe();\n        this._parentPositions.clear();\n        this._boundaryElement =\n            this._rootElement =\n                this._ownerSVGElement =\n                    this._placeholderTemplate =\n                        this._previewTemplate =\n                            this._anchor =\n                                this._parentDragRef =\n                                    null;\n    }\n    /** Checks whether the element is currently being dragged. */\n    isDragging() {\n        return this._hasStartedDragging && this._dragDropRegistry.isDragging(this);\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._rootElement.style.transform = this._initialTransform || '';\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform = { x: 0, y: 0 };\n    }\n    /**\n     * Sets a handle as disabled. While a handle is disabled, it'll capture and interrupt dragging.\n     * @param handle Handle element that should be disabled.\n     */\n    disableHandle(handle) {\n        if (!this._disabledHandles.has(handle) && this._handles.indexOf(handle) > -1) {\n            this._disabledHandles.add(handle);\n            toggleNativeDragInteractions(handle, true);\n        }\n    }\n    /**\n     * Enables a handle, if it has been disabled.\n     * @param handle Handle element to be enabled.\n     */\n    enableHandle(handle) {\n        if (this._disabledHandles.has(handle)) {\n            this._disabledHandles.delete(handle);\n            toggleNativeDragInteractions(handle, this.disabled);\n        }\n    }\n    /** Sets the layout direction of the draggable item. */\n    withDirection(direction) {\n        this._direction = direction;\n        return this;\n    }\n    /** Sets the container that the item is part of. */\n    _withDropContainer(container) {\n        this._dropContainer = container;\n    }\n    /**\n     * Gets the current position in pixels the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        const position = this.isDragging() ? this._activeTransform : this._passiveTransform;\n        return { x: position.x, y: position.y };\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._activeTransform = { x: 0, y: 0 };\n        this._passiveTransform.x = value.x;\n        this._passiveTransform.y = value.y;\n        if (!this._dropContainer) {\n            this._applyRootElementTransform(value.x, value.y);\n        }\n        return this;\n    }\n    /**\n     * Sets the container into which to insert the preview element.\n     * @param value Container into which to insert the preview.\n     */\n    withPreviewContainer(value) {\n        this._previewContainer = value;\n        return this;\n    }\n    /** Updates the item's sort order based on the last-known pointer position. */\n    _sortFromLastPointerPosition() {\n        const position = this._lastKnownPointerPosition;\n        if (position && this._dropContainer) {\n            this._updateActiveDropContainer(this._getConstrainedPointerPosition(position), position);\n        }\n    }\n    /** Unsubscribes from the global subscriptions. */\n    _removeSubscriptions() {\n        this._pointerMoveSubscription.unsubscribe();\n        this._pointerUpSubscription.unsubscribe();\n        this._scrollSubscription.unsubscribe();\n    }\n    /** Destroys the preview element and its ViewRef. */\n    _destroyPreview() {\n        var _a, _b;\n        (_a = this._preview) === null || _a === void 0 ? void 0 : _a.remove();\n        (_b = this._previewRef) === null || _b === void 0 ? void 0 : _b.destroy();\n        this._preview = this._previewRef = null;\n    }\n    /** Destroys the placeholder element and its ViewRef. */\n    _destroyPlaceholder() {\n        var _a, _b;\n        (_a = this._placeholder) === null || _a === void 0 ? void 0 : _a.remove();\n        (_b = this._placeholderRef) === null || _b === void 0 ? void 0 : _b.destroy();\n        this._placeholder = this._placeholderRef = null;\n    }\n    /**\n     * Clears subscriptions and stops the dragging sequence.\n     * @param event Browser event object that ended the sequence.\n     */\n    _endDragSequence(event) {\n        // Note that here we use `isDragging` from the service, rather than from `this`.\n        // The difference is that the one from the service reflects whether a dragging sequence\n        // has been initiated, whereas the one on `this` includes whether the user has passed\n        // the minimum dragging threshold.\n        if (!this._dragDropRegistry.isDragging(this)) {\n            return;\n        }\n        this._removeSubscriptions();\n        this._dragDropRegistry.stopDragging(this);\n        this._toggleNativeDragInteractions();\n        if (this._handles) {\n            this._rootElement.style.webkitTapHighlightColor =\n                this._rootElementTapHighlight;\n        }\n        if (!this._hasStartedDragging) {\n            return;\n        }\n        this.released.next({ source: this, event });\n        if (this._dropContainer) {\n            // Stop scrolling immediately, instead of waiting for the animation to finish.\n            this._dropContainer._stopScrolling();\n            this._animatePreviewToPlaceholder().then(() => {\n                this._cleanupDragArtifacts(event);\n                this._cleanupCachedDimensions();\n                this._dragDropRegistry.stopDragging(this);\n            });\n        }\n        else {\n            // Convert the active transform into a passive one. This means that next time\n            // the user starts dragging the item, its position will be calculated relatively\n            // to the new passive transform.\n            this._passiveTransform.x = this._activeTransform.x;\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            this._passiveTransform.y = this._activeTransform.y;\n            this._ngZone.run(() => {\n                this.ended.next({\n                    source: this,\n                    distance: this._getDragDistance(pointerPosition),\n                    dropPoint: pointerPosition,\n                    event,\n                });\n            });\n            this._cleanupCachedDimensions();\n            this._dragDropRegistry.stopDragging(this);\n        }\n    }\n    /** Starts the dragging sequence. */\n    _startDragSequence(event) {\n        if (isTouchEvent(event)) {\n            this._lastTouchEventTime = Date.now();\n        }\n        this._toggleNativeDragInteractions();\n        const dropContainer = this._dropContainer;\n        if (dropContainer) {\n            const element = this._rootElement;\n            const parent = element.parentNode;\n            const placeholder = (this._placeholder = this._createPlaceholderElement());\n            const anchor = (this._anchor = this._anchor || this._document.createComment(''));\n            // Needs to happen before the root element is moved.\n            const shadowRoot = this._getShadowRoot();\n            // Insert an anchor node so that we can restore the element's position in the DOM.\n            parent.insertBefore(anchor, element);\n            // There's no risk of transforms stacking when inside a drop container so\n            // we can keep the initial transform up to date any time dragging starts.\n            this._initialTransform = element.style.transform || '';\n            // Create the preview after the initial transform has\n            // been cached, because it can be affected by the transform.\n            this._preview = this._createPreviewElement();\n            // We move the element out at the end of the body and we make it hidden, because keeping it in\n            // place will throw off the consumer's `:last-child` selectors. We can't remove the element\n            // from the DOM completely, because iOS will stop firing all subsequent events in the chain.\n            toggleVisibility(element, false, dragImportantProperties);\n            this._document.body.appendChild(parent.replaceChild(placeholder, element));\n            this._getPreviewInsertionPoint(parent, shadowRoot).appendChild(this._preview);\n            this.started.next({ source: this, event }); // Emit before notifying the container.\n            dropContainer.start();\n            this._initialContainer = dropContainer;\n            this._initialIndex = dropContainer.getItemIndex(this);\n        }\n        else {\n            this.started.next({ source: this, event });\n            this._initialContainer = this._initialIndex = undefined;\n        }\n        // Important to run after we've called `start` on the parent container\n        // so that it has had time to resolve its scrollable parents.\n        this._parentPositions.cache(dropContainer ? dropContainer.getScrollableParents() : []);\n    }\n    /**\n     * Sets up the different variables and subscriptions\n     * that will be necessary for the dragging sequence.\n     * @param referenceElement Element that started the drag sequence.\n     * @param event Browser event object that started the sequence.\n     */\n    _initializeDragSequence(referenceElement, event) {\n        // Stop propagation if the item is inside another\n        // draggable so we don't start multiple drag sequences.\n        if (this._parentDragRef) {\n            event.stopPropagation();\n        }\n        const isDragging = this.isDragging();\n        const isTouchSequence = isTouchEvent(event);\n        const isAuxiliaryMouseButton = !isTouchSequence && event.button !== 0;\n        const rootElement = this._rootElement;\n        const target = _getEventTarget(event);\n        const isSyntheticEvent = !isTouchSequence &&\n            this._lastTouchEventTime &&\n            this._lastTouchEventTime + MOUSE_EVENT_IGNORE_TIME > Date.now();\n        const isFakeEvent = isTouchSequence\n            ? isFakeTouchstartFromScreenReader(event)\n            : isFakeMousedownFromScreenReader(event);\n        // If the event started from an element with the native HTML drag&drop, it'll interfere\n        // with our own dragging (e.g. `img` tags do it by default). Prevent the default action\n        // to stop it from happening. Note that preventing on `dragstart` also seems to work, but\n        // it's flaky and it fails if the user drags it away quickly. Also note that we only want\n        // to do this for `mousedown` since doing the same for `touchstart` will stop any `click`\n        // events from firing on touch devices.\n        if (target && target.draggable && event.type === 'mousedown') {\n            event.preventDefault();\n        }\n        // Abort if the user is already dragging or is using a mouse button other than the primary one.\n        if (isDragging || isAuxiliaryMouseButton || isSyntheticEvent || isFakeEvent) {\n            return;\n        }\n        // If we've got handles, we need to disable the tap highlight on the entire root element,\n        // otherwise iOS will still add it, even though all the drag interactions on the handle\n        // are disabled.\n        if (this._handles.length) {\n            const rootStyles = rootElement.style;\n            this._rootElementTapHighlight = rootStyles.webkitTapHighlightColor || '';\n            rootStyles.webkitTapHighlightColor = 'transparent';\n        }\n        this._hasStartedDragging = this._hasMoved = false;\n        // Avoid multiple subscriptions and memory leaks when multi touch\n        // (isDragging check above isn't enough because of possible temporal and/or dimensional delays)\n        this._removeSubscriptions();\n        this._initialClientRect = this._rootElement.getBoundingClientRect();\n        this._pointerMoveSubscription = this._dragDropRegistry.pointerMove.subscribe(this._pointerMove);\n        this._pointerUpSubscription = this._dragDropRegistry.pointerUp.subscribe(this._pointerUp);\n        this._scrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(scrollEvent => this._updateOnScroll(scrollEvent));\n        if (this._boundaryElement) {\n            this._boundaryRect = getMutableClientRect(this._boundaryElement);\n        }\n        // If we have a custom preview we can't know ahead of time how large it'll be so we position\n        // it next to the cursor. The exception is when the consumer has opted into making the preview\n        // the same size as the root element, in which case we do know the size.\n        const previewTemplate = this._previewTemplate;\n        this._pickupPositionInElement =\n            previewTemplate && previewTemplate.template && !previewTemplate.matchSize\n                ? { x: 0, y: 0 }\n                : this._getPointerPositionInElement(this._initialClientRect, referenceElement, event);\n        const pointerPosition = (this._pickupPositionOnPage =\n            this._lastKnownPointerPosition =\n                this._getPointerPositionOnPage(event));\n        this._pointerDirectionDelta = { x: 0, y: 0 };\n        this._pointerPositionAtLastDirectionChange = { x: pointerPosition.x, y: pointerPosition.y };\n        this._dragStartTime = Date.now();\n        this._dragDropRegistry.startDragging(this, event);\n    }\n    /** Cleans up the DOM artifacts that were added to facilitate the element being dragged. */\n    _cleanupDragArtifacts(event) {\n        // Restore the element's visibility and insert it at its old position in the DOM.\n        // It's important that we maintain the position, because moving the element around in the DOM\n        // can throw off `NgFor` which does smart diffing and re-creates elements only when necessary,\n        // while moving the existing elements in all other cases.\n        toggleVisibility(this._rootElement, true, dragImportantProperties);\n        this._anchor.parentNode.replaceChild(this._rootElement, this._anchor);\n        this._destroyPreview();\n        this._destroyPlaceholder();\n        this._initialClientRect =\n            this._boundaryRect =\n                this._previewRect =\n                    this._initialTransform =\n                        undefined;\n        // Re-enter the NgZone since we bound `document` events on the outside.\n        this._ngZone.run(() => {\n            const container = this._dropContainer;\n            const currentIndex = container.getItemIndex(this);\n            const pointerPosition = this._getPointerPositionOnPage(event);\n            const distance = this._getDragDistance(pointerPosition);\n            const isPointerOverContainer = container._isOverContainer(pointerPosition.x, pointerPosition.y);\n            this.ended.next({ source: this, distance, dropPoint: pointerPosition, event });\n            this.dropped.next({\n                item: this,\n                currentIndex,\n                previousIndex: this._initialIndex,\n                container: container,\n                previousContainer: this._initialContainer,\n                isPointerOverContainer,\n                distance,\n                dropPoint: pointerPosition,\n                event,\n            });\n            container.drop(this, currentIndex, this._initialIndex, this._initialContainer, isPointerOverContainer, distance, pointerPosition);\n            this._dropContainer = this._initialContainer;\n        });\n    }\n    /**\n     * Updates the item's position in its drop container, or moves it\n     * into a new one, depending on its current drag position.\n     */\n    _updateActiveDropContainer({ x, y }, { x: rawX, y: rawY }) {\n        // Drop container that draggable has been moved into.\n        let newContainer = this._initialContainer._getSiblingContainerFromPosition(this, x, y);\n        // If we couldn't find a new container to move the item into, and the item has left its\n        // initial container, check whether the it's over the initial container. This handles the\n        // case where two containers are connected one way and the user tries to undo dragging an\n        // item into a new container.\n        if (!newContainer &&\n            this._dropContainer !== this._initialContainer &&\n            this._initialContainer._isOverContainer(x, y)) {\n            newContainer = this._initialContainer;\n        }\n        if (newContainer && newContainer !== this._dropContainer) {\n            this._ngZone.run(() => {\n                // Notify the old container that the item has left.\n                this.exited.next({ item: this, container: this._dropContainer });\n                this._dropContainer.exit(this);\n                // Notify the new container that the item has entered.\n                this._dropContainer = newContainer;\n                this._dropContainer.enter(this, x, y, newContainer === this._initialContainer &&\n                    // If we're re-entering the initial container and sorting is disabled,\n                    // put item the into its starting index to begin with.\n                    newContainer.sortingDisabled\n                    ? this._initialIndex\n                    : undefined);\n                this.entered.next({\n                    item: this,\n                    container: newContainer,\n                    currentIndex: newContainer.getItemIndex(this),\n                });\n            });\n        }\n        // Dragging may have been interrupted as a result of the events above.\n        if (this.isDragging()) {\n            this._dropContainer._startScrollingIfNecessary(rawX, rawY);\n            this._dropContainer._sortItem(this, x, y, this._pointerDirectionDelta);\n            if (this.constrainPosition) {\n                this._applyPreviewTransform(x, y);\n            }\n            else {\n                this._applyPreviewTransform(x - this._pickupPositionInElement.x, y - this._pickupPositionInElement.y);\n            }\n        }\n    }\n    /**\n     * Creates the element that will be rendered next to the user's pointer\n     * and will be used as a preview of the element that is being dragged.\n     */\n    _createPreviewElement() {\n        const previewConfig = this._previewTemplate;\n        const previewClass = this.previewClass;\n        const previewTemplate = previewConfig ? previewConfig.template : null;\n        let preview;\n        if (previewTemplate && previewConfig) {\n            // Measure the element before we've inserted the preview\n            // since the insertion could throw off the measurement.\n            const rootRect = previewConfig.matchSize ? this._initialClientRect : null;\n            const viewRef = previewConfig.viewContainer.createEmbeddedView(previewTemplate, previewConfig.context);\n            viewRef.detectChanges();\n            preview = getRootNode(viewRef, this._document);\n            this._previewRef = viewRef;\n            if (previewConfig.matchSize) {\n                matchElementSize(preview, rootRect);\n            }\n            else {\n                preview.style.transform = getTransform(this._pickupPositionOnPage.x, this._pickupPositionOnPage.y);\n            }\n        }\n        else {\n            preview = deepCloneNode(this._rootElement);\n            matchElementSize(preview, this._initialClientRect);\n            if (this._initialTransform) {\n                preview.style.transform = this._initialTransform;\n            }\n        }\n        extendStyles(preview.style, {\n            // It's important that we disable the pointer events on the preview, because\n            // it can throw off the `document.elementFromPoint` calls in the `CdkDropList`.\n            'pointer-events': 'none',\n            // We have to reset the margin, because it can throw off positioning relative to the viewport.\n            'margin': '0',\n            'position': 'fixed',\n            'top': '0',\n            'left': '0',\n            'z-index': `${this._config.zIndex || 1000}`,\n        }, dragImportantProperties);\n        toggleNativeDragInteractions(preview, false);\n        preview.classList.add('cdk-drag-preview');\n        preview.setAttribute('dir', this._direction);\n        if (previewClass) {\n            if (Array.isArray(previewClass)) {\n                previewClass.forEach(className => preview.classList.add(className));\n            }\n            else {\n                preview.classList.add(previewClass);\n            }\n        }\n        return preview;\n    }\n    /**\n     * Animates the preview element from its current position to the location of the drop placeholder.\n     * @returns Promise that resolves when the animation completes.\n     */\n    _animatePreviewToPlaceholder() {\n        // If the user hasn't moved yet, the transitionend event won't fire.\n        if (!this._hasMoved) {\n            return Promise.resolve();\n        }\n        const placeholderRect = this._placeholder.getBoundingClientRect();\n        // Apply the class that adds a transition to the preview.\n        this._preview.classList.add('cdk-drag-animating');\n        // Move the preview to the placeholder position.\n        this._applyPreviewTransform(placeholderRect.left, placeholderRect.top);\n        // If the element doesn't have a `transition`, the `transitionend` event won't fire. Since\n        // we need to trigger a style recalculation in order for the `cdk-drag-animating` class to\n        // apply its style, we take advantage of the available info to figure out whether we need to\n        // bind the event in the first place.\n        const duration = getTransformTransitionDurationInMs(this._preview);\n        if (duration === 0) {\n            return Promise.resolve();\n        }\n        return this._ngZone.runOutsideAngular(() => {\n            return new Promise(resolve => {\n                const handler = ((event) => {\n                    var _a;\n                    if (!event ||\n                        (_getEventTarget(event) === this._preview && event.propertyName === 'transform')) {\n                        (_a = this._preview) === null || _a === void 0 ? void 0 : _a.removeEventListener('transitionend', handler);\n                        resolve();\n                        clearTimeout(timeout);\n                    }\n                });\n                // If a transition is short enough, the browser might not fire the `transitionend` event.\n                // Since we know how long it's supposed to take, add a timeout with a 50% buffer that'll\n                // fire if the transition hasn't completed when it was supposed to.\n                const timeout = setTimeout(handler, duration * 1.5);\n                this._preview.addEventListener('transitionend', handler);\n            });\n        });\n    }\n    /** Creates an element that will be shown instead of the current element while dragging. */\n    _createPlaceholderElement() {\n        const placeholderConfig = this._placeholderTemplate;\n        const placeholderTemplate = placeholderConfig ? placeholderConfig.template : null;\n        let placeholder;\n        if (placeholderTemplate) {\n            this._placeholderRef = placeholderConfig.viewContainer.createEmbeddedView(placeholderTemplate, placeholderConfig.context);\n            this._placeholderRef.detectChanges();\n            placeholder = getRootNode(this._placeholderRef, this._document);\n        }\n        else {\n            placeholder = deepCloneNode(this._rootElement);\n        }\n        // Stop pointer events on the preview so the user can't\n        // interact with it while the preview is animating.\n        placeholder.style.pointerEvents = 'none';\n        placeholder.classList.add('cdk-drag-placeholder');\n        return placeholder;\n    }\n    /**\n     * Figures out the coordinates at which an element was picked up.\n     * @param referenceElement Element that initiated the dragging.\n     * @param event Event that initiated the dragging.\n     */\n    _getPointerPositionInElement(elementRect, referenceElement, event) {\n        const handleElement = referenceElement === this._rootElement ? null : referenceElement;\n        const referenceRect = handleElement ? handleElement.getBoundingClientRect() : elementRect;\n        const point = isTouchEvent(event) ? event.targetTouches[0] : event;\n        const scrollPosition = this._getViewportScrollPosition();\n        const x = point.pageX - referenceRect.left - scrollPosition.left;\n        const y = point.pageY - referenceRect.top - scrollPosition.top;\n        return {\n            x: referenceRect.left - elementRect.left + x,\n            y: referenceRect.top - elementRect.top + y,\n        };\n    }\n    /** Determines the point of the page that was touched by the user. */\n    _getPointerPositionOnPage(event) {\n        const scrollPosition = this._getViewportScrollPosition();\n        const point = isTouchEvent(event)\n            ? // `touches` will be empty for start/end events so we have to fall back to `changedTouches`.\n                // Also note that on real devices we're guaranteed for either `touches` or `changedTouches`\n                // to have a value, but Firefox in device emulation mode has a bug where both can be empty\n                // for `touchstart` and `touchend` so we fall back to a dummy object in order to avoid\n                // throwing an error. The value returned here will be incorrect, but since this only\n                // breaks inside a developer tool and the value is only used for secondary information,\n                // we can get away with it. See https://bugzilla.mozilla.org/show_bug.cgi?id=1615824.\n                event.touches[0] || event.changedTouches[0] || { pageX: 0, pageY: 0 }\n            : event;\n        const x = point.pageX - scrollPosition.left;\n        const y = point.pageY - scrollPosition.top;\n        // if dragging SVG element, try to convert from the screen coordinate system to the SVG\n        // coordinate system\n        if (this._ownerSVGElement) {\n            const svgMatrix = this._ownerSVGElement.getScreenCTM();\n            if (svgMatrix) {\n                const svgPoint = this._ownerSVGElement.createSVGPoint();\n                svgPoint.x = x;\n                svgPoint.y = y;\n                return svgPoint.matrixTransform(svgMatrix.inverse());\n            }\n        }\n        return { x, y };\n    }\n    /** Gets the pointer position on the page, accounting for any position constraints. */\n    _getConstrainedPointerPosition(point) {\n        const dropContainerLock = this._dropContainer ? this._dropContainer.lockAxis : null;\n        let { x, y } = this.constrainPosition\n            ? this.constrainPosition(point, this, this._initialClientRect)\n            : point;\n        if (this.lockAxis === 'x' || dropContainerLock === 'x') {\n            y = this._pickupPositionOnPage.y;\n        }\n        else if (this.lockAxis === 'y' || dropContainerLock === 'y') {\n            x = this._pickupPositionOnPage.x;\n        }\n        if (this._boundaryRect) {\n            const { x: pickupX, y: pickupY } = this._pickupPositionInElement;\n            const boundaryRect = this._boundaryRect;\n            const { width: previewWidth, height: previewHeight } = this._getPreviewRect();\n            const minY = boundaryRect.top + pickupY;\n            const maxY = boundaryRect.bottom - (previewHeight - pickupY);\n            const minX = boundaryRect.left + pickupX;\n            const maxX = boundaryRect.right - (previewWidth - pickupX);\n            x = clamp$1(x, minX, maxX);\n            y = clamp$1(y, minY, maxY);\n        }\n        return { x, y };\n    }\n    /** Updates the current drag delta, based on the user's current pointer position on the page. */\n    _updatePointerDirectionDelta(pointerPositionOnPage) {\n        const { x, y } = pointerPositionOnPage;\n        const delta = this._pointerDirectionDelta;\n        const positionSinceLastChange = this._pointerPositionAtLastDirectionChange;\n        // Amount of pixels the user has dragged since the last time the direction changed.\n        const changeX = Math.abs(x - positionSinceLastChange.x);\n        const changeY = Math.abs(y - positionSinceLastChange.y);\n        // Because we handle pointer events on a per-pixel basis, we don't want the delta\n        // to change for every pixel, otherwise anything that depends on it can look erratic.\n        // To make the delta more consistent, we track how much the user has moved since the last\n        // delta change and we only update it after it has reached a certain threshold.\n        if (changeX > this._config.pointerDirectionChangeThreshold) {\n            delta.x = x > positionSinceLastChange.x ? 1 : -1;\n            positionSinceLastChange.x = x;\n        }\n        if (changeY > this._config.pointerDirectionChangeThreshold) {\n            delta.y = y > positionSinceLastChange.y ? 1 : -1;\n            positionSinceLastChange.y = y;\n        }\n        return delta;\n    }\n    /** Toggles the native drag interactions, based on how many handles are registered. */\n    _toggleNativeDragInteractions() {\n        if (!this._rootElement || !this._handles) {\n            return;\n        }\n        const shouldEnable = this._handles.length > 0 || !this.isDragging();\n        if (shouldEnable !== this._nativeInteractionsEnabled) {\n            this._nativeInteractionsEnabled = shouldEnable;\n            toggleNativeDragInteractions(this._rootElement, shouldEnable);\n        }\n    }\n    /** Removes the manually-added event listeners from the root element. */\n    _removeRootElementListeners(element) {\n        element.removeEventListener('mousedown', this._pointerDown, activeEventListenerOptions);\n        element.removeEventListener('touchstart', this._pointerDown, passiveEventListenerOptions);\n        element.removeEventListener('dragstart', this._nativeDragStart, activeEventListenerOptions);\n    }\n    /**\n     * Applies a `transform` to the root element, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyRootElementTransform(x, y) {\n        const transform = getTransform(x, y);\n        const styles = this._rootElement.style;\n        // Cache the previous transform amount only after the first drag sequence, because\n        // we don't want our own transforms to stack on top of each other.\n        // Should be excluded none because none + translate3d(x, y, x) is invalid css\n        if (this._initialTransform == null) {\n            this._initialTransform =\n                styles.transform && styles.transform != 'none' ? styles.transform : '';\n        }\n        // Preserve the previous `transform` value, if there was one. Note that we apply our own\n        // transform before the user's, because things like rotation can affect which direction\n        // the element will be translated towards.\n        styles.transform = combineTransforms(transform, this._initialTransform);\n    }\n    /**\n     * Applies a `transform` to the preview, taking into account any existing transforms on it.\n     * @param x New transform value along the X axis.\n     * @param y New transform value along the Y axis.\n     */\n    _applyPreviewTransform(x, y) {\n        var _a;\n        // Only apply the initial transform if the preview is a clone of the original element, otherwise\n        // it could be completely different and the transform might not make sense anymore.\n        const initialTransform = ((_a = this._previewTemplate) === null || _a === void 0 ? void 0 : _a.template) ? undefined : this._initialTransform;\n        const transform = getTransform(x, y);\n        this._preview.style.transform = combineTransforms(transform, initialTransform);\n    }\n    /**\n     * Gets the distance that the user has dragged during the current drag sequence.\n     * @param currentPosition Current position of the user's pointer.\n     */\n    _getDragDistance(currentPosition) {\n        const pickupPosition = this._pickupPositionOnPage;\n        if (pickupPosition) {\n            return { x: currentPosition.x - pickupPosition.x, y: currentPosition.y - pickupPosition.y };\n        }\n        return { x: 0, y: 0 };\n    }\n    /** Cleans up any cached element dimensions that we don't need after dragging has stopped. */\n    _cleanupCachedDimensions() {\n        this._boundaryRect = this._previewRect = undefined;\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the element is still inside its boundary after the viewport has been resized.\n     * If not, the position is adjusted so that the element fits again.\n     */\n    _containInsideBoundaryOnResize() {\n        let { x, y } = this._passiveTransform;\n        if ((x === 0 && y === 0) || this.isDragging() || !this._boundaryElement) {\n            return;\n        }\n        // Note: don't use `_clientRectAtStart` here, because we want the latest position.\n        const elementRect = this._rootElement.getBoundingClientRect();\n        const boundaryRect = this._boundaryElement.getBoundingClientRect();\n        // It's possible that the element got hidden away after dragging (e.g. by switching to a\n        // different tab). Don't do anything in this case so we don't clear the user's position.\n        if ((boundaryRect.width === 0 && boundaryRect.height === 0) ||\n            (elementRect.width === 0 && elementRect.height === 0)) {\n            return;\n        }\n        const leftOverflow = boundaryRect.left - elementRect.left;\n        const rightOverflow = elementRect.right - boundaryRect.right;\n        const topOverflow = boundaryRect.top - elementRect.top;\n        const bottomOverflow = elementRect.bottom - boundaryRect.bottom;\n        // If the element has become wider than the boundary, we can't\n        // do much to make it fit so we just anchor it to the left.\n        if (boundaryRect.width > elementRect.width) {\n            if (leftOverflow > 0) {\n                x += leftOverflow;\n            }\n            if (rightOverflow > 0) {\n                x -= rightOverflow;\n            }\n        }\n        else {\n            x = 0;\n        }\n        // If the element has become taller than the boundary, we can't\n        // do much to make it fit so we just anchor it to the top.\n        if (boundaryRect.height > elementRect.height) {\n            if (topOverflow > 0) {\n                y += topOverflow;\n            }\n            if (bottomOverflow > 0) {\n                y -= bottomOverflow;\n            }\n        }\n        else {\n            y = 0;\n        }\n        if (x !== this._passiveTransform.x || y !== this._passiveTransform.y) {\n            this.setFreeDragPosition({ y, x });\n        }\n    }\n    /** Gets the drag start delay, based on the event type. */\n    _getDragStartDelay(event) {\n        const value = this.dragStartDelay;\n        if (typeof value === 'number') {\n            return value;\n        }\n        else if (isTouchEvent(event)) {\n            return value.touch;\n        }\n        return value ? value.mouse : 0;\n    }\n    /** Updates the internal state of the draggable element when scrolling has occurred. */\n    _updateOnScroll(event) {\n        const scrollDifference = this._parentPositions.handleScroll(event);\n        if (scrollDifference) {\n            const target = _getEventTarget(event);\n            // ClientRect dimensions are based on the scroll position of the page and its parent\n            // node so we have to update the cached boundary ClientRect if the user has scrolled.\n            if (this._boundaryRect &&\n                target !== this._boundaryElement &&\n                target.contains(this._boundaryElement)) {\n                adjustClientRect(this._boundaryRect, scrollDifference.top, scrollDifference.left);\n            }\n            this._pickupPositionOnPage.x += scrollDifference.left;\n            this._pickupPositionOnPage.y += scrollDifference.top;\n            // If we're in free drag mode, we have to update the active transform, because\n            // it isn't relative to the viewport like the preview inside a drop list.\n            if (!this._dropContainer) {\n                this._activeTransform.x -= scrollDifference.left;\n                this._activeTransform.y -= scrollDifference.top;\n                this._applyRootElementTransform(this._activeTransform.x, this._activeTransform.y);\n            }\n        }\n    }\n    /** Gets the scroll position of the viewport. */\n    _getViewportScrollPosition() {\n        var _a;\n        return (((_a = this._parentPositions.positions.get(this._document)) === null || _a === void 0 ? void 0 : _a.scrollPosition) ||\n            this._parentPositions.getViewportScrollPosition());\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (this._cachedShadowRoot === undefined) {\n            this._cachedShadowRoot = _getShadowRoot(this._rootElement);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Gets the element into which the drag preview should be inserted. */\n    _getPreviewInsertionPoint(initialParent, shadowRoot) {\n        const previewContainer = this._previewContainer || 'global';\n        if (previewContainer === 'parent') {\n            return initialParent;\n        }\n        if (previewContainer === 'global') {\n            const documentRef = this._document;\n            // We can't use the body if the user is in fullscreen mode,\n            // because the preview will render under the fullscreen element.\n            // TODO(crisbeto): dedupe this with the `FullscreenOverlayContainer` eventually.\n            return (shadowRoot ||\n                documentRef.fullscreenElement ||\n                documentRef.webkitFullscreenElement ||\n                documentRef.mozFullScreenElement ||\n                documentRef.msFullscreenElement ||\n                documentRef.body);\n        }\n        return coerceElement(previewContainer);\n    }\n    /** Lazily resolves and returns the dimensions of the preview. */\n    _getPreviewRect() {\n        // Cache the preview element rect if we haven't cached it already or if\n        // we cached it too early before the element dimensions were computed.\n        if (!this._previewRect || (!this._previewRect.width && !this._previewRect.height)) {\n            this._previewRect = this._preview\n                ? this._preview.getBoundingClientRect()\n                : this._initialClientRect;\n        }\n        return this._previewRect;\n    }\n    /** Gets a handle that is the target of an event. */\n    _getTargetHandle(event) {\n        return this._handles.find(handle => {\n            return event.target && (event.target === handle || handle.contains(event.target));\n        });\n    }\n}\n/**\n * Gets a 3d `transform` that can be applied to an element.\n * @param x Desired position of the element along the X axis.\n * @param y Desired position of the element along the Y axis.\n */\nfunction getTransform(x, y) {\n    // Round the transforms since some browsers will\n    // blur the elements for sub-pixel transforms.\n    return `translate3d(${Math.round(x)}px, ${Math.round(y)}px, 0)`;\n}\n/** Clamps a value between a minimum and a maximum. */\nfunction clamp$1(value, min, max) {\n    return Math.max(min, Math.min(max, value));\n}\n/** Determines whether an event is a touch event. */\nfunction isTouchEvent(event) {\n    // This function is called for every pixel that the user has dragged so we need it to be\n    // as fast as possible. Since we only bind mouse events and touch events, we can assume\n    // that if the event's name starts with `t`, it's a touch event.\n    return event.type[0] === 't';\n}\n/**\n * Gets the root HTML element of an embedded view.\n * If the root is not an HTML element it gets wrapped in one.\n */\nfunction getRootNode(viewRef, _document) {\n    const rootNodes = viewRef.rootNodes;\n    if (rootNodes.length === 1 && rootNodes[0].nodeType === _document.ELEMENT_NODE) {\n        return rootNodes[0];\n    }\n    const wrapper = _document.createElement('div');\n    rootNodes.forEach(node => wrapper.appendChild(node));\n    return wrapper;\n}\n/**\n * Matches the target element's size to the source's size.\n * @param target Element that needs to be resized.\n * @param sourceRect Dimensions of the source element.\n */\nfunction matchElementSize(target, sourceRect) {\n    target.style.width = `${sourceRect.width}px`;\n    target.style.height = `${sourceRect.height}px`;\n    target.style.transform = getTransform(sourceRect.left, sourceRect.top);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Moves an item one index in an array to another.\n * @param array Array in which to move the item.\n * @param fromIndex Starting index of the item.\n * @param toIndex Index to which the item should be moved.\n */\nfunction moveItemInArray(array, fromIndex, toIndex) {\n    const from = clamp(fromIndex, array.length - 1);\n    const to = clamp(toIndex, array.length - 1);\n    if (from === to) {\n        return;\n    }\n    const target = array[from];\n    const delta = to < from ? -1 : 1;\n    for (let i = from; i !== to; i += delta) {\n        array[i] = array[i + delta];\n    }\n    array[to] = target;\n}\n/**\n * Moves an item from one array to another.\n * @param currentArray Array from which to transfer the item.\n * @param targetArray Array into which to put the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n */\nfunction transferArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const from = clamp(currentIndex, currentArray.length - 1);\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray.splice(from, 1)[0]);\n    }\n}\n/**\n * Copies an item from one array to another, leaving it in its\n * original position in current array.\n * @param currentArray Array from which to copy the item.\n * @param targetArray Array into which is copy the item.\n * @param currentIndex Index of the item in its current array.\n * @param targetIndex Index at which to insert the item.\n *\n */\nfunction copyArrayItem(currentArray, targetArray, currentIndex, targetIndex) {\n    const to = clamp(targetIndex, targetArray.length);\n    if (currentArray.length) {\n        targetArray.splice(to, 0, currentArray[currentIndex]);\n    }\n}\n/** Clamps a number between zero and a maximum. */\nfunction clamp(value, max) {\n    return Math.max(0, Math.min(max, value));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Strategy that only supports sorting along a single axis.\n * Items are reordered using CSS transforms which allows for sorting to be animated.\n * @docs-private\n */\nclass SingleAxisSortStrategy {\n    constructor(_element, _dragDropRegistry) {\n        this._element = _element;\n        this._dragDropRegistry = _dragDropRegistry;\n        /** Cache of the dimensions of all the items inside the container. */\n        this._itemPositions = [];\n        /** Direction in which the list is oriented. */\n        this.orientation = 'vertical';\n        /**\n         * Keeps track of the item that was last swapped with the dragged item, as well as what direction\n         * the pointer was moving in when the swap occurred and whether the user's pointer continued to\n         * overlap with the swapped item after the swapping occurred.\n         */\n        this._previousSwap = {\n            drag: null,\n            delta: 0,\n            overlaps: false,\n        };\n    }\n    /**\n     * To be called when the drag sequence starts.\n     * @param items Items that are currently in the list.\n     */\n    start(items) {\n        this.withItems(items);\n    }\n    /**\n     * To be called when an item is being sorted.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    sort(item, pointerX, pointerY, pointerDelta) {\n        const siblings = this._itemPositions;\n        const newIndex = this._getItemIndexFromPointerPosition(item, pointerX, pointerY, pointerDelta);\n        if (newIndex === -1 && siblings.length > 0) {\n            return null;\n        }\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentIndex = siblings.findIndex(currentItem => currentItem.drag === item);\n        const siblingAtNewPosition = siblings[newIndex];\n        const currentPosition = siblings[currentIndex].clientRect;\n        const newPosition = siblingAtNewPosition.clientRect;\n        const delta = currentIndex > newIndex ? 1 : -1;\n        // How many pixels the item's placeholder should be offset.\n        const itemOffset = this._getItemOffsetPx(currentPosition, newPosition, delta);\n        // How many pixels all the other items should be offset.\n        const siblingOffset = this._getSiblingOffsetPx(currentIndex, siblings, delta);\n        // Save the previous order of the items before moving the item to its new index.\n        // We use this to check whether an item has been moved as a result of the sorting.\n        const oldOrder = siblings.slice();\n        // Shuffle the array in place.\n        moveItemInArray(siblings, currentIndex, newIndex);\n        siblings.forEach((sibling, index) => {\n            // Don't do anything if the position hasn't changed.\n            if (oldOrder[index] === sibling) {\n                return;\n            }\n            const isDraggedItem = sibling.drag === item;\n            const offset = isDraggedItem ? itemOffset : siblingOffset;\n            const elementToOffset = isDraggedItem\n                ? item.getPlaceholderElement()\n                : sibling.drag.getRootElement();\n            // Update the offset to reflect the new position.\n            sibling.offset += offset;\n            // Since we're moving the items with a `transform`, we need to adjust their cached\n            // client rects to reflect their new position, as well as swap their positions in the cache.\n            // Note that we shouldn't use `getBoundingClientRect` here to update the cache, because the\n            // elements may be mid-animation which will give us a wrong result.\n            if (isHorizontal) {\n                // Round the transforms since some browsers will\n                // blur the elements, for sub-pixel transforms.\n                elementToOffset.style.transform = combineTransforms(`translate3d(${Math.round(sibling.offset)}px, 0, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, 0, offset);\n            }\n            else {\n                elementToOffset.style.transform = combineTransforms(`translate3d(0, ${Math.round(sibling.offset)}px, 0)`, sibling.initialTransform);\n                adjustClientRect(sibling.clientRect, offset, 0);\n            }\n        });\n        // Note that it's important that we do this after the client rects have been adjusted.\n        this._previousSwap.overlaps = isInsideClientRect(newPosition, pointerX, pointerY);\n        this._previousSwap.drag = siblingAtNewPosition.drag;\n        this._previousSwap.delta = isHorizontal ? pointerDelta.x : pointerDelta.y;\n        return { previousIndex: currentIndex, currentIndex: newIndex };\n    }\n    /**\n     * Called when an item is being moved into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        const newIndex = index == null || index < 0\n            ? // We use the coordinates of where the item entered the drop\n                // zone to figure out at which index it should be inserted.\n                this._getItemIndexFromPointerPosition(item, pointerX, pointerY)\n            : index;\n        const activeDraggables = this._activeDraggables;\n        const currentIndex = activeDraggables.indexOf(item);\n        const placeholder = item.getPlaceholderElement();\n        let newPositionReference = activeDraggables[newIndex];\n        // If the item at the new position is the same as the item that is being dragged,\n        // it means that we're trying to restore the item to its initial position. In this\n        // case we should use the next item from the list as the reference.\n        if (newPositionReference === item) {\n            newPositionReference = activeDraggables[newIndex + 1];\n        }\n        // If we didn't find a new position reference, it means that either the item didn't start off\n        // in this container, or that the item requested to be inserted at the end of the list.\n        if (!newPositionReference &&\n            (newIndex == null || newIndex === -1 || newIndex < activeDraggables.length - 1) &&\n            this._shouldEnterAsFirstChild(pointerX, pointerY)) {\n            newPositionReference = activeDraggables[0];\n        }\n        // Since the item may be in the `activeDraggables` already (e.g. if the user dragged it\n        // into another container and back again), we have to ensure that it isn't duplicated.\n        if (currentIndex > -1) {\n            activeDraggables.splice(currentIndex, 1);\n        }\n        // Don't use items that are being dragged as a reference, because\n        // their element has been moved down to the bottom of the body.\n        if (newPositionReference && !this._dragDropRegistry.isDragging(newPositionReference)) {\n            const element = newPositionReference.getRootElement();\n            element.parentElement.insertBefore(placeholder, element);\n            activeDraggables.splice(newIndex, 0, item);\n        }\n        else {\n            coerceElement(this._element).appendChild(placeholder);\n            activeDraggables.push(item);\n        }\n        // The transform needs to be cleared so it doesn't throw off the measurements.\n        placeholder.style.transform = '';\n        // Note that usually `start` is called together with `enter` when an item goes into a new\n        // container. This will cache item positions, but we need to refresh them since the amount\n        // of items has changed.\n        this._cacheItemPositions();\n    }\n    /** Sets the items that are currently part of the list. */\n    withItems(items) {\n        this._activeDraggables = items.slice();\n        this._cacheItemPositions();\n    }\n    /** Assigns a sort predicate to the strategy. */\n    withSortPredicate(predicate) {\n        this._sortPredicate = predicate;\n    }\n    /** Resets the strategy to its initial state before dragging was started. */\n    reset() {\n        // TODO(crisbeto): may have to wait for the animations to finish.\n        this._activeDraggables.forEach(item => {\n            var _a;\n            const rootElement = item.getRootElement();\n            if (rootElement) {\n                const initialTransform = (_a = this._itemPositions.find(p => p.drag === item)) === null || _a === void 0 ? void 0 : _a.initialTransform;\n                rootElement.style.transform = initialTransform || '';\n            }\n        });\n        this._itemPositions = [];\n        this._activeDraggables = [];\n        this._previousSwap.drag = null;\n        this._previousSwap.delta = 0;\n        this._previousSwap.overlaps = false;\n    }\n    /**\n     * Gets a snapshot of items currently in the list.\n     * Can include items that we dragged in from another list.\n     */\n    getActiveItemsSnapshot() {\n        return this._activeDraggables;\n    }\n    /** Gets the index of a specific item. */\n    getItemIndex(item) {\n        // Items are sorted always by top/left in the cache, however they flow differently in RTL.\n        // The rest of the logic still stands no matter what orientation we're in, however\n        // we need to invert the array when determining the index.\n        const items = this.orientation === 'horizontal' && this.direction === 'rtl'\n            ? this._itemPositions.slice().reverse()\n            : this._itemPositions;\n        return items.findIndex(currentItem => currentItem.drag === item);\n    }\n    /** Used to notify the strategy that the scroll position has changed. */\n    updateOnScroll(topDifference, leftDifference) {\n        // Since we know the amount that the user has scrolled we can shift all of the\n        // client rectangles ourselves. This is cheaper than re-measuring everything and\n        // we can avoid inconsistent behavior where we might be measuring the element before\n        // its position has changed.\n        this._itemPositions.forEach(({ clientRect }) => {\n            adjustClientRect(clientRect, topDifference, leftDifference);\n        });\n        // We need two loops for this, because we want all of the cached\n        // positions to be up-to-date before we re-sort the item.\n        this._itemPositions.forEach(({ drag }) => {\n            if (this._dragDropRegistry.isDragging(drag)) {\n                // We need to re-sort the item manually, because the pointer move\n                // events won't be dispatched while the user is scrolling.\n                drag._sortFromLastPointerPosition();\n            }\n        });\n    }\n    /** Refreshes the position cache of the items and sibling containers. */\n    _cacheItemPositions() {\n        const isHorizontal = this.orientation === 'horizontal';\n        this._itemPositions = this._activeDraggables\n            .map(drag => {\n            const elementToMeasure = drag.getVisibleElement();\n            return {\n                drag,\n                offset: 0,\n                initialTransform: elementToMeasure.style.transform || '',\n                clientRect: getMutableClientRect(elementToMeasure),\n            };\n        })\n            .sort((a, b) => {\n            return isHorizontal\n                ? a.clientRect.left - b.clientRect.left\n                : a.clientRect.top - b.clientRect.top;\n        });\n    }\n    /**\n     * Gets the offset in pixels by which the item that is being dragged should be moved.\n     * @param currentPosition Current position of the item.\n     * @param newPosition Position of the item where the current item should be moved.\n     * @param delta Direction in which the user is moving.\n     */\n    _getItemOffsetPx(currentPosition, newPosition, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        let itemOffset = isHorizontal\n            ? newPosition.left - currentPosition.left\n            : newPosition.top - currentPosition.top;\n        // Account for differences in the item width/height.\n        if (delta === -1) {\n            itemOffset += isHorizontal\n                ? newPosition.width - currentPosition.width\n                : newPosition.height - currentPosition.height;\n        }\n        return itemOffset;\n    }\n    /**\n     * Gets the offset in pixels by which the items that aren't being dragged should be moved.\n     * @param currentIndex Index of the item currently being dragged.\n     * @param siblings All of the items in the list.\n     * @param delta Direction in which the user is moving.\n     */\n    _getSiblingOffsetPx(currentIndex, siblings, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const currentPosition = siblings[currentIndex].clientRect;\n        const immediateSibling = siblings[currentIndex + delta * -1];\n        let siblingOffset = currentPosition[isHorizontal ? 'width' : 'height'] * delta;\n        if (immediateSibling) {\n            const start = isHorizontal ? 'left' : 'top';\n            const end = isHorizontal ? 'right' : 'bottom';\n            // Get the spacing between the start of the current item and the end of the one immediately\n            // after it in the direction in which the user is dragging, or vice versa. We add it to the\n            // offset in order to push the element to where it will be when it's inline and is influenced\n            // by the `margin` of its siblings.\n            if (delta === -1) {\n                siblingOffset -= immediateSibling.clientRect[start] - currentPosition[end];\n            }\n            else {\n                siblingOffset += currentPosition[start] - immediateSibling.clientRect[end];\n            }\n        }\n        return siblingOffset;\n    }\n    /**\n     * Checks if pointer is entering in the first position\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     */\n    _shouldEnterAsFirstChild(pointerX, pointerY) {\n        if (!this._activeDraggables.length) {\n            return false;\n        }\n        const itemPositions = this._itemPositions;\n        const isHorizontal = this.orientation === 'horizontal';\n        // `itemPositions` are sorted by position while `activeDraggables` are sorted by child index\n        // check if container is using some sort of \"reverse\" ordering (eg: flex-direction: row-reverse)\n        const reversed = itemPositions[0].drag !== this._activeDraggables[0];\n        if (reversed) {\n            const lastItemRect = itemPositions[itemPositions.length - 1].clientRect;\n            return isHorizontal ? pointerX >= lastItemRect.right : pointerY >= lastItemRect.bottom;\n        }\n        else {\n            const firstItemRect = itemPositions[0].clientRect;\n            return isHorizontal ? pointerX <= firstItemRect.left : pointerY <= firstItemRect.top;\n        }\n    }\n    /**\n     * Gets the index of an item in the drop container, based on the position of the user's pointer.\n     * @param item Item that is being sorted.\n     * @param pointerX Position of the user's pointer along the X axis.\n     * @param pointerY Position of the user's pointer along the Y axis.\n     * @param delta Direction in which the user is moving their pointer.\n     */\n    _getItemIndexFromPointerPosition(item, pointerX, pointerY, delta) {\n        const isHorizontal = this.orientation === 'horizontal';\n        const index = this._itemPositions.findIndex(({ drag, clientRect }) => {\n            // Skip the item itself.\n            if (drag === item) {\n                return false;\n            }\n            if (delta) {\n                const direction = isHorizontal ? delta.x : delta.y;\n                // If the user is still hovering over the same item as last time, their cursor hasn't left\n                // the item after we made the swap, and they didn't change the direction in which they're\n                // dragging, we don't consider it a direction swap.\n                if (drag === this._previousSwap.drag &&\n                    this._previousSwap.overlaps &&\n                    direction === this._previousSwap.delta) {\n                    return false;\n                }\n            }\n            return isHorizontal\n                ? // Round these down since most browsers report client rects with\n                    // sub-pixel precision, whereas the pointer coordinates are rounded to pixels.\n                    pointerX >= Math.floor(clientRect.left) && pointerX < Math.floor(clientRect.right)\n                : pointerY >= Math.floor(clientRect.top) && pointerY < Math.floor(clientRect.bottom);\n        });\n        return index === -1 || !this._sortPredicate(index, item) ? -1 : index;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Proximity, as a ratio to width/height, at which a\n * dragged item will affect the drop container.\n */\nconst DROP_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Proximity, as a ratio to width/height at which to start auto-scrolling the drop list or the\n * viewport. The value comes from trying it out manually until it feels right.\n */\nconst SCROLL_PROXIMITY_THRESHOLD = 0.05;\n/**\n * Reference to a drop list. Used to manipulate or dispose of the container.\n */\nclass DropListRef {\n    constructor(element, _dragDropRegistry, _document, _ngZone, _viewportRuler) {\n        this._dragDropRegistry = _dragDropRegistry;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        /** Whether starting a dragging sequence from this container is disabled. */\n        this.disabled = false;\n        /** Whether sorting items within the list is disabled. */\n        this.sortingDisabled = false;\n        /**\n         * Whether auto-scrolling the view when the user\n         * moves their pointer close to the edges is disabled.\n         */\n        this.autoScrollDisabled = false;\n        /** Number of pixels to scroll for each frame when auto-scrolling an element. */\n        this.autoScrollStep = 2;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Function that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits right before dragging has started. */\n        this.beforeStarted = new Subject();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new Subject();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new Subject();\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new Subject();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new Subject();\n        /** Whether an item in the list is being dragged. */\n        this._isDragging = false;\n        /** Draggable items in the container. */\n        this._draggables = [];\n        /** Drop lists that are connected to the current one. */\n        this._siblings = [];\n        /** Connected siblings that currently have a dragged item. */\n        this._activeSiblings = new Set();\n        /** Subscription to the window being scrolled. */\n        this._viewportScrollSubscription = Subscription.EMPTY;\n        /** Vertical direction in which the list is currently scrolling. */\n        this._verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        /** Horizontal direction in which the list is currently scrolling. */\n        this._horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        /** Used to signal to the current auto-scroll sequence when to stop. */\n        this._stopScrollTimers = new Subject();\n        /** Shadow root of the current element. Necessary for `elementFromPoint` to resolve correctly. */\n        this._cachedShadowRoot = null;\n        /** Starts the interval that'll auto-scroll the element. */\n        this._startScrollInterval = () => {\n            this._stopScrolling();\n            interval(0, animationFrameScheduler)\n                .pipe(takeUntil(this._stopScrollTimers))\n                .subscribe(() => {\n                const node = this._scrollNode;\n                const scrollStep = this.autoScrollStep;\n                if (this._verticalScrollDirection === 1 /* AutoScrollVerticalDirection.UP */) {\n                    node.scrollBy(0, -scrollStep);\n                }\n                else if (this._verticalScrollDirection === 2 /* AutoScrollVerticalDirection.DOWN */) {\n                    node.scrollBy(0, scrollStep);\n                }\n                if (this._horizontalScrollDirection === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n                    node.scrollBy(-scrollStep, 0);\n                }\n                else if (this._horizontalScrollDirection === 2 /* AutoScrollHorizontalDirection.RIGHT */) {\n                    node.scrollBy(scrollStep, 0);\n                }\n            });\n        };\n        this.element = coerceElement(element);\n        this._document = _document;\n        this.withScrollableParents([this.element]);\n        _dragDropRegistry.registerDropContainer(this);\n        this._parentPositions = new ParentPositionTracker(_document);\n        this._sortStrategy = new SingleAxisSortStrategy(this.element, _dragDropRegistry);\n        this._sortStrategy.withSortPredicate((index, item) => this.sortPredicate(index, item, this));\n    }\n    /** Removes the drop list functionality from the DOM element. */\n    dispose() {\n        this._stopScrolling();\n        this._stopScrollTimers.complete();\n        this._viewportScrollSubscription.unsubscribe();\n        this.beforeStarted.complete();\n        this.entered.complete();\n        this.exited.complete();\n        this.dropped.complete();\n        this.sorted.complete();\n        this._activeSiblings.clear();\n        this._scrollNode = null;\n        this._parentPositions.clear();\n        this._dragDropRegistry.removeDropContainer(this);\n    }\n    /** Whether an item from this list is currently being dragged. */\n    isDragging() {\n        return this._isDragging;\n    }\n    /** Starts dragging an item. */\n    start() {\n        this._draggingStarted();\n        this._notifyReceivingSiblings();\n    }\n    /**\n     * Attempts to move an item into the container.\n     * @param item Item that was moved into the container.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param index Index at which the item entered. If omitted, the container will try to figure it\n     *   out automatically.\n     */\n    enter(item, pointerX, pointerY, index) {\n        this._draggingStarted();\n        // If sorting is disabled, we want the item to return to its starting\n        // position if the user is returning it to its initial container.\n        if (index == null && this.sortingDisabled) {\n            index = this._draggables.indexOf(item);\n        }\n        this._sortStrategy.enter(item, pointerX, pointerY, index);\n        // Note that this usually happens inside `_draggingStarted` as well, but the dimensions\n        // can change when the sort strategy moves the item around inside `enter`.\n        this._cacheParentPositions();\n        // Notify siblings at the end so that the item has been inserted into the `activeDraggables`.\n        this._notifyReceivingSiblings();\n        this.entered.next({ item, container: this, currentIndex: this.getItemIndex(item) });\n    }\n    /**\n     * Removes an item from the container after it was dragged into another container by the user.\n     * @param item Item that was dragged out.\n     */\n    exit(item) {\n        this._reset();\n        this.exited.next({ item, container: this });\n    }\n    /**\n     * Drops an item into this container.\n     * @param item Item being dropped into the container.\n     * @param currentIndex Index at which the item should be inserted.\n     * @param previousIndex Index of the item when dragging started.\n     * @param previousContainer Container from which the item got dragged in.\n     * @param isPointerOverContainer Whether the user's pointer was over the\n     *    container when the item was dropped.\n     * @param distance Distance the user has dragged since the start of the dragging sequence.\n     * @param event Event that triggered the dropping sequence.\n     *\n     * @breaking-change 15.0.0 `previousIndex` and `event` parameters to become required.\n     */\n    drop(item, currentIndex, previousIndex, previousContainer, isPointerOverContainer, distance, dropPoint, event = {}) {\n        this._reset();\n        this.dropped.next({\n            item,\n            currentIndex,\n            previousIndex,\n            container: this,\n            previousContainer,\n            isPointerOverContainer,\n            distance,\n            dropPoint,\n            event,\n        });\n    }\n    /**\n     * Sets the draggable items that are a part of this list.\n     * @param items Items that are a part of this list.\n     */\n    withItems(items) {\n        const previousItems = this._draggables;\n        this._draggables = items;\n        items.forEach(item => item._withDropContainer(this));\n        if (this.isDragging()) {\n            const draggedItems = previousItems.filter(item => item.isDragging());\n            // If all of the items being dragged were removed\n            // from the list, abort the current drag sequence.\n            if (draggedItems.every(item => items.indexOf(item) === -1)) {\n                this._reset();\n            }\n            else {\n                this._sortStrategy.withItems(this._draggables);\n            }\n        }\n        return this;\n    }\n    /** Sets the layout direction of the drop list. */\n    withDirection(direction) {\n        this._sortStrategy.direction = direction;\n        return this;\n    }\n    /**\n     * Sets the containers that are connected to this one. When two or more containers are\n     * connected, the user will be allowed to transfer items between them.\n     * @param connectedTo Other containers that the current containers should be connected to.\n     */\n    connectedTo(connectedTo) {\n        this._siblings = connectedTo.slice();\n        return this;\n    }\n    /**\n     * Sets the orientation of the container.\n     * @param orientation New orientation for the container.\n     */\n    withOrientation(orientation) {\n        // TODO(crisbeto): eventually we should be constructing the new sort strategy here based on\n        // the new orientation. For now we can assume that it'll always be `SingleAxisSortStrategy`.\n        this._sortStrategy.orientation = orientation;\n        return this;\n    }\n    /**\n     * Sets which parent elements are can be scrolled while the user is dragging.\n     * @param elements Elements that can be scrolled.\n     */\n    withScrollableParents(elements) {\n        const element = coerceElement(this.element);\n        // We always allow the current element to be scrollable\n        // so we need to ensure that it's in the array.\n        this._scrollableElements =\n            elements.indexOf(element) === -1 ? [element, ...elements] : elements.slice();\n        return this;\n    }\n    /** Gets the scrollable parents that are registered with this drop container. */\n    getScrollableParents() {\n        return this._scrollableElements;\n    }\n    /**\n     * Figures out the index of an item in the container.\n     * @param item Item whose index should be determined.\n     */\n    getItemIndex(item) {\n        return this._isDragging\n            ? this._sortStrategy.getItemIndex(item)\n            : this._draggables.indexOf(item);\n    }\n    /**\n     * Whether the list is able to receive the item that\n     * is currently being dragged inside a connected drop list.\n     */\n    isReceiving() {\n        return this._activeSiblings.size > 0;\n    }\n    /**\n     * Sorts an item inside the container based on its position.\n     * @param item Item to be sorted.\n     * @param pointerX Position of the item along the X axis.\n     * @param pointerY Position of the item along the Y axis.\n     * @param pointerDelta Direction in which the pointer is moving along each axis.\n     */\n    _sortItem(item, pointerX, pointerY, pointerDelta) {\n        // Don't sort the item if sorting is disabled or it's out of range.\n        if (this.sortingDisabled ||\n            !this._clientRect ||\n            !isPointerNearClientRect(this._clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n            return;\n        }\n        const result = this._sortStrategy.sort(item, pointerX, pointerY, pointerDelta);\n        if (result) {\n            this.sorted.next({\n                previousIndex: result.previousIndex,\n                currentIndex: result.currentIndex,\n                container: this,\n                item,\n            });\n        }\n    }\n    /**\n     * Checks whether the user's pointer is close to the edges of either the\n     * viewport or the drop list and starts the auto-scroll sequence.\n     * @param pointerX User's pointer position along the x axis.\n     * @param pointerY User's pointer position along the y axis.\n     */\n    _startScrollingIfNecessary(pointerX, pointerY) {\n        if (this.autoScrollDisabled) {\n            return;\n        }\n        let scrollNode;\n        let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n        let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n        // Check whether we should start scrolling any of the parent containers.\n        this._parentPositions.positions.forEach((position, element) => {\n            // We have special handling for the `document` below. Also this would be\n            // nicer with a  for...of loop, but it requires changing a compiler flag.\n            if (element === this._document || !position.clientRect || scrollNode) {\n                return;\n            }\n            if (isPointerNearClientRect(position.clientRect, DROP_PROXIMITY_THRESHOLD, pointerX, pointerY)) {\n                [verticalScrollDirection, horizontalScrollDirection] = getElementScrollDirections(element, position.clientRect, pointerX, pointerY);\n                if (verticalScrollDirection || horizontalScrollDirection) {\n                    scrollNode = element;\n                }\n            }\n        });\n        // Otherwise check if we can start scrolling the viewport.\n        if (!verticalScrollDirection && !horizontalScrollDirection) {\n            const { width, height } = this._viewportRuler.getViewportSize();\n            const clientRect = {\n                width,\n                height,\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0,\n            };\n            verticalScrollDirection = getVerticalScrollDirection(clientRect, pointerY);\n            horizontalScrollDirection = getHorizontalScrollDirection(clientRect, pointerX);\n            scrollNode = window;\n        }\n        if (scrollNode &&\n            (verticalScrollDirection !== this._verticalScrollDirection ||\n                horizontalScrollDirection !== this._horizontalScrollDirection ||\n                scrollNode !== this._scrollNode)) {\n            this._verticalScrollDirection = verticalScrollDirection;\n            this._horizontalScrollDirection = horizontalScrollDirection;\n            this._scrollNode = scrollNode;\n            if ((verticalScrollDirection || horizontalScrollDirection) && scrollNode) {\n                this._ngZone.runOutsideAngular(this._startScrollInterval);\n            }\n            else {\n                this._stopScrolling();\n            }\n        }\n    }\n    /** Stops any currently-running auto-scroll sequences. */\n    _stopScrolling() {\n        this._stopScrollTimers.next();\n    }\n    /** Starts the dragging sequence within the list. */\n    _draggingStarted() {\n        const styles = coerceElement(this.element).style;\n        this.beforeStarted.next();\n        this._isDragging = true;\n        // We need to disable scroll snapping while the user is dragging, because it breaks automatic\n        // scrolling. The browser seems to round the value based on the snapping points which means\n        // that we can't increment/decrement the scroll position.\n        this._initialScrollSnap = styles.msScrollSnapType || styles.scrollSnapType || '';\n        styles.scrollSnapType = styles.msScrollSnapType = 'none';\n        this._sortStrategy.start(this._draggables);\n        this._cacheParentPositions();\n        this._viewportScrollSubscription.unsubscribe();\n        this._listenToScrollEvents();\n    }\n    /** Caches the positions of the configured scrollable parents. */\n    _cacheParentPositions() {\n        const element = coerceElement(this.element);\n        this._parentPositions.cache(this._scrollableElements);\n        // The list element is always in the `scrollableElements`\n        // so we can take advantage of the cached `ClientRect`.\n        this._clientRect = this._parentPositions.positions.get(element).clientRect;\n    }\n    /** Resets the container to its initial state. */\n    _reset() {\n        this._isDragging = false;\n        const styles = coerceElement(this.element).style;\n        styles.scrollSnapType = styles.msScrollSnapType = this._initialScrollSnap;\n        this._siblings.forEach(sibling => sibling._stopReceiving(this));\n        this._sortStrategy.reset();\n        this._stopScrolling();\n        this._viewportScrollSubscription.unsubscribe();\n        this._parentPositions.clear();\n    }\n    /**\n     * Checks whether the user's pointer is positioned over the container.\n     * @param x Pointer position along the X axis.\n     * @param y Pointer position along the Y axis.\n     */\n    _isOverContainer(x, y) {\n        return this._clientRect != null && isInsideClientRect(this._clientRect, x, y);\n    }\n    /**\n     * Figures out whether an item should be moved into a sibling\n     * drop container, based on its current position.\n     * @param item Drag item that is being moved.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _getSiblingContainerFromPosition(item, x, y) {\n        return this._siblings.find(sibling => sibling._canReceive(item, x, y));\n    }\n    /**\n     * Checks whether the drop list can receive the passed-in item.\n     * @param item Item that is being dragged into the list.\n     * @param x Position of the item along the X axis.\n     * @param y Position of the item along the Y axis.\n     */\n    _canReceive(item, x, y) {\n        if (!this._clientRect ||\n            !isInsideClientRect(this._clientRect, x, y) ||\n            !this.enterPredicate(item, this)) {\n            return false;\n        }\n        const elementFromPoint = this._getShadowRoot().elementFromPoint(x, y);\n        // If there's no element at the pointer position, then\n        // the client rect is probably scrolled out of the view.\n        if (!elementFromPoint) {\n            return false;\n        }\n        const nativeElement = coerceElement(this.element);\n        // The `ClientRect`, that we're using to find the container over which the user is\n        // hovering, doesn't give us any information on whether the element has been scrolled\n        // out of the view or whether it's overlapping with other containers. This means that\n        // we could end up transferring the item into a container that's invisible or is positioned\n        // below another one. We use the result from `elementFromPoint` to get the top-most element\n        // at the pointer position and to find whether it's one of the intersecting drop containers.\n        return elementFromPoint === nativeElement || nativeElement.contains(elementFromPoint);\n    }\n    /**\n     * Called by one of the connected drop lists when a dragging sequence has started.\n     * @param sibling Sibling in which dragging has started.\n     */\n    _startReceiving(sibling, items) {\n        const activeSiblings = this._activeSiblings;\n        if (!activeSiblings.has(sibling) &&\n            items.every(item => {\n                // Note that we have to add an exception to the `enterPredicate` for items that started off\n                // in this drop list. The drag ref has logic that allows an item to return to its initial\n                // container, if it has left the initial container and none of the connected containers\n                // allow it to enter. See `DragRef._updateActiveDropContainer` for more context.\n                return this.enterPredicate(item, this) || this._draggables.indexOf(item) > -1;\n            })) {\n            activeSiblings.add(sibling);\n            this._cacheParentPositions();\n            this._listenToScrollEvents();\n        }\n    }\n    /**\n     * Called by a connected drop list when dragging has stopped.\n     * @param sibling Sibling whose dragging has stopped.\n     */\n    _stopReceiving(sibling) {\n        this._activeSiblings.delete(sibling);\n        this._viewportScrollSubscription.unsubscribe();\n    }\n    /**\n     * Starts listening to scroll events on the viewport.\n     * Used for updating the internal state of the list.\n     */\n    _listenToScrollEvents() {\n        this._viewportScrollSubscription = this._dragDropRegistry\n            .scrolled(this._getShadowRoot())\n            .subscribe(event => {\n            if (this.isDragging()) {\n                const scrollDifference = this._parentPositions.handleScroll(event);\n                if (scrollDifference) {\n                    this._sortStrategy.updateOnScroll(scrollDifference.top, scrollDifference.left);\n                }\n            }\n            else if (this.isReceiving()) {\n                this._cacheParentPositions();\n            }\n        });\n    }\n    /**\n     * Lazily resolves and returns the shadow root of the element. We do this in a function, rather\n     * than saving it in property directly on init, because we want to resolve it as late as possible\n     * in order to ensure that the element has been moved into the shadow DOM. Doing it inside the\n     * constructor might be too early if the element is inside of something like `ngFor` or `ngIf`.\n     */\n    _getShadowRoot() {\n        if (!this._cachedShadowRoot) {\n            const shadowRoot = _getShadowRoot(coerceElement(this.element));\n            this._cachedShadowRoot = (shadowRoot || this._document);\n        }\n        return this._cachedShadowRoot;\n    }\n    /** Notifies any siblings that may potentially receive the item. */\n    _notifyReceivingSiblings() {\n        const draggedItems = this._sortStrategy\n            .getActiveItemsSnapshot()\n            .filter(item => item.isDragging());\n        this._siblings.forEach(sibling => sibling._startReceiving(this, draggedItems));\n    }\n}\n/**\n * Gets whether the vertical auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getVerticalScrollDirection(clientRect, pointerY) {\n    const { top, bottom, height } = clientRect;\n    const yThreshold = height * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerY >= top - yThreshold && pointerY <= top + yThreshold) {\n        return 1 /* AutoScrollVerticalDirection.UP */;\n    }\n    else if (pointerY >= bottom - yThreshold && pointerY <= bottom + yThreshold) {\n        return 2 /* AutoScrollVerticalDirection.DOWN */;\n    }\n    return 0 /* AutoScrollVerticalDirection.NONE */;\n}\n/**\n * Gets whether the horizontal auto-scroll direction of a node.\n * @param clientRect Dimensions of the node.\n * @param pointerX Position of the user's pointer along the x axis.\n */\nfunction getHorizontalScrollDirection(clientRect, pointerX) {\n    const { left, right, width } = clientRect;\n    const xThreshold = width * SCROLL_PROXIMITY_THRESHOLD;\n    if (pointerX >= left - xThreshold && pointerX <= left + xThreshold) {\n        return 1 /* AutoScrollHorizontalDirection.LEFT */;\n    }\n    else if (pointerX >= right - xThreshold && pointerX <= right + xThreshold) {\n        return 2 /* AutoScrollHorizontalDirection.RIGHT */;\n    }\n    return 0 /* AutoScrollHorizontalDirection.NONE */;\n}\n/**\n * Gets the directions in which an element node should be scrolled,\n * assuming that the user's pointer is already within it scrollable region.\n * @param element Element for which we should calculate the scroll direction.\n * @param clientRect Bounding client rectangle of the element.\n * @param pointerX Position of the user's pointer along the x axis.\n * @param pointerY Position of the user's pointer along the y axis.\n */\nfunction getElementScrollDirections(element, clientRect, pointerX, pointerY) {\n    const computedVertical = getVerticalScrollDirection(clientRect, pointerY);\n    const computedHorizontal = getHorizontalScrollDirection(clientRect, pointerX);\n    let verticalScrollDirection = 0 /* AutoScrollVerticalDirection.NONE */;\n    let horizontalScrollDirection = 0 /* AutoScrollHorizontalDirection.NONE */;\n    // Note that we here we do some extra checks for whether the element is actually scrollable in\n    // a certain direction and we only assign the scroll direction if it is. We do this so that we\n    // can allow other elements to be scrolled, if the current element can't be scrolled anymore.\n    // This allows us to handle cases where the scroll regions of two scrollable elements overlap.\n    if (computedVertical) {\n        const scrollTop = element.scrollTop;\n        if (computedVertical === 1 /* AutoScrollVerticalDirection.UP */) {\n            if (scrollTop > 0) {\n                verticalScrollDirection = 1 /* AutoScrollVerticalDirection.UP */;\n            }\n        }\n        else if (element.scrollHeight - scrollTop > element.clientHeight) {\n            verticalScrollDirection = 2 /* AutoScrollVerticalDirection.DOWN */;\n        }\n    }\n    if (computedHorizontal) {\n        const scrollLeft = element.scrollLeft;\n        if (computedHorizontal === 1 /* AutoScrollHorizontalDirection.LEFT */) {\n            if (scrollLeft > 0) {\n                horizontalScrollDirection = 1 /* AutoScrollHorizontalDirection.LEFT */;\n            }\n        }\n        else if (element.scrollWidth - scrollLeft > element.clientWidth) {\n            horizontalScrollDirection = 2 /* AutoScrollHorizontalDirection.RIGHT */;\n        }\n    }\n    return [verticalScrollDirection, horizontalScrollDirection];\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Event options that can be used to bind an active, capturing event. */\nconst activeCapturingEventOptions = normalizePassiveListenerOptions({\n    passive: false,\n    capture: true,\n});\n/**\n * Service that keeps track of all the drag item and drop container\n * instances, and manages global event listeners on the `document`.\n * @docs-private\n */\n// Note: this class is generic, rather than referencing CdkDrag and CdkDropList directly, in order\n// to avoid circular imports. If we were to reference them here, importing the registry into the\n// classes that are registering themselves will introduce a circular import.\nclass DragDropRegistry {\n    constructor(_ngZone, _document) {\n        this._ngZone = _ngZone;\n        /** Registered drop container instances. */\n        this._dropInstances = new Set();\n        /** Registered drag item instances. */\n        this._dragInstances = new Set();\n        /** Drag item instances that are currently being dragged. */\n        this._activeDragInstances = [];\n        /** Keeps track of the event listeners that we've bound to the `document`. */\n        this._globalListeners = new Map();\n        /**\n         * Predicate function to check if an item is being dragged.  Moved out into a property,\n         * because it'll be called a lot and we don't want to create a new function every time.\n         */\n        this._draggingPredicate = (item) => item.isDragging();\n        /**\n         * Emits the `touchmove` or `mousemove` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerMove = new Subject();\n        /**\n         * Emits the `touchend` or `mouseup` events that are dispatched\n         * while the user is dragging a drag item instance.\n         */\n        this.pointerUp = new Subject();\n        /**\n         * Emits when the viewport has been scrolled while the user is dragging an item.\n         * @deprecated To be turned into a private member. Use the `scrolled` method instead.\n         * @breaking-change 13.0.0\n         */\n        this.scroll = new Subject();\n        /**\n         * Event listener that will prevent the default browser action while the user is dragging.\n         * @param event Event whose default action should be prevented.\n         */\n        this._preventDefaultWhileDragging = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                event.preventDefault();\n            }\n        };\n        /** Event listener for `touchmove` that is bound even if no dragging is happening. */\n        this._persistentTouchmoveListener = (event) => {\n            if (this._activeDragInstances.length > 0) {\n                // Note that we only want to prevent the default action after dragging has actually started.\n                // Usually this is the same time at which the item is added to the `_activeDragInstances`,\n                // but it could be pushed back if the user has set up a drag delay or threshold.\n                if (this._activeDragInstances.some(this._draggingPredicate)) {\n                    event.preventDefault();\n                }\n                this.pointerMove.next(event);\n            }\n        };\n        this._document = _document;\n    }\n    /** Adds a drop container to the registry. */\n    registerDropContainer(drop) {\n        if (!this._dropInstances.has(drop)) {\n            this._dropInstances.add(drop);\n        }\n    }\n    /** Adds a drag item instance to the registry. */\n    registerDragItem(drag) {\n        this._dragInstances.add(drag);\n        // The `touchmove` event gets bound once, ahead of time, because WebKit\n        // won't preventDefault on a dynamically-added `touchmove` listener.\n        // See https://bugs.webkit.org/show_bug.cgi?id=184250.\n        if (this._dragInstances.size === 1) {\n            this._ngZone.runOutsideAngular(() => {\n                // The event handler has to be explicitly active,\n                // because newer browsers make it passive by default.\n                this._document.addEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n            });\n        }\n    }\n    /** Removes a drop container from the registry. */\n    removeDropContainer(drop) {\n        this._dropInstances.delete(drop);\n    }\n    /** Removes a drag item instance from the registry. */\n    removeDragItem(drag) {\n        this._dragInstances.delete(drag);\n        this.stopDragging(drag);\n        if (this._dragInstances.size === 0) {\n            this._document.removeEventListener('touchmove', this._persistentTouchmoveListener, activeCapturingEventOptions);\n        }\n    }\n    /**\n     * Starts the dragging sequence for a drag instance.\n     * @param drag Drag instance which is being dragged.\n     * @param event Event that initiated the dragging.\n     */\n    startDragging(drag, event) {\n        // Do not process the same drag twice to avoid memory leaks and redundant listeners\n        if (this._activeDragInstances.indexOf(drag) > -1) {\n            return;\n        }\n        this._activeDragInstances.push(drag);\n        if (this._activeDragInstances.length === 1) {\n            const isTouchEvent = event.type.startsWith('touch');\n            // We explicitly bind __active__ listeners here, because newer browsers will default to\n            // passive ones for `mousemove` and `touchmove`. The events need to be active, because we\n            // use `preventDefault` to prevent the page from scrolling while the user is dragging.\n            this._globalListeners\n                .set(isTouchEvent ? 'touchend' : 'mouseup', {\n                handler: (e) => this.pointerUp.next(e),\n                options: true,\n            })\n                .set('scroll', {\n                handler: (e) => this.scroll.next(e),\n                // Use capturing so that we pick up scroll changes in any scrollable nodes that aren't\n                // the document. See https://github.com/angular/components/issues/17144.\n                options: true,\n            })\n                // Preventing the default action on `mousemove` isn't enough to disable text selection\n                // on Safari so we need to prevent the selection event as well. Alternatively this can\n                // be done by setting `user-select: none` on the `body`, however it has causes a style\n                // recalculation which can be expensive on pages with a lot of elements.\n                .set('selectstart', {\n                handler: this._preventDefaultWhileDragging,\n                options: activeCapturingEventOptions,\n            });\n            // We don't have to bind a move event for touch drag sequences, because\n            // we already have a persistent global one bound from `registerDragItem`.\n            if (!isTouchEvent) {\n                this._globalListeners.set('mousemove', {\n                    handler: (e) => this.pointerMove.next(e),\n                    options: activeCapturingEventOptions,\n                });\n            }\n            this._ngZone.runOutsideAngular(() => {\n                this._globalListeners.forEach((config, name) => {\n                    this._document.addEventListener(name, config.handler, config.options);\n                });\n            });\n        }\n    }\n    /** Stops dragging a drag item instance. */\n    stopDragging(drag) {\n        const index = this._activeDragInstances.indexOf(drag);\n        if (index > -1) {\n            this._activeDragInstances.splice(index, 1);\n            if (this._activeDragInstances.length === 0) {\n                this._clearGlobalListeners();\n            }\n        }\n    }\n    /** Gets whether a drag item instance is currently being dragged. */\n    isDragging(drag) {\n        return this._activeDragInstances.indexOf(drag) > -1;\n    }\n    /**\n     * Gets a stream that will emit when any element on the page is scrolled while an item is being\n     * dragged.\n     * @param shadowRoot Optional shadow root that the current dragging sequence started from.\n     *   Top-level listeners won't pick up events coming from the shadow DOM so this parameter can\n     *   be used to include an additional top-level listener at the shadow root level.\n     */\n    scrolled(shadowRoot) {\n        const streams = [this.scroll];\n        if (shadowRoot && shadowRoot !== this._document) {\n            // Note that this is basically the same as `fromEvent` from rxjs, but we do it ourselves,\n            // because we want to guarantee that the event is bound outside of the `NgZone`. With\n            // `fromEvent` it'll only happen if the subscription is outside the `NgZone`.\n            streams.push(new Observable((observer) => {\n                return this._ngZone.runOutsideAngular(() => {\n                    const eventOptions = true;\n                    const callback = (event) => {\n                        if (this._activeDragInstances.length) {\n                            observer.next(event);\n                        }\n                    };\n                    shadowRoot.addEventListener('scroll', callback, eventOptions);\n                    return () => {\n                        shadowRoot.removeEventListener('scroll', callback, eventOptions);\n                    };\n                });\n            }));\n        }\n        return merge(...streams);\n    }\n    ngOnDestroy() {\n        this._dragInstances.forEach(instance => this.removeDragItem(instance));\n        this._dropInstances.forEach(instance => this.removeDropContainer(instance));\n        this._clearGlobalListeners();\n        this.pointerMove.complete();\n        this.pointerUp.complete();\n    }\n    /** Clears out the global event listeners from the `document`. */\n    _clearGlobalListeners() {\n        this._globalListeners.forEach((config, name) => {\n            this._document.removeEventListener(name, config.handler, config.options);\n        });\n        this._globalListeners.clear();\n    }\n}\nDragDropRegistry.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropRegistry, deps: [{ token: i0.NgZone }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable });\nDragDropRegistry.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropRegistry, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () {\n        return [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Default configuration to be used when creating a `DragRef`. */\nconst DEFAULT_CONFIG = {\n    dragStartThreshold: 5,\n    pointerDirectionChangeThreshold: 5,\n};\n/**\n * Service that allows for drag-and-drop functionality to be attached to DOM elements.\n */\nclass DragDrop {\n    constructor(_document, _ngZone, _viewportRuler, _dragDropRegistry) {\n        this._document = _document;\n        this._ngZone = _ngZone;\n        this._viewportRuler = _viewportRuler;\n        this._dragDropRegistry = _dragDropRegistry;\n    }\n    /**\n     * Turns an element into a draggable item.\n     * @param element Element to which to attach the dragging functionality.\n     * @param config Object used to configure the dragging behavior.\n     */\n    createDrag(element, config = DEFAULT_CONFIG) {\n        return new DragRef(element, config, this._document, this._ngZone, this._viewportRuler, this._dragDropRegistry);\n    }\n    /**\n     * Turns an element into a drop list.\n     * @param element Element to which to attach the drop list functionality.\n     */\n    createDropList(element) {\n        return new DropListRef(element, this._dragDropRegistry, this._document, this._ngZone, this._viewportRuler);\n    }\n}\nDragDrop.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDrop, deps: [{ token: DOCUMENT }, { token: i0.NgZone }, { token: i1.ViewportRuler }, { token: DragDropRegistry }], target: i0.ɵɵFactoryTarget.Injectable });\nDragDrop.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDrop, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDrop, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () {\n        return [{ type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }, { type: i0.NgZone }, { type: i1.ViewportRuler }, { type: DragDropRegistry }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used for a `CdkDrag` to provide itself as a parent to the\n * drag-specific child directive (`CdkDragHandle`, `CdkDragPreview` etc.). Used primarily\n * to avoid circular imports.\n * @docs-private\n */\nconst CDK_DRAG_PARENT = new InjectionToken('CDK_DRAG_PARENT');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDropListGroup`. It serves as\n * alternative token to the actual `CdkDropListGroup` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST_GROUP = new InjectionToken('CdkDropListGroup');\n/**\n * Declaratively connects sibling `cdkDropList` instances together. All of the `cdkDropList`\n * elements that are placed inside a `cdkDropListGroup` will be connected to each other\n * automatically. Can be used as an alternative to the `cdkDropListConnectedTo` input\n * from `cdkDropList`.\n */\nclass CdkDropListGroup {\n    constructor() {\n        /** Drop lists registered inside the group. */\n        this._items = new Set();\n        this._disabled = false;\n    }\n    /** Whether starting a dragging sequence from inside this group is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n    }\n    ngOnDestroy() {\n        this._items.clear();\n    }\n}\nCdkDropListGroup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDropListGroup, deps: [], target: i0.ɵɵFactoryTarget.Directive });\nCdkDropListGroup.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDropListGroup, selector: \"[cdkDropListGroup]\", inputs: { disabled: [\"cdkDropListGroupDisabled\", \"disabled\"] }, providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }], exportAs: [\"cdkDropListGroup\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDropListGroup, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropListGroup]',\n                    exportAs: 'cdkDropListGroup',\n                    providers: [{ provide: CDK_DROP_LIST_GROUP, useExisting: CdkDropListGroup }],\n                }]\n        }], propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDropListGroupDisabled']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to configure the\n * behavior of the drag&drop-related components.\n */\nconst CDK_DRAG_CONFIG = new InjectionToken('CDK_DRAG_CONFIG');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Asserts that a particular node is an element.\n * @param node Node to be checked.\n * @param name Name to attach to the error message.\n */\nfunction assertElementNode(node, name) {\n    if (node.nodeType !== 1) {\n        throw Error(`${name} must be attached to an element node. ` + `Currently attached to \"${node.nodeName}\".`);\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Counter used to generate unique ids for drop zones. */\nlet _uniqueIdCounter = 0;\n/**\n * Injection token that can be used to reference instances of `CdkDropList`. It serves as\n * alternative token to the actual `CdkDropList` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DROP_LIST = new InjectionToken('CdkDropList');\n/** Container that wraps a set of draggable items. */\nclass CdkDropList {\n    constructor(\n    /** Element that the drop list is attached to. */\n    element, dragDrop, _changeDetectorRef, _scrollDispatcher, _dir, _group, config) {\n        this.element = element;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._dir = _dir;\n        this._group = _group;\n        /** Emits when the list has been destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Other draggable containers that this container is connected to and into which the\n         * container's items can be transferred. Can either be references to other drop containers,\n         * or their unique IDs.\n         */\n        this.connectedTo = [];\n        /**\n         * Unique ID for the drop zone. Can be used as a reference\n         * in the `connectedTo` of another `CdkDropList`.\n         */\n        this.id = `cdk-drop-list-${_uniqueIdCounter++}`;\n        /**\n         * Function that is used to determine whether an item\n         * is allowed to be moved into a drop container.\n         */\n        this.enterPredicate = () => true;\n        /** Functions that is used to determine whether an item can be sorted into a particular index. */\n        this.sortPredicate = () => true;\n        /** Emits when the user drops an item inside the container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits when the user has moved a new drag item into this container.\n         */\n        this.entered = new EventEmitter();\n        /**\n         * Emits when the user removes an item from the container\n         * by dragging it into another container.\n         */\n        this.exited = new EventEmitter();\n        /** Emits as the user is swapping items while actively dragging. */\n        this.sorted = new EventEmitter();\n        /**\n         * Keeps track of the items that are registered with this container. Historically we used to\n         * do this with a `ContentChildren` query, however queries don't handle transplanted views very\n         * well which means that we can't handle cases like dragging the headers of a `mat-table`\n         * correctly. What we do instead is to have the items register themselves with the container\n         * and then we sort them based on their position in the DOM.\n         */\n        this._unsortedItems = new Set();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDropList');\n        }\n        this._dropListRef = dragDrop.createDropList(element);\n        this._dropListRef.data = this;\n        if (config) {\n            this._assignDefaults(config);\n        }\n        this._dropListRef.enterPredicate = (drag, drop) => {\n            return this.enterPredicate(drag.data, drop.data);\n        };\n        this._dropListRef.sortPredicate = (index, drag, drop) => {\n            return this.sortPredicate(index, drag.data, drop.data);\n        };\n        this._setupInputSyncSubscription(this._dropListRef);\n        this._handleEvents(this._dropListRef);\n        CdkDropList._dropLists.push(this);\n        if (_group) {\n            _group._items.add(this);\n        }\n    }\n    /** Whether starting a dragging sequence from this container is disabled. */\n    get disabled() {\n        return this._disabled || (!!this._group && this._group.disabled);\n    }\n    set disabled(value) {\n        // Usually we sync the directive and ref state right before dragging starts, in order to have\n        // a single point of failure and to avoid having to use setters for everything. `disabled` is\n        // a special case, because it can prevent the `beforeStarted` event from firing, which can lock\n        // the user in a disabled state, so we also need to sync it as it's being set.\n        this._dropListRef.disabled = this._disabled = coerceBooleanProperty(value);\n    }\n    /** Registers an items with the drop list. */\n    addItem(item) {\n        this._unsortedItems.add(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Removes an item from the drop list. */\n    removeItem(item) {\n        this._unsortedItems.delete(item);\n        if (this._dropListRef.isDragging()) {\n            this._syncItemsWithRef();\n        }\n    }\n    /** Gets the registered items in the list, sorted by their position in the DOM. */\n    getSortedItems() {\n        return Array.from(this._unsortedItems).sort((a, b) => {\n            const documentPosition = a._dragRef\n                .getVisibleElement()\n                .compareDocumentPosition(b._dragRef.getVisibleElement());\n            // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n            // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n            // tslint:disable-next-line:no-bitwise\n            return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n        });\n    }\n    ngOnDestroy() {\n        const index = CdkDropList._dropLists.indexOf(this);\n        if (index > -1) {\n            CdkDropList._dropLists.splice(index, 1);\n        }\n        if (this._group) {\n            this._group._items.delete(this);\n        }\n        this._unsortedItems.clear();\n        this._dropListRef.dispose();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Syncs the inputs of the CdkDropList with the options of the underlying DropListRef. */\n    _setupInputSyncSubscription(ref) {\n        if (this._dir) {\n            this._dir.change\n                .pipe(startWith(this._dir.value), takeUntil(this._destroyed))\n                .subscribe(value => ref.withDirection(value));\n        }\n        ref.beforeStarted.subscribe(() => {\n            const siblings = coerceArray(this.connectedTo).map(drop => {\n                if (typeof drop === 'string') {\n                    const correspondingDropList = CdkDropList._dropLists.find(list => list.id === drop);\n                    if (!correspondingDropList && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                        console.warn(`CdkDropList could not find connected drop list with id \"${drop}\"`);\n                    }\n                    return correspondingDropList;\n                }\n                return drop;\n            });\n            if (this._group) {\n                this._group._items.forEach(drop => {\n                    if (siblings.indexOf(drop) === -1) {\n                        siblings.push(drop);\n                    }\n                });\n            }\n            // Note that we resolve the scrollable parents here so that we delay the resolution\n            // as long as possible, ensuring that the element is in its final place in the DOM.\n            if (!this._scrollableParentsResolved) {\n                const scrollableParents = this._scrollDispatcher\n                    .getAncestorScrollContainers(this.element)\n                    .map(scrollable => scrollable.getElementRef().nativeElement);\n                this._dropListRef.withScrollableParents(scrollableParents);\n                // Only do this once since it involves traversing the DOM and the parents\n                // shouldn't be able to change without the drop list being destroyed.\n                this._scrollableParentsResolved = true;\n            }\n            ref.disabled = this.disabled;\n            ref.lockAxis = this.lockAxis;\n            ref.sortingDisabled = coerceBooleanProperty(this.sortingDisabled);\n            ref.autoScrollDisabled = coerceBooleanProperty(this.autoScrollDisabled);\n            ref.autoScrollStep = coerceNumberProperty(this.autoScrollStep, 2);\n            ref\n                .connectedTo(siblings.filter(drop => drop && drop !== this).map(list => list._dropListRef))\n                .withOrientation(this.orientation);\n        });\n    }\n    /** Handles events from the underlying DropListRef. */\n    _handleEvents(ref) {\n        ref.beforeStarted.subscribe(() => {\n            this._syncItemsWithRef();\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(event => {\n            this.entered.emit({\n                container: this,\n                item: event.item.data,\n                currentIndex: event.currentIndex,\n            });\n        });\n        ref.exited.subscribe(event => {\n            this.exited.emit({\n                container: this,\n                item: event.item.data,\n            });\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.sorted.subscribe(event => {\n            this.sorted.emit({\n                previousIndex: event.previousIndex,\n                currentIndex: event.currentIndex,\n                container: this,\n                item: event.item.data,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                item: dropEvent.item.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n            // Mark for check since all of these events run outside of change\n            // detection and we're not guaranteed for something else to have triggered it.\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, draggingDisabled, sortingDisabled, listAutoScrollDisabled, listOrientation } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.sortingDisabled = sortingDisabled == null ? false : sortingDisabled;\n        this.autoScrollDisabled = listAutoScrollDisabled == null ? false : listAutoScrollDisabled;\n        this.orientation = listOrientation || 'vertical';\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n    }\n    /** Syncs up the registered drag items with underlying drop list ref. */\n    _syncItemsWithRef() {\n        this._dropListRef.withItems(this.getSortedItems().map(item => item._dragRef));\n    }\n}\n/** Keeps track of the drop lists that are currently on the page. */\nCdkDropList._dropLists = [];\nCdkDropList.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDropList, deps: [{ token: i0.ElementRef }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: i1.ScrollDispatcher }, { token: i3.Directionality, optional: true }, { token: CDK_DROP_LIST_GROUP, optional: true, skipSelf: true }, { token: CDK_DRAG_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDropList.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDropList, selector: \"[cdkDropList], cdk-drop-list\", inputs: { connectedTo: [\"cdkDropListConnectedTo\", \"connectedTo\"], data: [\"cdkDropListData\", \"data\"], orientation: [\"cdkDropListOrientation\", \"orientation\"], id: \"id\", lockAxis: [\"cdkDropListLockAxis\", \"lockAxis\"], disabled: [\"cdkDropListDisabled\", \"disabled\"], sortingDisabled: [\"cdkDropListSortingDisabled\", \"sortingDisabled\"], enterPredicate: [\"cdkDropListEnterPredicate\", \"enterPredicate\"], sortPredicate: [\"cdkDropListSortPredicate\", \"sortPredicate\"], autoScrollDisabled: [\"cdkDropListAutoScrollDisabled\", \"autoScrollDisabled\"], autoScrollStep: [\"cdkDropListAutoScrollStep\", \"autoScrollStep\"] }, outputs: { dropped: \"cdkDropListDropped\", entered: \"cdkDropListEntered\", exited: \"cdkDropListExited\", sorted: \"cdkDropListSorted\" }, host: { properties: { \"attr.id\": \"id\", \"class.cdk-drop-list-disabled\": \"disabled\", \"class.cdk-drop-list-dragging\": \"_dropListRef.isDragging()\", \"class.cdk-drop-list-receiving\": \"_dropListRef.isReceiving()\" }, classAttribute: \"cdk-drop-list\" }, providers: [\n        // Prevent child drop lists from picking up the same group as their parent.\n        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n    ], exportAs: [\"cdkDropList\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDropList, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDropList], cdk-drop-list',\n                    exportAs: 'cdkDropList',\n                    providers: [\n                        // Prevent child drop lists from picking up the same group as their parent.\n                        { provide: CDK_DROP_LIST_GROUP, useValue: undefined },\n                        { provide: CDK_DROP_LIST, useExisting: CdkDropList },\n                    ],\n                    host: {\n                        'class': 'cdk-drop-list',\n                        '[attr.id]': 'id',\n                        '[class.cdk-drop-list-disabled]': 'disabled',\n                        '[class.cdk-drop-list-dragging]': '_dropListRef.isDragging()',\n                        '[class.cdk-drop-list-receiving]': '_dropListRef.isReceiving()',\n                    },\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: i1.ScrollDispatcher }, { type: i3.Directionality, decorators: [{\n                        type: Optional\n                    }] }, { type: CdkDropListGroup, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [CDK_DROP_LIST_GROUP]\n                    }, {\n                        type: SkipSelf\n                    }] }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [CDK_DRAG_CONFIG]\n                    }] }];\n    }, propDecorators: { connectedTo: [{\n                type: Input,\n                args: ['cdkDropListConnectedTo']\n            }], data: [{\n                type: Input,\n                args: ['cdkDropListData']\n            }], orientation: [{\n                type: Input,\n                args: ['cdkDropListOrientation']\n            }], id: [{\n                type: Input\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDropListLockAxis']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDropListDisabled']\n            }], sortingDisabled: [{\n                type: Input,\n                args: ['cdkDropListSortingDisabled']\n            }], enterPredicate: [{\n                type: Input,\n                args: ['cdkDropListEnterPredicate']\n            }], sortPredicate: [{\n                type: Input,\n                args: ['cdkDropListSortPredicate']\n            }], autoScrollDisabled: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollDisabled']\n            }], autoScrollStep: [{\n                type: Input,\n                args: ['cdkDropListAutoScrollStep']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDropListDropped']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDropListEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDropListExited']\n            }], sorted: [{\n                type: Output,\n                args: ['cdkDropListSorted']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragHandle`. It serves as\n * alternative token to the actual `CdkDragHandle` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_HANDLE = new InjectionToken('CdkDragHandle');\n/** Handle that can be used to drag a CdkDrag instance. */\nclass CdkDragHandle {\n    constructor(element, parentDrag) {\n        this.element = element;\n        /** Emits when the state of the handle has changed. */\n        this._stateChanges = new Subject();\n        this._disabled = false;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            assertElementNode(element.nativeElement, 'cdkDragHandle');\n        }\n        this._parentDrag = parentDrag;\n    }\n    /** Whether starting to drag through this handle is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._stateChanges.next(this);\n    }\n    ngOnDestroy() {\n        this._stateChanges.complete();\n    }\n}\nCdkDragHandle.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragHandle, deps: [{ token: i0.ElementRef }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragHandle.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDragHandle, selector: \"[cdkDragHandle]\", inputs: { disabled: [\"cdkDragHandleDisabled\", \"disabled\"] }, host: { classAttribute: \"cdk-drag-handle\" }, providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragHandle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDragHandle]',\n                    host: {\n                        'class': 'cdk-drag-handle',\n                    },\n                    providers: [{ provide: CDK_DRAG_HANDLE, useExisting: CdkDragHandle }],\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [CDK_DRAG_PARENT]\n                    }, {\n                        type: Optional\n                    }, {\n                        type: SkipSelf\n                    }] }];\n    }, propDecorators: { disabled: [{\n                type: Input,\n                args: ['cdkDragHandleDisabled']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPlaceholder`. It serves as\n * alternative token to the actual `CdkDragPlaceholder` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PLACEHOLDER = new InjectionToken('CdkDragPlaceholder');\n/**\n * Element that will be used as a template for the placeholder of a CdkDrag when\n * it is being dragged. The placeholder is displayed in place of the element being dragged.\n */\nclass CdkDragPlaceholder {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n}\nCdkDragPlaceholder.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragPlaceholder, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragPlaceholder.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDragPlaceholder, selector: \"ng-template[cdkDragPlaceholder]\", inputs: { data: \"data\" }, providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragPlaceholder, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPlaceholder]',\n                    providers: [{ provide: CDK_DRAG_PLACEHOLDER, useExisting: CdkDragPlaceholder }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token that can be used to reference instances of `CdkDragPreview`. It serves as\n * alternative token to the actual `CdkDragPreview` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_DRAG_PREVIEW = new InjectionToken('CdkDragPreview');\n/**\n * Element that will be used as a template for the preview\n * of a CdkDrag when it is being dragged.\n */\nclass CdkDragPreview {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n        this._matchSize = false;\n    }\n    /** Whether the preview should preserve the same size as the item that is being dragged. */\n    get matchSize() {\n        return this._matchSize;\n    }\n    set matchSize(value) {\n        this._matchSize = coerceBooleanProperty(value);\n    }\n}\nCdkDragPreview.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragPreview, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDragPreview.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDragPreview, selector: \"ng-template[cdkDragPreview]\", inputs: { data: \"data\", matchSize: \"matchSize\" }, providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDragPreview, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[cdkDragPreview]',\n                    providers: [{ provide: CDK_DRAG_PREVIEW, useExisting: CdkDragPreview }],\n                }]\n        }], ctorParameters: function () { return [{ type: i0.TemplateRef }]; }, propDecorators: { data: [{\n                type: Input\n            }], matchSize: [{\n                type: Input\n            }] } });\n\nconst DRAG_HOST_CLASS = 'cdk-drag';\n/** Element that can be moved inside a CdkDropList container. */\nclass CdkDrag {\n    constructor(\n    /** Element that the draggable is attached to. */\n    element, \n    /** Droppable container that the draggable is a part of. */\n    dropContainer, \n    /**\n     * @deprecated `_document` parameter no longer being used and will be removed.\n     * @breaking-change 12.0.0\n     */\n    _document, _ngZone, _viewContainerRef, config, _dir, dragDrop, _changeDetectorRef, _selfHandle, _parentDrag) {\n        this.element = element;\n        this.dropContainer = dropContainer;\n        this._ngZone = _ngZone;\n        this._viewContainerRef = _viewContainerRef;\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._selfHandle = _selfHandle;\n        this._parentDrag = _parentDrag;\n        this._destroyed = new Subject();\n        /** Emits when the user starts dragging the item. */\n        this.started = new EventEmitter();\n        /** Emits when the user has released a drag item, before any animations have started. */\n        this.released = new EventEmitter();\n        /** Emits when the user stops dragging an item in the container. */\n        this.ended = new EventEmitter();\n        /** Emits when the user has moved the item into a new container. */\n        this.entered = new EventEmitter();\n        /** Emits when the user removes the item its container by dragging it into another container. */\n        this.exited = new EventEmitter();\n        /** Emits when the user drops the item inside a container. */\n        this.dropped = new EventEmitter();\n        /**\n         * Emits as the user is dragging the item. Use with caution,\n         * because this event will fire for every pixel that the user has dragged.\n         */\n        this.moved = new Observable((observer) => {\n            const subscription = this._dragRef.moved\n                .pipe(map(movedEvent => ({\n                source: this,\n                pointerPosition: movedEvent.pointerPosition,\n                event: movedEvent.event,\n                delta: movedEvent.delta,\n                distance: movedEvent.distance,\n            })))\n                .subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n            };\n        });\n        this._dragRef = dragDrop.createDrag(element, {\n            dragStartThreshold: config && config.dragStartThreshold != null ? config.dragStartThreshold : 5,\n            pointerDirectionChangeThreshold: config && config.pointerDirectionChangeThreshold != null\n                ? config.pointerDirectionChangeThreshold\n                : 5,\n            zIndex: config === null || config === void 0 ? void 0 : config.zIndex,\n        });\n        this._dragRef.data = this;\n        // We have to keep track of the drag instances in order to be able to match an element to\n        // a drag instance. We can't go through the global registry of `DragRef`, because the root\n        // element could be different.\n        CdkDrag._dragInstances.push(this);\n        if (config) {\n            this._assignDefaults(config);\n        }\n        // Note that usually the container is assigned when the drop list is picks up the item, but in\n        // some cases (mainly transplanted views with OnPush, see #18341) we may end up in a situation\n        // where there are no items on the first change detection pass, but the items get picked up as\n        // soon as the user triggers another pass by dragging. This is a problem, because the item would\n        // have to switch from standalone mode to drag mode in the middle of the dragging sequence which\n        // is too late since the two modes save different kinds of information. We work around it by\n        // assigning the drop container both from here and the list.\n        if (dropContainer) {\n            this._dragRef._withDropContainer(dropContainer._dropListRef);\n            dropContainer.addItem(this);\n        }\n        this._syncInputs(this._dragRef);\n        this._handleEvents(this._dragRef);\n    }\n    /** Whether starting to drag this element is disabled. */\n    get disabled() {\n        return this._disabled || (this.dropContainer && this.dropContainer.disabled);\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._dragRef.disabled = this._disabled;\n    }\n    /**\n     * Returns the element that is being used as a placeholder\n     * while the current element is being dragged.\n     */\n    getPlaceholderElement() {\n        return this._dragRef.getPlaceholderElement();\n    }\n    /** Returns the root draggable element. */\n    getRootElement() {\n        return this._dragRef.getRootElement();\n    }\n    /** Resets a standalone drag item to its initial position. */\n    reset() {\n        this._dragRef.reset();\n    }\n    /**\n     * Gets the pixel coordinates of the draggable outside of a drop container.\n     */\n    getFreeDragPosition() {\n        return this._dragRef.getFreeDragPosition();\n    }\n    /**\n     * Sets the current position in pixels the draggable outside of a drop container.\n     * @param value New position to be set.\n     */\n    setFreeDragPosition(value) {\n        this._dragRef.setFreeDragPosition(value);\n    }\n    ngAfterViewInit() {\n        // Normally this isn't in the zone, but it can cause major performance regressions for apps\n        // using `zone-patch-rxjs` because it'll trigger a change detection when it unsubscribes.\n        this._ngZone.runOutsideAngular(() => {\n            // We need to wait for the zone to stabilize, in order for the reference\n            // element to be in the proper place in the DOM. This is mostly relevant\n            // for draggable elements inside portals since they get stamped out in\n            // their original DOM position and then they get transferred to the portal.\n            this._ngZone.onStable.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                this._updateRootElement();\n                this._setupHandlesListener();\n                if (this.freeDragPosition) {\n                    this._dragRef.setFreeDragPosition(this.freeDragPosition);\n                }\n            });\n        });\n    }\n    ngOnChanges(changes) {\n        const rootSelectorChange = changes['rootElementSelector'];\n        const positionChange = changes['freeDragPosition'];\n        // We don't have to react to the first change since it's being\n        // handled in `ngAfterViewInit` where it needs to be deferred.\n        if (rootSelectorChange && !rootSelectorChange.firstChange) {\n            this._updateRootElement();\n        }\n        // Skip the first change since it's being handled in `ngAfterViewInit`.\n        if (positionChange && !positionChange.firstChange && this.freeDragPosition) {\n            this._dragRef.setFreeDragPosition(this.freeDragPosition);\n        }\n    }\n    ngOnDestroy() {\n        if (this.dropContainer) {\n            this.dropContainer.removeItem(this);\n        }\n        const index = CdkDrag._dragInstances.indexOf(this);\n        if (index > -1) {\n            CdkDrag._dragInstances.splice(index, 1);\n        }\n        // Unnecessary in most cases, but used to avoid extra change detections with `zone-paths-rxjs`.\n        this._ngZone.runOutsideAngular(() => {\n            this._destroyed.next();\n            this._destroyed.complete();\n            this._dragRef.dispose();\n        });\n    }\n    /** Syncs the root element with the `DragRef`. */\n    _updateRootElement() {\n        var _a;\n        const element = this.element.nativeElement;\n        let rootElement = element;\n        if (this.rootElementSelector) {\n            rootElement =\n                element.closest !== undefined\n                    ? element.closest(this.rootElementSelector)\n                    : // Comment tag doesn't have closest method, so use parent's one.\n                        (_a = element.parentElement) === null || _a === void 0 ? void 0 : _a.closest(this.rootElementSelector);\n        }\n        if (rootElement && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            assertElementNode(rootElement, 'cdkDrag');\n        }\n        this._dragRef.withRootElement(rootElement || element);\n    }\n    /** Gets the boundary element, based on the `boundaryElement` value. */\n    _getBoundaryElement() {\n        const boundary = this.boundaryElement;\n        if (!boundary) {\n            return null;\n        }\n        if (typeof boundary === 'string') {\n            return this.element.nativeElement.closest(boundary);\n        }\n        return coerceElement(boundary);\n    }\n    /** Syncs the inputs of the CdkDrag with the options of the underlying DragRef. */\n    _syncInputs(ref) {\n        ref.beforeStarted.subscribe(() => {\n            if (!ref.isDragging()) {\n                const dir = this._dir;\n                const dragStartDelay = this.dragStartDelay;\n                const placeholder = this._placeholderTemplate\n                    ? {\n                        template: this._placeholderTemplate.templateRef,\n                        context: this._placeholderTemplate.data,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                const preview = this._previewTemplate\n                    ? {\n                        template: this._previewTemplate.templateRef,\n                        context: this._previewTemplate.data,\n                        matchSize: this._previewTemplate.matchSize,\n                        viewContainer: this._viewContainerRef,\n                    }\n                    : null;\n                ref.disabled = this.disabled;\n                ref.lockAxis = this.lockAxis;\n                ref.dragStartDelay =\n                    typeof dragStartDelay === 'object' && dragStartDelay\n                        ? dragStartDelay\n                        : coerceNumberProperty(dragStartDelay);\n                ref.constrainPosition = this.constrainPosition;\n                ref.previewClass = this.previewClass;\n                ref\n                    .withBoundaryElement(this._getBoundaryElement())\n                    .withPlaceholderTemplate(placeholder)\n                    .withPreviewTemplate(preview)\n                    .withPreviewContainer(this.previewContainer || 'global');\n                if (dir) {\n                    ref.withDirection(dir.value);\n                }\n            }\n        });\n        // This only needs to be resolved once.\n        ref.beforeStarted.pipe(take(1)).subscribe(() => {\n            var _a;\n            // If we managed to resolve a parent through DI, use it.\n            if (this._parentDrag) {\n                ref.withParent(this._parentDrag._dragRef);\n                return;\n            }\n            // Otherwise fall back to resolving the parent by looking up the DOM. This can happen if\n            // the item was projected into another item by something like `ngTemplateOutlet`.\n            let parent = this.element.nativeElement.parentElement;\n            while (parent) {\n                if (parent.classList.contains(DRAG_HOST_CLASS)) {\n                    ref.withParent(((_a = CdkDrag._dragInstances.find(drag => {\n                        return drag.element.nativeElement === parent;\n                    })) === null || _a === void 0 ? void 0 : _a._dragRef) || null);\n                    break;\n                }\n                parent = parent.parentElement;\n            }\n        });\n    }\n    /** Handles the events from the underlying `DragRef`. */\n    _handleEvents(ref) {\n        ref.started.subscribe(startEvent => {\n            this.started.emit({ source: this, event: startEvent.event });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.released.subscribe(releaseEvent => {\n            this.released.emit({ source: this, event: releaseEvent.event });\n        });\n        ref.ended.subscribe(endEvent => {\n            this.ended.emit({\n                source: this,\n                distance: endEvent.distance,\n                dropPoint: endEvent.dropPoint,\n                event: endEvent.event,\n            });\n            // Since all of these events run outside of change detection,\n            // we need to ensure that everything is marked correctly.\n            this._changeDetectorRef.markForCheck();\n        });\n        ref.entered.subscribe(enterEvent => {\n            this.entered.emit({\n                container: enterEvent.container.data,\n                item: this,\n                currentIndex: enterEvent.currentIndex,\n            });\n        });\n        ref.exited.subscribe(exitEvent => {\n            this.exited.emit({\n                container: exitEvent.container.data,\n                item: this,\n            });\n        });\n        ref.dropped.subscribe(dropEvent => {\n            this.dropped.emit({\n                previousIndex: dropEvent.previousIndex,\n                currentIndex: dropEvent.currentIndex,\n                previousContainer: dropEvent.previousContainer.data,\n                container: dropEvent.container.data,\n                isPointerOverContainer: dropEvent.isPointerOverContainer,\n                item: this,\n                distance: dropEvent.distance,\n                dropPoint: dropEvent.dropPoint,\n                event: dropEvent.event,\n            });\n        });\n    }\n    /** Assigns the default input values based on a provided config object. */\n    _assignDefaults(config) {\n        const { lockAxis, dragStartDelay, constrainPosition, previewClass, boundaryElement, draggingDisabled, rootElementSelector, previewContainer, } = config;\n        this.disabled = draggingDisabled == null ? false : draggingDisabled;\n        this.dragStartDelay = dragStartDelay || 0;\n        if (lockAxis) {\n            this.lockAxis = lockAxis;\n        }\n        if (constrainPosition) {\n            this.constrainPosition = constrainPosition;\n        }\n        if (previewClass) {\n            this.previewClass = previewClass;\n        }\n        if (boundaryElement) {\n            this.boundaryElement = boundaryElement;\n        }\n        if (rootElementSelector) {\n            this.rootElementSelector = rootElementSelector;\n        }\n        if (previewContainer) {\n            this.previewContainer = previewContainer;\n        }\n    }\n    /** Sets up the listener that syncs the handles with the drag ref. */\n    _setupHandlesListener() {\n        // Listen for any newly-added handles.\n        this._handles.changes\n            .pipe(startWith(this._handles), \n        // Sync the new handles with the DragRef.\n        tap((handles) => {\n            const childHandleElements = handles\n                .filter(handle => handle._parentDrag === this)\n                .map(handle => handle.element);\n            // Usually handles are only allowed to be a descendant of the drag element, but if\n            // the consumer defined a different drag root, we should allow the drag element\n            // itself to be a handle too.\n            if (this._selfHandle && this.rootElementSelector) {\n                childHandleElements.push(this.element);\n            }\n            this._dragRef.withHandles(childHandleElements);\n        }), \n        // Listen if the state of any of the handles changes.\n        switchMap((handles) => {\n            return merge(...handles.map(item => {\n                return item._stateChanges.pipe(startWith(item));\n            }));\n        }), takeUntil(this._destroyed))\n            .subscribe(handleInstance => {\n            // Enabled/disable the handle that changed in the DragRef.\n            const dragRef = this._dragRef;\n            const handle = handleInstance.element.nativeElement;\n            handleInstance.disabled ? dragRef.disableHandle(handle) : dragRef.enableHandle(handle);\n        });\n    }\n}\nCdkDrag._dragInstances = [];\nCdkDrag.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDrag, deps: [{ token: i0.ElementRef }, { token: CDK_DROP_LIST, optional: true, skipSelf: true }, { token: DOCUMENT }, { token: i0.NgZone }, { token: i0.ViewContainerRef }, { token: CDK_DRAG_CONFIG, optional: true }, { token: i3.Directionality, optional: true }, { token: DragDrop }, { token: i0.ChangeDetectorRef }, { token: CDK_DRAG_HANDLE, optional: true, self: true }, { token: CDK_DRAG_PARENT, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.Directive });\nCdkDrag.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkDrag, selector: \"[cdkDrag]\", inputs: { data: [\"cdkDragData\", \"data\"], lockAxis: [\"cdkDragLockAxis\", \"lockAxis\"], rootElementSelector: [\"cdkDragRootElement\", \"rootElementSelector\"], boundaryElement: [\"cdkDragBoundary\", \"boundaryElement\"], dragStartDelay: [\"cdkDragStartDelay\", \"dragStartDelay\"], freeDragPosition: [\"cdkDragFreeDragPosition\", \"freeDragPosition\"], disabled: [\"cdkDragDisabled\", \"disabled\"], constrainPosition: [\"cdkDragConstrainPosition\", \"constrainPosition\"], previewClass: [\"cdkDragPreviewClass\", \"previewClass\"], previewContainer: [\"cdkDragPreviewContainer\", \"previewContainer\"] }, outputs: { started: \"cdkDragStarted\", released: \"cdkDragReleased\", ended: \"cdkDragEnded\", entered: \"cdkDragEntered\", exited: \"cdkDragExited\", dropped: \"cdkDragDropped\", moved: \"cdkDragMoved\" }, host: { properties: { \"class.cdk-drag-disabled\": \"disabled\", \"class.cdk-drag-dragging\": \"_dragRef.isDragging()\" }, classAttribute: \"cdk-drag\" }, providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }], queries: [{ propertyName: \"_previewTemplate\", first: true, predicate: CDK_DRAG_PREVIEW, descendants: true }, { propertyName: \"_placeholderTemplate\", first: true, predicate: CDK_DRAG_PLACEHOLDER, descendants: true }, { propertyName: \"_handles\", predicate: CDK_DRAG_HANDLE, descendants: true }], exportAs: [\"cdkDrag\"], usesOnChanges: true, ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkDrag, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkDrag]',\n                    exportAs: 'cdkDrag',\n                    host: {\n                        'class': DRAG_HOST_CLASS,\n                        '[class.cdk-drag-disabled]': 'disabled',\n                        '[class.cdk-drag-dragging]': '_dragRef.isDragging()',\n                    },\n                    providers: [{ provide: CDK_DRAG_PARENT, useExisting: CdkDrag }],\n                }]\n        }], ctorParameters: function () {\n        return [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [CDK_DROP_LIST]\n                    }, {\n                        type: Optional\n                    }, {\n                        type: SkipSelf\n                    }] }, { type: undefined, decorators: [{\n                        type: Inject,\n                        args: [DOCUMENT]\n                    }] }, { type: i0.NgZone }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Inject,\n                        args: [CDK_DRAG_CONFIG]\n                    }] }, { type: i3.Directionality, decorators: [{\n                        type: Optional\n                    }] }, { type: DragDrop }, { type: i0.ChangeDetectorRef }, { type: CdkDragHandle, decorators: [{\n                        type: Optional\n                    }, {\n                        type: Self\n                    }, {\n                        type: Inject,\n                        args: [CDK_DRAG_HANDLE]\n                    }] }, { type: CdkDrag, decorators: [{\n                        type: Optional\n                    }, {\n                        type: SkipSelf\n                    }, {\n                        type: Inject,\n                        args: [CDK_DRAG_PARENT]\n                    }] }];\n    }, propDecorators: { _handles: [{\n                type: ContentChildren,\n                args: [CDK_DRAG_HANDLE, { descendants: true }]\n            }], _previewTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PREVIEW]\n            }], _placeholderTemplate: [{\n                type: ContentChild,\n                args: [CDK_DRAG_PLACEHOLDER]\n            }], data: [{\n                type: Input,\n                args: ['cdkDragData']\n            }], lockAxis: [{\n                type: Input,\n                args: ['cdkDragLockAxis']\n            }], rootElementSelector: [{\n                type: Input,\n                args: ['cdkDragRootElement']\n            }], boundaryElement: [{\n                type: Input,\n                args: ['cdkDragBoundary']\n            }], dragStartDelay: [{\n                type: Input,\n                args: ['cdkDragStartDelay']\n            }], freeDragPosition: [{\n                type: Input,\n                args: ['cdkDragFreeDragPosition']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkDragDisabled']\n            }], constrainPosition: [{\n                type: Input,\n                args: ['cdkDragConstrainPosition']\n            }], previewClass: [{\n                type: Input,\n                args: ['cdkDragPreviewClass']\n            }], previewContainer: [{\n                type: Input,\n                args: ['cdkDragPreviewContainer']\n            }], started: [{\n                type: Output,\n                args: ['cdkDragStarted']\n            }], released: [{\n                type: Output,\n                args: ['cdkDragReleased']\n            }], ended: [{\n                type: Output,\n                args: ['cdkDragEnded']\n            }], entered: [{\n                type: Output,\n                args: ['cdkDragEntered']\n            }], exited: [{\n                type: Output,\n                args: ['cdkDragExited']\n            }], dropped: [{\n                type: Output,\n                args: ['cdkDragDropped']\n            }], moved: [{\n                type: Output,\n                args: ['cdkDragMoved']\n            }] } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DragDropModule {\n}\nDragDropModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nDragDropModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropModule, declarations: [CdkDropList,\n        CdkDropListGroup,\n        CdkDrag,\n        CdkDragHandle,\n        CdkDragPreview,\n        CdkDragPlaceholder], exports: [CdkScrollableModule,\n        CdkDropList,\n        CdkDropListGroup,\n        CdkDrag,\n        CdkDragHandle,\n        CdkDragPreview,\n        CdkDragPlaceholder] });\nDragDropModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropModule, providers: [DragDrop], imports: [CdkScrollableModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: DragDropModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [\n                        CdkDropList,\n                        CdkDropListGroup,\n                        CdkDrag,\n                        CdkDragHandle,\n                        CdkDragPreview,\n                        CdkDragPlaceholder,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        CdkDropList,\n                        CdkDropListGroup,\n                        CdkDrag,\n                        CdkDragHandle,\n                        CdkDragPreview,\n                        CdkDragPlaceholder,\n                    ],\n                    providers: [DragDrop],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_DRAG_CONFIG, CDK_DRAG_HANDLE, CDK_DRAG_PARENT, CDK_DRAG_PLACEHOLDER, CDK_DRAG_PREVIEW, CDK_DROP_LIST, CDK_DROP_LIST_GROUP, CdkDrag, CdkDragHandle, CdkDragPlaceholder, CdkDragPreview, CdkDropList, CdkDropListGroup, DragDrop, DragDropModule, DragDropRegistry, DragRef, DropListRef, copyArrayItem, moveItemInArray, transferArrayItem };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,MAArB,EAA6BC,cAA7B,EAA6CC,SAA7C,EAAwDC,KAAxD,EAA+DC,YAA/D,EAA6EC,QAA7E,EAAuFC,QAAvF,EAAiGC,MAAjG,EAAyGC,IAAzG,EAA+GC,eAA/G,EAAgIC,YAAhI,EAA8IC,QAA9I,QAA8J,eAA9J;AACA,SAASC,QAAT,QAAyB,iBAAzB;AACA,SAASC,eAAT,EAA0BC,+BAA1B,EAA2DC,cAA3D,QAAiF,uBAAjF;AACA,SAASC,qBAAT,EAAgCC,aAAhC,EAA+CC,WAA/C,EAA4DC,oBAA5D,QAAwF,uBAAxF;AACA,SAASC,gCAAT,EAA2CC,+BAA3C,QAAkF,mBAAlF;AACA,SAASC,OAAT,EAAkBC,YAAlB,EAAgCC,QAAhC,EAA0CC,uBAA1C,EAAmEC,UAAnE,EAA+EC,KAA/E,QAA4F,MAA5F;AACA,SAASC,SAAT,EAAoBC,SAApB,EAA+BC,GAA/B,EAAoCC,IAApC,EAA0CC,GAA1C,EAA+CC,SAA/C,QAAgE,gBAAhE;AACA,OAAO,KAAKC,EAAZ,MAAoB,wBAApB;AACA,SAASC,mBAAT,QAAoC,wBAApC;AACA,OAAO,KAAKC,EAAZ,MAAoB,mBAApB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,YAAT,CAAsBC,IAAtB,EAA4BC,MAA5B,EAAoCC,mBAApC,EAAyD;EACrD,KAAK,IAAIC,GAAT,IAAgBF,MAAhB,EAAwB;IACpB,IAAIA,MAAM,CAACG,cAAP,CAAsBD,GAAtB,CAAJ,EAAgC;MAC5B,MAAME,KAAK,GAAGJ,MAAM,CAACE,GAAD,CAApB;;MACA,IAAIE,KAAJ,EAAW;QACPL,IAAI,CAACM,WAAL,CAAiBH,GAAjB,EAAsBE,KAAtB,EAA6B,CAACH,mBAAmB,KAAK,IAAxB,IAAgCA,mBAAmB,KAAK,KAAK,CAA7D,GAAiE,KAAK,CAAtE,GAA0EA,mBAAmB,CAACK,GAApB,CAAwBJ,GAAxB,CAA3E,IAA2G,WAA3G,GAAyH,EAAtJ;MACH,CAFD,MAGK;QACDH,IAAI,CAACQ,cAAL,CAAoBL,GAApB;MACH;IACJ;EACJ;;EACD,OAAOH,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASS,4BAAT,CAAsCC,OAAtC,EAA+CC,MAA/C,EAAuD;EACnD,MAAMC,UAAU,GAAGD,MAAM,GAAG,EAAH,GAAQ,MAAjC;EACAZ,YAAY,CAACW,OAAO,CAACG,KAAT,EAAgB;IACxB,gBAAgBF,MAAM,GAAG,EAAH,GAAQ,MADN;IAExB,qBAAqBA,MAAM,GAAG,EAAH,GAAQ,MAFX;IAGxB,+BAA+BA,MAAM,GAAG,EAAH,GAAQ,aAHrB;IAIxB,eAAeC,UAJS;IAKxB,mBAAmBA,UALK;IAMxB,uBAAuBA,UANC;IAOxB,oBAAoBA;EAPI,CAAhB,CAAZ;AASH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASE,gBAAT,CAA0BJ,OAA1B,EAAmCC,MAAnC,EAA2CT,mBAA3C,EAAgE;EAC5DH,YAAY,CAACW,OAAO,CAACG,KAAT,EAAgB;IACxBE,QAAQ,EAAEJ,MAAM,GAAG,EAAH,GAAQ,OADA;IAExBK,GAAG,EAAEL,MAAM,GAAG,EAAH,GAAQ,GAFK;IAGxBM,OAAO,EAAEN,MAAM,GAAG,EAAH,GAAQ,GAHC;IAIxBO,IAAI,EAAEP,MAAM,GAAG,EAAH,GAAQ;EAJI,CAAhB,EAKTT,mBALS,CAAZ;AAMH;AACD;AACA;AACA;AACA;;;AACA,SAASiB,iBAAT,CAA2BC,SAA3B,EAAsCC,gBAAtC,EAAwD;EACpD,OAAOA,gBAAgB,IAAIA,gBAAgB,IAAI,MAAxC,GACDD,SAAS,GAAG,GAAZ,GAAkBC,gBADjB,GAEDD,SAFN;AAGH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASE,qBAAT,CAA+BjB,KAA/B,EAAsC;EAClC;EACA,MAAMkB,UAAU,GAAGlB,KAAK,CAACmB,WAAN,GAAoBC,OAApB,CAA4B,IAA5B,IAAoC,CAAC,CAArC,GAAyC,CAAzC,GAA6C,IAAhE;EACA,OAAOC,UAAU,CAACrB,KAAD,CAAV,GAAoBkB,UAA3B;AACH;AACD;;;AACA,SAASI,kCAAT,CAA4CjB,OAA5C,EAAqD;EACjD,MAAMkB,aAAa,GAAGC,gBAAgB,CAACnB,OAAD,CAAtC;EACA,MAAMoB,sBAAsB,GAAGC,qBAAqB,CAACH,aAAD,EAAgB,qBAAhB,CAApD;EACA,MAAMI,QAAQ,GAAGF,sBAAsB,CAACG,IAAvB,CAA4BC,IAAI,IAAIA,IAAI,KAAK,WAAT,IAAwBA,IAAI,KAAK,KAArE,CAAjB,CAHiD,CAIjD;;EACA,IAAI,CAACF,QAAL,EAAe;IACX,OAAO,CAAP;EACH,CAPgD,CAQjD;EACA;;;EACA,MAAMG,aAAa,GAAGL,sBAAsB,CAACL,OAAvB,CAA+BO,QAA/B,CAAtB;EACA,MAAMI,YAAY,GAAGL,qBAAqB,CAACH,aAAD,EAAgB,qBAAhB,CAA1C;EACA,MAAMS,SAAS,GAAGN,qBAAqB,CAACH,aAAD,EAAgB,kBAAhB,CAAvC;EACA,OAAQN,qBAAqB,CAACc,YAAY,CAACD,aAAD,CAAb,CAArB,GACJb,qBAAqB,CAACe,SAAS,CAACF,aAAD,CAAV,CADzB;AAEH;AACD;;;AACA,SAASJ,qBAAT,CAA+BH,aAA/B,EAA8CU,IAA9C,EAAoD;EAChD,MAAMjC,KAAK,GAAGuB,aAAa,CAACW,gBAAd,CAA+BD,IAA/B,CAAd;EACA,OAAOjC,KAAK,CAACmC,KAAN,CAAY,GAAZ,EAAiBhD,GAAjB,CAAqBiD,IAAI,IAAIA,IAAI,CAACC,IAAL,EAA7B,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,oBAAT,CAA8BjC,OAA9B,EAAuC;EACnC,MAAMkC,UAAU,GAAGlC,OAAO,CAACmC,qBAAR,EAAnB,CADmC,CAEnC;EACA;EACA;EACA;;EACA,OAAO;IACH7B,GAAG,EAAE4B,UAAU,CAAC5B,GADb;IAEH8B,KAAK,EAAEF,UAAU,CAACE,KAFf;IAGHC,MAAM,EAAEH,UAAU,CAACG,MAHhB;IAIH7B,IAAI,EAAE0B,UAAU,CAAC1B,IAJd;IAKH8B,KAAK,EAAEJ,UAAU,CAACI,KALf;IAMHC,MAAM,EAAEL,UAAU,CAACK,MANhB;IAOHC,CAAC,EAAEN,UAAU,CAACM,CAPX;IAQHC,CAAC,EAAEP,UAAU,CAACO;EARX,CAAP;AAUH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASC,kBAAT,CAA4BR,UAA5B,EAAwCM,CAAxC,EAA2CC,CAA3C,EAA8C;EAC1C,MAAM;IAAEnC,GAAF;IAAO+B,MAAP;IAAe7B,IAAf;IAAqB4B;EAArB,IAA+BF,UAArC;EACA,OAAOO,CAAC,IAAInC,GAAL,IAAYmC,CAAC,IAAIJ,MAAjB,IAA2BG,CAAC,IAAIhC,IAAhC,IAAwCgC,CAAC,IAAIJ,KAApD;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASO,gBAAT,CAA0BT,UAA1B,EAAsC5B,GAAtC,EAA2CE,IAA3C,EAAiD;EAC7C0B,UAAU,CAAC5B,GAAX,IAAkBA,GAAlB;EACA4B,UAAU,CAACG,MAAX,GAAoBH,UAAU,CAAC5B,GAAX,GAAiB4B,UAAU,CAACK,MAAhD;EACAL,UAAU,CAAC1B,IAAX,IAAmBA,IAAnB;EACA0B,UAAU,CAACE,KAAX,GAAmBF,UAAU,CAAC1B,IAAX,GAAkB0B,UAAU,CAACI,KAAhD;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASM,uBAAT,CAAiCC,IAAjC,EAAuCC,SAAvC,EAAkDC,QAAlD,EAA4DC,QAA5D,EAAsE;EAClE,MAAM;IAAE1C,GAAF;IAAO8B,KAAP;IAAcC,MAAd;IAAsB7B,IAAtB;IAA4B8B,KAA5B;IAAmCC;EAAnC,IAA8CM,IAApD;EACA,MAAMI,UAAU,GAAGX,KAAK,GAAGQ,SAA3B;EACA,MAAMI,UAAU,GAAGX,MAAM,GAAGO,SAA5B;EACA,OAAQE,QAAQ,GAAG1C,GAAG,GAAG4C,UAAjB,IACJF,QAAQ,GAAGX,MAAM,GAAGa,UADhB,IAEJH,QAAQ,GAAGvC,IAAI,GAAGyC,UAFd,IAGJF,QAAQ,GAAGX,KAAK,GAAGa,UAHvB;AAIH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAME,qBAAN,CAA4B;EACxBC,WAAW,CAACC,SAAD,EAAY;IACnB,KAAKA,SAAL,GAAiBA,SAAjB;IACA;;IACA,KAAKC,SAAL,GAAiB,IAAIC,GAAJ,EAAjB;EACH;EACD;;;EACAC,KAAK,GAAG;IACJ,KAAKF,SAAL,CAAeE,KAAf;EACH;EACD;;;EACAC,KAAK,CAACC,QAAD,EAAW;IACZ,KAAKF,KAAL;IACA,KAAKF,SAAL,CAAeK,GAAf,CAAmB,KAAKN,SAAxB,EAAmC;MAC/BO,cAAc,EAAE,KAAKC,yBAAL;IADe,CAAnC;IAGAH,QAAQ,CAACI,OAAT,CAAiB9D,OAAO,IAAI;MACxB,KAAKsD,SAAL,CAAeK,GAAf,CAAmB3D,OAAnB,EAA4B;QACxB4D,cAAc,EAAE;UAAEtD,GAAG,EAAEN,OAAO,CAAC+D,SAAf;UAA0BvD,IAAI,EAAER,OAAO,CAACgE;QAAxC,CADQ;QAExB9B,UAAU,EAAED,oBAAoB,CAACjC,OAAD;MAFR,CAA5B;IAIH,CALD;EAMH;EACD;;;EACAiE,YAAY,CAACC,KAAD,EAAQ;IAChB,MAAMC,MAAM,GAAGtG,eAAe,CAACqG,KAAD,CAA9B;;IACA,MAAME,cAAc,GAAG,KAAKd,SAAL,CAAee,GAAf,CAAmBF,MAAnB,CAAvB;;IACA,IAAI,CAACC,cAAL,EAAqB;MACjB,OAAO,IAAP;IACH;;IACD,MAAMR,cAAc,GAAGQ,cAAc,CAACR,cAAtC;IACA,IAAIU,MAAJ;IACA,IAAIC,OAAJ;;IACA,IAAIJ,MAAM,KAAK,KAAKd,SAApB,EAA+B;MAC3B,MAAMmB,sBAAsB,GAAG,KAAKX,yBAAL,EAA/B;MACAS,MAAM,GAAGE,sBAAsB,CAAClE,GAAhC;MACAiE,OAAO,GAAGC,sBAAsB,CAAChE,IAAjC;IACH,CAJD,MAKK;MACD8D,MAAM,GAAGH,MAAM,CAACJ,SAAhB;MACAQ,OAAO,GAAGJ,MAAM,CAACH,UAAjB;IACH;;IACD,MAAMS,aAAa,GAAGb,cAAc,CAACtD,GAAf,GAAqBgE,MAA3C;IACA,MAAMI,cAAc,GAAGd,cAAc,CAACpD,IAAf,GAAsB+D,OAA7C,CAnBgB,CAoBhB;IACA;;IACA,KAAKjB,SAAL,CAAeQ,OAAf,CAAuB,CAACzD,QAAD,EAAWsE,IAAX,KAAoB;MACvC,IAAItE,QAAQ,CAAC6B,UAAT,IAAuBiC,MAAM,KAAKQ,IAAlC,IAA0CR,MAAM,CAACS,QAAP,CAAgBD,IAAhB,CAA9C,EAAqE;QACjEhC,gBAAgB,CAACtC,QAAQ,CAAC6B,UAAV,EAAsBuC,aAAtB,EAAqCC,cAArC,CAAhB;MACH;IACJ,CAJD;IAKAd,cAAc,CAACtD,GAAf,GAAqBgE,MAArB;IACAV,cAAc,CAACpD,IAAf,GAAsB+D,OAAtB;IACA,OAAO;MAAEjE,GAAG,EAAEmE,aAAP;MAAsBjE,IAAI,EAAEkE;IAA5B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIb,yBAAyB,GAAG;IACxB,OAAO;MAAEvD,GAAG,EAAEuE,MAAM,CAACC,OAAd;MAAuBtE,IAAI,EAAEqE,MAAM,CAACE;IAApC,CAAP;EACH;;AA/DuB;AAkE5B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,aAAT,CAAuBL,IAAvB,EAA6B;EACzB,MAAMM,KAAK,GAAGN,IAAI,CAACO,SAAL,CAAe,IAAf,CAAd;EACA,MAAMC,iBAAiB,GAAGF,KAAK,CAACG,gBAAN,CAAuB,MAAvB,CAA1B;EACA,MAAMC,QAAQ,GAAGV,IAAI,CAACU,QAAL,CAAcvE,WAAd,EAAjB,CAHyB,CAIzB;;EACAmE,KAAK,CAACK,eAAN,CAAsB,IAAtB;;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,iBAAiB,CAACK,MAAtC,EAA8CD,CAAC,EAA/C,EAAmD;IAC/CJ,iBAAiB,CAACI,CAAD,CAAjB,CAAqBD,eAArB,CAAqC,IAArC;EACH;;EACD,IAAID,QAAQ,KAAK,QAAjB,EAA2B;IACvBI,kBAAkB,CAACd,IAAD,EAAOM,KAAP,CAAlB;EACH,CAFD,MAGK,IAAII,QAAQ,KAAK,OAAb,IAAwBA,QAAQ,KAAK,QAArC,IAAiDA,QAAQ,KAAK,UAAlE,EAA8E;IAC/EK,iBAAiB,CAACf,IAAD,EAAOM,KAAP,CAAjB;EACH;;EACDU,YAAY,CAAC,QAAD,EAAWhB,IAAX,EAAiBM,KAAjB,EAAwBQ,kBAAxB,CAAZ;EACAE,YAAY,CAAC,yBAAD,EAA4BhB,IAA5B,EAAkCM,KAAlC,EAAyCS,iBAAzC,CAAZ;EACA,OAAOT,KAAP;AACH;AACD;;;AACA,SAASU,YAAT,CAAsBC,QAAtB,EAAgCjB,IAAhC,EAAsCM,KAAtC,EAA6CY,QAA7C,EAAuD;EACnD,MAAMC,kBAAkB,GAAGnB,IAAI,CAACS,gBAAL,CAAsBQ,QAAtB,CAA3B;;EACA,IAAIE,kBAAkB,CAACN,MAAvB,EAA+B;IAC3B,MAAMO,aAAa,GAAGd,KAAK,CAACG,gBAAN,CAAuBQ,QAAvB,CAAtB;;IACA,KAAK,IAAIL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,kBAAkB,CAACN,MAAvC,EAA+CD,CAAC,EAAhD,EAAoD;MAChDM,QAAQ,CAACC,kBAAkB,CAACP,CAAD,CAAnB,EAAwBQ,aAAa,CAACR,CAAD,CAArC,CAAR;IACH;EACJ;AACJ,C,CACD;;;AACA,IAAIS,aAAa,GAAG,CAApB;AACA;;AACA,SAASN,iBAAT,CAA2BnG,MAA3B,EAAmC0F,KAAnC,EAA0C;EACtC;EACA,IAAIA,KAAK,CAACgB,IAAN,KAAe,MAAnB,EAA2B;IACvBhB,KAAK,CAACtF,KAAN,GAAcJ,MAAM,CAACI,KAArB;EACH,CAJqC,CAKtC;EACA;EACA;;;EACA,IAAIsF,KAAK,CAACgB,IAAN,KAAe,OAAf,IAA0BhB,KAAK,CAACrD,IAApC,EAA0C;IACtCqD,KAAK,CAACrD,IAAN,GAAc,aAAYqD,KAAK,CAACrD,IAAK,IAAGoE,aAAa,EAAG,EAAxD;EACH;AACJ;AACD;;;AACA,SAASP,kBAAT,CAA4BlG,MAA5B,EAAoC0F,KAApC,EAA2C;EACvC,MAAMiB,OAAO,GAAGjB,KAAK,CAACkB,UAAN,CAAiB,IAAjB,CAAhB;;EACA,IAAID,OAAJ,EAAa;IACT;IACA;IACA,IAAI;MACAA,OAAO,CAACE,SAAR,CAAkB7G,MAAlB,EAA0B,CAA1B,EAA6B,CAA7B;IACH,CAFD,CAGA,OAAO8G,EAAP,EAAW,CAAG;EACjB;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,2BAA2B,GAAGxI,+BAA+B,CAAC;EAAEyI,OAAO,EAAE;AAAX,CAAD,CAAnE;AACA;;AACA,MAAMC,0BAA0B,GAAG1I,+BAA+B,CAAC;EAAEyI,OAAO,EAAE;AAAX,CAAD,CAAlE;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAME,uBAAuB,GAAG,GAAhC;AACA;;AACA,MAAMC,uBAAuB,GAAG,IAAIC,GAAJ,CAAQ,CACpC;AACA,UAFoC,CAAR,CAAhC;AAIA;AACA;AACA;;AACA,MAAMC,OAAN,CAAc;EACVxD,WAAW,CAACpD,OAAD,EAAU6G,OAAV,EAAmBxD,SAAnB,EAA8ByD,OAA9B,EAAuCC,cAAvC,EAAuDC,iBAAvD,EAA0E;IACjF,KAAKH,OAAL,GAAeA,OAAf;IACA,KAAKxD,SAAL,GAAiBA,SAAjB;IACA,KAAKyD,OAAL,GAAeA,OAAf;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA;AACR;AACA;AACA;AACA;AACA;;IACQ,KAAKC,iBAAL,GAAyB;MAAEzE,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAzB;IACA;;IACA,KAAKyE,gBAAL,GAAwB;MAAE1E,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAxB;IACA;AACR;AACA;AACA;;IACQ,KAAK0E,mBAAL,GAA2B,KAA3B;IACA;;IACA,KAAKC,WAAL,GAAmB,IAAI9I,OAAJ,EAAnB;IACA;;IACA,KAAK+I,wBAAL,GAAgC9I,YAAY,CAAC+I,KAA7C;IACA;;IACA,KAAKC,sBAAL,GAA8BhJ,YAAY,CAAC+I,KAA3C;IACA;;IACA,KAAKE,mBAAL,GAA2BjJ,YAAY,CAAC+I,KAAxC;IACA;;IACA,KAAKG,mBAAL,GAA2BlJ,YAAY,CAAC+I,KAAxC;IACA;;IACA,KAAKI,gBAAL,GAAwB,IAAxB;IACA;;IACA,KAAKC,0BAAL,GAAkC,IAAlC;IACA;;IACA,KAAKC,QAAL,GAAgB,EAAhB;IACA;;IACA,KAAKC,gBAAL,GAAwB,IAAIlB,GAAJ,EAAxB;IACA;;IACA,KAAKmB,UAAL,GAAkB,KAAlB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,CAAtB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA;;IACA,KAAKC,aAAL,GAAqB,IAAI3J,OAAJ,EAArB;IACA;;IACA,KAAK4J,OAAL,GAAe,IAAI5J,OAAJ,EAAf;IACA;;IACA,KAAK6J,QAAL,GAAgB,IAAI7J,OAAJ,EAAhB;IACA;;IACA,KAAK8J,KAAL,GAAa,IAAI9J,OAAJ,EAAb;IACA;;IACA,KAAK+J,OAAL,GAAe,IAAI/J,OAAJ,EAAf;IACA;;IACA,KAAKgK,MAAL,GAAc,IAAIhK,OAAJ,EAAd;IACA;;IACA,KAAKiK,OAAL,GAAe,IAAIjK,OAAJ,EAAf;IACA;AACR;AACA;AACA;;IACQ,KAAKkK,KAAL,GAAa,KAAKpB,WAAlB;IACA;;IACA,KAAKqB,YAAL,GAAqBvE,KAAD,IAAW;MAC3B,KAAK+D,aAAL,CAAmBS,IAAnB,GAD2B,CAE3B;;MACA,IAAI,KAAKd,QAAL,CAAcpC,MAAlB,EAA0B;QACtB,MAAMmD,YAAY,GAAG,KAAKC,gBAAL,CAAsB1E,KAAtB,CAArB;;QACA,IAAIyE,YAAY,IAAI,CAAC,KAAKd,gBAAL,CAAsBhI,GAAtB,CAA0B8I,YAA1B,CAAjB,IAA4D,CAAC,KAAKE,QAAtE,EAAgF;UAC5E,KAAKC,uBAAL,CAA6BH,YAA7B,EAA2CzE,KAA3C;QACH;MACJ,CALD,MAMK,IAAI,CAAC,KAAK2E,QAAV,EAAoB;QACrB,KAAKC,uBAAL,CAA6B,KAAKC,YAAlC,EAAgD7E,KAAhD;MACH;IACJ,CAZD;IAaA;;;IACA,KAAK8E,YAAL,GAAqB9E,KAAD,IAAW;MAC3B,MAAM+E,eAAe,GAAG,KAAKC,yBAAL,CAA+BhF,KAA/B,CAAxB;;MACA,IAAI,CAAC,KAAKiD,mBAAV,EAA+B;QAC3B,MAAMgC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASJ,eAAe,CAACzG,CAAhB,GAAoB,KAAK8G,qBAAL,CAA2B9G,CAAxD,CAAlB;QACA,MAAM+G,SAAS,GAAGH,IAAI,CAACC,GAAL,CAASJ,eAAe,CAACxG,CAAhB,GAAoB,KAAK6G,qBAAL,CAA2B7G,CAAxD,CAAlB;QACA,MAAM+G,eAAe,GAAGL,SAAS,GAAGI,SAAZ,IAAyB,KAAK1C,OAAL,CAAa4C,kBAA9D,CAH2B,CAI3B;QACA;QACA;QACA;;QACA,IAAID,eAAJ,EAAqB;UACjB,MAAME,cAAc,GAAGC,IAAI,CAACC,GAAL,MAAc,KAAKC,cAAL,GAAsB,KAAKC,kBAAL,CAAwB5F,KAAxB,CAA3D;;UACA,MAAM6F,SAAS,GAAG,KAAKC,cAAvB;;UACA,IAAI,CAACN,cAAL,EAAqB;YACjB,KAAKO,gBAAL,CAAsB/F,KAAtB;;YACA;UACH,CANgB,CAOjB;UACA;UACA;;;UACA,IAAI,CAAC6F,SAAD,IAAe,CAACA,SAAS,CAACG,UAAV,EAAD,IAA2B,CAACH,SAAS,CAACI,WAAV,EAA/C,EAAyE;YACrE;YACA;YACAjG,KAAK,CAACkG,cAAN;YACA,KAAKjD,mBAAL,GAA2B,IAA3B;;YACA,KAAKL,OAAL,CAAauD,GAAb,CAAiB,MAAM,KAAKC,kBAAL,CAAwBpG,KAAxB,CAAvB;UACH;QACJ;;QACD;MACH,CA7B0B,CA8B3B;MACA;MACA;;;MACAA,KAAK,CAACkG,cAAN;;MACA,MAAMG,0BAA0B,GAAG,KAAKC,8BAAL,CAAoCvB,eAApC,CAAnC;;MACA,KAAKwB,SAAL,GAAiB,IAAjB;MACA,KAAKC,yBAAL,GAAiCzB,eAAjC;;MACA,KAAK0B,4BAAL,CAAkCJ,0BAAlC;;MACA,IAAI,KAAKP,cAAT,EAAyB;QACrB,KAAKY,0BAAL,CAAgCL,0BAAhC,EAA4DtB,eAA5D;MACH,CAFD,MAGK;QACD;QACA;QACA,MAAM4B,MAAM,GAAG,KAAKC,iBAAL,GAAyB,KAAKC,kBAA9B,GAAmD,KAAKzB,qBAAvE;QACA,MAAM0B,eAAe,GAAG,KAAK9D,gBAA7B;QACA8D,eAAe,CAACxI,CAAhB,GAAoB+H,0BAA0B,CAAC/H,CAA3B,GAA+BqI,MAAM,CAACrI,CAAtC,GAA0C,KAAKyE,iBAAL,CAAuBzE,CAArF;QACAwI,eAAe,CAACvI,CAAhB,GAAoB8H,0BAA0B,CAAC9H,CAA3B,GAA+BoI,MAAM,CAACpI,CAAtC,GAA0C,KAAKwE,iBAAL,CAAuBxE,CAArF;;QACA,KAAKwI,0BAAL,CAAgCD,eAAe,CAACxI,CAAhD,EAAmDwI,eAAe,CAACvI,CAAnE;MACH,CAjD0B,CAkD3B;MACA;MACA;;;MACA,IAAI,KAAK2E,WAAL,CAAiB8D,SAAjB,CAA2B1F,MAA/B,EAAuC;QACnC,KAAKsB,OAAL,CAAauD,GAAb,CAAiB,MAAM;UACnB,KAAKjD,WAAL,CAAiBsB,IAAjB,CAAsB;YAClBnJ,MAAM,EAAE,IADU;YAElB0J,eAAe,EAAEsB,0BAFC;YAGlBrG,KAHkB;YAIlBiH,QAAQ,EAAE,KAAKC,gBAAL,CAAsBb,0BAAtB,CAJQ;YAKlBc,KAAK,EAAE,KAAKC;UALM,CAAtB;QAOH,CARD;MASH;IACJ,CAhED;IAiEA;;;IACA,KAAKC,UAAL,GAAmBrH,KAAD,IAAW;MACzB,KAAK+F,gBAAL,CAAsB/F,KAAtB;IACH,CAFD;IAGA;;;IACA,KAAKsH,gBAAL,GAAyBtH,KAAD,IAAW;MAC/B,IAAI,KAAK0D,QAAL,CAAcpC,MAAlB,EAA0B;QACtB,MAAMmD,YAAY,GAAG,KAAKC,gBAAL,CAAsB1E,KAAtB,CAArB;;QACA,IAAIyE,YAAY,IAAI,CAAC,KAAKd,gBAAL,CAAsBhI,GAAtB,CAA0B8I,YAA1B,CAAjB,IAA4D,CAAC,KAAKE,QAAtE,EAAgF;UAC5E3E,KAAK,CAACkG,cAAN;QACH;MACJ,CALD,MAMK,IAAI,CAAC,KAAKvB,QAAV,EAAoB;QACrB;QACA;QACA3E,KAAK,CAACkG,cAAN;MACH;IACJ,CAZD;;IAaA,KAAKqB,eAAL,CAAqBzL,OAArB,EAA8B0L,UAA9B,CAAyC7E,OAAO,CAAC8E,aAAR,IAAyB,IAAlE;IACA,KAAKC,gBAAL,GAAwB,IAAIzI,qBAAJ,CAA0BE,SAA1B,CAAxB;;IACA2D,iBAAiB,CAAC6E,gBAAlB,CAAmC,IAAnC;EACH;EACD;;;EACY,IAARhD,QAAQ,GAAG;IACX,OAAO,KAAKb,SAAL,IAAkB,CAAC,EAAE,KAAKgC,cAAL,IAAuB,KAAKA,cAAL,CAAoBnB,QAA7C,CAA1B;EACH;;EACW,IAARA,QAAQ,CAAClJ,KAAD,EAAQ;IAChB,MAAMmM,QAAQ,GAAG9N,qBAAqB,CAAC2B,KAAD,CAAtC;;IACA,IAAImM,QAAQ,KAAK,KAAK9D,SAAtB,EAAiC;MAC7B,KAAKA,SAAL,GAAiB8D,QAAjB;;MACA,KAAKC,6BAAL;;MACA,KAAKnE,QAAL,CAAc9D,OAAd,CAAsBkI,MAAM,IAAIjM,4BAA4B,CAACiM,MAAD,EAASF,QAAT,CAA5D;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIG,qBAAqB,GAAG;IACpB,OAAO,KAAKC,YAAZ;EACH;EACD;;;EACAC,cAAc,GAAG;IACb,OAAO,KAAKpD,YAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACIqD,iBAAiB,GAAG;IAChB,OAAO,KAAKlC,UAAL,KAAoB,KAAK+B,qBAAL,EAApB,GAAmD,KAAKE,cAAL,EAA1D;EACH;EACD;;;EACAE,WAAW,CAACC,OAAD,EAAU;IACjB,KAAK1E,QAAL,GAAgB0E,OAAO,CAACxN,GAAR,CAAYkN,MAAM,IAAI/N,aAAa,CAAC+N,MAAD,CAAnC,CAAhB;;IACA,KAAKpE,QAAL,CAAc9D,OAAd,CAAsBkI,MAAM,IAAIjM,4BAA4B,CAACiM,MAAD,EAAS,KAAKnD,QAAd,CAA5D;;IACA,KAAKkD,6BAAL,GAHiB,CAIjB;IACA;IACA;IACA;;;IACA,MAAMQ,eAAe,GAAG,IAAI5F,GAAJ,EAAxB;;IACA,KAAKkB,gBAAL,CAAsB/D,OAAtB,CAA8BkI,MAAM,IAAI;MACpC,IAAI,KAAKpE,QAAL,CAAc7G,OAAd,CAAsBiL,MAAtB,IAAgC,CAAC,CAArC,EAAwC;QACpCO,eAAe,CAACC,GAAhB,CAAoBR,MAApB;MACH;IACJ,CAJD;;IAKA,KAAKnE,gBAAL,GAAwB0E,eAAxB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIE,mBAAmB,CAACC,QAAD,EAAW;IAC1B,KAAKC,gBAAL,GAAwBD,QAAxB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIE,uBAAuB,CAACF,QAAD,EAAW;IAC9B,KAAKG,oBAAL,GAA4BH,QAA5B;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIjB,eAAe,CAACqB,WAAD,EAAc;IACzB,MAAM9M,OAAO,GAAG/B,aAAa,CAAC6O,WAAD,CAA7B;;IACA,IAAI9M,OAAO,KAAK,KAAK+I,YAArB,EAAmC;MAC/B,IAAI,KAAKA,YAAT,EAAuB;QACnB,KAAKgE,2BAAL,CAAiC,KAAKhE,YAAtC;MACH;;MACD,KAAKjC,OAAL,CAAakG,iBAAb,CAA+B,MAAM;QACjChN,OAAO,CAACiN,gBAAR,CAAyB,WAAzB,EAAsC,KAAKxE,YAA3C,EAAyDjC,0BAAzD;QACAxG,OAAO,CAACiN,gBAAR,CAAyB,YAAzB,EAAuC,KAAKxE,YAA5C,EAA0DnC,2BAA1D;QACAtG,OAAO,CAACiN,gBAAR,CAAyB,WAAzB,EAAsC,KAAKzB,gBAA3C,EAA6DhF,0BAA7D;MACH,CAJD;;MAKA,KAAK0G,iBAAL,GAAyBC,SAAzB;MACA,KAAKpE,YAAL,GAAoB/I,OAApB;IACH;;IACD,IAAI,OAAOoN,UAAP,KAAsB,WAAtB,IAAqC,KAAKrE,YAAL,YAA6BqE,UAAtE,EAAkF;MAC9E,KAAKC,gBAAL,GAAwB,KAAKtE,YAAL,CAAkBuE,eAA1C;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;;;EACIC,mBAAmB,CAACC,eAAD,EAAkB;IACjC,KAAK9F,gBAAL,GAAwB8F,eAAe,GAAGvP,aAAa,CAACuP,eAAD,CAAhB,GAAoC,IAA3E;;IACA,KAAK/F,mBAAL,CAAyBgG,WAAzB;;IACA,IAAID,eAAJ,EAAqB;MACjB,KAAK/F,mBAAL,GAA2B,KAAKV,cAAL,CACtB2G,MADsB,CACf,EADe,EAEtBC,SAFsB,CAEZ,MAAM,KAAKC,8BAAL,EAFM,CAA3B;IAGH;;IACD,OAAO,IAAP;EACH;EACD;;;EACAlC,UAAU,CAACmC,MAAD,EAAS;IACf,KAAKC,cAAL,GAAsBD,MAAtB;IACA,OAAO,IAAP;EACH;EACD;;;EACAE,OAAO,GAAG;IACN,IAAI1H,EAAJ,EAAQ2H,EAAR;;IACA,KAAKjB,2BAAL,CAAiC,KAAKhE,YAAtC,EAFM,CAGN;IACA;;;IACA,IAAI,KAAKmB,UAAL,EAAJ,EAAuB;MACnB;MACA;MACA,CAAC7D,EAAE,GAAG,KAAK0C,YAAX,MAA6B,IAA7B,IAAqC1C,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAAC4H,MAAH,EAA9D;IACH;;IACD,CAACD,EAAE,GAAG,KAAKE,OAAX,MAAwB,IAAxB,IAAgCF,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACC,MAAH,EAAzD;;IACA,KAAKE,eAAL;;IACA,KAAKC,mBAAL;;IACA,KAAKpH,iBAAL,CAAuBqH,cAAvB,CAAsC,IAAtC;;IACA,KAAKC,oBAAL;;IACA,KAAKrG,aAAL,CAAmBsG,QAAnB;IACA,KAAKrG,OAAL,CAAaqG,QAAb;IACA,KAAKpG,QAAL,CAAcoG,QAAd;IACA,KAAKnG,KAAL,CAAWmG,QAAX;IACA,KAAKlG,OAAL,CAAakG,QAAb;IACA,KAAKjG,MAAL,CAAYiG,QAAZ;IACA,KAAKhG,OAAL,CAAagG,QAAb;;IACA,KAAKnH,WAAL,CAAiBmH,QAAjB;;IACA,KAAK3G,QAAL,GAAgB,EAAhB;;IACA,KAAKC,gBAAL,CAAsBrE,KAAtB;;IACA,KAAKwG,cAAL,GAAsBmD,SAAtB;;IACA,KAAK1F,mBAAL,CAAyBgG,WAAzB;;IACA,KAAK7B,gBAAL,CAAsBpI,KAAtB;;IACA,KAAKkE,gBAAL,GACI,KAAKqB,YAAL,GACI,KAAKsE,gBAAL,GACI,KAAKR,oBAAL,GACI,KAAKF,gBAAL,GACI,KAAKuB,OAAL,GACI,KAAKJ,cAAL,GACI,IAP5B;EAQH;EACD;;;EACA5D,UAAU,GAAG;IACT,OAAO,KAAK/C,mBAAL,IAA4B,KAAKH,iBAAL,CAAuBkD,UAAvB,CAAkC,IAAlC,CAAnC;EACH;EACD;;;EACAsE,KAAK,GAAG;IACJ,KAAKzF,YAAL,CAAkB5I,KAAlB,CAAwBO,SAAxB,GAAoC,KAAKwM,iBAAL,IAA0B,EAA9D;IACA,KAAKhG,gBAAL,GAAwB;MAAE1E,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAxB;IACA,KAAKwE,iBAAL,GAAyB;MAAEzE,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAzB;EACH;EACD;AACJ;AACA;AACA;;;EACIgM,aAAa,CAACzC,MAAD,EAAS;IAClB,IAAI,CAAC,KAAKnE,gBAAL,CAAsBhI,GAAtB,CAA0BmM,MAA1B,CAAD,IAAsC,KAAKpE,QAAL,CAAc7G,OAAd,CAAsBiL,MAAtB,IAAgC,CAAC,CAA3E,EAA8E;MAC1E,KAAKnE,gBAAL,CAAsB2E,GAAtB,CAA0BR,MAA1B;;MACAjM,4BAA4B,CAACiM,MAAD,EAAS,IAAT,CAA5B;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACI0C,YAAY,CAAC1C,MAAD,EAAS;IACjB,IAAI,KAAKnE,gBAAL,CAAsBhI,GAAtB,CAA0BmM,MAA1B,CAAJ,EAAuC;MACnC,KAAKnE,gBAAL,CAAsB8G,MAAtB,CAA6B3C,MAA7B;;MACAjM,4BAA4B,CAACiM,MAAD,EAAS,KAAKnD,QAAd,CAA5B;IACH;EACJ;EACD;;;EACA+F,aAAa,CAACC,SAAD,EAAY;IACrB,KAAK/G,UAAL,GAAkB+G,SAAlB;IACA,OAAO,IAAP;EACH;EACD;;;EACAC,kBAAkB,CAAC/E,SAAD,EAAY;IAC1B,KAAKC,cAAL,GAAsBD,SAAtB;EACH;EACD;AACJ;AACA;;;EACIgF,mBAAmB,GAAG;IAClB,MAAM1O,QAAQ,GAAG,KAAK6J,UAAL,KAAoB,KAAKhD,gBAAzB,GAA4C,KAAKD,iBAAlE;IACA,OAAO;MAAEzE,CAAC,EAAEnC,QAAQ,CAACmC,CAAd;MAAiBC,CAAC,EAAEpC,QAAQ,CAACoC;IAA7B,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIuM,mBAAmB,CAACrP,KAAD,EAAQ;IACvB,KAAKuH,gBAAL,GAAwB;MAAE1E,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAxB;IACA,KAAKwE,iBAAL,CAAuBzE,CAAvB,GAA2B7C,KAAK,CAAC6C,CAAjC;IACA,KAAKyE,iBAAL,CAAuBxE,CAAvB,GAA2B9C,KAAK,CAAC8C,CAAjC;;IACA,IAAI,CAAC,KAAKuH,cAAV,EAA0B;MACtB,KAAKiB,0BAAL,CAAgCtL,KAAK,CAAC6C,CAAtC,EAAyC7C,KAAK,CAAC8C,CAA/C;IACH;;IACD,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIwM,oBAAoB,CAACtP,KAAD,EAAQ;IACxB,KAAKuP,iBAAL,GAAyBvP,KAAzB;IACA,OAAO,IAAP;EACH;EACD;;;EACAwP,4BAA4B,GAAG;IAC3B,MAAM9O,QAAQ,GAAG,KAAKqK,yBAAtB;;IACA,IAAIrK,QAAQ,IAAI,KAAK2J,cAArB,EAAqC;MACjC,KAAKY,0BAAL,CAAgC,KAAKJ,8BAAL,CAAoCnK,QAApC,CAAhC,EAA+EA,QAA/E;IACH;EACJ;EACD;;;EACAiO,oBAAoB,GAAG;IACnB,KAAKjH,wBAAL,CAA8BoG,WAA9B;;IACA,KAAKlG,sBAAL,CAA4BkG,WAA5B;;IACA,KAAKjG,mBAAL,CAAyBiG,WAAzB;EACH;EACD;;;EACAU,eAAe,GAAG;IACd,IAAI9H,EAAJ,EAAQ2H,EAAR;;IACA,CAAC3H,EAAE,GAAG,KAAK+I,QAAX,MAAyB,IAAzB,IAAiC/I,EAAE,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,EAAE,CAAC4H,MAAH,EAA1D;IACA,CAACD,EAAE,GAAG,KAAKqB,WAAX,MAA4B,IAA5B,IAAoCrB,EAAE,KAAK,KAAK,CAAhD,GAAoD,KAAK,CAAzD,GAA6DA,EAAE,CAACsB,OAAH,EAA7D;IACA,KAAKF,QAAL,GAAgB,KAAKC,WAAL,GAAmB,IAAnC;EACH;EACD;;;EACAjB,mBAAmB,GAAG;IAClB,IAAI/H,EAAJ,EAAQ2H,EAAR;;IACA,CAAC3H,EAAE,GAAG,KAAK6F,YAAX,MAA6B,IAA7B,IAAqC7F,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAAC4H,MAAH,EAA9D;IACA,CAACD,EAAE,GAAG,KAAKuB,eAAX,MAAgC,IAAhC,IAAwCvB,EAAE,KAAK,KAAK,CAApD,GAAwD,KAAK,CAA7D,GAAiEA,EAAE,CAACsB,OAAH,EAAjE;IACA,KAAKpD,YAAL,GAAoB,KAAKqD,eAAL,GAAuB,IAA3C;EACH;EACD;AACJ;AACA;AACA;;;EACItF,gBAAgB,CAAC/F,KAAD,EAAQ;IACpB;IACA;IACA;IACA;IACA,IAAI,CAAC,KAAK8C,iBAAL,CAAuBkD,UAAvB,CAAkC,IAAlC,CAAL,EAA8C;MAC1C;IACH;;IACD,KAAKoE,oBAAL;;IACA,KAAKtH,iBAAL,CAAuBwI,YAAvB,CAAoC,IAApC;;IACA,KAAKzD,6BAAL;;IACA,IAAI,KAAKnE,QAAT,EAAmB;MACf,KAAKmB,YAAL,CAAkB5I,KAAlB,CAAwBsP,uBAAxB,GACI,KAAKC,wBADT;IAEH;;IACD,IAAI,CAAC,KAAKvI,mBAAV,EAA+B;MAC3B;IACH;;IACD,KAAKgB,QAAL,CAAcO,IAAd,CAAmB;MAAEnJ,MAAM,EAAE,IAAV;MAAgB2E;IAAhB,CAAnB;;IACA,IAAI,KAAK8F,cAAT,EAAyB;MACrB;MACA,KAAKA,cAAL,CAAoB2F,cAApB;;MACA,KAAKC,4BAAL,GAAoCC,IAApC,CAAyC,MAAM;QAC3C,KAAKC,qBAAL,CAA2B5L,KAA3B;;QACA,KAAK6L,wBAAL;;QACA,KAAK/I,iBAAL,CAAuBwI,YAAvB,CAAoC,IAApC;MACH,CAJD;IAKH,CARD,MASK;MACD;MACA;MACA;MACA,KAAKvI,iBAAL,CAAuBzE,CAAvB,GAA2B,KAAK0E,gBAAL,CAAsB1E,CAAjD;;MACA,MAAMyG,eAAe,GAAG,KAAKC,yBAAL,CAA+BhF,KAA/B,CAAxB;;MACA,KAAK+C,iBAAL,CAAuBxE,CAAvB,GAA2B,KAAKyE,gBAAL,CAAsBzE,CAAjD;;MACA,KAAKqE,OAAL,CAAauD,GAAb,CAAiB,MAAM;QACnB,KAAKjC,KAAL,CAAWM,IAAX,CAAgB;UACZnJ,MAAM,EAAE,IADI;UAEZ4L,QAAQ,EAAE,KAAKC,gBAAL,CAAsBnC,eAAtB,CAFE;UAGZ+G,SAAS,EAAE/G,eAHC;UAIZ/E;QAJY,CAAhB;MAMH,CAPD;;MAQA,KAAK6L,wBAAL;;MACA,KAAK/I,iBAAL,CAAuBwI,YAAvB,CAAoC,IAApC;IACH;EACJ;EACD;;;EACAlF,kBAAkB,CAACpG,KAAD,EAAQ;IACtB,IAAI+L,YAAY,CAAC/L,KAAD,CAAhB,EAAyB;MACrB,KAAKgM,mBAAL,GAA2BvG,IAAI,CAACC,GAAL,EAA3B;IACH;;IACD,KAAKmC,6BAAL;;IACA,MAAMoE,aAAa,GAAG,KAAKnG,cAA3B;;IACA,IAAImG,aAAJ,EAAmB;MACf,MAAMnQ,OAAO,GAAG,KAAK+I,YAArB;MACA,MAAM8E,MAAM,GAAG7N,OAAO,CAACoQ,UAAvB;;MACA,MAAMC,WAAW,GAAI,KAAKnE,YAAL,GAAoB,KAAKoE,yBAAL,EAAzC;;MACA,MAAMC,MAAM,GAAI,KAAKrC,OAAL,GAAe,KAAKA,OAAL,IAAgB,KAAK7K,SAAL,CAAemN,aAAf,CAA6B,EAA7B,CAA/C,CAJe,CAKf;;;MACA,MAAMC,UAAU,GAAG,KAAK1S,cAAL,EAAnB,CANe,CAOf;;;MACA8P,MAAM,CAAC6C,YAAP,CAAoBH,MAApB,EAA4BvQ,OAA5B,EARe,CASf;MACA;;MACA,KAAKkN,iBAAL,GAAyBlN,OAAO,CAACG,KAAR,CAAcO,SAAd,IAA2B,EAApD,CAXe,CAYf;MACA;;MACA,KAAK0O,QAAL,GAAgB,KAAKuB,qBAAL,EAAhB,CAde,CAef;MACA;MACA;;MACAvQ,gBAAgB,CAACJ,OAAD,EAAU,KAAV,EAAiB0G,uBAAjB,CAAhB;;MACA,KAAKrD,SAAL,CAAeuN,IAAf,CAAoBC,WAApB,CAAgChD,MAAM,CAACiD,YAAP,CAAoBT,WAApB,EAAiCrQ,OAAjC,CAAhC;;MACA,KAAK+Q,yBAAL,CAA+BlD,MAA/B,EAAuC4C,UAAvC,EAAmDI,WAAnD,CAA+D,KAAKzB,QAApE;;MACA,KAAKlH,OAAL,CAAaQ,IAAb,CAAkB;QAAEnJ,MAAM,EAAE,IAAV;QAAgB2E;MAAhB,CAAlB,EArBe,CAqB6B;;MAC5CiM,aAAa,CAACa,KAAd;MACA,KAAKC,iBAAL,GAAyBd,aAAzB;MACA,KAAKe,aAAL,GAAqBf,aAAa,CAACgB,YAAd,CAA2B,IAA3B,CAArB;IACH,CAzBD,MA0BK;MACD,KAAKjJ,OAAL,CAAaQ,IAAb,CAAkB;QAAEnJ,MAAM,EAAE,IAAV;QAAgB2E;MAAhB,CAAlB;MACA,KAAK+M,iBAAL,GAAyB,KAAKC,aAAL,GAAqB/D,SAA9C;IACH,CAnCqB,CAoCtB;IACA;;;IACA,KAAKvB,gBAAL,CAAsBnI,KAAtB,CAA4B0M,aAAa,GAAGA,aAAa,CAACiB,oBAAd,EAAH,GAA0C,EAAnF;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACItI,uBAAuB,CAACuI,gBAAD,EAAmBnN,KAAnB,EAA0B;IAC7C;IACA;IACA,IAAI,KAAK4J,cAAT,EAAyB;MACrB5J,KAAK,CAACoN,eAAN;IACH;;IACD,MAAMpH,UAAU,GAAG,KAAKA,UAAL,EAAnB;IACA,MAAMqH,eAAe,GAAGtB,YAAY,CAAC/L,KAAD,CAApC;IACA,MAAMsN,sBAAsB,GAAG,CAACD,eAAD,IAAoBrN,KAAK,CAACuN,MAAN,KAAiB,CAApE;IACA,MAAM3E,WAAW,GAAG,KAAK/D,YAAzB;;IACA,MAAM5E,MAAM,GAAGtG,eAAe,CAACqG,KAAD,CAA9B;;IACA,MAAMwN,gBAAgB,GAAG,CAACH,eAAD,IACrB,KAAKrB,mBADgB,IAErB,KAAKA,mBAAL,GAA2BzJ,uBAA3B,GAAqDkD,IAAI,CAACC,GAAL,EAFzD;IAGA,MAAM+H,WAAW,GAAGJ,eAAe,GAC7BnT,gCAAgC,CAAC8F,KAAD,CADH,GAE7B7F,+BAA+B,CAAC6F,KAAD,CAFrC,CAd6C,CAiB7C;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIC,MAAM,IAAIA,MAAM,CAACyN,SAAjB,IAA8B1N,KAAK,CAAC+B,IAAN,KAAe,WAAjD,EAA8D;MAC1D/B,KAAK,CAACkG,cAAN;IACH,CAzB4C,CA0B7C;;;IACA,IAAIF,UAAU,IAAIsH,sBAAd,IAAwCE,gBAAxC,IAA4DC,WAAhE,EAA6E;MACzE;IACH,CA7B4C,CA8B7C;IACA;IACA;;;IACA,IAAI,KAAK/J,QAAL,CAAcpC,MAAlB,EAA0B;MACtB,MAAMqM,UAAU,GAAG/E,WAAW,CAAC3M,KAA/B;MACA,KAAKuP,wBAAL,GAAgCmC,UAAU,CAACpC,uBAAX,IAAsC,EAAtE;MACAoC,UAAU,CAACpC,uBAAX,GAAqC,aAArC;IACH;;IACD,KAAKtI,mBAAL,GAA2B,KAAKsD,SAAL,GAAiB,KAA5C,CAtC6C,CAuC7C;IACA;;IACA,KAAK6D,oBAAL;;IACA,KAAKvD,kBAAL,GAA0B,KAAKhC,YAAL,CAAkB5G,qBAAlB,EAA1B;IACA,KAAKkF,wBAAL,GAAgC,KAAKL,iBAAL,CAAuB8K,WAAvB,CAAmCnE,SAAnC,CAA6C,KAAK3E,YAAlD,CAAhC;IACA,KAAKzB,sBAAL,GAA8B,KAAKP,iBAAL,CAAuB+K,SAAvB,CAAiCpE,SAAjC,CAA2C,KAAKpC,UAAhD,CAA9B;IACA,KAAK/D,mBAAL,GAA2B,KAAKR,iBAAL,CACtBgL,QADsB,CACb,KAAKjU,cAAL,EADa,EAEtB4P,SAFsB,CAEZsE,WAAW,IAAI,KAAKC,eAAL,CAAqBD,WAArB,CAFH,CAA3B;;IAGA,IAAI,KAAKvK,gBAAT,EAA2B;MACvB,KAAKyK,aAAL,GAAqBlQ,oBAAoB,CAAC,KAAKyF,gBAAN,CAAzC;IACH,CAlD4C,CAmD7C;IACA;IACA;;;IACA,MAAM0K,eAAe,GAAG,KAAKzF,gBAA7B;IACA,KAAK0F,wBAAL,GACID,eAAe,IAAIA,eAAe,CAAC1F,QAAnC,IAA+C,CAAC0F,eAAe,CAACE,SAAhE,GACM;MAAE9P,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CADN,GAEM,KAAK8P,4BAAL,CAAkC,KAAKxH,kBAAvC,EAA2DsG,gBAA3D,EAA6EnN,KAA7E,CAHV;;IAIA,MAAM+E,eAAe,GAAI,KAAKK,qBAAL,GACrB,KAAKoB,yBAAL,GACI,KAAKxB,yBAAL,CAA+BhF,KAA/B,CAFR;;IAGA,KAAKoH,sBAAL,GAA8B;MAAE9I,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAA9B;IACA,KAAK+P,qCAAL,GAA6C;MAAEhQ,CAAC,EAAEyG,eAAe,CAACzG,CAArB;MAAwBC,CAAC,EAAEwG,eAAe,CAACxG;IAA3C,CAA7C;IACA,KAAKoH,cAAL,GAAsBF,IAAI,CAACC,GAAL,EAAtB;;IACA,KAAK5C,iBAAL,CAAuByL,aAAvB,CAAqC,IAArC,EAA2CvO,KAA3C;EACH;EACD;;;EACA4L,qBAAqB,CAAC5L,KAAD,EAAQ;IACzB;IACA;IACA;IACA;IACA9D,gBAAgB,CAAC,KAAK2I,YAAN,EAAoB,IAApB,EAA0BrC,uBAA1B,CAAhB;;IACA,KAAKwH,OAAL,CAAakC,UAAb,CAAwBU,YAAxB,CAAqC,KAAK/H,YAA1C,EAAwD,KAAKmF,OAA7D;;IACA,KAAKC,eAAL;;IACA,KAAKC,mBAAL;;IACA,KAAKrD,kBAAL,GACI,KAAKoH,aAAL,GACI,KAAKO,YAAL,GACI,KAAKxF,iBAAL,GACIC,SAJhB,CATyB,CAczB;;IACA,KAAKrG,OAAL,CAAauD,GAAb,CAAiB,MAAM;MACnB,MAAMN,SAAS,GAAG,KAAKC,cAAvB;MACA,MAAM2I,YAAY,GAAG5I,SAAS,CAACoH,YAAV,CAAuB,IAAvB,CAArB;;MACA,MAAMlI,eAAe,GAAG,KAAKC,yBAAL,CAA+BhF,KAA/B,CAAxB;;MACA,MAAMiH,QAAQ,GAAG,KAAKC,gBAAL,CAAsBnC,eAAtB,CAAjB;;MACA,MAAM2J,sBAAsB,GAAG7I,SAAS,CAAC8I,gBAAV,CAA2B5J,eAAe,CAACzG,CAA3C,EAA8CyG,eAAe,CAACxG,CAA9D,CAA/B;;MACA,KAAK2F,KAAL,CAAWM,IAAX,CAAgB;QAAEnJ,MAAM,EAAE,IAAV;QAAgB4L,QAAhB;QAA0B6E,SAAS,EAAE/G,eAArC;QAAsD/E;MAAtD,CAAhB;MACA,KAAKqE,OAAL,CAAaG,IAAb,CAAkB;QACdoK,IAAI,EAAE,IADQ;QAEdH,YAFc;QAGdI,aAAa,EAAE,KAAK7B,aAHN;QAIdnH,SAAS,EAAEA,SAJG;QAKdiJ,iBAAiB,EAAE,KAAK/B,iBALV;QAMd2B,sBANc;QAOdzH,QAPc;QAQd6E,SAAS,EAAE/G,eARG;QASd/E;MATc,CAAlB;MAWA6F,SAAS,CAACkJ,IAAV,CAAe,IAAf,EAAqBN,YAArB,EAAmC,KAAKzB,aAAxC,EAAuD,KAAKD,iBAA5D,EAA+E2B,sBAA/E,EAAuGzH,QAAvG,EAAiHlC,eAAjH;MACA,KAAKe,cAAL,GAAsB,KAAKiH,iBAA3B;IACH,CApBD;EAqBH;EACD;AACJ;AACA;AACA;;;EACIrG,0BAA0B,CAAC;IAAEpI,CAAF;IAAKC;EAAL,CAAD,EAAW;IAAED,CAAC,EAAE0Q,IAAL;IAAWzQ,CAAC,EAAE0Q;EAAd,CAAX,EAAiC;IACvD;IACA,IAAIC,YAAY,GAAG,KAAKnC,iBAAL,CAAuBoC,gCAAvB,CAAwD,IAAxD,EAA8D7Q,CAA9D,EAAiEC,CAAjE,CAAnB,CAFuD,CAGvD;IACA;IACA;IACA;;;IACA,IAAI,CAAC2Q,YAAD,IACA,KAAKpJ,cAAL,KAAwB,KAAKiH,iBAD7B,IAEA,KAAKA,iBAAL,CAAuB4B,gBAAvB,CAAwCrQ,CAAxC,EAA2CC,CAA3C,CAFJ,EAEmD;MAC/C2Q,YAAY,GAAG,KAAKnC,iBAApB;IACH;;IACD,IAAImC,YAAY,IAAIA,YAAY,KAAK,KAAKpJ,cAA1C,EAA0D;MACtD,KAAKlD,OAAL,CAAauD,GAAb,CAAiB,MAAM;QACnB;QACA,KAAK/B,MAAL,CAAYI,IAAZ,CAAiB;UAAEoK,IAAI,EAAE,IAAR;UAAc/I,SAAS,EAAE,KAAKC;QAA9B,CAAjB;;QACA,KAAKA,cAAL,CAAoBsJ,IAApB,CAAyB,IAAzB,EAHmB,CAInB;;;QACA,KAAKtJ,cAAL,GAAsBoJ,YAAtB;;QACA,KAAKpJ,cAAL,CAAoBuJ,KAApB,CAA0B,IAA1B,EAAgC/Q,CAAhC,EAAmCC,CAAnC,EAAsC2Q,YAAY,KAAK,KAAKnC,iBAAtB,IAClC;QACA;QACAmC,YAAY,CAACI,eAHqB,GAIhC,KAAKtC,aAJ2B,GAKhC/D,SALN;;QAMA,KAAK9E,OAAL,CAAaK,IAAb,CAAkB;UACdoK,IAAI,EAAE,IADQ;UAEd/I,SAAS,EAAEqJ,YAFG;UAGdT,YAAY,EAAES,YAAY,CAACjC,YAAb,CAA0B,IAA1B;QAHA,CAAlB;MAKH,CAjBD;IAkBH,CA/BsD,CAgCvD;;;IACA,IAAI,KAAKjH,UAAL,EAAJ,EAAuB;MACnB,KAAKF,cAAL,CAAoByJ,0BAApB,CAA+CP,IAA/C,EAAqDC,IAArD;;MACA,KAAKnJ,cAAL,CAAoB0J,SAApB,CAA8B,IAA9B,EAAoClR,CAApC,EAAuCC,CAAvC,EAA0C,KAAK6I,sBAA/C;;MACA,IAAI,KAAKR,iBAAT,EAA4B;QACxB,KAAK6I,sBAAL,CAA4BnR,CAA5B,EAA+BC,CAA/B;MACH,CAFD,MAGK;QACD,KAAKkR,sBAAL,CAA4BnR,CAAC,GAAG,KAAK6P,wBAAL,CAA8B7P,CAA9D,EAAiEC,CAAC,GAAG,KAAK4P,wBAAL,CAA8B5P,CAAnG;MACH;IACJ;EACJ;EACD;AACJ;AACA;AACA;;;EACIkO,qBAAqB,GAAG;IACpB,MAAMiD,aAAa,GAAG,KAAKjH,gBAA3B;IACA,MAAMkH,YAAY,GAAG,KAAKA,YAA1B;IACA,MAAMzB,eAAe,GAAGwB,aAAa,GAAGA,aAAa,CAAClH,QAAjB,GAA4B,IAAjE;IACA,IAAIoH,OAAJ;;IACA,IAAI1B,eAAe,IAAIwB,aAAvB,EAAsC;MAClC;MACA;MACA,MAAMG,QAAQ,GAAGH,aAAa,CAACtB,SAAd,GAA0B,KAAKvH,kBAA/B,GAAoD,IAArE;MACA,MAAMiJ,OAAO,GAAGJ,aAAa,CAACK,aAAd,CAA4BC,kBAA5B,CAA+C9B,eAA/C,EAAgEwB,aAAa,CAAC1N,OAA9E,CAAhB;MACA8N,OAAO,CAACG,aAAR;MACAL,OAAO,GAAGM,WAAW,CAACJ,OAAD,EAAU,KAAK3Q,SAAf,CAArB;MACA,KAAKgM,WAAL,GAAmB2E,OAAnB;;MACA,IAAIJ,aAAa,CAACtB,SAAlB,EAA6B;QACzB+B,gBAAgB,CAACP,OAAD,EAAUC,QAAV,CAAhB;MACH,CAFD,MAGK;QACDD,OAAO,CAAC3T,KAAR,CAAcO,SAAd,GAA0B4T,YAAY,CAAC,KAAKhL,qBAAL,CAA2B9G,CAA5B,EAA+B,KAAK8G,qBAAL,CAA2B7G,CAA1D,CAAtC;MACH;IACJ,CAdD,MAeK;MACDqR,OAAO,GAAG9O,aAAa,CAAC,KAAK+D,YAAN,CAAvB;MACAsL,gBAAgB,CAACP,OAAD,EAAU,KAAK/I,kBAAf,CAAhB;;MACA,IAAI,KAAKmC,iBAAT,EAA4B;QACxB4G,OAAO,CAAC3T,KAAR,CAAcO,SAAd,GAA0B,KAAKwM,iBAA/B;MACH;IACJ;;IACD7N,YAAY,CAACyU,OAAO,CAAC3T,KAAT,EAAgB;MACxB;MACA;MACA,kBAAkB,MAHM;MAIxB;MACA,UAAU,GALc;MAMxB,YAAY,OANY;MAOxB,OAAO,GAPiB;MAQxB,QAAQ,GARgB;MASxB,WAAY,GAAE,KAAK0G,OAAL,CAAa0N,MAAb,IAAuB,IAAK;IATlB,CAAhB,EAUT7N,uBAVS,CAAZ;IAWA3G,4BAA4B,CAAC+T,OAAD,EAAU,KAAV,CAA5B;IACAA,OAAO,CAACU,SAAR,CAAkBhI,GAAlB,CAAsB,kBAAtB;IACAsH,OAAO,CAACW,YAAR,CAAqB,KAArB,EAA4B,KAAK3M,UAAjC;;IACA,IAAI+L,YAAJ,EAAkB;MACd,IAAIa,KAAK,CAACC,OAAN,CAAcd,YAAd,CAAJ,EAAiC;QAC7BA,YAAY,CAAC/P,OAAb,CAAqB8Q,SAAS,IAAId,OAAO,CAACU,SAAR,CAAkBhI,GAAlB,CAAsBoI,SAAtB,CAAlC;MACH,CAFD,MAGK;QACDd,OAAO,CAACU,SAAR,CAAkBhI,GAAlB,CAAsBqH,YAAtB;MACH;IACJ;;IACD,OAAOC,OAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIlE,4BAA4B,GAAG;IAC3B;IACA,IAAI,CAAC,KAAKnF,SAAV,EAAqB;MACjB,OAAOoK,OAAO,CAACC,OAAR,EAAP;IACH;;IACD,MAAMC,eAAe,GAAG,KAAK7I,YAAL,CAAkB/J,qBAAlB,EAAxB,CAL2B,CAM3B;;;IACA,KAAKiN,QAAL,CAAcoF,SAAd,CAAwBhI,GAAxB,CAA4B,oBAA5B,EAP2B,CAQ3B;;;IACA,KAAKmH,sBAAL,CAA4BoB,eAAe,CAACvU,IAA5C,EAAkDuU,eAAe,CAACzU,GAAlE,EAT2B,CAU3B;IACA;IACA;IACA;;;IACA,MAAM0U,QAAQ,GAAG/T,kCAAkC,CAAC,KAAKmO,QAAN,CAAnD;;IACA,IAAI4F,QAAQ,KAAK,CAAjB,EAAoB;MAChB,OAAOH,OAAO,CAACC,OAAR,EAAP;IACH;;IACD,OAAO,KAAKhO,OAAL,CAAakG,iBAAb,CAA+B,MAAM;MACxC,OAAO,IAAI6H,OAAJ,CAAYC,OAAO,IAAI;QAC1B,MAAMG,OAAO,GAAK/Q,KAAD,IAAW;UACxB,IAAImC,EAAJ;;UACA,IAAI,CAACnC,KAAD,IACCrG,eAAe,CAACqG,KAAD,CAAf,KAA2B,KAAKkL,QAAhC,IAA4ClL,KAAK,CAACgR,YAAN,KAAuB,WADxE,EACsF;YAClF,CAAC7O,EAAE,GAAG,KAAK+I,QAAX,MAAyB,IAAzB,IAAiC/I,EAAE,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,EAAE,CAAC8O,mBAAH,CAAuB,eAAvB,EAAwCF,OAAxC,CAA1D;YACAH,OAAO;YACPM,YAAY,CAACC,OAAD,CAAZ;UACH;QACJ,CARD,CAD0B,CAU1B;QACA;QACA;;;QACA,MAAMA,OAAO,GAAGC,UAAU,CAACL,OAAD,EAAUD,QAAQ,GAAG,GAArB,CAA1B;;QACA,KAAK5F,QAAL,CAAcnC,gBAAd,CAA+B,eAA/B,EAAgDgI,OAAhD;MACH,CAfM,CAAP;IAgBH,CAjBM,CAAP;EAkBH;EACD;;;EACA3E,yBAAyB,GAAG;IACxB,MAAMiF,iBAAiB,GAAG,KAAK1I,oBAA/B;IACA,MAAM2I,mBAAmB,GAAGD,iBAAiB,GAAGA,iBAAiB,CAAC7I,QAArB,GAAgC,IAA7E;IACA,IAAI2D,WAAJ;;IACA,IAAImF,mBAAJ,EAAyB;MACrB,KAAKjG,eAAL,GAAuBgG,iBAAiB,CAACtB,aAAlB,CAAgCC,kBAAhC,CAAmDsB,mBAAnD,EAAwED,iBAAiB,CAACrP,OAA1F,CAAvB;;MACA,KAAKqJ,eAAL,CAAqB4E,aAArB;;MACA9D,WAAW,GAAG+D,WAAW,CAAC,KAAK7E,eAAN,EAAuB,KAAKlM,SAA5B,CAAzB;IACH,CAJD,MAKK;MACDgN,WAAW,GAAGrL,aAAa,CAAC,KAAK+D,YAAN,CAA3B;IACH,CAXuB,CAYxB;IACA;;;IACAsH,WAAW,CAAClQ,KAAZ,CAAkBsV,aAAlB,GAAkC,MAAlC;IACApF,WAAW,CAACmE,SAAZ,CAAsBhI,GAAtB,CAA0B,sBAA1B;IACA,OAAO6D,WAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIkC,4BAA4B,CAACmD,WAAD,EAAcrE,gBAAd,EAAgCnN,KAAhC,EAAuC;IAC/D,MAAMyR,aAAa,GAAGtE,gBAAgB,KAAK,KAAKtI,YAA1B,GAAyC,IAAzC,GAAgDsI,gBAAtE;IACA,MAAMuE,aAAa,GAAGD,aAAa,GAAGA,aAAa,CAACxT,qBAAd,EAAH,GAA2CuT,WAA9E;IACA,MAAMG,KAAK,GAAG5F,YAAY,CAAC/L,KAAD,CAAZ,GAAsBA,KAAK,CAAC4R,aAAN,CAAoB,CAApB,CAAtB,GAA+C5R,KAA7D;;IACA,MAAMN,cAAc,GAAG,KAAKmS,0BAAL,EAAvB;;IACA,MAAMvT,CAAC,GAAGqT,KAAK,CAACG,KAAN,GAAcJ,aAAa,CAACpV,IAA5B,GAAmCoD,cAAc,CAACpD,IAA5D;IACA,MAAMiC,CAAC,GAAGoT,KAAK,CAACI,KAAN,GAAcL,aAAa,CAACtV,GAA5B,GAAkCsD,cAAc,CAACtD,GAA3D;IACA,OAAO;MACHkC,CAAC,EAAEoT,aAAa,CAACpV,IAAd,GAAqBkV,WAAW,CAAClV,IAAjC,GAAwCgC,CADxC;MAEHC,CAAC,EAAEmT,aAAa,CAACtV,GAAd,GAAoBoV,WAAW,CAACpV,GAAhC,GAAsCmC;IAFtC,CAAP;EAIH;EACD;;;EACAyG,yBAAyB,CAAChF,KAAD,EAAQ;IAC7B,MAAMN,cAAc,GAAG,KAAKmS,0BAAL,EAAvB;;IACA,MAAMF,KAAK,GAAG5F,YAAY,CAAC/L,KAAD,CAAZ,GACR;IACE;IACA;IACA;IACA;IACA;IACA;IACAA,KAAK,CAACgS,OAAN,CAAc,CAAd,KAAoBhS,KAAK,CAACiS,cAAN,CAAqB,CAArB,CAApB,IAA+C;MAAEH,KAAK,EAAE,CAAT;MAAYC,KAAK,EAAE;IAAnB,CARzC,GASR/R,KATN;IAUA,MAAM1B,CAAC,GAAGqT,KAAK,CAACG,KAAN,GAAcpS,cAAc,CAACpD,IAAvC;IACA,MAAMiC,CAAC,GAAGoT,KAAK,CAACI,KAAN,GAAcrS,cAAc,CAACtD,GAAvC,CAb6B,CAc7B;IACA;;IACA,IAAI,KAAK+M,gBAAT,EAA2B;MACvB,MAAM+I,SAAS,GAAG,KAAK/I,gBAAL,CAAsBgJ,YAAtB,EAAlB;;MACA,IAAID,SAAJ,EAAe;QACX,MAAME,QAAQ,GAAG,KAAKjJ,gBAAL,CAAsBkJ,cAAtB,EAAjB;;QACAD,QAAQ,CAAC9T,CAAT,GAAaA,CAAb;QACA8T,QAAQ,CAAC7T,CAAT,GAAaA,CAAb;QACA,OAAO6T,QAAQ,CAACE,eAAT,CAAyBJ,SAAS,CAACK,OAAV,EAAzB,CAAP;MACH;IACJ;;IACD,OAAO;MAAEjU,CAAF;MAAKC;IAAL,CAAP;EACH;EACD;;;EACA+H,8BAA8B,CAACqL,KAAD,EAAQ;IAClC,MAAMa,iBAAiB,GAAG,KAAK1M,cAAL,GAAsB,KAAKA,cAAL,CAAoB2M,QAA1C,GAAqD,IAA/E;IACA,IAAI;MAAEnU,CAAF;MAAKC;IAAL,IAAW,KAAKqI,iBAAL,GACT,KAAKA,iBAAL,CAAuB+K,KAAvB,EAA8B,IAA9B,EAAoC,KAAK9K,kBAAzC,CADS,GAET8K,KAFN;;IAGA,IAAI,KAAKc,QAAL,KAAkB,GAAlB,IAAyBD,iBAAiB,KAAK,GAAnD,EAAwD;MACpDjU,CAAC,GAAG,KAAK6G,qBAAL,CAA2B7G,CAA/B;IACH,CAFD,MAGK,IAAI,KAAKkU,QAAL,KAAkB,GAAlB,IAAyBD,iBAAiB,KAAK,GAAnD,EAAwD;MACzDlU,CAAC,GAAG,KAAK8G,qBAAL,CAA2B9G,CAA/B;IACH;;IACD,IAAI,KAAK2P,aAAT,EAAwB;MACpB,MAAM;QAAE3P,CAAC,EAAEoU,OAAL;QAAcnU,CAAC,EAAEoU;MAAjB,IAA6B,KAAKxE,wBAAxC;MACA,MAAMyE,YAAY,GAAG,KAAK3E,aAA1B;;MACA,MAAM;QAAE7P,KAAK,EAAEyU,YAAT;QAAuBxU,MAAM,EAAEyU;MAA/B,IAAiD,KAAKC,eAAL,EAAvD;;MACA,MAAMC,IAAI,GAAGJ,YAAY,CAACxW,GAAb,GAAmBuW,OAAhC;MACA,MAAMM,IAAI,GAAGL,YAAY,CAACzU,MAAb,IAAuB2U,aAAa,GAAGH,OAAvC,CAAb;MACA,MAAMO,IAAI,GAAGN,YAAY,CAACtW,IAAb,GAAoBoW,OAAjC;MACA,MAAMS,IAAI,GAAGP,YAAY,CAAC1U,KAAb,IAAsB2U,YAAY,GAAGH,OAArC,CAAb;MACApU,CAAC,GAAG8U,OAAO,CAAC9U,CAAD,EAAI4U,IAAJ,EAAUC,IAAV,CAAX;MACA5U,CAAC,GAAG6U,OAAO,CAAC7U,CAAD,EAAIyU,IAAJ,EAAUC,IAAV,CAAX;IACH;;IACD,OAAO;MAAE3U,CAAF;MAAKC;IAAL,CAAP;EACH;EACD;;;EACAkI,4BAA4B,CAAC4M,qBAAD,EAAwB;IAChD,MAAM;MAAE/U,CAAF;MAAKC;IAAL,IAAW8U,qBAAjB;IACA,MAAMlM,KAAK,GAAG,KAAKC,sBAAnB;IACA,MAAMkM,uBAAuB,GAAG,KAAKhF,qCAArC,CAHgD,CAIhD;;IACA,MAAMiF,OAAO,GAAGrO,IAAI,CAACC,GAAL,CAAS7G,CAAC,GAAGgV,uBAAuB,CAAChV,CAArC,CAAhB;IACA,MAAMkV,OAAO,GAAGtO,IAAI,CAACC,GAAL,CAAS5G,CAAC,GAAG+U,uBAAuB,CAAC/U,CAArC,CAAhB,CANgD,CAOhD;IACA;IACA;IACA;;IACA,IAAIgV,OAAO,GAAG,KAAK5Q,OAAL,CAAa8Q,+BAA3B,EAA4D;MACxDtM,KAAK,CAAC7I,CAAN,GAAUA,CAAC,GAAGgV,uBAAuB,CAAChV,CAA5B,GAAgC,CAAhC,GAAoC,CAAC,CAA/C;MACAgV,uBAAuB,CAAChV,CAAxB,GAA4BA,CAA5B;IACH;;IACD,IAAIkV,OAAO,GAAG,KAAK7Q,OAAL,CAAa8Q,+BAA3B,EAA4D;MACxDtM,KAAK,CAAC5I,CAAN,GAAUA,CAAC,GAAG+U,uBAAuB,CAAC/U,CAA5B,GAAgC,CAAhC,GAAoC,CAAC,CAA/C;MACA+U,uBAAuB,CAAC/U,CAAxB,GAA4BA,CAA5B;IACH;;IACD,OAAO4I,KAAP;EACH;EACD;;;EACAU,6BAA6B,GAAG;IAC5B,IAAI,CAAC,KAAKhD,YAAN,IAAsB,CAAC,KAAKnB,QAAhC,EAA0C;MACtC;IACH;;IACD,MAAMgQ,YAAY,GAAG,KAAKhQ,QAAL,CAAcpC,MAAd,GAAuB,CAAvB,IAA4B,CAAC,KAAK0E,UAAL,EAAlD;;IACA,IAAI0N,YAAY,KAAK,KAAKjQ,0BAA1B,EAAsD;MAClD,KAAKA,0BAAL,GAAkCiQ,YAAlC;MACA7X,4BAA4B,CAAC,KAAKgJ,YAAN,EAAoB6O,YAApB,CAA5B;IACH;EACJ;EACD;;;EACA7K,2BAA2B,CAAC/M,OAAD,EAAU;IACjCA,OAAO,CAACmV,mBAAR,CAA4B,WAA5B,EAAyC,KAAK1M,YAA9C,EAA4DjC,0BAA5D;IACAxG,OAAO,CAACmV,mBAAR,CAA4B,YAA5B,EAA0C,KAAK1M,YAA/C,EAA6DnC,2BAA7D;IACAtG,OAAO,CAACmV,mBAAR,CAA4B,WAA5B,EAAyC,KAAK3J,gBAA9C,EAAgEhF,0BAAhE;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIyE,0BAA0B,CAACzI,CAAD,EAAIC,CAAJ,EAAO;IAC7B,MAAM/B,SAAS,GAAG4T,YAAY,CAAC9R,CAAD,EAAIC,CAAJ,CAA9B;IACA,MAAMoV,MAAM,GAAG,KAAK9O,YAAL,CAAkB5I,KAAjC,CAF6B,CAG7B;IACA;IACA;;IACA,IAAI,KAAK+M,iBAAL,IAA0B,IAA9B,EAAoC;MAChC,KAAKA,iBAAL,GACI2K,MAAM,CAACnX,SAAP,IAAoBmX,MAAM,CAACnX,SAAP,IAAoB,MAAxC,GAAiDmX,MAAM,CAACnX,SAAxD,GAAoE,EADxE;IAEH,CAT4B,CAU7B;IACA;IACA;;;IACAmX,MAAM,CAACnX,SAAP,GAAmBD,iBAAiB,CAACC,SAAD,EAAY,KAAKwM,iBAAjB,CAApC;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIyG,sBAAsB,CAACnR,CAAD,EAAIC,CAAJ,EAAO;IACzB,IAAI4D,EAAJ,CADyB,CAEzB;IACA;;;IACA,MAAM1F,gBAAgB,GAAG,CAAC,CAAC0F,EAAE,GAAG,KAAKsG,gBAAX,MAAiC,IAAjC,IAAyCtG,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACqG,QAAtE,IAAkFS,SAAlF,GAA8F,KAAKD,iBAA5H;IACA,MAAMxM,SAAS,GAAG4T,YAAY,CAAC9R,CAAD,EAAIC,CAAJ,CAA9B;IACA,KAAK2M,QAAL,CAAcjP,KAAd,CAAoBO,SAApB,GAAgCD,iBAAiB,CAACC,SAAD,EAAYC,gBAAZ,CAAjD;EACH;EACD;AACJ;AACA;AACA;;;EACIyK,gBAAgB,CAAC0M,eAAD,EAAkB;IAC9B,MAAMC,cAAc,GAAG,KAAKzO,qBAA5B;;IACA,IAAIyO,cAAJ,EAAoB;MAChB,OAAO;QAAEvV,CAAC,EAAEsV,eAAe,CAACtV,CAAhB,GAAoBuV,cAAc,CAACvV,CAAxC;QAA2CC,CAAC,EAAEqV,eAAe,CAACrV,CAAhB,GAAoBsV,cAAc,CAACtV;MAAjF,CAAP;IACH;;IACD,OAAO;MAAED,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE;IAAX,CAAP;EACH;EACD;;;EACAsN,wBAAwB,GAAG;IACvB,KAAKoC,aAAL,GAAqB,KAAKO,YAAL,GAAoBvF,SAAzC;;IACA,KAAKvB,gBAAL,CAAsBpI,KAAtB;EACH;EACD;AACJ;AACA;AACA;;;EACIoK,8BAA8B,GAAG;IAC7B,IAAI;MAAEpL,CAAF;MAAKC;IAAL,IAAW,KAAKwE,iBAApB;;IACA,IAAKzE,CAAC,KAAK,CAAN,IAAWC,CAAC,KAAK,CAAlB,IAAwB,KAAKyH,UAAL,EAAxB,IAA6C,CAAC,KAAKxC,gBAAvD,EAAyE;MACrE;IACH,CAJ4B,CAK7B;;;IACA,MAAMgO,WAAW,GAAG,KAAK3M,YAAL,CAAkB5G,qBAAlB,EAApB;;IACA,MAAM2U,YAAY,GAAG,KAAKpP,gBAAL,CAAsBvF,qBAAtB,EAArB,CAP6B,CAQ7B;IACA;;;IACA,IAAK2U,YAAY,CAACxU,KAAb,KAAuB,CAAvB,IAA4BwU,YAAY,CAACvU,MAAb,KAAwB,CAArD,IACCmT,WAAW,CAACpT,KAAZ,KAAsB,CAAtB,IAA2BoT,WAAW,CAACnT,MAAZ,KAAuB,CADvD,EAC2D;MACvD;IACH;;IACD,MAAMyV,YAAY,GAAGlB,YAAY,CAACtW,IAAb,GAAoBkV,WAAW,CAAClV,IAArD;IACA,MAAMyX,aAAa,GAAGvC,WAAW,CAACtT,KAAZ,GAAoB0U,YAAY,CAAC1U,KAAvD;IACA,MAAM8V,WAAW,GAAGpB,YAAY,CAACxW,GAAb,GAAmBoV,WAAW,CAACpV,GAAnD;IACA,MAAM6X,cAAc,GAAGzC,WAAW,CAACrT,MAAZ,GAAqByU,YAAY,CAACzU,MAAzD,CAjB6B,CAkB7B;IACA;;IACA,IAAIyU,YAAY,CAACxU,KAAb,GAAqBoT,WAAW,CAACpT,KAArC,EAA4C;MACxC,IAAI0V,YAAY,GAAG,CAAnB,EAAsB;QAClBxV,CAAC,IAAIwV,YAAL;MACH;;MACD,IAAIC,aAAa,GAAG,CAApB,EAAuB;QACnBzV,CAAC,IAAIyV,aAAL;MACH;IACJ,CAPD,MAQK;MACDzV,CAAC,GAAG,CAAJ;IACH,CA9B4B,CA+B7B;IACA;;;IACA,IAAIsU,YAAY,CAACvU,MAAb,GAAsBmT,WAAW,CAACnT,MAAtC,EAA8C;MAC1C,IAAI2V,WAAW,GAAG,CAAlB,EAAqB;QACjBzV,CAAC,IAAIyV,WAAL;MACH;;MACD,IAAIC,cAAc,GAAG,CAArB,EAAwB;QACpB1V,CAAC,IAAI0V,cAAL;MACH;IACJ,CAPD,MAQK;MACD1V,CAAC,GAAG,CAAJ;IACH;;IACD,IAAID,CAAC,KAAK,KAAKyE,iBAAL,CAAuBzE,CAA7B,IAAkCC,CAAC,KAAK,KAAKwE,iBAAL,CAAuBxE,CAAnE,EAAsE;MAClE,KAAKuM,mBAAL,CAAyB;QAAEvM,CAAF;QAAKD;MAAL,CAAzB;IACH;EACJ;EACD;;;EACAsH,kBAAkB,CAAC5F,KAAD,EAAQ;IACtB,MAAMvE,KAAK,GAAG,KAAKoI,cAAnB;;IACA,IAAI,OAAOpI,KAAP,KAAiB,QAArB,EAA+B;MAC3B,OAAOA,KAAP;IACH,CAFD,MAGK,IAAIsQ,YAAY,CAAC/L,KAAD,CAAhB,EAAyB;MAC1B,OAAOvE,KAAK,CAACyY,KAAb;IACH;;IACD,OAAOzY,KAAK,GAAGA,KAAK,CAAC0Y,KAAT,GAAiB,CAA7B;EACH;EACD;;;EACAnG,eAAe,CAAChO,KAAD,EAAQ;IACnB,MAAMoU,gBAAgB,GAAG,KAAK1M,gBAAL,CAAsB3H,YAAtB,CAAmCC,KAAnC,CAAzB;;IACA,IAAIoU,gBAAJ,EAAsB;MAClB,MAAMnU,MAAM,GAAGtG,eAAe,CAACqG,KAAD,CAA9B,CADkB,CAElB;MACA;;;MACA,IAAI,KAAKiO,aAAL,IACAhO,MAAM,KAAK,KAAKuD,gBADhB,IAEAvD,MAAM,CAACS,QAAP,CAAgB,KAAK8C,gBAArB,CAFJ,EAE4C;QACxC/E,gBAAgB,CAAC,KAAKwP,aAAN,EAAqBmG,gBAAgB,CAAChY,GAAtC,EAA2CgY,gBAAgB,CAAC9X,IAA5D,CAAhB;MACH;;MACD,KAAK8I,qBAAL,CAA2B9G,CAA3B,IAAgC8V,gBAAgB,CAAC9X,IAAjD;MACA,KAAK8I,qBAAL,CAA2B7G,CAA3B,IAAgC6V,gBAAgB,CAAChY,GAAjD,CAVkB,CAWlB;MACA;;MACA,IAAI,CAAC,KAAK0J,cAAV,EAA0B;QACtB,KAAK9C,gBAAL,CAAsB1E,CAAtB,IAA2B8V,gBAAgB,CAAC9X,IAA5C;QACA,KAAK0G,gBAAL,CAAsBzE,CAAtB,IAA2B6V,gBAAgB,CAAChY,GAA5C;;QACA,KAAK2K,0BAAL,CAAgC,KAAK/D,gBAAL,CAAsB1E,CAAtD,EAAyD,KAAK0E,gBAAL,CAAsBzE,CAA/E;MACH;IACJ;EACJ;EACD;;;EACAsT,0BAA0B,GAAG;IACzB,IAAI1P,EAAJ;;IACA,OAAQ,CAAC,CAACA,EAAE,GAAG,KAAKuF,gBAAL,CAAsBtI,SAAtB,CAAgCe,GAAhC,CAAoC,KAAKhB,SAAzC,CAAN,MAA+D,IAA/D,IAAuEgD,EAAE,KAAK,KAAK,CAAnF,GAAuF,KAAK,CAA5F,GAAgGA,EAAE,CAACzC,cAApG,KACJ,KAAKgI,gBAAL,CAAsB/H,yBAAtB,EADJ;EAEH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACI9F,cAAc,GAAG;IACb,IAAI,KAAKwa,iBAAL,KAA2BpL,SAA/B,EAA0C;MACtC,KAAKoL,iBAAL,GAAyBxa,cAAc,CAAC,KAAKgL,YAAN,CAAvC;IACH;;IACD,OAAO,KAAKwP,iBAAZ;EACH;EACD;;;EACAxH,yBAAyB,CAACyH,aAAD,EAAgB/H,UAAhB,EAA4B;IACjD,MAAMgI,gBAAgB,GAAG,KAAKvJ,iBAAL,IAA0B,QAAnD;;IACA,IAAIuJ,gBAAgB,KAAK,QAAzB,EAAmC;MAC/B,OAAOD,aAAP;IACH;;IACD,IAAIC,gBAAgB,KAAK,QAAzB,EAAmC;MAC/B,MAAMC,WAAW,GAAG,KAAKrV,SAAzB,CAD+B,CAE/B;MACA;MACA;;MACA,OAAQoN,UAAU,IACdiI,WAAW,CAACC,iBADR,IAEJD,WAAW,CAACE,uBAFR,IAGJF,WAAW,CAACG,oBAHR,IAIJH,WAAW,CAACI,mBAJR,IAKJJ,WAAW,CAAC9H,IALhB;IAMH;;IACD,OAAO3S,aAAa,CAACwa,gBAAD,CAApB;EACH;EACD;;;EACAxB,eAAe,GAAG;IACd;IACA;IACA,IAAI,CAAC,KAAKvE,YAAN,IAAuB,CAAC,KAAKA,YAAL,CAAkBpQ,KAAnB,IAA4B,CAAC,KAAKoQ,YAAL,CAAkBnQ,MAA1E,EAAmF;MAC/E,KAAKmQ,YAAL,GAAoB,KAAKtD,QAAL,GACd,KAAKA,QAAL,CAAcjN,qBAAd,EADc,GAEd,KAAK4I,kBAFX;IAGH;;IACD,OAAO,KAAK2H,YAAZ;EACH;EACD;;;EACA9J,gBAAgB,CAAC1E,KAAD,EAAQ;IACpB,OAAO,KAAK0D,QAAL,CAAcrG,IAAd,CAAmByK,MAAM,IAAI;MAChC,OAAO9H,KAAK,CAACC,MAAN,KAAiBD,KAAK,CAACC,MAAN,KAAiB6H,MAAjB,IAA2BA,MAAM,CAACpH,QAAP,CAAgBV,KAAK,CAACC,MAAtB,CAA5C,CAAP;IACH,CAFM,CAAP;EAGH;;AA9iCS;AAgjCd;AACA;AACA;AACA;AACA;;;AACA,SAASmQ,YAAT,CAAsB9R,CAAtB,EAAyBC,CAAzB,EAA4B;EACxB;EACA;EACA,OAAQ,eAAc2G,IAAI,CAAC2P,KAAL,CAAWvW,CAAX,CAAc,OAAM4G,IAAI,CAAC2P,KAAL,CAAWtW,CAAX,CAAc,QAAxD;AACH;AACD;;;AACA,SAAS6U,OAAT,CAAiB3X,KAAjB,EAAwBqZ,GAAxB,EAA6BC,GAA7B,EAAkC;EAC9B,OAAO7P,IAAI,CAAC6P,GAAL,CAASD,GAAT,EAAc5P,IAAI,CAAC4P,GAAL,CAASC,GAAT,EAActZ,KAAd,CAAd,CAAP;AACH;AACD;;;AACA,SAASsQ,YAAT,CAAsB/L,KAAtB,EAA6B;EACzB;EACA;EACA;EACA,OAAOA,KAAK,CAAC+B,IAAN,CAAW,CAAX,MAAkB,GAAzB;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASmO,WAAT,CAAqBJ,OAArB,EAA8B3Q,SAA9B,EAAyC;EACrC,MAAM6V,SAAS,GAAGlF,OAAO,CAACkF,SAA1B;;EACA,IAAIA,SAAS,CAAC1T,MAAV,KAAqB,CAArB,IAA0B0T,SAAS,CAAC,CAAD,CAAT,CAAaC,QAAb,KAA0B9V,SAAS,CAAC+V,YAAlE,EAAgF;IAC5E,OAAOF,SAAS,CAAC,CAAD,CAAhB;EACH;;EACD,MAAMG,OAAO,GAAGhW,SAAS,CAACiW,aAAV,CAAwB,KAAxB,CAAhB;;EACAJ,SAAS,CAACpV,OAAV,CAAkBa,IAAI,IAAI0U,OAAO,CAACxI,WAAR,CAAoBlM,IAApB,CAA1B;EACA,OAAO0U,OAAP;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAAShF,gBAAT,CAA0BlQ,MAA1B,EAAkCoV,UAAlC,EAA8C;EAC1CpV,MAAM,CAAChE,KAAP,CAAamC,KAAb,GAAsB,GAAEiX,UAAU,CAACjX,KAAM,IAAzC;EACA6B,MAAM,CAAChE,KAAP,CAAaoC,MAAb,GAAuB,GAAEgX,UAAU,CAAChX,MAAO,IAA3C;EACA4B,MAAM,CAAChE,KAAP,CAAaO,SAAb,GAAyB4T,YAAY,CAACiF,UAAU,CAAC/Y,IAAZ,EAAkB+Y,UAAU,CAACjZ,GAA7B,CAArC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASkZ,eAAT,CAAyBC,KAAzB,EAAgCC,SAAhC,EAA2CC,OAA3C,EAAoD;EAChD,MAAMC,IAAI,GAAGC,KAAK,CAACH,SAAD,EAAYD,KAAK,CAACjU,MAAN,GAAe,CAA3B,CAAlB;EACA,MAAMsU,EAAE,GAAGD,KAAK,CAACF,OAAD,EAAUF,KAAK,CAACjU,MAAN,GAAe,CAAzB,CAAhB;;EACA,IAAIoU,IAAI,KAAKE,EAAb,EAAiB;IACb;EACH;;EACD,MAAM3V,MAAM,GAAGsV,KAAK,CAACG,IAAD,CAApB;EACA,MAAMvO,KAAK,GAAGyO,EAAE,GAAGF,IAAL,GAAY,CAAC,CAAb,GAAiB,CAA/B;;EACA,KAAK,IAAIrU,CAAC,GAAGqU,IAAb,EAAmBrU,CAAC,KAAKuU,EAAzB,EAA6BvU,CAAC,IAAI8F,KAAlC,EAAyC;IACrCoO,KAAK,CAAClU,CAAD,CAAL,GAAWkU,KAAK,CAAClU,CAAC,GAAG8F,KAAL,CAAhB;EACH;;EACDoO,KAAK,CAACK,EAAD,CAAL,GAAY3V,MAAZ;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS4V,iBAAT,CAA2BC,YAA3B,EAAyCC,WAAzC,EAAsDtH,YAAtD,EAAoEuH,WAApE,EAAiF;EAC7E,MAAMN,IAAI,GAAGC,KAAK,CAAClH,YAAD,EAAeqH,YAAY,CAACxU,MAAb,GAAsB,CAArC,CAAlB;EACA,MAAMsU,EAAE,GAAGD,KAAK,CAACK,WAAD,EAAcD,WAAW,CAACzU,MAA1B,CAAhB;;EACA,IAAIwU,YAAY,CAACxU,MAAjB,EAAyB;IACrByU,WAAW,CAACE,MAAZ,CAAmBL,EAAnB,EAAuB,CAAvB,EAA0BE,YAAY,CAACG,MAAb,CAAoBP,IAApB,EAA0B,CAA1B,EAA6B,CAA7B,CAA1B;EACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASQ,aAAT,CAAuBJ,YAAvB,EAAqCC,WAArC,EAAkDtH,YAAlD,EAAgEuH,WAAhE,EAA6E;EACzE,MAAMJ,EAAE,GAAGD,KAAK,CAACK,WAAD,EAAcD,WAAW,CAACzU,MAA1B,CAAhB;;EACA,IAAIwU,YAAY,CAACxU,MAAjB,EAAyB;IACrByU,WAAW,CAACE,MAAZ,CAAmBL,EAAnB,EAAuB,CAAvB,EAA0BE,YAAY,CAACrH,YAAD,CAAtC;EACH;AACJ;AACD;;;AACA,SAASkH,KAAT,CAAela,KAAf,EAAsBsZ,GAAtB,EAA2B;EACvB,OAAO7P,IAAI,CAAC6P,GAAL,CAAS,CAAT,EAAY7P,IAAI,CAAC4P,GAAL,CAASC,GAAT,EAActZ,KAAd,CAAZ,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0a,sBAAN,CAA6B;EACzBjX,WAAW,CAACkX,QAAD,EAAWtT,iBAAX,EAA8B;IACrC,KAAKsT,QAAL,GAAgBA,QAAhB;IACA,KAAKtT,iBAAL,GAAyBA,iBAAzB;IACA;;IACA,KAAKuT,cAAL,GAAsB,EAAtB;IACA;;IACA,KAAKC,WAAL,GAAmB,UAAnB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKC,aAAL,GAAqB;MACjBC,IAAI,EAAE,IADW;MAEjBrP,KAAK,EAAE,CAFU;MAGjBsP,QAAQ,EAAE;IAHO,CAArB;EAKH;EACD;AACJ;AACA;AACA;;;EACI3J,KAAK,CAAC4J,KAAD,EAAQ;IACT,KAAKC,SAAL,CAAeD,KAAf;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIE,IAAI,CAAChI,IAAD,EAAO/P,QAAP,EAAiBC,QAAjB,EAA2B+X,YAA3B,EAAyC;IACzC,MAAMC,QAAQ,GAAG,KAAKT,cAAtB;;IACA,MAAMU,QAAQ,GAAG,KAAKC,gCAAL,CAAsCpI,IAAtC,EAA4C/P,QAA5C,EAAsDC,QAAtD,EAAgE+X,YAAhE,CAAjB;;IACA,IAAIE,QAAQ,KAAK,CAAC,CAAd,IAAmBD,QAAQ,CAACxV,MAAT,GAAkB,CAAzC,EAA4C;MACxC,OAAO,IAAP;IACH;;IACD,MAAM2V,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C;IACA,MAAM7H,YAAY,GAAGqI,QAAQ,CAACI,SAAT,CAAmBC,WAAW,IAAIA,WAAW,CAACX,IAAZ,KAAqB5H,IAAvD,CAArB;IACA,MAAMwI,oBAAoB,GAAGN,QAAQ,CAACC,QAAD,CAArC;IACA,MAAMnD,eAAe,GAAGkD,QAAQ,CAACrI,YAAD,CAAR,CAAuBzQ,UAA/C;IACA,MAAMqZ,WAAW,GAAGD,oBAAoB,CAACpZ,UAAzC;IACA,MAAMmJ,KAAK,GAAGsH,YAAY,GAAGsI,QAAf,GAA0B,CAA1B,GAA8B,CAAC,CAA7C,CAXyC,CAYzC;;IACA,MAAMO,UAAU,GAAG,KAAKC,gBAAL,CAAsB3D,eAAtB,EAAuCyD,WAAvC,EAAoDlQ,KAApD,CAAnB,CAbyC,CAczC;;;IACA,MAAMqQ,aAAa,GAAG,KAAKC,mBAAL,CAAyBhJ,YAAzB,EAAuCqI,QAAvC,EAAiD3P,KAAjD,CAAtB,CAfyC,CAgBzC;IACA;;;IACA,MAAMuQ,QAAQ,GAAGZ,QAAQ,CAACa,KAAT,EAAjB,CAlByC,CAmBzC;;IACArC,eAAe,CAACwB,QAAD,EAAWrI,YAAX,EAAyBsI,QAAzB,CAAf;IACAD,QAAQ,CAAClX,OAAT,CAAiB,CAACgY,OAAD,EAAUC,KAAV,KAAoB;MACjC;MACA,IAAIH,QAAQ,CAACG,KAAD,CAAR,KAAoBD,OAAxB,EAAiC;QAC7B;MACH;;MACD,MAAME,aAAa,GAAGF,OAAO,CAACpB,IAAR,KAAiB5H,IAAvC;MACA,MAAMjI,MAAM,GAAGmR,aAAa,GAAGR,UAAH,GAAgBE,aAA5C;MACA,MAAMO,eAAe,GAAGD,aAAa,GAC/BlJ,IAAI,CAAC7G,qBAAL,EAD+B,GAE/B6P,OAAO,CAACpB,IAAR,CAAavO,cAAb,EAFN,CAPiC,CAUjC;;MACA2P,OAAO,CAACjR,MAAR,IAAkBA,MAAlB,CAXiC,CAYjC;MACA;MACA;MACA;;MACA,IAAIsQ,YAAJ,EAAkB;QACd;QACA;QACAc,eAAe,CAAC9b,KAAhB,CAAsBO,SAAtB,GAAkCD,iBAAiB,CAAE,eAAc2I,IAAI,CAAC2P,KAAL,CAAW+C,OAAO,CAACjR,MAAnB,CAA2B,WAA3C,EAAuDiR,OAAO,CAACnb,gBAA/D,CAAnD;QACAgC,gBAAgB,CAACmZ,OAAO,CAAC5Z,UAAT,EAAqB,CAArB,EAAwB2I,MAAxB,CAAhB;MACH,CALD,MAMK;QACDoR,eAAe,CAAC9b,KAAhB,CAAsBO,SAAtB,GAAkCD,iBAAiB,CAAE,kBAAiB2I,IAAI,CAAC2P,KAAL,CAAW+C,OAAO,CAACjR,MAAnB,CAA2B,QAA9C,EAAuDiR,OAAO,CAACnb,gBAA/D,CAAnD;QACAgC,gBAAgB,CAACmZ,OAAO,CAAC5Z,UAAT,EAAqB2I,MAArB,EAA6B,CAA7B,CAAhB;MACH;IACJ,CA1BD,EArByC,CAgDzC;;IACA,KAAK4P,aAAL,CAAmBE,QAAnB,GAA8BjY,kBAAkB,CAAC6Y,WAAD,EAAcxY,QAAd,EAAwBC,QAAxB,CAAhD;IACA,KAAKyX,aAAL,CAAmBC,IAAnB,GAA0BY,oBAAoB,CAACZ,IAA/C;IACA,KAAKD,aAAL,CAAmBpP,KAAnB,GAA2B8P,YAAY,GAAGJ,YAAY,CAACvY,CAAhB,GAAoBuY,YAAY,CAACtY,CAAxE;IACA,OAAO;MAAEsQ,aAAa,EAAEJ,YAAjB;MAA+BA,YAAY,EAAEsI;IAA7C,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI1H,KAAK,CAACT,IAAD,EAAO/P,QAAP,EAAiBC,QAAjB,EAA2B+Y,KAA3B,EAAkC;IACnC,MAAMd,QAAQ,GAAGc,KAAK,IAAI,IAAT,IAAiBA,KAAK,GAAG,CAAzB,GACX;IACE;IACA,KAAKb,gCAAL,CAAsCpI,IAAtC,EAA4C/P,QAA5C,EAAsDC,QAAtD,CAHS,GAIX+Y,KAJN;IAKA,MAAMG,gBAAgB,GAAG,KAAKC,iBAA9B;IACA,MAAMxJ,YAAY,GAAGuJ,gBAAgB,CAACnb,OAAjB,CAAyB+R,IAAzB,CAArB;IACA,MAAMzC,WAAW,GAAGyC,IAAI,CAAC7G,qBAAL,EAApB;IACA,IAAImQ,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAD,CAA3C,CATmC,CAUnC;IACA;IACA;;IACA,IAAImB,oBAAoB,KAAKtJ,IAA7B,EAAmC;MAC/BsJ,oBAAoB,GAAGF,gBAAgB,CAACjB,QAAQ,GAAG,CAAZ,CAAvC;IACH,CAfkC,CAgBnC;IACA;;;IACA,IAAI,CAACmB,oBAAD,KACCnB,QAAQ,IAAI,IAAZ,IAAoBA,QAAQ,KAAK,CAAC,CAAlC,IAAuCA,QAAQ,GAAGiB,gBAAgB,CAAC1W,MAAjB,GAA0B,CAD7E,KAEA,KAAK6W,wBAAL,CAA8BtZ,QAA9B,EAAwCC,QAAxC,CAFJ,EAEuD;MACnDoZ,oBAAoB,GAAGF,gBAAgB,CAAC,CAAD,CAAvC;IACH,CAtBkC,CAuBnC;IACA;;;IACA,IAAIvJ,YAAY,GAAG,CAAC,CAApB,EAAuB;MACnBuJ,gBAAgB,CAAC/B,MAAjB,CAAwBxH,YAAxB,EAAsC,CAAtC;IACH,CA3BkC,CA4BnC;IACA;;;IACA,IAAIyJ,oBAAoB,IAAI,CAAC,KAAKpV,iBAAL,CAAuBkD,UAAvB,CAAkCkS,oBAAlC,CAA7B,EAAsF;MAClF,MAAMpc,OAAO,GAAGoc,oBAAoB,CAACjQ,cAArB,EAAhB;MACAnM,OAAO,CAACsc,aAAR,CAAsB5L,YAAtB,CAAmCL,WAAnC,EAAgDrQ,OAAhD;MACAkc,gBAAgB,CAAC/B,MAAjB,CAAwBc,QAAxB,EAAkC,CAAlC,EAAqCnI,IAArC;IACH,CAJD,MAKK;MACD7U,aAAa,CAAC,KAAKqc,QAAN,CAAb,CAA6BzJ,WAA7B,CAAyCR,WAAzC;MACA6L,gBAAgB,CAACK,IAAjB,CAAsBzJ,IAAtB;IACH,CAtCkC,CAuCnC;;;IACAzC,WAAW,CAAClQ,KAAZ,CAAkBO,SAAlB,GAA8B,EAA9B,CAxCmC,CAyCnC;IACA;IACA;;IACA,KAAK8b,mBAAL;EACH;EACD;;;EACA3B,SAAS,CAACD,KAAD,EAAQ;IACb,KAAKuB,iBAAL,GAAyBvB,KAAK,CAACiB,KAAN,EAAzB;;IACA,KAAKW,mBAAL;EACH;EACD;;;EACAC,iBAAiB,CAACC,SAAD,EAAY;IACzB,KAAKC,cAAL,GAAsBD,SAAtB;EACH;EACD;;;EACAlO,KAAK,GAAG;IACJ;IACA,KAAK2N,iBAAL,CAAuBrY,OAAvB,CAA+BgP,IAAI,IAAI;MACnC,IAAIzM,EAAJ;;MACA,MAAMyG,WAAW,GAAGgG,IAAI,CAAC3G,cAAL,EAApB;;MACA,IAAIW,WAAJ,EAAiB;QACb,MAAMnM,gBAAgB,GAAG,CAAC0F,EAAE,GAAG,KAAKkU,cAAL,CAAoBhZ,IAApB,CAAyBqb,CAAC,IAAIA,CAAC,CAAClC,IAAF,KAAW5H,IAAzC,CAAN,MAA0D,IAA1D,IAAkEzM,EAAE,KAAK,KAAK,CAA9E,GAAkF,KAAK,CAAvF,GAA2FA,EAAE,CAAC1F,gBAAvH;QACAmM,WAAW,CAAC3M,KAAZ,CAAkBO,SAAlB,GAA8BC,gBAAgB,IAAI,EAAlD;MACH;IACJ,CAPD;;IAQA,KAAK4Z,cAAL,GAAsB,EAAtB;IACA,KAAK4B,iBAAL,GAAyB,EAAzB;IACA,KAAK1B,aAAL,CAAmBC,IAAnB,GAA0B,IAA1B;IACA,KAAKD,aAAL,CAAmBpP,KAAnB,GAA2B,CAA3B;IACA,KAAKoP,aAAL,CAAmBE,QAAnB,GAA8B,KAA9B;EACH;EACD;AACJ;AACA;AACA;;;EACIkC,sBAAsB,GAAG;IACrB,OAAO,KAAKV,iBAAZ;EACH;EACD;;;EACAhL,YAAY,CAAC2B,IAAD,EAAO;IACf;IACA;IACA;IACA,MAAM8H,KAAK,GAAG,KAAKJ,WAAL,KAAqB,YAArB,IAAqC,KAAK3L,SAAL,KAAmB,KAAxD,GACR,KAAK0L,cAAL,CAAoBsB,KAApB,GAA4BiB,OAA5B,EADQ,GAER,KAAKvC,cAFX;IAGA,OAAOK,KAAK,CAACQ,SAAN,CAAgBC,WAAW,IAAIA,WAAW,CAACX,IAAZ,KAAqB5H,IAApD,CAAP;EACH;EACD;;;EACAiK,cAAc,CAACtY,aAAD,EAAgBC,cAAhB,EAAgC;IAC1C;IACA;IACA;IACA;IACA,KAAK6V,cAAL,CAAoBzW,OAApB,CAA4B,CAAC;MAAE5B;IAAF,CAAD,KAAoB;MAC5CS,gBAAgB,CAACT,UAAD,EAAauC,aAAb,EAA4BC,cAA5B,CAAhB;IACH,CAFD,EAL0C,CAQ1C;IACA;;;IACA,KAAK6V,cAAL,CAAoBzW,OAApB,CAA4B,CAAC;MAAE4W;IAAF,CAAD,KAAc;MACtC,IAAI,KAAK1T,iBAAL,CAAuBkD,UAAvB,CAAkCwQ,IAAlC,CAAJ,EAA6C;QACzC;QACA;QACAA,IAAI,CAACvL,4BAAL;MACH;IACJ,CAND;EAOH;EACD;;;EACAqN,mBAAmB,GAAG;IAClB,MAAMrB,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C;IACA,KAAKD,cAAL,GAAsB,KAAK4B,iBAAL,CACjBrd,GADiB,CACb4b,IAAI,IAAI;MACb,MAAMsC,gBAAgB,GAAGtC,IAAI,CAACtO,iBAAL,EAAzB;MACA,OAAO;QACHsO,IADG;QAEH7P,MAAM,EAAE,CAFL;QAGHlK,gBAAgB,EAAEqc,gBAAgB,CAAC7c,KAAjB,CAAuBO,SAAvB,IAAoC,EAHnD;QAIHwB,UAAU,EAAED,oBAAoB,CAAC+a,gBAAD;MAJ7B,CAAP;IAMH,CATqB,EAUjBlC,IAViB,CAUZ,CAACmC,CAAD,EAAIC,CAAJ,KAAU;MAChB,OAAO/B,YAAY,GACb8B,CAAC,CAAC/a,UAAF,CAAa1B,IAAb,GAAoB0c,CAAC,CAAChb,UAAF,CAAa1B,IADpB,GAEbyc,CAAC,CAAC/a,UAAF,CAAa5B,GAAb,GAAmB4c,CAAC,CAAChb,UAAF,CAAa5B,GAFtC;IAGH,CAdqB,CAAtB;EAeH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACImb,gBAAgB,CAAC3D,eAAD,EAAkByD,WAAlB,EAA+BlQ,KAA/B,EAAsC;IAClD,MAAM8P,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C;IACA,IAAIgB,UAAU,GAAGL,YAAY,GACvBI,WAAW,CAAC/a,IAAZ,GAAmBsX,eAAe,CAACtX,IADZ,GAEvB+a,WAAW,CAACjb,GAAZ,GAAkBwX,eAAe,CAACxX,GAFxC,CAFkD,CAKlD;;IACA,IAAI+K,KAAK,KAAK,CAAC,CAAf,EAAkB;MACdmQ,UAAU,IAAIL,YAAY,GACpBI,WAAW,CAACjZ,KAAZ,GAAoBwV,eAAe,CAACxV,KADhB,GAEpBiZ,WAAW,CAAChZ,MAAZ,GAAqBuV,eAAe,CAACvV,MAF3C;IAGH;;IACD,OAAOiZ,UAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIG,mBAAmB,CAAChJ,YAAD,EAAeqI,QAAf,EAAyB3P,KAAzB,EAAgC;IAC/C,MAAM8P,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C;IACA,MAAM1C,eAAe,GAAGkD,QAAQ,CAACrI,YAAD,CAAR,CAAuBzQ,UAA/C;IACA,MAAMib,gBAAgB,GAAGnC,QAAQ,CAACrI,YAAY,GAAGtH,KAAK,GAAG,CAAC,CAAzB,CAAjC;IACA,IAAIqQ,aAAa,GAAG5D,eAAe,CAACqD,YAAY,GAAG,OAAH,GAAa,QAA1B,CAAf,GAAqD9P,KAAzE;;IACA,IAAI8R,gBAAJ,EAAsB;MAClB,MAAMnM,KAAK,GAAGmK,YAAY,GAAG,MAAH,GAAY,KAAtC;MACA,MAAMiC,GAAG,GAAGjC,YAAY,GAAG,OAAH,GAAa,QAArC,CAFkB,CAGlB;MACA;MACA;MACA;;MACA,IAAI9P,KAAK,KAAK,CAAC,CAAf,EAAkB;QACdqQ,aAAa,IAAIyB,gBAAgB,CAACjb,UAAjB,CAA4B8O,KAA5B,IAAqC8G,eAAe,CAACsF,GAAD,CAArE;MACH,CAFD,MAGK;QACD1B,aAAa,IAAI5D,eAAe,CAAC9G,KAAD,CAAf,GAAyBmM,gBAAgB,CAACjb,UAAjB,CAA4Bkb,GAA5B,CAA1C;MACH;IACJ;;IACD,OAAO1B,aAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIW,wBAAwB,CAACtZ,QAAD,EAAWC,QAAX,EAAqB;IACzC,IAAI,CAAC,KAAKmZ,iBAAL,CAAuB3W,MAA5B,EAAoC;MAChC,OAAO,KAAP;IACH;;IACD,MAAM6X,aAAa,GAAG,KAAK9C,cAA3B;IACA,MAAMY,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C,CALyC,CAMzC;IACA;;IACA,MAAM8C,QAAQ,GAAGD,aAAa,CAAC,CAAD,CAAb,CAAiB3C,IAAjB,KAA0B,KAAKyB,iBAAL,CAAuB,CAAvB,CAA3C;;IACA,IAAImB,QAAJ,EAAc;MACV,MAAMC,YAAY,GAAGF,aAAa,CAACA,aAAa,CAAC7X,MAAd,GAAuB,CAAxB,CAAb,CAAwCtD,UAA7D;MACA,OAAOiZ,YAAY,GAAGpY,QAAQ,IAAIwa,YAAY,CAACnb,KAA5B,GAAoCY,QAAQ,IAAIua,YAAY,CAAClb,MAAhF;IACH,CAHD,MAIK;MACD,MAAMmb,aAAa,GAAGH,aAAa,CAAC,CAAD,CAAb,CAAiBnb,UAAvC;MACA,OAAOiZ,YAAY,GAAGpY,QAAQ,IAAIya,aAAa,CAAChd,IAA7B,GAAoCwC,QAAQ,IAAIwa,aAAa,CAACld,GAAjF;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI4a,gCAAgC,CAACpI,IAAD,EAAO/P,QAAP,EAAiBC,QAAjB,EAA2BqI,KAA3B,EAAkC;IAC9D,MAAM8P,YAAY,GAAG,KAAKX,WAAL,KAAqB,YAA1C;;IACA,MAAMuB,KAAK,GAAG,KAAKxB,cAAL,CAAoBa,SAApB,CAA8B,CAAC;MAAEV,IAAF;MAAQxY;IAAR,CAAD,KAA0B;MAClE;MACA,IAAIwY,IAAI,KAAK5H,IAAb,EAAmB;QACf,OAAO,KAAP;MACH;;MACD,IAAIzH,KAAJ,EAAW;QACP,MAAMwD,SAAS,GAAGsM,YAAY,GAAG9P,KAAK,CAAC7I,CAAT,GAAa6I,KAAK,CAAC5I,CAAjD,CADO,CAEP;QACA;QACA;;QACA,IAAIiY,IAAI,KAAK,KAAKD,aAAL,CAAmBC,IAA5B,IACA,KAAKD,aAAL,CAAmBE,QADnB,IAEA9L,SAAS,KAAK,KAAK4L,aAAL,CAAmBpP,KAFrC,EAE4C;UACxC,OAAO,KAAP;QACH;MACJ;;MACD,OAAO8P,YAAY,GACb;MACE;MACApY,QAAQ,IAAIqG,IAAI,CAACqU,KAAL,CAAWvb,UAAU,CAAC1B,IAAtB,CAAZ,IAA2CuC,QAAQ,GAAGqG,IAAI,CAACqU,KAAL,CAAWvb,UAAU,CAACE,KAAtB,CAH3C,GAIbY,QAAQ,IAAIoG,IAAI,CAACqU,KAAL,CAAWvb,UAAU,CAAC5B,GAAtB,CAAZ,IAA0C0C,QAAQ,GAAGoG,IAAI,CAACqU,KAAL,CAAWvb,UAAU,CAACG,MAAtB,CAJ3D;IAKH,CArBa,CAAd;;IAsBA,OAAO0Z,KAAK,KAAK,CAAC,CAAX,IAAgB,CAAC,KAAKY,cAAL,CAAoBZ,KAApB,EAA2BjJ,IAA3B,CAAjB,GAAoD,CAAC,CAArD,GAAyDiJ,KAAhE;EACH;;AAnUwB;AAsU7B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAM2B,wBAAwB,GAAG,IAAjC;AACA;AACA;AACA;AACA;;AACA,MAAMC,0BAA0B,GAAG,IAAnC;AACA;AACA;AACA;;AACA,MAAMC,WAAN,CAAkB;EACdxa,WAAW,CAACpD,OAAD,EAAUgH,iBAAV,EAA6B3D,SAA7B,EAAwCyD,OAAxC,EAAiDC,cAAjD,EAAiE;IACxE,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKF,OAAL,GAAeA,OAAf;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA;;IACA,KAAK8B,QAAL,GAAgB,KAAhB;IACA;;IACA,KAAK2K,eAAL,GAAuB,KAAvB;IACA;AACR;AACA;AACA;;IACQ,KAAKqK,kBAAL,GAA0B,KAA1B;IACA;;IACA,KAAKC,cAAL,GAAsB,CAAtB;IACA;AACR;AACA;AACA;;IACQ,KAAKC,cAAL,GAAsB,MAAM,IAA5B;IACA;;;IACA,KAAKC,aAAL,GAAqB,MAAM,IAA3B;IACA;;;IACA,KAAK/V,aAAL,GAAqB,IAAI3J,OAAJ,EAArB;IACA;AACR;AACA;;IACQ,KAAK+J,OAAL,GAAe,IAAI/J,OAAJ,EAAf;IACA;AACR;AACA;AACA;;IACQ,KAAKgK,MAAL,GAAc,IAAIhK,OAAJ,EAAd;IACA;;IACA,KAAKiK,OAAL,GAAe,IAAIjK,OAAJ,EAAf;IACA;;IACA,KAAK2f,MAAL,GAAc,IAAI3f,OAAJ,EAAd;IACA;;IACA,KAAK4f,WAAL,GAAmB,KAAnB;IACA;;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA;;IACA,KAAKC,SAAL,GAAiB,EAAjB;IACA;;IACA,KAAKC,eAAL,GAAuB,IAAI1X,GAAJ,EAAvB;IACA;;IACA,KAAK2X,2BAAL,GAAmC/f,YAAY,CAAC+I,KAAhD;IACA;;IACA,KAAKiX,wBAAL,GAAgC;IAAE;IAAlC;IACA;;IACA,KAAKC,0BAAL,GAAkC;IAAE;IAApC;IACA;;IACA,KAAKC,iBAAL,GAAyB,IAAIngB,OAAJ,EAAzB;IACA;;IACA,KAAKia,iBAAL,GAAyB,IAAzB;IACA;;IACA,KAAKmG,oBAAL,GAA4B,MAAM;MAC9B,KAAK/O,cAAL;;MACAnR,QAAQ,CAAC,CAAD,EAAIC,uBAAJ,CAAR,CACKkgB,IADL,CACU/f,SAAS,CAAC,KAAK6f,iBAAN,CADnB,EAEK9Q,SAFL,CAEe,MAAM;QACjB,MAAMhJ,IAAI,GAAG,KAAKia,WAAlB;QACA,MAAMC,UAAU,GAAG,KAAKf,cAAxB;;QACA,IAAI,KAAKS,wBAAL,KAAkC;QAAE;QAAxC,EAA8E;UAC1E5Z,IAAI,CAACma,QAAL,CAAc,CAAd,EAAiB,CAACD,UAAlB;QACH,CAFD,MAGK,IAAI,KAAKN,wBAAL,KAAkC;QAAE;QAAxC,EAAgF;UACjF5Z,IAAI,CAACma,QAAL,CAAc,CAAd,EAAiBD,UAAjB;QACH;;QACD,IAAI,KAAKL,0BAAL,KAAoC;QAAE;QAA1C,EAAoF;UAChF7Z,IAAI,CAACma,QAAL,CAAc,CAACD,UAAf,EAA2B,CAA3B;QACH,CAFD,MAGK,IAAI,KAAKL,0BAAL,KAAoC;QAAE;QAA1C,EAAqF;UACtF7Z,IAAI,CAACma,QAAL,CAAcD,UAAd,EAA0B,CAA1B;QACH;MACJ,CAjBD;IAkBH,CApBD;;IAqBA,KAAK7e,OAAL,GAAe/B,aAAa,CAAC+B,OAAD,CAA5B;IACA,KAAKqD,SAAL,GAAiBA,SAAjB;IACA,KAAK0b,qBAAL,CAA2B,CAAC,KAAK/e,OAAN,CAA3B;;IACAgH,iBAAiB,CAACgY,qBAAlB,CAAwC,IAAxC;;IACA,KAAKpT,gBAAL,GAAwB,IAAIzI,qBAAJ,CAA0BE,SAA1B,CAAxB;IACA,KAAK4b,aAAL,GAAqB,IAAI5E,sBAAJ,CAA2B,KAAKra,OAAhC,EAAyCgH,iBAAzC,CAArB;;IACA,KAAKiY,aAAL,CAAmBxC,iBAAnB,CAAqC,CAACV,KAAD,EAAQjJ,IAAR,KAAiB,KAAKkL,aAAL,CAAmBjC,KAAnB,EAA0BjJ,IAA1B,EAAgC,IAAhC,CAAtD;EACH;EACD;;;EACA/E,OAAO,GAAG;IACN,KAAK4B,cAAL;;IACA,KAAK8O,iBAAL,CAAuBlQ,QAAvB;;IACA,KAAK+P,2BAAL,CAAiC7Q,WAAjC;;IACA,KAAKxF,aAAL,CAAmBsG,QAAnB;IACA,KAAKlG,OAAL,CAAakG,QAAb;IACA,KAAKjG,MAAL,CAAYiG,QAAZ;IACA,KAAKhG,OAAL,CAAagG,QAAb;IACA,KAAK0P,MAAL,CAAY1P,QAAZ;;IACA,KAAK8P,eAAL,CAAqB7a,KAArB;;IACA,KAAKob,WAAL,GAAmB,IAAnB;;IACA,KAAKhT,gBAAL,CAAsBpI,KAAtB;;IACA,KAAKwD,iBAAL,CAAuBkY,mBAAvB,CAA2C,IAA3C;EACH;EACD;;;EACAhV,UAAU,GAAG;IACT,OAAO,KAAKgU,WAAZ;EACH;EACD;;;EACAlN,KAAK,GAAG;IACJ,KAAKmO,gBAAL;;IACA,KAAKC,wBAAL;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;EACI7L,KAAK,CAACT,IAAD,EAAO/P,QAAP,EAAiBC,QAAjB,EAA2B+Y,KAA3B,EAAkC;IACnC,KAAKoD,gBAAL,GADmC,CAEnC;IACA;;;IACA,IAAIpD,KAAK,IAAI,IAAT,IAAiB,KAAKvI,eAA1B,EAA2C;MACvCuI,KAAK,GAAG,KAAKoC,WAAL,CAAiBpd,OAAjB,CAAyB+R,IAAzB,CAAR;IACH;;IACD,KAAKmM,aAAL,CAAmB1L,KAAnB,CAAyBT,IAAzB,EAA+B/P,QAA/B,EAAyCC,QAAzC,EAAmD+Y,KAAnD,EAPmC,CAQnC;IACA;;;IACA,KAAKsD,qBAAL,GAVmC,CAWnC;;;IACA,KAAKD,wBAAL;;IACA,KAAK/W,OAAL,CAAaK,IAAb,CAAkB;MAAEoK,IAAF;MAAQ/I,SAAS,EAAE,IAAnB;MAAyB4I,YAAY,EAAE,KAAKxB,YAAL,CAAkB2B,IAAlB;IAAvC,CAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACIQ,IAAI,CAACR,IAAD,EAAO;IACP,KAAKwM,MAAL;;IACA,KAAKhX,MAAL,CAAYI,IAAZ,CAAiB;MAAEoK,IAAF;MAAQ/I,SAAS,EAAE;IAAnB,CAAjB;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;EACIkJ,IAAI,CAACH,IAAD,EAAOH,YAAP,EAAqBI,aAArB,EAAoCC,iBAApC,EAAuDJ,sBAAvD,EAA+EzH,QAA/E,EAAyF6E,SAAzF,EAAoG9L,KAAK,GAAG,EAA5G,EAAgH;IAChH,KAAKob,MAAL;;IACA,KAAK/W,OAAL,CAAaG,IAAb,CAAkB;MACdoK,IADc;MAEdH,YAFc;MAGdI,aAHc;MAIdhJ,SAAS,EAAE,IAJG;MAKdiJ,iBALc;MAMdJ,sBANc;MAOdzH,QAPc;MAQd6E,SARc;MASd9L;IATc,CAAlB;EAWH;EACD;AACJ;AACA;AACA;;;EACI2W,SAAS,CAACD,KAAD,EAAQ;IACb,MAAM2E,aAAa,GAAG,KAAKpB,WAA3B;IACA,KAAKA,WAAL,GAAmBvD,KAAnB;IACAA,KAAK,CAAC9W,OAAN,CAAcgP,IAAI,IAAIA,IAAI,CAAChE,kBAAL,CAAwB,IAAxB,CAAtB;;IACA,IAAI,KAAK5E,UAAL,EAAJ,EAAuB;MACnB,MAAMsV,YAAY,GAAGD,aAAa,CAACE,MAAd,CAAqB3M,IAAI,IAAIA,IAAI,CAAC5I,UAAL,EAA7B,CAArB,CADmB,CAEnB;MACA;;MACA,IAAIsV,YAAY,CAACE,KAAb,CAAmB5M,IAAI,IAAI8H,KAAK,CAAC7Z,OAAN,CAAc+R,IAAd,MAAwB,CAAC,CAApD,CAAJ,EAA4D;QACxD,KAAKwM,MAAL;MACH,CAFD,MAGK;QACD,KAAKL,aAAL,CAAmBpE,SAAnB,CAA6B,KAAKsD,WAAlC;MACH;IACJ;;IACD,OAAO,IAAP;EACH;EACD;;;EACAvP,aAAa,CAACC,SAAD,EAAY;IACrB,KAAKoQ,aAAL,CAAmBpQ,SAAnB,GAA+BA,SAA/B;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACI8Q,WAAW,CAACA,WAAD,EAAc;IACrB,KAAKvB,SAAL,GAAiBuB,WAAW,CAAC9D,KAAZ,EAAjB;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI+D,eAAe,CAACpF,WAAD,EAAc;IACzB;IACA;IACA,KAAKyE,aAAL,CAAmBzE,WAAnB,GAAiCA,WAAjC;IACA,OAAO,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIuE,qBAAqB,CAACrb,QAAD,EAAW;IAC5B,MAAM1D,OAAO,GAAG/B,aAAa,CAAC,KAAK+B,OAAN,CAA7B,CAD4B,CAE5B;IACA;;IACA,KAAK6f,mBAAL,GACInc,QAAQ,CAAC3C,OAAT,CAAiBf,OAAjB,MAA8B,CAAC,CAA/B,GAAmC,CAACA,OAAD,EAAU,GAAG0D,QAAb,CAAnC,GAA4DA,QAAQ,CAACmY,KAAT,EADhE;IAEA,OAAO,IAAP;EACH;EACD;;;EACAzK,oBAAoB,GAAG;IACnB,OAAO,KAAKyO,mBAAZ;EACH;EACD;AACJ;AACA;AACA;;;EACI1O,YAAY,CAAC2B,IAAD,EAAO;IACf,OAAO,KAAKoL,WAAL,GACD,KAAKe,aAAL,CAAmB9N,YAAnB,CAAgC2B,IAAhC,CADC,GAED,KAAKqL,WAAL,CAAiBpd,OAAjB,CAAyB+R,IAAzB,CAFN;EAGH;EACD;AACJ;AACA;AACA;;;EACI3I,WAAW,GAAG;IACV,OAAO,KAAKkU,eAAL,CAAqByB,IAArB,GAA4B,CAAnC;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACIpM,SAAS,CAACZ,IAAD,EAAO/P,QAAP,EAAiBC,QAAjB,EAA2B+X,YAA3B,EAAyC;IAC9C;IACA,IAAI,KAAKvH,eAAL,IACA,CAAC,KAAKuM,WADN,IAEA,CAACnd,uBAAuB,CAAC,KAAKmd,WAAN,EAAmBrC,wBAAnB,EAA6C3a,QAA7C,EAAuDC,QAAvD,CAF5B,EAE8F;MAC1F;IACH;;IACD,MAAMgd,MAAM,GAAG,KAAKf,aAAL,CAAmBnE,IAAnB,CAAwBhI,IAAxB,EAA8B/P,QAA9B,EAAwCC,QAAxC,EAAkD+X,YAAlD,CAAf;;IACA,IAAIiF,MAAJ,EAAY;MACR,KAAK/B,MAAL,CAAYvV,IAAZ,CAAiB;QACbqK,aAAa,EAAEiN,MAAM,CAACjN,aADT;QAEbJ,YAAY,EAAEqN,MAAM,CAACrN,YAFR;QAGb5I,SAAS,EAAE,IAHE;QAIb+I;MAJa,CAAjB;IAMH;EACJ;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIW,0BAA0B,CAAC1Q,QAAD,EAAWC,QAAX,EAAqB;IAC3C,IAAI,KAAK6a,kBAAT,EAA6B;MACzB;IACH;;IACD,IAAIoC,UAAJ;IACA,IAAIC,uBAAuB,GAAG;IAAE;IAAhC;IACA,IAAIC,yBAAyB,GAAG;IAAE;IAAlC,CAN2C,CAO3C;;IACA,KAAKvU,gBAAL,CAAsBtI,SAAtB,CAAgCQ,OAAhC,CAAwC,CAACzD,QAAD,EAAWL,OAAX,KAAuB;MAC3D;MACA;MACA,IAAIA,OAAO,KAAK,KAAKqD,SAAjB,IAA8B,CAAChD,QAAQ,CAAC6B,UAAxC,IAAsD+d,UAA1D,EAAsE;QAClE;MACH;;MACD,IAAIrd,uBAAuB,CAACvC,QAAQ,CAAC6B,UAAV,EAAsBwb,wBAAtB,EAAgD3a,QAAhD,EAA0DC,QAA1D,CAA3B,EAAgG;QAC5F,CAACkd,uBAAD,EAA0BC,yBAA1B,IAAuDC,0BAA0B,CAACpgB,OAAD,EAAUK,QAAQ,CAAC6B,UAAnB,EAA+Ba,QAA/B,EAAyCC,QAAzC,CAAjF;;QACA,IAAIkd,uBAAuB,IAAIC,yBAA/B,EAA0D;UACtDF,UAAU,GAAGjgB,OAAb;QACH;MACJ;IACJ,CAZD,EAR2C,CAqB3C;;;IACA,IAAI,CAACkgB,uBAAD,IAA4B,CAACC,yBAAjC,EAA4D;MACxD,MAAM;QAAE7d,KAAF;QAASC;MAAT,IAAoB,KAAKwE,cAAL,CAAoBsZ,eAApB,EAA1B;;MACA,MAAMne,UAAU,GAAG;QACfI,KADe;QAEfC,MAFe;QAGfjC,GAAG,EAAE,CAHU;QAIf8B,KAAK,EAAEE,KAJQ;QAKfD,MAAM,EAAEE,MALO;QAMf/B,IAAI,EAAE;MANS,CAAnB;MAQA0f,uBAAuB,GAAGI,0BAA0B,CAACpe,UAAD,EAAac,QAAb,CAApD;MACAmd,yBAAyB,GAAGI,4BAA4B,CAACre,UAAD,EAAaa,QAAb,CAAxD;MACAkd,UAAU,GAAGpb,MAAb;IACH;;IACD,IAAIob,UAAU,KACTC,uBAAuB,KAAK,KAAK3B,wBAAjC,IACG4B,yBAAyB,KAAK,KAAK3B,0BADtC,IAEGyB,UAAU,KAAK,KAAKrB,WAHd,CAAd,EAG0C;MACtC,KAAKL,wBAAL,GAAgC2B,uBAAhC;MACA,KAAK1B,0BAAL,GAAkC2B,yBAAlC;MACA,KAAKvB,WAAL,GAAmBqB,UAAnB;;MACA,IAAI,CAACC,uBAAuB,IAAIC,yBAA5B,KAA0DF,UAA9D,EAA0E;QACtE,KAAKnZ,OAAL,CAAakG,iBAAb,CAA+B,KAAK0R,oBAApC;MACH,CAFD,MAGK;QACD,KAAK/O,cAAL;MACH;IACJ;EACJ;EACD;;;EACAA,cAAc,GAAG;IACb,KAAK8O,iBAAL,CAAuB/V,IAAvB;EACH;EACD;;;EACAyW,gBAAgB,GAAG;IACf,MAAMtH,MAAM,GAAG5Z,aAAa,CAAC,KAAK+B,OAAN,CAAb,CAA4BG,KAA3C;IACA,KAAK8H,aAAL,CAAmBS,IAAnB;IACA,KAAKwV,WAAL,GAAmB,IAAnB,CAHe,CAIf;IACA;IACA;;IACA,KAAKsC,kBAAL,GAA0B3I,MAAM,CAAC4I,gBAAP,IAA2B5I,MAAM,CAAC6I,cAAlC,IAAoD,EAA9E;IACA7I,MAAM,CAAC6I,cAAP,GAAwB7I,MAAM,CAAC4I,gBAAP,GAA0B,MAAlD;;IACA,KAAKxB,aAAL,CAAmBjO,KAAnB,CAAyB,KAAKmN,WAA9B;;IACA,KAAKkB,qBAAL;;IACA,KAAKf,2BAAL,CAAiC7Q,WAAjC;;IACA,KAAKkT,qBAAL;EACH;EACD;;;EACAtB,qBAAqB,GAAG;IACpB,MAAMrf,OAAO,GAAG/B,aAAa,CAAC,KAAK+B,OAAN,CAA7B;;IACA,KAAK4L,gBAAL,CAAsBnI,KAAtB,CAA4B,KAAKoc,mBAAjC,EAFoB,CAGpB;IACA;;;IACA,KAAKE,WAAL,GAAmB,KAAKnU,gBAAL,CAAsBtI,SAAtB,CAAgCe,GAAhC,CAAoCrE,OAApC,EAA6CkC,UAAhE;EACH;EACD;;;EACAod,MAAM,GAAG;IACL,KAAKpB,WAAL,GAAmB,KAAnB;IACA,MAAMrG,MAAM,GAAG5Z,aAAa,CAAC,KAAK+B,OAAN,CAAb,CAA4BG,KAA3C;IACA0X,MAAM,CAAC6I,cAAP,GAAwB7I,MAAM,CAAC4I,gBAAP,GAA0B,KAAKD,kBAAvD;;IACA,KAAKpC,SAAL,CAAeta,OAAf,CAAuBgY,OAAO,IAAIA,OAAO,CAAC8E,cAAR,CAAuB,IAAvB,CAAlC;;IACA,KAAK3B,aAAL,CAAmBzQ,KAAnB;;IACA,KAAKmB,cAAL;;IACA,KAAK2O,2BAAL,CAAiC7Q,WAAjC;;IACA,KAAK7B,gBAAL,CAAsBpI,KAAtB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIqP,gBAAgB,CAACrQ,CAAD,EAAIC,CAAJ,EAAO;IACnB,OAAO,KAAKsd,WAAL,IAAoB,IAApB,IAA4Brd,kBAAkB,CAAC,KAAKqd,WAAN,EAAmBvd,CAAnB,EAAsBC,CAAtB,CAArD;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI4Q,gCAAgC,CAACP,IAAD,EAAOtQ,CAAP,EAAUC,CAAV,EAAa;IACzC,OAAO,KAAK2b,SAAL,CAAe7c,IAAf,CAAoBua,OAAO,IAAIA,OAAO,CAAC+E,WAAR,CAAoB/N,IAApB,EAA0BtQ,CAA1B,EAA6BC,CAA7B,CAA/B,CAAP;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIoe,WAAW,CAAC/N,IAAD,EAAOtQ,CAAP,EAAUC,CAAV,EAAa;IACpB,IAAI,CAAC,KAAKsd,WAAN,IACA,CAACrd,kBAAkB,CAAC,KAAKqd,WAAN,EAAmBvd,CAAnB,EAAsBC,CAAtB,CADnB,IAEA,CAAC,KAAKsb,cAAL,CAAoBjL,IAApB,EAA0B,IAA1B,CAFL,EAEsC;MAClC,OAAO,KAAP;IACH;;IACD,MAAMgO,gBAAgB,GAAG,KAAK/iB,cAAL,GAAsB+iB,gBAAtB,CAAuCte,CAAvC,EAA0CC,CAA1C,CAAzB,CANoB,CAOpB;IACA;;;IACA,IAAI,CAACqe,gBAAL,EAAuB;MACnB,OAAO,KAAP;IACH;;IACD,MAAMC,aAAa,GAAG9iB,aAAa,CAAC,KAAK+B,OAAN,CAAnC,CAZoB,CAapB;IACA;IACA;IACA;IACA;IACA;;IACA,OAAO8gB,gBAAgB,KAAKC,aAArB,IAAsCA,aAAa,CAACnc,QAAd,CAAuBkc,gBAAvB,CAA7C;EACH;EACD;AACJ;AACA;AACA;;;EACIE,eAAe,CAAClF,OAAD,EAAUlB,KAAV,EAAiB;IAC5B,MAAMqG,cAAc,GAAG,KAAK5C,eAA5B;;IACA,IAAI,CAAC4C,cAAc,CAACphB,GAAf,CAAmBic,OAAnB,CAAD,IACAlB,KAAK,CAAC8E,KAAN,CAAY5M,IAAI,IAAI;MAChB;MACA;MACA;MACA;MACA,OAAO,KAAKiL,cAAL,CAAoBjL,IAApB,EAA0B,IAA1B,KAAmC,KAAKqL,WAAL,CAAiBpd,OAAjB,CAAyB+R,IAAzB,IAAiC,CAAC,CAA5E;IACH,CAND,CADJ,EAOQ;MACJmO,cAAc,CAACzU,GAAf,CAAmBsP,OAAnB;;MACA,KAAKuD,qBAAL;;MACA,KAAKsB,qBAAL;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIC,cAAc,CAAC9E,OAAD,EAAU;IACpB,KAAKuC,eAAL,CAAqB1P,MAArB,CAA4BmN,OAA5B;;IACA,KAAKwC,2BAAL,CAAiC7Q,WAAjC;EACH;EACD;AACJ;AACA;AACA;;;EACIkT,qBAAqB,GAAG;IACpB,KAAKrC,2BAAL,GAAmC,KAAKtX,iBAAL,CAC9BgL,QAD8B,CACrB,KAAKjU,cAAL,EADqB,EAE9B4P,SAF8B,CAEpBzJ,KAAK,IAAI;MACpB,IAAI,KAAKgG,UAAL,EAAJ,EAAuB;QACnB,MAAMoO,gBAAgB,GAAG,KAAK1M,gBAAL,CAAsB3H,YAAtB,CAAmCC,KAAnC,CAAzB;;QACA,IAAIoU,gBAAJ,EAAsB;UAClB,KAAK2G,aAAL,CAAmBlC,cAAnB,CAAkCzE,gBAAgB,CAAChY,GAAnD,EAAwDgY,gBAAgB,CAAC9X,IAAzE;QACH;MACJ,CALD,MAMK,IAAI,KAAK2J,WAAL,EAAJ,EAAwB;QACzB,KAAKkV,qBAAL;MACH;IACJ,CAZkC,CAAnC;EAaH;EACD;AACJ;AACA;AACA;AACA;AACA;;;EACIthB,cAAc,GAAG;IACb,IAAI,CAAC,KAAKwa,iBAAV,EAA6B;MACzB,MAAM9H,UAAU,GAAG1S,cAAc,CAACE,aAAa,CAAC,KAAK+B,OAAN,CAAd,CAAjC;;MACA,KAAKuY,iBAAL,GAA0B9H,UAAU,IAAI,KAAKpN,SAA7C;IACH;;IACD,OAAO,KAAKkV,iBAAZ;EACH;EACD;;;EACA6G,wBAAwB,GAAG;IACvB,MAAMI,YAAY,GAAG,KAAKP,aAAL,CAChBpC,sBADgB,GAEhB4C,MAFgB,CAET3M,IAAI,IAAIA,IAAI,CAAC5I,UAAL,EAFC,CAArB;;IAGA,KAAKkU,SAAL,CAAeta,OAAf,CAAuBgY,OAAO,IAAIA,OAAO,CAACkF,eAAR,CAAwB,IAAxB,EAA8BxB,YAA9B,CAAlC;EACH;;AA1da;AA4dlB;AACA;AACA;AACA;AACA;;;AACA,SAASc,0BAAT,CAAoCpe,UAApC,EAAgDc,QAAhD,EAA0D;EACtD,MAAM;IAAE1C,GAAF;IAAO+B,MAAP;IAAeE;EAAf,IAA0BL,UAAhC;EACA,MAAMgB,UAAU,GAAGX,MAAM,GAAGob,0BAA5B;;EACA,IAAI3a,QAAQ,IAAI1C,GAAG,GAAG4C,UAAlB,IAAgCF,QAAQ,IAAI1C,GAAG,GAAG4C,UAAtD,EAAkE;IAC9D,OAAO;IAAE;IAAT;EACH,CAFD,MAGK,IAAIF,QAAQ,IAAIX,MAAM,GAAGa,UAArB,IAAmCF,QAAQ,IAAIX,MAAM,GAAGa,UAA5D,EAAwE;IACzE,OAAO;IAAE;IAAT;EACH;;EACD,OAAO;EAAE;EAAT;AACH;AACD;AACA;AACA;AACA;AACA;;;AACA,SAASqd,4BAAT,CAAsCre,UAAtC,EAAkDa,QAAlD,EAA4D;EACxD,MAAM;IAAEvC,IAAF;IAAQ4B,KAAR;IAAeE;EAAf,IAAyBJ,UAA/B;EACA,MAAMe,UAAU,GAAGX,KAAK,GAAGqb,0BAA3B;;EACA,IAAI5a,QAAQ,IAAIvC,IAAI,GAAGyC,UAAnB,IAAiCF,QAAQ,IAAIvC,IAAI,GAAGyC,UAAxD,EAAoE;IAChE,OAAO;IAAE;IAAT;EACH,CAFD,MAGK,IAAIF,QAAQ,IAAIX,KAAK,GAAGa,UAApB,IAAkCF,QAAQ,IAAIX,KAAK,GAAGa,UAA1D,EAAsE;IACvE,OAAO;IAAE;IAAT;EACH;;EACD,OAAO;EAAE;EAAT;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASmd,0BAAT,CAAoCpgB,OAApC,EAA6CkC,UAA7C,EAAyDa,QAAzD,EAAmEC,QAAnE,EAA6E;EACzE,MAAMke,gBAAgB,GAAGZ,0BAA0B,CAACpe,UAAD,EAAac,QAAb,CAAnD;EACA,MAAMme,kBAAkB,GAAGZ,4BAA4B,CAACre,UAAD,EAAaa,QAAb,CAAvD;EACA,IAAImd,uBAAuB,GAAG;EAAE;EAAhC;EACA,IAAIC,yBAAyB,GAAG;EAAE;EAAlC,CAJyE,CAKzE;EACA;EACA;EACA;;EACA,IAAIe,gBAAJ,EAAsB;IAClB,MAAMnd,SAAS,GAAG/D,OAAO,CAAC+D,SAA1B;;IACA,IAAImd,gBAAgB,KAAK;IAAE;IAA3B,EAAiE;MAC7D,IAAInd,SAAS,GAAG,CAAhB,EAAmB;QACfmc,uBAAuB,GAAG;QAAE;QAA5B;MACH;IACJ,CAJD,MAKK,IAAIlgB,OAAO,CAACohB,YAAR,GAAuBrd,SAAvB,GAAmC/D,OAAO,CAACqhB,YAA/C,EAA6D;MAC9DnB,uBAAuB,GAAG;MAAE;MAA5B;IACH;EACJ;;EACD,IAAIiB,kBAAJ,EAAwB;IACpB,MAAMnd,UAAU,GAAGhE,OAAO,CAACgE,UAA3B;;IACA,IAAImd,kBAAkB,KAAK;IAAE;IAA7B,EAAuE;MACnE,IAAInd,UAAU,GAAG,CAAjB,EAAoB;QAChBmc,yBAAyB,GAAG;QAAE;QAA9B;MACH;IACJ,CAJD,MAKK,IAAIngB,OAAO,CAACshB,WAAR,GAAsBtd,UAAtB,GAAmChE,OAAO,CAACuhB,WAA/C,EAA4D;MAC7DpB,yBAAyB,GAAG;MAAE;MAA9B;IACH;EACJ;;EACD,OAAO,CAACD,uBAAD,EAA0BC,yBAA1B,CAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMqB,2BAA2B,GAAG1jB,+BAA+B,CAAC;EAChEyI,OAAO,EAAE,KADuD;EAEhEkb,OAAO,EAAE;AAFuD,CAAD,CAAnE;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAN,CAAuB;EACnBte,WAAW,CAAC0D,OAAD,EAAUzD,SAAV,EAAqB;IAC5B,KAAKyD,OAAL,GAAeA,OAAf;IACA;;IACA,KAAK6a,cAAL,GAAsB,IAAIhb,GAAJ,EAAtB;IACA;;IACA,KAAKib,cAAL,GAAsB,IAAIjb,GAAJ,EAAtB;IACA;;IACA,KAAKkb,oBAAL,GAA4B,EAA5B;IACA;;IACA,KAAKC,gBAAL,GAAwB,IAAIve,GAAJ,EAAxB;IACA;AACR;AACA;AACA;;IACQ,KAAKwe,kBAAL,GAA2BjP,IAAD,IAAUA,IAAI,CAAC5I,UAAL,EAApC;IACA;AACR;AACA;AACA;;;IACQ,KAAK4H,WAAL,GAAmB,IAAIxT,OAAJ,EAAnB;IACA;AACR;AACA;AACA;;IACQ,KAAKyT,SAAL,GAAiB,IAAIzT,OAAJ,EAAjB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAK0jB,MAAL,GAAc,IAAI1jB,OAAJ,EAAd;IACA;AACR;AACA;AACA;;IACQ,KAAK2jB,4BAAL,GAAqC/d,KAAD,IAAW;MAC3C,IAAI,KAAK2d,oBAAL,CAA0Brc,MAA1B,GAAmC,CAAvC,EAA0C;QACtCtB,KAAK,CAACkG,cAAN;MACH;IACJ,CAJD;IAKA;;;IACA,KAAK8X,4BAAL,GAAqChe,KAAD,IAAW;MAC3C,IAAI,KAAK2d,oBAAL,CAA0Brc,MAA1B,GAAmC,CAAvC,EAA0C;QACtC;QACA;QACA;QACA,IAAI,KAAKqc,oBAAL,CAA0BM,IAA1B,CAA+B,KAAKJ,kBAApC,CAAJ,EAA6D;UACzD7d,KAAK,CAACkG,cAAN;QACH;;QACD,KAAK0H,WAAL,CAAiBpJ,IAAjB,CAAsBxE,KAAtB;MACH;IACJ,CAVD;;IAWA,KAAKb,SAAL,GAAiBA,SAAjB;EACH;EACD;;;EACA2b,qBAAqB,CAAC/L,IAAD,EAAO;IACxB,IAAI,CAAC,KAAK0O,cAAL,CAAoB9hB,GAApB,CAAwBoT,IAAxB,CAAL,EAAoC;MAChC,KAAK0O,cAAL,CAAoBnV,GAApB,CAAwByG,IAAxB;IACH;EACJ;EACD;;;EACApH,gBAAgB,CAAC6O,IAAD,EAAO;IACnB,KAAKkH,cAAL,CAAoBpV,GAApB,CAAwBkO,IAAxB,EADmB,CAEnB;IACA;IACA;;;IACA,IAAI,KAAKkH,cAAL,CAAoB9B,IAApB,KAA6B,CAAjC,EAAoC;MAChC,KAAKhZ,OAAL,CAAakG,iBAAb,CAA+B,MAAM;QACjC;QACA;QACA,KAAK3J,SAAL,CAAe4J,gBAAf,CAAgC,WAAhC,EAA6C,KAAKiV,4BAAlD,EAAgFV,2BAAhF;MACH,CAJD;IAKH;EACJ;EACD;;;EACAtC,mBAAmB,CAACjM,IAAD,EAAO;IACtB,KAAK0O,cAAL,CAAoBhT,MAApB,CAA2BsE,IAA3B;EACH;EACD;;;EACA5E,cAAc,CAACqM,IAAD,EAAO;IACjB,KAAKkH,cAAL,CAAoBjT,MAApB,CAA2B+L,IAA3B;;IACA,KAAKlL,YAAL,CAAkBkL,IAAlB;;IACA,IAAI,KAAKkH,cAAL,CAAoB9B,IAApB,KAA6B,CAAjC,EAAoC;MAChC,KAAKzc,SAAL,CAAe8R,mBAAf,CAAmC,WAAnC,EAAgD,KAAK+M,4BAArD,EAAmFV,2BAAnF;IACH;EACJ;EACD;AACJ;AACA;AACA;AACA;;;EACI/O,aAAa,CAACiI,IAAD,EAAOxW,KAAP,EAAc;IACvB;IACA,IAAI,KAAK2d,oBAAL,CAA0B9gB,OAA1B,CAAkC2Z,IAAlC,IAA0C,CAAC,CAA/C,EAAkD;MAC9C;IACH;;IACD,KAAKmH,oBAAL,CAA0BtF,IAA1B,CAA+B7B,IAA/B;;IACA,IAAI,KAAKmH,oBAAL,CAA0Brc,MAA1B,KAAqC,CAAzC,EAA4C;MACxC,MAAMyK,YAAY,GAAG/L,KAAK,CAAC+B,IAAN,CAAWmc,UAAX,CAAsB,OAAtB,CAArB,CADwC,CAExC;MACA;MACA;;MACA,KAAKN,gBAAL,CACKne,GADL,CACSsM,YAAY,GAAG,UAAH,GAAgB,SADrC,EACgD;QAC5CgF,OAAO,EAAGoN,CAAD,IAAO,KAAKtQ,SAAL,CAAerJ,IAAf,CAAoB2Z,CAApB,CAD4B;QAE5CC,OAAO,EAAE;MAFmC,CADhD,EAKK3e,GALL,CAKS,QALT,EAKmB;QACfsR,OAAO,EAAGoN,CAAD,IAAO,KAAKL,MAAL,CAAYtZ,IAAZ,CAAiB2Z,CAAjB,CADD;QAEf;QACA;QACAC,OAAO,EAAE;MAJM,CALnB,EAWI;MACA;MACA;MACA;MAdJ,CAeK3e,GAfL,CAeS,aAfT,EAewB;QACpBsR,OAAO,EAAE,KAAKgN,4BADM;QAEpBK,OAAO,EAAEd;MAFW,CAfxB,EALwC,CAwBxC;MACA;;;MACA,IAAI,CAACvR,YAAL,EAAmB;QACf,KAAK6R,gBAAL,CAAsBne,GAAtB,CAA0B,WAA1B,EAAuC;UACnCsR,OAAO,EAAGoN,CAAD,IAAO,KAAKvQ,WAAL,CAAiBpJ,IAAjB,CAAsB2Z,CAAtB,CADmB;UAEnCC,OAAO,EAAEd;QAF0B,CAAvC;MAIH;;MACD,KAAK1a,OAAL,CAAakG,iBAAb,CAA+B,MAAM;QACjC,KAAK8U,gBAAL,CAAsBhe,OAAtB,CAA8B,CAACye,MAAD,EAAS3gB,IAAT,KAAkB;UAC5C,KAAKyB,SAAL,CAAe4J,gBAAf,CAAgCrL,IAAhC,EAAsC2gB,MAAM,CAACtN,OAA7C,EAAsDsN,MAAM,CAACD,OAA7D;QACH,CAFD;MAGH,CAJD;IAKH;EACJ;EACD;;;EACA9S,YAAY,CAACkL,IAAD,EAAO;IACf,MAAMqB,KAAK,GAAG,KAAK8F,oBAAL,CAA0B9gB,OAA1B,CAAkC2Z,IAAlC,CAAd;;IACA,IAAIqB,KAAK,GAAG,CAAC,CAAb,EAAgB;MACZ,KAAK8F,oBAAL,CAA0B1H,MAA1B,CAAiC4B,KAAjC,EAAwC,CAAxC;;MACA,IAAI,KAAK8F,oBAAL,CAA0Brc,MAA1B,KAAqC,CAAzC,EAA4C;QACxC,KAAKgd,qBAAL;MACH;IACJ;EACJ;EACD;;;EACAtY,UAAU,CAACwQ,IAAD,EAAO;IACb,OAAO,KAAKmH,oBAAL,CAA0B9gB,OAA1B,CAAkC2Z,IAAlC,IAA0C,CAAC,CAAlD;EACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;EACI1I,QAAQ,CAACvB,UAAD,EAAa;IACjB,MAAMgS,OAAO,GAAG,CAAC,KAAKT,MAAN,CAAhB;;IACA,IAAIvR,UAAU,IAAIA,UAAU,KAAK,KAAKpN,SAAtC,EAAiD;MAC7C;MACA;MACA;MACAof,OAAO,CAAClG,IAAR,CAAa,IAAI7d,UAAJ,CAAgBgkB,QAAD,IAAc;QACtC,OAAO,KAAK5b,OAAL,CAAakG,iBAAb,CAA+B,MAAM;UACxC,MAAM2V,YAAY,GAAG,IAArB;;UACA,MAAM9c,QAAQ,GAAI3B,KAAD,IAAW;YACxB,IAAI,KAAK2d,oBAAL,CAA0Brc,MAA9B,EAAsC;cAClCkd,QAAQ,CAACha,IAAT,CAAcxE,KAAd;YACH;UACJ,CAJD;;UAKAuM,UAAU,CAACxD,gBAAX,CAA4B,QAA5B,EAAsCpH,QAAtC,EAAgD8c,YAAhD;UACA,OAAO,MAAM;YACTlS,UAAU,CAAC0E,mBAAX,CAA+B,QAA/B,EAAyCtP,QAAzC,EAAmD8c,YAAnD;UACH,CAFD;QAGH,CAXM,CAAP;MAYH,CAbY,CAAb;IAcH;;IACD,OAAOhkB,KAAK,CAAC,GAAG8jB,OAAJ,CAAZ;EACH;;EACDG,WAAW,GAAG;IACV,KAAKhB,cAAL,CAAoB9d,OAApB,CAA4B+e,QAAQ,IAAI,KAAKxU,cAAL,CAAoBwU,QAApB,CAAxC;;IACA,KAAKlB,cAAL,CAAoB7d,OAApB,CAA4B+e,QAAQ,IAAI,KAAK3D,mBAAL,CAAyB2D,QAAzB,CAAxC;;IACA,KAAKL,qBAAL;;IACA,KAAK1Q,WAAL,CAAiBvD,QAAjB;IACA,KAAKwD,SAAL,CAAexD,QAAf;EACH;EACD;;;EACAiU,qBAAqB,GAAG;IACpB,KAAKV,gBAAL,CAAsBhe,OAAtB,CAA8B,CAACye,MAAD,EAAS3gB,IAAT,KAAkB;MAC5C,KAAKyB,SAAL,CAAe8R,mBAAf,CAAmCvT,IAAnC,EAAyC2gB,MAAM,CAACtN,OAAhD,EAAyDsN,MAAM,CAACD,OAAhE;IACH,CAFD;;IAGA,KAAKR,gBAAL,CAAsBte,KAAtB;EACH;;AAlMkB;;AAoMvBke,gBAAgB,CAACoB,IAAjB;EAAA,iBAA6GpB,gBAA7G,EAAmG5kB,EAAnG,UAA+IA,EAAE,CAACimB,MAAlJ,GAAmGjmB,EAAnG,UAAqKc,QAArK;AAAA;;AACA8jB,gBAAgB,CAACsB,KAAjB,kBADmGlmB,EACnG;EAAA,OAAiH4kB,gBAAjH;EAAA,SAAiHA,gBAAjH;EAAA,YAA+I;AAA/I;;AACA;EAAA,mDAFmG5kB,EAEnG,mBAA2F4kB,gBAA3F,EAAyH,CAAC;IAC9Gzb,IAAI,EAAElJ,UADwG;IAE9GkmB,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFwG,CAAD,CAAzH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAEjd,IAAI,EAAEnJ,EAAE,CAACimB;IAAX,CAAD,EAAsB;MAAE9c,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QAC7Cld,IAAI,EAAEjJ,MADuC;QAE7CimB,IAAI,EAAE,CAACrlB,QAAD;MAFuC,CAAD;IAA/B,CAAtB,CAAP;EAIH,CARL;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMwlB,cAAc,GAAG;EACnB3Z,kBAAkB,EAAE,CADD;EAEnBkO,+BAA+B,EAAE;AAFd,CAAvB;AAIA;AACA;AACA;;AACA,MAAM0L,QAAN,CAAe;EACXjgB,WAAW,CAACC,SAAD,EAAYyD,OAAZ,EAAqBC,cAArB,EAAqCC,iBAArC,EAAwD;IAC/D,KAAK3D,SAAL,GAAiBA,SAAjB;IACA,KAAKyD,OAAL,GAAeA,OAAf;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIsc,UAAU,CAACtjB,OAAD,EAAUuiB,MAAM,GAAGa,cAAnB,EAAmC;IACzC,OAAO,IAAIxc,OAAJ,CAAY5G,OAAZ,EAAqBuiB,MAArB,EAA6B,KAAKlf,SAAlC,EAA6C,KAAKyD,OAAlD,EAA2D,KAAKC,cAAhE,EAAgF,KAAKC,iBAArF,CAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIuc,cAAc,CAACvjB,OAAD,EAAU;IACpB,OAAO,IAAI4d,WAAJ,CAAgB5d,OAAhB,EAAyB,KAAKgH,iBAA9B,EAAiD,KAAK3D,SAAtD,EAAiE,KAAKyD,OAAtE,EAA+E,KAAKC,cAApF,CAAP;EACH;;AArBU;;AAuBfsc,QAAQ,CAACP,IAAT;EAAA,iBAAqGO,QAArG,EAlDmGvmB,EAkDnG,UAA+Hc,QAA/H,GAlDmGd,EAkDnG,UAAoJA,EAAE,CAACimB,MAAvJ,GAlDmGjmB,EAkDnG,UAA0KoC,EAAE,CAACskB,aAA7K,GAlDmG1mB,EAkDnG,UAAuM4kB,gBAAvM;AAAA;;AACA2B,QAAQ,CAACL,KAAT,kBAnDmGlmB,EAmDnG;EAAA,OAAyGumB,QAAzG;EAAA,SAAyGA,QAAzG;EAAA,YAA+H;AAA/H;;AACA;EAAA,mDApDmGvmB,EAoDnG,mBAA2FumB,QAA3F,EAAiH,CAAC;IACtGpd,IAAI,EAAElJ,UADgG;IAEtGkmB,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFgG,CAAD,CAAjH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAEjd,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QACxBld,IAAI,EAAEjJ,MADkB;QAExBimB,IAAI,EAAE,CAACrlB,QAAD;MAFkB,CAAD;IAA/B,CAAD,EAGW;MAAEqI,IAAI,EAAEnJ,EAAE,CAACimB;IAAX,CAHX,EAGgC;MAAE9c,IAAI,EAAE/G,EAAE,CAACskB;IAAX,CAHhC,EAG4D;MAAEvd,IAAI,EAAEyb;IAAR,CAH5D,CAAP;EAIH,CARL;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM+B,eAAe,GAAG,IAAIxmB,cAAJ,CAAmB,iBAAnB,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMymB,mBAAmB,GAAG,IAAIzmB,cAAJ,CAAmB,kBAAnB,CAA5B;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAM0mB,gBAAN,CAAuB;EACnBvgB,WAAW,GAAG;IACV;IACA,KAAKwgB,MAAL,GAAc,IAAIjd,GAAJ,EAAd;IACA,KAAKqB,SAAL,GAAiB,KAAjB;EACH;EACD;;;EACY,IAARa,QAAQ,GAAG;IACX,OAAO,KAAKb,SAAZ;EACH;;EACW,IAARa,QAAQ,CAAClJ,KAAD,EAAQ;IAChB,KAAKqI,SAAL,GAAiBhK,qBAAqB,CAAC2B,KAAD,CAAtC;EACH;;EACDijB,WAAW,GAAG;IACV,KAAKgB,MAAL,CAAYpgB,KAAZ;EACH;;AAfkB;;AAiBvBmgB,gBAAgB,CAACb,IAAjB;EAAA,iBAA6Ga,gBAA7G;AAAA;;AACAA,gBAAgB,CAACE,IAAjB,kBAlHmG/mB,EAkHnG;EAAA,MAAiG6mB,gBAAjG;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA,WAlHmG7mB,EAkHnG,oBAA8N,CAAC;IAAEgnB,OAAO,EAAEJ,mBAAX;IAAgCK,WAAW,EAAEJ;EAA7C,CAAD,CAA9N;AAAA;;AACA;EAAA,mDAnHmG7mB,EAmHnG,mBAA2F6mB,gBAA3F,EAAyH,CAAC;IAC9G1d,IAAI,EAAE/I,SADwG;IAE9G+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,oBADX;MAECoe,QAAQ,EAAE,kBAFX;MAGCC,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEJ,mBAAX;QAAgCK,WAAW,EAAEJ;MAA7C,CAAD;IAHZ,CAAD;EAFwG,CAAD,CAAzH,QAO4B;IAAE9a,QAAQ,EAAE,CAAC;MACzB5C,IAAI,EAAE9I,KADmB;MAEzB8lB,IAAI,EAAE,CAAC,0BAAD;IAFmB,CAAD;EAAZ,CAP5B;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMiB,eAAe,GAAG,IAAIjnB,cAAJ,CAAmB,iBAAnB,CAAxB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;AACA,SAASknB,iBAAT,CAA2Bxf,IAA3B,EAAiC/C,IAAjC,EAAuC;EACnC,IAAI+C,IAAI,CAACwU,QAAL,KAAkB,CAAtB,EAAyB;IACrB,MAAMiL,KAAK,CAAE,GAAExiB,IAAK,wCAAR,GAAmD,0BAAyB+C,IAAI,CAACU,QAAS,IAA3F,CAAX;EACH;AACJ;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIgf,gBAAgB,GAAG,CAAvB;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,aAAa,GAAG,IAAIrnB,cAAJ,CAAmB,aAAnB,CAAtB;AACA;;AACA,MAAMsnB,WAAN,CAAkB;EACdnhB,WAAW;EACX;EACApD,OAFW,EAEFwkB,QAFE,EAEQC,kBAFR,EAE4BC,iBAF5B,EAE+CC,IAF/C,EAEqDC,MAFrD,EAE6DrC,MAF7D,EAEqE;IAC5E,KAAKviB,OAAL,GAAeA,OAAf;IACA,KAAKykB,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,iBAAL,GAAyBA,iBAAzB;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAIvmB,OAAJ,EAAlB;IACA;AACR;AACA;AACA;AACA;;IACQ,KAAKqhB,WAAL,GAAmB,EAAnB;IACA;AACR;AACA;AACA;;IACQ,KAAKmF,EAAL,GAAW,iBAAgBT,gBAAgB,EAAG,EAA9C;IACA;AACR;AACA;AACA;;IACQ,KAAKtG,cAAL,GAAsB,MAAM,IAA5B;IACA;;;IACA,KAAKC,aAAL,GAAqB,MAAM,IAA3B;IACA;;;IACA,KAAKzV,OAAL,GAAe,IAAInL,YAAJ,EAAf;IACA;AACR;AACA;;IACQ,KAAKiL,OAAL,GAAe,IAAIjL,YAAJ,EAAf;IACA;AACR;AACA;AACA;;IACQ,KAAKkL,MAAL,GAAc,IAAIlL,YAAJ,EAAd;IACA;;IACA,KAAK6gB,MAAL,GAAc,IAAI7gB,YAAJ,EAAd;IACA;AACR;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAK2nB,cAAL,GAAsB,IAAIpe,GAAJ,EAAtB;;IACA,IAAI,OAAOqe,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/Cb,iBAAiB,CAACnkB,OAAO,CAAC+gB,aAAT,EAAwB,aAAxB,CAAjB;IACH;;IACD,KAAKkE,YAAL,GAAoBT,QAAQ,CAACjB,cAAT,CAAwBvjB,OAAxB,CAApB;IACA,KAAKilB,YAAL,CAAkBC,IAAlB,GAAyB,IAAzB;;IACA,IAAI3C,MAAJ,EAAY;MACR,KAAK4C,eAAL,CAAqB5C,MAArB;IACH;;IACD,KAAK0C,YAAL,CAAkBlH,cAAlB,GAAmC,CAACrD,IAAD,EAAOzH,IAAP,KAAgB;MAC/C,OAAO,KAAK8K,cAAL,CAAoBrD,IAAI,CAACwK,IAAzB,EAA+BjS,IAAI,CAACiS,IAApC,CAAP;IACH,CAFD;;IAGA,KAAKD,YAAL,CAAkBjH,aAAlB,GAAkC,CAACjC,KAAD,EAAQrB,IAAR,EAAczH,IAAd,KAAuB;MACrD,OAAO,KAAK+K,aAAL,CAAmBjC,KAAnB,EAA0BrB,IAAI,CAACwK,IAA/B,EAAqCjS,IAAI,CAACiS,IAA1C,CAAP;IACH,CAFD;;IAGA,KAAKE,2BAAL,CAAiC,KAAKH,YAAtC;;IACA,KAAKI,aAAL,CAAmB,KAAKJ,YAAxB;;IACAV,WAAW,CAACe,UAAZ,CAAuB/I,IAAvB,CAA4B,IAA5B;;IACA,IAAIqI,MAAJ,EAAY;MACRA,MAAM,CAAChB,MAAP,CAAcpX,GAAd,CAAkB,IAAlB;IACH;EACJ;EACD;;;EACY,IAAR3D,QAAQ,GAAG;IACX,OAAO,KAAKb,SAAL,IAAmB,CAAC,CAAC,KAAK4c,MAAP,IAAiB,KAAKA,MAAL,CAAY/b,QAAvD;EACH;;EACW,IAARA,QAAQ,CAAClJ,KAAD,EAAQ;IAChB;IACA;IACA;IACA;IACA,KAAKslB,YAAL,CAAkBpc,QAAlB,GAA6B,KAAKb,SAAL,GAAiBhK,qBAAqB,CAAC2B,KAAD,CAAnE;EACH;EACD;;;EACA4lB,OAAO,CAACzS,IAAD,EAAO;IACV,KAAKiS,cAAL,CAAoBvY,GAApB,CAAwBsG,IAAxB;;IACA,IAAI,KAAKmS,YAAL,CAAkB/a,UAAlB,EAAJ,EAAoC;MAChC,KAAKsb,iBAAL;IACH;EACJ;EACD;;;EACAC,UAAU,CAAC3S,IAAD,EAAO;IACb,KAAKiS,cAAL,CAAoBpW,MAApB,CAA2BmE,IAA3B;;IACA,IAAI,KAAKmS,YAAL,CAAkB/a,UAAlB,EAAJ,EAAoC;MAChC,KAAKsb,iBAAL;IACH;EACJ;EACD;;;EACAE,cAAc,GAAG;IACb,OAAOhR,KAAK,CAACkF,IAAN,CAAW,KAAKmL,cAAhB,EAAgCjK,IAAhC,CAAqC,CAACmC,CAAD,EAAIC,CAAJ,KAAU;MAClD,MAAMyI,gBAAgB,GAAG1I,CAAC,CAAC2I,QAAF,CACpBxZ,iBADoB,GAEpByZ,uBAFoB,CAEI3I,CAAC,CAAC0I,QAAF,CAAWxZ,iBAAX,EAFJ,CAAzB,CADkD,CAIlD;MACA;MACA;;;MACA,OAAOuZ,gBAAgB,GAAGG,IAAI,CAACC,2BAAxB,GAAsD,CAAC,CAAvD,GAA2D,CAAlE;IACH,CARM,CAAP;EASH;;EACDnD,WAAW,GAAG;IACV,MAAM7G,KAAK,GAAGwI,WAAW,CAACe,UAAZ,CAAuBvkB,OAAvB,CAA+B,IAA/B,CAAd;;IACA,IAAIgb,KAAK,GAAG,CAAC,CAAb,EAAgB;MACZwI,WAAW,CAACe,UAAZ,CAAuBnL,MAAvB,CAA8B4B,KAA9B,EAAqC,CAArC;IACH;;IACD,IAAI,KAAK6I,MAAT,EAAiB;MACb,KAAKA,MAAL,CAAYhB,MAAZ,CAAmBjV,MAAnB,CAA0B,IAA1B;IACH;;IACD,KAAKoW,cAAL,CAAoBvhB,KAApB;;IACA,KAAKyhB,YAAL,CAAkBlX,OAAlB;;IACA,KAAK8W,UAAL,CAAgBnc,IAAhB;;IACA,KAAKmc,UAAL,CAAgBtW,QAAhB;EACH;EACD;;;EACA6W,2BAA2B,CAACY,GAAD,EAAM;IAC7B,IAAI,KAAKrB,IAAT,EAAe;MACX,KAAKA,IAAL,CAAUjX,MAAV,CACKiR,IADL,CACU9f,SAAS,CAAC,KAAK8lB,IAAL,CAAUhlB,KAAX,CADnB,EACsCf,SAAS,CAAC,KAAKimB,UAAN,CAD/C,EAEKlX,SAFL,CAEehO,KAAK,IAAIqmB,GAAG,CAACpX,aAAJ,CAAkBjP,KAAlB,CAFxB;IAGH;;IACDqmB,GAAG,CAAC/d,aAAJ,CAAkB0F,SAAlB,CAA4B,MAAM;MAC9B,MAAMqN,QAAQ,GAAG9c,WAAW,CAAC,KAAKyhB,WAAN,CAAX,CAA8B7gB,GAA9B,CAAkCmU,IAAI,IAAI;QACvD,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;UAC1B,MAAMgT,qBAAqB,GAAG1B,WAAW,CAACe,UAAZ,CAAuB/jB,IAAvB,CAA4B2kB,IAAI,IAAIA,IAAI,CAACpB,EAAL,KAAY7R,IAAhD,CAA9B;;UACA,IAAI,CAACgT,qBAAD,KAA2B,OAAOjB,SAAP,KAAqB,WAArB,IAAoCA,SAA/D,CAAJ,EAA+E;YAC3EmB,OAAO,CAACC,IAAR,CAAc,2DAA0DnT,IAAK,GAA7E;UACH;;UACD,OAAOgT,qBAAP;QACH;;QACD,OAAOhT,IAAP;MACH,CATgB,CAAjB;;MAUA,IAAI,KAAK2R,MAAT,EAAiB;QACb,KAAKA,MAAL,CAAYhB,MAAZ,CAAmB9f,OAAnB,CAA2BmP,IAAI,IAAI;UAC/B,IAAI+H,QAAQ,CAACja,OAAT,CAAiBkS,IAAjB,MAA2B,CAAC,CAAhC,EAAmC;YAC/B+H,QAAQ,CAACuB,IAAT,CAActJ,IAAd;UACH;QACJ,CAJD;MAKH,CAjB6B,CAkB9B;MACA;;;MACA,IAAI,CAAC,KAAKoT,0BAAV,EAAsC;QAClC,MAAMC,iBAAiB,GAAG,KAAK5B,iBAAL,CACrB6B,2BADqB,CACO,KAAKvmB,OADZ,EAErBlB,GAFqB,CAEjB0nB,UAAU,IAAIA,UAAU,CAACC,aAAX,GAA2B1F,aAFxB,CAA1B;;QAGA,KAAKkE,YAAL,CAAkBlG,qBAAlB,CAAwCuH,iBAAxC,EAJkC,CAKlC;QACA;;;QACA,KAAKD,0BAAL,GAAkC,IAAlC;MACH;;MACDL,GAAG,CAACnd,QAAJ,GAAe,KAAKA,QAApB;MACAmd,GAAG,CAACrP,QAAJ,GAAe,KAAKA,QAApB;MACAqP,GAAG,CAACxS,eAAJ,GAAsBxV,qBAAqB,CAAC,KAAKwV,eAAN,CAA3C;MACAwS,GAAG,CAACnI,kBAAJ,GAAyB7f,qBAAqB,CAAC,KAAK6f,kBAAN,CAA9C;MACAmI,GAAG,CAAClI,cAAJ,GAAqB3f,oBAAoB,CAAC,KAAK2f,cAAN,EAAsB,CAAtB,CAAzC;MACAkI,GAAG,CACErG,WADL,CACiB3E,QAAQ,CAACyE,MAAT,CAAgBxM,IAAI,IAAIA,IAAI,IAAIA,IAAI,KAAK,IAAzC,EAA+CnU,GAA/C,CAAmDonB,IAAI,IAAIA,IAAI,CAACjB,YAAhE,CADjB,EAEKrF,eAFL,CAEqB,KAAKpF,WAF1B;IAGH,CArCD;EAsCH;EACD;;;EACA6K,aAAa,CAACW,GAAD,EAAM;IACfA,GAAG,CAAC/d,aAAJ,CAAkB0F,SAAlB,CAA4B,MAAM;MAC9B,KAAK6X,iBAAL;;MACA,KAAKf,kBAAL,CAAwBiC,YAAxB;IACH,CAHD;IAIAV,GAAG,CAAC3d,OAAJ,CAAYsF,SAAZ,CAAsBzJ,KAAK,IAAI;MAC3B,KAAKmE,OAAL,CAAase,IAAb,CAAkB;QACd5c,SAAS,EAAE,IADG;QAEd+I,IAAI,EAAE5O,KAAK,CAAC4O,IAAN,CAAWoS,IAFH;QAGdvS,YAAY,EAAEzO,KAAK,CAACyO;MAHN,CAAlB;IAKH,CAND;IAOAqT,GAAG,CAAC1d,MAAJ,CAAWqF,SAAX,CAAqBzJ,KAAK,IAAI;MAC1B,KAAKoE,MAAL,CAAYqe,IAAZ,CAAiB;QACb5c,SAAS,EAAE,IADE;QAEb+I,IAAI,EAAE5O,KAAK,CAAC4O,IAAN,CAAWoS;MAFJ,CAAjB;;MAIA,KAAKT,kBAAL,CAAwBiC,YAAxB;IACH,CAND;IAOAV,GAAG,CAAC/H,MAAJ,CAAWtQ,SAAX,CAAqBzJ,KAAK,IAAI;MAC1B,KAAK+Z,MAAL,CAAY0I,IAAZ,CAAiB;QACb5T,aAAa,EAAE7O,KAAK,CAAC6O,aADR;QAEbJ,YAAY,EAAEzO,KAAK,CAACyO,YAFP;QAGb5I,SAAS,EAAE,IAHE;QAIb+I,IAAI,EAAE5O,KAAK,CAAC4O,IAAN,CAAWoS;MAJJ,CAAjB;IAMH,CAPD;IAQAc,GAAG,CAACzd,OAAJ,CAAYoF,SAAZ,CAAsBiZ,SAAS,IAAI;MAC/B,KAAKre,OAAL,CAAaoe,IAAb,CAAkB;QACd5T,aAAa,EAAE6T,SAAS,CAAC7T,aADX;QAEdJ,YAAY,EAAEiU,SAAS,CAACjU,YAFV;QAGdK,iBAAiB,EAAE4T,SAAS,CAAC5T,iBAAV,CAA4BkS,IAHjC;QAIdnb,SAAS,EAAE6c,SAAS,CAAC7c,SAAV,CAAoBmb,IAJjB;QAKdpS,IAAI,EAAE8T,SAAS,CAAC9T,IAAV,CAAeoS,IALP;QAMdtS,sBAAsB,EAAEgU,SAAS,CAAChU,sBANpB;QAOdzH,QAAQ,EAAEyb,SAAS,CAACzb,QAPN;QAQd6E,SAAS,EAAE4W,SAAS,CAAC5W,SARP;QASd9L,KAAK,EAAE0iB,SAAS,CAAC1iB;MATH,CAAlB,EAD+B,CAY/B;MACA;;MACA,KAAKugB,kBAAL,CAAwBiC,YAAxB;IACH,CAfD;EAgBH;EACD;;;EACAvB,eAAe,CAAC5C,MAAD,EAAS;IACpB,MAAM;MAAE5L,QAAF;MAAYkQ,gBAAZ;MAA8BrT,eAA9B;MAA+CsT,sBAA/C;MAAuEC;IAAvE,IAA2FxE,MAAjG;IACA,KAAK1Z,QAAL,GAAgBge,gBAAgB,IAAI,IAApB,GAA2B,KAA3B,GAAmCA,gBAAnD;IACA,KAAKrT,eAAL,GAAuBA,eAAe,IAAI,IAAnB,GAA0B,KAA1B,GAAkCA,eAAzD;IACA,KAAKqK,kBAAL,GAA0BiJ,sBAAsB,IAAI,IAA1B,GAAiC,KAAjC,GAAyCA,sBAAnE;IACA,KAAKtM,WAAL,GAAmBuM,eAAe,IAAI,UAAtC;;IACA,IAAIpQ,QAAJ,EAAc;MACV,KAAKA,QAAL,GAAgBA,QAAhB;IACH;EACJ;EACD;;;EACA6O,iBAAiB,GAAG;IAChB,KAAKP,YAAL,CAAkBpK,SAAlB,CAA4B,KAAK6K,cAAL,GAAsB5mB,GAAtB,CAA0BgU,IAAI,IAAIA,IAAI,CAAC8S,QAAvC,CAA5B;EACH;;AAlOa;AAoOlB;;;AACArB,WAAW,CAACe,UAAZ,GAAyB,EAAzB;;AACAf,WAAW,CAACzB,IAAZ;EAAA,iBAAwGyB,WAAxG,EApZmGznB,EAoZnG,mBAAqIA,EAAE,CAACkqB,UAAxI,GApZmGlqB,EAoZnG,mBAA+JumB,QAA/J,GApZmGvmB,EAoZnG,mBAAoLA,EAAE,CAACmqB,iBAAvL,GApZmGnqB,EAoZnG,mBAAqNoC,EAAE,CAACgoB,gBAAxN,GApZmGpqB,EAoZnG,mBAAqPsC,EAAE,CAAC+nB,cAAxP,MApZmGrqB,EAoZnG,mBAAmS4mB,mBAAnS,OApZmG5mB,EAoZnG,mBAAmWonB,eAAnW;AAAA;;AACAK,WAAW,CAACV,IAAZ,kBArZmG/mB,EAqZnG;EAAA,MAA4FynB,WAA5F;EAAA;EAAA;EAAA;EAAA;IAAA;MArZmGznB,EAqZnG;MArZmGA,EAqZnG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WArZmGA,EAqZnG,oBAA+mC,CACvmC;EACA;IAAEgnB,OAAO,EAAEJ,mBAAX;IAAgC0D,QAAQ,EAAEja;EAA1C,CAFumC,EAGvmC;IAAE2W,OAAO,EAAEQ,aAAX;IAA0BP,WAAW,EAAEQ;EAAvC,CAHumC,CAA/mC;AAAA;;AAKA;EAAA,mDA1ZmGznB,EA0ZnG,mBAA2FynB,WAA3F,EAAoH,CAAC;IACzGte,IAAI,EAAE/I,SADmG;IAEzG+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,8BADX;MAECoe,QAAQ,EAAE,aAFX;MAGCC,SAAS,EAAE,CACP;MACA;QAAEH,OAAO,EAAEJ,mBAAX;QAAgC0D,QAAQ,EAAEja;MAA1C,CAFO,EAGP;QAAE2W,OAAO,EAAEQ,aAAX;QAA0BP,WAAW,EAAEQ;MAAvC,CAHO,CAHZ;MAQC8C,IAAI,EAAE;QACF,SAAS,eADP;QAEF,aAAa,IAFX;QAGF,kCAAkC,UAHhC;QAIF,kCAAkC,2BAJhC;QAKF,mCAAmC;MALjC;IARP,CAAD;EAFmG,CAAD,CAApH,EAkB4B,YAAY;IAChC,OAAO,CAAC;MAAEphB,IAAI,EAAEnJ,EAAE,CAACkqB;IAAX,CAAD,EAA0B;MAAE/gB,IAAI,EAAEod;IAAR,CAA1B,EAA8C;MAAEpd,IAAI,EAAEnJ,EAAE,CAACmqB;IAAX,CAA9C,EAA8E;MAAEhhB,IAAI,EAAE/G,EAAE,CAACgoB;IAAX,CAA9E,EAA6G;MAAEjhB,IAAI,EAAE7G,EAAE,CAAC+nB,cAAX;MAA2BhE,UAAU,EAAE,CAAC;QAC5Ild,IAAI,EAAE5I;MADsI,CAAD;IAAvC,CAA7G,EAEW;MAAE4I,IAAI,EAAE0d,gBAAR;MAA0BR,UAAU,EAAE,CAAC;QACzCld,IAAI,EAAE5I;MADmC,CAAD,EAEzC;QACC4I,IAAI,EAAEjJ,MADP;QAECimB,IAAI,EAAE,CAACS,mBAAD;MAFP,CAFyC,EAKzC;QACCzd,IAAI,EAAE3I;MADP,CALyC;IAAtC,CAFX,EASW;MAAE2I,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QAClCld,IAAI,EAAE5I;MAD4B,CAAD,EAElC;QACC4I,IAAI,EAAEjJ,MADP;QAECimB,IAAI,EAAE,CAACiB,eAAD;MAFP,CAFkC;IAA/B,CATX,CAAP;EAeH,CAlCL,EAkCuB;IAAEvE,WAAW,EAAE,CAAC;MACvB1Z,IAAI,EAAE9I,KADiB;MAEvB8lB,IAAI,EAAE,CAAC,wBAAD;IAFiB,CAAD,CAAf;IAGPiC,IAAI,EAAE,CAAC;MACPjf,IAAI,EAAE9I,KADC;MAEP8lB,IAAI,EAAE,CAAC,iBAAD;IAFC,CAAD,CAHC;IAMPzI,WAAW,EAAE,CAAC;MACdvU,IAAI,EAAE9I,KADQ;MAEd8lB,IAAI,EAAE,CAAC,wBAAD;IAFQ,CAAD,CANN;IASP6B,EAAE,EAAE,CAAC;MACL7e,IAAI,EAAE9I;IADD,CAAD,CATG;IAWPwZ,QAAQ,EAAE,CAAC;MACX1Q,IAAI,EAAE9I,KADK;MAEX8lB,IAAI,EAAE,CAAC,qBAAD;IAFK,CAAD,CAXH;IAcPpa,QAAQ,EAAE,CAAC;MACX5C,IAAI,EAAE9I,KADK;MAEX8lB,IAAI,EAAE,CAAC,qBAAD;IAFK,CAAD,CAdH;IAiBPzP,eAAe,EAAE,CAAC;MAClBvN,IAAI,EAAE9I,KADY;MAElB8lB,IAAI,EAAE,CAAC,4BAAD;IAFY,CAAD,CAjBV;IAoBPlF,cAAc,EAAE,CAAC;MACjB9X,IAAI,EAAE9I,KADW;MAEjB8lB,IAAI,EAAE,CAAC,2BAAD;IAFW,CAAD,CApBT;IAuBPjF,aAAa,EAAE,CAAC;MAChB/X,IAAI,EAAE9I,KADU;MAEhB8lB,IAAI,EAAE,CAAC,0BAAD;IAFU,CAAD,CAvBR;IA0BPpF,kBAAkB,EAAE,CAAC;MACrB5X,IAAI,EAAE9I,KADe;MAErB8lB,IAAI,EAAE,CAAC,+BAAD;IAFe,CAAD,CA1Bb;IA6BPnF,cAAc,EAAE,CAAC;MACjB7X,IAAI,EAAE9I,KADW;MAEjB8lB,IAAI,EAAE,CAAC,2BAAD;IAFW,CAAD,CA7BT;IAgCP1a,OAAO,EAAE,CAAC;MACVtC,IAAI,EAAE1I,MADI;MAEV0lB,IAAI,EAAE,CAAC,oBAAD;IAFI,CAAD,CAhCF;IAmCP5a,OAAO,EAAE,CAAC;MACVpC,IAAI,EAAE1I,MADI;MAEV0lB,IAAI,EAAE,CAAC,oBAAD;IAFI,CAAD,CAnCF;IAsCP3a,MAAM,EAAE,CAAC;MACTrC,IAAI,EAAE1I,MADG;MAET0lB,IAAI,EAAE,CAAC,mBAAD;IAFG,CAAD,CAtCD;IAyCPhF,MAAM,EAAE,CAAC;MACThY,IAAI,EAAE1I,MADG;MAET0lB,IAAI,EAAE,CAAC,mBAAD;IAFG,CAAD;EAzCD,CAlCvB;AAAA;AAgFA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMqE,eAAe,GAAG,IAAIrqB,cAAJ,CAAmB,eAAnB,CAAxB;AACA;;AACA,MAAMsqB,aAAN,CAAoB;EAChBnkB,WAAW,CAACpD,OAAD,EAAUwnB,UAAV,EAAsB;IAC7B,KAAKxnB,OAAL,GAAeA,OAAf;IACA;;IACA,KAAKynB,aAAL,GAAqB,IAAInpB,OAAJ,EAArB;IACA,KAAK0J,SAAL,GAAiB,KAAjB;;IACA,IAAI,OAAOgd,SAAP,KAAqB,WAArB,IAAoCA,SAAxC,EAAmD;MAC/Cb,iBAAiB,CAACnkB,OAAO,CAAC+gB,aAAT,EAAwB,eAAxB,CAAjB;IACH;;IACD,KAAK2G,WAAL,GAAmBF,UAAnB;EACH;EACD;;;EACY,IAAR3e,QAAQ,GAAG;IACX,OAAO,KAAKb,SAAZ;EACH;;EACW,IAARa,QAAQ,CAAClJ,KAAD,EAAQ;IAChB,KAAKqI,SAAL,GAAiBhK,qBAAqB,CAAC2B,KAAD,CAAtC;;IACA,KAAK8nB,aAAL,CAAmB/e,IAAnB,CAAwB,IAAxB;EACH;;EACDka,WAAW,GAAG;IACV,KAAK6E,aAAL,CAAmBlZ,QAAnB;EACH;;AArBe;;AAuBpBgZ,aAAa,CAACzE,IAAd;EAAA,iBAA0GyE,aAA1G,EA/gBmGzqB,EA+gBnG,mBAAyIA,EAAE,CAACkqB,UAA5I,GA/gBmGlqB,EA+gBnG,mBAAmK2mB,eAAnK;AAAA;;AACA8D,aAAa,CAAC1D,IAAd,kBAhhBmG/mB,EAghBnG;EAAA,MAA8FyqB,aAA9F;EAAA;EAAA;EAAA;IAAA;EAAA;EAAA,WAhhBmGzqB,EAghBnG,oBAA+P,CAAC;IAAEgnB,OAAO,EAAEwD,eAAX;IAA4BvD,WAAW,EAAEwD;EAAzC,CAAD,CAA/P;AAAA;;AACA;EAAA,mDAjhBmGzqB,EAihBnG,mBAA2FyqB,aAA3F,EAAsH,CAAC;IAC3GthB,IAAI,EAAE/I,SADqG;IAE3G+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,iBADX;MAECyhB,IAAI,EAAE;QACF,SAAS;MADP,CAFP;MAKCpD,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEwD,eAAX;QAA4BvD,WAAW,EAAEwD;MAAzC,CAAD;IALZ,CAAD;EAFqG,CAAD,CAAtH,EAS4B,YAAY;IAChC,OAAO,CAAC;MAAEthB,IAAI,EAAEnJ,EAAE,CAACkqB;IAAX,CAAD,EAA0B;MAAE/gB,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QACjDld,IAAI,EAAEjJ,MAD2C;QAEjDimB,IAAI,EAAE,CAACQ,eAAD;MAF2C,CAAD,EAGjD;QACCxd,IAAI,EAAE5I;MADP,CAHiD,EAKjD;QACC4I,IAAI,EAAE3I;MADP,CALiD;IAA/B,CAA1B,CAAP;EAQH,CAlBL,EAkBuB;IAAEuL,QAAQ,EAAE,CAAC;MACpB5C,IAAI,EAAE9I,KADc;MAEpB8lB,IAAI,EAAE,CAAC,uBAAD;IAFc,CAAD;EAAZ,CAlBvB;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM0E,oBAAoB,GAAG,IAAI1qB,cAAJ,CAAmB,oBAAnB,CAA7B;AACA;AACA;AACA;AACA;;AACA,MAAM2qB,kBAAN,CAAyB;EACrBxkB,WAAW,CAACykB,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;EACH;;AAHoB;;AAKzBD,kBAAkB,CAAC9E,IAAnB;EAAA,iBAA+G8E,kBAA/G,EA9jBmG9qB,EA8jBnG,mBAAmJA,EAAE,CAACgrB,WAAtJ;AAAA;;AACAF,kBAAkB,CAAC/D,IAAnB,kBA/jBmG/mB,EA+jBnG;EAAA,MAAmG8qB,kBAAnG;EAAA;EAAA;IAAA;EAAA;EAAA,WA/jBmG9qB,EA+jBnG,oBAAyM,CAAC;IAAEgnB,OAAO,EAAE6D,oBAAX;IAAiC5D,WAAW,EAAE6D;EAA9C,CAAD,CAAzM;AAAA;;AACA;EAAA,mDAhkBmG9qB,EAgkBnG,mBAA2F8qB,kBAA3F,EAA2H,CAAC;IAChH3hB,IAAI,EAAE/I,SAD0G;IAEhH+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,iCADX;MAECqe,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAE6D,oBAAX;QAAiC5D,WAAW,EAAE6D;MAA9C,CAAD;IAFZ,CAAD;EAF0G,CAAD,CAA3H,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAE3hB,IAAI,EAAEnJ,EAAE,CAACgrB;IAAX,CAAD,CAAP;EAAoC,CAN9E,EAMgG;IAAE5C,IAAI,EAAE,CAAC;MACzFjf,IAAI,EAAE9I;IADmF,CAAD;EAAR,CANhG;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAM4qB,gBAAgB,GAAG,IAAI9qB,cAAJ,CAAmB,gBAAnB,CAAzB;AACA;AACA;AACA;AACA;;AACA,MAAM+qB,cAAN,CAAqB;EACjB5kB,WAAW,CAACykB,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB;IACA,KAAKI,UAAL,GAAkB,KAAlB;EACH;EACD;;;EACa,IAAT3V,SAAS,GAAG;IACZ,OAAO,KAAK2V,UAAZ;EACH;;EACY,IAAT3V,SAAS,CAAC3S,KAAD,EAAQ;IACjB,KAAKsoB,UAAL,GAAkBjqB,qBAAqB,CAAC2B,KAAD,CAAvC;EACH;;AAXgB;;AAarBqoB,cAAc,CAAClF,IAAf;EAAA,iBAA2GkF,cAA3G,EAxmBmGlrB,EAwmBnG,mBAA2IA,EAAE,CAACgrB,WAA9I;AAAA;;AACAE,cAAc,CAACnE,IAAf,kBAzmBmG/mB,EAymBnG;EAAA,MAA+FkrB,cAA/F;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAzmBmGlrB,EAymBnG,oBAAqN,CAAC;IAAEgnB,OAAO,EAAEiE,gBAAX;IAA6BhE,WAAW,EAAEiE;EAA1C,CAAD,CAArN;AAAA;;AACA;EAAA,mDA1mBmGlrB,EA0mBnG,mBAA2FkrB,cAA3F,EAAuH,CAAC;IAC5G/hB,IAAI,EAAE/I,SADsG;IAE5G+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,6BADX;MAECqe,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEiE,gBAAX;QAA6BhE,WAAW,EAAEiE;MAA1C,CAAD;IAFZ,CAAD;EAFsG,CAAD,CAAvH,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAE/hB,IAAI,EAAEnJ,EAAE,CAACgrB;IAAX,CAAD,CAAP;EAAoC,CAN9E,EAMgG;IAAE5C,IAAI,EAAE,CAAC;MACzFjf,IAAI,EAAE9I;IADmF,CAAD,CAAR;IAEhFmV,SAAS,EAAE,CAAC;MACZrM,IAAI,EAAE9I;IADM,CAAD;EAFqE,CANhG;AAAA;;AAYA,MAAM+qB,eAAe,GAAG,UAAxB;AACA;;AACA,MAAMC,OAAN,CAAc;EACV/kB,WAAW;EACX;EACApD,OAFW;EAGX;EACAmQ,aAJW;EAKX;AACJ;AACA;AACA;EACI9M,SATW,EASAyD,OATA,EASSshB,iBATT,EAS4B7F,MAT5B,EASoCoC,IATpC,EAS0CH,QAT1C,EASoDC,kBATpD,EASwE4D,WATxE,EASqFX,WATrF,EASkG;IACzG,KAAK1nB,OAAL,GAAeA,OAAf;IACA,KAAKmQ,aAAL,GAAqBA,aAArB;IACA,KAAKrJ,OAAL,GAAeA,OAAf;IACA,KAAKshB,iBAAL,GAAyBA,iBAAzB;IACA,KAAKzD,IAAL,GAAYA,IAAZ;IACA,KAAKF,kBAAL,GAA0BA,kBAA1B;IACA,KAAK4D,WAAL,GAAmBA,WAAnB;IACA,KAAKX,WAAL,GAAmBA,WAAnB;IACA,KAAK7C,UAAL,GAAkB,IAAIvmB,OAAJ,EAAlB;IACA;;IACA,KAAK4J,OAAL,GAAe,IAAI9K,YAAJ,EAAf;IACA;;IACA,KAAK+K,QAAL,GAAgB,IAAI/K,YAAJ,EAAhB;IACA;;IACA,KAAKgL,KAAL,GAAa,IAAIhL,YAAJ,EAAb;IACA;;IACA,KAAKiL,OAAL,GAAe,IAAIjL,YAAJ,EAAf;IACA;;IACA,KAAKkL,MAAL,GAAc,IAAIlL,YAAJ,EAAd;IACA;;IACA,KAAKmL,OAAL,GAAe,IAAInL,YAAJ,EAAf;IACA;AACR;AACA;AACA;;IACQ,KAAKoL,KAAL,GAAa,IAAI9J,UAAJ,CAAgBgkB,QAAD,IAAc;MACtC,MAAM4F,YAAY,GAAG,KAAK1C,QAAL,CAAcpd,KAAd,CAChBmW,IADgB,CACX7f,GAAG,CAACypB,UAAU,KAAK;QACzBhpB,MAAM,EAAE,IADiB;QAEzB0J,eAAe,EAAEsf,UAAU,CAACtf,eAFH;QAGzB/E,KAAK,EAAEqkB,UAAU,CAACrkB,KAHO;QAIzBmH,KAAK,EAAEkd,UAAU,CAACld,KAJO;QAKzBF,QAAQ,EAAEod,UAAU,CAACpd;MALI,CAAL,CAAX,CADQ,EAQhBwC,SARgB,CAQN+U,QARM,CAArB;;MASA,OAAO,MAAM;QACT4F,YAAY,CAAC7a,WAAb;MACH,CAFD;IAGH,CAbY,CAAb;IAcA,KAAKmY,QAAL,GAAgBpB,QAAQ,CAAClB,UAAT,CAAoBtjB,OAApB,EAA6B;MACzCyJ,kBAAkB,EAAE8Y,MAAM,IAAIA,MAAM,CAAC9Y,kBAAP,IAA6B,IAAvC,GAA8C8Y,MAAM,CAAC9Y,kBAArD,GAA0E,CADrD;MAEzCkO,+BAA+B,EAAE4K,MAAM,IAAIA,MAAM,CAAC5K,+BAAP,IAA0C,IAApD,GAC3B4K,MAAM,CAAC5K,+BADoB,GAE3B,CAJmC;MAKzCpD,MAAM,EAAEgO,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAK,KAAK,CAAnC,GAAuC,KAAK,CAA5C,GAAgDA,MAAM,CAAChO;IALtB,CAA7B,CAAhB;IAOA,KAAKqR,QAAL,CAAcV,IAAd,GAAqB,IAArB,CA/CyG,CAgDzG;IACA;IACA;;IACAiD,OAAO,CAACvG,cAAR,CAAuBrF,IAAvB,CAA4B,IAA5B;;IACA,IAAIgG,MAAJ,EAAY;MACR,KAAK4C,eAAL,CAAqB5C,MAArB;IACH,CAtDwG,CAuDzG;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAIpS,aAAJ,EAAmB;MACf,KAAKyV,QAAL,CAAc9W,kBAAd,CAAiCqB,aAAa,CAAC8U,YAA/C;;MACA9U,aAAa,CAACoV,OAAd,CAAsB,IAAtB;IACH;;IACD,KAAKiD,WAAL,CAAiB,KAAK5C,QAAtB;;IACA,KAAKP,aAAL,CAAmB,KAAKO,QAAxB;EACH;EACD;;;EACY,IAAR/c,QAAQ,GAAG;IACX,OAAO,KAAKb,SAAL,IAAmB,KAAKmI,aAAL,IAAsB,KAAKA,aAAL,CAAmBtH,QAAnE;EACH;;EACW,IAARA,QAAQ,CAAClJ,KAAD,EAAQ;IAChB,KAAKqI,SAAL,GAAiBhK,qBAAqB,CAAC2B,KAAD,CAAtC;IACA,KAAKimB,QAAL,CAAc/c,QAAd,GAAyB,KAAKb,SAA9B;EACH;EACD;AACJ;AACA;AACA;;;EACIiE,qBAAqB,GAAG;IACpB,OAAO,KAAK2Z,QAAL,CAAc3Z,qBAAd,EAAP;EACH;EACD;;;EACAE,cAAc,GAAG;IACb,OAAO,KAAKyZ,QAAL,CAAczZ,cAAd,EAAP;EACH;EACD;;;EACAqC,KAAK,GAAG;IACJ,KAAKoX,QAAL,CAAcpX,KAAd;EACH;EACD;AACJ;AACA;;;EACIO,mBAAmB,GAAG;IAClB,OAAO,KAAK6W,QAAL,CAAc7W,mBAAd,EAAP;EACH;EACD;AACJ;AACA;AACA;;;EACIC,mBAAmB,CAACrP,KAAD,EAAQ;IACvB,KAAKimB,QAAL,CAAc5W,mBAAd,CAAkCrP,KAAlC;EACH;;EACD8oB,eAAe,GAAG;IACd;IACA;IACA,KAAK3hB,OAAL,CAAakG,iBAAb,CAA+B,MAAM;MACjC;MACA;MACA;MACA;MACA,KAAKlG,OAAL,CAAa4hB,QAAb,CAAsB/J,IAAtB,CAA2B5f,IAAI,CAAC,CAAD,CAA/B,EAAoCH,SAAS,CAAC,KAAKimB,UAAN,CAA7C,EAAgElX,SAAhE,CAA0E,MAAM;QAC5E,KAAKgb,kBAAL;;QACA,KAAKC,qBAAL;;QACA,IAAI,KAAKC,gBAAT,EAA2B;UACvB,KAAKjD,QAAL,CAAc5W,mBAAd,CAAkC,KAAK6Z,gBAAvC;QACH;MACJ,CAND;IAOH,CAZD;EAaH;;EACDC,WAAW,CAACC,OAAD,EAAU;IACjB,MAAMC,kBAAkB,GAAGD,OAAO,CAAC,qBAAD,CAAlC;IACA,MAAME,cAAc,GAAGF,OAAO,CAAC,kBAAD,CAA9B,CAFiB,CAGjB;IACA;;IACA,IAAIC,kBAAkB,IAAI,CAACA,kBAAkB,CAACE,WAA9C,EAA2D;MACvD,KAAKP,kBAAL;IACH,CAPgB,CAQjB;;;IACA,IAAIM,cAAc,IAAI,CAACA,cAAc,CAACC,WAAlC,IAAiD,KAAKL,gBAA1D,EAA4E;MACxE,KAAKjD,QAAL,CAAc5W,mBAAd,CAAkC,KAAK6Z,gBAAvC;IACH;EACJ;;EACDjG,WAAW,GAAG;IACV,IAAI,KAAKzS,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBsV,UAAnB,CAA8B,IAA9B;IACH;;IACD,MAAM1J,KAAK,GAAGoM,OAAO,CAACvG,cAAR,CAAuB7gB,OAAvB,CAA+B,IAA/B,CAAd;;IACA,IAAIgb,KAAK,GAAG,CAAC,CAAb,EAAgB;MACZoM,OAAO,CAACvG,cAAR,CAAuBzH,MAAvB,CAA8B4B,KAA9B,EAAqC,CAArC;IACH,CAPS,CAQV;;;IACA,KAAKjV,OAAL,CAAakG,iBAAb,CAA+B,MAAM;MACjC,KAAK6X,UAAL,CAAgBnc,IAAhB;;MACA,KAAKmc,UAAL,CAAgBtW,QAAhB;;MACA,KAAKqX,QAAL,CAAc7X,OAAd;IACH,CAJD;EAKH;EACD;;;EACA4a,kBAAkB,GAAG;IACjB,IAAItiB,EAAJ;;IACA,MAAMrG,OAAO,GAAG,KAAKA,OAAL,CAAa+gB,aAA7B;IACA,IAAIjU,WAAW,GAAG9M,OAAlB;;IACA,IAAI,KAAKmpB,mBAAT,EAA8B;MAC1Brc,WAAW,GACP9M,OAAO,CAACopB,OAAR,KAAoBjc,SAApB,GACMnN,OAAO,CAACopB,OAAR,CAAgB,KAAKD,mBAArB,CADN,GAEM;MACE,CAAC9iB,EAAE,GAAGrG,OAAO,CAACsc,aAAd,MAAiC,IAAjC,IAAyCjW,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAAC+iB,OAAH,CAAW,KAAKD,mBAAhB,CAJ9E;IAKH;;IACD,IAAIrc,WAAW,KAAK,OAAOkY,SAAP,KAAqB,WAArB,IAAoCA,SAAzC,CAAf,EAAoE;MAChEb,iBAAiB,CAACrX,WAAD,EAAc,SAAd,CAAjB;IACH;;IACD,KAAK8Y,QAAL,CAAcna,eAAd,CAA8BqB,WAAW,IAAI9M,OAA7C;EACH;EACD;;;EACAqpB,mBAAmB,GAAG;IAClB,MAAMC,QAAQ,GAAG,KAAK9b,eAAtB;;IACA,IAAI,CAAC8b,QAAL,EAAe;MACX,OAAO,IAAP;IACH;;IACD,IAAI,OAAOA,QAAP,KAAoB,QAAxB,EAAkC;MAC9B,OAAO,KAAKtpB,OAAL,CAAa+gB,aAAb,CAA2BqI,OAA3B,CAAmCE,QAAnC,CAAP;IACH;;IACD,OAAOrrB,aAAa,CAACqrB,QAAD,CAApB;EACH;EACD;;;EACAd,WAAW,CAACxC,GAAD,EAAM;IACbA,GAAG,CAAC/d,aAAJ,CAAkB0F,SAAlB,CAA4B,MAAM;MAC9B,IAAI,CAACqY,GAAG,CAAC9b,UAAJ,EAAL,EAAuB;QACnB,MAAMqf,GAAG,GAAG,KAAK5E,IAAjB;QACA,MAAM5c,cAAc,GAAG,KAAKA,cAA5B;QACA,MAAMsI,WAAW,GAAG,KAAKxD,oBAAL,GACd;UACEH,QAAQ,EAAE,KAAKG,oBAAL,CAA0Bgb,WADtC;UAEE3hB,OAAO,EAAE,KAAK2G,oBAAL,CAA0BqY,IAFrC;UAGEjR,aAAa,EAAE,KAAKmU;QAHtB,CADc,GAMd,IANN;QAOA,MAAMtU,OAAO,GAAG,KAAKnH,gBAAL,GACV;UACED,QAAQ,EAAE,KAAKC,gBAAL,CAAsBkb,WADlC;UAEE3hB,OAAO,EAAE,KAAKyG,gBAAL,CAAsBuY,IAFjC;UAGE5S,SAAS,EAAE,KAAK3F,gBAAL,CAAsB2F,SAHnC;UAIE2B,aAAa,EAAE,KAAKmU;QAJtB,CADU,GAOV,IAPN;QAQApC,GAAG,CAACnd,QAAJ,GAAe,KAAKA,QAApB;QACAmd,GAAG,CAACrP,QAAJ,GAAe,KAAKA,QAApB;QACAqP,GAAG,CAACje,cAAJ,GACI,OAAOA,cAAP,KAA0B,QAA1B,IAAsCA,cAAtC,GACMA,cADN,GAEM5J,oBAAoB,CAAC4J,cAAD,CAH9B;QAIAie,GAAG,CAAClb,iBAAJ,GAAwB,KAAKA,iBAA7B;QACAkb,GAAG,CAACnS,YAAJ,GAAmB,KAAKA,YAAxB;QACAmS,GAAG,CACEzY,mBADL,CACyB,KAAK8b,mBAAL,EADzB,EAEKzc,uBAFL,CAE6ByD,WAF7B,EAGK5D,mBAHL,CAGyBqH,OAHzB,EAIK7E,oBAJL,CAI0B,KAAKwJ,gBAAL,IAAyB,QAJnD;;QAKA,IAAI8Q,GAAJ,EAAS;UACLvD,GAAG,CAACpX,aAAJ,CAAkB2a,GAAG,CAAC5pB,KAAtB;QACH;MACJ;IACJ,CApCD,EADa,CAsCb;;IACAqmB,GAAG,CAAC/d,aAAJ,CAAkB0W,IAAlB,CAAuB5f,IAAI,CAAC,CAAD,CAA3B,EAAgC4O,SAAhC,CAA0C,MAAM;MAC5C,IAAItH,EAAJ,CAD4C,CAE5C;;;MACA,IAAI,KAAKqhB,WAAT,EAAsB;QAClB1B,GAAG,CAACta,UAAJ,CAAe,KAAKgc,WAAL,CAAiB9B,QAAhC;QACA;MACH,CAN2C,CAO5C;MACA;;;MACA,IAAI/X,MAAM,GAAG,KAAK7N,OAAL,CAAa+gB,aAAb,CAA2BzE,aAAxC;;MACA,OAAOzO,MAAP,EAAe;QACX,IAAIA,MAAM,CAAC2G,SAAP,CAAiB5P,QAAjB,CAA0BsjB,eAA1B,CAAJ,EAAgD;UAC5ClC,GAAG,CAACta,UAAJ,CAAe,CAAC,CAACrF,EAAE,GAAG8hB,OAAO,CAACvG,cAAR,CAAuBrgB,IAAvB,CAA4BmZ,IAAI,IAAI;YACtD,OAAOA,IAAI,CAAC1a,OAAL,CAAa+gB,aAAb,KAA+BlT,MAAtC;UACH,CAFqB,CAAN,MAER,IAFQ,IAEAxH,EAAE,KAAK,KAAK,CAFZ,GAEgB,KAAK,CAFrB,GAEyBA,EAAE,CAACuf,QAF7B,KAE0C,IAFzD;UAGA;QACH;;QACD/X,MAAM,GAAGA,MAAM,CAACyO,aAAhB;MACH;IACJ,CAnBD;EAoBH;EACD;;;EACA+I,aAAa,CAACW,GAAD,EAAM;IACfA,GAAG,CAAC9d,OAAJ,CAAYyF,SAAZ,CAAsB6b,UAAU,IAAI;MAChC,KAAKthB,OAAL,CAAaye,IAAb,CAAkB;QAAEpnB,MAAM,EAAE,IAAV;QAAgB2E,KAAK,EAAEslB,UAAU,CAACtlB;MAAlC,CAAlB,EADgC,CAEhC;MACA;;MACA,KAAKugB,kBAAL,CAAwBiC,YAAxB;IACH,CALD;IAMAV,GAAG,CAAC7d,QAAJ,CAAawF,SAAb,CAAuB8b,YAAY,IAAI;MACnC,KAAKthB,QAAL,CAAcwe,IAAd,CAAmB;QAAEpnB,MAAM,EAAE,IAAV;QAAgB2E,KAAK,EAAEulB,YAAY,CAACvlB;MAApC,CAAnB;IACH,CAFD;IAGA8hB,GAAG,CAAC5d,KAAJ,CAAUuF,SAAV,CAAoB+b,QAAQ,IAAI;MAC5B,KAAKthB,KAAL,CAAWue,IAAX,CAAgB;QACZpnB,MAAM,EAAE,IADI;QAEZ4L,QAAQ,EAAEue,QAAQ,CAACve,QAFP;QAGZ6E,SAAS,EAAE0Z,QAAQ,CAAC1Z,SAHR;QAIZ9L,KAAK,EAAEwlB,QAAQ,CAACxlB;MAJJ,CAAhB,EAD4B,CAO5B;MACA;;MACA,KAAKugB,kBAAL,CAAwBiC,YAAxB;IACH,CAVD;IAWAV,GAAG,CAAC3d,OAAJ,CAAYsF,SAAZ,CAAsBgc,UAAU,IAAI;MAChC,KAAKthB,OAAL,CAAase,IAAb,CAAkB;QACd5c,SAAS,EAAE4f,UAAU,CAAC5f,SAAX,CAAqBmb,IADlB;QAEdpS,IAAI,EAAE,IAFQ;QAGdH,YAAY,EAAEgX,UAAU,CAAChX;MAHX,CAAlB;IAKH,CAND;IAOAqT,GAAG,CAAC1d,MAAJ,CAAWqF,SAAX,CAAqBic,SAAS,IAAI;MAC9B,KAAKthB,MAAL,CAAYqe,IAAZ,CAAiB;QACb5c,SAAS,EAAE6f,SAAS,CAAC7f,SAAV,CAAoBmb,IADlB;QAEbpS,IAAI,EAAE;MAFO,CAAjB;IAIH,CALD;IAMAkT,GAAG,CAACzd,OAAJ,CAAYoF,SAAZ,CAAsBiZ,SAAS,IAAI;MAC/B,KAAKre,OAAL,CAAaoe,IAAb,CAAkB;QACd5T,aAAa,EAAE6T,SAAS,CAAC7T,aADX;QAEdJ,YAAY,EAAEiU,SAAS,CAACjU,YAFV;QAGdK,iBAAiB,EAAE4T,SAAS,CAAC5T,iBAAV,CAA4BkS,IAHjC;QAIdnb,SAAS,EAAE6c,SAAS,CAAC7c,SAAV,CAAoBmb,IAJjB;QAKdtS,sBAAsB,EAAEgU,SAAS,CAAChU,sBALpB;QAMdE,IAAI,EAAE,IANQ;QAOd3H,QAAQ,EAAEyb,SAAS,CAACzb,QAPN;QAQd6E,SAAS,EAAE4W,SAAS,CAAC5W,SARP;QASd9L,KAAK,EAAE0iB,SAAS,CAAC1iB;MATH,CAAlB;IAWH,CAZD;EAaH;EACD;;;EACAihB,eAAe,CAAC5C,MAAD,EAAS;IACpB,MAAM;MAAE5L,QAAF;MAAY5O,cAAZ;MAA4B+C,iBAA5B;MAA+C+I,YAA/C;MAA6DrG,eAA7D;MAA8EqZ,gBAA9E;MAAgGsC,mBAAhG;MAAqH1Q;IAArH,IAA2I8J,MAAjJ;IACA,KAAK1Z,QAAL,GAAgBge,gBAAgB,IAAI,IAApB,GAA2B,KAA3B,GAAmCA,gBAAnD;IACA,KAAK9e,cAAL,GAAsBA,cAAc,IAAI,CAAxC;;IACA,IAAI4O,QAAJ,EAAc;MACV,KAAKA,QAAL,GAAgBA,QAAhB;IACH;;IACD,IAAI7L,iBAAJ,EAAuB;MACnB,KAAKA,iBAAL,GAAyBA,iBAAzB;IACH;;IACD,IAAI+I,YAAJ,EAAkB;MACd,KAAKA,YAAL,GAAoBA,YAApB;IACH;;IACD,IAAIrG,eAAJ,EAAqB;MACjB,KAAKA,eAAL,GAAuBA,eAAvB;IACH;;IACD,IAAI2b,mBAAJ,EAAyB;MACrB,KAAKA,mBAAL,GAA2BA,mBAA3B;IACH;;IACD,IAAI1Q,gBAAJ,EAAsB;MAClB,KAAKA,gBAAL,GAAwBA,gBAAxB;IACH;EACJ;EACD;;;EACAmQ,qBAAqB,GAAG;IACpB;IACA,KAAKhhB,QAAL,CAAcmhB,OAAd,CACKpK,IADL,CACU9f,SAAS,CAAC,KAAK+I,QAAN,CADnB,EAEA;IACA5I,GAAG,CAAEsN,OAAD,IAAa;MACb,MAAMud,mBAAmB,GAAGvd,OAAO,CAC9BmT,MADuB,CAChBzT,MAAM,IAAIA,MAAM,CAAC0b,WAAP,KAAuB,IADjB,EAEvB5oB,GAFuB,CAEnBkN,MAAM,IAAIA,MAAM,CAAChM,OAFE,CAA5B,CADa,CAIb;MACA;MACA;;MACA,IAAI,KAAKqoB,WAAL,IAAoB,KAAKc,mBAA7B,EAAkD;QAC9CU,mBAAmB,CAACtN,IAApB,CAAyB,KAAKvc,OAA9B;MACH;;MACD,KAAK4lB,QAAL,CAAcvZ,WAAd,CAA0Bwd,mBAA1B;IACH,CAXE,CAHH,EAeA;IACA5qB,SAAS,CAAEqN,OAAD,IAAa;MACnB,OAAO3N,KAAK,CAAC,GAAG2N,OAAO,CAACxN,GAAR,CAAYgU,IAAI,IAAI;QAChC,OAAOA,IAAI,CAAC2U,aAAL,CAAmB9I,IAAnB,CAAwB9f,SAAS,CAACiU,IAAD,CAAjC,CAAP;MACH,CAFe,CAAJ,CAAZ;IAGH,CAJQ,CAhBT,EAoBIlU,SAAS,CAAC,KAAKimB,UAAN,CApBb,EAqBKlX,SArBL,CAqBemc,cAAc,IAAI;MAC7B;MACA,MAAMC,OAAO,GAAG,KAAKnE,QAArB;MACA,MAAM5Z,MAAM,GAAG8d,cAAc,CAAC9pB,OAAf,CAAuB+gB,aAAtC;MACA+I,cAAc,CAACjhB,QAAf,GAA0BkhB,OAAO,CAACtb,aAAR,CAAsBzC,MAAtB,CAA1B,GAA0D+d,OAAO,CAACrb,YAAR,CAAqB1C,MAArB,CAA1D;IACH,CA1BD;EA2BH;;AAhWS;;AAkWdmc,OAAO,CAACvG,cAAR,GAAyB,EAAzB;;AACAuG,OAAO,CAACrF,IAAR;EAAA,iBAAoGqF,OAApG,EA39BmGrrB,EA29BnG,mBAA6HA,EAAE,CAACkqB,UAAhI,GA39BmGlqB,EA29BnG,mBAAuJwnB,aAAvJ,OA39BmGxnB,EA29BnG,mBAAiNc,QAAjN,GA39BmGd,EA29BnG,mBAAsOA,EAAE,CAACimB,MAAzO,GA39BmGjmB,EA29BnG,mBAA4PA,EAAE,CAACktB,gBAA/P,GA39BmGltB,EA29BnG,mBAA4RonB,eAA5R,MA39BmGpnB,EA29BnG,mBAAwUsC,EAAE,CAAC+nB,cAA3U,MA39BmGrqB,EA29BnG,mBAAsXumB,QAAtX,GA39BmGvmB,EA29BnG,mBAA2YA,EAAE,CAACmqB,iBAA9Y,GA39BmGnqB,EA29BnG,mBAA4awqB,eAA5a,OA39BmGxqB,EA29BnG,mBAAoe2mB,eAApe;AAAA;;AACA0E,OAAO,CAACtE,IAAR,kBA59BmG/mB,EA49BnG;EAAA,MAAwFqrB,OAAxF;EAAA;EAAA;IAAA;MA59BmGrrB,EA49BnG,0BAA4oCirB,gBAA5oC;MA59BmGjrB,EA49BnG,0BAAmvC6qB,oBAAnvC;MA59BmG7qB,EA49BnG,0BAAq0CwqB,eAAr0C;IAAA;;IAAA;MAAA;;MA59BmGxqB,EA49BnG,qBA59BmGA,EA49BnG;MA59BmGA,EA49BnG,qBA59BmGA,EA49BnG;MA59BmGA,EA49BnG,qBA59BmGA,EA49BnG;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MA59BmGA,EA49BnG;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA,WA59BmGA,EA49BnG,oBAAghC,CAAC;IAAEgnB,OAAO,EAAEL,eAAX;IAA4BM,WAAW,EAAEoE;EAAzC,CAAD,CAAhhC,GA59BmGrrB,EA49BnG;AAAA;;AACA;EAAA,mDA79BmGA,EA69BnG,mBAA2FqrB,OAA3F,EAAgH,CAAC;IACrGliB,IAAI,EAAE/I,SAD+F;IAErG+lB,IAAI,EAAE,CAAC;MACCrd,QAAQ,EAAE,WADX;MAECoe,QAAQ,EAAE,SAFX;MAGCqD,IAAI,EAAE;QACF,SAASa,eADP;QAEF,6BAA6B,UAF3B;QAGF,6BAA6B;MAH3B,CAHP;MAQCjE,SAAS,EAAE,CAAC;QAAEH,OAAO,EAAEL,eAAX;QAA4BM,WAAW,EAAEoE;MAAzC,CAAD;IARZ,CAAD;EAF+F,CAAD,CAAhH,EAY4B,YAAY;IAChC,OAAO,CAAC;MAAEliB,IAAI,EAAEnJ,EAAE,CAACkqB;IAAX,CAAD,EAA0B;MAAE/gB,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QACjDld,IAAI,EAAEjJ,MAD2C;QAEjDimB,IAAI,EAAE,CAACqB,aAAD;MAF2C,CAAD,EAGjD;QACCre,IAAI,EAAE5I;MADP,CAHiD,EAKjD;QACC4I,IAAI,EAAE3I;MADP,CALiD;IAA/B,CAA1B,EAOW;MAAE2I,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QAClCld,IAAI,EAAEjJ,MAD4B;QAElCimB,IAAI,EAAE,CAACrlB,QAAD;MAF4B,CAAD;IAA/B,CAPX,EAUW;MAAEqI,IAAI,EAAEnJ,EAAE,CAACimB;IAAX,CAVX,EAUgC;MAAE9c,IAAI,EAAEnJ,EAAE,CAACktB;IAAX,CAVhC,EAU+D;MAAE/jB,IAAI,EAAEkH,SAAR;MAAmBgW,UAAU,EAAE,CAAC;QACtFld,IAAI,EAAE5I;MADgF,CAAD,EAEtF;QACC4I,IAAI,EAAEjJ,MADP;QAECimB,IAAI,EAAE,CAACiB,eAAD;MAFP,CAFsF;IAA/B,CAV/D,EAeW;MAAEje,IAAI,EAAE7G,EAAE,CAAC+nB,cAAX;MAA2BhE,UAAU,EAAE,CAAC;QAC1Cld,IAAI,EAAE5I;MADoC,CAAD;IAAvC,CAfX,EAiBW;MAAE4I,IAAI,EAAEod;IAAR,CAjBX,EAiB+B;MAAEpd,IAAI,EAAEnJ,EAAE,CAACmqB;IAAX,CAjB/B,EAiB+D;MAAEhhB,IAAI,EAAEshB,aAAR;MAAuBpE,UAAU,EAAE,CAAC;QAC1Fld,IAAI,EAAE5I;MADoF,CAAD,EAE1F;QACC4I,IAAI,EAAEzI;MADP,CAF0F,EAI1F;QACCyI,IAAI,EAAEjJ,MADP;QAECimB,IAAI,EAAE,CAACqE,eAAD;MAFP,CAJ0F;IAAnC,CAjB/D,EAwBW;MAAErhB,IAAI,EAAEkiB,OAAR;MAAiBhF,UAAU,EAAE,CAAC;QAChCld,IAAI,EAAE5I;MAD0B,CAAD,EAEhC;QACC4I,IAAI,EAAE3I;MADP,CAFgC,EAIhC;QACC2I,IAAI,EAAEjJ,MADP;QAECimB,IAAI,EAAE,CAACQ,eAAD;MAFP,CAJgC;IAA7B,CAxBX,CAAP;EAgCH,CA7CL,EA6CuB;IAAE7b,QAAQ,EAAE,CAAC;MACpB3B,IAAI,EAAExI,eADc;MAEpBwlB,IAAI,EAAE,CAACqE,eAAD,EAAkB;QAAE2C,WAAW,EAAE;MAAf,CAAlB;IAFc,CAAD,CAAZ;IAGPtd,gBAAgB,EAAE,CAAC;MACnB1G,IAAI,EAAEvI,YADa;MAEnBulB,IAAI,EAAE,CAAC8E,gBAAD;IAFa,CAAD,CAHX;IAMPlb,oBAAoB,EAAE,CAAC;MACvB5G,IAAI,EAAEvI,YADiB;MAEvBulB,IAAI,EAAE,CAAC0E,oBAAD;IAFiB,CAAD,CANf;IASPzC,IAAI,EAAE,CAAC;MACPjf,IAAI,EAAE9I,KADC;MAEP8lB,IAAI,EAAE,CAAC,aAAD;IAFC,CAAD,CATC;IAYPtM,QAAQ,EAAE,CAAC;MACX1Q,IAAI,EAAE9I,KADK;MAEX8lB,IAAI,EAAE,CAAC,iBAAD;IAFK,CAAD,CAZH;IAePkG,mBAAmB,EAAE,CAAC;MACtBljB,IAAI,EAAE9I,KADgB;MAEtB8lB,IAAI,EAAE,CAAC,oBAAD;IAFgB,CAAD,CAfd;IAkBPzV,eAAe,EAAE,CAAC;MAClBvH,IAAI,EAAE9I,KADY;MAElB8lB,IAAI,EAAE,CAAC,iBAAD;IAFY,CAAD,CAlBV;IAqBPlb,cAAc,EAAE,CAAC;MACjB9B,IAAI,EAAE9I,KADW;MAEjB8lB,IAAI,EAAE,CAAC,mBAAD;IAFW,CAAD,CArBT;IAwBP4F,gBAAgB,EAAE,CAAC;MACnB5iB,IAAI,EAAE9I,KADa;MAEnB8lB,IAAI,EAAE,CAAC,yBAAD;IAFa,CAAD,CAxBX;IA2BPpa,QAAQ,EAAE,CAAC;MACX5C,IAAI,EAAE9I,KADK;MAEX8lB,IAAI,EAAE,CAAC,iBAAD;IAFK,CAAD,CA3BH;IA8BPnY,iBAAiB,EAAE,CAAC;MACpB7E,IAAI,EAAE9I,KADc;MAEpB8lB,IAAI,EAAE,CAAC,0BAAD;IAFc,CAAD,CA9BZ;IAiCPpP,YAAY,EAAE,CAAC;MACf5N,IAAI,EAAE9I,KADS;MAEf8lB,IAAI,EAAE,CAAC,qBAAD;IAFS,CAAD,CAjCP;IAoCPxK,gBAAgB,EAAE,CAAC;MACnBxS,IAAI,EAAE9I,KADa;MAEnB8lB,IAAI,EAAE,CAAC,yBAAD;IAFa,CAAD,CApCX;IAuCP/a,OAAO,EAAE,CAAC;MACVjC,IAAI,EAAE1I,MADI;MAEV0lB,IAAI,EAAE,CAAC,gBAAD;IAFI,CAAD,CAvCF;IA0CP9a,QAAQ,EAAE,CAAC;MACXlC,IAAI,EAAE1I,MADK;MAEX0lB,IAAI,EAAE,CAAC,iBAAD;IAFK,CAAD,CA1CH;IA6CP7a,KAAK,EAAE,CAAC;MACRnC,IAAI,EAAE1I,MADE;MAER0lB,IAAI,EAAE,CAAC,cAAD;IAFE,CAAD,CA7CA;IAgDP5a,OAAO,EAAE,CAAC;MACVpC,IAAI,EAAE1I,MADI;MAEV0lB,IAAI,EAAE,CAAC,gBAAD;IAFI,CAAD,CAhDF;IAmDP3a,MAAM,EAAE,CAAC;MACTrC,IAAI,EAAE1I,MADG;MAET0lB,IAAI,EAAE,CAAC,eAAD;IAFG,CAAD,CAnDD;IAsDP1a,OAAO,EAAE,CAAC;MACVtC,IAAI,EAAE1I,MADI;MAEV0lB,IAAI,EAAE,CAAC,gBAAD;IAFI,CAAD,CAtDF;IAyDPza,KAAK,EAAE,CAAC;MACRvC,IAAI,EAAE1I,MADE;MAER0lB,IAAI,EAAE,CAAC,cAAD;IAFE,CAAD;EAzDA,CA7CvB;AAAA;AA2GA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMiH,cAAN,CAAqB;;AAErBA,cAAc,CAACpH,IAAf;EAAA,iBAA2GoH,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAllCmGrtB,EAklCnG;EAAA,MAA4GotB;AAA5G;AAYAA,cAAc,CAACE,IAAf,kBA9lCmGttB,EA8lCnG;EAAA,WAAuI,CAACumB,QAAD,CAAvI;EAAA,UAA6JlkB,mBAA7J;AAAA;;AACA;EAAA,mDA/lCmGrC,EA+lCnG,mBAA2FotB,cAA3F,EAAuH,CAAC;IAC5GjkB,IAAI,EAAEtI,QADsG;IAE5GslB,IAAI,EAAE,CAAC;MACCoH,YAAY,EAAE,CACV9F,WADU,EAEVZ,gBAFU,EAGVwE,OAHU,EAIVZ,aAJU,EAKVS,cALU,EAMVJ,kBANU,CADf;MASC0C,OAAO,EAAE,CACLnrB,mBADK,EAELolB,WAFK,EAGLZ,gBAHK,EAILwE,OAJK,EAKLZ,aALK,EAMLS,cANK,EAOLJ,kBAPK,CATV;MAkBC3D,SAAS,EAAE,CAACZ,QAAD;IAlBZ,CAAD;EAFsG,CAAD,CAAvH;AAAA;AAwBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASa,eAAT,EAA0BoD,eAA1B,EAA2C7D,eAA3C,EAA4DkE,oBAA5D,EAAkFI,gBAAlF,EAAoGzD,aAApG,EAAmHZ,mBAAnH,EAAwIyE,OAAxI,EAAiJZ,aAAjJ,EAAgKK,kBAAhK,EAAoLI,cAApL,EAAoMzD,WAApM,EAAiNZ,gBAAjN,EAAmON,QAAnO,EAA6O6G,cAA7O,EAA6PxI,gBAA7P,EAA+Q9a,OAA/Q,EAAwRgX,WAAxR,EAAqSxD,aAArS,EAAoTZ,eAApT,EAAqUO,iBAArU"}, "metadata": {}, "sourceType": "module"}