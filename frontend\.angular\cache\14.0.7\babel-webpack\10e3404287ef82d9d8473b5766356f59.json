{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const SequenceError = createErrorClass(_super => function SequenceErrorImpl(message) {\n  _super(this);\n\n  this.name = 'SequenceError';\n  this.message = message;\n});", "map": {"version": 3, "names": ["createErrorClass", "SequenceError", "_super", "SequenceErrorImpl", "message", "name"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/SequenceError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const SequenceError = createErrorClass((_super) => function SequenceErrorImpl(message) {\n    _super(this);\n    this.name = 'SequenceError';\n    this.message = message;\n});\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAO,MAAMC,aAAa,GAAGD,gBAAgB,CAAEE,MAAD,IAAY,SAASC,iBAAT,CAA2BC,OAA3B,EAAoC;EAC1FF,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKG,IAAL,GAAY,eAAZ;EACA,KAAKD,OAAL,GAAeA,OAAf;AACH,CAJ4C,CAAtC"}, "metadata": {}, "sourceType": "module"}