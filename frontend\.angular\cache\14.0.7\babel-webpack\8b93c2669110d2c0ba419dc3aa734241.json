{"ast": null, "code": "import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nexport class Observable {\n  constructor(subscribe) {\n    if (subscribe) {\n      this._subscribe = subscribe;\n    }\n  }\n\n  lift(operator) {\n    const observable = new Observable();\n    observable.source = this;\n    observable.operator = operator;\n    return observable;\n  }\n\n  subscribe(observerOrNext, error, complete) {\n    const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n    errorContext(() => {\n      const {\n        operator,\n        source\n      } = this;\n      subscriber.add(operator ? operator.call(subscriber, source) : source ? this._subscribe(subscriber) : this._trySubscribe(subscriber));\n    });\n    return subscriber;\n  }\n\n  _trySubscribe(sink) {\n    try {\n      return this._subscribe(sink);\n    } catch (err) {\n      sink.error(err);\n    }\n  }\n\n  forEach(next, promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      const subscriber = new SafeSubscriber({\n        next: value => {\n          try {\n            next(value);\n          } catch (err) {\n            reject(err);\n            subscriber.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n      this.subscribe(subscriber);\n    });\n  }\n\n  _subscribe(subscriber) {\n    var _a;\n\n    return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n  }\n\n  [Symbol_observable]() {\n    return this;\n  }\n\n  pipe(...operations) {\n    return pipeFromArray(operations)(this);\n  }\n\n  toPromise(promiseCtor) {\n    promiseCtor = getPromiseCtor(promiseCtor);\n    return new promiseCtor((resolve, reject) => {\n      let value;\n      this.subscribe(x => value = x, err => reject(err), () => resolve(value));\n    });\n  }\n\n}\n\nObservable.create = subscribe => {\n  return new Observable(subscribe);\n};\n\nfunction getPromiseCtor(promiseCtor) {\n  var _a;\n\n  return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\n\nfunction isObserver(value) {\n  return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\n\nfunction isSubscriber(value) {\n  return value && value instanceof Subscriber || isObserver(value) && isSubscription(value);\n}", "map": {"version": 3, "names": ["SafeSubscriber", "Subscriber", "isSubscription", "observable", "Symbol_observable", "pipeFromArray", "config", "isFunction", "errorContext", "Observable", "constructor", "subscribe", "_subscribe", "lift", "operator", "source", "observerOrNext", "error", "complete", "subscriber", "isSubscriber", "add", "call", "_trySubscribe", "sink", "err", "for<PERSON>ach", "next", "promiseCtor", "getPromiseCtor", "resolve", "reject", "value", "unsubscribe", "_a", "pipe", "operations", "to<PERSON>romise", "x", "create", "Promise", "isObserver"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/Observable.js"], "sourcesContent": ["import { SafeSubscriber, Subscriber } from './Subscriber';\nimport { isSubscription } from './Subscription';\nimport { observable as Symbol_observable } from './symbol/observable';\nimport { pipeFromArray } from './util/pipe';\nimport { config } from './config';\nimport { isFunction } from './util/isFunction';\nimport { errorContext } from './util/errorContext';\nexport class Observable {\n    constructor(subscribe) {\n        if (subscribe) {\n            this._subscribe = subscribe;\n        }\n    }\n    lift(operator) {\n        const observable = new Observable();\n        observable.source = this;\n        observable.operator = operator;\n        return observable;\n    }\n    subscribe(observerOrNext, error, complete) {\n        const subscriber = isSubscriber(observerOrNext) ? observerOrNext : new SafeSubscriber(observerOrNext, error, complete);\n        errorContext(() => {\n            const { operator, source } = this;\n            subscriber.add(operator\n                ?\n                    operator.call(subscriber, source)\n                : source\n                    ?\n                        this._subscribe(subscriber)\n                    :\n                        this._trySubscribe(subscriber));\n        });\n        return subscriber;\n    }\n    _trySubscribe(sink) {\n        try {\n            return this._subscribe(sink);\n        }\n        catch (err) {\n            sink.error(err);\n        }\n    }\n    forEach(next, promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            const subscriber = new SafeSubscriber({\n                next: (value) => {\n                    try {\n                        next(value);\n                    }\n                    catch (err) {\n                        reject(err);\n                        subscriber.unsubscribe();\n                    }\n                },\n                error: reject,\n                complete: resolve,\n            });\n            this.subscribe(subscriber);\n        });\n    }\n    _subscribe(subscriber) {\n        var _a;\n        return (_a = this.source) === null || _a === void 0 ? void 0 : _a.subscribe(subscriber);\n    }\n    [Symbol_observable]() {\n        return this;\n    }\n    pipe(...operations) {\n        return pipeFromArray(operations)(this);\n    }\n    toPromise(promiseCtor) {\n        promiseCtor = getPromiseCtor(promiseCtor);\n        return new promiseCtor((resolve, reject) => {\n            let value;\n            this.subscribe((x) => (value = x), (err) => reject(err), () => resolve(value));\n        });\n    }\n}\nObservable.create = (subscribe) => {\n    return new Observable(subscribe);\n};\nfunction getPromiseCtor(promiseCtor) {\n    var _a;\n    return (_a = promiseCtor !== null && promiseCtor !== void 0 ? promiseCtor : config.Promise) !== null && _a !== void 0 ? _a : Promise;\n}\nfunction isObserver(value) {\n    return value && isFunction(value.next) && isFunction(value.error) && isFunction(value.complete);\n}\nfunction isSubscriber(value) {\n    return (value && value instanceof Subscriber) || (isObserver(value) && isSubscription(value));\n}\n"], "mappings": "AAAA,SAASA,cAAT,EAAyBC,UAAzB,QAA2C,cAA3C;AACA,SAASC,cAAT,QAA+B,gBAA/B;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,qBAAhD;AACA,SAASC,aAAT,QAA8B,aAA9B;AACA,SAASC,MAAT,QAAuB,UAAvB;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,SAASC,YAAT,QAA6B,qBAA7B;AACA,OAAO,MAAMC,UAAN,CAAiB;EACpBC,WAAW,CAACC,SAAD,EAAY;IACnB,IAAIA,SAAJ,EAAe;MACX,KAAKC,UAAL,GAAkBD,SAAlB;IACH;EACJ;;EACDE,IAAI,CAACC,QAAD,EAAW;IACX,MAAMX,UAAU,GAAG,IAAIM,UAAJ,EAAnB;IACAN,UAAU,CAACY,MAAX,GAAoB,IAApB;IACAZ,UAAU,CAACW,QAAX,GAAsBA,QAAtB;IACA,OAAOX,UAAP;EACH;;EACDQ,SAAS,CAACK,cAAD,EAAiBC,KAAjB,EAAwBC,QAAxB,EAAkC;IACvC,MAAMC,UAAU,GAAGC,YAAY,CAACJ,cAAD,CAAZ,GAA+BA,cAA/B,GAAgD,IAAIhB,cAAJ,CAAmBgB,cAAnB,EAAmCC,KAAnC,EAA0CC,QAA1C,CAAnE;IACAV,YAAY,CAAC,MAAM;MACf,MAAM;QAAEM,QAAF;QAAYC;MAAZ,IAAuB,IAA7B;MACAI,UAAU,CAACE,GAAX,CAAeP,QAAQ,GAEfA,QAAQ,CAACQ,IAAT,CAAcH,UAAd,EAA0BJ,MAA1B,CAFe,GAGjBA,MAAM,GAEA,KAAKH,UAAL,CAAgBO,UAAhB,CAFA,GAIA,KAAKI,aAAL,CAAmBJ,UAAnB,CAPZ;IAQH,CAVW,CAAZ;IAWA,OAAOA,UAAP;EACH;;EACDI,aAAa,CAACC,IAAD,EAAO;IAChB,IAAI;MACA,OAAO,KAAKZ,UAAL,CAAgBY,IAAhB,CAAP;IACH,CAFD,CAGA,OAAOC,GAAP,EAAY;MACRD,IAAI,CAACP,KAAL,CAAWQ,GAAX;IACH;EACJ;;EACDC,OAAO,CAACC,IAAD,EAAOC,WAAP,EAAoB;IACvBA,WAAW,GAAGC,cAAc,CAACD,WAAD,CAA5B;IACA,OAAO,IAAIA,WAAJ,CAAgB,CAACE,OAAD,EAAUC,MAAV,KAAqB;MACxC,MAAMZ,UAAU,GAAG,IAAInB,cAAJ,CAAmB;QAClC2B,IAAI,EAAGK,KAAD,IAAW;UACb,IAAI;YACAL,IAAI,CAACK,KAAD,CAAJ;UACH,CAFD,CAGA,OAAOP,GAAP,EAAY;YACRM,MAAM,CAACN,GAAD,CAAN;YACAN,UAAU,CAACc,WAAX;UACH;QACJ,CATiC;QAUlChB,KAAK,EAAEc,MAV2B;QAWlCb,QAAQ,EAAEY;MAXwB,CAAnB,CAAnB;MAaA,KAAKnB,SAAL,CAAeQ,UAAf;IACH,CAfM,CAAP;EAgBH;;EACDP,UAAU,CAACO,UAAD,EAAa;IACnB,IAAIe,EAAJ;;IACA,OAAO,CAACA,EAAE,GAAG,KAAKnB,MAAX,MAAuB,IAAvB,IAA+BmB,EAAE,KAAK,KAAK,CAA3C,GAA+C,KAAK,CAApD,GAAwDA,EAAE,CAACvB,SAAH,CAAaQ,UAAb,CAA/D;EACH;;EACiB,CAAjBf,iBAAiB,IAAI;IAClB,OAAO,IAAP;EACH;;EACD+B,IAAI,CAAC,GAAGC,UAAJ,EAAgB;IAChB,OAAO/B,aAAa,CAAC+B,UAAD,CAAb,CAA0B,IAA1B,CAAP;EACH;;EACDC,SAAS,CAACT,WAAD,EAAc;IACnBA,WAAW,GAAGC,cAAc,CAACD,WAAD,CAA5B;IACA,OAAO,IAAIA,WAAJ,CAAgB,CAACE,OAAD,EAAUC,MAAV,KAAqB;MACxC,IAAIC,KAAJ;MACA,KAAKrB,SAAL,CAAgB2B,CAAD,IAAQN,KAAK,GAAGM,CAA/B,EAAoCb,GAAD,IAASM,MAAM,CAACN,GAAD,CAAlD,EAAyD,MAAMK,OAAO,CAACE,KAAD,CAAtE;IACH,CAHM,CAAP;EAIH;;AAtEmB;;AAwExBvB,UAAU,CAAC8B,MAAX,GAAqB5B,SAAD,IAAe;EAC/B,OAAO,IAAIF,UAAJ,CAAeE,SAAf,CAAP;AACH,CAFD;;AAGA,SAASkB,cAAT,CAAwBD,WAAxB,EAAqC;EACjC,IAAIM,EAAJ;;EACA,OAAO,CAACA,EAAE,GAAGN,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiDA,WAAjD,GAA+DtB,MAAM,CAACkC,OAA5E,MAAyF,IAAzF,IAAiGN,EAAE,KAAK,KAAK,CAA7G,GAAiHA,EAAjH,GAAsHM,OAA7H;AACH;;AACD,SAASC,UAAT,CAAoBT,KAApB,EAA2B;EACvB,OAAOA,KAAK,IAAIzB,UAAU,CAACyB,KAAK,CAACL,IAAP,CAAnB,IAAmCpB,UAAU,CAACyB,KAAK,CAACf,KAAP,CAA7C,IAA8DV,UAAU,CAACyB,KAAK,CAACd,QAAP,CAA/E;AACH;;AACD,SAASE,YAAT,CAAsBY,KAAtB,EAA6B;EACzB,OAAQA,KAAK,IAAIA,KAAK,YAAY/B,UAA3B,IAA2CwC,UAAU,CAACT,KAAD,CAAV,IAAqB9B,cAAc,CAAC8B,KAAD,CAArF;AACH"}, "metadata": {}, "sourceType": "module"}