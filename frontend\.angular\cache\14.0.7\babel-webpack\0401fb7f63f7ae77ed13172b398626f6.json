{"ast": null, "code": "/*!\n * Chart.js v3.9.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction noop() {}\n\nconst uid = function () {\n  let id = 0;\n  return function () {\n    return id++;\n  };\n}();\n\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\n\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n\n  const type = Object.prototype.toString.call(value);\n\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n\n  return false;\n}\n\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\nconst isNumberFinite = value => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : value / dimension;\n\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n\n  if (isArray(loopable)) {\n    len = loopable.length;\n\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\n\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n\n    if (!isObject(source)) {\n      continue;\n    }\n\n    const keys = Object.keys(source);\n\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n\n  return target;\n}\n\nfunction mergeIf(target, source) {\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\n\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\nconst keyResolvers = {\n  '': v => v,\n  x: o => o.x,\n  y: o => o.y\n};\n\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n\n  return resolver(obj);\n}\n\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n\n      obj = obj && obj[k];\n    }\n\n    return obj;\n  };\n}\n\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n\n  for (const part of parts) {\n    tmp += part;\n\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n\n  return keys;\n}\n\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nconst defined = value => typeof value !== 'undefined';\n\nconst isFunction = value => typeof value === 'function';\n\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\n\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\n\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\n\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n\n  let e = 1;\n  let p = 0;\n\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n\n  return p;\n}\n\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < -0.5 * PI) {\n    angle += TAU;\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n\n  const s = _normalizeAngle(start);\n\n  const e = _normalizeAngle(end);\n\n  const angleToStart = _normalizeAngle(s - a);\n\n  const angleToEnd = _normalizeAngle(e - a);\n\n  const startToAngle = _normalizeAngle(a - s);\n\n  const endToAngle = _normalizeAngle(a - e);\n\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {\n    lo,\n    hi\n  };\n}\n\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => table[index][key] <= value : index => table[index][key] < value);\n\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\n\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n\n    });\n  });\n}\n\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n\n  if (set.size === ilen) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n\n  return window.requestAnimationFrame;\n}();\n\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || (args => Array.prototype.slice.call(args));\n\n  let ticking = false;\n  let args = [];\n  return function (...rest) {\n    args = updateArgs(rest);\n\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\n\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n\n    return delay;\n  };\n}\n\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {\n      iScale,\n      _parsed\n    } = meta;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = _limitValue(Math.min(_lookupByKey(_parsed, iScale.axis, min).lo, animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo), 0, pointCount - 1);\n    }\n\n    if (maxDefined) {\n      count = _limitValue(Math.max(_lookupByKey(_parsed, iScale.axis, max, true).hi + 1, animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1), start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {\n    start,\n    count\n  };\n}\n\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n\nconst atEdge = t => t === 0 || t === 1;\n\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\n\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t) {\n    let s = 1.70158;\n\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\n/*!\n * @kurkle/color v0.2.1\n * https://github.com/kurkle/color#readme\n * (c) 2022 Jukka Kurkela\n * Released under the MIT License\n */\n\nfunction round(v) {\n  return v + 0.5 | 0;\n}\n\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\n\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\n\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\n\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\n\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n\nconst map$1 = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  A: 10,\n  B: 11,\n  C: 12,\n  D: 13,\n  E: 14,\n  F: 15,\n  a: 10,\n  b: 11,\n  c: 12,\n  d: 13,\n  e: 14,\n  f: 15\n};\nconst hex = [...'0123456789ABCDEF'];\n\nconst h1 = b => hex[b & 0xF];\n\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\n\nconst eq = b => (b & 0xF0) >> 4 === (b & 0xF);\n\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\n\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? map$1[str[7]] << 4 | map$1[str[8]] : 255\n      };\n    }\n  }\n\n  return ret;\n}\n\nconst alpha = (a, f) => a < 255 ? f(a) : '';\n\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f) : undefined;\n}\n\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\n\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n\n  return [f(0), f(8), f(4)];\n}\n\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n\n  return [f(5), f(3), f(1)];\n}\n\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n\n  return rgb;\n}\n\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return (g - b) / d + (g < b ? 6 : 0);\n  }\n\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n\n  return (r - g) / d + 4;\n}\n\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n\n  return [h | 0, s || 0, l];\n}\n\nfunction calln(f, a, b, c) {\n  return (Array.isArray(a) ? f(a[0], a[1], a[2]) : f(a, b, c)).map(n2b);\n}\n\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\n\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\n\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\n\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\n\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n\n  if (!m) {\n    return;\n  }\n\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\n\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\n\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255 ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})` : `hsl(${h}, ${s}%, ${l}%)`;\n}\n\nconst map = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names$1 = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\n\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n\n  return unpacked;\n}\n\nlet names;\n\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\n\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n\n  if (!m) {\n    return;\n  }\n\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\nfunction rgbString(v) {\n  return v && (v.a < 255 ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})` : `rgb(${v.r}, ${v.g}, ${v.b})`);\n}\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\n\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\n\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\n\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\n\nfunction fromObject(input) {\n  var v = {\n    r: 0,\n    g: 0,\n    b: 0,\n    a: 255\n  };\n\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {\n        r: input[0],\n        g: input[1],\n        b: input[2],\n        a: 255\n      };\n\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    });\n    v.a = n2b(v.a);\n  }\n\n  return v;\n}\n\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n\n  return hueParse(str);\n}\n\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n\n    const type = typeof input;\n    let v;\n\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n\n    this._rgb = v;\n    this._valid = !!v;\n  }\n\n  get valid() {\n    return this._valid;\n  }\n\n  get rgb() {\n    var v = clone(this._rgb);\n\n    if (v) {\n      v.a = b2n(v.a);\n    }\n\n    return v;\n  }\n\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n\n    return this;\n  }\n\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n\n    return this;\n  }\n\n  clone() {\n    return new Color(this.rgb);\n  }\n\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n\n}\n\nfunction index_esm(input) {\n  return new Color(input);\n}\n\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\n\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\n\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n\n  const keys = key.split('.');\n\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n\n  return merge(getScope$1(root, ''), scope);\n}\n\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n\n          return valueOrDefault(local, target);\n        },\n\n        set(value) {\n          this[privateName] = value;\n        }\n\n      }\n    });\n  }\n\n}\n\nvar defaults = new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n});\n\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n\n  return longest;\n}\n\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n  const gcLen = gc.length / 2;\n\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n\n    gc.splice(0, gcLen);\n  }\n\n  return longest;\n}\n\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nfunction drawPoint(ctx, options, x, y) {\n  drawPointLegend(ctx, options, x, y, null);\n}\n\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n\n      ctx.closePath();\n      break;\n\n    case 'triangle':\n      ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n\n    case 'rectRounded':\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n\n      rad += QUARTER_PI;\n\n    case 'rectRot':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      ctx.closePath();\n      break;\n\n    case 'crossRot':\n      rad += QUARTER_PI;\n\n    case 'cross':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n\n    case 'star':\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      rad += QUARTER_PI;\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      ctx.moveTo(x + yOffset, y - xOffset);\n      ctx.lineTo(x - yOffset, y + xOffset);\n      break;\n\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n      break;\n  }\n\n  ctx.fill();\n\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\n\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n\n  ctx.lineTo(target.x, target.y);\n}\n\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\n\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n\n  ctx.restore();\n}\n\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\n\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n\n    case '%':\n      value /= 100;\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = v => +v || 0;\n\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n\n  return ret;\n}\n\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n\n  let style = valueOrDefault(options.style, fallback.style);\n\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n\n    if (value === undefined) {\n      continue;\n    }\n\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n\n      return value;\n    }\n  }\n}\n\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\nfunction _createResolver(scopes, prefixes = [''], rootScopes = scopes, fallback, getTarget = () => scopes[0]) {\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n\n  });\n}\n\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n\n  });\n}\n\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\n\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop];\n\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n\n  return value;\n}\n\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n\n  _stack.add(prop);\n\n  value = value(_context, _subProxy || receiver);\n\n  _stack.delete(prop);\n\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n\n  return value;\n}\n\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n\n    value = [];\n\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n\n  return value;\n}\n\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n\n  return false;\n}\n\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n\n  if (key === null) {\n    return false;\n  }\n\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n\n    if (key === null) {\n      return false;\n    }\n  }\n\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\n\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n\n  return key;\n}\n\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n\n  const target = parent[prop];\n\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n\n  return target;\n}\n\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n\n    if (defined(value)) {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\n\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n\n    const value = scope[key];\n\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n\n  return Array.from(set);\n}\n\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n\n  return parsed;\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\n\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\n\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n\n  let inAreaNext = _isPointInArea(points[0], area);\n\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n\n    if (!inArea) {\n      continue;\n    }\n\n    point = points[i];\n\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n\n  return parent;\n}\n\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = element => window.getComputedStyle(element, null);\n\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\n\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n\n  return {\n    x,\n    y,\n    box\n  };\n}\n\nfunction getRelativePosition(evt, chart) {\n  if ('native' in evt) {\n    return evt;\n  }\n\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = v => Math.round(v * 10) / 10;\n\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n\n  return {\n    width,\n    height\n  };\n}\n\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n\n  return false;\n}\n\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {}\n\n  return passiveSupported;\n}();\n\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n\n  const a = _pointInLine(p1, cp1, t);\n\n  const b = _pointInLine(cp1, cp2, t);\n\n  const c = _pointInLine(cp2, p2, t);\n\n  const d = _pointInLine(a, b, t);\n\n  const e = _pointInLine(b, c, t);\n\n  return _pointInLine(d, e, t);\n}\n\nconst intlCache = new Map();\n\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n\n  return formatter;\n}\n\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\n\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n\n    setWidth(w) {\n      width = w;\n    },\n\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n\n      return align === 'right' ? 'left' : 'right';\n    },\n\n    xPlus(x, value) {\n      return x - value;\n    },\n\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n\n  };\n};\n\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n\n    setWidth(w) {},\n\n    textAlign(align) {\n      return align;\n    },\n\n    xPlus(x, value) {\n      return x + value;\n    },\n\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n\n  };\n};\n\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\n\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n\n      start--;\n      end--;\n    }\n\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\n\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n\n  const shouldStart = () => inside || startIsBefore();\n\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n\n  return result;\n}\n\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n\n  return result;\n}\n\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  start %= count;\n\n  if (loop) {\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\n\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n\n      if (prev.skip) {\n        start = end;\n      }\n    }\n\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n\n  return result;\n}\n\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n\n    if (s === e) {\n      return;\n    }\n\n    s += count;\n\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n\n    while (points[e % count].skip) {\n      e += dir;\n    }\n\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n\n      prev = pt;\n      prevStyle = style;\n    }\n\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\n\nexport { _isPointInArea as $, _factorize as A, finiteOrDefault as B, callback as C, _addGrace as D, _limitValue as E, toDegrees as F, _measureText as G, HALF_PI as H, _int16Range as I, _alignPixel as J, toPadding as K, clipArea as L, renderText as M, unclipArea as N, toFont as O, PI as P, each as Q, _toLeftRightCenter as R, _alignStartEnd as S, TAU as T, overrides as U, merge as V, _capitalize as W, getRelativePosition as X, _rlookupByKey as Y, _lookupByKey as Z, _arrayUnique as _, resolve as a, toLineHeight as a$, getAngleFromPoint as a0, getMaximumSize as a1, _getParentNode as a2, readUsedSize as a3, throttled as a4, supportsEventListenerOptions as a5, _isDomSupported as a6, descriptors as a7, isFunction as a8, _attachContext as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, noop as aF, distanceBetweenPoints as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, _longestText as aM, _filterBetween as aN, _lookup as aO, isPatternOrGradient as aP, getHoverColor as aQ, clone$1 as aR, _merger as aS, _mergerIf as aT, _deprecated as aU, _splitKey as aV, toFontString as aW, splineCurve as aX, splineCurveMonotone as aY, getStyle as aZ, fontString as a_, _createResolver as aa, _descriptors as ab, mergeIf as ac, uid as ad, debounce as ae, retinaScale as af, clearCanvas as ag, setsEqual as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _readValueToProps as al, _updateBezierControlPoints as am, _computeSegments as an, _boundSegments as ao, _steppedInterpolation as ap, _bezierInterpolation as aq, _pointInLine as ar, _steppedLineTo as as, _bezierCurveTo as at, drawPoint as au, addRoundedRectPath as av, toTRBL as aw, toTRBLCorners as ax, _boundSegment as ay, _normalizeAngle as az, isArray as b, PITAU as b0, INFINITY as b1, RAD_PER_DEG as b2, QUARTER_PI as b3, TWO_THIRDS_PI as b4, _angleDiff as b5, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, log10 as z };", "map": {"version": 3, "names": ["noop", "uid", "id", "isNullOrUndef", "value", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone$1", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "current", "undefined", "console", "warn", "keyResolvers", "v", "x", "o", "y", "resolveObjectKey", "obj", "resolver", "_getKeyResolver", "_splitKey", "parts", "split", "tmp", "part", "push", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "niceNum", "range", "roundedRange", "round", "almostEquals", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNumber", "n", "isNaN", "epsilon", "abs", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "add", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "updateFn", "updateArgs", "ticking", "rest", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "_parsed", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "lim", "l", "h", "p2b", "n2b", "b2n", "n2p", "map$1", "A", "B", "C", "D", "E", "F", "c", "f", "hex", "h1", "h2", "eq", "isShort", "r", "g", "hexParse", "ret", "alpha", "hexString", "HUE_RE", "hsl2rgbn", "hsv2rgbn", "hwb2rgbn", "w", "rgb", "hueValue", "rgb2hsl", "calln", "hsl2rgb", "hwb2rgb", "hsv2rgb", "hue", "hue<PERSON><PERSON><PERSON>", "exec", "p1", "p2", "rotate", "deg", "hslString", "Z", "Y", "X", "W", "V", "U", "T", "S", "R", "Q", "P", "O", "N", "M", "L", "K", "G", "H", "I", "J", "names$1", "OiceXe", "antiquewEte", "aqua", "aquamarRe", "azuY", "beige", "bisque", "black", "blan<PERSON>ed<PERSON><PERSON>", "Xe", "XeviTet", "bPwn", "burlywood", "caMtXe", "<PERSON><PERSON><PERSON><PERSON>", "KocTate", "cSO", "cSnflowerXe", "cSnsilk", "crimson", "cyan", "xXe", "xcyan", "xgTMnPd", "xWay", "xgYF", "xgYy", "xkhaki", "xmagFta", "xTivegYF", "xSange", "xScEd", "xYd", "xsOmon", "xsHgYF", "xUXe", "xUWay", "xUgYy", "xQe", "xviTet", "dAppRk", "dApskyXe", "dim<PERSON>ay", "dimgYy", "dodgerXe", "fiYbrick", "flSOwEte", "foYstWAn", "fuKsia", "gaRsbSo", "ghostwEte", "gTd", "gTMnPd", "Way", "gYF", "gYFLw", "gYy", "honeyMw", "hotpRk", "RdianYd", "Rdigo", "ivSy", "khaki", "lavFMr", "lavFMrXsh", "lawngYF", "NmoncEffon", "ZXe", "ZcSO", "<PERSON><PERSON><PERSON>", "ZgTMnPdLw", "ZWay", "ZgYF", "ZgYy", "ZpRk", "ZsOmon", "ZsHgYF", "ZskyXe", "ZUWay", "ZUgYy", "ZstAlXe", "ZLw", "lime", "limegYF", "lRF", "magFta", "ma<PERSON><PERSON>", "VaquamarRe", "VXe", "VScEd", "VpurpN", "VsHgYF", "VUXe", "VsprRggYF", "VQe", "VviTetYd", "midnightXe", "mRtcYam", "misty<PERSON>e", "moccasR", "navajowEte", "navy", "Tdlace", "Tive", "TivedBb", "<PERSON><PERSON>", "SangeYd", "ScEd", "pOegTMnPd", "pOegYF", "pOeQe", "pOeviTetYd", "papayawEp", "pHKpuff", "peru", "pRk", "plum", "powMrXe", "purpN", "YbeccapurpN", "Yd", "Psybrown", "PyOXe", "saddNbPwn", "sOmon", "sandybPwn", "sHgYF", "sHshell", "siFna", "silver", "skyXe", "UXe", "UWay", "UgYy", "snow", "sprRggYF", "stAlXe", "tan", "teO", "tEstN", "tomato", "Qe", "viTet", "JHt", "wEte", "wEtesmoke", "Lw", "LwgYF", "unpack", "unpacked", "tkeys", "j", "ok", "nk", "replace", "parseInt", "names", "nameParse", "transparent", "toLowerCase", "RGB_RE", "rgbParse", "rgbString", "to", "interpolate", "rgb1", "rgb2", "modHSL", "ratio", "clone", "proto", "fromObject", "input", "functionParse", "Color", "constructor", "_rgb", "_valid", "valid", "mix", "color", "weight", "c1", "c2", "w2", "w1", "clearer", "greyscale", "val", "opaquer", "negate", "lighten", "darken", "saturate", "desaturate", "index_esm", "isPatternOrGradient", "getHoverColor", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "_descriptors", "animation", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "chart", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "describe", "get", "override", "route", "name", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "defaults", "_scriptable", "startsWith", "_indexable", "_fallback", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "width", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "xOffset", "yOffset", "cornerRadius", "pointStyle", "rotation", "radius", "rad", "translate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "point", "area", "margin", "top", "bottom", "clipArea", "clip", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "renderText", "text", "opts", "lines", "strokeWidth", "strokeColor", "line", "setRenderOpts", "strokeStyle", "lineWidth", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "decorateText", "translation", "fillStyle", "textAlign", "textBaseline", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "decorationWidth", "addRoundedRectPath", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "RegExp", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "grace", "beginAtZero", "change", "keepZero", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "_resolve", "Symbol", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "includes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "delta", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "loop", "controlPoints", "spanGaps", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "evt", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "Intl", "NumberFormat", "formatNumber", "num", "format", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "bounds", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "$", "_", "a$", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "aA", "aB", "aC", "aD", "aE", "aF", "aG", "aH", "aI", "aJ", "aK", "aL", "aM", "aN", "aO", "aP", "aQ", "aR", "aS", "aT", "aU", "aV", "aW", "aX", "aY", "aZ", "a_", "aa", "ab", "ac", "ad", "ae", "af", "ag", "ah", "ai", "aj", "ak", "al", "am", "an", "ao", "ap", "aq", "ar", "as", "at", "au", "av", "aw", "ax", "ay", "az", "b0", "b1", "b2", "b3", "b4", "b5", "q", "u", "z"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/chart.js/dist/chunks/helpers.segment.mjs"], "sourcesContent": ["/*!\n * Chart.js v3.9.1\n * https://www.chartjs.org\n * (c) 2022 Chart.js Contributors\n * Released under the MIT License\n */\nfunction noop() {}\nconst uid = (function() {\n  let id = 0;\n  return function() {\n    return id++;\n  };\n}());\nfunction isNullOrUndef(value) {\n  return value === null || typeof value === 'undefined';\n}\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\nconst isNumberFinite = (value) => (typeof value === 'number' || value instanceof Number) && isFinite(+value);\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : value / dimension;\nconst toDimension = (value, dimension) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction clone$1(source) {\n  if (isArray(source)) {\n    return source.map(clone$1);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone$1(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone$1(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  for (let i = 0; i < ilen; ++i) {\n    source = sources[i];\n    if (!isObject(source)) {\n      continue;\n    }\n    const keys = Object.keys(source);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, source, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  return merge(target, source, {merger: _mergerIf});\n}\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone$1(sval);\n  }\n}\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n\t\t\t'\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\nconst keyResolvers = {\n  '': v => v,\n  x: o => o.x,\n  y: o => o.y\n};\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = (value) => typeof value !== 'undefined';\nconst isFunction = (value) => typeof value === 'function';\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\nfunction isNumber(n) {\n  return !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < (-0.5 * PI)) {\n    angle += TAU;\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {lo, hi};\n}\nconst _lookupByKey = (table, key, value, last) =>\n  _lookup(table, value, last\n    ? index => table[index][key] <= value\n    : index => table[index][key] < value);\nconst _rlookupByKey = (table, key, value) =>\n  _lookup(table, value, index => table[index][key] >= value);\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\nfunction _arrayUnique(items) {\n  const set = new Set();\n  let i, ilen;\n  for (i = 0, ilen = items.length; i < ilen; ++i) {\n    set.add(items[i]);\n  }\n  if (set.size === ilen) {\n    return items;\n  }\n  return Array.from(set);\n}\n\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\nconst requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\nfunction throttled(fn, thisArg, updateFn) {\n  const updateArgs = updateFn || ((args) => Array.prototype.slice.call(args));\n  let ticking = false;\n  let args = [];\n  return function(...rest) {\n    args = updateArgs(rest);\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, args);\n      });\n    }\n  };\n}\nfunction debounce(fn, delay) {\n  let timeout;\n  return function(...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\nconst _toLeftRightCenter = (align) => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {iScale, _parsed} = meta;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n    if (minDefined) {\n      start = _limitValue(Math.min(\n        _lookupByKey(_parsed, iScale.axis, min).lo,\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo),\n      0, pointCount - 1);\n    }\n    if (maxDefined) {\n      count = _limitValue(Math.max(\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1),\n      start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {start, count};\n}\nfunction _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n\nconst atEdge = (t) => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n  easeInOutBounce: t => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n};\n\n/*!\n * @kurkle/color v0.2.1\n * https://github.com/kurkle/color#readme\n * (c) 2022 Jukka Kurkela\n * Released under the MIT License\n */\nfunction round(v) {\n  return v + 0.5 | 0;\n}\nconst lim = (v, l, h) => Math.max(Math.min(v, h), l);\nfunction p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\nfunction n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\nfunction b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\nfunction n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\nconst map$1 = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\nconst hex = [...'0123456789ABCDEF'];\nconst h1 = b => hex[b & 0xF];\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\nfunction hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map$1[str[1]] * 17,\n        g: 255 & map$1[str[2]] * 17,\n        b: 255 & map$1[str[3]] * 17,\n        a: len === 5 ? map$1[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map$1[str[1]] << 4 | map$1[str[2]],\n        g: map$1[str[3]] << 4 | map$1[str[4]],\n        b: map$1[str[5]] << 4 | map$1[str[6]],\n        a: len === 9 ? (map$1[str[7]] << 4 | map$1[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\nconst alpha = (a, f) => a < 255 ? f(a) : '';\nfunction hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\nfunction hsv2rgbn(h, s, v) {\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\nfunction rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\nfunction hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\nfunction hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\nfunction hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\nfunction hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\nfunction rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\nfunction hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\nconst map = {\n  x: 'dark',\n  Z: 'light',\n  Y: 're',\n  X: 'blu',\n  W: 'gr',\n  V: 'medium',\n  U: 'slate',\n  A: 'ee',\n  T: 'ol',\n  S: 'or',\n  B: 'ra',\n  C: 'lateg',\n  D: 'ights',\n  R: 'in',\n  Q: 'turquois',\n  E: 'hi',\n  P: 'ro',\n  O: 'al',\n  N: 'le',\n  M: 'de',\n  L: 'yello',\n  F: 'en',\n  K: 'ch',\n  G: 'arks',\n  H: 'ea',\n  I: 'ightg',\n  J: 'wh'\n};\nconst names$1 = {\n  OiceXe: 'f0f8ff',\n  antiquewEte: 'faebd7',\n  aqua: 'ffff',\n  aquamarRe: '7fffd4',\n  azuY: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '0',\n  blanKedOmond: 'ffebcd',\n  Xe: 'ff',\n  XeviTet: '8a2be2',\n  bPwn: 'a52a2a',\n  burlywood: 'deb887',\n  caMtXe: '5f9ea0',\n  KartYuse: '7fff00',\n  KocTate: 'd2691e',\n  cSO: 'ff7f50',\n  cSnflowerXe: '6495ed',\n  cSnsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: 'ffff',\n  xXe: '8b',\n  xcyan: '8b8b',\n  xgTMnPd: 'b8860b',\n  xWay: 'a9a9a9',\n  xgYF: '6400',\n  xgYy: 'a9a9a9',\n  xkhaki: 'bdb76b',\n  xmagFta: '8b008b',\n  xTivegYF: '556b2f',\n  xSange: 'ff8c00',\n  xScEd: '9932cc',\n  xYd: '8b0000',\n  xsOmon: 'e9967a',\n  xsHgYF: '8fbc8f',\n  xUXe: '483d8b',\n  xUWay: '2f4f4f',\n  xUgYy: '2f4f4f',\n  xQe: 'ced1',\n  xviTet: '9400d3',\n  dAppRk: 'ff1493',\n  dApskyXe: 'bfff',\n  dimWay: '696969',\n  dimgYy: '696969',\n  dodgerXe: '1e90ff',\n  fiYbrick: 'b22222',\n  flSOwEte: 'fffaf0',\n  foYstWAn: '228b22',\n  fuKsia: 'ff00ff',\n  gaRsbSo: 'dcdcdc',\n  ghostwEte: 'f8f8ff',\n  gTd: 'ffd700',\n  gTMnPd: 'daa520',\n  Way: '808080',\n  gYF: '8000',\n  gYFLw: 'adff2f',\n  gYy: '808080',\n  honeyMw: 'f0fff0',\n  hotpRk: 'ff69b4',\n  RdianYd: 'cd5c5c',\n  Rdigo: '4b0082',\n  ivSy: 'fffff0',\n  khaki: 'f0e68c',\n  lavFMr: 'e6e6fa',\n  lavFMrXsh: 'fff0f5',\n  lawngYF: '7cfc00',\n  NmoncEffon: 'fffacd',\n  ZXe: 'add8e6',\n  ZcSO: 'f08080',\n  Zcyan: 'e0ffff',\n  ZgTMnPdLw: 'fafad2',\n  ZWay: 'd3d3d3',\n  ZgYF: '90ee90',\n  ZgYy: 'd3d3d3',\n  ZpRk: 'ffb6c1',\n  ZsOmon: 'ffa07a',\n  ZsHgYF: '20b2aa',\n  ZskyXe: '87cefa',\n  ZUWay: '778899',\n  ZUgYy: '778899',\n  ZstAlXe: 'b0c4de',\n  ZLw: 'ffffe0',\n  lime: 'ff00',\n  limegYF: '32cd32',\n  lRF: 'faf0e6',\n  magFta: 'ff00ff',\n  maPon: '800000',\n  VaquamarRe: '66cdaa',\n  VXe: 'cd',\n  VScEd: 'ba55d3',\n  VpurpN: '9370db',\n  VsHgYF: '3cb371',\n  VUXe: '7b68ee',\n  VsprRggYF: 'fa9a',\n  VQe: '48d1cc',\n  VviTetYd: 'c71585',\n  midnightXe: '191970',\n  mRtcYam: 'f5fffa',\n  mistyPse: 'ffe4e1',\n  moccasR: 'ffe4b5',\n  navajowEte: 'ffdead',\n  navy: '80',\n  Tdlace: 'fdf5e6',\n  Tive: '808000',\n  TivedBb: '6b8e23',\n  Sange: 'ffa500',\n  SangeYd: 'ff4500',\n  ScEd: 'da70d6',\n  pOegTMnPd: 'eee8aa',\n  pOegYF: '98fb98',\n  pOeQe: 'afeeee',\n  pOeviTetYd: 'db7093',\n  papayawEp: 'ffefd5',\n  pHKpuff: 'ffdab9',\n  peru: 'cd853f',\n  pRk: 'ffc0cb',\n  plum: 'dda0dd',\n  powMrXe: 'b0e0e6',\n  purpN: '800080',\n  YbeccapurpN: '663399',\n  Yd: 'ff0000',\n  Psybrown: 'bc8f8f',\n  PyOXe: '4169e1',\n  saddNbPwn: '8b4513',\n  sOmon: 'fa8072',\n  sandybPwn: 'f4a460',\n  sHgYF: '2e8b57',\n  sHshell: 'fff5ee',\n  siFna: 'a0522d',\n  silver: 'c0c0c0',\n  skyXe: '87ceeb',\n  UXe: '6a5acd',\n  UWay: '708090',\n  UgYy: '708090',\n  snow: 'fffafa',\n  sprRggYF: 'ff7f',\n  stAlXe: '4682b4',\n  tan: 'd2b48c',\n  teO: '8080',\n  tEstN: 'd8bfd8',\n  tomato: 'ff6347',\n  Qe: '40e0d0',\n  viTet: 'ee82ee',\n  JHt: 'f5deb3',\n  wEte: 'ffffff',\n  wEtesmoke: 'f5f5f5',\n  Lw: 'ffff00',\n  LwgYF: '9acd32'\n};\nfunction unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names$1);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names$1[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\nlet names;\nfunction nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\nfunction rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n  if (!m) {\n    return;\n  }\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\nfunction rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\nfunction interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\nclass Color {\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      v = fromObject(input);\n    } else if (type === 'string') {\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n    this._rgb = v;\n    this._valid = !!v;\n  }\n  get valid() {\n    return this._valid;\n  }\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2;\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n  clone() {\n    return new Color(this.rgb);\n  }\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n  greyscale() {\n    const rgb = this._rgb;\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\nfunction index_esm(input) {\n  return new Color(input);\n}\n\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : index_esm(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : index_esm(value).saturate(0.5).darken(0.1).hexString();\n}\n\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n}\nvar defaults = new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n});\n\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    if (thing !== undefined && thing !== null && isArray(thing) !== true) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\nfunction clearCanvas(canvas, ctx) {\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  drawPointLegend(ctx, options, x, y, null);\n}\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n  default:\n    if (w) {\n      ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n    } else {\n      ctx.arc(x, y, radius, 0, TAU);\n    }\n    ctx.closePath();\n    break;\n  case 'triangle':\n    ctx.moveTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    rad += TWO_THIRDS_PI;\n    ctx.lineTo(x + Math.sin(rad) * radius, y - Math.cos(rad) * radius);\n    ctx.closePath();\n    break;\n  case 'rectRounded':\n    cornerRadius = radius * 0.516;\n    size = radius - cornerRadius;\n    xOffset = Math.cos(rad + QUARTER_PI) * size;\n    yOffset = Math.sin(rad + QUARTER_PI) * size;\n    ctx.arc(x - xOffset, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n    ctx.arc(x + yOffset, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n    ctx.arc(x + xOffset, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n    ctx.arc(x - yOffset, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n    ctx.closePath();\n    break;\n  case 'rect':\n    if (!rotation) {\n      size = Math.SQRT1_2 * radius;\n      width = w ? w / 2 : size;\n      ctx.rect(x - width, y - size, 2 * width, 2 * size);\n      break;\n    }\n    rad += QUARTER_PI;\n  case 'rectRot':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    ctx.closePath();\n    break;\n  case 'crossRot':\n    rad += QUARTER_PI;\n  case 'cross':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'star':\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    rad += QUARTER_PI;\n    xOffset = Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    ctx.moveTo(x + yOffset, y - xOffset);\n    ctx.lineTo(x - yOffset, y + xOffset);\n    break;\n  case 'line':\n    xOffset = w ? w / 2 : Math.cos(rad) * radius;\n    yOffset = Math.sin(rad) * radius;\n    ctx.moveTo(x - xOffset, y - yOffset);\n    ctx.lineTo(x + xOffset, y + yOffset);\n    break;\n  case 'dash':\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + Math.cos(rad) * radius, y + Math.sin(rad) * radius);\n    break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5;\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += font.lineHeight;\n  }\n  ctx.restore();\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction addRoundedRectPath(ctx, rect) {\n  const {x, y, w, h, radius} = rect;\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, -HALF_PI, PI, true);\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  ctx.lineTo(x + w, y + radius.topRight);\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  ctx.lineTo(x + radius.topLeft, y);\n}\n\nconst LINE_HEIGHT = new RegExp(/^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/);\nconst FONT_STYLE = new RegExp(/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/);\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n  case 'px':\n    return value;\n  case '%':\n    value /= 100;\n    break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\nfunction toTRBL(value) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = '';\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\nfunction _createResolver(scopes, prefixes = [''], rootScopes = scopes, fallback, getTarget = () => scopes[0]) {\n  if (!defined(fallback)) {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: rootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope) => _createResolver([scope, ...scopes], prefixes, rootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete target._keys;\n      delete scopes[0][prop];\n      return true;\n    },\n    get(target, prop) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value;\n      delete target._keys;\n      return true;\n    }\n  });\n}\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    deleteProperty(target, prop) {\n      delete target[prop];\n      delete proxy[prop];\n      return true;\n    },\n    get(target, prop, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    set(target, prop, value) {\n      proxy[prop] = value;\n      delete target[prop];\n      return true;\n    }\n  });\n}\nfunction _descriptors(proxy, defaults = {scriptable: true, indexable: true}) {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop)) {\n    return target[prop];\n  }\n  const value = resolve();\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop];\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, value, target, receiver) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  value = value(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  if (defined(_context.index) && isIndexable(prop)) {\n    value = value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (defined(fallback) && fallback !== key && fallback !== parentFallback) {\n        return fallback;\n      }\n    } else if (scope === false && defined(parentFallback) && key !== parentFallback) {\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (defined(fallback) && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    return value;\n  }\n  return target;\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (defined(value)) {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (defined(value)) {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis) => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01;\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n      : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n      : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = (element) => window.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {offsetX, offsetY} = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\nfunction getRelativePosition(evt, chart) {\n  if ('native' in evt) {\n    return evt;\n  }\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(evt, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect();\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? Math.floor(width / aspectRatio) : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    height = round1(width / 2);\n  }\n  return {\n    width,\n    height\n  };\n}\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = deviceHeight / pixelRatio;\n  chart.width = deviceWidth / pixelRatio;\n  const canvas = chart.canvas;\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\nconst supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    window.addEventListener('test', null, options);\n    window.removeEventListener('test', null, options);\n  } catch (e) {\n  }\n  return passiveSupported;\n}());\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n    : mode === 'after' ? t < 1 ? p1.y : p2.y\n    : t > 0 ? p2.y : p1.y\n  };\n}\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\n\nconst getRightToLeftAdapter = function(rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\nconst getLeftToRightAdapter = function() {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    },\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  let {start, end, loop} = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {start, end};\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  return prevStyle && JSON.stringify(style) !== JSON.stringify(prevStyle);\n}\n\nexport { _isPointInArea as $, _factorize as A, finiteOrDefault as B, callback as C, _addGrace as D, _limitValue as E, toDegrees as F, _measureText as G, HALF_PI as H, _int16Range as I, _alignPixel as J, toPadding as K, clipArea as L, renderText as M, unclipArea as N, toFont as O, PI as P, each as Q, _toLeftRightCenter as R, _alignStartEnd as S, TAU as T, overrides as U, merge as V, _capitalize as W, getRelativePosition as X, _rlookupByKey as Y, _lookupByKey as Z, _arrayUnique as _, resolve as a, toLineHeight as a$, getAngleFromPoint as a0, getMaximumSize as a1, _getParentNode as a2, readUsedSize as a3, throttled as a4, supportsEventListenerOptions as a5, _isDomSupported as a6, descriptors as a7, isFunction as a8, _attachContext as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, noop as aF, distanceBetweenPoints as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, _longestText as aM, _filterBetween as aN, _lookup as aO, isPatternOrGradient as aP, getHoverColor as aQ, clone$1 as aR, _merger as aS, _mergerIf as aT, _deprecated as aU, _splitKey as aV, toFontString as aW, splineCurve as aX, splineCurveMonotone as aY, getStyle as aZ, fontString as a_, _createResolver as aa, _descriptors as ab, mergeIf as ac, uid as ad, debounce as ae, retinaScale as af, clearCanvas as ag, setsEqual as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _readValueToProps as al, _updateBezierControlPoints as am, _computeSegments as an, _boundSegments as ao, _steppedInterpolation as ap, _bezierInterpolation as aq, _pointInLine as ar, _steppedLineTo as as, _bezierCurveTo as at, drawPoint as au, addRoundedRectPath as av, toTRBL as aw, toTRBLCorners as ax, _boundSegment as ay, _normalizeAngle as az, isArray as b, PITAU as b0, INFINITY as b1, RAD_PER_DEG as b2, QUARTER_PI as b3, TWO_THIRDS_PI as b4, _angleDiff as b5, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, createContext as h, isObject as i, defined as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, log10 as z };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAT,GAAgB,CAAE;;AAClB,MAAMC,GAAG,GAAI,YAAW;EACtB,IAAIC,EAAE,GAAG,CAAT;EACA,OAAO,YAAW;IAChB,OAAOA,EAAE,EAAT;EACD,CAFD;AAGD,CALY,EAAb;;AAMA,SAASC,aAAT,CAAuBC,KAAvB,EAA8B;EAC5B,OAAOA,KAAK,KAAK,IAAV,IAAkB,OAAOA,KAAP,KAAiB,WAA1C;AACD;;AACD,SAASC,OAAT,CAAiBD,KAAjB,EAAwB;EACtB,IAAIE,KAAK,CAACD,OAAN,IAAiBC,KAAK,CAACD,OAAN,CAAcD,KAAd,CAArB,EAA2C;IACzC,OAAO,IAAP;EACD;;EACD,MAAMG,IAAI,GAAGC,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BP,KAA/B,CAAb;;EACA,IAAIG,IAAI,CAACK,KAAL,CAAW,CAAX,EAAc,CAAd,MAAqB,SAArB,IAAkCL,IAAI,CAACK,KAAL,CAAW,CAAC,CAAZ,MAAmB,QAAzD,EAAmE;IACjE,OAAO,IAAP;EACD;;EACD,OAAO,KAAP;AACD;;AACD,SAASC,QAAT,CAAkBT,KAAlB,EAAyB;EACvB,OAAOA,KAAK,KAAK,IAAV,IAAkBI,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BP,KAA/B,MAA0C,iBAAnE;AACD;;AACD,MAAMU,cAAc,GAAIV,KAAD,IAAW,CAAC,OAAOA,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,YAAYW,MAA/C,KAA0DC,QAAQ,CAAC,CAACZ,KAAF,CAApG;;AACA,SAASa,eAAT,CAAyBb,KAAzB,EAAgCc,YAAhC,EAA8C;EAC5C,OAAOJ,cAAc,CAACV,KAAD,CAAd,GAAwBA,KAAxB,GAAgCc,YAAvC;AACD;;AACD,SAASC,cAAT,CAAwBf,KAAxB,EAA+Bc,YAA/B,EAA6C;EAC3C,OAAO,OAAOd,KAAP,KAAiB,WAAjB,GAA+Bc,YAA/B,GAA8Cd,KAArD;AACD;;AACD,MAAMgB,YAAY,GAAG,CAAChB,KAAD,EAAQiB,SAAR,KACnB,OAAOjB,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACkB,QAAN,CAAe,GAAf,CAA7B,GACEC,UAAU,CAACnB,KAAD,CAAV,GAAoB,GADtB,GAEIA,KAAK,GAAGiB,SAHd;;AAIA,MAAMG,WAAW,GAAG,CAACpB,KAAD,EAAQiB,SAAR,KAClB,OAAOjB,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,CAACkB,QAAN,CAAe,GAAf,CAA7B,GACEC,UAAU,CAACnB,KAAD,CAAV,GAAoB,GAApB,GAA0BiB,SAD5B,GAEI,CAACjB,KAHP;;AAIA,SAASqB,QAAT,CAAkBC,EAAlB,EAAsBC,IAAtB,EAA4BC,OAA5B,EAAqC;EACnC,IAAIF,EAAE,IAAI,OAAOA,EAAE,CAACf,IAAV,KAAmB,UAA7B,EAAyC;IACvC,OAAOe,EAAE,CAACG,KAAH,CAASD,OAAT,EAAkBD,IAAlB,CAAP;EACD;AACF;;AACD,SAASG,IAAT,CAAcC,QAAd,EAAwBL,EAAxB,EAA4BE,OAA5B,EAAqCI,OAArC,EAA8C;EAC5C,IAAIC,CAAJ,EAAOC,GAAP,EAAYC,IAAZ;;EACA,IAAI9B,OAAO,CAAC0B,QAAD,CAAX,EAAuB;IACrBG,GAAG,GAAGH,QAAQ,CAACK,MAAf;;IACA,IAAIJ,OAAJ,EAAa;MACX,KAAKC,CAAC,GAAGC,GAAG,GAAG,CAAf,EAAkBD,CAAC,IAAI,CAAvB,EAA0BA,CAAC,EAA3B,EAA+B;QAC7BP,EAAE,CAACf,IAAH,CAAQiB,OAAR,EAAiBG,QAAQ,CAACE,CAAD,CAAzB,EAA8BA,CAA9B;MACD;IACF,CAJD,MAIO;MACL,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,GAAhB,EAAqBD,CAAC,EAAtB,EAA0B;QACxBP,EAAE,CAACf,IAAH,CAAQiB,OAAR,EAAiBG,QAAQ,CAACE,CAAD,CAAzB,EAA8BA,CAA9B;MACD;IACF;EACF,CAXD,MAWO,IAAIpB,QAAQ,CAACkB,QAAD,CAAZ,EAAwB;IAC7BI,IAAI,GAAG3B,MAAM,CAAC2B,IAAP,CAAYJ,QAAZ,CAAP;IACAG,GAAG,GAAGC,IAAI,CAACC,MAAX;;IACA,KAAKH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,GAAhB,EAAqBD,CAAC,EAAtB,EAA0B;MACxBP,EAAE,CAACf,IAAH,CAAQiB,OAAR,EAAiBG,QAAQ,CAACI,IAAI,CAACF,CAAD,CAAL,CAAzB,EAAoCE,IAAI,CAACF,CAAD,CAAxC;IACD;EACF;AACF;;AACD,SAASI,cAAT,CAAwBC,EAAxB,EAA4BC,EAA5B,EAAgC;EAC9B,IAAIN,CAAJ,EAAOO,IAAP,EAAaC,EAAb,EAAiBC,EAAjB;;EACA,IAAI,CAACJ,EAAD,IAAO,CAACC,EAAR,IAAcD,EAAE,CAACF,MAAH,KAAcG,EAAE,CAACH,MAAnC,EAA2C;IACzC,OAAO,KAAP;EACD;;EACD,KAAKH,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGF,EAAE,CAACF,MAAtB,EAA8BH,CAAC,GAAGO,IAAlC,EAAwC,EAAEP,CAA1C,EAA6C;IAC3CQ,EAAE,GAAGH,EAAE,CAACL,CAAD,CAAP;IACAS,EAAE,GAAGH,EAAE,CAACN,CAAD,CAAP;;IACA,IAAIQ,EAAE,CAACE,YAAH,KAAoBD,EAAE,CAACC,YAAvB,IAAuCF,EAAE,CAACG,KAAH,KAAaF,EAAE,CAACE,KAA3D,EAAkE;MAChE,OAAO,KAAP;IACD;EACF;;EACD,OAAO,IAAP;AACD;;AACD,SAASC,OAAT,CAAiBC,MAAjB,EAAyB;EACvB,IAAIzC,OAAO,CAACyC,MAAD,CAAX,EAAqB;IACnB,OAAOA,MAAM,CAACC,GAAP,CAAWF,OAAX,CAAP;EACD;;EACD,IAAIhC,QAAQ,CAACiC,MAAD,CAAZ,EAAsB;IACpB,MAAME,MAAM,GAAGxC,MAAM,CAACyC,MAAP,CAAc,IAAd,CAAf;IACA,MAAMd,IAAI,GAAG3B,MAAM,CAAC2B,IAAP,CAAYW,MAAZ,CAAb;IACA,MAAMI,IAAI,GAAGf,IAAI,CAACC,MAAlB;IACA,IAAIe,CAAC,GAAG,CAAR;;IACA,OAAOA,CAAC,GAAGD,IAAX,EAAiB,EAAEC,CAAnB,EAAsB;MACpBH,MAAM,CAACb,IAAI,CAACgB,CAAD,CAAL,CAAN,GAAkBN,OAAO,CAACC,MAAM,CAACX,IAAI,CAACgB,CAAD,CAAL,CAAP,CAAzB;IACD;;IACD,OAAOH,MAAP;EACD;;EACD,OAAOF,MAAP;AACD;;AACD,SAASM,UAAT,CAAoBC,GAApB,EAAyB;EACvB,OAAO,CAAC,WAAD,EAAc,WAAd,EAA2B,aAA3B,EAA0CC,OAA1C,CAAkDD,GAAlD,MAA2D,CAAC,CAAnE;AACD;;AACD,SAASE,OAAT,CAAiBF,GAAjB,EAAsBL,MAAtB,EAA8BF,MAA9B,EAAsCU,OAAtC,EAA+C;EAC7C,IAAI,CAACJ,UAAU,CAACC,GAAD,CAAf,EAAsB;IACpB;EACD;;EACD,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAD,CAAnB;EACA,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAD,CAAnB;;EACA,IAAIxC,QAAQ,CAAC4C,IAAD,CAAR,IAAkB5C,QAAQ,CAAC6C,IAAD,CAA9B,EAAsC;IACpCC,KAAK,CAACF,IAAD,EAAOC,IAAP,EAAaF,OAAb,CAAL;EACD,CAFD,MAEO;IACLR,MAAM,CAACK,GAAD,CAAN,GAAcR,OAAO,CAACa,IAAD,CAArB;EACD;AACF;;AACD,SAASC,KAAT,CAAeX,MAAf,EAAuBF,MAAvB,EAA+BU,OAA/B,EAAwC;EACtC,MAAMI,OAAO,GAAGvD,OAAO,CAACyC,MAAD,CAAP,GAAkBA,MAAlB,GAA2B,CAACA,MAAD,CAA3C;EACA,MAAMN,IAAI,GAAGoB,OAAO,CAACxB,MAArB;;EACA,IAAI,CAACvB,QAAQ,CAACmC,MAAD,CAAb,EAAuB;IACrB,OAAOA,MAAP;EACD;;EACDQ,OAAO,GAAGA,OAAO,IAAI,EAArB;EACA,MAAMK,MAAM,GAAGL,OAAO,CAACK,MAAR,IAAkBN,OAAjC;;EACA,KAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGO,IAApB,EAA0B,EAAEP,CAA5B,EAA+B;IAC7Ba,MAAM,GAAGc,OAAO,CAAC3B,CAAD,CAAhB;;IACA,IAAI,CAACpB,QAAQ,CAACiC,MAAD,CAAb,EAAuB;MACrB;IACD;;IACD,MAAMX,IAAI,GAAG3B,MAAM,CAAC2B,IAAP,CAAYW,MAAZ,CAAb;;IACA,KAAK,IAAIK,CAAC,GAAG,CAAR,EAAWD,IAAI,GAAGf,IAAI,CAACC,MAA5B,EAAoCe,CAAC,GAAGD,IAAxC,EAA8C,EAAEC,CAAhD,EAAmD;MACjDU,MAAM,CAAC1B,IAAI,CAACgB,CAAD,CAAL,EAAUH,MAAV,EAAkBF,MAAlB,EAA0BU,OAA1B,CAAN;IACD;EACF;;EACD,OAAOR,MAAP;AACD;;AACD,SAASc,OAAT,CAAiBd,MAAjB,EAAyBF,MAAzB,EAAiC;EAC/B,OAAOa,KAAK,CAACX,MAAD,EAASF,MAAT,EAAiB;IAACe,MAAM,EAAEE;EAAT,CAAjB,CAAZ;AACD;;AACD,SAASA,SAAT,CAAmBV,GAAnB,EAAwBL,MAAxB,EAAgCF,MAAhC,EAAwC;EACtC,IAAI,CAACM,UAAU,CAACC,GAAD,CAAf,EAAsB;IACpB;EACD;;EACD,MAAMI,IAAI,GAAGT,MAAM,CAACK,GAAD,CAAnB;EACA,MAAMK,IAAI,GAAGZ,MAAM,CAACO,GAAD,CAAnB;;EACA,IAAIxC,QAAQ,CAAC4C,IAAD,CAAR,IAAkB5C,QAAQ,CAAC6C,IAAD,CAA9B,EAAsC;IACpCI,OAAO,CAACL,IAAD,EAAOC,IAAP,CAAP;EACD,CAFD,MAEO,IAAI,CAAClD,MAAM,CAACC,SAAP,CAAiBuD,cAAjB,CAAgCrD,IAAhC,CAAqCqC,MAArC,EAA6CK,GAA7C,CAAL,EAAwD;IAC7DL,MAAM,CAACK,GAAD,CAAN,GAAcR,OAAO,CAACa,IAAD,CAArB;EACD;AACF;;AACD,SAASO,WAAT,CAAqBC,KAArB,EAA4B9D,KAA5B,EAAmC+D,QAAnC,EAA6CC,OAA7C,EAAsD;EACpD,IAAIhE,KAAK,KAAKiE,SAAd,EAAyB;IACvBC,OAAO,CAACC,IAAR,CAAaL,KAAK,GAAG,KAAR,GAAgBC,QAAhB,GACd,+BADc,GACoBC,OADpB,GAC8B,WAD3C;EAED;AACF;;AACD,MAAMI,YAAY,GAAG;EACnB,IAAIC,CAAC,IAAIA,CADU;EAEnBC,CAAC,EAAEC,CAAC,IAAIA,CAAC,CAACD,CAFS;EAGnBE,CAAC,EAAED,CAAC,IAAIA,CAAC,CAACC;AAHS,CAArB;;AAKA,SAASC,gBAAT,CAA0BC,GAA1B,EAA+BzB,GAA/B,EAAoC;EAClC,MAAM0B,QAAQ,GAAGP,YAAY,CAACnB,GAAD,CAAZ,KAAsBmB,YAAY,CAACnB,GAAD,CAAZ,GAAoB2B,eAAe,CAAC3B,GAAD,CAAzD,CAAjB;;EACA,OAAO0B,QAAQ,CAACD,GAAD,CAAf;AACD;;AACD,SAASE,eAAT,CAAyB3B,GAAzB,EAA8B;EAC5B,MAAMlB,IAAI,GAAG8C,SAAS,CAAC5B,GAAD,CAAtB;;EACA,OAAOyB,GAAG,IAAI;IACZ,KAAK,MAAM3B,CAAX,IAAgBhB,IAAhB,EAAsB;MACpB,IAAIgB,CAAC,KAAK,EAAV,EAAc;QACZ;MACD;;MACD2B,GAAG,GAAGA,GAAG,IAAIA,GAAG,CAAC3B,CAAD,CAAhB;IACD;;IACD,OAAO2B,GAAP;EACD,CARD;AASD;;AACD,SAASG,SAAT,CAAmB5B,GAAnB,EAAwB;EACtB,MAAM6B,KAAK,GAAG7B,GAAG,CAAC8B,KAAJ,CAAU,GAAV,CAAd;EACA,MAAMhD,IAAI,GAAG,EAAb;EACA,IAAIiD,GAAG,GAAG,EAAV;;EACA,KAAK,MAAMC,IAAX,IAAmBH,KAAnB,EAA0B;IACxBE,GAAG,IAAIC,IAAP;;IACA,IAAID,GAAG,CAAC9D,QAAJ,CAAa,IAAb,CAAJ,EAAwB;MACtB8D,GAAG,GAAGA,GAAG,CAACxE,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd,IAAmB,GAAzB;IACD,CAFD,MAEO;MACLuB,IAAI,CAACmD,IAAL,CAAUF,GAAV;MACAA,GAAG,GAAG,EAAN;IACD;EACF;;EACD,OAAOjD,IAAP;AACD;;AACD,SAASoD,WAAT,CAAqBC,GAArB,EAA0B;EACxB,OAAOA,GAAG,CAACC,MAAJ,CAAW,CAAX,EAAcC,WAAd,KAA8BF,GAAG,CAAC5E,KAAJ,CAAU,CAAV,CAArC;AACD;;AACD,MAAM+E,OAAO,GAAIvF,KAAD,IAAW,OAAOA,KAAP,KAAiB,WAA5C;;AACA,MAAMwF,UAAU,GAAIxF,KAAD,IAAW,OAAOA,KAAP,KAAiB,UAA/C;;AACA,MAAMyF,SAAS,GAAG,CAACC,CAAD,EAAIC,CAAJ,KAAU;EAC1B,IAAID,CAAC,CAACE,IAAF,KAAWD,CAAC,CAACC,IAAjB,EAAuB;IACrB,OAAO,KAAP;EACD;;EACD,KAAK,MAAMC,IAAX,IAAmBH,CAAnB,EAAsB;IACpB,IAAI,CAACC,CAAC,CAACG,GAAF,CAAMD,IAAN,CAAL,EAAkB;MAChB,OAAO,KAAP;IACD;EACF;;EACD,OAAO,IAAP;AACD,CAVD;;AAWA,SAASE,aAAT,CAAuBC,CAAvB,EAA0B;EACxB,OAAOA,CAAC,CAAC7F,IAAF,KAAW,SAAX,IAAwB6F,CAAC,CAAC7F,IAAF,KAAW,OAAnC,IAA8C6F,CAAC,CAAC7F,IAAF,KAAW,aAAhE;AACD;;AAED,MAAM8F,EAAE,GAAGC,IAAI,CAACD,EAAhB;AACA,MAAME,GAAG,GAAG,IAAIF,EAAhB;AACA,MAAMG,KAAK,GAAGD,GAAG,GAAGF,EAApB;AACA,MAAMI,QAAQ,GAAG1F,MAAM,CAAC2F,iBAAxB;AACA,MAAMC,WAAW,GAAGN,EAAE,GAAG,GAAzB;AACA,MAAMO,OAAO,GAAGP,EAAE,GAAG,CAArB;AACA,MAAMQ,UAAU,GAAGR,EAAE,GAAG,CAAxB;AACA,MAAMS,aAAa,GAAGT,EAAE,GAAG,CAAL,GAAS,CAA/B;AACA,MAAMU,KAAK,GAAGT,IAAI,CAACS,KAAnB;AACA,MAAMC,IAAI,GAAGV,IAAI,CAACU,IAAlB;;AACA,SAASC,OAAT,CAAiBC,KAAjB,EAAwB;EACtB,MAAMC,YAAY,GAAGb,IAAI,CAACc,KAAL,CAAWF,KAAX,CAArB;EACAA,KAAK,GAAGG,YAAY,CAACH,KAAD,EAAQC,YAAR,EAAsBD,KAAK,GAAG,IAA9B,CAAZ,GAAkDC,YAAlD,GAAiED,KAAzE;EACA,MAAMI,SAAS,GAAGhB,IAAI,CAACiB,GAAL,CAAS,EAAT,EAAajB,IAAI,CAACkB,KAAL,CAAWT,KAAK,CAACG,KAAD,CAAhB,CAAb,CAAlB;EACA,MAAMO,QAAQ,GAAGP,KAAK,GAAGI,SAAzB;EACA,MAAMI,YAAY,GAAGD,QAAQ,IAAI,CAAZ,GAAgB,CAAhB,GAAoBA,QAAQ,IAAI,CAAZ,GAAgB,CAAhB,GAAoBA,QAAQ,IAAI,CAAZ,GAAgB,CAAhB,GAAoB,EAAjF;EACA,OAAOC,YAAY,GAAGJ,SAAtB;AACD;;AACD,SAASK,UAAT,CAAoBvH,KAApB,EAA2B;EACzB,MAAMwH,MAAM,GAAG,EAAf;EACA,MAAMC,IAAI,GAAGvB,IAAI,CAACuB,IAAL,CAAUzH,KAAV,CAAb;EACA,IAAI6B,CAAJ;;EACA,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG4F,IAAhB,EAAsB5F,CAAC,EAAvB,EAA2B;IACzB,IAAI7B,KAAK,GAAG6B,CAAR,KAAc,CAAlB,EAAqB;MACnB2F,MAAM,CAACtC,IAAP,CAAYrD,CAAZ;MACA2F,MAAM,CAACtC,IAAP,CAAYlF,KAAK,GAAG6B,CAApB;IACD;EACF;;EACD,IAAI4F,IAAI,MAAMA,IAAI,GAAG,CAAb,CAAR,EAAyB;IACvBD,MAAM,CAACtC,IAAP,CAAYuC,IAAZ;EACD;;EACDD,MAAM,CAACE,IAAP,CAAY,CAAChC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAA1B,EAA6BgC,GAA7B;EACA,OAAOH,MAAP;AACD;;AACD,SAASI,QAAT,CAAkBC,CAAlB,EAAqB;EACnB,OAAO,CAACC,KAAK,CAAC3G,UAAU,CAAC0G,CAAD,CAAX,CAAN,IAAyBjH,QAAQ,CAACiH,CAAD,CAAxC;AACD;;AACD,SAASZ,YAAT,CAAsB3C,CAAtB,EAAyBE,CAAzB,EAA4BuD,OAA5B,EAAqC;EACnC,OAAO7B,IAAI,CAAC8B,GAAL,CAAS1D,CAAC,GAAGE,CAAb,IAAkBuD,OAAzB;AACD;;AACD,SAASE,WAAT,CAAqB3D,CAArB,EAAwByD,OAAxB,EAAiC;EAC/B,MAAMG,OAAO,GAAGhC,IAAI,CAACc,KAAL,CAAW1C,CAAX,CAAhB;EACA,OAAS4D,OAAO,GAAGH,OAAX,IAAuBzD,CAAxB,IAAgC4D,OAAO,GAAGH,OAAX,IAAuBzD,CAA7D;AACD;;AACD,SAAS6D,kBAAT,CAA4BC,KAA5B,EAAmCxF,MAAnC,EAA2CyF,QAA3C,EAAqD;EACnD,IAAIxG,CAAJ,EAAOO,IAAP,EAAapC,KAAb;;EACA,KAAK6B,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGgG,KAAK,CAACpG,MAAzB,EAAiCH,CAAC,GAAGO,IAArC,EAA2CP,CAAC,EAA5C,EAAgD;IAC9C7B,KAAK,GAAGoI,KAAK,CAACvG,CAAD,CAAL,CAASwG,QAAT,CAAR;;IACA,IAAI,CAACP,KAAK,CAAC9H,KAAD,CAAV,EAAmB;MACjB4C,MAAM,CAAC0F,GAAP,GAAapC,IAAI,CAACoC,GAAL,CAAS1F,MAAM,CAAC0F,GAAhB,EAAqBtI,KAArB,CAAb;MACA4C,MAAM,CAAC2F,GAAP,GAAarC,IAAI,CAACqC,GAAL,CAAS3F,MAAM,CAAC2F,GAAhB,EAAqBvI,KAArB,CAAb;IACD;EACF;AACF;;AACD,SAASwI,SAAT,CAAmBC,OAAnB,EAA4B;EAC1B,OAAOA,OAAO,IAAIxC,EAAE,GAAG,GAAT,CAAd;AACD;;AACD,SAASyC,SAAT,CAAmBC,OAAnB,EAA4B;EAC1B,OAAOA,OAAO,IAAI,MAAM1C,EAAV,CAAd;AACD;;AACD,SAAS2C,cAAT,CAAwBtE,CAAxB,EAA2B;EACzB,IAAI,CAAC5D,cAAc,CAAC4D,CAAD,CAAnB,EAAwB;IACtB;EACD;;EACD,IAAI0B,CAAC,GAAG,CAAR;EACA,IAAI6C,CAAC,GAAG,CAAR;;EACA,OAAO3C,IAAI,CAACc,KAAL,CAAW1C,CAAC,GAAG0B,CAAf,IAAoBA,CAApB,KAA0B1B,CAAjC,EAAoC;IAClC0B,CAAC,IAAI,EAAL;IACA6C,CAAC;EACF;;EACD,OAAOA,CAAP;AACD;;AACD,SAASC,iBAAT,CAA2BC,WAA3B,EAAwCC,UAAxC,EAAoD;EAClD,MAAMC,mBAAmB,GAAGD,UAAU,CAAC1E,CAAX,GAAeyE,WAAW,CAACzE,CAAvD;EACA,MAAM4E,mBAAmB,GAAGF,UAAU,CAACxE,CAAX,GAAeuE,WAAW,CAACvE,CAAvD;EACA,MAAM2E,wBAAwB,GAAGjD,IAAI,CAACuB,IAAL,CAAUwB,mBAAmB,GAAGA,mBAAtB,GAA4CC,mBAAmB,GAAGA,mBAA5E,CAAjC;EACA,IAAIE,KAAK,GAAGlD,IAAI,CAACmD,KAAL,CAAWH,mBAAX,EAAgCD,mBAAhC,CAAZ;;EACA,IAAIG,KAAK,GAAI,CAAC,GAAD,GAAOnD,EAApB,EAAyB;IACvBmD,KAAK,IAAIjD,GAAT;EACD;;EACD,OAAO;IACLiD,KADK;IAELE,QAAQ,EAAEH;EAFL,CAAP;AAID;;AACD,SAASI,qBAAT,CAA+BC,GAA/B,EAAoCC,GAApC,EAAyC;EACvC,OAAOvD,IAAI,CAACuB,IAAL,CAAUvB,IAAI,CAACiB,GAAL,CAASsC,GAAG,CAACnF,CAAJ,GAAQkF,GAAG,CAAClF,CAArB,EAAwB,CAAxB,IAA6B4B,IAAI,CAACiB,GAAL,CAASsC,GAAG,CAACjF,CAAJ,GAAQgF,GAAG,CAAChF,CAArB,EAAwB,CAAxB,CAAvC,CAAP;AACD;;AACD,SAASkF,UAAT,CAAoBhE,CAApB,EAAuBC,CAAvB,EAA0B;EACxB,OAAO,CAACD,CAAC,GAAGC,CAAJ,GAAQS,KAAT,IAAkBD,GAAlB,GAAwBF,EAA/B;AACD;;AACD,SAAS0D,eAAT,CAAyBjE,CAAzB,EAA4B;EAC1B,OAAO,CAACA,CAAC,GAAGS,GAAJ,GAAUA,GAAX,IAAkBA,GAAzB;AACD;;AACD,SAASyD,aAAT,CAAuBR,KAAvB,EAA8BS,KAA9B,EAAqCC,GAArC,EAA0CC,qBAA1C,EAAiE;EAC/D,MAAMrE,CAAC,GAAGiE,eAAe,CAACP,KAAD,CAAzB;;EACA,MAAMY,CAAC,GAAGL,eAAe,CAACE,KAAD,CAAzB;;EACA,MAAM7D,CAAC,GAAG2D,eAAe,CAACG,GAAD,CAAzB;;EACA,MAAMG,YAAY,GAAGN,eAAe,CAACK,CAAC,GAAGtE,CAAL,CAApC;;EACA,MAAMwE,UAAU,GAAGP,eAAe,CAAC3D,CAAC,GAAGN,CAAL,CAAlC;;EACA,MAAMyE,YAAY,GAAGR,eAAe,CAACjE,CAAC,GAAGsE,CAAL,CAApC;;EACA,MAAMI,UAAU,GAAGT,eAAe,CAACjE,CAAC,GAAGM,CAAL,CAAlC;;EACA,OAAON,CAAC,KAAKsE,CAAN,IAAWtE,CAAC,KAAKM,CAAjB,IAAuB+D,qBAAqB,IAAIC,CAAC,KAAKhE,CAAtD,IACDiE,YAAY,GAAGC,UAAf,IAA6BC,YAAY,GAAGC,UADlD;AAED;;AACD,SAASC,WAAT,CAAqBrK,KAArB,EAA4BsI,GAA5B,EAAiCC,GAAjC,EAAsC;EACpC,OAAOrC,IAAI,CAACqC,GAAL,CAASD,GAAT,EAAcpC,IAAI,CAACoC,GAAL,CAASC,GAAT,EAAcvI,KAAd,CAAd,CAAP;AACD;;AACD,SAASsK,WAAT,CAAqBtK,KAArB,EAA4B;EAC1B,OAAOqK,WAAW,CAACrK,KAAD,EAAQ,CAAC,KAAT,EAAgB,KAAhB,CAAlB;AACD;;AACD,SAASuK,UAAT,CAAoBvK,KAApB,EAA2B6J,KAA3B,EAAkCC,GAAlC,EAAuC/B,OAAO,GAAG,IAAjD,EAAuD;EACrD,OAAO/H,KAAK,IAAIkG,IAAI,CAACoC,GAAL,CAASuB,KAAT,EAAgBC,GAAhB,IAAuB/B,OAAhC,IAA2C/H,KAAK,IAAIkG,IAAI,CAACqC,GAAL,CAASsB,KAAT,EAAgBC,GAAhB,IAAuB/B,OAAlF;AACD;;AAED,SAASyC,OAAT,CAAiBC,KAAjB,EAAwBzK,KAAxB,EAA+B0K,GAA/B,EAAoC;EAClCA,GAAG,GAAGA,GAAG,KAAMlI,KAAD,IAAWiI,KAAK,CAACjI,KAAD,CAAL,GAAexC,KAA/B,CAAT;;EACA,IAAI2K,EAAE,GAAGF,KAAK,CAACzI,MAAN,GAAe,CAAxB;EACA,IAAI4I,EAAE,GAAG,CAAT;EACA,IAAIC,GAAJ;;EACA,OAAOF,EAAE,GAAGC,EAAL,GAAU,CAAjB,EAAoB;IAClBC,GAAG,GAAID,EAAE,GAAGD,EAAN,IAAa,CAAnB;;IACA,IAAID,GAAG,CAACG,GAAD,CAAP,EAAc;MACZD,EAAE,GAAGC,GAAL;IACD,CAFD,MAEO;MACLF,EAAE,GAAGE,GAAL;IACD;EACF;;EACD,OAAO;IAACD,EAAD;IAAKD;EAAL,CAAP;AACD;;AACD,MAAMG,YAAY,GAAG,CAACL,KAAD,EAAQxH,GAAR,EAAajD,KAAb,EAAoB+K,IAApB,KACnBP,OAAO,CAACC,KAAD,EAAQzK,KAAR,EAAe+K,IAAI,GACtBvI,KAAK,IAAIiI,KAAK,CAACjI,KAAD,CAAL,CAAaS,GAAb,KAAqBjD,KADR,GAEtBwC,KAAK,IAAIiI,KAAK,CAACjI,KAAD,CAAL,CAAaS,GAAb,IAAoBjD,KAF1B,CADT;;AAIA,MAAMgL,aAAa,GAAG,CAACP,KAAD,EAAQxH,GAAR,EAAajD,KAAb,KACpBwK,OAAO,CAACC,KAAD,EAAQzK,KAAR,EAAewC,KAAK,IAAIiI,KAAK,CAACjI,KAAD,CAAL,CAAaS,GAAb,KAAqBjD,KAA7C,CADT;;AAEA,SAASiL,cAAT,CAAwBC,MAAxB,EAAgC5C,GAAhC,EAAqCC,GAArC,EAA0C;EACxC,IAAIsB,KAAK,GAAG,CAAZ;EACA,IAAIC,GAAG,GAAGoB,MAAM,CAAClJ,MAAjB;;EACA,OAAO6H,KAAK,GAAGC,GAAR,IAAeoB,MAAM,CAACrB,KAAD,CAAN,GAAgBvB,GAAtC,EAA2C;IACzCuB,KAAK;EACN;;EACD,OAAOC,GAAG,GAAGD,KAAN,IAAeqB,MAAM,CAACpB,GAAG,GAAG,CAAP,CAAN,GAAkBvB,GAAxC,EAA6C;IAC3CuB,GAAG;EACJ;;EACD,OAAOD,KAAK,GAAG,CAAR,IAAaC,GAAG,GAAGoB,MAAM,CAAClJ,MAA1B,GACHkJ,MAAM,CAAC1K,KAAP,CAAaqJ,KAAb,EAAoBC,GAApB,CADG,GAEHoB,MAFJ;AAGD;;AACD,MAAMC,WAAW,GAAG,CAAC,MAAD,EAAS,KAAT,EAAgB,OAAhB,EAAyB,QAAzB,EAAmC,SAAnC,CAApB;;AACA,SAASC,iBAAT,CAA2BhD,KAA3B,EAAkCiD,QAAlC,EAA4C;EAC1C,IAAIjD,KAAK,CAACkD,QAAV,EAAoB;IAClBlD,KAAK,CAACkD,QAAN,CAAeC,SAAf,CAAyBrG,IAAzB,CAA8BmG,QAA9B;;IACA;EACD;;EACDjL,MAAM,CAACoL,cAAP,CAAsBpD,KAAtB,EAA6B,UAA7B,EAAyC;IACvCqD,YAAY,EAAE,IADyB;IAEvCC,UAAU,EAAE,KAF2B;IAGvC1L,KAAK,EAAE;MACLuL,SAAS,EAAE,CAACF,QAAD;IADN;EAHgC,CAAzC;EAOAF,WAAW,CAACQ,OAAZ,CAAqB1I,GAAD,IAAS;IAC3B,MAAM2I,MAAM,GAAG,YAAYzG,WAAW,CAAClC,GAAD,CAAtC;;IACA,MAAM4I,IAAI,GAAGzD,KAAK,CAACnF,GAAD,CAAlB;IACA7C,MAAM,CAACoL,cAAP,CAAsBpD,KAAtB,EAA6BnF,GAA7B,EAAkC;MAChCwI,YAAY,EAAE,IADkB;MAEhCC,UAAU,EAAE,KAFoB;;MAGhC1L,KAAK,CAAC,GAAGuB,IAAJ,EAAU;QACb,MAAMuK,GAAG,GAAGD,IAAI,CAACpK,KAAL,CAAW,IAAX,EAAiBF,IAAjB,CAAZ;;QACA6G,KAAK,CAACkD,QAAN,CAAeC,SAAf,CAAyBI,OAAzB,CAAkCI,MAAD,IAAY;UAC3C,IAAI,OAAOA,MAAM,CAACH,MAAD,CAAb,KAA0B,UAA9B,EAA0C;YACxCG,MAAM,CAACH,MAAD,CAAN,CAAe,GAAGrK,IAAlB;UACD;QACF,CAJD;;QAKA,OAAOuK,GAAP;MACD;;IAX+B,CAAlC;EAaD,CAhBD;AAiBD;;AACD,SAASE,mBAAT,CAA6B5D,KAA7B,EAAoCiD,QAApC,EAA8C;EAC5C,MAAMY,IAAI,GAAG7D,KAAK,CAACkD,QAAnB;;EACA,IAAI,CAACW,IAAL,EAAW;IACT;EACD;;EACD,MAAMV,SAAS,GAAGU,IAAI,CAACV,SAAvB;EACA,MAAM/I,KAAK,GAAG+I,SAAS,CAACrI,OAAV,CAAkBmI,QAAlB,CAAd;;EACA,IAAI7I,KAAK,KAAK,CAAC,CAAf,EAAkB;IAChB+I,SAAS,CAACW,MAAV,CAAiB1J,KAAjB,EAAwB,CAAxB;EACD;;EACD,IAAI+I,SAAS,CAACvJ,MAAV,GAAmB,CAAvB,EAA0B;IACxB;EACD;;EACDmJ,WAAW,CAACQ,OAAZ,CAAqB1I,GAAD,IAAS;IAC3B,OAAOmF,KAAK,CAACnF,GAAD,CAAZ;EACD,CAFD;EAGA,OAAOmF,KAAK,CAACkD,QAAb;AACD;;AACD,SAASa,YAAT,CAAsBC,KAAtB,EAA6B;EAC3B,MAAMC,GAAG,GAAG,IAAIC,GAAJ,EAAZ;EACA,IAAIzK,CAAJ,EAAOO,IAAP;;EACA,KAAKP,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGgK,KAAK,CAACpK,MAAzB,EAAiCH,CAAC,GAAGO,IAArC,EAA2C,EAAEP,CAA7C,EAAgD;IAC9CwK,GAAG,CAACE,GAAJ,CAAQH,KAAK,CAACvK,CAAD,CAAb;EACD;;EACD,IAAIwK,GAAG,CAACzG,IAAJ,KAAaxD,IAAjB,EAAuB;IACrB,OAAOgK,KAAP;EACD;;EACD,OAAOlM,KAAK,CAACsM,IAAN,CAAWH,GAAX,CAAP;AACD;;AAED,SAASI,UAAT,CAAoBC,SAApB,EAA+BC,SAA/B,EAA0CC,UAA1C,EAAsD;EACpD,OAAOD,SAAS,GAAG,GAAZ,GAAkBD,SAAlB,GAA8B,KAA9B,GAAsCE,UAA7C;AACD;;AACD,MAAMC,gBAAgB,GAAI,YAAW;EACnC,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;IACjC,OAAO,UAASzL,QAAT,EAAmB;MACxB,OAAOA,QAAQ,EAAf;IACD,CAFD;EAGD;;EACD,OAAOyL,MAAM,CAACC,qBAAd;AACD,CAPyB,EAA1B;;AAQA,SAASC,SAAT,CAAmB1L,EAAnB,EAAuBE,OAAvB,EAAgCyL,QAAhC,EAA0C;EACxC,MAAMC,UAAU,GAAGD,QAAQ,KAAM1L,IAAD,IAAUrB,KAAK,CAACG,SAAN,CAAgBG,KAAhB,CAAsBD,IAAtB,CAA2BgB,IAA3B,CAAf,CAA3B;;EACA,IAAI4L,OAAO,GAAG,KAAd;EACA,IAAI5L,IAAI,GAAG,EAAX;EACA,OAAO,UAAS,GAAG6L,IAAZ,EAAkB;IACvB7L,IAAI,GAAG2L,UAAU,CAACE,IAAD,CAAjB;;IACA,IAAI,CAACD,OAAL,EAAc;MACZA,OAAO,GAAG,IAAV;MACAN,gBAAgB,CAACtM,IAAjB,CAAsBuM,MAAtB,EAA8B,MAAM;QAClCK,OAAO,GAAG,KAAV;QACA7L,EAAE,CAACG,KAAH,CAASD,OAAT,EAAkBD,IAAlB;MACD,CAHD;IAID;EACF,CATD;AAUD;;AACD,SAAS8L,QAAT,CAAkB/L,EAAlB,EAAsBgM,KAAtB,EAA6B;EAC3B,IAAIC,OAAJ;EACA,OAAO,UAAS,GAAGhM,IAAZ,EAAkB;IACvB,IAAI+L,KAAJ,EAAW;MACTE,YAAY,CAACD,OAAD,CAAZ;MACAA,OAAO,GAAGE,UAAU,CAACnM,EAAD,EAAKgM,KAAL,EAAY/L,IAAZ,CAApB;IACD,CAHD,MAGO;MACLD,EAAE,CAACG,KAAH,CAAS,IAAT,EAAeF,IAAf;IACD;;IACD,OAAO+L,KAAP;EACD,CARD;AASD;;AACD,MAAMI,kBAAkB,GAAIC,KAAD,IAAWA,KAAK,KAAK,OAAV,GAAoB,MAApB,GAA6BA,KAAK,KAAK,KAAV,GAAkB,OAAlB,GAA4B,QAA/F;;AACA,MAAMC,cAAc,GAAG,CAACD,KAAD,EAAQ9D,KAAR,EAAeC,GAAf,KAAuB6D,KAAK,KAAK,OAAV,GAAoB9D,KAApB,GAA4B8D,KAAK,KAAK,KAAV,GAAkB7D,GAAlB,GAAwB,CAACD,KAAK,GAAGC,GAAT,IAAgB,CAAlH;;AACA,MAAM+D,MAAM,GAAG,CAACF,KAAD,EAAQG,IAAR,EAAcC,KAAd,EAAqBC,GAArB,KAA6B;EAC1C,MAAMC,KAAK,GAAGD,GAAG,GAAG,MAAH,GAAY,OAA7B;EACA,OAAOL,KAAK,KAAKM,KAAV,GAAkBF,KAAlB,GAA0BJ,KAAK,KAAK,QAAV,GAAqB,CAACG,IAAI,GAAGC,KAAR,IAAiB,CAAtC,GAA0CD,IAA3E;AACD,CAHD;;AAIA,SAASI,gCAAT,CAA0CC,IAA1C,EAAgDC,MAAhD,EAAwDC,kBAAxD,EAA4E;EAC1E,MAAMC,UAAU,GAAGF,MAAM,CAACpM,MAA1B;EACA,IAAI6H,KAAK,GAAG,CAAZ;EACA,IAAI0E,KAAK,GAAGD,UAAZ;;EACA,IAAIH,IAAI,CAACK,OAAT,EAAkB;IAChB,MAAM;MAACC,MAAD;MAASC;IAAT,IAAoBP,IAA1B;IACA,MAAMQ,IAAI,GAAGF,MAAM,CAACE,IAApB;IACA,MAAM;MAACrG,GAAD;MAAMC,GAAN;MAAWqG,UAAX;MAAuBC;IAAvB,IAAqCJ,MAAM,CAACK,aAAP,EAA3C;;IACA,IAAIF,UAAJ,EAAgB;MACd/E,KAAK,GAAGQ,WAAW,CAACnE,IAAI,CAACoC,GAAL,CAClBwC,YAAY,CAAC4D,OAAD,EAAUD,MAAM,CAACE,IAAjB,EAAuBrG,GAAvB,CAAZ,CAAwCsC,EADtB,EAElByD,kBAAkB,GAAGC,UAAH,GAAgBxD,YAAY,CAACsD,MAAD,EAASO,IAAT,EAAeF,MAAM,CAACM,gBAAP,CAAwBzG,GAAxB,CAAf,CAAZ,CAAyDsC,EAFzE,CAAD,EAGnB,CAHmB,EAGhB0D,UAAU,GAAG,CAHG,CAAnB;IAID;;IACD,IAAIO,UAAJ,EAAgB;MACdN,KAAK,GAAGlE,WAAW,CAACnE,IAAI,CAACqC,GAAL,CAClBuC,YAAY,CAAC4D,OAAD,EAAUD,MAAM,CAACE,IAAjB,EAAuBpG,GAAvB,EAA4B,IAA5B,CAAZ,CAA8CoC,EAA9C,GAAmD,CADjC,EAElB0D,kBAAkB,GAAG,CAAH,GAAOvD,YAAY,CAACsD,MAAD,EAASO,IAAT,EAAeF,MAAM,CAACM,gBAAP,CAAwBxG,GAAxB,CAAf,EAA6C,IAA7C,CAAZ,CAA+DoC,EAA/D,GAAoE,CAF3E,CAAD,EAGnBd,KAHmB,EAGZyE,UAHY,CAAX,GAGazE,KAHrB;IAID,CALD,MAKO;MACL0E,KAAK,GAAGD,UAAU,GAAGzE,KAArB;IACD;EACF;;EACD,OAAO;IAACA,KAAD;IAAQ0E;EAAR,CAAP;AACD;;AACD,SAASS,mBAAT,CAA6Bb,IAA7B,EAAmC;EACjC,MAAM;IAACc,MAAD;IAASC,MAAT;IAAiBC;EAAjB,IAAiChB,IAAvC;EACA,MAAMiB,SAAS,GAAG;IAChBC,IAAI,EAAEJ,MAAM,CAAC3G,GADG;IAEhBgH,IAAI,EAAEL,MAAM,CAAC1G,GAFG;IAGhBgH,IAAI,EAAEL,MAAM,CAAC5G,GAHG;IAIhBkH,IAAI,EAAEN,MAAM,CAAC3G;EAJG,CAAlB;;EAMA,IAAI,CAAC4G,YAAL,EAAmB;IACjBhB,IAAI,CAACgB,YAAL,GAAoBC,SAApB;IACA,OAAO,IAAP;EACD;;EACD,MAAMK,OAAO,GAAGN,YAAY,CAACE,IAAb,KAAsBJ,MAAM,CAAC3G,GAA7B,IACb6G,YAAY,CAACG,IAAb,KAAsBL,MAAM,CAAC1G,GADhB,IAEb4G,YAAY,CAACI,IAAb,KAAsBL,MAAM,CAAC5G,GAFhB,IAGb6G,YAAY,CAACK,IAAb,KAAsBN,MAAM,CAAC3G,GAHhC;EAIAnI,MAAM,CAACsP,MAAP,CAAcP,YAAd,EAA4BC,SAA5B;EACA,OAAOK,OAAP;AACD;;AAED,MAAME,MAAM,GAAIC,CAAD,IAAOA,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAK,CAAvC;;AACA,MAAMC,SAAS,GAAG,CAACD,CAAD,EAAI5F,CAAJ,EAAOnB,CAAP,KAAa,EAAE3C,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,MAAMyI,CAAC,IAAI,CAAX,CAAZ,IAA6B1J,IAAI,CAAC4J,GAAL,CAAS,CAACF,CAAC,GAAG5F,CAAL,IAAU7D,GAAV,GAAgB0C,CAAzB,CAA/B,CAA/B;;AACA,MAAMkH,UAAU,GAAG,CAACH,CAAD,EAAI5F,CAAJ,EAAOnB,CAAP,KAAa3C,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,CAAC,EAAD,GAAMyI,CAAlB,IAAuB1J,IAAI,CAAC4J,GAAL,CAAS,CAACF,CAAC,GAAG5F,CAAL,IAAU7D,GAAV,GAAgB0C,CAAzB,CAAvB,GAAqD,CAArF;;AACA,MAAMmH,OAAO,GAAG;EACdC,MAAM,EAAEL,CAAC,IAAIA,CADC;EAEdM,UAAU,EAAEN,CAAC,IAAIA,CAAC,GAAGA,CAFP;EAGdO,WAAW,EAAEP,CAAC,IAAI,CAACA,CAAD,IAAMA,CAAC,GAAG,CAAV,CAHJ;EAIdQ,aAAa,EAAER,CAAC,IAAK,CAACA,CAAC,IAAI,GAAN,IAAa,CAAd,GAChB,MAAMA,CAAN,GAAUA,CADM,GAEhB,CAAC,GAAD,IAAS,EAAEA,CAAH,IAASA,CAAC,GAAG,CAAb,IAAkB,CAA1B,CANU;EAOdS,WAAW,EAAET,CAAC,IAAIA,CAAC,GAAGA,CAAJ,GAAQA,CAPZ;EAQdU,YAAY,EAAEV,CAAC,IAAI,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmB,CARxB;EASdW,cAAc,EAAEX,CAAC,IAAK,CAACA,CAAC,IAAI,GAAN,IAAa,CAAd,GACjB,MAAMA,CAAN,GAAUA,CAAV,GAAcA,CADG,GAEjB,OAAO,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmB,CAA1B,CAXU;EAYdY,WAAW,EAAEZ,CAAC,IAAIA,CAAC,GAAGA,CAAJ,GAAQA,CAAR,GAAYA,CAZhB;EAada,YAAY,EAAEb,CAAC,IAAI,EAAE,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmBA,CAAnB,GAAuB,CAAzB,CAbL;EAcdc,cAAc,EAAEd,CAAC,IAAK,CAACA,CAAC,IAAI,GAAN,IAAa,CAAd,GACjB,MAAMA,CAAN,GAAUA,CAAV,GAAcA,CAAd,GAAkBA,CADD,GAEjB,CAAC,GAAD,IAAQ,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmBA,CAAnB,GAAuB,CAA/B,CAhBU;EAiBde,WAAW,EAAEf,CAAC,IAAIA,CAAC,GAAGA,CAAJ,GAAQA,CAAR,GAAYA,CAAZ,GAAgBA,CAjBpB;EAkBdgB,YAAY,EAAEhB,CAAC,IAAI,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmBA,CAAnB,GAAuBA,CAAvB,GAA2B,CAlBhC;EAmBdiB,cAAc,EAAEjB,CAAC,IAAK,CAACA,CAAC,IAAI,GAAN,IAAa,CAAd,GACjB,MAAMA,CAAN,GAAUA,CAAV,GAAcA,CAAd,GAAkBA,CAAlB,GAAsBA,CADL,GAEjB,OAAO,CAACA,CAAC,IAAI,CAAN,IAAWA,CAAX,GAAeA,CAAf,GAAmBA,CAAnB,GAAuBA,CAAvB,GAA2B,CAAlC,CArBU;EAsBdkB,UAAU,EAAElB,CAAC,IAAI,CAAC1J,IAAI,CAAC6K,GAAL,CAASnB,CAAC,GAAGpJ,OAAb,CAAD,GAAyB,CAtB5B;EAuBdwK,WAAW,EAAEpB,CAAC,IAAI1J,IAAI,CAAC4J,GAAL,CAASF,CAAC,GAAGpJ,OAAb,CAvBJ;EAwBdyK,aAAa,EAAErB,CAAC,IAAI,CAAC,GAAD,IAAQ1J,IAAI,CAAC6K,GAAL,CAAS9K,EAAE,GAAG2J,CAAd,IAAmB,CAA3B,CAxBN;EAyBdsB,UAAU,EAAEtB,CAAC,IAAKA,CAAC,KAAK,CAAP,GAAY,CAAZ,GAAgB1J,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,MAAMyI,CAAC,GAAG,CAAV,CAAZ,CAzBnB;EA0BduB,WAAW,EAAEvB,CAAC,IAAKA,CAAC,KAAK,CAAP,GAAY,CAAZ,GAAgB,CAAC1J,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,CAAC,EAAD,GAAMyI,CAAlB,CAAD,GAAwB,CA1B5C;EA2BdwB,aAAa,EAAExB,CAAC,IAAID,MAAM,CAACC,CAAD,CAAN,GAAYA,CAAZ,GAAgBA,CAAC,GAAG,GAAJ,GAChC,MAAM1J,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,MAAMyI,CAAC,GAAG,CAAJ,GAAQ,CAAd,CAAZ,CAD0B,GAEhC,OAAO,CAAC1J,IAAI,CAACiB,GAAL,CAAS,CAAT,EAAY,CAAC,EAAD,IAAOyI,CAAC,GAAG,CAAJ,GAAQ,CAAf,CAAZ,CAAD,GAAkC,CAAzC,CA7BU;EA8BdyB,UAAU,EAAEzB,CAAC,IAAKA,CAAC,IAAI,CAAN,GAAWA,CAAX,GAAe,EAAE1J,IAAI,CAACuB,IAAL,CAAU,IAAImI,CAAC,GAAGA,CAAlB,IAAuB,CAAzB,CA9BlB;EA+Bd0B,WAAW,EAAE1B,CAAC,IAAI1J,IAAI,CAACuB,IAAL,CAAU,IAAI,CAACmI,CAAC,IAAI,CAAN,IAAWA,CAAzB,CA/BJ;EAgCd2B,aAAa,EAAE3B,CAAC,IAAK,CAACA,CAAC,IAAI,GAAN,IAAa,CAAd,GAChB,CAAC,GAAD,IAAQ1J,IAAI,CAACuB,IAAL,CAAU,IAAImI,CAAC,GAAGA,CAAlB,IAAuB,CAA/B,CADgB,GAEhB,OAAO1J,IAAI,CAACuB,IAAL,CAAU,IAAI,CAACmI,CAAC,IAAI,CAAN,IAAWA,CAAzB,IAA8B,CAArC,CAlCU;EAmCd4B,aAAa,EAAE5B,CAAC,IAAID,MAAM,CAACC,CAAD,CAAN,GAAYA,CAAZ,GAAgBC,SAAS,CAACD,CAAD,EAAI,KAAJ,EAAW,GAAX,CAnC/B;EAoCd6B,cAAc,EAAE7B,CAAC,IAAID,MAAM,CAACC,CAAD,CAAN,GAAYA,CAAZ,GAAgBG,UAAU,CAACH,CAAD,EAAI,KAAJ,EAAW,GAAX,CApCjC;;EAqCd8B,gBAAgB,CAAC9B,CAAD,EAAI;IAClB,MAAM5F,CAAC,GAAG,MAAV;IACA,MAAMnB,CAAC,GAAG,IAAV;IACA,OAAO8G,MAAM,CAACC,CAAD,CAAN,GAAYA,CAAZ,GACLA,CAAC,GAAG,GAAJ,GACI,MAAMC,SAAS,CAACD,CAAC,GAAG,CAAL,EAAQ5F,CAAR,EAAWnB,CAAX,CADnB,GAEI,MAAM,MAAMkH,UAAU,CAACH,CAAC,GAAG,CAAJ,GAAQ,CAAT,EAAY5F,CAAZ,EAAenB,CAAf,CAH5B;EAID,CA5Ca;;EA6Cd8I,UAAU,CAAC/B,CAAD,EAAI;IACZ,MAAM5F,CAAC,GAAG,OAAV;IACA,OAAO4F,CAAC,GAAGA,CAAJ,IAAS,CAAC5F,CAAC,GAAG,CAAL,IAAU4F,CAAV,GAAc5F,CAAvB,CAAP;EACD,CAhDa;;EAiDd4H,WAAW,CAAChC,CAAD,EAAI;IACb,MAAM5F,CAAC,GAAG,OAAV;IACA,OAAO,CAAC4F,CAAC,IAAI,CAAN,IAAWA,CAAX,IAAgB,CAAC5F,CAAC,GAAG,CAAL,IAAU4F,CAAV,GAAc5F,CAA9B,IAAmC,CAA1C;EACD,CApDa;;EAqDd6H,aAAa,CAACjC,CAAD,EAAI;IACf,IAAI5F,CAAC,GAAG,OAAR;;IACA,IAAI,CAAC4F,CAAC,IAAI,GAAN,IAAa,CAAjB,EAAoB;MAClB,OAAO,OAAOA,CAAC,GAAGA,CAAJ,IAAS,CAAC,CAAC5F,CAAC,IAAK,KAAP,IAAiB,CAAlB,IAAuB4F,CAAvB,GAA2B5F,CAApC,CAAP,CAAP;IACD;;IACD,OAAO,OAAO,CAAC4F,CAAC,IAAI,CAAN,IAAWA,CAAX,IAAgB,CAAC,CAAC5F,CAAC,IAAK,KAAP,IAAiB,CAAlB,IAAuB4F,CAAvB,GAA2B5F,CAA3C,IAAgD,CAAvD,CAAP;EACD,CA3Da;;EA4Dd8H,YAAY,EAAElC,CAAC,IAAI,IAAII,OAAO,CAAC+B,aAAR,CAAsB,IAAInC,CAA1B,CA5DT;;EA6DdmC,aAAa,CAACnC,CAAD,EAAI;IACf,MAAMoC,CAAC,GAAG,MAAV;IACA,MAAMC,CAAC,GAAG,IAAV;;IACA,IAAIrC,CAAC,GAAI,IAAIqC,CAAb,EAAiB;MACf,OAAOD,CAAC,GAAGpC,CAAJ,GAAQA,CAAf;IACD;;IACD,IAAIA,CAAC,GAAI,IAAIqC,CAAb,EAAiB;MACf,OAAOD,CAAC,IAAIpC,CAAC,IAAK,MAAMqC,CAAhB,CAAD,GAAuBrC,CAAvB,GAA2B,IAAlC;IACD;;IACD,IAAIA,CAAC,GAAI,MAAMqC,CAAf,EAAmB;MACjB,OAAOD,CAAC,IAAIpC,CAAC,IAAK,OAAOqC,CAAjB,CAAD,GAAwBrC,CAAxB,GAA4B,MAAnC;IACD;;IACD,OAAOoC,CAAC,IAAIpC,CAAC,IAAK,QAAQqC,CAAlB,CAAD,GAAyBrC,CAAzB,GAA6B,QAApC;EACD,CA1Ea;;EA2EdsC,eAAe,EAAEtC,CAAC,IAAKA,CAAC,GAAG,GAAL,GAClBI,OAAO,CAAC8B,YAAR,CAAqBlC,CAAC,GAAG,CAAzB,IAA8B,GADZ,GAElBI,OAAO,CAAC+B,aAAR,CAAsBnC,CAAC,GAAG,CAAJ,GAAQ,CAA9B,IAAmC,GAAnC,GAAyC;AA7E/B,CAAhB;AAgFA;AACA;AACA;AACA;AACA;AACA;;AACA,SAAS5I,KAAT,CAAe3C,CAAf,EAAkB;EAChB,OAAOA,CAAC,GAAG,GAAJ,GAAU,CAAjB;AACD;;AACD,MAAM8N,GAAG,GAAG,CAAC9N,CAAD,EAAI+N,CAAJ,EAAOC,CAAP,KAAanM,IAAI,CAACqC,GAAL,CAASrC,IAAI,CAACoC,GAAL,CAASjE,CAAT,EAAYgO,CAAZ,CAAT,EAAyBD,CAAzB,CAAzB;;AACA,SAASE,GAAT,CAAajO,CAAb,EAAgB;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,IAAL,CAAN,EAAkB,CAAlB,EAAqB,GAArB,CAAV;AACD;;AACD,SAASkO,GAAT,CAAalO,CAAb,EAAgB;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,GAAL,CAAN,EAAiB,CAAjB,EAAoB,GAApB,CAAV;AACD;;AACD,SAASmO,GAAT,CAAanO,CAAb,EAAgB;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,IAAL,CAAL,GAAkB,GAAnB,EAAwB,CAAxB,EAA2B,CAA3B,CAAV;AACD;;AACD,SAASoO,GAAT,CAAapO,CAAb,EAAgB;EACd,OAAO8N,GAAG,CAACnL,KAAK,CAAC3C,CAAC,GAAG,GAAL,CAAN,EAAiB,CAAjB,EAAoB,GAApB,CAAV;AACD;;AACD,MAAMqO,KAAK,GAAG;EAAC,GAAG,CAAJ;EAAO,GAAG,CAAV;EAAa,GAAG,CAAhB;EAAmB,GAAG,CAAtB;EAAyB,GAAG,CAA5B;EAA+B,GAAG,CAAlC;EAAqC,GAAG,CAAxC;EAA2C,GAAG,CAA9C;EAAiD,GAAG,CAApD;EAAuD,GAAG,CAA1D;EAA6DC,CAAC,EAAE,EAAhE;EAAoEC,CAAC,EAAE,EAAvE;EAA2EC,CAAC,EAAE,EAA9E;EAAkFC,CAAC,EAAE,EAArF;EAAyFC,CAAC,EAAE,EAA5F;EAAgGC,CAAC,EAAE,EAAnG;EAAuGtN,CAAC,EAAE,EAA1G;EAA8GC,CAAC,EAAE,EAAjH;EAAqHsN,CAAC,EAAE,EAAxH;EAA4HhB,CAAC,EAAE,EAA/H;EAAmIjM,CAAC,EAAE,EAAtI;EAA0IkN,CAAC,EAAE;AAA7I,CAAd;AACA,MAAMC,GAAG,GAAG,CAAC,GAAG,kBAAJ,CAAZ;;AACA,MAAMC,EAAE,GAAGzN,CAAC,IAAIwN,GAAG,CAACxN,CAAC,GAAG,GAAL,CAAnB;;AACA,MAAM0N,EAAE,GAAG1N,CAAC,IAAIwN,GAAG,CAAC,CAACxN,CAAC,GAAG,IAAL,KAAc,CAAf,CAAH,GAAuBwN,GAAG,CAACxN,CAAC,GAAG,GAAL,CAA1C;;AACA,MAAM2N,EAAE,GAAG3N,CAAC,IAAK,CAACA,CAAC,GAAG,IAAL,KAAc,CAAf,MAAuBA,CAAC,GAAG,GAA3B,CAAhB;;AACA,MAAM4N,OAAO,GAAGlP,CAAC,IAAIiP,EAAE,CAACjP,CAAC,CAACmP,CAAH,CAAF,IAAWF,EAAE,CAACjP,CAAC,CAACoP,CAAH,CAAb,IAAsBH,EAAE,CAACjP,CAAC,CAACsB,CAAH,CAAxB,IAAiC2N,EAAE,CAACjP,CAAC,CAACqB,CAAH,CAAxD;;AACA,SAASgO,QAAT,CAAkBtO,GAAlB,EAAuB;EACrB,IAAItD,GAAG,GAAGsD,GAAG,CAACpD,MAAd;EACA,IAAI2R,GAAJ;;EACA,IAAIvO,GAAG,CAAC,CAAD,CAAH,KAAW,GAAf,EAAoB;IAClB,IAAItD,GAAG,KAAK,CAAR,IAAaA,GAAG,KAAK,CAAzB,EAA4B;MAC1B6R,GAAG,GAAG;QACJH,CAAC,EAAE,MAAMd,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,GAAgB,EADrB;QAEJqO,CAAC,EAAE,MAAMf,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,GAAgB,EAFrB;QAGJO,CAAC,EAAE,MAAM+M,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,GAAgB,EAHrB;QAIJM,CAAC,EAAE5D,GAAG,KAAK,CAAR,GAAY4Q,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,GAAgB,EAA5B,GAAiC;MAJhC,CAAN;IAMD,CAPD,MAOO,IAAItD,GAAG,KAAK,CAAR,IAAaA,GAAG,KAAK,CAAzB,EAA4B;MACjC6R,GAAG,GAAG;QACJH,CAAC,EAAEd,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,IAAiB,CAAjB,GAAqBsN,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CADzB;QAEJqO,CAAC,EAAEf,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,IAAiB,CAAjB,GAAqBsN,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAFzB;QAGJO,CAAC,EAAE+M,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,IAAiB,CAAjB,GAAqBsN,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAHzB;QAIJM,CAAC,EAAE5D,GAAG,KAAK,CAAR,GAAa4Q,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAL,IAAiB,CAAjB,GAAqBsN,KAAK,CAACtN,GAAG,CAAC,CAAD,CAAJ,CAAvC,GAAmD;MAJlD,CAAN;IAMD;EACF;;EACD,OAAOuO,GAAP;AACD;;AACD,MAAMC,KAAK,GAAG,CAAClO,CAAD,EAAIwN,CAAJ,KAAUxN,CAAC,GAAG,GAAJ,GAAUwN,CAAC,CAACxN,CAAD,CAAX,GAAiB,EAAzC;;AACA,SAASmO,SAAT,CAAmBxP,CAAnB,EAAsB;EACpB,IAAI6O,CAAC,GAAGK,OAAO,CAAClP,CAAD,CAAP,GAAa+O,EAAb,GAAkBC,EAA1B;EACA,OAAOhP,CAAC,GACJ,MAAM6O,CAAC,CAAC7O,CAAC,CAACmP,CAAH,CAAP,GAAeN,CAAC,CAAC7O,CAAC,CAACoP,CAAH,CAAhB,GAAwBP,CAAC,CAAC7O,CAAC,CAACsB,CAAH,CAAzB,GAAiCiO,KAAK,CAACvP,CAAC,CAACqB,CAAH,EAAMwN,CAAN,CADlC,GAEJjP,SAFJ;AAGD;;AACD,MAAM6P,MAAM,GAAG,8GAAf;;AACA,SAASC,QAAT,CAAkB1B,CAAlB,EAAqBrI,CAArB,EAAwBoI,CAAxB,EAA2B;EACzB,MAAM1M,CAAC,GAAGsE,CAAC,GAAG9D,IAAI,CAACoC,GAAL,CAAS8J,CAAT,EAAY,IAAIA,CAAhB,CAAd;;EACA,MAAMc,CAAC,GAAG,CAACrL,CAAD,EAAI9E,CAAC,GAAG,CAAC8E,CAAC,GAAGwK,CAAC,GAAG,EAAT,IAAe,EAAvB,KAA8BD,CAAC,GAAG1M,CAAC,GAAGQ,IAAI,CAACqC,GAAL,CAASrC,IAAI,CAACoC,GAAL,CAASvF,CAAC,GAAG,CAAb,EAAgB,IAAIA,CAApB,EAAuB,CAAvB,CAAT,EAAoC,CAAC,CAArC,CAAhD;;EACA,OAAO,CAACmQ,CAAC,CAAC,CAAD,CAAF,EAAOA,CAAC,CAAC,CAAD,CAAR,EAAaA,CAAC,CAAC,CAAD,CAAd,CAAP;AACD;;AACD,SAASc,QAAT,CAAkB3B,CAAlB,EAAqBrI,CAArB,EAAwB3F,CAAxB,EAA2B;EACzB,MAAM6O,CAAC,GAAG,CAACrL,CAAD,EAAI9E,CAAC,GAAG,CAAC8E,CAAC,GAAGwK,CAAC,GAAG,EAAT,IAAe,CAAvB,KAA6BhO,CAAC,GAAGA,CAAC,GAAG2F,CAAJ,GAAQ9D,IAAI,CAACqC,GAAL,CAASrC,IAAI,CAACoC,GAAL,CAASvF,CAAT,EAAY,IAAIA,CAAhB,EAAmB,CAAnB,CAAT,EAAgC,CAAhC,CAAnD;;EACA,OAAO,CAACmQ,CAAC,CAAC,CAAD,CAAF,EAAOA,CAAC,CAAC,CAAD,CAAR,EAAaA,CAAC,CAAC,CAAD,CAAd,CAAP;AACD;;AACD,SAASe,QAAT,CAAkB5B,CAAlB,EAAqB6B,CAArB,EAAwBvO,CAAxB,EAA2B;EACzB,MAAMwO,GAAG,GAAGJ,QAAQ,CAAC1B,CAAD,EAAI,CAAJ,EAAO,GAAP,CAApB;EACA,IAAIxQ,CAAJ;;EACA,IAAIqS,CAAC,GAAGvO,CAAJ,GAAQ,CAAZ,EAAe;IACb9D,CAAC,GAAG,KAAKqS,CAAC,GAAGvO,CAAT,CAAJ;IACAuO,CAAC,IAAIrS,CAAL;IACA8D,CAAC,IAAI9D,CAAL;EACD;;EACD,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG,CAAhB,EAAmBA,CAAC,EAApB,EAAwB;IACtBsS,GAAG,CAACtS,CAAD,CAAH,IAAU,IAAIqS,CAAJ,GAAQvO,CAAlB;IACAwO,GAAG,CAACtS,CAAD,CAAH,IAAUqS,CAAV;EACD;;EACD,OAAOC,GAAP;AACD;;AACD,SAASC,QAAT,CAAkBZ,CAAlB,EAAqBC,CAArB,EAAwB9N,CAAxB,EAA2BsM,CAA3B,EAA8B1J,GAA9B,EAAmC;EACjC,IAAIiL,CAAC,KAAKjL,GAAV,EAAe;IACb,OAAQ,CAACkL,CAAC,GAAG9N,CAAL,IAAUsM,CAAX,IAAiBwB,CAAC,GAAG9N,CAAJ,GAAQ,CAAR,GAAY,CAA7B,CAAP;EACD;;EACD,IAAI8N,CAAC,KAAKlL,GAAV,EAAe;IACb,OAAO,CAAC5C,CAAC,GAAG6N,CAAL,IAAUvB,CAAV,GAAc,CAArB;EACD;;EACD,OAAO,CAACuB,CAAC,GAAGC,CAAL,IAAUxB,CAAV,GAAc,CAArB;AACD;;AACD,SAASoC,OAAT,CAAiBhQ,CAAjB,EAAoB;EAClB,MAAMyC,KAAK,GAAG,GAAd;EACA,MAAM0M,CAAC,GAAGnP,CAAC,CAACmP,CAAF,GAAM1M,KAAhB;EACA,MAAM2M,CAAC,GAAGpP,CAAC,CAACoP,CAAF,GAAM3M,KAAhB;EACA,MAAMnB,CAAC,GAAGtB,CAAC,CAACsB,CAAF,GAAMmB,KAAhB;EACA,MAAMyB,GAAG,GAAGrC,IAAI,CAACqC,GAAL,CAASiL,CAAT,EAAYC,CAAZ,EAAe9N,CAAf,CAAZ;EACA,MAAM2C,GAAG,GAAGpC,IAAI,CAACoC,GAAL,CAASkL,CAAT,EAAYC,CAAZ,EAAe9N,CAAf,CAAZ;EACA,MAAMyM,CAAC,GAAG,CAAC7J,GAAG,GAAGD,GAAP,IAAc,CAAxB;EACA,IAAI+J,CAAJ,EAAOrI,CAAP,EAAUiI,CAAV;;EACA,IAAI1J,GAAG,KAAKD,GAAZ,EAAiB;IACf2J,CAAC,GAAG1J,GAAG,GAAGD,GAAV;IACA0B,CAAC,GAAGoI,CAAC,GAAG,GAAJ,GAAUH,CAAC,IAAI,IAAI1J,GAAJ,GAAUD,GAAd,CAAX,GAAgC2J,CAAC,IAAI1J,GAAG,GAAGD,GAAV,CAArC;IACA+J,CAAC,GAAG+B,QAAQ,CAACZ,CAAD,EAAIC,CAAJ,EAAO9N,CAAP,EAAUsM,CAAV,EAAa1J,GAAb,CAAZ;IACA8J,CAAC,GAAGA,CAAC,GAAG,EAAJ,GAAS,GAAb;EACD;;EACD,OAAO,CAACA,CAAC,GAAG,CAAL,EAAQrI,CAAC,IAAI,CAAb,EAAgBoI,CAAhB,CAAP;AACD;;AACD,SAASkC,KAAT,CAAepB,CAAf,EAAkBxN,CAAlB,EAAqBC,CAArB,EAAwBsN,CAAxB,EAA2B;EACzB,OAAO,CACL/S,KAAK,CAACD,OAAN,CAAcyF,CAAd,IACIwN,CAAC,CAACxN,CAAC,CAAC,CAAD,CAAF,EAAOA,CAAC,CAAC,CAAD,CAAR,EAAaA,CAAC,CAAC,CAAD,CAAd,CADL,GAEIwN,CAAC,CAACxN,CAAD,EAAIC,CAAJ,EAAOsN,CAAP,CAHA,EAILtQ,GAJK,CAID4P,GAJC,CAAP;AAKD;;AACD,SAASgC,OAAT,CAAiBlC,CAAjB,EAAoBrI,CAApB,EAAuBoI,CAAvB,EAA0B;EACxB,OAAOkC,KAAK,CAACP,QAAD,EAAW1B,CAAX,EAAcrI,CAAd,EAAiBoI,CAAjB,CAAZ;AACD;;AACD,SAASoC,OAAT,CAAiBnC,CAAjB,EAAoB6B,CAApB,EAAuBvO,CAAvB,EAA0B;EACxB,OAAO2O,KAAK,CAACL,QAAD,EAAW5B,CAAX,EAAc6B,CAAd,EAAiBvO,CAAjB,CAAZ;AACD;;AACD,SAAS8O,OAAT,CAAiBpC,CAAjB,EAAoBrI,CAApB,EAAuB3F,CAAvB,EAA0B;EACxB,OAAOiQ,KAAK,CAACN,QAAD,EAAW3B,CAAX,EAAcrI,CAAd,EAAiB3F,CAAjB,CAAZ;AACD;;AACD,SAASqQ,GAAT,CAAarC,CAAb,EAAgB;EACd,OAAO,CAACA,CAAC,GAAG,GAAJ,GAAU,GAAX,IAAkB,GAAzB;AACD;;AACD,SAASsC,QAAT,CAAkBvP,GAAlB,EAAuB;EACrB,MAAM4M,CAAC,GAAG8B,MAAM,CAACc,IAAP,CAAYxP,GAAZ,CAAV;EACA,IAAIM,CAAC,GAAG,GAAR;EACA,IAAIrB,CAAJ;;EACA,IAAI,CAAC2N,CAAL,EAAQ;IACN;EACD;;EACD,IAAIA,CAAC,CAAC,CAAD,CAAD,KAAS3N,CAAb,EAAgB;IACdqB,CAAC,GAAGsM,CAAC,CAAC,CAAD,CAAD,GAAOM,GAAG,CAAC,CAACN,CAAC,CAAC,CAAD,CAAH,CAAV,GAAoBO,GAAG,CAAC,CAACP,CAAC,CAAC,CAAD,CAAH,CAA3B;EACD;;EACD,MAAMK,CAAC,GAAGqC,GAAG,CAAC,CAAC1C,CAAC,CAAC,CAAD,CAAH,CAAb;EACA,MAAM6C,EAAE,GAAG,CAAC7C,CAAC,CAAC,CAAD,CAAF,GAAQ,GAAnB;EACA,MAAM8C,EAAE,GAAG,CAAC9C,CAAC,CAAC,CAAD,CAAF,GAAQ,GAAnB;;EACA,IAAIA,CAAC,CAAC,CAAD,CAAD,KAAS,KAAb,EAAoB;IAClB3N,CAAC,GAAGmQ,OAAO,CAACnC,CAAD,EAAIwC,EAAJ,EAAQC,EAAR,CAAX;EACD,CAFD,MAEO,IAAI9C,CAAC,CAAC,CAAD,CAAD,KAAS,KAAb,EAAoB;IACzB3N,CAAC,GAAGoQ,OAAO,CAACpC,CAAD,EAAIwC,EAAJ,EAAQC,EAAR,CAAX;EACD,CAFM,MAEA;IACLzQ,CAAC,GAAGkQ,OAAO,CAAClC,CAAD,EAAIwC,EAAJ,EAAQC,EAAR,CAAX;EACD;;EACD,OAAO;IACLtB,CAAC,EAAEnP,CAAC,CAAC,CAAD,CADC;IAELoP,CAAC,EAAEpP,CAAC,CAAC,CAAD,CAFC;IAGLsB,CAAC,EAAEtB,CAAC,CAAC,CAAD,CAHC;IAILqB,CAAC,EAAEA;EAJE,CAAP;AAMD;;AACD,SAASqP,MAAT,CAAgB1Q,CAAhB,EAAmB2Q,GAAnB,EAAwB;EACtB,IAAI3C,CAAC,GAAGgC,OAAO,CAAChQ,CAAD,CAAf;EACAgO,CAAC,CAAC,CAAD,CAAD,GAAOqC,GAAG,CAACrC,CAAC,CAAC,CAAD,CAAD,GAAO2C,GAAR,CAAV;EACA3C,CAAC,GAAGkC,OAAO,CAAClC,CAAD,CAAX;EACAhO,CAAC,CAACmP,CAAF,GAAMnB,CAAC,CAAC,CAAD,CAAP;EACAhO,CAAC,CAACoP,CAAF,GAAMpB,CAAC,CAAC,CAAD,CAAP;EACAhO,CAAC,CAACsB,CAAF,GAAM0M,CAAC,CAAC,CAAD,CAAP;AACD;;AACD,SAAS4C,SAAT,CAAmB5Q,CAAnB,EAAsB;EACpB,IAAI,CAACA,CAAL,EAAQ;IACN;EACD;;EACD,MAAMqB,CAAC,GAAG2O,OAAO,CAAChQ,CAAD,CAAjB;EACA,MAAMgO,CAAC,GAAG3M,CAAC,CAAC,CAAD,CAAX;EACA,MAAMsE,CAAC,GAAGyI,GAAG,CAAC/M,CAAC,CAAC,CAAD,CAAF,CAAb;EACA,MAAM0M,CAAC,GAAGK,GAAG,CAAC/M,CAAC,CAAC,CAAD,CAAF,CAAb;EACA,OAAOrB,CAAC,CAACqB,CAAF,GAAM,GAAN,GACF,QAAO2M,CAAE,KAAIrI,CAAE,MAAKoI,CAAE,MAAKI,GAAG,CAACnO,CAAC,CAACqB,CAAH,CAAM,GADlC,GAEF,OAAM2M,CAAE,KAAIrI,CAAE,MAAKoI,CAAE,IAF1B;AAGD;;AACD,MAAMzP,GAAG,GAAG;EACV2B,CAAC,EAAE,MADO;EAEV4Q,CAAC,EAAE,OAFO;EAGVC,CAAC,EAAE,IAHO;EAIVC,CAAC,EAAE,KAJO;EAKVC,CAAC,EAAE,IALO;EAMVC,CAAC,EAAE,QANO;EAOVC,CAAC,EAAE,OAPO;EAQV5C,CAAC,EAAE,IARO;EASV6C,CAAC,EAAE,IATO;EAUVC,CAAC,EAAE,IAVO;EAWV7C,CAAC,EAAE,IAXO;EAYVC,CAAC,EAAE,OAZO;EAaVC,CAAC,EAAE,OAbO;EAcV4C,CAAC,EAAE,IAdO;EAeVC,CAAC,EAAE,UAfO;EAgBV5C,CAAC,EAAE,IAhBO;EAiBV6C,CAAC,EAAE,IAjBO;EAkBVC,CAAC,EAAE,IAlBO;EAmBVC,CAAC,EAAE,IAnBO;EAoBVC,CAAC,EAAE,IApBO;EAqBVC,CAAC,EAAE,OArBO;EAsBVhD,CAAC,EAAE,IAtBO;EAuBViD,CAAC,EAAE,IAvBO;EAwBVC,CAAC,EAAE,MAxBO;EAyBVC,CAAC,EAAE,IAzBO;EA0BVC,CAAC,EAAE,OA1BO;EA2BVC,CAAC,EAAE;AA3BO,CAAZ;AA6BA,MAAMC,OAAO,GAAG;EACdC,MAAM,EAAE,QADM;EAEdC,WAAW,EAAE,QAFC;EAGdC,IAAI,EAAE,MAHQ;EAIdC,SAAS,EAAE,QAJG;EAKdC,IAAI,EAAE,QALQ;EAMdC,KAAK,EAAE,QANO;EAOdC,MAAM,EAAE,QAPM;EAQdC,KAAK,EAAE,GARO;EASdC,YAAY,EAAE,QATA;EAUdC,EAAE,EAAE,IAVU;EAWdC,OAAO,EAAE,QAXK;EAYdC,IAAI,EAAE,QAZQ;EAadC,SAAS,EAAE,QAbG;EAcdC,MAAM,EAAE,QAdM;EAedC,QAAQ,EAAE,QAfI;EAgBdC,OAAO,EAAE,QAhBK;EAiBdC,GAAG,EAAE,QAjBS;EAkBdC,WAAW,EAAE,QAlBC;EAmBdC,OAAO,EAAE,QAnBK;EAoBdC,OAAO,EAAE,QApBK;EAqBdC,IAAI,EAAE,MArBQ;EAsBdC,GAAG,EAAE,IAtBS;EAuBdC,KAAK,EAAE,MAvBO;EAwBdC,OAAO,EAAE,QAxBK;EAyBdC,IAAI,EAAE,QAzBQ;EA0BdC,IAAI,EAAE,MA1BQ;EA2BdC,IAAI,EAAE,QA3BQ;EA4BdC,MAAM,EAAE,QA5BM;EA6BdC,OAAO,EAAE,QA7BK;EA8BdC,QAAQ,EAAE,QA9BI;EA+BdC,MAAM,EAAE,QA/BM;EAgCdC,KAAK,EAAE,QAhCO;EAiCdC,GAAG,EAAE,QAjCS;EAkCdC,MAAM,EAAE,QAlCM;EAmCdC,MAAM,EAAE,QAnCM;EAoCdC,IAAI,EAAE,QApCQ;EAqCdC,KAAK,EAAE,QArCO;EAsCdC,KAAK,EAAE,QAtCO;EAuCdC,GAAG,EAAE,MAvCS;EAwCdC,MAAM,EAAE,QAxCM;EAyCdC,MAAM,EAAE,QAzCM;EA0CdC,QAAQ,EAAE,MA1CI;EA2CdC,MAAM,EAAE,QA3CM;EA4CdC,MAAM,EAAE,QA5CM;EA6CdC,QAAQ,EAAE,QA7CI;EA8CdC,QAAQ,EAAE,QA9CI;EA+CdC,QAAQ,EAAE,QA/CI;EAgDdC,QAAQ,EAAE,QAhDI;EAiDdC,MAAM,EAAE,QAjDM;EAkDdC,OAAO,EAAE,QAlDK;EAmDdC,SAAS,EAAE,QAnDG;EAoDdC,GAAG,EAAE,QApDS;EAqDdC,MAAM,EAAE,QArDM;EAsDdC,GAAG,EAAE,QAtDS;EAuDdC,GAAG,EAAE,MAvDS;EAwDdC,KAAK,EAAE,QAxDO;EAyDdC,GAAG,EAAE,QAzDS;EA0DdC,OAAO,EAAE,QA1DK;EA2DdC,MAAM,EAAE,QA3DM;EA4DdC,OAAO,EAAE,QA5DK;EA6DdC,KAAK,EAAE,QA7DO;EA8DdC,IAAI,EAAE,QA9DQ;EA+DdC,KAAK,EAAE,QA/DO;EAgEdC,MAAM,EAAE,QAhEM;EAiEdC,SAAS,EAAE,QAjEG;EAkEdC,OAAO,EAAE,QAlEK;EAmEdC,UAAU,EAAE,QAnEE;EAoEdC,GAAG,EAAE,QApES;EAqEdC,IAAI,EAAE,QArEQ;EAsEdC,KAAK,EAAE,QAtEO;EAuEdC,SAAS,EAAE,QAvEG;EAwEdC,IAAI,EAAE,QAxEQ;EAyEdC,IAAI,EAAE,QAzEQ;EA0EdC,IAAI,EAAE,QA1EQ;EA2EdC,IAAI,EAAE,QA3EQ;EA4EdC,MAAM,EAAE,QA5EM;EA6EdC,MAAM,EAAE,QA7EM;EA8EdC,MAAM,EAAE,QA9EM;EA+EdC,KAAK,EAAE,QA/EO;EAgFdC,KAAK,EAAE,QAhFO;EAiFdC,OAAO,EAAE,QAjFK;EAkFdC,GAAG,EAAE,QAlFS;EAmFdC,IAAI,EAAE,MAnFQ;EAoFdC,OAAO,EAAE,QApFK;EAqFdC,GAAG,EAAE,QArFS;EAsFdC,MAAM,EAAE,QAtFM;EAuFdC,KAAK,EAAE,QAvFO;EAwFdC,UAAU,EAAE,QAxFE;EAyFdC,GAAG,EAAE,IAzFS;EA0FdC,KAAK,EAAE,QA1FO;EA2FdC,MAAM,EAAE,QA3FM;EA4FdC,MAAM,EAAE,QA5FM;EA6FdC,IAAI,EAAE,QA7FQ;EA8FdC,SAAS,EAAE,MA9FG;EA+FdC,GAAG,EAAE,QA/FS;EAgGdC,QAAQ,EAAE,QAhGI;EAiGdC,UAAU,EAAE,QAjGE;EAkGdC,OAAO,EAAE,QAlGK;EAmGdC,QAAQ,EAAE,QAnGI;EAoGdC,OAAO,EAAE,QApGK;EAqGdC,UAAU,EAAE,QArGE;EAsGdC,IAAI,EAAE,IAtGQ;EAuGdC,MAAM,EAAE,QAvGM;EAwGdC,IAAI,EAAE,QAxGQ;EAyGdC,OAAO,EAAE,QAzGK;EA0GdC,KAAK,EAAE,QA1GO;EA2GdC,OAAO,EAAE,QA3GK;EA4GdC,IAAI,EAAE,QA5GQ;EA6GdC,SAAS,EAAE,QA7GG;EA8GdC,MAAM,EAAE,QA9GM;EA+GdC,KAAK,EAAE,QA/GO;EAgHdC,UAAU,EAAE,QAhHE;EAiHdC,SAAS,EAAE,QAjHG;EAkHdC,OAAO,EAAE,QAlHK;EAmHdC,IAAI,EAAE,QAnHQ;EAoHdC,GAAG,EAAE,QApHS;EAqHdC,IAAI,EAAE,QArHQ;EAsHdC,OAAO,EAAE,QAtHK;EAuHdC,KAAK,EAAE,QAvHO;EAwHdC,WAAW,EAAE,QAxHC;EAyHdC,EAAE,EAAE,QAzHU;EA0HdC,QAAQ,EAAE,QA1HI;EA2HdC,KAAK,EAAE,QA3HO;EA4HdC,SAAS,EAAE,QA5HG;EA6HdC,KAAK,EAAE,QA7HO;EA8HdC,SAAS,EAAE,QA9HG;EA+HdC,KAAK,EAAE,QA/HO;EAgIdC,OAAO,EAAE,QAhIK;EAiIdC,KAAK,EAAE,QAjIO;EAkIdC,MAAM,EAAE,QAlIM;EAmIdC,KAAK,EAAE,QAnIO;EAoIdC,GAAG,EAAE,QApIS;EAqIdC,IAAI,EAAE,QArIQ;EAsIdC,IAAI,EAAE,QAtIQ;EAuIdC,IAAI,EAAE,QAvIQ;EAwIdC,QAAQ,EAAE,MAxII;EAyIdC,MAAM,EAAE,QAzIM;EA0IdC,GAAG,EAAE,QA1IS;EA2IdC,GAAG,EAAE,MA3IS;EA4IdC,KAAK,EAAE,QA5IO;EA6IdC,MAAM,EAAE,QA7IM;EA8IdC,EAAE,EAAE,QA9IU;EA+IdC,KAAK,EAAE,QA/IO;EAgJdC,GAAG,EAAE,QAhJS;EAiJdC,IAAI,EAAE,QAjJQ;EAkJdC,SAAS,EAAE,QAlJG;EAmJdC,EAAE,EAAE,QAnJU;EAoJdC,KAAK,EAAE;AApJO,CAAhB;;AAsJA,SAASC,MAAT,GAAkB;EAChB,MAAMC,QAAQ,GAAG,EAAjB;EACA,MAAM7d,IAAI,GAAG3B,MAAM,CAAC2B,IAAP,CAAYuU,OAAZ,CAAb;EACA,MAAMuJ,KAAK,GAAGzf,MAAM,CAAC2B,IAAP,CAAYY,GAAZ,CAAd;EACA,IAAId,CAAJ,EAAOie,CAAP,EAAU/c,CAAV,EAAagd,EAAb,EAAiBC,EAAjB;;EACA,KAAKne,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGE,IAAI,CAACC,MAArB,EAA6BH,CAAC,EAA9B,EAAkC;IAChCke,EAAE,GAAGC,EAAE,GAAGje,IAAI,CAACF,CAAD,CAAd;;IACA,KAAKie,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGD,KAAK,CAAC7d,MAAtB,EAA8B8d,CAAC,EAA/B,EAAmC;MACjC/c,CAAC,GAAG8c,KAAK,CAACC,CAAD,CAAT;MACAE,EAAE,GAAGA,EAAE,CAACC,OAAH,CAAWld,CAAX,EAAcJ,GAAG,CAACI,CAAD,CAAjB,CAAL;IACD;;IACDA,CAAC,GAAGmd,QAAQ,CAAC5J,OAAO,CAACyJ,EAAD,CAAR,EAAc,EAAd,CAAZ;IACAH,QAAQ,CAACI,EAAD,CAAR,GAAe,CAACjd,CAAC,IAAI,EAAL,GAAU,IAAX,EAAiBA,CAAC,IAAI,CAAL,GAAS,IAA1B,EAAgCA,CAAC,GAAG,IAApC,CAAf;EACD;;EACD,OAAO6c,QAAP;AACD;;AACD,IAAIO,KAAJ;;AACA,SAASC,SAAT,CAAmBhb,GAAnB,EAAwB;EACtB,IAAI,CAAC+a,KAAL,EAAY;IACVA,KAAK,GAAGR,MAAM,EAAd;IACAQ,KAAK,CAACE,WAAN,GAAoB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAApB;EACD;;EACD,MAAM3a,CAAC,GAAGya,KAAK,CAAC/a,GAAG,CAACkb,WAAJ,EAAD,CAAf;EACA,OAAO5a,CAAC,IAAI;IACV8N,CAAC,EAAE9N,CAAC,CAAC,CAAD,CADM;IAEV+N,CAAC,EAAE/N,CAAC,CAAC,CAAD,CAFM;IAGVC,CAAC,EAAED,CAAC,CAAC,CAAD,CAHM;IAIVA,CAAC,EAAEA,CAAC,CAAC1D,MAAF,KAAa,CAAb,GAAiB0D,CAAC,CAAC,CAAD,CAAlB,GAAwB;EAJjB,CAAZ;AAMD;;AACD,MAAM6a,MAAM,GAAG,sGAAf;;AACA,SAASC,QAAT,CAAkBpb,GAAlB,EAAuB;EACrB,MAAM4M,CAAC,GAAGuO,MAAM,CAAC3L,IAAP,CAAYxP,GAAZ,CAAV;EACA,IAAIM,CAAC,GAAG,GAAR;EACA,IAAI8N,CAAJ,EAAOC,CAAP,EAAU9N,CAAV;;EACA,IAAI,CAACqM,CAAL,EAAQ;IACN;EACD;;EACD,IAAIA,CAAC,CAAC,CAAD,CAAD,KAASwB,CAAb,EAAgB;IACd,MAAMnP,CAAC,GAAG,CAAC2N,CAAC,CAAC,CAAD,CAAZ;IACAtM,CAAC,GAAGsM,CAAC,CAAC,CAAD,CAAD,GAAOM,GAAG,CAACjO,CAAD,CAAV,GAAgB8N,GAAG,CAAC9N,CAAC,GAAG,GAAL,EAAU,CAAV,EAAa,GAAb,CAAvB;EACD;;EACDmP,CAAC,GAAG,CAACxB,CAAC,CAAC,CAAD,CAAN;EACAyB,CAAC,GAAG,CAACzB,CAAC,CAAC,CAAD,CAAN;EACArM,CAAC,GAAG,CAACqM,CAAC,CAAC,CAAD,CAAN;EACAwB,CAAC,GAAG,OAAOxB,CAAC,CAAC,CAAD,CAAD,GAAOM,GAAG,CAACkB,CAAD,CAAV,GAAgBrB,GAAG,CAACqB,CAAD,EAAI,CAAJ,EAAO,GAAP,CAA1B,CAAJ;EACAC,CAAC,GAAG,OAAOzB,CAAC,CAAC,CAAD,CAAD,GAAOM,GAAG,CAACmB,CAAD,CAAV,GAAgBtB,GAAG,CAACsB,CAAD,EAAI,CAAJ,EAAO,GAAP,CAA1B,CAAJ;EACA9N,CAAC,GAAG,OAAOqM,CAAC,CAAC,CAAD,CAAD,GAAOM,GAAG,CAAC3M,CAAD,CAAV,GAAgBwM,GAAG,CAACxM,CAAD,EAAI,CAAJ,EAAO,GAAP,CAA1B,CAAJ;EACA,OAAO;IACL6N,CAAC,EAAEA,CADE;IAELC,CAAC,EAAEA,CAFE;IAGL9N,CAAC,EAAEA,CAHE;IAILD,CAAC,EAAEA;EAJE,CAAP;AAMD;;AACD,SAAS+a,SAAT,CAAmBpc,CAAnB,EAAsB;EACpB,OAAOA,CAAC,KACNA,CAAC,CAACqB,CAAF,GAAM,GAAN,GACK,QAAOrB,CAAC,CAACmP,CAAE,KAAInP,CAAC,CAACoP,CAAE,KAAIpP,CAAC,CAACsB,CAAE,KAAI6M,GAAG,CAACnO,CAAC,CAACqB,CAAH,CAAM,GAD7C,GAEK,OAAMrB,CAAC,CAACmP,CAAE,KAAInP,CAAC,CAACoP,CAAE,KAAIpP,CAAC,CAACsB,CAAE,GAHzB,CAAR;AAKD;;AACD,MAAM+a,EAAE,GAAGrc,CAAC,IAAIA,CAAC,IAAI,SAAL,GAAiBA,CAAC,GAAG,KAArB,GAA6B6B,IAAI,CAACiB,GAAL,CAAS9C,CAAT,EAAY,MAAM,GAAlB,IAAyB,KAAzB,GAAiC,KAA9E;;AACA,MAAMmI,IAAI,GAAGnI,CAAC,IAAIA,CAAC,IAAI,OAAL,GAAeA,CAAC,GAAG,KAAnB,GAA2B6B,IAAI,CAACiB,GAAL,CAAS,CAAC9C,CAAC,GAAG,KAAL,IAAc,KAAvB,EAA8B,GAA9B,CAA7C;;AACA,SAASsc,WAAT,CAAqBC,IAArB,EAA2BC,IAA3B,EAAiCjR,CAAjC,EAAoC;EAClC,MAAM4D,CAAC,GAAGhH,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACpN,CAAN,CAAJ,CAAd;EACA,MAAMC,CAAC,GAAGjH,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACnN,CAAN,CAAJ,CAAd;EACA,MAAM9N,CAAC,GAAG6G,IAAI,CAACgG,GAAG,CAACoO,IAAI,CAACjb,CAAN,CAAJ,CAAd;EACA,OAAO;IACL6N,CAAC,EAAEjB,GAAG,CAACmO,EAAE,CAAClN,CAAC,GAAG5D,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAACrN,CAAN,CAAJ,CAAJ,GAAoBA,CAAxB,CAAN,CAAH,CADD;IAELC,CAAC,EAAElB,GAAG,CAACmO,EAAE,CAACjN,CAAC,GAAG7D,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAACpN,CAAN,CAAJ,CAAJ,GAAoBA,CAAxB,CAAN,CAAH,CAFD;IAGL9N,CAAC,EAAE4M,GAAG,CAACmO,EAAE,CAAC/a,CAAC,GAAGiK,CAAC,IAAIpD,IAAI,CAACgG,GAAG,CAACqO,IAAI,CAAClb,CAAN,CAAJ,CAAJ,GAAoBA,CAAxB,CAAN,CAAH,CAHD;IAILD,CAAC,EAAEkb,IAAI,CAAClb,CAAL,GAASkK,CAAC,IAAIiR,IAAI,CAACnb,CAAL,GAASkb,IAAI,CAAClb,CAAlB;EAJR,CAAP;AAMD;;AACD,SAASob,MAAT,CAAgBzc,CAAhB,EAAmBxC,CAAnB,EAAsBkf,KAAtB,EAA6B;EAC3B,IAAI1c,CAAJ,EAAO;IACL,IAAIW,GAAG,GAAGqP,OAAO,CAAChQ,CAAD,CAAjB;IACAW,GAAG,CAACnD,CAAD,CAAH,GAASqE,IAAI,CAACqC,GAAL,CAAS,CAAT,EAAYrC,IAAI,CAACoC,GAAL,CAAStD,GAAG,CAACnD,CAAD,CAAH,GAASmD,GAAG,CAACnD,CAAD,CAAH,GAASkf,KAA3B,EAAkClf,CAAC,KAAK,CAAN,GAAU,GAAV,GAAgB,CAAlD,CAAZ,CAAT;IACAmD,GAAG,GAAGuP,OAAO,CAACvP,GAAD,CAAb;IACAX,CAAC,CAACmP,CAAF,GAAMxO,GAAG,CAAC,CAAD,CAAT;IACAX,CAAC,CAACoP,CAAF,GAAMzO,GAAG,CAAC,CAAD,CAAT;IACAX,CAAC,CAACsB,CAAF,GAAMX,GAAG,CAAC,CAAD,CAAT;EACD;AACF;;AACD,SAASgc,KAAT,CAAe3c,CAAf,EAAkB4c,KAAlB,EAAyB;EACvB,OAAO5c,CAAC,GAAGjE,MAAM,CAACsP,MAAP,CAAcuR,KAAK,IAAI,EAAvB,EAA2B5c,CAA3B,CAAH,GAAmCA,CAA3C;AACD;;AACD,SAAS6c,UAAT,CAAoBC,KAApB,EAA2B;EACzB,IAAI9c,CAAC,GAAG;IAACmP,CAAC,EAAE,CAAJ;IAAOC,CAAC,EAAE,CAAV;IAAa9N,CAAC,EAAE,CAAhB;IAAmBD,CAAC,EAAE;EAAtB,CAAR;;EACA,IAAIxF,KAAK,CAACD,OAAN,CAAckhB,KAAd,CAAJ,EAA0B;IACxB,IAAIA,KAAK,CAACnf,MAAN,IAAgB,CAApB,EAAuB;MACrBqC,CAAC,GAAG;QAACmP,CAAC,EAAE2N,KAAK,CAAC,CAAD,CAAT;QAAc1N,CAAC,EAAE0N,KAAK,CAAC,CAAD,CAAtB;QAA2Bxb,CAAC,EAAEwb,KAAK,CAAC,CAAD,CAAnC;QAAwCzb,CAAC,EAAE;MAA3C,CAAJ;;MACA,IAAIyb,KAAK,CAACnf,MAAN,GAAe,CAAnB,EAAsB;QACpBqC,CAAC,CAACqB,CAAF,GAAM6M,GAAG,CAAC4O,KAAK,CAAC,CAAD,CAAN,CAAT;MACD;IACF;EACF,CAPD,MAOO;IACL9c,CAAC,GAAG2c,KAAK,CAACG,KAAD,EAAQ;MAAC3N,CAAC,EAAE,CAAJ;MAAOC,CAAC,EAAE,CAAV;MAAa9N,CAAC,EAAE,CAAhB;MAAmBD,CAAC,EAAE;IAAtB,CAAR,CAAT;IACArB,CAAC,CAACqB,CAAF,GAAM6M,GAAG,CAAClO,CAAC,CAACqB,CAAH,CAAT;EACD;;EACD,OAAOrB,CAAP;AACD;;AACD,SAAS+c,aAAT,CAAuBhc,GAAvB,EAA4B;EAC1B,IAAIA,GAAG,CAACC,MAAJ,CAAW,CAAX,MAAkB,GAAtB,EAA2B;IACzB,OAAOmb,QAAQ,CAACpb,GAAD,CAAf;EACD;;EACD,OAAOuP,QAAQ,CAACvP,GAAD,CAAf;AACD;;AACD,MAAMic,KAAN,CAAY;EACVC,WAAW,CAACH,KAAD,EAAQ;IACjB,IAAIA,KAAK,YAAYE,KAArB,EAA4B;MAC1B,OAAOF,KAAP;IACD;;IACD,MAAMhhB,IAAI,GAAG,OAAOghB,KAApB;IACA,IAAI9c,CAAJ;;IACA,IAAIlE,IAAI,KAAK,QAAb,EAAuB;MACrBkE,CAAC,GAAG6c,UAAU,CAACC,KAAD,CAAd;IACD,CAFD,MAEO,IAAIhhB,IAAI,KAAK,QAAb,EAAuB;MAC5BkE,CAAC,GAAGqP,QAAQ,CAACyN,KAAD,CAAR,IAAmBf,SAAS,CAACe,KAAD,CAA5B,IAAuCC,aAAa,CAACD,KAAD,CAAxD;IACD;;IACD,KAAKI,IAAL,GAAYld,CAAZ;IACA,KAAKmd,MAAL,GAAc,CAAC,CAACnd,CAAhB;EACD;;EACQ,IAALod,KAAK,GAAG;IACV,OAAO,KAAKD,MAAZ;EACD;;EACM,IAAHrN,GAAG,GAAG;IACR,IAAI9P,CAAC,GAAG2c,KAAK,CAAC,KAAKO,IAAN,CAAb;;IACA,IAAIld,CAAJ,EAAO;MACLA,CAAC,CAACqB,CAAF,GAAM8M,GAAG,CAACnO,CAAC,CAACqB,CAAH,CAAT;IACD;;IACD,OAAOrB,CAAP;EACD;;EACM,IAAH8P,GAAG,CAACzP,GAAD,EAAM;IACX,KAAK6c,IAAL,GAAYL,UAAU,CAACxc,GAAD,CAAtB;EACD;;EACD+b,SAAS,GAAG;IACV,OAAO,KAAKe,MAAL,GAAcf,SAAS,CAAC,KAAKc,IAAN,CAAvB,GAAqCtd,SAA5C;EACD;;EACD4P,SAAS,GAAG;IACV,OAAO,KAAK2N,MAAL,GAAc3N,SAAS,CAAC,KAAK0N,IAAN,CAAvB,GAAqCtd,SAA5C;EACD;;EACDgR,SAAS,GAAG;IACV,OAAO,KAAKuM,MAAL,GAAcvM,SAAS,CAAC,KAAKsM,IAAN,CAAvB,GAAqCtd,SAA5C;EACD;;EACDyd,GAAG,CAACC,KAAD,EAAQC,MAAR,EAAgB;IACjB,IAAID,KAAJ,EAAW;MACT,MAAME,EAAE,GAAG,KAAK1N,GAAhB;MACA,MAAM2N,EAAE,GAAGH,KAAK,CAACxN,GAAjB;MACA,IAAI4N,EAAJ;MACA,MAAMlZ,CAAC,GAAG+Y,MAAM,KAAKG,EAAX,GAAgB,GAAhB,GAAsBH,MAAhC;MACA,MAAM1N,CAAC,GAAG,IAAIrL,CAAJ,GAAQ,CAAlB;MACA,MAAMnD,CAAC,GAAGmc,EAAE,CAACnc,CAAH,GAAOoc,EAAE,CAACpc,CAApB;MACA,MAAMsc,EAAE,GAAG,CAAC,CAAC9N,CAAC,GAAGxO,CAAJ,KAAU,CAAC,CAAX,GAAewO,CAAf,GAAmB,CAACA,CAAC,GAAGxO,CAAL,KAAW,IAAIwO,CAAC,GAAGxO,CAAnB,CAApB,IAA6C,CAA9C,IAAmD,GAA9D;MACAqc,EAAE,GAAG,IAAIC,EAAT;MACAH,EAAE,CAACrO,CAAH,GAAO,OAAOwO,EAAE,GAAGH,EAAE,CAACrO,CAAR,GAAYuO,EAAE,GAAGD,EAAE,CAACtO,CAApB,GAAwB,GAAtC;MACAqO,EAAE,CAACpO,CAAH,GAAO,OAAOuO,EAAE,GAAGH,EAAE,CAACpO,CAAR,GAAYsO,EAAE,GAAGD,EAAE,CAACrO,CAApB,GAAwB,GAAtC;MACAoO,EAAE,CAAClc,CAAH,GAAO,OAAOqc,EAAE,GAAGH,EAAE,CAAClc,CAAR,GAAYoc,EAAE,GAAGD,EAAE,CAACnc,CAApB,GAAwB,GAAtC;MACAkc,EAAE,CAACnc,CAAH,GAAOmD,CAAC,GAAGgZ,EAAE,CAACnc,CAAP,GAAW,CAAC,IAAImD,CAAL,IAAUiZ,EAAE,CAACpc,CAA/B;MACA,KAAKyO,GAAL,GAAW0N,EAAX;IACD;;IACD,OAAO,IAAP;EACD;;EACDlB,WAAW,CAACgB,KAAD,EAAQ/R,CAAR,EAAW;IACpB,IAAI+R,KAAJ,EAAW;MACT,KAAKJ,IAAL,GAAYZ,WAAW,CAAC,KAAKY,IAAN,EAAYI,KAAK,CAACJ,IAAlB,EAAwB3R,CAAxB,CAAvB;IACD;;IACD,OAAO,IAAP;EACD;;EACDoR,KAAK,GAAG;IACN,OAAO,IAAIK,KAAJ,CAAU,KAAKlN,GAAf,CAAP;EACD;;EACDP,KAAK,CAAClO,CAAD,EAAI;IACP,KAAK6b,IAAL,CAAU7b,CAAV,GAAc6M,GAAG,CAAC7M,CAAD,CAAjB;IACA,OAAO,IAAP;EACD;;EACDuc,OAAO,CAAClB,KAAD,EAAQ;IACb,MAAM5M,GAAG,GAAG,KAAKoN,IAAjB;IACApN,GAAG,CAACzO,CAAJ,IAAS,IAAIqb,KAAb;IACA,OAAO,IAAP;EACD;;EACDmB,SAAS,GAAG;IACV,MAAM/N,GAAG,GAAG,KAAKoN,IAAjB;IACA,MAAMY,GAAG,GAAGnb,KAAK,CAACmN,GAAG,CAACX,CAAJ,GAAQ,GAAR,GAAcW,GAAG,CAACV,CAAJ,GAAQ,IAAtB,GAA6BU,GAAG,CAACxO,CAAJ,GAAQ,IAAtC,CAAjB;IACAwO,GAAG,CAACX,CAAJ,GAAQW,GAAG,CAACV,CAAJ,GAAQU,GAAG,CAACxO,CAAJ,GAAQwc,GAAxB;IACA,OAAO,IAAP;EACD;;EACDC,OAAO,CAACrB,KAAD,EAAQ;IACb,MAAM5M,GAAG,GAAG,KAAKoN,IAAjB;IACApN,GAAG,CAACzO,CAAJ,IAAS,IAAIqb,KAAb;IACA,OAAO,IAAP;EACD;;EACDsB,MAAM,GAAG;IACP,MAAMhe,CAAC,GAAG,KAAKkd,IAAf;IACAld,CAAC,CAACmP,CAAF,GAAM,MAAMnP,CAAC,CAACmP,CAAd;IACAnP,CAAC,CAACoP,CAAF,GAAM,MAAMpP,CAAC,CAACoP,CAAd;IACApP,CAAC,CAACsB,CAAF,GAAM,MAAMtB,CAAC,CAACsB,CAAd;IACA,OAAO,IAAP;EACD;;EACD2c,OAAO,CAACvB,KAAD,EAAQ;IACbD,MAAM,CAAC,KAAKS,IAAN,EAAY,CAAZ,EAAeR,KAAf,CAAN;IACA,OAAO,IAAP;EACD;;EACDwB,MAAM,CAACxB,KAAD,EAAQ;IACZD,MAAM,CAAC,KAAKS,IAAN,EAAY,CAAZ,EAAe,CAACR,KAAhB,CAAN;IACA,OAAO,IAAP;EACD;;EACDyB,QAAQ,CAACzB,KAAD,EAAQ;IACdD,MAAM,CAAC,KAAKS,IAAN,EAAY,CAAZ,EAAeR,KAAf,CAAN;IACA,OAAO,IAAP;EACD;;EACD0B,UAAU,CAAC1B,KAAD,EAAQ;IAChBD,MAAM,CAAC,KAAKS,IAAN,EAAY,CAAZ,EAAe,CAACR,KAAhB,CAAN;IACA,OAAO,IAAP;EACD;;EACDhM,MAAM,CAACC,GAAD,EAAM;IACVD,MAAM,CAAC,KAAKwM,IAAN,EAAYvM,GAAZ,CAAN;IACA,OAAO,IAAP;EACD;;AA9GS;;AAgHZ,SAAS0N,SAAT,CAAmBvB,KAAnB,EAA0B;EACxB,OAAO,IAAIE,KAAJ,CAAUF,KAAV,CAAP;AACD;;AAED,SAASwB,mBAAT,CAA6B3iB,KAA7B,EAAoC;EAClC,IAAIA,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA9B,EAAwC;IACtC,MAAMG,IAAI,GAAGH,KAAK,CAACM,QAAN,EAAb;IACA,OAAOH,IAAI,KAAK,wBAAT,IAAqCA,IAAI,KAAK,yBAArD;EACD;;EACD,OAAO,KAAP;AACD;;AACD,SAASwhB,KAAT,CAAe3hB,KAAf,EAAsB;EACpB,OAAO2iB,mBAAmB,CAAC3iB,KAAD,CAAnB,GAA6BA,KAA7B,GAAqC0iB,SAAS,CAAC1iB,KAAD,CAArD;AACD;;AACD,SAAS4iB,aAAT,CAAuB5iB,KAAvB,EAA8B;EAC5B,OAAO2iB,mBAAmB,CAAC3iB,KAAD,CAAnB,GACHA,KADG,GAEH0iB,SAAS,CAAC1iB,KAAD,CAAT,CAAiBwiB,QAAjB,CAA0B,GAA1B,EAA+BD,MAA/B,CAAsC,GAAtC,EAA2C1O,SAA3C,EAFJ;AAGD;;AAED,MAAMgP,SAAS,GAAGziB,MAAM,CAACyC,MAAP,CAAc,IAAd,CAAlB;AACA,MAAMigB,WAAW,GAAG1iB,MAAM,CAACyC,MAAP,CAAc,IAAd,CAApB;;AACA,SAASkgB,UAAT,CAAoBC,IAApB,EAA0B/f,GAA1B,EAA+B;EAC7B,IAAI,CAACA,GAAL,EAAU;IACR,OAAO+f,IAAP;EACD;;EACD,MAAMjhB,IAAI,GAAGkB,GAAG,CAAC8B,KAAJ,CAAU,GAAV,CAAb;;EACA,KAAK,IAAIlD,CAAC,GAAG,CAAR,EAAWgG,CAAC,GAAG9F,IAAI,CAACC,MAAzB,EAAiCH,CAAC,GAAGgG,CAArC,EAAwC,EAAEhG,CAA1C,EAA6C;IAC3C,MAAMkB,CAAC,GAAGhB,IAAI,CAACF,CAAD,CAAd;IACAmhB,IAAI,GAAGA,IAAI,CAACjgB,CAAD,CAAJ,KAAYigB,IAAI,CAACjgB,CAAD,CAAJ,GAAU3C,MAAM,CAACyC,MAAP,CAAc,IAAd,CAAtB,CAAP;EACD;;EACD,OAAOmgB,IAAP;AACD;;AACD,SAAS3W,GAAT,CAAa4W,IAAb,EAAmBnf,KAAnB,EAA0BoH,MAA1B,EAAkC;EAChC,IAAI,OAAOpH,KAAP,KAAiB,QAArB,EAA+B;IAC7B,OAAOP,KAAK,CAACwf,UAAU,CAACE,IAAD,EAAOnf,KAAP,CAAX,EAA0BoH,MAA1B,CAAZ;EACD;;EACD,OAAO3H,KAAK,CAACwf,UAAU,CAACE,IAAD,EAAO,EAAP,CAAX,EAAuBnf,KAAvB,CAAZ;AACD;;AACD,MAAMof,QAAN,CAAe;EACb5B,WAAW,CAAC6B,YAAD,EAAe;IACxB,KAAKC,SAAL,GAAiBnf,SAAjB;IACA,KAAKof,eAAL,GAAuB,iBAAvB;IACA,KAAKC,WAAL,GAAmB,iBAAnB;IACA,KAAK3B,KAAL,GAAa,MAAb;IACA,KAAK4B,QAAL,GAAgB,EAAhB;;IACA,KAAKC,gBAAL,GAAyBC,OAAD,IAAaA,OAAO,CAACC,KAAR,CAAcC,QAAd,CAAuBC,mBAAvB,EAArC;;IACA,KAAKC,QAAL,GAAgB,EAAhB;IACA,KAAKC,MAAL,GAAc,CACZ,WADY,EAEZ,UAFY,EAGZ,OAHY,EAIZ,YAJY,EAKZ,WALY,CAAd;IAOA,KAAKC,IAAL,GAAY;MACVC,MAAM,EAAE,oDADE;MAEVpe,IAAI,EAAE,EAFI;MAGVqe,KAAK,EAAE,QAHG;MAIVC,UAAU,EAAE,GAJF;MAKVtC,MAAM,EAAE;IALE,CAAZ;IAOA,KAAKuC,KAAL,GAAa,EAAb;;IACA,KAAKC,oBAAL,GAA4B,CAACC,GAAD,EAAMjhB,OAAN,KAAkBwf,aAAa,CAACxf,OAAO,CAACigB,eAAT,CAA3D;;IACA,KAAKiB,gBAAL,GAAwB,CAACD,GAAD,EAAMjhB,OAAN,KAAkBwf,aAAa,CAACxf,OAAO,CAACkgB,WAAT,CAAvD;;IACA,KAAKiB,UAAL,GAAkB,CAACF,GAAD,EAAMjhB,OAAN,KAAkBwf,aAAa,CAACxf,OAAO,CAACue,KAAT,CAAjD;;IACA,KAAK6C,SAAL,GAAiB,GAAjB;IACA,KAAKC,WAAL,GAAmB;MACjBC,IAAI,EAAE,SADW;MAEjBC,SAAS,EAAE,IAFM;MAGjBC,gBAAgB,EAAE;IAHD,CAAnB;IAKA,KAAKC,mBAAL,GAA2B,IAA3B;IACA,KAAKC,OAAL,GAAe,IAAf;IACA,KAAKC,OAAL,GAAe,IAAf;IACA,KAAKC,OAAL,GAAe,IAAf;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,KAAL,GAAalhB,SAAb;IACA,KAAKmhB,MAAL,GAAc,EAAd;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA,KAAKC,uBAAL,GAA+B,IAA/B;IACA,KAAKC,QAAL,CAAcpC,YAAd;EACD;;EACD9W,GAAG,CAACvI,KAAD,EAAQoH,MAAR,EAAgB;IACjB,OAAOmB,GAAG,CAAC,IAAD,EAAOvI,KAAP,EAAcoH,MAAd,CAAV;EACD;;EACDsa,GAAG,CAAC1hB,KAAD,EAAQ;IACT,OAAOif,UAAU,CAAC,IAAD,EAAOjf,KAAP,CAAjB;EACD;;EACDyhB,QAAQ,CAACzhB,KAAD,EAAQoH,MAAR,EAAgB;IACtB,OAAOmB,GAAG,CAACyW,WAAD,EAAchf,KAAd,EAAqBoH,MAArB,CAAV;EACD;;EACDua,QAAQ,CAAC3hB,KAAD,EAAQoH,MAAR,EAAgB;IACtB,OAAOmB,GAAG,CAACwW,SAAD,EAAY/e,KAAZ,EAAmBoH,MAAnB,CAAV;EACD;;EACDwa,KAAK,CAAC5hB,KAAD,EAAQ6hB,IAAR,EAAcC,WAAd,EAA2BC,UAA3B,EAAuC;IAC1C,MAAMC,WAAW,GAAG/C,UAAU,CAAC,IAAD,EAAOjf,KAAP,CAA9B;IACA,MAAMiiB,iBAAiB,GAAGhD,UAAU,CAAC,IAAD,EAAO6C,WAAP,CAApC;IACA,MAAMI,WAAW,GAAG,MAAML,IAA1B;IACAvlB,MAAM,CAAC6lB,gBAAP,CAAwBH,WAAxB,EAAqC;MACnC,CAACE,WAAD,GAAe;QACbhmB,KAAK,EAAE8lB,WAAW,CAACH,IAAD,CADL;QAEbO,QAAQ,EAAE;MAFG,CADoB;MAKnC,CAACP,IAAD,GAAQ;QACNja,UAAU,EAAE,IADN;;QAEN8Z,GAAG,GAAG;UACJ,MAAMW,KAAK,GAAG,KAAKH,WAAL,CAAd;UACA,MAAMpjB,MAAM,GAAGmjB,iBAAiB,CAACF,UAAD,CAAhC;;UACA,IAAIplB,QAAQ,CAAC0lB,KAAD,CAAZ,EAAqB;YACnB,OAAO/lB,MAAM,CAACsP,MAAP,CAAc,EAAd,EAAkB9M,MAAlB,EAA0BujB,KAA1B,CAAP;UACD;;UACD,OAAOplB,cAAc,CAAColB,KAAD,EAAQvjB,MAAR,CAArB;QACD,CATK;;QAUNyJ,GAAG,CAACrM,KAAD,EAAQ;UACT,KAAKgmB,WAAL,IAAoBhmB,KAApB;QACD;;MAZK;IAL2B,CAArC;EAoBD;;AAjFY;;AAmFf,IAAIomB,QAAQ,GAAG,IAAIlD,QAAJ,CAAa;EAC1BmD,WAAW,EAAGV,IAAD,IAAU,CAACA,IAAI,CAACW,UAAL,CAAgB,IAAhB,CADE;EAE1BC,UAAU,EAAGZ,IAAD,IAAUA,IAAI,KAAK,QAFL;EAG1BxB,KAAK,EAAE;IACLqC,SAAS,EAAE;EADN,CAHmB;EAM1B/B,WAAW,EAAE;IACX4B,WAAW,EAAE,KADF;IAEXE,UAAU,EAAE;EAFD;AANa,CAAb,CAAf;;AAYA,SAASE,YAAT,CAAsB1C,IAAtB,EAA4B;EAC1B,IAAI,CAACA,IAAD,IAAShkB,aAAa,CAACgkB,IAAI,CAACne,IAAN,CAAtB,IAAqC7F,aAAa,CAACgkB,IAAI,CAACC,MAAN,CAAtD,EAAqE;IACnE,OAAO,IAAP;EACD;;EACD,OAAO,CAACD,IAAI,CAACE,KAAL,GAAaF,IAAI,CAACE,KAAL,GAAa,GAA1B,GAAgC,EAAjC,KACJF,IAAI,CAACnC,MAAL,GAAcmC,IAAI,CAACnC,MAAL,GAAc,GAA5B,GAAkC,EAD9B,IAELmC,IAAI,CAACne,IAFA,GAEO,KAFP,GAGLme,IAAI,CAACC,MAHP;AAID;;AACD,SAAS0C,YAAT,CAAsBrC,GAAtB,EAA2BsC,IAA3B,EAAiCC,EAAjC,EAAqCC,OAArC,EAA8CC,MAA9C,EAAsD;EACpD,IAAIC,SAAS,GAAGJ,IAAI,CAACG,MAAD,CAApB;;EACA,IAAI,CAACC,SAAL,EAAgB;IACdA,SAAS,GAAGJ,IAAI,CAACG,MAAD,CAAJ,GAAezC,GAAG,CAAC2C,WAAJ,CAAgBF,MAAhB,EAAwBG,KAAnD;IACAL,EAAE,CAAC1hB,IAAH,CAAQ4hB,MAAR;EACD;;EACD,IAAIC,SAAS,GAAGF,OAAhB,EAAyB;IACvBA,OAAO,GAAGE,SAAV;EACD;;EACD,OAAOF,OAAP;AACD;;AACD,SAASK,YAAT,CAAsB7C,GAAtB,EAA2BN,IAA3B,EAAiCoD,aAAjC,EAAgDC,KAAhD,EAAuD;EACrDA,KAAK,GAAGA,KAAK,IAAI,EAAjB;EACA,IAAIT,IAAI,GAAGS,KAAK,CAACT,IAAN,GAAaS,KAAK,CAACT,IAAN,IAAc,EAAtC;EACA,IAAIC,EAAE,GAAGQ,KAAK,CAACC,cAAN,GAAuBD,KAAK,CAACC,cAAN,IAAwB,EAAxD;;EACA,IAAID,KAAK,CAACrD,IAAN,KAAeA,IAAnB,EAAyB;IACvB4C,IAAI,GAAGS,KAAK,CAACT,IAAN,GAAa,EAApB;IACAC,EAAE,GAAGQ,KAAK,CAACC,cAAN,GAAuB,EAA5B;IACAD,KAAK,CAACrD,IAAN,GAAaA,IAAb;EACD;;EACDM,GAAG,CAACiD,IAAJ;EACAjD,GAAG,CAACN,IAAJ,GAAWA,IAAX;EACA,IAAI8C,OAAO,GAAG,CAAd;EACA,MAAMzkB,IAAI,GAAG+kB,aAAa,CAACnlB,MAA3B;EACA,IAAIH,CAAJ,EAAOie,CAAP,EAAUyH,IAAV,EAAgBC,KAAhB,EAAuBC,WAAvB;;EACA,KAAK5lB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGO,IAAhB,EAAsBP,CAAC,EAAvB,EAA2B;IACzB2lB,KAAK,GAAGL,aAAa,CAACtlB,CAAD,CAArB;;IACA,IAAI2lB,KAAK,KAAKvjB,SAAV,IAAuBujB,KAAK,KAAK,IAAjC,IAAyCvnB,OAAO,CAACunB,KAAD,CAAP,KAAmB,IAAhE,EAAsE;MACpEX,OAAO,GAAGH,YAAY,CAACrC,GAAD,EAAMsC,IAAN,EAAYC,EAAZ,EAAgBC,OAAhB,EAAyBW,KAAzB,CAAtB;IACD,CAFD,MAEO,IAAIvnB,OAAO,CAACunB,KAAD,CAAX,EAAoB;MACzB,KAAK1H,CAAC,GAAG,CAAJ,EAAOyH,IAAI,GAAGC,KAAK,CAACxlB,MAAzB,EAAiC8d,CAAC,GAAGyH,IAArC,EAA2CzH,CAAC,EAA5C,EAAgD;QAC9C2H,WAAW,GAAGD,KAAK,CAAC1H,CAAD,CAAnB;;QACA,IAAI2H,WAAW,KAAKxjB,SAAhB,IAA6BwjB,WAAW,KAAK,IAA7C,IAAqD,CAACxnB,OAAO,CAACwnB,WAAD,CAAjE,EAAgF;UAC9EZ,OAAO,GAAGH,YAAY,CAACrC,GAAD,EAAMsC,IAAN,EAAYC,EAAZ,EAAgBC,OAAhB,EAAyBY,WAAzB,CAAtB;QACD;MACF;IACF;EACF;;EACDpD,GAAG,CAACqD,OAAJ;EACA,MAAMC,KAAK,GAAGf,EAAE,CAAC5kB,MAAH,GAAY,CAA1B;;EACA,IAAI2lB,KAAK,GAAGR,aAAa,CAACnlB,MAA1B,EAAkC;IAChC,KAAKH,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG8lB,KAAhB,EAAuB9lB,CAAC,EAAxB,EAA4B;MAC1B,OAAO8kB,IAAI,CAACC,EAAE,CAAC/kB,CAAD,CAAH,CAAX;IACD;;IACD+kB,EAAE,CAAC1a,MAAH,CAAU,CAAV,EAAayb,KAAb;EACD;;EACD,OAAOd,OAAP;AACD;;AACD,SAASe,WAAT,CAAqBlE,KAArB,EAA4BmE,KAA5B,EAAmCZ,KAAnC,EAA0C;EACxC,MAAMzD,gBAAgB,GAAGE,KAAK,CAACoE,uBAA/B;EACA,MAAMC,SAAS,GAAGd,KAAK,KAAK,CAAV,GAAc/gB,IAAI,CAACqC,GAAL,CAAS0e,KAAK,GAAG,CAAjB,EAAoB,GAApB,CAAd,GAAyC,CAA3D;EACA,OAAO/gB,IAAI,CAACc,KAAL,CAAW,CAAC6gB,KAAK,GAAGE,SAAT,IAAsBvE,gBAAjC,IAAqDA,gBAArD,GAAwEuE,SAA/E;AACD;;AACD,SAASC,WAAT,CAAqBC,MAArB,EAA6B5D,GAA7B,EAAkC;EAChCA,GAAG,GAAGA,GAAG,IAAI4D,MAAM,CAACC,UAAP,CAAkB,IAAlB,CAAb;EACA7D,GAAG,CAACiD,IAAJ;EACAjD,GAAG,CAAC8D,cAAJ;EACA9D,GAAG,CAAC+D,SAAJ,CAAc,CAAd,EAAiB,CAAjB,EAAoBH,MAAM,CAAChB,KAA3B,EAAkCgB,MAAM,CAACI,MAAzC;EACAhE,GAAG,CAACqD,OAAJ;AACD;;AACD,SAASY,SAAT,CAAmBjE,GAAnB,EAAwBjhB,OAAxB,EAAiCkB,CAAjC,EAAoCE,CAApC,EAAuC;EACrC+jB,eAAe,CAAClE,GAAD,EAAMjhB,OAAN,EAAekB,CAAf,EAAkBE,CAAlB,EAAqB,IAArB,CAAf;AACD;;AACD,SAAS+jB,eAAT,CAAyBlE,GAAzB,EAA8BjhB,OAA9B,EAAuCkB,CAAvC,EAA0CE,CAA1C,EAA6C0P,CAA7C,EAAgD;EAC9C,IAAI/T,IAAJ,EAAUqoB,OAAV,EAAmBC,OAAnB,EAA4B7iB,IAA5B,EAAkC8iB,YAAlC,EAAgDzB,KAAhD;EACA,MAAMhD,KAAK,GAAG7gB,OAAO,CAACulB,UAAtB;EACA,MAAMC,QAAQ,GAAGxlB,OAAO,CAACwlB,QAAzB;EACA,MAAMC,MAAM,GAAGzlB,OAAO,CAACylB,MAAvB;EACA,IAAIC,GAAG,GAAG,CAACF,QAAQ,IAAI,CAAb,IAAkBriB,WAA5B;;EACA,IAAI0d,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA9B,EAAwC;IACtC9jB,IAAI,GAAG8jB,KAAK,CAAC3jB,QAAN,EAAP;;IACA,IAAIH,IAAI,KAAK,2BAAT,IAAwCA,IAAI,KAAK,4BAArD,EAAmF;MACjFkkB,GAAG,CAACiD,IAAJ;MACAjD,GAAG,CAAC0E,SAAJ,CAAczkB,CAAd,EAAiBE,CAAjB;MACA6f,GAAG,CAACtP,MAAJ,CAAW+T,GAAX;MACAzE,GAAG,CAAC2E,SAAJ,CAAc/E,KAAd,EAAqB,CAACA,KAAK,CAACgD,KAAP,GAAe,CAApC,EAAuC,CAAChD,KAAK,CAACoE,MAAP,GAAgB,CAAvD,EAA0DpE,KAAK,CAACgD,KAAhE,EAAuEhD,KAAK,CAACoE,MAA7E;MACAhE,GAAG,CAACqD,OAAJ;MACA;IACD;EACF;;EACD,IAAI5f,KAAK,CAAC+gB,MAAD,CAAL,IAAiBA,MAAM,IAAI,CAA/B,EAAkC;IAChC;EACD;;EACDxE,GAAG,CAAC4E,SAAJ;;EACA,QAAQhF,KAAR;IACA;MACE,IAAI/P,CAAJ,EAAO;QACLmQ,GAAG,CAAC6E,OAAJ,CAAY5kB,CAAZ,EAAeE,CAAf,EAAkB0P,CAAC,GAAG,CAAtB,EAAyB2U,MAAzB,EAAiC,CAAjC,EAAoC,CAApC,EAAuC1iB,GAAvC;MACD,CAFD,MAEO;QACLke,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAR,EAAWE,CAAX,EAAcqkB,MAAd,EAAsB,CAAtB,EAAyB1iB,GAAzB;MACD;;MACDke,GAAG,CAAC+E,SAAJ;MACA;;IACF,KAAK,UAAL;MACE/E,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAG4B,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA/B,EAAuCrkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA3D;MACAC,GAAG,IAAIpiB,aAAP;MACA2d,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAG4B,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA/B,EAAuCrkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA3D;MACAC,GAAG,IAAIpiB,aAAP;MACA2d,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAG4B,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA/B,EAAuCrkB,CAAC,GAAG0B,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA3D;MACAxE,GAAG,CAAC+E,SAAJ;MACA;;IACF,KAAK,aAAL;MACEV,YAAY,GAAGG,MAAM,GAAG,KAAxB;MACAjjB,IAAI,GAAGijB,MAAM,GAAGH,YAAhB;MACAF,OAAO,GAAGtiB,IAAI,CAAC6K,GAAL,CAAS+X,GAAG,GAAGriB,UAAf,IAA6Bb,IAAvC;MACA6iB,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAG,GAAGriB,UAAf,IAA6Bb,IAAvC;MACAye,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGkkB,OAAZ,EAAqBhkB,CAAC,GAAGikB,OAAzB,EAAkCC,YAAlC,EAAgDI,GAAG,GAAG7iB,EAAtD,EAA0D6iB,GAAG,GAAGtiB,OAAhE;MACA6d,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGmkB,OAAZ,EAAqBjkB,CAAC,GAAGgkB,OAAzB,EAAkCE,YAAlC,EAAgDI,GAAG,GAAGtiB,OAAtD,EAA+DsiB,GAA/D;MACAzE,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGkkB,OAAZ,EAAqBhkB,CAAC,GAAGikB,OAAzB,EAAkCC,YAAlC,EAAgDI,GAAhD,EAAqDA,GAAG,GAAGtiB,OAA3D;MACA6d,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGmkB,OAAZ,EAAqBjkB,CAAC,GAAGgkB,OAAzB,EAAkCE,YAAlC,EAAgDI,GAAG,GAAGtiB,OAAtD,EAA+DsiB,GAAG,GAAG7iB,EAArE;MACAoe,GAAG,CAAC+E,SAAJ;MACA;;IACF,KAAK,MAAL;MACE,IAAI,CAACR,QAAL,EAAe;QACbhjB,IAAI,GAAGM,IAAI,CAACqjB,OAAL,GAAeV,MAAtB;QACA5B,KAAK,GAAG/S,CAAC,GAAGA,CAAC,GAAG,CAAP,GAAWtO,IAApB;QACAye,GAAG,CAACmF,IAAJ,CAASllB,CAAC,GAAG2iB,KAAb,EAAoBziB,CAAC,GAAGoB,IAAxB,EAA8B,IAAIqhB,KAAlC,EAAyC,IAAIrhB,IAA7C;QACA;MACD;;MACDkjB,GAAG,IAAIriB,UAAP;;IACF,KAAK,SAAL;MACE+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA1B;MACAJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA1B;MACAxE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAnE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAnE,GAAG,CAAC+E,SAAJ;MACA;;IACF,KAAK,UAAL;MACEN,GAAG,IAAIriB,UAAP;;IACF,KAAK,OAAL;MACE+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA1B;MACAJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA1B;MACAxE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAnE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACA;;IACF,KAAK,MAAL;MACEA,OAAO,GAAGtiB,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA1B;MACAJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA1B;MACAxE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAnE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAM,GAAG,IAAIriB,UAAP;MACA+hB,OAAO,GAAGtiB,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA1B;MACAJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA1B;MACAxE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACAnE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGmkB,OAAf,EAAwBjkB,CAAC,GAAGgkB,OAA5B;MACA;;IACF,KAAK,MAAL;MACEA,OAAO,GAAGtU,CAAC,GAAGA,CAAC,GAAG,CAAP,GAAWhO,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAAtC;MACAJ,OAAO,GAAGviB,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA1B;MACAxE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACApE,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGkkB,OAAf,EAAwBhkB,CAAC,GAAGikB,OAA5B;MACA;;IACF,KAAK,MAAL;MACEpE,GAAG,CAACgF,MAAJ,CAAW/kB,CAAX,EAAcE,CAAd;MACA6f,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAG4B,IAAI,CAAC6K,GAAL,CAAS+X,GAAT,IAAgBD,MAA/B,EAAuCrkB,CAAC,GAAG0B,IAAI,CAAC4J,GAAL,CAASgZ,GAAT,IAAgBD,MAA3D;MACA;EA/EF;;EAiFAxE,GAAG,CAACoF,IAAJ;;EACA,IAAIrmB,OAAO,CAACsmB,WAAR,GAAsB,CAA1B,EAA6B;IAC3BrF,GAAG,CAACsF,MAAJ;EACD;AACF;;AACD,SAASC,cAAT,CAAwBC,KAAxB,EAA+BC,IAA/B,EAAqCC,MAArC,EAA6C;EAC3CA,MAAM,GAAGA,MAAM,IAAI,GAAnB;EACA,OAAO,CAACD,IAAD,IAAUD,KAAK,IAAIA,KAAK,CAACvlB,CAAN,GAAUwlB,IAAI,CAAChc,IAAL,GAAYic,MAA/B,IAAyCF,KAAK,CAACvlB,CAAN,GAAUwlB,IAAI,CAAC/b,KAAL,GAAagc,MAAhE,IACjBF,KAAK,CAACrlB,CAAN,GAAUslB,IAAI,CAACE,GAAL,GAAWD,MADJ,IACcF,KAAK,CAACrlB,CAAN,GAAUslB,IAAI,CAACG,MAAL,GAAcF,MADvD;AAED;;AACD,SAASG,QAAT,CAAkB7F,GAAlB,EAAuByF,IAAvB,EAA6B;EAC3BzF,GAAG,CAACiD,IAAJ;EACAjD,GAAG,CAAC4E,SAAJ;EACA5E,GAAG,CAACmF,IAAJ,CAASM,IAAI,CAAChc,IAAd,EAAoBgc,IAAI,CAACE,GAAzB,EAA8BF,IAAI,CAAC/b,KAAL,GAAa+b,IAAI,CAAChc,IAAhD,EAAsDgc,IAAI,CAACG,MAAL,GAAcH,IAAI,CAACE,GAAzE;EACA3F,GAAG,CAAC8F,IAAJ;AACD;;AACD,SAASC,UAAT,CAAoB/F,GAApB,EAAyB;EACvBA,GAAG,CAACqD,OAAJ;AACD;;AACD,SAAS2C,cAAT,CAAwBhG,GAAxB,EAA6BtgB,QAA7B,EAAuCnB,MAAvC,EAA+C0nB,IAA/C,EAAqD5F,IAArD,EAA2D;EACzD,IAAI,CAAC3gB,QAAL,EAAe;IACb,OAAOsgB,GAAG,CAACiF,MAAJ,CAAW1mB,MAAM,CAAC0B,CAAlB,EAAqB1B,MAAM,CAAC4B,CAA5B,CAAP;EACD;;EACD,IAAIkgB,IAAI,KAAK,QAAb,EAAuB;IACrB,MAAM6F,QAAQ,GAAG,CAACxmB,QAAQ,CAACO,CAAT,GAAa1B,MAAM,CAAC0B,CAArB,IAA0B,GAA3C;IACA+f,GAAG,CAACiF,MAAJ,CAAWiB,QAAX,EAAqBxmB,QAAQ,CAACS,CAA9B;IACA6f,GAAG,CAACiF,MAAJ,CAAWiB,QAAX,EAAqB3nB,MAAM,CAAC4B,CAA5B;EACD,CAJD,MAIO,IAAIkgB,IAAI,KAAK,OAAT,KAAqB,CAAC,CAAC4F,IAA3B,EAAiC;IACtCjG,GAAG,CAACiF,MAAJ,CAAWvlB,QAAQ,CAACO,CAApB,EAAuB1B,MAAM,CAAC4B,CAA9B;EACD,CAFM,MAEA;IACL6f,GAAG,CAACiF,MAAJ,CAAW1mB,MAAM,CAAC0B,CAAlB,EAAqBP,QAAQ,CAACS,CAA9B;EACD;;EACD6f,GAAG,CAACiF,MAAJ,CAAW1mB,MAAM,CAAC0B,CAAlB,EAAqB1B,MAAM,CAAC4B,CAA5B;AACD;;AACD,SAASgmB,cAAT,CAAwBnG,GAAxB,EAA6BtgB,QAA7B,EAAuCnB,MAAvC,EAA+C0nB,IAA/C,EAAqD;EACnD,IAAI,CAACvmB,QAAL,EAAe;IACb,OAAOsgB,GAAG,CAACiF,MAAJ,CAAW1mB,MAAM,CAAC0B,CAAlB,EAAqB1B,MAAM,CAAC4B,CAA5B,CAAP;EACD;;EACD6f,GAAG,CAACoG,aAAJ,CACEH,IAAI,GAAGvmB,QAAQ,CAAC2mB,IAAZ,GAAmB3mB,QAAQ,CAAC4mB,IADlC,EAEEL,IAAI,GAAGvmB,QAAQ,CAAC6mB,IAAZ,GAAmB7mB,QAAQ,CAAC8mB,IAFlC,EAGEP,IAAI,GAAG1nB,MAAM,CAAC+nB,IAAV,GAAiB/nB,MAAM,CAAC8nB,IAH9B,EAIEJ,IAAI,GAAG1nB,MAAM,CAACioB,IAAV,GAAiBjoB,MAAM,CAACgoB,IAJ9B,EAKEhoB,MAAM,CAAC0B,CALT,EAME1B,MAAM,CAAC4B,CANT;AAOD;;AACD,SAASsmB,UAAT,CAAoBzG,GAApB,EAAyB0G,IAAzB,EAA+BzmB,CAA/B,EAAkCE,CAAlC,EAAqCuf,IAArC,EAA2CiH,IAAI,GAAG,EAAlD,EAAsD;EACpD,MAAMC,KAAK,GAAGhrB,OAAO,CAAC8qB,IAAD,CAAP,GAAgBA,IAAhB,GAAuB,CAACA,IAAD,CAArC;EACA,MAAMpB,MAAM,GAAGqB,IAAI,CAACE,WAAL,GAAmB,CAAnB,IAAwBF,IAAI,CAACG,WAAL,KAAqB,EAA5D;EACA,IAAItpB,CAAJ,EAAOupB,IAAP;EACA/G,GAAG,CAACiD,IAAJ;EACAjD,GAAG,CAACN,IAAJ,GAAWA,IAAI,CAAC+C,MAAhB;EACAuE,aAAa,CAAChH,GAAD,EAAM2G,IAAN,CAAb;;EACA,KAAKnpB,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGopB,KAAK,CAACjpB,MAAtB,EAA8B,EAAEH,CAAhC,EAAmC;IACjCupB,IAAI,GAAGH,KAAK,CAACppB,CAAD,CAAZ;;IACA,IAAI8nB,MAAJ,EAAY;MACV,IAAIqB,IAAI,CAACG,WAAT,EAAsB;QACpB9G,GAAG,CAACiH,WAAJ,GAAkBN,IAAI,CAACG,WAAvB;MACD;;MACD,IAAI,CAACprB,aAAa,CAACirB,IAAI,CAACE,WAAN,CAAlB,EAAsC;QACpC7G,GAAG,CAACkH,SAAJ,GAAgBP,IAAI,CAACE,WAArB;MACD;;MACD7G,GAAG,CAACmH,UAAJ,CAAeJ,IAAf,EAAqB9mB,CAArB,EAAwBE,CAAxB,EAA2BwmB,IAAI,CAACS,QAAhC;IACD;;IACDpH,GAAG,CAACqH,QAAJ,CAAaN,IAAb,EAAmB9mB,CAAnB,EAAsBE,CAAtB,EAAyBwmB,IAAI,CAACS,QAA9B;IACAE,YAAY,CAACtH,GAAD,EAAM/f,CAAN,EAASE,CAAT,EAAY4mB,IAAZ,EAAkBJ,IAAlB,CAAZ;IACAxmB,CAAC,IAAIuf,IAAI,CAACG,UAAV;EACD;;EACDG,GAAG,CAACqD,OAAJ;AACD;;AACD,SAAS2D,aAAT,CAAuBhH,GAAvB,EAA4B2G,IAA5B,EAAkC;EAChC,IAAIA,IAAI,CAACY,WAAT,EAAsB;IACpBvH,GAAG,CAAC0E,SAAJ,CAAciC,IAAI,CAACY,WAAL,CAAiB,CAAjB,CAAd,EAAmCZ,IAAI,CAACY,WAAL,CAAiB,CAAjB,CAAnC;EACD;;EACD,IAAI,CAAC7rB,aAAa,CAACirB,IAAI,CAACpC,QAAN,CAAlB,EAAmC;IACjCvE,GAAG,CAACtP,MAAJ,CAAWiW,IAAI,CAACpC,QAAhB;EACD;;EACD,IAAIoC,IAAI,CAACrJ,KAAT,EAAgB;IACd0C,GAAG,CAACwH,SAAJ,GAAgBb,IAAI,CAACrJ,KAArB;EACD;;EACD,IAAIqJ,IAAI,CAACc,SAAT,EAAoB;IAClBzH,GAAG,CAACyH,SAAJ,GAAgBd,IAAI,CAACc,SAArB;EACD;;EACD,IAAId,IAAI,CAACe,YAAT,EAAuB;IACrB1H,GAAG,CAAC0H,YAAJ,GAAmBf,IAAI,CAACe,YAAxB;EACD;AACF;;AACD,SAASJ,YAAT,CAAsBtH,GAAtB,EAA2B/f,CAA3B,EAA8BE,CAA9B,EAAiC4mB,IAAjC,EAAuCJ,IAAvC,EAA6C;EAC3C,IAAIA,IAAI,CAACgB,aAAL,IAAsBhB,IAAI,CAACiB,SAA/B,EAA0C;IACxC,MAAMC,OAAO,GAAG7H,GAAG,CAAC2C,WAAJ,CAAgBoE,IAAhB,CAAhB;IACA,MAAMtd,IAAI,GAAGxJ,CAAC,GAAG4nB,OAAO,CAACC,qBAAzB;IACA,MAAMpe,KAAK,GAAGzJ,CAAC,GAAG4nB,OAAO,CAACE,sBAA1B;IACA,MAAMpC,GAAG,GAAGxlB,CAAC,GAAG0nB,OAAO,CAACG,uBAAxB;IACA,MAAMpC,MAAM,GAAGzlB,CAAC,GAAG0nB,OAAO,CAACI,wBAA3B;IACA,MAAMC,WAAW,GAAGvB,IAAI,CAACgB,aAAL,GAAqB,CAAChC,GAAG,GAAGC,MAAP,IAAiB,CAAtC,GAA0CA,MAA9D;IACA5F,GAAG,CAACiH,WAAJ,GAAkBjH,GAAG,CAACwH,SAAtB;IACAxH,GAAG,CAAC4E,SAAJ;IACA5E,GAAG,CAACkH,SAAJ,GAAgBP,IAAI,CAACwB,eAAL,IAAwB,CAAxC;IACAnI,GAAG,CAACgF,MAAJ,CAAWvb,IAAX,EAAiBye,WAAjB;IACAlI,GAAG,CAACiF,MAAJ,CAAWvb,KAAX,EAAkBwe,WAAlB;IACAlI,GAAG,CAACsF,MAAJ;EACD;AACF;;AACD,SAAS8C,kBAAT,CAA4BpI,GAA5B,EAAiCmF,IAAjC,EAAuC;EACrC,MAAM;IAACllB,CAAD;IAAIE,CAAJ;IAAO0P,CAAP;IAAU7B,CAAV;IAAawW;EAAb,IAAuBW,IAA7B;EACAnF,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGukB,MAAM,CAAC6D,OAAnB,EAA4BloB,CAAC,GAAGqkB,MAAM,CAAC6D,OAAvC,EAAgD7D,MAAM,CAAC6D,OAAvD,EAAgE,CAAClmB,OAAjE,EAA0EP,EAA1E,EAA8E,IAA9E;EACAoe,GAAG,CAACiF,MAAJ,CAAWhlB,CAAX,EAAcE,CAAC,GAAG6N,CAAJ,GAAQwW,MAAM,CAAC8D,UAA7B;EACAtI,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAGukB,MAAM,CAAC8D,UAAnB,EAA+BnoB,CAAC,GAAG6N,CAAJ,GAAQwW,MAAM,CAAC8D,UAA9C,EAA0D9D,MAAM,CAAC8D,UAAjE,EAA6E1mB,EAA7E,EAAiFO,OAAjF,EAA0F,IAA1F;EACA6d,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAG4P,CAAJ,GAAQ2U,MAAM,CAAC+D,WAA1B,EAAuCpoB,CAAC,GAAG6N,CAA3C;EACAgS,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAG4P,CAAJ,GAAQ2U,MAAM,CAAC+D,WAAvB,EAAoCpoB,CAAC,GAAG6N,CAAJ,GAAQwW,MAAM,CAAC+D,WAAnD,EAAgE/D,MAAM,CAAC+D,WAAvE,EAAoFpmB,OAApF,EAA6F,CAA7F,EAAgG,IAAhG;EACA6d,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAG4P,CAAf,EAAkB1P,CAAC,GAAGqkB,MAAM,CAACgE,QAA7B;EACAxI,GAAG,CAAC8E,GAAJ,CAAQ7kB,CAAC,GAAG4P,CAAJ,GAAQ2U,MAAM,CAACgE,QAAvB,EAAiCroB,CAAC,GAAGqkB,MAAM,CAACgE,QAA5C,EAAsDhE,MAAM,CAACgE,QAA7D,EAAuE,CAAvE,EAA0E,CAACrmB,OAA3E,EAAoF,IAApF;EACA6d,GAAG,CAACiF,MAAJ,CAAWhlB,CAAC,GAAGukB,MAAM,CAAC6D,OAAtB,EAA+BloB,CAA/B;AACD;;AAED,MAAMsoB,WAAW,GAAG,IAAIC,MAAJ,CAAW,sCAAX,CAApB;AACA,MAAMC,UAAU,GAAG,IAAID,MAAJ,CAAW,uEAAX,CAAnB;;AACA,SAASE,YAAT,CAAsBjtB,KAAtB,EAA6B4F,IAA7B,EAAmC;EACjC,MAAMsnB,OAAO,GAAG,CAAC,KAAKltB,KAAN,EAAamtB,KAAb,CAAmBL,WAAnB,CAAhB;;EACA,IAAI,CAACI,OAAD,IAAYA,OAAO,CAAC,CAAD,CAAP,KAAe,QAA/B,EAAyC;IACvC,OAAOtnB,IAAI,GAAG,GAAd;EACD;;EACD5F,KAAK,GAAG,CAACktB,OAAO,CAAC,CAAD,CAAhB;;EACA,QAAQA,OAAO,CAAC,CAAD,CAAf;IACA,KAAK,IAAL;MACE,OAAOltB,KAAP;;IACF,KAAK,GAAL;MACEA,KAAK,IAAI,GAAT;MACA;EALF;;EAOA,OAAO4F,IAAI,GAAG5F,KAAd;AACD;;AACD,MAAMotB,YAAY,GAAG/oB,CAAC,IAAI,CAACA,CAAD,IAAM,CAAhC;;AACA,SAASgpB,iBAAT,CAA2BrtB,KAA3B,EAAkCstB,KAAlC,EAAyC;EACvC,MAAM3Z,GAAG,GAAG,EAAZ;EACA,MAAM4Z,QAAQ,GAAG9sB,QAAQ,CAAC6sB,KAAD,CAAzB;EACA,MAAMvrB,IAAI,GAAGwrB,QAAQ,GAAGntB,MAAM,CAAC2B,IAAP,CAAYurB,KAAZ,CAAH,GAAwBA,KAA7C;EACA,MAAME,IAAI,GAAG/sB,QAAQ,CAACT,KAAD,CAAR,GACTutB,QAAQ,GACNE,IAAI,IAAI1sB,cAAc,CAACf,KAAK,CAACytB,IAAD,CAAN,EAAcztB,KAAK,CAACstB,KAAK,CAACG,IAAD,CAAN,CAAnB,CADhB,GAENA,IAAI,IAAIztB,KAAK,CAACytB,IAAD,CAHN,GAIT,MAAMztB,KAJV;;EAKA,KAAK,MAAMytB,IAAX,IAAmB1rB,IAAnB,EAAyB;IACvB4R,GAAG,CAAC8Z,IAAD,CAAH,GAAYL,YAAY,CAACI,IAAI,CAACC,IAAD,CAAL,CAAxB;EACD;;EACD,OAAO9Z,GAAP;AACD;;AACD,SAAS+Z,MAAT,CAAgB1tB,KAAhB,EAAuB;EACrB,OAAOqtB,iBAAiB,CAACrtB,KAAD,EAAQ;IAACgqB,GAAG,EAAE,GAAN;IAAWjc,KAAK,EAAE,GAAlB;IAAuBkc,MAAM,EAAE,GAA/B;IAAoCnc,IAAI,EAAE;EAA1C,CAAR,CAAxB;AACD;;AACD,SAAS6f,aAAT,CAAuB3tB,KAAvB,EAA8B;EAC5B,OAAOqtB,iBAAiB,CAACrtB,KAAD,EAAQ,CAAC,SAAD,EAAY,UAAZ,EAAwB,YAAxB,EAAsC,aAAtC,CAAR,CAAxB;AACD;;AACD,SAAS4tB,SAAT,CAAmB5tB,KAAnB,EAA0B;EACxB,MAAM0E,GAAG,GAAGgpB,MAAM,CAAC1tB,KAAD,CAAlB;EACA0E,GAAG,CAACuiB,KAAJ,GAAYviB,GAAG,CAACoJ,IAAJ,GAAWpJ,GAAG,CAACqJ,KAA3B;EACArJ,GAAG,CAAC2jB,MAAJ,GAAa3jB,GAAG,CAACslB,GAAJ,GAAUtlB,GAAG,CAACulB,MAA3B;EACA,OAAOvlB,GAAP;AACD;;AACD,SAASmpB,MAAT,CAAgBzqB,OAAhB,EAAyB0qB,QAAzB,EAAmC;EACjC1qB,OAAO,GAAGA,OAAO,IAAI,EAArB;EACA0qB,QAAQ,GAAGA,QAAQ,IAAI1H,QAAQ,CAACrC,IAAhC;EACA,IAAIne,IAAI,GAAG7E,cAAc,CAACqC,OAAO,CAACwC,IAAT,EAAekoB,QAAQ,CAACloB,IAAxB,CAAzB;;EACA,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;IAC5BA,IAAI,GAAGsa,QAAQ,CAACta,IAAD,EAAO,EAAP,CAAf;EACD;;EACD,IAAIqe,KAAK,GAAGljB,cAAc,CAACqC,OAAO,CAAC6gB,KAAT,EAAgB6J,QAAQ,CAAC7J,KAAzB,CAA1B;;EACA,IAAIA,KAAK,IAAI,CAAC,CAAC,KAAKA,KAAN,EAAakJ,KAAb,CAAmBH,UAAnB,CAAd,EAA8C;IAC5C9oB,OAAO,CAACC,IAAR,CAAa,oCAAoC8f,KAApC,GAA4C,GAAzD;IACAA,KAAK,GAAG,EAAR;EACD;;EACD,MAAMF,IAAI,GAAG;IACXC,MAAM,EAAEjjB,cAAc,CAACqC,OAAO,CAAC4gB,MAAT,EAAiB8J,QAAQ,CAAC9J,MAA1B,CADX;IAEXE,UAAU,EAAE+I,YAAY,CAAClsB,cAAc,CAACqC,OAAO,CAAC8gB,UAAT,EAAqB4J,QAAQ,CAAC5J,UAA9B,CAAf,EAA0Dte,IAA1D,CAFb;IAGXA,IAHW;IAIXqe,KAJW;IAKXrC,MAAM,EAAE7gB,cAAc,CAACqC,OAAO,CAACwe,MAAT,EAAiBkM,QAAQ,CAAClM,MAA1B,CALX;IAMXkF,MAAM,EAAE;EANG,CAAb;EAQA/C,IAAI,CAAC+C,MAAL,GAAcL,YAAY,CAAC1C,IAAD,CAA1B;EACA,OAAOA,IAAP;AACD;;AACD,SAASgK,OAAT,CAAiBC,MAAjB,EAAyBvK,OAAzB,EAAkCjhB,KAAlC,EAAyCyrB,IAAzC,EAA+C;EAC7C,IAAIC,SAAS,GAAG,IAAhB;EACA,IAAIrsB,CAAJ,EAAOO,IAAP,EAAapC,KAAb;;EACA,KAAK6B,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAG4rB,MAAM,CAAChsB,MAA1B,EAAkCH,CAAC,GAAGO,IAAtC,EAA4C,EAAEP,CAA9C,EAAiD;IAC/C7B,KAAK,GAAGguB,MAAM,CAACnsB,CAAD,CAAd;;IACA,IAAI7B,KAAK,KAAKiE,SAAd,EAAyB;MACvB;IACD;;IACD,IAAIwf,OAAO,KAAKxf,SAAZ,IAAyB,OAAOjE,KAAP,KAAiB,UAA9C,EAA0D;MACxDA,KAAK,GAAGA,KAAK,CAACyjB,OAAD,CAAb;MACAyK,SAAS,GAAG,KAAZ;IACD;;IACD,IAAI1rB,KAAK,KAAKyB,SAAV,IAAuBhE,OAAO,CAACD,KAAD,CAAlC,EAA2C;MACzCA,KAAK,GAAGA,KAAK,CAACwC,KAAK,GAAGxC,KAAK,CAACgC,MAAf,CAAb;MACAksB,SAAS,GAAG,KAAZ;IACD;;IACD,IAAIluB,KAAK,KAAKiE,SAAd,EAAyB;MACvB,IAAIgqB,IAAI,IAAI,CAACC,SAAb,EAAwB;QACtBD,IAAI,CAACC,SAAL,GAAiB,KAAjB;MACD;;MACD,OAAOluB,KAAP;IACD;EACF;AACF;;AACD,SAASmuB,SAAT,CAAmBC,MAAnB,EAA2BC,KAA3B,EAAkCC,WAAlC,EAA+C;EAC7C,MAAM;IAAChmB,GAAD;IAAMC;EAAN,IAAa6lB,MAAnB;EACA,MAAMG,MAAM,GAAGntB,WAAW,CAACitB,KAAD,EAAQ,CAAC9lB,GAAG,GAAGD,GAAP,IAAc,CAAtB,CAA1B;;EACA,MAAMkmB,QAAQ,GAAG,CAACxuB,KAAD,EAAQuM,GAAR,KAAgB+hB,WAAW,IAAItuB,KAAK,KAAK,CAAzB,GAA6B,CAA7B,GAAiCA,KAAK,GAAGuM,GAA1E;;EACA,OAAO;IACLjE,GAAG,EAAEkmB,QAAQ,CAAClmB,GAAD,EAAM,CAACpC,IAAI,CAAC8B,GAAL,CAASumB,MAAT,CAAP,CADR;IAELhmB,GAAG,EAAEimB,QAAQ,CAACjmB,GAAD,EAAMgmB,MAAN;EAFR,CAAP;AAID;;AACD,SAASE,aAAT,CAAuBC,aAAvB,EAAsCjL,OAAtC,EAA+C;EAC7C,OAAOrjB,MAAM,CAACsP,MAAP,CAActP,MAAM,CAACyC,MAAP,CAAc6rB,aAAd,CAAd,EAA4CjL,OAA5C,CAAP;AACD;;AAED,SAASkL,eAAT,CAAyBC,MAAzB,EAAiCC,QAAQ,GAAG,CAAC,EAAD,CAA5C,EAAkDC,UAAU,GAAGF,MAA/D,EAAuEd,QAAvE,EAAiFiB,SAAS,GAAG,MAAMH,MAAM,CAAC,CAAD,CAAzG,EAA8G;EAC5G,IAAI,CAACrpB,OAAO,CAACuoB,QAAD,CAAZ,EAAwB;IACtBA,QAAQ,GAAGkB,QAAQ,CAAC,WAAD,EAAcJ,MAAd,CAAnB;EACD;;EACD,MAAMxH,KAAK,GAAG;IACZ,CAAC6H,MAAM,CAACC,WAAR,GAAsB,QADV;IAEZC,UAAU,EAAE,IAFA;IAGZC,OAAO,EAAER,MAHG;IAIZS,WAAW,EAAEP,UAJD;IAKZtI,SAAS,EAAEsH,QALC;IAMZwB,UAAU,EAAEP,SANA;IAOZtJ,QAAQ,EAAG3hB,KAAD,IAAW6qB,eAAe,CAAC,CAAC7qB,KAAD,EAAQ,GAAG8qB,MAAX,CAAD,EAAqBC,QAArB,EAA+BC,UAA/B,EAA2ChB,QAA3C;EAPxB,CAAd;EASA,OAAO,IAAIyB,KAAJ,CAAUnI,KAAV,EAAiB;IACtBoI,cAAc,CAAC5sB,MAAD,EAAS6qB,IAAT,EAAe;MAC3B,OAAO7qB,MAAM,CAAC6qB,IAAD,CAAb;MACA,OAAO7qB,MAAM,CAAC6sB,KAAd;MACA,OAAOb,MAAM,CAAC,CAAD,CAAN,CAAUnB,IAAV,CAAP;MACA,OAAO,IAAP;IACD,CANqB;;IAOtBjI,GAAG,CAAC5iB,MAAD,EAAS6qB,IAAT,EAAe;MAChB,OAAOiC,OAAO,CAAC9sB,MAAD,EAAS6qB,IAAT,EACZ,MAAMkC,oBAAoB,CAAClC,IAAD,EAAOoB,QAAP,EAAiBD,MAAjB,EAAyBhsB,MAAzB,CADd,CAAd;IAED,CAVqB;;IAWtBgtB,wBAAwB,CAAChtB,MAAD,EAAS6qB,IAAT,EAAe;MACrC,OAAOoC,OAAO,CAACD,wBAAR,CAAiChtB,MAAM,CAACwsB,OAAP,CAAe,CAAf,CAAjC,EAAoD3B,IAApD,CAAP;IACD,CAbqB;;IActBqC,cAAc,GAAG;MACf,OAAOD,OAAO,CAACC,cAAR,CAAuBlB,MAAM,CAAC,CAAD,CAA7B,CAAP;IACD,CAhBqB;;IAiBtB9oB,GAAG,CAAClD,MAAD,EAAS6qB,IAAT,EAAe;MAChB,OAAOsC,oBAAoB,CAACntB,MAAD,CAApB,CAA6BotB,QAA7B,CAAsCvC,IAAtC,CAAP;IACD,CAnBqB;;IAoBtBwC,OAAO,CAACrtB,MAAD,EAAS;MACd,OAAOmtB,oBAAoB,CAACntB,MAAD,CAA3B;IACD,CAtBqB;;IAuBtByJ,GAAG,CAACzJ,MAAD,EAAS6qB,IAAT,EAAeztB,KAAf,EAAsB;MACvB,MAAMkwB,OAAO,GAAGttB,MAAM,CAACutB,QAAP,KAAoBvtB,MAAM,CAACutB,QAAP,GAAkBpB,SAAS,EAA/C,CAAhB;MACAnsB,MAAM,CAAC6qB,IAAD,CAAN,GAAeyC,OAAO,CAACzC,IAAD,CAAP,GAAgBztB,KAA/B;MACA,OAAO4C,MAAM,CAAC6sB,KAAd;MACA,OAAO,IAAP;IACD;;EA5BqB,CAAjB,CAAP;AA8BD;;AACD,SAASW,cAAT,CAAwBC,KAAxB,EAA+B5M,OAA/B,EAAwC6M,QAAxC,EAAkDC,kBAAlD,EAAsE;EACpE,MAAMnJ,KAAK,GAAG;IACZ+H,UAAU,EAAE,KADA;IAEZqB,MAAM,EAAEH,KAFI;IAGZI,QAAQ,EAAEhN,OAHE;IAIZiN,SAAS,EAAEJ,QAJC;IAKZK,MAAM,EAAE,IAAIrkB,GAAJ,EALI;IAMZ6W,YAAY,EAAEA,YAAY,CAACkN,KAAD,EAAQE,kBAAR,CANd;IAOZK,UAAU,EAAGvM,GAAD,IAAS+L,cAAc,CAACC,KAAD,EAAQhM,GAAR,EAAaiM,QAAb,EAAuBC,kBAAvB,CAPvB;IAQZ9K,QAAQ,EAAG3hB,KAAD,IAAWssB,cAAc,CAACC,KAAK,CAAC5K,QAAN,CAAe3hB,KAAf,CAAD,EAAwB2f,OAAxB,EAAiC6M,QAAjC,EAA2CC,kBAA3C;EARvB,CAAd;EAUA,OAAO,IAAIhB,KAAJ,CAAUnI,KAAV,EAAiB;IACtBoI,cAAc,CAAC5sB,MAAD,EAAS6qB,IAAT,EAAe;MAC3B,OAAO7qB,MAAM,CAAC6qB,IAAD,CAAb;MACA,OAAO4C,KAAK,CAAC5C,IAAD,CAAZ;MACA,OAAO,IAAP;IACD,CALqB;;IAMtBjI,GAAG,CAAC5iB,MAAD,EAAS6qB,IAAT,EAAeoD,QAAf,EAAyB;MAC1B,OAAOnB,OAAO,CAAC9sB,MAAD,EAAS6qB,IAAT,EACZ,MAAMqD,mBAAmB,CAACluB,MAAD,EAAS6qB,IAAT,EAAeoD,QAAf,CADb,CAAd;IAED,CATqB;;IAUtBjB,wBAAwB,CAAChtB,MAAD,EAAS6qB,IAAT,EAAe;MACrC,OAAO7qB,MAAM,CAACugB,YAAP,CAAoB4N,OAApB,GACHlB,OAAO,CAAC/pB,GAAR,CAAYuqB,KAAZ,EAAmB5C,IAAnB,IAA2B;QAAC/hB,UAAU,EAAE,IAAb;QAAmBD,YAAY,EAAE;MAAjC,CAA3B,GAAoExH,SADjE,GAEH4rB,OAAO,CAACD,wBAAR,CAAiCS,KAAjC,EAAwC5C,IAAxC,CAFJ;IAGD,CAdqB;;IAetBqC,cAAc,GAAG;MACf,OAAOD,OAAO,CAACC,cAAR,CAAuBO,KAAvB,CAAP;IACD,CAjBqB;;IAkBtBvqB,GAAG,CAAClD,MAAD,EAAS6qB,IAAT,EAAe;MAChB,OAAOoC,OAAO,CAAC/pB,GAAR,CAAYuqB,KAAZ,EAAmB5C,IAAnB,CAAP;IACD,CApBqB;;IAqBtBwC,OAAO,GAAG;MACR,OAAOJ,OAAO,CAACI,OAAR,CAAgBI,KAAhB,CAAP;IACD,CAvBqB;;IAwBtBhkB,GAAG,CAACzJ,MAAD,EAAS6qB,IAAT,EAAeztB,KAAf,EAAsB;MACvBqwB,KAAK,CAAC5C,IAAD,CAAL,GAAcztB,KAAd;MACA,OAAO4C,MAAM,CAAC6qB,IAAD,CAAb;MACA,OAAO,IAAP;IACD;;EA5BqB,CAAjB,CAAP;AA8BD;;AACD,SAAStK,YAAT,CAAsBkN,KAAtB,EAA6BjK,QAAQ,GAAG;EAAC4K,UAAU,EAAE,IAAb;EAAmBC,SAAS,EAAE;AAA9B,CAAxC,EAA6E;EAC3E,MAAM;IAAC5K,WAAW,GAAGD,QAAQ,CAAC4K,UAAxB;IAAoCzK,UAAU,GAAGH,QAAQ,CAAC6K,SAA1D;IAAqEC,QAAQ,GAAG9K,QAAQ,CAAC2K;EAAzF,IAAoGV,KAA1G;EACA,OAAO;IACLU,OAAO,EAAEG,QADJ;IAELF,UAAU,EAAE3K,WAFP;IAGL4K,SAAS,EAAE1K,UAHN;IAIL4K,YAAY,EAAE3rB,UAAU,CAAC6gB,WAAD,CAAV,GAA0BA,WAA1B,GAAwC,MAAMA,WAJvD;IAKL+K,WAAW,EAAE5rB,UAAU,CAAC+gB,UAAD,CAAV,GAAyBA,UAAzB,GAAsC,MAAMA;EALpD,CAAP;AAOD;;AACD,MAAM8K,OAAO,GAAG,CAACC,MAAD,EAAS3L,IAAT,KAAkB2L,MAAM,GAAGA,MAAM,GAAGnsB,WAAW,CAACwgB,IAAD,CAAvB,GAAgCA,IAAxE;;AACA,MAAM4L,gBAAgB,GAAG,CAAC9D,IAAD,EAAOztB,KAAP,KAAiBS,QAAQ,CAACT,KAAD,CAAR,IAAmBytB,IAAI,KAAK,UAA5B,KACvCrtB,MAAM,CAAC0vB,cAAP,CAAsB9vB,KAAtB,MAAiC,IAAjC,IAAyCA,KAAK,CAACshB,WAAN,KAAsBlhB,MADxB,CAA1C;;AAEA,SAASsvB,OAAT,CAAiB9sB,MAAjB,EAAyB6qB,IAAzB,EAA+BM,OAA/B,EAAwC;EACtC,IAAI3tB,MAAM,CAACC,SAAP,CAAiBuD,cAAjB,CAAgCrD,IAAhC,CAAqCqC,MAArC,EAA6C6qB,IAA7C,CAAJ,EAAwD;IACtD,OAAO7qB,MAAM,CAAC6qB,IAAD,CAAb;EACD;;EACD,MAAMztB,KAAK,GAAG+tB,OAAO,EAArB;EACAnrB,MAAM,CAAC6qB,IAAD,CAAN,GAAeztB,KAAf;EACA,OAAOA,KAAP;AACD;;AACD,SAAS8wB,mBAAT,CAA6BluB,MAA7B,EAAqC6qB,IAArC,EAA2CoD,QAA3C,EAAqD;EACnD,MAAM;IAACL,MAAD;IAASC,QAAT;IAAmBC,SAAnB;IAA8BvN,YAAY,EAAEL;EAA5C,IAA2DlgB,MAAjE;EACA,IAAI5C,KAAK,GAAGwwB,MAAM,CAAC/C,IAAD,CAAlB;;EACA,IAAIjoB,UAAU,CAACxF,KAAD,CAAV,IAAqB8iB,WAAW,CAACqO,YAAZ,CAAyB1D,IAAzB,CAAzB,EAAyD;IACvDztB,KAAK,GAAGwxB,kBAAkB,CAAC/D,IAAD,EAAOztB,KAAP,EAAc4C,MAAd,EAAsBiuB,QAAtB,CAA1B;EACD;;EACD,IAAI5wB,OAAO,CAACD,KAAD,CAAP,IAAkBA,KAAK,CAACgC,MAA5B,EAAoC;IAClChC,KAAK,GAAGyxB,aAAa,CAAChE,IAAD,EAAOztB,KAAP,EAAc4C,MAAd,EAAsBkgB,WAAW,CAACsO,WAAlC,CAArB;EACD;;EACD,IAAIG,gBAAgB,CAAC9D,IAAD,EAAOztB,KAAP,CAApB,EAAmC;IACjCA,KAAK,GAAGowB,cAAc,CAACpwB,KAAD,EAAQywB,QAAR,EAAkBC,SAAS,IAAIA,SAAS,CAACjD,IAAD,CAAxC,EAAgD3K,WAAhD,CAAtB;EACD;;EACD,OAAO9iB,KAAP;AACD;;AACD,SAASwxB,kBAAT,CAA4B/D,IAA5B,EAAkCztB,KAAlC,EAAyC4C,MAAzC,EAAiDiuB,QAAjD,EAA2D;EACzD,MAAM;IAACL,MAAD;IAASC,QAAT;IAAmBC,SAAnB;IAA8BC;EAA9B,IAAwC/tB,MAA9C;;EACA,IAAI+tB,MAAM,CAAC7qB,GAAP,CAAW2nB,IAAX,CAAJ,EAAsB;IACpB,MAAM,IAAIiE,KAAJ,CAAU,yBAAyBxxB,KAAK,CAACsM,IAAN,CAAWmkB,MAAX,EAAmBgB,IAAnB,CAAwB,IAAxB,CAAzB,GAAyD,IAAzD,GAAgElE,IAA1E,CAAN;EACD;;EACDkD,MAAM,CAACpkB,GAAP,CAAWkhB,IAAX;;EACAztB,KAAK,GAAGA,KAAK,CAACywB,QAAD,EAAWC,SAAS,IAAIG,QAAxB,CAAb;;EACAF,MAAM,CAACiB,MAAP,CAAcnE,IAAd;;EACA,IAAI8D,gBAAgB,CAAC9D,IAAD,EAAOztB,KAAP,CAApB,EAAmC;IACjCA,KAAK,GAAG6xB,iBAAiB,CAACrB,MAAM,CAACpB,OAAR,EAAiBoB,MAAjB,EAAyB/C,IAAzB,EAA+BztB,KAA/B,CAAzB;EACD;;EACD,OAAOA,KAAP;AACD;;AACD,SAASyxB,aAAT,CAAuBhE,IAAvB,EAA6BztB,KAA7B,EAAoC4C,MAApC,EAA4CwuB,WAA5C,EAAyD;EACvD,MAAM;IAACZ,MAAD;IAASC,QAAT;IAAmBC,SAAnB;IAA8BvN,YAAY,EAAEL;EAA5C,IAA2DlgB,MAAjE;;EACA,IAAI2C,OAAO,CAACkrB,QAAQ,CAACjuB,KAAV,CAAP,IAA2B4uB,WAAW,CAAC3D,IAAD,CAA1C,EAAkD;IAChDztB,KAAK,GAAGA,KAAK,CAACywB,QAAQ,CAACjuB,KAAT,GAAiBxC,KAAK,CAACgC,MAAxB,CAAb;EACD,CAFD,MAEO,IAAIvB,QAAQ,CAACT,KAAK,CAAC,CAAD,CAAN,CAAZ,EAAwB;IAC7B,MAAM8xB,GAAG,GAAG9xB,KAAZ;;IACA,MAAM4uB,MAAM,GAAG4B,MAAM,CAACpB,OAAP,CAAe2C,MAAf,CAAsB/nB,CAAC,IAAIA,CAAC,KAAK8nB,GAAjC,CAAf;;IACA9xB,KAAK,GAAG,EAAR;;IACA,KAAK,MAAM6F,IAAX,IAAmBisB,GAAnB,EAAwB;MACtB,MAAMntB,QAAQ,GAAGktB,iBAAiB,CAACjD,MAAD,EAAS4B,MAAT,EAAiB/C,IAAjB,EAAuB5nB,IAAvB,CAAlC;MACA7F,KAAK,CAACkF,IAAN,CAAWkrB,cAAc,CAACzrB,QAAD,EAAW8rB,QAAX,EAAqBC,SAAS,IAAIA,SAAS,CAACjD,IAAD,CAA3C,EAAmD3K,WAAnD,CAAzB;IACD;EACF;;EACD,OAAO9iB,KAAP;AACD;;AACD,SAASgyB,eAAT,CAAyBlE,QAAzB,EAAmCL,IAAnC,EAAyCztB,KAAzC,EAAgD;EAC9C,OAAOwF,UAAU,CAACsoB,QAAD,CAAV,GAAuBA,QAAQ,CAACL,IAAD,EAAOztB,KAAP,CAA/B,GAA+C8tB,QAAtD;AACD;;AACD,MAAMmE,QAAQ,GAAG,CAAChvB,GAAD,EAAMivB,MAAN,KAAiBjvB,GAAG,KAAK,IAAR,GAAeivB,MAAf,GAC9B,OAAOjvB,GAAP,KAAe,QAAf,GAA0BwB,gBAAgB,CAACytB,MAAD,EAASjvB,GAAT,CAA1C,GAA0DgB,SAD9D;;AAEA,SAASkuB,SAAT,CAAmB9lB,GAAnB,EAAwB+lB,YAAxB,EAAsCnvB,GAAtC,EAA2CovB,cAA3C,EAA2DryB,KAA3D,EAAkE;EAChE,KAAK,MAAMkyB,MAAX,IAAqBE,YAArB,EAAmC;IACjC,MAAMtuB,KAAK,GAAGmuB,QAAQ,CAAChvB,GAAD,EAAMivB,MAAN,CAAtB;;IACA,IAAIpuB,KAAJ,EAAW;MACTuI,GAAG,CAACE,GAAJ,CAAQzI,KAAR;MACA,MAAMgqB,QAAQ,GAAGkE,eAAe,CAACluB,KAAK,CAAC0iB,SAAP,EAAkBvjB,GAAlB,EAAuBjD,KAAvB,CAAhC;;MACA,IAAIuF,OAAO,CAACuoB,QAAD,CAAP,IAAqBA,QAAQ,KAAK7qB,GAAlC,IAAyC6qB,QAAQ,KAAKuE,cAA1D,EAA0E;QACxE,OAAOvE,QAAP;MACD;IACF,CAND,MAMO,IAAIhqB,KAAK,KAAK,KAAV,IAAmByB,OAAO,CAAC8sB,cAAD,CAA1B,IAA8CpvB,GAAG,KAAKovB,cAA1D,EAA0E;MAC/E,OAAO,IAAP;IACD;EACF;;EACD,OAAO,KAAP;AACD;;AACD,SAASR,iBAAT,CAA2BO,YAA3B,EAAyCztB,QAAzC,EAAmD8oB,IAAnD,EAAyDztB,KAAzD,EAAgE;EAC9D,MAAM8uB,UAAU,GAAGnqB,QAAQ,CAAC0qB,WAA5B;EACA,MAAMvB,QAAQ,GAAGkE,eAAe,CAACrtB,QAAQ,CAAC6hB,SAAV,EAAqBiH,IAArB,EAA2BztB,KAA3B,CAAhC;EACA,MAAMsyB,SAAS,GAAG,CAAC,GAAGF,YAAJ,EAAkB,GAAGtD,UAArB,CAAlB;EACA,MAAMziB,GAAG,GAAG,IAAIC,GAAJ,EAAZ;EACAD,GAAG,CAACE,GAAJ,CAAQvM,KAAR;EACA,IAAIiD,GAAG,GAAGsvB,gBAAgB,CAAClmB,GAAD,EAAMimB,SAAN,EAAiB7E,IAAjB,EAAuBK,QAAQ,IAAIL,IAAnC,EAAyCztB,KAAzC,CAA1B;;EACA,IAAIiD,GAAG,KAAK,IAAZ,EAAkB;IAChB,OAAO,KAAP;EACD;;EACD,IAAIsC,OAAO,CAACuoB,QAAD,CAAP,IAAqBA,QAAQ,KAAKL,IAAtC,EAA4C;IAC1CxqB,GAAG,GAAGsvB,gBAAgB,CAAClmB,GAAD,EAAMimB,SAAN,EAAiBxE,QAAjB,EAA2B7qB,GAA3B,EAAgCjD,KAAhC,CAAtB;;IACA,IAAIiD,GAAG,KAAK,IAAZ,EAAkB;MAChB,OAAO,KAAP;IACD;EACF;;EACD,OAAO0rB,eAAe,CAACzuB,KAAK,CAACsM,IAAN,CAAWH,GAAX,CAAD,EAAkB,CAAC,EAAD,CAAlB,EAAwByiB,UAAxB,EAAoChB,QAApC,EACpB,MAAM0E,YAAY,CAAC7tB,QAAD,EAAW8oB,IAAX,EAAiBztB,KAAjB,CADE,CAAtB;AAED;;AACD,SAASuyB,gBAAT,CAA0BlmB,GAA1B,EAA+BimB,SAA/B,EAA0CrvB,GAA1C,EAA+C6qB,QAA/C,EAAyDjoB,IAAzD,EAA+D;EAC7D,OAAO5C,GAAP,EAAY;IACVA,GAAG,GAAGkvB,SAAS,CAAC9lB,GAAD,EAAMimB,SAAN,EAAiBrvB,GAAjB,EAAsB6qB,QAAtB,EAAgCjoB,IAAhC,CAAf;EACD;;EACD,OAAO5C,GAAP;AACD;;AACD,SAASuvB,YAAT,CAAsB7tB,QAAtB,EAAgC8oB,IAAhC,EAAsCztB,KAAtC,EAA6C;EAC3C,MAAMkyB,MAAM,GAAGvtB,QAAQ,CAAC2qB,UAAT,EAAf;;EACA,IAAI,EAAE7B,IAAI,IAAIyE,MAAV,CAAJ,EAAuB;IACrBA,MAAM,CAACzE,IAAD,CAAN,GAAe,EAAf;EACD;;EACD,MAAM7qB,MAAM,GAAGsvB,MAAM,CAACzE,IAAD,CAArB;;EACA,IAAIxtB,OAAO,CAAC2C,MAAD,CAAP,IAAmBnC,QAAQ,CAACT,KAAD,CAA/B,EAAwC;IACtC,OAAOA,KAAP;EACD;;EACD,OAAO4C,MAAP;AACD;;AACD,SAAS+sB,oBAAT,CAA8BlC,IAA9B,EAAoCoB,QAApC,EAA8CD,MAA9C,EAAsDyB,KAAtD,EAA6D;EAC3D,IAAIrwB,KAAJ;;EACA,KAAK,MAAMsxB,MAAX,IAAqBzC,QAArB,EAA+B;IAC7B7uB,KAAK,GAAGgvB,QAAQ,CAACqC,OAAO,CAACC,MAAD,EAAS7D,IAAT,CAAR,EAAwBmB,MAAxB,CAAhB;;IACA,IAAIrpB,OAAO,CAACvF,KAAD,CAAX,EAAoB;MAClB,OAAOuxB,gBAAgB,CAAC9D,IAAD,EAAOztB,KAAP,CAAhB,GACH6xB,iBAAiB,CAACjD,MAAD,EAASyB,KAAT,EAAgB5C,IAAhB,EAAsBztB,KAAtB,CADd,GAEHA,KAFJ;IAGD;EACF;AACF;;AACD,SAASgvB,QAAT,CAAkB/rB,GAAlB,EAAuB2rB,MAAvB,EAA+B;EAC7B,KAAK,MAAM9qB,KAAX,IAAoB8qB,MAApB,EAA4B;IAC1B,IAAI,CAAC9qB,KAAL,EAAY;MACV;IACD;;IACD,MAAM9D,KAAK,GAAG8D,KAAK,CAACb,GAAD,CAAnB;;IACA,IAAIsC,OAAO,CAACvF,KAAD,CAAX,EAAoB;MAClB,OAAOA,KAAP;IACD;EACF;AACF;;AACD,SAAS+vB,oBAAT,CAA8BntB,MAA9B,EAAsC;EACpC,IAAIb,IAAI,GAAGa,MAAM,CAAC6sB,KAAlB;;EACA,IAAI,CAAC1tB,IAAL,EAAW;IACTA,IAAI,GAAGa,MAAM,CAAC6sB,KAAP,GAAegD,wBAAwB,CAAC7vB,MAAM,CAACwsB,OAAR,CAA9C;EACD;;EACD,OAAOrtB,IAAP;AACD;;AACD,SAAS0wB,wBAAT,CAAkC7D,MAAlC,EAA0C;EACxC,MAAMviB,GAAG,GAAG,IAAIC,GAAJ,EAAZ;;EACA,KAAK,MAAMxI,KAAX,IAAoB8qB,MAApB,EAA4B;IAC1B,KAAK,MAAM3rB,GAAX,IAAkB7C,MAAM,CAAC2B,IAAP,CAAY+B,KAAZ,EAAmBiuB,MAAnB,CAA0BhvB,CAAC,IAAI,CAACA,CAAC,CAACujB,UAAF,CAAa,GAAb,CAAhC,CAAlB,EAAsE;MACpEja,GAAG,CAACE,GAAJ,CAAQtJ,GAAR;IACD;EACF;;EACD,OAAO/C,KAAK,CAACsM,IAAN,CAAWH,GAAX,CAAP;AACD;;AACD,SAASqmB,2BAAT,CAAqCvkB,IAArC,EAA2CwY,IAA3C,EAAiD9c,KAAjD,EAAwD0E,KAAxD,EAA+D;EAC7D,MAAM;IAACE;EAAD,IAAWN,IAAjB;EACA,MAAM;IAAClL,GAAG,GAAG;EAAP,IAAc,KAAK0vB,QAAzB;EACA,MAAMC,MAAM,GAAG,IAAI1yB,KAAJ,CAAUqO,KAAV,CAAf;EACA,IAAI1M,CAAJ,EAAOO,IAAP,EAAaI,KAAb,EAAoBqD,IAApB;;EACA,KAAKhE,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGmM,KAAnB,EAA0B1M,CAAC,GAAGO,IAA9B,EAAoC,EAAEP,CAAtC,EAAyC;IACvCW,KAAK,GAAGX,CAAC,GAAGgI,KAAZ;IACAhE,IAAI,GAAG8gB,IAAI,CAACnkB,KAAD,CAAX;IACAowB,MAAM,CAAC/wB,CAAD,CAAN,GAAY;MACV2R,CAAC,EAAE/E,MAAM,CAACokB,KAAP,CAAapuB,gBAAgB,CAACoB,IAAD,EAAO5C,GAAP,CAA7B,EAA0CT,KAA1C;IADO,CAAZ;EAGD;;EACD,OAAOowB,MAAP;AACD;;AAED,MAAME,OAAO,GAAGnyB,MAAM,CAACmyB,OAAP,IAAkB,KAAlC;;AACA,MAAMC,QAAQ,GAAG,CAAC3kB,MAAD,EAASvM,CAAT,KAAeA,CAAC,GAAGuM,MAAM,CAACpM,MAAX,IAAqB,CAACoM,MAAM,CAACvM,CAAD,CAAN,CAAUmxB,IAAhC,IAAwC5kB,MAAM,CAACvM,CAAD,CAA9E;;AACA,MAAMoxB,YAAY,GAAIzO,SAAD,IAAeA,SAAS,KAAK,GAAd,GAAoB,GAApB,GAA0B,GAA9D;;AACA,SAAS0O,WAAT,CAAqBC,UAArB,EAAiCC,WAAjC,EAA8CC,UAA9C,EAA0DzjB,CAA1D,EAA6D;EAC3D,MAAM7L,QAAQ,GAAGovB,UAAU,CAACH,IAAX,GAAkBI,WAAlB,GAAgCD,UAAjD;EACA,MAAMnvB,OAAO,GAAGovB,WAAhB;EACA,MAAME,IAAI,GAAGD,UAAU,CAACL,IAAX,GAAkBI,WAAlB,GAAgCC,UAA7C;EACA,MAAME,GAAG,GAAGhqB,qBAAqB,CAACvF,OAAD,EAAUD,QAAV,CAAjC;EACA,MAAMyvB,GAAG,GAAGjqB,qBAAqB,CAAC+pB,IAAD,EAAOtvB,OAAP,CAAjC;EACA,IAAIyvB,GAAG,GAAGF,GAAG,IAAIA,GAAG,GAAGC,GAAV,CAAb;EACA,IAAIE,GAAG,GAAGF,GAAG,IAAID,GAAG,GAAGC,GAAV,CAAb;EACAC,GAAG,GAAG3rB,KAAK,CAAC2rB,GAAD,CAAL,GAAa,CAAb,GAAiBA,GAAvB;EACAC,GAAG,GAAG5rB,KAAK,CAAC4rB,GAAD,CAAL,GAAa,CAAb,GAAiBA,GAAvB;EACA,MAAMC,EAAE,GAAG/jB,CAAC,GAAG6jB,GAAf;EACA,MAAMG,EAAE,GAAGhkB,CAAC,GAAG8jB,GAAf;EACA,OAAO;IACL3vB,QAAQ,EAAE;MACRO,CAAC,EAAEN,OAAO,CAACM,CAAR,GAAYqvB,EAAE,IAAIL,IAAI,CAAChvB,CAAL,GAASP,QAAQ,CAACO,CAAtB,CADT;MAERE,CAAC,EAAER,OAAO,CAACQ,CAAR,GAAYmvB,EAAE,IAAIL,IAAI,CAAC9uB,CAAL,GAAST,QAAQ,CAACS,CAAtB;IAFT,CADL;IAKL8uB,IAAI,EAAE;MACJhvB,CAAC,EAAEN,OAAO,CAACM,CAAR,GAAYsvB,EAAE,IAAIN,IAAI,CAAChvB,CAAL,GAASP,QAAQ,CAACO,CAAtB,CADb;MAEJE,CAAC,EAAER,OAAO,CAACQ,CAAR,GAAYovB,EAAE,IAAIN,IAAI,CAAC9uB,CAAL,GAAST,QAAQ,CAACS,CAAtB;IAFb;EALD,CAAP;AAUD;;AACD,SAASqvB,cAAT,CAAwBzlB,MAAxB,EAAgC0lB,MAAhC,EAAwCC,EAAxC,EAA4C;EAC1C,MAAMC,SAAS,GAAG5lB,MAAM,CAACpM,MAAzB;EACA,IAAIiyB,MAAJ,EAAYC,KAAZ,EAAmBC,IAAnB,EAAyBC,gBAAzB,EAA2CC,YAA3C;EACA,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAAS,CAAT,CAAzB;;EACA,KAAK,IAAIvM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmyB,SAAS,GAAG,CAAhC,EAAmC,EAAEnyB,CAArC,EAAwC;IACtCwyB,YAAY,GAAGC,UAAf;IACAA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAASvM,CAAC,GAAG,CAAb,CAArB;;IACA,IAAI,CAACwyB,YAAD,IAAiB,CAACC,UAAtB,EAAkC;MAChC;IACD;;IACD,IAAIrtB,YAAY,CAAC6sB,MAAM,CAACjyB,CAAD,CAAP,EAAY,CAAZ,EAAeixB,OAAf,CAAhB,EAAyC;MACvCiB,EAAE,CAAClyB,CAAD,CAAF,GAAQkyB,EAAE,CAAClyB,CAAC,GAAG,CAAL,CAAF,GAAY,CAApB;MACA;IACD;;IACDoyB,MAAM,GAAGF,EAAE,CAAClyB,CAAD,CAAF,GAAQiyB,MAAM,CAACjyB,CAAD,CAAvB;IACAqyB,KAAK,GAAGH,EAAE,CAAClyB,CAAC,GAAG,CAAL,CAAF,GAAYiyB,MAAM,CAACjyB,CAAD,CAA1B;IACAuyB,gBAAgB,GAAGluB,IAAI,CAACiB,GAAL,CAAS8sB,MAAT,EAAiB,CAAjB,IAAsB/tB,IAAI,CAACiB,GAAL,CAAS+sB,KAAT,EAAgB,CAAhB,CAAzC;;IACA,IAAIE,gBAAgB,IAAI,CAAxB,EAA2B;MACzB;IACD;;IACDD,IAAI,GAAG,IAAIjuB,IAAI,CAACuB,IAAL,CAAU2sB,gBAAV,CAAX;IACAL,EAAE,CAAClyB,CAAD,CAAF,GAAQoyB,MAAM,GAAGE,IAAT,GAAgBL,MAAM,CAACjyB,CAAD,CAA9B;IACAkyB,EAAE,CAAClyB,CAAC,GAAG,CAAL,CAAF,GAAYqyB,KAAK,GAAGC,IAAR,GAAeL,MAAM,CAACjyB,CAAD,CAAjC;EACD;AACF;;AACD,SAAS0yB,eAAT,CAAyBnmB,MAAzB,EAAiC2lB,EAAjC,EAAqCvP,SAAS,GAAG,GAAjD,EAAsD;EACpD,MAAMgQ,SAAS,GAAGvB,YAAY,CAACzO,SAAD,CAA9B;EACA,MAAMwP,SAAS,GAAG5lB,MAAM,CAACpM,MAAzB;EACA,IAAIyyB,KAAJ,EAAWC,WAAX,EAAwBL,YAAxB;EACA,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAAS,CAAT,CAAzB;;EACA,KAAK,IAAIvM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmyB,SAApB,EAA+B,EAAEnyB,CAAjC,EAAoC;IAClC6yB,WAAW,GAAGL,YAAd;IACAA,YAAY,GAAGC,UAAf;IACAA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAASvM,CAAC,GAAG,CAAb,CAArB;;IACA,IAAI,CAACwyB,YAAL,EAAmB;MACjB;IACD;;IACD,MAAMM,MAAM,GAAGN,YAAY,CAAC7P,SAAD,CAA3B;IACA,MAAMoQ,MAAM,GAAGP,YAAY,CAACG,SAAD,CAA3B;;IACA,IAAIE,WAAJ,EAAiB;MACfD,KAAK,GAAG,CAACE,MAAM,GAAGD,WAAW,CAAClQ,SAAD,CAArB,IAAoC,CAA5C;MACA6P,YAAY,CAAE,MAAK7P,SAAU,EAAjB,CAAZ,GAAkCmQ,MAAM,GAAGF,KAA3C;MACAJ,YAAY,CAAE,MAAKG,SAAU,EAAjB,CAAZ,GAAkCI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAAClyB,CAAD,CAArD;IACD;;IACD,IAAIyyB,UAAJ,EAAgB;MACdG,KAAK,GAAG,CAACH,UAAU,CAAC9P,SAAD,CAAV,GAAwBmQ,MAAzB,IAAmC,CAA3C;MACAN,YAAY,CAAE,MAAK7P,SAAU,EAAjB,CAAZ,GAAkCmQ,MAAM,GAAGF,KAA3C;MACAJ,YAAY,CAAE,MAAKG,SAAU,EAAjB,CAAZ,GAAkCI,MAAM,GAAGH,KAAK,GAAGV,EAAE,CAAClyB,CAAD,CAArD;IACD;EACF;AACF;;AACD,SAASgzB,mBAAT,CAA6BzmB,MAA7B,EAAqCoW,SAAS,GAAG,GAAjD,EAAsD;EACpD,MAAMgQ,SAAS,GAAGvB,YAAY,CAACzO,SAAD,CAA9B;EACA,MAAMwP,SAAS,GAAG5lB,MAAM,CAACpM,MAAzB;EACA,MAAM8xB,MAAM,GAAG5zB,KAAK,CAAC8zB,SAAD,CAAL,CAAiBvK,IAAjB,CAAsB,CAAtB,CAAf;EACA,MAAMsK,EAAE,GAAG7zB,KAAK,CAAC8zB,SAAD,CAAhB;EACA,IAAInyB,CAAJ,EAAO6yB,WAAP,EAAoBL,YAApB;EACA,IAAIC,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAAS,CAAT,CAAzB;;EACA,KAAKvM,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGmyB,SAAhB,EAA2B,EAAEnyB,CAA7B,EAAgC;IAC9B6yB,WAAW,GAAGL,YAAd;IACAA,YAAY,GAAGC,UAAf;IACAA,UAAU,GAAGvB,QAAQ,CAAC3kB,MAAD,EAASvM,CAAC,GAAG,CAAb,CAArB;;IACA,IAAI,CAACwyB,YAAL,EAAmB;MACjB;IACD;;IACD,IAAIC,UAAJ,EAAgB;MACd,MAAMQ,UAAU,GAAGR,UAAU,CAAC9P,SAAD,CAAV,GAAwB6P,YAAY,CAAC7P,SAAD,CAAvD;MACAsP,MAAM,CAACjyB,CAAD,CAAN,GAAYizB,UAAU,KAAK,CAAf,GAAmB,CAACR,UAAU,CAACE,SAAD,CAAV,GAAwBH,YAAY,CAACG,SAAD,CAArC,IAAoDM,UAAvE,GAAoF,CAAhG;IACD;;IACDf,EAAE,CAAClyB,CAAD,CAAF,GAAQ,CAAC6yB,WAAD,GAAeZ,MAAM,CAACjyB,CAAD,CAArB,GACJ,CAACyyB,UAAD,GAAcR,MAAM,CAACjyB,CAAC,GAAG,CAAL,CAApB,GACC+E,IAAI,CAACktB,MAAM,CAACjyB,CAAC,GAAG,CAAL,CAAP,CAAJ,KAAwB+E,IAAI,CAACktB,MAAM,CAACjyB,CAAD,CAAP,CAA7B,GAA4C,CAA5C,GACA,CAACiyB,MAAM,CAACjyB,CAAC,GAAG,CAAL,CAAN,GAAgBiyB,MAAM,CAACjyB,CAAD,CAAvB,IAA8B,CAHlC;EAID;;EACDgyB,cAAc,CAACzlB,MAAD,EAAS0lB,MAAT,EAAiBC,EAAjB,CAAd;EACAQ,eAAe,CAACnmB,MAAD,EAAS2lB,EAAT,EAAavP,SAAb,CAAf;AACD;;AACD,SAASuQ,eAAT,CAAyBC,EAAzB,EAA6B1sB,GAA7B,EAAkCC,GAAlC,EAAuC;EACrC,OAAOrC,IAAI,CAACqC,GAAL,CAASrC,IAAI,CAACoC,GAAL,CAAS0sB,EAAT,EAAazsB,GAAb,CAAT,EAA4BD,GAA5B,CAAP;AACD;;AACD,SAAS2sB,eAAT,CAAyB7mB,MAAzB,EAAiC0b,IAAjC,EAAuC;EACrC,IAAIjoB,CAAJ,EAAOO,IAAP,EAAaynB,KAAb,EAAoBqL,MAApB,EAA4BC,UAA5B;;EACA,IAAIC,UAAU,GAAGxL,cAAc,CAACxb,MAAM,CAAC,CAAD,CAAP,EAAY0b,IAAZ,CAA/B;;EACA,KAAKjoB,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGgM,MAAM,CAACpM,MAA1B,EAAkCH,CAAC,GAAGO,IAAtC,EAA4C,EAAEP,CAA9C,EAAiD;IAC/CszB,UAAU,GAAGD,MAAb;IACAA,MAAM,GAAGE,UAAT;IACAA,UAAU,GAAGvzB,CAAC,GAAGO,IAAI,GAAG,CAAX,IAAgBwnB,cAAc,CAACxb,MAAM,CAACvM,CAAC,GAAG,CAAL,CAAP,EAAgBioB,IAAhB,CAA3C;;IACA,IAAI,CAACoL,MAAL,EAAa;MACX;IACD;;IACDrL,KAAK,GAAGzb,MAAM,CAACvM,CAAD,CAAd;;IACA,IAAIszB,UAAJ,EAAgB;MACdtL,KAAK,CAACa,IAAN,GAAaqK,eAAe,CAAClL,KAAK,CAACa,IAAP,EAAaZ,IAAI,CAAChc,IAAlB,EAAwBgc,IAAI,CAAC/b,KAA7B,CAA5B;MACA8b,KAAK,CAACe,IAAN,GAAamK,eAAe,CAAClL,KAAK,CAACe,IAAP,EAAad,IAAI,CAACE,GAAlB,EAAuBF,IAAI,CAACG,MAA5B,CAA5B;IACD;;IACD,IAAImL,UAAJ,EAAgB;MACdvL,KAAK,CAACc,IAAN,GAAaoK,eAAe,CAAClL,KAAK,CAACc,IAAP,EAAab,IAAI,CAAChc,IAAlB,EAAwBgc,IAAI,CAAC/b,KAA7B,CAA5B;MACA8b,KAAK,CAACgB,IAAN,GAAakK,eAAe,CAAClL,KAAK,CAACgB,IAAP,EAAaf,IAAI,CAACE,GAAlB,EAAuBF,IAAI,CAACG,MAA5B,CAA5B;IACD;EACF;AACF;;AACD,SAASoL,0BAAT,CAAoCjnB,MAApC,EAA4ChL,OAA5C,EAAqD0mB,IAArD,EAA2DwL,IAA3D,EAAiE9Q,SAAjE,EAA4E;EAC1E,IAAI3iB,CAAJ,EAAOO,IAAP,EAAaynB,KAAb,EAAoB0L,aAApB;;EACA,IAAInyB,OAAO,CAACoyB,QAAZ,EAAsB;IACpBpnB,MAAM,GAAGA,MAAM,CAAC2jB,MAAP,CAAeiD,EAAD,IAAQ,CAACA,EAAE,CAAChC,IAA1B,CAAT;EACD;;EACD,IAAI5vB,OAAO,CAACqyB,sBAAR,KAAmC,UAAvC,EAAmD;IACjDZ,mBAAmB,CAACzmB,MAAD,EAASoW,SAAT,CAAnB;EACD,CAFD,MAEO;IACL,IAAIkR,IAAI,GAAGJ,IAAI,GAAGlnB,MAAM,CAACA,MAAM,CAACpM,MAAP,GAAgB,CAAjB,CAAT,GAA+BoM,MAAM,CAAC,CAAD,CAApD;;IACA,KAAKvM,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGgM,MAAM,CAACpM,MAA1B,EAAkCH,CAAC,GAAGO,IAAtC,EAA4C,EAAEP,CAA9C,EAAiD;MAC/CgoB,KAAK,GAAGzb,MAAM,CAACvM,CAAD,CAAd;MACA0zB,aAAa,GAAGrC,WAAW,CACzBwC,IADyB,EAEzB7L,KAFyB,EAGzBzb,MAAM,CAAClI,IAAI,CAACoC,GAAL,CAASzG,CAAC,GAAG,CAAb,EAAgBO,IAAI,IAAIkzB,IAAI,GAAG,CAAH,GAAO,CAAf,CAApB,IAAyClzB,IAA1C,CAHmB,EAIzBgB,OAAO,CAACuyB,OAJiB,CAA3B;MAMA9L,KAAK,CAACa,IAAN,GAAa6K,aAAa,CAACxxB,QAAd,CAAuBO,CAApC;MACAulB,KAAK,CAACe,IAAN,GAAa2K,aAAa,CAACxxB,QAAd,CAAuBS,CAApC;MACAqlB,KAAK,CAACc,IAAN,GAAa4K,aAAa,CAACjC,IAAd,CAAmBhvB,CAAhC;MACAulB,KAAK,CAACgB,IAAN,GAAa0K,aAAa,CAACjC,IAAd,CAAmB9uB,CAAhC;MACAkxB,IAAI,GAAG7L,KAAP;IACD;EACF;;EACD,IAAIzmB,OAAO,CAAC6xB,eAAZ,EAA6B;IAC3BA,eAAe,CAAC7mB,MAAD,EAAS0b,IAAT,CAAf;EACD;AACF;;AAED,SAAS8L,eAAT,GAA2B;EACzB,OAAO,OAAO9oB,MAAP,KAAkB,WAAlB,IAAiC,OAAO+oB,QAAP,KAAoB,WAA5D;AACD;;AACD,SAASC,cAAT,CAAwBC,OAAxB,EAAiC;EAC/B,IAAI7D,MAAM,GAAG6D,OAAO,CAACC,UAArB;;EACA,IAAI9D,MAAM,IAAIA,MAAM,CAAC5xB,QAAP,OAAsB,qBAApC,EAA2D;IACzD4xB,MAAM,GAAGA,MAAM,CAAC+D,IAAhB;EACD;;EACD,OAAO/D,MAAP;AACD;;AACD,SAASgE,aAAT,CAAuBC,UAAvB,EAAmCnT,IAAnC,EAAyCoT,cAAzC,EAAyD;EACvD,IAAIC,aAAJ;;EACA,IAAI,OAAOF,UAAP,KAAsB,QAA1B,EAAoC;IAClCE,aAAa,GAAGnW,QAAQ,CAACiW,UAAD,EAAa,EAAb,CAAxB;;IACA,IAAIA,UAAU,CAACjzB,OAAX,CAAmB,GAAnB,MAA4B,CAAC,CAAjC,EAAoC;MAClCmzB,aAAa,GAAGA,aAAa,GAAG,GAAhB,GAAsBrT,IAAI,CAACgT,UAAL,CAAgBI,cAAhB,CAAtC;IACD;EACF,CALD,MAKO;IACLC,aAAa,GAAGF,UAAhB;EACD;;EACD,OAAOE,aAAP;AACD;;AACD,MAAMC,gBAAgB,GAAIC,OAAD,IAAazpB,MAAM,CAACwpB,gBAAP,CAAwBC,OAAxB,EAAiC,IAAjC,CAAtC;;AACA,SAASC,QAAT,CAAkBC,EAAlB,EAAsBpuB,QAAtB,EAAgC;EAC9B,OAAOiuB,gBAAgB,CAACG,EAAD,CAAhB,CAAqBC,gBAArB,CAAsCruB,QAAtC,CAAP;AACD;;AACD,MAAMsuB,SAAS,GAAG,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAAlB;;AACA,SAASC,kBAAT,CAA4BC,MAA5B,EAAoC5S,KAApC,EAA2C6S,MAA3C,EAAmD;EACjD,MAAMtvB,MAAM,GAAG,EAAf;EACAsvB,MAAM,GAAGA,MAAM,GAAG,MAAMA,MAAT,GAAkB,EAAjC;;EACA,KAAK,IAAIj1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;IAC1B,MAAMk1B,GAAG,GAAGJ,SAAS,CAAC90B,CAAD,CAArB;IACA2F,MAAM,CAACuvB,GAAD,CAAN,GAAc51B,UAAU,CAAC01B,MAAM,CAAC5S,KAAK,GAAG,GAAR,GAAc8S,GAAd,GAAoBD,MAArB,CAAP,CAAV,IAAkD,CAAhE;EACD;;EACDtvB,MAAM,CAACyf,KAAP,GAAezf,MAAM,CAACsG,IAAP,GAActG,MAAM,CAACuG,KAApC;EACAvG,MAAM,CAAC6gB,MAAP,GAAgB7gB,MAAM,CAACwiB,GAAP,GAAaxiB,MAAM,CAACyiB,MAApC;EACA,OAAOziB,MAAP;AACD;;AACD,MAAMwvB,YAAY,GAAG,CAAC1yB,CAAD,EAAIE,CAAJ,EAAO5B,MAAP,KAAkB,CAAC0B,CAAC,GAAG,CAAJ,IAASE,CAAC,GAAG,CAAd,MAAqB,CAAC5B,MAAD,IAAW,CAACA,MAAM,CAACq0B,UAAxC,CAAvC;;AACA,SAASC,iBAAT,CAA2BlxB,CAA3B,EAA8BiiB,MAA9B,EAAsC;EACpC,MAAMkP,OAAO,GAAGnxB,CAAC,CAACmxB,OAAlB;EACA,MAAMz0B,MAAM,GAAGy0B,OAAO,IAAIA,OAAO,CAACn1B,MAAnB,GAA4Bm1B,OAAO,CAAC,CAAD,CAAnC,GAAyCnxB,CAAxD;EACA,MAAM;IAACoxB,OAAD;IAAUC;EAAV,IAAqB30B,MAA3B;EACA,IAAI40B,GAAG,GAAG,KAAV;EACA,IAAIhzB,CAAJ,EAAOE,CAAP;;EACA,IAAIwyB,YAAY,CAACI,OAAD,EAAUC,OAAV,EAAmBrxB,CAAC,CAACpD,MAArB,CAAhB,EAA8C;IAC5C0B,CAAC,GAAG8yB,OAAJ;IACA5yB,CAAC,GAAG6yB,OAAJ;EACD,CAHD,MAGO;IACL,MAAM7N,IAAI,GAAGvB,MAAM,CAACsP,qBAAP,EAAb;IACAjzB,CAAC,GAAG5B,MAAM,CAAC80B,OAAP,GAAiBhO,IAAI,CAAC1b,IAA1B;IACAtJ,CAAC,GAAG9B,MAAM,CAAC+0B,OAAP,GAAiBjO,IAAI,CAACQ,GAA1B;IACAsN,GAAG,GAAG,IAAN;EACD;;EACD,OAAO;IAAChzB,CAAD;IAAIE,CAAJ;IAAO8yB;EAAP,CAAP;AACD;;AACD,SAASI,mBAAT,CAA6BC,GAA7B,EAAkCjU,KAAlC,EAAyC;EACvC,IAAI,YAAYiU,GAAhB,EAAqB;IACnB,OAAOA,GAAP;EACD;;EACD,MAAM;IAAC1P,MAAD;IAASH;EAAT,IAAoCpE,KAA1C;EACA,MAAMO,KAAK,GAAGqS,gBAAgB,CAACrO,MAAD,CAA9B;EACA,MAAM2P,SAAS,GAAG3T,KAAK,CAAC4T,SAAN,KAAoB,YAAtC;EACA,MAAMC,QAAQ,GAAGlB,kBAAkB,CAAC3S,KAAD,EAAQ,SAAR,CAAnC;EACA,MAAM8T,OAAO,GAAGnB,kBAAkB,CAAC3S,KAAD,EAAQ,QAAR,EAAkB,OAAlB,CAAlC;EACA,MAAM;IAAC3f,CAAD;IAAIE,CAAJ;IAAO8yB;EAAP,IAAcJ,iBAAiB,CAACS,GAAD,EAAM1P,MAAN,CAArC;EACA,MAAMO,OAAO,GAAGsP,QAAQ,CAAChqB,IAAT,IAAiBwpB,GAAG,IAAIS,OAAO,CAACjqB,IAAhC,CAAhB;EACA,MAAM2a,OAAO,GAAGqP,QAAQ,CAAC9N,GAAT,IAAgBsN,GAAG,IAAIS,OAAO,CAAC/N,GAA/B,CAAhB;EACA,IAAI;IAAC/C,KAAD;IAAQoB;EAAR,IAAkB3E,KAAtB;;EACA,IAAIkU,SAAJ,EAAe;IACb3Q,KAAK,IAAI6Q,QAAQ,CAAC7Q,KAAT,GAAiB8Q,OAAO,CAAC9Q,KAAlC;IACAoB,MAAM,IAAIyP,QAAQ,CAACzP,MAAT,GAAkB0P,OAAO,CAAC1P,MAApC;EACD;;EACD,OAAO;IACL/jB,CAAC,EAAE4B,IAAI,CAACc,KAAL,CAAW,CAAC1C,CAAC,GAAGkkB,OAAL,IAAgBvB,KAAhB,GAAwBgB,MAAM,CAAChB,KAA/B,GAAuCa,uBAAlD,CADE;IAELtjB,CAAC,EAAE0B,IAAI,CAACc,KAAL,CAAW,CAACxC,CAAC,GAAGikB,OAAL,IAAgBJ,MAAhB,GAAyBJ,MAAM,CAACI,MAAhC,GAAyCP,uBAApD;EAFE,CAAP;AAID;;AACD,SAASkQ,gBAAT,CAA0B/P,MAA1B,EAAkChB,KAAlC,EAAyCoB,MAAzC,EAAiD;EAC/C,IAAIoD,QAAJ,EAAcwM,SAAd;;EACA,IAAIhR,KAAK,KAAKhjB,SAAV,IAAuBokB,MAAM,KAAKpkB,SAAtC,EAAiD;IAC/C,MAAMi0B,SAAS,GAAGpC,cAAc,CAAC7N,MAAD,CAAhC;;IACA,IAAI,CAACiQ,SAAL,EAAgB;MACdjR,KAAK,GAAGgB,MAAM,CAACkQ,WAAf;MACA9P,MAAM,GAAGJ,MAAM,CAACmQ,YAAhB;IACD,CAHD,MAGO;MACL,MAAM5O,IAAI,GAAG0O,SAAS,CAACX,qBAAV,EAAb;MACA,MAAMc,cAAc,GAAG/B,gBAAgB,CAAC4B,SAAD,CAAvC;MACA,MAAMI,eAAe,GAAG1B,kBAAkB,CAACyB,cAAD,EAAiB,QAAjB,EAA2B,OAA3B,CAA1C;MACA,MAAME,gBAAgB,GAAG3B,kBAAkB,CAACyB,cAAD,EAAiB,SAAjB,CAA3C;MACApR,KAAK,GAAGuC,IAAI,CAACvC,KAAL,GAAasR,gBAAgB,CAACtR,KAA9B,GAAsCqR,eAAe,CAACrR,KAA9D;MACAoB,MAAM,GAAGmB,IAAI,CAACnB,MAAL,GAAckQ,gBAAgB,CAAClQ,MAA/B,GAAwCiQ,eAAe,CAACjQ,MAAjE;MACAoD,QAAQ,GAAGyK,aAAa,CAACmC,cAAc,CAAC5M,QAAhB,EAA0ByM,SAA1B,EAAqC,aAArC,CAAxB;MACAD,SAAS,GAAG/B,aAAa,CAACmC,cAAc,CAACJ,SAAhB,EAA2BC,SAA3B,EAAsC,cAAtC,CAAzB;IACD;EACF;;EACD,OAAO;IACLjR,KADK;IAELoB,MAFK;IAGLoD,QAAQ,EAAEA,QAAQ,IAAIplB,QAHjB;IAIL4xB,SAAS,EAAEA,SAAS,IAAI5xB;EAJnB,CAAP;AAMD;;AACD,MAAMmyB,MAAM,GAAGn0B,CAAC,IAAI6B,IAAI,CAACc,KAAL,CAAW3C,CAAC,GAAG,EAAf,IAAqB,EAAzC;;AACA,SAASo0B,cAAT,CAAwBxQ,MAAxB,EAAgCyQ,OAAhC,EAAyCC,QAAzC,EAAmDC,WAAnD,EAAgE;EAC9D,MAAM3U,KAAK,GAAGqS,gBAAgB,CAACrO,MAAD,CAA9B;EACA,MAAM4Q,OAAO,GAAGjC,kBAAkB,CAAC3S,KAAD,EAAQ,QAAR,CAAlC;EACA,MAAMwH,QAAQ,GAAGyK,aAAa,CAACjS,KAAK,CAACwH,QAAP,EAAiBxD,MAAjB,EAAyB,aAAzB,CAAb,IAAwD5hB,QAAzE;EACA,MAAM4xB,SAAS,GAAG/B,aAAa,CAACjS,KAAK,CAACgU,SAAP,EAAkBhQ,MAAlB,EAA0B,cAA1B,CAAb,IAA0D5hB,QAA5E;EACA,MAAMyyB,aAAa,GAAGd,gBAAgB,CAAC/P,MAAD,EAASyQ,OAAT,EAAkBC,QAAlB,CAAtC;EACA,IAAI;IAAC1R,KAAD;IAAQoB;EAAR,IAAkByQ,aAAtB;;EACA,IAAI7U,KAAK,CAAC4T,SAAN,KAAoB,aAAxB,EAAuC;IACrC,MAAME,OAAO,GAAGnB,kBAAkB,CAAC3S,KAAD,EAAQ,QAAR,EAAkB,OAAlB,CAAlC;IACA,MAAM6T,QAAQ,GAAGlB,kBAAkB,CAAC3S,KAAD,EAAQ,SAAR,CAAnC;IACAgD,KAAK,IAAI6Q,QAAQ,CAAC7Q,KAAT,GAAiB8Q,OAAO,CAAC9Q,KAAlC;IACAoB,MAAM,IAAIyP,QAAQ,CAACzP,MAAT,GAAkB0P,OAAO,CAAC1P,MAApC;EACD;;EACDpB,KAAK,GAAG/gB,IAAI,CAACqC,GAAL,CAAS,CAAT,EAAY0e,KAAK,GAAG4R,OAAO,CAAC5R,KAA5B,CAAR;EACAoB,MAAM,GAAGniB,IAAI,CAACqC,GAAL,CAAS,CAAT,EAAYqwB,WAAW,GAAG1yB,IAAI,CAACkB,KAAL,CAAW6f,KAAK,GAAG2R,WAAnB,CAAH,GAAqCvQ,MAAM,GAAGwQ,OAAO,CAACxQ,MAA7E,CAAT;EACApB,KAAK,GAAGuR,MAAM,CAACtyB,IAAI,CAACoC,GAAL,CAAS2e,KAAT,EAAgBwE,QAAhB,EAA0BqN,aAAa,CAACrN,QAAxC,CAAD,CAAd;EACApD,MAAM,GAAGmQ,MAAM,CAACtyB,IAAI,CAACoC,GAAL,CAAS+f,MAAT,EAAiB4P,SAAjB,EAA4Ba,aAAa,CAACb,SAA1C,CAAD,CAAf;;EACA,IAAIhR,KAAK,IAAI,CAACoB,MAAd,EAAsB;IACpBA,MAAM,GAAGmQ,MAAM,CAACvR,KAAK,GAAG,CAAT,CAAf;EACD;;EACD,OAAO;IACLA,KADK;IAELoB;EAFK,CAAP;AAID;;AACD,SAAS0Q,WAAT,CAAqBrV,KAArB,EAA4BsV,UAA5B,EAAwCC,UAAxC,EAAoD;EAClD,MAAMC,UAAU,GAAGF,UAAU,IAAI,CAAjC;EACA,MAAMG,YAAY,GAAGjzB,IAAI,CAACkB,KAAL,CAAWsc,KAAK,CAAC2E,MAAN,GAAe6Q,UAA1B,CAArB;EACA,MAAME,WAAW,GAAGlzB,IAAI,CAACkB,KAAL,CAAWsc,KAAK,CAACuD,KAAN,GAAciS,UAAzB,CAApB;EACAxV,KAAK,CAAC2E,MAAN,GAAe8Q,YAAY,GAAGD,UAA9B;EACAxV,KAAK,CAACuD,KAAN,GAAcmS,WAAW,GAAGF,UAA5B;EACA,MAAMjR,MAAM,GAAGvE,KAAK,CAACuE,MAArB;;EACA,IAAIA,MAAM,CAAChE,KAAP,KAAiBgV,UAAU,IAAK,CAAChR,MAAM,CAAChE,KAAP,CAAaoE,MAAd,IAAwB,CAACJ,MAAM,CAAChE,KAAP,CAAagD,KAAtE,CAAJ,EAAmF;IACjFgB,MAAM,CAAChE,KAAP,CAAaoE,MAAb,GAAuB,GAAE3E,KAAK,CAAC2E,MAAO,IAAtC;IACAJ,MAAM,CAAChE,KAAP,CAAagD,KAAb,GAAsB,GAAEvD,KAAK,CAACuD,KAAM,IAApC;EACD;;EACD,IAAIvD,KAAK,CAACoE,uBAAN,KAAkCoR,UAAlC,IACGjR,MAAM,CAACI,MAAP,KAAkB8Q,YADrB,IAEGlR,MAAM,CAAChB,KAAP,KAAiBmS,WAFxB,EAEqC;IACnC1V,KAAK,CAACoE,uBAAN,GAAgCoR,UAAhC;IACAjR,MAAM,CAACI,MAAP,GAAgB8Q,YAAhB;IACAlR,MAAM,CAAChB,KAAP,GAAemS,WAAf;IACA1V,KAAK,CAACW,GAAN,CAAUgV,YAAV,CAAuBH,UAAvB,EAAmC,CAAnC,EAAsC,CAAtC,EAAyCA,UAAzC,EAAqD,CAArD,EAAwD,CAAxD;IACA,OAAO,IAAP;EACD;;EACD,OAAO,KAAP;AACD;;AACD,MAAMI,4BAA4B,GAAI,YAAW;EAC/C,IAAIC,gBAAgB,GAAG,KAAvB;;EACA,IAAI;IACF,MAAMn2B,OAAO,GAAG;MACd,IAAIo2B,OAAJ,GAAc;QACZD,gBAAgB,GAAG,IAAnB;QACA,OAAO,KAAP;MACD;;IAJa,CAAhB;IAMAzsB,MAAM,CAAC2sB,gBAAP,CAAwB,MAAxB,EAAgC,IAAhC,EAAsCr2B,OAAtC;IACA0J,MAAM,CAAC4sB,mBAAP,CAA2B,MAA3B,EAAmC,IAAnC,EAAyCt2B,OAAzC;EACD,CATD,CASE,OAAO4C,CAAP,EAAU,CACX;;EACD,OAAOuzB,gBAAP;AACD,CAdqC,EAAtC;;AAeA,SAASI,YAAT,CAAsBpD,OAAtB,EAA+BluB,QAA/B,EAAyC;EACvC,MAAMrI,KAAK,GAAGw2B,QAAQ,CAACD,OAAD,EAAUluB,QAAV,CAAtB;EACA,MAAM6kB,OAAO,GAAGltB,KAAK,IAAIA,KAAK,CAACmtB,KAAN,CAAY,mBAAZ,CAAzB;EACA,OAAOD,OAAO,GAAG,CAACA,OAAO,CAAC,CAAD,CAAX,GAAiBjpB,SAA/B;AACD;;AAED,SAAS21B,YAAT,CAAsB/kB,EAAtB,EAA0BC,EAA1B,EAA8BlF,CAA9B,EAAiC8U,IAAjC,EAAuC;EACrC,OAAO;IACLpgB,CAAC,EAAEuQ,EAAE,CAACvQ,CAAH,GAAOsL,CAAC,IAAIkF,EAAE,CAACxQ,CAAH,GAAOuQ,EAAE,CAACvQ,CAAd,CADN;IAELE,CAAC,EAAEqQ,EAAE,CAACrQ,CAAH,GAAOoL,CAAC,IAAIkF,EAAE,CAACtQ,CAAH,GAAOqQ,EAAE,CAACrQ,CAAd;EAFN,CAAP;AAID;;AACD,SAASq1B,qBAAT,CAA+BhlB,EAA/B,EAAmCC,EAAnC,EAAuClF,CAAvC,EAA0C8U,IAA1C,EAAgD;EAC9C,OAAO;IACLpgB,CAAC,EAAEuQ,EAAE,CAACvQ,CAAH,GAAOsL,CAAC,IAAIkF,EAAE,CAACxQ,CAAH,GAAOuQ,EAAE,CAACvQ,CAAd,CADN;IAELE,CAAC,EAAEkgB,IAAI,KAAK,QAAT,GAAoB9U,CAAC,GAAG,GAAJ,GAAUiF,EAAE,CAACrQ,CAAb,GAAiBsQ,EAAE,CAACtQ,CAAxC,GACDkgB,IAAI,KAAK,OAAT,GAAmB9U,CAAC,GAAG,CAAJ,GAAQiF,EAAE,CAACrQ,CAAX,GAAesQ,EAAE,CAACtQ,CAArC,GACAoL,CAAC,GAAG,CAAJ,GAAQkF,EAAE,CAACtQ,CAAX,GAAeqQ,EAAE,CAACrQ;EAJf,CAAP;AAMD;;AACD,SAASs1B,oBAAT,CAA8BjlB,EAA9B,EAAkCC,EAAlC,EAAsClF,CAAtC,EAAyC8U,IAAzC,EAA+C;EAC7C,MAAMqV,GAAG,GAAG;IAACz1B,CAAC,EAAEuQ,EAAE,CAAC8V,IAAP;IAAanmB,CAAC,EAAEqQ,EAAE,CAACgW;EAAnB,CAAZ;EACA,MAAMmP,GAAG,GAAG;IAAC11B,CAAC,EAAEwQ,EAAE,CAAC4V,IAAP;IAAalmB,CAAC,EAAEsQ,EAAE,CAAC8V;EAAnB,CAAZ;;EACA,MAAMllB,CAAC,GAAGk0B,YAAY,CAAC/kB,EAAD,EAAKklB,GAAL,EAAUnqB,CAAV,CAAtB;;EACA,MAAMjK,CAAC,GAAGi0B,YAAY,CAACG,GAAD,EAAMC,GAAN,EAAWpqB,CAAX,CAAtB;;EACA,MAAMqD,CAAC,GAAG2mB,YAAY,CAACI,GAAD,EAAMllB,EAAN,EAAUlF,CAAV,CAAtB;;EACA,MAAMqC,CAAC,GAAG2nB,YAAY,CAACl0B,CAAD,EAAIC,CAAJ,EAAOiK,CAAP,CAAtB;;EACA,MAAM5J,CAAC,GAAG4zB,YAAY,CAACj0B,CAAD,EAAIsN,CAAJ,EAAOrD,CAAP,CAAtB;;EACA,OAAOgqB,YAAY,CAAC3nB,CAAD,EAAIjM,CAAJ,EAAO4J,CAAP,CAAnB;AACD;;AAED,MAAMqqB,SAAS,GAAG,IAAIC,GAAJ,EAAlB;;AACA,SAASC,eAAT,CAAyBC,MAAzB,EAAiCh3B,OAAjC,EAA0C;EACxCA,OAAO,GAAGA,OAAO,IAAI,EAArB;EACA,MAAMi3B,QAAQ,GAAGD,MAAM,GAAGE,IAAI,CAACC,SAAL,CAAen3B,OAAf,CAA1B;EACA,IAAIo3B,SAAS,GAAGP,SAAS,CAACzU,GAAV,CAAc6U,QAAd,CAAhB;;EACA,IAAI,CAACG,SAAL,EAAgB;IACdA,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAT,CAAsBN,MAAtB,EAA8Bh3B,OAA9B,CAAZ;IACA62B,SAAS,CAAC5tB,GAAV,CAAcguB,QAAd,EAAwBG,SAAxB;EACD;;EACD,OAAOA,SAAP;AACD;;AACD,SAASG,YAAT,CAAsBC,GAAtB,EAA2BR,MAA3B,EAAmCh3B,OAAnC,EAA4C;EAC1C,OAAO+2B,eAAe,CAACC,MAAD,EAASh3B,OAAT,CAAf,CAAiCy3B,MAAjC,CAAwCD,GAAxC,CAAP;AACD;;AAED,MAAME,qBAAqB,GAAG,UAASC,KAAT,EAAgB9T,KAAhB,EAAuB;EACnD,OAAO;IACL3iB,CAAC,CAACA,CAAD,EAAI;MACH,OAAOy2B,KAAK,GAAGA,KAAR,GAAgB9T,KAAhB,GAAwB3iB,CAA/B;IACD,CAHI;;IAIL02B,QAAQ,CAAC9mB,CAAD,EAAI;MACV+S,KAAK,GAAG/S,CAAR;IACD,CANI;;IAOL4X,SAAS,CAACne,KAAD,EAAQ;MACf,IAAIA,KAAK,KAAK,QAAd,EAAwB;QACtB,OAAOA,KAAP;MACD;;MACD,OAAOA,KAAK,KAAK,OAAV,GAAoB,MAApB,GAA6B,OAApC;IACD,CAZI;;IAaLstB,KAAK,CAAC32B,CAAD,EAAItE,KAAJ,EAAW;MACd,OAAOsE,CAAC,GAAGtE,KAAX;IACD,CAfI;;IAgBLk7B,UAAU,CAAC52B,CAAD,EAAI62B,SAAJ,EAAe;MACvB,OAAO72B,CAAC,GAAG62B,SAAX;IACD;;EAlBI,CAAP;AAoBD,CArBD;;AAsBA,MAAMC,qBAAqB,GAAG,YAAW;EACvC,OAAO;IACL92B,CAAC,CAACA,CAAD,EAAI;MACH,OAAOA,CAAP;IACD,CAHI;;IAIL02B,QAAQ,CAAC9mB,CAAD,EAAI,CACX,CALI;;IAML4X,SAAS,CAACne,KAAD,EAAQ;MACf,OAAOA,KAAP;IACD,CARI;;IASLstB,KAAK,CAAC32B,CAAD,EAAItE,KAAJ,EAAW;MACd,OAAOsE,CAAC,GAAGtE,KAAX;IACD,CAXI;;IAYLk7B,UAAU,CAAC52B,CAAD,EAAI+2B,UAAJ,EAAgB;MACxB,OAAO/2B,CAAP;IACD;;EAdI,CAAP;AAgBD,CAjBD;;AAkBA,SAASg3B,aAAT,CAAuBttB,GAAvB,EAA4B+sB,KAA5B,EAAmC9T,KAAnC,EAA0C;EACxC,OAAOjZ,GAAG,GAAG8sB,qBAAqB,CAACC,KAAD,EAAQ9T,KAAR,CAAxB,GAAyCmU,qBAAqB,EAAxE;AACD;;AACD,SAASG,qBAAT,CAA+BlX,GAA/B,EAAoCmX,SAApC,EAA+C;EAC7C,IAAIvX,KAAJ,EAAWwX,QAAX;;EACA,IAAID,SAAS,KAAK,KAAd,IAAuBA,SAAS,KAAK,KAAzC,EAAgD;IAC9CvX,KAAK,GAAGI,GAAG,CAAC4D,MAAJ,CAAWhE,KAAnB;IACAwX,QAAQ,GAAG,CACTxX,KAAK,CAACyS,gBAAN,CAAuB,WAAvB,CADS,EAETzS,KAAK,CAACyX,mBAAN,CAA0B,WAA1B,CAFS,CAAX;IAIAzX,KAAK,CAAC0X,WAAN,CAAkB,WAAlB,EAA+BH,SAA/B,EAA0C,WAA1C;IACAnX,GAAG,CAACuX,iBAAJ,GAAwBH,QAAxB;EACD;AACF;;AACD,SAASI,oBAAT,CAA8BxX,GAA9B,EAAmCoX,QAAnC,EAA6C;EAC3C,IAAIA,QAAQ,KAAKx3B,SAAjB,EAA4B;IAC1B,OAAOogB,GAAG,CAACuX,iBAAX;IACAvX,GAAG,CAAC4D,MAAJ,CAAWhE,KAAX,CAAiB0X,WAAjB,CAA6B,WAA7B,EAA0CF,QAAQ,CAAC,CAAD,CAAlD,EAAuDA,QAAQ,CAAC,CAAD,CAA/D;EACD;AACF;;AAED,SAASK,UAAT,CAAoBzzB,QAApB,EAA8B;EAC5B,IAAIA,QAAQ,KAAK,OAAjB,EAA0B;IACxB,OAAO;MACL0zB,OAAO,EAAEnyB,aADJ;MAELoyB,OAAO,EAAEtyB,UAFJ;MAGLuyB,SAAS,EAAEtyB;IAHN,CAAP;EAKD;;EACD,OAAO;IACLoyB,OAAO,EAAExxB,UADJ;IAELyxB,OAAO,EAAE,CAACt2B,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAGC,CAFlB;IAGLs2B,SAAS,EAAE33B,CAAC,IAAIA;EAHX,CAAP;AAKD;;AACD,SAAS43B,gBAAT,CAA0B;EAACryB,KAAD;EAAQC,GAAR;EAAayE,KAAb;EAAoB+mB,IAApB;EAA0BrR;AAA1B,CAA1B,EAA4D;EAC1D,OAAO;IACLpa,KAAK,EAAEA,KAAK,GAAG0E,KADV;IAELzE,GAAG,EAAEA,GAAG,GAAGyE,KAFN;IAGL+mB,IAAI,EAAEA,IAAI,IAAI,CAACxrB,GAAG,GAAGD,KAAN,GAAc,CAAf,IAAoB0E,KAApB,KAA8B,CAHvC;IAIL0V;EAJK,CAAP;AAMD;;AACD,SAASkY,UAAT,CAAoBC,OAApB,EAA6BhuB,MAA7B,EAAqCiuB,MAArC,EAA6C;EAC3C,MAAM;IAACh0B,QAAD;IAAWwB,KAAK,EAAEyyB,UAAlB;IAA8BxyB,GAAG,EAAEyyB;EAAnC,IAA+CF,MAArD;EACA,MAAM;IAACN,OAAD;IAAUE;EAAV,IAAuBH,UAAU,CAACzzB,QAAD,CAAvC;EACA,MAAMkG,KAAK,GAAGH,MAAM,CAACpM,MAArB;EACA,IAAI;IAAC6H,KAAD;IAAQC,GAAR;IAAawrB;EAAb,IAAqB8G,OAAzB;EACA,IAAIv6B,CAAJ,EAAOO,IAAP;;EACA,IAAIkzB,IAAJ,EAAU;IACRzrB,KAAK,IAAI0E,KAAT;IACAzE,GAAG,IAAIyE,KAAP;;IACA,KAAK1M,CAAC,GAAG,CAAJ,EAAOO,IAAI,GAAGmM,KAAnB,EAA0B1M,CAAC,GAAGO,IAA9B,EAAoC,EAAEP,CAAtC,EAAyC;MACvC,IAAI,CAACk6B,OAAO,CAACE,SAAS,CAAC7tB,MAAM,CAACvE,KAAK,GAAG0E,KAAT,CAAN,CAAsBlG,QAAtB,CAAD,CAAV,EAA6Ci0B,UAA7C,EAAyDC,QAAzD,CAAZ,EAAgF;QAC9E;MACD;;MACD1yB,KAAK;MACLC,GAAG;IACJ;;IACDD,KAAK,IAAI0E,KAAT;IACAzE,GAAG,IAAIyE,KAAP;EACD;;EACD,IAAIzE,GAAG,GAAGD,KAAV,EAAiB;IACfC,GAAG,IAAIyE,KAAP;EACD;;EACD,OAAO;IAAC1E,KAAD;IAAQC,GAAR;IAAawrB,IAAb;IAAmBrR,KAAK,EAAEmY,OAAO,CAACnY;EAAlC,CAAP;AACD;;AACD,SAASuY,aAAT,CAAuBJ,OAAvB,EAAgChuB,MAAhC,EAAwCiuB,MAAxC,EAAgD;EAC9C,IAAI,CAACA,MAAL,EAAa;IACX,OAAO,CAACD,OAAD,CAAP;EACD;;EACD,MAAM;IAAC/zB,QAAD;IAAWwB,KAAK,EAAEyyB,UAAlB;IAA8BxyB,GAAG,EAAEyyB;EAAnC,IAA+CF,MAArD;EACA,MAAM9tB,KAAK,GAAGH,MAAM,CAACpM,MAArB;EACA,MAAM;IAACg6B,OAAD;IAAUD,OAAV;IAAmBE;EAAnB,IAAgCH,UAAU,CAACzzB,QAAD,CAAhD;EACA,MAAM;IAACwB,KAAD;IAAQC,GAAR;IAAawrB,IAAb;IAAmBrR;EAAnB,IAA4BkY,UAAU,CAACC,OAAD,EAAUhuB,MAAV,EAAkBiuB,MAAlB,CAA5C;EACA,MAAM70B,MAAM,GAAG,EAAf;EACA,IAAIi1B,MAAM,GAAG,KAAb;EACA,IAAIC,QAAQ,GAAG,IAAf;EACA,IAAI18B,KAAJ,EAAW6pB,KAAX,EAAkB8S,SAAlB;;EACA,MAAMC,aAAa,GAAG,MAAMb,OAAO,CAACO,UAAD,EAAaK,SAAb,EAAwB38B,KAAxB,CAAP,IAAyCg8B,OAAO,CAACM,UAAD,EAAaK,SAAb,CAAP,KAAmC,CAAxG;;EACA,MAAME,WAAW,GAAG,MAAMb,OAAO,CAACO,QAAD,EAAWv8B,KAAX,CAAP,KAA6B,CAA7B,IAAkC+7B,OAAO,CAACQ,QAAD,EAAWI,SAAX,EAAsB38B,KAAtB,CAAnE;;EACA,MAAM88B,WAAW,GAAG,MAAML,MAAM,IAAIG,aAAa,EAAjD;;EACA,MAAMG,UAAU,GAAG,MAAM,CAACN,MAAD,IAAWI,WAAW,EAA/C;;EACA,KAAK,IAAIh7B,CAAC,GAAGgI,KAAR,EAAe6rB,IAAI,GAAG7rB,KAA3B,EAAkChI,CAAC,IAAIiI,GAAvC,EAA4C,EAAEjI,CAA9C,EAAiD;IAC/CgoB,KAAK,GAAGzb,MAAM,CAACvM,CAAC,GAAG0M,KAAL,CAAd;;IACA,IAAIsb,KAAK,CAACmJ,IAAV,EAAgB;MACd;IACD;;IACDhzB,KAAK,GAAGi8B,SAAS,CAACpS,KAAK,CAACxhB,QAAD,CAAN,CAAjB;;IACA,IAAIrI,KAAK,KAAK28B,SAAd,EAAyB;MACvB;IACD;;IACDF,MAAM,GAAGV,OAAO,CAAC/7B,KAAD,EAAQs8B,UAAR,EAAoBC,QAApB,CAAhB;;IACA,IAAIG,QAAQ,KAAK,IAAb,IAAqBI,WAAW,EAApC,EAAwC;MACtCJ,QAAQ,GAAGV,OAAO,CAACh8B,KAAD,EAAQs8B,UAAR,CAAP,KAA+B,CAA/B,GAAmCz6B,CAAnC,GAAuC6zB,IAAlD;IACD;;IACD,IAAIgH,QAAQ,KAAK,IAAb,IAAqBK,UAAU,EAAnC,EAAuC;MACrCv1B,MAAM,CAACtC,IAAP,CAAYg3B,gBAAgB,CAAC;QAACryB,KAAK,EAAE6yB,QAAR;QAAkB5yB,GAAG,EAAEjI,CAAvB;QAA0ByzB,IAA1B;QAAgC/mB,KAAhC;QAAuC0V;MAAvC,CAAD,CAA5B;MACAyY,QAAQ,GAAG,IAAX;IACD;;IACDhH,IAAI,GAAG7zB,CAAP;IACA86B,SAAS,GAAG38B,KAAZ;EACD;;EACD,IAAI08B,QAAQ,KAAK,IAAjB,EAAuB;IACrBl1B,MAAM,CAACtC,IAAP,CAAYg3B,gBAAgB,CAAC;MAACryB,KAAK,EAAE6yB,QAAR;MAAkB5yB,GAAlB;MAAuBwrB,IAAvB;MAA6B/mB,KAA7B;MAAoC0V;IAApC,CAAD,CAA5B;EACD;;EACD,OAAOzc,MAAP;AACD;;AACD,SAASw1B,cAAT,CAAwB5R,IAAxB,EAA8BiR,MAA9B,EAAsC;EACpC,MAAM70B,MAAM,GAAG,EAAf;EACA,MAAMy1B,QAAQ,GAAG7R,IAAI,CAAC6R,QAAtB;;EACA,KAAK,IAAIp7B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGo7B,QAAQ,CAACj7B,MAA7B,EAAqCH,CAAC,EAAtC,EAA0C;IACxC,MAAMq7B,GAAG,GAAGV,aAAa,CAACS,QAAQ,CAACp7B,CAAD,CAAT,EAAcupB,IAAI,CAAChd,MAAnB,EAA2BiuB,MAA3B,CAAzB;;IACA,IAAIa,GAAG,CAACl7B,MAAR,EAAgB;MACdwF,MAAM,CAACtC,IAAP,CAAY,GAAGg4B,GAAf;IACD;EACF;;EACD,OAAO11B,MAAP;AACD;;AACD,SAAS21B,eAAT,CAAyB/uB,MAAzB,EAAiCG,KAAjC,EAAwC+mB,IAAxC,EAA8CE,QAA9C,EAAwD;EACtD,IAAI3rB,KAAK,GAAG,CAAZ;EACA,IAAIC,GAAG,GAAGyE,KAAK,GAAG,CAAlB;;EACA,IAAI+mB,IAAI,IAAI,CAACE,QAAb,EAAuB;IACrB,OAAO3rB,KAAK,GAAG0E,KAAR,IAAiB,CAACH,MAAM,CAACvE,KAAD,CAAN,CAAcmpB,IAAvC,EAA6C;MAC3CnpB,KAAK;IACN;EACF;;EACD,OAAOA,KAAK,GAAG0E,KAAR,IAAiBH,MAAM,CAACvE,KAAD,CAAN,CAAcmpB,IAAtC,EAA4C;IAC1CnpB,KAAK;EACN;;EACDA,KAAK,IAAI0E,KAAT;;EACA,IAAI+mB,IAAJ,EAAU;IACRxrB,GAAG,IAAID,KAAP;EACD;;EACD,OAAOC,GAAG,GAAGD,KAAN,IAAeuE,MAAM,CAACtE,GAAG,GAAGyE,KAAP,CAAN,CAAoBykB,IAA1C,EAAgD;IAC9ClpB,GAAG;EACJ;;EACDA,GAAG,IAAIyE,KAAP;EACA,OAAO;IAAC1E,KAAD;IAAQC;EAAR,CAAP;AACD;;AACD,SAASszB,aAAT,CAAuBhvB,MAAvB,EAA+BvE,KAA/B,EAAsCtB,GAAtC,EAA2C+sB,IAA3C,EAAiD;EAC/C,MAAM/mB,KAAK,GAAGH,MAAM,CAACpM,MAArB;EACA,MAAMwF,MAAM,GAAG,EAAf;EACA,IAAIuD,IAAI,GAAGlB,KAAX;EACA,IAAI6rB,IAAI,GAAGtnB,MAAM,CAACvE,KAAD,CAAjB;EACA,IAAIC,GAAJ;;EACA,KAAKA,GAAG,GAAGD,KAAK,GAAG,CAAnB,EAAsBC,GAAG,IAAIvB,GAA7B,EAAkC,EAAEuB,GAApC,EAAyC;IACvC,MAAMuzB,GAAG,GAAGjvB,MAAM,CAACtE,GAAG,GAAGyE,KAAP,CAAlB;;IACA,IAAI8uB,GAAG,CAACrK,IAAJ,IAAYqK,GAAG,CAACC,IAApB,EAA0B;MACxB,IAAI,CAAC5H,IAAI,CAAC1C,IAAV,EAAgB;QACdsC,IAAI,GAAG,KAAP;QACA9tB,MAAM,CAACtC,IAAP,CAAY;UAAC2E,KAAK,EAAEA,KAAK,GAAG0E,KAAhB;UAAuBzE,GAAG,EAAE,CAACA,GAAG,GAAG,CAAP,IAAYyE,KAAxC;UAA+C+mB;QAA/C,CAAZ;QACAzrB,KAAK,GAAGkB,IAAI,GAAGsyB,GAAG,CAACC,IAAJ,GAAWxzB,GAAX,GAAiB,IAAhC;MACD;IACF,CAND,MAMO;MACLiB,IAAI,GAAGjB,GAAP;;MACA,IAAI4rB,IAAI,CAAC1C,IAAT,EAAe;QACbnpB,KAAK,GAAGC,GAAR;MACD;IACF;;IACD4rB,IAAI,GAAG2H,GAAP;EACD;;EACD,IAAItyB,IAAI,KAAK,IAAb,EAAmB;IACjBvD,MAAM,CAACtC,IAAP,CAAY;MAAC2E,KAAK,EAAEA,KAAK,GAAG0E,KAAhB;MAAuBzE,GAAG,EAAEiB,IAAI,GAAGwD,KAAnC;MAA0C+mB;IAA1C,CAAZ;EACD;;EACD,OAAO9tB,MAAP;AACD;;AACD,SAAS+1B,gBAAT,CAA0BnS,IAA1B,EAAgCoS,cAAhC,EAAgD;EAC9C,MAAMpvB,MAAM,GAAGgd,IAAI,CAAChd,MAApB;EACA,MAAMonB,QAAQ,GAAGpK,IAAI,CAAChoB,OAAL,CAAaoyB,QAA9B;EACA,MAAMjnB,KAAK,GAAGH,MAAM,CAACpM,MAArB;;EACA,IAAI,CAACuM,KAAL,EAAY;IACV,OAAO,EAAP;EACD;;EACD,MAAM+mB,IAAI,GAAG,CAAC,CAAClK,IAAI,CAACqS,KAApB;EACA,MAAM;IAAC5zB,KAAD;IAAQC;EAAR,IAAeqzB,eAAe,CAAC/uB,MAAD,EAASG,KAAT,EAAgB+mB,IAAhB,EAAsBE,QAAtB,CAApC;;EACA,IAAIA,QAAQ,KAAK,IAAjB,EAAuB;IACrB,OAAOkI,aAAa,CAACtS,IAAD,EAAO,CAAC;MAACvhB,KAAD;MAAQC,GAAR;MAAawrB;IAAb,CAAD,CAAP,EAA6BlnB,MAA7B,EAAqCovB,cAArC,CAApB;EACD;;EACD,MAAMj1B,GAAG,GAAGuB,GAAG,GAAGD,KAAN,GAAcC,GAAG,GAAGyE,KAApB,GAA4BzE,GAAxC;EACA,MAAM6zB,YAAY,GAAG,CAAC,CAACvS,IAAI,CAACwS,SAAP,IAAoB/zB,KAAK,KAAK,CAA9B,IAAmCC,GAAG,KAAKyE,KAAK,GAAG,CAAxE;EACA,OAAOmvB,aAAa,CAACtS,IAAD,EAAOgS,aAAa,CAAChvB,MAAD,EAASvE,KAAT,EAAgBtB,GAAhB,EAAqBo1B,YAArB,CAApB,EAAwDvvB,MAAxD,EAAgEovB,cAAhE,CAApB;AACD;;AACD,SAASE,aAAT,CAAuBtS,IAAvB,EAA6B6R,QAA7B,EAAuC7uB,MAAvC,EAA+CovB,cAA/C,EAA+D;EAC7D,IAAI,CAACA,cAAD,IAAmB,CAACA,cAAc,CAAC5M,UAAnC,IAAiD,CAACxiB,MAAtD,EAA8D;IAC5D,OAAO6uB,QAAP;EACD;;EACD,OAAOY,eAAe,CAACzS,IAAD,EAAO6R,QAAP,EAAiB7uB,MAAjB,EAAyBovB,cAAzB,CAAtB;AACD;;AACD,SAASK,eAAT,CAAyBzS,IAAzB,EAA+B6R,QAA/B,EAAyC7uB,MAAzC,EAAiDovB,cAAjD,EAAiE;EAC/D,MAAMM,YAAY,GAAG1S,IAAI,CAAC2S,MAAL,CAAY7V,UAAZ,EAArB;;EACA,MAAM8V,SAAS,GAAGC,SAAS,CAAC7S,IAAI,CAAChoB,OAAN,CAA3B;EACA,MAAM;IAAC86B,aAAa,EAAE37B,YAAhB;IAA8Ba,OAAO,EAAE;MAACoyB;IAAD;EAAvC,IAAqDpK,IAA3D;EACA,MAAM7c,KAAK,GAAGH,MAAM,CAACpM,MAArB;EACA,MAAMwF,MAAM,GAAG,EAAf;EACA,IAAI22B,SAAS,GAAGH,SAAhB;EACA,IAAIn0B,KAAK,GAAGozB,QAAQ,CAAC,CAAD,CAAR,CAAYpzB,KAAxB;EACA,IAAIhI,CAAC,GAAGgI,KAAR;;EACA,SAASu0B,QAAT,CAAkBp0B,CAAlB,EAAqBhE,CAArB,EAAwBoM,CAAxB,EAA2BisB,EAA3B,EAA+B;IAC7B,MAAMC,GAAG,GAAG9I,QAAQ,GAAG,CAAC,CAAJ,GAAQ,CAA5B;;IACA,IAAIxrB,CAAC,KAAKhE,CAAV,EAAa;MACX;IACD;;IACDgE,CAAC,IAAIuE,KAAL;;IACA,OAAOH,MAAM,CAACpE,CAAC,GAAGuE,KAAL,CAAN,CAAkBykB,IAAzB,EAA+B;MAC7BhpB,CAAC,IAAIs0B,GAAL;IACD;;IACD,OAAOlwB,MAAM,CAACpI,CAAC,GAAGuI,KAAL,CAAN,CAAkBykB,IAAzB,EAA+B;MAC7BhtB,CAAC,IAAIs4B,GAAL;IACD;;IACD,IAAIt0B,CAAC,GAAGuE,KAAJ,KAAcvI,CAAC,GAAGuI,KAAtB,EAA6B;MAC3B/G,MAAM,CAACtC,IAAP,CAAY;QAAC2E,KAAK,EAAEG,CAAC,GAAGuE,KAAZ;QAAmBzE,GAAG,EAAE9D,CAAC,GAAGuI,KAA5B;QAAmC+mB,IAAI,EAAEljB,CAAzC;QAA4C6R,KAAK,EAAEoa;MAAnD,CAAZ;MACAF,SAAS,GAAGE,EAAZ;MACAx0B,KAAK,GAAG7D,CAAC,GAAGuI,KAAZ;IACD;EACF;;EACD,KAAK,MAAM6tB,OAAX,IAAsBa,QAAtB,EAAgC;IAC9BpzB,KAAK,GAAG2rB,QAAQ,GAAG3rB,KAAH,GAAWuyB,OAAO,CAACvyB,KAAnC;IACA,IAAI6rB,IAAI,GAAGtnB,MAAM,CAACvE,KAAK,GAAG0E,KAAT,CAAjB;IACA,IAAI0V,KAAJ;;IACA,KAAKpiB,CAAC,GAAGgI,KAAK,GAAG,CAAjB,EAAoBhI,CAAC,IAAIu6B,OAAO,CAACtyB,GAAjC,EAAsCjI,CAAC,EAAvC,EAA2C;MACzC,MAAMmzB,EAAE,GAAG5mB,MAAM,CAACvM,CAAC,GAAG0M,KAAL,CAAjB;MACA0V,KAAK,GAAGga,SAAS,CAACT,cAAc,CAAC5M,UAAf,CAA0BnC,aAAa,CAACqP,YAAD,EAAe;QACtE39B,IAAI,EAAE,SADgE;QAEtEo+B,EAAE,EAAE7I,IAFkE;QAGtE7gB,EAAE,EAAEmgB,EAHkE;QAItEwJ,WAAW,EAAE,CAAC38B,CAAC,GAAG,CAAL,IAAU0M,KAJ+C;QAKtEkwB,WAAW,EAAE58B,CAAC,GAAG0M,KALqD;QAMtEhM;MANsE,CAAf,CAAvC,CAAD,CAAjB;;MAQA,IAAIm8B,YAAY,CAACza,KAAD,EAAQka,SAAR,CAAhB,EAAoC;QAClCC,QAAQ,CAACv0B,KAAD,EAAQhI,CAAC,GAAG,CAAZ,EAAeu6B,OAAO,CAAC9G,IAAvB,EAA6B6I,SAA7B,CAAR;MACD;;MACDzI,IAAI,GAAGV,EAAP;MACAmJ,SAAS,GAAGla,KAAZ;IACD;;IACD,IAAIpa,KAAK,GAAGhI,CAAC,GAAG,CAAhB,EAAmB;MACjBu8B,QAAQ,CAACv0B,KAAD,EAAQhI,CAAC,GAAG,CAAZ,EAAeu6B,OAAO,CAAC9G,IAAvB,EAA6B6I,SAA7B,CAAR;IACD;EACF;;EACD,OAAO32B,MAAP;AACD;;AACD,SAASy2B,SAAT,CAAmB76B,OAAnB,EAA4B;EAC1B,OAAO;IACLigB,eAAe,EAAEjgB,OAAO,CAACigB,eADpB;IAELsb,cAAc,EAAEv7B,OAAO,CAACu7B,cAFnB;IAGLC,UAAU,EAAEx7B,OAAO,CAACw7B,UAHf;IAILC,gBAAgB,EAAEz7B,OAAO,CAACy7B,gBAJrB;IAKLC,eAAe,EAAE17B,OAAO,CAAC07B,eALpB;IAMLpV,WAAW,EAAEtmB,OAAO,CAACsmB,WANhB;IAOLpG,WAAW,EAAElgB,OAAO,CAACkgB;EAPhB,CAAP;AASD;;AACD,SAASob,YAAT,CAAsBza,KAAtB,EAA6Bka,SAA7B,EAAwC;EACtC,OAAOA,SAAS,IAAI7D,IAAI,CAACC,SAAL,CAAetW,KAAf,MAA0BqW,IAAI,CAACC,SAAL,CAAe4D,SAAf,CAA9C;AACD;;AAED,SAASvU,cAAc,IAAImV,CAA3B,EAA8Bx3B,UAAU,IAAIoL,CAA5C,EAA+C9R,eAAe,IAAI+R,CAAlE,EAAqEvR,QAAQ,IAAIwR,CAAjF,EAAoFsb,SAAS,IAAIrb,CAAjG,EAAoGzI,WAAW,IAAI0I,CAAnH,EAAsHrK,SAAS,IAAIsK,CAAnI,EAAsI0T,YAAY,IAAIxQ,CAAtJ,EAAyJ1P,OAAO,IAAI2P,CAApK,EAAuK7L,WAAW,IAAI8L,CAAtL,EAAyLwR,WAAW,IAAIvR,CAAxM,EAA2MuX,SAAS,IAAI3X,CAAxN,EAA2NiU,QAAQ,IAAIlU,CAAvO,EAA0O8U,UAAU,IAAI/U,CAAxP,EAA2PqU,UAAU,IAAItU,CAAzQ,EAA4Q+X,MAAM,IAAIhY,CAAtR,EAAyR5P,EAAE,IAAI2P,CAA/R,EAAkSlU,IAAI,IAAIiU,CAA1S,EAA6SjI,kBAAkB,IAAIgI,CAAnU,EAAsU9H,cAAc,IAAI6H,CAAxV,EAA2VtP,GAAG,IAAIqP,CAAlW,EAAqWqN,SAAS,IAAItN,CAAlX,EAAqXhS,KAAK,IAAI+R,CAA9X,EAAiYnQ,WAAW,IAAIkQ,CAAhZ,EAAmZqiB,mBAAmB,IAAItiB,CAA1a,EAA6apK,aAAa,IAAImK,CAA9b,EAAicrK,YAAY,IAAIoK,CAAjd,EAAod/I,YAAY,IAAI6yB,CAApe,EAAuejR,OAAO,IAAIroB,CAAlf,EAAqfunB,YAAY,IAAIgS,EAArgB,EAAygBn2B,iBAAiB,IAAI5G,EAA9hB,EAAkiBu2B,cAAc,IAAIt2B,EAApjB,EAAwjB2zB,cAAc,IAAIoJ,EAA1kB,EAA8kBvF,YAAY,IAAIwF,EAA9lB,EAAkmBnyB,SAAS,IAAIoyB,EAA/mB,EAAmnB9F,4BAA4B,IAAI+F,EAAnpB,EAAupBzJ,eAAe,IAAI0J,EAA1qB,EAA8qBxc,WAAW,IAAIyc,EAA7rB,EAAisB/5B,UAAU,IAAIg6B,EAA/sB,EAAmtBpP,cAAc,IAAIqP,EAAruB,EAAyuBnE,aAAa,IAAIoE,EAA1vB,EAA8vBnE,qBAAqB,IAAIoE,EAAvxB,EAA2xB9xB,MAAM,IAAI+xB,EAAryB,EAAyyB/D,oBAAoB,IAAIgE,EAAj0B,EAAq0BtX,eAAe,IAAIuX,EAAx1B,EAA41BlgC,IAAI,IAAImgC,EAAp2B,EAAw2Bx2B,qBAAqB,IAAIy2B,EAAj4B,EAAq4B73B,kBAAkB,IAAI83B,EAA35B,EAA+5Bp5B,OAAO,IAAIq5B,EAA16B,EAA86Bj4B,WAAW,IAAIk4B,EAA77B,EAAi8Bl5B,YAAY,IAAIm5B,EAAj9B,EAAq9Bx3B,cAAc,IAAIy3B,EAAv+B,EAA2+BnZ,YAAY,IAAIoZ,EAA3/B,EAA+/Br1B,cAAc,IAAIs1B,EAAjhC,EAAqhC/1B,OAAO,IAAIg2B,EAAhiC,EAAoiC7d,mBAAmB,IAAI8d,EAA3jC,EAA+jC7d,aAAa,IAAI8d,EAAhlC,EAAolCj+B,OAAO,IAAIk+B,EAA/lC,EAAmmCx9B,OAAO,IAAIy9B,EAA9mC,EAAknCj9B,SAAS,IAAIk9B,EAA/nC,EAAmoCh9B,WAAW,IAAIi9B,EAAlpC,EAAspCj8B,SAAS,IAAIk8B,EAAnqC,EAAuqCta,YAAY,IAAIua,EAAvrC,EAA2rC9N,WAAW,IAAI+N,EAA1sC,EAA8sCpM,mBAAmB,IAAIqM,EAAruC,EAAyuC1K,QAAQ,IAAI2K,EAArvC,EAAyvC10B,UAAU,IAAI20B,EAAvwC,EAA2wCzS,eAAe,IAAI0S,EAA9xC,EAAkyCle,YAAY,IAAIme,EAAlzC,EAAszC59B,OAAO,IAAI69B,EAAj0C,EAAq0C1hC,GAAG,IAAI2hC,EAA50C,EAAg1Cn0B,QAAQ,IAAIo0B,EAA51C,EAAg2C1I,WAAW,IAAI2I,EAA/2C,EAAm3C1Z,WAAW,IAAI2Z,EAAl4C,EAAs4Cl8B,SAAS,IAAIm8B,EAAn5C,EAAu5C3/B,cAAc,IAAI4/B,EAAz6C,EAA66C97B,aAAa,IAAI+7B,EAA97C,EAAk8Cv3B,UAAU,IAAIw3B,EAAh9C,EAAo9C1U,iBAAiB,IAAI2U,EAAz+C,EAA6+C3M,0BAA0B,IAAI4M,EAA3gD,EAA+gD1E,gBAAgB,IAAI2E,EAAniD,EAAuiDlF,cAAc,IAAImF,EAAzjD,EAA6jDtI,qBAAqB,IAAIuI,EAAtlD,EAA0lDtI,oBAAoB,IAAIuI,EAAlnD,EAAsnDzI,YAAY,IAAI0I,EAAtoD,EAA0oDjY,cAAc,IAAIkY,EAA5pD,EAAgqD/X,cAAc,IAAIgY,EAAlrD,EAAsrDla,SAAS,IAAIma,EAAnsD,EAAusDhW,kBAAkB,IAAIiW,EAA7tD,EAAiuDhV,MAAM,IAAIiV,EAA3uD,EAA+uDhV,aAAa,IAAIiV,EAAhwD,EAAowDpG,aAAa,IAAIqG,EAArxD,EAAyxDl5B,eAAe,IAAIm5B,EAA5yD,EAAgzD7iC,OAAO,IAAI0F,CAA3zD,EAA8zDS,KAAK,IAAI28B,EAAv0D,EAA20D18B,QAAQ,IAAI28B,EAAv1D,EAA21Dz8B,WAAW,IAAI08B,EAA12D,EAA82Dx8B,UAAU,IAAIy8B,EAA53D,EAAg4Dx8B,aAAa,IAAIy8B,EAAj5D,EAAq5Dz5B,UAAU,IAAI05B,EAAn6D,EAAu6DzhB,KAAK,IAAI1O,CAAh7D,EAAm7DmT,QAAQ,IAAInU,CAA/7D,EAAk8DjC,OAAO,IAAIhK,CAA78D,EAAg9DvB,gBAAgB,IAAIyO,CAAp+D,EAAu+DxS,cAAc,IAAI+S,CAAz/D,EAA4/Dgb,aAAa,IAAIpc,CAA7gE,EAAghE5R,QAAQ,IAAIoB,CAA5hE,EAA+hE0D,OAAO,IAAIua,CAA1iE,EAA6iE/f,aAAa,IAAIgD,CAA9jE,EAAikEqI,iBAAiB,IAAIgH,CAAtlE,EAAylEpR,YAAY,IAAIgR,CAAzmE,EAA4mE5Q,WAAW,IAAIyG,CAA3nE,EAA8nE8yB,YAAY,IAAIp2B,CAA9oE,EAAipEqF,aAAa,IAAIf,CAAlqE,EAAqqEqF,gCAAgC,IAAIm1B,CAAzsE,EAA4sEx2B,gBAAgB,IAAI2G,CAAhuE,EAAmuE5M,IAAI,IAAIoD,CAA3uE,EAA8uExB,SAAS,IAAIoH,CAA3vE,EAA8vE5D,mBAAmB,IAAIs3B,CAArxE,EAAwxEviC,cAAc,IAAIsD,CAA1yE,EAA6yE2K,mBAAmB,IAAIkF,CAAp0E,EAAu0EtM,QAAQ,IAAItD,CAAn1E,EAAs1EouB,2BAA2B,IAAIluB,CAAr3E,EAAw3EmC,KAAK,IAAI48B,CAAj4E"}, "metadata": {}, "sourceType": "module"}