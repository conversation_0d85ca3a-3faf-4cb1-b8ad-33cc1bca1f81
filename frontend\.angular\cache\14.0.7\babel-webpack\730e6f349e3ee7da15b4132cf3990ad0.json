{"ast": null, "code": "export function isFunction(value) {\n  return typeof value === 'function';\n}", "map": {"version": 3, "names": ["isFunction", "value"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isFunction.js"], "sourcesContent": ["export function isFunction(value) {\n    return typeof value === 'function';\n}\n"], "mappings": "AAAA,OAAO,SAASA,UAAT,CAAoBC,KAApB,EAA2B;EAC9B,OAAO,OAAOA,KAAP,KAAiB,UAAxB;AACH"}, "metadata": {}, "sourceType": "module"}