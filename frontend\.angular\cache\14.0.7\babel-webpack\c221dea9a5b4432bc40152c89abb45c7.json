{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const EmptyError = createErrorClass(_super => function EmptyErrorImpl() {\n  _super(this);\n\n  this.name = 'EmptyError';\n  this.message = 'no elements in sequence';\n});", "map": {"version": 3, "names": ["createErrorClass", "EmptyError", "_super", "EmptyErrorImpl", "name", "message"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/EmptyError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const EmptyError = createErrorClass((_super) => function EmptyErrorImpl() {\n    _super(this);\n    this.name = 'EmptyError';\n    this.message = 'no elements in sequence';\n});\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,oBAAjC;AACA,OAAO,MAAMC,UAAU,GAAGD,gBAAgB,CAAEE,MAAD,IAAY,SAASC,cAAT,GAA0B;EAC7ED,MAAM,CAAC,IAAD,CAAN;;EACA,KAAKE,IAAL,GAAY,YAAZ;EACA,KAAKC,OAAL,GAAe,yBAAf;AACH,CAJyC,CAAnC"}, "metadata": {}, "sourceType": "module"}