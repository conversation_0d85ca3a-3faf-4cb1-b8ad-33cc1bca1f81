<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mock Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Formation Management - Mock Authentication Test</h1>
    
    <div class="section">
        <h3>1. Test Backend Connection</h3>
        <button onclick="testBackendConnection()">Test Backend</button>
        <div id="connectionResult"></div>
    </div>

    <div class="section">
        <h3>2. Test Mock Authentication</h3>
        <div>
            <input type="email" id="username" placeholder="Username" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="admin123">
            <button onclick="testMockLogin()">Mock Login</button>
        </div>
        <div id="loginResult"></div>
    </div>

    <div class="section">
        <h3>3. Test Different User Roles</h3>
        <button onclick="loginAs('<EMAIL>', 'admin123')">Login as Admin</button>
        <button onclick="loginAs('<EMAIL>', 'trainer123')">Login as Trainer</button>
        <button onclick="loginAs('<EMAIL>', 'employee123')">Login as Employee</button>
        <div id="roleResult"></div>
    </div>

    <div class="section">
        <h3>4. Test Mock Config</h3>
        <button onclick="testMockConfig()">Get Mock Config</button>
        <div id="configResult"></div>
    </div>

    <div class="section">
        <h3>5. Test Protected Routes with Token</h3>
        <button onclick="testProtectedRoutes()">Test Protected API</button>
        <div id="protectedResult"></div>
    </div>

    <div class="section">
        <h3>6. Test Database Content (Unprotected)</h3>
        <button onclick="testDatabaseContent()">Check Database</button>
        <div id="databaseResult"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:8000/api';
        let currentToken = null;

        async function testBackendConnection() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<div>Testing backend connection...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/auth/config`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">✅ Backend connection successful</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Backend responded with status: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Connection failed: ${error.message}</div>`;
            }
        }

        async function testMockLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '<div>Testing mock login...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/auth/mock/login`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    currentToken = data.data.access_token;
                    resultDiv.innerHTML = `
                        <div class="success">✅ Mock login successful</div>
                        <div><strong>Token saved for testing protected routes</strong></div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Mock login failed: ${data.message}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Request failed: ${error.message}</div>`;
            }
        }

        async function loginAs(username, password) {
            const resultDiv = document.getElementById('roleResult');
            
            resultDiv.innerHTML = `<div>Testing login as ${username}...</div>`;
            
            try {
                const response = await fetch(`${baseUrl}/auth/mock/login`, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    currentToken = data.data.access_token;
                    resultDiv.innerHTML = `
                        <div class="success">✅ Login successful as ${data.data.user.name}</div>
                        <div><strong>Roles:</strong> ${data.data.user.roles.join(', ')}</div>
                        <div><strong>Team:</strong> ${data.data.user.team}</div>
                        <div><strong>Token saved for testing</strong></div>
                        <pre>${JSON.stringify(data.data.user, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">❌ Login failed: ${data.message}</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Request failed: ${error.message}</div>`;
            }
        }

        async function testMockConfig() {
            const resultDiv = document.getElementById('configResult');
            resultDiv.innerHTML = '<div>Testing mock config...</div>';
            
            try {
                const response = await fetch(`${baseUrl}/auth/mock/config`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✅ Mock config retrieved successfully</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to get config</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Request failed: ${error.message}</div>`;
            }
        }

        async function testProtectedRoutes() {
            const resultDiv = document.getElementById('protectedResult');

            if (!currentToken) {
                resultDiv.innerHTML = '<div class="error">❌ Please login first to get a token</div>';
                return;
            }

            resultDiv.innerHTML = '<div>Testing protected routes with token...</div>';

            const tests = [
                { name: 'Users', url: `${baseUrl}/users` },
                { name: 'Teams', url: `${baseUrl}/teams` },
                { name: 'Formations', url: `${baseUrl}/formations` },
                { name: 'Statistics', url: `${baseUrl}/statistics/dashboard` }
            ];

            let results = [];

            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${currentToken}`
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results.push(`✅ ${test.name}: ${Array.isArray(data) ? data.length + ' items' : 'Success'}`);
                    } else {
                        results.push(`❌ ${test.name}: HTTP ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${test.name}: ${error.message}`);
                }
            }

            resultDiv.innerHTML = `
                <div class="success">Protected Routes Test Results:</div>
                <ul>${results.map(r => `<li>${r}</li>`).join('')}</ul>
            `;
        }

        async function testDatabaseContent() {
            const resultDiv = document.getElementById('databaseResult');
            resultDiv.innerHTML = '<div>Testing database content (unprotected routes)...</div>';

            const tests = [
                { name: 'Users', url: `${baseUrl}/test/users` },
                { name: 'Teams', url: `${baseUrl}/test/teams` },
                { name: 'Formations', url: `${baseUrl}/test/formations` },
                { name: 'Statistics', url: `${baseUrl}/test/statistics/dashboard` }
            ];

            let results = [];

            for (const test of tests) {
                try {
                    const response = await fetch(test.url, {
                        method: 'GET',
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        results.push({
                            name: test.name,
                            success: true,
                            count: Array.isArray(data) ? data.length : 'Object',
                            sample: Array.isArray(data) && data.length > 0 ? data[0] : data
                        });
                    } else {
                        results.push({
                            name: test.name,
                            success: false,
                            error: `HTTP ${response.status}`
                        });
                    }
                } catch (error) {
                    results.push({
                        name: test.name,
                        success: false,
                        error: error.message
                    });
                }
            }

            let html = '<div class="success">Database Content Test Results:</div>';
            results.forEach(result => {
                if (result.success) {
                    html += `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd;">
                            <strong>✅ ${result.name}:</strong> ${result.count} items
                            <details>
                                <summary>Sample data</summary>
                                <pre>${JSON.stringify(result.sample, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    html += `<div class="error">❌ ${result.name}: ${result.error}</div>`;
                }
            });

            resultDiv.innerHTML = html;
        }

        // Auto-test backend connection on page load
        window.onload = function() {
            testBackendConnection();
        };
    </script>
</body>
</html>
