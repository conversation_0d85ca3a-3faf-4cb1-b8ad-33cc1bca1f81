{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n  return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}", "map": {"version": 3, "names": ["isFunction", "isAsyncIterable", "obj", "Symbol", "asyncIterator"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/util/isAsyncIterable.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n    return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}\n"], "mappings": "AAAA,SAASA,UAAT,QAA2B,cAA3B;AACA,OAAO,SAASC,eAAT,CAAyBC,GAAzB,EAA8B;EACjC,OAAOC,MAAM,CAACC,aAAP,IAAwBJ,UAAU,CAACE,GAAG,KAAK,IAAR,IAAgBA,GAAG,KAAK,KAAK,CAA7B,GAAiC,KAAK,CAAtC,GAA0CA,GAAG,CAACC,MAAM,CAACC,aAAR,CAA9C,CAAzC;AACH"}, "metadata": {}, "sourceType": "module"}