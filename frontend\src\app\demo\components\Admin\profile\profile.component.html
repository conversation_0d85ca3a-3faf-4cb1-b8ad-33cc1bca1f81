<div class="grid">
    <div class="col-12">
        <div class="card">
            <h5>Admin Profile</h5>
            
            <!-- Loading State -->
            <div *ngIf="loading" class="flex align-items-center justify-content-center" style="height: 200px;">
                <i class="pi pi-spin pi-spinner" style="font-size: 2rem;"></i>
                <span class="ml-2">Loading profile...</span>
            </div>

            <!-- Profile Content -->
            <div *ngIf="!loading && userProfile" class="grid">
                <!-- Profile Header -->
                <div class="col-12">
                    <div class="flex align-items-center mb-4">
                        <div class="flex align-items-center justify-content-center bg-blue-100 border-round mr-3" 
                             [ngStyle]="{width: '4rem', height: '4rem'}">
                            <i class="pi pi-user text-blue-500 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="m-0 text-900">{{userProfile.first_name}} {{userProfile.last_name}}</h3>
                            <p class="m-0 text-600">Administrator</p>
                        </div>
                    </div>
                </div>

                <!-- Profile Information -->
                <div class="col-12 md:col-6">
                    <div class="card">
                        <h6>Personal Information</h6>
                        <div class="grid">
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">First Name</label>
                                <input pInputText 
                                       [(ngModel)]="userProfile.first_name" 
                                       [disabled]="!editMode"
                                       class="w-full" />
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Last Name</label>
                                <input pInputText 
                                       [(ngModel)]="userProfile.last_name" 
                                       [disabled]="!editMode"
                                       class="w-full" />
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Email</label>
                                <input pInputText 
                                       [(ngModel)]="userProfile.email" 
                                       [disabled]="!editMode"
                                       class="w-full" />
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Phone</label>
                                <input pInputText 
                                       [(ngModel)]="userProfile.phone" 
                                       [disabled]="!editMode"
                                       placeholder="Enter phone number"
                                       class="w-full" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="col-12 md:col-6">
                    <div class="card">
                        <h6>Account Information</h6>
                        <div class="grid">
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Role</label>
                                <input pInputText 
                                       value="Administrator" 
                                       disabled
                                       class="w-full" />
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Status</label>
                                <span class="p-tag p-tag-success">Active</span>
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Member Since</label>
                                <input pInputText 
                                       [value]="userProfile.created_at | date:'mediumDate'" 
                                       disabled
                                       class="w-full" />
                            </div>
                            <div class="col-12">
                                <label class="block text-900 font-medium mb-2">Last Updated</label>
                                <input pInputText 
                                       [value]="userProfile.updated_at | date:'medium'" 
                                       disabled
                                       class="w-full" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="col-12">
                    <div class="card">
                        <div class="flex justify-content-between align-items-center">
                            <h6>Actions</h6>
                            <div>
                                <button *ngIf="!editMode" 
                                        pButton pRipple 
                                        label="Edit Profile" 
                                        icon="pi pi-pencil" 
                                        class="p-button-outlined mr-2"
                                        (click)="toggleEditMode()">
                                </button>
                                <button *ngIf="editMode" 
                                        pButton pRipple 
                                        label="Save Changes" 
                                        icon="pi pi-check" 
                                        class="p-button-success mr-2"
                                        (click)="saveProfile()"
                                        [loading]="saving">
                                </button>
                                <button *ngIf="editMode" 
                                        pButton pRipple 
                                        label="Cancel" 
                                        icon="pi pi-times" 
                                        class="p-button-secondary"
                                        (click)="cancelEdit()">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="col-12">
                    <div class="card">
                        <h6>Admin Statistics</h6>
                        <div class="grid">
                            <div class="col-12 md:col-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-500">{{stats.totalFormations}}</div>
                                    <div class="text-600">Total Formations</div>
                                </div>
                            </div>
                            <div class="col-12 md:col-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-500">{{stats.totalEmployees}}</div>
                                    <div class="text-600">Total Employees</div>
                                </div>
                            </div>
                            <div class="col-12 md:col-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-orange-500">{{stats.totalTrainers}}</div>
                                    <div class="text-600">Total Trainers</div>
                                </div>
                            </div>
                            <div class="col-12 md:col-3">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-500">{{stats.totalTeams}}</div>
                                    <div class="text-600">Total Teams</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Error State -->
            <div *ngIf="!loading && !userProfile" class="text-center p-4">
                <i class="pi pi-exclamation-triangle text-orange-500 text-4xl mb-3"></i>
                <h5>Unable to load profile</h5>
                <p class="text-600">Please try refreshing the page or contact support.</p>
                <button pButton pRipple 
                        label="Retry" 
                        icon="pi pi-refresh" 
                        (click)="loadProfile()">
                </button>
            </div>
        </div>
    </div>
</div>

<p-toast></p-toast>
