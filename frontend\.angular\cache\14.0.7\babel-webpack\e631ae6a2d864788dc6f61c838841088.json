{"ast": null, "code": "import { from } from './from';\nexport function pairs(obj, scheduler) {\n  return from(Object.entries(obj), scheduler);\n}", "map": {"version": 3, "names": ["from", "pairs", "obj", "scheduler", "Object", "entries"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/pairs.js"], "sourcesContent": ["import { from } from './from';\nexport function pairs(obj, scheduler) {\n    return from(Object.entries(obj), scheduler);\n}\n"], "mappings": "AAAA,SAASA,IAAT,QAAqB,QAArB;AACA,OAAO,SAASC,KAAT,CAAeC,GAAf,EAAoBC,SAApB,EAA+B;EAClC,OAAOH,IAAI,CAACI,MAAM,CAACC,OAAP,CAAeH,GAAf,CAAD,EAAsBC,SAAtB,CAAX;AACH"}, "metadata": {}, "sourceType": "module"}