{"ast": null, "code": "import getCurrentScriptSource from \"./getCurrentScriptSource.js\";\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\n\nfunction parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var options = {};\n\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      options[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {// URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n\n    if (scriptSourceURL) {\n      options = scriptSourceURL;\n      options.fromCurrentScript = true;\n    }\n  }\n\n  return options;\n}\n\nexport default parseURL;", "map": {"version": 3, "names": ["getCurrentScriptSource", "parseURL", "resourceQuery", "options", "searchParams", "slice", "split", "i", "length", "pair", "decodeURIComponent", "scriptSource", "scriptSourceURL", "URL", "self", "location", "href", "error", "fromCurrentScript"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/webpack-dev-server/client/utils/parseURL.js"], "sourcesContent": ["import getCurrentScriptSource from \"./getCurrentScriptSource.js\";\n/**\n * @param {string} resourceQuery\n * @returns {{ [key: string]: string | boolean }}\n */\n\nfunction parseURL(resourceQuery) {\n  /** @type {{ [key: string]: string }} */\n  var options = {};\n\n  if (typeof resourceQuery === \"string\" && resourceQuery !== \"\") {\n    var searchParams = resourceQuery.slice(1).split(\"&\");\n\n    for (var i = 0; i < searchParams.length; i++) {\n      var pair = searchParams[i].split(\"=\");\n      options[pair[0]] = decodeURIComponent(pair[1]);\n    }\n  } else {\n    // Else, get the url from the <script> this file was called with.\n    var scriptSource = getCurrentScriptSource();\n    var scriptSourceURL;\n\n    try {\n      // The placeholder `baseURL` with `window.location.href`,\n      // is to allow parsing of path-relative or protocol-relative URLs,\n      // and will have no effect if `scriptSource` is a fully valid URL.\n      scriptSourceURL = new URL(scriptSource, self.location.href);\n    } catch (error) {// URL parsing failed, do nothing.\n      // We will still proceed to see if we can recover using `resourceQuery`\n    }\n\n    if (scriptSourceURL) {\n      options = scriptSourceURL;\n      options.fromCurrentScript = true;\n    }\n  }\n\n  return options;\n}\n\nexport default parseURL;"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,6BAAnC;AACA;AACA;AACA;AACA;;AAEA,SAASC,QAAT,CAAkBC,aAAlB,EAAiC;EAC/B;EACA,IAAIC,OAAO,GAAG,EAAd;;EAEA,IAAI,OAAOD,aAAP,KAAyB,QAAzB,IAAqCA,aAAa,KAAK,EAA3D,EAA+D;IAC7D,IAAIE,YAAY,GAAGF,aAAa,CAACG,KAAd,CAAoB,CAApB,EAAuBC,KAAvB,CAA6B,GAA7B,CAAnB;;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,YAAY,CAACI,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;MAC5C,IAAIE,IAAI,GAAGL,YAAY,CAACG,CAAD,CAAZ,CAAgBD,KAAhB,CAAsB,GAAtB,CAAX;MACAH,OAAO,CAACM,IAAI,CAAC,CAAD,CAAL,CAAP,GAAmBC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArC;IACD;EACF,CAPD,MAOO;IACL;IACA,IAAIE,YAAY,GAAGX,sBAAsB,EAAzC;IACA,IAAIY,eAAJ;;IAEA,IAAI;MACF;MACA;MACA;MACAA,eAAe,GAAG,IAAIC,GAAJ,CAAQF,YAAR,EAAsBG,IAAI,CAACC,QAAL,CAAcC,IAApC,CAAlB;IACD,CALD,CAKE,OAAOC,KAAP,EAAc,CAAC;MACf;IACD;;IAED,IAAIL,eAAJ,EAAqB;MACnBT,OAAO,GAAGS,eAAV;MACAT,OAAO,CAACe,iBAAR,GAA4B,IAA5B;IACD;EACF;;EAED,OAAOf,OAAP;AACD;;AAED,eAAeF,QAAf"}, "metadata": {}, "sourceType": "module"}