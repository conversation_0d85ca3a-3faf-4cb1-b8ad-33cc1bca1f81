{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nfunction Avatar_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.label);\n  }\n}\n\nfunction Avatar_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r5.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-avatar-icon\");\n  }\n}\n\nfunction Avatar_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Avatar_ng_template_3_span_0_Template, 1, 3, \"span\", 5);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n\n    const _r3 = i0.ɵɵreference(6);\n\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.icon)(\"ngIfElse\", _r3);\n  }\n}\n\nfunction Avatar_ng_template_5_img_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 8);\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r6.image, i0.ɵɵsanitizeUrl);\n  }\n}\n\nfunction Avatar_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Avatar_ng_template_5_img_0_Template, 1, 1, \"img\", 7);\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.image);\n  }\n}\n\nconst _c0 = [\"*\"];\n\nclass Avatar {\n  constructor() {\n    this.size = \"normal\";\n    this.shape = \"square\";\n  }\n\n  containerClass() {\n    return {\n      'p-avatar p-component': true,\n      'p-avatar-image': this.image != null,\n      'p-avatar-circle': this.shape === 'circle',\n      'p-avatar-lg': this.size === 'large',\n      'p-avatar-xl': this.size === 'xlarge'\n    };\n  }\n\n}\n\nAvatar.ɵfac = function Avatar_Factory(t) {\n  return new (t || Avatar)();\n};\n\nAvatar.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Avatar,\n  selectors: [[\"p-avatar\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    label: \"label\",\n    icon: \"icon\",\n    image: \"image\",\n    size: \"size\",\n    shape: \"shape\",\n    style: \"style\",\n    styleClass: \"styleClass\"\n  },\n  ngContentSelectors: _c0,\n  decls: 7,\n  vars: 6,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-avatar-text\", 4, \"ngIf\", \"ngIfElse\"], [\"iconTemplate\", \"\"], [\"imageTemplate\", \"\"], [1, \"p-avatar-text\"], [3, \"class\", \"ngClass\", 4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\"], [3, \"src\", 4, \"ngIf\"], [3, \"src\"]],\n  template: function Avatar_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵprojection(1);\n      i0.ɵɵtemplate(2, Avatar_span_2_Template, 2, 1, \"span\", 1);\n      i0.ɵɵtemplate(3, Avatar_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵtemplate(5, Avatar_ng_template_5_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(4);\n\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.label)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle],\n  styles: [\".p-avatar{display:inline-flex;align-items:center;justify-content:center;width:2rem;height:2rem;font-size:1rem}.p-avatar.p-avatar-image{background-color:transparent}.p-avatar.p-avatar-circle{border-radius:50%;overflow:hidden}.p-avatar .p-avatar-icon{font-size:1rem}.p-avatar img{width:100%;height:100%}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Avatar, [{\n    type: Component,\n    args: [{\n      selector: 'p-avatar',\n      template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-avatar-text\" *ngIf=\"label; else iconTemplate\">{{label}}</span>\n            <ng-template #iconTemplate><span [class]=\"icon\" [ngClass]=\"'p-avatar-icon'\" *ngIf=\"icon; else imageTemplate\"></span></ng-template>\n            <ng-template #imageTemplate><img [src]=\"image\" *ngIf=\"image\"></ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-avatar{display:inline-flex;align-items:center;justify-content:center;width:2rem;height:2rem;font-size:1rem}.p-avatar.p-avatar-image{background-color:transparent}.p-avatar.p-avatar-circle{border-radius:50%;overflow:hidden}.p-avatar .p-avatar-icon{font-size:1rem}.p-avatar img{width:100%;height:100%}\\n\"]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    image: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    shape: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }]\n  });\n})();\n\nclass AvatarModule {}\n\nAvatarModule.ɵfac = function AvatarModule_Factory(t) {\n  return new (t || AvatarModule)();\n};\n\nAvatarModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AvatarModule\n});\nAvatarModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AvatarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Avatar],\n      declarations: [Avatar]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Avatar, AvatarModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "Avatar", "constructor", "size", "shape", "containerClass", "image", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "label", "icon", "style", "styleClass", "AvatarModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-avatar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass Avatar {\n    constructor() {\n        this.size = \"normal\";\n        this.shape = \"square\";\n    }\n    containerClass() {\n        return {\n            'p-avatar p-component': true,\n            'p-avatar-image': this.image != null,\n            'p-avatar-circle': this.shape === 'circle',\n            'p-avatar-lg': this.size === 'large',\n            'p-avatar-xl': this.size === 'xlarge'\n        };\n    }\n}\nAvatar.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Avatar, deps: [], target: i0.ɵɵFactoryTarget.Component });\nAvatar.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Avatar, selector: \"p-avatar\", inputs: { label: \"label\", icon: \"icon\", image: \"image\", size: \"size\", shape: \"shape\", style: \"style\", styleClass: \"styleClass\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-avatar-text\" *ngIf=\"label; else iconTemplate\">{{label}}</span>\n            <ng-template #iconTemplate><span [class]=\"icon\" [ngClass]=\"'p-avatar-icon'\" *ngIf=\"icon; else imageTemplate\"></span></ng-template>\n            <ng-template #imageTemplate><img [src]=\"image\" *ngIf=\"image\"></ng-template>\n        </div>\n    `, isInline: true, styles: [\".p-avatar{display:inline-flex;align-items:center;justify-content:center;width:2rem;height:2rem;font-size:1rem}.p-avatar.p-avatar-image{background-color:transparent}.p-avatar.p-avatar-circle{border-radius:50%;overflow:hidden}.p-avatar .p-avatar-icon{font-size:1rem}.p-avatar img{width:100%;height:100%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Avatar, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-avatar', template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-content></ng-content>\n            <span class=\"p-avatar-text\" *ngIf=\"label; else iconTemplate\">{{label}}</span>\n            <ng-template #iconTemplate><span [class]=\"icon\" [ngClass]=\"'p-avatar-icon'\" *ngIf=\"icon; else imageTemplate\"></span></ng-template>\n            <ng-template #imageTemplate><img [src]=\"image\" *ngIf=\"image\"></ng-template>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-avatar{display:inline-flex;align-items:center;justify-content:center;width:2rem;height:2rem;font-size:1rem}.p-avatar.p-avatar-image{background-color:transparent}.p-avatar.p-avatar-circle{border-radius:50%;overflow:hidden}.p-avatar .p-avatar-icon{font-size:1rem}.p-avatar img{width:100%;height:100%}\\n\"] }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], image: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], shape: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }] } });\nclass AvatarModule {\n}\nAvatarModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAvatarModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarModule, declarations: [Avatar], imports: [CommonModule], exports: [Avatar] });\nAvatarModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: AvatarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Avatar],\n                    declarations: [Avatar]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Avatar, AvatarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;;;IAiByFP,EAI7E,6B;IAJ6EA,EAIhB,U;IAJgBA,EAIP,e;;;;mBAJOA,E;IAAAA,EAIhB,a;IAJgBA,EAIhB,gC;;;;;;IAJgBA,EAKlD,wB;;;;mBALkDA,E;IAAAA,EAK5C,wB;IAL4CA,EAK7B,uC;;;;;;IAL6BA,EAKlD,qE;;;;mBALkDA,E;;gBAAAA,E;;IAAAA,EAKA,iD;;;;;;IALAA,EAMjD,uB;;;;mBANiDA,E;IAAAA,EAM5C,iCAN4CA,EAM5C,e;;;;;;IAN4CA,EAMjD,mE;;;;mBANiDA,E;IAAAA,EAM7B,iC;;;;;;AArB5D,MAAMQ,MAAN,CAAa;EACTC,WAAW,GAAG;IACV,KAAKC,IAAL,GAAY,QAAZ;IACA,KAAKC,KAAL,GAAa,QAAb;EACH;;EACDC,cAAc,GAAG;IACb,OAAO;MACH,wBAAwB,IADrB;MAEH,kBAAkB,KAAKC,KAAL,IAAc,IAF7B;MAGH,mBAAmB,KAAKF,KAAL,KAAe,QAH/B;MAIH,eAAe,KAAKD,IAAL,KAAc,OAJ1B;MAKH,eAAe,KAAKA,IAAL,KAAc;IAL1B,CAAP;EAOH;;AAbQ;;AAebF,MAAM,CAACM,IAAP;EAAA,iBAAmGN,MAAnG;AAAA;;AACAA,MAAM,CAACO,IAAP,kBADyFf,EACzF;EAAA,MAAuFQ,MAAvF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADyFR,EACzF;MADyFA,EAEjF,4BADR;MADyFA,EAG7E,gBAFZ;MADyFA,EAI7E,uDAHZ;MADyFA,EAK7E,4EAL6EA,EAK7E,wBAJZ;MADyFA,EAM7E,4EAN6EA,EAM7E,wBALZ;MADyFA,EAOjF,eANR;IAAA;;IAAA;MAAA,YADyFA,EACzF;;MADyFA,EAE/C,2BAD1C;MADyFA,EAE5E,kEADb;MADyFA,EAIhD,aAHzC;MADyFA,EAIhD,+CAHzC;IAAA;EAAA;EAAA,eAO8XM,EAAE,CAACU,OAPjY,EAO4dV,EAAE,CAACW,IAP/d,EAOgkBX,EAAE,CAACY,OAPnkB;EAAA;EAAA;EAAA;AAAA;;AAQA;EAAA,mDATyFlB,EASzF,mBAA2FQ,MAA3F,EAA+G,CAAC;IACpGW,IAAI,EAAElB,SAD8F;IAEpGmB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAZ;MAAwBC,QAAQ,EAAG;AACtD;AACA;AACA;AACA;AACA;AACA;AACA,KAPmB;MAOZC,eAAe,EAAErB,uBAAuB,CAACsB,MAP7B;MAOqCC,aAAa,EAAEtB,iBAAiB,CAACuB,IAPtE;MAO4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAPlF;MASIC,MAAM,EAAE,CAAC,iTAAD;IATZ,CAAD;EAF8F,CAAD,CAA/G,QAY4B;IAAEC,KAAK,EAAE,CAAC;MACtBV,IAAI,EAAEf;IADgB,CAAD,CAAT;IAEZ0B,IAAI,EAAE,CAAC;MACPX,IAAI,EAAEf;IADC,CAAD,CAFM;IAIZS,KAAK,EAAE,CAAC;MACRM,IAAI,EAAEf;IADE,CAAD,CAJK;IAMZM,IAAI,EAAE,CAAC;MACPS,IAAI,EAAEf;IADC,CAAD,CANM;IAQZO,KAAK,EAAE,CAAC;MACRQ,IAAI,EAAEf;IADE,CAAD,CARK;IAUZ2B,KAAK,EAAE,CAAC;MACRZ,IAAI,EAAEf;IADE,CAAD,CAVK;IAYZ4B,UAAU,EAAE,CAAC;MACbb,IAAI,EAAEf;IADO,CAAD;EAZA,CAZ5B;AAAA;;AA2BA,MAAM6B,YAAN,CAAmB;;AAEnBA,YAAY,CAACnB,IAAb;EAAA,iBAAyGmB,YAAzG;AAAA;;AACAA,YAAY,CAACC,IAAb,kBAvCyFlC,EAuCzF;EAAA,MAA0GiC;AAA1G;AACAA,YAAY,CAACE,IAAb,kBAxCyFnC,EAwCzF;EAAA,UAAkIO,YAAlI;AAAA;;AACA;EAAA,mDAzCyFP,EAyCzF,mBAA2FiC,YAA3F,EAAqH,CAAC;IAC1Gd,IAAI,EAAEd,QADoG;IAE1Ge,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAAC7B,YAAD,CADV;MAEC8B,OAAO,EAAE,CAAC7B,MAAD,CAFV;MAGC8B,YAAY,EAAE,CAAC9B,MAAD;IAHf,CAAD;EAFoG,CAAD,CAArH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,MAAT,EAAiByB,YAAjB"}, "metadata": {}, "sourceType": "module"}