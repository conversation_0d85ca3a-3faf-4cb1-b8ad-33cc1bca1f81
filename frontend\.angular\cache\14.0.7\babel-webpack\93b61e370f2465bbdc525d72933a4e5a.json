{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { MessageService } from 'primeng/api';\nimport { lastValueFrom } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../../services/team.service\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/table\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/ripple\";\nimport * as i8 from \"primeng/toast\";\nimport * as i9 from \"primeng/toolbar\";\nimport * as i10 from \"primeng/inputtext\";\nimport * as i11 from \"primeng/dialog\";\n\nfunction TeamsComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.openNew());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_5_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.deleteSelectedTeams());\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.selectedTeams || !ctx_r0.selectedTeams.length);\n  }\n}\n\nfunction TeamsComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"h5\", 21);\n    i0.ɵɵtext(2, \"Manage Teams\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 22);\n    i0.ɵɵelement(4, \"i\", 23);\n    i0.ɵɵelementStart(5, \"input\", 24);\n    i0.ɵɵlistener(\"input\", function TeamsComponent_ng_template_8_Template_input_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n\n      const _r1 = i0.ɵɵreference(7);\n\n      return i0.ɵɵresetView(ctx_r13.onGlobalFilter(_r1, $event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\n\nfunction TeamsComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"th\", 25);\n    i0.ɵɵelement(2, \"p-tableHeaderCheckbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\", 26);\n    i0.ɵɵtext(4, \"ID \");\n    i0.ɵɵelement(5, \"p-sortIcon\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 28);\n    i0.ɵɵtext(7, \"Name \");\n    i0.ɵɵelement(8, \"p-sortIcon\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 30);\n    i0.ɵɵtext(10, \"Speciality \");\n    i0.ɵɵelement(11, \"p-sortIcon\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"th\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TeamsComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵelement(2, \"p-tableCheckbox\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"div\", 33)(11, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_10_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const team_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.editTeam(team_r15));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_10_Template_button_click_12_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const team_r15 = restoredCtx.$implicit;\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.deleteTeam(team_r15));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n\n  if (rf & 2) {\n    const team_r15 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", team_r15);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(team_r15.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(team_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(team_r15.speciality || \"N/A\");\n  }\n}\n\nfunction TeamsComponent_ng_template_12_small_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 42);\n    i0.ɵɵtext(1, \"Name is required.\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"ng-invalid ng-dirty\": a0\n  };\n};\n\nfunction TeamsComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"label\", 37);\n    i0.ɵɵtext(2, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function TeamsComponent_ng_template_12_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.team.name = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, TeamsComponent_ng_template_12_small_4_Template, 2, 0, \"small\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36)(6, \"label\", 40);\n    i0.ɵɵtext(7, \"Speciality\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"input\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function TeamsComponent_ng_template_12_Template_input_ngModelChange_8_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.team.speciality = $event);\n    });\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.team.name)(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r5.submitted && !ctx_r5.team.name));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.submitted && !ctx_r5.team.name);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r5.team.speciality);\n  }\n}\n\nfunction TeamsComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_13_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.hideDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_13_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.saveTeam());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TeamsComponent_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Are you sure you want to delete \");\n    i0.ɵɵelementStart(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \"?\");\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r7.team.name);\n  }\n}\n\nfunction TeamsComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.deleteTeamDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_18_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.confirmDelete());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction TeamsComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.deleteTeamsDialog = false);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function TeamsComponent_ng_template_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.confirmDeleteSelected());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\n\nconst _c1 = function () {\n  return [\"name\", \"speciality\"];\n};\n\nconst _c2 = function () {\n  return [10, 20, 30];\n};\n\nconst _c3 = function () {\n  return {\n    width: \"450px\"\n  };\n};\n\nexport class TeamsComponent {\n  constructor(teamService, messageService) {\n    this.teamService = teamService;\n    this.messageService = messageService;\n    this.teamDialog = false;\n    this.deleteTeamDialog = false;\n    this.deleteTeamsDialog = false;\n    this.teams = [];\n    this.team = {};\n    this.selectedTeams = [];\n    this.submitted = false;\n    this.cols = [];\n    this.rowsPerPageOptions = [5, 10, 20];\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this.teams = yield lastValueFrom(_this.teamService.getTeams());\n      } catch (error) {\n        console.error('Error loading teams:', error);\n\n        _this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load teams',\n          life: 3000\n        });\n      }\n\n      _this.cols = [{\n        field: 'id',\n        header: 'ID'\n      }, {\n        field: 'name',\n        header: 'Name'\n      }, {\n        field: 'speciality',\n        header: 'Speciality'\n      }];\n    })();\n  }\n\n  openNew() {\n    this.team = {};\n    this.submitted = false;\n    this.teamDialog = true;\n  }\n\n  onGlobalFilter(table, event) {\n    const input = event.target;\n    table.filterGlobal(input.value, 'contains');\n  }\n\n  editTeam(team) {\n    this.team = Object.assign({}, team);\n    this.teamDialog = true;\n  }\n\n  deleteTeam(team) {\n    this.team = team;\n    this.deleteTeamDialog = true;\n  }\n\n  deleteSelectedTeams() {\n    this.deleteTeamsDialog = true;\n  }\n\n  hideDialog() {\n    this.teamDialog = false;\n    this.submitted = false;\n  }\n\n  saveTeam() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      var _a;\n\n      _this2.submitted = true;\n\n      if ((_a = _this2.team.name) === null || _a === void 0 ? void 0 : _a.trim()) {\n        try {\n          if (_this2.team.id) {\n            // Update existing team\n            const updatedTeam = yield lastValueFrom(_this2.teamService.updateTeam(_this2.team));\n\n            const index = _this2.findIndexById(_this2.team.id);\n\n            _this2.teams[index] = updatedTeam;\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Team Updated',\n              life: 3000\n            });\n          } else {\n            // Create new team\n            const newTeam = yield lastValueFrom(_this2.teamService.addTeam(_this2.team));\n\n            _this2.teams.push(newTeam);\n\n            _this2.messageService.add({\n              severity: 'success',\n              summary: 'Successful',\n              detail: 'Team Created',\n              life: 3000\n            });\n          }\n\n          _this2.teamDialog = false;\n          _this2.team = {};\n        } catch (error) {\n          console.error('Error saving team:', error);\n\n          _this2.messageService.add({\n            severity: 'error',\n            summary: 'Error',\n            detail: 'Failed to save team',\n            life: 3000\n          });\n        }\n      }\n    })();\n  }\n\n  confirmDelete() {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this3.teamService.deleteTeam(_this3.team.id).toPromise();\n        _this3.teams = _this3.teams.filter(val => val.id !== _this3.team.id);\n        _this3.deleteTeamDialog = false;\n        _this3.team = {};\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Team Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting team:', error);\n\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete team',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  confirmDeleteSelected() {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        for (const team of _this4.selectedTeams) {\n          yield _this4.teamService.deleteTeam(team.id).toPromise();\n        }\n\n        _this4.teams = _this4.teams.filter(val => !_this4.selectedTeams.includes(val));\n        _this4.deleteTeamsDialog = false;\n        _this4.selectedTeams = [];\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Successful',\n          detail: 'Teams Deleted',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error deleting teams:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to delete teams',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  findIndexById(id) {\n    return this.teams.findIndex(team => team.id === id);\n  }\n\n}\n\nTeamsComponent.ɵfac = function TeamsComponent_Factory(t) {\n  return new (t || TeamsComponent)(i0.ɵɵdirectiveInject(i1.TeamService), i0.ɵɵdirectiveInject(i2.MessageService));\n};\n\nTeamsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: TeamsComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 25,\n  vars: 27,\n  consts: [[1, \"grid\"], [1, \"col-12\"], [1, \"card\", \"px-6\", \"py-6\"], [\"styleClass\", \"mb-4\"], [\"pTemplate\", \"left\"], [\"responsiveLayout\", \"scroll\", \"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} teams\", \"selectionMode\", \"multiple\", \"dataKey\", \"id\", 3, \"value\", \"columns\", \"rows\", \"globalFilterFields\", \"paginator\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"selection\", \"rowHover\", \"selectionChange\"], [\"dt\", \"\"], [\"pTemplate\", \"caption\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [\"header\", \"Team Details\", 1, \"p-fluid\", 3, \"visible\", \"modal\", \"visibleChange\"], [\"pTemplate\", \"content\"], [\"pTemplate\", \"footer\"], [\"header\", \"Confirm\", 3, \"visible\", \"modal\", \"visibleChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"pi\", \"pi-exclamation-triangle\", \"mr-3\", 2, \"font-size\", \"2rem\"], [4, \"ngIf\"], [1, \"my-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"New\", \"icon\", \"pi pi-plus\", 1, \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Delete\", \"icon\", \"pi pi-trash\", 1, \"p-button-danger\", 3, \"disabled\", \"click\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"md:align-items-center\"], [1, \"m-0\"], [1, \"block\", \"mt-2\", \"md:mt-0\", \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"pInputText\", \"\", \"type\", \"text\", \"placeholder\", \"Search...\", 1, \"w-full\", \"sm:w-auto\", 3, \"input\"], [2, \"width\", \"3rem\"], [\"pSortableColumn\", \"id\"], [\"field\", \"id\"], [\"pSortableColumn\", \"name\"], [\"field\", \"name\"], [\"pSortableColumn\", \"speciality\"], [\"field\", \"speciality\"], [3, \"value\"], [1, \"flex\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-pencil\", 1, \"p-button-rounded\", \"p-button-success\", \"mr-2\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-trash\", 1, \"p-button-rounded\", \"p-button-warning\", 3, \"click\"], [1, \"field\"], [\"for\", \"name\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"name\", \"required\", \"\", \"autofocus\", \"\", 3, \"ngModel\", \"ngClass\", \"ngModelChange\"], [\"class\", \"ng-dirty ng-invalid\", 4, \"ngIf\"], [\"for\", \"speciality\"], [\"type\", \"text\", \"pInputText\", \"\", \"id\", \"speciality\", \"placeholder\", \"e.g., Development, UI/UX, Security\", 3, \"ngModel\", \"ngModelChange\"], [1, \"ng-dirty\", \"ng-invalid\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Cancel\", \"icon\", \"pi pi-times\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Save\", \"icon\", \"pi pi-check\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-times\", \"label\", \"No\", 1, \"p-button-text\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"icon\", \"pi pi-check\", \"label\", \"Yes\", 1, \"p-button-text\", 3, \"click\"]],\n  template: function TeamsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n      i0.ɵɵelement(3, \"p-toast\");\n      i0.ɵɵelementStart(4, \"p-toolbar\", 3);\n      i0.ɵɵtemplate(5, TeamsComponent_ng_template_5_Template, 3, 1, \"ng-template\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"p-table\", 5, 6);\n      i0.ɵɵlistener(\"selectionChange\", function TeamsComponent_Template_p_table_selectionChange_6_listener($event) {\n        return ctx.selectedTeams = $event;\n      });\n      i0.ɵɵtemplate(8, TeamsComponent_ng_template_8_Template, 6, 0, \"ng-template\", 7);\n      i0.ɵɵtemplate(9, TeamsComponent_ng_template_9_Template, 13, 0, \"ng-template\", 8);\n      i0.ɵɵtemplate(10, TeamsComponent_ng_template_10_Template, 13, 4, \"ng-template\", 9);\n      i0.ɵɵelementEnd()()()();\n      i0.ɵɵelementStart(11, \"p-dialog\", 10);\n      i0.ɵɵlistener(\"visibleChange\", function TeamsComponent_Template_p_dialog_visibleChange_11_listener($event) {\n        return ctx.teamDialog = $event;\n      });\n      i0.ɵɵtemplate(12, TeamsComponent_ng_template_12_Template, 9, 6, \"ng-template\", 11);\n      i0.ɵɵtemplate(13, TeamsComponent_ng_template_13_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(14, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function TeamsComponent_Template_p_dialog_visibleChange_14_listener($event) {\n        return ctx.deleteTeamDialog = $event;\n      });\n      i0.ɵɵelementStart(15, \"div\", 14);\n      i0.ɵɵelement(16, \"i\", 15);\n      i0.ɵɵtemplate(17, TeamsComponent_span_17_Template, 5, 1, \"span\", 16);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(18, TeamsComponent_ng_template_18_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"p-dialog\", 13);\n      i0.ɵɵlistener(\"visibleChange\", function TeamsComponent_Template_p_dialog_visibleChange_19_listener($event) {\n        return ctx.deleteTeamsDialog = $event;\n      });\n      i0.ɵɵelementStart(20, \"div\", 14);\n      i0.ɵɵelement(21, \"i\", 15);\n      i0.ɵɵelementStart(22, \"span\");\n      i0.ɵɵtext(23, \"Are you sure you want to delete selected teams?\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(24, TeamsComponent_ng_template_24_Template, 2, 0, \"ng-template\", 12);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(6);\n      i0.ɵɵproperty(\"value\", ctx.teams)(\"columns\", ctx.cols)(\"rows\", 10)(\"globalFilterFields\", i0.ɵɵpureFunction0(22, _c1))(\"paginator\", true)(\"rowsPerPageOptions\", i0.ɵɵpureFunction0(23, _c2))(\"showCurrentPageReport\", true)(\"selection\", ctx.selectedTeams)(\"rowHover\", true);\n      i0.ɵɵadvance(5);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.teamDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(25, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.deleteTeamDialog)(\"modal\", true);\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.team);\n      i0.ɵɵadvance(2);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c3));\n      i0.ɵɵproperty(\"visible\", ctx.deleteTeamsDialog)(\"modal\", true);\n    }\n  },\n  dependencies: [i3.NgClass, i3.NgIf, i4.Table, i2.PrimeTemplate, i4.SortableColumn, i4.SortIcon, i4.TableCheckbox, i4.TableHeaderCheckbox, i5.ButtonDirective, i6.DefaultValueAccessor, i6.NgControlStatus, i6.RequiredValidator, i6.NgModel, i7.Ripple, i8.Toast, i9.Toolbar, i10.InputText, i11.Dialog],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AAGA,SAASA,cAAT,QAA+B,aAA/B;AAEA,SAASC,aAAT,QAA8B,MAA9B;;;;;;;;;;;;;;;;;;ICCoBC,gCAAkB,CAAlB,EAAkB,QAAlB,EAAkB,EAAlB;IACwFA;MAAAA;MAAA;MAAA,OAASA,iCAAT;IAAkB,CAAlB;IAAoBA;IACxGA;IAAkFA;MAAAA;MAAA;MAAA,OAASA,6CAAT;IAA8B,CAA9B;IAAqFA;;;;;IAArDA;IAAAA;;;;;;;;IAWtHA,gCAA2F,CAA3F,EAA2F,IAA3F,EAA2F,EAA3F;IACoBA;IAAYA;IAC5BA;IACIA;IACAA;IAA8BA;MAAAA;MAAA;;MAAA;;MAAA,OAASA,mDAAT;IAAmC,CAAnC;IAA9BA;;;;;;IAMRA,2BAAI,CAAJ,EAAI,IAAJ,EAAI,EAAJ;IAEQA;IACJA;IACAA;IAAyBA;IAAGA;IAAoCA;IAChEA;IAA2BA;IAAKA;IAAsCA;IACtEA;IAAiCA;IAAWA;IAA4CA;IACxFA;IACJA;;;;;;;;IAIAA,2BAAI,CAAJ,EAAI,IAAJ;IAEQA;IACJA;IACAA;IAAIA;IAAWA;IACfA;IAAIA;IAAaA;IACjBA;IAAIA;IAA4BA;IAChCA,2BAAI,EAAJ,EAAI,KAAJ,EAAI,EAAJ,EAAI,EAAJ,EAAI,QAAJ,EAAI,EAAJ;IAEmGA;MAAA;MAAA;MAAA;MAAA,OAASA,0CAAT;IAAuB,CAAvB;IAAyBA;IACpHA;IAAqFA;MAAA;MAAA;MAAA;MAAA,OAASA,4CAAT;IAAyB,CAAzB;IAA2BA;;;;;IARnGA;IAAAA;IAEjBA;IAAAA;IACAA;IAAAA;IACAA;IAAAA;;;;;;IAqBhBA;IAAmEA;IAAiBA;;;;;;;;;;;;;;IAJxFA,gCAAmB,CAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IACsBA;IAAIA;IACtBA;IAAwCA;MAAAA;MAAA;MAAA,OAAaA,0CAAb;IAA8B,CAA9B;IAAxCA;IAEAA;IACJA;IACAA,gCAAmB,CAAnB,EAAmB,OAAnB,EAAmB,EAAnB;IAC4BA;IAAUA;IAClCA;IAA8CA;MAAAA;MAAA;MAAA,OAAaA,gDAAb;IACrD,CADqD;IAA9CA;;;;;IANwCA;IAAAA,2CAAuB,SAAvB,EAAuBA,iEAAvB;IAEJA;IAAAA;IAIUA;IAAAA;;;;;;;;IAKlDA;IAAgFA;MAAAA;MAAA;MAAA,OAASA,oCAAT;IAAqB,CAArB;IAAuBA;IACvGA;IAA8EA;MAAAA;MAAA;MAAA,OAASA,kCAAT;IAAmB,CAAnB;IAAqBA;;;;;;IAQnGA;IAAmBA;IAAgCA;IAAGA;IAAaA;IAAIA;IAACA;;;;;IAAlBA;IAAAA;;;;;;;;IAGtDA;IAA4EA;MAAAA;MAAA;MAAA,iDAA4B,KAA5B;IAAiC,CAAjC;IAAmCA;IAC/GA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,uCAAT;IAAwB,CAAxB;IAA0BA;;;;;;;;IAWvGA;IAA4EA;MAAAA;MAAA;MAAA,kDAA6B,KAA7B;IAAkC,CAAlC;IAAoCA;IAChHA;IAA6EA;MAAAA;MAAA;MAAA,OAASA,+CAAT;IAAgC,CAAhC;IAAkCA;;;;;;;;;;;;;;;;;;AD3FvH,OAAM,MAAOC,cAAP,CAAqB;EAWvBC,YACYC,WADZ,EAEYC,cAFZ,EAE0C;IAD9B;IACA;IAZZ,kBAAsB,KAAtB;IACA,wBAA4B,KAA5B;IACA,yBAA6B,KAA7B;IACA,aAAgB,EAAhB;IACA,YAAa,EAAb;IACA,qBAAwB,EAAxB;IACA,iBAAqB,KAArB;IACA,YAAc,EAAd;IACA,0BAAqB,CAAC,CAAD,EAAI,EAAJ,EAAQ,EAAR,CAArB;EAKK;;EAECC,QAAQ;IAAA;;IAAA;MACV,IAAI;QACA,KAAI,CAACC,KAAL,SAAmBP,aAAa,CAAC,KAAI,CAACI,WAAL,CAAiBI,QAAjB,EAAD,CAAhC;MACH,CAFD,CAEE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,KAAI,CAACJ,cAAL,CAAoBM,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,sBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;;MAED,KAAI,CAACC,IAAL,GAAY,CACR;QAAEC,KAAK,EAAE,IAAT;QAAeC,MAAM,EAAE;MAAvB,CADQ,EAER;QAAED,KAAK,EAAE,MAAT;QAAiBC,MAAM,EAAE;MAAzB,CAFQ,EAGR;QAAED,KAAK,EAAE,YAAT;QAAuBC,MAAM,EAAE;MAA/B,CAHQ,CAAZ;IAbU;EAkBb;;EAEDC,OAAO;IACH,KAAKC,IAAL,GAAY,EAAZ;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,UAAL,GAAkB,IAAlB;EACH;;EAEDC,cAAc,CAACC,KAAD,EAAeC,KAAf,EAA2B;IACrC,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAApB;IACAH,KAAK,CAACI,YAAN,CAAmBF,KAAK,CAACG,KAAzB,EAAgC,UAAhC;EACH;;EAEDC,QAAQ,CAACV,IAAD,EAAW;IACf,KAAKA,IAAL,GAASW,kBAAQX,IAAR,CAAT;IACA,KAAKE,UAAL,GAAkB,IAAlB;EACH;;EAEDU,UAAU,CAACZ,IAAD,EAAW;IACjB,KAAKA,IAAL,GAAYA,IAAZ;IACA,KAAKa,gBAAL,GAAwB,IAAxB;EACH;;EAEDC,mBAAmB;IACf,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;EAEDC,UAAU;IACN,KAAKd,UAAL,GAAkB,KAAlB;IACA,KAAKD,SAAL,GAAiB,KAAjB;EACH;;EAEKgB,QAAQ;IAAA;;IAAA;;;MACV,MAAI,CAAChB,SAAL,GAAiB,IAAjB;;MAEA,IAAI,YAAI,CAACD,IAAL,CAAUkB,IAAV,MAAc,IAAd,IAAcC,aAAd,GAAc,MAAd,GAAcA,GAAEC,IAAF,EAAlB,EAA4B;QACxB,IAAI;UACA,IAAI,MAAI,CAACpB,IAAL,CAAUqB,EAAd,EAAkB;YACd;YACA,MAAMC,WAAW,SAAS1C,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiBuC,UAAjB,CAA4B,MAAI,CAACvB,IAAjC,CAAD,CAAvC;;YACA,MAAMwB,KAAK,GAAG,MAAI,CAACC,aAAL,CAAmB,MAAI,CAACzB,IAAL,CAAUqB,EAA7B,CAAd;;YACA,MAAI,CAAClC,KAAL,CAAWqC,KAAX,IAAoBF,WAApB;;YACA,MAAI,CAACrC,cAAL,CAAoBM,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,cAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH,CAXD,MAWO;YACH;YACA,MAAM+B,OAAO,SAAS9C,aAAa,CAAC,MAAI,CAACI,WAAL,CAAiB2C,OAAjB,CAAyB,MAAI,CAAC3B,IAA9B,CAAD,CAAnC;;YACA,MAAI,CAACb,KAAL,CAAWyC,IAAX,CAAgBF,OAAhB;;YACA,MAAI,CAACzC,cAAL,CAAoBM,GAApB,CAAwB;cACpBC,QAAQ,EAAE,SADU;cAEpBC,OAAO,EAAE,YAFW;cAGpBC,MAAM,EAAE,cAHY;cAIpBC,IAAI,EAAE;YAJc,CAAxB;UAMH;;UAED,MAAI,CAACO,UAAL,GAAkB,KAAlB;UACA,MAAI,CAACF,IAAL,GAAY,EAAZ;QACH,CA1BD,CA0BE,OAAOX,KAAP,EAAc;UACZC,OAAO,CAACD,KAAR,CAAc,oBAAd,EAAoCA,KAApC;;UACA,MAAI,CAACJ,cAAL,CAAoBM,GAApB,CAAwB;YACpBC,QAAQ,EAAE,OADU;YAEpBC,OAAO,EAAE,OAFW;YAGpBC,MAAM,EAAE,qBAHY;YAIpBC,IAAI,EAAE;UAJc,CAAxB;QAMH;MACJ;IAvCS;EAwCb;;EAEKkC,aAAa;IAAA;;IAAA;MACf,IAAI;QACA,MAAM,MAAI,CAAC7C,WAAL,CAAiB4B,UAAjB,CAA4B,MAAI,CAACZ,IAAL,CAAUqB,EAAtC,EAA0CS,SAA1C,EAAN;QACA,MAAI,CAAC3C,KAAL,GAAa,MAAI,CAACA,KAAL,CAAW4C,MAAX,CAAkBC,GAAG,IAAIA,GAAG,CAACX,EAAJ,KAAW,MAAI,CAACrB,IAAL,CAAUqB,EAA9C,CAAb;QACA,MAAI,CAACR,gBAAL,GAAwB,KAAxB;QACA,MAAI,CAACb,IAAL,GAAY,EAAZ;;QACA,MAAI,CAACf,cAAL,CAAoBM,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,cAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAXD,CAWE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,MAAI,CAACJ,cAAL,CAAoBM,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,uBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IApBc;EAqBlB;;EAEKsC,qBAAqB;IAAA;;IAAA;MACvB,IAAI;QACA,KAAK,MAAMjC,IAAX,IAAmB,MAAI,CAACkC,aAAxB,EAAuC;UACnC,MAAM,MAAI,CAAClD,WAAL,CAAiB4B,UAAjB,CAA4BZ,IAAI,CAACqB,EAAjC,EAAqCS,SAArC,EAAN;QACH;;QACD,MAAI,CAAC3C,KAAL,GAAa,MAAI,CAACA,KAAL,CAAW4C,MAAX,CAAkBC,GAAG,IAAI,CAAC,MAAI,CAACE,aAAL,CAAmBC,QAAnB,CAA4BH,GAA5B,CAA1B,CAAb;QACA,MAAI,CAACjB,iBAAL,GAAyB,KAAzB;QACA,MAAI,CAACmB,aAAL,GAAqB,EAArB;;QACA,MAAI,CAACjD,cAAL,CAAoBM,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,YAFW;UAGpBC,MAAM,EAAE,eAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAbD,CAaE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,uBAAd,EAAuCA,KAAvC;;QACA,MAAI,CAACJ,cAAL,CAAoBM,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,wBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAtBsB;EAuB1B;;EAED8B,aAAa,CAACJ,EAAD,EAAW;IACpB,OAAO,KAAKlC,KAAL,CAAWiD,SAAX,CAAqBpC,IAAI,IAAIA,IAAI,CAACqB,EAAL,KAAYA,EAAzC,CAAP;EACH;;AA9JsB;;;mBAAdvC,gBAAcD;AAAA;;;QAAdC;EAAcuD;EAAAC,iCAFZ,CAAC3D,cAAD,CAEY;EAFI4D;EAAAC;EAAAC;EAAAC;IAAA;MCT/B7D,+BAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB,EAAkB,CAAlB,EAAkB,KAAlB,EAAkB,CAAlB;MAGYA;MACAA;MACIA;MAMJA;MAEAA;MAGSA;QAAA;MAAA;MAELA;MAUAA;MAYAA;MAgBJA;MAMZA;MAAUA;QAAA;MAAA;MACNA;MAaAA;MAIJA;MAGAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MACJA;MACAA;MAIJA;MAGAA;MAAUA;QAAA;MAAA;MACNA;MACIA;MACAA;MAAMA;MAA+CA;MAEzDA;MAIJA;;;;MA3FyBA;MAAAA,kCAAe,SAAf,EAAe8D,QAAf,EAAe,MAAf,EAAe,EAAf,EAAe,oBAAf,EAAe9D,2BAAf,EAAe,WAAf,EAAe,IAAf,EAAe,oBAAf,EAAeA,2BAAf,EAAe,uBAAf,EAAe,IAAf,EAAe,WAAf,EAAe8D,iBAAf,EAAe,UAAf,EAAe,IAAf;MAiDU9D;MAAAA;MAAzBA,yCAAwB,OAAxB,EAAwB,IAAxB;MAqB+DA;MAAAA;MAA/DA,+CAA8B,OAA9B,EAA8B,IAA9B;MAGKA;MAAAA;MAS2DA;MAAAA;MAAhEA,gDAA+B,OAA/B,EAA+B,IAA/B", "names": ["MessageService", "lastValueFrom", "i0", "TeamsComponent", "constructor", "teamService", "messageService", "ngOnInit", "teams", "getTeams", "error", "console", "add", "severity", "summary", "detail", "life", "cols", "field", "header", "openNew", "team", "submitted", "teamDialog", "onGlobalFilter", "table", "event", "input", "target", "filterGlobal", "value", "editTeam", "Object", "deleteTeam", "deleteTeamDialog", "deleteSelectedTeams", "deleteTeamsDialog", "hideDialog", "saveTeam", "name", "_a", "trim", "id", "updatedTeam", "updateTeam", "index", "findIndexById", "newTeam", "addTeam", "push", "confirmDelete", "to<PERSON>romise", "filter", "val", "confirmDeleteSelected", "selectedTeams", "includes", "findIndex", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\crud\\teams\\teams.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\crud\\teams\\teams.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Team } from '../../../../../models/team.model';\nimport { TeamService } from '../../../../../services/team.service';\nimport { MessageService } from 'primeng/api';\nimport { Table } from 'primeng/table';\nimport { lastValueFrom } from 'rxjs';\n\n@Component({\n    templateUrl: './teams.component.html',\n    providers: [MessageService]\n})\nexport class TeamsComponent implements OnInit {\n    teamDialog: boolean = false;\n    deleteTeamDialog: boolean = false;\n    deleteTeamsDialog: boolean = false;\n    teams: Team[] = [];\n    team: Team = {} as Team;\n    selectedTeams: Team[] = [];\n    submitted: boolean = false;\n    cols: any[] = [];\n    rowsPerPageOptions = [5, 10, 20];\n\n    constructor(\n        private teamService: TeamService, \n        private messageService: MessageService\n    ) { }\n\n    async ngOnInit() {\n        try {\n            this.teams = await lastValueFrom(this.teamService.getTeams());\n        } catch (error) {\n            console.error('Error loading teams:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to load teams',\n                life: 3000\n            });\n        }\n\n        this.cols = [\n            { field: 'id', header: 'ID' },\n            { field: 'name', header: 'Name' },\n            { field: 'speciality', header: 'Speciality' }\n        ];\n    }\n\n    openNew() {\n        this.team = {} as Team;\n        this.submitted = false;\n        this.teamDialog = true;\n    }\n\n    onGlobalFilter(table: Table, event: Event) {\n        const input = event.target as HTMLInputElement;\n        table.filterGlobal(input.value, 'contains');\n    }\n\n    editTeam(team: Team) {\n        this.team = { ...team };\n        this.teamDialog = true;\n    }\n\n    deleteTeam(team: Team) {\n        this.team = team;\n        this.deleteTeamDialog = true;\n    }\n\n    deleteSelectedTeams() {\n        this.deleteTeamsDialog = true;\n    }\n\n    hideDialog() {\n        this.teamDialog = false;\n        this.submitted = false;\n    }\n\n    async saveTeam() {\n        this.submitted = true;\n        \n        if (this.team.name?.trim()) {\n            try {\n                if (this.team.id) {\n                    // Update existing team\n                    const updatedTeam = await lastValueFrom(this.teamService.updateTeam(this.team));\n                    const index = this.findIndexById(this.team.id);\n                    this.teams[index] = updatedTeam;\n                    this.messageService.add({\n                        severity: 'success',\n                        summary: 'Successful',\n                        detail: 'Team Updated',\n                        life: 3000\n                    });\n                } else {\n                    // Create new team\n                    const newTeam = await lastValueFrom(this.teamService.addTeam(this.team));\n                    this.teams.push(newTeam);\n                    this.messageService.add({\n                        severity: 'success',\n                        summary: 'Successful',\n                        detail: 'Team Created',\n                        life: 3000\n                    });\n                }\n                \n                this.teamDialog = false;\n                this.team = {} as Team;\n            } catch (error) {\n                console.error('Error saving team:', error);\n                this.messageService.add({\n                    severity: 'error', \n                    summary: 'Error', \n                    detail: 'Failed to save team', \n                    life: 3000\n                });\n            }\n        }\n    }\n\n    async confirmDelete() {\n        try {\n            await this.teamService.deleteTeam(this.team.id).toPromise();\n            this.teams = this.teams.filter(val => val.id !== this.team.id);\n            this.deleteTeamDialog = false;\n            this.team = {} as Team;\n            this.messageService.add({\n                severity: 'success', \n                summary: 'Successful', \n                detail: 'Team Deleted', \n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error deleting team:', error);\n            this.messageService.add({\n                severity: 'error', \n                summary: 'Error', \n                detail: 'Failed to delete team', \n                life: 3000\n            });\n        }\n    }\n\n    async confirmDeleteSelected() {\n        try {\n            for (const team of this.selectedTeams) {\n                await this.teamService.deleteTeam(team.id).toPromise();\n            }\n            this.teams = this.teams.filter(val => !this.selectedTeams.includes(val));\n            this.deleteTeamsDialog = false;\n            this.selectedTeams = [];\n            this.messageService.add({\n                severity: 'success', \n                summary: 'Successful', \n                detail: 'Teams Deleted', \n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error deleting teams:', error);\n            this.messageService.add({\n                severity: 'error', \n                summary: 'Error', \n                detail: 'Failed to delete teams', \n                life: 3000\n            });\n        }\n    }\n\n    findIndexById(id: number): number {\n        return this.teams.findIndex(team => team.id === id);\n    }\n}\n", "<div class=\"grid\">\n    <div class=\"col-12\">\n        <div class=\"card px-6 py-6\">\n            <p-toast></p-toast>\n            <p-toolbar styleClass=\"mb-4\">\n                <ng-template pTemplate=\"left\">\n                    <div class=\"my-2\">\n                        <button pButton pRipple label=\"New\" icon=\"pi pi-plus\" class=\"p-button-success mr-2\" (click)=\"openNew()\"></button>\n                        <button pButton pRipple label=\"Delete\" icon=\"pi pi-trash\" class=\"p-button-danger\" (click)=\"deleteSelectedTeams()\" [disabled]=\"!selectedTeams || !selectedTeams.length\"></button>\n                    </div>\n                </ng-template>\n            </p-toolbar>\n            \n            <p-table #dt [value]=\"teams\" [columns]=\"cols\" responsiveLayout=\"scroll\" [rows]=\"10\" \n                     [globalFilterFields]=\"['name','speciality']\" [paginator]=\"true\" [rowsPerPageOptions]=\"[10,20,30]\" \n                     [showCurrentPageReport]=\"true\" currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} teams\" \n                     [(selection)]=\"selectedTeams\" selectionMode=\"multiple\" [rowHover]=\"true\" dataKey=\"id\">\n                \n                <ng-template pTemplate=\"caption\">\n                    <div class=\"flex flex-column md:flex-row md:justify-content-between md:align-items-center\">\n                        <h5 class=\"m-0\">Manage Teams</h5>\n                        <span class=\"block mt-2 md:mt-0 p-input-icon-left\">\n                            <i class=\"pi pi-search\"></i>\n                            <input pInputText type=\"text\" (input)=\"onGlobalFilter(dt, $event)\" placeholder=\"Search...\" class=\"w-full sm:w-auto\"/>\n                        </span>\n                    </div>\n                </ng-template>\n                \n                <ng-template pTemplate=\"header\">\n                    <tr>\n                        <th style=\"width: 3rem\">\n                            <p-tableHeaderCheckbox></p-tableHeaderCheckbox>\n                        </th>\n                        <th pSortableColumn=\"id\">ID <p-sortIcon field=\"id\"></p-sortIcon></th>\n                        <th pSortableColumn=\"name\">Name <p-sortIcon field=\"name\"></p-sortIcon></th>\n                        <th pSortableColumn=\"speciality\">Speciality <p-sortIcon field=\"speciality\"></p-sortIcon></th>\n                        <th></th>\n                    </tr>\n                </ng-template>\n                \n                <ng-template pTemplate=\"body\" let-team>\n                    <tr>\n                        <td>\n                            <p-tableCheckbox [value]=\"team\"></p-tableCheckbox>\n                        </td>\n                        <td>{{team.id}}</td>\n                        <td>{{team.name}}</td>\n                        <td>{{team.speciality || 'N/A'}}</td>\n                        <td>\n                            <div class=\"flex\">\n                                <button pButton pRipple icon=\"pi pi-pencil\" class=\"p-button-rounded p-button-success mr-2\" (click)=\"editTeam(team)\"></button>\n                                <button pButton pRipple icon=\"pi pi-trash\" class=\"p-button-rounded p-button-warning\" (click)=\"deleteTeam(team)\"></button>\n                            </div>\n                        </td>\n                    </tr>\n                </ng-template>\n            </p-table>\n        </div>\n    </div>\n</div>\n\n<!-- Team Dialog -->\n<p-dialog [(visible)]=\"teamDialog\" [style]=\"{width: '450px'}\" header=\"Team Details\" [modal]=\"true\" class=\"p-fluid\">\n    <ng-template pTemplate=\"content\">\n        <div class=\"field\">\n            <label for=\"name\">Name</label>\n            <input type=\"text\" pInputText id=\"name\" [(ngModel)]=\"team.name\" required autofocus \n                   [ngClass]=\"{'ng-invalid ng-dirty' : submitted && !team.name}\"/>\n            <small class=\"ng-dirty ng-invalid\" *ngIf=\"submitted && !team.name\">Name is required.</small>\n        </div>\n        <div class=\"field\">\n            <label for=\"speciality\">Speciality</label>\n            <input type=\"text\" pInputText id=\"speciality\" [(ngModel)]=\"team.speciality\" \n                   placeholder=\"e.g., Development, UI/UX, Security\"/>\n        </div>\n    </ng-template>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple label=\"Cancel\" icon=\"pi pi-times\" class=\"p-button-text\" (click)=\"hideDialog()\"></button>\n        <button pButton pRipple label=\"Save\" icon=\"pi pi-check\" class=\"p-button-text\" (click)=\"saveTeam()\"></button>\n    </ng-template>\n</p-dialog>\n\n<!-- Delete Team Dialog -->\n<p-dialog [(visible)]=\"deleteTeamDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\n    <div class=\"flex align-items-center justify-content-center\">\n        <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\n        <span *ngIf=\"team\">Are you sure you want to delete <b>{{team.name}}</b>?</span>\n    </div>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteTeamDialog = false\"></button>\n        <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDelete()\"></button>\n    </ng-template>\n</p-dialog>\n\n<!-- Delete Teams Dialog -->\n<p-dialog [(visible)]=\"deleteTeamsDialog\" header=\"Confirm\" [modal]=\"true\" [style]=\"{width:'450px'}\">\n    <div class=\"flex align-items-center justify-content-center\">\n        <i class=\"pi pi-exclamation-triangle mr-3\" style=\"font-size: 2rem\"></i>\n        <span>Are you sure you want to delete selected teams?</span>\n    </div>\n    <ng-template pTemplate=\"footer\">\n        <button pButton pRipple icon=\"pi pi-times\" class=\"p-button-text\" label=\"No\" (click)=\"deleteTeamsDialog = false\"></button>\n        <button pButton pRipple icon=\"pi pi-check\" class=\"p-button-text\" label=\"Yes\" (click)=\"confirmDeleteSelected()\"></button>\n    </ng-template>\n</p-dialog>\n"]}, "metadata": {}, "sourceType": "module"}