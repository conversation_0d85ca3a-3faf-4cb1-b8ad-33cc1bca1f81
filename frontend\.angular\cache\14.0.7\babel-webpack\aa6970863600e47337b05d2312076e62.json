{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nconst _c0 = [\"container\"];\n\nfunction Splitter_ng_template_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Splitter_ng_template_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"mousedown\", function Splitter_ng_template_2_div_2_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onGutterMouseDown($event, i_r3));\n    })(\"touchstart\", function Splitter_ng_template_2_div_2_Template_div_touchstart_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const i_r3 = i0.ɵɵnextContext().index;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onGutterTouchStart($event, i_r3));\n    });\n    i0.ɵɵelement(1, \"div\", 6);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r5.gutterStyle());\n  }\n}\n\nfunction Splitter_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtemplate(1, Splitter_ng_template_2_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, Splitter_ng_template_2_div_2_Template, 2, 1, \"div\", 4);\n  }\n\n  if (rf & 2) {\n    const panel_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.panelStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.panelContainerClass())(\"ngStyle\", ctx_r1.panelStyle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", panel_r2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r3 !== ctx_r1.panels.length - 1);\n  }\n}\n\nclass Splitter {\n  constructor(cd, el) {\n    this.cd = cd;\n    this.el = el;\n    this.stateStorage = \"session\";\n    this.stateKey = null;\n    this.layout = \"horizontal\";\n    this.gutterSize = 4;\n    this.minSizes = [];\n    this.onResizeEnd = new EventEmitter();\n    this.onResizeStart = new EventEmitter();\n    this.nested = false;\n    this.panels = [];\n    this.dragging = false;\n    this.mouseMoveListener = null;\n    this.mouseUpListener = null;\n    this.touchMoveListener = null;\n    this.touchEndListener = null;\n    this.size = null;\n    this.gutterElement = null;\n    this.startPos = null;\n    this.prevPanelElement = null;\n    this.nextPanelElement = null;\n    this.nextPanelSize = null;\n    this.prevPanelSize = null;\n    this._panelSizes = [];\n    this.prevPanelIndex = null;\n  }\n\n  get panelSizes() {\n    return this._panelSizes;\n  }\n\n  set panelSizes(val) {\n    this._panelSizes = val;\n\n    if (this.el && this.el.nativeElement && this.panels.length > 0) {\n      let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n      let _panelSizes = [];\n      this.panels.map((panel, i) => {\n        let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n        let panelSize = panelInitialSize || 100 / this.panels.length;\n        _panelSizes[i] = panelSize;\n        children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n    }\n  }\n\n  ngOnInit() {\n    this.nested = this.isNested();\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'panel':\n          this.panels.push(item.template);\n          break;\n\n        default:\n          this.panels.push(item.template);\n          break;\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    if (this.panels && this.panels.length) {\n      let initialized = false;\n\n      if (this.isStateful()) {\n        initialized = this.restoreState();\n      }\n\n      if (!initialized) {\n        let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n        let _panelSizes = [];\n        this.panels.map((panel, i) => {\n          let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n          let panelSize = panelInitialSize || 100 / this.panels.length;\n          _panelSizes[i] = panelSize;\n          children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n        });\n        this._panelSizes = _panelSizes;\n      }\n    }\n  }\n\n  resizeStart(event, index) {\n    this.gutterElement = event.currentTarget;\n    this.size = this.horizontal() ? DomHandler.getWidth(this.containerViewChild.nativeElement) : DomHandler.getHeight(this.containerViewChild.nativeElement);\n    this.dragging = true;\n    this.startPos = this.horizontal() ? event.pageX || event.changedTouches[0].pageX : event.pageY || event.changedTouches[0].pageY;\n    this.prevPanelElement = this.gutterElement.previousElementSibling;\n    this.nextPanelElement = this.gutterElement.nextElementSibling;\n    this.prevPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true)) / this.size;\n    this.nextPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true)) / this.size;\n    this.prevPanelIndex = index;\n    DomHandler.addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    DomHandler.addClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.onResizeStart.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n  }\n\n  onResize(event) {\n    let newPos;\n    if (this.horizontal()) newPos = event.pageX * 100 / this.size - this.startPos * 100 / this.size;else newPos = event.pageY * 100 / this.size - this.startPos * 100 / this.size;\n    let newPrevPanelSize = this.prevPanelSize + newPos;\n    let newNextPanelSize = this.nextPanelSize - newPos;\n\n    if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n      this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      this._panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n      this._panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n    }\n  }\n\n  resizeEnd(event) {\n    if (this.isStateful()) {\n      this.saveState();\n    }\n\n    this.onResizeEnd.emit({\n      originalEvent: event,\n      sizes: this._panelSizes\n    });\n    DomHandler.removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n    DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n    this.clear();\n  }\n\n  onGutterMouseDown(event, index) {\n    this.resizeStart(event, index);\n    this.bindMouseListeners();\n  }\n\n  onGutterTouchStart(event, index) {\n    if (event.cancelable) {\n      this.resizeStart(event, index);\n      this.bindTouchListeners();\n      event.preventDefault();\n    }\n  }\n\n  onGutterTouchEnd(event) {\n    this.resizeEnd(event);\n    this.unbindTouchListeners();\n    if (event.cancelable) event.preventDefault();\n  }\n\n  validateResize(newPrevPanelSize, newNextPanelSize) {\n    if (this.minSizes.length >= 1 && this.minSizes[0] && this.minSizes[0] > newPrevPanelSize) {\n      return false;\n    }\n\n    if (this.minSizes.length > 1 && this.minSizes[1] && this.minSizes[1] > newNextPanelSize) {\n      return false;\n    }\n\n    return true;\n  }\n\n  bindMouseListeners() {\n    if (!this.mouseMoveListener) {\n      this.mouseMoveListener = event => this.onResize(event);\n\n      document.addEventListener('mousemove', this.mouseMoveListener);\n    }\n\n    if (!this.mouseUpListener) {\n      this.mouseUpListener = event => {\n        this.resizeEnd(event);\n        this.unbindMouseListeners();\n      };\n\n      document.addEventListener('mouseup', this.mouseUpListener);\n    }\n  }\n\n  bindTouchListeners() {\n    if (!this.touchMoveListener) {\n      this.touchMoveListener = event => this.onResize(event.changedTouches[0]);\n\n      document.addEventListener('touchmove', this.touchMoveListener);\n    }\n\n    if (!this.touchEndListener) {\n      this.touchEndListener = event => {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n      };\n\n      document.addEventListener('touchend', this.touchEndListener);\n    }\n  }\n\n  unbindMouseListeners() {\n    if (this.mouseMoveListener) {\n      document.removeEventListener('mousemove', this.mouseMoveListener);\n      this.mouseMoveListener = null;\n    }\n\n    if (this.mouseUpListener) {\n      document.removeEventListener('mouseup', this.mouseUpListener);\n      this.mouseUpListener = null;\n    }\n  }\n\n  unbindTouchListeners() {\n    if (this.touchMoveListener) {\n      document.removeEventListener('touchmove', this.touchMoveListener);\n      this.touchMoveListener = null;\n    }\n\n    if (this.touchEndListener) {\n      document.removeEventListener('touchend', this.touchEndListener);\n      this.touchEndListener = null;\n    }\n  }\n\n  clear() {\n    this.dragging = false;\n    this.size = null;\n    this.startPos = null;\n    this.prevPanelElement = null;\n    this.nextPanelElement = null;\n    this.prevPanelSize = null;\n    this.nextPanelSize = null;\n    this.gutterElement = null;\n    this.prevPanelIndex = null;\n  }\n\n  isNested() {\n    if (this.el.nativeElement) {\n      let parent = this.el.nativeElement.parentElement;\n\n      while (parent && !DomHandler.hasClass(parent, 'p-splitter')) {\n        parent = parent.parentElement;\n      }\n\n      return parent !== null;\n    } else {\n      return false;\n    }\n  }\n\n  isStateful() {\n    return this.stateKey != null;\n  }\n\n  getStorage() {\n    switch (this.stateStorage) {\n      case 'local':\n        return window.localStorage;\n\n      case 'session':\n        return window.sessionStorage;\n\n      default:\n        throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n    }\n  }\n\n  saveState() {\n    this.getStorage().setItem(this.stateKey, JSON.stringify(this._panelSizes));\n  }\n\n  restoreState() {\n    const storage = this.getStorage();\n    const stateString = storage.getItem(this.stateKey);\n\n    if (stateString) {\n      this._panelSizes = JSON.parse(stateString);\n      let children = [...this.containerViewChild.nativeElement.children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n      children.forEach((child, i) => {\n        child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + (this.panels.length - 1) * this.gutterSize + 'px)';\n      });\n      return true;\n    }\n\n    return false;\n  }\n\n  containerClass() {\n    return {\n      'p-splitter p-component': true,\n      'p-splitter-horizontal': this.layout === \"horizontal\",\n      'p-splitter-vertical': this.layout === \"vertical\"\n    };\n  }\n\n  panelContainerClass() {\n    return {\n      'p-splitter-panel': true,\n      'p-splitter-panel-nested': true\n    };\n  }\n\n  gutterStyle() {\n    if (this.horizontal()) return {\n      width: this.gutterSize + 'px'\n    };else return {\n      height: this.gutterSize + 'px'\n    };\n  }\n\n  horizontal() {\n    return this.layout === 'horizontal';\n  }\n\n}\n\nSplitter.ɵfac = function Splitter_Factory(t) {\n  return new (t || Splitter)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nSplitter.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Splitter,\n  selectors: [[\"p-splitter\"]],\n  contentQueries: function Splitter_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Splitter_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  hostVars: 2,\n  hostBindings: function Splitter_HostBindings(rf, ctx) {\n    if (rf & 2) {\n      i0.ɵɵclassProp(\"p-splitter-panel-nested\", ctx.nested);\n    }\n  },\n  inputs: {\n    styleClass: \"styleClass\",\n    panelStyleClass: \"panelStyleClass\",\n    style: \"style\",\n    panelStyle: \"panelStyle\",\n    stateStorage: \"stateStorage\",\n    stateKey: \"stateKey\",\n    layout: \"layout\",\n    gutterSize: \"gutterSize\",\n    minSizes: \"minSizes\",\n    panelSizes: \"panelSizes\"\n  },\n  outputs: {\n    onResizeEnd: \"onResizeEnd\",\n    onResizeStart: \"onResizeStart\"\n  },\n  decls: 3,\n  vars: 5,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"container\", \"\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-splitter-gutter\", 3, \"ngStyle\", \"mousedown\", \"touchstart\", 4, \"ngIf\"], [1, \"p-splitter-gutter\", 3, \"ngStyle\", \"mousedown\", \"touchstart\"], [1, \"p-splitter-gutter-handle\"]],\n  template: function Splitter_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵtemplate(2, Splitter_ng_template_2_Template, 3, 6, \"ng-template\", 2);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", ctx.containerClass())(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.panels);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n  styles: [\".p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Splitter, [{\n    type: Component,\n    args: [{\n      selector: 'p-splitter',\n      template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-template ngFor let-panel let-i=\"index\" [ngForOf]=\"panels\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div class=\"p-splitter-gutter\" *ngIf=\"i !== (panels.length - 1)\" [ngStyle]=\"gutterStyle()\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\" (touchstart)=\"onGutterTouchStart($event, i)\">\n                    <div class=\"p-splitter-gutter-handle\"></div>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'p-element',\n        '[class.p-splitter-panel-nested]': 'nested'\n      },\n      styles: [\".p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.ElementRef\n    }];\n  }, {\n    styleClass: [{\n      type: Input\n    }],\n    panelStyleClass: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    panelStyle: [{\n      type: Input\n    }],\n    stateStorage: [{\n      type: Input\n    }],\n    stateKey: [{\n      type: Input\n    }],\n    layout: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input\n    }],\n    minSizes: [{\n      type: Input\n    }],\n    onResizeEnd: [{\n      type: Output\n    }],\n    onResizeStart: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container', {\n        static: false\n      }]\n    }],\n    panelSizes: [{\n      type: Input\n    }]\n  });\n})();\n\nclass SplitterModule {}\n\nSplitterModule.ɵfac = function SplitterModule_Factory(t) {\n  return new (t || SplitterModule)();\n};\n\nSplitterModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: SplitterModule\n});\nSplitterModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SplitterModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Splitter, SharedModule],\n      declarations: [Splitter]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Splitter, SplitterModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "ContentChildren", "ViewChild", "NgModule", "i1", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "PrimeTemplate", "SharedModule", "Splitter", "constructor", "cd", "el", "stateStorage", "stateKey", "layout", "gutterSize", "minSizes", "onResizeEnd", "onResizeStart", "nested", "panels", "dragging", "mouseMoveListener", "mouseUpListener", "touchMoveListener", "touchEndListener", "size", "gutterElement", "startPos", "prevPanelElement", "nextPanelElement", "nextPanelSize", "prevPanelSize", "_panelSizes", "prevPanelIndex", "panelSizes", "val", "nativeElement", "length", "children", "filter", "child", "hasClass", "map", "panel", "i", "panelInitialSize", "panelSize", "style", "flexBasis", "ngOnInit", "isNested", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "push", "template", "ngAfterViewInit", "initialized", "isStateful", "restoreState", "resizeStart", "event", "index", "currentTarget", "horizontal", "getWidth", "containerViewChild", "getHeight", "pageX", "changedTouches", "pageY", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "getOuterWidth", "getOuterHeight", "addClass", "emit", "originalEvent", "sizes", "onResize", "newPos", "newPrevPanelSize", "newNextPanelSize", "validateResize", "resizeEnd", "saveState", "removeClass", "clear", "onGutterMouseDown", "bindMouseListeners", "onGutterTouchStart", "cancelable", "bindTouchListeners", "preventDefault", "onGutterTouchEnd", "unbindTouchListeners", "document", "addEventListener", "unbindMouseListeners", "removeEventListener", "parent", "parentElement", "getStorage", "window", "localStorage", "sessionStorage", "Error", "setItem", "JSON", "stringify", "storage", "stateString", "getItem", "parse", "containerClass", "panelContainerClass", "gutterStyle", "width", "height", "ɵfac", "ChangeDetectorRef", "ElementRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "type", "args", "selector", "encapsulation", "None", "changeDetection", "OnPush", "host", "styles", "styleClass", "panelStyleClass", "panelStyle", "static", "SplitterModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-splitter.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\n\nclass Splitter {\n    constructor(cd, el) {\n        this.cd = cd;\n        this.el = el;\n        this.stateStorage = \"session\";\n        this.stateKey = null;\n        this.layout = \"horizontal\";\n        this.gutterSize = 4;\n        this.minSizes = [];\n        this.onResizeEnd = new EventEmitter();\n        this.onResizeStart = new EventEmitter();\n        this.nested = false;\n        this.panels = [];\n        this.dragging = false;\n        this.mouseMoveListener = null;\n        this.mouseUpListener = null;\n        this.touchMoveListener = null;\n        this.touchEndListener = null;\n        this.size = null;\n        this.gutterElement = null;\n        this.startPos = null;\n        this.prevPanelElement = null;\n        this.nextPanelElement = null;\n        this.nextPanelSize = null;\n        this.prevPanelSize = null;\n        this._panelSizes = [];\n        this.prevPanelIndex = null;\n    }\n    get panelSizes() {\n        return this._panelSizes;\n    }\n    set panelSizes(val) {\n        this._panelSizes = val;\n        if (this.el && this.el.nativeElement && this.panels.length > 0) {\n            let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n            let _panelSizes = [];\n            this.panels.map((panel, i) => {\n                let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                let panelSize = panelInitialSize || (100 / this.panels.length);\n                _panelSizes[i] = panelSize;\n                children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + ((this.panels.length - 1) * this.gutterSize) + 'px)';\n            });\n        }\n    }\n    ngOnInit() {\n        this.nested = this.isNested();\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'panel':\n                    this.panels.push(item.template);\n                    break;\n                default:\n                    this.panels.push(item.template);\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        if (this.panels && this.panels.length) {\n            let initialized = false;\n            if (this.isStateful()) {\n                initialized = this.restoreState();\n            }\n            if (!initialized) {\n                let children = [...this.el.nativeElement.children[0].children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n                let _panelSizes = [];\n                this.panels.map((panel, i) => {\n                    let panelInitialSize = this.panelSizes.length - 1 >= i ? this.panelSizes[i] : null;\n                    let panelSize = panelInitialSize || (100 / this.panels.length);\n                    _panelSizes[i] = panelSize;\n                    children[i].style.flexBasis = 'calc(' + panelSize + '% - ' + ((this.panels.length - 1) * this.gutterSize) + 'px)';\n                });\n                this._panelSizes = _panelSizes;\n            }\n        }\n    }\n    resizeStart(event, index) {\n        this.gutterElement = event.currentTarget;\n        this.size = this.horizontal() ? DomHandler.getWidth(this.containerViewChild.nativeElement) : DomHandler.getHeight(this.containerViewChild.nativeElement);\n        this.dragging = true;\n        this.startPos = this.horizontal() ? (event.pageX || event.changedTouches[0].pageX) : (event.pageY || event.changedTouches[0].pageY);\n        this.prevPanelElement = this.gutterElement.previousElementSibling;\n        this.nextPanelElement = this.gutterElement.nextElementSibling;\n        this.prevPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.prevPanelElement, true) : DomHandler.getOuterHeight(this.prevPanelElement, true)) / this.size;\n        this.nextPanelSize = 100 * (this.horizontal() ? DomHandler.getOuterWidth(this.nextPanelElement, true) : DomHandler.getOuterHeight(this.nextPanelElement, true)) / this.size;\n        this.prevPanelIndex = index;\n        DomHandler.addClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        DomHandler.addClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n        this.onResizeStart.emit({ originalEvent: event, sizes: this._panelSizes });\n    }\n    onResize(event) {\n        let newPos;\n        if (this.horizontal())\n            newPos = (event.pageX * 100 / this.size) - (this.startPos * 100 / this.size);\n        else\n            newPos = (event.pageY * 100 / this.size) - (this.startPos * 100 / this.size);\n        let newPrevPanelSize = this.prevPanelSize + newPos;\n        let newNextPanelSize = this.nextPanelSize - newPos;\n        if (this.validateResize(newPrevPanelSize, newNextPanelSize)) {\n            this.prevPanelElement.style.flexBasis = 'calc(' + newPrevPanelSize + '% - ' + ((this.panels.length - 1) * this.gutterSize) + 'px)';\n            this.nextPanelElement.style.flexBasis = 'calc(' + newNextPanelSize + '% - ' + ((this.panels.length - 1) * this.gutterSize) + 'px)';\n            this._panelSizes[this.prevPanelIndex] = newPrevPanelSize;\n            this._panelSizes[this.prevPanelIndex + 1] = newNextPanelSize;\n        }\n    }\n    resizeEnd(event) {\n        if (this.isStateful()) {\n            this.saveState();\n        }\n        this.onResizeEnd.emit({ originalEvent: event, sizes: this._panelSizes });\n        DomHandler.removeClass(this.gutterElement, 'p-splitter-gutter-resizing');\n        DomHandler.removeClass(this.containerViewChild.nativeElement, 'p-splitter-resizing');\n        this.clear();\n    }\n    onGutterMouseDown(event, index) {\n        this.resizeStart(event, index);\n        this.bindMouseListeners();\n    }\n    onGutterTouchStart(event, index) {\n        if (event.cancelable) {\n            this.resizeStart(event, index);\n            this.bindTouchListeners();\n            event.preventDefault();\n        }\n    }\n    onGutterTouchEnd(event) {\n        this.resizeEnd(event);\n        this.unbindTouchListeners();\n        if (event.cancelable)\n            event.preventDefault();\n    }\n    validateResize(newPrevPanelSize, newNextPanelSize) {\n        if (this.minSizes.length >= 1 && this.minSizes[0] && this.minSizes[0] > newPrevPanelSize) {\n            return false;\n        }\n        if (this.minSizes.length > 1 && this.minSizes[1] && this.minSizes[1] > newNextPanelSize) {\n            return false;\n        }\n        return true;\n    }\n    bindMouseListeners() {\n        if (!this.mouseMoveListener) {\n            this.mouseMoveListener = event => this.onResize(event);\n            document.addEventListener('mousemove', this.mouseMoveListener);\n        }\n        if (!this.mouseUpListener) {\n            this.mouseUpListener = event => {\n                this.resizeEnd(event);\n                this.unbindMouseListeners();\n            };\n            document.addEventListener('mouseup', this.mouseUpListener);\n        }\n    }\n    bindTouchListeners() {\n        if (!this.touchMoveListener) {\n            this.touchMoveListener = event => this.onResize(event.changedTouches[0]);\n            document.addEventListener('touchmove', this.touchMoveListener);\n        }\n        if (!this.touchEndListener) {\n            this.touchEndListener = event => {\n                this.resizeEnd(event);\n                this.unbindTouchListeners();\n            };\n            document.addEventListener('touchend', this.touchEndListener);\n        }\n    }\n    unbindMouseListeners() {\n        if (this.mouseMoveListener) {\n            document.removeEventListener('mousemove', this.mouseMoveListener);\n            this.mouseMoveListener = null;\n        }\n        if (this.mouseUpListener) {\n            document.removeEventListener('mouseup', this.mouseUpListener);\n            this.mouseUpListener = null;\n        }\n    }\n    unbindTouchListeners() {\n        if (this.touchMoveListener) {\n            document.removeEventListener('touchmove', this.touchMoveListener);\n            this.touchMoveListener = null;\n        }\n        if (this.touchEndListener) {\n            document.removeEventListener('touchend', this.touchEndListener);\n            this.touchEndListener = null;\n        }\n    }\n    clear() {\n        this.dragging = false;\n        this.size = null;\n        this.startPos = null;\n        this.prevPanelElement = null;\n        this.nextPanelElement = null;\n        this.prevPanelSize = null;\n        this.nextPanelSize = null;\n        this.gutterElement = null;\n        this.prevPanelIndex = null;\n    }\n    isNested() {\n        if (this.el.nativeElement) {\n            let parent = this.el.nativeElement.parentElement;\n            while (parent && !DomHandler.hasClass(parent, 'p-splitter')) {\n                parent = parent.parentElement;\n            }\n            return parent !== null;\n        }\n        else {\n            return false;\n        }\n    }\n    isStateful() {\n        return this.stateKey != null;\n    }\n    getStorage() {\n        switch (this.stateStorage) {\n            case 'local':\n                return window.localStorage;\n            case 'session':\n                return window.sessionStorage;\n            default:\n                throw new Error(this.stateStorage + ' is not a valid value for the state storage, supported values are \"local\" and \"session\".');\n        }\n    }\n    saveState() {\n        this.getStorage().setItem(this.stateKey, JSON.stringify(this._panelSizes));\n    }\n    restoreState() {\n        const storage = this.getStorage();\n        const stateString = storage.getItem(this.stateKey);\n        if (stateString) {\n            this._panelSizes = JSON.parse(stateString);\n            let children = [...this.containerViewChild.nativeElement.children].filter(child => DomHandler.hasClass(child, 'p-splitter-panel'));\n            children.forEach((child, i) => {\n                child.style.flexBasis = 'calc(' + this._panelSizes[i] + '% - ' + ((this.panels.length - 1) * this.gutterSize) + 'px)';\n            });\n            return true;\n        }\n        return false;\n    }\n    containerClass() {\n        return {\n            'p-splitter p-component': true,\n            'p-splitter-horizontal': this.layout === \"horizontal\",\n            'p-splitter-vertical': this.layout === \"vertical\"\n        };\n    }\n    panelContainerClass() {\n        return {\n            'p-splitter-panel': true,\n            'p-splitter-panel-nested': true\n        };\n    }\n    gutterStyle() {\n        if (this.horizontal())\n            return { width: this.gutterSize + 'px' };\n        else\n            return { height: this.gutterSize + 'px' };\n    }\n    horizontal() {\n        return this.layout === 'horizontal';\n    }\n}\nSplitter.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Splitter, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nSplitter.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Splitter, selector: \"p-splitter\", inputs: { styleClass: \"styleClass\", panelStyleClass: \"panelStyleClass\", style: \"style\", panelStyle: \"panelStyle\", stateStorage: \"stateStorage\", stateKey: \"stateKey\", layout: \"layout\", gutterSize: \"gutterSize\", minSizes: \"minSizes\", panelSizes: \"panelSizes\" }, outputs: { onResizeEnd: \"onResizeEnd\", onResizeStart: \"onResizeStart\" }, host: { properties: { \"class.p-splitter-panel-nested\": \"nested\" }, classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }], ngImport: i0, template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-template ngFor let-panel let-i=\"index\" [ngForOf]=\"panels\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div class=\"p-splitter-gutter\" *ngIf=\"i !== (panels.length - 1)\" [ngStyle]=\"gutterStyle()\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\" (touchstart)=\"onGutterTouchStart($event, i)\">\n                    <div class=\"p-splitter-gutter-handle\"></div>\n                </div>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\".p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Splitter, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-splitter', template: `\n        <div #container [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\">\n            <ng-template ngFor let-panel let-i=\"index\" [ngForOf]=\"panels\">\n                <div [ngClass]=\"panelContainerClass()\" [class]=\"panelStyleClass\" [ngStyle]=\"panelStyle\">\n                    <ng-container *ngTemplateOutlet=\"panel\"></ng-container>\n                </div>\n                <div class=\"p-splitter-gutter\" *ngIf=\"i !== (panels.length - 1)\" [ngStyle]=\"gutterStyle()\"\n                    (mousedown)=\"onGutterMouseDown($event, i)\" (touchstart)=\"onGutterTouchStart($event, i)\">\n                    <div class=\"p-splitter-gutter-handle\"></div>\n                </div>\n            </ng-template>\n        </div>\n    `, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'class': 'p-element',\n                        '[class.p-splitter-panel-nested]': 'nested'\n                    }, styles: [\".p-splitter{display:flex;flex-wrap:nowrap}.p-splitter-vertical{flex-direction:column}.p-splitter-panel{flex-grow:1}.p-splitter-panel-nested{display:flex}.p-splitter-panel p-splitter{flex-grow:1}.p-splitter-panel .p-splitter{flex-grow:1;border:0 none}.p-splitter-gutter{flex-grow:0;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:col-resize}.p-splitter-horizontal.p-splitter-resizing{cursor:col-resize;-webkit-user-select:none;user-select:none}.p-splitter-horizontal>.p-splitter-gutter>.p-splitter-gutter-handle{height:24px;width:100%}.p-splitter-horizontal>.p-splitter-gutter{cursor:col-resize}.p-splitter-vertical.p-splitter-resizing{cursor:row-resize;-webkit-user-select:none;user-select:none}.p-splitter-vertical>.p-splitter-gutter{cursor:row-resize}.p-splitter-vertical>.p-splitter-gutter>.p-splitter-gutter-handle{width:24px;height:100%}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }]; }, propDecorators: { styleClass: [{\n                type: Input\n            }], panelStyleClass: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], panelStyle: [{\n                type: Input\n            }], stateStorage: [{\n                type: Input\n            }], stateKey: [{\n                type: Input\n            }], layout: [{\n                type: Input\n            }], gutterSize: [{\n                type: Input\n            }], minSizes: [{\n                type: Input\n            }], onResizeEnd: [{\n                type: Output\n            }], onResizeStart: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container', { static: false }]\n            }], panelSizes: [{\n                type: Input\n            }] } });\nclass SplitterModule {\n}\nSplitterModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SplitterModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nSplitterModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: SplitterModule, declarations: [Splitter], imports: [CommonModule], exports: [Splitter, SharedModule] });\nSplitterModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SplitterModule, imports: [CommonModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: SplitterModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Splitter, SharedModule],\n                    declarations: [Splitter]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Splitter, SplitterModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,iBAAlC,EAAqDC,uBAArD,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,eAA7F,EAA8GC,SAA9G,EAAyHC,QAAzH,QAAyI,eAAzI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,aAA5C;;;;;IAyQ2Fd,EAKvE,sB;;;;;;gBALuEA,E;;IAAAA,EAO3E,4B;IAP2EA,EAQvE;MARuEA,EAQvE;MAAA,aARuEA,EAQvE;MAAA,eARuEA,EAQvE;MAAA,OARuEA,EAQ1D,oDAAb;IAAA;MARuEA,EAQvE;MAAA,aARuEA,EAQvE;MAAA,eARuEA,EAQvE;MAAA,OARuEA,EAQd,qDAAzD;IAAA,E;IARuEA,EASvE,uB;IATuEA,EAU3E,e;;;;mBAV2EA,E;IAAAA,EAOV,4C;;;;;;IAPUA,EAI3E,4B;IAJ2EA,EAKvE,uF;IALuEA,EAM3E,e;IAN2EA,EAO3E,qE;;;;;;mBAP2EA,E;IAAAA,EAIpC,mC;IAJoCA,EAItE,kF;IAJsEA,EAKxD,a;IALwDA,EAKxD,yC;IALwDA,EAO3C,a;IAP2CA,EAO3C,sD;;;;AA9QhD,MAAMe,QAAN,CAAe;EACXC,WAAW,CAACC,EAAD,EAAKC,EAAL,EAAS;IAChB,KAAKD,EAAL,GAAUA,EAAV;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,YAAL,GAAoB,SAApB;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA,KAAKC,MAAL,GAAc,YAAd;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,QAAL,GAAgB,EAAhB;IACA,KAAKC,WAAL,GAAmB,IAAIvB,YAAJ,EAAnB;IACA,KAAKwB,aAAL,GAAqB,IAAIxB,YAAJ,EAArB;IACA,KAAKyB,MAAL,GAAc,KAAd;IACA,KAAKC,MAAL,GAAc,EAAd;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,iBAAL,GAAyB,IAAzB;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA,KAAKC,iBAAL,GAAyB,IAAzB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,IAAL,GAAY,IAAZ;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,aAAL,GAAqB,IAArB;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA,KAAKC,cAAL,GAAsB,IAAtB;EACH;;EACa,IAAVC,UAAU,GAAG;IACb,OAAO,KAAKF,WAAZ;EACH;;EACa,IAAVE,UAAU,CAACC,GAAD,EAAM;IAChB,KAAKH,WAAL,GAAmBG,GAAnB;;IACA,IAAI,KAAKzB,EAAL,IAAW,KAAKA,EAAL,CAAQ0B,aAAnB,IAAoC,KAAKjB,MAAL,CAAYkB,MAAZ,GAAqB,CAA7D,EAAgE;MAC5D,IAAIC,QAAQ,GAAG,CAAC,GAAG,KAAK5B,EAAL,CAAQ0B,aAAR,CAAsBE,QAAtB,CAA+B,CAA/B,EAAkCA,QAAtC,EAAgDC,MAAhD,CAAuDC,KAAK,IAAIpC,UAAU,CAACqC,QAAX,CAAoBD,KAApB,EAA2B,kBAA3B,CAAhE,CAAf;MACA,IAAIR,WAAW,GAAG,EAAlB;MACA,KAAKb,MAAL,CAAYuB,GAAZ,CAAgB,CAACC,KAAD,EAAQC,CAAR,KAAc;QAC1B,IAAIC,gBAAgB,GAAG,KAAKX,UAAL,CAAgBG,MAAhB,GAAyB,CAAzB,IAA8BO,CAA9B,GAAkC,KAAKV,UAAL,CAAgBU,CAAhB,CAAlC,GAAuD,IAA9E;QACA,IAAIE,SAAS,GAAGD,gBAAgB,IAAK,MAAM,KAAK1B,MAAL,CAAYkB,MAAvD;QACAL,WAAW,CAACY,CAAD,CAAX,GAAiBE,SAAjB;QACAR,QAAQ,CAACM,CAAD,CAAR,CAAYG,KAAZ,CAAkBC,SAAlB,GAA8B,UAAUF,SAAV,GAAsB,MAAtB,GAAgC,CAAC,KAAK3B,MAAL,CAAYkB,MAAZ,GAAqB,CAAtB,IAA2B,KAAKvB,UAAhE,GAA8E,KAA5G;MACH,CALD;IAMH;EACJ;;EACDmC,QAAQ,GAAG;IACP,KAAK/B,MAAL,GAAc,KAAKgC,QAAL,EAAd;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,OAAL;UACI,KAAKpC,MAAL,CAAYqC,IAAZ,CAAiBF,IAAI,CAACG,QAAtB;UACA;;QACJ;UACI,KAAKtC,MAAL,CAAYqC,IAAZ,CAAiBF,IAAI,CAACG,QAAtB;UACA;MANR;IAQH,CATD;EAUH;;EACDC,eAAe,GAAG;IACd,IAAI,KAAKvC,MAAL,IAAe,KAAKA,MAAL,CAAYkB,MAA/B,EAAuC;MACnC,IAAIsB,WAAW,GAAG,KAAlB;;MACA,IAAI,KAAKC,UAAL,EAAJ,EAAuB;QACnBD,WAAW,GAAG,KAAKE,YAAL,EAAd;MACH;;MACD,IAAI,CAACF,WAAL,EAAkB;QACd,IAAIrB,QAAQ,GAAG,CAAC,GAAG,KAAK5B,EAAL,CAAQ0B,aAAR,CAAsBE,QAAtB,CAA+B,CAA/B,EAAkCA,QAAtC,EAAgDC,MAAhD,CAAuDC,KAAK,IAAIpC,UAAU,CAACqC,QAAX,CAAoBD,KAApB,EAA2B,kBAA3B,CAAhE,CAAf;QACA,IAAIR,WAAW,GAAG,EAAlB;QACA,KAAKb,MAAL,CAAYuB,GAAZ,CAAgB,CAACC,KAAD,EAAQC,CAAR,KAAc;UAC1B,IAAIC,gBAAgB,GAAG,KAAKX,UAAL,CAAgBG,MAAhB,GAAyB,CAAzB,IAA8BO,CAA9B,GAAkC,KAAKV,UAAL,CAAgBU,CAAhB,CAAlC,GAAuD,IAA9E;UACA,IAAIE,SAAS,GAAGD,gBAAgB,IAAK,MAAM,KAAK1B,MAAL,CAAYkB,MAAvD;UACAL,WAAW,CAACY,CAAD,CAAX,GAAiBE,SAAjB;UACAR,QAAQ,CAACM,CAAD,CAAR,CAAYG,KAAZ,CAAkBC,SAAlB,GAA8B,UAAUF,SAAV,GAAsB,MAAtB,GAAgC,CAAC,KAAK3B,MAAL,CAAYkB,MAAZ,GAAqB,CAAtB,IAA2B,KAAKvB,UAAhE,GAA8E,KAA5G;QACH,CALD;QAMA,KAAKkB,WAAL,GAAmBA,WAAnB;MACH;IACJ;EACJ;;EACD8B,WAAW,CAACC,KAAD,EAAQC,KAAR,EAAe;IACtB,KAAKtC,aAAL,GAAqBqC,KAAK,CAACE,aAA3B;IACA,KAAKxC,IAAL,GAAY,KAAKyC,UAAL,KAAoB9D,UAAU,CAAC+D,QAAX,CAAoB,KAAKC,kBAAL,CAAwBhC,aAA5C,CAApB,GAAiFhC,UAAU,CAACiE,SAAX,CAAqB,KAAKD,kBAAL,CAAwBhC,aAA7C,CAA7F;IACA,KAAKhB,QAAL,GAAgB,IAAhB;IACA,KAAKO,QAAL,GAAgB,KAAKuC,UAAL,KAAqBH,KAAK,CAACO,KAAN,IAAeP,KAAK,CAACQ,cAAN,CAAqB,CAArB,EAAwBD,KAA5D,GAAsEP,KAAK,CAACS,KAAN,IAAeT,KAAK,CAACQ,cAAN,CAAqB,CAArB,EAAwBC,KAA7H;IACA,KAAK5C,gBAAL,GAAwB,KAAKF,aAAL,CAAmB+C,sBAA3C;IACA,KAAK5C,gBAAL,GAAwB,KAAKH,aAAL,CAAmBgD,kBAA3C;IACA,KAAK3C,aAAL,GAAqB,OAAO,KAAKmC,UAAL,KAAoB9D,UAAU,CAACuE,aAAX,CAAyB,KAAK/C,gBAA9B,EAAgD,IAAhD,CAApB,GAA4ExB,UAAU,CAACwE,cAAX,CAA0B,KAAKhD,gBAA/B,EAAiD,IAAjD,CAAnF,IAA6I,KAAKH,IAAvK;IACA,KAAKK,aAAL,GAAqB,OAAO,KAAKoC,UAAL,KAAoB9D,UAAU,CAACuE,aAAX,CAAyB,KAAK9C,gBAA9B,EAAgD,IAAhD,CAApB,GAA4EzB,UAAU,CAACwE,cAAX,CAA0B,KAAK/C,gBAA/B,EAAiD,IAAjD,CAAnF,IAA6I,KAAKJ,IAAvK;IACA,KAAKQ,cAAL,GAAsB+B,KAAtB;IACA5D,UAAU,CAACyE,QAAX,CAAoB,KAAKnD,aAAzB,EAAwC,4BAAxC;IACAtB,UAAU,CAACyE,QAAX,CAAoB,KAAKT,kBAAL,CAAwBhC,aAA5C,EAA2D,qBAA3D;IACA,KAAKnB,aAAL,CAAmB6D,IAAnB,CAAwB;MAAEC,aAAa,EAAEhB,KAAjB;MAAwBiB,KAAK,EAAE,KAAKhD;IAApC,CAAxB;EACH;;EACDiD,QAAQ,CAAClB,KAAD,EAAQ;IACZ,IAAImB,MAAJ;IACA,IAAI,KAAKhB,UAAL,EAAJ,EACIgB,MAAM,GAAInB,KAAK,CAACO,KAAN,GAAc,GAAd,GAAoB,KAAK7C,IAA1B,GAAmC,KAAKE,QAAL,GAAgB,GAAhB,GAAsB,KAAKF,IAAvE,CADJ,KAGIyD,MAAM,GAAInB,KAAK,CAACS,KAAN,GAAc,GAAd,GAAoB,KAAK/C,IAA1B,GAAmC,KAAKE,QAAL,GAAgB,GAAhB,GAAsB,KAAKF,IAAvE;IACJ,IAAI0D,gBAAgB,GAAG,KAAKpD,aAAL,GAAqBmD,MAA5C;IACA,IAAIE,gBAAgB,GAAG,KAAKtD,aAAL,GAAqBoD,MAA5C;;IACA,IAAI,KAAKG,cAAL,CAAoBF,gBAApB,EAAsCC,gBAAtC,CAAJ,EAA6D;MACzD,KAAKxD,gBAAL,CAAsBmB,KAAtB,CAA4BC,SAA5B,GAAwC,UAAUmC,gBAAV,GAA6B,MAA7B,GAAuC,CAAC,KAAKhE,MAAL,CAAYkB,MAAZ,GAAqB,CAAtB,IAA2B,KAAKvB,UAAvE,GAAqF,KAA7H;MACA,KAAKe,gBAAL,CAAsBkB,KAAtB,CAA4BC,SAA5B,GAAwC,UAAUoC,gBAAV,GAA6B,MAA7B,GAAuC,CAAC,KAAKjE,MAAL,CAAYkB,MAAZ,GAAqB,CAAtB,IAA2B,KAAKvB,UAAvE,GAAqF,KAA7H;MACA,KAAKkB,WAAL,CAAiB,KAAKC,cAAtB,IAAwCkD,gBAAxC;MACA,KAAKnD,WAAL,CAAiB,KAAKC,cAAL,GAAsB,CAAvC,IAA4CmD,gBAA5C;IACH;EACJ;;EACDE,SAAS,CAACvB,KAAD,EAAQ;IACb,IAAI,KAAKH,UAAL,EAAJ,EAAuB;MACnB,KAAK2B,SAAL;IACH;;IACD,KAAKvE,WAAL,CAAiB8D,IAAjB,CAAsB;MAAEC,aAAa,EAAEhB,KAAjB;MAAwBiB,KAAK,EAAE,KAAKhD;IAApC,CAAtB;IACA5B,UAAU,CAACoF,WAAX,CAAuB,KAAK9D,aAA5B,EAA2C,4BAA3C;IACAtB,UAAU,CAACoF,WAAX,CAAuB,KAAKpB,kBAAL,CAAwBhC,aAA/C,EAA8D,qBAA9D;IACA,KAAKqD,KAAL;EACH;;EACDC,iBAAiB,CAAC3B,KAAD,EAAQC,KAAR,EAAe;IAC5B,KAAKF,WAAL,CAAiBC,KAAjB,EAAwBC,KAAxB;IACA,KAAK2B,kBAAL;EACH;;EACDC,kBAAkB,CAAC7B,KAAD,EAAQC,KAAR,EAAe;IAC7B,IAAID,KAAK,CAAC8B,UAAV,EAAsB;MAClB,KAAK/B,WAAL,CAAiBC,KAAjB,EAAwBC,KAAxB;MACA,KAAK8B,kBAAL;MACA/B,KAAK,CAACgC,cAAN;IACH;EACJ;;EACDC,gBAAgB,CAACjC,KAAD,EAAQ;IACpB,KAAKuB,SAAL,CAAevB,KAAf;IACA,KAAKkC,oBAAL;IACA,IAAIlC,KAAK,CAAC8B,UAAV,EACI9B,KAAK,CAACgC,cAAN;EACP;;EACDV,cAAc,CAACF,gBAAD,EAAmBC,gBAAnB,EAAqC;IAC/C,IAAI,KAAKrE,QAAL,CAAcsB,MAAd,IAAwB,CAAxB,IAA6B,KAAKtB,QAAL,CAAc,CAAd,CAA7B,IAAiD,KAAKA,QAAL,CAAc,CAAd,IAAmBoE,gBAAxE,EAA0F;MACtF,OAAO,KAAP;IACH;;IACD,IAAI,KAAKpE,QAAL,CAAcsB,MAAd,GAAuB,CAAvB,IAA4B,KAAKtB,QAAL,CAAc,CAAd,CAA5B,IAAgD,KAAKA,QAAL,CAAc,CAAd,IAAmBqE,gBAAvE,EAAyF;MACrF,OAAO,KAAP;IACH;;IACD,OAAO,IAAP;EACH;;EACDO,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKtE,iBAAV,EAA6B;MACzB,KAAKA,iBAAL,GAAyB0C,KAAK,IAAI,KAAKkB,QAAL,CAAclB,KAAd,CAAlC;;MACAmC,QAAQ,CAACC,gBAAT,CAA0B,WAA1B,EAAuC,KAAK9E,iBAA5C;IACH;;IACD,IAAI,CAAC,KAAKC,eAAV,EAA2B;MACvB,KAAKA,eAAL,GAAuByC,KAAK,IAAI;QAC5B,KAAKuB,SAAL,CAAevB,KAAf;QACA,KAAKqC,oBAAL;MACH,CAHD;;MAIAF,QAAQ,CAACC,gBAAT,CAA0B,SAA1B,EAAqC,KAAK7E,eAA1C;IACH;EACJ;;EACDwE,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKvE,iBAAV,EAA6B;MACzB,KAAKA,iBAAL,GAAyBwC,KAAK,IAAI,KAAKkB,QAAL,CAAclB,KAAK,CAACQ,cAAN,CAAqB,CAArB,CAAd,CAAlC;;MACA2B,QAAQ,CAACC,gBAAT,CAA0B,WAA1B,EAAuC,KAAK5E,iBAA5C;IACH;;IACD,IAAI,CAAC,KAAKC,gBAAV,EAA4B;MACxB,KAAKA,gBAAL,GAAwBuC,KAAK,IAAI;QAC7B,KAAKuB,SAAL,CAAevB,KAAf;QACA,KAAKkC,oBAAL;MACH,CAHD;;MAIAC,QAAQ,CAACC,gBAAT,CAA0B,UAA1B,EAAsC,KAAK3E,gBAA3C;IACH;EACJ;;EACD4E,oBAAoB,GAAG;IACnB,IAAI,KAAK/E,iBAAT,EAA4B;MACxB6E,QAAQ,CAACG,mBAAT,CAA6B,WAA7B,EAA0C,KAAKhF,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;;IACD,IAAI,KAAKC,eAAT,EAA0B;MACtB4E,QAAQ,CAACG,mBAAT,CAA6B,SAA7B,EAAwC,KAAK/E,eAA7C;MACA,KAAKA,eAAL,GAAuB,IAAvB;IACH;EACJ;;EACD2E,oBAAoB,GAAG;IACnB,IAAI,KAAK1E,iBAAT,EAA4B;MACxB2E,QAAQ,CAACG,mBAAT,CAA6B,WAA7B,EAA0C,KAAK9E,iBAA/C;MACA,KAAKA,iBAAL,GAAyB,IAAzB;IACH;;IACD,IAAI,KAAKC,gBAAT,EAA2B;MACvB0E,QAAQ,CAACG,mBAAT,CAA6B,UAA7B,EAAyC,KAAK7E,gBAA9C;MACA,KAAKA,gBAAL,GAAwB,IAAxB;IACH;EACJ;;EACDiE,KAAK,GAAG;IACJ,KAAKrE,QAAL,GAAgB,KAAhB;IACA,KAAKK,IAAL,GAAY,IAAZ;IACA,KAAKE,QAAL,GAAgB,IAAhB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKC,gBAAL,GAAwB,IAAxB;IACA,KAAKE,aAAL,GAAqB,IAArB;IACA,KAAKD,aAAL,GAAqB,IAArB;IACA,KAAKJ,aAAL,GAAqB,IAArB;IACA,KAAKO,cAAL,GAAsB,IAAtB;EACH;;EACDiB,QAAQ,GAAG;IACP,IAAI,KAAKxC,EAAL,CAAQ0B,aAAZ,EAA2B;MACvB,IAAIkE,MAAM,GAAG,KAAK5F,EAAL,CAAQ0B,aAAR,CAAsBmE,aAAnC;;MACA,OAAOD,MAAM,IAAI,CAAClG,UAAU,CAACqC,QAAX,CAAoB6D,MAApB,EAA4B,YAA5B,CAAlB,EAA6D;QACzDA,MAAM,GAAGA,MAAM,CAACC,aAAhB;MACH;;MACD,OAAOD,MAAM,KAAK,IAAlB;IACH,CAND,MAOK;MACD,OAAO,KAAP;IACH;EACJ;;EACD1C,UAAU,GAAG;IACT,OAAO,KAAKhD,QAAL,IAAiB,IAAxB;EACH;;EACD4F,UAAU,GAAG;IACT,QAAQ,KAAK7F,YAAb;MACI,KAAK,OAAL;QACI,OAAO8F,MAAM,CAACC,YAAd;;MACJ,KAAK,SAAL;QACI,OAAOD,MAAM,CAACE,cAAd;;MACJ;QACI,MAAM,IAAIC,KAAJ,CAAU,KAAKjG,YAAL,GAAoB,0FAA9B,CAAN;IANR;EAQH;;EACD4E,SAAS,GAAG;IACR,KAAKiB,UAAL,GAAkBK,OAAlB,CAA0B,KAAKjG,QAA/B,EAAyCkG,IAAI,CAACC,SAAL,CAAe,KAAK/E,WAApB,CAAzC;EACH;;EACD6B,YAAY,GAAG;IACX,MAAMmD,OAAO,GAAG,KAAKR,UAAL,EAAhB;IACA,MAAMS,WAAW,GAAGD,OAAO,CAACE,OAAR,CAAgB,KAAKtG,QAArB,CAApB;;IACA,IAAIqG,WAAJ,EAAiB;MACb,KAAKjF,WAAL,GAAmB8E,IAAI,CAACK,KAAL,CAAWF,WAAX,CAAnB;MACA,IAAI3E,QAAQ,GAAG,CAAC,GAAG,KAAK8B,kBAAL,CAAwBhC,aAAxB,CAAsCE,QAA1C,EAAoDC,MAApD,CAA2DC,KAAK,IAAIpC,UAAU,CAACqC,QAAX,CAAoBD,KAApB,EAA2B,kBAA3B,CAApE,CAAf;MACAF,QAAQ,CAACe,OAAT,CAAiB,CAACb,KAAD,EAAQI,CAAR,KAAc;QAC3BJ,KAAK,CAACO,KAAN,CAAYC,SAAZ,GAAwB,UAAU,KAAKhB,WAAL,CAAiBY,CAAjB,CAAV,GAAgC,MAAhC,GAA0C,CAAC,KAAKzB,MAAL,CAAYkB,MAAZ,GAAqB,CAAtB,IAA2B,KAAKvB,UAA1E,GAAwF,KAAhH;MACH,CAFD;MAGA,OAAO,IAAP;IACH;;IACD,OAAO,KAAP;EACH;;EACDsG,cAAc,GAAG;IACb,OAAO;MACH,0BAA0B,IADvB;MAEH,yBAAyB,KAAKvG,MAAL,KAAgB,YAFtC;MAGH,uBAAuB,KAAKA,MAAL,KAAgB;IAHpC,CAAP;EAKH;;EACDwG,mBAAmB,GAAG;IAClB,OAAO;MACH,oBAAoB,IADjB;MAEH,2BAA2B;IAFxB,CAAP;EAIH;;EACDC,WAAW,GAAG;IACV,IAAI,KAAKpD,UAAL,EAAJ,EACI,OAAO;MAAEqD,KAAK,EAAE,KAAKzG,UAAL,GAAkB;IAA3B,CAAP,CADJ,KAGI,OAAO;MAAE0G,MAAM,EAAE,KAAK1G,UAAL,GAAkB;IAA5B,CAAP;EACP;;EACDoD,UAAU,GAAG;IACT,OAAO,KAAKrD,MAAL,KAAgB,YAAvB;EACH;;AArQU;;AAuQfN,QAAQ,CAACkH,IAAT;EAAA,iBAAqGlH,QAArG,EAA2Ff,EAA3F,mBAA+HA,EAAE,CAACkI,iBAAlI,GAA2FlI,EAA3F,mBAAgKA,EAAE,CAACmI,UAAnK;AAAA;;AACApH,QAAQ,CAACqH,IAAT,kBAD2FpI,EAC3F;EAAA,MAAyFe,QAAzF;EAAA;EAAA;IAAA;MAD2Ff,EAC3F,0BAA4lBa,aAA5lB;IAAA;;IAAA;MAAA;;MAD2Fb,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;IAAA;;IAAA;MAAA;;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAEnF,+BADR;MAD2FA,EAG/E,uEAFZ;MAD2FA,EAYnF,eAXR;IAAA;;IAAA;MAD2FA,EAEtC,2BADrD;MAD2FA,EAEnE,kEADxB;MAD2FA,EAGpC,aAFvD;MAD2FA,EAGpC,kCAFvD;IAAA;EAAA;EAAA,eAYu7BU,EAAE,CAAC2H,OAZ17B,EAYqhC3H,EAAE,CAAC4H,OAZxhC,EAYkpC5H,EAAE,CAAC6H,IAZrpC,EAYsvC7H,EAAE,CAAC8H,gBAZzvC,EAY65C9H,EAAE,CAAC+H,OAZh6C;EAAA;EAAA;EAAA;AAAA;;AAaA;EAAA,mDAd2FzI,EAc3F,mBAA2Fe,QAA3F,EAAiH,CAAC;IACtG2H,IAAI,EAAExI,SADgG;IAEtGyI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0B3E,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAZmB;MAYZ4E,aAAa,EAAE1I,iBAAiB,CAAC2I,IAZrB;MAY2BC,eAAe,EAAE3I,uBAAuB,CAAC4I,MAZpE;MAY4EC,IAAI,EAAE;QAC7E,SAAS,WADoE;QAE7E,mCAAmC;MAF0C,CAZlF;MAeIC,MAAM,EAAE,CAAC,02BAAD;IAfZ,CAAD;EAFgG,CAAD,CAAjH,EAkB4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAE1I,EAAE,CAACkI;IAAX,CAAD,EAAiC;MAAEQ,IAAI,EAAE1I,EAAE,CAACmI;IAAX,CAAjC,CAAP;EAAmE,CAlB7G,EAkB+H;IAAEgB,UAAU,EAAE,CAAC;MAC9HT,IAAI,EAAErI;IADwH,CAAD,CAAd;IAE/G+I,eAAe,EAAE,CAAC;MAClBV,IAAI,EAAErI;IADY,CAAD,CAF8F;IAI/GkD,KAAK,EAAE,CAAC;MACRmF,IAAI,EAAErI;IADE,CAAD,CAJwG;IAM/GgJ,UAAU,EAAE,CAAC;MACbX,IAAI,EAAErI;IADO,CAAD,CANmG;IAQ/Gc,YAAY,EAAE,CAAC;MACfuH,IAAI,EAAErI;IADS,CAAD,CARiG;IAU/Ge,QAAQ,EAAE,CAAC;MACXsH,IAAI,EAAErI;IADK,CAAD,CAVqG;IAY/GgB,MAAM,EAAE,CAAC;MACTqH,IAAI,EAAErI;IADG,CAAD,CAZuG;IAc/GiB,UAAU,EAAE,CAAC;MACboH,IAAI,EAAErI;IADO,CAAD,CAdmG;IAgB/GkB,QAAQ,EAAE,CAAC;MACXmH,IAAI,EAAErI;IADK,CAAD,CAhBqG;IAkB/GmB,WAAW,EAAE,CAAC;MACdkH,IAAI,EAAEpI;IADQ,CAAD,CAlBkG;IAoB/GmB,aAAa,EAAE,CAAC;MAChBiH,IAAI,EAAEpI;IADU,CAAD,CApBgG;IAsB/GsD,SAAS,EAAE,CAAC;MACZ8E,IAAI,EAAEnI,eADM;MAEZoI,IAAI,EAAE,CAAC9H,aAAD;IAFM,CAAD,CAtBoG;IAyB/G+D,kBAAkB,EAAE,CAAC;MACrB8D,IAAI,EAAElI,SADe;MAErBmI,IAAI,EAAE,CAAC,WAAD,EAAc;QAAEW,MAAM,EAAE;MAAV,CAAd;IAFe,CAAD,CAzB2F;IA4B/G5G,UAAU,EAAE,CAAC;MACbgG,IAAI,EAAErI;IADO,CAAD;EA5BmG,CAlB/H;AAAA;;AAiDA,MAAMkJ,cAAN,CAAqB;;AAErBA,cAAc,CAACtB,IAAf;EAAA,iBAA2GsB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAlE2FxJ,EAkE3F;EAAA,MAA4GuJ;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAnE2FzJ,EAmE3F;EAAA,UAAsIW,YAAtI,EAAoJG,YAApJ;AAAA;;AACA;EAAA,mDApE2Fd,EAoE3F,mBAA2FuJ,cAA3F,EAAuH,CAAC;IAC5Gb,IAAI,EAAEjI,QADsG;IAE5GkI,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAAC/I,YAAD,CADV;MAECgJ,OAAO,EAAE,CAAC5I,QAAD,EAAWD,YAAX,CAFV;MAGC8I,YAAY,EAAE,CAAC7I,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmBwI,cAAnB"}, "metadata": {}, "sourceType": "module"}