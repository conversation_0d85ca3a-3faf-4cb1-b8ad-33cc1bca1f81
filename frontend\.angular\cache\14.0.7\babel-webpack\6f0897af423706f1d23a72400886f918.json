{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\n\nfunction ConfirmPopup_div_0_i_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 8);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r2.confirmation.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-confirm-popup-icon\");\n  }\n}\n\nfunction ConfirmPopup_div_0_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ConfirmPopup_div_0_button_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.reject());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.confirmation.rejectButtonStyleClass || \"p-button-text\");\n    i0.ɵɵproperty(\"icon\", ctx_r3.confirmation.rejectIcon)(\"label\", ctx_r3.rejectButtonLabel)(\"ngClass\", \"p-confirm-popup-reject p-button-sm\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r3.rejectButtonLabel);\n  }\n}\n\nfunction ConfirmPopup_div_0_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ConfirmPopup_div_0_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.accept());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r4.confirmation.acceptButtonStyleClass);\n    i0.ɵɵproperty(\"icon\", ctx_r4.confirmation.acceptIcon)(\"label\", ctx_r4.acceptButtonLabel)(\"ngClass\", \"p-confirm-popup-accept p-button-sm\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r4.acceptButtonLabel);\n  }\n}\n\nconst _c0 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c1 = function (a1) {\n  return {\n    value: \"open\",\n    params: a1\n  };\n};\n\nfunction ConfirmPopup_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵlistener(\"click\", function ConfirmPopup_div_0_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onOverlayClick($event));\n    })(\"@animation.start\", function ConfirmPopup_div_0_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onAnimationStart($event));\n    })(\"@animation.done\", function ConfirmPopup_div_0_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 2, 3);\n    i0.ɵɵtemplate(3, ConfirmPopup_div_0_i_3_Template, 1, 3, \"i\", 4);\n    i0.ɵɵelementStart(4, \"span\", 5);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵtemplate(7, ConfirmPopup_div_0_button_7_Template, 1, 6, \"button\", 7);\n    i0.ɵɵtemplate(8, ConfirmPopup_div_0_button_8_Template, 1, 6, \"button\", 7);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.styleClass);\n    i0.ɵɵproperty(\"ngClass\", \"p-confirm-popup p-component\")(\"ngStyle\", ctx_r0.style)(\"@animation\", i0.ɵɵpureFunction1(12, _c1, i0.ɵɵpureFunction2(9, _c0, ctx_r0.showTransitionOptions, ctx_r0.hideTransitionOptions)));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.confirmation.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.confirmation.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.confirmation.rejectVisible !== false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.confirmation.acceptVisible !== false);\n  }\n}\n\nclass ConfirmPopup {\n  constructor(el, confirmationService, renderer, cd, config, overlayService) {\n    this.el = el;\n    this.confirmationService = confirmationService;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.defaultFocus = \"accept\";\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.subscription = this.confirmationService.requireConfirmation$.subscribe(confirmation => {\n      if (!confirmation) {\n        this.hide();\n        return;\n      }\n\n      if (confirmation.key === this.key) {\n        this.confirmation = confirmation;\n\n        if (this.confirmation.accept) {\n          this.confirmation.acceptEvent = new EventEmitter();\n          this.confirmation.acceptEvent.subscribe(this.confirmation.accept);\n        }\n\n        if (this.confirmation.reject) {\n          this.confirmation.rejectEvent = new EventEmitter();\n          this.confirmation.rejectEvent.subscribe(this.confirmation.reject);\n        }\n\n        this.visible = true;\n      }\n    });\n  }\n\n  get visible() {\n    return this._visible;\n  }\n\n  set visible(value) {\n    this._visible = value;\n    this.cd.markForCheck();\n  }\n\n  onAnimationStart(event) {\n    if (event.toState === 'open') {\n      this.container = event.element;\n      document.body.appendChild(this.container);\n      this.align();\n      this.bindListeners();\n      const element = this.getElementToFocus();\n\n      if (element) {\n        element.focus();\n      }\n    }\n  }\n\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.onContainerDestroy();\n        break;\n    }\n  }\n\n  getElementToFocus() {\n    switch (this.defaultFocus) {\n      case 'accept':\n        return DomHandler.findSingle(this.container, '.p-confirm-popup-accept');\n\n      case 'reject':\n        return DomHandler.findSingle(this.container, '.p-confirm-popup-reject');\n\n      case 'none':\n        return null;\n    }\n  }\n\n  align() {\n    if (this.autoZIndex) {\n      ZIndexUtils.set('overlay', this.container, this.config.zIndex.overlay);\n    }\n\n    DomHandler.absolutePosition(this.container, this.confirmation.target);\n    const containerOffset = DomHandler.getOffset(this.container);\n    const targetOffset = DomHandler.getOffset(this.confirmation.target);\n    let arrowLeft = 0;\n\n    if (containerOffset.left < targetOffset.left) {\n      arrowLeft = targetOffset.left - containerOffset.left;\n    }\n\n    this.container.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n\n    if (containerOffset.top < targetOffset.top) {\n      DomHandler.addClass(this.container, 'p-confirm-popup-flipped');\n    }\n  }\n\n  hide() {\n    this.visible = false;\n  }\n\n  accept() {\n    if (this.confirmation.acceptEvent) {\n      this.confirmation.acceptEvent.emit();\n    }\n\n    this.hide();\n  }\n\n  reject() {\n    if (this.confirmation.rejectEvent) {\n      this.confirmation.rejectEvent.emit();\n    }\n\n    this.hide();\n  }\n\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n  }\n\n  bindListeners() {\n    this.bindDocumentClickListener();\n    this.bindDocumentResizeListener();\n    this.bindScrollListener();\n  }\n\n  unbindListeners() {\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindScrollListener();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : document;\n      this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, event => {\n        let targetElement = this.confirmation.target;\n\n        if (this.container !== event.target && !this.container.contains(event.target) && targetElement !== event.target && !targetElement.contains(event.target)) {\n          this.hide();\n        }\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  onWindowResize() {\n    if (this.visible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.confirmation.target, () => {\n        if (this.visible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  unsubscribeConfirmationSubscriptions() {\n    if (this.confirmation) {\n      if (this.confirmation.acceptEvent) {\n        this.confirmation.acceptEvent.unsubscribe();\n      }\n\n      if (this.confirmation.rejectEvent) {\n        this.confirmation.rejectEvent.unsubscribe();\n      }\n    }\n  }\n\n  onContainerDestroy() {\n    this.unbindListeners();\n    this.unsubscribeConfirmationSubscriptions();\n\n    if (this.autoZIndex) {\n      ZIndexUtils.clear(this.container);\n    }\n\n    this.confirmation = null;\n    this.container = null;\n  }\n\n  restoreAppend() {\n    if (this.container) {\n      document.body.removeChild(this.container);\n    }\n\n    this.onContainerDestroy();\n  }\n\n  get acceptButtonLabel() {\n    return this.confirmation.acceptLabel || this.config.getTranslation(TranslationKeys.ACCEPT);\n  }\n\n  get rejectButtonLabel() {\n    return this.confirmation.rejectLabel || this.config.getTranslation(TranslationKeys.REJECT);\n  }\n\n  ngOnDestroy() {\n    this.restoreAppend();\n\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nConfirmPopup.ɵfac = function ConfirmPopup_Factory(t) {\n  return new (t || ConfirmPopup)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.ConfirmationService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n};\n\nConfirmPopup.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ConfirmPopup,\n  selectors: [[\"p-confirmPopup\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    key: \"key\",\n    defaultFocus: \"defaultFocus\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    visible: \"visible\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"ngStyle\", \"class\", \"click\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\", \"click\"], [1, \"p-confirm-popup-content\"], [\"content\", \"\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [1, \"p-confirm-popup-message\"], [1, \"p-confirm-popup-footer\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"icon\", \"label\", \"ngClass\", \"class\", \"click\", 4, \"ngIf\"], [3, \"ngClass\"], [\"type\", \"button\", \"pButton\", \"\", 3, \"icon\", \"label\", \"ngClass\", \"click\"]],\n  template: function ConfirmPopup_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, ConfirmPopup_div_0_Template, 9, 14, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.visible);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgStyle, i3.ButtonDirective],\n  styles: [\".p-confirm-popup{position:absolute;margin-top:10px;top:0;left:0}.p-confirm-popup-flipped{margin-top:0;margin-bottom:10px}.p-confirm-popup:after,.p-confirm-popup:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-confirm-popup:after{border-width:8px;margin-left:-8px}.p-confirm-popup:before{border-width:10px;margin-left:-10px}.p-confirm-popup-flipped:after,.p-confirm-popup-flipped:before{bottom:auto;top:100%}.p-confirm-popup.p-confirm-popup-flipped:after{border-bottom-color:transparent}.p-confirm-popup.p-confirm-popup-flipped:before{border-bottom-color:transparent}.p-confirm-popup .p-confirm-popup-content{display:flex;align-items:center}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('animation', [state('void', style({\n      transform: 'scaleY(0.8)',\n      opacity: 0\n    })), state('open', style({\n      transform: 'translateY(0)',\n      opacity: 1\n    })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => void', animate('{{hideTransitionParams}}'))])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfirmPopup, [{\n    type: Component,\n    args: [{\n      selector: 'p-confirmPopup',\n      template: `\n        <div *ngIf=\"visible\" [ngClass]=\"'p-confirm-popup p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: 'open', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n            (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div #content class=\"p-confirm-popup-content\">\n                <i [ngClass]=\"'p-confirm-popup-icon'\" [class]=\"confirmation.icon\" *ngIf=\"confirmation.icon\"></i>\n                <span class=\"p-confirm-popup-message\">{{confirmation.message}}</span>\n            </div>\n            <div class=\"p-confirm-popup-footer\">\n                <button type=\"button\" pButton [icon]=\"confirmation.rejectIcon\" [label]=\"rejectButtonLabel\" (click)=\"reject()\" [ngClass]=\"'p-confirm-popup-reject p-button-sm'\"\n                    [class]=\"confirmation.rejectButtonStyleClass || 'p-button-text'\" *ngIf=\"confirmation.rejectVisible !== false\" [attr.aria-label]=\"rejectButtonLabel\"></button>\n                <button type=\"button\" pButton [icon]=\"confirmation.acceptIcon\" [label]=\"acceptButtonLabel\" (click)=\"accept()\" [ngClass]=\"'p-confirm-popup-accept p-button-sm'\"\n                    [class]=\"confirmation.acceptButtonStyleClass\" *ngIf=\"confirmation.acceptVisible !== false\" [attr.aria-label]=\"acceptButtonLabel\"></button>\n            </div>\n        </div>\n    `,\n      animations: [trigger('animation', [state('void', style({\n        transform: 'scaleY(0.8)',\n        opacity: 0\n      })), state('open', style({\n        transform: 'translateY(0)',\n        opacity: 1\n      })), transition('void => open', animate('{{showTransitionParams}}')), transition('open => void', animate('{{hideTransitionParams}}'))])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-confirm-popup{position:absolute;margin-top:10px;top:0;left:0}.p-confirm-popup-flipped{margin-top:0;margin-bottom:10px}.p-confirm-popup:after,.p-confirm-popup:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-confirm-popup:after{border-width:8px;margin-left:-8px}.p-confirm-popup:before{border-width:10px;margin-left:-10px}.p-confirm-popup-flipped:after,.p-confirm-popup-flipped:before{bottom:auto;top:100%}.p-confirm-popup.p-confirm-popup-flipped:after{border-bottom-color:transparent}.p-confirm-popup.p-confirm-popup-flipped:before{border-bottom-color:transparent}.p-confirm-popup .p-confirm-popup-content{display:flex;align-items:center}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i1.ConfirmationService\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.PrimeNGConfig\n    }, {\n      type: i1.OverlayService\n    }];\n  }, {\n    key: [{\n      type: Input\n    }],\n    defaultFocus: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ConfirmPopupModule {}\n\nConfirmPopupModule.ɵfac = function ConfirmPopupModule_Factory(t) {\n  return new (t || ConfirmPopupModule)();\n};\n\nConfirmPopupModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ConfirmPopupModule\n});\nConfirmPopupModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, ButtonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ConfirmPopupModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, ButtonModule],\n      exports: [ConfirmPopup],\n      declarations: [ConfirmPopup]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ConfirmPopup, ConfirmPopupModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i2", "CommonModule", "i1", "Translation<PERSON>eys", "i3", "ButtonModule", "ZIndexUtils", "trigger", "state", "style", "transition", "animate", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ConfirmPopup", "constructor", "el", "confirmationService", "renderer", "cd", "config", "overlayService", "defaultFocus", "showTransitionOptions", "hideTransitionOptions", "autoZIndex", "baseZIndex", "subscription", "requireConfirmation$", "subscribe", "confirmation", "hide", "key", "accept", "acceptEvent", "reject", "rejectEvent", "visible", "_visible", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onAnimationStart", "event", "toState", "container", "element", "document", "body", "append<PERSON><PERSON><PERSON>", "align", "bindListeners", "getElementToFocus", "focus", "onAnimationEnd", "onContainerDestroy", "findSingle", "set", "zIndex", "overlay", "absolutePosition", "target", "containerOffset", "getOffset", "targetOffset", "arrowLeft", "left", "setProperty", "top", "addClass", "emit", "onOverlayClick", "add", "originalEvent", "nativeElement", "bindDocumentClickListener", "bindDocumentResizeListener", "bindScrollListener", "unbindListeners", "unbindDocumentClickListener", "unbindDocumentResizeListener", "unbindScrollListener", "documentClickListener", "documentEvent", "isIOS", "documentTarget", "ownerDocument", "listen", "targetElement", "contains", "onWindowResize", "isTouchDevice", "documentResizeListener", "bind", "window", "addEventListener", "removeEventListener", "<PERSON><PERSON><PERSON><PERSON>", "unsubscribeConfirmationSubscriptions", "unsubscribe", "clear", "restoreAppend", "<PERSON><PERSON><PERSON><PERSON>", "acceptButtonLabel", "acceptLabel", "getTranslation", "ACCEPT", "rejectButtonLabel", "<PERSON><PERSON><PERSON><PERSON>", "REJECT", "ngOnDestroy", "ɵfac", "ElementRef", "ConfirmationService", "Renderer2", "ChangeDetectorRef", "PrimeNGConfig", "OverlayService", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "ButtonDirective", "transform", "opacity", "type", "args", "selector", "template", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "ConfirmPopupModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-confirmpopup.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { ZIndexUtils } from 'primeng/utils';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\n\nclass ConfirmPopup {\n    constructor(el, confirmationService, renderer, cd, config, overlayService) {\n        this.el = el;\n        this.confirmationService = confirmationService;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.defaultFocus = \"accept\";\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.subscription = this.confirmationService.requireConfirmation$.subscribe(confirmation => {\n            if (!confirmation) {\n                this.hide();\n                return;\n            }\n            if (confirmation.key === this.key) {\n                this.confirmation = confirmation;\n                if (this.confirmation.accept) {\n                    this.confirmation.acceptEvent = new EventEmitter();\n                    this.confirmation.acceptEvent.subscribe(this.confirmation.accept);\n                }\n                if (this.confirmation.reject) {\n                    this.confirmation.rejectEvent = new EventEmitter();\n                    this.confirmation.rejectEvent.subscribe(this.confirmation.reject);\n                }\n                this.visible = true;\n            }\n        });\n    }\n    get visible() {\n        return this._visible;\n    }\n    set visible(value) {\n        this._visible = value;\n        this.cd.markForCheck();\n    }\n    onAnimationStart(event) {\n        if (event.toState === 'open') {\n            this.container = event.element;\n            document.body.appendChild(this.container);\n            this.align();\n            this.bindListeners();\n            const element = this.getElementToFocus();\n            if (element) {\n                element.focus();\n            }\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.onContainerDestroy();\n                break;\n        }\n    }\n    getElementToFocus() {\n        switch (this.defaultFocus) {\n            case 'accept':\n                return DomHandler.findSingle(this.container, '.p-confirm-popup-accept');\n            case 'reject':\n                return DomHandler.findSingle(this.container, '.p-confirm-popup-reject');\n            case 'none':\n                return null;\n        }\n    }\n    align() {\n        if (this.autoZIndex) {\n            ZIndexUtils.set('overlay', this.container, this.config.zIndex.overlay);\n        }\n        DomHandler.absolutePosition(this.container, this.confirmation.target);\n        const containerOffset = DomHandler.getOffset(this.container);\n        const targetOffset = DomHandler.getOffset(this.confirmation.target);\n        let arrowLeft = 0;\n        if (containerOffset.left < targetOffset.left) {\n            arrowLeft = targetOffset.left - containerOffset.left;\n        }\n        this.container.style.setProperty('--overlayArrowLeft', `${arrowLeft}px`);\n        if (containerOffset.top < targetOffset.top) {\n            DomHandler.addClass(this.container, 'p-confirm-popup-flipped');\n        }\n    }\n    hide() {\n        this.visible = false;\n    }\n    accept() {\n        if (this.confirmation.acceptEvent) {\n            this.confirmation.acceptEvent.emit();\n        }\n        this.hide();\n    }\n    reject() {\n        if (this.confirmation.rejectEvent) {\n            this.confirmation.rejectEvent.emit();\n        }\n        this.hide();\n    }\n    onOverlayClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n    }\n    bindListeners() {\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindScrollListener();\n    }\n    unbindListeners() {\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindScrollListener();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            let documentEvent = DomHandler.isIOS() ? 'touchstart' : 'click';\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : document;\n            this.documentClickListener = this.renderer.listen(documentTarget, documentEvent, (event) => {\n                let targetElement = this.confirmation.target;\n                if (this.container !== event.target && !this.container.contains(event.target) &&\n                    targetElement !== event.target && !targetElement.contains(event.target)) {\n                    this.hide();\n                }\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.visible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.confirmation.target, () => {\n                if (this.visible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    unsubscribeConfirmationSubscriptions() {\n        if (this.confirmation) {\n            if (this.confirmation.acceptEvent) {\n                this.confirmation.acceptEvent.unsubscribe();\n            }\n            if (this.confirmation.rejectEvent) {\n                this.confirmation.rejectEvent.unsubscribe();\n            }\n        }\n    }\n    onContainerDestroy() {\n        this.unbindListeners();\n        this.unsubscribeConfirmationSubscriptions();\n        if (this.autoZIndex) {\n            ZIndexUtils.clear(this.container);\n        }\n        this.confirmation = null;\n        this.container = null;\n    }\n    restoreAppend() {\n        if (this.container) {\n            document.body.removeChild(this.container);\n        }\n        this.onContainerDestroy();\n    }\n    get acceptButtonLabel() {\n        return this.confirmation.acceptLabel || this.config.getTranslation(TranslationKeys.ACCEPT);\n    }\n    get rejectButtonLabel() {\n        return this.confirmation.rejectLabel || this.config.getTranslation(TranslationKeys.REJECT);\n    }\n    ngOnDestroy() {\n        this.restoreAppend();\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nConfirmPopup.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopup, deps: [{ token: i0.ElementRef }, { token: i1.ConfirmationService }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nConfirmPopup.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: ConfirmPopup, selector: \"p-confirmPopup\", inputs: { key: \"key\", defaultFocus: \"defaultFocus\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", style: \"style\", styleClass: \"styleClass\", visible: \"visible\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div *ngIf=\"visible\" [ngClass]=\"'p-confirm-popup p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: 'open', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n            (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div #content class=\"p-confirm-popup-content\">\n                <i [ngClass]=\"'p-confirm-popup-icon'\" [class]=\"confirmation.icon\" *ngIf=\"confirmation.icon\"></i>\n                <span class=\"p-confirm-popup-message\">{{confirmation.message}}</span>\n            </div>\n            <div class=\"p-confirm-popup-footer\">\n                <button type=\"button\" pButton [icon]=\"confirmation.rejectIcon\" [label]=\"rejectButtonLabel\" (click)=\"reject()\" [ngClass]=\"'p-confirm-popup-reject p-button-sm'\"\n                    [class]=\"confirmation.rejectButtonStyleClass || 'p-button-text'\" *ngIf=\"confirmation.rejectVisible !== false\" [attr.aria-label]=\"rejectButtonLabel\"></button>\n                <button type=\"button\" pButton [icon]=\"confirmation.acceptIcon\" [label]=\"acceptButtonLabel\" (click)=\"accept()\" [ngClass]=\"'p-confirm-popup-accept p-button-sm'\"\n                    [class]=\"confirmation.acceptButtonStyleClass\" *ngIf=\"confirmation.acceptVisible !== false\" [attr.aria-label]=\"acceptButtonLabel\"></button>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-confirm-popup{position:absolute;margin-top:10px;top:0;left:0}.p-confirm-popup-flipped{margin-top:0;margin-bottom:10px}.p-confirm-popup:after,.p-confirm-popup:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-confirm-popup:after{border-width:8px;margin-left:-8px}.p-confirm-popup:before{border-width:10px;margin-left:-10px}.p-confirm-popup-flipped:after,.p-confirm-popup-flipped:before{bottom:auto;top:100%}.p-confirm-popup.p-confirm-popup-flipped:after{border-bottom-color:transparent}.p-confirm-popup.p-confirm-popup-flipped:before{border-bottom-color:transparent}.p-confirm-popup .p-confirm-popup-content{display:flex;align-items:center}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.ButtonDirective, selector: \"[pButton]\", inputs: [\"iconPos\", \"loadingIcon\", \"label\", \"icon\", \"loading\"] }], animations: [\n        trigger('animation', [\n            state('void', style({\n                transform: 'scaleY(0.8)',\n                opacity: 0\n            })),\n            state('open', style({\n                transform: 'translateY(0)',\n                opacity: 1\n            })),\n            transition('void => open', animate('{{showTransitionParams}}')),\n            transition('open => void', animate('{{hideTransitionParams}}')),\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopup, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-confirmPopup', template: `\n        <div *ngIf=\"visible\" [ngClass]=\"'p-confirm-popup p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\" (click)=\"onOverlayClick($event)\"\n            [@animation]=\"{value: 'open', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\"\n            (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\">\n            <div #content class=\"p-confirm-popup-content\">\n                <i [ngClass]=\"'p-confirm-popup-icon'\" [class]=\"confirmation.icon\" *ngIf=\"confirmation.icon\"></i>\n                <span class=\"p-confirm-popup-message\">{{confirmation.message}}</span>\n            </div>\n            <div class=\"p-confirm-popup-footer\">\n                <button type=\"button\" pButton [icon]=\"confirmation.rejectIcon\" [label]=\"rejectButtonLabel\" (click)=\"reject()\" [ngClass]=\"'p-confirm-popup-reject p-button-sm'\"\n                    [class]=\"confirmation.rejectButtonStyleClass || 'p-button-text'\" *ngIf=\"confirmation.rejectVisible !== false\" [attr.aria-label]=\"rejectButtonLabel\"></button>\n                <button type=\"button\" pButton [icon]=\"confirmation.acceptIcon\" [label]=\"acceptButtonLabel\" (click)=\"accept()\" [ngClass]=\"'p-confirm-popup-accept p-button-sm'\"\n                    [class]=\"confirmation.acceptButtonStyleClass\" *ngIf=\"confirmation.acceptVisible !== false\" [attr.aria-label]=\"acceptButtonLabel\"></button>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('animation', [\n                            state('void', style({\n                                transform: 'scaleY(0.8)',\n                                opacity: 0\n                            })),\n                            state('open', style({\n                                transform: 'translateY(0)',\n                                opacity: 1\n                            })),\n                            transition('void => open', animate('{{showTransitionParams}}')),\n                            transition('open => void', animate('{{hideTransitionParams}}')),\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-confirm-popup{position:absolute;margin-top:10px;top:0;left:0}.p-confirm-popup-flipped{margin-top:0;margin-bottom:10px}.p-confirm-popup:after,.p-confirm-popup:before{bottom:100%;left:calc(var(--overlayArrowLeft, 0) + 1.25rem);content:\\\" \\\";height:0;width:0;position:absolute;pointer-events:none}.p-confirm-popup:after{border-width:8px;margin-left:-8px}.p-confirm-popup:before{border-width:10px;margin-left:-10px}.p-confirm-popup-flipped:after,.p-confirm-popup-flipped:before{bottom:auto;top:100%}.p-confirm-popup.p-confirm-popup-flipped:after{border-bottom-color:transparent}.p-confirm-popup.p-confirm-popup-flipped:before{border-bottom-color:transparent}.p-confirm-popup .p-confirm-popup-content{display:flex;align-items:center}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i1.ConfirmationService }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }]; }, propDecorators: { key: [{\n                type: Input\n            }], defaultFocus: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }] } });\nclass ConfirmPopupModule {\n}\nConfirmPopupModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopupModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nConfirmPopupModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopupModule, declarations: [ConfirmPopup], imports: [CommonModule, ButtonModule], exports: [ConfirmPopup] });\nConfirmPopupModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopupModule, imports: [CommonModule, ButtonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ConfirmPopupModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, ButtonModule],\n                    exports: [ConfirmPopup],\n                    declarations: [ConfirmPopup]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConfirmPopup, ConfirmPopupModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,QAArF,QAAqG,eAArG;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,eAAT,QAAgC,aAAhC;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,OAAT,EAAkBC,KAAlB,EAAyBC,KAAzB,EAAgCC,UAAhC,EAA4CC,OAA5C,QAA2D,qBAA3D;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;;;;IA4M+FpB,EAM/E,qB;;;;mBAN+EA,E;IAAAA,EAMzC,qC;IANyCA,EAM5E,8C;;;;;;gBAN4EA,E;;IAAAA,EAU/E,+B;IAV+EA,EAUY;MAVZA,EAUY;MAAA,eAVZA,EAUY;MAAA,OAVZA,EAUqB,6BAAT;IAAA,E;IAVZA,EAWyE,e;;;;mBAXzEA,E;IAAAA,EAW3E,0E;IAX2EA,EAUjD,uI;IAViDA,EAWmC,oD;;;;;;gBAXnCA,E;;IAAAA,EAY/E,+B;IAZ+EA,EAYY;MAZZA,EAYY;MAAA,eAZZA,EAYY;MAAA,OAZZA,EAYqB,6BAAT;IAAA,E;IAZZA,EAasD,e;;;;mBAbtDA,E;IAAAA,EAa3E,uD;IAb2EA,EAYjD,uI;IAZiDA,EAagB,oD;;;;;;;;;;;;;;;;;;;;iBAbhBA,E;;IAAAA,EAEvF,4B;IAFuFA,EAEe;MAFfA,EAEe;MAAA,eAFfA,EAEe;MAAA,OAFfA,EAEwB,2CAAT;IAAA;MAFfA,EAEe;MAAA,gBAFfA,EAEe;MAAA,OAFfA,EAI/D,8CAF8E;IAAA;MAFfA,EAEe;MAAA,gBAFfA,EAEe;MAAA,OAFfA,EAIlB,4CAFiC;IAAA,E;IAFfA,EAKnF,+B;IALmFA,EAM/E,6D;IAN+EA,EAO/E,6B;IAP+EA,EAOzC,U;IAPyCA,EAOjB,iB;IAPiBA,EASnF,4B;IATmFA,EAU/E,uE;IAV+EA,EAY/E,uE;IAZ+EA,EAcnF,iB;;;;mBAdmFA,E;IAAAA,EAEN,8B;IAFMA,EAElE,6FAFkEA,EAElE,0BAFkEA,EAElE,sF;IAFkEA,EAMZ,a;IANYA,EAMZ,6C;IANYA,EAOzC,a;IAPyCA,EAOzC,+C;IAPyCA,EAWT,a;IAXSA,EAWT,gE;IAXSA,EAa5B,a;IAb4BA,EAa5B,gE;;;;AAvNnE,MAAMqB,YAAN,CAAmB;EACfC,WAAW,CAACC,EAAD,EAAKC,mBAAL,EAA0BC,QAA1B,EAAoCC,EAApC,EAAwCC,MAAxC,EAAgDC,cAAhD,EAAgE;IACvE,KAAKL,EAAL,GAAUA,EAAV;IACA,KAAKC,mBAAL,GAA2BA,mBAA3B;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,YAAL,GAAoB,QAApB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,YAAL,GAAoB,KAAKV,mBAAL,CAAyBW,oBAAzB,CAA8CC,SAA9C,CAAwDC,YAAY,IAAI;MACxF,IAAI,CAACA,YAAL,EAAmB;QACf,KAAKC,IAAL;QACA;MACH;;MACD,IAAID,YAAY,CAACE,GAAb,KAAqB,KAAKA,GAA9B,EAAmC;QAC/B,KAAKF,YAAL,GAAoBA,YAApB;;QACA,IAAI,KAAKA,YAAL,CAAkBG,MAAtB,EAA8B;UAC1B,KAAKH,YAAL,CAAkBI,WAAlB,GAAgC,IAAIxC,YAAJ,EAAhC;UACA,KAAKoC,YAAL,CAAkBI,WAAlB,CAA8BL,SAA9B,CAAwC,KAAKC,YAAL,CAAkBG,MAA1D;QACH;;QACD,IAAI,KAAKH,YAAL,CAAkBK,MAAtB,EAA8B;UAC1B,KAAKL,YAAL,CAAkBM,WAAlB,GAAgC,IAAI1C,YAAJ,EAAhC;UACA,KAAKoC,YAAL,CAAkBM,WAAlB,CAA8BP,SAA9B,CAAwC,KAAKC,YAAL,CAAkBK,MAA1D;QACH;;QACD,KAAKE,OAAL,GAAe,IAAf;MACH;IACJ,CAjBmB,CAApB;EAkBH;;EACU,IAAPA,OAAO,GAAG;IACV,OAAO,KAAKC,QAAZ;EACH;;EACU,IAAPD,OAAO,CAACE,KAAD,EAAQ;IACf,KAAKD,QAAL,GAAgBC,KAAhB;IACA,KAAKpB,EAAL,CAAQqB,YAAR;EACH;;EACDC,gBAAgB,CAACC,KAAD,EAAQ;IACpB,IAAIA,KAAK,CAACC,OAAN,KAAkB,MAAtB,EAA8B;MAC1B,KAAKC,SAAL,GAAiBF,KAAK,CAACG,OAAvB;MACAC,QAAQ,CAACC,IAAT,CAAcC,WAAd,CAA0B,KAAKJ,SAA/B;MACA,KAAKK,KAAL;MACA,KAAKC,aAAL;MACA,MAAML,OAAO,GAAG,KAAKM,iBAAL,EAAhB;;MACA,IAAIN,OAAJ,EAAa;QACTA,OAAO,CAACO,KAAR;MACH;IACJ;EACJ;;EACDC,cAAc,CAACX,KAAD,EAAQ;IAClB,QAAQA,KAAK,CAACC,OAAd;MACI,KAAK,MAAL;QACI,KAAKW,kBAAL;QACA;IAHR;EAKH;;EACDH,iBAAiB,GAAG;IAChB,QAAQ,KAAK7B,YAAb;MACI,KAAK,QAAL;QACI,OAAOV,UAAU,CAAC2C,UAAX,CAAsB,KAAKX,SAA3B,EAAsC,yBAAtC,CAAP;;MACJ,KAAK,QAAL;QACI,OAAOhC,UAAU,CAAC2C,UAAX,CAAsB,KAAKX,SAA3B,EAAsC,yBAAtC,CAAP;;MACJ,KAAK,MAAL;QACI,OAAO,IAAP;IANR;EAQH;;EACDK,KAAK,GAAG;IACJ,IAAI,KAAKxB,UAAT,EAAqB;MACjBnB,WAAW,CAACkD,GAAZ,CAAgB,SAAhB,EAA2B,KAAKZ,SAAhC,EAA2C,KAAKxB,MAAL,CAAYqC,MAAZ,CAAmBC,OAA9D;IACH;;IACD9C,UAAU,CAAC+C,gBAAX,CAA4B,KAAKf,SAAjC,EAA4C,KAAKd,YAAL,CAAkB8B,MAA9D;IACA,MAAMC,eAAe,GAAGjD,UAAU,CAACkD,SAAX,CAAqB,KAAKlB,SAA1B,CAAxB;IACA,MAAMmB,YAAY,GAAGnD,UAAU,CAACkD,SAAX,CAAqB,KAAKhC,YAAL,CAAkB8B,MAAvC,CAArB;IACA,IAAII,SAAS,GAAG,CAAhB;;IACA,IAAIH,eAAe,CAACI,IAAhB,GAAuBF,YAAY,CAACE,IAAxC,EAA8C;MAC1CD,SAAS,GAAGD,YAAY,CAACE,IAAb,GAAoBJ,eAAe,CAACI,IAAhD;IACH;;IACD,KAAKrB,SAAL,CAAenC,KAAf,CAAqByD,WAArB,CAAiC,oBAAjC,EAAwD,GAAEF,SAAU,IAApE;;IACA,IAAIH,eAAe,CAACM,GAAhB,GAAsBJ,YAAY,CAACI,GAAvC,EAA4C;MACxCvD,UAAU,CAACwD,QAAX,CAAoB,KAAKxB,SAAzB,EAAoC,yBAApC;IACH;EACJ;;EACDb,IAAI,GAAG;IACH,KAAKM,OAAL,GAAe,KAAf;EACH;;EACDJ,MAAM,GAAG;IACL,IAAI,KAAKH,YAAL,CAAkBI,WAAtB,EAAmC;MAC/B,KAAKJ,YAAL,CAAkBI,WAAlB,CAA8BmC,IAA9B;IACH;;IACD,KAAKtC,IAAL;EACH;;EACDI,MAAM,GAAG;IACL,IAAI,KAAKL,YAAL,CAAkBM,WAAtB,EAAmC;MAC/B,KAAKN,YAAL,CAAkBM,WAAlB,CAA8BiC,IAA9B;IACH;;IACD,KAAKtC,IAAL;EACH;;EACDuC,cAAc,CAAC5B,KAAD,EAAQ;IAClB,KAAKrB,cAAL,CAAoBkD,GAApB,CAAwB;MACpBC,aAAa,EAAE9B,KADK;MAEpBkB,MAAM,EAAE,KAAK5C,EAAL,CAAQyD;IAFI,CAAxB;EAIH;;EACDvB,aAAa,GAAG;IACZ,KAAKwB,yBAAL;IACA,KAAKC,0BAAL;IACA,KAAKC,kBAAL;EACH;;EACDC,eAAe,GAAG;IACd,KAAKC,2BAAL;IACA,KAAKC,4BAAL;IACA,KAAKC,oBAAL;EACH;;EACDN,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAKO,qBAAV,EAAiC;MAC7B,IAAIC,aAAa,GAAGtE,UAAU,CAACuE,KAAX,KAAqB,YAArB,GAAoC,OAAxD;MACA,MAAMC,cAAc,GAAG,KAAKpE,EAAL,GAAU,KAAKA,EAAL,CAAQyD,aAAR,CAAsBY,aAAhC,GAAgDvC,QAAvE;MACA,KAAKmC,qBAAL,GAA6B,KAAK/D,QAAL,CAAcoE,MAAd,CAAqBF,cAArB,EAAqCF,aAArC,EAAqDxC,KAAD,IAAW;QACxF,IAAI6C,aAAa,GAAG,KAAKzD,YAAL,CAAkB8B,MAAtC;;QACA,IAAI,KAAKhB,SAAL,KAAmBF,KAAK,CAACkB,MAAzB,IAAmC,CAAC,KAAKhB,SAAL,CAAe4C,QAAf,CAAwB9C,KAAK,CAACkB,MAA9B,CAApC,IACA2B,aAAa,KAAK7C,KAAK,CAACkB,MADxB,IACkC,CAAC2B,aAAa,CAACC,QAAd,CAAuB9C,KAAK,CAACkB,MAA7B,CADvC,EAC6E;UACzE,KAAK7B,IAAL;QACH;MACJ,CAN4B,CAA7B;IAOH;EACJ;;EACD+C,2BAA2B,GAAG;IAC1B,IAAI,KAAKG,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACDQ,cAAc,GAAG;IACb,IAAI,KAAKpD,OAAL,IAAgB,CAACzB,UAAU,CAAC8E,aAAX,EAArB,EAAiD;MAC7C,KAAK3D,IAAL;IACH;EACJ;;EACD4C,0BAA0B,GAAG;IACzB,KAAKgB,sBAAL,GAA8B,KAAKF,cAAL,CAAoBG,IAApB,CAAyB,IAAzB,CAA9B;IACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKH,sBAAvC;EACH;;EACDZ,4BAA4B,GAAG;IAC3B,IAAI,KAAKY,sBAAT,EAAiC;MAC7BE,MAAM,CAACE,mBAAP,CAA2B,QAA3B,EAAqC,KAAKJ,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDf,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKoB,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAInF,6BAAJ,CAAkC,KAAKiB,YAAL,CAAkB8B,MAApD,EAA4D,MAAM;QACnF,IAAI,KAAKvB,OAAT,EAAkB;UACd,KAAKN,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAKiE,aAAL,CAAmBpB,kBAAnB;EACH;;EACDI,oBAAoB,GAAG;IACnB,IAAI,KAAKgB,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBhB,oBAAnB;IACH;EACJ;;EACDiB,oCAAoC,GAAG;IACnC,IAAI,KAAKnE,YAAT,EAAuB;MACnB,IAAI,KAAKA,YAAL,CAAkBI,WAAtB,EAAmC;QAC/B,KAAKJ,YAAL,CAAkBI,WAAlB,CAA8BgE,WAA9B;MACH;;MACD,IAAI,KAAKpE,YAAL,CAAkBM,WAAtB,EAAmC;QAC/B,KAAKN,YAAL,CAAkBM,WAAlB,CAA8B8D,WAA9B;MACH;IACJ;EACJ;;EACD5C,kBAAkB,GAAG;IACjB,KAAKuB,eAAL;IACA,KAAKoB,oCAAL;;IACA,IAAI,KAAKxE,UAAT,EAAqB;MACjBnB,WAAW,CAAC6F,KAAZ,CAAkB,KAAKvD,SAAvB;IACH;;IACD,KAAKd,YAAL,GAAoB,IAApB;IACA,KAAKc,SAAL,GAAiB,IAAjB;EACH;;EACDwD,aAAa,GAAG;IACZ,IAAI,KAAKxD,SAAT,EAAoB;MAChBE,QAAQ,CAACC,IAAT,CAAcsD,WAAd,CAA0B,KAAKzD,SAA/B;IACH;;IACD,KAAKU,kBAAL;EACH;;EACoB,IAAjBgD,iBAAiB,GAAG;IACpB,OAAO,KAAKxE,YAAL,CAAkByE,WAAlB,IAAiC,KAAKnF,MAAL,CAAYoF,cAAZ,CAA2BrG,eAAe,CAACsG,MAA3C,CAAxC;EACH;;EACoB,IAAjBC,iBAAiB,GAAG;IACpB,OAAO,KAAK5E,YAAL,CAAkB6E,WAAlB,IAAiC,KAAKvF,MAAL,CAAYoF,cAAZ,CAA2BrG,eAAe,CAACyG,MAA3C,CAAxC;EACH;;EACDC,WAAW,GAAG;IACV,KAAKT,aAAL;;IACA,IAAI,KAAKzE,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBuE,WAAlB;IACH;EACJ;;AAxMc;;AA0MnBpF,YAAY,CAACgG,IAAb;EAAA,iBAAyGhG,YAAzG,EAA+FrB,EAA/F,mBAAuIA,EAAE,CAACsH,UAA1I,GAA+FtH,EAA/F,mBAAiKS,EAAE,CAAC8G,mBAApK,GAA+FvH,EAA/F,mBAAoMA,EAAE,CAACwH,SAAvM,GAA+FxH,EAA/F,mBAA6NA,EAAE,CAACyH,iBAAhO,GAA+FzH,EAA/F,mBAA8PS,EAAE,CAACiH,aAAjQ,GAA+F1H,EAA/F,mBAA2RS,EAAE,CAACkH,cAA9R;AAAA;;AACAtG,YAAY,CAACuG,IAAb,kBAD+F5H,EAC/F;EAAA,MAA6FqB,YAA7F;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD+FrB,EAEvF,4DADR;IAAA;;IAAA;MAD+FA,EAEjF,gCADd;IAAA;EAAA;EAAA,eAe4yBO,EAAE,CAACsH,OAf/yB,EAe04BtH,EAAE,CAACuH,IAf74B,EAe8+BvH,EAAE,CAACwH,OAfj/B,EAemkCpH,EAAE,CAACqH,eAftkC;EAAA;EAAA;EAAA;IAAA,WAe6rC,CACrrClH,OAAO,CAAC,WAAD,EAAc,CACjBC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;MAChBiH,SAAS,EAAE,aADK;MAEhBC,OAAO,EAAE;IAFO,CAAD,CAAd,CADY,EAKjBnH,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;MAChBiH,SAAS,EAAE,eADK;MAEhBC,OAAO,EAAE;IAFO,CAAD,CAAd,CALY,EASjBjH,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CATO,EAUjBD,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CAVO,CAAd,CAD8qC;EAf7rC;EAAA;AAAA;;AA6BA;EAAA,mDA9B+FlB,EA8B/F,mBAA2FqB,YAA3F,EAAqH,CAAC;IAC1G8G,IAAI,EAAEjI,SADoG;IAE1GkI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,gBAAZ;MAA8BC,QAAQ,EAAG;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAfmB;MAeZC,UAAU,EAAE,CACKzH,OAAO,CAAC,WAAD,EAAc,CACjBC,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;QAChBiH,SAAS,EAAE,aADK;QAEhBC,OAAO,EAAE;MAFO,CAAD,CAAd,CADY,EAKjBnH,KAAK,CAAC,MAAD,EAASC,KAAK,CAAC;QAChBiH,SAAS,EAAE,eADK;QAEhBC,OAAO,EAAE;MAFO,CAAD,CAAd,CALY,EASjBjH,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CATO,EAUjBD,UAAU,CAAC,cAAD,EAAiBC,OAAO,CAAC,0BAAD,CAAxB,CAVO,CAAd,CADZ,CAfA;MA4BIsH,eAAe,EAAErI,uBAAuB,CAACsI,MA5B7C;MA4BqDC,aAAa,EAAEtI,iBAAiB,CAACuI,IA5BtF;MA4B4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CA5BlG;MA8BIC,MAAM,EAAE,CAAC,+tBAAD;IA9BZ,CAAD;EAFoG,CAAD,CAArH,EAiC4B,YAAY;IAAE,OAAO,CAAC;MAAEV,IAAI,EAAEnI,EAAE,CAACsH;IAAX,CAAD,EAA0B;MAAEa,IAAI,EAAE1H,EAAE,CAAC8G;IAAX,CAA1B,EAA4D;MAAEY,IAAI,EAAEnI,EAAE,CAACwH;IAAX,CAA5D,EAAoF;MAAEW,IAAI,EAAEnI,EAAE,CAACyH;IAAX,CAApF,EAAoH;MAAEU,IAAI,EAAE1H,EAAE,CAACiH;IAAX,CAApH,EAAgJ;MAAES,IAAI,EAAE1H,EAAE,CAACkH;IAAX,CAAhJ,CAAP;EAAsL,CAjChO,EAiCkP;IAAEpF,GAAG,EAAE,CAAC;MAC1O4F,IAAI,EAAE9H;IADoO,CAAD,CAAP;IAElOwB,YAAY,EAAE,CAAC;MACfsG,IAAI,EAAE9H;IADS,CAAD,CAFoN;IAIlOyB,qBAAqB,EAAE,CAAC;MACxBqG,IAAI,EAAE9H;IADkB,CAAD,CAJ2M;IAMlO0B,qBAAqB,EAAE,CAAC;MACxBoG,IAAI,EAAE9H;IADkB,CAAD,CAN2M;IAQlO2B,UAAU,EAAE,CAAC;MACbmG,IAAI,EAAE9H;IADO,CAAD,CARsN;IAUlO4B,UAAU,EAAE,CAAC;MACbkG,IAAI,EAAE9H;IADO,CAAD,CAVsN;IAYlOW,KAAK,EAAE,CAAC;MACRmH,IAAI,EAAE9H;IADE,CAAD,CAZ2N;IAclOyI,UAAU,EAAE,CAAC;MACbX,IAAI,EAAE9H;IADO,CAAD,CAdsN;IAgBlOuC,OAAO,EAAE,CAAC;MACVuF,IAAI,EAAE9H;IADI,CAAD;EAhByN,CAjClP;AAAA;;AAoDA,MAAM0I,kBAAN,CAAyB;;AAEzBA,kBAAkB,CAAC1B,IAAnB;EAAA,iBAA+G0B,kBAA/G;AAAA;;AACAA,kBAAkB,CAACC,IAAnB,kBArF+FhJ,EAqF/F;EAAA,MAAgH+I;AAAhH;AACAA,kBAAkB,CAACE,IAAnB,kBAtF+FjJ,EAsF/F;EAAA,UAA8IQ,YAA9I,EAA4JI,YAA5J;AAAA;;AACA;EAAA,mDAvF+FZ,EAuF/F,mBAA2F+I,kBAA3F,EAA2H,CAAC;IAChHZ,IAAI,EAAE7H,QAD0G;IAEhH8H,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAAC1I,YAAD,EAAeI,YAAf,CADV;MAECuI,OAAO,EAAE,CAAC9H,YAAD,CAFV;MAGC+H,YAAY,EAAE,CAAC/H,YAAD;IAHf,CAAD;EAF0G,CAAD,CAA3H;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,YAAT,EAAuB0H,kBAAvB"}, "metadata": {}, "sourceType": "module"}