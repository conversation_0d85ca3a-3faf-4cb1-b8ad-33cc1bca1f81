{"ast": null, "code": "import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\n\nclass MutationObserverFactory {\n  create(callback) {\n    return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n  }\n\n}\n\nMutationObserverFactory.ɵfac = function MutationObserverFactory_Factory(t) {\n  return new (t || MutationObserverFactory)();\n};\n\nMutationObserverFactory.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: MutationObserverFactory,\n  factory: MutationObserverFactory.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MutationObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\n\n\nclass ContentObserver {\n  constructor(_mutationObserverFactory) {\n    this._mutationObserverFactory = _mutationObserverFactory;\n    /** Keeps track of the existing MutationObservers so they can be reused. */\n\n    this._observedElements = new Map();\n  }\n\n  ngOnDestroy() {\n    this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n  }\n\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this._observeElement(element);\n\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n\n        this._unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing MutationObserver if available, or creating a\n   * new one if not.\n   */\n\n\n  _observeElement(element) {\n    if (!this._observedElements.has(element)) {\n      const stream = new Subject();\n\n      const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n\n      if (observer) {\n        observer.observe(element, {\n          characterData: true,\n          childList: true,\n          subtree: true\n        });\n      }\n\n      this._observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this._observedElements.get(element).count++;\n    }\n\n    return this._observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n   * observing this element.\n   */\n\n\n  _unobserveElement(element) {\n    if (this._observedElements.has(element)) {\n      this._observedElements.get(element).count--;\n\n      if (!this._observedElements.get(element).count) {\n        this._cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying MutationObserver for the specified element. */\n\n\n  _cleanupObserver(element) {\n    if (this._observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this._observedElements.get(element);\n\n      if (observer) {\n        observer.disconnect();\n      }\n\n      stream.complete();\n\n      this._observedElements.delete(element);\n    }\n  }\n\n}\n\nContentObserver.ɵfac = function ContentObserver_Factory(t) {\n  return new (t || ContentObserver)(i0.ɵɵinject(MutationObserverFactory));\n};\n\nContentObserver.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: ContentObserver,\n  factory: ContentObserver.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ContentObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: MutationObserverFactory\n    }];\n  }, null);\n})();\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\n\n\nclass CdkObserveContent {\n  constructor(_contentObserver, _elementRef, _ngZone) {\n    this._contentObserver = _contentObserver;\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    /** Event emitted for each change in the element's content. */\n\n    this.event = new EventEmitter();\n    this._disabled = false;\n    this._currentSubscription = null;\n  }\n  /**\n   * Whether observing content is disabled. This option can be used\n   * to disconnect the underlying MutationObserver until it is needed.\n   */\n\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    this._disabled ? this._unsubscribe() : this._subscribe();\n  }\n  /** Debounce interval for emitting the changes. */\n\n\n  get debounce() {\n    return this._debounce;\n  }\n\n  set debounce(value) {\n    this._debounce = coerceNumberProperty(value);\n\n    this._subscribe();\n  }\n\n  ngAfterContentInit() {\n    if (!this._currentSubscription && !this.disabled) {\n      this._subscribe();\n    }\n  }\n\n  ngOnDestroy() {\n    this._unsubscribe();\n  }\n\n  _subscribe() {\n    this._unsubscribe();\n\n    const stream = this._contentObserver.observe(this._elementRef); // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n    // Consider brining it back inside the zone next time we're making breaking changes.\n    // Bringing it back inside can cause things like infinite change detection loops and changed\n    // after checked errors if people's code isn't handling it properly.\n\n\n    this._ngZone.runOutsideAngular(() => {\n      this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n    });\n  }\n\n  _unsubscribe() {\n    var _a;\n\n    (_a = this._currentSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n  }\n\n}\n\nCdkObserveContent.ɵfac = function CdkObserveContent_Factory(t) {\n  return new (t || CdkObserveContent)(i0.ɵɵdirectiveInject(ContentObserver), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nCdkObserveContent.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n  type: CdkObserveContent,\n  selectors: [[\"\", \"cdkObserveContent\", \"\"]],\n  inputs: {\n    disabled: [\"cdkObserveContentDisabled\", \"disabled\"],\n    debounce: \"debounce\"\n  },\n  outputs: {\n    event: \"cdkObserveContent\"\n  },\n  exportAs: [\"cdkObserveContent\"]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkObserveContent, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkObserveContent]',\n      exportAs: 'cdkObserveContent'\n    }]\n  }], function () {\n    return [{\n      type: ContentObserver\n    }, {\n      type: i0.ElementRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    event: [{\n      type: Output,\n      args: ['cdkObserveContent']\n    }],\n    disabled: [{\n      type: Input,\n      args: ['cdkObserveContentDisabled']\n    }],\n    debounce: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ObserversModule {}\n\nObserversModule.ɵfac = function ObserversModule_Factory(t) {\n  return new (t || ObserversModule)();\n};\n\nObserversModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ObserversModule\n});\nObserversModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [MutationObserverFactory]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ObserversModule, [{\n    type: NgModule,\n    args: [{\n      exports: [CdkObserveContent],\n      declarations: [CdkObserveContent],\n      providers: [MutationObserverFactory]\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };", "map": {"version": 3, "names": ["coerceElement", "coerceBooleanProperty", "coerceNumberProperty", "i0", "Injectable", "EventEmitter", "Directive", "Output", "Input", "NgModule", "Observable", "Subject", "debounceTime", "MutationObserverFactory", "create", "callback", "MutationObserver", "ɵfac", "ɵprov", "type", "args", "providedIn", "ContentObserver", "constructor", "_mutationObserverFactory", "_observedElements", "Map", "ngOnDestroy", "for<PERSON>ach", "_", "element", "_cleanupObserver", "observe", "elementOrRef", "observer", "stream", "_observeElement", "subscription", "subscribe", "unsubscribe", "_unobserveElement", "has", "mutations", "next", "characterData", "childList", "subtree", "set", "count", "get", "disconnect", "complete", "delete", "CdkObserveContent", "_contentObserver", "_elementRef", "_ngZone", "event", "_disabled", "_currentSubscription", "disabled", "value", "_unsubscribe", "_subscribe", "debounce", "_debounce", "ngAfterContentInit", "runOutsideAngular", "pipe", "_a", "ElementRef", "NgZone", "ɵdir", "selector", "exportAs", "ObserversModule", "ɵmod", "ɵinj", "exports", "declarations", "providers"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/observers.mjs"], "sourcesContent": ["import { coerceElement, coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { Observable, Subject } from 'rxjs';\nimport { debounceTime } from 'rxjs/operators';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Factory that creates a new MutationObserver and allows us to stub it out in unit tests.\n * @docs-private\n */\nclass MutationObserverFactory {\n    create(callback) {\n        return typeof MutationObserver === 'undefined' ? null : new MutationObserver(callback);\n    }\n}\nMutationObserverFactory.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: MutationObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nMutationObserverFactory.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: MutationObserverFactory, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: MutationObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass ContentObserver {\n    constructor(_mutationObserverFactory) {\n        this._mutationObserverFactory = _mutationObserverFactory;\n        /** Keeps track of the existing MutationObservers so they can be reused. */\n        this._observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this._observedElements.forEach((_, element) => this._cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this._observeElement(element);\n            const subscription = stream.subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this._unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing MutationObserver if available, or creating a\n     * new one if not.\n     */\n    _observeElement(element) {\n        if (!this._observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this._mutationObserverFactory.create(mutations => stream.next(mutations));\n            if (observer) {\n                observer.observe(element, {\n                    characterData: true,\n                    childList: true,\n                    subtree: true,\n                });\n            }\n            this._observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this._observedElements.get(element).count++;\n        }\n        return this._observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying MutationObserver if nobody else is\n     * observing this element.\n     */\n    _unobserveElement(element) {\n        if (this._observedElements.has(element)) {\n            this._observedElements.get(element).count--;\n            if (!this._observedElements.get(element).count) {\n                this._cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying MutationObserver for the specified element. */\n    _cleanupObserver(element) {\n        if (this._observedElements.has(element)) {\n            const { observer, stream } = this._observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this._observedElements.delete(element);\n        }\n    }\n}\nContentObserver.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ContentObserver, deps: [{ token: MutationObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable });\nContentObserver.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ContentObserver, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ContentObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () { return [{ type: MutationObserverFactory }]; } });\n/**\n * Directive that triggers a callback whenever the content of\n * its associated element has changed.\n */\nclass CdkObserveContent {\n    constructor(_contentObserver, _elementRef, _ngZone) {\n        this._contentObserver = _contentObserver;\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Event emitted for each change in the element's content. */\n        this.event = new EventEmitter();\n        this._disabled = false;\n        this._currentSubscription = null;\n    }\n    /**\n     * Whether observing content is disabled. This option can be used\n     * to disconnect the underlying MutationObserver until it is needed.\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        this._disabled ? this._unsubscribe() : this._subscribe();\n    }\n    /** Debounce interval for emitting the changes. */\n    get debounce() {\n        return this._debounce;\n    }\n    set debounce(value) {\n        this._debounce = coerceNumberProperty(value);\n        this._subscribe();\n    }\n    ngAfterContentInit() {\n        if (!this._currentSubscription && !this.disabled) {\n            this._subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this._unsubscribe();\n    }\n    _subscribe() {\n        this._unsubscribe();\n        const stream = this._contentObserver.observe(this._elementRef);\n        // TODO(mmalerba): We shouldn't be emitting on this @Output() outside the zone.\n        // Consider brining it back inside the zone next time we're making breaking changes.\n        // Bringing it back inside can cause things like infinite change detection loops and changed\n        // after checked errors if people's code isn't handling it properly.\n        this._ngZone.runOutsideAngular(() => {\n            this._currentSubscription = (this.debounce ? stream.pipe(debounceTime(this.debounce)) : stream).subscribe(this.event);\n        });\n    }\n    _unsubscribe() {\n        var _a;\n        (_a = this._currentSubscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n    }\n}\nCdkObserveContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkObserveContent, deps: [{ token: ContentObserver }, { token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive });\nCdkObserveContent.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"14.0.1\", type: CdkObserveContent, selector: \"[cdkObserveContent]\", inputs: { disabled: [\"cdkObserveContentDisabled\", \"disabled\"], debounce: \"debounce\" }, outputs: { event: \"cdkObserveContent\" }, exportAs: [\"cdkObserveContent\"], ngImport: i0 });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: CdkObserveContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkObserveContent]',\n                    exportAs: 'cdkObserveContent',\n                }]\n        }], ctorParameters: function () { return [{ type: ContentObserver }, { type: i0.ElementRef }, { type: i0.NgZone }]; }, propDecorators: { event: [{\n                type: Output,\n                args: ['cdkObserveContent']\n            }], disabled: [{\n                type: Input,\n                args: ['cdkObserveContentDisabled']\n            }], debounce: [{\n                type: Input\n            }] } });\nclass ObserversModule {\n}\nObserversModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ObserversModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nObserversModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: ObserversModule, declarations: [CdkObserveContent], exports: [CdkObserveContent] });\nObserversModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ObserversModule, providers: [MutationObserverFactory] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: ObserversModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkObserveContent],\n                    declarations: [CdkObserveContent],\n                    providers: [MutationObserverFactory],\n                }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkObserveContent, ContentObserver, MutationObserverFactory, ObserversModule };\n"], "mappings": "AAAA,SAASA,aAAT,EAAwBC,qBAAxB,EAA+CC,oBAA/C,QAA2E,uBAA3E;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,MAA9C,EAAsDC,KAAtD,EAA6DC,QAA7D,QAA6E,eAA7E;AACA,SAASC,UAAT,EAAqBC,OAArB,QAAoC,MAApC;AACA,SAASC,YAAT,QAA6B,gBAA7B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;AACA,MAAMC,uBAAN,CAA8B;EAC1BC,MAAM,CAACC,QAAD,EAAW;IACb,OAAO,OAAOC,gBAAP,KAA4B,WAA5B,GAA0C,IAA1C,GAAiD,IAAIA,gBAAJ,CAAqBD,QAArB,CAAxD;EACH;;AAHyB;;AAK9BF,uBAAuB,CAACI,IAAxB;EAAA,iBAAoHJ,uBAApH;AAAA;;AACAA,uBAAuB,CAACK,KAAxB,kBAD0Gf,EAC1G;EAAA,OAAwHU,uBAAxH;EAAA,SAAwHA,uBAAxH;EAAA,YAA6J;AAA7J;;AACA;EAAA,mDAF0GV,EAE1G,mBAA2FU,uBAA3F,EAAgI,CAAC;IACrHM,IAAI,EAAEf,UAD+G;IAErHgB,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAF+G,CAAD,CAAhI;AAAA;AAIA;;;AACA,MAAMC,eAAN,CAAsB;EAClBC,WAAW,CAACC,wBAAD,EAA2B;IAClC,KAAKA,wBAAL,GAAgCA,wBAAhC;IACA;;IACA,KAAKC,iBAAL,GAAyB,IAAIC,GAAJ,EAAzB;EACH;;EACDC,WAAW,GAAG;IACV,KAAKF,iBAAL,CAAuBG,OAAvB,CAA+B,CAACC,CAAD,EAAIC,OAAJ,KAAgB,KAAKC,gBAAL,CAAsBD,OAAtB,CAA/C;EACH;;EACDE,OAAO,CAACC,YAAD,EAAe;IAClB,MAAMH,OAAO,GAAG9B,aAAa,CAACiC,YAAD,CAA7B;IACA,OAAO,IAAIvB,UAAJ,CAAgBwB,QAAD,IAAc;MAChC,MAAMC,MAAM,GAAG,KAAKC,eAAL,CAAqBN,OAArB,CAAf;;MACA,MAAMO,YAAY,GAAGF,MAAM,CAACG,SAAP,CAAiBJ,QAAjB,CAArB;MACA,OAAO,MAAM;QACTG,YAAY,CAACE,WAAb;;QACA,KAAKC,iBAAL,CAAuBV,OAAvB;MACH,CAHD;IAIH,CAPM,CAAP;EAQH;EACD;AACJ;AACA;AACA;;;EACIM,eAAe,CAACN,OAAD,EAAU;IACrB,IAAI,CAAC,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAL,EAA0C;MACtC,MAAMK,MAAM,GAAG,IAAIxB,OAAJ,EAAf;;MACA,MAAMuB,QAAQ,GAAG,KAAKV,wBAAL,CAA8BV,MAA9B,CAAqC4B,SAAS,IAAIP,MAAM,CAACQ,IAAP,CAAYD,SAAZ,CAAlD,CAAjB;;MACA,IAAIR,QAAJ,EAAc;QACVA,QAAQ,CAACF,OAAT,CAAiBF,OAAjB,EAA0B;UACtBc,aAAa,EAAE,IADO;UAEtBC,SAAS,EAAE,IAFW;UAGtBC,OAAO,EAAE;QAHa,CAA1B;MAKH;;MACD,KAAKrB,iBAAL,CAAuBsB,GAAvB,CAA2BjB,OAA3B,EAAoC;QAAEI,QAAF;QAAYC,MAAZ;QAAoBa,KAAK,EAAE;MAA3B,CAApC;IACH,CAXD,MAYK;MACD,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAApC;IACH;;IACD,OAAO,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCK,MAA3C;EACH;EACD;AACJ;AACA;AACA;;;EACIK,iBAAiB,CAACV,OAAD,EAAU;IACvB,IAAI,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAJ,EAAyC;MACrC,KAAKL,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAApC;;MACA,IAAI,CAAC,KAAKvB,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,EAAoCkB,KAAzC,EAAgD;QAC5C,KAAKjB,gBAAL,CAAsBD,OAAtB;MACH;IACJ;EACJ;EACD;;;EACAC,gBAAgB,CAACD,OAAD,EAAU;IACtB,IAAI,KAAKL,iBAAL,CAAuBgB,GAAvB,CAA2BX,OAA3B,CAAJ,EAAyC;MACrC,MAAM;QAAEI,QAAF;QAAYC;MAAZ,IAAuB,KAAKV,iBAAL,CAAuBwB,GAAvB,CAA2BnB,OAA3B,CAA7B;;MACA,IAAII,QAAJ,EAAc;QACVA,QAAQ,CAACgB,UAAT;MACH;;MACDf,MAAM,CAACgB,QAAP;;MACA,KAAK1B,iBAAL,CAAuB2B,MAAvB,CAA8BtB,OAA9B;IACH;EACJ;;AAhEiB;;AAkEtBR,eAAe,CAACL,IAAhB;EAAA,iBAA4GK,eAA5G,EAzE0GnB,EAyE1G,UAA6IU,uBAA7I;AAAA;;AACAS,eAAe,CAACJ,KAAhB,kBA1E0Gf,EA0E1G;EAAA,OAAgHmB,eAAhH;EAAA,SAAgHA,eAAhH;EAAA,YAA6I;AAA7I;;AACA;EAAA,mDA3E0GnB,EA2E1G,mBAA2FmB,eAA3F,EAAwH,CAAC;IAC7GH,IAAI,EAAEf,UADuG;IAE7GgB,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFuG,CAAD,CAAxH,EAG4B,YAAY;IAAE,OAAO,CAAC;MAAEF,IAAI,EAAEN;IAAR,CAAD,CAAP;EAA6C,CAHvF;AAAA;AAIA;AACA;AACA;AACA;;;AACA,MAAMwC,iBAAN,CAAwB;EACpB9B,WAAW,CAAC+B,gBAAD,EAAmBC,WAAnB,EAAgCC,OAAhC,EAAyC;IAChD,KAAKF,gBAAL,GAAwBA,gBAAxB;IACA,KAAKC,WAAL,GAAmBA,WAAnB;IACA,KAAKC,OAAL,GAAeA,OAAf;IACA;;IACA,KAAKC,KAAL,GAAa,IAAIpD,YAAJ,EAAb;IACA,KAAKqD,SAAL,GAAiB,KAAjB;IACA,KAAKC,oBAAL,GAA4B,IAA5B;EACH;EACD;AACJ;AACA;AACA;;;EACgB,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKF,SAAZ;EACH;;EACW,IAARE,QAAQ,CAACC,KAAD,EAAQ;IAChB,KAAKH,SAAL,GAAiBzD,qBAAqB,CAAC4D,KAAD,CAAtC;IACA,KAAKH,SAAL,GAAiB,KAAKI,YAAL,EAAjB,GAAuC,KAAKC,UAAL,EAAvC;EACH;EACD;;;EACY,IAARC,QAAQ,GAAG;IACX,OAAO,KAAKC,SAAZ;EACH;;EACW,IAARD,QAAQ,CAACH,KAAD,EAAQ;IAChB,KAAKI,SAAL,GAAiB/D,oBAAoB,CAAC2D,KAAD,CAArC;;IACA,KAAKE,UAAL;EACH;;EACDG,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKP,oBAAN,IAA8B,CAAC,KAAKC,QAAxC,EAAkD;MAC9C,KAAKG,UAAL;IACH;EACJ;;EACDpC,WAAW,GAAG;IACV,KAAKmC,YAAL;EACH;;EACDC,UAAU,GAAG;IACT,KAAKD,YAAL;;IACA,MAAM3B,MAAM,GAAG,KAAKmB,gBAAL,CAAsBtB,OAAtB,CAA8B,KAAKuB,WAAnC,CAAf,CAFS,CAGT;IACA;IACA;IACA;;;IACA,KAAKC,OAAL,CAAaW,iBAAb,CAA+B,MAAM;MACjC,KAAKR,oBAAL,GAA4B,CAAC,KAAKK,QAAL,GAAgB7B,MAAM,CAACiC,IAAP,CAAYxD,YAAY,CAAC,KAAKoD,QAAN,CAAxB,CAAhB,GAA2D7B,MAA5D,EAAoEG,SAApE,CAA8E,KAAKmB,KAAnF,CAA5B;IACH,CAFD;EAGH;;EACDK,YAAY,GAAG;IACX,IAAIO,EAAJ;;IACA,CAACA,EAAE,GAAG,KAAKV,oBAAX,MAAqC,IAArC,IAA6CU,EAAE,KAAK,KAAK,CAAzD,GAA6D,KAAK,CAAlE,GAAsEA,EAAE,CAAC9B,WAAH,EAAtE;EACH;;AAnDmB;;AAqDxBc,iBAAiB,CAACpC,IAAlB;EAAA,iBAA8GoC,iBAA9G,EAxI0GlD,EAwI1G,mBAAiJmB,eAAjJ,GAxI0GnB,EAwI1G,mBAA6KA,EAAE,CAACmE,UAAhL,GAxI0GnE,EAwI1G,mBAAuMA,EAAE,CAACoE,MAA1M;AAAA;;AACAlB,iBAAiB,CAACmB,IAAlB,kBAzI0GrE,EAyI1G;EAAA,MAAkGkD,iBAAlG;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;;AACA;EAAA,mDA1I0GlD,EA0I1G,mBAA2FkD,iBAA3F,EAA0H,CAAC;IAC/GlC,IAAI,EAAEb,SADyG;IAE/Gc,IAAI,EAAE,CAAC;MACCqD,QAAQ,EAAE,qBADX;MAECC,QAAQ,EAAE;IAFX,CAAD;EAFyG,CAAD,CAA1H,EAM4B,YAAY;IAAE,OAAO,CAAC;MAAEvD,IAAI,EAAEG;IAAR,CAAD,EAA4B;MAAEH,IAAI,EAAEhB,EAAE,CAACmE;IAAX,CAA5B,EAAqD;MAAEnD,IAAI,EAAEhB,EAAE,CAACoE;IAAX,CAArD,CAAP;EAAmF,CAN7H,EAM+I;IAAEd,KAAK,EAAE,CAAC;MACzItC,IAAI,EAAEZ,MADmI;MAEzIa,IAAI,EAAE,CAAC,mBAAD;IAFmI,CAAD,CAAT;IAG/HwC,QAAQ,EAAE,CAAC;MACXzC,IAAI,EAAEX,KADK;MAEXY,IAAI,EAAE,CAAC,2BAAD;IAFK,CAAD,CAHqH;IAM/H4C,QAAQ,EAAE,CAAC;MACX7C,IAAI,EAAEX;IADK,CAAD;EANqH,CAN/I;AAAA;;AAeA,MAAMmE,eAAN,CAAsB;;AAEtBA,eAAe,CAAC1D,IAAhB;EAAA,iBAA4G0D,eAA5G;AAAA;;AACAA,eAAe,CAACC,IAAhB,kBA5J0GzE,EA4J1G;EAAA,MAA6GwE;AAA7G;AACAA,eAAe,CAACE,IAAhB,kBA7J0G1E,EA6J1G;EAAA,WAAyI,CAACU,uBAAD;AAAzI;;AACA;EAAA,mDA9J0GV,EA8J1G,mBAA2FwE,eAA3F,EAAwH,CAAC;IAC7GxD,IAAI,EAAEV,QADuG;IAE7GW,IAAI,EAAE,CAAC;MACC0D,OAAO,EAAE,CAACzB,iBAAD,CADV;MAEC0B,YAAY,EAAE,CAAC1B,iBAAD,CAFf;MAGC2B,SAAS,EAAE,CAACnE,uBAAD;IAHZ,CAAD;EAFuG,CAAD,CAAxH;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASwC,iBAAT,EAA4B/B,eAA5B,EAA6CT,uBAA7C,EAAsE8D,eAAtE"}, "metadata": {}, "sourceType": "module"}