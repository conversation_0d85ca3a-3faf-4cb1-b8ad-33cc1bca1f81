{"ast": null, "code": "import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n  if (input instanceof Observable) {\n    return input;\n  }\n\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return fromInteropObservable(input);\n    }\n\n    if (isArrayLike(input)) {\n      return fromArrayLike(input);\n    }\n\n    if (isPromise(input)) {\n      return fromPromise(input);\n    }\n\n    if (isAsyncIterable(input)) {\n      return fromAsyncIterable(input);\n    }\n\n    if (isIterable(input)) {\n      return fromIterable(input);\n    }\n\n    if (isReadableStreamLike(input)) {\n      return fromReadableStreamLike(input);\n    }\n  }\n\n  throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n  return new Observable(subscriber => {\n    const obs = obj[Symbol_observable]();\n\n    if (isFunction(obs.subscribe)) {\n      return obs.subscribe(subscriber);\n    }\n\n    throw new TypeError('Provided object does not correctly implement Symbol.observable');\n  });\n}\nexport function fromArrayLike(array) {\n  return new Observable(subscriber => {\n    for (let i = 0; i < array.length && !subscriber.closed; i++) {\n      subscriber.next(array[i]);\n    }\n\n    subscriber.complete();\n  });\n}\nexport function fromPromise(promise) {\n  return new Observable(subscriber => {\n    promise.then(value => {\n      if (!subscriber.closed) {\n        subscriber.next(value);\n        subscriber.complete();\n      }\n    }, err => subscriber.error(err)).then(null, reportUnhandledError);\n  });\n}\nexport function fromIterable(iterable) {\n  return new Observable(subscriber => {\n    for (const value of iterable) {\n      subscriber.next(value);\n\n      if (subscriber.closed) {\n        return;\n      }\n    }\n\n    subscriber.complete();\n  });\n}\nexport function fromAsyncIterable(asyncIterable) {\n  return new Observable(subscriber => {\n    process(asyncIterable, subscriber).catch(err => subscriber.error(err));\n  });\n}\nexport function fromReadableStreamLike(readableStream) {\n  return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\n\nfunction process(asyncIterable, subscriber) {\n  var asyncIterable_1, asyncIterable_1_1;\n\n  var e_1, _a;\n\n  return __awaiter(this, void 0, void 0, function* () {\n    try {\n      for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n        const value = asyncIterable_1_1.value;\n        subscriber.next(value);\n\n        if (subscriber.closed) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n\n    subscriber.complete();\n  });\n}", "map": {"version": 3, "names": ["__asyncValues", "__awaiter", "isArrayLike", "isPromise", "Observable", "isInteropObservable", "isAsyncIterable", "createInvalidObservableTypeError", "isIterable", "isReadableStreamLike", "readableStreamLikeToAsyncGenerator", "isFunction", "reportUnhandledError", "observable", "Symbol_observable", "innerFrom", "input", "fromInteropObservable", "fromArrayLike", "fromPromise", "fromAsyncIterable", "fromIterable", "fromReadableStreamLike", "obj", "subscriber", "obs", "subscribe", "TypeError", "array", "i", "length", "closed", "next", "complete", "promise", "then", "value", "err", "error", "iterable", "asyncIterable", "process", "catch", "readableStream", "asyncIterable_1", "asyncIterable_1_1", "e_1", "_a", "done", "e_1_1", "return", "call"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/observable/innerFrom.js"], "sourcesContent": ["import { __asyncValues, __awaiter } from \"tslib\";\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isPromise } from '../util/isPromise';\nimport { Observable } from '../Observable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isIterable } from '../util/isIterable';\nimport { isReadableStreamLike, readableStreamLikeToAsyncGenerator } from '../util/isReadableStreamLike';\nimport { isFunction } from '../util/isFunction';\nimport { reportUnhandledError } from '../util/reportUnhandledError';\nimport { observable as Symbol_observable } from '../symbol/observable';\nexport function innerFrom(input) {\n    if (input instanceof Observable) {\n        return input;\n    }\n    if (input != null) {\n        if (isInteropObservable(input)) {\n            return fromInteropObservable(input);\n        }\n        if (isArrayLike(input)) {\n            return fromArrayLike(input);\n        }\n        if (isPromise(input)) {\n            return fromPromise(input);\n        }\n        if (isAsyncIterable(input)) {\n            return fromAsyncIterable(input);\n        }\n        if (isIterable(input)) {\n            return fromIterable(input);\n        }\n        if (isReadableStreamLike(input)) {\n            return fromReadableStreamLike(input);\n        }\n    }\n    throw createInvalidObservableTypeError(input);\n}\nexport function fromInteropObservable(obj) {\n    return new Observable((subscriber) => {\n        const obs = obj[Symbol_observable]();\n        if (isFunction(obs.subscribe)) {\n            return obs.subscribe(subscriber);\n        }\n        throw new TypeError('Provided object does not correctly implement Symbol.observable');\n    });\n}\nexport function fromArrayLike(array) {\n    return new Observable((subscriber) => {\n        for (let i = 0; i < array.length && !subscriber.closed; i++) {\n            subscriber.next(array[i]);\n        }\n        subscriber.complete();\n    });\n}\nexport function fromPromise(promise) {\n    return new Observable((subscriber) => {\n        promise\n            .then((value) => {\n            if (!subscriber.closed) {\n                subscriber.next(value);\n                subscriber.complete();\n            }\n        }, (err) => subscriber.error(err))\n            .then(null, reportUnhandledError);\n    });\n}\nexport function fromIterable(iterable) {\n    return new Observable((subscriber) => {\n        for (const value of iterable) {\n            subscriber.next(value);\n            if (subscriber.closed) {\n                return;\n            }\n        }\n        subscriber.complete();\n    });\n}\nexport function fromAsyncIterable(asyncIterable) {\n    return new Observable((subscriber) => {\n        process(asyncIterable, subscriber).catch((err) => subscriber.error(err));\n    });\n}\nexport function fromReadableStreamLike(readableStream) {\n    return fromAsyncIterable(readableStreamLikeToAsyncGenerator(readableStream));\n}\nfunction process(asyncIterable, subscriber) {\n    var asyncIterable_1, asyncIterable_1_1;\n    var e_1, _a;\n    return __awaiter(this, void 0, void 0, function* () {\n        try {\n            for (asyncIterable_1 = __asyncValues(asyncIterable); asyncIterable_1_1 = yield asyncIterable_1.next(), !asyncIterable_1_1.done;) {\n                const value = asyncIterable_1_1.value;\n                subscriber.next(value);\n                if (subscriber.closed) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (asyncIterable_1_1 && !asyncIterable_1_1.done && (_a = asyncIterable_1.return)) yield _a.call(asyncIterable_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        subscriber.complete();\n    });\n}\n"], "mappings": "AAAA,SAASA,aAAT,EAAwBC,SAAxB,QAAyC,OAAzC;AACA,SAASC,WAAT,QAA4B,qBAA5B;AACA,SAASC,SAAT,QAA0B,mBAA1B;AACA,SAASC,UAAT,QAA2B,eAA3B;AACA,SAASC,mBAAT,QAAoC,6BAApC;AACA,SAASC,eAAT,QAAgC,yBAAhC;AACA,SAASC,gCAAT,QAAiD,gCAAjD;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,oBAAT,EAA+BC,kCAA/B,QAAyE,8BAAzE;AACA,SAASC,UAAT,QAA2B,oBAA3B;AACA,SAASC,oBAAT,QAAqC,8BAArC;AACA,SAASC,UAAU,IAAIC,iBAAvB,QAAgD,sBAAhD;AACA,OAAO,SAASC,SAAT,CAAmBC,KAAnB,EAA0B;EAC7B,IAAIA,KAAK,YAAYZ,UAArB,EAAiC;IAC7B,OAAOY,KAAP;EACH;;EACD,IAAIA,KAAK,IAAI,IAAb,EAAmB;IACf,IAAIX,mBAAmB,CAACW,KAAD,CAAvB,EAAgC;MAC5B,OAAOC,qBAAqB,CAACD,KAAD,CAA5B;IACH;;IACD,IAAId,WAAW,CAACc,KAAD,CAAf,EAAwB;MACpB,OAAOE,aAAa,CAACF,KAAD,CAApB;IACH;;IACD,IAAIb,SAAS,CAACa,KAAD,CAAb,EAAsB;MAClB,OAAOG,WAAW,CAACH,KAAD,CAAlB;IACH;;IACD,IAAIV,eAAe,CAACU,KAAD,CAAnB,EAA4B;MACxB,OAAOI,iBAAiB,CAACJ,KAAD,CAAxB;IACH;;IACD,IAAIR,UAAU,CAACQ,KAAD,CAAd,EAAuB;MACnB,OAAOK,YAAY,CAACL,KAAD,CAAnB;IACH;;IACD,IAAIP,oBAAoB,CAACO,KAAD,CAAxB,EAAiC;MAC7B,OAAOM,sBAAsB,CAACN,KAAD,CAA7B;IACH;EACJ;;EACD,MAAMT,gCAAgC,CAACS,KAAD,CAAtC;AACH;AACD,OAAO,SAASC,qBAAT,CAA+BM,GAA/B,EAAoC;EACvC,OAAO,IAAInB,UAAJ,CAAgBoB,UAAD,IAAgB;IAClC,MAAMC,GAAG,GAAGF,GAAG,CAACT,iBAAD,CAAH,EAAZ;;IACA,IAAIH,UAAU,CAACc,GAAG,CAACC,SAAL,CAAd,EAA+B;MAC3B,OAAOD,GAAG,CAACC,SAAJ,CAAcF,UAAd,CAAP;IACH;;IACD,MAAM,IAAIG,SAAJ,CAAc,gEAAd,CAAN;EACH,CANM,CAAP;AAOH;AACD,OAAO,SAAST,aAAT,CAAuBU,KAAvB,EAA8B;EACjC,OAAO,IAAIxB,UAAJ,CAAgBoB,UAAD,IAAgB;IAClC,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,KAAK,CAACE,MAAV,IAAoB,CAACN,UAAU,CAACO,MAAhD,EAAwDF,CAAC,EAAzD,EAA6D;MACzDL,UAAU,CAACQ,IAAX,CAAgBJ,KAAK,CAACC,CAAD,CAArB;IACH;;IACDL,UAAU,CAACS,QAAX;EACH,CALM,CAAP;AAMH;AACD,OAAO,SAASd,WAAT,CAAqBe,OAArB,EAA8B;EACjC,OAAO,IAAI9B,UAAJ,CAAgBoB,UAAD,IAAgB;IAClCU,OAAO,CACFC,IADL,CACWC,KAAD,IAAW;MACjB,IAAI,CAACZ,UAAU,CAACO,MAAhB,EAAwB;QACpBP,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;QACAZ,UAAU,CAACS,QAAX;MACH;IACJ,CAND,EAMII,GAAD,IAASb,UAAU,CAACc,KAAX,CAAiBD,GAAjB,CANZ,EAOKF,IAPL,CAOU,IAPV,EAOgBvB,oBAPhB;EAQH,CATM,CAAP;AAUH;AACD,OAAO,SAASS,YAAT,CAAsBkB,QAAtB,EAAgC;EACnC,OAAO,IAAInC,UAAJ,CAAgBoB,UAAD,IAAgB;IAClC,KAAK,MAAMY,KAAX,IAAoBG,QAApB,EAA8B;MAC1Bf,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;;MACA,IAAIZ,UAAU,CAACO,MAAf,EAAuB;QACnB;MACH;IACJ;;IACDP,UAAU,CAACS,QAAX;EACH,CARM,CAAP;AASH;AACD,OAAO,SAASb,iBAAT,CAA2BoB,aAA3B,EAA0C;EAC7C,OAAO,IAAIpC,UAAJ,CAAgBoB,UAAD,IAAgB;IAClCiB,OAAO,CAACD,aAAD,EAAgBhB,UAAhB,CAAP,CAAmCkB,KAAnC,CAA0CL,GAAD,IAASb,UAAU,CAACc,KAAX,CAAiBD,GAAjB,CAAlD;EACH,CAFM,CAAP;AAGH;AACD,OAAO,SAASf,sBAAT,CAAgCqB,cAAhC,EAAgD;EACnD,OAAOvB,iBAAiB,CAACV,kCAAkC,CAACiC,cAAD,CAAnC,CAAxB;AACH;;AACD,SAASF,OAAT,CAAiBD,aAAjB,EAAgChB,UAAhC,EAA4C;EACxC,IAAIoB,eAAJ,EAAqBC,iBAArB;;EACA,IAAIC,GAAJ,EAASC,EAAT;;EACA,OAAO9C,SAAS,CAAC,IAAD,EAAO,KAAK,CAAZ,EAAe,KAAK,CAApB,EAAuB,aAAa;IAChD,IAAI;MACA,KAAK2C,eAAe,GAAG5C,aAAa,CAACwC,aAAD,CAApC,EAAqDK,iBAAiB,GAAG,MAAMD,eAAe,CAACZ,IAAhB,EAA1B,EAAkD,CAACa,iBAAiB,CAACG,IAA1H,GAAiI;QAC7H,MAAMZ,KAAK,GAAGS,iBAAiB,CAACT,KAAhC;QACAZ,UAAU,CAACQ,IAAX,CAAgBI,KAAhB;;QACA,IAAIZ,UAAU,CAACO,MAAf,EAAuB;UACnB;QACH;MACJ;IACJ,CARD,CASA,OAAOkB,KAAP,EAAc;MAAEH,GAAG,GAAG;QAAER,KAAK,EAAEW;MAAT,CAAN;IAAyB,CATzC,SAUQ;MACJ,IAAI;QACA,IAAIJ,iBAAiB,IAAI,CAACA,iBAAiB,CAACG,IAAxC,KAAiDD,EAAE,GAAGH,eAAe,CAACM,MAAtE,CAAJ,EAAmF,MAAMH,EAAE,CAACI,IAAH,CAAQP,eAAR,CAAN;MACtF,CAFD,SAGQ;QAAE,IAAIE,GAAJ,EAAS,MAAMA,GAAG,CAACR,KAAV;MAAkB;IACxC;;IACDd,UAAU,CAACS,QAAX;EACH,CAlBe,CAAhB;AAmBH"}, "metadata": {}, "sourceType": "module"}