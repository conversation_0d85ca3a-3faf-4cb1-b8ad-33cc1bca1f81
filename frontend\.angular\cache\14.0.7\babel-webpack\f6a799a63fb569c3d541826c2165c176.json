{"ast": null, "code": "import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind;\n\n(function (NotificationKind) {\n  NotificationKind[\"NEXT\"] = \"N\";\n  NotificationKind[\"ERROR\"] = \"E\";\n  NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind || (NotificationKind = {}));\n\nexport class Notification {\n  constructor(kind, value, error) {\n    this.kind = kind;\n    this.value = value;\n    this.error = error;\n    this.hasValue = kind === 'N';\n  }\n\n  observe(observer) {\n    return observeNotification(this, observer);\n  }\n\n  do(nextHandler, errorHandler, completeHandler) {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n  }\n\n  accept(nextOrObserver, error, complete) {\n    var _a;\n\n    return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next) ? this.observe(nextOrObserver) : this.do(nextOrObserver, error, complete);\n  }\n\n  toObservable() {\n    const {\n      kind,\n      value,\n      error\n    } = this;\n    const result = kind === 'N' ? of(value) : kind === 'E' ? throwError(() => error) : kind === 'C' ? EMPTY : 0;\n\n    if (!result) {\n      throw new TypeError(`Unexpected notification kind ${kind}`);\n    }\n\n    return result;\n  }\n\n  static createNext(value) {\n    return new Notification('N', value);\n  }\n\n  static createError(err) {\n    return new Notification('E', undefined, err);\n  }\n\n  static createComplete() {\n    return Notification.completeNotification;\n  }\n\n}\nNotification.completeNotification = new Notification('C');\nexport function observeNotification(notification, observer) {\n  var _a, _b, _c;\n\n  const {\n    kind,\n    value,\n    error\n  } = notification;\n\n  if (typeof kind !== 'string') {\n    throw new TypeError('Invalid notification, missing \"kind\"');\n  }\n\n  kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}", "map": {"version": 3, "names": ["EMPTY", "of", "throwError", "isFunction", "NotificationKind", "Notification", "constructor", "kind", "value", "error", "hasValue", "observe", "observer", "observeNotification", "do", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "completeHandler", "accept", "nextOrObserver", "complete", "_a", "next", "toObservable", "result", "TypeError", "createNext", "createError", "err", "undefined", "createComplete", "completeNotification", "notification", "_b", "_c", "call"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/Notification.js"], "sourcesContent": ["import { EMPTY } from './observable/empty';\nimport { of } from './observable/of';\nimport { throwError } from './observable/throwError';\nimport { isFunction } from './util/isFunction';\nexport var NotificationKind;\n(function (NotificationKind) {\n    NotificationKind[\"NEXT\"] = \"N\";\n    NotificationKind[\"ERROR\"] = \"E\";\n    NotificationKind[\"COMPLETE\"] = \"C\";\n})(NotificationKind || (NotificationKind = {}));\nexport class Notification {\n    constructor(kind, value, error) {\n        this.kind = kind;\n        this.value = value;\n        this.error = error;\n        this.hasValue = kind === 'N';\n    }\n    observe(observer) {\n        return observeNotification(this, observer);\n    }\n    do(nextHandler, errorHandler, completeHandler) {\n        const { kind, value, error } = this;\n        return kind === 'N' ? nextHandler === null || nextHandler === void 0 ? void 0 : nextHandler(value) : kind === 'E' ? errorHandler === null || errorHandler === void 0 ? void 0 : errorHandler(error) : completeHandler === null || completeHandler === void 0 ? void 0 : completeHandler();\n    }\n    accept(nextOrObserver, error, complete) {\n        var _a;\n        return isFunction((_a = nextOrObserver) === null || _a === void 0 ? void 0 : _a.next)\n            ? this.observe(nextOrObserver)\n            : this.do(nextOrObserver, error, complete);\n    }\n    toObservable() {\n        const { kind, value, error } = this;\n        const result = kind === 'N'\n            ?\n                of(value)\n            :\n                kind === 'E'\n                    ?\n                        throwError(() => error)\n                    :\n                        kind === 'C'\n                            ?\n                                EMPTY\n                            :\n                                0;\n        if (!result) {\n            throw new TypeError(`Unexpected notification kind ${kind}`);\n        }\n        return result;\n    }\n    static createNext(value) {\n        return new Notification('N', value);\n    }\n    static createError(err) {\n        return new Notification('E', undefined, err);\n    }\n    static createComplete() {\n        return Notification.completeNotification;\n    }\n}\nNotification.completeNotification = new Notification('C');\nexport function observeNotification(notification, observer) {\n    var _a, _b, _c;\n    const { kind, value, error } = notification;\n    if (typeof kind !== 'string') {\n        throw new TypeError('Invalid notification, missing \"kind\"');\n    }\n    kind === 'N' ? (_a = observer.next) === null || _a === void 0 ? void 0 : _a.call(observer, value) : kind === 'E' ? (_b = observer.error) === null || _b === void 0 ? void 0 : _b.call(observer, error) : (_c = observer.complete) === null || _c === void 0 ? void 0 : _c.call(observer);\n}\n"], "mappings": "AAAA,SAASA,KAAT,QAAsB,oBAAtB;AACA,SAASC,EAAT,QAAmB,iBAAnB;AACA,SAASC,UAAT,QAA2B,yBAA3B;AACA,SAASC,UAAT,QAA2B,mBAA3B;AACA,OAAO,IAAIC,gBAAJ;;AACP,CAAC,UAAUA,gBAAV,EAA4B;EACzBA,gBAAgB,CAAC,MAAD,CAAhB,GAA2B,GAA3B;EACAA,gBAAgB,CAAC,OAAD,CAAhB,GAA4B,GAA5B;EACAA,gBAAgB,CAAC,UAAD,CAAhB,GAA+B,GAA/B;AACH,CAJD,EAIGA,gBAAgB,KAAKA,gBAAgB,GAAG,EAAxB,CAJnB;;AAKA,OAAO,MAAMC,YAAN,CAAmB;EACtBC,WAAW,CAACC,IAAD,EAAOC,KAAP,EAAcC,KAAd,EAAqB;IAC5B,KAAKF,IAAL,GAAYA,IAAZ;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,QAAL,GAAgBH,IAAI,KAAK,GAAzB;EACH;;EACDI,OAAO,CAACC,QAAD,EAAW;IACd,OAAOC,mBAAmB,CAAC,IAAD,EAAOD,QAAP,CAA1B;EACH;;EACDE,EAAE,CAACC,WAAD,EAAcC,YAAd,EAA4BC,eAA5B,EAA6C;IAC3C,MAAM;MAAEV,IAAF;MAAQC,KAAR;MAAeC;IAAf,IAAyB,IAA/B;IACA,OAAOF,IAAI,KAAK,GAAT,GAAeQ,WAAW,KAAK,IAAhB,IAAwBA,WAAW,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,WAAW,CAACP,KAAD,CAApF,GAA8FD,IAAI,KAAK,GAAT,GAAeS,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAACP,KAAD,CAAvF,GAAiGQ,eAAe,KAAK,IAApB,IAA4BA,eAAe,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,eAAe,EAAvR;EACH;;EACDC,MAAM,CAACC,cAAD,EAAiBV,KAAjB,EAAwBW,QAAxB,EAAkC;IACpC,IAAIC,EAAJ;;IACA,OAAOlB,UAAU,CAAC,CAACkB,EAAE,GAAGF,cAAN,MAA0B,IAA1B,IAAkCE,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACC,IAA/D,CAAV,GACD,KAAKX,OAAL,CAAaQ,cAAb,CADC,GAED,KAAKL,EAAL,CAAQK,cAAR,EAAwBV,KAAxB,EAA+BW,QAA/B,CAFN;EAGH;;EACDG,YAAY,GAAG;IACX,MAAM;MAAEhB,IAAF;MAAQC,KAAR;MAAeC;IAAf,IAAyB,IAA/B;IACA,MAAMe,MAAM,GAAGjB,IAAI,KAAK,GAAT,GAEPN,EAAE,CAACO,KAAD,CAFK,GAIPD,IAAI,KAAK,GAAT,GAEQL,UAAU,CAAC,MAAMO,KAAP,CAFlB,GAIQF,IAAI,KAAK,GAAT,GAEQP,KAFR,GAIQ,CAZxB;;IAaA,IAAI,CAACwB,MAAL,EAAa;MACT,MAAM,IAAIC,SAAJ,CAAe,gCAA+BlB,IAAK,EAAnD,CAAN;IACH;;IACD,OAAOiB,MAAP;EACH;;EACgB,OAAVE,UAAU,CAAClB,KAAD,EAAQ;IACrB,OAAO,IAAIH,YAAJ,CAAiB,GAAjB,EAAsBG,KAAtB,CAAP;EACH;;EACiB,OAAXmB,WAAW,CAACC,GAAD,EAAM;IACpB,OAAO,IAAIvB,YAAJ,CAAiB,GAAjB,EAAsBwB,SAAtB,EAAiCD,GAAjC,CAAP;EACH;;EACoB,OAAdE,cAAc,GAAG;IACpB,OAAOzB,YAAY,CAAC0B,oBAApB;EACH;;AAhDqB;AAkD1B1B,YAAY,CAAC0B,oBAAb,GAAoC,IAAI1B,YAAJ,CAAiB,GAAjB,CAApC;AACA,OAAO,SAASQ,mBAAT,CAA6BmB,YAA7B,EAA2CpB,QAA3C,EAAqD;EACxD,IAAIS,EAAJ,EAAQY,EAAR,EAAYC,EAAZ;;EACA,MAAM;IAAE3B,IAAF;IAAQC,KAAR;IAAeC;EAAf,IAAyBuB,YAA/B;;EACA,IAAI,OAAOzB,IAAP,KAAgB,QAApB,EAA8B;IAC1B,MAAM,IAAIkB,SAAJ,CAAc,sCAAd,CAAN;EACH;;EACDlB,IAAI,KAAK,GAAT,GAAe,CAACc,EAAE,GAAGT,QAAQ,CAACU,IAAf,MAAyB,IAAzB,IAAiCD,EAAE,KAAK,KAAK,CAA7C,GAAiD,KAAK,CAAtD,GAA0DA,EAAE,CAACc,IAAH,CAAQvB,QAAR,EAAkBJ,KAAlB,CAAzE,GAAoGD,IAAI,KAAK,GAAT,GAAe,CAAC0B,EAAE,GAAGrB,QAAQ,CAACH,KAAf,MAA0B,IAA1B,IAAkCwB,EAAE,KAAK,KAAK,CAA9C,GAAkD,KAAK,CAAvD,GAA2DA,EAAE,CAACE,IAAH,CAAQvB,QAAR,EAAkBH,KAAlB,CAA1E,GAAqG,CAACyB,EAAE,GAAGtB,QAAQ,CAACQ,QAAf,MAA6B,IAA7B,IAAqCc,EAAE,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,EAAE,CAACC,IAAH,CAAQvB,QAAR,CAAvQ;AACH"}, "metadata": {}, "sourceType": "module"}