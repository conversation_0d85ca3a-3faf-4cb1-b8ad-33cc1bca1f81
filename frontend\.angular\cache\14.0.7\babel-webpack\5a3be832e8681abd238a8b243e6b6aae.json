{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\nconst _c0 = [\"container\"];\nconst _c1 = [\"input\"];\nconst _c2 = [\"colorSelector\"];\nconst _c3 = [\"colorHandle\"];\nconst _c4 = [\"hue\"];\nconst _c5 = [\"hueHandle\"];\n\nconst _c6 = function (a0) {\n  return {\n    \"p-disabled\": a0\n  };\n};\n\nfunction ColorPicker_input_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"input\", 4, 5);\n    i0.ɵɵlistener(\"focus\", function ColorPicker_input_2_Template_input_focus_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onInputFocus());\n    })(\"click\", function ColorPicker_input_2_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.onInputClick());\n    })(\"keydown\", function ColorPicker_input_2_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onInputKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.inputBgColor);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c6, ctx_r1.disabled))(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"id\", ctx_r1.inputId)(\"tabindex\", ctx_r1.tabindex);\n  }\n}\n\nconst _c7 = function (a1, a2) {\n  return {\n    \"p-colorpicker-panel\": true,\n    \"p-colorpicker-overlay-panel\": a1,\n    \"p-disabled\": a2\n  };\n};\n\nconst _c8 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c9 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction ColorPicker_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵlistener(\"click\", function ColorPicker_div_3_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onOverlayClick($event));\n    })(\"@overlayAnimation.start\", function ColorPicker_div_3_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function ColorPicker_div_3_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.onOverlayAnimationEnd($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 7)(2, \"div\", 8, 9);\n    i0.ɵɵlistener(\"touchstart\", function ColorPicker_div_3_Template_div_touchstart_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.onColorTouchStart($event));\n    })(\"touchmove\", function ColorPicker_div_3_Template_div_touchmove_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onMove($event));\n    })(\"touchend\", function ColorPicker_div_3_Template_div_touchend_2_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.onDragEnd());\n    })(\"mousedown\", function ColorPicker_div_3_Template_div_mousedown_2_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r19 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r19.onColorMousedown($event));\n    });\n    i0.ɵɵelementStart(4, \"div\", 10);\n    i0.ɵɵelement(5, \"div\", 11, 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 13, 14);\n    i0.ɵɵlistener(\"mousedown\", function ColorPicker_div_3_Template_div_mousedown_7_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.onHueMousedown($event));\n    })(\"touchstart\", function ColorPicker_div_3_Template_div_touchstart_7_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.onHueTouchStart($event));\n    })(\"touchmove\", function ColorPicker_div_3_Template_div_touchmove_7_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.onMove($event));\n    })(\"touchend\", function ColorPicker_div_3_Template_div_touchend_7_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.onDragEnd());\n    });\n    i0.ɵɵelement(9, \"div\", 15, 16);\n    i0.ɵɵelementEnd()()();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c7, !ctx_r2.inline, ctx_r2.disabled))(\"@overlayAnimation\", i0.ɵɵpureFunction1(9, _c9, i0.ɵɵpureFunction2(6, _c8, ctx_r2.showTransitionOptions, ctx_r2.hideTransitionOptions)))(\"@.disabled\", ctx_r2.inline === true);\n  }\n}\n\nconst _c10 = function (a1, a2) {\n  return {\n    \"p-colorpicker p-component\": true,\n    \"p-colorpicker-overlay\": a1,\n    \"p-colorpicker-dragging\": a2\n  };\n};\n\nconst COLORPICKER_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => ColorPicker),\n  multi: true\n};\n\nclass ColorPicker {\n  constructor(el, renderer, cd, config, overlayService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.format = 'hex';\n    this.autoZIndex = true;\n    this.baseZIndex = 0;\n    this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '.1s linear';\n    this.onChange = new EventEmitter();\n    this.onShow = new EventEmitter();\n    this.onHide = new EventEmitter();\n    this.value = {\n      h: 0,\n      s: 100,\n      b: 100\n    };\n    this.defaultColor = 'ff0000';\n\n    this.onModelChange = () => {};\n\n    this.onModelTouched = () => {};\n  }\n\n  set colorSelector(element) {\n    this.colorSelectorViewChild = element;\n  }\n\n  set colorHandle(element) {\n    this.colorHandleViewChild = element;\n  }\n\n  set hue(element) {\n    this.hueViewChild = element;\n  }\n\n  set hueHandle(element) {\n    this.hueHandleViewChild = element;\n  }\n\n  onHueMousedown(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    this.bindDocumentMousemoveListener();\n    this.bindDocumentMouseupListener();\n    this.hueDragging = true;\n    this.pickHue(event);\n  }\n\n  onHueTouchStart(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    this.hueDragging = true;\n    this.pickHue(event, event.changedTouches[0]);\n  }\n\n  onColorTouchStart(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    this.colorDragging = true;\n    this.pickColor(event, event.changedTouches[0]);\n  }\n\n  pickHue(event, position) {\n    let pageY = position ? position.pageY : event.pageY;\n    let top = this.hueViewChild.nativeElement.getBoundingClientRect().top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0);\n    this.value = this.validateHSB({\n      h: Math.floor(360 * (150 - Math.max(0, Math.min(150, pageY - top))) / 150),\n      s: this.value.s,\n      b: this.value.b\n    });\n    this.updateColorSelector();\n    this.updateUI();\n    this.updateModel();\n    this.onChange.emit({\n      originalEvent: event,\n      value: this.getValueToUpdate()\n    });\n  }\n\n  onColorMousedown(event) {\n    if (this.disabled) {\n      return;\n    }\n\n    this.bindDocumentMousemoveListener();\n    this.bindDocumentMouseupListener();\n    this.colorDragging = true;\n    this.pickColor(event);\n  }\n\n  onMove(event) {\n    if (this.colorDragging) {\n      this.pickColor(event, event.changedTouches[0]);\n      event.preventDefault();\n    }\n\n    if (this.hueDragging) {\n      this.pickHue(event, event.changedTouches[0]);\n      event.preventDefault();\n    }\n  }\n\n  onDragEnd() {\n    this.colorDragging = false;\n    this.hueDragging = false;\n    this.unbindDocumentMousemoveListener();\n    this.unbindDocumentMouseupListener();\n  }\n\n  pickColor(event, position) {\n    let pageX = position ? position.pageX : event.pageX;\n    let pageY = position ? position.pageY : event.pageY;\n    let rect = this.colorSelectorViewChild.nativeElement.getBoundingClientRect();\n    let top = rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0);\n    let left = rect.left + document.body.scrollLeft;\n    let saturation = Math.floor(100 * Math.max(0, Math.min(150, pageX - left)) / 150);\n    let brightness = Math.floor(100 * (150 - Math.max(0, Math.min(150, pageY - top))) / 150);\n    this.value = this.validateHSB({\n      h: this.value.h,\n      s: saturation,\n      b: brightness\n    });\n    this.updateUI();\n    this.updateModel();\n    this.onChange.emit({\n      originalEvent: event,\n      value: this.getValueToUpdate()\n    });\n  }\n\n  getValueToUpdate() {\n    let val;\n\n    switch (this.format) {\n      case 'hex':\n        val = '#' + this.HSBtoHEX(this.value);\n        break;\n\n      case 'rgb':\n        val = this.HSBtoRGB(this.value);\n        break;\n\n      case 'hsb':\n        val = this.value;\n        break;\n    }\n\n    return val;\n  }\n\n  updateModel() {\n    this.onModelChange(this.getValueToUpdate());\n  }\n\n  writeValue(value) {\n    if (value) {\n      switch (this.format) {\n        case 'hex':\n          this.value = this.HEXtoHSB(value);\n          break;\n\n        case 'rgb':\n          this.value = this.RGBtoHSB(value);\n          break;\n\n        case 'hsb':\n          this.value = value;\n          break;\n      }\n    } else {\n      this.value = this.HEXtoHSB(this.defaultColor);\n    }\n\n    this.updateColorSelector();\n    this.updateUI();\n    this.cd.markForCheck();\n  }\n\n  updateColorSelector() {\n    if (this.colorSelectorViewChild) {\n      const hsb = {};\n      hsb.s = 100;\n      hsb.b = 100;\n      hsb.h = this.value.h;\n      this.colorSelectorViewChild.nativeElement.style.backgroundColor = '#' + this.HSBtoHEX(hsb);\n    }\n  }\n\n  updateUI() {\n    if (this.colorHandleViewChild && this.hueHandleViewChild.nativeElement) {\n      this.colorHandleViewChild.nativeElement.style.left = Math.floor(150 * this.value.s / 100) + 'px';\n      this.colorHandleViewChild.nativeElement.style.top = Math.floor(150 * (100 - this.value.b) / 100) + 'px';\n      this.hueHandleViewChild.nativeElement.style.top = Math.floor(150 - 150 * this.value.h / 360) + 'px';\n    }\n\n    this.inputBgColor = '#' + this.HSBtoHEX(this.value);\n  }\n\n  onInputFocus() {\n    this.onModelTouched();\n  }\n\n  show() {\n    this.overlayVisible = true;\n    this.cd.markForCheck();\n  }\n\n  onOverlayAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (!this.inline) {\n          this.overlay = event.element;\n          this.appendOverlay();\n\n          if (this.autoZIndex) {\n            ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n          }\n\n          this.alignOverlay();\n          this.bindDocumentClickListener();\n          this.bindDocumentResizeListener();\n          this.bindScrollListener();\n          this.updateColorSelector();\n          this.updateUI();\n        }\n\n        break;\n\n      case 'void':\n        this.onOverlayHide();\n        break;\n    }\n  }\n\n  onOverlayAnimationEnd(event) {\n    switch (event.toState) {\n      case 'visible':\n        if (!this.inline) {\n          this.onShow.emit({});\n        }\n\n        break;\n\n      case 'void':\n        if (this.autoZIndex) {\n          ZIndexUtils.clear(event.element);\n        }\n\n        this.onHide.emit({});\n        break;\n    }\n  }\n\n  appendOverlay() {\n    if (this.appendTo) {\n      if (this.appendTo === 'body') document.body.appendChild(this.overlay);else DomHandler.appendChild(this.overlay, this.appendTo);\n    }\n  }\n\n  restoreOverlayAppend() {\n    if (this.overlay && this.appendTo) {\n      this.el.nativeElement.appendChild(this.overlay);\n    }\n  }\n\n  alignOverlay() {\n    if (this.appendTo) DomHandler.absolutePosition(this.overlay, this.inputViewChild.nativeElement);else DomHandler.relativePosition(this.overlay, this.inputViewChild.nativeElement);\n  }\n\n  hide() {\n    this.overlayVisible = false;\n    this.cd.markForCheck();\n  }\n\n  onInputClick() {\n    this.selfClick = true;\n    this.togglePanel();\n  }\n\n  togglePanel() {\n    if (!this.overlayVisible) this.show();else this.hide();\n  }\n\n  onInputKeydown(event) {\n    switch (event.which) {\n      //space\n      case 32:\n        this.togglePanel();\n        event.preventDefault();\n        break;\n      //escape and tab\n\n      case 27:\n      case 9:\n        this.hide();\n        break;\n    }\n  }\n\n  onOverlayClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.el.nativeElement\n    });\n    this.selfClick = true;\n  }\n\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n        if (!this.selfClick) {\n          this.overlayVisible = false;\n          this.unbindDocumentClickListener();\n        }\n\n        this.selfClick = false;\n        this.cd.markForCheck();\n      });\n    }\n  }\n\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n\n  bindDocumentMousemoveListener() {\n    if (!this.documentMousemoveListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentMousemoveListener = this.renderer.listen(documentTarget, 'mousemove', event => {\n        if (this.colorDragging) {\n          this.pickColor(event);\n        }\n\n        if (this.hueDragging) {\n          this.pickHue(event);\n        }\n      });\n    }\n  }\n\n  unbindDocumentMousemoveListener() {\n    if (this.documentMousemoveListener) {\n      this.documentMousemoveListener();\n      this.documentMousemoveListener = null;\n    }\n  }\n\n  bindDocumentMouseupListener() {\n    if (!this.documentMouseupListener) {\n      const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n      this.documentMouseupListener = this.renderer.listen(documentTarget, 'mouseup', () => {\n        this.colorDragging = false;\n        this.hueDragging = false;\n        this.unbindDocumentMousemoveListener();\n        this.unbindDocumentMouseupListener();\n      });\n    }\n  }\n\n  unbindDocumentMouseupListener() {\n    if (this.documentMouseupListener) {\n      this.documentMouseupListener();\n      this.documentMouseupListener = null;\n    }\n  }\n\n  bindDocumentResizeListener() {\n    this.documentResizeListener = this.onWindowResize.bind(this);\n    window.addEventListener('resize', this.documentResizeListener);\n  }\n\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  onWindowResize() {\n    if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n      this.hide();\n    }\n  }\n\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild.nativeElement, () => {\n        if (this.overlayVisible) {\n          this.hide();\n        }\n      });\n    }\n\n    this.scrollHandler.bindScrollListener();\n  }\n\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n\n  validateHSB(hsb) {\n    return {\n      h: Math.min(360, Math.max(0, hsb.h)),\n      s: Math.min(100, Math.max(0, hsb.s)),\n      b: Math.min(100, Math.max(0, hsb.b))\n    };\n  }\n\n  validateRGB(rgb) {\n    return {\n      r: Math.min(255, Math.max(0, rgb.r)),\n      g: Math.min(255, Math.max(0, rgb.g)),\n      b: Math.min(255, Math.max(0, rgb.b))\n    };\n  }\n\n  validateHEX(hex) {\n    var len = 6 - hex.length;\n\n    if (len > 0) {\n      var o = [];\n\n      for (var i = 0; i < len; i++) {\n        o.push('0');\n      }\n\n      o.push(hex);\n      hex = o.join('');\n    }\n\n    return hex;\n  }\n\n  HEXtoRGB(hex) {\n    let hexValue = parseInt(hex.indexOf('#') > -1 ? hex.substring(1) : hex, 16);\n    return {\n      r: hexValue >> 16,\n      g: (hexValue & 0x00FF00) >> 8,\n      b: hexValue & 0x0000FF\n    };\n  }\n\n  HEXtoHSB(hex) {\n    return this.RGBtoHSB(this.HEXtoRGB(hex));\n  }\n\n  RGBtoHSB(rgb) {\n    var hsb = {\n      h: 0,\n      s: 0,\n      b: 0\n    };\n    var min = Math.min(rgb.r, rgb.g, rgb.b);\n    var max = Math.max(rgb.r, rgb.g, rgb.b);\n    var delta = max - min;\n    hsb.b = max;\n    hsb.s = max != 0 ? 255 * delta / max : 0;\n\n    if (hsb.s != 0) {\n      if (rgb.r == max) {\n        hsb.h = (rgb.g - rgb.b) / delta;\n      } else if (rgb.g == max) {\n        hsb.h = 2 + (rgb.b - rgb.r) / delta;\n      } else {\n        hsb.h = 4 + (rgb.r - rgb.g) / delta;\n      }\n    } else {\n      hsb.h = -1;\n    }\n\n    hsb.h *= 60;\n\n    if (hsb.h < 0) {\n      hsb.h += 360;\n    }\n\n    hsb.s *= 100 / 255;\n    hsb.b *= 100 / 255;\n    return hsb;\n  }\n\n  HSBtoRGB(hsb) {\n    var rgb = {\n      r: null,\n      g: null,\n      b: null\n    };\n    let h = hsb.h;\n    let s = hsb.s * 255 / 100;\n    let v = hsb.b * 255 / 100;\n\n    if (s == 0) {\n      rgb = {\n        r: v,\n        g: v,\n        b: v\n      };\n    } else {\n      let t1 = v;\n      let t2 = (255 - s) * v / 255;\n      let t3 = (t1 - t2) * (h % 60) / 60;\n      if (h == 360) h = 0;\n\n      if (h < 60) {\n        rgb.r = t1;\n        rgb.b = t2;\n        rgb.g = t2 + t3;\n      } else if (h < 120) {\n        rgb.g = t1;\n        rgb.b = t2;\n        rgb.r = t1 - t3;\n      } else if (h < 180) {\n        rgb.g = t1;\n        rgb.r = t2;\n        rgb.b = t2 + t3;\n      } else if (h < 240) {\n        rgb.b = t1;\n        rgb.r = t2;\n        rgb.g = t1 - t3;\n      } else if (h < 300) {\n        rgb.b = t1;\n        rgb.g = t2;\n        rgb.r = t2 + t3;\n      } else if (h < 360) {\n        rgb.r = t1;\n        rgb.g = t2;\n        rgb.b = t1 - t3;\n      } else {\n        rgb.r = 0;\n        rgb.g = 0;\n        rgb.b = 0;\n      }\n    }\n\n    return {\n      r: Math.round(rgb.r),\n      g: Math.round(rgb.g),\n      b: Math.round(rgb.b)\n    };\n  }\n\n  RGBtoHEX(rgb) {\n    var hex = [rgb.r.toString(16), rgb.g.toString(16), rgb.b.toString(16)];\n\n    for (var key in hex) {\n      if (hex[key].length == 1) {\n        hex[key] = '0' + hex[key];\n      }\n    }\n\n    return hex.join('');\n  }\n\n  HSBtoHEX(hsb) {\n    return this.RGBtoHEX(this.HSBtoRGB(hsb));\n  }\n\n  onOverlayHide() {\n    this.unbindScrollListener();\n    this.unbindDocumentResizeListener();\n    this.unbindDocumentClickListener();\n    this.overlay = null;\n  }\n\n  ngOnDestroy() {\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n\n    if (this.overlay && this.autoZIndex) {\n      ZIndexUtils.clear(this.overlay);\n    }\n\n    this.restoreOverlayAppend();\n    this.onOverlayHide();\n  }\n\n}\n\nColorPicker.ɵfac = function ColorPicker_Factory(t) {\n  return new (t || ColorPicker)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n};\n\nColorPicker.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ColorPicker,\n  selectors: [[\"p-colorPicker\"]],\n  viewQuery: function ColorPicker_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n      i0.ɵɵviewQuery(_c2, 5);\n      i0.ɵɵviewQuery(_c3, 5);\n      i0.ɵɵviewQuery(_c4, 5);\n      i0.ɵɵviewQuery(_c5, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.colorSelector = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.colorHandle = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hue = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hueHandle = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    style: \"style\",\n    styleClass: \"styleClass\",\n    inline: \"inline\",\n    format: \"format\",\n    appendTo: \"appendTo\",\n    disabled: \"disabled\",\n    tabindex: \"tabindex\",\n    inputId: \"inputId\",\n    autoZIndex: \"autoZIndex\",\n    baseZIndex: \"baseZIndex\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\"\n  },\n  outputs: {\n    onChange: \"onChange\",\n    onShow: \"onShow\",\n    onHide: \"onHide\"\n  },\n  features: [i0.ɵɵProvidersFeature([COLORPICKER_VALUE_ACCESSOR])],\n  decls: 4,\n  vars: 9,\n  consts: [[3, \"ngStyle\", \"ngClass\"], [\"container\", \"\"], [\"type\", \"text\", \"class\", \"p-colorpicker-preview p-inputtext\", \"readonly\", \"readonly\", 3, \"ngClass\", \"disabled\", \"backgroundColor\", \"focus\", \"click\", \"keydown\", 4, \"ngIf\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"text\", \"readonly\", \"readonly\", 1, \"p-colorpicker-preview\", \"p-inputtext\", 3, \"ngClass\", \"disabled\", \"focus\", \"click\", \"keydown\"], [\"input\", \"\"], [3, \"ngClass\", \"click\"], [1, \"p-colorpicker-content\"], [1, \"p-colorpicker-color-selector\", 3, \"touchstart\", \"touchmove\", \"touchend\", \"mousedown\"], [\"colorSelector\", \"\"], [1, \"p-colorpicker-color\"], [1, \"p-colorpicker-color-handle\"], [\"colorHandle\", \"\"], [1, \"p-colorpicker-hue\", 3, \"mousedown\", \"touchstart\", \"touchmove\", \"touchend\"], [\"hue\", \"\"], [1, \"p-colorpicker-hue-handle\"], [\"hueHandle\", \"\"]],\n  template: function ColorPicker_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0, 1);\n      i0.ɵɵtemplate(2, ColorPicker_input_2_Template, 2, 8, \"input\", 2);\n      i0.ɵɵtemplate(3, ColorPicker_div_3_Template, 11, 11, \"div\", 3);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction2(6, _c10, !ctx.inline, ctx.colorDragging || ctx.hueDragging));\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", !ctx.inline);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.inline || ctx.overlayVisible);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgIf, i2.NgStyle],\n  styles: [\".p-colorpicker{display:inline-block}.p-colorpicker-dragging{cursor:pointer}.p-colorpicker-overlay{position:relative}.p-colorpicker-panel{position:relative;width:193px;height:166px}.p-colorpicker-overlay-panel{position:absolute;top:0;left:0}.p-colorpicker-preview{cursor:pointer}.p-colorpicker-panel .p-colorpicker-content{position:relative}.p-colorpicker-panel .p-colorpicker-color-selector{width:150px;height:150px;top:8px;left:8px;position:absolute}.p-colorpicker-panel .p-colorpicker-color{width:150px;height:150px}.p-colorpicker-panel .p-colorpicker-color-handle{position:absolute;top:0px;left:150px;border-radius:100%;width:10px;height:10px;border-width:1px;border-style:solid;margin:-5px 0 0 -5px;cursor:pointer;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue{width:17px;height:150px;top:8px;left:167px;position:absolute;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue-handle{position:absolute;top:150px;left:0px;width:21px;margin-left:-2px;margin-top:-5px;height:10px;border-width:2px;border-style:solid;opacity:.85;cursor:pointer}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('overlayAnimation', [transition(':enter', [style({\n      opacity: 0,\n      transform: 'scaleY(0.8)'\n    }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPicker, [{\n    type: Component,\n    args: [{\n      selector: 'p-colorPicker',\n      template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"{'p-colorpicker p-component':true,'p-colorpicker-overlay':!inline,'p-colorpicker-dragging':colorDragging||hueDragging}\">\n            <input #input type=\"text\" *ngIf=\"!inline\" class=\"p-colorpicker-preview p-inputtext\" readonly=\"readonly\" [ngClass]=\"{'p-disabled': disabled}\"\n                (focus)=\"onInputFocus()\" (click)=\"onInputClick()\" (keydown)=\"onInputKeydown($event)\" [attr.id]=\"inputId\" [attr.tabindex]=\"tabindex\" [disabled]=\"disabled\"\n                [style.backgroundColor]=\"inputBgColor\">\n            <div *ngIf=\"inline || overlayVisible\" [ngClass]=\"{'p-colorpicker-panel': true, 'p-colorpicker-overlay-panel':!inline, 'p-disabled': disabled}\" (click)=\"onOverlayClick($event)\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"inline === true\"\n                    (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <div class=\"p-colorpicker-content\">\n                    <div #colorSelector class=\"p-colorpicker-color-selector\" (touchstart)=\"onColorTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\" (mousedown)=\"onColorMousedown($event)\">\n                        <div class=\"p-colorpicker-color\">\n                            <div #colorHandle class=\"p-colorpicker-color-handle\"></div>\n                        </div>\n                    </div>\n                    <div #hue class=\"p-colorpicker-hue\" (mousedown)=\"onHueMousedown($event)\" (touchstart)=\"onHueTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\">\n                        <div #hueHandle class=\"p-colorpicker-hue-handle\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayAnimation', [transition(':enter', [style({\n        opacity: 0,\n        transform: 'scaleY(0.8)'\n      }), animate('{{showTransitionParams}}')]), transition(':leave', [animate('{{hideTransitionParams}}', style({\n        opacity: 0\n      }))])])],\n      providers: [COLORPICKER_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-colorpicker{display:inline-block}.p-colorpicker-dragging{cursor:pointer}.p-colorpicker-overlay{position:relative}.p-colorpicker-panel{position:relative;width:193px;height:166px}.p-colorpicker-overlay-panel{position:absolute;top:0;left:0}.p-colorpicker-preview{cursor:pointer}.p-colorpicker-panel .p-colorpicker-content{position:relative}.p-colorpicker-panel .p-colorpicker-color-selector{width:150px;height:150px;top:8px;left:8px;position:absolute}.p-colorpicker-panel .p-colorpicker-color{width:150px;height:150px}.p-colorpicker-panel .p-colorpicker-color-handle{position:absolute;top:0px;left:150px;border-radius:100%;width:10px;height:10px;border-width:1px;border-style:solid;margin:-5px 0 0 -5px;cursor:pointer;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue{width:17px;height:150px;top:8px;left:167px;position:absolute;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue-handle{position:absolute;top:150px;left:0px;width:21px;margin-left:-2px;margin-top:-5px;height:10px;border-width:2px;border-style:solid;opacity:.85;cursor:pointer}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.Renderer2\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.PrimeNGConfig\n    }, {\n      type: i1.OverlayService\n    }];\n  }, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    format: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    inputId: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    onChange: [{\n      type: Output\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    containerViewChild: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    inputViewChild: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    colorSelector: [{\n      type: ViewChild,\n      args: ['colorSelector']\n    }],\n    colorHandle: [{\n      type: ViewChild,\n      args: ['colorHandle']\n    }],\n    hue: [{\n      type: ViewChild,\n      args: ['hue']\n    }],\n    hueHandle: [{\n      type: ViewChild,\n      args: ['hueHandle']\n    }]\n  });\n})();\n\nclass ColorPickerModule {}\n\nColorPickerModule.ɵfac = function ColorPickerModule_Factory(t) {\n  return new (t || ColorPickerModule)();\n};\n\nColorPickerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ColorPickerModule\n});\nColorPickerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ColorPickerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ColorPicker],\n      declarations: [ColorPicker]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { COLORPICKER_VALUE_ACCESSOR, ColorPicker, ColorPickerModule };", "map": {"version": 3, "names": ["i0", "forwardRef", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ViewChild", "NgModule", "trigger", "transition", "style", "animate", "i2", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "NG_VALUE_ACCESSOR", "ZIndexUtils", "i1", "COLORPICKER_VALUE_ACCESSOR", "provide", "useExisting", "ColorPicker", "multi", "constructor", "el", "renderer", "cd", "config", "overlayService", "format", "autoZIndex", "baseZIndex", "showTransitionOptions", "hideTransitionOptions", "onChange", "onShow", "onHide", "value", "h", "s", "b", "defaultColor", "onModelChange", "onModelTouched", "colorSelector", "element", "colorSelectorViewChild", "colorHandle", "colorHandleViewChild", "hue", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hueHandleViewChild", "onHueMousedown", "event", "disabled", "bindDocumentMousemoveListener", "bindDocumentMouseupListener", "hueDragging", "pickHue", "onHueTouchStart", "changedTouches", "onColorTouchStart", "colorDragging", "pickColor", "position", "pageY", "top", "nativeElement", "getBoundingClientRect", "window", "pageYOffset", "document", "documentElement", "scrollTop", "body", "validateHSB", "Math", "floor", "max", "min", "updateColorSelector", "updateUI", "updateModel", "emit", "originalEvent", "getValueToUpdate", "onColorMousedown", "onMove", "preventDefault", "onDragEnd", "unbindDocumentMousemoveListener", "unbindDocumentMouseupListener", "pageX", "rect", "left", "scrollLeft", "saturation", "brightness", "val", "HSBtoHEX", "HSBtoRGB", "writeValue", "HEXtoHSB", "RGBtoHSB", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hsb", "backgroundColor", "inputBgColor", "onInputFocus", "show", "overlayVisible", "onOverlayAnimationStart", "toState", "inline", "overlay", "appendOverlay", "set", "zIndex", "alignOverlay", "bindDocumentClickListener", "bindDocumentResizeListener", "bindScrollListener", "onOverlayHide", "onOverlayAnimationEnd", "clear", "appendTo", "append<PERSON><PERSON><PERSON>", "restoreOverlayAppend", "absolutePosition", "inputViewChild", "relativePosition", "hide", "onInputClick", "selfClick", "togglePanel", "onInputKeydown", "which", "onOverlayClick", "add", "target", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "documentClickListener", "documentTarget", "ownerDocument", "listen", "unbindDocumentClickListener", "documentMousemoveListener", "documentMouseupListener", "documentResizeListener", "onWindowResize", "bind", "addEventListener", "unbindDocumentResizeListener", "removeEventListener", "isTouchDevice", "<PERSON><PERSON><PERSON><PERSON>", "containerViewChild", "unbindScrollListener", "validateRGB", "rgb", "r", "g", "validateHEX", "hex", "len", "length", "o", "i", "push", "join", "HEXtoRGB", "hexValue", "parseInt", "indexOf", "substring", "delta", "v", "t1", "t2", "t3", "round", "RGBtoHEX", "toString", "key", "ngOnDestroy", "destroy", "ɵfac", "ElementRef", "Renderer2", "ChangeDetectorRef", "PrimeNGConfig", "OverlayService", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "opacity", "transform", "type", "args", "selector", "template", "animations", "providers", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "styleClass", "tabindex", "inputId", "ColorPickerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-colorpicker.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ZIndexUtils } from 'primeng/utils';\nimport * as i1 from 'primeng/api';\n\nconst COLORPICKER_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => ColorPicker),\n    multi: true\n};\nclass ColorPicker {\n    constructor(el, renderer, cd, config, overlayService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.format = 'hex';\n        this.autoZIndex = true;\n        this.baseZIndex = 0;\n        this.showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '.1s linear';\n        this.onChange = new EventEmitter();\n        this.onShow = new EventEmitter();\n        this.onHide = new EventEmitter();\n        this.value = { h: 0, s: 100, b: 100 };\n        this.defaultColor = 'ff0000';\n        this.onModelChange = () => { };\n        this.onModelTouched = () => { };\n    }\n    set colorSelector(element) {\n        this.colorSelectorViewChild = element;\n    }\n    set colorHandle(element) {\n        this.colorHandleViewChild = element;\n    }\n    set hue(element) {\n        this.hueViewChild = element;\n    }\n    set hueHandle(element) {\n        this.hueHandleViewChild = element;\n    }\n    onHueMousedown(event) {\n        if (this.disabled) {\n            return;\n        }\n        this.bindDocumentMousemoveListener();\n        this.bindDocumentMouseupListener();\n        this.hueDragging = true;\n        this.pickHue(event);\n    }\n    onHueTouchStart(event) {\n        if (this.disabled) {\n            return;\n        }\n        this.hueDragging = true;\n        this.pickHue(event, event.changedTouches[0]);\n    }\n    onColorTouchStart(event) {\n        if (this.disabled) {\n            return;\n        }\n        this.colorDragging = true;\n        this.pickColor(event, event.changedTouches[0]);\n    }\n    pickHue(event, position) {\n        let pageY = position ? position.pageY : event.pageY;\n        let top = this.hueViewChild.nativeElement.getBoundingClientRect().top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0);\n        this.value = this.validateHSB({\n            h: Math.floor(360 * (150 - Math.max(0, Math.min(150, (pageY - top)))) / 150),\n            s: this.value.s,\n            b: this.value.b\n        });\n        this.updateColorSelector();\n        this.updateUI();\n        this.updateModel();\n        this.onChange.emit({ originalEvent: event, value: this.getValueToUpdate() });\n    }\n    onColorMousedown(event) {\n        if (this.disabled) {\n            return;\n        }\n        this.bindDocumentMousemoveListener();\n        this.bindDocumentMouseupListener();\n        this.colorDragging = true;\n        this.pickColor(event);\n    }\n    onMove(event) {\n        if (this.colorDragging) {\n            this.pickColor(event, event.changedTouches[0]);\n            event.preventDefault();\n        }\n        if (this.hueDragging) {\n            this.pickHue(event, event.changedTouches[0]);\n            event.preventDefault();\n        }\n    }\n    onDragEnd() {\n        this.colorDragging = false;\n        this.hueDragging = false;\n        this.unbindDocumentMousemoveListener();\n        this.unbindDocumentMouseupListener();\n    }\n    pickColor(event, position) {\n        let pageX = position ? position.pageX : event.pageX;\n        let pageY = position ? position.pageY : event.pageY;\n        let rect = this.colorSelectorViewChild.nativeElement.getBoundingClientRect();\n        let top = rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0);\n        let left = rect.left + document.body.scrollLeft;\n        let saturation = Math.floor(100 * (Math.max(0, Math.min(150, ((pageX) - left)))) / 150);\n        let brightness = Math.floor(100 * (150 - Math.max(0, Math.min(150, ((pageY) - top)))) / 150);\n        this.value = this.validateHSB({\n            h: this.value.h,\n            s: saturation,\n            b: brightness\n        });\n        this.updateUI();\n        this.updateModel();\n        this.onChange.emit({ originalEvent: event, value: this.getValueToUpdate() });\n    }\n    getValueToUpdate() {\n        let val;\n        switch (this.format) {\n            case 'hex':\n                val = '#' + this.HSBtoHEX(this.value);\n                break;\n            case 'rgb':\n                val = this.HSBtoRGB(this.value);\n                break;\n            case 'hsb':\n                val = this.value;\n                break;\n        }\n        return val;\n    }\n    updateModel() {\n        this.onModelChange(this.getValueToUpdate());\n    }\n    writeValue(value) {\n        if (value) {\n            switch (this.format) {\n                case 'hex':\n                    this.value = this.HEXtoHSB(value);\n                    break;\n                case 'rgb':\n                    this.value = this.RGBtoHSB(value);\n                    break;\n                case 'hsb':\n                    this.value = value;\n                    break;\n            }\n        }\n        else {\n            this.value = this.HEXtoHSB(this.defaultColor);\n        }\n        this.updateColorSelector();\n        this.updateUI();\n        this.cd.markForCheck();\n    }\n    updateColorSelector() {\n        if (this.colorSelectorViewChild) {\n            const hsb = {};\n            hsb.s = 100;\n            hsb.b = 100;\n            hsb.h = this.value.h;\n            this.colorSelectorViewChild.nativeElement.style.backgroundColor = '#' + this.HSBtoHEX(hsb);\n        }\n    }\n    updateUI() {\n        if (this.colorHandleViewChild && this.hueHandleViewChild.nativeElement) {\n            this.colorHandleViewChild.nativeElement.style.left = Math.floor(150 * this.value.s / 100) + 'px';\n            this.colorHandleViewChild.nativeElement.style.top = Math.floor(150 * (100 - this.value.b) / 100) + 'px';\n            this.hueHandleViewChild.nativeElement.style.top = Math.floor(150 - (150 * this.value.h / 360)) + 'px';\n        }\n        this.inputBgColor = '#' + this.HSBtoHEX(this.value);\n    }\n    onInputFocus() {\n        this.onModelTouched();\n    }\n    show() {\n        this.overlayVisible = true;\n        this.cd.markForCheck();\n    }\n    onOverlayAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (!this.inline) {\n                    this.overlay = event.element;\n                    this.appendOverlay();\n                    if (this.autoZIndex) {\n                        ZIndexUtils.set('overlay', this.overlay, this.config.zIndex.overlay);\n                    }\n                    this.alignOverlay();\n                    this.bindDocumentClickListener();\n                    this.bindDocumentResizeListener();\n                    this.bindScrollListener();\n                    this.updateColorSelector();\n                    this.updateUI();\n                }\n                break;\n            case 'void':\n                this.onOverlayHide();\n                break;\n        }\n    }\n    onOverlayAnimationEnd(event) {\n        switch (event.toState) {\n            case 'visible':\n                if (!this.inline) {\n                    this.onShow.emit({});\n                }\n                break;\n            case 'void':\n                if (this.autoZIndex) {\n                    ZIndexUtils.clear(event.element);\n                }\n                this.onHide.emit({});\n                break;\n        }\n    }\n    appendOverlay() {\n        if (this.appendTo) {\n            if (this.appendTo === 'body')\n                document.body.appendChild(this.overlay);\n            else\n                DomHandler.appendChild(this.overlay, this.appendTo);\n        }\n    }\n    restoreOverlayAppend() {\n        if (this.overlay && this.appendTo) {\n            this.el.nativeElement.appendChild(this.overlay);\n        }\n    }\n    alignOverlay() {\n        if (this.appendTo)\n            DomHandler.absolutePosition(this.overlay, this.inputViewChild.nativeElement);\n        else\n            DomHandler.relativePosition(this.overlay, this.inputViewChild.nativeElement);\n    }\n    hide() {\n        this.overlayVisible = false;\n        this.cd.markForCheck();\n    }\n    onInputClick() {\n        this.selfClick = true;\n        this.togglePanel();\n    }\n    togglePanel() {\n        if (!this.overlayVisible)\n            this.show();\n        else\n            this.hide();\n    }\n    onInputKeydown(event) {\n        switch (event.which) {\n            //space\n            case 32:\n                this.togglePanel();\n                event.preventDefault();\n                break;\n            //escape and tab\n            case 27:\n            case 9:\n                this.hide();\n                break;\n        }\n    }\n    onOverlayClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.el.nativeElement\n        });\n        this.selfClick = true;\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentClickListener = this.renderer.listen(documentTarget, 'click', () => {\n                if (!this.selfClick) {\n                    this.overlayVisible = false;\n                    this.unbindDocumentClickListener();\n                }\n                this.selfClick = false;\n                this.cd.markForCheck();\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentMousemoveListener() {\n        if (!this.documentMousemoveListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentMousemoveListener = this.renderer.listen(documentTarget, 'mousemove', (event) => {\n                if (this.colorDragging) {\n                    this.pickColor(event);\n                }\n                if (this.hueDragging) {\n                    this.pickHue(event);\n                }\n            });\n        }\n    }\n    unbindDocumentMousemoveListener() {\n        if (this.documentMousemoveListener) {\n            this.documentMousemoveListener();\n            this.documentMousemoveListener = null;\n        }\n    }\n    bindDocumentMouseupListener() {\n        if (!this.documentMouseupListener) {\n            const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n            this.documentMouseupListener = this.renderer.listen(documentTarget, 'mouseup', () => {\n                this.colorDragging = false;\n                this.hueDragging = false;\n                this.unbindDocumentMousemoveListener();\n                this.unbindDocumentMouseupListener();\n            });\n        }\n    }\n    unbindDocumentMouseupListener() {\n        if (this.documentMouseupListener) {\n            this.documentMouseupListener();\n            this.documentMouseupListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        this.documentResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.documentResizeListener);\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n            this.hide();\n        }\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild.nativeElement, () => {\n                if (this.overlayVisible) {\n                    this.hide();\n                }\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    validateHSB(hsb) {\n        return {\n            h: Math.min(360, Math.max(0, hsb.h)),\n            s: Math.min(100, Math.max(0, hsb.s)),\n            b: Math.min(100, Math.max(0, hsb.b))\n        };\n    }\n    validateRGB(rgb) {\n        return {\n            r: Math.min(255, Math.max(0, rgb.r)),\n            g: Math.min(255, Math.max(0, rgb.g)),\n            b: Math.min(255, Math.max(0, rgb.b))\n        };\n    }\n    validateHEX(hex) {\n        var len = 6 - hex.length;\n        if (len > 0) {\n            var o = [];\n            for (var i = 0; i < len; i++) {\n                o.push('0');\n            }\n            o.push(hex);\n            hex = o.join('');\n        }\n        return hex;\n    }\n    HEXtoRGB(hex) {\n        let hexValue = parseInt(((hex.indexOf('#') > -1) ? hex.substring(1) : hex), 16);\n        return { r: hexValue >> 16, g: (hexValue & 0x00FF00) >> 8, b: (hexValue & 0x0000FF) };\n    }\n    HEXtoHSB(hex) {\n        return this.RGBtoHSB(this.HEXtoRGB(hex));\n    }\n    RGBtoHSB(rgb) {\n        var hsb = {\n            h: 0,\n            s: 0,\n            b: 0\n        };\n        var min = Math.min(rgb.r, rgb.g, rgb.b);\n        var max = Math.max(rgb.r, rgb.g, rgb.b);\n        var delta = max - min;\n        hsb.b = max;\n        hsb.s = max != 0 ? 255 * delta / max : 0;\n        if (hsb.s != 0) {\n            if (rgb.r == max) {\n                hsb.h = (rgb.g - rgb.b) / delta;\n            }\n            else if (rgb.g == max) {\n                hsb.h = 2 + (rgb.b - rgb.r) / delta;\n            }\n            else {\n                hsb.h = 4 + (rgb.r - rgb.g) / delta;\n            }\n        }\n        else {\n            hsb.h = -1;\n        }\n        hsb.h *= 60;\n        if (hsb.h < 0) {\n            hsb.h += 360;\n        }\n        hsb.s *= 100 / 255;\n        hsb.b *= 100 / 255;\n        return hsb;\n    }\n    HSBtoRGB(hsb) {\n        var rgb = {\n            r: null, g: null, b: null\n        };\n        let h = hsb.h;\n        let s = hsb.s * 255 / 100;\n        let v = hsb.b * 255 / 100;\n        if (s == 0) {\n            rgb = {\n                r: v,\n                g: v,\n                b: v\n            };\n        }\n        else {\n            let t1 = v;\n            let t2 = (255 - s) * v / 255;\n            let t3 = (t1 - t2) * (h % 60) / 60;\n            if (h == 360)\n                h = 0;\n            if (h < 60) {\n                rgb.r = t1;\n                rgb.b = t2;\n                rgb.g = t2 + t3;\n            }\n            else if (h < 120) {\n                rgb.g = t1;\n                rgb.b = t2;\n                rgb.r = t1 - t3;\n            }\n            else if (h < 180) {\n                rgb.g = t1;\n                rgb.r = t2;\n                rgb.b = t2 + t3;\n            }\n            else if (h < 240) {\n                rgb.b = t1;\n                rgb.r = t2;\n                rgb.g = t1 - t3;\n            }\n            else if (h < 300) {\n                rgb.b = t1;\n                rgb.g = t2;\n                rgb.r = t2 + t3;\n            }\n            else if (h < 360) {\n                rgb.r = t1;\n                rgb.g = t2;\n                rgb.b = t1 - t3;\n            }\n            else {\n                rgb.r = 0;\n                rgb.g = 0;\n                rgb.b = 0;\n            }\n        }\n        return { r: Math.round(rgb.r), g: Math.round(rgb.g), b: Math.round(rgb.b) };\n    }\n    RGBtoHEX(rgb) {\n        var hex = [\n            rgb.r.toString(16),\n            rgb.g.toString(16),\n            rgb.b.toString(16)\n        ];\n        for (var key in hex) {\n            if (hex[key].length == 1) {\n                hex[key] = '0' + hex[key];\n            }\n        }\n        return hex.join('');\n    }\n    HSBtoHEX(hsb) {\n        return this.RGBtoHEX(this.HSBtoRGB(hsb));\n    }\n    onOverlayHide() {\n        this.unbindScrollListener();\n        this.unbindDocumentResizeListener();\n        this.unbindDocumentClickListener();\n        this.overlay = null;\n    }\n    ngOnDestroy() {\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        if (this.overlay && this.autoZIndex) {\n            ZIndexUtils.clear(this.overlay);\n        }\n        this.restoreOverlayAppend();\n        this.onOverlayHide();\n    }\n}\nColorPicker.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPicker, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }], target: i0.ɵɵFactoryTarget.Component });\nColorPicker.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: ColorPicker, selector: \"p-colorPicker\", inputs: { style: \"style\", styleClass: \"styleClass\", inline: \"inline\", format: \"format\", appendTo: \"appendTo\", disabled: \"disabled\", tabindex: \"tabindex\", inputId: \"inputId\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\" }, outputs: { onChange: \"onChange\", onShow: \"onShow\", onHide: \"onHide\" }, host: { classAttribute: \"p-element\" }, providers: [COLORPICKER_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"containerViewChild\", first: true, predicate: [\"container\"], descendants: true }, { propertyName: \"inputViewChild\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"colorSelector\", first: true, predicate: [\"colorSelector\"], descendants: true }, { propertyName: \"colorHandle\", first: true, predicate: [\"colorHandle\"], descendants: true }, { propertyName: \"hue\", first: true, predicate: [\"hue\"], descendants: true }, { propertyName: \"hueHandle\", first: true, predicate: [\"hueHandle\"], descendants: true }], ngImport: i0, template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"{'p-colorpicker p-component':true,'p-colorpicker-overlay':!inline,'p-colorpicker-dragging':colorDragging||hueDragging}\">\n            <input #input type=\"text\" *ngIf=\"!inline\" class=\"p-colorpicker-preview p-inputtext\" readonly=\"readonly\" [ngClass]=\"{'p-disabled': disabled}\"\n                (focus)=\"onInputFocus()\" (click)=\"onInputClick()\" (keydown)=\"onInputKeydown($event)\" [attr.id]=\"inputId\" [attr.tabindex]=\"tabindex\" [disabled]=\"disabled\"\n                [style.backgroundColor]=\"inputBgColor\">\n            <div *ngIf=\"inline || overlayVisible\" [ngClass]=\"{'p-colorpicker-panel': true, 'p-colorpicker-overlay-panel':!inline, 'p-disabled': disabled}\" (click)=\"onOverlayClick($event)\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"inline === true\"\n                    (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <div class=\"p-colorpicker-content\">\n                    <div #colorSelector class=\"p-colorpicker-color-selector\" (touchstart)=\"onColorTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\" (mousedown)=\"onColorMousedown($event)\">\n                        <div class=\"p-colorpicker-color\">\n                            <div #colorHandle class=\"p-colorpicker-color-handle\"></div>\n                        </div>\n                    </div>\n                    <div #hue class=\"p-colorpicker-hue\" (mousedown)=\"onHueMousedown($event)\" (touchstart)=\"onHueTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\">\n                        <div #hueHandle class=\"p-colorpicker-hue-handle\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\".p-colorpicker{display:inline-block}.p-colorpicker-dragging{cursor:pointer}.p-colorpicker-overlay{position:relative}.p-colorpicker-panel{position:relative;width:193px;height:166px}.p-colorpicker-overlay-panel{position:absolute;top:0;left:0}.p-colorpicker-preview{cursor:pointer}.p-colorpicker-panel .p-colorpicker-content{position:relative}.p-colorpicker-panel .p-colorpicker-color-selector{width:150px;height:150px;top:8px;left:8px;position:absolute}.p-colorpicker-panel .p-colorpicker-color{width:150px;height:150px}.p-colorpicker-panel .p-colorpicker-color-handle{position:absolute;top:0px;left:150px;border-radius:100%;width:10px;height:10px;border-width:1px;border-style:solid;margin:-5px 0 0 -5px;cursor:pointer;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue{width:17px;height:150px;top:8px;left:167px;position:absolute;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue-handle{position:absolute;top:150px;left:0px;width:21px;margin-left:-2px;margin-top:-5px;height:10px;border-width:2px;border-style:solid;opacity:.85;cursor:pointer}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], animations: [\n        trigger('overlayAnimation', [\n            transition(':enter', [\n                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition(':leave', [\n                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPicker, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-colorPicker', template: `\n        <div #container [ngStyle]=\"style\" [class]=\"styleClass\" [ngClass]=\"{'p-colorpicker p-component':true,'p-colorpicker-overlay':!inline,'p-colorpicker-dragging':colorDragging||hueDragging}\">\n            <input #input type=\"text\" *ngIf=\"!inline\" class=\"p-colorpicker-preview p-inputtext\" readonly=\"readonly\" [ngClass]=\"{'p-disabled': disabled}\"\n                (focus)=\"onInputFocus()\" (click)=\"onInputClick()\" (keydown)=\"onInputKeydown($event)\" [attr.id]=\"inputId\" [attr.tabindex]=\"tabindex\" [disabled]=\"disabled\"\n                [style.backgroundColor]=\"inputBgColor\">\n            <div *ngIf=\"inline || overlayVisible\" [ngClass]=\"{'p-colorpicker-panel': true, 'p-colorpicker-overlay-panel':!inline, 'p-disabled': disabled}\" (click)=\"onOverlayClick($event)\"\n                [@overlayAnimation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" [@.disabled]=\"inline === true\"\n                    (@overlayAnimation.start)=\"onOverlayAnimationStart($event)\" (@overlayAnimation.done)=\"onOverlayAnimationEnd($event)\">\n                <div class=\"p-colorpicker-content\">\n                    <div #colorSelector class=\"p-colorpicker-color-selector\" (touchstart)=\"onColorTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\" (mousedown)=\"onColorMousedown($event)\">\n                        <div class=\"p-colorpicker-color\">\n                            <div #colorHandle class=\"p-colorpicker-color-handle\"></div>\n                        </div>\n                    </div>\n                    <div #hue class=\"p-colorpicker-hue\" (mousedown)=\"onHueMousedown($event)\" (touchstart)=\"onHueTouchStart($event)\" (touchmove)=\"onMove($event)\" (touchend)=\"onDragEnd()\">\n                        <div #hueHandle class=\"p-colorpicker-hue-handle\"></div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('overlayAnimation', [\n                            transition(':enter', [\n                                style({ opacity: 0, transform: 'scaleY(0.8)' }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition(':leave', [\n                                animate('{{hideTransitionParams}}', style({ opacity: 0 }))\n                            ])\n                        ])\n                    ], providers: [COLORPICKER_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-colorpicker{display:inline-block}.p-colorpicker-dragging{cursor:pointer}.p-colorpicker-overlay{position:relative}.p-colorpicker-panel{position:relative;width:193px;height:166px}.p-colorpicker-overlay-panel{position:absolute;top:0;left:0}.p-colorpicker-preview{cursor:pointer}.p-colorpicker-panel .p-colorpicker-content{position:relative}.p-colorpicker-panel .p-colorpicker-color-selector{width:150px;height:150px;top:8px;left:8px;position:absolute}.p-colorpicker-panel .p-colorpicker-color{width:150px;height:150px}.p-colorpicker-panel .p-colorpicker-color-handle{position:absolute;top:0px;left:150px;border-radius:100%;width:10px;height:10px;border-width:1px;border-style:solid;margin:-5px 0 0 -5px;cursor:pointer;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue{width:17px;height:150px;top:8px;left:167px;position:absolute;opacity:.85}.p-colorpicker-panel .p-colorpicker-hue-handle{position:absolute;top:150px;left:0px;width:21px;margin-left:-2px;margin-top:-5px;height:10px;border-width:2px;border-style:solid;opacity:.85;cursor:pointer}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }]; }, propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], inline: [{\n                type: Input\n            }], format: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], inputId: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], onChange: [{\n                type: Output\n            }], onShow: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], containerViewChild: [{\n                type: ViewChild,\n                args: ['container']\n            }], inputViewChild: [{\n                type: ViewChild,\n                args: ['input']\n            }], colorSelector: [{\n                type: ViewChild,\n                args: ['colorSelector']\n            }], colorHandle: [{\n                type: ViewChild,\n                args: ['colorHandle']\n            }], hue: [{\n                type: ViewChild,\n                args: ['hue']\n            }], hueHandle: [{\n                type: ViewChild,\n                args: ['hueHandle']\n            }] } });\nclass ColorPickerModule {\n}\nColorPickerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPickerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nColorPickerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPickerModule, declarations: [ColorPicker], imports: [CommonModule], exports: [ColorPicker] });\nColorPickerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPickerModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ColorPickerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [ColorPicker],\n                    declarations: [ColorPicker]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { COLORPICKER_VALUE_ACCESSOR, ColorPicker, ColorPickerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,YAArB,EAAmCC,SAAnC,EAA8CC,uBAA9C,EAAuEC,iBAAvE,EAA0FC,KAA1F,EAAiGC,MAAjG,EAAyGC,SAAzG,EAAoHC,QAApH,QAAoI,eAApI;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,EAAqBC,6BAArB,QAA0D,aAA1D;AACA,SAASC,iBAAT,QAAkC,gBAAlC;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;;;;;;;;;;;;;;;;gBA4gB8FpB,E;;IAAAA,EAGlF,iC;IAHkFA,EAI9E;MAJ8EA,EAI9E;MAAA,eAJ8EA,EAI9E;MAAA,OAJ8EA,EAIrE,mCAAT;IAAA;MAJ8EA,EAI9E;MAAA,eAJ8EA,EAI9E;MAAA,OAJ8EA,EAI5C,mCAAlC;IAAA;MAJ8EA,EAI9E;MAAA,eAJ8EA,EAI9E;MAAA,OAJ8EA,EAIjB,2CAA7D;IAAA,E;IAJ8EA,EAGlF,e;;;;mBAHkFA,E;IAAAA,EAK9E,qD;IAL8EA,EAGsB,uBAHtBA,EAGsB,uE;IAHtBA,EAIO,+D;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAJPA,E;;IAAAA,EAMlF,4B;IANkFA,EAM6D;MAN7DA,EAM6D;MAAA,gBAN7DA,EAM6D;MAAA,OAN7DA,EAMsE,4CAAT;IAAA;MAN7DA,EAM6D;MAAA,gBAN7DA,EAM6D;MAAA,OAN7DA,EAQ/C,qDAF4G;IAAA;MAN7DA,EAM6D;MAAA,gBAN7DA,EAM6D;MAAA,OAN7DA,EAQY,mDAFiD;IAAA,E;IAN7DA,EAS9E,4C;IAT8EA,EAUjB;MAViBA,EAUjB;MAAA,gBAViBA,EAUjB;MAAA,OAViBA,EAUH,+CAAd;IAAA;MAViBA,EAUjB;MAAA,gBAViBA,EAUjB;MAAA,OAViBA,EAUqC,oCAAtD;IAAA;MAViBA,EAUjB;MAAA,gBAViBA,EAUjB;MAAA,OAViBA,EAUiE,iCAAlF;IAAA;MAViBA,EAUjB;MAAA,gBAViBA,EAUjB;MAAA,OAViBA,EAU2F,8CAA5G;IAAA,E;IAViBA,EAWtE,6B;IAXsEA,EAYlE,4B;IAZkEA,EAatE,iB;IAbsEA,EAe1E,iC;IAf0EA,EAetC;MAfsCA,EAetC;MAAA,gBAfsCA,EAetC;MAAA,OAfsCA,EAezB,4CAAb;IAAA;MAfsCA,EAetC;MAAA,gBAfsCA,EAetC;MAAA,OAfsCA,EAea,6CAAnD;IAAA;MAfsCA,EAetC;MAAA,gBAfsCA,EAetC;MAAA,OAfsCA,EAemD,oCAAzF;IAAA;MAfsCA,EAetC;MAAA,gBAfsCA,EAetC;MAAA,OAfsCA,EAe+E,iCAArH;IAAA,E;IAfsCA,EAgBtE,4B;IAhBsEA,EAiB1E,mB;;;;mBAjB0EA,E;IAAAA,EAM5C,uBAN4CA,EAM5C,gFAN4CA,EAM5C,yBAN4CA,EAM5C,4H;;;;;;;;;;;;AAhhBlD,MAAMqB,0BAA0B,GAAG;EAC/BC,OAAO,EAAEJ,iBADsB;EAE/BK,WAAW,EAAEtB,UAAU,CAAC,MAAMuB,WAAP,CAFQ;EAG/BC,KAAK,EAAE;AAHwB,CAAnC;;AAKA,MAAMD,WAAN,CAAkB;EACdE,WAAW,CAACC,EAAD,EAAKC,QAAL,EAAeC,EAAf,EAAmBC,MAAnB,EAA2BC,cAA3B,EAA2C;IAClD,KAAKJ,EAAL,GAAUA,EAAV;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,cAAL,GAAsBA,cAAtB;IACA,KAAKC,MAAL,GAAc,KAAd;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,qBAAL,GAA6B,iCAA7B;IACA,KAAKC,qBAAL,GAA6B,YAA7B;IACA,KAAKC,QAAL,GAAgB,IAAInC,YAAJ,EAAhB;IACA,KAAKoC,MAAL,GAAc,IAAIpC,YAAJ,EAAd;IACA,KAAKqC,MAAL,GAAc,IAAIrC,YAAJ,EAAd;IACA,KAAKsC,KAAL,GAAa;MAAEC,CAAC,EAAE,CAAL;MAAQC,CAAC,EAAE,GAAX;MAAgBC,CAAC,EAAE;IAAnB,CAAb;IACA,KAAKC,YAAL,GAAoB,QAApB;;IACA,KAAKC,aAAL,GAAqB,MAAM,CAAG,CAA9B;;IACA,KAAKC,cAAL,GAAsB,MAAM,CAAG,CAA/B;EACH;;EACgB,IAAbC,aAAa,CAACC,OAAD,EAAU;IACvB,KAAKC,sBAAL,GAA8BD,OAA9B;EACH;;EACc,IAAXE,WAAW,CAACF,OAAD,EAAU;IACrB,KAAKG,oBAAL,GAA4BH,OAA5B;EACH;;EACM,IAAHI,GAAG,CAACJ,OAAD,EAAU;IACb,KAAKK,YAAL,GAAoBL,OAApB;EACH;;EACY,IAATM,SAAS,CAACN,OAAD,EAAU;IACnB,KAAKO,kBAAL,GAA0BP,OAA1B;EACH;;EACDQ,cAAc,CAACC,KAAD,EAAQ;IAClB,IAAI,KAAKC,QAAT,EAAmB;MACf;IACH;;IACD,KAAKC,6BAAL;IACA,KAAKC,2BAAL;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,OAAL,CAAaL,KAAb;EACH;;EACDM,eAAe,CAACN,KAAD,EAAQ;IACnB,IAAI,KAAKC,QAAT,EAAmB;MACf;IACH;;IACD,KAAKG,WAAL,GAAmB,IAAnB;IACA,KAAKC,OAAL,CAAaL,KAAb,EAAoBA,KAAK,CAACO,cAAN,CAAqB,CAArB,CAApB;EACH;;EACDC,iBAAiB,CAACR,KAAD,EAAQ;IACrB,IAAI,KAAKC,QAAT,EAAmB;MACf;IACH;;IACD,KAAKQ,aAAL,GAAqB,IAArB;IACA,KAAKC,SAAL,CAAeV,KAAf,EAAsBA,KAAK,CAACO,cAAN,CAAqB,CAArB,CAAtB;EACH;;EACDF,OAAO,CAACL,KAAD,EAAQW,QAAR,EAAkB;IACrB,IAAIC,KAAK,GAAGD,QAAQ,GAAGA,QAAQ,CAACC,KAAZ,GAAoBZ,KAAK,CAACY,KAA9C;IACA,IAAIC,GAAG,GAAG,KAAKjB,YAAL,CAAkBkB,aAAlB,CAAgCC,qBAAhC,GAAwDF,GAAxD,IAA+DG,MAAM,CAACC,WAAP,IAAsBC,QAAQ,CAACC,eAAT,CAAyBC,SAA/C,IAA4DF,QAAQ,CAACG,IAAT,CAAcD,SAA1E,IAAuF,CAAtJ,CAAV;IACA,KAAKrC,KAAL,GAAa,KAAKuC,WAAL,CAAiB;MAC1BtC,CAAC,EAAEuC,IAAI,CAACC,KAAL,CAAW,OAAO,MAAMD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYF,IAAI,CAACG,GAAL,CAAS,GAAT,EAAed,KAAK,GAAGC,GAAvB,CAAZ,CAAb,IAA0D,GAArE,CADuB;MAE1B5B,CAAC,EAAE,KAAKF,KAAL,CAAWE,CAFY;MAG1BC,CAAC,EAAE,KAAKH,KAAL,CAAWG;IAHY,CAAjB,CAAb;IAKA,KAAKyC,mBAAL;IACA,KAAKC,QAAL;IACA,KAAKC,WAAL;IACA,KAAKjD,QAAL,CAAckD,IAAd,CAAmB;MAAEC,aAAa,EAAE/B,KAAjB;MAAwBjB,KAAK,EAAE,KAAKiD,gBAAL;IAA/B,CAAnB;EACH;;EACDC,gBAAgB,CAACjC,KAAD,EAAQ;IACpB,IAAI,KAAKC,QAAT,EAAmB;MACf;IACH;;IACD,KAAKC,6BAAL;IACA,KAAKC,2BAAL;IACA,KAAKM,aAAL,GAAqB,IAArB;IACA,KAAKC,SAAL,CAAeV,KAAf;EACH;;EACDkC,MAAM,CAAClC,KAAD,EAAQ;IACV,IAAI,KAAKS,aAAT,EAAwB;MACpB,KAAKC,SAAL,CAAeV,KAAf,EAAsBA,KAAK,CAACO,cAAN,CAAqB,CAArB,CAAtB;MACAP,KAAK,CAACmC,cAAN;IACH;;IACD,IAAI,KAAK/B,WAAT,EAAsB;MAClB,KAAKC,OAAL,CAAaL,KAAb,EAAoBA,KAAK,CAACO,cAAN,CAAqB,CAArB,CAApB;MACAP,KAAK,CAACmC,cAAN;IACH;EACJ;;EACDC,SAAS,GAAG;IACR,KAAK3B,aAAL,GAAqB,KAArB;IACA,KAAKL,WAAL,GAAmB,KAAnB;IACA,KAAKiC,+BAAL;IACA,KAAKC,6BAAL;EACH;;EACD5B,SAAS,CAACV,KAAD,EAAQW,QAAR,EAAkB;IACvB,IAAI4B,KAAK,GAAG5B,QAAQ,GAAGA,QAAQ,CAAC4B,KAAZ,GAAoBvC,KAAK,CAACuC,KAA9C;IACA,IAAI3B,KAAK,GAAGD,QAAQ,GAAGA,QAAQ,CAACC,KAAZ,GAAoBZ,KAAK,CAACY,KAA9C;IACA,IAAI4B,IAAI,GAAG,KAAKhD,sBAAL,CAA4BsB,aAA5B,CAA0CC,qBAA1C,EAAX;IACA,IAAIF,GAAG,GAAG2B,IAAI,CAAC3B,GAAL,IAAYG,MAAM,CAACC,WAAP,IAAsBC,QAAQ,CAACC,eAAT,CAAyBC,SAA/C,IAA4DF,QAAQ,CAACG,IAAT,CAAcD,SAA1E,IAAuF,CAAnG,CAAV;IACA,IAAIqB,IAAI,GAAGD,IAAI,CAACC,IAAL,GAAYvB,QAAQ,CAACG,IAAT,CAAcqB,UAArC;IACA,IAAIC,UAAU,GAAGpB,IAAI,CAACC,KAAL,CAAW,MAAOD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYF,IAAI,CAACG,GAAL,CAAS,GAAT,EAAgBa,KAAD,GAAUE,IAAzB,CAAZ,CAAP,GAAuD,GAAlE,CAAjB;IACA,IAAIG,UAAU,GAAGrB,IAAI,CAACC,KAAL,CAAW,OAAO,MAAMD,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYF,IAAI,CAACG,GAAL,CAAS,GAAT,EAAgBd,KAAD,GAAUC,GAAzB,CAAZ,CAAb,IAA4D,GAAvE,CAAjB;IACA,KAAK9B,KAAL,GAAa,KAAKuC,WAAL,CAAiB;MAC1BtC,CAAC,EAAE,KAAKD,KAAL,CAAWC,CADY;MAE1BC,CAAC,EAAE0D,UAFuB;MAG1BzD,CAAC,EAAE0D;IAHuB,CAAjB,CAAb;IAKA,KAAKhB,QAAL;IACA,KAAKC,WAAL;IACA,KAAKjD,QAAL,CAAckD,IAAd,CAAmB;MAAEC,aAAa,EAAE/B,KAAjB;MAAwBjB,KAAK,EAAE,KAAKiD,gBAAL;IAA/B,CAAnB;EACH;;EACDA,gBAAgB,GAAG;IACf,IAAIa,GAAJ;;IACA,QAAQ,KAAKtE,MAAb;MACI,KAAK,KAAL;QACIsE,GAAG,GAAG,MAAM,KAAKC,QAAL,CAAc,KAAK/D,KAAnB,CAAZ;QACA;;MACJ,KAAK,KAAL;QACI8D,GAAG,GAAG,KAAKE,QAAL,CAAc,KAAKhE,KAAnB,CAAN;QACA;;MACJ,KAAK,KAAL;QACI8D,GAAG,GAAG,KAAK9D,KAAX;QACA;IATR;;IAWA,OAAO8D,GAAP;EACH;;EACDhB,WAAW,GAAG;IACV,KAAKzC,aAAL,CAAmB,KAAK4C,gBAAL,EAAnB;EACH;;EACDgB,UAAU,CAACjE,KAAD,EAAQ;IACd,IAAIA,KAAJ,EAAW;MACP,QAAQ,KAAKR,MAAb;QACI,KAAK,KAAL;UACI,KAAKQ,KAAL,GAAa,KAAKkE,QAAL,CAAclE,KAAd,CAAb;UACA;;QACJ,KAAK,KAAL;UACI,KAAKA,KAAL,GAAa,KAAKmE,QAAL,CAAcnE,KAAd,CAAb;UACA;;QACJ,KAAK,KAAL;UACI,KAAKA,KAAL,GAAaA,KAAb;UACA;MATR;IAWH,CAZD,MAaK;MACD,KAAKA,KAAL,GAAa,KAAKkE,QAAL,CAAc,KAAK9D,YAAnB,CAAb;IACH;;IACD,KAAKwC,mBAAL;IACA,KAAKC,QAAL;IACA,KAAKxD,EAAL,CAAQ+E,YAAR;EACH;;EACDxB,mBAAmB,GAAG;IAClB,IAAI,KAAKnC,sBAAT,EAAiC;MAC7B,MAAM4D,GAAG,GAAG,EAAZ;MACAA,GAAG,CAACnE,CAAJ,GAAQ,GAAR;MACAmE,GAAG,CAAClE,CAAJ,GAAQ,GAAR;MACAkE,GAAG,CAACpE,CAAJ,GAAQ,KAAKD,KAAL,CAAWC,CAAnB;MACA,KAAKQ,sBAAL,CAA4BsB,aAA5B,CAA0C3D,KAA1C,CAAgDkG,eAAhD,GAAkE,MAAM,KAAKP,QAAL,CAAcM,GAAd,CAAxE;IACH;EACJ;;EACDxB,QAAQ,GAAG;IACP,IAAI,KAAKlC,oBAAL,IAA6B,KAAKI,kBAAL,CAAwBgB,aAAzD,EAAwE;MACpE,KAAKpB,oBAAL,CAA0BoB,aAA1B,CAAwC3D,KAAxC,CAA8CsF,IAA9C,GAAqDlB,IAAI,CAACC,KAAL,CAAW,MAAM,KAAKzC,KAAL,CAAWE,CAAjB,GAAqB,GAAhC,IAAuC,IAA5F;MACA,KAAKS,oBAAL,CAA0BoB,aAA1B,CAAwC3D,KAAxC,CAA8C0D,GAA9C,GAAoDU,IAAI,CAACC,KAAL,CAAW,OAAO,MAAM,KAAKzC,KAAL,CAAWG,CAAxB,IAA6B,GAAxC,IAA+C,IAAnG;MACA,KAAKY,kBAAL,CAAwBgB,aAAxB,CAAsC3D,KAAtC,CAA4C0D,GAA5C,GAAkDU,IAAI,CAACC,KAAL,CAAW,MAAO,MAAM,KAAKzC,KAAL,CAAWC,CAAjB,GAAqB,GAAvC,IAA+C,IAAjG;IACH;;IACD,KAAKsE,YAAL,GAAoB,MAAM,KAAKR,QAAL,CAAc,KAAK/D,KAAnB,CAA1B;EACH;;EACDwE,YAAY,GAAG;IACX,KAAKlE,cAAL;EACH;;EACDmE,IAAI,GAAG;IACH,KAAKC,cAAL,GAAsB,IAAtB;IACA,KAAKrF,EAAL,CAAQ+E,YAAR;EACH;;EACDO,uBAAuB,CAAC1D,KAAD,EAAQ;IAC3B,QAAQA,KAAK,CAAC2D,OAAd;MACI,KAAK,SAAL;QACI,IAAI,CAAC,KAAKC,MAAV,EAAkB;UACd,KAAKC,OAAL,GAAe7D,KAAK,CAACT,OAArB;UACA,KAAKuE,aAAL;;UACA,IAAI,KAAKtF,UAAT,EAAqB;YACjBd,WAAW,CAACqG,GAAZ,CAAgB,SAAhB,EAA2B,KAAKF,OAAhC,EAAyC,KAAKxF,MAAL,CAAY2F,MAAZ,CAAmBH,OAA5D;UACH;;UACD,KAAKI,YAAL;UACA,KAAKC,yBAAL;UACA,KAAKC,0BAAL;UACA,KAAKC,kBAAL;UACA,KAAKzC,mBAAL;UACA,KAAKC,QAAL;QACH;;QACD;;MACJ,KAAK,MAAL;QACI,KAAKyC,aAAL;QACA;IAlBR;EAoBH;;EACDC,qBAAqB,CAACtE,KAAD,EAAQ;IACzB,QAAQA,KAAK,CAAC2D,OAAd;MACI,KAAK,SAAL;QACI,IAAI,CAAC,KAAKC,MAAV,EAAkB;UACd,KAAK/E,MAAL,CAAYiD,IAAZ,CAAiB,EAAjB;QACH;;QACD;;MACJ,KAAK,MAAL;QACI,IAAI,KAAKtD,UAAT,EAAqB;UACjBd,WAAW,CAAC6G,KAAZ,CAAkBvE,KAAK,CAACT,OAAxB;QACH;;QACD,KAAKT,MAAL,CAAYgD,IAAZ,CAAiB,EAAjB;QACA;IAXR;EAaH;;EACDgC,aAAa,GAAG;IACZ,IAAI,KAAKU,QAAT,EAAmB;MACf,IAAI,KAAKA,QAAL,KAAkB,MAAtB,EACItD,QAAQ,CAACG,IAAT,CAAcoD,WAAd,CAA0B,KAAKZ,OAA/B,EADJ,KAGItG,UAAU,CAACkH,WAAX,CAAuB,KAAKZ,OAA5B,EAAqC,KAAKW,QAA1C;IACP;EACJ;;EACDE,oBAAoB,GAAG;IACnB,IAAI,KAAKb,OAAL,IAAgB,KAAKW,QAAzB,EAAmC;MAC/B,KAAKtG,EAAL,CAAQ4C,aAAR,CAAsB2D,WAAtB,CAAkC,KAAKZ,OAAvC;IACH;EACJ;;EACDI,YAAY,GAAG;IACX,IAAI,KAAKO,QAAT,EACIjH,UAAU,CAACoH,gBAAX,CAA4B,KAAKd,OAAjC,EAA0C,KAAKe,cAAL,CAAoB9D,aAA9D,EADJ,KAGIvD,UAAU,CAACsH,gBAAX,CAA4B,KAAKhB,OAAjC,EAA0C,KAAKe,cAAL,CAAoB9D,aAA9D;EACP;;EACDgE,IAAI,GAAG;IACH,KAAKrB,cAAL,GAAsB,KAAtB;IACA,KAAKrF,EAAL,CAAQ+E,YAAR;EACH;;EACD4B,YAAY,GAAG;IACX,KAAKC,SAAL,GAAiB,IAAjB;IACA,KAAKC,WAAL;EACH;;EACDA,WAAW,GAAG;IACV,IAAI,CAAC,KAAKxB,cAAV,EACI,KAAKD,IAAL,GADJ,KAGI,KAAKsB,IAAL;EACP;;EACDI,cAAc,CAAClF,KAAD,EAAQ;IAClB,QAAQA,KAAK,CAACmF,KAAd;MACI;MACA,KAAK,EAAL;QACI,KAAKF,WAAL;QACAjF,KAAK,CAACmC,cAAN;QACA;MACJ;;MACA,KAAK,EAAL;MACA,KAAK,CAAL;QACI,KAAK2C,IAAL;QACA;IAVR;EAYH;;EACDM,cAAc,CAACpF,KAAD,EAAQ;IAClB,KAAK1B,cAAL,CAAoB+G,GAApB,CAAwB;MACpBtD,aAAa,EAAE/B,KADK;MAEpBsF,MAAM,EAAE,KAAKpH,EAAL,CAAQ4C;IAFI,CAAxB;IAIA,KAAKkE,SAAL,GAAiB,IAAjB;EACH;;EACDO,gBAAgB,CAACC,EAAD,EAAK;IACjB,KAAKpG,aAAL,GAAqBoG,EAArB;EACH;;EACDC,iBAAiB,CAACD,EAAD,EAAK;IAClB,KAAKnG,cAAL,GAAsBmG,EAAtB;EACH;;EACDE,gBAAgB,CAAC7C,GAAD,EAAM;IAClB,KAAK5C,QAAL,GAAgB4C,GAAhB;IACA,KAAKzE,EAAL,CAAQ+E,YAAR;EACH;;EACDe,yBAAyB,GAAG;IACxB,IAAI,CAAC,KAAKyB,qBAAV,EAAiC;MAC7B,MAAMC,cAAc,GAAG,KAAK1H,EAAL,GAAU,KAAKA,EAAL,CAAQ4C,aAAR,CAAsB+E,aAAhC,GAAgD,UAAvE;MACA,KAAKF,qBAAL,GAA6B,KAAKxH,QAAL,CAAc2H,MAAd,CAAqBF,cAArB,EAAqC,OAArC,EAA8C,MAAM;QAC7E,IAAI,CAAC,KAAKZ,SAAV,EAAqB;UACjB,KAAKvB,cAAL,GAAsB,KAAtB;UACA,KAAKsC,2BAAL;QACH;;QACD,KAAKf,SAAL,GAAiB,KAAjB;QACA,KAAK5G,EAAL,CAAQ+E,YAAR;MACH,CAP4B,CAA7B;IAQH;EACJ;;EACD4C,2BAA2B,GAAG;IAC1B,IAAI,KAAKJ,qBAAT,EAAgC;MAC5B,KAAKA,qBAAL;MACA,KAAKA,qBAAL,GAA6B,IAA7B;IACH;EACJ;;EACDzF,6BAA6B,GAAG;IAC5B,IAAI,CAAC,KAAK8F,yBAAV,EAAqC;MACjC,MAAMJ,cAAc,GAAG,KAAK1H,EAAL,GAAU,KAAKA,EAAL,CAAQ4C,aAAR,CAAsB+E,aAAhC,GAAgD,UAAvE;MACA,KAAKG,yBAAL,GAAiC,KAAK7H,QAAL,CAAc2H,MAAd,CAAqBF,cAArB,EAAqC,WAArC,EAAmD5F,KAAD,IAAW;QAC1F,IAAI,KAAKS,aAAT,EAAwB;UACpB,KAAKC,SAAL,CAAeV,KAAf;QACH;;QACD,IAAI,KAAKI,WAAT,EAAsB;UAClB,KAAKC,OAAL,CAAaL,KAAb;QACH;MACJ,CAPgC,CAAjC;IAQH;EACJ;;EACDqC,+BAA+B,GAAG;IAC9B,IAAI,KAAK2D,yBAAT,EAAoC;MAChC,KAAKA,yBAAL;MACA,KAAKA,yBAAL,GAAiC,IAAjC;IACH;EACJ;;EACD7F,2BAA2B,GAAG;IAC1B,IAAI,CAAC,KAAK8F,uBAAV,EAAmC;MAC/B,MAAML,cAAc,GAAG,KAAK1H,EAAL,GAAU,KAAKA,EAAL,CAAQ4C,aAAR,CAAsB+E,aAAhC,GAAgD,UAAvE;MACA,KAAKI,uBAAL,GAA+B,KAAK9H,QAAL,CAAc2H,MAAd,CAAqBF,cAArB,EAAqC,SAArC,EAAgD,MAAM;QACjF,KAAKnF,aAAL,GAAqB,KAArB;QACA,KAAKL,WAAL,GAAmB,KAAnB;QACA,KAAKiC,+BAAL;QACA,KAAKC,6BAAL;MACH,CAL8B,CAA/B;IAMH;EACJ;;EACDA,6BAA6B,GAAG;IAC5B,IAAI,KAAK2D,uBAAT,EAAkC;MAC9B,KAAKA,uBAAL;MACA,KAAKA,uBAAL,GAA+B,IAA/B;IACH;EACJ;;EACD9B,0BAA0B,GAAG;IACzB,KAAK+B,sBAAL,GAA8B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA9B;IACApF,MAAM,CAACqF,gBAAP,CAAwB,QAAxB,EAAkC,KAAKH,sBAAvC;EACH;;EACDI,4BAA4B,GAAG;IAC3B,IAAI,KAAKJ,sBAAT,EAAiC;MAC7BlF,MAAM,CAACuF,mBAAP,CAA2B,QAA3B,EAAqC,KAAKL,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDC,cAAc,GAAG;IACb,IAAI,KAAK1C,cAAL,IAAuB,CAAClG,UAAU,CAACiJ,aAAX,EAA5B,EAAwD;MACpD,KAAK1B,IAAL;IACH;EACJ;;EACDV,kBAAkB,GAAG;IACjB,IAAI,CAAC,KAAKqC,aAAV,EAAyB;MACrB,KAAKA,aAAL,GAAqB,IAAIjJ,6BAAJ,CAAkC,KAAKkJ,kBAAL,CAAwB5F,aAA1D,EAAyE,MAAM;QAChG,IAAI,KAAK2C,cAAT,EAAyB;UACrB,KAAKqB,IAAL;QACH;MACJ,CAJoB,CAArB;IAKH;;IACD,KAAK2B,aAAL,CAAmBrC,kBAAnB;EACH;;EACDuC,oBAAoB,GAAG;IACnB,IAAI,KAAKF,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmBE,oBAAnB;IACH;EACJ;;EACDrF,WAAW,CAAC8B,GAAD,EAAM;IACb,OAAO;MACHpE,CAAC,EAAEuC,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY2B,GAAG,CAACpE,CAAhB,CAAd,CADA;MAEHC,CAAC,EAAEsC,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY2B,GAAG,CAACnE,CAAhB,CAAd,CAFA;MAGHC,CAAC,EAAEqC,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAY2B,GAAG,CAAClE,CAAhB,CAAd;IAHA,CAAP;EAKH;;EACD0H,WAAW,CAACC,GAAD,EAAM;IACb,OAAO;MACHC,CAAC,EAAEvF,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYoF,GAAG,CAACC,CAAhB,CAAd,CADA;MAEHC,CAAC,EAAExF,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYoF,GAAG,CAACE,CAAhB,CAAd,CAFA;MAGH7H,CAAC,EAAEqC,IAAI,CAACG,GAAL,CAAS,GAAT,EAAcH,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYoF,GAAG,CAAC3H,CAAhB,CAAd;IAHA,CAAP;EAKH;;EACD8H,WAAW,CAACC,GAAD,EAAM;IACb,IAAIC,GAAG,GAAG,IAAID,GAAG,CAACE,MAAlB;;IACA,IAAID,GAAG,GAAG,CAAV,EAAa;MACT,IAAIE,CAAC,GAAG,EAAR;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,GAApB,EAAyBG,CAAC,EAA1B,EAA8B;QAC1BD,CAAC,CAACE,IAAF,CAAO,GAAP;MACH;;MACDF,CAAC,CAACE,IAAF,CAAOL,GAAP;MACAA,GAAG,GAAGG,CAAC,CAACG,IAAF,CAAO,EAAP,CAAN;IACH;;IACD,OAAON,GAAP;EACH;;EACDO,QAAQ,CAACP,GAAD,EAAM;IACV,IAAIQ,QAAQ,GAAGC,QAAQ,CAAGT,GAAG,CAACU,OAAJ,CAAY,GAAZ,IAAmB,CAAC,CAArB,GAA0BV,GAAG,CAACW,SAAJ,CAAc,CAAd,CAA1B,GAA6CX,GAA/C,EAAqD,EAArD,CAAvB;IACA,OAAO;MAAEH,CAAC,EAAEW,QAAQ,IAAI,EAAjB;MAAqBV,CAAC,EAAE,CAACU,QAAQ,GAAG,QAAZ,KAAyB,CAAjD;MAAoDvI,CAAC,EAAGuI,QAAQ,GAAG;IAAnE,CAAP;EACH;;EACDxE,QAAQ,CAACgE,GAAD,EAAM;IACV,OAAO,KAAK/D,QAAL,CAAc,KAAKsE,QAAL,CAAcP,GAAd,CAAd,CAAP;EACH;;EACD/D,QAAQ,CAAC2D,GAAD,EAAM;IACV,IAAIzD,GAAG,GAAG;MACNpE,CAAC,EAAE,CADG;MAENC,CAAC,EAAE,CAFG;MAGNC,CAAC,EAAE;IAHG,CAAV;IAKA,IAAIwC,GAAG,GAAGH,IAAI,CAACG,GAAL,CAASmF,GAAG,CAACC,CAAb,EAAgBD,GAAG,CAACE,CAApB,EAAuBF,GAAG,CAAC3H,CAA3B,CAAV;IACA,IAAIuC,GAAG,GAAGF,IAAI,CAACE,GAAL,CAASoF,GAAG,CAACC,CAAb,EAAgBD,GAAG,CAACE,CAApB,EAAuBF,GAAG,CAAC3H,CAA3B,CAAV;IACA,IAAI2I,KAAK,GAAGpG,GAAG,GAAGC,GAAlB;IACA0B,GAAG,CAAClE,CAAJ,GAAQuC,GAAR;IACA2B,GAAG,CAACnE,CAAJ,GAAQwC,GAAG,IAAI,CAAP,GAAW,MAAMoG,KAAN,GAAcpG,GAAzB,GAA+B,CAAvC;;IACA,IAAI2B,GAAG,CAACnE,CAAJ,IAAS,CAAb,EAAgB;MACZ,IAAI4H,GAAG,CAACC,CAAJ,IAASrF,GAAb,EAAkB;QACd2B,GAAG,CAACpE,CAAJ,GAAQ,CAAC6H,GAAG,CAACE,CAAJ,GAAQF,GAAG,CAAC3H,CAAb,IAAkB2I,KAA1B;MACH,CAFD,MAGK,IAAIhB,GAAG,CAACE,CAAJ,IAAStF,GAAb,EAAkB;QACnB2B,GAAG,CAACpE,CAAJ,GAAQ,IAAI,CAAC6H,GAAG,CAAC3H,CAAJ,GAAQ2H,GAAG,CAACC,CAAb,IAAkBe,KAA9B;MACH,CAFI,MAGA;QACDzE,GAAG,CAACpE,CAAJ,GAAQ,IAAI,CAAC6H,GAAG,CAACC,CAAJ,GAAQD,GAAG,CAACE,CAAb,IAAkBc,KAA9B;MACH;IACJ,CAVD,MAWK;MACDzE,GAAG,CAACpE,CAAJ,GAAQ,CAAC,CAAT;IACH;;IACDoE,GAAG,CAACpE,CAAJ,IAAS,EAAT;;IACA,IAAIoE,GAAG,CAACpE,CAAJ,GAAQ,CAAZ,EAAe;MACXoE,GAAG,CAACpE,CAAJ,IAAS,GAAT;IACH;;IACDoE,GAAG,CAACnE,CAAJ,IAAS,MAAM,GAAf;IACAmE,GAAG,CAAClE,CAAJ,IAAS,MAAM,GAAf;IACA,OAAOkE,GAAP;EACH;;EACDL,QAAQ,CAACK,GAAD,EAAM;IACV,IAAIyD,GAAG,GAAG;MACNC,CAAC,EAAE,IADG;MACGC,CAAC,EAAE,IADN;MACY7H,CAAC,EAAE;IADf,CAAV;IAGA,IAAIF,CAAC,GAAGoE,GAAG,CAACpE,CAAZ;IACA,IAAIC,CAAC,GAAGmE,GAAG,CAACnE,CAAJ,GAAQ,GAAR,GAAc,GAAtB;IACA,IAAI6I,CAAC,GAAG1E,GAAG,CAAClE,CAAJ,GAAQ,GAAR,GAAc,GAAtB;;IACA,IAAID,CAAC,IAAI,CAAT,EAAY;MACR4H,GAAG,GAAG;QACFC,CAAC,EAAEgB,CADD;QAEFf,CAAC,EAAEe,CAFD;QAGF5I,CAAC,EAAE4I;MAHD,CAAN;IAKH,CAND,MAOK;MACD,IAAIC,EAAE,GAAGD,CAAT;MACA,IAAIE,EAAE,GAAG,CAAC,MAAM/I,CAAP,IAAY6I,CAAZ,GAAgB,GAAzB;MACA,IAAIG,EAAE,GAAG,CAACF,EAAE,GAAGC,EAAN,KAAahJ,CAAC,GAAG,EAAjB,IAAuB,EAAhC;MACA,IAAIA,CAAC,IAAI,GAAT,EACIA,CAAC,GAAG,CAAJ;;MACJ,IAAIA,CAAC,GAAG,EAAR,EAAY;QACR6H,GAAG,CAACC,CAAJ,GAAQiB,EAAR;QACAlB,GAAG,CAAC3H,CAAJ,GAAQ8I,EAAR;QACAnB,GAAG,CAACE,CAAJ,GAAQiB,EAAE,GAAGC,EAAb;MACH,CAJD,MAKK,IAAIjJ,CAAC,GAAG,GAAR,EAAa;QACd6H,GAAG,CAACE,CAAJ,GAAQgB,EAAR;QACAlB,GAAG,CAAC3H,CAAJ,GAAQ8I,EAAR;QACAnB,GAAG,CAACC,CAAJ,GAAQiB,EAAE,GAAGE,EAAb;MACH,CAJI,MAKA,IAAIjJ,CAAC,GAAG,GAAR,EAAa;QACd6H,GAAG,CAACE,CAAJ,GAAQgB,EAAR;QACAlB,GAAG,CAACC,CAAJ,GAAQkB,EAAR;QACAnB,GAAG,CAAC3H,CAAJ,GAAQ8I,EAAE,GAAGC,EAAb;MACH,CAJI,MAKA,IAAIjJ,CAAC,GAAG,GAAR,EAAa;QACd6H,GAAG,CAAC3H,CAAJ,GAAQ6I,EAAR;QACAlB,GAAG,CAACC,CAAJ,GAAQkB,EAAR;QACAnB,GAAG,CAACE,CAAJ,GAAQgB,EAAE,GAAGE,EAAb;MACH,CAJI,MAKA,IAAIjJ,CAAC,GAAG,GAAR,EAAa;QACd6H,GAAG,CAAC3H,CAAJ,GAAQ6I,EAAR;QACAlB,GAAG,CAACE,CAAJ,GAAQiB,EAAR;QACAnB,GAAG,CAACC,CAAJ,GAAQkB,EAAE,GAAGC,EAAb;MACH,CAJI,MAKA,IAAIjJ,CAAC,GAAG,GAAR,EAAa;QACd6H,GAAG,CAACC,CAAJ,GAAQiB,EAAR;QACAlB,GAAG,CAACE,CAAJ,GAAQiB,EAAR;QACAnB,GAAG,CAAC3H,CAAJ,GAAQ6I,EAAE,GAAGE,EAAb;MACH,CAJI,MAKA;QACDpB,GAAG,CAACC,CAAJ,GAAQ,CAAR;QACAD,GAAG,CAACE,CAAJ,GAAQ,CAAR;QACAF,GAAG,CAAC3H,CAAJ,GAAQ,CAAR;MACH;IACJ;;IACD,OAAO;MAAE4H,CAAC,EAAEvF,IAAI,CAAC2G,KAAL,CAAWrB,GAAG,CAACC,CAAf,CAAL;MAAwBC,CAAC,EAAExF,IAAI,CAAC2G,KAAL,CAAWrB,GAAG,CAACE,CAAf,CAA3B;MAA8C7H,CAAC,EAAEqC,IAAI,CAAC2G,KAAL,CAAWrB,GAAG,CAAC3H,CAAf;IAAjD,CAAP;EACH;;EACDiJ,QAAQ,CAACtB,GAAD,EAAM;IACV,IAAII,GAAG,GAAG,CACNJ,GAAG,CAACC,CAAJ,CAAMsB,QAAN,CAAe,EAAf,CADM,EAENvB,GAAG,CAACE,CAAJ,CAAMqB,QAAN,CAAe,EAAf,CAFM,EAGNvB,GAAG,CAAC3H,CAAJ,CAAMkJ,QAAN,CAAe,EAAf,CAHM,CAAV;;IAKA,KAAK,IAAIC,GAAT,IAAgBpB,GAAhB,EAAqB;MACjB,IAAIA,GAAG,CAACoB,GAAD,CAAH,CAASlB,MAAT,IAAmB,CAAvB,EAA0B;QACtBF,GAAG,CAACoB,GAAD,CAAH,GAAW,MAAMpB,GAAG,CAACoB,GAAD,CAApB;MACH;IACJ;;IACD,OAAOpB,GAAG,CAACM,IAAJ,CAAS,EAAT,CAAP;EACH;;EACDzE,QAAQ,CAACM,GAAD,EAAM;IACV,OAAO,KAAK+E,QAAL,CAAc,KAAKpF,QAAL,CAAcK,GAAd,CAAd,CAAP;EACH;;EACDiB,aAAa,GAAG;IACZ,KAAKsC,oBAAL;IACA,KAAKL,4BAAL;IACA,KAAKP,2BAAL;IACA,KAAKlC,OAAL,GAAe,IAAf;EACH;;EACDyE,WAAW,GAAG;IACV,IAAI,KAAK7B,aAAT,EAAwB;MACpB,KAAKA,aAAL,CAAmB8B,OAAnB;MACA,KAAK9B,aAAL,GAAqB,IAArB;IACH;;IACD,IAAI,KAAK5C,OAAL,IAAgB,KAAKrF,UAAzB,EAAqC;MACjCd,WAAW,CAAC6G,KAAZ,CAAkB,KAAKV,OAAvB;IACH;;IACD,KAAKa,oBAAL;IACA,KAAKL,aAAL;EACH;;AAngBa;;AAqgBlBtG,WAAW,CAACyK,IAAZ;EAAA,iBAAwGzK,WAAxG,EAA8FxB,EAA9F,mBAAqIA,EAAE,CAACkM,UAAxI,GAA8FlM,EAA9F,mBAA+JA,EAAE,CAACmM,SAAlK,GAA8FnM,EAA9F,mBAAwLA,EAAE,CAACoM,iBAA3L,GAA8FpM,EAA9F,mBAAyNoB,EAAE,CAACiL,aAA5N,GAA8FrM,EAA9F,mBAAsPoB,EAAE,CAACkL,cAAzP;AAAA;;AACA9K,WAAW,CAAC+K,IAAZ,kBAD8FvM,EAC9F;EAAA,MAA4FwB,WAA5F;EAAA;EAAA;IAAA;MAD8FxB,EAC9F;MAD8FA,EAC9F;MAD8FA,EAC9F;MAD8FA,EAC9F;MAD8FA,EAC9F;MAD8FA,EAC9F;IAAA;;IAAA;MAAA;;MAD8FA,EAC9F,qBAD8FA,EAC9F;MAD8FA,EAC9F,qBAD8FA,EAC9F;MAD8FA,EAC9F,qBAD8FA,EAC9F;MAD8FA,EAC9F,qBAD8FA,EAC9F;MAD8FA,EAC9F,qBAD8FA,EAC9F;MAD8FA,EAC9F,qBAD8FA,EAC9F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD8FA,EAC9F,oBAAikB,CAACqB,0BAAD,CAAjkB;EAAA;EAAA;EAAA;EAAA;IAAA;MAD8FrB,EAEtF,+BADR;MAD8FA,EAGlF,8DAFZ;MAD8FA,EAMlF,4DALZ;MAD8FA,EAoBtF,eAnBR;IAAA;;IAAA;MAD8FA,EAEpD,2BAD1C;MAD8FA,EAEtE,6CAFsEA,EAEtE,6EADxB;MAD8FA,EAGvD,aAFvC;MAD8FA,EAGvD,gCAFvC;MAD8FA,EAM5E,aALlB;MAD8FA,EAM5E,qDALlB;IAAA;EAAA;EAAA,eAoBumCc,EAAE,CAAC0L,OApB1mC,EAoBqsC1L,EAAE,CAAC2L,IApBxsC,EAoByyC3L,EAAE,CAAC4L,OApB5yC;EAAA;EAAA;EAAA;IAAA,WAoBg3C,CACx2ChM,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;MAAE+L,OAAO,EAAE,CAAX;MAAcC,SAAS,EAAE;IAAzB,CAAD,CADY,EAEjB/L,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAE+L,OAAO,EAAE;IAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADi2C;EApBh3C;EAAA;AAAA;;AA+BA;EAAA,mDAhC8F3M,EAgC9F,mBAA2FwB,WAA3F,EAAoH,CAAC;IACzGqL,IAAI,EAAE1M,SADmG;IAEzG2M,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAZ;MAA6BC,QAAQ,EAAG;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KApBmB;MAoBZC,UAAU,EAAE,CACKvM,OAAO,CAAC,kBAAD,EAAqB,CACxBC,UAAU,CAAC,QAAD,EAAW,CACjBC,KAAK,CAAC;QAAE+L,OAAO,EAAE,CAAX;QAAcC,SAAS,EAAE;MAAzB,CAAD,CADY,EAEjB/L,OAAO,CAAC,0BAAD,CAFU,CAAX,CADc,EAKxBF,UAAU,CAAC,QAAD,EAAW,CACjBE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAE+L,OAAO,EAAE;MAAX,CAAD,CAAlC,CADU,CAAX,CALc,CAArB,CADZ,CApBA;MA8BIO,SAAS,EAAE,CAAC7L,0BAAD,CA9Bf;MA8B6C8L,eAAe,EAAE/M,uBAAuB,CAACgN,MA9BtF;MA8B8FC,aAAa,EAAEhN,iBAAiB,CAACiN,IA9B/H;MA8BqIC,IAAI,EAAE;QACtI,SAAS;MAD6H,CA9B3I;MAgCIC,MAAM,EAAE,CAAC,0hCAAD;IAhCZ,CAAD;EAFmG,CAAD,CAApH,EAmC4B,YAAY;IAAE,OAAO,CAAC;MAAEX,IAAI,EAAE7M,EAAE,CAACkM;IAAX,CAAD,EAA0B;MAAEW,IAAI,EAAE7M,EAAE,CAACmM;IAAX,CAA1B,EAAkD;MAAEU,IAAI,EAAE7M,EAAE,CAACoM;IAAX,CAAlD,EAAkF;MAAES,IAAI,EAAEzL,EAAE,CAACiL;IAAX,CAAlF,EAA8G;MAAEQ,IAAI,EAAEzL,EAAE,CAACkL;IAAX,CAA9G,CAAP;EAAoJ,CAnC9L,EAmCgN;IAAE1L,KAAK,EAAE,CAAC;MAC1MiM,IAAI,EAAEvM;IADoM,CAAD,CAAT;IAEhMmN,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEvM;IADO,CAAD,CAFoL;IAIhM+G,MAAM,EAAE,CAAC;MACTwF,IAAI,EAAEvM;IADG,CAAD,CAJwL;IAMhM0B,MAAM,EAAE,CAAC;MACT6K,IAAI,EAAEvM;IADG,CAAD,CANwL;IAQhM2H,QAAQ,EAAE,CAAC;MACX4E,IAAI,EAAEvM;IADK,CAAD,CARsL;IAUhMoD,QAAQ,EAAE,CAAC;MACXmJ,IAAI,EAAEvM;IADK,CAAD,CAVsL;IAYhMoN,QAAQ,EAAE,CAAC;MACXb,IAAI,EAAEvM;IADK,CAAD,CAZsL;IAchMqN,OAAO,EAAE,CAAC;MACVd,IAAI,EAAEvM;IADI,CAAD,CAduL;IAgBhM2B,UAAU,EAAE,CAAC;MACb4K,IAAI,EAAEvM;IADO,CAAD,CAhBoL;IAkBhM4B,UAAU,EAAE,CAAC;MACb2K,IAAI,EAAEvM;IADO,CAAD,CAlBoL;IAoBhM6B,qBAAqB,EAAE,CAAC;MACxB0K,IAAI,EAAEvM;IADkB,CAAD,CApByK;IAsBhM8B,qBAAqB,EAAE,CAAC;MACxByK,IAAI,EAAEvM;IADkB,CAAD,CAtByK;IAwBhM+B,QAAQ,EAAE,CAAC;MACXwK,IAAI,EAAEtM;IADK,CAAD,CAxBsL;IA0BhM+B,MAAM,EAAE,CAAC;MACTuK,IAAI,EAAEtM;IADG,CAAD,CA1BwL;IA4BhMgC,MAAM,EAAE,CAAC;MACTsK,IAAI,EAAEtM;IADG,CAAD,CA5BwL;IA8BhM4J,kBAAkB,EAAE,CAAC;MACrB0C,IAAI,EAAErM,SADe;MAErBsM,IAAI,EAAE,CAAC,WAAD;IAFe,CAAD,CA9B4K;IAiChMzE,cAAc,EAAE,CAAC;MACjBwE,IAAI,EAAErM,SADW;MAEjBsM,IAAI,EAAE,CAAC,OAAD;IAFW,CAAD,CAjCgL;IAoChM/J,aAAa,EAAE,CAAC;MAChB8J,IAAI,EAAErM,SADU;MAEhBsM,IAAI,EAAE,CAAC,eAAD;IAFU,CAAD,CApCiL;IAuChM5J,WAAW,EAAE,CAAC;MACd2J,IAAI,EAAErM,SADQ;MAEdsM,IAAI,EAAE,CAAC,aAAD;IAFQ,CAAD,CAvCmL;IA0ChM1J,GAAG,EAAE,CAAC;MACNyJ,IAAI,EAAErM,SADA;MAENsM,IAAI,EAAE,CAAC,KAAD;IAFA,CAAD,CA1C2L;IA6ChMxJ,SAAS,EAAE,CAAC;MACZuJ,IAAI,EAAErM,SADM;MAEZsM,IAAI,EAAE,CAAC,WAAD;IAFM,CAAD;EA7CqL,CAnChN;AAAA;;AAoFA,MAAMc,iBAAN,CAAwB;;AAExBA,iBAAiB,CAAC3B,IAAlB;EAAA,iBAA8G2B,iBAA9G;AAAA;;AACAA,iBAAiB,CAACC,IAAlB,kBAvH8F7N,EAuH9F;EAAA,MAA+G4N;AAA/G;AACAA,iBAAiB,CAACE,IAAlB,kBAxH8F9N,EAwH9F;EAAA,UAA4Ie,YAA5I;AAAA;;AACA;EAAA,mDAzH8Ff,EAyH9F,mBAA2F4N,iBAA3F,EAA0H,CAAC;IAC/Gf,IAAI,EAAEpM,QADyG;IAE/GqM,IAAI,EAAE,CAAC;MACCiB,OAAO,EAAE,CAAChN,YAAD,CADV;MAECiN,OAAO,EAAE,CAACxM,WAAD,CAFV;MAGCyM,YAAY,EAAE,CAACzM,WAAD;IAHf,CAAD;EAFyG,CAAD,CAA1H;AAAA;AASA;AACA;AACA;;;AAEA,SAASH,0BAAT,EAAqCG,WAArC,EAAkDoM,iBAAlD"}, "metadata": {}, "sourceType": "module"}