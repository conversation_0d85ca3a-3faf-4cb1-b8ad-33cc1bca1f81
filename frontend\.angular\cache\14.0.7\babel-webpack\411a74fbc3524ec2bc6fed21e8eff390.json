{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { lastValueFrom } from 'rxjs';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../../../services/statistics.service\";\nimport * as i3 from \"primeng/api\";\nimport * as i4 from \"primeng/chart\";\n\nconst _c0 = function () {\n  return {\n    \"width\": \"50%\"\n  };\n};\n\nexport class ChartsComponent {\n  constructor(layoutService, statisticsService, messageService) {\n    this.layoutService = layoutService;\n    this.statisticsService = statisticsService;\n    this.messageService = messageService; // Data\n\n    this.formationStats = [];\n    this.teamStats = [];\n    this.employeeStats = [];\n    this.dashboardStats = null;\n    this.monthlyFormations = []; // Loading states\n\n    this.loadingFormations = false;\n    this.loadingTeams = false;\n    this.loadingEmployees = false;\n    this.loading = true; // Filters\n\n    this.startDate = '';\n    this.endDate = '';\n    this.selectedTab = 0;\n    this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n      this.initCharts();\n    });\n  }\n\n  ngOnInit() {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      _this.initCharts();\n\n      yield _this.loadStatistics();\n    })();\n  }\n\n  loadStatistics() {\n    var _this2 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        _this2.loading = true; // Load dashboard stats\n\n        _this2.dashboardStats = yield lastValueFrom(_this2.statisticsService.getDashboardStats()); // Load monthly formations\n\n        _this2.monthlyFormations = yield lastValueFrom(_this2.statisticsService.getMonthlyFormations()); // Load formation stats\n\n        _this2.formationStats = yield lastValueFrom(_this2.statisticsService.getFormationsStats()); // Load team stats\n\n        _this2.teamStats = yield lastValueFrom(_this2.statisticsService.getTeamsStats()); // Load employee stats\n\n        _this2.employeeStats = yield lastValueFrom(_this2.statisticsService.getEmployeesStats()); // Update charts with real data\n\n        _this2.updateChartsWithRealData();\n      } catch (error) {\n        console.error('Error loading statistics:', error);\n\n        _this2.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to load statistics',\n          life: 3000\n        });\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n\n  initCharts() {\n    const documentStyle = getComputedStyle(document.documentElement);\n    const textColor = documentStyle.getPropertyValue('--text-color');\n    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n    const surfaceBorder = documentStyle.getPropertyValue('--surface-border'); // Bar Chart\n\n    this.barData = {\n      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n      datasets: [{\n        label: 'Formations',\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        data: [65, 59, 80, 81, 56, 55, 40]\n      }, {\n        label: 'Attendance Rate',\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        data: [28, 48, 40, 19, 86, 27, 90]\n      }]\n    };\n    this.barOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary,\n            font: {\n              weight: 500\n            }\n          },\n          grid: {\n            display: false,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    }; // Pie Chart\n\n    this.pieData = {\n      labels: ['Present', 'Absent', 'Pending'],\n      datasets: [{\n        data: [540, 325, 702],\n        backgroundColor: [documentStyle.getPropertyValue('--green-500'), documentStyle.getPropertyValue('--red-500'), documentStyle.getPropertyValue('--yellow-500')],\n        hoverBackgroundColor: [documentStyle.getPropertyValue('--green-400'), documentStyle.getPropertyValue('--red-400'), documentStyle.getPropertyValue('--yellow-400')]\n      }]\n    };\n    this.pieOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            usePointStyle: true,\n            color: textColor\n          }\n        }\n      }\n    }; // Line Chart\n\n    this.lineData = {\n      labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n      datasets: [{\n        label: 'Formations Count',\n        data: [65, 59, 80, 81, 56, 55, 40],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        tension: .4\n      }, {\n        label: 'Attendance Rate',\n        data: [28, 48, 40, 19, 86, 27, 90],\n        fill: false,\n        backgroundColor: documentStyle.getPropertyValue('--green-600'),\n        borderColor: documentStyle.getPropertyValue('--green-600'),\n        tension: .4\n      }]\n    };\n    this.lineOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        x: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        },\n        y: {\n          ticks: {\n            color: textColorSecondary\n          },\n          grid: {\n            color: surfaceBorder,\n            drawBorder: false\n          }\n        }\n      }\n    }; // Polar Area Chart\n\n    this.polarData = {\n      datasets: [{\n        data: [11, 16, 7, 3],\n        backgroundColor: [documentStyle.getPropertyValue('--red-500'), documentStyle.getPropertyValue('--blue-500'), documentStyle.getPropertyValue('--yellow-500'), documentStyle.getPropertyValue('--green-500')],\n        label: 'Team Performance'\n      }],\n      labels: ['Team A', 'Team B', 'Team C', 'Team D']\n    };\n    this.polarOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        r: {\n          grid: {\n            color: surfaceBorder\n          }\n        }\n      }\n    }; // Radar Chart\n\n    this.radarData = {\n      labels: ['Attendance', 'Participation', 'Completion', 'Feedback', 'Performance', 'Engagement', 'Results'],\n      datasets: [{\n        label: 'Current Period',\n        borderColor: documentStyle.getPropertyValue('--primary-500'),\n        pointBackgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        pointBorderColor: documentStyle.getPropertyValue('--primary-500'),\n        pointHoverBackgroundColor: textColor,\n        pointHoverBorderColor: documentStyle.getPropertyValue('--primary-500'),\n        data: [65, 59, 90, 81, 56, 55, 40]\n      }, {\n        label: 'Previous Period',\n        borderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointBackgroundColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        pointHoverBackgroundColor: textColor,\n        pointHoverBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n        data: [28, 48, 40, 19, 96, 27, 100]\n      }]\n    };\n    this.radarOptions = {\n      plugins: {\n        legend: {\n          labels: {\n            color: textColor\n          }\n        }\n      },\n      scales: {\n        r: {\n          grid: {\n            color: textColorSecondary\n          }\n        }\n      }\n    };\n  }\n\n  updateChartsWithRealData() {\n    if (this.monthlyFormations.length > 0) {\n      const months = this.monthlyFormations.map(f => f.month);\n      const formationCounts = this.monthlyFormations.map(f => f.count);\n      const attendanceRates = this.monthlyFormations.map(f => f.attendanceRate); // Update line chart with real data\n\n      this.lineData = Object.assign(Object.assign({}, this.lineData), {\n        labels: months,\n        datasets: [Object.assign(Object.assign({}, this.lineData.datasets[0]), {\n          data: formationCounts\n        }), Object.assign(Object.assign({}, this.lineData.datasets[1]), {\n          data: attendanceRates\n        })]\n      }); // Update bar chart with real data\n\n      this.barData = Object.assign(Object.assign({}, this.barData), {\n        labels: months,\n        datasets: [Object.assign(Object.assign({}, this.barData.datasets[0]), {\n          data: formationCounts\n        }), Object.assign(Object.assign({}, this.barData.datasets[1]), {\n          data: attendanceRates\n        })]\n      });\n    }\n\n    if (this.teamStats.length > 0) {\n      const teamNames = this.teamStats.map(t => t.nom);\n      const teamAttendance = this.teamStats.map(t => t.averageAttendance); // Update polar chart with team data\n\n      this.polarData = Object.assign(Object.assign({}, this.polarData), {\n        labels: teamNames,\n        datasets: [Object.assign(Object.assign({}, this.polarData.datasets[0]), {\n          data: teamAttendance\n        })]\n      });\n    }\n  }\n\n  exportToPDF(type) {\n    var _this3 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this3.statisticsService.exportToPDF(type));\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-statistics.pdf`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n\n        _this3.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'PDF exported successfully',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error exporting PDF:', error);\n\n        _this3.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to export PDF',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  exportToCSV(type) {\n    var _this4 = this;\n\n    return _asyncToGenerator(function* () {\n      try {\n        const blob = yield lastValueFrom(_this4.statisticsService.exportToCSV(type));\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${type}-statistics.csv`;\n        link.click();\n        window.URL.revokeObjectURL(url);\n\n        _this4.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'CSV exported successfully',\n          life: 3000\n        });\n      } catch (error) {\n        console.error('Error exporting CSV:', error);\n\n        _this4.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to export CSV',\n          life: 3000\n        });\n      }\n    })();\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nChartsComponent.ɵfac = function ChartsComponent_Factory(t) {\n  return new (t || ChartsComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StatisticsService), i0.ɵɵdirectiveInject(i3.MessageService));\n};\n\nChartsComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: ChartsComponent,\n  selectors: [[\"ng-component\"]],\n  features: [i0.ɵɵProvidersFeature([MessageService])],\n  decls: 27,\n  vars: 27,\n  consts: [[1, \"grid\", \"p-fluid\"], [1, \"col-12\", \"lg:col-6\"], [1, \"card\"], [\"type\", \"line\", 3, \"data\", \"options\"], [1, \"card\", \"flex\", \"flex-column\", \"align-items-center\"], [1, \"text-left\", \"w-full\"], [\"type\", \"pie\", 3, \"data\", \"options\"], [\"type\", \"polarArea\", 3, \"data\", \"options\"], [\"type\", \"bar\", 3, \"data\", \"options\"], [\"type\", \"doughnut\", 3, \"data\", \"options\"], [\"type\", \"radar\", 3, \"data\", \"options\"]],\n  template: function ChartsComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h5\");\n      i0.ɵɵtext(4, \"Linear Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(5, \"p-chart\", 3);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(6, \"div\", 4)(7, \"h5\", 5);\n      i0.ɵɵtext(8, \"Pie Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(9, \"p-chart\", 6);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(10, \"div\", 4)(11, \"h5\", 5);\n      i0.ɵɵtext(12, \"Polar Area Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(13, \"p-chart\", 7);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(14, \"div\", 1)(15, \"div\", 2)(16, \"h5\");\n      i0.ɵɵtext(17, \"Bar Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(18, \"p-chart\", 8);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(19, \"div\", 4)(20, \"h5\", 5);\n      i0.ɵɵtext(21, \"Doughnut Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(22, \"p-chart\", 9);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(23, \"div\", 4)(24, \"h5\", 5);\n      i0.ɵɵtext(25, \"Radar Chart\");\n      i0.ɵɵelementEnd();\n      i0.ɵɵelement(26, \"p-chart\", 10);\n      i0.ɵɵelementEnd()()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(5);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c0));\n      i0.ɵɵproperty(\"data\", ctx.lineData)(\"options\", ctx.lineOptions);\n      i0.ɵɵadvance(4);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(23, _c0));\n      i0.ɵɵproperty(\"data\", ctx.pieData)(\"options\", ctx.pieOptions);\n      i0.ɵɵadvance(4);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(24, _c0));\n      i0.ɵɵproperty(\"data\", ctx.polarData)(\"options\", ctx.polarOptions);\n      i0.ɵɵadvance(5);\n      i0.ɵɵproperty(\"data\", ctx.barData)(\"options\", ctx.barOptions);\n      i0.ɵɵadvance(4);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(25, _c0));\n      i0.ɵɵproperty(\"data\", ctx.pieData)(\"options\", ctx.pieOptions);\n      i0.ɵɵadvance(4);\n      i0.ɵɵstyleMap(i0.ɵɵpureFunction0(26, _c0));\n      i0.ɵɵproperty(\"data\", ctx.radarData)(\"options\", ctx.radarOptions);\n    }\n  },\n  dependencies: [i4.UIChart],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": ";AACA,SAAuBA,aAAvB,QAA4C,MAA5C;AAGA,SAASC,cAAT,QAA+B,aAA/B;;;;;;;;;;;;;AAMA,OAAM,MAAOC,eAAP,CAAsB;EA0CxBC,YACWC,aADX,EAEYC,iBAFZ,EAGYC,cAHZ,EAG0C;IAF/B;IACC;IACA,qCAA8B,CA3C1C;;IACA,sBAAmC,EAAnC;IACA,iBAAyB,EAAzB;IACA,qBAAiC,EAAjC;IACA,sBAAwC,IAAxC;IACA,yBAAyC,EAAzC,CAsC0C,CApC1C;;IACA,yBAA6B,KAA7B;IACA,oBAAwB,KAAxB;IACA,wBAA4B,KAA5B;IACA,eAAmB,IAAnB,CAgC0C,CA9B1C;;IACA,iBAAoB,EAApB;IACA,eAAkB,EAAlB;IACA,mBAAsB,CAAtB;IA6BI,KAAKC,YAAL,GAAoB,KAAKH,aAAL,CAAmBI,aAAnB,CAAiCC,SAAjC,CAA2C,MAAK;MAChE,KAAKC,UAAL;IACH,CAFmB,CAApB;EAGH;;EAEKC,QAAQ;IAAA;;IAAA;MACV,KAAI,CAACD,UAAL;;MACA,MAAM,KAAI,CAACE,cAAL,EAAN;IAFU;EAGb;;EAEKA,cAAc;IAAA;;IAAA;MAChB,IAAI;QACA,MAAI,CAACC,OAAL,GAAe,IAAf,CADA,CAGA;;QACA,MAAI,CAACC,cAAL,SAA4Bd,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBU,iBAAvB,EAAD,CAAzC,CAJA,CAMA;;QACA,MAAI,CAACC,iBAAL,SAA+BhB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBY,oBAAvB,EAAD,CAA5C,CAPA,CASA;;QACA,MAAI,CAACC,cAAL,SAA4BlB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBc,kBAAvB,EAAD,CAAzC,CAVA,CAYA;;QACA,MAAI,CAACC,SAAL,SAAuBpB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBgB,aAAvB,EAAD,CAApC,CAbA,CAeA;;QACA,MAAI,CAACC,aAAL,SAA2BtB,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBkB,iBAAvB,EAAD,CAAxC,CAhBA,CAkBA;;QACA,MAAI,CAACC,wBAAL;MAEH,CArBD,CAqBE,OAAOC,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,2BAAd,EAA2CA,KAA3C;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CA7BD,SA6BU;QACN,MAAI,CAAClB,OAAL,GAAe,KAAf;MACH;IAhCe;EAiCnB;;EAEDH,UAAU;IACN,MAAMsB,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAV,CAAtC;IACA,MAAMC,SAAS,GAAGJ,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAAlB;IACA,MAAMC,kBAAkB,GAAGN,aAAa,CAACK,gBAAd,CAA+B,wBAA/B,CAA3B;IACA,MAAME,aAAa,GAAGP,aAAa,CAACK,gBAAd,CAA+B,kBAA/B,CAAtB,CAJM,CAMN;;IACA,KAAKG,OAAL,GAAe;MACXC,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADG;MAEXC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,YADX;QAEIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAFrB;QAGIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAHjB;QAIIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAJV,CADM,EAON;QACIH,KAAK,EAAE,iBADX;QAEIC,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAFrB;QAGIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAHjB;QAIIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAJV,CAPM;IAFC,CAAf;IAkBA,KAAKC,UAAL,GAAkB;MACdC,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADK;MAQde,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEZ,kBADJ;YAEHgB,IAAI,EAAE;cACFC,MAAM,EAAE;YADN;UAFH,CADR;UAOCC,IAAI,EAAE;YACFC,OAAO,EAAE,KADP;YAEFC,UAAU,EAAE;UAFV;QAPP,CADC;QAaJC,CAAC,EAAE;UACCN,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP;MAbC;IARM,CAAlB,CAzBM,CA0DN;;IACA,KAAKE,OAAL,GAAe;MACXnB,MAAM,EAAE,CAAC,SAAD,EAAY,QAAZ,EAAsB,SAAtB,CADG;MAEXC,QAAQ,EAAE,CACN;QACII,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADV;QAEIF,eAAe,EAAE,CACbZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CADa,EAEbL,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CAFa,EAGbL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHa,CAFrB;QAOIwB,oBAAoB,EAAE,CAClB7B,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CADkB,EAElBL,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CAFkB,EAGlBL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHkB;MAP1B,CADM;IAFC,CAAf;IAkBA,KAAKyB,UAAL,GAAkB;MACdd,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJsB,aAAa,EAAE,IADX;YAEJb,KAAK,EAAEd;UAFH;QADJ;MADH;IADK,CAAlB,CA7EM,CAwFN;;IACA,KAAK4B,QAAL,GAAgB;MACZvB,MAAM,EAAE,CAAC,SAAD,EAAY,UAAZ,EAAwB,OAAxB,EAAiC,OAAjC,EAA0C,KAA1C,EAAiD,MAAjD,EAAyD,MAAzD,CADI;MAEZC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,kBADX;QAEIG,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,CAFV;QAGImB,IAAI,EAAE,KAHV;QAIIrB,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CALjB;QAMI6B,OAAO,EAAE;MANb,CADM,EASN;QACIvB,KAAK,EAAE,iBADX;QAEIG,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB,CAFV;QAGImB,IAAI,EAAE,KAHV;QAIIrB,eAAe,EAAEZ,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJrB;QAKIQ,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CALjB;QAMI6B,OAAO,EAAE;MANb,CATM;IAFE,CAAhB;IAsBA,KAAKC,WAAL,GAAmB;MACfnB,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADM;MAQfe,MAAM,EAAE;QACJC,CAAC,EAAE;UACCC,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP,CADC;QAUJC,CAAC,EAAE;UACCN,KAAK,EAAE;YACHH,KAAK,EAAEZ;UADJ,CADR;UAICkB,IAAI,EAAE;YACFN,KAAK,EAAEX,aADL;YAEFmB,UAAU,EAAE;UAFV;QAJP;MAVC;IARO,CAAnB,CA/GM,CA6IN;;IACA,KAAKU,SAAL,GAAiB;MACb1B,QAAQ,EAAE,CAAC;QACPI,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,CAAT,EAAY,CAAZ,CADC;QAEPF,eAAe,EAAE,CACbZ,aAAa,CAACK,gBAAd,CAA+B,WAA/B,CADa,EAEbL,aAAa,CAACK,gBAAd,CAA+B,YAA/B,CAFa,EAGbL,aAAa,CAACK,gBAAd,CAA+B,cAA/B,CAHa,EAIbL,aAAa,CAACK,gBAAd,CAA+B,aAA/B,CAJa,CAFV;QAQPM,KAAK,EAAE;MARA,CAAD,CADG;MAWbF,MAAM,EAAE,CAAC,QAAD,EAAW,QAAX,EAAqB,QAArB,EAA+B,QAA/B;IAXK,CAAjB;IAcA,KAAK4B,YAAL,GAAoB;MAChBrB,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADO;MAQhBe,MAAM,EAAE;QACJmB,CAAC,EAAE;UACCd,IAAI,EAAE;YACFN,KAAK,EAAEX;UADL;QADP;MADC;IARQ,CAApB,CA5JM,CA6KN;;IACA,KAAKgC,SAAL,GAAiB;MACb9B,MAAM,EAAE,CAAC,YAAD,EAAe,eAAf,EAAgC,YAAhC,EAA8C,UAA9C,EAA0D,aAA1D,EAAyE,YAAzE,EAAuF,SAAvF,CADK;MAEbC,QAAQ,EAAE,CACN;QACIC,KAAK,EAAE,gBADX;QAEIE,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAFjB;QAGImC,oBAAoB,EAAExC,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAH1B;QAIIoC,gBAAgB,EAAEzC,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAJtB;QAKIqC,yBAAyB,EAAEtC,SAL/B;QAMIuC,qBAAqB,EAAE3C,aAAa,CAACK,gBAAd,CAA+B,eAA/B,CAN3B;QAOIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,EAAzB;MAPV,CADM,EAUN;QACIH,KAAK,EAAE,iBADX;QAEIE,WAAW,EAAEb,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAFjB;QAGImC,oBAAoB,EAAExC,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAH1B;QAIIoC,gBAAgB,EAAEzC,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAJtB;QAKIqC,yBAAyB,EAAEtC,SAL/B;QAMIuC,qBAAqB,EAAE3C,aAAa,CAACK,gBAAd,CAA+B,gBAA/B,CAN3B;QAOIS,IAAI,EAAE,CAAC,EAAD,EAAK,EAAL,EAAS,EAAT,EAAa,EAAb,EAAiB,EAAjB,EAAqB,EAArB,EAAyB,GAAzB;MAPV,CAVM;IAFG,CAAjB;IAwBA,KAAK8B,YAAL,GAAoB;MAChB5B,OAAO,EAAE;QACLC,MAAM,EAAE;UACJR,MAAM,EAAE;YACJS,KAAK,EAAEd;UADH;QADJ;MADH,CADO;MAQhBe,MAAM,EAAE;QACJmB,CAAC,EAAE;UACCd,IAAI,EAAE;YACFN,KAAK,EAAEZ;UADL;QADP;MADC;IARQ,CAApB;EAgBH;;EAEDd,wBAAwB;IACpB,IAAI,KAAKR,iBAAL,CAAuB6D,MAAvB,GAAgC,CAApC,EAAuC;MACnC,MAAMC,MAAM,GAAG,KAAK9D,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACC,KAAlC,CAAf;MACA,MAAMC,eAAe,GAAG,KAAKlE,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACG,KAAlC,CAAxB;MACA,MAAMC,eAAe,GAAG,KAAKpE,iBAAL,CAAuB+D,GAAvB,CAA2BC,CAAC,IAAIA,CAAC,CAACK,cAAlC,CAAxB,CAHmC,CAKnC;;MACA,KAAKrB,QAAL,GAAasB,gCACN,KAAKtB,QADC,GACO;QAChBvB,MAAM,EAAEqC,MADQ;QAEhBpC,QAAQ,EAAE,iCAEC,KAAKsB,QAAL,CAActB,QAAd,CAAuB,CAAvB,IAAyB;UAC5BI,IAAI,EAAEoC;QADsB,EAF1B,kCAMC,KAAKlB,QAAL,CAActB,QAAd,CAAuB,CAAvB,IAAyB;UAC5BI,IAAI,EAAEsC;QADsB,EAN1B;MAFM,CADP,CAAb,CANmC,CAqBnC;;MACA,KAAK5C,OAAL,GAAY8C,gCACL,KAAK9C,OADA,GACO;QACfC,MAAM,EAAEqC,MADO;QAEfpC,QAAQ,EAAE,iCAEC,KAAKF,OAAL,CAAaE,QAAb,CAAsB,CAAtB,IAAwB;UAC3BI,IAAI,EAAEoC;QADqB,EAFzB,kCAMC,KAAK1C,OAAL,CAAaE,QAAb,CAAsB,CAAtB,IAAwB;UAC3BI,IAAI,EAAEsC;QADqB,EANzB;MAFK,CADP,CAAZ;IAcH;;IAED,IAAI,KAAKhE,SAAL,CAAeyD,MAAf,GAAwB,CAA5B,EAA+B;MAC3B,MAAMU,SAAS,GAAG,KAAKnE,SAAL,CAAe2D,GAAf,CAAmBS,CAAC,IAAIA,CAAC,CAACC,GAA1B,CAAlB;MACA,MAAMC,cAAc,GAAG,KAAKtE,SAAL,CAAe2D,GAAf,CAAmBS,CAAC,IAAIA,CAAC,CAACG,iBAA1B,CAAvB,CAF2B,CAI3B;;MACA,KAAKvB,SAAL,GAAckB,gCACP,KAAKlB,SADE,GACO;QACjB3B,MAAM,EAAE8C,SADS;QAEjB7C,QAAQ,EAAE,iCACH,KAAK0B,SAAL,CAAe1B,QAAf,CAAwB,CAAxB,CADG,GACuB;UAC7BI,IAAI,EAAE4C;QADuB,CADvB;MAFO,CADP,CAAd;IAQH;EACJ;;EAEKE,WAAW,CAACC,IAAD,EAAa;IAAA;;IAAA;MAC1B,IAAI;QACA,MAAMC,IAAI,SAAS9F,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBuF,WAAvB,CAAmCC,IAAnC,CAAD,CAAhC;QACA,MAAME,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BJ,IAA3B,CAAZ;QACA,MAAMK,IAAI,GAAGjE,QAAQ,CAACkE,aAAT,CAAuB,GAAvB,CAAb;QACAD,IAAI,CAACE,IAAL,GAAYN,GAAZ;QACAI,IAAI,CAACG,QAAL,GAAgB,GAAGT,IAAI,iBAAvB;QACAM,IAAI,CAACI,KAAL;QACAP,MAAM,CAACC,GAAP,CAAWO,eAAX,CAA2BT,GAA3B;;QAEA,MAAI,CAACzF,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,SAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAfD,CAeE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,sBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAxByB;EAyB7B;;EAEK0E,WAAW,CAACZ,IAAD,EAAa;IAAA;;IAAA;MAC1B,IAAI;QACA,MAAMC,IAAI,SAAS9F,aAAa,CAAC,MAAI,CAACK,iBAAL,CAAuBoG,WAAvB,CAAmCZ,IAAnC,CAAD,CAAhC;QACA,MAAME,GAAG,GAAGC,MAAM,CAACC,GAAP,CAAWC,eAAX,CAA2BJ,IAA3B,CAAZ;QACA,MAAMK,IAAI,GAAGjE,QAAQ,CAACkE,aAAT,CAAuB,GAAvB,CAAb;QACAD,IAAI,CAACE,IAAL,GAAYN,GAAZ;QACAI,IAAI,CAACG,QAAL,GAAgB,GAAGT,IAAI,iBAAvB;QACAM,IAAI,CAACI,KAAL;QACAP,MAAM,CAACC,GAAP,CAAWO,eAAX,CAA2BT,GAA3B;;QAEA,MAAI,CAACzF,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,SADU;UAEpBC,OAAO,EAAE,SAFW;UAGpBC,MAAM,EAAE,2BAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH,CAfD,CAeE,OAAON,KAAP,EAAc;QACZC,OAAO,CAACD,KAAR,CAAc,sBAAd,EAAsCA,KAAtC;;QACA,MAAI,CAACnB,cAAL,CAAoBqB,GAApB,CAAwB;UACpBC,QAAQ,EAAE,OADU;UAEpBC,OAAO,EAAE,OAFW;UAGpBC,MAAM,EAAE,sBAHY;UAIpBC,IAAI,EAAE;QAJc,CAAxB;MAMH;IAxByB;EAyB7B;;EAED2E,WAAW;IACP,IAAI,KAAKnG,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBoG,WAAlB;IACH;EACJ;;AArauB;;;mBAAfzG,iBAAe0G;AAAA;;;QAAf1G;EAAe2G;EAAAC,iCAFb,CAAC7G,cAAD,CAEa;EAFG8G;EAAAC;EAAAC;EAAAC;IAAA;MCR/BN,+BAA0B,CAA1B,EAA0B,KAA1B,EAA0B,CAA1B,EAA0B,CAA1B,EAA0B,KAA1B,EAA0B,CAA1B,EAA0B,CAA1B,EAA0B,IAA1B;MAGgBA;MAAYA;MAChBA;MACJA;MAEAA,+BAAsD,CAAtD,EAAsD,IAAtD,EAAsD,CAAtD;MACiCA;MAASA;MACtCA;MACJA;MAEAA,gCAAsD,EAAtD,EAAsD,IAAtD,EAAsD,CAAtD;MACiCA;MAAgBA;MAC7CA;MACJA;MAEJA,gCAA6B,EAA7B,EAA6B,KAA7B,EAA6B,CAA7B,EAA6B,EAA7B,EAA6B,IAA7B;MAEYA;MAASA;MACbA;MACJA;MAEAA,gCAAsD,EAAtD,EAAsD,IAAtD,EAAsD,CAAtD;MACiCA;MAAcA;MAC3CA;MACJA;MAEAA,gCAAsD,EAAtD,EAAsD,IAAtD,EAAsD,CAAtD;MACiCA;MAAWA;MACxCA;MACJA;;;;MA3BmEA;MAAAA;MAA1CA,oCAAiB,SAAjB,EAAiBO,eAAjB;MAKuCP;MAAAA;MAAxCA,mCAAgB,SAAhB,EAAgBO,cAAhB;MAKkDP;MAAAA;MAA5CA,qCAAkB,SAAlB,EAAkBO,gBAAlB;MAMNP;MAAAA,mCAAgB,SAAhB,EAAgBO,cAAhB;MAK6CP;MAAAA;MAAxCA,mCAAgB,SAAhB,EAAgBO,cAAhB;MAKyCP;MAAAA;MAA5CA,qCAAkB,SAAlB,EAAkBO,gBAAlB", "names": ["lastValueFrom", "MessageService", "ChartsComponent", "constructor", "layoutService", "statisticsService", "messageService", "subscription", "configUpdate$", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "loadStatistics", "loading", "dashboardStats", "getDashboardStats", "monthlyFormations", "getMonthlyFormations", "formationStats", "getFormationsStats", "teamStats", "getTeamsStats", "employeeStats", "getEmployeesStats", "updateChartsWithRealData", "error", "console", "add", "severity", "summary", "detail", "life", "documentStyle", "getComputedStyle", "document", "documentElement", "textColor", "getPropertyValue", "textColorSecondary", "surfaceBorder", "barData", "labels", "datasets", "label", "backgroundColor", "borderColor", "data", "barOptions", "plugins", "legend", "color", "scales", "x", "ticks", "font", "weight", "grid", "display", "drawBorder", "y", "pieData", "hoverBackgroundColor", "pieOptions", "usePointStyle", "lineData", "fill", "tension", "lineOptions", "polarData", "polarOptions", "r", "radarData", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "radarOptions", "length", "months", "map", "f", "month", "formationCounts", "count", "attendanceRates", "attendanceRate", "Object", "teamNames", "t", "nom", "teamAttendance", "averageAttendance", "exportToPDF", "type", "blob", "url", "window", "URL", "createObjectURL", "link", "createElement", "href", "download", "click", "revokeObjectURL", "exportToCSV", "ngOnDestroy", "unsubscribe", "i0", "selectors", "features", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\charts\\charts.component.ts", "C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\Admin\\charts\\charts.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\nimport { Subscription, lastValueFrom } from 'rxjs';\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\nimport { StatisticsService, FormationStats, TeamStats, EmployeeStats, DashboardStats, MonthlyFormations } from '../../../../services/statistics.service';\nimport { MessageService } from 'primeng/api';\n\n@Component({\n    templateUrl: './charts.component.html',\n    providers: [MessageService]\n})\nexport class ChartsComponent implements OnInit, OnDestroy {\n\n    // Data\n    formationStats: FormationStats[] = [];\n    teamStats: TeamStats[] = [];\n    employeeStats: EmployeeStats[] = [];\n    dashboardStats: DashboardStats | null = null;\n    monthlyFormations: MonthlyFormations[] = [];\n\n    // Loading states\n    loadingFormations: boolean = false;\n    loadingTeams: boolean = false;\n    loadingEmployees: boolean = false;\n    loading: boolean = true;\n\n    // Filters\n    startDate: string = '';\n    endDate: string = '';\n    selectedTab: number = 0;\n\n    // Chart data properties - AJOUT DES PROPRIÉTÉS MANQUANTES\n    barData: any;\n    barOptions: any;\n    pieData: any;\n    pieOptions: any;\n    lineData: any;\n    lineOptions: any;\n    polarData: any;\n    polarOptions: any;\n    radarData: any;\n    radarOptions: any;\n    \n    // Additional chart data\n    attendanceChartData: any;\n    attendanceChartOptions: any;\n    teamChartData: any;\n    teamChartOptions: any;\n    monthlyChartData: any;\n    monthlyChartOptions: any;\n\n    subscription!: Subscription;\n\n    constructor(\n        public layoutService: LayoutService,\n        private statisticsService: StatisticsService,\n        private messageService: MessageService\n    ) {\n        this.subscription = this.layoutService.configUpdate$.subscribe(() => {\n            this.initCharts();\n        });\n    }\n\n    async ngOnInit() {\n        this.initCharts();\n        await this.loadStatistics();\n    }\n\n    async loadStatistics() {\n        try {\n            this.loading = true;\n            \n            // Load dashboard stats\n            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());\n            \n            // Load monthly formations\n            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());\n            \n            // Load formation stats\n            this.formationStats = await lastValueFrom(this.statisticsService.getFormationsStats());\n            \n            // Load team stats\n            this.teamStats = await lastValueFrom(this.statisticsService.getTeamsStats());\n            \n            // Load employee stats\n            this.employeeStats = await lastValueFrom(this.statisticsService.getEmployeesStats());\n            \n            // Update charts with real data\n            this.updateChartsWithRealData();\n            \n        } catch (error) {\n            console.error('Error loading statistics:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to load statistics',\n                life: 3000\n            });\n        } finally {\n            this.loading = false;\n        }\n    }\n\n    initCharts() {\n        const documentStyle = getComputedStyle(document.documentElement);\n        const textColor = documentStyle.getPropertyValue('--text-color');\n        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n\n        // Bar Chart\n        this.barData = {\n            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n            datasets: [\n                {\n                    label: 'Formations',\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    data: [65, 59, 80, 81, 56, 55, 40]\n                },\n                {\n                    label: 'Attendance Rate',\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    data: [28, 48, 40, 19, 86, 27, 90]\n                }\n            ]\n        };\n\n        this.barOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary,\n                        font: {\n                            weight: 500\n                        }\n                    },\n                    grid: {\n                        display: false,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n            }\n        };\n\n        // Pie Chart\n        this.pieData = {\n            labels: ['Present', 'Absent', 'Pending'],\n            datasets: [\n                {\n                    data: [540, 325, 702],\n                    backgroundColor: [\n                        documentStyle.getPropertyValue('--green-500'),\n                        documentStyle.getPropertyValue('--red-500'),\n                        documentStyle.getPropertyValue('--yellow-500')\n                    ],\n                    hoverBackgroundColor: [\n                        documentStyle.getPropertyValue('--green-400'),\n                        documentStyle.getPropertyValue('--red-400'),\n                        documentStyle.getPropertyValue('--yellow-400')\n                    ]\n                }]\n        };\n\n        this.pieOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        usePointStyle: true,\n                        color: textColor\n                    }\n                }\n            }\n        };\n\n        // Line Chart\n        this.lineData = {\n            labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n            datasets: [\n                {\n                    label: 'Formations Count',\n                    data: [65, 59, 80, 81, 56, 55, 40],\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    tension: .4\n                },\n                {\n                    label: 'Attendance Rate',\n                    data: [28, 48, 40, 19, 86, 27, 90],\n                    fill: false,\n                    backgroundColor: documentStyle.getPropertyValue('--green-600'),\n                    borderColor: documentStyle.getPropertyValue('--green-600'),\n                    tension: .4\n                }\n            ]\n        };\n\n        this.lineOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                x: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                },\n                y: {\n                    ticks: {\n                        color: textColorSecondary\n                    },\n                    grid: {\n                        color: surfaceBorder,\n                        drawBorder: false\n                    }\n                }\n            }\n        };\n\n        // Polar Area Chart\n        this.polarData = {\n            datasets: [{\n                data: [11, 16, 7, 3],\n                backgroundColor: [\n                    documentStyle.getPropertyValue('--red-500'),\n                    documentStyle.getPropertyValue('--blue-500'),\n                    documentStyle.getPropertyValue('--yellow-500'),\n                    documentStyle.getPropertyValue('--green-500')\n                ],\n                label: 'Team Performance'\n            }],\n            labels: ['Team A', 'Team B', 'Team C', 'Team D']\n        };\n\n        this.polarOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                r: {\n                    grid: {\n                        color: surfaceBorder\n                    }\n                }\n            }\n        };\n\n        // Radar Chart\n        this.radarData = {\n            labels: ['Attendance', 'Participation', 'Completion', 'Feedback', 'Performance', 'Engagement', 'Results'],\n            datasets: [\n                {\n                    label: 'Current Period',\n                    borderColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointBackgroundColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointBorderColor: documentStyle.getPropertyValue('--primary-500'),\n                    pointHoverBackgroundColor: textColor,\n                    pointHoverBorderColor: documentStyle.getPropertyValue('--primary-500'),\n                    data: [65, 59, 90, 81, 56, 55, 40]\n                },\n                {\n                    label: 'Previous Period',\n                    borderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointBackgroundColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    pointHoverBackgroundColor: textColor,\n                    pointHoverBorderColor: documentStyle.getPropertyValue('--bluegray-500'),\n                    data: [28, 48, 40, 19, 96, 27, 100]\n                }\n            ]\n        };\n\n        this.radarOptions = {\n            plugins: {\n                legend: {\n                    labels: {\n                        color: textColor\n                    }\n                }\n            },\n            scales: {\n                r: {\n                    grid: {\n                        color: textColorSecondary\n                    }\n                }\n            }\n        };\n    }\n\n    updateChartsWithRealData() {\n        if (this.monthlyFormations.length > 0) {\n            const months = this.monthlyFormations.map(f => f.month);\n            const formationCounts = this.monthlyFormations.map(f => f.count);\n            const attendanceRates = this.monthlyFormations.map(f => f.attendanceRate);\n\n            // Update line chart with real data\n            this.lineData = {\n                ...this.lineData,\n                labels: months,\n                datasets: [\n                    {\n                        ...this.lineData.datasets[0],\n                        data: formationCounts\n                    },\n                    {\n                        ...this.lineData.datasets[1],\n                        data: attendanceRates\n                    }\n                ]\n            };\n\n            // Update bar chart with real data\n            this.barData = {\n                ...this.barData,\n                labels: months,\n                datasets: [\n                    {\n                        ...this.barData.datasets[0],\n                        data: formationCounts\n                    },\n                    {\n                        ...this.barData.datasets[1],\n                        data: attendanceRates\n                    }\n                ]\n            };\n        }\n\n        if (this.teamStats.length > 0) {\n            const teamNames = this.teamStats.map(t => t.nom);\n            const teamAttendance = this.teamStats.map(t => t.averageAttendance);\n\n            // Update polar chart with team data\n            this.polarData = {\n                ...this.polarData,\n                labels: teamNames,\n                datasets: [{\n                    ...this.polarData.datasets[0],\n                    data: teamAttendance\n                }]\n            };\n        }\n    }\n\n    async exportToPDF(type: string) {\n        try {\n            const blob = await lastValueFrom(this.statisticsService.exportToPDF(type));\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${type}-statistics.pdf`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n            \n            this.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'PDF exported successfully',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error exporting PDF:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to export PDF',\n                life: 3000\n            });\n        }\n    }\n\n    async exportToCSV(type: string) {\n        try {\n            const blob = await lastValueFrom(this.statisticsService.exportToCSV(type));\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `${type}-statistics.csv`;\n            link.click();\n            window.URL.revokeObjectURL(url);\n            \n            this.messageService.add({\n                severity: 'success',\n                summary: 'Success',\n                detail: 'CSV exported successfully',\n                life: 3000\n            });\n        } catch (error) {\n            console.error('Error exporting CSV:', error);\n            this.messageService.add({\n                severity: 'error',\n                summary: 'Error',\n                detail: 'Failed to export CSV',\n                life: 3000\n            });\n        }\n    }\n\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\n\n", "<div class=\"grid p-fluid\">\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card\">\n            <h5>Linear Chart</h5>\n            <p-chart type=\"line\" [data]=\"lineData\" [options]=\"lineOptions\" [style]=\"{'width': '50%'}\"></p-chart>\n        </div>\n\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Pie Chart</h5>\n            <p-chart type=\"pie\" [data]=\"pieData\" [options]=\"pieOptions\" [style]=\"{'width': '50%'}\"></p-chart>\n        </div>\n\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Polar Area Chart</h5>\n            <p-chart type=\"polarArea\" [data]=\"polarData\" [options]=\"polarOptions\" [style]=\"{'width': '50%'}\"></p-chart>\n        </div>\n    </div>\n    <div class=\"col-12 lg:col-6\">\n        <div class=\"card\">\n            <h5>Bar Chart</h5>\n            <p-chart type=\"bar\" [data]=\"barData\" [options]=\"barOptions\"></p-chart>\n        </div>\n\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Doughnut Chart</h5>\n            <p-chart type=\"doughnut\" [data]=\"pieData\" [options]=\"pieOptions\" [style]=\"{'width': '50%'}\"></p-chart>\n        </div>\n\n        <div class=\"card flex flex-column align-items-center\">\n            <h5 class=\"text-left w-full\">Radar Chart</h5>\n            <p-chart type=\"radar\" [data]=\"radarData\" [options]=\"radarOptions\" [style]=\"{'width': '50%'}\"></p-chart>\n        </div>\n    </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module"}