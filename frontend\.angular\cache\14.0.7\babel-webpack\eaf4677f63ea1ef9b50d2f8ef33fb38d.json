{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n  return operate((source, subscriber) => {\n    let prev;\n    let hasPrev = false;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const p = prev;\n      prev = value;\n      hasPrev && subscriber.next([p, value]);\n      hasPrev = true;\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "pairwise", "source", "subscriber", "prev", "has<PERSON>rev", "subscribe", "value", "p", "next"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/pairwise.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function pairwise() {\n    return operate((source, subscriber) => {\n        let prev;\n        let hasPrev = false;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const p = prev;\n            prev = value;\n            hasPrev && subscriber.next([p, value]);\n            hasPrev = true;\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,QAAT,GAAoB;EACvB,OAAOF,OAAO,CAAC,CAACG,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,IAAJ;IACA,IAAIC,OAAO,GAAG,KAAd;IACAH,MAAM,CAACI,SAAP,CAAiBN,wBAAwB,CAACG,UAAD,EAAcI,KAAD,IAAW;MAC7D,MAAMC,CAAC,GAAGJ,IAAV;MACAA,IAAI,GAAGG,KAAP;MACAF,OAAO,IAAIF,UAAU,CAACM,IAAX,CAAgB,CAACD,CAAD,EAAID,KAAJ,CAAhB,CAAX;MACAF,OAAO,GAAG,IAAV;IACH,CALwC,CAAzC;EAMH,CATa,CAAd;AAUH"}, "metadata": {}, "sourceType": "module"}