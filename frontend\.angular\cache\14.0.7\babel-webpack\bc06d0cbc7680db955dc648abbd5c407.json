{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/GestionFormation/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../../code/code.component\";\nimport * as i3 from \"primeng/tooltip\";\n\nfunction BlockViewer_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1, \"Free\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BlockViewer_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction BlockViewer_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.containerClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.previewStyle);\n  }\n}\n\nfunction BlockViewer_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-code\", 16);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.code, \"\\n                \");\n  }\n}\n\nconst _c0 = function (a0) {\n  return {\n    \"block-action-active\": a0\n  };\n};\n\nconst _c1 = [\"*\"];\nvar BlockView;\n\n(function (BlockView) {\n  BlockView[BlockView[\"PREVIEW\"] = 0] = \"PREVIEW\";\n  BlockView[BlockView[\"CODE\"] = 1] = \"CODE\";\n})(BlockView || (BlockView = {}));\n\nexport class BlockViewer {\n  constructor() {\n    this.free = true;\n    this.new = false;\n    this.BlockView = BlockView;\n    this.blockView = BlockView.PREVIEW;\n  }\n\n  activateView(event, blockView) {\n    this.blockView = blockView;\n    event.preventDefault();\n  }\n\n  copyCode(event) {\n    var _this = this;\n\n    return _asyncToGenerator(function* () {\n      yield navigator.clipboard.writeText(_this.code);\n      event.preventDefault();\n    })();\n  }\n\n}\n\nBlockViewer.ɵfac = function BlockViewer_Factory(t) {\n  return new (t || BlockViewer)();\n};\n\nBlockViewer.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: BlockViewer,\n  selectors: [[\"block-viewer\"]],\n  inputs: {\n    header: \"header\",\n    code: \"code\",\n    containerClass: \"containerClass\",\n    previewStyle: \"previewStyle\",\n    free: \"free\",\n    new: \"new\"\n  },\n  ngContentSelectors: _c1,\n  decls: 19,\n  vars: 13,\n  consts: [[1, \"block-section\"], [1, \"block-header\"], [1, \"block-title\"], [\"class\", \"badge-free\", 4, \"ngIf\"], [\"class\", \"badge-new\", 4, \"ngIf\"], [1, \"block-actions\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\"], [3, \"ngClass\", \"click\"], [\"pTooltip\", \"Copied to clipboard\", \"tooltipEvent\", \"focus\", \"tooltipPosition\", \"bottom\", 1, \"block-action-copy\", 3, \"click\"], [1, \"pi\", \"pi-copy\", \"m-0\"], [1, \"block-content\"], [3, \"class\", \"ngStyle\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"badge-free\"], [1, \"badge-new\"], [3, \"ngStyle\"], [\"lang\", \"markup\"]],\n  template: function BlockViewer_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2)(3, \"span\");\n      i0.ɵɵtext(4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(5, BlockViewer_span_5_Template, 2, 0, \"span\", 3);\n      i0.ɵɵtemplate(6, BlockViewer_span_6_Template, 2, 0, \"span\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵelementStart(7, \"div\", 5)(8, \"a\", 6);\n      i0.ɵɵlistener(\"click\", function BlockViewer_Template_a_click_8_listener($event) {\n        return ctx.activateView($event, ctx.BlockView.PREVIEW);\n      });\n      i0.ɵɵelementStart(9, \"span\");\n      i0.ɵɵtext(10, \"Preview\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(11, \"a\", 7);\n      i0.ɵɵlistener(\"click\", function BlockViewer_Template_a_click_11_listener($event) {\n        return ctx.activateView($event, ctx.BlockView.CODE);\n      });\n      i0.ɵɵelementStart(12, \"span\");\n      i0.ɵɵtext(13, \"Code\");\n      i0.ɵɵelementEnd()();\n      i0.ɵɵelementStart(14, \"a\", 8);\n      i0.ɵɵlistener(\"click\", function BlockViewer_Template_a_click_14_listener($event) {\n        return ctx.copyCode($event);\n      });\n      i0.ɵɵelement(15, \"i\", 9);\n      i0.ɵɵelementEnd()()();\n      i0.ɵɵelementStart(16, \"div\", 10);\n      i0.ɵɵtemplate(17, BlockViewer_div_17_Template, 2, 3, \"div\", 11);\n      i0.ɵɵtemplate(18, BlockViewer_div_18_Template, 3, 1, \"div\", 12);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(4);\n      i0.ɵɵtextInterpolate(ctx.header);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.free);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.new);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.blockView == ctx.BlockView.PREVIEW));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.blockView == ctx.BlockView.CODE));\n      i0.ɵɵattribute(\"tabindex\", \"0\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵattribute(\"tabindex\", \"0\");\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngIf\", ctx.blockView == ctx.BlockView.PREVIEW);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.blockView == ctx.BlockView.CODE);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgIf, i1.NgStyle, i2.AppCodeComponent, i3.Tooltip],\n  styles: [\".block-section[_ngcontent-%COMP%] {\\n  margin-bottom: 4rem;\\n  overflow: hidden;\\n}\\n\\n.block-header[_ngcontent-%COMP%] {\\n  padding: 1rem 2rem;\\n  background-color: var(--surface-section);\\n  border-top-left-radius: 12px;\\n  border-top-right-radius: 12px;\\n  border: 1px solid var(--surface-d);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-title[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-title[_ngcontent-%COMP%]   .badge-free[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  padding: 0.25rem 0.5rem;\\n  background-color: var(--orange-500);\\n  color: white;\\n  margin-left: 1rem;\\n  font-weight: 700;\\n  font-size: 0.875rem;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  margin-left: 1rem;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 0.75rem;\\n  padding: 0.5rem 1rem;\\n  border-radius: 4px;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  transition: background-color 0.2s;\\n  cursor: pointer;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:not(.block-action-disabled):hover {\\n  background-color: var(--surface-c);\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-active[_ngcontent-%COMP%] {\\n  border-color: var(--primary-color);\\n  color: var(--primary-color);\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-copy[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-size: 1.25rem;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: auto !important;\\n}\\n\\n.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.block-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n  border: 1px solid var(--surface-d);\\n  border-top: 0 none;\\n  border-bottom-left-radius: 12px;\\n  border-bottom-right-radius: 12px;\\n  overflow: hidden;\\n}\\n\\n[_nghost-%COMP%]     pre[class*=language-] {\\n  margin: 0 !important;\\n  border-radius: 0 !important;\\n}\\n\\n[_nghost-%COMP%]     pre[class*=language-]:before, [_nghost-%COMP%]     pre[class*=language-]:after {\\n  display: none !important;\\n}\\n\\n[_nghost-%COMP%]     pre[class*=language-] code {\\n  border-left: 0 none !important;\\n  box-shadow: none !important;\\n  background: var(--surface-e) !important;\\n  margin: 0;\\n  color: var(--text-color);\\n  font-size: 14px;\\n  padding: 0 2rem !important;\\n}\\n\\n@media screen and (max-width: 575px) {\\n  .block-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: start;\\n  }\\n  .block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%] {\\n    margin-top: 1rem;\\n    margin-left: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\"]\n});", "map": {"version": 3, "mappings": ";;;;;;;;IAcgBA;IAAsCA;IAAIA;;;;;;IAC1CA;IAAoCA;IAAGA;;;;;;IAY3CA;IACIA;IACJA;;;;;IAFKA;IAAyBA;;;;;;IAG9BA,4BAAyC,CAAzC,EAAyC,UAAzC,EAAyC,EAAzC;IACkDA;IAC9CA;;;;;IAD8CA;IAAAA;;;;;;;;;;;AA7B9D,IAAKC,SAAL;;AAAA,WAAKA,SAAL,EAAc;EACVA;EACAA;AACH,CAHD,EAAKA,SAAS,KAATA,SAAS,MAAd;;AAqCA,OAAM,MAAOC,WAAP,CAAkB;EAhCxBC;IA0Ca,YAAgB,IAAhB;IAEA,WAAe,KAAf;IAET,iBAAYF,SAAZ;IAEA,iBAAuBA,SAAS,CAACG,OAAjC;EAcH;;EAZGC,YAAY,CAACC,KAAD,EAAeC,SAAf,EAAmC;IAE3C,KAAKA,SAAL,GAAiBA,SAAjB;IACAD,KAAK,CAACE,cAAN;EACH;;EAGKC,QAAQ,CAACH,KAAD,EAAa;IAAA;;IAAA;MACvB,MAAMI,SAAS,CAACC,SAAV,CAAoBC,SAApB,CAA8B,KAAI,CAACC,IAAnC,CAAN;MACAP,KAAK,CAACE,cAAN;IAFuB;EAG1B;;AA5BmB;;;mBAAXN;AAAW;;;QAAXA;EAAWY;EAAAC;IAAAC;IAAAH;IAAAI;IAAAC;IAAAC;IAAAC;EAAA;EAAAC;EAAAC;EAAAC;EAAAC;EAAAC;IAAA;;MA7BpBzB,+BAA2B,CAA3B,EAA2B,KAA3B,EAA2B,CAA3B,EAA2B,CAA3B,EAA2B,MAA3B,EAA2B,CAA3B,EAA2B,CAA3B,EAA2B,MAA3B;MAGkBA;MAAUA;MAChBA;MACAA;MACJA;MACAA,+BAA2B,CAA3B,EAA2B,GAA3B,EAA2B,CAA3B;MACwFA;QAAA,OAAS0B,+CAAT;MAAgD,CAAhD;MAAkD1B;MAAMA;MAAOA;MACnJA;MAA0FA;QAAA,OAAS0B,4CAAT;MAA6C,CAA7C;MACtF1B;MAAMA;MAAIA;MAEdA;MAAmDA;QAAA,OAAS0B,oBAAT;MAAyB,CAAzB;MAC8B1B;MAA8BA;MAGvHA;MACIA;MAGAA;MAIJA;;;;MArBcA;MAAAA;MACoBA;MAAAA;MACDA;MAAAA;MAGTA;MAAAA;MACSA;MAAAA;MAAtBA;MAGAA;MAAAA;MAKiDA;MAAAA;MAGlDA;MAAAA", "names": ["i0", "BlockView", "Block<PERSON>iewer", "constructor", "PREVIEW", "activateView", "event", "blockView", "preventDefault", "copyCode", "navigator", "clipboard", "writeText", "code", "selectors", "inputs", "header", "containerClass", "previewStyle", "free", "new", "ngContentSelectors", "decls", "vars", "consts", "template", "ctx"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\GestionFormation\\frontend\\src\\app\\demo\\components\\primeblocks\\blockviewer\\blockviewer.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\n\nenum BlockView {\n    PREVIEW,\n    CODE\n}\n\n@Component({\n    selector: 'block-viewer',\n    template: `\n    <div class=\"block-section\">\n        <div class=\"block-header\">\n            <span class=\"block-title\">\n                <span>{{header}}</span>\n                <span class=\"badge-free\" *ngIf=\"free\">Free</span>\n                <span class=\"badge-new\" *ngIf=\"new\">New</span>\n            </span>\n            <div class=\"block-actions\">\n                <a tabindex=\"0\" [ngClass]=\"{'block-action-active': blockView == BlockView.PREVIEW}\" (click)=\"activateView($event, BlockView.PREVIEW)\"><span>Preview</span></a>\n                <a [attr.tabindex]=\"'0'\" [ngClass]=\"{'block-action-active': blockView == BlockView.CODE}\" (click)=\"activateView($event, BlockView.CODE)\">\n                    <span>Code</span>\n                </a>\n                <a [attr.tabindex]=\"'0'\" class=\"block-action-copy\" (click)=\"copyCode($event)\" \n                    pTooltip=\"Copied to clipboard\" tooltipEvent=\"focus\" tooltipPosition=\"bottom\"><i class=\"pi pi-copy m-0\"></i></a>\n            </div>\n        </div>\n        <div class=\"block-content\">\n            <div [class]=\"containerClass\" [ngStyle]=\"previewStyle\" *ngIf=\"blockView == BlockView.PREVIEW\">\n                <ng-content></ng-content>\n            </div>\n            <div *ngIf=\"blockView == BlockView.CODE\">\n                <app-code lang=\"markup\" ngPreserveWhitespaces>{{code}}\n                </app-code>\n            </div>\n        </div>\n    </div>\n  `,\n    styleUrls: ['./blockviewer.component.scss']\n})\nexport class BlockViewer {\n\n    @Input() header!: string;\n\n    @Input() code!: string;\n\n    @Input() containerClass!: string;\n\n    @Input() previewStyle!: object;\n\n    @Input() free: boolean = true;\n\n    @Input() new: boolean = false;\n\n    BlockView = BlockView;\n\n    blockView: BlockView = BlockView.PREVIEW;\n\n    activateView(event: Event, blockView: BlockView) {\n\n        this.blockView = blockView;\n        event.preventDefault();\n    }\n\n\n    async copyCode(event: Event) {\n        await navigator.clipboard.writeText(this.code);\n        event.preventDefault();\n    }\n\n}\n"]}, "metadata": {}, "sourceType": "module"}