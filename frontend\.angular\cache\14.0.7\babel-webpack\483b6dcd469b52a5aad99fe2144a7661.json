{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n  const findIndex = emit === 'index';\n  return (source, subscriber) => {\n    let index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const i = index++;\n\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, () => {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "find", "predicate", "thisArg", "createFind", "emit", "findIndex", "source", "subscriber", "index", "subscribe", "value", "i", "call", "next", "complete", "undefined"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/find.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n    return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n    const findIndex = emit === 'index';\n    return (source, subscriber) => {\n        let index = 0;\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const i = index++;\n            if (predicate.call(thisArg, value, i, source)) {\n                subscriber.next(findIndex ? i : value);\n                subscriber.complete();\n            }\n        }, () => {\n            subscriber.next(findIndex ? -1 : undefined);\n            subscriber.complete();\n        }));\n    };\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,IAAT,CAAcC,SAAd,EAAyBC,OAAzB,EAAkC;EACrC,OAAOJ,OAAO,CAACK,UAAU,CAACF,SAAD,EAAYC,OAAZ,EAAqB,OAArB,CAAX,CAAd;AACH;AACD,OAAO,SAASC,UAAT,CAAoBF,SAApB,EAA+BC,OAA/B,EAAwCE,IAAxC,EAA8C;EACjD,MAAMC,SAAS,GAAGD,IAAI,KAAK,OAA3B;EACA,OAAO,CAACE,MAAD,EAASC,UAAT,KAAwB;IAC3B,IAAIC,KAAK,GAAG,CAAZ;IACAF,MAAM,CAACG,SAAP,CAAiBV,wBAAwB,CAACQ,UAAD,EAAcG,KAAD,IAAW;MAC7D,MAAMC,CAAC,GAAGH,KAAK,EAAf;;MACA,IAAIP,SAAS,CAACW,IAAV,CAAeV,OAAf,EAAwBQ,KAAxB,EAA+BC,CAA/B,EAAkCL,MAAlC,CAAJ,EAA+C;QAC3CC,UAAU,CAACM,IAAX,CAAgBR,SAAS,GAAGM,CAAH,GAAOD,KAAhC;QACAH,UAAU,CAACO,QAAX;MACH;IACJ,CANwC,EAMtC,MAAM;MACLP,UAAU,CAACM,IAAX,CAAgBR,SAAS,GAAG,CAAC,CAAJ,GAAQU,SAAjC;MACAR,UAAU,CAACO,QAAX;IACH,CATwC,CAAzC;EAUH,CAZD;AAaH"}, "metadata": {}, "sourceType": "module"}