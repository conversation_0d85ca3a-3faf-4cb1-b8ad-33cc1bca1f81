{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ZIndexUtils, UniqueComponentId } from 'primeng/utils';\nimport { DomHandler } from 'primeng/dom';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, transition, style, animate } from '@angular/animations';\nconst _c0 = [\"mask\"];\n\nconst _c1 = function (a0, a1) {\n  return {\n    showTransitionParams: a0,\n    hideTransitionParams: a1\n  };\n};\n\nconst _c2 = function (a1) {\n  return {\n    value: \"visible\",\n    params: a1\n  };\n};\n\nfunction Galleria_div_0_div_1_p_galleriaContent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 6);\n    i0.ɵɵlistener(\"@animation.start\", function Galleria_div_0_div_1_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r6.onAnimationStart($event));\n    })(\"@animation.done\", function Galleria_div_0_div_1_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r8 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r8.onAnimationEnd($event));\n    })(\"maskHide\", function Galleria_div_0_div_1_p_galleriaContent_2_Template_p_galleriaContent_maskHide_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r9 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r9.onMaskHide());\n    })(\"activeItemChange\", function Galleria_div_0_div_1_p_galleriaContent_2_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r10 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r10.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@animation\", i0.ɵɵpureFunction1(8, _c2, i0.ɵɵpureFunction2(5, _c1, ctx_r5.showTransitionOptions, ctx_r5.hideTransitionOptions)))(\"value\", ctx_r5.value)(\"activeIndex\", ctx_r5.activeIndex)(\"numVisible\", ctx_r5.numVisible)(\"ngStyle\", ctx_r5.containerStyle);\n  }\n}\n\nconst _c3 = function (a1) {\n  return {\n    \"p-galleria-mask p-component-overlay p-component-overlay-enter\": true,\n    \"p-galleria-visible\": a1\n  };\n};\n\nfunction Galleria_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3, 4);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_1_p_galleriaContent_2_Template, 1, 10, \"p-galleriaContent\", 5);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.maskClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c3, ctx_r3.visible));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.visible);\n  }\n}\n\nfunction Galleria_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, Galleria_div_0_div_1_Template, 3, 6, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.maskVisible);\n  }\n}\n\nfunction Galleria_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 7);\n    i0.ɵɵlistener(\"activeItemChange\", function Galleria_ng_template_1_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"numVisible\", ctx_r2.numVisible);\n  }\n}\n\nfunction GalleriaContent_div_0_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaContent_div_0_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r5.maskHide.emit());\n    });\n    i0.ɵɵelement(1, \"span\", 9);\n    i0.ɵɵelementEnd();\n  }\n}\n\nfunction GalleriaContent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 11);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\n\nfunction GalleriaContent_div_0_p_galleriaThumbnails_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"p-galleriaThumbnails\", 12);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_onActiveIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r7.onActiveIndexChange($event));\n    })(\"stopSlideShow\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_stopSlideShow_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"containerId\", ctx_r3.id)(\"value\", ctx_r3.value)(\"activeIndex\", ctx_r3.activeIndex)(\"templates\", ctx_r3.galleria.templates)(\"numVisible\", ctx_r3.numVisible)(\"responsiveOptions\", ctx_r3.galleria.responsiveOptions)(\"circular\", ctx_r3.galleria.circular)(\"isVertical\", ctx_r3.isVertical())(\"contentHeight\", ctx_r3.galleria.verticalThumbnailViewPortHeight)(\"showThumbnailNavigators\", ctx_r3.galleria.showThumbnailNavigators)(\"slideShowActive\", ctx_r3.slideShowActive);\n  }\n}\n\nfunction GalleriaContent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"templates\", ctx_r4.galleria.templates);\n  }\n}\n\nconst _c4 = function (a1, a2, a3) {\n  return {\n    \"p-galleria p-component\": true,\n    \"p-galleria-fullscreen\": a1,\n    \"p-galleria-indicator-onitem\": a2,\n    \"p-galleria-item-nav-onhover\": a3\n  };\n};\n\nconst _c5 = function () {\n  return {};\n};\n\nfunction GalleriaContent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_Template, 2, 0, \"button\", 2);\n    i0.ɵɵtemplate(2, GalleriaContent_div_0_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-galleriaItem\", 5);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_Template_p_galleriaItem_onActiveIndexChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onActiveIndexChange($event));\n    })(\"startSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_startSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.startSlideShow());\n    })(\"stopSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_stopSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GalleriaContent_div_0_p_galleriaThumbnails_5_Template, 1, 11, \"p-galleriaThumbnails\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GalleriaContent_div_0_div_6_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.galleriaClass());\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(20, _c4, ctx_r0.galleria.fullScreen, ctx_r0.galleria.showIndicatorsOnItem, ctx_r0.galleria.showItemNavigatorsOnHover && !ctx_r0.galleria.fullScreen))(\"ngStyle\", !ctx_r0.galleria.fullScreen ? ctx_r0.galleria.containerStyle : i0.ɵɵpureFunction0(24, _c5));\n    i0.ɵɵattribute(\"id\", ctx_r0.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.galleria.fullScreen);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.galleria.templates && ctx_r0.galleria.headerFacet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r0.value)(\"activeIndex\", ctx_r0.activeIndex)(\"circular\", ctx_r0.galleria.circular)(\"templates\", ctx_r0.galleria.templates)(\"showIndicators\", ctx_r0.galleria.showIndicators)(\"changeItemOnIndicatorHover\", ctx_r0.galleria.changeItemOnIndicatorHover)(\"indicatorFacet\", ctx_r0.galleria.indicatorFacet)(\"captionFacet\", ctx_r0.galleria.captionFacet)(\"showItemNavigators\", ctx_r0.galleria.showItemNavigators)(\"autoPlay\", ctx_r0.galleria.autoPlay)(\"slideShowActive\", ctx_r0.slideShowActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.galleria.showThumbnails);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.galleria.templates && ctx_r0.galleria.footerFacet);\n  }\n}\n\nfunction GalleriaItemSlot_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction GalleriaItemSlot_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaItemSlot_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate)(\"ngTemplateOutletContext\", ctx_r0.context);\n  }\n}\n\nconst _c6 = function (a1) {\n  return {\n    \"p-galleria-item-prev p-galleria-item-nav p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction GalleriaItem_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navBackward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 7);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c6, ctx_r0.isNavBackwardDisabled()))(\"disabled\", ctx_r0.isNavBackwardDisabled());\n  }\n}\n\nconst _c7 = function (a1) {\n  return {\n    \"p-galleria-item-next p-galleria-item-nav p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nfunction GalleriaItem_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.navForward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c7, ctx_r1.isNavForwardDisabled()))(\"disabled\", ctx_r1.isNavForwardDisabled());\n  }\n}\n\nfunction GalleriaItem_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", ctx_r2.activeItem)(\"templates\", ctx_r2.templates);\n  }\n}\n\nfunction GalleriaItem_ul_6_li_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 16);\n  }\n}\n\nconst _c8 = function (a1) {\n  return {\n    \"p-galleria-indicator\": true,\n    \"p-highlight\": a1\n  };\n};\n\nfunction GalleriaItem_ul_6_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"li\", 13);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_ul_6_li_1_Template_li_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const index_r10 = restoredCtx.index;\n      const ctx_r12 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r12.onIndicatorClick(index_r10));\n    })(\"mouseenter\", function GalleriaItem_ul_6_li_1_Template_li_mouseenter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const index_r10 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.onIndicatorMouseEnter(index_r10));\n    })(\"keydown.enter\", function GalleriaItem_ul_6_li_1_Template_li_keydown_enter_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const index_r10 = restoredCtx.index;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r15.onIndicatorKeyDown(index_r10));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_ul_6_li_1_button_1_Template, 1, 0, \"button\", 14);\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 15);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const index_r10 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c8, ctx_r8.isIndicatorItemActive(index_r10)));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.indicatorFacet);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"index\", index_r10)(\"templates\", ctx_r8.templates);\n  }\n}\n\nfunction GalleriaItem_ul_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 11);\n    i0.ɵɵtemplate(1, GalleriaItem_ul_6_li_1_Template, 3, 6, \"li\", 12);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.value);\n  }\n}\n\nconst _c9 = [\"itemsContainer\"];\n\nconst _c10 = function (a1) {\n  return {\n    \"p-galleria-thumbnail-prev p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c11 = function (a1, a2) {\n  return {\n    \"p-galleria-thumbnail-prev-icon pi\": true,\n    \"pi-chevron-left\": a1,\n    \"pi-chevron-up\": a2\n  };\n};\n\nfunction GalleriaThumbnails_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navBackward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c10, ctx_r0.isNavBackwardDisabled()))(\"disabled\", ctx_r0.isNavBackwardDisabled());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c11, !ctx_r0.isVertical, ctx_r0.isVertical));\n  }\n}\n\nconst _c12 = function (a1, a2, a3, a4) {\n  return {\n    \"p-galleria-thumbnail-item\": true,\n    \"p-galleria-thumbnail-item-current\": a1,\n    \"p-galleria-thumbnail-item-active\": a2,\n    \"p-galleria-thumbnail-item-start\": a3,\n    \"p-galleria-thumbnail-item-end\": a4\n  };\n};\n\nfunction GalleriaThumbnails_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_div_6_Template_div_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const index_r7 = restoredCtx.index;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.onItemClick(index_r7));\n    })(\"keydown.enter\", function GalleriaThumbnails_div_6_Template_div_keydown_enter_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r9);\n      const index_r7 = restoredCtx.index;\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onItemClick(index_r7));\n    });\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 10);\n    i0.ɵɵelementEnd()();\n  }\n\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(4, _c12, ctx_r2.activeIndex === index_r7, ctx_r2.isItemActive(index_r7), ctx_r2.firstItemAciveIndex() === index_r7, ctx_r2.lastItemActiveIndex() === index_r7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵattribute(\"tabindex\", ctx_r2.getTabIndex(index_r7));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"item\", item_r6)(\"templates\", ctx_r2.templates);\n  }\n}\n\nconst _c13 = function (a1) {\n  return {\n    \"p-galleria-thumbnail-next p-link\": true,\n    \"p-disabled\": a1\n  };\n};\n\nconst _c14 = function (a1, a2) {\n  return {\n    \"p-galleria-thumbnail-next-icon pi\": true,\n    \"pi-chevron-right\": a1,\n    \"pi-chevron-down\": a2\n  };\n};\n\nfunction GalleriaThumbnails_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.navForward($event));\n    });\n    i0.ɵɵelement(1, \"span\", 8);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c13, ctx_r3.isNavForwardDisabled()))(\"disabled\", ctx_r3.isNavForwardDisabled());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c14, !ctx_r3.isVertical, ctx_r3.isVertical));\n  }\n}\n\nconst _c15 = function (a0) {\n  return {\n    \"height\": a0\n  };\n};\n\nclass Galleria {\n  constructor(element, cd, config) {\n    this.element = element;\n    this.cd = cd;\n    this.config = config;\n    this.fullScreen = false;\n    this.numVisible = 3;\n    this.showItemNavigators = false;\n    this.showThumbnailNavigators = true;\n    this.showItemNavigatorsOnHover = false;\n    this.changeItemOnIndicatorHover = false;\n    this.circular = false;\n    this.autoPlay = false;\n    this.transitionInterval = 4000;\n    this.showThumbnails = true;\n    this.thumbnailsPosition = \"bottom\";\n    this.verticalThumbnailViewPortHeight = \"300px\";\n    this.showIndicators = false;\n    this.showIndicatorsOnItem = false;\n    this.indicatorsPosition = \"bottom\";\n    this.baseZIndex = 0;\n    this.showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    this.hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    this.activeIndexChange = new EventEmitter();\n    this.visibleChange = new EventEmitter();\n    this._visible = false;\n    this._activeIndex = 0;\n    this.maskVisible = false;\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n\n  get visible() {\n    return this._visible;\n  }\n\n  set visible(visible) {\n    this._visible = visible;\n\n    if (this._visible && !this.maskVisible) {\n      this.maskVisible = true;\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerFacet = item.template;\n          break;\n\n        case 'footer':\n          this.footerFacet = item.template;\n          break;\n\n        case 'indicator':\n          this.indicatorFacet = item.template;\n          break;\n\n        case 'caption':\n          this.captionFacet = item.template;\n          break;\n      }\n    });\n  }\n\n  ngOnChanges(simpleChanges) {\n    var _a;\n\n    if (simpleChanges.value && ((_a = simpleChanges.value.currentValue) === null || _a === void 0 ? void 0 : _a.length) < this.numVisible) {\n      this.numVisible = simpleChanges.value.currentValue.length;\n    }\n  }\n\n  onMaskHide() {\n    this.visible = false;\n    this.visibleChange.emit(false);\n  }\n\n  onActiveItemChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeIndexChange.emit(index);\n    }\n  }\n\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.enableModality();\n        break;\n\n      case 'void':\n        DomHandler.addClass(this.mask.nativeElement, 'p-component-overlay-leave');\n        break;\n    }\n  }\n\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.disableModality();\n        break;\n    }\n  }\n\n  enableModality() {\n    DomHandler.addClass(document.body, 'p-overflow-hidden');\n    this.cd.markForCheck();\n\n    if (this.mask) {\n      ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n    }\n  }\n\n  disableModality() {\n    DomHandler.removeClass(document.body, 'p-overflow-hidden');\n    this.maskVisible = false;\n    this.cd.markForCheck();\n\n    if (this.mask) {\n      ZIndexUtils.clear(this.mask.nativeElement);\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.fullScreen) {\n      DomHandler.removeClass(document.body, 'p-overflow-hidden');\n    }\n\n    if (this.mask) {\n      this.disableModality();\n    }\n  }\n\n}\n\nGalleria.ɵfac = function Galleria_Factory(t) {\n  return new (t || Galleria)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n};\n\nGalleria.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Galleria,\n  selectors: [[\"p-galleria\"]],\n  contentQueries: function Galleria_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Galleria_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mask = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    activeIndex: \"activeIndex\",\n    fullScreen: \"fullScreen\",\n    id: \"id\",\n    value: \"value\",\n    numVisible: \"numVisible\",\n    responsiveOptions: \"responsiveOptions\",\n    showItemNavigators: \"showItemNavigators\",\n    showThumbnailNavigators: \"showThumbnailNavigators\",\n    showItemNavigatorsOnHover: \"showItemNavigatorsOnHover\",\n    changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n    circular: \"circular\",\n    autoPlay: \"autoPlay\",\n    transitionInterval: \"transitionInterval\",\n    showThumbnails: \"showThumbnails\",\n    thumbnailsPosition: \"thumbnailsPosition\",\n    verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\",\n    showIndicators: \"showIndicators\",\n    showIndicatorsOnItem: \"showIndicatorsOnItem\",\n    indicatorsPosition: \"indicatorsPosition\",\n    baseZIndex: \"baseZIndex\",\n    maskClass: \"maskClass\",\n    containerClass: \"containerClass\",\n    containerStyle: \"containerStyle\",\n    showTransitionOptions: \"showTransitionOptions\",\n    hideTransitionOptions: \"hideTransitionOptions\",\n    visible: \"visible\"\n  },\n  outputs: {\n    activeIndexChange: \"activeIndexChange\",\n    visibleChange: \"visibleChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"windowed\", \"\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [3, \"ngClass\"], [\"mask\", \"\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"maskHide\", \"activeItemChange\", 4, \"ngIf\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"maskHide\", \"activeItemChange\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"activeItemChange\"]],\n  template: function Galleria_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, Galleria_div_0_Template, 2, 1, \"div\", 0);\n      i0.ɵɵtemplate(1, Galleria_ng_template_1_Template, 1, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n\n      i0.ɵɵproperty(\"ngIf\", ctx.fullScreen)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: function () {\n    return [i2.NgClass, i2.NgIf, i2.NgStyle, GalleriaContent];\n  },\n  styles: [\".p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}\\n\"],\n  encapsulation: 2,\n  data: {\n    animation: [trigger('animation', [transition('void => visible', [style({\n      transform: 'scale(0.7)',\n      opacity: 0\n    }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n      transform: 'scale(0.7)',\n      opacity: 0\n    }))])])]\n  },\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Galleria, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleria',\n      template: `\n        <div *ngIf=\"fullScreen;else windowed\">\n            <div *ngIf=\"maskVisible\" #mask [ngClass]=\"{'p-galleria-mask p-component-overlay p-component-overlay-enter':true, 'p-galleria-visible': this.visible}\" [class]=\"maskClass\">\n                <p-galleriaContent *ngIf=\"visible\" [@animation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (maskHide)=\"onMaskHide()\" (activeItemChange)=\"onActiveItemChange($event)\" [ngStyle]=\"containerStyle\"></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `,\n      animations: [trigger('animation', [transition('void => visible', [style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }, {\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i1.PrimeNGConfig\n    }];\n  }, {\n    activeIndex: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input\n    }],\n    showThumbnailNavigators: [{\n      type: Input\n    }],\n    showItemNavigatorsOnHover: [{\n      type: Input\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    transitionInterval: [{\n      type: Input\n    }],\n    showThumbnails: [{\n      type: Input\n    }],\n    thumbnailsPosition: [{\n      type: Input\n    }],\n    verticalThumbnailViewPortHeight: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    showIndicatorsOnItem: [{\n      type: Input\n    }],\n    indicatorsPosition: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    maskClass: [{\n      type: Input\n    }],\n    containerClass: [{\n      type: Input\n    }],\n    containerStyle: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    mask: [{\n      type: ViewChild,\n      args: ['mask']\n    }],\n    visible: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\n\nclass GalleriaContent {\n  constructor(galleria, cd) {\n    this.galleria = galleria;\n    this.cd = cd;\n    this.value = [];\n    this.maskHide = new EventEmitter();\n    this.activeItemChange = new EventEmitter();\n    this.id = this.galleria.id || UniqueComponentId();\n    this.slideShowActicve = false;\n    this._activeIndex = 0;\n    this.slideShowActive = true;\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n\n  galleriaClass() {\n    const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n    const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n    return (this.galleria.containerClass ? this.galleria.containerClass + \" \" : '') + (thumbnailsPosClass ? thumbnailsPosClass + \" \" : '') + (indicatorPosClass ? indicatorPosClass + \" \" : '');\n  }\n\n  startSlideShow() {\n    this.interval = setInterval(() => {\n      let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n      this.onActiveIndexChange(activeIndex);\n      this.activeIndex = activeIndex;\n    }, this.galleria.transitionInterval);\n    this.slideShowActive = true;\n  }\n\n  stopSlideShow() {\n    if (this.interval) {\n      clearInterval(this.interval);\n    }\n\n    this.slideShowActive = false;\n  }\n\n  getPositionClass(preClassName, position) {\n    const positions = ['top', 'left', 'bottom', 'right'];\n    const pos = positions.find(item => item === position);\n    return pos ? `${preClassName}-${pos}` : '';\n  }\n\n  isVertical() {\n    return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n  }\n\n  onActiveIndexChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeItemChange.emit(this.activeIndex);\n    }\n  }\n\n}\n\nGalleriaContent.ɵfac = function GalleriaContent_Factory(t) {\n  return new (t || GalleriaContent)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nGalleriaContent.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: GalleriaContent,\n  selectors: [[\"p-galleriaContent\"]],\n  inputs: {\n    activeIndex: \"activeIndex\",\n    value: \"value\",\n    numVisible: \"numVisible\"\n  },\n  outputs: {\n    maskHide: \"maskHide\",\n    activeItemChange: \"activeItemChange\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [\"type\", \"button\", \"class\", \"p-galleria-close p-link\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-header\", 4, \"ngIf\"], [1, \"p-galleria-content\"], [3, \"value\", \"activeIndex\", \"circular\", \"templates\", \"showIndicators\", \"changeItemOnIndicatorHover\", \"indicatorFacet\", \"captionFacet\", \"showItemNavigators\", \"autoPlay\", \"slideShowActive\", \"onActiveIndexChange\", \"startSlideShow\", \"stopSlideShow\"], [3, \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\", \"onActiveIndexChange\", \"stopSlideShow\", 4, \"ngIf\"], [\"class\", \"p-galleria-footer\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-galleria-close\", \"p-link\", 3, \"click\"], [1, \"p-galleria-close-icon\", \"pi\", \"pi-times\"], [1, \"p-galleria-header\"], [\"type\", \"header\", 3, \"templates\"], [3, \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\", \"onActiveIndexChange\", \"stopSlideShow\"], [1, \"p-galleria-footer\"], [\"type\", \"footer\", 3, \"templates\"]],\n  template: function GalleriaContent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, GalleriaContent_div_0_Template, 7, 25, \"div\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.value && ctx.value.length > 0);\n    }\n  },\n  dependencies: function () {\n    return [i2.NgClass, i2.NgIf, i2.NgStyle, i3.Ripple, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails];\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaContent, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaContent',\n      template: `\n        <div [attr.id]=\"id\" *ngIf=\"value && value.length > 0\" [ngClass]=\"{'p-galleria p-component': true, 'p-galleria-fullscreen': this.galleria.fullScreen,\n            'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem, 'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen}\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\" [class]=\"galleriaClass()\">\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple>\n                <span class=\"p-galleria-close-icon pi pi-times\"></span>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\">\n                <p-galleriaItem [value]=\"value\" [activeIndex]=\"activeIndex\" [circular]=\"galleria.circular\" [templates]=\"galleria.templates\" (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\" [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\" [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\" [showItemNavigators]=\"galleria.showItemNavigators\" [autoPlay]=\"galleria.autoPlay\" [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaItem>\n\n                <p-galleriaThumbnails *ngIf=\"galleria.showThumbnails\" [containerId]=\"id\" [value]=\"value\" (onActiveIndexChange)=\"onActiveIndexChange($event)\" [activeIndex]=\"activeIndex\" [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\" [responsiveOptions]=\"galleria.responsiveOptions\" [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\" [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\" [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: Galleria\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    activeIndex: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    maskHide: [{\n      type: Output\n    }],\n    activeItemChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass GalleriaItemSlot {\n  get item() {\n    return this._item;\n  }\n\n  set item(item) {\n    this._item = item;\n\n    if (this.templates) {\n      this.templates.forEach(item => {\n        if (item.getType() === this.type) {\n          switch (this.type) {\n            case 'item':\n            case 'caption':\n            case 'thumbnail':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n          }\n        }\n      });\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      if (item.getType() === this.type) {\n        switch (this.type) {\n          case 'item':\n          case 'caption':\n          case 'thumbnail':\n            this.context = {\n              $implicit: this.item\n            };\n            this.contentTemplate = item.template;\n            break;\n\n          case 'indicator':\n            this.context = {\n              $implicit: this.index\n            };\n            this.contentTemplate = item.template;\n            break;\n\n          default:\n            this.context = {};\n            this.contentTemplate = item.template;\n            break;\n        }\n      }\n    });\n  }\n\n}\n\nGalleriaItemSlot.ɵfac = function GalleriaItemSlot_Factory(t) {\n  return new (t || GalleriaItemSlot)();\n};\n\nGalleriaItemSlot.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: GalleriaItemSlot,\n  selectors: [[\"p-galleriaItemSlot\"]],\n  inputs: {\n    templates: \"templates\",\n    index: \"index\",\n    item: \"item\",\n    type: \"type\"\n  },\n  decls: 1,\n  vars: 1,\n  consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n  template: function GalleriaItemSlot_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, GalleriaItemSlot_ng_container_0_Template, 2, 2, \"ng-container\", 0);\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate);\n    }\n  },\n  dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItemSlot, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItemSlot',\n      template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    templates: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    item: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }]\n  });\n})();\n\nclass GalleriaItem {\n  constructor() {\n    this.circular = false;\n    this.showItemNavigators = false;\n    this.showIndicators = true;\n    this.slideShowActive = true;\n    this.changeItemOnIndicatorHover = true;\n    this.autoPlay = false;\n    this.startSlideShow = new EventEmitter();\n    this.stopSlideShow = new EventEmitter();\n    this.onActiveIndexChange = new EventEmitter();\n    this._activeIndex = 0;\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n    this.activeItem = this.value[this._activeIndex];\n  }\n\n  ngOnInit() {\n    if (this.autoPlay) {\n      this.startSlideShow.emit();\n    }\n  }\n\n  next() {\n    let nextItemIndex = this.activeIndex + 1;\n    let activeIndex = this.circular && this.value.length - 1 === this.activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n\n  prev() {\n    let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n    let activeIndex = this.circular && this.activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n\n  navForward(e) {\n    this.stopTheSlideShow();\n    this.next();\n\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  navBackward(e) {\n    this.stopTheSlideShow();\n    this.prev();\n\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  onIndicatorClick(index) {\n    this.stopTheSlideShow();\n    this.onActiveIndexChange.emit(index);\n  }\n\n  onIndicatorMouseEnter(index) {\n    if (this.changeItemOnIndicatorHover) {\n      this.stopTheSlideShow();\n      this.onActiveIndexChange.emit(index);\n    }\n  }\n\n  onIndicatorKeyDown(index) {\n    this.stopTheSlideShow();\n    this.onActiveIndexChange.emit(index);\n  }\n\n  isNavForwardDisabled() {\n    return !this.circular && this.activeIndex === this.value.length - 1;\n  }\n\n  isNavBackwardDisabled() {\n    return !this.circular && this.activeIndex === 0;\n  }\n\n  isIndicatorItemActive(index) {\n    return this.activeIndex === index;\n  }\n\n}\n\nGalleriaItem.ɵfac = function GalleriaItem_Factory(t) {\n  return new (t || GalleriaItem)();\n};\n\nGalleriaItem.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: GalleriaItem,\n  selectors: [[\"p-galleriaItem\"]],\n  inputs: {\n    circular: \"circular\",\n    value: \"value\",\n    showItemNavigators: \"showItemNavigators\",\n    showIndicators: \"showIndicators\",\n    slideShowActive: \"slideShowActive\",\n    changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n    autoPlay: \"autoPlay\",\n    templates: \"templates\",\n    indicatorFacet: \"indicatorFacet\",\n    captionFacet: \"captionFacet\",\n    activeIndex: \"activeIndex\"\n  },\n  outputs: {\n    startSlideShow: \"startSlideShow\",\n    stopSlideShow: \"stopSlideShow\",\n    onActiveIndexChange: \"onActiveIndexChange\"\n  },\n  decls: 7,\n  vars: 6,\n  consts: [[1, \"p-galleria-item-wrapper\"], [1, \"p-galleria-item-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"item\", 1, \"p-galleria-item\", 3, \"item\", \"templates\"], [\"class\", \"p-galleria-caption\", 4, \"ngIf\"], [\"class\", \"p-galleria-indicators p-reset\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\"], [1, \"p-galleria-item-prev-icon\", \"pi\", \"pi-chevron-left\"], [1, \"p-galleria-item-next-icon\", \"pi\", \"pi-chevron-right\"], [1, \"p-galleria-caption\"], [\"type\", \"caption\", 3, \"item\", \"templates\"], [1, \"p-galleria-indicators\", \"p-reset\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"mouseenter\", \"keydown.enter\", 4, \"ngFor\", \"ngForOf\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"mouseenter\", \"keydown.enter\"], [\"type\", \"button\", \"tabIndex\", \"-1\", \"class\", \"p-link\", 4, \"ngIf\"], [\"type\", \"indicator\", 3, \"index\", \"templates\"], [\"type\", \"button\", \"tabIndex\", \"-1\", 1, \"p-link\"]],\n  template: function GalleriaItem_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, GalleriaItem_button_2_Template, 2, 4, \"button\", 2);\n      i0.ɵɵelement(3, \"p-galleriaItemSlot\", 3);\n      i0.ɵɵtemplate(4, GalleriaItem_button_4_Template, 2, 4, \"button\", 2);\n      i0.ɵɵtemplate(5, GalleriaItem_div_5_Template, 2, 2, \"div\", 4);\n      i0.ɵɵelementEnd();\n      i0.ɵɵtemplate(6, GalleriaItem_ul_6_Template, 2, 1, \"ul\", 5);\n      i0.ɵɵelementEnd();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"item\", ctx.activeItem)(\"templates\", ctx.templates);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.captionFacet);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.Ripple, GalleriaItemSlot],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItem, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItem',\n      template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-prev-icon pi pi-chevron-left\"></span>\n                </button>\n                <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-next p-galleria-item-nav p-link': true,'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\"  [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-next-icon pi pi-chevron-right\"></span>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li *ngFor=\"let item of value; let index = index;\" tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\" (mouseenter)=\"onIndicatorMouseEnter(index)\" (keydown.enter)=\"onIndicatorKeyDown(index)\"\n                    [ngClass]=\"{'p-galleria-indicator': true,'p-highlight': isIndicatorItemActive(index)}\">\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\">\n                    </button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    circular: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    slideShowActive: [{\n      type: Input\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    templates: [{\n      type: Input\n    }],\n    indicatorFacet: [{\n      type: Input\n    }],\n    captionFacet: [{\n      type: Input\n    }],\n    startSlideShow: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\n\nclass GalleriaThumbnails {\n  constructor(cd) {\n    this.cd = cd;\n    this.isVertical = false;\n    this.slideShowActive = false;\n    this.circular = false;\n    this.contentHeight = \"300px\";\n    this.showThumbnailNavigators = true;\n    this.onActiveIndexChange = new EventEmitter();\n    this.stopSlideShow = new EventEmitter();\n    this.startPos = null;\n    this.thumbnailsStyle = null;\n    this.sortedResponsiveOptions = null;\n    this.totalShiftedItems = 0;\n    this.page = 0;\n    this._numVisible = 0;\n    this.d_numVisible = 0;\n    this._oldNumVisible = 0;\n    this._activeIndex = 0;\n    this._oldactiveIndex = 0;\n  }\n\n  get numVisible() {\n    return this._numVisible;\n  }\n\n  set numVisible(numVisible) {\n    this._numVisible = numVisible;\n    this._oldNumVisible = this.d_numVisible;\n    this.d_numVisible = numVisible;\n  }\n\n  get activeIndex() {\n    return this._activeIndex;\n  }\n\n  set activeIndex(activeIndex) {\n    this._oldactiveIndex = this._activeIndex;\n    this._activeIndex = activeIndex;\n  }\n\n  ngOnInit() {\n    this.createStyle();\n\n    if (this.responsiveOptions) {\n      this.bindDocumentListeners();\n    }\n  }\n\n  ngAfterContentChecked() {\n    let totalShiftedItems = this.totalShiftedItems;\n\n    if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n      if (this._activeIndex <= this.getMedianItemIndex()) {\n        totalShiftedItems = 0;\n      } else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      } else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n      } else {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n      }\n\n      if (totalShiftedItems !== this.totalShiftedItems) {\n        this.totalShiftedItems = totalShiftedItems;\n      }\n\n      if (this.itemsContainer && this.itemsContainer.nativeElement) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      }\n\n      if (this._oldactiveIndex !== this._activeIndex) {\n        DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n        this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n      }\n\n      this._oldactiveIndex = this._activeIndex;\n      this._oldNumVisible = this.d_numVisible;\n    }\n  }\n\n  ngAfterViewInit() {\n    this.calculatePosition();\n  }\n\n  createStyle() {\n    if (!this.thumbnailsStyle) {\n      this.thumbnailsStyle = document.createElement('style');\n      this.thumbnailsStyle.type = 'text/css';\n      document.body.appendChild(this.thumbnailsStyle);\n    }\n\n    let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n\n    if (this.responsiveOptions) {\n      this.sortedResponsiveOptions = [...this.responsiveOptions];\n      this.sortedResponsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n\n      for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n        let res = this.sortedResponsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n\n    this.thumbnailsStyle.innerHTML = innerHTML;\n  }\n\n  calculatePosition() {\n    if (this.itemsContainer && this.sortedResponsiveOptions) {\n      let windowWidth = window.innerWidth;\n      let matchedResponsiveData = {\n        numVisible: this._numVisible\n      };\n\n      for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n        let res = this.sortedResponsiveOptions[i];\n\n        if (parseInt(res.breakpoint, 10) >= windowWidth) {\n          matchedResponsiveData = res;\n        }\n      }\n\n      if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n        this.d_numVisible = matchedResponsiveData.numVisible;\n        this.cd.markForCheck();\n      }\n    }\n  }\n\n  getTabIndex(index) {\n    return this.isItemActive(index) ? 0 : null;\n  }\n\n  navForward(e) {\n    this.stopTheSlideShow();\n    let nextItemIndex = this._activeIndex + 1;\n\n    if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n      this.step(-1);\n    }\n\n    let activeIndex = this.circular && this.value.length - 1 === this._activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  navBackward(e) {\n    this.stopTheSlideShow();\n    let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n    let diff = prevItemIndex + this.totalShiftedItems;\n\n    if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n      this.step(1);\n    }\n\n    let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  onItemClick(index) {\n    this.stopTheSlideShow();\n    let selectedItemIndex = index;\n\n    if (selectedItemIndex !== this._activeIndex) {\n      const diff = selectedItemIndex + this.totalShiftedItems;\n      let dir = 0;\n\n      if (selectedItemIndex < this._activeIndex) {\n        dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n\n        if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n          this.step(dir);\n        }\n      } else {\n        dir = this.getMedianItemIndex() - diff;\n\n        if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n          this.step(dir);\n        }\n      }\n\n      this.activeIndex = selectedItemIndex;\n      this.onActiveIndexChange.emit(this.activeIndex);\n    }\n  }\n\n  step(dir) {\n    let totalShiftedItems = this.totalShiftedItems + dir;\n\n    if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > this.value.length - 1) {\n      totalShiftedItems = this.d_numVisible - this.value.length;\n    } else if (dir > 0 && totalShiftedItems > 0) {\n      totalShiftedItems = 0;\n    }\n\n    if (this.circular) {\n      if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n        totalShiftedItems = 0;\n      } else if (dir > 0 && this._activeIndex === 0) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      }\n    }\n\n    if (this.itemsContainer) {\n      DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n\n    this.totalShiftedItems = totalShiftedItems;\n  }\n\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n\n  changePageOnTouch(e, diff) {\n    if (diff < 0) {\n      // left\n      this.navForward(e);\n    } else {\n      // right\n      this.navBackward(e);\n    }\n  }\n\n  getTotalPageNumber() {\n    return this.value.length > this.d_numVisible ? this.value.length - this.d_numVisible + 1 : 0;\n  }\n\n  getMedianItemIndex() {\n    let index = Math.floor(this.d_numVisible / 2);\n    return this.d_numVisible % 2 ? index : index - 1;\n  }\n\n  onTransitionEnd() {\n    if (this.itemsContainer && this.itemsContainer.nativeElement) {\n      DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transition = '';\n    }\n  }\n\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n\n    if (this.isVertical) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n\n  isNavBackwardDisabled() {\n    return !this.circular && this._activeIndex === 0 || this.value.length <= this.d_numVisible;\n  }\n\n  isNavForwardDisabled() {\n    return !this.circular && this._activeIndex === this.value.length - 1 || this.value.length <= this.d_numVisible;\n  }\n\n  firstItemAciveIndex() {\n    return this.totalShiftedItems * -1;\n  }\n\n  lastItemActiveIndex() {\n    return this.firstItemAciveIndex() + this.d_numVisible - 1;\n  }\n\n  isItemActive(index) {\n    return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n  }\n\n  bindDocumentListeners() {\n    if (!this.documentResizeListener) {\n      this.documentResizeListener = () => {\n        this.calculatePosition();\n      };\n\n      window.addEventListener('resize', this.documentResizeListener);\n    }\n  }\n\n  unbindDocumentListeners() {\n    if (this.documentResizeListener) {\n      window.removeEventListener('resize', this.documentResizeListener);\n      this.documentResizeListener = null;\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n\n    if (this.thumbnailsStyle) {\n      this.thumbnailsStyle.parentNode.removeChild(this.thumbnailsStyle);\n    }\n  }\n\n}\n\nGalleriaThumbnails.ɵfac = function GalleriaThumbnails_Factory(t) {\n  return new (t || GalleriaThumbnails)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nGalleriaThumbnails.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: GalleriaThumbnails,\n  selectors: [[\"p-galleriaThumbnails\"]],\n  viewQuery: function GalleriaThumbnails_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c9, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n    }\n  },\n  inputs: {\n    containerId: \"containerId\",\n    value: \"value\",\n    isVertical: \"isVertical\",\n    slideShowActive: \"slideShowActive\",\n    circular: \"circular\",\n    responsiveOptions: \"responsiveOptions\",\n    contentHeight: \"contentHeight\",\n    showThumbnailNavigators: \"showThumbnailNavigators\",\n    templates: \"templates\",\n    numVisible: \"numVisible\",\n    activeIndex: \"activeIndex\"\n  },\n  outputs: {\n    onActiveIndexChange: \"onActiveIndexChange\",\n    stopSlideShow: \"stopSlideShow\"\n  },\n  decls: 8,\n  vars: 6,\n  consts: [[1, \"p-galleria-thumbnail-wrapper\"], [1, \"p-galleria-thumbnail-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-galleria-thumbnail-items-container\", 3, \"ngStyle\"], [1, \"p-galleria-thumbnail-items\", 3, \"transitionend\", \"touchstart\", \"touchmove\", \"touchend\"], [\"itemsContainer\", \"\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\"], [3, \"ngClass\"], [1, \"p-galleria-thumbnail-item-content\", 3, \"click\", \"keydown.enter\"], [\"type\", \"thumbnail\", 3, \"item\", \"templates\"]],\n  template: function GalleriaThumbnails_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n      i0.ɵɵtemplate(2, GalleriaThumbnails_button_2_Template, 2, 8, \"button\", 2);\n      i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4, 5);\n      i0.ɵɵlistener(\"transitionend\", function GalleriaThumbnails_Template_div_transitionend_4_listener() {\n        return ctx.onTransitionEnd();\n      })(\"touchstart\", function GalleriaThumbnails_Template_div_touchstart_4_listener($event) {\n        return ctx.onTouchStart($event);\n      })(\"touchmove\", function GalleriaThumbnails_Template_div_touchmove_4_listener($event) {\n        return ctx.onTouchMove($event);\n      })(\"touchend\", function GalleriaThumbnails_Template_div_touchend_4_listener($event) {\n        return ctx.onTouchEnd($event);\n      });\n      i0.ɵɵtemplate(6, GalleriaThumbnails_div_6_Template, 3, 9, \"div\", 6);\n      i0.ɵɵelementEnd()();\n      i0.ɵɵtemplate(7, GalleriaThumbnails_button_7_Template, 2, 8, \"button\", 2);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c15, ctx.isVertical ? ctx.contentHeight : \"\"));\n      i0.ɵɵadvance(3);\n      i0.ɵɵproperty(\"ngForOf\", ctx.value);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgStyle, i3.Ripple, GalleriaItemSlot],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaThumbnails, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaThumbnails',\n      template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-prev-icon pi': true, 'pi-chevron-left': !this.isVertical, 'pi-chevron-up': this.isVertical}\"></span>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{'height': isVertical ? contentHeight : ''}\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\"\n                        (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" (touchend)=\"onTouchEnd($event)\">\n                        <div *ngFor=\"let item of value; let index = index;\" [ngClass]=\"{'p-galleria-thumbnail-item': true, 'p-galleria-thumbnail-item-current': activeIndex === index, 'p-galleria-thumbnail-item-active': isItemActive(index),\n                            'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index, 'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index }\">\n                            <div class=\"p-galleria-thumbnail-item-content\" [attr.tabindex]=\"getTabIndex(index)\" (click)=\"onItemClick(index)\" (keydown.enter)=\"onItemClick(index)\">\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\" [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-next-icon pi': true, 'pi-chevron-right': !this.isVertical, 'pi-chevron-down': this.isVertical}\"></span>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    containerId: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    isVertical: [{\n      type: Input\n    }],\n    slideShowActive: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    contentHeight: [{\n      type: Input\n    }],\n    showThumbnailNavigators: [{\n      type: Input\n    }],\n    templates: [{\n      type: Input\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\n\nclass GalleriaModule {}\n\nGalleriaModule.ɵfac = function GalleriaModule_Factory(t) {\n  return new (t || GalleriaModule)();\n};\n\nGalleriaModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: GalleriaModule\n});\nGalleriaModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, SharedModule, RippleModule, CommonModule, SharedModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule],\n      exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n      declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Galleria, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaThumbnails };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "Output", "ContentChildren", "NgModule", "i2", "CommonModule", "i1", "PrimeTemplate", "SharedModule", "ZIndexUtils", "UniqueComponentId", "<PERSON><PERSON><PERSON><PERSON>", "i3", "RippleModule", "trigger", "transition", "style", "animate", "Galleria", "constructor", "element", "cd", "config", "fullScreen", "numVisible", "showItemNavigators", "showThumbnailNavigators", "showItemNavigatorsOnHover", "changeItemOnIndicatorHover", "circular", "autoPlay", "transitionInterval", "showThumbnails", "thumbnailsPosition", "verticalThumbnailViewPortHeight", "showIndicators", "showIndicatorsOnItem", "indicatorsPosition", "baseZIndex", "showTransitionOptions", "hideTransitionOptions", "activeIndexChange", "visibleChange", "_visible", "_activeIndex", "maskVisible", "activeIndex", "visible", "ngAfterContentInit", "templates", "for<PERSON>ach", "item", "getType", "headerFacet", "template", "footer<PERSON><PERSON><PERSON>", "indicatorFacet", "captionFacet", "ngOnChanges", "simpleChanges", "_a", "value", "currentValue", "length", "onMaskHide", "emit", "onActiveItemChange", "index", "onAnimationStart", "event", "toState", "enableModality", "addClass", "mask", "nativeElement", "onAnimationEnd", "disableModality", "document", "body", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "zIndex", "modal", "removeClass", "clear", "ngOnDestroy", "ɵfac", "ElementRef", "ChangeDetectorRef", "PrimeNGConfig", "ɵcmp", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "GalleriaContent", "transform", "opacity", "type", "args", "selector", "animations", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "id", "responsiveOptions", "maskClass", "containerClass", "containerStyle", "galleria", "maskHide", "activeItemChange", "slideShowActicve", "slideShowActive", "galleriaClass", "thumbnailsPosClass", "getPositionClass", "indicatorPosClass", "startSlideShow", "interval", "setInterval", "onActiveIndexChange", "stopSlideShow", "clearInterval", "preClassName", "position", "positions", "pos", "find", "isVertical", "<PERSON><PERSON><PERSON>", "GalleriaItemSlot", "GalleriaItem", "GalleriaThumbnails", "_item", "context", "$implicit", "contentTemplate", "NgTemplateOutlet", "activeItem", "ngOnInit", "next", "nextItemIndex", "prev", "prevItemIndex", "stopTheSlideShow", "navForward", "e", "cancelable", "preventDefault", "navBackward", "onIndicatorClick", "onIndicatorMouseEnter", "onIndicatorKeyDown", "isNavForwardDisabled", "isNavBackwardDisabled", "isIndicatorItemActive", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentHeight", "startPos", "thumbnailsStyle", "sortedResponsiveOptions", "totalShiftedItems", "page", "_numVisible", "d_numVisible", "_oldNumVisible", "_oldactiveIndex", "createStyle", "bindDocumentListeners", "ngAfterContentChecked", "itemsContainer", "getMedianItemIndex", "ngAfterViewInit", "calculatePosition", "createElement", "append<PERSON><PERSON><PERSON>", "innerHTML", "containerId", "sort", "data1", "data2", "value1", "breakpoint", "value2", "result", "localeCompare", "undefined", "numeric", "i", "res", "windowWidth", "window", "innerWidth", "matchedResponsiveData", "parseInt", "getTabIndex", "isItemActive", "getTotalPageNumber", "step", "diff", "onItemClick", "selectedItemIndex", "dir", "changePageOnTouch", "Math", "floor", "onTransitionEnd", "onTouchEnd", "<PERSON><PERSON><PERSON>", "changedTouches", "pageY", "y", "pageX", "x", "onTouchMove", "onTouchStart", "firstItemAciveIndex", "lastItemActiveIndex", "documentResizeListener", "addEventListener", "unbindDocumentListeners", "removeEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "GalleriaModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-galleria.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, Output, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { ZIndexUtils, UniqueComponentId } from 'primeng/utils';\nimport { DomHandler } from 'primeng/dom';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { trigger, transition, style, animate } from '@angular/animations';\n\nclass Galleria {\n    constructor(element, cd, config) {\n        this.element = element;\n        this.cd = cd;\n        this.config = config;\n        this.fullScreen = false;\n        this.numVisible = 3;\n        this.showItemNavigators = false;\n        this.showThumbnailNavigators = true;\n        this.showItemNavigatorsOnHover = false;\n        this.changeItemOnIndicatorHover = false;\n        this.circular = false;\n        this.autoPlay = false;\n        this.transitionInterval = 4000;\n        this.showThumbnails = true;\n        this.thumbnailsPosition = \"bottom\";\n        this.verticalThumbnailViewPortHeight = \"300px\";\n        this.showIndicators = false;\n        this.showIndicatorsOnItem = false;\n        this.indicatorsPosition = \"bottom\";\n        this.baseZIndex = 0;\n        this.showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n        this.hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n        this.activeIndexChange = new EventEmitter();\n        this.visibleChange = new EventEmitter();\n        this._visible = false;\n        this._activeIndex = 0;\n        this.maskVisible = false;\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    ;\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    get visible() {\n        return this._visible;\n    }\n    ;\n    set visible(visible) {\n        this._visible = visible;\n        if (this._visible && !this.maskVisible) {\n            this.maskVisible = true;\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerFacet = item.template;\n                    break;\n                case 'footer':\n                    this.footerFacet = item.template;\n                    break;\n                case 'indicator':\n                    this.indicatorFacet = item.template;\n                    break;\n                case 'caption':\n                    this.captionFacet = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnChanges(simpleChanges) {\n        var _a;\n        if (simpleChanges.value && ((_a = simpleChanges.value.currentValue) === null || _a === void 0 ? void 0 : _a.length) < this.numVisible) {\n            this.numVisible = simpleChanges.value.currentValue.length;\n        }\n    }\n    onMaskHide() {\n        this.visible = false;\n        this.visibleChange.emit(false);\n    }\n    onActiveItemChange(index) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeIndexChange.emit(index);\n        }\n    }\n    onAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.enableModality();\n                break;\n            case 'void':\n                DomHandler.addClass(this.mask.nativeElement, 'p-component-overlay-leave');\n                break;\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.disableModality();\n                break;\n        }\n    }\n    enableModality() {\n        DomHandler.addClass(document.body, 'p-overflow-hidden');\n        this.cd.markForCheck();\n        if (this.mask) {\n            ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n        }\n    }\n    disableModality() {\n        DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        this.maskVisible = false;\n        this.cd.markForCheck();\n        if (this.mask) {\n            ZIndexUtils.clear(this.mask.nativeElement);\n        }\n    }\n    ngOnDestroy() {\n        if (this.fullScreen) {\n            DomHandler.removeClass(document.body, 'p-overflow-hidden');\n        }\n        if (this.mask) {\n            this.disableModality();\n        }\n    }\n}\nGalleria.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Galleria, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\nGalleria.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Galleria, selector: \"p-galleria\", inputs: { activeIndex: \"activeIndex\", fullScreen: \"fullScreen\", id: \"id\", value: \"value\", numVisible: \"numVisible\", responsiveOptions: \"responsiveOptions\", showItemNavigators: \"showItemNavigators\", showThumbnailNavigators: \"showThumbnailNavigators\", showItemNavigatorsOnHover: \"showItemNavigatorsOnHover\", changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\", circular: \"circular\", autoPlay: \"autoPlay\", transitionInterval: \"transitionInterval\", showThumbnails: \"showThumbnails\", thumbnailsPosition: \"thumbnailsPosition\", verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\", showIndicators: \"showIndicators\", showIndicatorsOnItem: \"showIndicatorsOnItem\", indicatorsPosition: \"indicatorsPosition\", baseZIndex: \"baseZIndex\", maskClass: \"maskClass\", containerClass: \"containerClass\", containerStyle: \"containerStyle\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", visible: \"visible\" }, outputs: { activeIndexChange: \"activeIndexChange\", visibleChange: \"visibleChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"mask\", first: true, predicate: [\"mask\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div *ngIf=\"fullScreen;else windowed\">\n            <div *ngIf=\"maskVisible\" #mask [ngClass]=\"{'p-galleria-mask p-component-overlay p-component-overlay-enter':true, 'p-galleria-visible': this.visible}\" [class]=\"maskClass\">\n                <p-galleriaContent *ngIf=\"visible\" [@animation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (maskHide)=\"onMaskHide()\" (activeItemChange)=\"onActiveItemChange($event)\" [ngStyle]=\"containerStyle\"></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `, isInline: true, styles: [\".p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i2.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(function () { return GalleriaContent; }), selector: \"p-galleriaContent\", inputs: [\"activeIndex\", \"value\", \"numVisible\"], outputs: [\"maskHide\", \"activeItemChange\"] }], animations: [\n        trigger('animation', [\n            transition('void => visible', [\n                style({ transform: 'scale(0.7)', opacity: 0 }),\n                animate('{{showTransitionParams}}')\n            ]),\n            transition('visible => void', [\n                animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))\n            ])\n        ])\n    ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Galleria, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-galleria', template: `\n        <div *ngIf=\"fullScreen;else windowed\">\n            <div *ngIf=\"maskVisible\" #mask [ngClass]=\"{'p-galleria-mask p-component-overlay p-component-overlay-enter':true, 'p-galleria-visible': this.visible}\" [class]=\"maskClass\">\n                <p-galleriaContent *ngIf=\"visible\" [@animation]=\"{value: 'visible', params: {showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions}}\" (@animation.start)=\"onAnimationStart($event)\" (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (maskHide)=\"onMaskHide()\" (activeItemChange)=\"onActiveItemChange($event)\" [ngStyle]=\"containerStyle\"></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `, animations: [\n                        trigger('animation', [\n                            transition('void => visible', [\n                                style({ transform: 'scale(0.7)', opacity: 0 }),\n                                animate('{{showTransitionParams}}')\n                            ]),\n                            transition('visible => void', [\n                                animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))\n                            ])\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }]; }, propDecorators: { activeIndex: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], showItemNavigators: [{\n                type: Input\n            }], showThumbnailNavigators: [{\n                type: Input\n            }], showItemNavigatorsOnHover: [{\n                type: Input\n            }], changeItemOnIndicatorHover: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], transitionInterval: [{\n                type: Input\n            }], showThumbnails: [{\n                type: Input\n            }], thumbnailsPosition: [{\n                type: Input\n            }], verticalThumbnailViewPortHeight: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], showIndicatorsOnItem: [{\n                type: Input\n            }], indicatorsPosition: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], maskClass: [{\n                type: Input\n            }], containerClass: [{\n                type: Input\n            }], containerStyle: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], mask: [{\n                type: ViewChild,\n                args: ['mask']\n            }], visible: [{\n                type: Input\n            }], activeIndexChange: [{\n                type: Output\n            }], visibleChange: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass GalleriaContent {\n    constructor(galleria, cd) {\n        this.galleria = galleria;\n        this.cd = cd;\n        this.value = [];\n        this.maskHide = new EventEmitter();\n        this.activeItemChange = new EventEmitter();\n        this.id = this.galleria.id || UniqueComponentId();\n        this.slideShowActicve = false;\n        this._activeIndex = 0;\n        this.slideShowActive = true;\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    ;\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    galleriaClass() {\n        const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n        const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n        return (this.galleria.containerClass ? this.galleria.containerClass + \" \" : '') + (thumbnailsPosClass ? thumbnailsPosClass + \" \" : '') + (indicatorPosClass ? indicatorPosClass + \" \" : '');\n    }\n    startSlideShow() {\n        this.interval = setInterval(() => {\n            let activeIndex = (this.galleria.circular && (this.value.length - 1) === this.activeIndex) ? 0 : (this.activeIndex + 1);\n            this.onActiveIndexChange(activeIndex);\n            this.activeIndex = activeIndex;\n        }, this.galleria.transitionInterval);\n        this.slideShowActive = true;\n    }\n    stopSlideShow() {\n        if (this.interval) {\n            clearInterval(this.interval);\n        }\n        this.slideShowActive = false;\n    }\n    getPositionClass(preClassName, position) {\n        const positions = ['top', 'left', 'bottom', 'right'];\n        const pos = positions.find(item => item === position);\n        return pos ? `${preClassName}-${pos}` : '';\n    }\n    isVertical() {\n        return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n    }\n    onActiveIndexChange(index) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeItemChange.emit(this.activeIndex);\n        }\n    }\n}\nGalleriaContent.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaContent, deps: [{ token: Galleria }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nGalleriaContent.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: GalleriaContent, selector: \"p-galleriaContent\", inputs: { activeIndex: \"activeIndex\", value: \"value\", numVisible: \"numVisible\" }, outputs: { maskHide: \"maskHide\", activeItemChange: \"activeItemChange\" }, ngImport: i0, template: `\n        <div [attr.id]=\"id\" *ngIf=\"value && value.length > 0\" [ngClass]=\"{'p-galleria p-component': true, 'p-galleria-fullscreen': this.galleria.fullScreen,\n            'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem, 'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen}\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\" [class]=\"galleriaClass()\">\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple>\n                <span class=\"p-galleria-close-icon pi pi-times\"></span>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\">\n                <p-galleriaItem [value]=\"value\" [activeIndex]=\"activeIndex\" [circular]=\"galleria.circular\" [templates]=\"galleria.templates\" (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\" [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\" [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\" [showItemNavigators]=\"galleria.showItemNavigators\" [autoPlay]=\"galleria.autoPlay\" [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaItem>\n\n                <p-galleriaThumbnails *ngIf=\"galleria.showThumbnails\" [containerId]=\"id\" [value]=\"value\" (onActiveIndexChange)=\"onActiveIndexChange($event)\" [activeIndex]=\"activeIndex\" [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\" [responsiveOptions]=\"galleria.responsiveOptions\" [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\" [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\" [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(function () { return i2.NgClass; }), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgIf; }), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i2.NgStyle; }), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(function () { return i3.Ripple; }), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(function () { return GalleriaItemSlot; }), selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }, { kind: \"component\", type: i0.forwardRef(function () { return GalleriaItem; }), selector: \"p-galleriaItem\", inputs: [\"circular\", \"value\", \"showItemNavigators\", \"showIndicators\", \"slideShowActive\", \"changeItemOnIndicatorHover\", \"autoPlay\", \"templates\", \"indicatorFacet\", \"captionFacet\", \"activeIndex\"], outputs: [\"startSlideShow\", \"stopSlideShow\", \"onActiveIndexChange\"] }, { kind: \"component\", type: i0.forwardRef(function () { return GalleriaThumbnails; }), selector: \"p-galleriaThumbnails\", inputs: [\"containerId\", \"value\", \"isVertical\", \"slideShowActive\", \"circular\", \"responsiveOptions\", \"contentHeight\", \"showThumbnailNavigators\", \"templates\", \"numVisible\", \"activeIndex\"], outputs: [\"onActiveIndexChange\", \"stopSlideShow\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaContent',\n                    template: `\n        <div [attr.id]=\"id\" *ngIf=\"value && value.length > 0\" [ngClass]=\"{'p-galleria p-component': true, 'p-galleria-fullscreen': this.galleria.fullScreen,\n            'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem, 'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen}\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\" [class]=\"galleriaClass()\">\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple>\n                <span class=\"p-galleria-close-icon pi pi-times\"></span>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\">\n                <p-galleriaItem [value]=\"value\" [activeIndex]=\"activeIndex\" [circular]=\"galleria.circular\" [templates]=\"galleria.templates\" (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\" [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\" [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\" [showItemNavigators]=\"galleria.showItemNavigators\" [autoPlay]=\"galleria.autoPlay\" [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaItem>\n\n                <p-galleriaThumbnails *ngIf=\"galleria.showThumbnails\" [containerId]=\"id\" [value]=\"value\" (onActiveIndexChange)=\"onActiveIndexChange($event)\" [activeIndex]=\"activeIndex\" [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\" [responsiveOptions]=\"galleria.responsiveOptions\" [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\" [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\" [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\" (stopSlideShow)=\"stopSlideShow()\"></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: function () { return [{ type: Galleria }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { activeIndex: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], maskHide: [{\n                type: Output\n            }], activeItemChange: [{\n                type: Output\n            }] } });\nclass GalleriaItemSlot {\n    get item() {\n        return this._item;\n    }\n    ;\n    set item(item) {\n        this._item = item;\n        if (this.templates) {\n            this.templates.forEach((item) => {\n                if (item.getType() === this.type) {\n                    switch (this.type) {\n                        case 'item':\n                        case 'caption':\n                        case 'thumbnail':\n                            this.context = { $implicit: this.item };\n                            this.contentTemplate = item.template;\n                            break;\n                    }\n                }\n            });\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            if (item.getType() === this.type) {\n                switch (this.type) {\n                    case 'item':\n                    case 'caption':\n                    case 'thumbnail':\n                        this.context = { $implicit: this.item };\n                        this.contentTemplate = item.template;\n                        break;\n                    case 'indicator':\n                        this.context = { $implicit: this.index };\n                        this.contentTemplate = item.template;\n                        break;\n                    default:\n                        this.context = {};\n                        this.contentTemplate = item.template;\n                        break;\n                }\n            }\n        });\n    }\n}\nGalleriaItemSlot.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaItemSlot, deps: [], target: i0.ɵɵFactoryTarget.Component });\nGalleriaItemSlot.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: GalleriaItemSlot, selector: \"p-galleriaItemSlot\", inputs: { templates: \"templates\", index: \"index\", item: \"item\", type: \"type\" }, ngImport: i0, template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaItemSlot, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaItemSlot',\n                    template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], propDecorators: { templates: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], item: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }] } });\nclass GalleriaItem {\n    constructor() {\n        this.circular = false;\n        this.showItemNavigators = false;\n        this.showIndicators = true;\n        this.slideShowActive = true;\n        this.changeItemOnIndicatorHover = true;\n        this.autoPlay = false;\n        this.startSlideShow = new EventEmitter();\n        this.stopSlideShow = new EventEmitter();\n        this.onActiveIndexChange = new EventEmitter();\n        this._activeIndex = 0;\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    ;\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n        this.activeItem = this.value[this._activeIndex];\n    }\n    ngOnInit() {\n        if (this.autoPlay) {\n            this.startSlideShow.emit();\n        }\n    }\n    next() {\n        let nextItemIndex = this.activeIndex + 1;\n        let activeIndex = this.circular && this.value.length - 1 === this.activeIndex\n            ? 0\n            : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n    prev() {\n        let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n        let activeIndex = this.circular && this.activeIndex === 0\n            ? this.value.length - 1\n            : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n    navForward(e) {\n        this.stopTheSlideShow();\n        this.next();\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e) {\n        this.stopTheSlideShow();\n        this.prev();\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onIndicatorClick(index) {\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n    }\n    onIndicatorMouseEnter(index) {\n        if (this.changeItemOnIndicatorHover) {\n            this.stopTheSlideShow();\n            this.onActiveIndexChange.emit(index);\n        }\n    }\n    onIndicatorKeyDown(index) {\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n    }\n    isNavForwardDisabled() {\n        return !this.circular && this.activeIndex === (this.value.length - 1);\n    }\n    isNavBackwardDisabled() {\n        return !this.circular && this.activeIndex === 0;\n    }\n    isIndicatorItemActive(index) {\n        return this.activeIndex === index;\n    }\n}\nGalleriaItem.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaItem, deps: [], target: i0.ɵɵFactoryTarget.Component });\nGalleriaItem.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: GalleriaItem, selector: \"p-galleriaItem\", inputs: { circular: \"circular\", value: \"value\", showItemNavigators: \"showItemNavigators\", showIndicators: \"showIndicators\", slideShowActive: \"slideShowActive\", changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\", autoPlay: \"autoPlay\", templates: \"templates\", indicatorFacet: \"indicatorFacet\", captionFacet: \"captionFacet\", activeIndex: \"activeIndex\" }, outputs: { startSlideShow: \"startSlideShow\", stopSlideShow: \"stopSlideShow\", onActiveIndexChange: \"onActiveIndexChange\" }, ngImport: i0, template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-prev-icon pi pi-chevron-left\"></span>\n                </button>\n                <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-next p-galleria-item-nav p-link': true,'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\"  [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-next-icon pi pi-chevron-right\"></span>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li *ngFor=\"let item of value; let index = index;\" tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\" (mouseenter)=\"onIndicatorMouseEnter(index)\" (keydown.enter)=\"onIndicatorKeyDown(index)\"\n                    [ngClass]=\"{'p-galleria-indicator': true,'p-highlight': isIndicatorItemActive(index)}\">\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\">\n                    </button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }, { kind: \"component\", type: GalleriaItemSlot, selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaItem, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaItem',\n                    template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-prev-icon pi pi-chevron-left\"></span>\n                </button>\n                <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                <button *ngIf=\"showItemNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-item-next p-galleria-item-nav p-link': true,'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\"  [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span class=\"p-galleria-item-next-icon pi pi-chevron-right\"></span>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li *ngFor=\"let item of value; let index = index;\" tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\" (mouseenter)=\"onIndicatorMouseEnter(index)\" (keydown.enter)=\"onIndicatorKeyDown(index)\"\n                    [ngClass]=\"{'p-galleria-indicator': true,'p-highlight': isIndicatorItemActive(index)}\">\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\">\n                    </button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], propDecorators: { circular: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], showItemNavigators: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], slideShowActive: [{\n                type: Input\n            }], changeItemOnIndicatorHover: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], templates: [{\n                type: Input\n            }], indicatorFacet: [{\n                type: Input\n            }], captionFacet: [{\n                type: Input\n            }], startSlideShow: [{\n                type: Output\n            }], stopSlideShow: [{\n                type: Output\n            }], onActiveIndexChange: [{\n                type: Output\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass GalleriaThumbnails {\n    constructor(cd) {\n        this.cd = cd;\n        this.isVertical = false;\n        this.slideShowActive = false;\n        this.circular = false;\n        this.contentHeight = \"300px\";\n        this.showThumbnailNavigators = true;\n        this.onActiveIndexChange = new EventEmitter();\n        this.stopSlideShow = new EventEmitter();\n        this.startPos = null;\n        this.thumbnailsStyle = null;\n        this.sortedResponsiveOptions = null;\n        this.totalShiftedItems = 0;\n        this.page = 0;\n        this._numVisible = 0;\n        this.d_numVisible = 0;\n        this._oldNumVisible = 0;\n        this._activeIndex = 0;\n        this._oldactiveIndex = 0;\n    }\n    get numVisible() {\n        return this._numVisible;\n    }\n    ;\n    set numVisible(numVisible) {\n        this._numVisible = numVisible;\n        this._oldNumVisible = this.d_numVisible;\n        this.d_numVisible = numVisible;\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    ;\n    set activeIndex(activeIndex) {\n        this._oldactiveIndex = this._activeIndex;\n        this._activeIndex = activeIndex;\n    }\n    ngOnInit() {\n        this.createStyle();\n        if (this.responsiveOptions) {\n            this.bindDocumentListeners();\n        }\n    }\n    ngAfterContentChecked() {\n        let totalShiftedItems = this.totalShiftedItems;\n        if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n            if (this._activeIndex <= this.getMedianItemIndex()) {\n                totalShiftedItems = 0;\n            }\n            else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n                totalShiftedItems = this.d_numVisible - this.value.length;\n            }\n            else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n                totalShiftedItems = (this._activeIndex * -1) + this.getMedianItemIndex() + 1;\n            }\n            else {\n                totalShiftedItems = (this._activeIndex * -1) + this.getMedianItemIndex();\n            }\n            if (totalShiftedItems !== this.totalShiftedItems) {\n                this.totalShiftedItems = totalShiftedItems;\n            }\n            if (this.itemsContainer && this.itemsContainer.nativeElement) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            }\n            if (this._oldactiveIndex !== this._activeIndex) {\n                DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n                this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n            }\n            this._oldactiveIndex = this._activeIndex;\n            this._oldNumVisible = this.d_numVisible;\n        }\n    }\n    ngAfterViewInit() {\n        this.calculatePosition();\n    }\n    createStyle() {\n        if (!this.thumbnailsStyle) {\n            this.thumbnailsStyle = document.createElement('style');\n            this.thumbnailsStyle.type = 'text/css';\n            document.body.appendChild(this.thumbnailsStyle);\n        }\n        let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${(100 / this.d_numVisible)}%\n            }\n        `;\n        if (this.responsiveOptions) {\n            this.sortedResponsiveOptions = [...this.responsiveOptions];\n            this.sortedResponsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n                if (value1 == null && value2 != null)\n                    result = -1;\n                else if (value1 != null && value2 == null)\n                    result = 1;\n                else if (value1 == null && value2 == null)\n                    result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string')\n                    result = value1.localeCompare(value2, undefined, { numeric: true });\n                else\n                    result = (value1 < value2) ? -1 : (value1 > value2) ? 1 : 0;\n                return -1 * result;\n            });\n            for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                let res = this.sortedResponsiveOptions[i];\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${(100 / res.numVisible)}%\n                        }\n                    }\n                `;\n            }\n        }\n        this.thumbnailsStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n        if (this.itemsContainer && this.sortedResponsiveOptions) {\n            let windowWidth = window.innerWidth;\n            let matchedResponsiveData = {\n                numVisible: this._numVisible\n            };\n            for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                let res = this.sortedResponsiveOptions[i];\n                if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                    matchedResponsiveData = res;\n                }\n            }\n            if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n                this.d_numVisible = matchedResponsiveData.numVisible;\n                this.cd.markForCheck();\n            }\n        }\n    }\n    getTabIndex(index) {\n        return this.isItemActive(index) ? 0 : null;\n    }\n    navForward(e) {\n        this.stopTheSlideShow();\n        let nextItemIndex = this._activeIndex + 1;\n        if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && ((-1 * this.totalShiftedItems) < this.getTotalPageNumber() - 1 || this.circular)) {\n            this.step(-1);\n        }\n        let activeIndex = this.circular && (this.value.length - 1) === this._activeIndex ? 0 : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e) {\n        this.stopTheSlideShow();\n        let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n        let diff = prevItemIndex + this.totalShiftedItems;\n        if ((this.d_numVisible - diff - 1) > this.getMedianItemIndex() && ((-1 * this.totalShiftedItems) !== 0 || this.circular)) {\n            this.step(1);\n        }\n        let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onItemClick(index) {\n        this.stopTheSlideShow();\n        let selectedItemIndex = index;\n        if (selectedItemIndex !== this._activeIndex) {\n            const diff = selectedItemIndex + this.totalShiftedItems;\n            let dir = 0;\n            if (selectedItemIndex < this._activeIndex) {\n                dir = (this.d_numVisible - diff - 1) - this.getMedianItemIndex();\n                if (dir > 0 && (-1 * this.totalShiftedItems) !== 0) {\n                    this.step(dir);\n                }\n            }\n            else {\n                dir = this.getMedianItemIndex() - diff;\n                if (dir < 0 && (-1 * this.totalShiftedItems) < this.getTotalPageNumber() - 1) {\n                    this.step(dir);\n                }\n            }\n            this.activeIndex = selectedItemIndex;\n            this.onActiveIndexChange.emit(this.activeIndex);\n        }\n    }\n    step(dir) {\n        let totalShiftedItems = this.totalShiftedItems + dir;\n        if (dir < 0 && (-1 * totalShiftedItems) + this.d_numVisible > (this.value.length - 1)) {\n            totalShiftedItems = this.d_numVisible - this.value.length;\n        }\n        else if (dir > 0 && totalShiftedItems > 0) {\n            totalShiftedItems = 0;\n        }\n        if (this.circular) {\n            if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n                totalShiftedItems = 0;\n            }\n            else if (dir > 0 && this._activeIndex === 0) {\n                totalShiftedItems = this.d_numVisible - this.value.length;\n            }\n        }\n        if (this.itemsContainer) {\n            DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n        this.totalShiftedItems = totalShiftedItems;\n    }\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n    changePageOnTouch(e, diff) {\n        if (diff < 0) { // left\n            this.navForward(e);\n        }\n        else { // right\n            this.navBackward(e);\n        }\n    }\n    getTotalPageNumber() {\n        return this.value.length > this.d_numVisible ? (this.value.length - this.d_numVisible) + 1 : 0;\n    }\n    getMedianItemIndex() {\n        let index = Math.floor(this.d_numVisible / 2);\n        return (this.d_numVisible % 2) ? index : index - 1;\n    }\n    onTransitionEnd() {\n        if (this.itemsContainer && this.itemsContainer.nativeElement) {\n            DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transition = '';\n        }\n    }\n    onTouchEnd(e) {\n        let touchobj = e.changedTouches[0];\n        if (this.isVertical) {\n            this.changePageOnTouch(e, (touchobj.pageY - this.startPos.y));\n        }\n        else {\n            this.changePageOnTouch(e, (touchobj.pageX - this.startPos.x));\n        }\n    }\n    onTouchMove(e) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onTouchStart(e) {\n        let touchobj = e.changedTouches[0];\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n    isNavBackwardDisabled() {\n        return (!this.circular && this._activeIndex === 0) || (this.value.length <= this.d_numVisible);\n    }\n    isNavForwardDisabled() {\n        return (!this.circular && this._activeIndex === (this.value.length - 1)) || (this.value.length <= this.d_numVisible);\n    }\n    firstItemAciveIndex() {\n        return this.totalShiftedItems * -1;\n    }\n    lastItemActiveIndex() {\n        return this.firstItemAciveIndex() + this.d_numVisible - 1;\n    }\n    isItemActive(index) {\n        return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n    }\n    bindDocumentListeners() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = () => {\n                this.calculatePosition();\n            };\n            window.addEventListener('resize', this.documentResizeListener);\n        }\n    }\n    unbindDocumentListeners() {\n        if (this.documentResizeListener) {\n            window.removeEventListener('resize', this.documentResizeListener);\n            this.documentResizeListener = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n        if (this.thumbnailsStyle) {\n            this.thumbnailsStyle.parentNode.removeChild(this.thumbnailsStyle);\n        }\n    }\n}\nGalleriaThumbnails.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaThumbnails, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nGalleriaThumbnails.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: GalleriaThumbnails, selector: \"p-galleriaThumbnails\", inputs: { containerId: \"containerId\", value: \"value\", isVertical: \"isVertical\", slideShowActive: \"slideShowActive\", circular: \"circular\", responsiveOptions: \"responsiveOptions\", contentHeight: \"contentHeight\", showThumbnailNavigators: \"showThumbnailNavigators\", templates: \"templates\", numVisible: \"numVisible\", activeIndex: \"activeIndex\" }, outputs: { onActiveIndexChange: \"onActiveIndexChange\", stopSlideShow: \"stopSlideShow\" }, viewQueries: [{ propertyName: \"itemsContainer\", first: true, predicate: [\"itemsContainer\"], descendants: true }], ngImport: i0, template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-prev-icon pi': true, 'pi-chevron-left': !this.isVertical, 'pi-chevron-up': this.isVertical}\"></span>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{'height': isVertical ? contentHeight : ''}\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\"\n                        (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" (touchend)=\"onTouchEnd($event)\">\n                        <div *ngFor=\"let item of value; let index = index;\" [ngClass]=\"{'p-galleria-thumbnail-item': true, 'p-galleria-thumbnail-item-current': activeIndex === index, 'p-galleria-thumbnail-item-active': isItemActive(index),\n                            'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index, 'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index }\">\n                            <div class=\"p-galleria-thumbnail-item-content\" [attr.tabindex]=\"getTabIndex(index)\" (click)=\"onItemClick(index)\" (keydown.enter)=\"onItemClick(index)\">\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\" [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-next-icon pi': true, 'pi-chevron-right': !this.isVertical, 'pi-chevron-down': this.isVertical}\"></span>\n                </button>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i3.Ripple, selector: \"[pRipple]\" }, { kind: \"component\", type: GalleriaItemSlot, selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaThumbnails, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaThumbnails',\n                    template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled()}\" (click)=\"navBackward($event)\" [disabled]=\"isNavBackwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-prev-icon pi': true, 'pi-chevron-left': !this.isVertical, 'pi-chevron-up': this.isVertical}\"></span>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{'height': isVertical ? contentHeight : ''}\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\"\n                        (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" (touchend)=\"onTouchEnd($event)\">\n                        <div *ngFor=\"let item of value; let index = index;\" [ngClass]=\"{'p-galleria-thumbnail-item': true, 'p-galleria-thumbnail-item-current': activeIndex === index, 'p-galleria-thumbnail-item-active': isItemActive(index),\n                            'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index, 'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index }\">\n                            <div class=\"p-galleria-thumbnail-item-content\" [attr.tabindex]=\"getTabIndex(index)\" (click)=\"onItemClick(index)\" (keydown.enter)=\"onItemClick(index)\">\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button *ngIf=\"showThumbnailNavigators\" type=\"button\" [ngClass]=\"{'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled()}\" (click)=\"navForward($event)\" [disabled]=\"isNavForwardDisabled()\" pRipple>\n                    <span [ngClass]=\"{'p-galleria-thumbnail-next-icon pi': true, 'pi-chevron-right': !this.isVertical, 'pi-chevron-down': this.isVertical}\"></span>\n                </button>\n            </div>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }]; }, propDecorators: { containerId: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], isVertical: [{\n                type: Input\n            }], slideShowActive: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], contentHeight: [{\n                type: Input\n            }], showThumbnailNavigators: [{\n                type: Input\n            }], templates: [{\n                type: Input\n            }], onActiveIndexChange: [{\n                type: Output\n            }], stopSlideShow: [{\n                type: Output\n            }], itemsContainer: [{\n                type: ViewChild,\n                args: ['itemsContainer']\n            }], numVisible: [{\n                type: Input\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass GalleriaModule {\n}\nGalleriaModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nGalleriaModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaModule, declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails], imports: [CommonModule, SharedModule, RippleModule], exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule] });\nGalleriaModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaModule, imports: [CommonModule, SharedModule, RippleModule, CommonModule, SharedModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: GalleriaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, RippleModule],\n                    exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n                    declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Galleria, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaThumbnails };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,SAArF,EAAgGC,MAAhG,EAAwGC,eAAxG,EAAyHC,QAAzH,QAAyI,eAAzI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,aAApB;AACA,SAASC,aAAT,EAAwBC,YAAxB,QAA4C,aAA5C;AACA,SAASC,WAAT,EAAsBC,iBAAtB,QAA+C,eAA/C;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,OAAO,KAAKC,EAAZ,MAAoB,gBAApB;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,OAAT,EAAkBC,UAAlB,EAA8BC,KAA9B,EAAqCC,OAArC,QAAoD,qBAApD;;;;;;;;;;;;;;;;;;;gBA2H2FvB,E;;IAAAA,EAI3E,0C;IAJ2EA,EAI8F;MAJ9FA,EAI8F;MAAA,eAJ9FA,EAI8F;MAAA,OAJ9FA,EAIkH,6CAApB;IAAA;MAJ9FA,EAI8F;MAAA,eAJ9FA,EAI8F;MAAA,OAJ9FA,EAI+J,2CAAjE;IAAA;MAJ9FA,EAI8F;MAAA,eAJ9FA,EAI8F;MAAA,OAJ9FA,EAKW,iCADmF;IAAA;MAJ9FA,EAI8F;MAAA,gBAJ9FA,EAI8F;MAAA,OAJ9FA,EAK6C,gDADiD;IAAA,E;IAJ9FA,EAKoG,e;;;;mBALpGA,E;IAAAA,EAIxC,0BAJwCA,EAIxC,yBAJwCA,EAIxC,mN;;;;;;;;;;;;;IAJwCA,EAG/E,+B;IAH+EA,EAI3E,gG;IAJ2EA,EAM/E,e;;;;mBAN+EA,E;IAAAA,EAGuE,6B;IAHvEA,EAGhD,uBAHgDA,EAGhD,yC;IAHgDA,EAIvD,a;IAJuDA,EAIvD,mC;;;;;;IAJuDA,EAEnF,yB;IAFmFA,EAG/E,6D;IAH+EA,EAOnF,e;;;;mBAPmFA,E;IAAAA,EAGzE,a;IAHyEA,EAGzE,uC;;;;;;iBAHyEA,E;;IAAAA,EAU/E,0C;IAV+EA,EAUU;MAVVA,EAUU;MAAA,gBAVVA,EAUU;MAAA,OAVVA,EAU8B,gDAApB;IAAA,E;IAVVA,EAU0D,e;;;;mBAV1DA,E;IAAAA,EAU5D,sG;;;;;;gBAV4DA,E;;IAAAA,EA0K/E,+B;IA1K+EA,EA0KG;MA1KHA,EA0KG;MAAA,eA1KHA,EA0KG;MAAA,OA1KHA,EA0KY,oCAAT;IAAA,E;IA1KHA,EA2K3E,wB;IA3K2EA,EA4K/E,e;;;;;;IA5K+EA,EA6K/E,6B;IA7K+EA,EA8K3E,uC;IA9K2EA,EA+K/E,e;;;;mBA/K+EA,E;IAAAA,EA8KzC,a;IA9KyCA,EA8KzC,mD;;;;;;gBA9KyCA,E;;IAAAA,EAsL3E,8C;IAtL2EA,EAsLc;MAtLdA,EAsLc;MAAA,eAtLdA,EAsLc;MAAA,OAtLdA,EAsLqC,gDAAvB;IAAA;MAtLdA,EAsLc;MAAA,eAtLdA,EAsLc;MAAA,OAtLdA,EAyLlB,oCAHgC;IAAA,E;IAtLdA,EAyLD,e;;;;mBAzLCA,E;IAAAA,EAsLrB,0d;;;;;;IAtLqBA,EA2L/E,6B;IA3L+EA,EA4L3E,uC;IA5L2EA,EA6L/E,e;;;;mBA7L+EA,E;IAAAA,EA4LzC,a;IA5LyCA,EA4LzC,mD;;;;;;;;;;;;;;;;;;;iBA5LyCA,E;;IAAAA,EAuKnF,4B;IAvKmFA,EA0K/E,0E;IA1K+EA,EA6K/E,oE;IA7K+EA,EAgL/E,oD;IAhL+EA,EAiLiD;MAjLjDA,EAiLiD;MAAA,gBAjLjDA,EAiLiD;MAAA,OAjLjDA,EAiLwE,iDAAvB;IAAA;MAjLjDA,EAiLiD;MAAA,gBAjLjDA,EAiLiD;MAAA,OAjLjDA,EAoLrD,sCAHsG;IAAA;MAjLjDA,EAiLiD;MAAA,gBAjLjDA,EAiLiD;MAAA,OAjLjDA,EAoLlB,qCAHmE;IAAA,E;IAjLjDA,EAoLD,e;IApLCA,EAsL3E,uG;IAtL2EA,EA0L/E,e;IA1L+EA,EA2L/E,oE;IA3L+EA,EA8LnF,e;;;;mBA9LmFA,E;IAAAA,EAyKf,mC;IAzKeA,EAuK7B,uBAvK6BA,EAuK7B,iPAvK6BA,EAuK7B,0B;IAvK6BA,EAuK9E,6B;IAvK8EA,EA0KtE,a;IA1KsEA,EA0KtE,+C;IA1KsEA,EA6KzE,a;IA7KyEA,EA6KzE,6E;IA7KyEA,EAiL3D,a;IAjL2DA,EAiL3D,0f;IAjL2DA,EAsLpD,a;IAtLoDA,EAsLpD,mD;IAtLoDA,EA2LzE,a;IA3LyEA,EA2LzE,6E;;;;;;IA3LyEA,EA2R/E,sB;;;;;;IA3R+EA,EA0RnF,2B;IA1RmFA,EA2R/E,gG;IA3R+EA,EA4RnF,wB;;;;mBA5RmFA,E;IAAAA,EA2RhE,a;IA3RgEA,EA2RhE,kG;;;;;;;;;;;;;gBA3RgEA,E;;IAAAA,EAyY3E,+B;IAzY2EA,EAyYwF;MAzYxFA,EAyYwF;MAAA,eAzYxFA,EAyYwF;MAAA,OAzYxFA,EAyYiG,wCAAT;IAAA,E;IAzYxFA,EA0YvE,wB;IA1YuEA,EA2Y3E,e;;;;mBA3Y2EA,E;IAAAA,EAyY1B,uBAzY0BA,EAyY1B,qG;;;;;;;;;;;;;gBAzY0BA,E;;IAAAA,EA6Y3E,+B;IA7Y2EA,EA6YsF;MA7YtFA,EA6YsF;MAAA,eA7YtFA,EA6YsF;MAAA,OA7YtFA,EA6Y+F,uCAAT;IAAA,E;IA7YtFA,EA8YvE,wB;IA9YuEA,EA+Y3E,e;;;;mBA/Y2EA,E;IAAAA,EA6Y1B,uBA7Y0BA,EA6Y1B,mG;;;;;;IA7Y0BA,EAgZ3E,4B;IAhZ2EA,EAiZvE,uC;IAjZuEA,EAkZ3E,e;;;;mBAlZ2EA,E;IAAAA,EAiZpC,a;IAjZoCA,EAiZpC,qE;;;;;;IAjZoCA,EAwZvE,2B;;;;;;;;;;;;;iBAxZuEA,E;;IAAAA,EAqZ3E,4B;IArZ2EA,EAsZvE;MAAA,oBAtZuEA,EAsZvE;MAAA;MAAA,gBAtZuEA,EAsZvE;MAAA,OAtZuEA,EAsZ9D,iDAAT;IAAA;MAAA,oBAtZuEA,EAsZvE;MAAA;MAAA,gBAtZuEA,EAsZvE;MAAA,OAtZuEA,EAsZvB,sDAAhD;IAAA;MAAA,oBAtZuEA,EAsZvE;MAAA;MAAA,gBAtZuEA,EAsZvE;MAAA,OAtZuEA,EAsZwB,mDAA/F;IAAA,E;IAtZuEA,EAwZvE,4E;IAxZuEA,EA0ZvE,uC;IA1ZuEA,EA2Z3E,e;;;;;mBA3Z2EA,E;IAAAA,EAuZvE,uBAvZuEA,EAuZvE,kE;IAvZuEA,EAwZnB,a;IAxZmBA,EAwZnB,2C;IAxZmBA,EA0ZlC,a;IA1ZkCA,EA0ZlC,8D;;;;;;IA1ZkCA,EAoZ/E,4B;IApZ+EA,EAqZ3E,+D;IArZ2EA,EA4Z/E,e;;;;mBA5Z+EA,E;IAAAA,EAqZtD,a;IArZsDA,EAqZtD,oC;;;;;;;;;;;;;;;;;;;;;;;gBArZsDA,E;;IAAAA,EAqwB3E,+B;IArwB2EA,EAqwB8E;MArwB9EA,EAqwB8E;MAAA,eArwB9EA,EAqwB8E;MAAA,OArwB9EA,EAqwBuF,wCAAT;IAAA,E;IArwB9EA,EAswBvE,wB;IAtwBuEA,EAuwB3E,e;;;;mBAvwB2EA,E;IAAAA,EAqwBrB,uBArwBqBA,EAqwBrB,sG;IArwBqBA,EAswBjE,a;IAtwBiEA,EAswBjE,uBAtwBiEA,EAswBjE,iE;;;;;;;;;;;;;;;;gBAtwBiEA,E;;IAAAA,EA2wBnE,yC;IA3wBmEA,EA6wBqB;MAAA,oBA7wBrBA,EA6wBqB;MAAA;MAAA,eA7wBrBA,EA6wBqB;MAAA,OA7wBrBA,EA6wB8B,0CAAT;IAAA;MAAA,oBA7wBrBA,EA6wBqB;MAAA;MAAA,gBA7wBrBA,EA6wBqB;MAAA,OA7wBrBA,EA6wBmE,2CAA9C;IAAA,E;IA7wBrBA,EA8wB3D,uC;IA9wB2DA,EA+wB/D,iB;;;;;;mBA/wB+DA,E;IAAAA,EA2wBf,uBA3wBeA,EA2wBf,gL;IA3wBeA,EA6wBhB,a;IA7wBgBA,EA6wBhB,sD;IA7wBgBA,EA8wBtB,a;IA9wBsBA,EA8wBtB,2D;;;;;;;;;;;;;;;;;;;;;iBA9wBsBA,E;;IAAAA,EAmxB3E,+B;IAnxB2EA,EAmxB6E;MAnxB7EA,EAmxB6E;MAAA,gBAnxB7EA,EAmxB6E;MAAA,OAnxB7EA,EAmxBsF,wCAAT;IAAA,E;IAnxB7EA,EAoxBvE,wB;IApxBuEA,EAqxB3E,e;;;;mBArxB2EA,E;IAAAA,EAmxBrB,uBAnxBqBA,EAmxBrB,oG;IAnxBqBA,EAoxBjE,a;IApxBiEA,EAoxBjE,uBApxBiEA,EAoxBjE,iE;;;;;;;;;;AA74B1B,MAAMwB,QAAN,CAAe;EACXC,WAAW,CAACC,OAAD,EAAUC,EAAV,EAAcC,MAAd,EAAsB;IAC7B,KAAKF,OAAL,GAAeA,OAAf;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,MAAL,GAAcA,MAAd;IACA,KAAKC,UAAL,GAAkB,KAAlB;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,kBAAL,GAA0B,KAA1B;IACA,KAAKC,uBAAL,GAA+B,IAA/B;IACA,KAAKC,yBAAL,GAAiC,KAAjC;IACA,KAAKC,0BAAL,GAAkC,KAAlC;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,QAAL,GAAgB,KAAhB;IACA,KAAKC,kBAAL,GAA0B,IAA1B;IACA,KAAKC,cAAL,GAAsB,IAAtB;IACA,KAAKC,kBAAL,GAA0B,QAA1B;IACA,KAAKC,+BAAL,GAAuC,OAAvC;IACA,KAAKC,cAAL,GAAsB,KAAtB;IACA,KAAKC,oBAAL,GAA4B,KAA5B;IACA,KAAKC,kBAAL,GAA0B,QAA1B;IACA,KAAKC,UAAL,GAAkB,CAAlB;IACA,KAAKC,qBAAL,GAA6B,kCAA7B;IACA,KAAKC,qBAAL,GAA6B,kCAA7B;IACA,KAAKC,iBAAL,GAAyB,IAAI9C,YAAJ,EAAzB;IACA,KAAK+C,aAAL,GAAqB,IAAI/C,YAAJ,EAArB;IACA,KAAKgD,QAAL,GAAgB,KAAhB;IACA,KAAKC,YAAL,GAAoB,CAApB;IACA,KAAKC,WAAL,GAAmB,KAAnB;EACH;;EACc,IAAXC,WAAW,GAAG;IACd,OAAO,KAAKF,YAAZ;EACH;;EAEc,IAAXE,WAAW,CAACA,WAAD,EAAc;IACzB,KAAKF,YAAL,GAAoBE,WAApB;EACH;;EACU,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKJ,QAAZ;EACH;;EAEU,IAAPI,OAAO,CAACA,OAAD,EAAU;IACjB,KAAKJ,QAAL,GAAgBI,OAAhB;;IACA,IAAI,KAAKJ,QAAL,IAAiB,CAAC,KAAKE,WAA3B,EAAwC;MACpC,KAAKA,WAAL,GAAmB,IAAnB;IACH;EACJ;;EACDG,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACC,OAAL,EAAR;QACI,KAAK,QAAL;UACI,KAAKC,WAAL,GAAmBF,IAAI,CAACG,QAAxB;UACA;;QACJ,KAAK,QAAL;UACI,KAAKC,WAAL,GAAmBJ,IAAI,CAACG,QAAxB;UACA;;QACJ,KAAK,WAAL;UACI,KAAKE,cAAL,GAAsBL,IAAI,CAACG,QAA3B;UACA;;QACJ,KAAK,SAAL;UACI,KAAKG,YAAL,GAAoBN,IAAI,CAACG,QAAzB;UACA;MAZR;IAcH,CAfD;EAgBH;;EACDI,WAAW,CAACC,aAAD,EAAgB;IACvB,IAAIC,EAAJ;;IACA,IAAID,aAAa,CAACE,KAAd,IAAuB,CAAC,CAACD,EAAE,GAAGD,aAAa,CAACE,KAAd,CAAoBC,YAA1B,MAA4C,IAA5C,IAAoDF,EAAE,KAAK,KAAK,CAAhE,GAAoE,KAAK,CAAzE,GAA6EA,EAAE,CAACG,MAAjF,IAA2F,KAAKvC,UAA3H,EAAuI;MACnI,KAAKA,UAAL,GAAkBmC,aAAa,CAACE,KAAd,CAAoBC,YAApB,CAAiCC,MAAnD;IACH;EACJ;;EACDC,UAAU,GAAG;IACT,KAAKjB,OAAL,GAAe,KAAf;IACA,KAAKL,aAAL,CAAmBuB,IAAnB,CAAwB,KAAxB;EACH;;EACDC,kBAAkB,CAACC,KAAD,EAAQ;IACtB,IAAI,KAAKrB,WAAL,KAAqBqB,KAAzB,EAAgC;MAC5B,KAAKrB,WAAL,GAAmBqB,KAAnB;MACA,KAAK1B,iBAAL,CAAuBwB,IAAvB,CAA4BE,KAA5B;IACH;EACJ;;EACDC,gBAAgB,CAACC,KAAD,EAAQ;IACpB,QAAQA,KAAK,CAACC,OAAd;MACI,KAAK,SAAL;QACI,KAAKC,cAAL;QACA;;MACJ,KAAK,MAAL;QACI5D,UAAU,CAAC6D,QAAX,CAAoB,KAAKC,IAAL,CAAUC,aAA9B,EAA6C,2BAA7C;QACA;IANR;EAQH;;EACDC,cAAc,CAACN,KAAD,EAAQ;IAClB,QAAQA,KAAK,CAACC,OAAd;MACI,KAAK,MAAL;QACI,KAAKM,eAAL;QACA;IAHR;EAKH;;EACDL,cAAc,GAAG;IACb5D,UAAU,CAAC6D,QAAX,CAAoBK,QAAQ,CAACC,IAA7B,EAAmC,mBAAnC;IACA,KAAKzD,EAAL,CAAQ0D,YAAR;;IACA,IAAI,KAAKN,IAAT,EAAe;MACXhE,WAAW,CAACuE,GAAZ,CAAgB,OAAhB,EAAyB,KAAKP,IAAL,CAAUC,aAAnC,EAAkD,KAAKpC,UAAL,IAAmB,KAAKhB,MAAL,CAAY2D,MAAZ,CAAmBC,KAAxF;IACH;EACJ;;EACDN,eAAe,GAAG;IACdjE,UAAU,CAACwE,WAAX,CAAuBN,QAAQ,CAACC,IAAhC,EAAsC,mBAAtC;IACA,KAAKjC,WAAL,GAAmB,KAAnB;IACA,KAAKxB,EAAL,CAAQ0D,YAAR;;IACA,IAAI,KAAKN,IAAT,EAAe;MACXhE,WAAW,CAAC2E,KAAZ,CAAkB,KAAKX,IAAL,CAAUC,aAA5B;IACH;EACJ;;EACDW,WAAW,GAAG;IACV,IAAI,KAAK9D,UAAT,EAAqB;MACjBZ,UAAU,CAACwE,WAAX,CAAuBN,QAAQ,CAACC,IAAhC,EAAsC,mBAAtC;IACH;;IACD,IAAI,KAAKL,IAAT,EAAe;MACX,KAAKG,eAAL;IACH;EACJ;;AAvHU;;AAyHf1D,QAAQ,CAACoE,IAAT;EAAA,iBAAqGpE,QAArG,EAA2FxB,EAA3F,mBAA+HA,EAAE,CAAC6F,UAAlI,GAA2F7F,EAA3F,mBAAyJA,EAAE,CAAC8F,iBAA5J,GAA2F9F,EAA3F,mBAA0LY,EAAE,CAACmF,aAA7L;AAAA;;AACAvE,QAAQ,CAACwE,IAAT,kBAD2FhG,EAC3F;EAAA,MAAyFwB,QAAzF;EAAA;EAAA;IAAA;MAD2FxB,EAC3F,0BAAiuCa,aAAjuC;IAAA;;IAAA;MAAA;;MAD2Fb,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;IAAA;;IAAA;MAAA;;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA,WAD2FA,EAC3F;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAEnF,uDADR;MAD2FA,EASnF,8EATmFA,EASnF,wBARR;IAAA;;IAAA;MAAA,YAD2FA,EAC3F;;MAD2FA,EAE7E,oDADd;IAAA;EAAA;EAAA;IAAA,QAWghJU,EAAE,CAACuF,OAXnhJ,EAWqpJvF,EAAE,CAACwF,IAXxpJ,EAWgyJxF,EAAE,CAACyF,OAXnyJ,EAW45JC,eAX55J;EAAA;EAAA;EAAA;EAAA;IAAA,WAW0jK,CACljKhF,OAAO,CAAC,WAAD,EAAc,CACjBC,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,KAAK,CAAC;MAAE+E,SAAS,EAAE,YAAb;MAA2BC,OAAO,EAAE;IAApC,CAAD,CADqB,EAE1B/E,OAAO,CAAC,0BAAD,CAFmB,CAApB,CADO,EAKjBF,UAAU,CAAC,iBAAD,EAAoB,CAC1BE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;MAAE+E,SAAS,EAAE,YAAb;MAA2BC,OAAO,EAAE;IAApC,CAAD,CAAlC,CADmB,CAApB,CALO,CAAd,CAD2iK;EAX1jK;EAAA;AAAA;;AAsBA;EAAA,mDAvB2FtG,EAuB3F,mBAA2FwB,QAA3F,EAAiH,CAAC;IACtG+E,IAAI,EAAErG,SADgG;IAEtGsG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0B7C,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAXmB;MAWZ8C,UAAU,EAAE,CACKtF,OAAO,CAAC,WAAD,EAAc,CACjBC,UAAU,CAAC,iBAAD,EAAoB,CAC1BC,KAAK,CAAC;QAAE+E,SAAS,EAAE,YAAb;QAA2BC,OAAO,EAAE;MAApC,CAAD,CADqB,EAE1B/E,OAAO,CAAC,0BAAD,CAFmB,CAApB,CADO,EAKjBF,UAAU,CAAC,iBAAD,EAAoB,CAC1BE,OAAO,CAAC,0BAAD,EAA6BD,KAAK,CAAC;QAAE+E,SAAS,EAAE,YAAb;QAA2BC,OAAO,EAAE;MAApC,CAAD,CAAlC,CADmB,CAApB,CALO,CAAd,CADZ,CAXA;MAqBIK,eAAe,EAAExG,uBAAuB,CAACyG,MArB7C;MAqBqDC,aAAa,EAAEzG,iBAAiB,CAAC0G,IArBtF;MAqB4FC,IAAI,EAAE;QAC7F,SAAS;MADoF,CArBlG;MAuBIC,MAAM,EAAE,CAAC,g6IAAD;IAvBZ,CAAD;EAFgG,CAAD,CAAjH,EA0B4B,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAEvG,EAAE,CAAC6F;IAAX,CAAD,EAA0B;MAAEU,IAAI,EAAEvG,EAAE,CAAC8F;IAAX,CAA1B,EAA0D;MAAES,IAAI,EAAE3F,EAAE,CAACmF;IAAX,CAA1D,CAAP;EAA+F,CA1BzI,EA0B2J;IAAE3C,WAAW,EAAE,CAAC;MAC3JmD,IAAI,EAAElG;IADqJ,CAAD,CAAf;IAE3IwB,UAAU,EAAE,CAAC;MACb0E,IAAI,EAAElG;IADO,CAAD,CAF+H;IAI3I4G,EAAE,EAAE,CAAC;MACLV,IAAI,EAAElG;IADD,CAAD,CAJuI;IAM3I8D,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAElG;IADE,CAAD,CANoI;IAQ3IyB,UAAU,EAAE,CAAC;MACbyE,IAAI,EAAElG;IADO,CAAD,CAR+H;IAU3I6G,iBAAiB,EAAE,CAAC;MACpBX,IAAI,EAAElG;IADc,CAAD,CAVwH;IAY3I0B,kBAAkB,EAAE,CAAC;MACrBwE,IAAI,EAAElG;IADe,CAAD,CAZuH;IAc3I2B,uBAAuB,EAAE,CAAC;MAC1BuE,IAAI,EAAElG;IADoB,CAAD,CAdkH;IAgB3I4B,yBAAyB,EAAE,CAAC;MAC5BsE,IAAI,EAAElG;IADsB,CAAD,CAhBgH;IAkB3I6B,0BAA0B,EAAE,CAAC;MAC7BqE,IAAI,EAAElG;IADuB,CAAD,CAlB+G;IAoB3I8B,QAAQ,EAAE,CAAC;MACXoE,IAAI,EAAElG;IADK,CAAD,CApBiI;IAsB3I+B,QAAQ,EAAE,CAAC;MACXmE,IAAI,EAAElG;IADK,CAAD,CAtBiI;IAwB3IgC,kBAAkB,EAAE,CAAC;MACrBkE,IAAI,EAAElG;IADe,CAAD,CAxBuH;IA0B3IiC,cAAc,EAAE,CAAC;MACjBiE,IAAI,EAAElG;IADW,CAAD,CA1B2H;IA4B3IkC,kBAAkB,EAAE,CAAC;MACrBgE,IAAI,EAAElG;IADe,CAAD,CA5BuH;IA8B3ImC,+BAA+B,EAAE,CAAC;MAClC+D,IAAI,EAAElG;IAD4B,CAAD,CA9B0G;IAgC3IoC,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAElG;IADW,CAAD,CAhC2H;IAkC3IqC,oBAAoB,EAAE,CAAC;MACvB6D,IAAI,EAAElG;IADiB,CAAD,CAlCqH;IAoC3IsC,kBAAkB,EAAE,CAAC;MACrB4D,IAAI,EAAElG;IADe,CAAD,CApCuH;IAsC3IuC,UAAU,EAAE,CAAC;MACb2D,IAAI,EAAElG;IADO,CAAD,CAtC+H;IAwC3I8G,SAAS,EAAE,CAAC;MACZZ,IAAI,EAAElG;IADM,CAAD,CAxCgI;IA0C3I+G,cAAc,EAAE,CAAC;MACjBb,IAAI,EAAElG;IADW,CAAD,CA1C2H;IA4C3IgH,cAAc,EAAE,CAAC;MACjBd,IAAI,EAAElG;IADW,CAAD,CA5C2H;IA8C3IwC,qBAAqB,EAAE,CAAC;MACxB0D,IAAI,EAAElG;IADkB,CAAD,CA9CoH;IAgD3IyC,qBAAqB,EAAE,CAAC;MACxByD,IAAI,EAAElG;IADkB,CAAD,CAhDoH;IAkD3I0E,IAAI,EAAE,CAAC;MACPwB,IAAI,EAAEjG,SADC;MAEPkG,IAAI,EAAE,CAAC,MAAD;IAFC,CAAD,CAlDqI;IAqD3InD,OAAO,EAAE,CAAC;MACVkD,IAAI,EAAElG;IADI,CAAD,CArDkI;IAuD3I0C,iBAAiB,EAAE,CAAC;MACpBwD,IAAI,EAAEhG;IADc,CAAD,CAvDwH;IAyD3IyC,aAAa,EAAE,CAAC;MAChBuD,IAAI,EAAEhG;IADU,CAAD,CAzD4H;IA2D3IgD,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAE/F,eADM;MAEZgG,IAAI,EAAE,CAAC3F,aAAD;IAFM,CAAD;EA3DgI,CA1B3J;AAAA;;AAyFA,MAAMuF,eAAN,CAAsB;EAClB3E,WAAW,CAAC6F,QAAD,EAAW3F,EAAX,EAAe;IACtB,KAAK2F,QAAL,GAAgBA,QAAhB;IACA,KAAK3F,EAAL,GAAUA,EAAV;IACA,KAAKwC,KAAL,GAAa,EAAb;IACA,KAAKoD,QAAL,GAAgB,IAAItH,YAAJ,EAAhB;IACA,KAAKuH,gBAAL,GAAwB,IAAIvH,YAAJ,EAAxB;IACA,KAAKgH,EAAL,GAAU,KAAKK,QAAL,CAAcL,EAAd,IAAoBjG,iBAAiB,EAA/C;IACA,KAAKyG,gBAAL,GAAwB,KAAxB;IACA,KAAKvE,YAAL,GAAoB,CAApB;IACA,KAAKwE,eAAL,GAAuB,IAAvB;EACH;;EACc,IAAXtE,WAAW,GAAG;IACd,OAAO,KAAKF,YAAZ;EACH;;EAEc,IAAXE,WAAW,CAACA,WAAD,EAAc;IACzB,KAAKF,YAAL,GAAoBE,WAApB;EACH;;EACDuE,aAAa,GAAG;IACZ,MAAMC,kBAAkB,GAAG,KAAKN,QAAL,CAAchF,cAAd,IAAgC,KAAKuF,gBAAL,CAAsB,uBAAtB,EAA+C,KAAKP,QAAL,CAAc/E,kBAA7D,CAA3D;IACA,MAAMuF,iBAAiB,GAAG,KAAKR,QAAL,CAAc7E,cAAd,IAAgC,KAAKoF,gBAAL,CAAsB,uBAAtB,EAA+C,KAAKP,QAAL,CAAc3E,kBAA7D,CAA1D;IACA,OAAO,CAAC,KAAK2E,QAAL,CAAcF,cAAd,GAA+B,KAAKE,QAAL,CAAcF,cAAd,GAA+B,GAA9D,GAAoE,EAArE,KAA4EQ,kBAAkB,GAAGA,kBAAkB,GAAG,GAAxB,GAA8B,EAA5H,KAAmIE,iBAAiB,GAAGA,iBAAiB,GAAG,GAAvB,GAA6B,EAAjL,CAAP;EACH;;EACDC,cAAc,GAAG;IACb,KAAKC,QAAL,GAAgBC,WAAW,CAAC,MAAM;MAC9B,IAAI7E,WAAW,GAAI,KAAKkE,QAAL,CAAcnF,QAAd,IAA2B,KAAKgC,KAAL,CAAWE,MAAX,GAAoB,CAArB,KAA4B,KAAKjB,WAA5D,GAA2E,CAA3E,GAAgF,KAAKA,WAAL,GAAmB,CAArH;MACA,KAAK8E,mBAAL,CAAyB9E,WAAzB;MACA,KAAKA,WAAL,GAAmBA,WAAnB;IACH,CAJ0B,EAIxB,KAAKkE,QAAL,CAAcjF,kBAJU,CAA3B;IAKA,KAAKqF,eAAL,GAAuB,IAAvB;EACH;;EACDS,aAAa,GAAG;IACZ,IAAI,KAAKH,QAAT,EAAmB;MACfI,aAAa,CAAC,KAAKJ,QAAN,CAAb;IACH;;IACD,KAAKN,eAAL,GAAuB,KAAvB;EACH;;EACDG,gBAAgB,CAACQ,YAAD,EAAeC,QAAf,EAAyB;IACrC,MAAMC,SAAS,GAAG,CAAC,KAAD,EAAQ,MAAR,EAAgB,QAAhB,EAA0B,OAA1B,CAAlB;IACA,MAAMC,GAAG,GAAGD,SAAS,CAACE,IAAV,CAAehF,IAAI,IAAIA,IAAI,KAAK6E,QAAhC,CAAZ;IACA,OAAOE,GAAG,GAAI,GAAEH,YAAa,IAAGG,GAAI,EAA1B,GAA8B,EAAxC;EACH;;EACDE,UAAU,GAAG;IACT,OAAO,KAAKpB,QAAL,CAAc/E,kBAAd,KAAqC,MAArC,IAA+C,KAAK+E,QAAL,CAAc/E,kBAAd,KAAqC,OAA3F;EACH;;EACD2F,mBAAmB,CAACzD,KAAD,EAAQ;IACvB,IAAI,KAAKrB,WAAL,KAAqBqB,KAAzB,EAAgC;MAC5B,KAAKrB,WAAL,GAAmBqB,KAAnB;MACA,KAAK+C,gBAAL,CAAsBjD,IAAtB,CAA2B,KAAKnB,WAAhC;IACH;EACJ;;AAnDiB;;AAqDtBgD,eAAe,CAACR,IAAhB;EAAA,iBAA4GQ,eAA5G,EArK2FpG,EAqK3F,mBAA6IwB,QAA7I,GArK2FxB,EAqK3F,mBAAkKA,EAAE,CAAC8F,iBAArK;AAAA;;AACAM,eAAe,CAACJ,IAAhB,kBAtK2FhG,EAsK3F;EAAA,MAAgGoG,eAAhG;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAtK2FpG,EAuKnF,+DADR;IAAA;;IAAA;MAtK2FA,EAuK9D,sDAD7B;IAAA;EAAA;EAAA;IAAA,QAyBoGU,EAAE,CAACuF,OAzBvG,EAyByOvF,EAAE,CAACwF,IAzB5O,EAyBoXxF,EAAE,CAACyF,OAzBvX,EAyBgfjF,EAAE,CAACyH,MAzBnf,EAyBslBC,gBAzBtlB,EAyB4vBC,YAzB5vB,EAyBinCC,kBAzBjnC;EAAA;EAAA;EAAA;AAAA;;AA0BA;EAAA,mDAhM2F9I,EAgM3F,mBAA2FoG,eAA3F,EAAwH,CAAC;IAC7GG,IAAI,EAAErG,SADuG;IAE7GsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBADX;MAEC7C,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA3BmB;MA4BC+C,eAAe,EAAExG,uBAAuB,CAACyG;IA5B1C,CAAD;EAFuG,CAAD,CAAxH,EAgC4B,YAAY;IAAE,OAAO,CAAC;MAAEL,IAAI,EAAE/E;IAAR,CAAD,EAAqB;MAAE+E,IAAI,EAAEvG,EAAE,CAAC8F;IAAX,CAArB,CAAP;EAA8D,CAhCxG,EAgC0H;IAAE1C,WAAW,EAAE,CAAC;MAC1HmD,IAAI,EAAElG;IADoH,CAAD,CAAf;IAE1G8D,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAElG;IADE,CAAD,CAFmG;IAI1GyB,UAAU,EAAE,CAAC;MACbyE,IAAI,EAAElG;IADO,CAAD,CAJ8F;IAM1GkH,QAAQ,EAAE,CAAC;MACXhB,IAAI,EAAEhG;IADK,CAAD,CANgG;IAQ1GiH,gBAAgB,EAAE,CAAC;MACnBjB,IAAI,EAAEhG;IADa,CAAD;EARwF,CAhC1H;AAAA;;AA2CA,MAAMqI,gBAAN,CAAuB;EACX,IAAJnF,IAAI,GAAG;IACP,OAAO,KAAKsF,KAAZ;EACH;;EAEO,IAAJtF,IAAI,CAACA,IAAD,EAAO;IACX,KAAKsF,KAAL,GAAatF,IAAb;;IACA,IAAI,KAAKF,SAAT,EAAoB;MAChB,KAAKA,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;QAC7B,IAAIA,IAAI,CAACC,OAAL,OAAmB,KAAK6C,IAA5B,EAAkC;UAC9B,QAAQ,KAAKA,IAAb;YACI,KAAK,MAAL;YACA,KAAK,SAAL;YACA,KAAK,WAAL;cACI,KAAKyC,OAAL,GAAe;gBAAEC,SAAS,EAAE,KAAKxF;cAAlB,CAAf;cACA,KAAKyF,eAAL,GAAuBzF,IAAI,CAACG,QAA5B;cACA;UANR;QAQH;MACJ,CAXD;IAYH;EACJ;;EACDN,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAeC,OAAf,CAAwBC,IAAD,IAAU;MAC7B,IAAIA,IAAI,CAACC,OAAL,OAAmB,KAAK6C,IAA5B,EAAkC;QAC9B,QAAQ,KAAKA,IAAb;UACI,KAAK,MAAL;UACA,KAAK,SAAL;UACA,KAAK,WAAL;YACI,KAAKyC,OAAL,GAAe;cAAEC,SAAS,EAAE,KAAKxF;YAAlB,CAAf;YACA,KAAKyF,eAAL,GAAuBzF,IAAI,CAACG,QAA5B;YACA;;UACJ,KAAK,WAAL;YACI,KAAKoF,OAAL,GAAe;cAAEC,SAAS,EAAE,KAAKxE;YAAlB,CAAf;YACA,KAAKyE,eAAL,GAAuBzF,IAAI,CAACG,QAA5B;YACA;;UACJ;YACI,KAAKoF,OAAL,GAAe,EAAf;YACA,KAAKE,eAAL,GAAuBzF,IAAI,CAACG,QAA5B;YACA;QAdR;MAgBH;IACJ,CAnBD;EAoBH;;AA3CkB;;AA6CvBgF,gBAAgB,CAAChD,IAAjB;EAAA,iBAA6GgD,gBAA7G;AAAA;;AACAA,gBAAgB,CAAC5C,IAAjB,kBAzR2FhG,EAyR3F;EAAA,MAAiG4I,gBAAjG;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAzR2F5I,EA0RnF,iFADR;IAAA;;IAAA;MAzR2FA,EA0RpE,wCADvB;IAAA;EAAA;EAAA,eAIiEU,EAAE,CAACwF,IAJpE,EAIqKxF,EAAE,CAACyI,gBAJxK;EAAA;EAAA;AAAA;;AAKA;EAAA,mDA9R2FnJ,EA8R3F,mBAA2F4I,gBAA3F,EAAyH,CAAC;IAC9GrC,IAAI,EAAErG,SADwG;IAE9GsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBADX;MAEC7C,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KANmB;MAOC+C,eAAe,EAAExG,uBAAuB,CAACyG;IAP1C,CAAD;EAFwG,CAAD,CAAzH,QAW4B;IAAErD,SAAS,EAAE,CAAC;MAC1BgD,IAAI,EAAElG;IADoB,CAAD,CAAb;IAEZoE,KAAK,EAAE,CAAC;MACR8B,IAAI,EAAElG;IADE,CAAD,CAFK;IAIZoD,IAAI,EAAE,CAAC;MACP8C,IAAI,EAAElG;IADC,CAAD,CAJM;IAMZkG,IAAI,EAAE,CAAC;MACPA,IAAI,EAAElG;IADC,CAAD;EANM,CAX5B;AAAA;;AAoBA,MAAMwI,YAAN,CAAmB;EACfpH,WAAW,GAAG;IACV,KAAKU,QAAL,GAAgB,KAAhB;IACA,KAAKJ,kBAAL,GAA0B,KAA1B;IACA,KAAKU,cAAL,GAAsB,IAAtB;IACA,KAAKiF,eAAL,GAAuB,IAAvB;IACA,KAAKxF,0BAAL,GAAkC,IAAlC;IACA,KAAKE,QAAL,GAAgB,KAAhB;IACA,KAAK2F,cAAL,GAAsB,IAAI9H,YAAJ,EAAtB;IACA,KAAKkI,aAAL,GAAqB,IAAIlI,YAAJ,EAArB;IACA,KAAKiI,mBAAL,GAA2B,IAAIjI,YAAJ,EAA3B;IACA,KAAKiD,YAAL,GAAoB,CAApB;EACH;;EACc,IAAXE,WAAW,GAAG;IACd,OAAO,KAAKF,YAAZ;EACH;;EAEc,IAAXE,WAAW,CAACA,WAAD,EAAc;IACzB,KAAKF,YAAL,GAAoBE,WAApB;IACA,KAAKgG,UAAL,GAAkB,KAAKjF,KAAL,CAAW,KAAKjB,YAAhB,CAAlB;EACH;;EACDmG,QAAQ,GAAG;IACP,IAAI,KAAKjH,QAAT,EAAmB;MACf,KAAK2F,cAAL,CAAoBxD,IAApB;IACH;EACJ;;EACD+E,IAAI,GAAG;IACH,IAAIC,aAAa,GAAG,KAAKnG,WAAL,GAAmB,CAAvC;IACA,IAAIA,WAAW,GAAG,KAAKjB,QAAL,IAAiB,KAAKgC,KAAL,CAAWE,MAAX,GAAoB,CAApB,KAA0B,KAAKjB,WAAhD,GACZ,CADY,GAEZmG,aAFN;IAGA,KAAKrB,mBAAL,CAAyB3D,IAAzB,CAA8BnB,WAA9B;EACH;;EACDoG,IAAI,GAAG;IACH,IAAIC,aAAa,GAAG,KAAKrG,WAAL,KAAqB,CAArB,GAAyB,KAAKA,WAAL,GAAmB,CAA5C,GAAgD,CAApE;IACA,IAAIA,WAAW,GAAG,KAAKjB,QAAL,IAAiB,KAAKiB,WAAL,KAAqB,CAAtC,GACZ,KAAKe,KAAL,CAAWE,MAAX,GAAoB,CADR,GAEZoF,aAFN;IAGA,KAAKvB,mBAAL,CAAyB3D,IAAzB,CAA8BnB,WAA9B;EACH;;EACDsG,gBAAgB,GAAG;IACf,IAAI,KAAKhC,eAAL,IAAwB,KAAKS,aAAjC,EAAgD;MAC5C,KAAKA,aAAL,CAAmB5D,IAAnB;IACH;EACJ;;EACDoF,UAAU,CAACC,CAAD,EAAI;IACV,KAAKF,gBAAL;IACA,KAAKJ,IAAL;;IACA,IAAIM,CAAC,IAAIA,CAAC,CAACC,UAAX,EAAuB;MACnBD,CAAC,CAACE,cAAF;IACH;EACJ;;EACDC,WAAW,CAACH,CAAD,EAAI;IACX,KAAKF,gBAAL;IACA,KAAKF,IAAL;;IACA,IAAII,CAAC,IAAIA,CAAC,CAACC,UAAX,EAAuB;MACnBD,CAAC,CAACE,cAAF;IACH;EACJ;;EACDE,gBAAgB,CAACvF,KAAD,EAAQ;IACpB,KAAKiF,gBAAL;IACA,KAAKxB,mBAAL,CAAyB3D,IAAzB,CAA8BE,KAA9B;EACH;;EACDwF,qBAAqB,CAACxF,KAAD,EAAQ;IACzB,IAAI,KAAKvC,0BAAT,EAAqC;MACjC,KAAKwH,gBAAL;MACA,KAAKxB,mBAAL,CAAyB3D,IAAzB,CAA8BE,KAA9B;IACH;EACJ;;EACDyF,kBAAkB,CAACzF,KAAD,EAAQ;IACtB,KAAKiF,gBAAL;IACA,KAAKxB,mBAAL,CAAyB3D,IAAzB,CAA8BE,KAA9B;EACH;;EACD0F,oBAAoB,GAAG;IACnB,OAAO,CAAC,KAAKhI,QAAN,IAAkB,KAAKiB,WAAL,KAAsB,KAAKe,KAAL,CAAWE,MAAX,GAAoB,CAAnE;EACH;;EACD+F,qBAAqB,GAAG;IACpB,OAAO,CAAC,KAAKjI,QAAN,IAAkB,KAAKiB,WAAL,KAAqB,CAA9C;EACH;;EACDiH,qBAAqB,CAAC5F,KAAD,EAAQ;IACzB,OAAO,KAAKrB,WAAL,KAAqBqB,KAA5B;EACH;;AAjFc;;AAmFnBoE,YAAY,CAACjD,IAAb;EAAA,iBAAyGiD,YAAzG;AAAA;;AACAA,YAAY,CAAC7C,IAAb,kBAtY2FhG,EAsY3F;EAAA,MAA6F6I,YAA7F;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAtY2F7I,EAuYnF,yCADR;MAtY2FA,EAyY3E,iEAHhB;MAtY2FA,EA4Y3E,sCANhB;MAtY2FA,EA6Y3E,iEAPhB;MAtY2FA,EAgZ3E,2DAVhB;MAtY2FA,EAmZ/E,eAbZ;MAtY2FA,EAoZ/E,yDAdZ;MAtY2FA,EA6ZnF,eAvBR;IAAA;;IAAA;MAtY2FA,EAyYlE,aAHzB;MAtY2FA,EAyYlE,2CAHzB;MAtY2FA,EA4Y3C,aANhD;MAtY2FA,EA4Y3C,+DANhD;MAtY2FA,EA6YlE,aAPzB;MAtY2FA,EA6YlE,2CAPzB;MAtY2FA,EAgZ1C,aAVjD;MAtY2FA,EAgZ1C,qCAVjD;MAtY2FA,EAoZ1E,aAdjB;MAtY2FA,EAoZ1E,uCAdjB;IAAA;EAAA;EAAA,eAwBiEU,EAAE,CAACuF,OAxBpE,EAwB+JvF,EAAE,CAAC4J,OAxBlK,EAwB4R5J,EAAE,CAACwF,IAxB/R,EAwBgYhF,EAAE,CAACyH,MAxBnY,EAwB+bC,gBAxB/b;EAAA;EAAA;AAAA;;AAyBA;EAAA,mDA/Z2F5I,EA+Z3F,mBAA2F6I,YAA3F,EAAqH,CAAC;IAC1GtC,IAAI,EAAErG,SADoG;IAE1GsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBADX;MAEC7C,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KA1BmB;MA2BC+C,eAAe,EAAExG,uBAAuB,CAACyG;IA3B1C,CAAD;EAFoG,CAAD,CAArH,QA+B4B;IAAEzE,QAAQ,EAAE,CAAC;MACzBoE,IAAI,EAAElG;IADmB,CAAD,CAAZ;IAEZ8D,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAElG;IADE,CAAD,CAFK;IAIZ0B,kBAAkB,EAAE,CAAC;MACrBwE,IAAI,EAAElG;IADe,CAAD,CAJR;IAMZoC,cAAc,EAAE,CAAC;MACjB8D,IAAI,EAAElG;IADW,CAAD,CANJ;IAQZqH,eAAe,EAAE,CAAC;MAClBnB,IAAI,EAAElG;IADY,CAAD,CARL;IAUZ6B,0BAA0B,EAAE,CAAC;MAC7BqE,IAAI,EAAElG;IADuB,CAAD,CAVhB;IAYZ+B,QAAQ,EAAE,CAAC;MACXmE,IAAI,EAAElG;IADK,CAAD,CAZE;IAcZkD,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAElG;IADM,CAAD,CAdC;IAgBZyD,cAAc,EAAE,CAAC;MACjByC,IAAI,EAAElG;IADW,CAAD,CAhBJ;IAkBZ0D,YAAY,EAAE,CAAC;MACfwC,IAAI,EAAElG;IADS,CAAD,CAlBF;IAoBZ0H,cAAc,EAAE,CAAC;MACjBxB,IAAI,EAAEhG;IADW,CAAD,CApBJ;IAsBZ4H,aAAa,EAAE,CAAC;MAChB5B,IAAI,EAAEhG;IADU,CAAD,CAtBH;IAwBZ2H,mBAAmB,EAAE,CAAC;MACtB3B,IAAI,EAAEhG;IADgB,CAAD,CAxBT;IA0BZ6C,WAAW,EAAE,CAAC;MACdmD,IAAI,EAAElG;IADQ,CAAD;EA1BD,CA/B5B;AAAA;;AA4DA,MAAMyI,kBAAN,CAAyB;EACrBrH,WAAW,CAACE,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;IACA,KAAK+G,UAAL,GAAkB,KAAlB;IACA,KAAKhB,eAAL,GAAuB,KAAvB;IACA,KAAKvF,QAAL,GAAgB,KAAhB;IACA,KAAKoI,aAAL,GAAqB,OAArB;IACA,KAAKvI,uBAAL,GAA+B,IAA/B;IACA,KAAKkG,mBAAL,GAA2B,IAAIjI,YAAJ,EAA3B;IACA,KAAKkI,aAAL,GAAqB,IAAIlI,YAAJ,EAArB;IACA,KAAKuK,QAAL,GAAgB,IAAhB;IACA,KAAKC,eAAL,GAAuB,IAAvB;IACA,KAAKC,uBAAL,GAA+B,IAA/B;IACA,KAAKC,iBAAL,GAAyB,CAAzB;IACA,KAAKC,IAAL,GAAY,CAAZ;IACA,KAAKC,WAAL,GAAmB,CAAnB;IACA,KAAKC,YAAL,GAAoB,CAApB;IACA,KAAKC,cAAL,GAAsB,CAAtB;IACA,KAAK7H,YAAL,GAAoB,CAApB;IACA,KAAK8H,eAAL,GAAuB,CAAvB;EACH;;EACa,IAAVlJ,UAAU,GAAG;IACb,OAAO,KAAK+I,WAAZ;EACH;;EAEa,IAAV/I,UAAU,CAACA,UAAD,EAAa;IACvB,KAAK+I,WAAL,GAAmB/I,UAAnB;IACA,KAAKiJ,cAAL,GAAsB,KAAKD,YAA3B;IACA,KAAKA,YAAL,GAAoBhJ,UAApB;EACH;;EACc,IAAXsB,WAAW,GAAG;IACd,OAAO,KAAKF,YAAZ;EACH;;EAEc,IAAXE,WAAW,CAACA,WAAD,EAAc;IACzB,KAAK4H,eAAL,GAAuB,KAAK9H,YAA5B;IACA,KAAKA,YAAL,GAAoBE,WAApB;EACH;;EACDiG,QAAQ,GAAG;IACP,KAAK4B,WAAL;;IACA,IAAI,KAAK/D,iBAAT,EAA4B;MACxB,KAAKgE,qBAAL;IACH;EACJ;;EACDC,qBAAqB,GAAG;IACpB,IAAIR,iBAAiB,GAAG,KAAKA,iBAA7B;;IACA,IAAI,CAAC,KAAKI,cAAL,KAAwB,KAAKD,YAA7B,IAA6C,KAAKE,eAAL,KAAyB,KAAK9H,YAA5E,KAA6F,KAAKkI,cAAtG,EAAsH;MAClH,IAAI,KAAKlI,YAAL,IAAqB,KAAKmI,kBAAL,EAAzB,EAAoD;QAChDV,iBAAiB,GAAG,CAApB;MACH,CAFD,MAGK,IAAI,KAAKxG,KAAL,CAAWE,MAAX,GAAoB,KAAKyG,YAAzB,GAAwC,KAAKO,kBAAL,EAAxC,GAAoE,KAAKnI,YAA7E,EAA2F;QAC5FyH,iBAAiB,GAAG,KAAKG,YAAL,GAAoB,KAAK3G,KAAL,CAAWE,MAAnD;MACH,CAFI,MAGA,IAAI,KAAKF,KAAL,CAAWE,MAAX,GAAoB,KAAKyG,YAAzB,GAAwC,KAAK5H,YAA7C,IAA6D,KAAK4H,YAAL,GAAoB,CAApB,KAA0B,CAA3F,EAA8F;QAC/FH,iBAAiB,GAAI,KAAKzH,YAAL,GAAoB,CAAC,CAAtB,GAA2B,KAAKmI,kBAAL,EAA3B,GAAuD,CAA3E;MACH,CAFI,MAGA;QACDV,iBAAiB,GAAI,KAAKzH,YAAL,GAAoB,CAAC,CAAtB,GAA2B,KAAKmI,kBAAL,EAA/C;MACH;;MACD,IAAIV,iBAAiB,KAAK,KAAKA,iBAA/B,EAAkD;QAC9C,KAAKA,iBAAL,GAAyBA,iBAAzB;MACH;;MACD,IAAI,KAAKS,cAAL,IAAuB,KAAKA,cAAL,CAAoBpG,aAA/C,EAA8D;QAC1D,KAAKoG,cAAL,CAAoBpG,aAApB,CAAkC1D,KAAlC,CAAwC+E,SAAxC,GAAoD,KAAKqC,UAAL,GAAmB,kBAAiBiC,iBAAiB,IAAI,MAAM,KAAKG,YAAf,CAA6B,OAAlF,GAA4F,eAAcH,iBAAiB,IAAI,MAAM,KAAKG,YAAf,CAA6B,UAA5M;MACH;;MACD,IAAI,KAAKE,eAAL,KAAyB,KAAK9H,YAAlC,EAAgD;QAC5CjC,UAAU,CAACwE,WAAX,CAAuB,KAAK2F,cAAL,CAAoBpG,aAA3C,EAA0D,gBAA1D;QACA,KAAKoG,cAAL,CAAoBpG,aAApB,CAAkC1D,KAAlC,CAAwCD,UAAxC,GAAqD,yBAArD;MACH;;MACD,KAAK2J,eAAL,GAAuB,KAAK9H,YAA5B;MACA,KAAK6H,cAAL,GAAsB,KAAKD,YAA3B;IACH;EACJ;;EACDQ,eAAe,GAAG;IACd,KAAKC,iBAAL;EACH;;EACDN,WAAW,GAAG;IACV,IAAI,CAAC,KAAKR,eAAV,EAA2B;MACvB,KAAKA,eAAL,GAAuBtF,QAAQ,CAACqG,aAAT,CAAuB,OAAvB,CAAvB;MACA,KAAKf,eAAL,CAAqBlE,IAArB,GAA4B,UAA5B;MACApB,QAAQ,CAACC,IAAT,CAAcqG,WAAd,CAA0B,KAAKhB,eAA/B;IACH;;IACD,IAAIiB,SAAS,GAAI;AACzB,eAAe,KAAKC,WAAY;AAChC,4BAA6B,MAAM,KAAKb,YAAc;AACtD;AACA,SAJQ;;IAKA,IAAI,KAAK5D,iBAAT,EAA4B;MACxB,KAAKwD,uBAAL,GAA+B,CAAC,GAAG,KAAKxD,iBAAT,CAA/B;MACA,KAAKwD,uBAAL,CAA6BkB,IAA7B,CAAkC,CAACC,KAAD,EAAQC,KAAR,KAAkB;QAChD,MAAMC,MAAM,GAAGF,KAAK,CAACG,UAArB;QACA,MAAMC,MAAM,GAAGH,KAAK,CAACE,UAArB;QACA,IAAIE,MAAM,GAAG,IAAb;QACA,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACIC,MAAM,GAAG,CAAC,CAAV,CADJ,KAEK,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAIH,MAAM,IAAI,IAAV,IAAkBE,MAAM,IAAI,IAAhC,EACDC,MAAM,GAAG,CAAT,CADC,KAEA,IAAI,OAAOH,MAAP,KAAkB,QAAlB,IAA8B,OAAOE,MAAP,KAAkB,QAApD,EACDC,MAAM,GAAGH,MAAM,CAACI,aAAP,CAAqBF,MAArB,EAA6BG,SAA7B,EAAwC;UAAEC,OAAO,EAAE;QAAX,CAAxC,CAAT,CADC,KAGDH,MAAM,GAAIH,MAAM,GAAGE,MAAV,GAAoB,CAAC,CAArB,GAA0BF,MAAM,GAAGE,MAAV,GAAoB,CAApB,GAAwB,CAA1D;QACJ,OAAO,CAAC,CAAD,GAAKC,MAAZ;MACH,CAfD;;MAgBA,KAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,uBAAL,CAA6BrG,MAAjD,EAAyDiI,CAAC,EAA1D,EAA8D;QAC1D,IAAIC,GAAG,GAAG,KAAK7B,uBAAL,CAA6B4B,CAA7B,CAAV;QACAZ,SAAS,IAAK;AAC9B,oDAAoDa,GAAG,CAACP,UAAW;AACnE,2BAA2B,KAAKL,WAAY;AAC5C,wCAAyC,MAAMY,GAAG,CAACzK,UAAY;AAC/D;AACA;AACA,iBANgB;MAOH;IACJ;;IACD,KAAK2I,eAAL,CAAqBiB,SAArB,GAAiCA,SAAjC;EACH;;EACDH,iBAAiB,GAAG;IAChB,IAAI,KAAKH,cAAL,IAAuB,KAAKV,uBAAhC,EAAyD;MACrD,IAAI8B,WAAW,GAAGC,MAAM,CAACC,UAAzB;MACA,IAAIC,qBAAqB,GAAG;QACxB7K,UAAU,EAAE,KAAK+I;MADO,CAA5B;;MAGA,KAAK,IAAIyB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK5B,uBAAL,CAA6BrG,MAAjD,EAAyDiI,CAAC,EAA1D,EAA8D;QAC1D,IAAIC,GAAG,GAAG,KAAK7B,uBAAL,CAA6B4B,CAA7B,CAAV;;QACA,IAAIM,QAAQ,CAACL,GAAG,CAACP,UAAL,EAAiB,EAAjB,CAAR,IAAgCQ,WAApC,EAAiD;UAC7CG,qBAAqB,GAAGJ,GAAxB;QACH;MACJ;;MACD,IAAI,KAAKzB,YAAL,KAAsB6B,qBAAqB,CAAC7K,UAAhD,EAA4D;QACxD,KAAKgJ,YAAL,GAAoB6B,qBAAqB,CAAC7K,UAA1C;QACA,KAAKH,EAAL,CAAQ0D,YAAR;MACH;IACJ;EACJ;;EACDwH,WAAW,CAACpI,KAAD,EAAQ;IACf,OAAO,KAAKqI,YAAL,CAAkBrI,KAAlB,IAA2B,CAA3B,GAA+B,IAAtC;EACH;;EACDkF,UAAU,CAACC,CAAD,EAAI;IACV,KAAKF,gBAAL;IACA,IAAIH,aAAa,GAAG,KAAKrG,YAAL,GAAoB,CAAxC;;IACA,IAAIqG,aAAa,GAAG,KAAKoB,iBAArB,GAAyC,KAAKU,kBAAL,EAAzC,KAAwE,CAAC,CAAD,GAAK,KAAKV,iBAAX,GAAgC,KAAKoC,kBAAL,KAA4B,CAA5D,IAAiE,KAAK5K,QAA7I,CAAJ,EAA4J;MACxJ,KAAK6K,IAAL,CAAU,CAAC,CAAX;IACH;;IACD,IAAI5J,WAAW,GAAG,KAAKjB,QAAL,IAAkB,KAAKgC,KAAL,CAAWE,MAAX,GAAoB,CAArB,KAA4B,KAAKnB,YAAlD,GAAiE,CAAjE,GAAqEqG,aAAvF;IACA,KAAKrB,mBAAL,CAAyB3D,IAAzB,CAA8BnB,WAA9B;;IACA,IAAIwG,CAAC,CAACC,UAAN,EAAkB;MACdD,CAAC,CAACE,cAAF;IACH;EACJ;;EACDC,WAAW,CAACH,CAAD,EAAI;IACX,KAAKF,gBAAL;IACA,IAAID,aAAa,GAAG,KAAKvG,YAAL,KAAsB,CAAtB,GAA0B,KAAKA,YAAL,GAAoB,CAA9C,GAAkD,CAAtE;IACA,IAAI+J,IAAI,GAAGxD,aAAa,GAAG,KAAKkB,iBAAhC;;IACA,IAAK,KAAKG,YAAL,GAAoBmC,IAApB,GAA2B,CAA5B,GAAiC,KAAK5B,kBAAL,EAAjC,KAAgE,CAAC,CAAD,GAAK,KAAKV,iBAAX,KAAkC,CAAlC,IAAuC,KAAKxI,QAA3G,CAAJ,EAA0H;MACtH,KAAK6K,IAAL,CAAU,CAAV;IACH;;IACD,IAAI5J,WAAW,GAAG,KAAKjB,QAAL,IAAiB,KAAKe,YAAL,KAAsB,CAAvC,GAA2C,KAAKiB,KAAL,CAAWE,MAAX,GAAoB,CAA/D,GAAmEoF,aAArF;IACA,KAAKvB,mBAAL,CAAyB3D,IAAzB,CAA8BnB,WAA9B;;IACA,IAAIwG,CAAC,CAACC,UAAN,EAAkB;MACdD,CAAC,CAACE,cAAF;IACH;EACJ;;EACDoD,WAAW,CAACzI,KAAD,EAAQ;IACf,KAAKiF,gBAAL;IACA,IAAIyD,iBAAiB,GAAG1I,KAAxB;;IACA,IAAI0I,iBAAiB,KAAK,KAAKjK,YAA/B,EAA6C;MACzC,MAAM+J,IAAI,GAAGE,iBAAiB,GAAG,KAAKxC,iBAAtC;MACA,IAAIyC,GAAG,GAAG,CAAV;;MACA,IAAID,iBAAiB,GAAG,KAAKjK,YAA7B,EAA2C;QACvCkK,GAAG,GAAI,KAAKtC,YAAL,GAAoBmC,IAApB,GAA2B,CAA5B,GAAiC,KAAK5B,kBAAL,EAAvC;;QACA,IAAI+B,GAAG,GAAG,CAAN,IAAY,CAAC,CAAD,GAAK,KAAKzC,iBAAX,KAAkC,CAAjD,EAAoD;UAChD,KAAKqC,IAAL,CAAUI,GAAV;QACH;MACJ,CALD,MAMK;QACDA,GAAG,GAAG,KAAK/B,kBAAL,KAA4B4B,IAAlC;;QACA,IAAIG,GAAG,GAAG,CAAN,IAAY,CAAC,CAAD,GAAK,KAAKzC,iBAAX,GAAgC,KAAKoC,kBAAL,KAA4B,CAA3E,EAA8E;UAC1E,KAAKC,IAAL,CAAUI,GAAV;QACH;MACJ;;MACD,KAAKhK,WAAL,GAAmB+J,iBAAnB;MACA,KAAKjF,mBAAL,CAAyB3D,IAAzB,CAA8B,KAAKnB,WAAnC;IACH;EACJ;;EACD4J,IAAI,CAACI,GAAD,EAAM;IACN,IAAIzC,iBAAiB,GAAG,KAAKA,iBAAL,GAAyByC,GAAjD;;IACA,IAAIA,GAAG,GAAG,CAAN,IAAY,CAAC,CAAD,GAAKzC,iBAAN,GAA2B,KAAKG,YAAhC,GAAgD,KAAK3G,KAAL,CAAWE,MAAX,GAAoB,CAAnF,EAAuF;MACnFsG,iBAAiB,GAAG,KAAKG,YAAL,GAAoB,KAAK3G,KAAL,CAAWE,MAAnD;IACH,CAFD,MAGK,IAAI+I,GAAG,GAAG,CAAN,IAAWzC,iBAAiB,GAAG,CAAnC,EAAsC;MACvCA,iBAAiB,GAAG,CAApB;IACH;;IACD,IAAI,KAAKxI,QAAT,EAAmB;MACf,IAAIiL,GAAG,GAAG,CAAN,IAAW,KAAKjJ,KAAL,CAAWE,MAAX,GAAoB,CAApB,KAA0B,KAAKnB,YAA9C,EAA4D;QACxDyH,iBAAiB,GAAG,CAApB;MACH,CAFD,MAGK,IAAIyC,GAAG,GAAG,CAAN,IAAW,KAAKlK,YAAL,KAAsB,CAArC,EAAwC;QACzCyH,iBAAiB,GAAG,KAAKG,YAAL,GAAoB,KAAK3G,KAAL,CAAWE,MAAnD;MACH;IACJ;;IACD,IAAI,KAAK+G,cAAT,EAAyB;MACrBnK,UAAU,CAACwE,WAAX,CAAuB,KAAK2F,cAAL,CAAoBpG,aAA3C,EAA0D,gBAA1D;MACA,KAAKoG,cAAL,CAAoBpG,aAApB,CAAkC1D,KAAlC,CAAwC+E,SAAxC,GAAoD,KAAKqC,UAAL,GAAmB,kBAAiBiC,iBAAiB,IAAI,MAAM,KAAKG,YAAf,CAA6B,OAAlF,GAA4F,eAAcH,iBAAiB,IAAI,MAAM,KAAKG,YAAf,CAA6B,UAA5M;MACA,KAAKM,cAAL,CAAoBpG,aAApB,CAAkC1D,KAAlC,CAAwCD,UAAxC,GAAqD,yBAArD;IACH;;IACD,KAAKsJ,iBAAL,GAAyBA,iBAAzB;EACH;;EACDjB,gBAAgB,GAAG;IACf,IAAI,KAAKhC,eAAL,IAAwB,KAAKS,aAAjC,EAAgD;MAC5C,KAAKA,aAAL,CAAmB5D,IAAnB;IACH;EACJ;;EACD8I,iBAAiB,CAACzD,CAAD,EAAIqD,IAAJ,EAAU;IACvB,IAAIA,IAAI,GAAG,CAAX,EAAc;MAAE;MACZ,KAAKtD,UAAL,CAAgBC,CAAhB;IACH,CAFD,MAGK;MAAE;MACH,KAAKG,WAAL,CAAiBH,CAAjB;IACH;EACJ;;EACDmD,kBAAkB,GAAG;IACjB,OAAO,KAAK5I,KAAL,CAAWE,MAAX,GAAoB,KAAKyG,YAAzB,GAAyC,KAAK3G,KAAL,CAAWE,MAAX,GAAoB,KAAKyG,YAA1B,GAA0C,CAAlF,GAAsF,CAA7F;EACH;;EACDO,kBAAkB,GAAG;IACjB,IAAI5G,KAAK,GAAG6I,IAAI,CAACC,KAAL,CAAW,KAAKzC,YAAL,GAAoB,CAA/B,CAAZ;IACA,OAAQ,KAAKA,YAAL,GAAoB,CAArB,GAA0BrG,KAA1B,GAAkCA,KAAK,GAAG,CAAjD;EACH;;EACD+I,eAAe,GAAG;IACd,IAAI,KAAKpC,cAAL,IAAuB,KAAKA,cAAL,CAAoBpG,aAA/C,EAA8D;MAC1D/D,UAAU,CAAC6D,QAAX,CAAoB,KAAKsG,cAAL,CAAoBpG,aAAxC,EAAuD,gBAAvD;MACA,KAAKoG,cAAL,CAAoBpG,aAApB,CAAkC1D,KAAlC,CAAwCD,UAAxC,GAAqD,EAArD;IACH;EACJ;;EACDoM,UAAU,CAAC7D,CAAD,EAAI;IACV,IAAI8D,QAAQ,GAAG9D,CAAC,CAAC+D,cAAF,CAAiB,CAAjB,CAAf;;IACA,IAAI,KAAKjF,UAAT,EAAqB;MACjB,KAAK2E,iBAAL,CAAuBzD,CAAvB,EAA2B8D,QAAQ,CAACE,KAAT,GAAiB,KAAKpD,QAAL,CAAcqD,CAA1D;IACH,CAFD,MAGK;MACD,KAAKR,iBAAL,CAAuBzD,CAAvB,EAA2B8D,QAAQ,CAACI,KAAT,GAAiB,KAAKtD,QAAL,CAAcuD,CAA1D;IACH;EACJ;;EACDC,WAAW,CAACpE,CAAD,EAAI;IACX,IAAIA,CAAC,CAACC,UAAN,EAAkB;MACdD,CAAC,CAACE,cAAF;IACH;EACJ;;EACDmE,YAAY,CAACrE,CAAD,EAAI;IACZ,IAAI8D,QAAQ,GAAG9D,CAAC,CAAC+D,cAAF,CAAiB,CAAjB,CAAf;IACA,KAAKnD,QAAL,GAAgB;MACZuD,CAAC,EAAEL,QAAQ,CAACI,KADA;MAEZD,CAAC,EAAEH,QAAQ,CAACE;IAFA,CAAhB;EAIH;;EACDxD,qBAAqB,GAAG;IACpB,OAAQ,CAAC,KAAKjI,QAAN,IAAkB,KAAKe,YAAL,KAAsB,CAAzC,IAAgD,KAAKiB,KAAL,CAAWE,MAAX,IAAqB,KAAKyG,YAAjF;EACH;;EACDX,oBAAoB,GAAG;IACnB,OAAQ,CAAC,KAAKhI,QAAN,IAAkB,KAAKe,YAAL,KAAuB,KAAKiB,KAAL,CAAWE,MAAX,GAAoB,CAA9D,IAAsE,KAAKF,KAAL,CAAWE,MAAX,IAAqB,KAAKyG,YAAvG;EACH;;EACDoD,mBAAmB,GAAG;IAClB,OAAO,KAAKvD,iBAAL,GAAyB,CAAC,CAAjC;EACH;;EACDwD,mBAAmB,GAAG;IAClB,OAAO,KAAKD,mBAAL,KAA6B,KAAKpD,YAAlC,GAAiD,CAAxD;EACH;;EACDgC,YAAY,CAACrI,KAAD,EAAQ;IAChB,OAAO,KAAKyJ,mBAAL,MAA8BzJ,KAA9B,IAAuC,KAAK0J,mBAAL,MAA8B1J,KAA5E;EACH;;EACDyG,qBAAqB,GAAG;IACpB,IAAI,CAAC,KAAKkD,sBAAV,EAAkC;MAC9B,KAAKA,sBAAL,GAA8B,MAAM;QAChC,KAAK7C,iBAAL;MACH,CAFD;;MAGAkB,MAAM,CAAC4B,gBAAP,CAAwB,QAAxB,EAAkC,KAAKD,sBAAvC;IACH;EACJ;;EACDE,uBAAuB,GAAG;IACtB,IAAI,KAAKF,sBAAT,EAAiC;MAC7B3B,MAAM,CAAC8B,mBAAP,CAA2B,QAA3B,EAAqC,KAAKH,sBAA1C;MACA,KAAKA,sBAAL,GAA8B,IAA9B;IACH;EACJ;;EACDzI,WAAW,GAAG;IACV,IAAI,KAAKuB,iBAAT,EAA4B;MACxB,KAAKoH,uBAAL;IACH;;IACD,IAAI,KAAK7D,eAAT,EAA0B;MACtB,KAAKA,eAAL,CAAqB+D,UAArB,CAAgCC,WAAhC,CAA4C,KAAKhE,eAAjD;IACH;EACJ;;AApSoB;;AAsSzB3B,kBAAkB,CAAClD,IAAnB;EAAA,iBAA+GkD,kBAA/G,EAjwB2F9I,EAiwB3F,mBAAmJA,EAAE,CAAC8F,iBAAtJ;AAAA;;AACAgD,kBAAkB,CAAC9C,IAAnB,kBAlwB2FhG,EAkwB3F;EAAA,MAAmG8I,kBAAnG;EAAA;EAAA;IAAA;MAlwB2F9I,EAkwB3F;IAAA;;IAAA;MAAA;;MAlwB2FA,EAkwB3F,qBAlwB2FA,EAkwB3F;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAlwB2FA,EAmwBnF,yCADR;MAlwB2FA,EAqwB3E,uEAHhB;MAlwB2FA,EAwwB3E,4CANhB;MAlwB2FA,EAywBf;QAAA,OAAiB,qBAAjB;MAAA;QAAA,OACtC,wBADsC;MAAA;QAAA,OACH,uBADG;MAAA;QAAA,OAC8B,sBAD9B;MAAA,EAP5E;MAlwB2FA,EA2wBnE,iEATxB;MAlwB2FA,EAixBvE,iBAfpB;MAlwB2FA,EAmxB3E,uEAjBhB;MAlwB2FA,EAsxB/E,iBApBZ;IAAA;;IAAA;MAlwB2FA,EAqwBlE,aAHzB;MAlwB2FA,EAqwBlE,gDAHzB;MAlwB2FA,EAwwBzB,aANlE;MAlwB2FA,EAwwBzB,uBAxwByBA,EAwwBzB,mEANlE;MAlwB2FA,EA2wB7C,aAT9C;MAlwB2FA,EA2wB7C,iCAT9C;MAlwB2FA,EAmxBlE,aAjBzB;MAlwB2FA,EAmxBlE,gDAjBzB;IAAA;EAAA;EAAA,eAsBiEU,EAAE,CAACuF,OAtBpE,EAsB+JvF,EAAE,CAAC4J,OAtBlK,EAsB4R5J,EAAE,CAACwF,IAtB/R,EAsBgYxF,EAAE,CAACyF,OAtBnY,EAsBqdjF,EAAE,CAACyH,MAtBxd,EAsBohBC,gBAtBphB;EAAA;EAAA;AAAA;;AAuBA;EAAA,mDAzxB2F5I,EAyxB3F,mBAA2F8I,kBAA3F,EAA2H,CAAC;IAChHvC,IAAI,EAAErG,SAD0G;IAEhHsG,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBADX;MAEC7C,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAxBmB;MAyBC+C,eAAe,EAAExG,uBAAuB,CAACyG;IAzB1C,CAAD;EAF0G,CAAD,CAA3H,EA6B4B,YAAY;IAAE,OAAO,CAAC;MAAEL,IAAI,EAAEvG,EAAE,CAAC8F;IAAX,CAAD,CAAP;EAA0C,CA7BpF,EA6BsG;IAAE6F,WAAW,EAAE,CAAC;MACtGpF,IAAI,EAAElG;IADgG,CAAD,CAAf;IAEtF8D,KAAK,EAAE,CAAC;MACRoC,IAAI,EAAElG;IADE,CAAD,CAF+E;IAItFqI,UAAU,EAAE,CAAC;MACbnC,IAAI,EAAElG;IADO,CAAD,CAJ0E;IAMtFqH,eAAe,EAAE,CAAC;MAClBnB,IAAI,EAAElG;IADY,CAAD,CANqE;IAQtF8B,QAAQ,EAAE,CAAC;MACXoE,IAAI,EAAElG;IADK,CAAD,CAR4E;IAUtF6G,iBAAiB,EAAE,CAAC;MACpBX,IAAI,EAAElG;IADc,CAAD,CAVmE;IAYtFkK,aAAa,EAAE,CAAC;MAChBhE,IAAI,EAAElG;IADU,CAAD,CAZuE;IActF2B,uBAAuB,EAAE,CAAC;MAC1BuE,IAAI,EAAElG;IADoB,CAAD,CAd6D;IAgBtFkD,SAAS,EAAE,CAAC;MACZgD,IAAI,EAAElG;IADM,CAAD,CAhB2E;IAkBtF6H,mBAAmB,EAAE,CAAC;MACtB3B,IAAI,EAAEhG;IADgB,CAAD,CAlBiE;IAoBtF4H,aAAa,EAAE,CAAC;MAChB5B,IAAI,EAAEhG;IADU,CAAD,CApBuE;IAsBtF6K,cAAc,EAAE,CAAC;MACjB7E,IAAI,EAAEjG,SADW;MAEjBkG,IAAI,EAAE,CAAC,gBAAD;IAFW,CAAD,CAtBsE;IAyBtF1E,UAAU,EAAE,CAAC;MACbyE,IAAI,EAAElG;IADO,CAAD,CAzB0E;IA2BtF+C,WAAW,EAAE,CAAC;MACdmD,IAAI,EAAElG;IADQ,CAAD;EA3ByE,CA7BtG;AAAA;;AA2DA,MAAMqO,cAAN,CAAqB;;AAErBA,cAAc,CAAC9I,IAAf;EAAA,iBAA2G8I,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAv1B2F3O,EAu1B3F;EAAA,MAA4G0O;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAx1B2F5O,EAw1B3F;EAAA,UAAsIW,YAAtI,EAAoJG,YAApJ,EAAkKK,YAAlK,EAAgLR,YAAhL,EAA8LG,YAA9L;AAAA;;AACA;EAAA,mDAz1B2Fd,EAy1B3F,mBAA2F0O,cAA3F,EAAuH,CAAC;IAC5GnI,IAAI,EAAE9F,QADsG;IAE5G+F,IAAI,EAAE,CAAC;MACCqI,OAAO,EAAE,CAAClO,YAAD,EAAeG,YAAf,EAA6BK,YAA7B,CADV;MAEC2N,OAAO,EAAE,CAACnO,YAAD,EAAea,QAAf,EAAyB4E,eAAzB,EAA0CwC,gBAA1C,EAA4DC,YAA5D,EAA0EC,kBAA1E,EAA8FhI,YAA9F,CAFV;MAGCiO,YAAY,EAAE,CAACvN,QAAD,EAAW4E,eAAX,EAA4BwC,gBAA5B,EAA8CC,YAA9C,EAA4DC,kBAA5D;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAAStH,QAAT,EAAmB4E,eAAnB,EAAoCyC,YAApC,EAAkDD,gBAAlD,EAAoE8F,cAApE,EAAoF5F,kBAApF"}, "metadata": {}, "sourceType": "module"}