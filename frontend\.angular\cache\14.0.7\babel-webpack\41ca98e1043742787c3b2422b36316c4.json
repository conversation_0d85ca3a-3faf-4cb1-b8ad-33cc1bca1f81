{"ast": null, "code": "/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n  static addClass(element, className) {\n    if (element.classList) element.classList.add(className);else element.className += ' ' + className;\n  }\n\n  static addMultipleClasses(element, className) {\n    if (element.classList) {\n      let styles = className.trim().split(' ');\n\n      for (let i = 0; i < styles.length; i++) {\n        element.classList.add(styles[i]);\n      }\n    } else {\n      let styles = className.split(' ');\n\n      for (let i = 0; i < styles.length; i++) {\n        element.className += ' ' + styles[i];\n      }\n    }\n  }\n\n  static removeClass(element, className) {\n    if (element.classList) element.classList.remove(className);else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n  }\n\n  static hasClass(element, className) {\n    if (element.classList) return element.classList.contains(className);else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n  }\n\n  static siblings(element) {\n    return Array.prototype.filter.call(element.parentNode.children, function (child) {\n      return child !== element;\n    });\n  }\n\n  static find(element, selector) {\n    return Array.from(element.querySelectorAll(selector));\n  }\n\n  static findSingle(element, selector) {\n    if (element) {\n      return element.querySelector(selector);\n    }\n\n    return null;\n  }\n\n  static index(element) {\n    let children = element.parentNode.childNodes;\n    let num = 0;\n\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].nodeType == 1) num++;\n    }\n\n    return -1;\n  }\n\n  static indexWithinGroup(element, attributeName) {\n    let children = element.parentNode ? element.parentNode.childNodes : [];\n    let num = 0;\n\n    for (var i = 0; i < children.length; i++) {\n      if (children[i] == element) return num;\n      if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n    }\n\n    return -1;\n  }\n\n  static relativePosition(element, target) {\n    let elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    const targetHeight = target.offsetHeight;\n    const targetOffset = target.getBoundingClientRect();\n    const viewport = this.getViewport();\n    let top, left;\n\n    if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n      top = -1 * elementDimensions.height;\n      element.style.transformOrigin = 'bottom';\n\n      if (targetOffset.top + top < 0) {\n        top = -1 * targetOffset.top;\n      }\n    } else {\n      top = targetHeight;\n      element.style.transformOrigin = 'top';\n    }\n\n    if (elementDimensions.width > viewport.width) {\n      // element wider then viewport and cannot fit on screen (align at left side of viewport)\n      left = targetOffset.left * -1;\n    } else if (targetOffset.left + elementDimensions.width > viewport.width) {\n      // element wider then viewport but can be fit on screen (align at right side of viewport)\n      left = (targetOffset.left + elementDimensions.width - viewport.width) * -1;\n    } else {\n      // element fits on screen (align with target)\n      left = 0;\n    }\n\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n  }\n\n  static absolutePosition(element, target) {\n    let elementDimensions = element.offsetParent ? {\n      width: element.offsetWidth,\n      height: element.offsetHeight\n    } : this.getHiddenElementDimensions(element);\n    let elementOuterHeight = elementDimensions.height;\n    let elementOuterWidth = elementDimensions.width;\n    let targetOuterHeight = target.offsetHeight;\n    let targetOuterWidth = target.offsetWidth;\n    let targetOffset = target.getBoundingClientRect();\n    let windowScrollTop = this.getWindowScrollTop();\n    let windowScrollLeft = this.getWindowScrollLeft();\n    let viewport = this.getViewport();\n    let top, left;\n\n    if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n      top = targetOffset.top + windowScrollTop - elementOuterHeight;\n      element.style.transformOrigin = 'bottom';\n\n      if (top < 0) {\n        top = windowScrollTop;\n      }\n    } else {\n      top = targetOuterHeight + targetOffset.top + windowScrollTop;\n      element.style.transformOrigin = 'top';\n    }\n\n    if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n    element.style.top = top + 'px';\n    element.style.left = left + 'px';\n  }\n\n  static getParents(element, parents = []) {\n    return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n  }\n\n  static getScrollableParents(element) {\n    let scrollableParents = [];\n\n    if (element) {\n      let parents = this.getParents(element);\n      const overflowRegex = /(auto|scroll)/;\n\n      const overflowCheck = node => {\n        let styleDeclaration = window['getComputedStyle'](node, null);\n        return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n      };\n\n      for (let parent of parents) {\n        let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n\n        if (scrollSelectors) {\n          let selectors = scrollSelectors.split(',');\n\n          for (let selector of selectors) {\n            let el = this.findSingle(parent, selector);\n\n            if (el && overflowCheck(el)) {\n              scrollableParents.push(el);\n            }\n          }\n        }\n\n        if (parent.nodeType !== 9 && overflowCheck(parent)) {\n          scrollableParents.push(parent);\n        }\n      }\n    }\n\n    return scrollableParents;\n  }\n\n  static getHiddenElementOuterHeight(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementHeight = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementHeight;\n  }\n\n  static getHiddenElementOuterWidth(element) {\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    let elementWidth = element.offsetWidth;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return elementWidth;\n  }\n\n  static getHiddenElementDimensions(element) {\n    let dimensions = {};\n    element.style.visibility = 'hidden';\n    element.style.display = 'block';\n    dimensions.width = element.offsetWidth;\n    dimensions.height = element.offsetHeight;\n    element.style.display = 'none';\n    element.style.visibility = 'visible';\n    return dimensions;\n  }\n\n  static scrollInView(container, item) {\n    let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n    let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n    let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n    let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n    let containerRect = container.getBoundingClientRect();\n    let itemRect = item.getBoundingClientRect();\n    let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n    let scroll = container.scrollTop;\n    let elementHeight = container.clientHeight;\n    let itemHeight = this.getOuterHeight(item);\n\n    if (offset < 0) {\n      container.scrollTop = scroll + offset;\n    } else if (offset + itemHeight > elementHeight) {\n      container.scrollTop = scroll + offset - elementHeight + itemHeight;\n    }\n  }\n\n  static fadeIn(element, duration) {\n    element.style.opacity = 0;\n    let last = +new Date();\n    let opacity = 0;\n\n    let tick = function () {\n      opacity = +element.style.opacity.replace(\",\", \".\") + (new Date().getTime() - last) / duration;\n      element.style.opacity = opacity;\n      last = +new Date();\n\n      if (+opacity < 1) {\n        window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n      }\n    };\n\n    tick();\n  }\n\n  static fadeOut(element, ms) {\n    var opacity = 1,\n        interval = 50,\n        duration = ms,\n        gap = interval / duration;\n    let fading = setInterval(() => {\n      opacity = opacity - gap;\n\n      if (opacity <= 0) {\n        opacity = 0;\n        clearInterval(fading);\n      }\n\n      element.style.opacity = opacity;\n    }, interval);\n  }\n\n  static getWindowScrollTop() {\n    let doc = document.documentElement;\n    return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n  }\n\n  static getWindowScrollLeft() {\n    let doc = document.documentElement;\n    return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n  }\n\n  static matches(element, selector) {\n    var p = Element.prototype;\n\n    var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n      return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n    };\n\n    return f.call(element, selector);\n  }\n\n  static getOuterWidth(el, margin) {\n    let width = el.offsetWidth;\n\n    if (margin) {\n      let style = getComputedStyle(el);\n      width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n\n    return width;\n  }\n\n  static getHorizontalPadding(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n  }\n\n  static getHorizontalMargin(el) {\n    let style = getComputedStyle(el);\n    return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n  }\n\n  static innerWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n\n  static width(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    return width;\n  }\n\n  static getInnerHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n    return height;\n  }\n\n  static getOuterHeight(el, margin) {\n    let height = el.offsetHeight;\n\n    if (margin) {\n      let style = getComputedStyle(el);\n      height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n    }\n\n    return height;\n  }\n\n  static getHeight(el) {\n    let height = el.offsetHeight;\n    let style = getComputedStyle(el);\n    height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n    return height;\n  }\n\n  static getWidth(el) {\n    let width = el.offsetWidth;\n    let style = getComputedStyle(el);\n    width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n    return width;\n  }\n\n  static getViewport() {\n    let win = window,\n        d = document,\n        e = d.documentElement,\n        g = d.getElementsByTagName('body')[0],\n        w = win.innerWidth || e.clientWidth || g.clientWidth,\n        h = win.innerHeight || e.clientHeight || g.clientHeight;\n    return {\n      width: w,\n      height: h\n    };\n  }\n\n  static getOffset(el) {\n    var rect = el.getBoundingClientRect();\n    return {\n      top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n      left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n    };\n  }\n\n  static replaceElementWith(element, replacementElement) {\n    let parentNode = element.parentNode;\n    if (!parentNode) throw `Can't replace element`;\n    return parentNode.replaceChild(replacementElement, element);\n  }\n\n  static getUserAgent() {\n    return navigator.userAgent;\n  }\n\n  static isIE() {\n    var ua = window.navigator.userAgent;\n    var msie = ua.indexOf('MSIE ');\n\n    if (msie > 0) {\n      // IE 10 or older => return version number\n      return true;\n    }\n\n    var trident = ua.indexOf('Trident/');\n\n    if (trident > 0) {\n      // IE 11 => return version number\n      var rv = ua.indexOf('rv:');\n      return true;\n    }\n\n    var edge = ua.indexOf('Edge/');\n\n    if (edge > 0) {\n      // Edge (IE 12+) => return version number\n      return true;\n    } // other browser\n\n\n    return false;\n  }\n\n  static isIOS() {\n    return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n  }\n\n  static isAndroid() {\n    return /(android)/i.test(navigator.userAgent);\n  }\n\n  static isTouchDevice() {\n    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  }\n\n  static appendChild(element, target) {\n    if (this.isElement(target)) target.appendChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);else throw 'Cannot append ' + target + ' to ' + element;\n  }\n\n  static removeChild(element, target) {\n    if (this.isElement(target)) target.removeChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);else throw 'Cannot remove ' + element + ' from ' + target;\n  }\n\n  static removeElement(element) {\n    if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);else element.remove();\n  }\n\n  static isElement(obj) {\n    return typeof HTMLElement === \"object\" ? obj instanceof HTMLElement : obj && typeof obj === \"object\" && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === \"string\";\n  }\n\n  static calculateScrollbarWidth(el) {\n    if (el) {\n      let style = getComputedStyle(el);\n      return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n    } else {\n      if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n      let scrollDiv = document.createElement(\"div\");\n      scrollDiv.className = \"p-scrollbar-measure\";\n      document.body.appendChild(scrollDiv);\n      let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n      document.body.removeChild(scrollDiv);\n      this.calculatedScrollbarWidth = scrollbarWidth;\n      return scrollbarWidth;\n    }\n  }\n\n  static calculateScrollbarHeight() {\n    if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n    let scrollDiv = document.createElement(\"div\");\n    scrollDiv.className = \"p-scrollbar-measure\";\n    document.body.appendChild(scrollDiv);\n    let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n    document.body.removeChild(scrollDiv);\n    this.calculatedScrollbarWidth = scrollbarHeight;\n    return scrollbarHeight;\n  }\n\n  static invokeElementMethod(element, methodName, args) {\n    element[methodName].apply(element, args);\n  }\n\n  static clearSelection() {\n    if (window.getSelection) {\n      if (window.getSelection().empty) {\n        window.getSelection().empty();\n      } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n        window.getSelection().removeAllRanges();\n      }\n    } else if (document['selection'] && document['selection'].empty) {\n      try {\n        document['selection'].empty();\n      } catch (error) {//ignore IE bug\n      }\n    }\n  }\n\n  static getBrowser() {\n    if (!this.browser) {\n      let matched = this.resolveUserAgent();\n      this.browser = {};\n\n      if (matched.browser) {\n        this.browser[matched.browser] = true;\n        this.browser['version'] = matched.version;\n      }\n\n      if (this.browser['chrome']) {\n        this.browser['webkit'] = true;\n      } else if (this.browser['webkit']) {\n        this.browser['safari'] = true;\n      }\n    }\n\n    return this.browser;\n  }\n\n  static resolveUserAgent() {\n    let ua = navigator.userAgent.toLowerCase();\n    let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf(\"compatible\") < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n    return {\n      browser: match[1] || \"\",\n      version: match[2] || \"0\"\n    };\n  }\n\n  static isInteger(value) {\n    if (Number.isInteger) {\n      return Number.isInteger(value);\n    } else {\n      return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n    }\n  }\n\n  static isHidden(element) {\n    return element.offsetParent === null;\n  }\n\n  static getFocusableElements(element) {\n    let focusableElements = DomHandler.find(element, `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]), select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]), [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]):not(.p-disabled)`);\n    let visibleFocusableElements = [];\n\n    for (let focusableElement of focusableElements) {\n      if (getComputedStyle(focusableElement).display != \"none\" && getComputedStyle(focusableElement).visibility != \"hidden\") visibleFocusableElements.push(focusableElement);\n    }\n\n    return visibleFocusableElements;\n  }\n\n  static generateZIndex() {\n    this.zindex = this.zindex || 999;\n    return ++this.zindex;\n  }\n\n}\n\nDomHandler.zindex = 1000;\nDomHandler.calculatedScrollbarWidth = null;\nDomHandler.calculatedScrollbarHeight = null;\n\nclass ConnectedOverlayScrollHandler {\n  constructor(element, listener = () => {}) {\n    this.element = element;\n    this.listener = listener;\n  }\n\n  bindScrollListener() {\n    this.scrollableParents = DomHandler.getScrollableParents(this.element);\n\n    for (let i = 0; i < this.scrollableParents.length; i++) {\n      this.scrollableParents[i].addEventListener('scroll', this.listener);\n    }\n  }\n\n  unbindScrollListener() {\n    if (this.scrollableParents) {\n      for (let i = 0; i < this.scrollableParents.length; i++) {\n        this.scrollableParents[i].removeEventListener('scroll', this.listener);\n      }\n    }\n  }\n\n  destroy() {\n    this.unbindScrollListener();\n    this.element = null;\n    this.listener = null;\n    this.scrollableParents = null;\n  }\n\n}\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ConnectedOverlayScrollHandler, DomHandler };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "addClass", "element", "className", "classList", "add", "addMultipleClasses", "styles", "trim", "split", "i", "length", "removeClass", "remove", "replace", "RegExp", "join", "hasClass", "contains", "test", "siblings", "Array", "prototype", "filter", "call", "parentNode", "children", "child", "find", "selector", "from", "querySelectorAll", "findSingle", "querySelector", "index", "childNodes", "num", "nodeType", "indexWithinGroup", "attributeName", "attributes", "relativePosition", "target", "elementDimensions", "offsetParent", "width", "offsetWidth", "height", "offsetHeight", "getHiddenElementDimensions", "targetHeight", "targetOffset", "getBoundingClientRect", "viewport", "getViewport", "top", "left", "style", "transform<PERSON><PERSON>in", "absolutePosition", "elementOuterHeight", "elementOuterWidth", "targetOuterHeight", "targetOuterWidth", "windowScrollTop", "getWindowScrollTop", "windowScrollLeft", "getWindowScrollLeft", "Math", "max", "getParents", "parents", "concat", "getScrollableParents", "scrollableParents", "overflowRegex", "overflowCheck", "node", "styleDeclaration", "window", "getPropertyValue", "parent", "scrollSelectors", "dataset", "selectors", "el", "push", "getHiddenElementOuterHeight", "visibility", "display", "elementHeight", "getHiddenElementOuterWidth", "elementWidth", "dimensions", "scrollInView", "container", "item", "borderTopValue", "getComputedStyle", "borderTop", "parseFloat", "paddingTopValue", "paddingTop", "containerRect", "itemRect", "offset", "document", "body", "scrollTop", "scroll", "clientHeight", "itemHeight", "getOuterHeight", "fadeIn", "duration", "opacity", "last", "Date", "tick", "getTime", "requestAnimationFrame", "setTimeout", "fadeOut", "ms", "interval", "gap", "fading", "setInterval", "clearInterval", "doc", "documentElement", "pageYOffset", "clientTop", "pageXOffset", "scrollLeft", "clientLeft", "matches", "p", "Element", "f", "webkitMatchesSelector", "s", "indexOf", "getOuterWidth", "margin", "marginLeft", "marginRight", "getHorizontalPadding", "paddingLeft", "paddingRight", "getHorizontalMargin", "innerWidth", "getInnerHeight", "paddingBottom", "marginTop", "marginBottom", "getHeight", "borderTopWidth", "borderBottomWidth", "getWidth", "borderLeftWidth", "borderRightWidth", "win", "d", "e", "g", "getElementsByTagName", "w", "clientWidth", "h", "innerHeight", "getOffset", "rect", "replaceElementWith", "replacementElement", "<PERSON><PERSON><PERSON><PERSON>", "getUserAgent", "navigator", "userAgent", "isIE", "ua", "msie", "trident", "rv", "edge", "isIOS", "isAndroid", "isTouchDevice", "maxTouchPoints", "append<PERSON><PERSON><PERSON>", "isElement", "nativeElement", "<PERSON><PERSON><PERSON><PERSON>", "removeElement", "obj", "HTMLElement", "nodeName", "calculateScrollbarWidth", "calculatedScrollbarWidth", "scrollDiv", "createElement", "scrollbarWidth", "calculateScrollbarHeight", "calculatedScrollbarHeight", "scrollbarHeight", "invokeElementMethod", "methodName", "args", "apply", "clearSelection", "getSelection", "empty", "removeAllRanges", "rangeCount", "getRangeAt", "getClientRects", "error", "<PERSON><PERSON><PERSON><PERSON>", "browser", "matched", "resolveUserAgent", "version", "toLowerCase", "match", "exec", "isInteger", "value", "Number", "isFinite", "floor", "isHidden", "getFocusableElements", "focusableElements", "visibleFocusableElements", "focusableElement", "generateZIndex", "zindex", "ConnectedOverlayScrollHandler", "constructor", "listener", "bindScrollListener", "addEventListener", "unbindScrollListener", "removeEventListener", "destroy"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-dom.mjs"], "sourcesContent": ["/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n    static addClass(element, className) {\n        if (element.classList)\n            element.classList.add(className);\n        else\n            element.className += ' ' + className;\n    }\n    static addMultipleClasses(element, className) {\n        if (element.classList) {\n            let styles = className.trim().split(' ');\n            for (let i = 0; i < styles.length; i++) {\n                element.classList.add(styles[i]);\n            }\n        }\n        else {\n            let styles = className.split(' ');\n            for (let i = 0; i < styles.length; i++) {\n                element.className += ' ' + styles[i];\n            }\n        }\n    }\n    static removeClass(element, className) {\n        if (element.classList)\n            element.classList.remove(className);\n        else\n            element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n    }\n    static hasClass(element, className) {\n        if (element.classList)\n            return element.classList.contains(className);\n        else\n            return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n    }\n    static siblings(element) {\n        return Array.prototype.filter.call(element.parentNode.children, function (child) {\n            return child !== element;\n        });\n    }\n    static find(element, selector) {\n        return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n        if (element) {\n            return element.querySelector(selector);\n        }\n        return null;\n    }\n    static index(element) {\n        let children = element.parentNode.childNodes;\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n        let children = element.parentNode ? element.parentNode.childNodes : [];\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static relativePosition(element, target) {\n        let elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const targetHeight = target.offsetHeight;\n        const targetOffset = target.getBoundingClientRect();\n        const viewport = this.getViewport();\n        let top, left;\n        if ((targetOffset.top + targetHeight + elementDimensions.height) > viewport.height) {\n            top = -1 * (elementDimensions.height);\n            element.style.transformOrigin = 'bottom';\n            if (targetOffset.top + top < 0) {\n                top = -1 * targetOffset.top;\n            }\n        }\n        else {\n            top = targetHeight;\n            element.style.transformOrigin = 'top';\n        }\n        if (elementDimensions.width > viewport.width) {\n            // element wider then viewport and cannot fit on screen (align at left side of viewport)\n            left = targetOffset.left * -1;\n        }\n        else if ((targetOffset.left + elementDimensions.width) > viewport.width) {\n            // element wider then viewport but can be fit on screen (align at right side of viewport)\n            left = (targetOffset.left + elementDimensions.width - viewport.width) * -1;\n        }\n        else {\n            // element fits on screen (align with target)\n            left = 0;\n        }\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n    }\n    static absolutePosition(element, target) {\n        let elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        let elementOuterHeight = elementDimensions.height;\n        let elementOuterWidth = elementDimensions.width;\n        let targetOuterHeight = target.offsetHeight;\n        let targetOuterWidth = target.offsetWidth;\n        let targetOffset = target.getBoundingClientRect();\n        let windowScrollTop = this.getWindowScrollTop();\n        let windowScrollLeft = this.getWindowScrollLeft();\n        let viewport = this.getViewport();\n        let top, left;\n        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n            top = targetOffset.top + windowScrollTop - elementOuterHeight;\n            element.style.transformOrigin = 'bottom';\n            if (top < 0) {\n                top = windowScrollTop;\n            }\n        }\n        else {\n            top = targetOuterHeight + targetOffset.top + windowScrollTop;\n            element.style.transformOrigin = 'top';\n        }\n        if (targetOffset.left + elementOuterWidth > viewport.width)\n            left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n        else\n            left = targetOffset.left + windowScrollLeft;\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n    }\n    static getParents(element, parents = []) {\n        return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n        let scrollableParents = [];\n        if (element) {\n            let parents = this.getParents(element);\n            const overflowRegex = /(auto|scroll)/;\n            const overflowCheck = (node) => {\n                let styleDeclaration = window['getComputedStyle'](node, null);\n                return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n            };\n            for (let parent of parents) {\n                let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n                if (scrollSelectors) {\n                    let selectors = scrollSelectors.split(',');\n                    for (let selector of selectors) {\n                        let el = this.findSingle(parent, selector);\n                        if (el && overflowCheck(el)) {\n                            scrollableParents.push(el);\n                        }\n                    }\n                }\n                if (parent.nodeType !== 9 && overflowCheck(parent)) {\n                    scrollableParents.push(parent);\n                }\n            }\n        }\n        return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementHeight = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementWidth = element.offsetWidth;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n        let dimensions = {};\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        dimensions.width = element.offsetWidth;\n        dimensions.height = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return dimensions;\n    }\n    static scrollInView(container, item) {\n        let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n        let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n        let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n        let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        let offset = (itemRect.top + document.body.scrollTop) - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n        let scroll = container.scrollTop;\n        let elementHeight = container.clientHeight;\n        let itemHeight = this.getOuterHeight(item);\n        if (offset < 0) {\n            container.scrollTop = scroll + offset;\n        }\n        else if ((offset + itemHeight) > elementHeight) {\n            container.scrollTop = scroll + offset - elementHeight + itemHeight;\n        }\n    }\n    static fadeIn(element, duration) {\n        element.style.opacity = 0;\n        let last = +new Date();\n        let opacity = 0;\n        let tick = function () {\n            opacity = +element.style.opacity.replace(\",\", \".\") + (new Date().getTime() - last) / duration;\n            element.style.opacity = opacity;\n            last = +new Date();\n            if (+opacity < 1) {\n                (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16);\n            }\n        };\n        tick();\n    }\n    static fadeOut(element, ms) {\n        var opacity = 1, interval = 50, duration = ms, gap = interval / duration;\n        let fading = setInterval(() => {\n            opacity = opacity - gap;\n            if (opacity <= 0) {\n                opacity = 0;\n                clearInterval(fading);\n            }\n            element.style.opacity = opacity;\n        }, interval);\n    }\n    static getWindowScrollTop() {\n        let doc = document.documentElement;\n        return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n        let doc = document.documentElement;\n        return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n        var p = Element.prototype;\n        var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n            return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n        };\n        return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n        let width = el.offsetWidth;\n        if (margin) {\n            let style = getComputedStyle(el);\n            width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n        }\n        return width;\n    }\n    static getHorizontalPadding(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static width(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static getInnerHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n        return height;\n    }\n    static getOuterHeight(el, margin) {\n        let height = el.offsetHeight;\n        if (margin) {\n            let style = getComputedStyle(el);\n            height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n        }\n        return height;\n    }\n    static getHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n        return height;\n    }\n    static getWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n        return width;\n    }\n    static getViewport() {\n        let win = window, d = document, e = d.documentElement, g = d.getElementsByTagName('body')[0], w = win.innerWidth || e.clientWidth || g.clientWidth, h = win.innerHeight || e.clientHeight || g.clientHeight;\n        return { width: w, height: h };\n    }\n    static getOffset(el) {\n        var rect = el.getBoundingClientRect();\n        return {\n            top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n            left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0),\n        };\n    }\n    static replaceElementWith(element, replacementElement) {\n        let parentNode = element.parentNode;\n        if (!parentNode)\n            throw `Can't replace element`;\n        return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n        return navigator.userAgent;\n    }\n    static isIE() {\n        var ua = window.navigator.userAgent;\n        var msie = ua.indexOf('MSIE ');\n        if (msie > 0) {\n            // IE 10 or older => return version number\n            return true;\n        }\n        var trident = ua.indexOf('Trident/');\n        if (trident > 0) {\n            // IE 11 => return version number\n            var rv = ua.indexOf('rv:');\n            return true;\n        }\n        var edge = ua.indexOf('Edge/');\n        if (edge > 0) {\n            // Edge (IE 12+) => return version number\n            return true;\n        }\n        // other browser\n        return false;\n    }\n    static isIOS() {\n        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n        return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n        return (('ontouchstart' in window) || (navigator.maxTouchPoints > 0));\n    }\n    static appendChild(element, target) {\n        if (this.isElement(target))\n            target.appendChild(element);\n        else if (target.el && target.el.nativeElement)\n            target.el.nativeElement.appendChild(element);\n        else\n            throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n        if (this.isElement(target))\n            target.removeChild(element);\n        else if (target.el && target.el.nativeElement)\n            target.el.nativeElement.removeChild(element);\n        else\n            throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n        if (!('remove' in Element.prototype))\n            element.parentNode.removeChild(element);\n        else\n            element.remove();\n    }\n    static isElement(obj) {\n        return (typeof HTMLElement === \"object\" ? obj instanceof HTMLElement :\n            obj && typeof obj === \"object\" && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === \"string\");\n    }\n    static calculateScrollbarWidth(el) {\n        if (el) {\n            let style = getComputedStyle(el);\n            return (el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth));\n        }\n        else {\n            if (this.calculatedScrollbarWidth !== null)\n                return this.calculatedScrollbarWidth;\n            let scrollDiv = document.createElement(\"div\");\n            scrollDiv.className = \"p-scrollbar-measure\";\n            document.body.appendChild(scrollDiv);\n            let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n            document.body.removeChild(scrollDiv);\n            this.calculatedScrollbarWidth = scrollbarWidth;\n            return scrollbarWidth;\n        }\n    }\n    static calculateScrollbarHeight() {\n        if (this.calculatedScrollbarHeight !== null)\n            return this.calculatedScrollbarHeight;\n        let scrollDiv = document.createElement(\"div\");\n        scrollDiv.className = \"p-scrollbar-measure\";\n        document.body.appendChild(scrollDiv);\n        let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarHeight;\n        return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n        element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n        if (window.getSelection) {\n            if (window.getSelection().empty) {\n                window.getSelection().empty();\n            }\n            else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n                window.getSelection().removeAllRanges();\n            }\n        }\n        else if (document['selection'] && document['selection'].empty) {\n            try {\n                document['selection'].empty();\n            }\n            catch (error) {\n                //ignore IE bug\n            }\n        }\n    }\n    static getBrowser() {\n        if (!this.browser) {\n            let matched = this.resolveUserAgent();\n            this.browser = {};\n            if (matched.browser) {\n                this.browser[matched.browser] = true;\n                this.browser['version'] = matched.version;\n            }\n            if (this.browser['chrome']) {\n                this.browser['webkit'] = true;\n            }\n            else if (this.browser['webkit']) {\n                this.browser['safari'] = true;\n            }\n        }\n        return this.browser;\n    }\n    static resolveUserAgent() {\n        let ua = navigator.userAgent.toLowerCase();\n        let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) ||\n            /(webkit)[ \\/]([\\w.]+)/.exec(ua) ||\n            /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) ||\n            /(msie) ([\\w.]+)/.exec(ua) ||\n            ua.indexOf(\"compatible\") < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) ||\n            [];\n        return {\n            browser: match[1] || \"\",\n            version: match[2] || \"0\"\n        };\n    }\n    static isInteger(value) {\n        if (Number.isInteger) {\n            return Number.isInteger(value);\n        }\n        else {\n            return typeof value === \"number\" && isFinite(value) && Math.floor(value) === value;\n        }\n    }\n    static isHidden(element) {\n        return element.offsetParent === null;\n    }\n    static getFocusableElements(element) {\n        let focusableElements = DomHandler.find(element, `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]), select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]), [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]),\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden]):not(.p-disabled)`);\n        let visibleFocusableElements = [];\n        for (let focusableElement of focusableElements) {\n            if (getComputedStyle(focusableElement).display != \"none\" && getComputedStyle(focusableElement).visibility != \"hidden\")\n                visibleFocusableElements.push(focusableElement);\n        }\n        return visibleFocusableElements;\n    }\n    static generateZIndex() {\n        this.zindex = this.zindex || 999;\n        return ++this.zindex;\n    }\n}\nDomHandler.zindex = 1000;\nDomHandler.calculatedScrollbarWidth = null;\nDomHandler.calculatedScrollbarHeight = null;\n\nclass ConnectedOverlayScrollHandler {\n    constructor(element, listener = () => { }) {\n        this.element = element;\n        this.listener = listener;\n    }\n    bindScrollListener() {\n        this.scrollableParents = DomHandler.getScrollableParents(this.element);\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,UAAN,CAAiB;EACE,OAARC,QAAQ,CAACC,OAAD,EAAUC,SAAV,EAAqB;IAChC,IAAID,OAAO,CAACE,SAAZ,EACIF,OAAO,CAACE,SAAR,CAAkBC,GAAlB,CAAsBF,SAAtB,EADJ,KAGID,OAAO,CAACC,SAAR,IAAqB,MAAMA,SAA3B;EACP;;EACwB,OAAlBG,kBAAkB,CAACJ,OAAD,EAAUC,SAAV,EAAqB;IAC1C,IAAID,OAAO,CAACE,SAAZ,EAAuB;MACnB,IAAIG,MAAM,GAAGJ,SAAS,CAACK,IAAV,GAAiBC,KAAjB,CAAuB,GAAvB,CAAb;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,MAAM,CAACI,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;QACpCR,OAAO,CAACE,SAAR,CAAkBC,GAAlB,CAAsBE,MAAM,CAACG,CAAD,CAA5B;MACH;IACJ,CALD,MAMK;MACD,IAAIH,MAAM,GAAGJ,SAAS,CAACM,KAAV,CAAgB,GAAhB,CAAb;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,MAAM,CAACI,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;QACpCR,OAAO,CAACC,SAAR,IAAqB,MAAMI,MAAM,CAACG,CAAD,CAAjC;MACH;IACJ;EACJ;;EACiB,OAAXE,WAAW,CAACV,OAAD,EAAUC,SAAV,EAAqB;IACnC,IAAID,OAAO,CAACE,SAAZ,EACIF,OAAO,CAACE,SAAR,CAAkBS,MAAlB,CAAyBV,SAAzB,EADJ,KAGID,OAAO,CAACC,SAAR,GAAoBD,OAAO,CAACC,SAAR,CAAkBW,OAAlB,CAA0B,IAAIC,MAAJ,CAAW,YAAYZ,SAAS,CAACM,KAAV,CAAgB,GAAhB,EAAqBO,IAArB,CAA0B,GAA1B,CAAZ,GAA6C,SAAxD,EAAmE,IAAnE,CAA1B,EAAoG,GAApG,CAApB;EACP;;EACc,OAARC,QAAQ,CAACf,OAAD,EAAUC,SAAV,EAAqB;IAChC,IAAID,OAAO,CAACE,SAAZ,EACI,OAAOF,OAAO,CAACE,SAAR,CAAkBc,QAAlB,CAA2Bf,SAA3B,CAAP,CADJ,KAGI,OAAO,IAAIY,MAAJ,CAAW,UAAUZ,SAAV,GAAsB,OAAjC,EAA0C,IAA1C,EAAgDgB,IAAhD,CAAqDjB,OAAO,CAACC,SAA7D,CAAP;EACP;;EACc,OAARiB,QAAQ,CAAClB,OAAD,EAAU;IACrB,OAAOmB,KAAK,CAACC,SAAN,CAAgBC,MAAhB,CAAuBC,IAAvB,CAA4BtB,OAAO,CAACuB,UAAR,CAAmBC,QAA/C,EAAyD,UAAUC,KAAV,EAAiB;MAC7E,OAAOA,KAAK,KAAKzB,OAAjB;IACH,CAFM,CAAP;EAGH;;EACU,OAAJ0B,IAAI,CAAC1B,OAAD,EAAU2B,QAAV,EAAoB;IAC3B,OAAOR,KAAK,CAACS,IAAN,CAAW5B,OAAO,CAAC6B,gBAAR,CAAyBF,QAAzB,CAAX,CAAP;EACH;;EACgB,OAAVG,UAAU,CAAC9B,OAAD,EAAU2B,QAAV,EAAoB;IACjC,IAAI3B,OAAJ,EAAa;MACT,OAAOA,OAAO,CAAC+B,aAAR,CAAsBJ,QAAtB,CAAP;IACH;;IACD,OAAO,IAAP;EACH;;EACW,OAALK,KAAK,CAAChC,OAAD,EAAU;IAClB,IAAIwB,QAAQ,GAAGxB,OAAO,CAACuB,UAAR,CAAmBU,UAAlC;IACA,IAAIC,GAAG,GAAG,CAAV;;IACA,KAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgB,QAAQ,CAACf,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;MACtC,IAAIgB,QAAQ,CAAChB,CAAD,CAAR,IAAeR,OAAnB,EACI,OAAOkC,GAAP;MACJ,IAAIV,QAAQ,CAAChB,CAAD,CAAR,CAAY2B,QAAZ,IAAwB,CAA5B,EACID,GAAG;IACV;;IACD,OAAO,CAAC,CAAR;EACH;;EACsB,OAAhBE,gBAAgB,CAACpC,OAAD,EAAUqC,aAAV,EAAyB;IAC5C,IAAIb,QAAQ,GAAGxB,OAAO,CAACuB,UAAR,GAAqBvB,OAAO,CAACuB,UAAR,CAAmBU,UAAxC,GAAqD,EAApE;IACA,IAAIC,GAAG,GAAG,CAAV;;IACA,KAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgB,QAAQ,CAACf,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;MACtC,IAAIgB,QAAQ,CAAChB,CAAD,CAAR,IAAeR,OAAnB,EACI,OAAOkC,GAAP;MACJ,IAAIV,QAAQ,CAAChB,CAAD,CAAR,CAAY8B,UAAZ,IAA0Bd,QAAQ,CAAChB,CAAD,CAAR,CAAY8B,UAAZ,CAAuBD,aAAvB,CAA1B,IAAmEb,QAAQ,CAAChB,CAAD,CAAR,CAAY2B,QAAZ,IAAwB,CAA/F,EACID,GAAG;IACV;;IACD,OAAO,CAAC,CAAR;EACH;;EACsB,OAAhBK,gBAAgB,CAACvC,OAAD,EAAUwC,MAAV,EAAkB;IACrC,IAAIC,iBAAiB,GAAGzC,OAAO,CAAC0C,YAAR,GAAuB;MAAEC,KAAK,EAAE3C,OAAO,CAAC4C,WAAjB;MAA8BC,MAAM,EAAE7C,OAAO,CAAC8C;IAA9C,CAAvB,GAAsF,KAAKC,0BAAL,CAAgC/C,OAAhC,CAA9G;IACA,MAAMgD,YAAY,GAAGR,MAAM,CAACM,YAA5B;IACA,MAAMG,YAAY,GAAGT,MAAM,CAACU,qBAAP,EAArB;IACA,MAAMC,QAAQ,GAAG,KAAKC,WAAL,EAAjB;IACA,IAAIC,GAAJ,EAASC,IAAT;;IACA,IAAKL,YAAY,CAACI,GAAb,GAAmBL,YAAnB,GAAkCP,iBAAiB,CAACI,MAArD,GAA+DM,QAAQ,CAACN,MAA5E,EAAoF;MAChFQ,GAAG,GAAG,CAAC,CAAD,GAAMZ,iBAAiB,CAACI,MAA9B;MACA7C,OAAO,CAACuD,KAAR,CAAcC,eAAd,GAAgC,QAAhC;;MACA,IAAIP,YAAY,CAACI,GAAb,GAAmBA,GAAnB,GAAyB,CAA7B,EAAgC;QAC5BA,GAAG,GAAG,CAAC,CAAD,GAAKJ,YAAY,CAACI,GAAxB;MACH;IACJ,CAND,MAOK;MACDA,GAAG,GAAGL,YAAN;MACAhD,OAAO,CAACuD,KAAR,CAAcC,eAAd,GAAgC,KAAhC;IACH;;IACD,IAAIf,iBAAiB,CAACE,KAAlB,GAA0BQ,QAAQ,CAACR,KAAvC,EAA8C;MAC1C;MACAW,IAAI,GAAGL,YAAY,CAACK,IAAb,GAAoB,CAAC,CAA5B;IACH,CAHD,MAIK,IAAKL,YAAY,CAACK,IAAb,GAAoBb,iBAAiB,CAACE,KAAvC,GAAgDQ,QAAQ,CAACR,KAA7D,EAAoE;MACrE;MACAW,IAAI,GAAG,CAACL,YAAY,CAACK,IAAb,GAAoBb,iBAAiB,CAACE,KAAtC,GAA8CQ,QAAQ,CAACR,KAAxD,IAAiE,CAAC,CAAzE;IACH,CAHI,MAIA;MACD;MACAW,IAAI,GAAG,CAAP;IACH;;IACDtD,OAAO,CAACuD,KAAR,CAAcF,GAAd,GAAoBA,GAAG,GAAG,IAA1B;IACArD,OAAO,CAACuD,KAAR,CAAcD,IAAd,GAAqBA,IAAI,GAAG,IAA5B;EACH;;EACsB,OAAhBG,gBAAgB,CAACzD,OAAD,EAAUwC,MAAV,EAAkB;IACrC,IAAIC,iBAAiB,GAAGzC,OAAO,CAAC0C,YAAR,GAAuB;MAAEC,KAAK,EAAE3C,OAAO,CAAC4C,WAAjB;MAA8BC,MAAM,EAAE7C,OAAO,CAAC8C;IAA9C,CAAvB,GAAsF,KAAKC,0BAAL,CAAgC/C,OAAhC,CAA9G;IACA,IAAI0D,kBAAkB,GAAGjB,iBAAiB,CAACI,MAA3C;IACA,IAAIc,iBAAiB,GAAGlB,iBAAiB,CAACE,KAA1C;IACA,IAAIiB,iBAAiB,GAAGpB,MAAM,CAACM,YAA/B;IACA,IAAIe,gBAAgB,GAAGrB,MAAM,CAACI,WAA9B;IACA,IAAIK,YAAY,GAAGT,MAAM,CAACU,qBAAP,EAAnB;IACA,IAAIY,eAAe,GAAG,KAAKC,kBAAL,EAAtB;IACA,IAAIC,gBAAgB,GAAG,KAAKC,mBAAL,EAAvB;IACA,IAAId,QAAQ,GAAG,KAAKC,WAAL,EAAf;IACA,IAAIC,GAAJ,EAASC,IAAT;;IACA,IAAIL,YAAY,CAACI,GAAb,GAAmBO,iBAAnB,GAAuCF,kBAAvC,GAA4DP,QAAQ,CAACN,MAAzE,EAAiF;MAC7EQ,GAAG,GAAGJ,YAAY,CAACI,GAAb,GAAmBS,eAAnB,GAAqCJ,kBAA3C;MACA1D,OAAO,CAACuD,KAAR,CAAcC,eAAd,GAAgC,QAAhC;;MACA,IAAIH,GAAG,GAAG,CAAV,EAAa;QACTA,GAAG,GAAGS,eAAN;MACH;IACJ,CAND,MAOK;MACDT,GAAG,GAAGO,iBAAiB,GAAGX,YAAY,CAACI,GAAjC,GAAuCS,eAA7C;MACA9D,OAAO,CAACuD,KAAR,CAAcC,eAAd,GAAgC,KAAhC;IACH;;IACD,IAAIP,YAAY,CAACK,IAAb,GAAoBK,iBAApB,GAAwCR,QAAQ,CAACR,KAArD,EACIW,IAAI,GAAGY,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYlB,YAAY,CAACK,IAAb,GAAoBU,gBAApB,GAAuCH,gBAAvC,GAA0DF,iBAAtE,CAAP,CADJ,KAGIL,IAAI,GAAGL,YAAY,CAACK,IAAb,GAAoBU,gBAA3B;IACJhE,OAAO,CAACuD,KAAR,CAAcF,GAAd,GAAoBA,GAAG,GAAG,IAA1B;IACArD,OAAO,CAACuD,KAAR,CAAcD,IAAd,GAAqBA,IAAI,GAAG,IAA5B;EACH;;EACgB,OAAVc,UAAU,CAACpE,OAAD,EAAUqE,OAAO,GAAG,EAApB,EAAwB;IACrC,OAAOrE,OAAO,CAAC,YAAD,CAAP,KAA0B,IAA1B,GAAiCqE,OAAjC,GAA2C,KAAKD,UAAL,CAAgBpE,OAAO,CAACuB,UAAxB,EAAoC8C,OAAO,CAACC,MAAR,CAAe,CAACtE,OAAO,CAACuB,UAAT,CAAf,CAApC,CAAlD;EACH;;EAC0B,OAApBgD,oBAAoB,CAACvE,OAAD,EAAU;IACjC,IAAIwE,iBAAiB,GAAG,EAAxB;;IACA,IAAIxE,OAAJ,EAAa;MACT,IAAIqE,OAAO,GAAG,KAAKD,UAAL,CAAgBpE,OAAhB,CAAd;MACA,MAAMyE,aAAa,GAAG,eAAtB;;MACA,MAAMC,aAAa,GAAIC,IAAD,IAAU;QAC5B,IAAIC,gBAAgB,GAAGC,MAAM,CAAC,kBAAD,CAAN,CAA2BF,IAA3B,EAAiC,IAAjC,CAAvB;QACA,OAAOF,aAAa,CAACxD,IAAd,CAAmB2D,gBAAgB,CAACE,gBAAjB,CAAkC,UAAlC,CAAnB,KAAqEL,aAAa,CAACxD,IAAd,CAAmB2D,gBAAgB,CAACE,gBAAjB,CAAkC,WAAlC,CAAnB,CAArE,IAA2IL,aAAa,CAACxD,IAAd,CAAmB2D,gBAAgB,CAACE,gBAAjB,CAAkC,WAAlC,CAAnB,CAAlJ;MACH,CAHD;;MAIA,KAAK,IAAIC,MAAT,IAAmBV,OAAnB,EAA4B;QACxB,IAAIW,eAAe,GAAGD,MAAM,CAAC5C,QAAP,KAAoB,CAApB,IAAyB4C,MAAM,CAACE,OAAP,CAAe,iBAAf,CAA/C;;QACA,IAAID,eAAJ,EAAqB;UACjB,IAAIE,SAAS,GAAGF,eAAe,CAACzE,KAAhB,CAAsB,GAAtB,CAAhB;;UACA,KAAK,IAAIoB,QAAT,IAAqBuD,SAArB,EAAgC;YAC5B,IAAIC,EAAE,GAAG,KAAKrD,UAAL,CAAgBiD,MAAhB,EAAwBpD,QAAxB,CAAT;;YACA,IAAIwD,EAAE,IAAIT,aAAa,CAACS,EAAD,CAAvB,EAA6B;cACzBX,iBAAiB,CAACY,IAAlB,CAAuBD,EAAvB;YACH;UACJ;QACJ;;QACD,IAAIJ,MAAM,CAAC5C,QAAP,KAAoB,CAApB,IAAyBuC,aAAa,CAACK,MAAD,CAA1C,EAAoD;UAChDP,iBAAiB,CAACY,IAAlB,CAAuBL,MAAvB;QACH;MACJ;IACJ;;IACD,OAAOP,iBAAP;EACH;;EACiC,OAA3Ba,2BAA2B,CAACrF,OAAD,EAAU;IACxCA,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,QAA3B;IACAtF,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,OAAxB;IACA,IAAIC,aAAa,GAAGxF,OAAO,CAAC8C,YAA5B;IACA9C,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,MAAxB;IACAvF,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,SAA3B;IACA,OAAOE,aAAP;EACH;;EACgC,OAA1BC,0BAA0B,CAACzF,OAAD,EAAU;IACvCA,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,QAA3B;IACAtF,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,OAAxB;IACA,IAAIG,YAAY,GAAG1F,OAAO,CAAC4C,WAA3B;IACA5C,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,MAAxB;IACAvF,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,SAA3B;IACA,OAAOI,YAAP;EACH;;EACgC,OAA1B3C,0BAA0B,CAAC/C,OAAD,EAAU;IACvC,IAAI2F,UAAU,GAAG,EAAjB;IACA3F,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,QAA3B;IACAtF,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,OAAxB;IACAI,UAAU,CAAChD,KAAX,GAAmB3C,OAAO,CAAC4C,WAA3B;IACA+C,UAAU,CAAC9C,MAAX,GAAoB7C,OAAO,CAAC8C,YAA5B;IACA9C,OAAO,CAACuD,KAAR,CAAcgC,OAAd,GAAwB,MAAxB;IACAvF,OAAO,CAACuD,KAAR,CAAc+B,UAAd,GAA2B,SAA3B;IACA,OAAOK,UAAP;EACH;;EACkB,OAAZC,YAAY,CAACC,SAAD,EAAYC,IAAZ,EAAkB;IACjC,IAAIC,cAAc,GAAGC,gBAAgB,CAACH,SAAD,CAAhB,CAA4Bf,gBAA5B,CAA6C,gBAA7C,CAArB;IACA,IAAImB,SAAS,GAAGF,cAAc,GAAGG,UAAU,CAACH,cAAD,CAAb,GAAgC,CAA9D;IACA,IAAII,eAAe,GAAGH,gBAAgB,CAACH,SAAD,CAAhB,CAA4Bf,gBAA5B,CAA6C,YAA7C,CAAtB;IACA,IAAIsB,UAAU,GAAGD,eAAe,GAAGD,UAAU,CAACC,eAAD,CAAb,GAAiC,CAAjE;IACA,IAAIE,aAAa,GAAGR,SAAS,CAAC3C,qBAAV,EAApB;IACA,IAAIoD,QAAQ,GAAGR,IAAI,CAAC5C,qBAAL,EAAf;IACA,IAAIqD,MAAM,GAAID,QAAQ,CAACjD,GAAT,GAAemD,QAAQ,CAACC,IAAT,CAAcC,SAA9B,IAA4CL,aAAa,CAAChD,GAAd,GAAoBmD,QAAQ,CAACC,IAAT,CAAcC,SAA9E,IAA2FT,SAA3F,GAAuGG,UAApH;IACA,IAAIO,MAAM,GAAGd,SAAS,CAACa,SAAvB;IACA,IAAIlB,aAAa,GAAGK,SAAS,CAACe,YAA9B;IACA,IAAIC,UAAU,GAAG,KAAKC,cAAL,CAAoBhB,IAApB,CAAjB;;IACA,IAAIS,MAAM,GAAG,CAAb,EAAgB;MACZV,SAAS,CAACa,SAAV,GAAsBC,MAAM,GAAGJ,MAA/B;IACH,CAFD,MAGK,IAAKA,MAAM,GAAGM,UAAV,GAAwBrB,aAA5B,EAA2C;MAC5CK,SAAS,CAACa,SAAV,GAAsBC,MAAM,GAAGJ,MAAT,GAAkBf,aAAlB,GAAkCqB,UAAxD;IACH;EACJ;;EACY,OAANE,MAAM,CAAC/G,OAAD,EAAUgH,QAAV,EAAoB;IAC7BhH,OAAO,CAACuD,KAAR,CAAc0D,OAAd,GAAwB,CAAxB;IACA,IAAIC,IAAI,GAAG,CAAC,IAAIC,IAAJ,EAAZ;IACA,IAAIF,OAAO,GAAG,CAAd;;IACA,IAAIG,IAAI,GAAG,YAAY;MACnBH,OAAO,GAAG,CAACjH,OAAO,CAACuD,KAAR,CAAc0D,OAAd,CAAsBrG,OAAtB,CAA8B,GAA9B,EAAmC,GAAnC,CAAD,GAA2C,CAAC,IAAIuG,IAAJ,GAAWE,OAAX,KAAuBH,IAAxB,IAAgCF,QAArF;MACAhH,OAAO,CAACuD,KAAR,CAAc0D,OAAd,GAAwBA,OAAxB;MACAC,IAAI,GAAG,CAAC,IAAIC,IAAJ,EAAR;;MACA,IAAI,CAACF,OAAD,GAAW,CAAf,EAAkB;QACbpC,MAAM,CAACyC,qBAAP,IAAgCA,qBAAqB,CAACF,IAAD,CAAtD,IAAiEG,UAAU,CAACH,IAAD,EAAO,EAAP,CAA3E;MACH;IACJ,CAPD;;IAQAA,IAAI;EACP;;EACa,OAAPI,OAAO,CAACxH,OAAD,EAAUyH,EAAV,EAAc;IACxB,IAAIR,OAAO,GAAG,CAAd;IAAA,IAAiBS,QAAQ,GAAG,EAA5B;IAAA,IAAgCV,QAAQ,GAAGS,EAA3C;IAAA,IAA+CE,GAAG,GAAGD,QAAQ,GAAGV,QAAhE;IACA,IAAIY,MAAM,GAAGC,WAAW,CAAC,MAAM;MAC3BZ,OAAO,GAAGA,OAAO,GAAGU,GAApB;;MACA,IAAIV,OAAO,IAAI,CAAf,EAAkB;QACdA,OAAO,GAAG,CAAV;QACAa,aAAa,CAACF,MAAD,CAAb;MACH;;MACD5H,OAAO,CAACuD,KAAR,CAAc0D,OAAd,GAAwBA,OAAxB;IACH,CAPuB,EAOrBS,QAPqB,CAAxB;EAQH;;EACwB,OAAlB3D,kBAAkB,GAAG;IACxB,IAAIgE,GAAG,GAAGvB,QAAQ,CAACwB,eAAnB;IACA,OAAO,CAACnD,MAAM,CAACoD,WAAP,IAAsBF,GAAG,CAACrB,SAA3B,KAAyCqB,GAAG,CAACG,SAAJ,IAAiB,CAA1D,CAAP;EACH;;EACyB,OAAnBjE,mBAAmB,GAAG;IACzB,IAAI8D,GAAG,GAAGvB,QAAQ,CAACwB,eAAnB;IACA,OAAO,CAACnD,MAAM,CAACsD,WAAP,IAAsBJ,GAAG,CAACK,UAA3B,KAA0CL,GAAG,CAACM,UAAJ,IAAkB,CAA5D,CAAP;EACH;;EACa,OAAPC,OAAO,CAACtI,OAAD,EAAU2B,QAAV,EAAoB;IAC9B,IAAI4G,CAAC,GAAGC,OAAO,CAACpH,SAAhB;;IACA,IAAIqH,CAAC,GAAGF,CAAC,CAAC,SAAD,CAAD,IAAgBA,CAAC,CAACG,qBAAlB,IAA2CH,CAAC,CAAC,oBAAD,CAA5C,IAAsEA,CAAC,CAAC,mBAAD,CAAvE,IAAgG,UAAUI,CAAV,EAAa;MACjH,OAAO,GAAGC,OAAH,CAAWtH,IAAX,CAAgBkF,QAAQ,CAAC3E,gBAAT,CAA0B8G,CAA1B,CAAhB,EAA8C,IAA9C,MAAwD,CAAC,CAAhE;IACH,CAFD;;IAGA,OAAOF,CAAC,CAACnH,IAAF,CAAOtB,OAAP,EAAgB2B,QAAhB,CAAP;EACH;;EACmB,OAAbkH,aAAa,CAAC1D,EAAD,EAAK2D,MAAL,EAAa;IAC7B,IAAInG,KAAK,GAAGwC,EAAE,CAACvC,WAAf;;IACA,IAAIkG,MAAJ,EAAY;MACR,IAAIvF,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;MACAxC,KAAK,IAAIuD,UAAU,CAAC3C,KAAK,CAACwF,UAAP,CAAV,GAA+B7C,UAAU,CAAC3C,KAAK,CAACyF,WAAP,CAAlD;IACH;;IACD,OAAOrG,KAAP;EACH;;EAC0B,OAApBsG,oBAAoB,CAAC9D,EAAD,EAAK;IAC5B,IAAI5B,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACA,OAAOe,UAAU,CAAC3C,KAAK,CAAC2F,WAAP,CAAV,GAAgChD,UAAU,CAAC3C,KAAK,CAAC4F,YAAP,CAAjD;EACH;;EACyB,OAAnBC,mBAAmB,CAACjE,EAAD,EAAK;IAC3B,IAAI5B,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACA,OAAOe,UAAU,CAAC3C,KAAK,CAACwF,UAAP,CAAV,GAA+B7C,UAAU,CAAC3C,KAAK,CAACyF,WAAP,CAAhD;EACH;;EACgB,OAAVK,UAAU,CAAClE,EAAD,EAAK;IAClB,IAAIxC,KAAK,GAAGwC,EAAE,CAACvC,WAAf;IACA,IAAIW,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACAxC,KAAK,IAAIuD,UAAU,CAAC3C,KAAK,CAAC2F,WAAP,CAAV,GAAgChD,UAAU,CAAC3C,KAAK,CAAC4F,YAAP,CAAnD;IACA,OAAOxG,KAAP;EACH;;EACW,OAALA,KAAK,CAACwC,EAAD,EAAK;IACb,IAAIxC,KAAK,GAAGwC,EAAE,CAACvC,WAAf;IACA,IAAIW,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACAxC,KAAK,IAAIuD,UAAU,CAAC3C,KAAK,CAAC2F,WAAP,CAAV,GAAgChD,UAAU,CAAC3C,KAAK,CAAC4F,YAAP,CAAnD;IACA,OAAOxG,KAAP;EACH;;EACoB,OAAd2G,cAAc,CAACnE,EAAD,EAAK;IACtB,IAAItC,MAAM,GAAGsC,EAAE,CAACrC,YAAhB;IACA,IAAIS,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACAtC,MAAM,IAAIqD,UAAU,CAAC3C,KAAK,CAAC6C,UAAP,CAAV,GAA+BF,UAAU,CAAC3C,KAAK,CAACgG,aAAP,CAAnD;IACA,OAAO1G,MAAP;EACH;;EACoB,OAAdiE,cAAc,CAAC3B,EAAD,EAAK2D,MAAL,EAAa;IAC9B,IAAIjG,MAAM,GAAGsC,EAAE,CAACrC,YAAhB;;IACA,IAAIgG,MAAJ,EAAY;MACR,IAAIvF,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;MACAtC,MAAM,IAAIqD,UAAU,CAAC3C,KAAK,CAACiG,SAAP,CAAV,GAA8BtD,UAAU,CAAC3C,KAAK,CAACkG,YAAP,CAAlD;IACH;;IACD,OAAO5G,MAAP;EACH;;EACe,OAAT6G,SAAS,CAACvE,EAAD,EAAK;IACjB,IAAItC,MAAM,GAAGsC,EAAE,CAACrC,YAAhB;IACA,IAAIS,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACAtC,MAAM,IAAIqD,UAAU,CAAC3C,KAAK,CAAC6C,UAAP,CAAV,GAA+BF,UAAU,CAAC3C,KAAK,CAACgG,aAAP,CAAzC,GAAiErD,UAAU,CAAC3C,KAAK,CAACoG,cAAP,CAA3E,GAAoGzD,UAAU,CAAC3C,KAAK,CAACqG,iBAAP,CAAxH;IACA,OAAO/G,MAAP;EACH;;EACc,OAARgH,QAAQ,CAAC1E,EAAD,EAAK;IAChB,IAAIxC,KAAK,GAAGwC,EAAE,CAACvC,WAAf;IACA,IAAIW,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;IACAxC,KAAK,IAAIuD,UAAU,CAAC3C,KAAK,CAAC2F,WAAP,CAAV,GAAgChD,UAAU,CAAC3C,KAAK,CAAC4F,YAAP,CAA1C,GAAiEjD,UAAU,CAAC3C,KAAK,CAACuG,eAAP,CAA3E,GAAqG5D,UAAU,CAAC3C,KAAK,CAACwG,gBAAP,CAAxH;IACA,OAAOpH,KAAP;EACH;;EACiB,OAAXS,WAAW,GAAG;IACjB,IAAI4G,GAAG,GAAGnF,MAAV;IAAA,IAAkBoF,CAAC,GAAGzD,QAAtB;IAAA,IAAgC0D,CAAC,GAAGD,CAAC,CAACjC,eAAtC;IAAA,IAAuDmC,CAAC,GAAGF,CAAC,CAACG,oBAAF,CAAuB,MAAvB,EAA+B,CAA/B,CAA3D;IAAA,IAA8FC,CAAC,GAAGL,GAAG,CAACX,UAAJ,IAAkBa,CAAC,CAACI,WAApB,IAAmCH,CAAC,CAACG,WAAvI;IAAA,IAAoJC,CAAC,GAAGP,GAAG,CAACQ,WAAJ,IAAmBN,CAAC,CAACtD,YAArB,IAAqCuD,CAAC,CAACvD,YAA/L;IACA,OAAO;MAAEjE,KAAK,EAAE0H,CAAT;MAAYxH,MAAM,EAAE0H;IAApB,CAAP;EACH;;EACe,OAATE,SAAS,CAACtF,EAAD,EAAK;IACjB,IAAIuF,IAAI,GAAGvF,EAAE,CAACjC,qBAAH,EAAX;IACA,OAAO;MACHG,GAAG,EAAEqH,IAAI,CAACrH,GAAL,IAAYwB,MAAM,CAACoD,WAAP,IAAsBzB,QAAQ,CAACwB,eAAT,CAAyBtB,SAA/C,IAA4DF,QAAQ,CAACC,IAAT,CAAcC,SAA1E,IAAuF,CAAnG,CADF;MAEHpD,IAAI,EAAEoH,IAAI,CAACpH,IAAL,IAAauB,MAAM,CAACsD,WAAP,IAAsB3B,QAAQ,CAACwB,eAAT,CAAyBI,UAA/C,IAA6D5B,QAAQ,CAACC,IAAT,CAAc2B,UAA3E,IAAyF,CAAtG;IAFH,CAAP;EAIH;;EACwB,OAAlBuC,kBAAkB,CAAC3K,OAAD,EAAU4K,kBAAV,EAA8B;IACnD,IAAIrJ,UAAU,GAAGvB,OAAO,CAACuB,UAAzB;IACA,IAAI,CAACA,UAAL,EACI,MAAO,uBAAP;IACJ,OAAOA,UAAU,CAACsJ,YAAX,CAAwBD,kBAAxB,EAA4C5K,OAA5C,CAAP;EACH;;EACkB,OAAZ8K,YAAY,GAAG;IAClB,OAAOC,SAAS,CAACC,SAAjB;EACH;;EACU,OAAJC,IAAI,GAAG;IACV,IAAIC,EAAE,GAAGrG,MAAM,CAACkG,SAAP,CAAiBC,SAA1B;IACA,IAAIG,IAAI,GAAGD,EAAE,CAACtC,OAAH,CAAW,OAAX,CAAX;;IACA,IAAIuC,IAAI,GAAG,CAAX,EAAc;MACV;MACA,OAAO,IAAP;IACH;;IACD,IAAIC,OAAO,GAAGF,EAAE,CAACtC,OAAH,CAAW,UAAX,CAAd;;IACA,IAAIwC,OAAO,GAAG,CAAd,EAAiB;MACb;MACA,IAAIC,EAAE,GAAGH,EAAE,CAACtC,OAAH,CAAW,KAAX,CAAT;MACA,OAAO,IAAP;IACH;;IACD,IAAI0C,IAAI,GAAGJ,EAAE,CAACtC,OAAH,CAAW,OAAX,CAAX;;IACA,IAAI0C,IAAI,GAAG,CAAX,EAAc;MACV;MACA,OAAO,IAAP;IACH,CAjBS,CAkBV;;;IACA,OAAO,KAAP;EACH;;EACW,OAALC,KAAK,GAAG;IACX,OAAO,mBAAmBtK,IAAnB,CAAwB8J,SAAS,CAACC,SAAlC,KAAgD,CAACnG,MAAM,CAAC,UAAD,CAA9D;EACH;;EACe,OAAT2G,SAAS,GAAG;IACf,OAAO,aAAavK,IAAb,CAAkB8J,SAAS,CAACC,SAA5B,CAAP;EACH;;EACmB,OAAbS,aAAa,GAAG;IACnB,OAAS,kBAAkB5G,MAAnB,IAA+BkG,SAAS,CAACW,cAAV,GAA2B,CAAlE;EACH;;EACiB,OAAXC,WAAW,CAAC3L,OAAD,EAAUwC,MAAV,EAAkB;IAChC,IAAI,KAAKoJ,SAAL,CAAepJ,MAAf,CAAJ,EACIA,MAAM,CAACmJ,WAAP,CAAmB3L,OAAnB,EADJ,KAEK,IAAIwC,MAAM,CAAC2C,EAAP,IAAa3C,MAAM,CAAC2C,EAAP,CAAU0G,aAA3B,EACDrJ,MAAM,CAAC2C,EAAP,CAAU0G,aAAV,CAAwBF,WAAxB,CAAoC3L,OAApC,EADC,KAGD,MAAM,mBAAmBwC,MAAnB,GAA4B,MAA5B,GAAqCxC,OAA3C;EACP;;EACiB,OAAX8L,WAAW,CAAC9L,OAAD,EAAUwC,MAAV,EAAkB;IAChC,IAAI,KAAKoJ,SAAL,CAAepJ,MAAf,CAAJ,EACIA,MAAM,CAACsJ,WAAP,CAAmB9L,OAAnB,EADJ,KAEK,IAAIwC,MAAM,CAAC2C,EAAP,IAAa3C,MAAM,CAAC2C,EAAP,CAAU0G,aAA3B,EACDrJ,MAAM,CAAC2C,EAAP,CAAU0G,aAAV,CAAwBC,WAAxB,CAAoC9L,OAApC,EADC,KAGD,MAAM,mBAAmBA,OAAnB,GAA6B,QAA7B,GAAwCwC,MAA9C;EACP;;EACmB,OAAbuJ,aAAa,CAAC/L,OAAD,EAAU;IAC1B,IAAI,EAAE,YAAYwI,OAAO,CAACpH,SAAtB,CAAJ,EACIpB,OAAO,CAACuB,UAAR,CAAmBuK,WAAnB,CAA+B9L,OAA/B,EADJ,KAGIA,OAAO,CAACW,MAAR;EACP;;EACe,OAATiL,SAAS,CAACI,GAAD,EAAM;IAClB,OAAQ,OAAOC,WAAP,KAAuB,QAAvB,GAAkCD,GAAG,YAAYC,WAAjD,GACJD,GAAG,IAAI,OAAOA,GAAP,KAAe,QAAtB,IAAkCA,GAAG,KAAK,IAA1C,IAAkDA,GAAG,CAAC7J,QAAJ,KAAiB,CAAnE,IAAwE,OAAO6J,GAAG,CAACE,QAAX,KAAwB,QADpG;EAEH;;EAC6B,OAAvBC,uBAAuB,CAAChH,EAAD,EAAK;IAC/B,IAAIA,EAAJ,EAAQ;MACJ,IAAI5B,KAAK,GAAGyC,gBAAgB,CAACb,EAAD,CAA5B;MACA,OAAQA,EAAE,CAACvC,WAAH,GAAiBuC,EAAE,CAACmF,WAApB,GAAkCpE,UAAU,CAAC3C,KAAK,CAACuG,eAAP,CAA5C,GAAsE5D,UAAU,CAAC3C,KAAK,CAACwG,gBAAP,CAAxF;IACH,CAHD,MAIK;MACD,IAAI,KAAKqC,wBAAL,KAAkC,IAAtC,EACI,OAAO,KAAKA,wBAAZ;MACJ,IAAIC,SAAS,GAAG7F,QAAQ,CAAC8F,aAAT,CAAuB,KAAvB,CAAhB;MACAD,SAAS,CAACpM,SAAV,GAAsB,qBAAtB;MACAuG,QAAQ,CAACC,IAAT,CAAckF,WAAd,CAA0BU,SAA1B;MACA,IAAIE,cAAc,GAAGF,SAAS,CAACzJ,WAAV,GAAwByJ,SAAS,CAAC/B,WAAvD;MACA9D,QAAQ,CAACC,IAAT,CAAcqF,WAAd,CAA0BO,SAA1B;MACA,KAAKD,wBAAL,GAAgCG,cAAhC;MACA,OAAOA,cAAP;IACH;EACJ;;EAC8B,OAAxBC,wBAAwB,GAAG;IAC9B,IAAI,KAAKC,yBAAL,KAAmC,IAAvC,EACI,OAAO,KAAKA,yBAAZ;IACJ,IAAIJ,SAAS,GAAG7F,QAAQ,CAAC8F,aAAT,CAAuB,KAAvB,CAAhB;IACAD,SAAS,CAACpM,SAAV,GAAsB,qBAAtB;IACAuG,QAAQ,CAACC,IAAT,CAAckF,WAAd,CAA0BU,SAA1B;IACA,IAAIK,eAAe,GAAGL,SAAS,CAACvJ,YAAV,GAAyBuJ,SAAS,CAACzF,YAAzD;IACAJ,QAAQ,CAACC,IAAT,CAAcqF,WAAd,CAA0BO,SAA1B;IACA,KAAKD,wBAAL,GAAgCM,eAAhC;IACA,OAAOA,eAAP;EACH;;EACyB,OAAnBC,mBAAmB,CAAC3M,OAAD,EAAU4M,UAAV,EAAsBC,IAAtB,EAA4B;IAClD7M,OAAO,CAAC4M,UAAD,CAAP,CAAoBE,KAApB,CAA0B9M,OAA1B,EAAmC6M,IAAnC;EACH;;EACoB,OAAdE,cAAc,GAAG;IACpB,IAAIlI,MAAM,CAACmI,YAAX,EAAyB;MACrB,IAAInI,MAAM,CAACmI,YAAP,GAAsBC,KAA1B,EAAiC;QAC7BpI,MAAM,CAACmI,YAAP,GAAsBC,KAAtB;MACH,CAFD,MAGK,IAAIpI,MAAM,CAACmI,YAAP,GAAsBE,eAAtB,IAAyCrI,MAAM,CAACmI,YAAP,GAAsBG,UAAtB,GAAmC,CAA5E,IAAiFtI,MAAM,CAACmI,YAAP,GAAsBI,UAAtB,CAAiC,CAAjC,EAAoCC,cAApC,GAAqD5M,MAArD,GAA8D,CAAnJ,EAAsJ;QACvJoE,MAAM,CAACmI,YAAP,GAAsBE,eAAtB;MACH;IACJ,CAPD,MAQK,IAAI1G,QAAQ,CAAC,WAAD,CAAR,IAAyBA,QAAQ,CAAC,WAAD,CAAR,CAAsByG,KAAnD,EAA0D;MAC3D,IAAI;QACAzG,QAAQ,CAAC,WAAD,CAAR,CAAsByG,KAAtB;MACH,CAFD,CAGA,OAAOK,KAAP,EAAc,CACV;MACH;IACJ;EACJ;;EACgB,OAAVC,UAAU,GAAG;IAChB,IAAI,CAAC,KAAKC,OAAV,EAAmB;MACf,IAAIC,OAAO,GAAG,KAAKC,gBAAL,EAAd;MACA,KAAKF,OAAL,GAAe,EAAf;;MACA,IAAIC,OAAO,CAACD,OAAZ,EAAqB;QACjB,KAAKA,OAAL,CAAaC,OAAO,CAACD,OAArB,IAAgC,IAAhC;QACA,KAAKA,OAAL,CAAa,SAAb,IAA0BC,OAAO,CAACE,OAAlC;MACH;;MACD,IAAI,KAAKH,OAAL,CAAa,QAAb,CAAJ,EAA4B;QACxB,KAAKA,OAAL,CAAa,QAAb,IAAyB,IAAzB;MACH,CAFD,MAGK,IAAI,KAAKA,OAAL,CAAa,QAAb,CAAJ,EAA4B;QAC7B,KAAKA,OAAL,CAAa,QAAb,IAAyB,IAAzB;MACH;IACJ;;IACD,OAAO,KAAKA,OAAZ;EACH;;EACsB,OAAhBE,gBAAgB,GAAG;IACtB,IAAIxC,EAAE,GAAGH,SAAS,CAACC,SAAV,CAAoB4C,WAApB,EAAT;IACA,IAAIC,KAAK,GAAG,wBAAwBC,IAAxB,CAA6B5C,EAA7B,KACR,wBAAwB4C,IAAxB,CAA6B5C,EAA7B,CADQ,IAER,qCAAqC4C,IAArC,CAA0C5C,EAA1C,CAFQ,IAGR,kBAAkB4C,IAAlB,CAAuB5C,EAAvB,CAHQ,IAIRA,EAAE,CAACtC,OAAH,CAAW,YAAX,IAA2B,CAA3B,IAAgC,gCAAgCkF,IAAhC,CAAqC5C,EAArC,CAJxB,IAKR,EALJ;IAMA,OAAO;MACHsC,OAAO,EAAEK,KAAK,CAAC,CAAD,CAAL,IAAY,EADlB;MAEHF,OAAO,EAAEE,KAAK,CAAC,CAAD,CAAL,IAAY;IAFlB,CAAP;EAIH;;EACe,OAATE,SAAS,CAACC,KAAD,EAAQ;IACpB,IAAIC,MAAM,CAACF,SAAX,EAAsB;MAClB,OAAOE,MAAM,CAACF,SAAP,CAAiBC,KAAjB,CAAP;IACH,CAFD,MAGK;MACD,OAAO,OAAOA,KAAP,KAAiB,QAAjB,IAA6BE,QAAQ,CAACF,KAAD,CAArC,IAAgD9J,IAAI,CAACiK,KAAL,CAAWH,KAAX,MAAsBA,KAA7E;IACH;EACJ;;EACc,OAARI,QAAQ,CAACpO,OAAD,EAAU;IACrB,OAAOA,OAAO,CAAC0C,YAAR,KAAyB,IAAhC;EACH;;EAC0B,OAApB2L,oBAAoB,CAACrO,OAAD,EAAU;IACjC,IAAIsO,iBAAiB,GAAGxO,UAAU,CAAC4B,IAAX,CAAgB1B,OAAhB,EAA0B;AAC1D;AACA;AACA;AACA,qIAJgC,CAAxB;IAKA,IAAIuO,wBAAwB,GAAG,EAA/B;;IACA,KAAK,IAAIC,gBAAT,IAA6BF,iBAA7B,EAAgD;MAC5C,IAAItI,gBAAgB,CAACwI,gBAAD,CAAhB,CAAmCjJ,OAAnC,IAA8C,MAA9C,IAAwDS,gBAAgB,CAACwI,gBAAD,CAAhB,CAAmClJ,UAAnC,IAAiD,QAA7G,EACIiJ,wBAAwB,CAACnJ,IAAzB,CAA8BoJ,gBAA9B;IACP;;IACD,OAAOD,wBAAP;EACH;;EACoB,OAAdE,cAAc,GAAG;IACpB,KAAKC,MAAL,GAAc,KAAKA,MAAL,IAAe,GAA7B;IACA,OAAO,EAAE,KAAKA,MAAd;EACH;;AAheY;;AAkejB5O,UAAU,CAAC4O,MAAX,GAAoB,IAApB;AACA5O,UAAU,CAACsM,wBAAX,GAAsC,IAAtC;AACAtM,UAAU,CAAC2M,yBAAX,GAAuC,IAAvC;;AAEA,MAAMkC,6BAAN,CAAoC;EAChCC,WAAW,CAAC5O,OAAD,EAAU6O,QAAQ,GAAG,MAAM,CAAG,CAA9B,EAAgC;IACvC,KAAK7O,OAAL,GAAeA,OAAf;IACA,KAAK6O,QAAL,GAAgBA,QAAhB;EACH;;EACDC,kBAAkB,GAAG;IACjB,KAAKtK,iBAAL,GAAyB1E,UAAU,CAACyE,oBAAX,CAAgC,KAAKvE,OAArC,CAAzB;;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKgE,iBAAL,CAAuB/D,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;MACpD,KAAKgE,iBAAL,CAAuBhE,CAAvB,EAA0BuO,gBAA1B,CAA2C,QAA3C,EAAqD,KAAKF,QAA1D;IACH;EACJ;;EACDG,oBAAoB,GAAG;IACnB,IAAI,KAAKxK,iBAAT,EAA4B;MACxB,KAAK,IAAIhE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKgE,iBAAL,CAAuB/D,MAA3C,EAAmDD,CAAC,EAApD,EAAwD;QACpD,KAAKgE,iBAAL,CAAuBhE,CAAvB,EAA0ByO,mBAA1B,CAA8C,QAA9C,EAAwD,KAAKJ,QAA7D;MACH;IACJ;EACJ;;EACDK,OAAO,GAAG;IACN,KAAKF,oBAAL;IACA,KAAKhP,OAAL,GAAe,IAAf;IACA,KAAK6O,QAAL,GAAgB,IAAhB;IACA,KAAKrK,iBAAL,GAAyB,IAAzB;EACH;;AAvB+B;AA0BpC;AACA;AACA;;;AAEA,SAASmK,6BAAT,EAAwC7O,UAAxC"}, "metadata": {}, "sourceType": "module"}