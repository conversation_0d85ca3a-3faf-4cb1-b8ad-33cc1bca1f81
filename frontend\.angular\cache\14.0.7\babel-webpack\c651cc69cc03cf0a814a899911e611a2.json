{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { <PERSON><PERSON><PERSON><PERSON> } from 'primeng/dom';\nimport { PrimeTemplate } from 'primeng/api';\nconst _c0 = [\"element\"];\nconst _c1 = [\"content\"];\n\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c2 = function (a0, a1) {\n  return {\n    $implicit: a0,\n    options: a1\n  };\n};\n\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r4.loadedItems, ctx_r4.getContentOptions()));\n  }\n}\n\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    const index_r13 = ctx.index;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r11.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, item_r12, ctx_r11.getOptions(index_r13)));\n  }\n}\n\nconst _c3 = function (a0) {\n  return {\n    \"p-scroller-loading\": a0\n  };\n};\n\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 9);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c3, ctx_r6.d_loading))(\"ngStyle\", ctx_r6.contentStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.loadedItems)(\"ngForTrackBy\", ctx_r6._trackBy || ctx_r6.index);\n  }\n}\n\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 11);\n  }\n\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r7.spacerStyle);\n  }\n}\n\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c4 = function (a0) {\n  return {\n    numCols: a0\n  };\n};\n\nconst _c5 = function (a0) {\n  return {\n    options: a0\n  };\n};\n\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const index_r20 = ctx.index;\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r18.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c5, ctx_r18.getLoaderOptions(index_r20, ctx_r18.both && i0.ɵɵpureFunction1(2, _c4, ctx_r18._numItemsInViewport.cols))));\n  }\n}\n\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r15.loaderArr);\n  }\n}\n\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c6 = function () {\n  return {\n    styleClass: \"p-scroller-loading-icon\"\n  };\n};\n\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r22.loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c5, i0.ɵɵpureFunction0(2, _c6)));\n  }\n}\n\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 16);\n  }\n}\n\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 0, \"ng-template\", null, 15, i0.ɵɵtemplateRefExtractor);\n  }\n\n  if (rf & 2) {\n    const _r23 = i0.ɵɵreference(2);\n\n    const ctx_r17 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r17.loaderIconTemplate)(\"ngIfElse\", _r23);\n  }\n}\n\nconst _c7 = function (a0) {\n  return {\n    \"p-component-overlay\": a0\n  };\n};\n\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 13, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r16 = i0.ɵɵreference(3);\n\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c7, !ctx_r8.loaderTemplate));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.loaderTemplate)(\"ngIfElse\", _r16);\n  }\n}\n\nconst _c8 = function (a1, a2) {\n  return {\n    \"p-scroller\": true,\n    \"p-both-scroll\": a1,\n    \"p-horizontal-scroll\": a2\n  };\n};\n\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2, 3);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 0);\n    i0.ɵɵtemplate(4, Scroller_ng_container_0_ng_template_4_Template, 3, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵtemplate(6, Scroller_ng_container_0_div_6_Template, 1, 1, \"div\", 5);\n    i0.ɵɵtemplate(7, Scroller_ng_container_0_div_7_Template, 4, 5, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const _r5 = i0.ɵɵreference(5);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0._style)(\"ngClass\", i0.ɵɵpureFunction2(10, _c8, ctx_r0.both, ctx_r0.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r0._id)(\"tabindex\", ctx_r0.tabindex);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.contentTemplate)(\"ngIfElse\", _r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0._showSpacer);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.loaderDisabled && ctx_r0._showLoader && ctx_r0.d_loading);\n  }\n}\n\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\n\nconst _c9 = function (a0, a1) {\n  return {\n    rows: a0,\n    columns: a1\n  };\n};\n\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n\n  if (rf & 2) {\n    const ctx_r28 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r28.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c2, ctx_r28.items, i0.ɵɵpureFunction2(2, _c9, ctx_r28._items, ctx_r28.loadedColumns)));\n  }\n}\n\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.contentTemplate);\n  }\n}\n\nconst _c10 = [\"*\"];\n\nclass Scroller {\n  constructor(cd, zone) {\n    this.cd = cd;\n    this.zone = zone;\n    this.onLazyLoad = new EventEmitter();\n    this.onScroll = new EventEmitter();\n    this.onScrollIndexChange = new EventEmitter();\n    this._tabindex = 0;\n    this._itemSize = 0;\n    this._orientation = 'vertical';\n    this._delay = 0;\n    this._resizeDelay = 10;\n    this._lazy = false;\n    this._disabled = false;\n    this._loaderDisabled = false;\n    this._showSpacer = true;\n    this._showLoader = false;\n    this._autoSize = false;\n    this.d_loading = false;\n    this.first = 0;\n    this.last = 0;\n    this.numItemsInViewport = 0;\n    this.lastScrollPos = 0;\n    this.loaderArr = [];\n    this.spacerStyle = {};\n    this.contentStyle = {};\n    this.initialized = false;\n\n    if (!this._disabled) {\n      this.zone.runOutsideAngular(() => {\n        this.windowResizeListener = this.onWindowResize.bind(this);\n        window.addEventListener('resize', this.windowResizeListener);\n        window.addEventListener('orientationchange', this.windowResizeListener);\n      });\n    }\n  }\n\n  get id() {\n    return this._id;\n  }\n\n  set id(val) {\n    this._id = val;\n  }\n\n  get style() {\n    return this._style;\n  }\n\n  set style(val) {\n    this._style = val;\n  }\n\n  get styleClass() {\n    return this._styleClass;\n  }\n\n  set styleClass(val) {\n    this._styleClass = val;\n  }\n\n  get tabindex() {\n    return this._tabindex;\n  }\n\n  set tabindex(val) {\n    this._tabindex = val;\n  }\n\n  get items() {\n    return this._items;\n  }\n\n  set items(val) {\n    this._items = val;\n  }\n\n  get itemSize() {\n    return this._itemSize;\n  }\n\n  set itemSize(val) {\n    this._itemSize = val;\n  }\n\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n  }\n\n  get scrollWidth() {\n    return this._scrollWidth;\n  }\n\n  set scrollWidth(val) {\n    this._scrollWidth = val;\n  }\n\n  get orientation() {\n    return this._orientation;\n  }\n\n  set orientation(val) {\n    this._orientation = val;\n  }\n\n  get delay() {\n    return this._delay;\n  }\n\n  set delay(val) {\n    this._delay = val;\n  }\n\n  get resizeDelay() {\n    return this._resizeDelay;\n  }\n\n  set resizeDelay(val) {\n    this._resizeDelay = val;\n  }\n\n  get lazy() {\n    return this._lazy;\n  }\n\n  set lazy(val) {\n    this._lazy = val;\n  }\n\n  get disabled() {\n    return this._disabled;\n  }\n\n  set disabled(val) {\n    this._disabled = val;\n  }\n\n  get loaderDisabled() {\n    return this._loaderDisabled;\n  }\n\n  set loaderDisabled(val) {\n    this._loaderDisabled = val;\n  }\n\n  get columns() {\n    return this._columns;\n  }\n\n  set columns(val) {\n    this._columns = val;\n  }\n\n  get showSpacer() {\n    return this._showSpacer;\n  }\n\n  set showSpacer(val) {\n    this._showSpacer = val;\n  }\n\n  get showLoader() {\n    return this._showLoader;\n  }\n\n  set showLoader(val) {\n    this._showLoader = val;\n  }\n\n  get numToleratedItems() {\n    return this._numToleratedItems;\n  }\n\n  set numToleratedItems(val) {\n    this._numToleratedItems = val;\n  }\n\n  get loading() {\n    return this._loading;\n  }\n\n  set loading(val) {\n    this._loading = val;\n  }\n\n  get autoSize() {\n    return this._autoSize;\n  }\n\n  set autoSize(val) {\n    this._autoSize = val;\n  }\n\n  get trackBy() {\n    return this._trackBy;\n  }\n\n  set trackBy(val) {\n    this._trackBy = val;\n  }\n\n  get options() {\n    return this._options;\n  }\n\n  set options(val) {\n    this._options = val;\n\n    if (val && typeof val === 'object') {\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n\n  get vertical() {\n    return this._orientation === 'vertical';\n  }\n\n  get horizontal() {\n    return this._orientation === 'horizontal';\n  }\n\n  get both() {\n    return this._orientation === 'both';\n  }\n\n  get loadedItems() {\n    if (this._items && !this.d_loading) {\n      if (this.both) return this._items.slice(this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this.first, this.last);\n    }\n\n    return [];\n  }\n\n  get loadedRows() {\n    return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n  }\n\n  get loadedColumns() {\n    if (this._columns && (this.both || this.horizontal)) {\n      return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n    }\n\n    return this._columns;\n  }\n\n  ngOnInit() {\n    this.setInitialState();\n  }\n\n  ngOnChanges(simpleChanges) {\n    if (this.initialized) {\n      let areItemsChanged = false;\n\n      if (simpleChanges.items) {\n        const {\n          previousValue: prevItems,\n          currentValue: currentItems\n        } = simpleChanges.items;\n        areItemsChanged = !prevItems || prevItems.length !== (currentItems || []).length;\n      }\n\n      const isChanged = areItemsChanged || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth;\n      isChanged && this.init();\n    }\n\n    if (simpleChanges.orientation) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n    }\n\n    if (simpleChanges.loading) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.loading;\n\n      if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n        this.d_loading = currentValue;\n      }\n    }\n\n    if (simpleChanges.numToleratedItems) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.numToleratedItems;\n\n      if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue;\n      }\n    }\n\n    if (simpleChanges.options) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.options;\n\n      if (this.lazy && (previousValue === null || previousValue === void 0 ? void 0 : previousValue.loading) !== (currentValue === null || currentValue === void 0 ? void 0 : currentValue.loading) && (currentValue === null || currentValue === void 0 ? void 0 : currentValue.loading) !== this.d_loading) {\n        this.d_loading = currentValue.loading;\n      }\n\n      if ((previousValue === null || previousValue === void 0 ? void 0 : previousValue.numToleratedItems) !== (currentValue === null || currentValue === void 0 ? void 0 : currentValue.numToleratedItems) && (currentValue === null || currentValue === void 0 ? void 0 : currentValue.numToleratedItems) !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue.numToleratedItems;\n      }\n    }\n  }\n\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n\n        case 'loadericon':\n          this.loaderIconTemplate = item.template;\n          break;\n\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n\n  ngAfterViewInit() {\n    this.setContentEl(this.contentEl);\n    this.init();\n    this.defaultWidth = DomHandler.getWidth(this.elementViewChild.nativeElement);\n    this.defaultHeight = DomHandler.getHeight(this.elementViewChild.nativeElement);\n    this.initialized = true;\n  }\n\n  ngAfterViewChecked() {\n    this.calculateAutoSize();\n  }\n\n  ngOnDestroy() {\n    if (this.windowResizeListener) {\n      window.removeEventListener('resize', this.windowResizeListener);\n      window.removeEventListener('orientationchange', this.windowResizeListener);\n      this.windowResizeListener = null;\n    }\n  }\n\n  init() {\n    if (!this._disabled) {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n      this.cd.detectChanges();\n    }\n  }\n\n  setContentEl(el) {\n    var _a, _b;\n\n    this.contentEl = el || ((_a = this.contentViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) || DomHandler.findSingle((_b = this.elementViewChild) === null || _b === void 0 ? void 0 : _b.nativeElement, '.p-scroller-content');\n  }\n\n  setInitialState() {\n    this.first = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.last = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.numItemsInViewport = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.d_loading = this._loading || false;\n    this.d_numToleratedItems = this._numToleratedItems;\n  }\n\n  getElementRef() {\n    return this.elementViewChild;\n  }\n\n  scrollTo(options) {\n    var _a, _b;\n\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    (_b = (_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) === null || _b === void 0 ? void 0 : _b.scrollTo(options);\n  }\n\n  scrollToIndex(index, behavior = 'auto') {\n    const {\n      numToleratedItems\n    } = this.calculateNumItems();\n    const contentPos = this.getContentPosition();\n\n    const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n\n    const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n\n    const scrollTo = (left = 0, top = 0) => this.scrollTo({\n      left,\n      top,\n      behavior\n    });\n\n    if (this.both) {\n      const newFirst = {\n        rows: calculateFirst(index[0], numToleratedItems[0]),\n        cols: calculateFirst(index[1], numToleratedItems[1])\n      };\n\n      if (newFirst.rows !== this.first.rows || newFirst.cols !== this.first.cols) {\n        scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n      }\n    } else {\n      const newFirst = calculateFirst(index, numToleratedItems);\n\n      if (newFirst !== this.first) {\n        this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n      }\n    }\n  }\n\n  scrollInView(index, to, behavior = 'auto') {\n    if (to) {\n      const {\n        first,\n        viewport\n      } = this.getRenderedRange();\n\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n\n      const isToStart = to === 'to-start';\n      const isToEnd = to === 'to-end';\n\n      if (isToStart) {\n        if (this.both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.first - first > index) {\n            const pos = (viewport.first - 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      } else if (isToEnd) {\n        if (this.both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.last - first <= index + 1) {\n            const pos = (viewport.first + 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      }\n    } else {\n      this.scrollToIndex(index, behavior);\n    }\n  }\n\n  getRenderedRange() {\n    var _a;\n\n    const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n    let firstInViewport = this.first;\n    let lastInViewport = 0;\n\n    if ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) {\n      const {\n        scrollTop,\n        scrollLeft\n      } = this.elementViewChild.nativeElement;\n\n      if (this.both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + this.numItemsInViewport.rows,\n          cols: firstInViewport.cols + this.numItemsInViewport.cols\n        };\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n        lastInViewport = firstInViewport + this.numItemsInViewport;\n      }\n    }\n\n    return {\n      first: this.first,\n      last: this.last,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  }\n\n  calculateNumItems() {\n    var _a, _b;\n\n    const contentPos = this.getContentPosition();\n    const contentWidth = ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0;\n    const contentHeight = ((_b = this.elementViewChild) === null || _b === void 0 ? void 0 : _b.nativeElement) ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0;\n\n    const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n\n    const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n\n    const numItemsInViewport = this.both ? {\n      rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n    } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n    const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport,\n      numToleratedItems\n    };\n  }\n\n  calculateOptions() {\n    const {\n      numItemsInViewport,\n      numToleratedItems\n    } = this.calculateNumItems();\n\n    const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n\n    const first = this.first;\n    const last = this.both ? {\n      rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n    this.last = last;\n    this.numItemsInViewport = numItemsInViewport;\n    this.d_numToleratedItems = numToleratedItems;\n\n    if (this.showLoader) {\n      this.loaderArr = this.both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(() => Array.from({\n        length: numItemsInViewport.cols\n      })) : Array.from({\n        length: numItemsInViewport\n      });\n    }\n\n    if (this._lazy) {\n      this.handleEvents('onLazyLoad', {\n        first,\n        last\n      });\n    }\n  }\n\n  calculateAutoSize() {\n    if (this._autoSize && !this.d_loading) {\n      Promise.resolve().then(() => {\n        if (this.contentEl) {\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n          const {\n            offsetWidth,\n            offsetHeight\n          } = this.contentEl;\n          (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = (offsetWidth < this.defaultWidth ? offsetWidth : this.defaultWidth) + 'px');\n          (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = (offsetHeight < this.defaultHeight ? offsetHeight : this.defaultHeight) + 'px');\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n        }\n      });\n    }\n  }\n\n  getLast(last = 0, isCols = false) {\n    return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n  }\n\n  getContentPosition() {\n    if (this.contentEl) {\n      const style = getComputedStyle(this.contentEl);\n      const left = parseInt(style.paddingLeft, 10) + Math.max(parseInt(style.left, 10), 0);\n      const right = parseInt(style.paddingRight, 10) + Math.max(parseInt(style.right, 10), 0);\n      const top = parseInt(style.paddingTop, 10) + Math.max(parseInt(style.top, 10), 0);\n      const bottom = parseInt(style.paddingBottom, 10) + Math.max(parseInt(style.bottom, 10), 0);\n      return {\n        left,\n        right,\n        top,\n        bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  }\n\n  setSize() {\n    var _a;\n\n    if ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) {\n      const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n      const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n      const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n\n      const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n\n      if (this.both || this.horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  }\n\n  setSpacerSize() {\n    if (this._items) {\n      const contentPos = this.getContentPosition();\n\n      const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = Object.assign(Object.assign({}, this.spacerStyle), {\n        [`${_name}`]: (_value || []).length * _size + _cpos + 'px'\n      });\n\n      if (this.both) {\n        setProp('height', this._items, this._itemSize[0], contentPos.y);\n        setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n      } else {\n        this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n      }\n    }\n  }\n\n  setContentPosition(pos) {\n    if (this.contentEl) {\n      const first = pos ? pos.first : this.first;\n\n      const calculateTranslateVal = (_first, _size) => _first * _size;\n\n      const setTransform = (_x = 0, _y = 0) => this.contentStyle = Object.assign(Object.assign({}, this.contentStyle), {\n        transform: `translate3d(${_x}px, ${_y}px, 0)`\n      });\n\n      if (this.both) {\n        setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n      } else {\n        const translateVal = calculateTranslateVal(first, this._itemSize);\n        this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  }\n\n  onScrollPositionChange(event) {\n    const target = event.target;\n    const contentPos = this.getContentPosition();\n\n    const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n\n    const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n\n    const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n\n    const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n\n    const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n      let lastValue = _first + _num + 2 * _numT;\n\n      if (_currentIndex >= _numT) {\n        lastValue += _numT + 1;\n      }\n\n      return this.getLast(lastValue, _isCols);\n    };\n\n    const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    let newFirst = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    let newLast = this.last;\n    let isRangeChanged = false;\n    let newScrollPos = this.lastScrollPos;\n\n    if (this.both) {\n      const isScrollDown = this.lastScrollPos.top <= scrollTop;\n      const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n      const currentIndex = {\n        rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n        cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n      };\n      const triggerIndex = {\n        rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n        cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n      };\n      newFirst = {\n        rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n        cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n      };\n      newLast = {\n        rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n        cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n      };\n      isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols;\n      newScrollPos = {\n        top: scrollTop,\n        left: scrollLeft\n      };\n    } else {\n      const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n      const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n      const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n      const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n      newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n      newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n      isRangeChanged = newFirst !== this.first || newLast !== this.last;\n      newScrollPos = scrollPos;\n    }\n\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  }\n\n  onScrollChange(event) {\n    const {\n      first,\n      last,\n      isRangeChanged,\n      scrollPos\n    } = this.onScrollPositionChange(event);\n\n    if (isRangeChanged) {\n      const newState = {\n        first,\n        last\n      };\n      this.setContentPosition(newState);\n      this.first = first;\n      this.last = last;\n      this.lastScrollPos = scrollPos;\n      this.handleEvents('onScrollIndexChange', newState);\n\n      if (this._lazy) {\n        this.handleEvents('onLazyLoad', newState);\n      }\n    }\n  }\n\n  onContainerScroll(event) {\n    this.handleEvents('onScroll', {\n      originalEvent: event\n    });\n\n    if (this._delay) {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n\n      if (!this.d_loading && this.showLoader) {\n        const {\n          isRangeChanged: changed\n        } = this.onScrollPositionChange(event);\n\n        if (changed) {\n          this.d_loading = true;\n          this.cd.detectChanges();\n        }\n      }\n\n      this.scrollTimeout = setTimeout(() => {\n        this.onScrollChange(event);\n\n        if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n          this.d_loading = false;\n          this.cd.detectChanges();\n        }\n      }, this._delay);\n    } else {\n      this.onScrollChange(event);\n    }\n  }\n\n  onWindowResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n    }\n\n    this.resizeTimeout = setTimeout(() => {\n      if (this.elementViewChild) {\n        const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n        const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n        const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n        reinit && this.zone.run(() => {\n          this.d_numToleratedItems = this._numToleratedItems;\n          this.defaultWidth = width;\n          this.defaultHeight = height;\n          this.init();\n        });\n      }\n    }, this._resizeDelay);\n  }\n\n  handleEvents(name, params) {\n    return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n  }\n\n  getContentOptions() {\n    return {\n      contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n      items: this.loadedItems,\n      getItemOptions: index => this.getOptions(index),\n      loading: this.d_loading,\n      getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n      itemSize: this._itemSize,\n      rows: this.loadedRows,\n      columns: this.loadedColumns,\n      spacerStyle: this.spacerStyle,\n      contentStyle: this.contentStyle,\n      vertical: this.vertical,\n      horizontal: this.horizontal,\n      both: this.both\n    };\n  }\n\n  getOptions(renderedIndex) {\n    const count = (this._items || []).length;\n    const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    };\n  }\n\n  getLoaderOptions(index, extOptions) {\n    const count = this.loaderArr.length;\n    return Object.assign({\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    }, extOptions);\n  }\n\n}\n\nScroller.ɵfac = function Scroller_Factory(t) {\n  return new (t || Scroller)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n};\n\nScroller.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Scroller,\n  selectors: [[\"p-scroller\"]],\n  contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n    if (rf & 1) {\n      i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n    }\n  },\n  viewQuery: function Scroller_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(_c0, 5);\n      i0.ɵɵviewQuery(_c1, 5);\n    }\n\n    if (rf & 2) {\n      let _t;\n\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n    }\n  },\n  hostAttrs: [1, \"p-scroller-viewport\", \"p-element\"],\n  inputs: {\n    id: \"id\",\n    style: \"style\",\n    styleClass: \"styleClass\",\n    tabindex: \"tabindex\",\n    items: \"items\",\n    itemSize: \"itemSize\",\n    scrollHeight: \"scrollHeight\",\n    scrollWidth: \"scrollWidth\",\n    orientation: \"orientation\",\n    delay: \"delay\",\n    resizeDelay: \"resizeDelay\",\n    lazy: \"lazy\",\n    disabled: \"disabled\",\n    loaderDisabled: \"loaderDisabled\",\n    columns: \"columns\",\n    showSpacer: \"showSpacer\",\n    showLoader: \"showLoader\",\n    numToleratedItems: \"numToleratedItems\",\n    loading: \"loading\",\n    autoSize: \"autoSize\",\n    trackBy: \"trackBy\",\n    options: \"options\"\n  },\n  outputs: {\n    onLazyLoad: \"onLazyLoad\",\n    onScroll: \"onScroll\",\n    onScrollIndexChange: \"onScrollIndexChange\"\n  },\n  features: [i0.ɵɵNgOnChangesFeature],\n  ngContentSelectors: _c10,\n  decls: 3,\n  vars: 2,\n  consts: [[4, \"ngIf\", \"ngIfElse\"], [\"disabledContainer\", \"\"], [3, \"ngStyle\", \"ngClass\", \"scroll\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"class\", \"p-scroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-scroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-scroller-content\", 3, \"ngClass\", \"ngStyle\"], [\"content\", \"\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-scroller-spacer\", 3, \"ngStyle\"], [1, \"p-scroller-loader\", 3, \"ngClass\"], [\"buildInLoader\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"buildInLoaderIcon\", \"\"], [1, \"p-scroller-loading-icon\", \"pi\", \"pi-spinner\", \"pi-spin\"], [4, \"ngIf\"]],\n  template: function Scroller_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵprojectionDef();\n      i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 13, \"ng-container\", 0);\n      i0.ɵɵtemplate(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    }\n\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(2);\n\n      i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n  styles: [\"p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{font-size:2rem}\\n\"],\n  encapsulation: 2\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scroller, [{\n    type: Component,\n    args: [{\n      selector: 'p-scroller',\n      template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div #element [attr.id]=\"_id\" [attr.tabindex]=\"tabindex\" [ngStyle]=\"_style\" [class]=\"_styleClass\"\n                [ngClass]=\"{'p-scroller': true, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal}\"\n                (scroll)=\"onContainerScroll($event)\">\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: loadedItems, options: getContentOptions()}\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{'p-scroller-loading': d_loading}\" [ngStyle]=\"contentStyle\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item, options: getOptions(index)}\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{'p-component-overlay': !loaderTemplate}\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols })}\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: {options: { styleClass: 'p-scroller-loading-icon' }}\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <i class=\"p-scroller-loading-icon pi pi-spinner pi-spin\"></i>\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: items, options: {rows: _items, columns: loadedColumns}}\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-scroller-viewport p-element'\n      },\n      styles: [\"p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{font-size:2rem}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i0.ChangeDetectorRef\n    }, {\n      type: i0.NgZone\n    }];\n  }, {\n    id: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    scrollWidth: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    resizeDelay: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    loaderDisabled: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    showSpacer: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    numToleratedItems: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    autoSize: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    elementViewChild: [{\n      type: ViewChild,\n      args: ['element']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass ScrollerModule {}\n\nScrollerModule.ɵfac = function ScrollerModule_Factory(t) {\n  return new (t || ScrollerModule)();\n};\n\nScrollerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ScrollerModule\n});\nScrollerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [Scroller],\n      declarations: [Scroller]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Scroller, ScrollerModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "ViewChild", "ContentChildren", "Output", "NgModule", "i1", "CommonModule", "<PERSON><PERSON><PERSON><PERSON>", "PrimeTemplate", "<PERSON><PERSON><PERSON>", "constructor", "cd", "zone", "onLazyLoad", "onScroll", "onScrollIndexChange", "_tabindex", "_itemSize", "_orientation", "_delay", "_resizeDelay", "_lazy", "_disabled", "_loaderDisabled", "_showSpacer", "_showLoader", "_autoSize", "d_loading", "first", "last", "numItemsInViewport", "lastScrollPos", "loaderArr", "spacerStyle", "contentStyle", "initialized", "runOutsideAngular", "windowResizeListener", "onWindowResize", "bind", "window", "addEventListener", "id", "_id", "val", "style", "_style", "styleClass", "_styleClass", "tabindex", "items", "_items", "itemSize", "scrollHeight", "_scrollHeight", "scrollWidth", "_scrollWidth", "orientation", "delay", "resizeDelay", "lazy", "disabled", "loaderDisabled", "columns", "_columns", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "numToleratedItems", "_numToleratedItems", "loading", "_loading", "autoSize", "trackBy", "_trackBy", "options", "_options", "Object", "entries", "for<PERSON>ach", "k", "v", "vertical", "horizontal", "both", "loadedItems", "slice", "rows", "map", "item", "cols", "loadedRows", "loadedColumns", "ngOnInit", "setInitialState", "ngOnChanges", "simpleChanges", "areItemsChanged", "previousValue", "prevItems", "currentValue", "currentItems", "length", "isChanged", "init", "top", "left", "d_numToleratedItems", "ngAfterContentInit", "templates", "getType", "contentTemplate", "template", "itemTemplate", "loaderTemplate", "loaderIconTemplate", "ngAfterViewInit", "setContentEl", "contentEl", "defaultWidth", "getWidth", "elementViewChild", "nativeElement", "defaultHeight", "getHeight", "ngAfterViewChecked", "calculateAutoSize", "ngOnDestroy", "removeEventListener", "setSize", "calculateOptions", "setSpacerSize", "detectChanges", "el", "_a", "_b", "contentViewChild", "findSingle", "getElementRef", "scrollTo", "scrollToIndex", "index", "behavior", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "scrollInView", "to", "viewport", "getRenderedRange", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "Math", "floor", "firstInViewport", "lastInViewport", "scrollTop", "scrollLeft", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "ceil", "calculateNumToleratedItems", "_numItems", "calculateLast", "_num", "_isCols", "getLast", "Array", "from", "handleEvents", "Promise", "resolve", "then", "minHeight", "min<PERSON><PERSON><PERSON>", "width", "height", "isCols", "min", "getComputedStyle", "parseInt", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "parentElement", "setProp", "_name", "_value", "assign", "setContentPosition", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "isRangeChanged", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "newState", "onContainerScroll", "originalEvent", "scrollTimeout", "clearTimeout", "changed", "setTimeout", "undefined", "resizeTimeout", "isDiffWidth", "isDiffHeight", "reinit", "run", "name", "params", "emit", "getContentOptions", "contentStyleClass", "getItemOptions", "getOptions", "getLoaderOptions", "renderedIndex", "count", "even", "odd", "extOptions", "ɵfac", "ChangeDetectorRef", "NgZone", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "type", "args", "selector", "changeDetection", "<PERSON><PERSON><PERSON>", "encapsulation", "None", "host", "styles", "ScrollerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-scroller.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, ViewChild, ContentChildren, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport { DomHandler } from 'primeng/dom';\nimport { PrimeTemplate } from 'primeng/api';\n\nclass Scroller {\n    constructor(cd, zone) {\n        this.cd = cd;\n        this.zone = zone;\n        this.onLazyLoad = new EventEmitter();\n        this.onScroll = new EventEmitter();\n        this.onScrollIndexChange = new EventEmitter();\n        this._tabindex = 0;\n        this._itemSize = 0;\n        this._orientation = 'vertical';\n        this._delay = 0;\n        this._resizeDelay = 10;\n        this._lazy = false;\n        this._disabled = false;\n        this._loaderDisabled = false;\n        this._showSpacer = true;\n        this._showLoader = false;\n        this._autoSize = false;\n        this.d_loading = false;\n        this.first = 0;\n        this.last = 0;\n        this.numItemsInViewport = 0;\n        this.lastScrollPos = 0;\n        this.loaderArr = [];\n        this.spacerStyle = {};\n        this.contentStyle = {};\n        this.initialized = false;\n        if (!this._disabled) {\n            this.zone.runOutsideAngular(() => {\n                this.windowResizeListener = this.onWindowResize.bind(this);\n                window.addEventListener('resize', this.windowResizeListener);\n                window.addEventListener('orientationchange', this.windowResizeListener);\n            });\n        }\n    }\n    get id() { return this._id; }\n    set id(val) { this._id = val; }\n    get style() { return this._style; }\n    set style(val) { this._style = val; }\n    get styleClass() { return this._styleClass; }\n    set styleClass(val) { this._styleClass = val; }\n    get tabindex() { return this._tabindex; }\n    set tabindex(val) { this._tabindex = val; }\n    get items() { return this._items; }\n    set items(val) { this._items = val; }\n    get itemSize() { return this._itemSize; }\n    set itemSize(val) { this._itemSize = val; }\n    get scrollHeight() { return this._scrollHeight; }\n    set scrollHeight(val) { this._scrollHeight = val; }\n    get scrollWidth() { return this._scrollWidth; }\n    set scrollWidth(val) { this._scrollWidth = val; }\n    get orientation() { return this._orientation; }\n    set orientation(val) { this._orientation = val; }\n    get delay() { return this._delay; }\n    set delay(val) { this._delay = val; }\n    get resizeDelay() { return this._resizeDelay; }\n    set resizeDelay(val) { this._resizeDelay = val; }\n    get lazy() { return this._lazy; }\n    set lazy(val) { this._lazy = val; }\n    get disabled() { return this._disabled; }\n    set disabled(val) { this._disabled = val; }\n    get loaderDisabled() { return this._loaderDisabled; }\n    set loaderDisabled(val) { this._loaderDisabled = val; }\n    get columns() { return this._columns; }\n    set columns(val) { this._columns = val; }\n    get showSpacer() { return this._showSpacer; }\n    set showSpacer(val) { this._showSpacer = val; }\n    get showLoader() { return this._showLoader; }\n    set showLoader(val) { this._showLoader = val; }\n    get numToleratedItems() { return this._numToleratedItems; }\n    set numToleratedItems(val) { this._numToleratedItems = val; }\n    get loading() { return this._loading; }\n    set loading(val) { this._loading = val; }\n    get autoSize() { return this._autoSize; }\n    set autoSize(val) { this._autoSize = val; }\n    get trackBy() { return this._trackBy; }\n    set trackBy(val) { this._trackBy = val; }\n    get options() { return this._options; }\n    set options(val) {\n        this._options = val;\n        if (val && typeof val === 'object') {\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    get vertical() {\n        return this._orientation === 'vertical';\n    }\n    get horizontal() {\n        return this._orientation === 'horizontal';\n    }\n    get both() {\n        return this._orientation === 'both';\n    }\n    get loadedItems() {\n        if (this._items && !this.d_loading) {\n            if (this.both)\n                return this._items.slice(this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this.first.cols, this.last.cols));\n            else if (this.horizontal && this._columns)\n                return this._items;\n            else\n                return this._items.slice(this.first, this.last);\n        }\n        return [];\n    }\n    get loadedRows() {\n        return this.d_loading ? (this._loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n    }\n    get loadedColumns() {\n        if (this._columns && (this.both || this.horizontal)) {\n            return this.d_loading && this._loaderDisabled ?\n                (this.both ? this.loaderArr[0] : this.loaderArr) :\n                this._columns.slice((this.both ? this.first.cols : this.first), (this.both ? this.last.cols : this.last));\n        }\n        return this._columns;\n    }\n    ngOnInit() {\n        this.setInitialState();\n    }\n    ngOnChanges(simpleChanges) {\n        if (this.initialized) {\n            let areItemsChanged = false;\n            if (simpleChanges.items) {\n                const { previousValue: prevItems, currentValue: currentItems } = simpleChanges.items;\n                areItemsChanged = !prevItems || prevItems.length !== (currentItems || []).length;\n            }\n            const isChanged = (areItemsChanged || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n            isChanged && this.init();\n        }\n        if (simpleChanges.orientation) {\n            this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        }\n        if (simpleChanges.loading) {\n            const { previousValue, currentValue } = simpleChanges.loading;\n            if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n                this.d_loading = currentValue;\n            }\n        }\n        if (simpleChanges.numToleratedItems) {\n            const { previousValue, currentValue } = simpleChanges.numToleratedItems;\n            if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue;\n            }\n        }\n        if (simpleChanges.options) {\n            const { previousValue, currentValue } = simpleChanges.options;\n            if (this.lazy && (previousValue === null || previousValue === void 0 ? void 0 : previousValue.loading) !== (currentValue === null || currentValue === void 0 ? void 0 : currentValue.loading) && (currentValue === null || currentValue === void 0 ? void 0 : currentValue.loading) !== this.d_loading) {\n                this.d_loading = currentValue.loading;\n            }\n            if ((previousValue === null || previousValue === void 0 ? void 0 : previousValue.numToleratedItems) !== (currentValue === null || currentValue === void 0 ? void 0 : currentValue.numToleratedItems) && (currentValue === null || currentValue === void 0 ? void 0 : currentValue.numToleratedItems) !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue.numToleratedItems;\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                case 'loadericon':\n                    this.loaderIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        this.setContentEl(this.contentEl);\n        this.init();\n        this.defaultWidth = DomHandler.getWidth(this.elementViewChild.nativeElement);\n        this.defaultHeight = DomHandler.getHeight(this.elementViewChild.nativeElement);\n        this.initialized = true;\n    }\n    ngAfterViewChecked() {\n        this.calculateAutoSize();\n    }\n    ngOnDestroy() {\n        if (this.windowResizeListener) {\n            window.removeEventListener('resize', this.windowResizeListener);\n            window.removeEventListener('orientationchange', this.windowResizeListener);\n            this.windowResizeListener = null;\n        }\n    }\n    init() {\n        if (!this._disabled) {\n            this.setSize();\n            this.calculateOptions();\n            this.setSpacerSize();\n            this.cd.detectChanges();\n        }\n    }\n    setContentEl(el) {\n        var _a, _b;\n        this.contentEl = el || ((_a = this.contentViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) || DomHandler.findSingle((_b = this.elementViewChild) === null || _b === void 0 ? void 0 : _b.nativeElement, '.p-scroller-content');\n    }\n    setInitialState() {\n        this.first = this.both ? { rows: 0, cols: 0 } : 0;\n        this.last = this.both ? { rows: 0, cols: 0 } : 0;\n        this.numItemsInViewport = this.both ? { rows: 0, cols: 0 } : 0;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.d_loading = this._loading || false;\n        this.d_numToleratedItems = this._numToleratedItems;\n    }\n    getElementRef() {\n        return this.elementViewChild;\n    }\n    scrollTo(options) {\n        var _a, _b;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        (_b = (_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) === null || _b === void 0 ? void 0 : _b.scrollTo(options);\n    }\n    scrollToIndex(index, behavior = 'auto') {\n        const { numToleratedItems } = this.calculateNumItems();\n        const contentPos = this.getContentPosition();\n        const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n        const calculateCoord = (_first, _size, _cpos) => (_first * _size) + _cpos;\n        const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n        if (this.both) {\n            const newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n            if (newFirst.rows !== this.first.rows || newFirst.cols !== this.first.cols) {\n                scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n            }\n        }\n        else {\n            const newFirst = calculateFirst(index, numToleratedItems);\n            if (newFirst !== this.first) {\n                this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n            }\n        }\n    }\n    scrollInView(index, to, behavior = 'auto') {\n        if (to) {\n            const { first, viewport } = this.getRenderedRange();\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            const isToStart = to === 'to-start';\n            const isToEnd = to === 'to-end';\n            if (isToStart) {\n                if (this.both) {\n                    if (viewport.first.rows - first.rows > index[0]) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.first.cols - first.cols > index[1]) {\n                        scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.first - first > index) {\n                        const pos = (viewport.first - 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n            else if (isToEnd) {\n                if (this.both) {\n                    if (viewport.last.rows - first.rows <= index[0] + 1) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                        scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.last - first <= index + 1) {\n                        const pos = (viewport.first + 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n        }\n        else {\n            this.scrollToIndex(index, behavior);\n        }\n    }\n    getRenderedRange() {\n        var _a;\n        const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        let firstInViewport = this.first;\n        let lastInViewport = 0;\n        if ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) {\n            const { scrollTop, scrollLeft } = this.elementViewChild.nativeElement;\n            if (this.both) {\n                firstInViewport = { rows: calculateFirstInViewport(scrollTop, this._itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this._itemSize[1]) };\n                lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n            }\n            else {\n                const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n                firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n                lastInViewport = firstInViewport + this.numItemsInViewport;\n            }\n        }\n        return {\n            first: this.first,\n            last: this.last,\n            viewport: {\n                first: firstInViewport,\n                last: lastInViewport\n            }\n        };\n    }\n    calculateNumItems() {\n        var _a, _b;\n        const contentPos = this.getContentPosition();\n        const contentWidth = ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0;\n        const contentHeight = ((_b = this.elementViewChild) === null || _b === void 0 ? void 0 : _b.nativeElement) ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0;\n        const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n        const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n        const numItemsInViewport = this.both ?\n            { rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1]) } :\n            calculateNumItemsInViewport((this.horizontal ? contentWidth : contentHeight), this._itemSize);\n        const numToleratedItems = this.d_numToleratedItems || (this.both ?\n            [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] :\n            calculateNumToleratedItems(numItemsInViewport));\n        return { numItemsInViewport, numToleratedItems };\n    }\n    calculateOptions() {\n        const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n        const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + ((_first < _numT ? 2 : 3) * _numT), _isCols);\n        const first = this.first;\n        const last = this.both ?\n            { rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true) } :\n            calculateLast(this.first, numItemsInViewport, numToleratedItems);\n        this.last = last;\n        this.numItemsInViewport = numItemsInViewport;\n        this.d_numToleratedItems = numToleratedItems;\n        if (this.showLoader) {\n            this.loaderArr = this.both ?\n                Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) :\n                Array.from({ length: numItemsInViewport });\n        }\n        if (this._lazy) {\n            this.handleEvents('onLazyLoad', { first, last });\n        }\n    }\n    calculateAutoSize() {\n        if (this._autoSize && !this.d_loading) {\n            Promise.resolve().then(() => {\n                if (this.contentEl) {\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n                    const { offsetWidth, offsetHeight } = this.contentEl;\n                    (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = (offsetWidth < this.defaultWidth ? offsetWidth : this.defaultWidth) + 'px');\n                    (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = (offsetHeight < this.defaultHeight ? offsetHeight : this.defaultHeight) + 'px');\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n                }\n            });\n        }\n    }\n    getLast(last = 0, isCols = false) {\n        return this._items ? Math.min((isCols ? (this._columns || this._items[0]).length : this._items.length), last) : 0;\n    }\n    getContentPosition() {\n        if (this.contentEl) {\n            const style = getComputedStyle(this.contentEl);\n            const left = parseInt(style.paddingLeft, 10) + Math.max(parseInt(style.left, 10), 0);\n            const right = parseInt(style.paddingRight, 10) + Math.max(parseInt(style.right, 10), 0);\n            const top = parseInt(style.paddingTop, 10) + Math.max(parseInt(style.top, 10), 0);\n            const bottom = parseInt(style.paddingBottom, 10) + Math.max(parseInt(style.bottom, 10), 0);\n            return { left, right, top, bottom, x: left + right, y: top + bottom };\n        }\n        return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n    }\n    setSize() {\n        var _a;\n        if ((_a = this.elementViewChild) === null || _a === void 0 ? void 0 : _a.nativeElement) {\n            const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n            const width = this._scrollWidth || `${(this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth)}px`;\n            const height = this._scrollHeight || `${(this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight)}px`;\n            const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n            if (this.both || this.horizontal) {\n                setProp('height', height);\n                setProp('width', width);\n            }\n            else {\n                setProp('height', height);\n            }\n        }\n    }\n    setSpacerSize() {\n        if (this._items) {\n            const contentPos = this.getContentPosition();\n            const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = Object.assign(Object.assign({}, this.spacerStyle), { [`${_name}`]: (((_value || []).length * _size) + _cpos) + 'px' });\n            if (this.both) {\n                setProp('height', this._items, this._itemSize[0], contentPos.y);\n                setProp('width', (this._columns || this._items[1]), this._itemSize[1], contentPos.x);\n            }\n            else {\n                this.horizontal ? setProp('width', (this._columns || this._items), this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n            }\n        }\n    }\n    setContentPosition(pos) {\n        if (this.contentEl) {\n            const first = pos ? pos.first : this.first;\n            const calculateTranslateVal = (_first, _size) => (_first * _size);\n            const setTransform = (_x = 0, _y = 0) => this.contentStyle = Object.assign(Object.assign({}, this.contentStyle), { transform: `translate3d(${_x}px, ${_y}px, 0)` });\n            if (this.both) {\n                setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n            }\n            else {\n                const translateVal = calculateTranslateVal(first, this._itemSize);\n                this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n            }\n        }\n    }\n    onScrollPositionChange(event) {\n        const target = event.target;\n        const contentPos = this.getContentPosition();\n        const calculateScrollPos = (_pos, _cpos) => _pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0;\n        const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            return (_currentIndex <= _numT ? _numT : (_isScrollDownOrRight ? (_last - _num - _numT) : (_first + _numT - 1)));\n        };\n        const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            if (_currentIndex <= _numT)\n                return 0;\n            else\n                return Math.max(0, _isScrollDownOrRight ?\n                    (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) :\n                    (_currentIndex > _triggerIndex ? _first : _currentIndex - (2 * _numT)));\n        };\n        const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n            let lastValue = _first + _num + (2 * _numT);\n            if (_currentIndex >= _numT) {\n                lastValue += (_numT + 1);\n            }\n            return this.getLast(lastValue, _isCols);\n        };\n        const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n        const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n        let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n        let newLast = this.last;\n        let isRangeChanged = false;\n        let newScrollPos = this.lastScrollPos;\n        if (this.both) {\n            const isScrollDown = this.lastScrollPos.top <= scrollTop;\n            const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n            const currentIndex = { rows: calculateCurrentIndex(scrollTop, this._itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this._itemSize[1]) };\n            const triggerIndex = {\n                rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n            };\n            newFirst = {\n                rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n            };\n            newLast = {\n                rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n            };\n            isRangeChanged = (newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows) || (newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols);\n            newScrollPos = { top: scrollTop, left: scrollLeft };\n        }\n        else {\n            const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n            const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n            const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n            const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n            newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n            newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n            isRangeChanged = newFirst !== this.first || newLast !== this.last;\n            newScrollPos = scrollPos;\n        }\n        return {\n            first: newFirst,\n            last: newLast,\n            isRangeChanged,\n            scrollPos: newScrollPos\n        };\n    }\n    onScrollChange(event) {\n        const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n        if (isRangeChanged) {\n            const newState = { first, last };\n            this.setContentPosition(newState);\n            this.first = first;\n            this.last = last;\n            this.lastScrollPos = scrollPos;\n            this.handleEvents('onScrollIndexChange', newState);\n            if (this._lazy) {\n                this.handleEvents('onLazyLoad', newState);\n            }\n        }\n    }\n    onContainerScroll(event) {\n        this.handleEvents('onScroll', { originalEvent: event });\n        if (this._delay) {\n            if (this.scrollTimeout) {\n                clearTimeout(this.scrollTimeout);\n            }\n            if (!this.d_loading && this.showLoader) {\n                const { isRangeChanged: changed } = this.onScrollPositionChange(event);\n                if (changed) {\n                    this.d_loading = true;\n                    this.cd.detectChanges();\n                }\n            }\n            this.scrollTimeout = setTimeout(() => {\n                this.onScrollChange(event);\n                if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n                    this.d_loading = false;\n                    this.cd.detectChanges();\n                }\n            }, this._delay);\n        }\n        else {\n            this.onScrollChange(event);\n        }\n    }\n    onWindowResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n        }\n        this.resizeTimeout = setTimeout(() => {\n            if (this.elementViewChild) {\n                const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n                const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                const reinit = this.both ? (isDiffWidth || isDiffHeight) : (this.horizontal ? isDiffWidth : (this.vertical ? isDiffHeight : false));\n                reinit && this.zone.run(() => {\n                    this.d_numToleratedItems = this._numToleratedItems;\n                    this.defaultWidth = width;\n                    this.defaultHeight = height;\n                    this.init();\n                });\n            }\n        }, this._resizeDelay);\n    }\n    handleEvents(name, params) {\n        return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n    }\n    getContentOptions() {\n        return {\n            contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n            items: this.loadedItems,\n            getItemOptions: (index) => this.getOptions(index),\n            loading: this.d_loading,\n            getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n            itemSize: this._itemSize,\n            rows: this.loadedRows,\n            columns: this.loadedColumns,\n            spacerStyle: this.spacerStyle,\n            contentStyle: this.contentStyle,\n            vertical: this.vertical,\n            horizontal: this.horizontal,\n            both: this.both\n        };\n    }\n    getOptions(renderedIndex) {\n        const count = (this._items || []).length;\n        const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === (count - 1),\n            even: index % 2 === 0,\n            odd: index % 2 !== 0\n        };\n    }\n    getLoaderOptions(index, extOptions) {\n        const count = this.loaderArr.length;\n        return Object.assign({ index,\n            count, first: index === 0, last: index === (count - 1), even: index % 2 === 0, odd: index % 2 !== 0 }, extOptions);\n    }\n}\nScroller.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Scroller, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\nScroller.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Scroller, selector: \"p-scroller\", inputs: { id: \"id\", style: \"style\", styleClass: \"styleClass\", tabindex: \"tabindex\", items: \"items\", itemSize: \"itemSize\", scrollHeight: \"scrollHeight\", scrollWidth: \"scrollWidth\", orientation: \"orientation\", delay: \"delay\", resizeDelay: \"resizeDelay\", lazy: \"lazy\", disabled: \"disabled\", loaderDisabled: \"loaderDisabled\", columns: \"columns\", showSpacer: \"showSpacer\", showLoader: \"showLoader\", numToleratedItems: \"numToleratedItems\", loading: \"loading\", autoSize: \"autoSize\", trackBy: \"trackBy\", options: \"options\" }, outputs: { onLazyLoad: \"onLazyLoad\", onScroll: \"onScroll\", onScrollIndexChange: \"onScrollIndexChange\" }, host: { classAttribute: \"p-scroller-viewport p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"elementViewChild\", first: true, predicate: [\"element\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div #element [attr.id]=\"_id\" [attr.tabindex]=\"tabindex\" [ngStyle]=\"_style\" [class]=\"_styleClass\"\n                [ngClass]=\"{'p-scroller': true, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal}\"\n                (scroll)=\"onContainerScroll($event)\">\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: loadedItems, options: getContentOptions()}\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{'p-scroller-loading': d_loading}\" [ngStyle]=\"contentStyle\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item, options: getOptions(index)}\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{'p-component-overlay': !loaderTemplate}\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols })}\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: {options: { styleClass: 'p-scroller-loading-icon' }}\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <i class=\"p-scroller-loading-icon pi pi-spinner pi-spin\"></i>\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: items, options: {rows: _items, columns: loadedColumns}}\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, isInline: true, styles: [\"p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{font-size:2rem}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Scroller, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-scroller', template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div #element [attr.id]=\"_id\" [attr.tabindex]=\"tabindex\" [ngStyle]=\"_style\" [class]=\"_styleClass\"\n                [ngClass]=\"{'p-scroller': true, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal}\"\n                (scroll)=\"onContainerScroll($event)\">\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: loadedItems, options: getContentOptions()}\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{'p-scroller-loading': d_loading}\" [ngStyle]=\"contentStyle\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: {$implicit: item, options: getOptions(index)}\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{'p-component-overlay': !loaderTemplate}\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: {options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols })}\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: {options: { styleClass: 'p-scroller-loading-icon' }}\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <i class=\"p-scroller-loading-icon pi pi-spinner pi-spin\"></i>\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: {$implicit: items, options: {rows: _items, columns: loadedColumns}}\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-scroller-viewport p-element'\n                    }, styles: [\"p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{font-size:2rem}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i0.ChangeDetectorRef }, { type: i0.NgZone }]; }, propDecorators: { id: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], scrollWidth: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], resizeDelay: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], loaderDisabled: [{\n                type: Input\n            }], columns: [{\n                type: Input\n            }], showSpacer: [{\n                type: Input\n            }], showLoader: [{\n                type: Input\n            }], numToleratedItems: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], autoSize: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], elementViewChild: [{\n                type: ViewChild,\n                args: ['element']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], onLazyLoad: [{\n                type: Output\n            }], onScroll: [{\n                type: Output\n            }], onScrollIndexChange: [{\n                type: Output\n            }] } });\nclass ScrollerModule {\n}\nScrollerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ScrollerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nScrollerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ScrollerModule, declarations: [Scroller], imports: [CommonModule], exports: [Scroller] });\nScrollerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ScrollerModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ScrollerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Scroller],\n                    declarations: [Scroller]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,SAArF,EAAgGC,eAAhG,EAAiHC,MAAjH,EAAyHC,QAAzH,QAAyI,eAAzI;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,SAASC,UAAT,QAA2B,aAA3B;AACA,SAASC,aAAT,QAA8B,aAA9B;;;;;;IA6jB2Fb,EAOvE,sB;;;;;;;;;;;;;IAPuEA,EAM3E,2B;IAN2EA,EAOvE,uG;IAPuEA,EAQ3E,wB;;;;mBAR2EA,E;IAAAA,EAOxD,a;IAPwDA,EAOxD,mFAPwDA,EAOxD,yE;;;;;;IAPwDA,EAY/D,sB;;;;;;IAZ+DA,EAWnE,2B;IAXmEA,EAY/D,qH;IAZ+DA,EAanE,wB;;;;;;oBAbmEA,E;IAAAA,EAYhD,a;IAZgDA,EAYhD,iFAZgDA,EAYhD,kE;;;;;;;;;;;;IAZgDA,EAUvE,+B;IAVuEA,EAWnE,uG;IAXmEA,EAcvE,e;;;;mBAduEA,E;IAAAA,EAU9B,uBAV8BA,EAU9B,2E;IAV8BA,EAWpC,a;IAXoCA,EAWpC,2F;;;;;;IAXoCA,EAgB3E,wB;;;;mBAhB2EA,E;IAAAA,EAgBxB,0C;;;;;;IAhBwBA,EAoB/D,sB;;;;;;;;;;;;;;;;;;IApB+DA,EAmBnE,2B;IAnBmEA,EAoB/D,4H;IApB+DA,EAqBnE,wB;;;;;oBArBmEA,E;IAAAA,EAoBhD,a;IApBgDA,EAoBhD,mFApBgDA,EAoBhD,6EApBgDA,EAoBhD,6D;;;;;;IApBgDA,EAkBvE,2B;IAlBuEA,EAmBnE,8G;IAnBmEA,EAsBvE,wB;;;;oBAtBuEA,E;IAAAA,EAmBpC,a;IAnBoCA,EAmBpC,yC;;;;;;IAnBoCA,EAyB/D,sB;;;;;;;;;;;;IAzB+DA,EAwBnE,2B;IAxBmEA,EAyB/D,2H;IAzB+DA,EA0BnE,wB;;;;oBA1BmEA,E;IAAAA,EAyBhD,a;IAzBgDA,EAyBhD,uFAzBgDA,EAyBhD,yBAzBgDA,EAyBhD,0B;;;;;;IAzBgDA,EA4B/D,sB;;;;;;IA5B+DA,EAwBnE,4G;IAxBmEA,EA2BnE,kHA3BmEA,EA2BnE,wB;;;;iBA3BmEA,E;;oBAAAA,E;IAAAA,EAwBpD,iE;;;;;;;;;;;;IAxBoDA,EAiB3E,6B;IAjB2EA,EAkBvE,8F;IAlBuEA,EAuBvE,oGAvBuEA,EAuBvE,wB;IAvBuEA,EA+B3E,e;;;;iBA/B2EA,E;;mBAAAA,E;IAAAA,EAiBQ,uBAjBRA,EAiBQ,iD;IAjBRA,EAkBxD,a;IAlBwDA,EAkBxD,4D;;;;;;;;;;;;;;iBAlBwDA,E;;IAAAA,EAEnF,2B;IAFmFA,EAG/E,+B;IAH+EA,EAK3E;MAL2EA,EAK3E;MAAA,gBAL2EA,EAK3E;MAAA,OAL2EA,EAKjE,+CAAV;IAAA,E;IAL2EA,EAM3E,wF;IAN2EA,EAS3E,6FAT2EA,EAS3E,wB;IAT2EA,EAgB3E,sE;IAhB2EA,EAiB3E,sE;IAjB2EA,EAgC/E,e;IAhC+EA,EAiCnF,wB;;;;gBAjCmFA,E;;mBAAAA,E;IAAAA,EAGH,a;IAHGA,EAGH,+B;IAHGA,EAGtB,iDAHsBA,EAGtB,0D;IAHsBA,EAGjE,2D;IAHiEA,EAM5D,a;IAN4DA,EAM5D,4D;IAN4DA,EAgBrE,a;IAhBqEA,EAgBrE,uC;IAhBqEA,EAiBrE,a;IAjBqEA,EAiBrE,qF;;;;;;IAjBqEA,EAqC3E,sB;;;;;;;;;;;;;IArC2EA,EAoC/E,2B;IApC+EA,EAqC3E,sG;IArC2EA,EAsC/E,wB;;;;oBAtC+EA,E;IAAAA,EAqC5D,a;IArC4DA,EAqC5D,oFArC4DA,EAqC5D,wCArC4DA,EAqC5D,iE;;;;;;IArC4DA,EAmC/E,gB;IAnC+EA,EAoC/E,wF;;;;mBApC+EA,E;IAAAA,EAoChE,a;IApCgEA,EAoChE,2C;;;;;;AA/lB3B,MAAMc,QAAN,CAAe;EACXC,WAAW,CAACC,EAAD,EAAKC,IAAL,EAAW;IAClB,KAAKD,EAAL,GAAUA,EAAV;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,UAAL,GAAkB,IAAIjB,YAAJ,EAAlB;IACA,KAAKkB,QAAL,GAAgB,IAAIlB,YAAJ,EAAhB;IACA,KAAKmB,mBAAL,GAA2B,IAAInB,YAAJ,EAA3B;IACA,KAAKoB,SAAL,GAAiB,CAAjB;IACA,KAAKC,SAAL,GAAiB,CAAjB;IACA,KAAKC,YAAL,GAAoB,UAApB;IACA,KAAKC,MAAL,GAAc,CAAd;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,KAAL,GAAa,KAAb;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,eAAL,GAAuB,KAAvB;IACA,KAAKC,WAAL,GAAmB,IAAnB;IACA,KAAKC,WAAL,GAAmB,KAAnB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,SAAL,GAAiB,KAAjB;IACA,KAAKC,KAAL,GAAa,CAAb;IACA,KAAKC,IAAL,GAAY,CAAZ;IACA,KAAKC,kBAAL,GAA0B,CAA1B;IACA,KAAKC,aAAL,GAAqB,CAArB;IACA,KAAKC,SAAL,GAAiB,EAAjB;IACA,KAAKC,WAAL,GAAmB,EAAnB;IACA,KAAKC,YAAL,GAAoB,EAApB;IACA,KAAKC,WAAL,GAAmB,KAAnB;;IACA,IAAI,CAAC,KAAKb,SAAV,EAAqB;MACjB,KAAKV,IAAL,CAAUwB,iBAAV,CAA4B,MAAM;QAC9B,KAAKC,oBAAL,GAA4B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA5B;QACAC,MAAM,CAACC,gBAAP,CAAwB,QAAxB,EAAkC,KAAKJ,oBAAvC;QACAG,MAAM,CAACC,gBAAP,CAAwB,mBAAxB,EAA6C,KAAKJ,oBAAlD;MACH,CAJD;IAKH;EACJ;;EACK,IAAFK,EAAE,GAAG;IAAE,OAAO,KAAKC,GAAZ;EAAkB;;EACvB,IAAFD,EAAE,CAACE,GAAD,EAAM;IAAE,KAAKD,GAAL,GAAWC,GAAX;EAAiB;;EACtB,IAALC,KAAK,GAAG;IAAE,OAAO,KAAKC,MAAZ;EAAqB;;EAC1B,IAALD,KAAK,CAACD,GAAD,EAAM;IAAE,KAAKE,MAAL,GAAcF,GAAd;EAAoB;;EACvB,IAAVG,UAAU,GAAG;IAAE,OAAO,KAAKC,WAAZ;EAA0B;;EAC/B,IAAVD,UAAU,CAACH,GAAD,EAAM;IAAE,KAAKI,WAAL,GAAmBJ,GAAnB;EAAyB;;EACnC,IAARK,QAAQ,GAAG;IAAE,OAAO,KAAKjC,SAAZ;EAAwB;;EAC7B,IAARiC,QAAQ,CAACL,GAAD,EAAM;IAAE,KAAK5B,SAAL,GAAiB4B,GAAjB;EAAuB;;EAClC,IAALM,KAAK,GAAG;IAAE,OAAO,KAAKC,MAAZ;EAAqB;;EAC1B,IAALD,KAAK,CAACN,GAAD,EAAM;IAAE,KAAKO,MAAL,GAAcP,GAAd;EAAoB;;EACzB,IAARQ,QAAQ,GAAG;IAAE,OAAO,KAAKnC,SAAZ;EAAwB;;EAC7B,IAARmC,QAAQ,CAACR,GAAD,EAAM;IAAE,KAAK3B,SAAL,GAAiB2B,GAAjB;EAAuB;;EAC3B,IAAZS,YAAY,GAAG;IAAE,OAAO,KAAKC,aAAZ;EAA4B;;EACjC,IAAZD,YAAY,CAACT,GAAD,EAAM;IAAE,KAAKU,aAAL,GAAqBV,GAArB;EAA2B;;EACpC,IAAXW,WAAW,GAAG;IAAE,OAAO,KAAKC,YAAZ;EAA2B;;EAChC,IAAXD,WAAW,CAACX,GAAD,EAAM;IAAE,KAAKY,YAAL,GAAoBZ,GAApB;EAA0B;;EAClC,IAAXa,WAAW,GAAG;IAAE,OAAO,KAAKvC,YAAZ;EAA2B;;EAChC,IAAXuC,WAAW,CAACb,GAAD,EAAM;IAAE,KAAK1B,YAAL,GAAoB0B,GAApB;EAA0B;;EACxC,IAALc,KAAK,GAAG;IAAE,OAAO,KAAKvC,MAAZ;EAAqB;;EAC1B,IAALuC,KAAK,CAACd,GAAD,EAAM;IAAE,KAAKzB,MAAL,GAAcyB,GAAd;EAAoB;;EACtB,IAAXe,WAAW,GAAG;IAAE,OAAO,KAAKvC,YAAZ;EAA2B;;EAChC,IAAXuC,WAAW,CAACf,GAAD,EAAM;IAAE,KAAKxB,YAAL,GAAoBwB,GAApB;EAA0B;;EACzC,IAAJgB,IAAI,GAAG;IAAE,OAAO,KAAKvC,KAAZ;EAAoB;;EACzB,IAAJuC,IAAI,CAAChB,GAAD,EAAM;IAAE,KAAKvB,KAAL,GAAauB,GAAb;EAAmB;;EACvB,IAARiB,QAAQ,GAAG;IAAE,OAAO,KAAKvC,SAAZ;EAAwB;;EAC7B,IAARuC,QAAQ,CAACjB,GAAD,EAAM;IAAE,KAAKtB,SAAL,GAAiBsB,GAAjB;EAAuB;;EACzB,IAAdkB,cAAc,GAAG;IAAE,OAAO,KAAKvC,eAAZ;EAA8B;;EACnC,IAAduC,cAAc,CAAClB,GAAD,EAAM;IAAE,KAAKrB,eAAL,GAAuBqB,GAAvB;EAA6B;;EAC5C,IAAPmB,OAAO,GAAG;IAAE,OAAO,KAAKC,QAAZ;EAAuB;;EAC5B,IAAPD,OAAO,CAACnB,GAAD,EAAM;IAAE,KAAKoB,QAAL,GAAgBpB,GAAhB;EAAsB;;EAC3B,IAAVqB,UAAU,GAAG;IAAE,OAAO,KAAKzC,WAAZ;EAA0B;;EAC/B,IAAVyC,UAAU,CAACrB,GAAD,EAAM;IAAE,KAAKpB,WAAL,GAAmBoB,GAAnB;EAAyB;;EACjC,IAAVsB,UAAU,GAAG;IAAE,OAAO,KAAKzC,WAAZ;EAA0B;;EAC/B,IAAVyC,UAAU,CAACtB,GAAD,EAAM;IAAE,KAAKnB,WAAL,GAAmBmB,GAAnB;EAAyB;;EAC1B,IAAjBuB,iBAAiB,GAAG;IAAE,OAAO,KAAKC,kBAAZ;EAAiC;;EACtC,IAAjBD,iBAAiB,CAACvB,GAAD,EAAM;IAAE,KAAKwB,kBAAL,GAA0BxB,GAA1B;EAAgC;;EAClD,IAAPyB,OAAO,GAAG;IAAE,OAAO,KAAKC,QAAZ;EAAuB;;EAC5B,IAAPD,OAAO,CAACzB,GAAD,EAAM;IAAE,KAAK0B,QAAL,GAAgB1B,GAAhB;EAAsB;;EAC7B,IAAR2B,QAAQ,GAAG;IAAE,OAAO,KAAK7C,SAAZ;EAAwB;;EAC7B,IAAR6C,QAAQ,CAAC3B,GAAD,EAAM;IAAE,KAAKlB,SAAL,GAAiBkB,GAAjB;EAAuB;;EAChC,IAAP4B,OAAO,GAAG;IAAE,OAAO,KAAKC,QAAZ;EAAuB;;EAC5B,IAAPD,OAAO,CAAC5B,GAAD,EAAM;IAAE,KAAK6B,QAAL,GAAgB7B,GAAhB;EAAsB;;EAC9B,IAAP8B,OAAO,GAAG;IAAE,OAAO,KAAKC,QAAZ;EAAuB;;EAC5B,IAAPD,OAAO,CAAC9B,GAAD,EAAM;IACb,KAAK+B,QAAL,GAAgB/B,GAAhB;;IACA,IAAIA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA1B,EAAoC;MAChCgC,MAAM,CAACC,OAAP,CAAejC,GAAf,EAAoBkC,OAApB,CAA4B,CAAC,CAACC,CAAD,EAAIC,CAAJ,CAAD,KAAY,KAAM,IAAGD,CAAE,EAAX,MAAkBC,CAAlB,KAAwB,KAAM,IAAGD,CAAE,EAAX,IAAgBC,CAAxC,CAAxC;IACH;EACJ;;EACW,IAARC,QAAQ,GAAG;IACX,OAAO,KAAK/D,YAAL,KAAsB,UAA7B;EACH;;EACa,IAAVgE,UAAU,GAAG;IACb,OAAO,KAAKhE,YAAL,KAAsB,YAA7B;EACH;;EACO,IAAJiE,IAAI,GAAG;IACP,OAAO,KAAKjE,YAAL,KAAsB,MAA7B;EACH;;EACc,IAAXkE,WAAW,GAAG;IACd,IAAI,KAAKjC,MAAL,IAAe,CAAC,KAAKxB,SAAzB,EAAoC;MAChC,IAAI,KAAKwD,IAAT,EACI,OAAO,KAAKhC,MAAL,CAAYkC,KAAZ,CAAkB,KAAKzD,KAAL,CAAW0D,IAA7B,EAAmC,KAAKzD,IAAL,CAAUyD,IAA7C,EAAmDC,GAAnD,CAAuDC,IAAI,IAAI,KAAKxB,QAAL,GAAgBwB,IAAhB,GAAuBA,IAAI,CAACH,KAAL,CAAW,KAAKzD,KAAL,CAAW6D,IAAtB,EAA4B,KAAK5D,IAAL,CAAU4D,IAAtC,CAAtF,CAAP,CADJ,KAEK,IAAI,KAAKP,UAAL,IAAmB,KAAKlB,QAA5B,EACD,OAAO,KAAKb,MAAZ,CADC,KAGD,OAAO,KAAKA,MAAL,CAAYkC,KAAZ,CAAkB,KAAKzD,KAAvB,EAA8B,KAAKC,IAAnC,CAAP;IACP;;IACD,OAAO,EAAP;EACH;;EACa,IAAV6D,UAAU,GAAG;IACb,OAAO,KAAK/D,SAAL,GAAkB,KAAKJ,eAAL,GAAuB,KAAKS,SAA5B,GAAwC,EAA1D,GAAgE,KAAKoD,WAA5E;EACH;;EACgB,IAAbO,aAAa,GAAG;IAChB,IAAI,KAAK3B,QAAL,KAAkB,KAAKmB,IAAL,IAAa,KAAKD,UAApC,CAAJ,EAAqD;MACjD,OAAO,KAAKvD,SAAL,IAAkB,KAAKJ,eAAvB,GACF,KAAK4D,IAAL,GAAY,KAAKnD,SAAL,CAAe,CAAf,CAAZ,GAAgC,KAAKA,SADnC,GAEH,KAAKgC,QAAL,CAAcqB,KAAd,CAAqB,KAAKF,IAAL,GAAY,KAAKvD,KAAL,CAAW6D,IAAvB,GAA8B,KAAK7D,KAAxD,EAAiE,KAAKuD,IAAL,GAAY,KAAKtD,IAAL,CAAU4D,IAAtB,GAA6B,KAAK5D,IAAnG,CAFJ;IAGH;;IACD,OAAO,KAAKmC,QAAZ;EACH;;EACD4B,QAAQ,GAAG;IACP,KAAKC,eAAL;EACH;;EACDC,WAAW,CAACC,aAAD,EAAgB;IACvB,IAAI,KAAK5D,WAAT,EAAsB;MAClB,IAAI6D,eAAe,GAAG,KAAtB;;MACA,IAAID,aAAa,CAAC7C,KAAlB,EAAyB;QACrB,MAAM;UAAE+C,aAAa,EAAEC,SAAjB;UAA4BC,YAAY,EAAEC;QAA1C,IAA2DL,aAAa,CAAC7C,KAA/E;QACA8C,eAAe,GAAG,CAACE,SAAD,IAAcA,SAAS,CAACG,MAAV,KAAqB,CAACD,YAAY,IAAI,EAAjB,EAAqBC,MAA1E;MACH;;MACD,MAAMC,SAAS,GAAIN,eAAe,IAAID,aAAa,CAAC3C,QAAjC,IAA6C2C,aAAa,CAAC1C,YAA3D,IAA2E0C,aAAa,CAACxC,WAA5G;MACA+C,SAAS,IAAI,KAAKC,IAAL,EAAb;IACH;;IACD,IAAIR,aAAa,CAACtC,WAAlB,EAA+B;MAC3B,KAAK1B,aAAL,GAAqB,KAAKoD,IAAL,GAAY;QAAEqB,GAAG,EAAE,CAAP;QAAUC,IAAI,EAAE;MAAhB,CAAZ,GAAkC,CAAvD;IACH;;IACD,IAAIV,aAAa,CAAC1B,OAAlB,EAA2B;MACvB,MAAM;QAAE4B,aAAF;QAAiBE;MAAjB,IAAkCJ,aAAa,CAAC1B,OAAtD;;MACA,IAAI,KAAKT,IAAL,IAAaqC,aAAa,KAAKE,YAA/B,IAA+CA,YAAY,KAAK,KAAKxE,SAAzE,EAAoF;QAChF,KAAKA,SAAL,GAAiBwE,YAAjB;MACH;IACJ;;IACD,IAAIJ,aAAa,CAAC5B,iBAAlB,EAAqC;MACjC,MAAM;QAAE8B,aAAF;QAAiBE;MAAjB,IAAkCJ,aAAa,CAAC5B,iBAAtD;;MACA,IAAI8B,aAAa,KAAKE,YAAlB,IAAkCA,YAAY,KAAK,KAAKO,mBAA5D,EAAiF;QAC7E,KAAKA,mBAAL,GAA2BP,YAA3B;MACH;IACJ;;IACD,IAAIJ,aAAa,CAACrB,OAAlB,EAA2B;MACvB,MAAM;QAAEuB,aAAF;QAAiBE;MAAjB,IAAkCJ,aAAa,CAACrB,OAAtD;;MACA,IAAI,KAAKd,IAAL,IAAa,CAACqC,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC5B,OAA7E,OAA2F8B,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAAC9B,OAApK,CAAb,IAA6L,CAAC8B,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAAC9B,OAA1E,MAAuF,KAAK1C,SAA7R,EAAwS;QACpS,KAAKA,SAAL,GAAiBwE,YAAY,CAAC9B,OAA9B;MACH;;MACD,IAAI,CAAC4B,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAAC9B,iBAA7E,OAAqGgC,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAAChC,iBAA9K,KAAoM,CAACgC,YAAY,KAAK,IAAjB,IAAyBA,YAAY,KAAK,KAAK,CAA/C,GAAmD,KAAK,CAAxD,GAA4DA,YAAY,CAAChC,iBAA1E,MAAiG,KAAKuC,mBAA9S,EAAmU;QAC/T,KAAKA,mBAAL,GAA2BP,YAAY,CAAChC,iBAAxC;MACH;IACJ;EACJ;;EACDwC,kBAAkB,GAAG;IACjB,KAAKC,SAAL,CAAe9B,OAAf,CAAwBU,IAAD,IAAU;MAC7B,QAAQA,IAAI,CAACqB,OAAL,EAAR;QACI,KAAK,SAAL;UACI,KAAKC,eAAL,GAAuBtB,IAAI,CAACuB,QAA5B;UACA;;QACJ,KAAK,MAAL;UACI,KAAKC,YAAL,GAAoBxB,IAAI,CAACuB,QAAzB;UACA;;QACJ,KAAK,QAAL;UACI,KAAKE,cAAL,GAAsBzB,IAAI,CAACuB,QAA3B;UACA;;QACJ,KAAK,YAAL;UACI,KAAKG,kBAAL,GAA0B1B,IAAI,CAACuB,QAA/B;UACA;;QACJ;UACI,KAAKC,YAAL,GAAoBxB,IAAI,CAACuB,QAAzB;UACA;MAfR;IAiBH,CAlBD;EAmBH;;EACDI,eAAe,GAAG;IACd,KAAKC,YAAL,CAAkB,KAAKC,SAAvB;IACA,KAAKd,IAAL;IACA,KAAKe,YAAL,GAAoB/G,UAAU,CAACgH,QAAX,CAAoB,KAAKC,gBAAL,CAAsBC,aAA1C,CAApB;IACA,KAAKC,aAAL,GAAqBnH,UAAU,CAACoH,SAAX,CAAqB,KAAKH,gBAAL,CAAsBC,aAA3C,CAArB;IACA,KAAKtF,WAAL,GAAmB,IAAnB;EACH;;EACDyF,kBAAkB,GAAG;IACjB,KAAKC,iBAAL;EACH;;EACDC,WAAW,GAAG;IACV,IAAI,KAAKzF,oBAAT,EAA+B;MAC3BG,MAAM,CAACuF,mBAAP,CAA2B,QAA3B,EAAqC,KAAK1F,oBAA1C;MACAG,MAAM,CAACuF,mBAAP,CAA2B,mBAA3B,EAAgD,KAAK1F,oBAArD;MACA,KAAKA,oBAAL,GAA4B,IAA5B;IACH;EACJ;;EACDkE,IAAI,GAAG;IACH,IAAI,CAAC,KAAKjF,SAAV,EAAqB;MACjB,KAAK0G,OAAL;MACA,KAAKC,gBAAL;MACA,KAAKC,aAAL;MACA,KAAKvH,EAAL,CAAQwH,aAAR;IACH;EACJ;;EACDf,YAAY,CAACgB,EAAD,EAAK;IACb,IAAIC,EAAJ,EAAQC,EAAR;;IACA,KAAKjB,SAAL,GAAiBe,EAAE,KAAK,CAACC,EAAE,GAAG,KAAKE,gBAAX,MAAiC,IAAjC,IAAyCF,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACZ,aAA1E,CAAF,IAA8FlH,UAAU,CAACiI,UAAX,CAAsB,CAACF,EAAE,GAAG,KAAKd,gBAAX,MAAiC,IAAjC,IAAyCc,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACb,aAA3F,EAA0G,qBAA1G,CAA/G;EACH;;EACD5B,eAAe,GAAG;IACd,KAAKjE,KAAL,GAAa,KAAKuD,IAAL,GAAY;MAAEG,IAAI,EAAE,CAAR;MAAWG,IAAI,EAAE;IAAjB,CAAZ,GAAmC,CAAhD;IACA,KAAK5D,IAAL,GAAY,KAAKsD,IAAL,GAAY;MAAEG,IAAI,EAAE,CAAR;MAAWG,IAAI,EAAE;IAAjB,CAAZ,GAAmC,CAA/C;IACA,KAAK3D,kBAAL,GAA0B,KAAKqD,IAAL,GAAY;MAAEG,IAAI,EAAE,CAAR;MAAWG,IAAI,EAAE;IAAjB,CAAZ,GAAmC,CAA7D;IACA,KAAK1D,aAAL,GAAqB,KAAKoD,IAAL,GAAY;MAAEqB,GAAG,EAAE,CAAP;MAAUC,IAAI,EAAE;IAAhB,CAAZ,GAAkC,CAAvD;IACA,KAAK9E,SAAL,GAAiB,KAAK2C,QAAL,IAAiB,KAAlC;IACA,KAAKoC,mBAAL,GAA2B,KAAKtC,kBAAhC;EACH;;EACDqE,aAAa,GAAG;IACZ,OAAO,KAAKjB,gBAAZ;EACH;;EACDkB,QAAQ,CAAChE,OAAD,EAAU;IACd,IAAI2D,EAAJ,EAAQC,EAAR;;IACA,KAAKvG,aAAL,GAAqB,KAAKoD,IAAL,GAAY;MAAEqB,GAAG,EAAE,CAAP;MAAUC,IAAI,EAAE;IAAhB,CAAZ,GAAkC,CAAvD;IACA,CAAC6B,EAAE,GAAG,CAACD,EAAE,GAAG,KAAKb,gBAAX,MAAiC,IAAjC,IAAyCa,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACZ,aAA3E,MAA8F,IAA9F,IAAsGa,EAAE,KAAK,KAAK,CAAlH,GAAsH,KAAK,CAA3H,GAA+HA,EAAE,CAACI,QAAH,CAAYhE,OAAZ,CAA/H;EACH;;EACDiE,aAAa,CAACC,KAAD,EAAQC,QAAQ,GAAG,MAAnB,EAA2B;IACpC,MAAM;MAAE1E;IAAF,IAAwB,KAAK2E,iBAAL,EAA9B;IACA,MAAMC,UAAU,GAAG,KAAKC,kBAAL,EAAnB;;IACA,MAAMC,cAAc,GAAG,CAACC,MAAM,GAAG,CAAV,EAAaC,KAAb,KAAwBD,MAAM,IAAIC,KAAV,GAAkB,CAAlB,GAAsBD,MAArE;;IACA,MAAME,cAAc,GAAG,CAACC,MAAD,EAASC,KAAT,EAAgBC,KAAhB,KAA2BF,MAAM,GAAGC,KAAV,GAAmBC,KAApE;;IACA,MAAMb,QAAQ,GAAG,CAACjC,IAAI,GAAG,CAAR,EAAWD,GAAG,GAAG,CAAjB,KAAuB,KAAKkC,QAAL,CAAc;MAAEjC,IAAF;MAAQD,GAAR;MAAaqC;IAAb,CAAd,CAAxC;;IACA,IAAI,KAAK1D,IAAT,EAAe;MACX,MAAMqE,QAAQ,GAAG;QAAElE,IAAI,EAAE2D,cAAc,CAACL,KAAK,CAAC,CAAD,CAAN,EAAWzE,iBAAiB,CAAC,CAAD,CAA5B,CAAtB;QAAwDsB,IAAI,EAAEwD,cAAc,CAACL,KAAK,CAAC,CAAD,CAAN,EAAWzE,iBAAiB,CAAC,CAAD,CAA5B;MAA5E,CAAjB;;MACA,IAAIqF,QAAQ,CAAClE,IAAT,KAAkB,KAAK1D,KAAL,CAAW0D,IAA7B,IAAqCkE,QAAQ,CAAC/D,IAAT,KAAkB,KAAK7D,KAAL,CAAW6D,IAAtE,EAA4E;QACxEiD,QAAQ,CAACU,cAAc,CAACI,QAAQ,CAAC/D,IAAV,EAAgB,KAAKxE,SAAL,CAAe,CAAf,CAAhB,EAAmC8H,UAAU,CAACtC,IAA9C,CAAf,EAAoE2C,cAAc,CAACI,QAAQ,CAAClE,IAAV,EAAgB,KAAKrE,SAAL,CAAe,CAAf,CAAhB,EAAmC8H,UAAU,CAACvC,GAA9C,CAAlF,CAAR;MACH;IACJ,CALD,MAMK;MACD,MAAMgD,QAAQ,GAAGP,cAAc,CAACL,KAAD,EAAQzE,iBAAR,CAA/B;;MACA,IAAIqF,QAAQ,KAAK,KAAK5H,KAAtB,EAA6B;QACzB,KAAKsD,UAAL,GAAkBwD,QAAQ,CAACU,cAAc,CAACI,QAAD,EAAW,KAAKvI,SAAhB,EAA2B8H,UAAU,CAACtC,IAAtC,CAAf,EAA4D,CAA5D,CAA1B,GAA2FiC,QAAQ,CAAC,CAAD,EAAIU,cAAc,CAACI,QAAD,EAAW,KAAKvI,SAAhB,EAA2B8H,UAAU,CAACvC,GAAtC,CAAlB,CAAnG;MACH;IACJ;EACJ;;EACDiD,YAAY,CAACb,KAAD,EAAQc,EAAR,EAAYb,QAAQ,GAAG,MAAvB,EAA+B;IACvC,IAAIa,EAAJ,EAAQ;MACJ,MAAM;QAAE9H,KAAF;QAAS+H;MAAT,IAAsB,KAAKC,gBAAL,EAA5B;;MACA,MAAMlB,QAAQ,GAAG,CAACjC,IAAI,GAAG,CAAR,EAAWD,GAAG,GAAG,CAAjB,KAAuB,KAAKkC,QAAL,CAAc;QAAEjC,IAAF;QAAQD,GAAR;QAAaqC;MAAb,CAAd,CAAxC;;MACA,MAAMgB,SAAS,GAAGH,EAAE,KAAK,UAAzB;MACA,MAAMI,OAAO,GAAGJ,EAAE,KAAK,QAAvB;;MACA,IAAIG,SAAJ,EAAe;QACX,IAAI,KAAK1E,IAAT,EAAe;UACX,IAAIwE,QAAQ,CAAC/H,KAAT,CAAe0D,IAAf,GAAsB1D,KAAK,CAAC0D,IAA5B,GAAmCsD,KAAK,CAAC,CAAD,CAA5C,EAAiD;YAC7CF,QAAQ,CAACiB,QAAQ,CAAC/H,KAAT,CAAe6D,IAAf,GAAsB,KAAKxE,SAAL,CAAe,CAAf,CAAvB,EAA0C,CAAC0I,QAAQ,CAAC/H,KAAT,CAAe0D,IAAf,GAAsB,CAAvB,IAA4B,KAAKrE,SAAL,CAAe,CAAf,CAAtE,CAAR;UACH,CAFD,MAGK,IAAI0I,QAAQ,CAAC/H,KAAT,CAAe6D,IAAf,GAAsB7D,KAAK,CAAC6D,IAA5B,GAAmCmD,KAAK,CAAC,CAAD,CAA5C,EAAiD;YAClDF,QAAQ,CAAC,CAACiB,QAAQ,CAAC/H,KAAT,CAAe6D,IAAf,GAAsB,CAAvB,IAA4B,KAAKxE,SAAL,CAAe,CAAf,CAA7B,EAAgD0I,QAAQ,CAAC/H,KAAT,CAAe0D,IAAf,GAAsB,KAAKrE,SAAL,CAAe,CAAf,CAAtE,CAAR;UACH;QACJ,CAPD,MAQK;UACD,IAAI0I,QAAQ,CAAC/H,KAAT,GAAiBA,KAAjB,GAAyBgH,KAA7B,EAAoC;YAChC,MAAMmB,GAAG,GAAG,CAACJ,QAAQ,CAAC/H,KAAT,GAAiB,CAAlB,IAAuB,KAAKX,SAAxC;YACA,KAAKiE,UAAL,GAAkBwD,QAAQ,CAACqB,GAAD,EAAM,CAAN,CAA1B,GAAqCrB,QAAQ,CAAC,CAAD,EAAIqB,GAAJ,CAA7C;UACH;QACJ;MACJ,CAfD,MAgBK,IAAID,OAAJ,EAAa;QACd,IAAI,KAAK3E,IAAT,EAAe;UACX,IAAIwE,QAAQ,CAAC9H,IAAT,CAAcyD,IAAd,GAAqB1D,KAAK,CAAC0D,IAA3B,IAAmCsD,KAAK,CAAC,CAAD,CAAL,GAAW,CAAlD,EAAqD;YACjDF,QAAQ,CAACiB,QAAQ,CAAC/H,KAAT,CAAe6D,IAAf,GAAsB,KAAKxE,SAAL,CAAe,CAAf,CAAvB,EAA0C,CAAC0I,QAAQ,CAAC/H,KAAT,CAAe0D,IAAf,GAAsB,CAAvB,IAA4B,KAAKrE,SAAL,CAAe,CAAf,CAAtE,CAAR;UACH,CAFD,MAGK,IAAI0I,QAAQ,CAAC9H,IAAT,CAAc4D,IAAd,GAAqB7D,KAAK,CAAC6D,IAA3B,IAAmCmD,KAAK,CAAC,CAAD,CAAL,GAAW,CAAlD,EAAqD;YACtDF,QAAQ,CAAC,CAACiB,QAAQ,CAAC/H,KAAT,CAAe6D,IAAf,GAAsB,CAAvB,IAA4B,KAAKxE,SAAL,CAAe,CAAf,CAA7B,EAAgD0I,QAAQ,CAAC/H,KAAT,CAAe0D,IAAf,GAAsB,KAAKrE,SAAL,CAAe,CAAf,CAAtE,CAAR;UACH;QACJ,CAPD,MAQK;UACD,IAAI0I,QAAQ,CAAC9H,IAAT,GAAgBD,KAAhB,IAAyBgH,KAAK,GAAG,CAArC,EAAwC;YACpC,MAAMmB,GAAG,GAAG,CAACJ,QAAQ,CAAC/H,KAAT,GAAiB,CAAlB,IAAuB,KAAKX,SAAxC;YACA,KAAKiE,UAAL,GAAkBwD,QAAQ,CAACqB,GAAD,EAAM,CAAN,CAA1B,GAAqCrB,QAAQ,CAAC,CAAD,EAAIqB,GAAJ,CAA7C;UACH;QACJ;MACJ;IACJ,CArCD,MAsCK;MACD,KAAKpB,aAAL,CAAmBC,KAAnB,EAA0BC,QAA1B;IACH;EACJ;;EACDe,gBAAgB,GAAG;IACf,IAAIvB,EAAJ;;IACA,MAAM2B,wBAAwB,GAAG,CAACC,IAAD,EAAOX,KAAP,KAAiBY,IAAI,CAACC,KAAL,CAAWF,IAAI,IAAIX,KAAK,IAAIW,IAAb,CAAf,CAAlD;;IACA,IAAIG,eAAe,GAAG,KAAKxI,KAA3B;IACA,IAAIyI,cAAc,GAAG,CAArB;;IACA,IAAI,CAAChC,EAAE,GAAG,KAAKb,gBAAX,MAAiC,IAAjC,IAAyCa,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACZ,aAAzE,EAAwF;MACpF,MAAM;QAAE6C,SAAF;QAAaC;MAAb,IAA4B,KAAK/C,gBAAL,CAAsBC,aAAxD;;MACA,IAAI,KAAKtC,IAAT,EAAe;QACXiF,eAAe,GAAG;UAAE9E,IAAI,EAAE0E,wBAAwB,CAACM,SAAD,EAAY,KAAKrJ,SAAL,CAAe,CAAf,CAAZ,CAAhC;UAAgEwE,IAAI,EAAEuE,wBAAwB,CAACO,UAAD,EAAa,KAAKtJ,SAAL,CAAe,CAAf,CAAb;QAA9F,CAAlB;QACAoJ,cAAc,GAAG;UAAE/E,IAAI,EAAE8E,eAAe,CAAC9E,IAAhB,GAAuB,KAAKxD,kBAAL,CAAwBwD,IAAvD;UAA6DG,IAAI,EAAE2E,eAAe,CAAC3E,IAAhB,GAAuB,KAAK3D,kBAAL,CAAwB2D;QAAlH,CAAjB;MACH,CAHD,MAIK;QACD,MAAM+E,SAAS,GAAG,KAAKtF,UAAL,GAAkBqF,UAAlB,GAA+BD,SAAjD;QACAF,eAAe,GAAGJ,wBAAwB,CAACQ,SAAD,EAAY,KAAKvJ,SAAjB,CAA1C;QACAoJ,cAAc,GAAGD,eAAe,GAAG,KAAKtI,kBAAxC;MACH;IACJ;;IACD,OAAO;MACHF,KAAK,EAAE,KAAKA,KADT;MAEHC,IAAI,EAAE,KAAKA,IAFR;MAGH8H,QAAQ,EAAE;QACN/H,KAAK,EAAEwI,eADD;QAENvI,IAAI,EAAEwI;MAFA;IAHP,CAAP;EAQH;;EACDvB,iBAAiB,GAAG;IAChB,IAAIT,EAAJ,EAAQC,EAAR;;IACA,MAAMS,UAAU,GAAG,KAAKC,kBAAL,EAAnB;IACA,MAAMyB,YAAY,GAAG,CAAC,CAACpC,EAAE,GAAG,KAAKb,gBAAX,MAAiC,IAAjC,IAAyCa,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACZ,aAAtE,IAAuF,KAAKD,gBAAL,CAAsBC,aAAtB,CAAoCiD,WAApC,GAAkD3B,UAAU,CAACtC,IAApJ,GAA2J,CAAhL;IACA,MAAMkE,aAAa,GAAG,CAAC,CAACrC,EAAE,GAAG,KAAKd,gBAAX,MAAiC,IAAjC,IAAyCc,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACb,aAAtE,IAAuF,KAAKD,gBAAL,CAAsBC,aAAtB,CAAoCmD,YAApC,GAAmD7B,UAAU,CAACvC,GAArJ,GAA2J,CAAjL;;IACA,MAAMqE,2BAA2B,GAAG,CAACC,YAAD,EAAe7J,SAAf,KAA6BiJ,IAAI,CAACa,IAAL,CAAUD,YAAY,IAAI7J,SAAS,IAAI6J,YAAjB,CAAtB,CAAjE;;IACA,MAAME,0BAA0B,GAAIC,SAAD,IAAef,IAAI,CAACa,IAAL,CAAUE,SAAS,GAAG,CAAtB,CAAlD;;IACA,MAAMnJ,kBAAkB,GAAG,KAAKqD,IAAL,GACvB;MAAEG,IAAI,EAAEuF,2BAA2B,CAACF,aAAD,EAAgB,KAAK1J,SAAL,CAAe,CAAf,CAAhB,CAAnC;MAAuEwE,IAAI,EAAEoF,2BAA2B,CAACJ,YAAD,EAAe,KAAKxJ,SAAL,CAAe,CAAf,CAAf;IAAxG,CADuB,GAEvB4J,2BAA2B,CAAE,KAAK3F,UAAL,GAAkBuF,YAAlB,GAAiCE,aAAnC,EAAmD,KAAK1J,SAAxD,CAF/B;IAGA,MAAMkD,iBAAiB,GAAG,KAAKuC,mBAAL,KAA6B,KAAKvB,IAAL,GACnD,CAAC6F,0BAA0B,CAAClJ,kBAAkB,CAACwD,IAApB,CAA3B,EAAsD0F,0BAA0B,CAAClJ,kBAAkB,CAAC2D,IAApB,CAAhF,CADmD,GAEnDuF,0BAA0B,CAAClJ,kBAAD,CAFJ,CAA1B;IAGA,OAAO;MAAEA,kBAAF;MAAsBqC;IAAtB,CAAP;EACH;;EACD8D,gBAAgB,GAAG;IACf,MAAM;MAAEnG,kBAAF;MAAsBqC;IAAtB,IAA4C,KAAK2E,iBAAL,EAAlD;;IACA,MAAMoC,aAAa,GAAG,CAAC7B,MAAD,EAAS8B,IAAT,EAAehC,KAAf,EAAsBiC,OAAO,GAAG,KAAhC,KAA0C,KAAKC,OAAL,CAAahC,MAAM,GAAG8B,IAAT,GAAiB,CAAC9B,MAAM,GAAGF,KAAT,GAAiB,CAAjB,GAAqB,CAAtB,IAA2BA,KAAzD,EAAiEiC,OAAjE,CAAhE;;IACA,MAAMxJ,KAAK,GAAG,KAAKA,KAAnB;IACA,MAAMC,IAAI,GAAG,KAAKsD,IAAL,GACT;MAAEG,IAAI,EAAE4F,aAAa,CAAC,KAAKtJ,KAAL,CAAW0D,IAAZ,EAAkBxD,kBAAkB,CAACwD,IAArC,EAA2CnB,iBAAiB,CAAC,CAAD,CAA5D,CAArB;MAAuFsB,IAAI,EAAEyF,aAAa,CAAC,KAAKtJ,KAAL,CAAW6D,IAAZ,EAAkB3D,kBAAkB,CAAC2D,IAArC,EAA2CtB,iBAAiB,CAAC,CAAD,CAA5D,EAAiE,IAAjE;IAA1G,CADS,GAET+G,aAAa,CAAC,KAAKtJ,KAAN,EAAaE,kBAAb,EAAiCqC,iBAAjC,CAFjB;IAGA,KAAKtC,IAAL,GAAYA,IAAZ;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAK4E,mBAAL,GAA2BvC,iBAA3B;;IACA,IAAI,KAAKD,UAAT,EAAqB;MACjB,KAAKlC,SAAL,GAAiB,KAAKmD,IAAL,GACbmG,KAAK,CAACC,IAAN,CAAW;QAAElF,MAAM,EAAEvE,kBAAkB,CAACwD;MAA7B,CAAX,EAAgDC,GAAhD,CAAoD,MAAM+F,KAAK,CAACC,IAAN,CAAW;QAAElF,MAAM,EAAEvE,kBAAkB,CAAC2D;MAA7B,CAAX,CAA1D,CADa,GAEb6F,KAAK,CAACC,IAAN,CAAW;QAAElF,MAAM,EAAEvE;MAAV,CAAX,CAFJ;IAGH;;IACD,IAAI,KAAKT,KAAT,EAAgB;MACZ,KAAKmK,YAAL,CAAkB,YAAlB,EAAgC;QAAE5J,KAAF;QAASC;MAAT,CAAhC;IACH;EACJ;;EACDgG,iBAAiB,GAAG;IAChB,IAAI,KAAKnG,SAAL,IAAkB,CAAC,KAAKC,SAA5B,EAAuC;MACnC8J,OAAO,CAACC,OAAR,GAAkBC,IAAlB,CAAuB,MAAM;QACzB,IAAI,KAAKtE,SAAT,EAAoB;UAChB,KAAKA,SAAL,CAAexE,KAAf,CAAqB+I,SAArB,GAAiC,KAAKvE,SAAL,CAAexE,KAAf,CAAqBgJ,QAArB,GAAgC,MAAjE;UACA,MAAM;YAAEnB,WAAF;YAAeE;UAAf,IAAgC,KAAKvD,SAA3C;UACA,CAAC,KAAKlC,IAAL,IAAa,KAAKD,UAAnB,MAAmC,KAAKsC,gBAAL,CAAsBC,aAAtB,CAAoC5E,KAApC,CAA0CiJ,KAA1C,GAAkD,CAACpB,WAAW,GAAG,KAAKpD,YAAnB,GAAkCoD,WAAlC,GAAgD,KAAKpD,YAAtD,IAAsE,IAA3J;UACA,CAAC,KAAKnC,IAAL,IAAa,KAAKF,QAAnB,MAAiC,KAAKuC,gBAAL,CAAsBC,aAAtB,CAAoC5E,KAApC,CAA0CkJ,MAA1C,GAAmD,CAACnB,YAAY,GAAG,KAAKlD,aAApB,GAAoCkD,YAApC,GAAmD,KAAKlD,aAAzD,IAA0E,IAA9J;UACA,KAAKL,SAAL,CAAexE,KAAf,CAAqB+I,SAArB,GAAiC,KAAKvE,SAAL,CAAexE,KAAf,CAAqBgJ,QAArB,GAAgC,EAAjE;QACH;MACJ,CARD;IASH;EACJ;;EACDR,OAAO,CAACxJ,IAAI,GAAG,CAAR,EAAWmK,MAAM,GAAG,KAApB,EAA2B;IAC9B,OAAO,KAAK7I,MAAL,GAAc+G,IAAI,CAAC+B,GAAL,CAAUD,MAAM,GAAG,CAAC,KAAKhI,QAAL,IAAiB,KAAKb,MAAL,CAAY,CAAZ,CAAlB,EAAkCkD,MAArC,GAA8C,KAAKlD,MAAL,CAAYkD,MAA1E,EAAmFxE,IAAnF,CAAd,GAAyG,CAAhH;EACH;;EACDmH,kBAAkB,GAAG;IACjB,IAAI,KAAK3B,SAAT,EAAoB;MAChB,MAAMxE,KAAK,GAAGqJ,gBAAgB,CAAC,KAAK7E,SAAN,CAA9B;MACA,MAAMZ,IAAI,GAAG0F,QAAQ,CAACtJ,KAAK,CAACuJ,WAAP,EAAoB,EAApB,CAAR,GAAkClC,IAAI,CAACmC,GAAL,CAASF,QAAQ,CAACtJ,KAAK,CAAC4D,IAAP,EAAa,EAAb,CAAjB,EAAmC,CAAnC,CAA/C;MACA,MAAM6F,KAAK,GAAGH,QAAQ,CAACtJ,KAAK,CAAC0J,YAAP,EAAqB,EAArB,CAAR,GAAmCrC,IAAI,CAACmC,GAAL,CAASF,QAAQ,CAACtJ,KAAK,CAACyJ,KAAP,EAAc,EAAd,CAAjB,EAAoC,CAApC,CAAjD;MACA,MAAM9F,GAAG,GAAG2F,QAAQ,CAACtJ,KAAK,CAAC2J,UAAP,EAAmB,EAAnB,CAAR,GAAiCtC,IAAI,CAACmC,GAAL,CAASF,QAAQ,CAACtJ,KAAK,CAAC2D,GAAP,EAAY,EAAZ,CAAjB,EAAkC,CAAlC,CAA7C;MACA,MAAMiG,MAAM,GAAGN,QAAQ,CAACtJ,KAAK,CAAC6J,aAAP,EAAsB,EAAtB,CAAR,GAAoCxC,IAAI,CAACmC,GAAL,CAASF,QAAQ,CAACtJ,KAAK,CAAC4J,MAAP,EAAe,EAAf,CAAjB,EAAqC,CAArC,CAAnD;MACA,OAAO;QAAEhG,IAAF;QAAQ6F,KAAR;QAAe9F,GAAf;QAAoBiG,MAApB;QAA4BE,CAAC,EAAElG,IAAI,GAAG6F,KAAtC;QAA6CM,CAAC,EAAEpG,GAAG,GAAGiG;MAAtD,CAAP;IACH;;IACD,OAAO;MAAEhG,IAAI,EAAE,CAAR;MAAW6F,KAAK,EAAE,CAAlB;MAAqB9F,GAAG,EAAE,CAA1B;MAA6BiG,MAAM,EAAE,CAArC;MAAwCE,CAAC,EAAE,CAA3C;MAA8CC,CAAC,EAAE;IAAjD,CAAP;EACH;;EACD5E,OAAO,GAAG;IACN,IAAIK,EAAJ;;IACA,IAAI,CAACA,EAAE,GAAG,KAAKb,gBAAX,MAAiC,IAAjC,IAAyCa,EAAE,KAAK,KAAK,CAArD,GAAyD,KAAK,CAA9D,GAAkEA,EAAE,CAACZ,aAAzE,EAAwF;MACpF,MAAMoF,aAAa,GAAG,KAAKrF,gBAAL,CAAsBC,aAAtB,CAAoCoF,aAApC,CAAkDA,aAAxE;MACA,MAAMf,KAAK,GAAG,KAAKtI,YAAL,IAAsB,GAAG,KAAKgE,gBAAL,CAAsBC,aAAtB,CAAoCiD,WAApC,IAAmDmC,aAAa,CAACnC,WAAa,IAArH;MACA,MAAMqB,MAAM,GAAG,KAAKzI,aAAL,IAAuB,GAAG,KAAKkE,gBAAL,CAAsBC,aAAtB,CAAoCmD,YAApC,IAAoDiC,aAAa,CAACjC,YAAc,IAAzH;;MACA,MAAMkC,OAAO,GAAG,CAACC,KAAD,EAAQC,MAAR,KAAmB,KAAKxF,gBAAL,CAAsBC,aAAtB,CAAoC5E,KAApC,CAA0CkK,KAA1C,IAAmDC,MAAtF;;MACA,IAAI,KAAK7H,IAAL,IAAa,KAAKD,UAAtB,EAAkC;QAC9B4H,OAAO,CAAC,QAAD,EAAWf,MAAX,CAAP;QACAe,OAAO,CAAC,OAAD,EAAUhB,KAAV,CAAP;MACH,CAHD,MAIK;QACDgB,OAAO,CAAC,QAAD,EAAWf,MAAX,CAAP;MACH;IACJ;EACJ;;EACD7D,aAAa,GAAG;IACZ,IAAI,KAAK/E,MAAT,EAAiB;MACb,MAAM4F,UAAU,GAAG,KAAKC,kBAAL,EAAnB;;MACA,MAAM8D,OAAO,GAAG,CAACC,KAAD,EAAQC,MAAR,EAAgB1D,KAAhB,EAAuBC,KAAK,GAAG,CAA/B,KAAqC,KAAKtH,WAAL,GAAmB2C,MAAM,CAACqI,MAAP,CAAcrI,MAAM,CAACqI,MAAP,CAAc,EAAd,EAAkB,KAAKhL,WAAvB,CAAd,EAAmD;QAAE,CAAE,GAAE8K,KAAM,EAAV,GAAgB,CAACC,MAAM,IAAI,EAAX,EAAe3G,MAAf,GAAwBiD,KAAzB,GAAkCC,KAAnC,GAA4C;MAA5D,CAAnD,CAAxE;;MACA,IAAI,KAAKpE,IAAT,EAAe;QACX2H,OAAO,CAAC,QAAD,EAAW,KAAK3J,MAAhB,EAAwB,KAAKlC,SAAL,CAAe,CAAf,CAAxB,EAA2C8H,UAAU,CAAC6D,CAAtD,CAAP;QACAE,OAAO,CAAC,OAAD,EAAW,KAAK9I,QAAL,IAAiB,KAAKb,MAAL,CAAY,CAAZ,CAA5B,EAA6C,KAAKlC,SAAL,CAAe,CAAf,CAA7C,EAAgE8H,UAAU,CAAC4D,CAA3E,CAAP;MACH,CAHD,MAIK;QACD,KAAKzH,UAAL,GAAkB4H,OAAO,CAAC,OAAD,EAAW,KAAK9I,QAAL,IAAiB,KAAKb,MAAjC,EAA0C,KAAKlC,SAA/C,EAA0D8H,UAAU,CAAC4D,CAArE,CAAzB,GAAmGG,OAAO,CAAC,QAAD,EAAW,KAAK3J,MAAhB,EAAwB,KAAKlC,SAA7B,EAAwC8H,UAAU,CAAC6D,CAAnD,CAA1G;MACH;IACJ;EACJ;;EACDM,kBAAkB,CAACnD,GAAD,EAAM;IACpB,IAAI,KAAK1C,SAAT,EAAoB;MAChB,MAAMzF,KAAK,GAAGmI,GAAG,GAAGA,GAAG,CAACnI,KAAP,GAAe,KAAKA,KAArC;;MACA,MAAMuL,qBAAqB,GAAG,CAAC9D,MAAD,EAASC,KAAT,KAAoBD,MAAM,GAAGC,KAA3D;;MACA,MAAM8D,YAAY,GAAG,CAACC,EAAE,GAAG,CAAN,EAASC,EAAE,GAAG,CAAd,KAAoB,KAAKpL,YAAL,GAAoB0C,MAAM,CAACqI,MAAP,CAAcrI,MAAM,CAACqI,MAAP,CAAc,EAAd,EAAkB,KAAK/K,YAAvB,CAAd,EAAoD;QAAEqL,SAAS,EAAG,eAAcF,EAAG,OAAMC,EAAG;MAAxC,CAApD,CAA7D;;MACA,IAAI,KAAKnI,IAAT,EAAe;QACXiI,YAAY,CAACD,qBAAqB,CAACvL,KAAK,CAAC6D,IAAP,EAAa,KAAKxE,SAAL,CAAe,CAAf,CAAb,CAAtB,EAAuDkM,qBAAqB,CAACvL,KAAK,CAAC0D,IAAP,EAAa,KAAKrE,SAAL,CAAe,CAAf,CAAb,CAA5E,CAAZ;MACH,CAFD,MAGK;QACD,MAAMuM,YAAY,GAAGL,qBAAqB,CAACvL,KAAD,EAAQ,KAAKX,SAAb,CAA1C;QACA,KAAKiE,UAAL,GAAkBkI,YAAY,CAACI,YAAD,EAAe,CAAf,CAA9B,GAAkDJ,YAAY,CAAC,CAAD,EAAII,YAAJ,CAA9D;MACH;IACJ;EACJ;;EACDC,sBAAsB,CAACC,KAAD,EAAQ;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAArB;IACA,MAAM5E,UAAU,GAAG,KAAKC,kBAAL,EAAnB;;IACA,MAAM4E,kBAAkB,GAAG,CAAC3D,IAAD,EAAOV,KAAP,KAAiBU,IAAI,GAAIA,IAAI,GAAGV,KAAP,GAAeU,IAAI,GAAGV,KAAtB,GAA8BU,IAAlC,GAA0C,CAA1F;;IACA,MAAM4D,qBAAqB,GAAG,CAAC5D,IAAD,EAAOX,KAAP,KAAiBY,IAAI,CAACC,KAAL,CAAWF,IAAI,IAAIX,KAAK,IAAIW,IAAb,CAAf,CAA/C;;IACA,MAAM6D,qBAAqB,GAAG,CAACC,aAAD,EAAgB1E,MAAhB,EAAwB2E,KAAxB,EAA+B7C,IAA/B,EAAqChC,KAArC,EAA4C8E,oBAA5C,KAAqE;MAC/F,OAAQF,aAAa,IAAI5E,KAAjB,GAAyBA,KAAzB,GAAkC8E,oBAAoB,GAAID,KAAK,GAAG7C,IAAR,GAAehC,KAAnB,GAA6BE,MAAM,GAAGF,KAAT,GAAiB,CAA5G;IACH,CAFD;;IAGA,MAAMF,cAAc,GAAG,CAAC8E,aAAD,EAAgBG,aAAhB,EAA+B7E,MAA/B,EAAuC2E,KAAvC,EAA8C7C,IAA9C,EAAoDhC,KAApD,EAA2D8E,oBAA3D,KAAoF;MACvG,IAAIF,aAAa,IAAI5E,KAArB,EACI,OAAO,CAAP,CADJ,KAGI,OAAOe,IAAI,CAACmC,GAAL,CAAS,CAAT,EAAY4B,oBAAoB,GAClCF,aAAa,GAAGG,aAAhB,GAAgC7E,MAAhC,GAAyC0E,aAAa,GAAG5E,KADvB,GAElC4E,aAAa,GAAGG,aAAhB,GAAgC7E,MAAhC,GAAyC0E,aAAa,GAAI,IAAI5E,KAF5D,CAAP;IAGP,CAPD;;IAQA,MAAM+B,aAAa,GAAG,CAAC6C,aAAD,EAAgB1E,MAAhB,EAAwB2E,KAAxB,EAA+B7C,IAA/B,EAAqChC,KAArC,EAA4CiC,OAAO,GAAG,KAAtD,KAAgE;MAClF,IAAI+C,SAAS,GAAG9E,MAAM,GAAG8B,IAAT,GAAiB,IAAIhC,KAArC;;MACA,IAAI4E,aAAa,IAAI5E,KAArB,EAA4B;QACxBgF,SAAS,IAAKhF,KAAK,GAAG,CAAtB;MACH;;MACD,OAAO,KAAKkC,OAAL,CAAa8C,SAAb,EAAwB/C,OAAxB,CAAP;IACH,CAND;;IAOA,MAAMd,SAAS,GAAGsD,kBAAkB,CAACD,MAAM,CAACrD,SAAR,EAAmBvB,UAAU,CAACvC,GAA9B,CAApC;IACA,MAAM+D,UAAU,GAAGqD,kBAAkB,CAACD,MAAM,CAACpD,UAAR,EAAoBxB,UAAU,CAACtC,IAA/B,CAArC;IACA,IAAI+C,QAAQ,GAAG,KAAKrE,IAAL,GAAY;MAAEG,IAAI,EAAE,CAAR;MAAWG,IAAI,EAAE;IAAjB,CAAZ,GAAmC,CAAlD;IACA,IAAI2I,OAAO,GAAG,KAAKvM,IAAnB;IACA,IAAIwM,cAAc,GAAG,KAArB;IACA,IAAIC,YAAY,GAAG,KAAKvM,aAAxB;;IACA,IAAI,KAAKoD,IAAT,EAAe;MACX,MAAMoJ,YAAY,GAAG,KAAKxM,aAAL,CAAmByE,GAAnB,IAA0B8D,SAA/C;MACA,MAAMkE,aAAa,GAAG,KAAKzM,aAAL,CAAmB0E,IAAnB,IAA2B8D,UAAjD;MACA,MAAMkE,YAAY,GAAG;QAAEnJ,IAAI,EAAEuI,qBAAqB,CAACvD,SAAD,EAAY,KAAKrJ,SAAL,CAAe,CAAf,CAAZ,CAA7B;QAA6DwE,IAAI,EAAEoI,qBAAqB,CAACtD,UAAD,EAAa,KAAKtJ,SAAL,CAAe,CAAf,CAAb;MAAxF,CAArB;MACA,MAAMyN,YAAY,GAAG;QACjBpJ,IAAI,EAAEwI,qBAAqB,CAACW,YAAY,CAACnJ,IAAd,EAAoB,KAAK1D,KAAL,CAAW0D,IAA/B,EAAqC,KAAKzD,IAAL,CAAUyD,IAA/C,EAAqD,KAAKxD,kBAAL,CAAwBwD,IAA7E,EAAmF,KAAKoB,mBAAL,CAAyB,CAAzB,CAAnF,EAAgH6H,YAAhH,CADV;QAEjB9I,IAAI,EAAEqI,qBAAqB,CAACW,YAAY,CAAChJ,IAAd,EAAoB,KAAK7D,KAAL,CAAW6D,IAA/B,EAAqC,KAAK5D,IAAL,CAAU4D,IAA/C,EAAqD,KAAK3D,kBAAL,CAAwB2D,IAA7E,EAAmF,KAAKiB,mBAAL,CAAyB,CAAzB,CAAnF,EAAgH8H,aAAhH;MAFV,CAArB;MAIAhF,QAAQ,GAAG;QACPlE,IAAI,EAAE2D,cAAc,CAACwF,YAAY,CAACnJ,IAAd,EAAoBoJ,YAAY,CAACpJ,IAAjC,EAAuC,KAAK1D,KAAL,CAAW0D,IAAlD,EAAwD,KAAKzD,IAAL,CAAUyD,IAAlE,EAAwE,KAAKxD,kBAAL,CAAwBwD,IAAhG,EAAsG,KAAKoB,mBAAL,CAAyB,CAAzB,CAAtG,EAAmI6H,YAAnI,CADb;QAEP9I,IAAI,EAAEwD,cAAc,CAACwF,YAAY,CAAChJ,IAAd,EAAoBiJ,YAAY,CAACjJ,IAAjC,EAAuC,KAAK7D,KAAL,CAAW6D,IAAlD,EAAwD,KAAK5D,IAAL,CAAU4D,IAAlE,EAAwE,KAAK3D,kBAAL,CAAwB2D,IAAhG,EAAsG,KAAKiB,mBAAL,CAAyB,CAAzB,CAAtG,EAAmI8H,aAAnI;MAFb,CAAX;MAIAJ,OAAO,GAAG;QACN9I,IAAI,EAAE4F,aAAa,CAACuD,YAAY,CAACnJ,IAAd,EAAoBkE,QAAQ,CAAClE,IAA7B,EAAmC,KAAKzD,IAAL,CAAUyD,IAA7C,EAAmD,KAAKxD,kBAAL,CAAwBwD,IAA3E,EAAiF,KAAKoB,mBAAL,CAAyB,CAAzB,CAAjF,CADb;QAENjB,IAAI,EAAEyF,aAAa,CAACuD,YAAY,CAAChJ,IAAd,EAAoB+D,QAAQ,CAAC/D,IAA7B,EAAmC,KAAK5D,IAAL,CAAU4D,IAA7C,EAAmD,KAAK3D,kBAAL,CAAwB2D,IAA3E,EAAiF,KAAKiB,mBAAL,CAAyB,CAAzB,CAAjF,EAA8G,IAA9G;MAFb,CAAV;MAIA2H,cAAc,GAAI7E,QAAQ,CAAClE,IAAT,KAAkB,KAAK1D,KAAL,CAAW0D,IAA7B,IAAqC8I,OAAO,CAAC9I,IAAR,KAAiB,KAAKzD,IAAL,CAAUyD,IAAjE,IAA2EkE,QAAQ,CAAC/D,IAAT,KAAkB,KAAK7D,KAAL,CAAW6D,IAA7B,IAAqC2I,OAAO,CAAC3I,IAAR,KAAiB,KAAK5D,IAAL,CAAU4D,IAA5J;MACA6I,YAAY,GAAG;QAAE9H,GAAG,EAAE8D,SAAP;QAAkB7D,IAAI,EAAE8D;MAAxB,CAAf;IACH,CAlBD,MAmBK;MACD,MAAMC,SAAS,GAAG,KAAKtF,UAAL,GAAkBqF,UAAlB,GAA+BD,SAAjD;MACA,MAAMqE,mBAAmB,GAAG,KAAK5M,aAAL,IAAsByI,SAAlD;MACA,MAAMiE,YAAY,GAAGZ,qBAAqB,CAACrD,SAAD,EAAY,KAAKvJ,SAAjB,CAA1C;MACA,MAAMyN,YAAY,GAAGZ,qBAAqB,CAACW,YAAD,EAAe,KAAK7M,KAApB,EAA2B,KAAKC,IAAhC,EAAsC,KAAKC,kBAA3C,EAA+D,KAAK4E,mBAApE,EAAyFiI,mBAAzF,CAA1C;MACAnF,QAAQ,GAAGP,cAAc,CAACwF,YAAD,EAAeC,YAAf,EAA6B,KAAK9M,KAAlC,EAAyC,KAAKC,IAA9C,EAAoD,KAAKC,kBAAzD,EAA6E,KAAK4E,mBAAlF,EAAuGiI,mBAAvG,CAAzB;MACAP,OAAO,GAAGlD,aAAa,CAACuD,YAAD,EAAejF,QAAf,EAAyB,KAAK3H,IAA9B,EAAoC,KAAKC,kBAAzC,EAA6D,KAAK4E,mBAAlE,CAAvB;MACA2H,cAAc,GAAG7E,QAAQ,KAAK,KAAK5H,KAAlB,IAA2BwM,OAAO,KAAK,KAAKvM,IAA7D;MACAyM,YAAY,GAAG9D,SAAf;IACH;;IACD,OAAO;MACH5I,KAAK,EAAE4H,QADJ;MAEH3H,IAAI,EAAEuM,OAFH;MAGHC,cAHG;MAIH7D,SAAS,EAAE8D;IAJR,CAAP;EAMH;;EACDM,cAAc,CAAClB,KAAD,EAAQ;IAClB,MAAM;MAAE9L,KAAF;MAASC,IAAT;MAAewM,cAAf;MAA+B7D;IAA/B,IAA6C,KAAKiD,sBAAL,CAA4BC,KAA5B,CAAnD;;IACA,IAAIW,cAAJ,EAAoB;MAChB,MAAMQ,QAAQ,GAAG;QAAEjN,KAAF;QAASC;MAAT,CAAjB;MACA,KAAKqL,kBAAL,CAAwB2B,QAAxB;MACA,KAAKjN,KAAL,GAAaA,KAAb;MACA,KAAKC,IAAL,GAAYA,IAAZ;MACA,KAAKE,aAAL,GAAqByI,SAArB;MACA,KAAKgB,YAAL,CAAkB,qBAAlB,EAAyCqD,QAAzC;;MACA,IAAI,KAAKxN,KAAT,EAAgB;QACZ,KAAKmK,YAAL,CAAkB,YAAlB,EAAgCqD,QAAhC;MACH;IACJ;EACJ;;EACDC,iBAAiB,CAACpB,KAAD,EAAQ;IACrB,KAAKlC,YAAL,CAAkB,UAAlB,EAA8B;MAAEuD,aAAa,EAAErB;IAAjB,CAA9B;;IACA,IAAI,KAAKvM,MAAT,EAAiB;MACb,IAAI,KAAK6N,aAAT,EAAwB;QACpBC,YAAY,CAAC,KAAKD,aAAN,CAAZ;MACH;;MACD,IAAI,CAAC,KAAKrN,SAAN,IAAmB,KAAKuC,UAA5B,EAAwC;QACpC,MAAM;UAAEmK,cAAc,EAAEa;QAAlB,IAA8B,KAAKzB,sBAAL,CAA4BC,KAA5B,CAApC;;QACA,IAAIwB,OAAJ,EAAa;UACT,KAAKvN,SAAL,GAAiB,IAAjB;UACA,KAAKhB,EAAL,CAAQwH,aAAR;QACH;MACJ;;MACD,KAAK6G,aAAL,GAAqBG,UAAU,CAAC,MAAM;QAClC,KAAKP,cAAL,CAAoBlB,KAApB;;QACA,IAAI,KAAK/L,SAAL,IAAkB,KAAKuC,UAAvB,KAAsC,CAAC,KAAK7C,KAAN,IAAe,KAAKiD,QAAL,KAAkB8K,SAAvE,CAAJ,EAAuF;UACnF,KAAKzN,SAAL,GAAiB,KAAjB;UACA,KAAKhB,EAAL,CAAQwH,aAAR;QACH;MACJ,CAN8B,EAM5B,KAAKhH,MANuB,CAA/B;IAOH,CAlBD,MAmBK;MACD,KAAKyN,cAAL,CAAoBlB,KAApB;IACH;EACJ;;EACDpL,cAAc,GAAG;IACb,IAAI,KAAK+M,aAAT,EAAwB;MACpBJ,YAAY,CAAC,KAAKI,aAAN,CAAZ;IACH;;IACD,KAAKA,aAAL,GAAqBF,UAAU,CAAC,MAAM;MAClC,IAAI,KAAK3H,gBAAT,EAA2B;QACvB,MAAM,CAACsE,KAAD,EAAQC,MAAR,IAAkB,CAACxL,UAAU,CAACgH,QAAX,CAAoB,KAAKC,gBAAL,CAAsBC,aAA1C,CAAD,EAA2DlH,UAAU,CAACoH,SAAX,CAAqB,KAAKH,gBAAL,CAAsBC,aAA3C,CAA3D,CAAxB;QACA,MAAM,CAAC6H,WAAD,EAAcC,YAAd,IAA8B,CAACzD,KAAK,KAAK,KAAKxE,YAAhB,EAA8ByE,MAAM,KAAK,KAAKrE,aAA9C,CAApC;QACA,MAAM8H,MAAM,GAAG,KAAKrK,IAAL,GAAamK,WAAW,IAAIC,YAA5B,GAA6C,KAAKrK,UAAL,GAAkBoK,WAAlB,GAAiC,KAAKrK,QAAL,GAAgBsK,YAAhB,GAA+B,KAA5H;QACAC,MAAM,IAAI,KAAK5O,IAAL,CAAU6O,GAAV,CAAc,MAAM;UAC1B,KAAK/I,mBAAL,GAA2B,KAAKtC,kBAAhC;UACA,KAAKkD,YAAL,GAAoBwE,KAApB;UACA,KAAKpE,aAAL,GAAqBqE,MAArB;UACA,KAAKxF,IAAL;QACH,CALS,CAAV;MAMH;IACJ,CAZ8B,EAY5B,KAAKnF,YAZuB,CAA/B;EAaH;;EACDoK,YAAY,CAACkE,IAAD,EAAOC,MAAP,EAAe;IACvB,OAAO,KAAKjL,OAAL,IAAgB,KAAKA,OAAL,CAAagL,IAAb,CAAhB,GAAqC,KAAKhL,OAAL,CAAagL,IAAb,EAAmBC,MAAnB,CAArC,GAAkE,KAAKD,IAAL,EAAWE,IAAX,CAAgBD,MAAhB,CAAzE;EACH;;EACDE,iBAAiB,GAAG;IAChB,OAAO;MACHC,iBAAiB,EAAG,sBAAqB,KAAKnO,SAAL,GAAiB,oBAAjB,GAAwC,EAAG,EADjF;MAEHuB,KAAK,EAAE,KAAKkC,WAFT;MAGH2K,cAAc,EAAGnH,KAAD,IAAW,KAAKoH,UAAL,CAAgBpH,KAAhB,CAHxB;MAIHvE,OAAO,EAAE,KAAK1C,SAJX;MAKHsO,gBAAgB,EAAE,CAACrH,KAAD,EAAQlE,OAAR,KAAoB,KAAKuL,gBAAL,CAAsBrH,KAAtB,EAA6BlE,OAA7B,CALnC;MAMHtB,QAAQ,EAAE,KAAKnC,SANZ;MAOHqE,IAAI,EAAE,KAAKI,UAPR;MAQH3B,OAAO,EAAE,KAAK4B,aARX;MASH1D,WAAW,EAAE,KAAKA,WATf;MAUHC,YAAY,EAAE,KAAKA,YAVhB;MAWH+C,QAAQ,EAAE,KAAKA,QAXZ;MAYHC,UAAU,EAAE,KAAKA,UAZd;MAaHC,IAAI,EAAE,KAAKA;IAbR,CAAP;EAeH;;EACD6K,UAAU,CAACE,aAAD,EAAgB;IACtB,MAAMC,KAAK,GAAG,CAAC,KAAKhN,MAAL,IAAe,EAAhB,EAAoBkD,MAAlC;IACA,MAAMuC,KAAK,GAAG,KAAKzD,IAAL,GAAY,KAAKvD,KAAL,CAAW0D,IAAX,GAAkB4K,aAA9B,GAA8C,KAAKtO,KAAL,GAAasO,aAAzE;IACA,OAAO;MACHtH,KADG;MAEHuH,KAFG;MAGHvO,KAAK,EAAEgH,KAAK,KAAK,CAHd;MAIH/G,IAAI,EAAE+G,KAAK,KAAMuH,KAAK,GAAG,CAJtB;MAKHC,IAAI,EAAExH,KAAK,GAAG,CAAR,KAAc,CALjB;MAMHyH,GAAG,EAAEzH,KAAK,GAAG,CAAR,KAAc;IANhB,CAAP;EAQH;;EACDqH,gBAAgB,CAACrH,KAAD,EAAQ0H,UAAR,EAAoB;IAChC,MAAMH,KAAK,GAAG,KAAKnO,SAAL,CAAeqE,MAA7B;IACA,OAAOzB,MAAM,CAACqI,MAAP,CAAc;MAAErE,KAAF;MACjBuH,KADiB;MACVvO,KAAK,EAAEgH,KAAK,KAAK,CADP;MACU/G,IAAI,EAAE+G,KAAK,KAAMuH,KAAK,GAAG,CADnC;MACuCC,IAAI,EAAExH,KAAK,GAAG,CAAR,KAAc,CAD3D;MAC8DyH,GAAG,EAAEzH,KAAK,GAAG,CAAR,KAAc;IADjF,CAAd,EACoG0H,UADpG,CAAP;EAEH;;AAzjBU;;AA2jBf7P,QAAQ,CAAC8P,IAAT;EAAA,iBAAqG9P,QAArG,EAA2Fd,EAA3F,mBAA+HA,EAAE,CAAC6Q,iBAAlI,GAA2F7Q,EAA3F,mBAAgKA,EAAE,CAAC8Q,MAAnK;AAAA;;AACAhQ,QAAQ,CAACiQ,IAAT,kBAD2F/Q,EAC3F;EAAA,MAAyFc,QAAzF;EAAA;EAAA;IAAA;MAD2Fd,EAC3F,0BAAu1Ba,aAAv1B;IAAA;;IAAA;MAAA;;MAD2Fb,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;MAD2FA,EAC3F;IAAA;;IAAA;MAAA;;MAD2FA,EAC3F,qBAD2FA,EAC3F;MAD2FA,EAC3F,qBAD2FA,EAC3F;IAAA;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;IAAA;IAAA;EAAA;EAAA,WAD2FA,EAC3F;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD2FA,EAC3F;MAD2FA,EAEnF,0EADR;MAD2FA,EAkCnF,8EAlCmFA,EAkCnF,wBAjCR;IAAA;;IAAA;MAAA,YAD2FA,EAC3F;;MAD2FA,EAEpE,oDADvB;IAAA;EAAA;EAAA,eAuCqpBU,EAAE,CAACsQ,OAvCxpB,EAuCmvBtQ,EAAE,CAACuQ,OAvCtvB,EAuCg3BvQ,EAAE,CAACwQ,IAvCn3B,EAuCo9BxQ,EAAE,CAACyQ,gBAvCv9B,EAuC2nCzQ,EAAE,CAAC0Q,OAvC9nC;EAAA;EAAA;AAAA;;AAwCA;EAAA,mDAzC2FpR,EAyC3F,mBAA2Fc,QAA3F,EAAiH,CAAC;IACtGuQ,IAAI,EAAEnR,SADgG;IAEtGoR,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAZ;MAA0BnK,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAvCmB;MAuCZoK,eAAe,EAAErR,uBAAuB,CAACsR,OAvC7B;MAuCsCC,aAAa,EAAEtR,iBAAiB,CAACuR,IAvCvE;MAuC6EC,IAAI,EAAE;QAC9E,SAAS;MADqE,CAvCnF;MAyCIC,MAAM,EAAE,CAAC,wkBAAD;IAzCZ,CAAD;EAFgG,CAAD,CAAjH,EA4C4B,YAAY;IAAE,OAAO,CAAC;MAAER,IAAI,EAAErR,EAAE,CAAC6Q;IAAX,CAAD,EAAiC;MAAEQ,IAAI,EAAErR,EAAE,CAAC8Q;IAAX,CAAjC,CAAP;EAA+D,CA5CzG,EA4C2H;IAAE/N,EAAE,EAAE,CAAC;MAClHsO,IAAI,EAAEhR;IAD4G,CAAD,CAAN;IAE3G6C,KAAK,EAAE,CAAC;MACRmO,IAAI,EAAEhR;IADE,CAAD,CAFoG;IAI3G+C,UAAU,EAAE,CAAC;MACbiO,IAAI,EAAEhR;IADO,CAAD,CAJ+F;IAM3GiD,QAAQ,EAAE,CAAC;MACX+N,IAAI,EAAEhR;IADK,CAAD,CANiG;IAQ3GkD,KAAK,EAAE,CAAC;MACR8N,IAAI,EAAEhR;IADE,CAAD,CARoG;IAU3GoD,QAAQ,EAAE,CAAC;MACX4N,IAAI,EAAEhR;IADK,CAAD,CAViG;IAY3GqD,YAAY,EAAE,CAAC;MACf2N,IAAI,EAAEhR;IADS,CAAD,CAZ6F;IAc3GuD,WAAW,EAAE,CAAC;MACdyN,IAAI,EAAEhR;IADQ,CAAD,CAd8F;IAgB3GyD,WAAW,EAAE,CAAC;MACduN,IAAI,EAAEhR;IADQ,CAAD,CAhB8F;IAkB3G0D,KAAK,EAAE,CAAC;MACRsN,IAAI,EAAEhR;IADE,CAAD,CAlBoG;IAoB3G2D,WAAW,EAAE,CAAC;MACdqN,IAAI,EAAEhR;IADQ,CAAD,CApB8F;IAsB3G4D,IAAI,EAAE,CAAC;MACPoN,IAAI,EAAEhR;IADC,CAAD,CAtBqG;IAwB3G6D,QAAQ,EAAE,CAAC;MACXmN,IAAI,EAAEhR;IADK,CAAD,CAxBiG;IA0B3G8D,cAAc,EAAE,CAAC;MACjBkN,IAAI,EAAEhR;IADW,CAAD,CA1B2F;IA4B3G+D,OAAO,EAAE,CAAC;MACViN,IAAI,EAAEhR;IADI,CAAD,CA5BkG;IA8B3GiE,UAAU,EAAE,CAAC;MACb+M,IAAI,EAAEhR;IADO,CAAD,CA9B+F;IAgC3GkE,UAAU,EAAE,CAAC;MACb8M,IAAI,EAAEhR;IADO,CAAD,CAhC+F;IAkC3GmE,iBAAiB,EAAE,CAAC;MACpB6M,IAAI,EAAEhR;IADc,CAAD,CAlCwF;IAoC3GqE,OAAO,EAAE,CAAC;MACV2M,IAAI,EAAEhR;IADI,CAAD,CApCkG;IAsC3GuE,QAAQ,EAAE,CAAC;MACXyM,IAAI,EAAEhR;IADK,CAAD,CAtCiG;IAwC3GwE,OAAO,EAAE,CAAC;MACVwM,IAAI,EAAEhR;IADI,CAAD,CAxCkG;IA0C3G0E,OAAO,EAAE,CAAC;MACVsM,IAAI,EAAEhR;IADI,CAAD,CA1CkG;IA4C3GwH,gBAAgB,EAAE,CAAC;MACnBwJ,IAAI,EAAE/Q,SADa;MAEnBgR,IAAI,EAAE,CAAC,SAAD;IAFa,CAAD,CA5CyF;IA+C3G1I,gBAAgB,EAAE,CAAC;MACnByI,IAAI,EAAE/Q,SADa;MAEnBgR,IAAI,EAAE,CAAC,SAAD;IAFa,CAAD,CA/CyF;IAkD3GrK,SAAS,EAAE,CAAC;MACZoK,IAAI,EAAE9Q,eADM;MAEZ+Q,IAAI,EAAE,CAACzQ,aAAD;IAFM,CAAD,CAlDgG;IAqD3GK,UAAU,EAAE,CAAC;MACbmQ,IAAI,EAAE7Q;IADO,CAAD,CArD+F;IAuD3GW,QAAQ,EAAE,CAAC;MACXkQ,IAAI,EAAE7Q;IADK,CAAD,CAvDiG;IAyD3GY,mBAAmB,EAAE,CAAC;MACtBiQ,IAAI,EAAE7Q;IADgB,CAAD;EAzDsF,CA5C3H;AAAA;;AAwGA,MAAMsR,cAAN,CAAqB;;AAErBA,cAAc,CAAClB,IAAf;EAAA,iBAA2GkB,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBApJ2F/R,EAoJ3F;EAAA,MAA4G8R;AAA5G;AACAA,cAAc,CAACE,IAAf,kBArJ2FhS,EAqJ3F;EAAA,UAAsIW,YAAtI;AAAA;;AACA;EAAA,mDAtJ2FX,EAsJ3F,mBAA2F8R,cAA3F,EAAuH,CAAC;IAC5GT,IAAI,EAAE5Q,QADsG;IAE5G6Q,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAACtR,YAAD,CADV;MAECuR,OAAO,EAAE,CAACpR,QAAD,CAFV;MAGCqR,YAAY,EAAE,CAACrR,QAAD;IAHf,CAAD;EAFsG,CAAD,CAAvH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,QAAT,EAAmBgR,cAAnB"}, "metadata": {}, "sourceType": "module"}