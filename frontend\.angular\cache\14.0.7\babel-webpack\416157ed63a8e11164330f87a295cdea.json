{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\n\nlet hasV8BreakIterator; // We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\n\ntry {\n  hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n} catch (_a) {\n  hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\n\n\nclass Platform {\n  constructor(_platformId) {\n    this._platformId = _platformId; // We want to use the Angular platform check because if the Document is shimmed\n    // without the navigator, the following checks will fail. This is preferred because\n    // sometimes the Document may be shimmed without the user's knowledge or intention\n\n    /** Whether the Angular application is being rendered in the browser. */\n\n    this.isBrowser = this._platformId ? isPlatformBrowser(this._platformId) : typeof document === 'object' && !!document;\n    /** Whether the current browser is Microsoft Edge. */\n\n    this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n    /** Whether the current rendering engine is Microsoft Trident. */\n\n    this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent); // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n\n    /** Whether the current rendering engine is Blink. */\n\n    this.BLINK = this.isBrowser && !!(window.chrome || hasV8BreakIterator) && typeof CSS !== 'undefined' && !this.EDGE && !this.TRIDENT; // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n    // ensure that Webkit runs standalone and is not used as another engine's base.\n\n    /** Whether the current rendering engine is WebKit. */\n\n    this.WEBKIT = this.isBrowser && /AppleWebKit/i.test(navigator.userAgent) && !this.BLINK && !this.EDGE && !this.TRIDENT;\n    /** Whether the current platform is Apple iOS. */\n\n    this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window); // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n    // them self as Gecko-like browsers and modify the userAgent's according to that.\n    // Since we only cover one explicit Firefox case, we can simply check for Firefox\n    // instead of having an unstable check for Gecko.\n\n    /** Whether the current browser is Firefox. */\n\n    this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n    /** Whether the current platform is Android. */\n    // Trident on mobile adds the android platform to the userAgent to trick detections.\n\n    this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT; // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n    // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n    // Safari browser should also use Webkit as its layout engine.\n\n    /** Whether the current browser is Safari. */\n\n    this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n  }\n\n}\n\nPlatform.ɵfac = function Platform_Factory(t) {\n  return new (t || Platform)(i0.ɵɵinject(PLATFORM_ID));\n};\n\nPlatform.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: Platform,\n  factory: Platform.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Platform, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nclass PlatformModule {}\n\nPlatformModule.ɵfac = function PlatformModule_Factory(t) {\n  return new (t || PlatformModule)();\n};\n\nPlatformModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: PlatformModule\n});\nPlatformModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result Set of input types support by the current browser. */\n\n\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\n\nconst candidateInputTypes = [// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\n\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  } // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n\n\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\n\n\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\n\nfunction supportsPassiveEventListeners() {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n        get: () => supportsPassiveEvents = true\n      }));\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\n\n\nfunction normalizePassiveListenerOptions(options) {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\n\n\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\n\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\n\nfunction supportsScrollBehavior() {\n  if (scrollBehaviorSupported == null) {\n    // If we're not in the browser, it can't be supported. Also check for `Element`, because\n    // some projects stub out the global `document` during SSR which can throw us off.\n    if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n      scrollBehaviorSupported = false;\n      return scrollBehaviorSupported;\n    } // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n\n\n    if ('scrollBehavior' in document.documentElement.style) {\n      scrollBehaviorSupported = true;\n    } else {\n      // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n      // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n      const scrollToFunction = Element.prototype.scrollTo;\n\n      if (scrollToFunction) {\n        // We can detect if the function has been polyfilled by calling `toString` on it. Native\n        // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n        // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n        // polyfilled functions as supporting scroll behavior.\n        scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n      } else {\n        scrollBehaviorSupported = false;\n      }\n    }\n  }\n\n  return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\n\n\nfunction getRtlScrollAxisType() {\n  // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n  if (typeof document !== 'object' || !document) {\n    return 0\n    /* RtlScrollAxisType.NORMAL */\n    ;\n  }\n\n  if (rtlScrollAxisType == null) {\n    // Create a 1px wide scrolling container and a 2px wide content element.\n    const scrollContainer = document.createElement('div');\n    const containerStyle = scrollContainer.style;\n    scrollContainer.dir = 'rtl';\n    containerStyle.width = '1px';\n    containerStyle.overflow = 'auto';\n    containerStyle.visibility = 'hidden';\n    containerStyle.pointerEvents = 'none';\n    containerStyle.position = 'absolute';\n    const content = document.createElement('div');\n    const contentStyle = content.style;\n    contentStyle.width = '2px';\n    contentStyle.height = '1px';\n    scrollContainer.appendChild(content);\n    document.body.appendChild(scrollContainer);\n    rtlScrollAxisType = 0\n    /* RtlScrollAxisType.NORMAL */\n    ; // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n    // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n    // dealing with one of the other two types of browsers.\n\n    if (scrollContainer.scrollLeft === 0) {\n      // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n      // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n      // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n      // return 0 when we read it again.\n      scrollContainer.scrollLeft = 1;\n      rtlScrollAxisType = scrollContainer.scrollLeft === 0 ? 1\n      /* RtlScrollAxisType.NEGATED */\n      : 2\n      /* RtlScrollAxisType.INVERTED */\n      ;\n    }\n\n    scrollContainer.remove();\n  }\n\n  return rtlScrollAxisType;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\n\nfunction _supportsShadowDom() {\n  if (shadowDomIsSupported == null) {\n    const head = typeof document !== 'undefined' ? document.head : null;\n    shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n  }\n\n  return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\n\n\nfunction _getShadowRoot(element) {\n  if (_supportsShadowDom()) {\n    const rootNode = element.getRootNode ? element.getRootNode() : null; // Note that this should be caught by `_supportsShadowDom`, but some\n    // teams have been able to hit this code path on unsupported browsers.\n\n    if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n      return rootNode;\n    }\n  }\n\n  return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\n\n\nfunction _getFocusedElementPierceShadowDom() {\n  let activeElement = typeof document !== 'undefined' && document ? document.activeElement : null;\n\n  while (activeElement && activeElement.shadowRoot) {\n    const newActiveElement = activeElement.shadowRoot.activeElement;\n\n    if (newActiveElement === activeElement) {\n      break;\n    } else {\n      activeElement = newActiveElement;\n    }\n  }\n\n  return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\n\n\nfunction _getEventTarget(event) {\n  // If an event is bound outside the Shadow DOM, the `event.target` will\n  // point to the shadow root so we have to use `composedPath` instead.\n  return event.composedPath ? event.composedPath()[0] : event.target;\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** Gets whether the code is currently running in a test environment. */\n\n\nfunction _isTestEnvironment() {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n  return (// @ts-ignore\n    typeof __karma__ !== 'undefined' && !!__karma__ || // @ts-ignore\n    typeof jasmine !== 'undefined' && !!jasmine || // @ts-ignore\n    typeof jest !== 'undefined' && !!jest || // @ts-ignore\n    typeof Mocha !== 'undefined' && !!Mocha\n  );\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };", "map": {"version": 3, "names": ["i0", "PLATFORM_ID", "Injectable", "Inject", "NgModule", "isPlatformBrowser", "hasV8BreakIterator", "Intl", "v8BreakIterator", "_a", "Platform", "constructor", "_platformId", "<PERSON><PERSON><PERSON><PERSON>", "document", "EDGE", "test", "navigator", "userAgent", "TRIDENT", "BLINK", "window", "chrome", "CSS", "WEBKIT", "IOS", "FIREFOX", "ANDROID", "SAFARI", "ɵfac", "ɵprov", "type", "args", "providedIn", "Object", "decorators", "PlatformModule", "ɵmod", "ɵinj", "supportedInputTypes", "candidateInputTypes", "getSupportedInputTypes", "Set", "featureTestInput", "createElement", "filter", "value", "setAttribute", "supportsPassiveEvents", "supportsPassiveEventListeners", "addEventListener", "defineProperty", "get", "normalizePassiveListenerOptions", "options", "capture", "rtlScrollAxisType", "scrollBehaviorSupported", "supportsScrollBehavior", "Element", "documentElement", "style", "scrollToFunction", "prototype", "scrollTo", "toString", "getRtlScrollAxisType", "scrollContainer", "containerStyle", "dir", "width", "overflow", "visibility", "pointerEvents", "position", "content", "contentStyle", "height", "append<PERSON><PERSON><PERSON>", "body", "scrollLeft", "remove", "shadowDomIsSupported", "_supportsShadowDom", "head", "createShadowRoot", "attachShadow", "_getShadowRoot", "element", "rootNode", "getRootNode", "ShadowRoot", "_getFocusedElementPierceShadowDom", "activeElement", "shadowRoot", "newActiveElement", "_getEventTarget", "event", "<PERSON><PERSON><PERSON>", "target", "_isTestEnvironment", "__karma__", "jasmine", "jest", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/platform.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { PLATFORM_ID, Injectable, Inject, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// Whether the current platform supports the V8 Break Iterator. The V8 check\n// is necessary to detect all Blink based browsers.\nlet hasV8BreakIterator;\n// We need a try/catch around the reference to `Intl`, because accessing it in some cases can\n// cause IE to throw. These cases are tied to particular versions of Windows and can happen if\n// the consumer is providing a polyfilled `Map`. See:\n// https://github.com/Microsoft/ChakraCore/issues/3189\n// https://github.com/angular/components/issues/15687\ntry {\n    hasV8BreakIterator = typeof Intl !== 'undefined' && Intl.v8BreakIterator;\n}\ncatch (_a) {\n    hasV8BreakIterator = false;\n}\n/**\n * Service to detect the current platform by comparing the userAgent strings and\n * checking browser-specific global properties.\n */\nclass Platform {\n    constructor(_platformId) {\n        this._platformId = _platformId;\n        // We want to use the Angular platform check because if the Document is shimmed\n        // without the navigator, the following checks will fail. This is preferred because\n        // sometimes the Document may be shimmed without the user's knowledge or intention\n        /** Whether the Angular application is being rendered in the browser. */\n        this.isBrowser = this._platformId\n            ? isPlatformBrowser(this._platformId)\n            : typeof document === 'object' && !!document;\n        /** Whether the current browser is Microsoft Edge. */\n        this.EDGE = this.isBrowser && /(edge)/i.test(navigator.userAgent);\n        /** Whether the current rendering engine is Microsoft Trident. */\n        this.TRIDENT = this.isBrowser && /(msie|trident)/i.test(navigator.userAgent);\n        // EdgeHTML and Trident mock Blink specific things and need to be excluded from this check.\n        /** Whether the current rendering engine is Blink. */\n        this.BLINK = this.isBrowser &&\n            !!(window.chrome || hasV8BreakIterator) &&\n            typeof CSS !== 'undefined' &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        // Webkit is part of the userAgent in EdgeHTML, Blink and Trident. Therefore we need to\n        // ensure that Webkit runs standalone and is not used as another engine's base.\n        /** Whether the current rendering engine is WebKit. */\n        this.WEBKIT = this.isBrowser &&\n            /AppleWebKit/i.test(navigator.userAgent) &&\n            !this.BLINK &&\n            !this.EDGE &&\n            !this.TRIDENT;\n        /** Whether the current platform is Apple iOS. */\n        this.IOS = this.isBrowser && /iPad|iPhone|iPod/.test(navigator.userAgent) && !('MSStream' in window);\n        // It's difficult to detect the plain Gecko engine, because most of the browsers identify\n        // them self as Gecko-like browsers and modify the userAgent's according to that.\n        // Since we only cover one explicit Firefox case, we can simply check for Firefox\n        // instead of having an unstable check for Gecko.\n        /** Whether the current browser is Firefox. */\n        this.FIREFOX = this.isBrowser && /(firefox|minefield)/i.test(navigator.userAgent);\n        /** Whether the current platform is Android. */\n        // Trident on mobile adds the android platform to the userAgent to trick detections.\n        this.ANDROID = this.isBrowser && /android/i.test(navigator.userAgent) && !this.TRIDENT;\n        // Safari browsers will include the Safari keyword in their userAgent. Some browsers may fake\n        // this and just place the Safari keyword in the userAgent. To be more safe about Safari every\n        // Safari browser should also use Webkit as its layout engine.\n        /** Whether the current browser is Safari. */\n        this.SAFARI = this.isBrowser && /safari/i.test(navigator.userAgent) && this.WEBKIT;\n    }\n}\nPlatform.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: Platform, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable });\nPlatform.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: Platform, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: Platform, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: function () {\n        return [{ type: Object, decorators: [{\n                        type: Inject,\n                        args: [PLATFORM_ID]\n                    }] }];\n    } });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass PlatformModule {\n}\nPlatformModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: PlatformModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nPlatformModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.1\", ngImport: i0, type: PlatformModule });\nPlatformModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: PlatformModule });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: PlatformModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n    // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n    // first changing it to something else:\n    // The specified value \"\" does not conform to the required format.\n    // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n    'color',\n    'button',\n    'checkbox',\n    'date',\n    'datetime-local',\n    'email',\n    'file',\n    'hidden',\n    'image',\n    'month',\n    'number',\n    'password',\n    'radio',\n    'range',\n    'reset',\n    'search',\n    'submit',\n    'tel',\n    'text',\n    'time',\n    'url',\n    'week',\n];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n    // Result is cached.\n    if (supportedInputTypes) {\n        return supportedInputTypes;\n    }\n    // We can't check if an input type is not supported until we're on the browser, so say that\n    // everything is supported when not on the browser. We don't use `Platform` here since it's\n    // just a helper function and can't inject it.\n    if (typeof document !== 'object' || !document) {\n        supportedInputTypes = new Set(candidateInputTypes);\n        return supportedInputTypes;\n    }\n    let featureTestInput = document.createElement('input');\n    supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n        featureTestInput.setAttribute('type', value);\n        return featureTestInput.type === value;\n    }));\n    return supportedInputTypes;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents;\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nfunction supportsPassiveEventListeners() {\n    if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n        try {\n            window.addEventListener('test', null, Object.defineProperty({}, 'passive', {\n                get: () => (supportsPassiveEvents = true),\n            }));\n        }\n        finally {\n            supportsPassiveEvents = supportsPassiveEvents || false;\n        }\n    }\n    return supportsPassiveEvents;\n}\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nfunction normalizePassiveListenerOptions(options) {\n    return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Cached result of the way the browser handles the horizontal scroll axis in RTL mode. */\nlet rtlScrollAxisType;\n/** Cached result of the check that indicates whether the browser supports scroll behaviors. */\nlet scrollBehaviorSupported;\n/** Check whether the browser supports scroll behaviors. */\nfunction supportsScrollBehavior() {\n    if (scrollBehaviorSupported == null) {\n        // If we're not in the browser, it can't be supported. Also check for `Element`, because\n        // some projects stub out the global `document` during SSR which can throw us off.\n        if (typeof document !== 'object' || !document || typeof Element !== 'function' || !Element) {\n            scrollBehaviorSupported = false;\n            return scrollBehaviorSupported;\n        }\n        // If the element can have a `scrollBehavior` style, we can be sure that it's supported.\n        if ('scrollBehavior' in document.documentElement.style) {\n            scrollBehaviorSupported = true;\n        }\n        else {\n            // At this point we have 3 possibilities: `scrollTo` isn't supported at all, it's\n            // supported but it doesn't handle scroll behavior, or it has been polyfilled.\n            const scrollToFunction = Element.prototype.scrollTo;\n            if (scrollToFunction) {\n                // We can detect if the function has been polyfilled by calling `toString` on it. Native\n                // functions are obfuscated using `[native code]`, whereas if it was overwritten we'd get\n                // the actual function source. Via https://davidwalsh.name/detect-native-function. Consider\n                // polyfilled functions as supporting scroll behavior.\n                scrollBehaviorSupported = !/\\{\\s*\\[native code\\]\\s*\\}/.test(scrollToFunction.toString());\n            }\n            else {\n                scrollBehaviorSupported = false;\n            }\n        }\n    }\n    return scrollBehaviorSupported;\n}\n/**\n * Checks the type of RTL scroll axis used by this browser. As of time of writing, Chrome is NORMAL,\n * Firefox & Safari are NEGATED, and IE & Edge are INVERTED.\n */\nfunction getRtlScrollAxisType() {\n    // We can't check unless we're on the browser. Just assume 'normal' if we're not.\n    if (typeof document !== 'object' || !document) {\n        return 0 /* RtlScrollAxisType.NORMAL */;\n    }\n    if (rtlScrollAxisType == null) {\n        // Create a 1px wide scrolling container and a 2px wide content element.\n        const scrollContainer = document.createElement('div');\n        const containerStyle = scrollContainer.style;\n        scrollContainer.dir = 'rtl';\n        containerStyle.width = '1px';\n        containerStyle.overflow = 'auto';\n        containerStyle.visibility = 'hidden';\n        containerStyle.pointerEvents = 'none';\n        containerStyle.position = 'absolute';\n        const content = document.createElement('div');\n        const contentStyle = content.style;\n        contentStyle.width = '2px';\n        contentStyle.height = '1px';\n        scrollContainer.appendChild(content);\n        document.body.appendChild(scrollContainer);\n        rtlScrollAxisType = 0 /* RtlScrollAxisType.NORMAL */;\n        // The viewport starts scrolled all the way to the right in RTL mode. If we are in a NORMAL\n        // browser this would mean that the scrollLeft should be 1. If it's zero instead we know we're\n        // dealing with one of the other two types of browsers.\n        if (scrollContainer.scrollLeft === 0) {\n            // In a NEGATED browser the scrollLeft is always somewhere in [-maxScrollAmount, 0]. For an\n            // INVERTED browser it is always somewhere in [0, maxScrollAmount]. We can determine which by\n            // setting to the scrollLeft to 1. This is past the max for a NEGATED browser, so it will\n            // return 0 when we read it again.\n            scrollContainer.scrollLeft = 1;\n            rtlScrollAxisType =\n                scrollContainer.scrollLeft === 0 ? 1 /* RtlScrollAxisType.NEGATED */ : 2 /* RtlScrollAxisType.INVERTED */;\n        }\n        scrollContainer.remove();\n    }\n    return rtlScrollAxisType;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nlet shadowDomIsSupported;\n/** Checks whether the user's browser support Shadow DOM. */\nfunction _supportsShadowDom() {\n    if (shadowDomIsSupported == null) {\n        const head = typeof document !== 'undefined' ? document.head : null;\n        shadowDomIsSupported = !!(head && (head.createShadowRoot || head.attachShadow));\n    }\n    return shadowDomIsSupported;\n}\n/** Gets the shadow root of an element, if supported and the element is inside the Shadow DOM. */\nfunction _getShadowRoot(element) {\n    if (_supportsShadowDom()) {\n        const rootNode = element.getRootNode ? element.getRootNode() : null;\n        // Note that this should be caught by `_supportsShadowDom`, but some\n        // teams have been able to hit this code path on unsupported browsers.\n        if (typeof ShadowRoot !== 'undefined' && ShadowRoot && rootNode instanceof ShadowRoot) {\n            return rootNode;\n        }\n    }\n    return null;\n}\n/**\n * Gets the currently-focused element on the page while\n * also piercing through Shadow DOM boundaries.\n */\nfunction _getFocusedElementPierceShadowDom() {\n    let activeElement = typeof document !== 'undefined' && document\n        ? document.activeElement\n        : null;\n    while (activeElement && activeElement.shadowRoot) {\n        const newActiveElement = activeElement.shadowRoot.activeElement;\n        if (newActiveElement === activeElement) {\n            break;\n        }\n        else {\n            activeElement = newActiveElement;\n        }\n    }\n    return activeElement;\n}\n/** Gets the target of an event while accounting for Shadow DOM. */\nfunction _getEventTarget(event) {\n    // If an event is bound outside the Shadow DOM, the `event.target` will\n    // point to the shadow root so we have to use `composedPath` instead.\n    return (event.composedPath ? event.composedPath()[0] : event.target);\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Gets whether the code is currently running in a test environment. */\nfunction _isTestEnvironment() {\n    // We can't use `declare const` because it causes conflicts inside Google with the real typings\n    // for these symbols and we can't read them off the global object, because they don't appear to\n    // be attached there for some runners like Jest.\n    // (see: https://github.com/angular/components/issues/23365#issuecomment-*********)\n    return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n        // @ts-ignore\n        (typeof jasmine !== 'undefined' && !!jasmine) ||\n        // @ts-ignore\n        (typeof jest !== 'undefined' && !!jest) ||\n        // @ts-ignore\n        (typeof Mocha !== 'undefined' && !!Mocha));\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Platform, PlatformModule, _getEventTarget, _getFocusedElementPierceShadowDom, _getShadowRoot, _isTestEnvironment, _supportsShadowDom, getRtlScrollAxisType, getSupportedInputTypes, normalizePassiveListenerOptions, supportsPassiveEventListeners, supportsScrollBehavior };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,WAAT,EAAsBC,UAAtB,EAAkCC,MAAlC,EAA0CC,QAA1C,QAA0D,eAA1D;AACA,SAASC,iBAAT,QAAkC,iBAAlC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIC,kBAAJ,C,CACA;AACA;AACA;AACA;AACA;;AACA,IAAI;EACAA,kBAAkB,GAAG,OAAOC,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,CAACC,eAAzD;AACH,CAFD,CAGA,OAAOC,EAAP,EAAW;EACPH,kBAAkB,GAAG,KAArB;AACH;AACD;AACA;AACA;AACA;;;AACA,MAAMI,QAAN,CAAe;EACXC,WAAW,CAACC,WAAD,EAAc;IACrB,KAAKA,WAAL,GAAmBA,WAAnB,CADqB,CAErB;IACA;IACA;;IACA;;IACA,KAAKC,SAAL,GAAiB,KAAKD,WAAL,GACXP,iBAAiB,CAAC,KAAKO,WAAN,CADN,GAEX,OAAOE,QAAP,KAAoB,QAApB,IAAgC,CAAC,CAACA,QAFxC;IAGA;;IACA,KAAKC,IAAL,GAAY,KAAKF,SAAL,IAAkB,UAAUG,IAAV,CAAeC,SAAS,CAACC,SAAzB,CAA9B;IACA;;IACA,KAAKC,OAAL,GAAe,KAAKN,SAAL,IAAkB,kBAAkBG,IAAlB,CAAuBC,SAAS,CAACC,SAAjC,CAAjC,CAZqB,CAarB;;IACA;;IACA,KAAKE,KAAL,GAAa,KAAKP,SAAL,IACT,CAAC,EAAEQ,MAAM,CAACC,MAAP,IAAiBhB,kBAAnB,CADQ,IAET,OAAOiB,GAAP,KAAe,WAFN,IAGT,CAAC,KAAKR,IAHG,IAIT,CAAC,KAAKI,OAJV,CAfqB,CAoBrB;IACA;;IACA;;IACA,KAAKK,MAAL,GAAc,KAAKX,SAAL,IACV,eAAeG,IAAf,CAAoBC,SAAS,CAACC,SAA9B,CADU,IAEV,CAAC,KAAKE,KAFI,IAGV,CAAC,KAAKL,IAHI,IAIV,CAAC,KAAKI,OAJV;IAKA;;IACA,KAAKM,GAAL,GAAW,KAAKZ,SAAL,IAAkB,mBAAmBG,IAAnB,CAAwBC,SAAS,CAACC,SAAlC,CAAlB,IAAkE,EAAE,cAAcG,MAAhB,CAA7E,CA7BqB,CA8BrB;IACA;IACA;IACA;;IACA;;IACA,KAAKK,OAAL,GAAe,KAAKb,SAAL,IAAkB,uBAAuBG,IAAvB,CAA4BC,SAAS,CAACC,SAAtC,CAAjC;IACA;IACA;;IACA,KAAKS,OAAL,GAAe,KAAKd,SAAL,IAAkB,WAAWG,IAAX,CAAgBC,SAAS,CAACC,SAA1B,CAAlB,IAA0D,CAAC,KAAKC,OAA/E,CAtCqB,CAuCrB;IACA;IACA;;IACA;;IACA,KAAKS,MAAL,GAAc,KAAKf,SAAL,IAAkB,UAAUG,IAAV,CAAeC,SAAS,CAACC,SAAzB,CAAlB,IAAyD,KAAKM,MAA5E;EACH;;AA7CU;;AA+Cfd,QAAQ,CAACmB,IAAT;EAAA,iBAAqGnB,QAArG,EAA2FV,EAA3F,UAA+HC,WAA/H;AAAA;;AACAS,QAAQ,CAACoB,KAAT,kBAD2F9B,EAC3F;EAAA,OAAyGU,QAAzG;EAAA,SAAyGA,QAAzG;EAAA,YAA+H;AAA/H;;AACA;EAAA,mDAF2FV,EAE3F,mBAA2FU,QAA3F,EAAiH,CAAC;IACtGqB,IAAI,EAAE7B,UADgG;IAEtG8B,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFgG,CAAD,CAAjH,EAG4B,YAAY;IAChC,OAAO,CAAC;MAAEF,IAAI,EAAEG,MAAR;MAAgBC,UAAU,EAAE,CAAC;QACrBJ,IAAI,EAAE5B,MADe;QAErB6B,IAAI,EAAE,CAAC/B,WAAD;MAFe,CAAD;IAA5B,CAAD,CAAP;EAIH,CARL;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMmC,cAAN,CAAqB;;AAErBA,cAAc,CAACP,IAAf;EAAA,iBAA2GO,cAA3G;AAAA;;AACAA,cAAc,CAACC,IAAf,kBAtB2FrC,EAsB3F;EAAA,MAA4GoC;AAA5G;AACAA,cAAc,CAACE,IAAf,kBAvB2FtC,EAuB3F;;AACA;EAAA,mDAxB2FA,EAwB3F,mBAA2FoC,cAA3F,EAAuH,CAAC;IAC5GL,IAAI,EAAE3B,QADsG;IAE5G4B,IAAI,EAAE,CAAC,EAAD;EAFsG,CAAD,CAAvH;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIO,mBAAJ;AACA;;AACA,MAAMC,mBAAmB,GAAG,CACxB;AACA;AACA;AACA;AACA,OALwB,EAMxB,QANwB,EAOxB,UAPwB,EAQxB,MARwB,EASxB,gBATwB,EAUxB,OAVwB,EAWxB,MAXwB,EAYxB,QAZwB,EAaxB,OAbwB,EAcxB,OAdwB,EAexB,QAfwB,EAgBxB,UAhBwB,EAiBxB,OAjBwB,EAkBxB,OAlBwB,EAmBxB,OAnBwB,EAoBxB,QApBwB,EAqBxB,QArBwB,EAsBxB,KAtBwB,EAuBxB,MAvBwB,EAwBxB,MAxBwB,EAyBxB,KAzBwB,EA0BxB,MA1BwB,CAA5B;AA4BA;;AACA,SAASC,sBAAT,GAAkC;EAC9B;EACA,IAAIF,mBAAJ,EAAyB;IACrB,OAAOA,mBAAP;EACH,CAJ6B,CAK9B;EACA;EACA;;;EACA,IAAI,OAAOzB,QAAP,KAAoB,QAApB,IAAgC,CAACA,QAArC,EAA+C;IAC3CyB,mBAAmB,GAAG,IAAIG,GAAJ,CAAQF,mBAAR,CAAtB;IACA,OAAOD,mBAAP;EACH;;EACD,IAAII,gBAAgB,GAAG7B,QAAQ,CAAC8B,aAAT,CAAuB,OAAvB,CAAvB;EACAL,mBAAmB,GAAG,IAAIG,GAAJ,CAAQF,mBAAmB,CAACK,MAApB,CAA2BC,KAAK,IAAI;IAC9DH,gBAAgB,CAACI,YAAjB,CAA8B,MAA9B,EAAsCD,KAAtC;IACA,OAAOH,gBAAgB,CAACZ,IAAjB,KAA0Be,KAAjC;EACH,CAH6B,CAAR,CAAtB;EAIA,OAAOP,mBAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIS,qBAAJ;AACA;AACA;AACA;AACA;;AACA,SAASC,6BAAT,GAAyC;EACrC,IAAID,qBAAqB,IAAI,IAAzB,IAAiC,OAAO3B,MAAP,KAAkB,WAAvD,EAAoE;IAChE,IAAI;MACAA,MAAM,CAAC6B,gBAAP,CAAwB,MAAxB,EAAgC,IAAhC,EAAsChB,MAAM,CAACiB,cAAP,CAAsB,EAAtB,EAA0B,SAA1B,EAAqC;QACvEC,GAAG,EAAE,MAAOJ,qBAAqB,GAAG;MADmC,CAArC,CAAtC;IAGH,CAJD,SAKQ;MACJA,qBAAqB,GAAGA,qBAAqB,IAAI,KAAjD;IACH;EACJ;;EACD,OAAOA,qBAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASK,+BAAT,CAAyCC,OAAzC,EAAkD;EAC9C,OAAOL,6BAA6B,KAAKK,OAAL,GAAe,CAAC,CAACA,OAAO,CAACC,OAA7D;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,IAAIC,iBAAJ;AACA;;AACA,IAAIC,uBAAJ;AACA;;AACA,SAASC,sBAAT,GAAkC;EAC9B,IAAID,uBAAuB,IAAI,IAA/B,EAAqC;IACjC;IACA;IACA,IAAI,OAAO3C,QAAP,KAAoB,QAApB,IAAgC,CAACA,QAAjC,IAA6C,OAAO6C,OAAP,KAAmB,UAAhE,IAA8E,CAACA,OAAnF,EAA4F;MACxFF,uBAAuB,GAAG,KAA1B;MACA,OAAOA,uBAAP;IACH,CANgC,CAOjC;;;IACA,IAAI,oBAAoB3C,QAAQ,CAAC8C,eAAT,CAAyBC,KAAjD,EAAwD;MACpDJ,uBAAuB,GAAG,IAA1B;IACH,CAFD,MAGK;MACD;MACA;MACA,MAAMK,gBAAgB,GAAGH,OAAO,CAACI,SAAR,CAAkBC,QAA3C;;MACA,IAAIF,gBAAJ,EAAsB;QAClB;QACA;QACA;QACA;QACAL,uBAAuB,GAAG,CAAC,4BAA4BzC,IAA5B,CAAiC8C,gBAAgB,CAACG,QAAjB,EAAjC,CAA3B;MACH,CAND,MAOK;QACDR,uBAAuB,GAAG,KAA1B;MACH;IACJ;EACJ;;EACD,OAAOA,uBAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASS,oBAAT,GAAgC;EAC5B;EACA,IAAI,OAAOpD,QAAP,KAAoB,QAApB,IAAgC,CAACA,QAArC,EAA+C;IAC3C,OAAO;IAAE;IAAT;EACH;;EACD,IAAI0C,iBAAiB,IAAI,IAAzB,EAA+B;IAC3B;IACA,MAAMW,eAAe,GAAGrD,QAAQ,CAAC8B,aAAT,CAAuB,KAAvB,CAAxB;IACA,MAAMwB,cAAc,GAAGD,eAAe,CAACN,KAAvC;IACAM,eAAe,CAACE,GAAhB,GAAsB,KAAtB;IACAD,cAAc,CAACE,KAAf,GAAuB,KAAvB;IACAF,cAAc,CAACG,QAAf,GAA0B,MAA1B;IACAH,cAAc,CAACI,UAAf,GAA4B,QAA5B;IACAJ,cAAc,CAACK,aAAf,GAA+B,MAA/B;IACAL,cAAc,CAACM,QAAf,GAA0B,UAA1B;IACA,MAAMC,OAAO,GAAG7D,QAAQ,CAAC8B,aAAT,CAAuB,KAAvB,CAAhB;IACA,MAAMgC,YAAY,GAAGD,OAAO,CAACd,KAA7B;IACAe,YAAY,CAACN,KAAb,GAAqB,KAArB;IACAM,YAAY,CAACC,MAAb,GAAsB,KAAtB;IACAV,eAAe,CAACW,WAAhB,CAA4BH,OAA5B;IACA7D,QAAQ,CAACiE,IAAT,CAAcD,WAAd,CAA0BX,eAA1B;IACAX,iBAAiB,GAAG;IAAE;IAAtB,CAhB2B,CAiB3B;IACA;IACA;;IACA,IAAIW,eAAe,CAACa,UAAhB,KAA+B,CAAnC,EAAsC;MAClC;MACA;MACA;MACA;MACAb,eAAe,CAACa,UAAhB,GAA6B,CAA7B;MACAxB,iBAAiB,GACbW,eAAe,CAACa,UAAhB,KAA+B,CAA/B,GAAmC;MAAE;MAArC,EAAuE;MAAE;MAD7E;IAEH;;IACDb,eAAe,CAACc,MAAhB;EACH;;EACD,OAAOzB,iBAAP;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,IAAI0B,oBAAJ;AACA;;AACA,SAASC,kBAAT,GAA8B;EAC1B,IAAID,oBAAoB,IAAI,IAA5B,EAAkC;IAC9B,MAAME,IAAI,GAAG,OAAOtE,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAACsE,IAA3C,GAAkD,IAA/D;IACAF,oBAAoB,GAAG,CAAC,EAAEE,IAAI,KAAKA,IAAI,CAACC,gBAAL,IAAyBD,IAAI,CAACE,YAAnC,CAAN,CAAxB;EACH;;EACD,OAAOJ,oBAAP;AACH;AACD;;;AACA,SAASK,cAAT,CAAwBC,OAAxB,EAAiC;EAC7B,IAAIL,kBAAkB,EAAtB,EAA0B;IACtB,MAAMM,QAAQ,GAAGD,OAAO,CAACE,WAAR,GAAsBF,OAAO,CAACE,WAAR,EAAtB,GAA8C,IAA/D,CADsB,CAEtB;IACA;;IACA,IAAI,OAAOC,UAAP,KAAsB,WAAtB,IAAqCA,UAArC,IAAmDF,QAAQ,YAAYE,UAA3E,EAAuF;MACnF,OAAOF,QAAP;IACH;EACJ;;EACD,OAAO,IAAP;AACH;AACD;AACA;AACA;AACA;;;AACA,SAASG,iCAAT,GAA6C;EACzC,IAAIC,aAAa,GAAG,OAAO/E,QAAP,KAAoB,WAApB,IAAmCA,QAAnC,GACdA,QAAQ,CAAC+E,aADK,GAEd,IAFN;;EAGA,OAAOA,aAAa,IAAIA,aAAa,CAACC,UAAtC,EAAkD;IAC9C,MAAMC,gBAAgB,GAAGF,aAAa,CAACC,UAAd,CAAyBD,aAAlD;;IACA,IAAIE,gBAAgB,KAAKF,aAAzB,EAAwC;MACpC;IACH,CAFD,MAGK;MACDA,aAAa,GAAGE,gBAAhB;IACH;EACJ;;EACD,OAAOF,aAAP;AACH;AACD;;;AACA,SAASG,eAAT,CAAyBC,KAAzB,EAAgC;EAC5B;EACA;EACA,OAAQA,KAAK,CAACC,YAAN,GAAqBD,KAAK,CAACC,YAAN,GAAqB,CAArB,CAArB,GAA+CD,KAAK,CAACE,MAA7D;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,SAASC,kBAAT,GAA8B;EAC1B;EACA;EACA;EACA;EACA,OACA;IACC,OAAOC,SAAP,KAAqB,WAArB,IAAoC,CAAC,CAACA,SAAvC,IACI;IACC,OAAOC,OAAP,KAAmB,WAAnB,IAAkC,CAAC,CAACA,OAFzC,IAGI;IACC,OAAOC,IAAP,KAAgB,WAAhB,IAA+B,CAAC,CAACA,IAJtC,IAKI;IACC,OAAOC,KAAP,KAAiB,WAAjB,IAAgC,CAAC,CAACA;EARvC;AASH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAAS9F,QAAT,EAAmB0B,cAAnB,EAAmC4D,eAAnC,EAAoDJ,iCAApD,EAAuFL,cAAvF,EAAuGa,kBAAvG,EAA2HjB,kBAA3H,EAA+IjB,oBAA/I,EAAqKzB,sBAArK,EAA6LY,+BAA7L,EAA8NJ,6BAA9N,EAA6PS,sBAA7P"}, "metadata": {}, "sourceType": "module"}