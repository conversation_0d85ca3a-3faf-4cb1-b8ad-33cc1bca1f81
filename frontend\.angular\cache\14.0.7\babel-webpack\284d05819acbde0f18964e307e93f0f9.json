{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let last = scheduler.now();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const now = scheduler.now();\n      const interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n\n}", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "timeInterval", "scheduler", "source", "subscriber", "last", "now", "subscribe", "value", "interval", "next", "TimeInterval", "constructor"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/timeInterval.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler = asyncScheduler) {\n    return operate((source, subscriber) => {\n        let last = scheduler.now();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const now = scheduler.now();\n            const interval = now - last;\n            last = now;\n            subscriber.next(new TimeInterval(value, interval));\n        }));\n    });\n}\nexport class TimeInterval {\n    constructor(value, interval) {\n        this.value = value;\n        this.interval = interval;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAT,QAA+B,oBAA/B;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,OAAO,SAASC,YAAT,CAAsBC,SAAS,GAAGJ,cAAlC,EAAkD;EACrD,OAAOC,OAAO,CAAC,CAACI,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,IAAI,GAAGH,SAAS,CAACI,GAAV,EAAX;IACAH,MAAM,CAACI,SAAP,CAAiBP,wBAAwB,CAACI,UAAD,EAAcI,KAAD,IAAW;MAC7D,MAAMF,GAAG,GAAGJ,SAAS,CAACI,GAAV,EAAZ;MACA,MAAMG,QAAQ,GAAGH,GAAG,GAAGD,IAAvB;MACAA,IAAI,GAAGC,GAAP;MACAF,UAAU,CAACM,IAAX,CAAgB,IAAIC,YAAJ,CAAiBH,KAAjB,EAAwBC,QAAxB,CAAhB;IACH,CALwC,CAAzC;EAMH,CARa,CAAd;AASH;AACD,OAAO,MAAME,YAAN,CAAmB;EACtBC,WAAW,CAACJ,KAAD,EAAQC,QAAR,EAAkB;IACzB,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKC,QAAL,GAAgBA,QAAhB;EACH;;AAJqB"}, "metadata": {}, "sourceType": "module"}