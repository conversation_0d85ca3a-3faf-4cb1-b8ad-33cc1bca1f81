{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport Chart from 'chart.js/auto';\n\nclass UIChart {\n  constructor(el) {\n    this.el = el;\n    this.plugins = [];\n    this.responsive = true;\n    this.onDataSelect = new EventEmitter();\n    this._options = {};\n  }\n\n  get data() {\n    return this._data;\n  }\n\n  set data(val) {\n    this._data = val;\n    this.reinit();\n  }\n\n  get options() {\n    return this._options;\n  }\n\n  set options(val) {\n    this._options = val;\n    this.reinit();\n  }\n\n  ngAfterViewInit() {\n    this.initChart();\n    this.initialized = true;\n  }\n\n  onCanvasClick(event) {\n    if (this.chart) {\n      const element = this.chart.getElementsAtEventForMode(event, 'nearest', {\n        intersect: true\n      }, false);\n      const dataset = this.chart.getElementsAtEventForMode(event, 'dataset', {\n        intersect: true\n      }, false);\n\n      if (element && element[0] && dataset) {\n        this.onDataSelect.emit({\n          originalEvent: event,\n          element: element[0],\n          dataset: dataset\n        });\n      }\n    }\n  }\n\n  initChart() {\n    let opts = this.options || {};\n    opts.responsive = this.responsive; // allows chart to resize in responsive mode\n\n    if (opts.responsive && (this.height || this.width)) {\n      opts.maintainAspectRatio = false;\n    }\n\n    this.chart = new Chart(this.el.nativeElement.children[0].children[0], {\n      type: this.type,\n      data: this.data,\n      options: this.options,\n      plugins: this.plugins\n    });\n  }\n\n  getCanvas() {\n    return this.el.nativeElement.children[0].children[0];\n  }\n\n  getBase64Image() {\n    return this.chart.toBase64Image();\n  }\n\n  generateLegend() {\n    if (this.chart) {\n      return this.chart.generateLegend();\n    }\n  }\n\n  refresh() {\n    if (this.chart) {\n      this.chart.update();\n    }\n  }\n\n  reinit() {\n    if (this.chart) {\n      this.chart.destroy();\n      this.initChart();\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.chart) {\n      this.chart.destroy();\n      this.initialized = false;\n      this.chart = null;\n    }\n  }\n\n}\n\nUIChart.ɵfac = function UIChart_Factory(t) {\n  return new (t || UIChart)(i0.ɵɵdirectiveInject(i0.ElementRef));\n};\n\nUIChart.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: UIChart,\n  selectors: [[\"p-chart\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    type: \"type\",\n    plugins: \"plugins\",\n    width: \"width\",\n    height: \"height\",\n    responsive: \"responsive\",\n    data: \"data\",\n    options: \"options\"\n  },\n  outputs: {\n    onDataSelect: \"onDataSelect\"\n  },\n  decls: 2,\n  vars: 6,\n  consts: [[2, \"position\", \"relative\"], [3, \"click\"]],\n  template: function UIChart_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"canvas\", 1);\n      i0.ɵɵlistener(\"click\", function UIChart_Template_canvas_click_1_listener($event) {\n        return ctx.onCanvasClick($event);\n      });\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵstyleProp(\"width\", ctx.responsive && !ctx.width ? null : ctx.width)(\"height\", ctx.responsive && !ctx.height ? null : ctx.height);\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"width\", ctx.responsive && !ctx.width ? null : ctx.width)(\"height\", ctx.responsive && !ctx.height ? null : ctx.height);\n    }\n  },\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UIChart, [{\n    type: Component,\n    args: [{\n      selector: 'p-chart',\n      template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      }\n    }]\n  }], function () {\n    return [{\n      type: i0.ElementRef\n    }];\n  }, {\n    type: [{\n      type: Input\n    }],\n    plugins: [{\n      type: Input\n    }],\n    width: [{\n      type: Input\n    }],\n    height: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    onDataSelect: [{\n      type: Output\n    }],\n    data: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ChartModule {}\n\nChartModule.ɵfac = function ChartModule_Factory(t) {\n  return new (t || ChartModule)();\n};\n\nChartModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ChartModule\n});\nChartModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChartModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [UIChart],\n      declarations: [UIChart]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ChartModule, UIChart };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "CommonModule", "Chart", "UIChart", "constructor", "el", "plugins", "responsive", "onDataSelect", "_options", "data", "_data", "val", "reinit", "options", "ngAfterViewInit", "initChart", "initialized", "onCanvasClick", "event", "chart", "element", "getElementsAtEventForMode", "intersect", "dataset", "emit", "originalEvent", "opts", "height", "width", "maintainAspectRatio", "nativeElement", "children", "type", "get<PERSON>anvas", "getBase64Image", "toBase64Image", "generateLegend", "refresh", "update", "destroy", "ngOnDestroy", "ɵfac", "ElementRef", "ɵcmp", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "ChartModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-chart.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport Chart from 'chart.js/auto';\n\nclass UIChart {\n    constructor(el) {\n        this.el = el;\n        this.plugins = [];\n        this.responsive = true;\n        this.onDataSelect = new EventEmitter();\n        this._options = {};\n    }\n    get data() {\n        return this._data;\n    }\n    set data(val) {\n        this._data = val;\n        this.reinit();\n    }\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        this.reinit();\n    }\n    ngAfterViewInit() {\n        this.initChart();\n        this.initialized = true;\n    }\n    onCanvasClick(event) {\n        if (this.chart) {\n            const element = this.chart.getElementsAtEventForMode(event, 'nearest', { intersect: true }, false);\n            const dataset = this.chart.getElementsAtEventForMode(event, 'dataset', { intersect: true }, false);\n            if (element && element[0] && dataset) {\n                this.onDataSelect.emit({ originalEvent: event, element: element[0], dataset: dataset });\n            }\n        }\n    }\n    initChart() {\n        let opts = this.options || {};\n        opts.responsive = this.responsive;\n        // allows chart to resize in responsive mode\n        if (opts.responsive && (this.height || this.width)) {\n            opts.maintainAspectRatio = false;\n        }\n        this.chart = new Chart(this.el.nativeElement.children[0].children[0], {\n            type: this.type,\n            data: this.data,\n            options: this.options,\n            plugins: this.plugins\n        });\n    }\n    getCanvas() {\n        return this.el.nativeElement.children[0].children[0];\n    }\n    getBase64Image() {\n        return this.chart.toBase64Image();\n    }\n    generateLegend() {\n        if (this.chart) {\n            return this.chart.generateLegend();\n        }\n    }\n    refresh() {\n        if (this.chart) {\n            this.chart.update();\n        }\n    }\n    reinit() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initChart();\n        }\n    }\n    ngOnDestroy() {\n        if (this.chart) {\n            this.chart.destroy();\n            this.initialized = false;\n            this.chart = null;\n        }\n    }\n}\nUIChart.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UIChart, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component });\nUIChart.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: UIChart, selector: \"p-chart\", inputs: { type: \"type\", plugins: \"plugins\", width: \"width\", height: \"height\", responsive: \"responsive\", data: \"data\", options: \"options\" }, outputs: { onDataSelect: \"onDataSelect\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: UIChart, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-chart',\n                    template: `\n        <div style=\"position:relative\" [style.width]=\"responsive && !width ? null : width\" [style.height]=\"responsive && !height ? null : height\">\n            <canvas [attr.width]=\"responsive && !width ? null : width\" [attr.height]=\"responsive && !height ? null : height\" (click)=\"onCanvasClick($event)\"></canvas>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        'class': 'p-element'\n                    }\n                }]\n        }], ctorParameters: function () { return [{ type: i0.ElementRef }]; }, propDecorators: { type: [{\n                type: Input\n            }], plugins: [{\n                type: Input\n            }], width: [{\n                type: Input\n            }], height: [{\n                type: Input\n            }], responsive: [{\n                type: Input\n            }], onDataSelect: [{\n                type: Output\n            }], data: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }] } });\nclass ChartModule {\n}\nChartModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChartModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nChartModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ChartModule, declarations: [UIChart], imports: [CommonModule], exports: [UIChart] });\nChartModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChartModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ChartModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [UIChart],\n                    declarations: [UIChart]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ChartModule, UIChart };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAOC,KAAP,MAAkB,eAAlB;;AAEA,MAAMC,OAAN,CAAc;EACVC,WAAW,CAACC,EAAD,EAAK;IACZ,KAAKA,EAAL,GAAUA,EAAV;IACA,KAAKC,OAAL,GAAe,EAAf;IACA,KAAKC,UAAL,GAAkB,IAAlB;IACA,KAAKC,YAAL,GAAoB,IAAId,YAAJ,EAApB;IACA,KAAKe,QAAL,GAAgB,EAAhB;EACH;;EACO,IAAJC,IAAI,GAAG;IACP,OAAO,KAAKC,KAAZ;EACH;;EACO,IAAJD,IAAI,CAACE,GAAD,EAAM;IACV,KAAKD,KAAL,GAAaC,GAAb;IACA,KAAKC,MAAL;EACH;;EACU,IAAPC,OAAO,GAAG;IACV,OAAO,KAAKL,QAAZ;EACH;;EACU,IAAPK,OAAO,CAACF,GAAD,EAAM;IACb,KAAKH,QAAL,GAAgBG,GAAhB;IACA,KAAKC,MAAL;EACH;;EACDE,eAAe,GAAG;IACd,KAAKC,SAAL;IACA,KAAKC,WAAL,GAAmB,IAAnB;EACH;;EACDC,aAAa,CAACC,KAAD,EAAQ;IACjB,IAAI,KAAKC,KAAT,EAAgB;MACZ,MAAMC,OAAO,GAAG,KAAKD,KAAL,CAAWE,yBAAX,CAAqCH,KAArC,EAA4C,SAA5C,EAAuD;QAAEI,SAAS,EAAE;MAAb,CAAvD,EAA4E,KAA5E,CAAhB;MACA,MAAMC,OAAO,GAAG,KAAKJ,KAAL,CAAWE,yBAAX,CAAqCH,KAArC,EAA4C,SAA5C,EAAuD;QAAEI,SAAS,EAAE;MAAb,CAAvD,EAA4E,KAA5E,CAAhB;;MACA,IAAIF,OAAO,IAAIA,OAAO,CAAC,CAAD,CAAlB,IAAyBG,OAA7B,EAAsC;QAClC,KAAKhB,YAAL,CAAkBiB,IAAlB,CAAuB;UAAEC,aAAa,EAAEP,KAAjB;UAAwBE,OAAO,EAAEA,OAAO,CAAC,CAAD,CAAxC;UAA6CG,OAAO,EAAEA;QAAtD,CAAvB;MACH;IACJ;EACJ;;EACDR,SAAS,GAAG;IACR,IAAIW,IAAI,GAAG,KAAKb,OAAL,IAAgB,EAA3B;IACAa,IAAI,CAACpB,UAAL,GAAkB,KAAKA,UAAvB,CAFQ,CAGR;;IACA,IAAIoB,IAAI,CAACpB,UAAL,KAAoB,KAAKqB,MAAL,IAAe,KAAKC,KAAxC,CAAJ,EAAoD;MAChDF,IAAI,CAACG,mBAAL,GAA2B,KAA3B;IACH;;IACD,KAAKV,KAAL,GAAa,IAAIlB,KAAJ,CAAU,KAAKG,EAAL,CAAQ0B,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,EAAkCA,QAAlC,CAA2C,CAA3C,CAAV,EAAyD;MAClEC,IAAI,EAAE,KAAKA,IADuD;MAElEvB,IAAI,EAAE,KAAKA,IAFuD;MAGlEI,OAAO,EAAE,KAAKA,OAHoD;MAIlER,OAAO,EAAE,KAAKA;IAJoD,CAAzD,CAAb;EAMH;;EACD4B,SAAS,GAAG;IACR,OAAO,KAAK7B,EAAL,CAAQ0B,aAAR,CAAsBC,QAAtB,CAA+B,CAA/B,EAAkCA,QAAlC,CAA2C,CAA3C,CAAP;EACH;;EACDG,cAAc,GAAG;IACb,OAAO,KAAKf,KAAL,CAAWgB,aAAX,EAAP;EACH;;EACDC,cAAc,GAAG;IACb,IAAI,KAAKjB,KAAT,EAAgB;MACZ,OAAO,KAAKA,KAAL,CAAWiB,cAAX,EAAP;IACH;EACJ;;EACDC,OAAO,GAAG;IACN,IAAI,KAAKlB,KAAT,EAAgB;MACZ,KAAKA,KAAL,CAAWmB,MAAX;IACH;EACJ;;EACD1B,MAAM,GAAG;IACL,IAAI,KAAKO,KAAT,EAAgB;MACZ,KAAKA,KAAL,CAAWoB,OAAX;MACA,KAAKxB,SAAL;IACH;EACJ;;EACDyB,WAAW,GAAG;IACV,IAAI,KAAKrB,KAAT,EAAgB;MACZ,KAAKA,KAAL,CAAWoB,OAAX;MACA,KAAKvB,WAAL,GAAmB,KAAnB;MACA,KAAKG,KAAL,GAAa,IAAb;IACH;EACJ;;AA7ES;;AA+EdjB,OAAO,CAACuC,IAAR;EAAA,iBAAoGvC,OAApG,EAA0FV,EAA1F,mBAA6HA,EAAE,CAACkD,UAAhI;AAAA;;AACAxC,OAAO,CAACyC,IAAR,kBAD0FnD,EAC1F;EAAA,MAAwFU,OAAxF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MAD0FV,EAElF,4CADR;MAD0FA,EAGmC;QAAA,OAAS,yBAAT;MAAA,EAF7H;MAD0FA,EAGmE,iBAF7J;IAAA;;IAAA;MAD0FA,EAEnD,mIADvC;MAD0FA,EAGtE,aAFpB;MAD0FA,EAGtE,mIAFpB;IAAA;EAAA;EAAA;EAAA;AAAA;;AAKA;EAAA,mDAN0FA,EAM1F,mBAA2FU,OAA3F,EAAgH,CAAC;IACrG8B,IAAI,EAAEtC,SAD+F;IAErGkD,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SADX;MAECC,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KANmB;MAOCC,eAAe,EAAEpD,uBAAuB,CAACqD,MAP1C;MAQCC,aAAa,EAAErD,iBAAiB,CAACsD,IARlC;MASCC,IAAI,EAAE;QACF,SAAS;MADP;IATP,CAAD;EAF+F,CAAD,CAAhH,EAe4B,YAAY;IAAE,OAAO,CAAC;MAAEnB,IAAI,EAAExC,EAAE,CAACkD;IAAX,CAAD,CAAP;EAAmC,CAf7E,EAe+F;IAAEV,IAAI,EAAE,CAAC;MACxFA,IAAI,EAAEnC;IADkF,CAAD,CAAR;IAE/EQ,OAAO,EAAE,CAAC;MACV2B,IAAI,EAAEnC;IADI,CAAD,CAFsE;IAI/E+B,KAAK,EAAE,CAAC;MACRI,IAAI,EAAEnC;IADE,CAAD,CAJwE;IAM/E8B,MAAM,EAAE,CAAC;MACTK,IAAI,EAAEnC;IADG,CAAD,CANuE;IAQ/ES,UAAU,EAAE,CAAC;MACb0B,IAAI,EAAEnC;IADO,CAAD,CARmE;IAU/EU,YAAY,EAAE,CAAC;MACfyB,IAAI,EAAElC;IADS,CAAD,CAViE;IAY/EW,IAAI,EAAE,CAAC;MACPuB,IAAI,EAAEnC;IADC,CAAD,CAZyE;IAc/EgB,OAAO,EAAE,CAAC;MACVmB,IAAI,EAAEnC;IADI,CAAD;EAdsE,CAf/F;AAAA;;AAgCA,MAAMuD,WAAN,CAAkB;;AAElBA,WAAW,CAACX,IAAZ;EAAA,iBAAwGW,WAAxG;AAAA;;AACAA,WAAW,CAACC,IAAZ,kBAzC0F7D,EAyC1F;EAAA,MAAyG4D;AAAzG;AACAA,WAAW,CAACE,IAAZ,kBA1C0F9D,EA0C1F;EAAA,UAAgIQ,YAAhI;AAAA;;AACA;EAAA,mDA3C0FR,EA2C1F,mBAA2F4D,WAA3F,EAAoH,CAAC;IACzGpB,IAAI,EAAEjC,QADmG;IAEzG6C,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAACvD,YAAD,CADV;MAECwD,OAAO,EAAE,CAACtD,OAAD,CAFV;MAGCuD,YAAY,EAAE,CAACvD,OAAD;IAHf,CAAD;EAFmG,CAAD,CAApH;AAAA;AASA;AACA;AACA;;;AAEA,SAASkD,WAAT,EAAsBlD,OAAtB"}, "metadata": {}, "sourceType": "module"}