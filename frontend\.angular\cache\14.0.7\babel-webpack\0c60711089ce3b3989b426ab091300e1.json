{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;", "map": {"version": 3, "names": ["mergeMap", "flatMap"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/flatMap.js"], "sourcesContent": ["import { mergeMap } from './mergeMap';\nexport const flatMap = mergeMap;\n"], "mappings": "AAAA,SAASA,QAAT,QAAyB,YAAzB;AACA,OAAO,MAAMC,OAAO,GAAGD,QAAhB"}, "metadata": {}, "sourceType": "module"}