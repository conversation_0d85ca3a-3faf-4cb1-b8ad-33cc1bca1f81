{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nfunction Steps_li_2_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\n\nfunction Steps_li_2_a_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nconst _c0 = function () {\n  return {\n    exact: false\n  };\n};\n\nfunction Steps_li_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 7);\n    i0.ɵɵlistener(\"click\", function Steps_li_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      const item_r1 = ctx_r13.$implicit;\n      const i_r2 = ctx_r13.index;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.itemClick($event, item_r1, i_r2));\n    })(\"keydown.enter\", function Steps_li_2_a_2_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r16 = i0.ɵɵnextContext();\n      const item_r1 = ctx_r16.$implicit;\n      const i_r2 = ctx_r16.index;\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.itemClick($event, item_r1, i_r2));\n    });\n    i0.ɵɵelementStart(1, \"span\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Steps_li_2_a_2_span_3_Template, 2, 1, \"span\", 9);\n    i0.ɵɵtemplate(4, Steps_li_2_a_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 10, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r8 = i0.ɵɵreference(5);\n\n    const ctx_r17 = i0.ɵɵnextContext();\n    const item_r1 = ctx_r17.$implicit;\n    const i_r2 = ctx_r17.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", item_r1.routerLink)(\"queryParams\", item_r1.queryParams)(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", item_r1.routerLinkActiveOptions || i0.ɵɵpureFunction0(16, _c0))(\"target\", item_r1.target)(\"fragment\", item_r1.fragment)(\"queryParamsHandling\", item_r1.queryParamsHandling)(\"preserveFragment\", item_r1.preserveFragment)(\"skipLocationChange\", item_r1.skipLocationChange)(\"replaceUrl\", item_r1.replaceUrl)(\"state\", item_r1.state);\n    i0.ɵɵattribute(\"id\", item_r1.id)(\"tabindex\", item_r1.disabled || ctx_r4.readonly ? null : item_r1.tabindex ? item_r1.tabindex : \"0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r2 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.escape !== false)(\"ngIfElse\", _r8);\n  }\n}\n\nfunction Steps_li_2_ng_template_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r1.label);\n  }\n}\n\nfunction Steps_li_2_ng_template_3_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"innerHTML\", item_r1.label, i0.ɵɵsanitizeHtml);\n  }\n}\n\nfunction Steps_li_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n\n    i0.ɵɵelementStart(0, \"a\", 13);\n    i0.ɵɵlistener(\"click\", function Steps_li_2_ng_template_3_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r24 = i0.ɵɵnextContext();\n      const item_r1 = ctx_r24.$implicit;\n      const i_r2 = ctx_r24.index;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.itemClick($event, item_r1, i_r2));\n    })(\"keydown.enter\", function Steps_li_2_ng_template_3_Template_a_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r27 = i0.ɵɵnextContext();\n      const item_r1 = ctx_r27.$implicit;\n      const i_r2 = ctx_r27.index;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.itemClick($event, item_r1, i_r2));\n    });\n    i0.ɵɵelementStart(1, \"span\", 8);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Steps_li_2_ng_template_3_span_3_Template, 2, 1, \"span\", 9);\n    i0.ɵɵtemplate(4, Steps_li_2_ng_template_3_ng_template_4_Template, 1, 1, \"ng-template\", null, 14, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const _r19 = i0.ɵɵreference(5);\n\n    const ctx_r28 = i0.ɵɵnextContext();\n    const item_r1 = ctx_r28.$implicit;\n    const i_r2 = ctx_r28.index;\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", item_r1.target);\n    i0.ɵɵattribute(\"href\", item_r1.url, i0.ɵɵsanitizeUrl)(\"id\", item_r1.id)(\"tabindex\", item_r1.disabled || i_r2 !== ctx_r6.activeIndex && ctx_r6.readonly ? null : item_r1.tabindex ? item_r1.tabindex : \"0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r2 + 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", item_r1.escape !== false)(\"ngIfElse\", _r19);\n  }\n}\n\nconst _c1 = function (a0, a1) {\n  return {\n    \"p-highlight p-steps-current\": a0,\n    \"p-disabled\": a1\n  };\n};\n\nfunction Steps_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 3, 4);\n    i0.ɵɵtemplate(2, Steps_li_2_a_2_Template, 6, 17, \"a\", 5);\n    i0.ɵɵtemplate(3, Steps_li_2_ng_template_3_Template, 6, 7, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n\n    const _r5 = i0.ɵɵreference(4);\n\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(item_r1.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", item_r1.style)(\"tooltipOptions\", item_r1.tooltipOptions)(\"ngClass\", i0.ɵɵpureFunction2(9, _c1, ctx_r0.isActive(item_r1, i_r2), item_r1.disabled || ctx_r0.readonly && !ctx_r0.isActive(item_r1, i_r2)));\n    i0.ɵɵattribute(\"aria-selected\", i_r2 === ctx_r0.activeIndex)(\"aria-expanded\", i_r2 === ctx_r0.activeIndex);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isClickableRouterLink(item_r1))(\"ngIfElse\", _r5);\n  }\n}\n\nconst _c2 = function (a1) {\n  return {\n    \"p-steps p-component\": true,\n    \"p-readonly\": a1\n  };\n};\n\nclass Steps {\n  constructor(router, route, cd) {\n    this.router = router;\n    this.route = route;\n    this.cd = cd;\n    this.activeIndex = 0;\n    this.readonly = true;\n    this.activeIndexChange = new EventEmitter();\n  }\n\n  ngOnInit() {\n    this.subscription = this.router.events.subscribe(() => this.cd.markForCheck());\n  }\n\n  itemClick(event, item, i) {\n    if (this.readonly || item.disabled) {\n      event.preventDefault();\n      return;\n    }\n\n    this.activeIndexChange.emit(i);\n\n    if (!item.url && !item.routerLink) {\n      event.preventDefault();\n    }\n\n    if (item.command) {\n      item.command({\n        originalEvent: event,\n        item: item,\n        index: i\n      });\n    }\n  }\n\n  isClickableRouterLink(item) {\n    return item.routerLink && !this.readonly && !item.disabled;\n  }\n\n  isActive(item, index) {\n    if (item.routerLink) {\n      let routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n      return this.router.isActive(this.router.createUrlTree(routerLink, {\n        relativeTo: this.route\n      }).toString(), false);\n    }\n\n    return index === this.activeIndex;\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n\n}\n\nSteps.ɵfac = function Steps_Factory(t) {\n  return new (t || Steps)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n};\n\nSteps.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: Steps,\n  selectors: [[\"p-steps\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    activeIndex: \"activeIndex\",\n    model: \"model\",\n    readonly: \"readonly\",\n    style: \"style\",\n    styleClass: \"styleClass\"\n  },\n  outputs: {\n    activeIndexChange: \"activeIndexChange\"\n  },\n  decls: 3,\n  vars: 7,\n  consts: [[3, \"ngClass\", \"ngStyle\"], [\"role\", \"tablist\"], [\"class\", \"p-steps-item\", \"role\", \"tab\", \"pTooltip\", \"\", 3, \"ngStyle\", \"class\", \"tooltipOptions\", \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"role\", \"tab\", \"pTooltip\", \"\", 1, \"p-steps-item\", 3, \"ngStyle\", \"tooltipOptions\", \"ngClass\"], [\"menuitem\", \"\"], [\"role\", \"presentation\", \"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\", 4, \"ngIf\", \"ngIfElse\"], [\"elseBlock\", \"\"], [\"role\", \"presentation\", 1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"click\", \"keydown.enter\"], [1, \"p-steps-number\"], [\"class\", \"p-steps-title\", 4, \"ngIf\", \"ngIfElse\"], [\"htmlLabel\", \"\"], [1, \"p-steps-title\"], [1, \"p-steps-title\", 3, \"innerHTML\"], [\"role\", \"presentation\", 1, \"p-menuitem-link\", 3, \"target\", \"click\", \"keydown.enter\"], [\"htmlRouteLabel\", \"\"]],\n  template: function Steps_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0)(1, \"ul\", 1);\n      i0.ɵɵtemplate(2, Steps_li_2_Template, 5, 12, \"li\", 2);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵclassMap(ctx.styleClass);\n      i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c2, ctx.readonly))(\"ngStyle\", ctx.style);\n      i0.ɵɵadvance(2);\n      i0.ɵɵproperty(\"ngForOf\", ctx.model);\n    }\n  },\n  dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgStyle, i1.RouterLinkWithHref, i1.RouterLinkActive, i3.Tooltip],\n  styles: [\".p-steps{position:relative}.p-steps ul{padding:0;margin:0;list-style-type:none;display:flex}.p-steps-item{position:relative;display:flex;justify-content:center;flex:1 1 auto}.p-steps-item .p-menuitem-link{display:inline-flex;flex-direction:column;align-items:center;overflow:hidden;text-decoration:none}.p-steps.p-steps-readonly .p-steps-item{cursor:auto}.p-steps-item.p-steps-current .p-menuitem-link{cursor:default}.p-steps-title{white-space:nowrap}.p-steps-number{display:flex;align-items:center;justify-content:center}.p-steps-title{display:block}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Steps, [{\n    type: Component,\n    args: [{\n      selector: 'p-steps',\n      template: `\n        <div [ngClass]=\"{'p-steps p-component':true,'p-readonly':readonly}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ul role=\"tablist\">\n                <li *ngFor=\"let item of model; let i = index\" class=\"p-steps-item\" #menuitem [ngStyle]=\"item.style\" [class]=\"item.styleClass\" role=\"tab\" [attr.aria-selected]=\"i === activeIndex\" [attr.aria-expanded]=\"i === activeIndex\" pTooltip [tooltipOptions]=\"item.tooltipOptions\"\n                    [ngClass]=\"{'p-highlight p-steps-current': isActive(item, i), 'p-disabled': item.disabled || (readonly && !isActive(item, i))}\">\n                    <a *ngIf=\"isClickableRouterLink(item); else elseBlock\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" role=\"presentation\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled || readonly ? null : (item.tabindex ? item.tabindex : '0')\"\n                        [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                        <span class=\"p-steps-number\">{{i + 1}}</span>\n                        <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                    </a>\n                    <ng-template #elseBlock>\n                        <a [attr.href]=\"item.url\" class=\"p-menuitem-link\" role=\"presentation\" (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\"\n                            [attr.tabindex]=\"item.disabled||(i !== activeIndex && readonly) ? null : (item.tabindex ? item.tabindex : '0')\">\n                            <span class=\"p-steps-number\">{{i + 1}}</span>\n                            <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                        </a>\n                    </ng-template>\n                </li>\n            </ul>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-steps{position:relative}.p-steps ul{padding:0;margin:0;list-style-type:none;display:flex}.p-steps-item{position:relative;display:flex;justify-content:center;flex:1 1 auto}.p-steps-item .p-menuitem-link{display:inline-flex;flex-direction:column;align-items:center;overflow:hidden;text-decoration:none}.p-steps.p-steps-readonly .p-steps-item{cursor:auto}.p-steps-item.p-steps-current .p-menuitem-link{cursor:default}.p-steps-title{white-space:nowrap}.p-steps-number{display:flex;align-items:center;justify-content:center}.p-steps-title{display:block}\\n\"]\n    }]\n  }], function () {\n    return [{\n      type: i1.Router\n    }, {\n      type: i1.ActivatedRoute\n    }, {\n      type: i0.ChangeDetectorRef\n    }];\n  }, {\n    activeIndex: [{\n      type: Input\n    }],\n    model: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }]\n  });\n})();\n\nclass StepsModule {}\n\nStepsModule.ɵfac = function StepsModule_Factory(t) {\n  return new (t || StepsModule)();\n};\n\nStepsModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: StepsModule\n});\nStepsModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(StepsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, TooltipModule],\n      exports: [Steps, RouterModule, TooltipModule],\n      declarations: [Steps]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { Steps, StepsModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "NgModule", "i2", "CommonModule", "i1", "RouterModule", "i3", "TooltipModule", "Steps", "constructor", "router", "route", "cd", "activeIndex", "readonly", "activeIndexChange", "ngOnInit", "subscription", "events", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "itemClick", "event", "item", "i", "disabled", "preventDefault", "emit", "url", "routerLink", "command", "originalEvent", "index", "isClickableRouterLink", "isActive", "Array", "isArray", "createUrlTree", "relativeTo", "toString", "ngOnDestroy", "unsubscribe", "ɵfac", "Router", "ActivatedRoute", "ChangeDetectorRef", "ɵcmp", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgStyle", "RouterLinkWithHref", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "model", "style", "styleClass", "StepsModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-steps.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule } from '@angular/core';\nimport * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\n\nclass Steps {\n    constructor(router, route, cd) {\n        this.router = router;\n        this.route = route;\n        this.cd = cd;\n        this.activeIndex = 0;\n        this.readonly = true;\n        this.activeIndexChange = new EventEmitter();\n    }\n    ngOnInit() {\n        this.subscription = this.router.events.subscribe(() => this.cd.markForCheck());\n    }\n    itemClick(event, item, i) {\n        if (this.readonly || item.disabled) {\n            event.preventDefault();\n            return;\n        }\n        this.activeIndexChange.emit(i);\n        if (!item.url && !item.routerLink) {\n            event.preventDefault();\n        }\n        if (item.command) {\n            item.command({\n                originalEvent: event,\n                item: item,\n                index: i\n            });\n        }\n    }\n    isClickableRouterLink(item) {\n        return item.routerLink && !this.readonly && !item.disabled;\n    }\n    isActive(item, index) {\n        if (item.routerLink) {\n            let routerLink = Array.isArray(item.routerLink) ? item.routerLink : [item.routerLink];\n            return this.router.isActive(this.router.createUrlTree(routerLink, { relativeTo: this.route }).toString(), false);\n        }\n        return index === this.activeIndex;\n    }\n    ngOnDestroy() {\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n}\nSteps.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Steps, deps: [{ token: i1.Router }, { token: i1.ActivatedRoute }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\nSteps.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: Steps, selector: \"p-steps\", inputs: { activeIndex: \"activeIndex\", model: \"model\", readonly: \"readonly\", style: \"style\", styleClass: \"styleClass\" }, outputs: { activeIndexChange: \"activeIndexChange\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div [ngClass]=\"{'p-steps p-component':true,'p-readonly':readonly}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ul role=\"tablist\">\n                <li *ngFor=\"let item of model; let i = index\" class=\"p-steps-item\" #menuitem [ngStyle]=\"item.style\" [class]=\"item.styleClass\" role=\"tab\" [attr.aria-selected]=\"i === activeIndex\" [attr.aria-expanded]=\"i === activeIndex\" pTooltip [tooltipOptions]=\"item.tooltipOptions\"\n                    [ngClass]=\"{'p-highlight p-steps-current': isActive(item, i), 'p-disabled': item.disabled || (readonly && !isActive(item, i))}\">\n                    <a *ngIf=\"isClickableRouterLink(item); else elseBlock\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" role=\"presentation\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled || readonly ? null : (item.tabindex ? item.tabindex : '0')\"\n                        [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                        <span class=\"p-steps-number\">{{i + 1}}</span>\n                        <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                    </a>\n                    <ng-template #elseBlock>\n                        <a [attr.href]=\"item.url\" class=\"p-menuitem-link\" role=\"presentation\" (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\"\n                            [attr.tabindex]=\"item.disabled||(i !== activeIndex && readonly) ? null : (item.tabindex ? item.tabindex : '0')\">\n                            <span class=\"p-steps-number\">{{i + 1}}</span>\n                            <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                        </a>\n                    </ng-template>\n                </li>\n            </ul>\n        </div>\n    `, isInline: true, styles: [\".p-steps{position:relative}.p-steps ul{padding:0;margin:0;list-style-type:none;display:flex}.p-steps-item{position:relative;display:flex;justify-content:center;flex:1 1 auto}.p-steps-item .p-menuitem-link{display:inline-flex;flex-direction:column;align-items:center;overflow:hidden;text-decoration:none}.p-steps.p-steps-readonly .p-steps-item{cursor:auto}.p-steps-item.p-steps-current .p-menuitem-link{cursor:default}.p-steps-title{white-space:nowrap}.p-steps-number{display:flex;align-items:center;justify-content:center}.p-steps-title{display:block}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i1.RouterLinkWithHref, selector: \"a[routerLink],area[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", \"relativeTo\", \"routerLink\"] }, { kind: \"directive\", type: i1.RouterLinkActive, selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i3.Tooltip, selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"fitContent\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: Steps, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-steps', template: `\n        <div [ngClass]=\"{'p-steps p-component':true,'p-readonly':readonly}\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ul role=\"tablist\">\n                <li *ngFor=\"let item of model; let i = index\" class=\"p-steps-item\" #menuitem [ngStyle]=\"item.style\" [class]=\"item.styleClass\" role=\"tab\" [attr.aria-selected]=\"i === activeIndex\" [attr.aria-expanded]=\"i === activeIndex\" pTooltip [tooltipOptions]=\"item.tooltipOptions\"\n                    [ngClass]=\"{'p-highlight p-steps-current': isActive(item, i), 'p-disabled': item.disabled || (readonly && !isActive(item, i))}\">\n                    <a *ngIf=\"isClickableRouterLink(item); else elseBlock\" [routerLink]=\"item.routerLink\" [queryParams]=\"item.queryParams\" role=\"presentation\" [routerLinkActive]=\"'p-menuitem-link-active'\" [routerLinkActiveOptions]=\"item.routerLinkActiveOptions||{exact:false}\" class=\"p-menuitem-link\"\n                        (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\" [attr.tabindex]=\"item.disabled || readonly ? null : (item.tabindex ? item.tabindex : '0')\"\n                        [fragment]=\"item.fragment\" [queryParamsHandling]=\"item.queryParamsHandling\" [preserveFragment]=\"item.preserveFragment\" [skipLocationChange]=\"item.skipLocationChange\" [replaceUrl]=\"item.replaceUrl\" [state]=\"item.state\">\n                        <span class=\"p-steps-number\">{{i + 1}}</span>\n                        <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlLabel\">{{item.label}}</span>\n                        <ng-template #htmlLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                    </a>\n                    <ng-template #elseBlock>\n                        <a [attr.href]=\"item.url\" class=\"p-menuitem-link\" role=\"presentation\" (click)=\"itemClick($event, item, i)\" (keydown.enter)=\"itemClick($event, item, i)\" [target]=\"item.target\" [attr.id]=\"item.id\"\n                            [attr.tabindex]=\"item.disabled||(i !== activeIndex && readonly) ? null : (item.tabindex ? item.tabindex : '0')\">\n                            <span class=\"p-steps-number\">{{i + 1}}</span>\n                            <span class=\"p-steps-title\" *ngIf=\"item.escape !== false; else htmlRouteLabel\">{{item.label}}</span>\n                            <ng-template #htmlRouteLabel><span class=\"p-steps-title\" [innerHTML]=\"item.label\"></span></ng-template>\n                        </a>\n                    </ng-template>\n                </li>\n            </ul>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-steps{position:relative}.p-steps ul{padding:0;margin:0;list-style-type:none;display:flex}.p-steps-item{position:relative;display:flex;justify-content:center;flex:1 1 auto}.p-steps-item .p-menuitem-link{display:inline-flex;flex-direction:column;align-items:center;overflow:hidden;text-decoration:none}.p-steps.p-steps-readonly .p-steps-item{cursor:auto}.p-steps-item.p-steps-current .p-menuitem-link{cursor:default}.p-steps-title{white-space:nowrap}.p-steps-number{display:flex;align-items:center;justify-content:center}.p-steps-title{display:block}\\n\"] }]\n        }], ctorParameters: function () { return [{ type: i1.Router }, { type: i1.ActivatedRoute }, { type: i0.ChangeDetectorRef }]; }, propDecorators: { activeIndex: [{\n                type: Input\n            }], model: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], activeIndexChange: [{\n                type: Output\n            }] } });\nclass StepsModule {\n}\nStepsModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: StepsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nStepsModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: StepsModule, declarations: [Steps], imports: [CommonModule, RouterModule, TooltipModule], exports: [Steps, RouterModule, TooltipModule] });\nStepsModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: StepsModule, imports: [CommonModule, RouterModule, TooltipModule, RouterModule, TooltipModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: StepsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, TooltipModule],\n                    exports: [Steps, RouterModule, TooltipModule],\n                    declarations: [Steps]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Steps, StepsModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,YAAT,EAAuBC,SAAvB,EAAkCC,uBAAlC,EAA2DC,iBAA3D,EAA8EC,KAA9E,EAAqFC,MAArF,EAA6FC,QAA7F,QAA6G,eAA7G;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,aAAT,QAA8B,iBAA9B;;;;IA+CwFb,EAUhE,8B;IAVgEA,EAUU,U;IAVVA,EAUwB,e;;;;oBAVxBA,E;IAAAA,EAUU,a;IAVVA,EAUU,iC;;;;;;IAVVA,EAWxC,yB;;;;oBAXwCA,E;IAAAA,EAWZ,wCAXYA,EAWZ,gB;;;;;;;;;;;;iBAXYA,E;;IAAAA,EAMpE,0B;IANoEA,EAOhE;MAPgEA,EAOhE;MAAA,gBAPgEA,EAOhE;MAAA;MAAA;MAAA,gBAPgEA,EAOhE;MAAA,OAPgEA,EAOvD,sDAAT;IAAA;MAPgEA,EAOhE;MAAA,gBAPgEA,EAOhE;MAAA;MAAA;MAAA,gBAPgEA,EAOhE;MAAA,OAPgEA,EAOV,sDAAtD;IAAA,E;IAPgEA,EAShE,6B;IATgEA,EASnC,U;IATmCA,EAS1B,e;IAT0BA,EAUhE,+D;IAVgEA,EAWhE,qFAXgEA,EAWhE,wB;IAXgEA,EAYpE,e;;;;gBAZoEA,E;;oBAAAA,E;;;mBAAAA,E;IAAAA,EAMb,8LANaA,EAMb,gS;IANaA,EAOyC,kI;IAPzCA,EASnC,a;IATmCA,EASnC,4B;IATmCA,EAUnC,a;IAVmCA,EAUnC,8D;;;;;;IAVmCA,EAiB5D,8B;IAjB4DA,EAiBmB,U;IAjBnBA,EAiBiC,e;;;;oBAjBjCA,E;IAAAA,EAiBmB,a;IAjBnBA,EAiBmB,iC;;;;;;IAjBnBA,EAkB/B,yB;;;;oBAlB+BA,E;IAAAA,EAkBH,wCAlBGA,EAkBH,gB;;;;;;iBAlBGA,E;;IAAAA,EAchE,2B;IAdgEA,EAcM;MAdNA,EAcM;MAAA,gBAdNA,EAcM;MAAA;MAAA;MAAA,gBAdNA,EAcM;MAAA,OAdNA,EAce,sDAAT;IAAA;MAdNA,EAcM;MAAA,gBAdNA,EAcM;MAAA;MAAA;MAAA,gBAdNA,EAcM;MAAA,OAdNA,EAc4D,sDAAtD;IAAA,E;IAdNA,EAgB5D,6B;IAhB4DA,EAgB/B,U;IAhB+BA,EAgBtB,e;IAhBsBA,EAiB5D,yE;IAjB4DA,EAkB5D,+FAlB4DA,EAkB5D,wB;IAlB4DA,EAmBhE,e;;;;iBAnBgEA,E;;oBAAAA,E;;;mBAAAA,E;IAAAA,EAcwF,qC;IAdxFA,EAc7D,kCAd6DA,EAc7D,oK;IAd6DA,EAgB/B,a;IAhB+BA,EAgB/B,4B;IAhB+BA,EAiB/B,a;IAjB+BA,EAiB/B,+D;;;;;;;;;;;;;IAjB+BA,EAIxE,8B;IAJwEA,EAMpE,sD;IANoEA,EAapE,gFAboEA,EAapE,wB;IAboEA,EAqBxE,e;;;;;;;gBArBwEA,E;;mBAAAA,E;IAAAA,EAI4B,+B;IAJ5BA,EAIK,2FAJLA,EAIK,iI;IAJLA,EAIiE,wG;IAJjEA,EAMhE,a;IANgEA,EAMhE,2E;;;;;;;;;;;AAnDxB,MAAMc,KAAN,CAAY;EACRC,WAAW,CAACC,MAAD,EAASC,KAAT,EAAgBC,EAAhB,EAAoB;IAC3B,KAAKF,MAAL,GAAcA,MAAd;IACA,KAAKC,KAAL,GAAaA,KAAb;IACA,KAAKC,EAAL,GAAUA,EAAV;IACA,KAAKC,WAAL,GAAmB,CAAnB;IACA,KAAKC,QAAL,GAAgB,IAAhB;IACA,KAAKC,iBAAL,GAAyB,IAAIpB,YAAJ,EAAzB;EACH;;EACDqB,QAAQ,GAAG;IACP,KAAKC,YAAL,GAAoB,KAAKP,MAAL,CAAYQ,MAAZ,CAAmBC,SAAnB,CAA6B,MAAM,KAAKP,EAAL,CAAQQ,YAAR,EAAnC,CAApB;EACH;;EACDC,SAAS,CAACC,KAAD,EAAQC,IAAR,EAAcC,CAAd,EAAiB;IACtB,IAAI,KAAKV,QAAL,IAAiBS,IAAI,CAACE,QAA1B,EAAoC;MAChCH,KAAK,CAACI,cAAN;MACA;IACH;;IACD,KAAKX,iBAAL,CAAuBY,IAAvB,CAA4BH,CAA5B;;IACA,IAAI,CAACD,IAAI,CAACK,GAAN,IAAa,CAACL,IAAI,CAACM,UAAvB,EAAmC;MAC/BP,KAAK,CAACI,cAAN;IACH;;IACD,IAAIH,IAAI,CAACO,OAAT,EAAkB;MACdP,IAAI,CAACO,OAAL,CAAa;QACTC,aAAa,EAAET,KADN;QAETC,IAAI,EAAEA,IAFG;QAGTS,KAAK,EAAER;MAHE,CAAb;IAKH;EACJ;;EACDS,qBAAqB,CAACV,IAAD,EAAO;IACxB,OAAOA,IAAI,CAACM,UAAL,IAAmB,CAAC,KAAKf,QAAzB,IAAqC,CAACS,IAAI,CAACE,QAAlD;EACH;;EACDS,QAAQ,CAACX,IAAD,EAAOS,KAAP,EAAc;IAClB,IAAIT,IAAI,CAACM,UAAT,EAAqB;MACjB,IAAIA,UAAU,GAAGM,KAAK,CAACC,OAAN,CAAcb,IAAI,CAACM,UAAnB,IAAiCN,IAAI,CAACM,UAAtC,GAAmD,CAACN,IAAI,CAACM,UAAN,CAApE;MACA,OAAO,KAAKnB,MAAL,CAAYwB,QAAZ,CAAqB,KAAKxB,MAAL,CAAY2B,aAAZ,CAA0BR,UAA1B,EAAsC;QAAES,UAAU,EAAE,KAAK3B;MAAnB,CAAtC,EAAkE4B,QAAlE,EAArB,EAAmG,KAAnG,CAAP;IACH;;IACD,OAAOP,KAAK,KAAK,KAAKnB,WAAtB;EACH;;EACD2B,WAAW,GAAG;IACV,IAAI,KAAKvB,YAAT,EAAuB;MACnB,KAAKA,YAAL,CAAkBwB,WAAlB;IACH;EACJ;;AA3CO;;AA6CZjC,KAAK,CAACkC,IAAN;EAAA,iBAAkGlC,KAAlG,EAAwFd,EAAxF,mBAAyHU,EAAE,CAACuC,MAA5H,GAAwFjD,EAAxF,mBAA+IU,EAAE,CAACwC,cAAlJ,GAAwFlD,EAAxF,mBAA6KA,EAAE,CAACmD,iBAAhL;AAAA;;AACArC,KAAK,CAACsC,IAAN,kBADwFpD,EACxF;EAAA,MAAsFc,KAAtF;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADwFd,EAEhF,wCADR;MADwFA,EAIxE,mDAHhB;MADwFA,EAsB5E,iBArBZ;IAAA;;IAAA;MADwFA,EAEM,2BAD9F;MADwFA,EAE3E,uBAF2EA,EAE3E,6DADb;MADwFA,EAInD,aAHrC;MADwFA,EAInD,iCAHrC;IAAA;EAAA;EAAA,eAuBwnBQ,EAAE,CAAC6C,OAvB3nB,EAuBstB7C,EAAE,CAAC8C,OAvBztB,EAuBm1B9C,EAAE,CAAC+C,IAvBt1B,EAuBu7B/C,EAAE,CAACgD,OAvB17B,EAuB4gC9C,EAAE,CAAC+C,kBAvB/gC,EAuB+wC/C,EAAE,CAACgD,gBAvBlxC,EAuBg/C9C,EAAE,CAAC+C,OAvBn/C;EAAA;EAAA;EAAA;AAAA;;AAwBA;EAAA,mDAzBwF3D,EAyBxF,mBAA2Fc,KAA3F,EAA8G,CAAC;IACnG8C,IAAI,EAAE1D,SAD6F;IAEnG2D,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,SAAZ;MAAuBC,QAAQ,EAAG;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAvBmB;MAuBZC,eAAe,EAAE7D,uBAAuB,CAAC8D,MAvB7B;MAuBqCC,aAAa,EAAE9D,iBAAiB,CAAC+D,IAvBtE;MAuB4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CAvBlF;MAyBIC,MAAM,EAAE,CAAC,2iBAAD;IAzBZ,CAAD;EAF6F,CAAD,CAA9G,EA4B4B,YAAY;IAAE,OAAO,CAAC;MAAET,IAAI,EAAElD,EAAE,CAACuC;IAAX,CAAD,EAAsB;MAAEW,IAAI,EAAElD,EAAE,CAACwC;IAAX,CAAtB,EAAmD;MAAEU,IAAI,EAAE5D,EAAE,CAACmD;IAAX,CAAnD,CAAP;EAA4F,CA5BtI,EA4BwJ;IAAEhC,WAAW,EAAE,CAAC;MACxJyC,IAAI,EAAEvD;IADkJ,CAAD,CAAf;IAExIiE,KAAK,EAAE,CAAC;MACRV,IAAI,EAAEvD;IADE,CAAD,CAFiI;IAIxIe,QAAQ,EAAE,CAAC;MACXwC,IAAI,EAAEvD;IADK,CAAD,CAJ8H;IAMxIkE,KAAK,EAAE,CAAC;MACRX,IAAI,EAAEvD;IADE,CAAD,CANiI;IAQxImE,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEvD;IADO,CAAD,CAR4H;IAUxIgB,iBAAiB,EAAE,CAAC;MACpBuC,IAAI,EAAEtD;IADc,CAAD;EAVqH,CA5BxJ;AAAA;;AAyCA,MAAMmE,WAAN,CAAkB;;AAElBA,WAAW,CAACzB,IAAZ;EAAA,iBAAwGyB,WAAxG;AAAA;;AACAA,WAAW,CAACC,IAAZ,kBArEwF1E,EAqExF;EAAA,MAAyGyE;AAAzG;AACAA,WAAW,CAACE,IAAZ,kBAtEwF3E,EAsExF;EAAA,UAAgIS,YAAhI,EAA8IE,YAA9I,EAA4JE,aAA5J,EAA2KF,YAA3K,EAAyLE,aAAzL;AAAA;;AACA;EAAA,mDAvEwFb,EAuExF,mBAA2FyE,WAA3F,EAAoH,CAAC;IACzGb,IAAI,EAAErD,QADmG;IAEzGsD,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAACnE,YAAD,EAAeE,YAAf,EAA6BE,aAA7B,CADV;MAECgE,OAAO,EAAE,CAAC/D,KAAD,EAAQH,YAAR,EAAsBE,aAAtB,CAFV;MAGCiE,YAAY,EAAE,CAAChE,KAAD;IAHf,CAAD;EAFmG,CAAD,CAApH;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,KAAT,EAAgB2D,WAAhB"}, "metadata": {}, "sourceType": "module"}