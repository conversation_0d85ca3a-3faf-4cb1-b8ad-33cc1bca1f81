{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass ProgressSpinner {\n  constructor() {\n    this.strokeWidth = \"2\";\n    this.fill = \"none\";\n    this.animationDuration = \"2s\";\n  }\n\n}\n\nProgressSpinner.ɵfac = function ProgressSpinner_Factory(t) {\n  return new (t || ProgressSpinner)();\n};\n\nProgressSpinner.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n  type: ProgressSpinner,\n  selectors: [[\"p-progressSpinner\"]],\n  hostAttrs: [1, \"p-element\"],\n  inputs: {\n    style: \"style\",\n    styleClass: \"styleClass\",\n    strokeWidth: \"strokeWidth\",\n    fill: \"fill\",\n    animationDuration: \"animationDuration\"\n  },\n  decls: 3,\n  vars: 6,\n  consts: [[\"role\", \"alert\", \"aria-busy\", \"true\", 1, \"p-progress-spinner\", 3, \"ngStyle\", \"ngClass\"], [\"viewBox\", \"25 25 50 50\", 1, \"p-progress-spinner-svg\"], [\"cx\", \"50\", \"cy\", \"50\", \"r\", \"20\", \"stroke-miterlimit\", \"10\", 1, \"p-progress-spinner-circle\"]],\n  template: function ProgressSpinner_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"div\", 0);\n      i0.ɵɵnamespaceSVG();\n      i0.ɵɵelementStart(1, \"svg\", 1);\n      i0.ɵɵelement(2, \"circle\", 2);\n      i0.ɵɵelementEnd()();\n    }\n\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", ctx.styleClass);\n      i0.ɵɵadvance(1);\n      i0.ɵɵstyleProp(\"animation-duration\", ctx.animationDuration);\n      i0.ɵɵadvance(1);\n      i0.ɵɵattribute(\"fill\", ctx.fill)(\"stroke-width\", ctx.strokeWidth);\n    }\n  },\n  dependencies: [i1.NgClass, i1.NgStyle],\n  styles: [\".p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;top:0;bottom:0;left:0;right:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"],\n  encapsulation: 2,\n  changeDetection: 0\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinner, [{\n    type: Component,\n    args: [{\n      selector: 'p-progressSpinner',\n      template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\"  role=\"alert\" aria-busy=\"true\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\"/>\n            </svg>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'class': 'p-element'\n      },\n      styles: [\".p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;top:0;bottom:0;left:0;right:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"]\n    }]\n  }], null, {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    strokeWidth: [{\n      type: Input\n    }],\n    fill: [{\n      type: Input\n    }],\n    animationDuration: [{\n      type: Input\n    }]\n  });\n})();\n\nclass ProgressSpinnerModule {}\n\nProgressSpinnerModule.ɵfac = function ProgressSpinnerModule_Factory(t) {\n  return new (t || ProgressSpinnerModule)();\n};\n\nProgressSpinnerModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: ProgressSpinnerModule\n});\nProgressSpinnerModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  imports: [CommonModule]\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ProgressSpinnerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [ProgressSpinner],\n      declarations: [ProgressSpinner]\n    }]\n  }], null, null);\n})();\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ProgressSpinner, ProgressSpinnerModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "NgModule", "i1", "CommonModule", "ProgressSpinner", "constructor", "strokeWidth", "fill", "animationDuration", "ɵfac", "ɵcmp", "Ng<PERSON><PERSON>", "NgStyle", "type", "args", "selector", "template", "changeDetection", "OnPush", "encapsulation", "None", "host", "styles", "style", "styleClass", "ProgressSpinnerModule", "ɵmod", "ɵinj", "imports", "exports", "declarations"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/primeng/fesm2015/primeng-progressspinner.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Input, NgModule } from '@angular/core';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\n\nclass ProgressSpinner {\n    constructor() {\n        this.strokeWidth = \"2\";\n        this.fill = \"none\";\n        this.animationDuration = \"2s\";\n    }\n}\nProgressSpinner.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinner, deps: [], target: i0.ɵɵFactoryTarget.Component });\nProgressSpinner.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"14.0.6\", type: ProgressSpinner, selector: \"p-progressSpinner\", inputs: { style: \"style\", styleClass: \"styleClass\", strokeWidth: \"strokeWidth\", fill: \"fill\", animationDuration: \"animationDuration\" }, host: { classAttribute: \"p-element\" }, ngImport: i0, template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\"  role=\"alert\" aria-busy=\"true\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\"/>\n            </svg>\n        </div>\n    `, isInline: true, styles: [\".p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;top:0;bottom:0;left:0;right:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinner, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-progressSpinner', template: `\n        <div class=\"p-progress-spinner\" [ngStyle]=\"style\" [ngClass]=\"styleClass\"  role=\"alert\" aria-busy=\"true\">\n            <svg class=\"p-progress-spinner-svg\" viewBox=\"25 25 50 50\" [style.animation-duration]=\"animationDuration\">\n                <circle class=\"p-progress-spinner-circle\" cx=\"50\" cy=\"50\" r=\"20\" [attr.fill]=\"fill\" [attr.stroke-width]=\"strokeWidth\" stroke-miterlimit=\"10\"/>\n            </svg>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        'class': 'p-element'\n                    }, styles: [\".p-progress-spinner{position:relative;margin:0 auto;width:100px;height:100px;display:inline-block}.p-progress-spinner:before{content:\\\"\\\";display:block;padding-top:100%}.p-progress-spinner-svg{animation:p-progress-spinner-rotate 2s linear infinite;height:100%;transform-origin:center center;width:100%;position:absolute;top:0;bottom:0;left:0;right:0;margin:auto}.p-progress-spinner-circle{stroke-dasharray:89,200;stroke-dashoffset:0;stroke:#d62d20;animation:p-progress-spinner-dash 1.5s ease-in-out infinite,p-progress-spinner-color 6s ease-in-out infinite;stroke-linecap:round}@keyframes p-progress-spinner-rotate{to{transform:rotate(360deg)}}@keyframes p-progress-spinner-dash{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35px}to{stroke-dasharray:89,200;stroke-dashoffset:-124px}}@keyframes p-progress-spinner-color{to,0%{stroke:#d62d20}40%{stroke:#0057e7}66%{stroke:#008744}80%,90%{stroke:#ffa700}}\\n\"] }]\n        }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], strokeWidth: [{\n                type: Input\n            }], fill: [{\n                type: Input\n            }], animationDuration: [{\n                type: Input\n            }] } });\nclass ProgressSpinnerModule {\n}\nProgressSpinnerModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinnerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nProgressSpinnerModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinnerModule, declarations: [ProgressSpinner], imports: [CommonModule], exports: [ProgressSpinner] });\nProgressSpinnerModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinnerModule, imports: [CommonModule] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.6\", ngImport: i0, type: ProgressSpinnerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [ProgressSpinner],\n                    declarations: [ProgressSpinner]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ProgressSpinner, ProgressSpinnerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAZ,MAAoB,eAApB;AACA,SAASC,SAAT,EAAoBC,uBAApB,EAA6CC,iBAA7C,EAAgEC,KAAhE,EAAuEC,QAAvE,QAAuF,eAAvF;AACA,OAAO,KAAKC,EAAZ,MAAoB,iBAApB;AACA,SAASC,YAAT,QAA6B,iBAA7B;;AAEA,MAAMC,eAAN,CAAsB;EAClBC,WAAW,GAAG;IACV,KAAKC,WAAL,GAAmB,GAAnB;IACA,KAAKC,IAAL,GAAY,MAAZ;IACA,KAAKC,iBAAL,GAAyB,IAAzB;EACH;;AALiB;;AAOtBJ,eAAe,CAACK,IAAhB;EAAA,iBAA4GL,eAA5G;AAAA;;AACAA,eAAe,CAACM,IAAhB,kBADkGd,EAClG;EAAA,MAAgGQ,eAAhG;EAAA;EAAA;EAAA;IAAA;IAAA;IAAA;IAAA;IAAA;EAAA;EAAA;EAAA;EAAA;EAAA;IAAA;MADkGR,EAE1F,4BADR;MADkGA,EAGtF,iBAFZ;MADkGA,EAGtF,4BAFZ;MADkGA,EAIlF,0BAHhB;MADkGA,EAKtF,iBAJZ;IAAA;;IAAA;MADkGA,EAE1D,4DADxC;MADkGA,EAG5B,aAFtE;MADkGA,EAG5B,yDAFtE;MADkGA,EAIjB,aAHjF;MADkGA,EAIjB,+DAHjF;IAAA;EAAA;EAAA,eAMsgCM,EAAE,CAACS,OANzgC,EAMomCT,EAAE,CAACU,OANvmC;EAAA;EAAA;EAAA;AAAA;;AAOA;EAAA,mDARkGhB,EAQlG,mBAA2FQ,eAA3F,EAAwH,CAAC;IAC7GS,IAAI,EAAEhB,SADuG;IAE7GiB,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,mBAAZ;MAAiCC,QAAQ,EAAG;AAC/D;AACA;AACA;AACA;AACA;AACA,KANmB;MAMZC,eAAe,EAAEnB,uBAAuB,CAACoB,MAN7B;MAMqCC,aAAa,EAAEpB,iBAAiB,CAACqB,IANtE;MAM4EC,IAAI,EAAE;QAC7E,SAAS;MADoE,CANlF;MAQIC,MAAM,EAAE,CAAC,y7BAAD;IARZ,CAAD;EAFuG,CAAD,CAAxH,QAW4B;IAAEC,KAAK,EAAE,CAAC;MACtBV,IAAI,EAAEb;IADgB,CAAD,CAAT;IAEZwB,UAAU,EAAE,CAAC;MACbX,IAAI,EAAEb;IADO,CAAD,CAFA;IAIZM,WAAW,EAAE,CAAC;MACdO,IAAI,EAAEb;IADQ,CAAD,CAJD;IAMZO,IAAI,EAAE,CAAC;MACPM,IAAI,EAAEb;IADC,CAAD,CANM;IAQZQ,iBAAiB,EAAE,CAAC;MACpBK,IAAI,EAAEb;IADc,CAAD;EARP,CAX5B;AAAA;;AAsBA,MAAMyB,qBAAN,CAA4B;;AAE5BA,qBAAqB,CAAChB,IAAtB;EAAA,iBAAkHgB,qBAAlH;AAAA;;AACAA,qBAAqB,CAACC,IAAtB,kBAjCkG9B,EAiClG;EAAA,MAAmH6B;AAAnH;AACAA,qBAAqB,CAACE,IAAtB,kBAlCkG/B,EAkClG;EAAA,UAAoJO,YAApJ;AAAA;;AACA;EAAA,mDAnCkGP,EAmClG,mBAA2F6B,qBAA3F,EAA8H,CAAC;IACnHZ,IAAI,EAAEZ,QAD6G;IAEnHa,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAACzB,YAAD,CADV;MAEC0B,OAAO,EAAE,CAACzB,eAAD,CAFV;MAGC0B,YAAY,EAAE,CAAC1B,eAAD;IAHf,CAAD;EAF6G,CAAD,CAA9H;AAAA;AASA;AACA;AACA;;;AAEA,SAASA,eAAT,EAA0BqB,qBAA1B"}, "metadata": {}, "sourceType": "module"}