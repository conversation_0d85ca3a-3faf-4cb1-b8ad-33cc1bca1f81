{"ast": null, "code": "import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function window(windowBoundaries) {\n  return operate((source, subscriber) => {\n    let windowSubject = new Subject();\n    subscriber.next(windowSubject.asObservable());\n\n    const errorHandler = err => {\n      windowSubject.error(err);\n      subscriber.error(err);\n    };\n\n    source.subscribe(createOperatorSubscriber(subscriber, value => windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value), () => {\n      windowSubject.complete();\n      subscriber.complete();\n    }, errorHandler));\n    windowBoundaries.subscribe(createOperatorSubscriber(subscriber, () => {\n      windowSubject.complete();\n      subscriber.next(windowSubject = new Subject());\n    }, noop, errorHandler));\n    return () => {\n      windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n      windowSubject = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "operate", "createOperatorSubscriber", "noop", "window", "windowBoundaries", "source", "subscriber", "windowSubject", "next", "asObservable", "<PERSON><PERSON><PERSON><PERSON>", "err", "error", "subscribe", "value", "complete", "unsubscribe"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/rxjs/dist/esm/internal/operators/window.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nexport function window(windowBoundaries) {\n    return operate((source, subscriber) => {\n        let windowSubject = new Subject();\n        subscriber.next(windowSubject.asObservable());\n        const errorHandler = (err) => {\n            windowSubject.error(err);\n            subscriber.error(err);\n        };\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.next(value), () => {\n            windowSubject.complete();\n            subscriber.complete();\n        }, errorHandler));\n        windowBoundaries.subscribe(createOperatorSubscriber(subscriber, () => {\n            windowSubject.complete();\n            subscriber.next((windowSubject = new Subject()));\n        }, noop, errorHandler));\n        return () => {\n            windowSubject === null || windowSubject === void 0 ? void 0 : windowSubject.unsubscribe();\n            windowSubject = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAT,QAAwB,YAAxB;AACA,SAASC,OAAT,QAAwB,cAAxB;AACA,SAASC,wBAAT,QAAyC,sBAAzC;AACA,SAASC,IAAT,QAAqB,cAArB;AACA,OAAO,SAASC,MAAT,CAAgBC,gBAAhB,EAAkC;EACrC,OAAOJ,OAAO,CAAC,CAACK,MAAD,EAASC,UAAT,KAAwB;IACnC,IAAIC,aAAa,GAAG,IAAIR,OAAJ,EAApB;IACAO,UAAU,CAACE,IAAX,CAAgBD,aAAa,CAACE,YAAd,EAAhB;;IACA,MAAMC,YAAY,GAAIC,GAAD,IAAS;MAC1BJ,aAAa,CAACK,KAAd,CAAoBD,GAApB;MACAL,UAAU,CAACM,KAAX,CAAiBD,GAAjB;IACH,CAHD;;IAIAN,MAAM,CAACQ,SAAP,CAAiBZ,wBAAwB,CAACK,UAAD,EAAcQ,KAAD,IAAWP,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACC,IAAd,CAAmBM,KAAnB,CAAtF,EAAiH,MAAM;MAC5JP,aAAa,CAACQ,QAAd;MACAT,UAAU,CAACS,QAAX;IACH,CAHwC,EAGtCL,YAHsC,CAAzC;IAIAN,gBAAgB,CAACS,SAAjB,CAA2BZ,wBAAwB,CAACK,UAAD,EAAa,MAAM;MAClEC,aAAa,CAACQ,QAAd;MACAT,UAAU,CAACE,IAAX,CAAiBD,aAAa,GAAG,IAAIR,OAAJ,EAAjC;IACH,CAHkD,EAGhDG,IAHgD,EAG1CQ,YAH0C,CAAnD;IAIA,OAAO,MAAM;MACTH,aAAa,KAAK,IAAlB,IAA0BA,aAAa,KAAK,KAAK,CAAjD,GAAqD,KAAK,CAA1D,GAA8DA,aAAa,CAACS,WAAd,EAA9D;MACAT,aAAa,GAAG,IAAhB;IACH,CAHD;EAIH,CAnBa,CAAd;AAoBH"}, "metadata": {}, "sourceType": "module"}