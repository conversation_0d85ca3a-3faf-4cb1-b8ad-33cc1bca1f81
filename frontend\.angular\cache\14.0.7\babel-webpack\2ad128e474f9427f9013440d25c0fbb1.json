{"ast": null, "code": "import { isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nclass DataSource {}\n/** Checks whether an object is a data source. */\n\n\nfunction isDataSource(value) {\n  // Check if the value is a DataSource by observing if it has a connect function. Cannot\n  // be checked as an `instanceof DataSource` since people could create their own sources\n  // that match the interface, but don't extend DataSource.\n  return value && typeof value.connect === 'function';\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/** DataSource wrapper for a native array. */\n\n\nclass ArrayDataSource extends DataSource {\n  constructor(_data) {\n    super();\n    this._data = _data;\n  }\n\n  connect() {\n    return isObservable(this._data) ? this._data : of(this._data);\n  }\n\n  disconnect() {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\n\n\nclass _DisposeViewRepeaterStrategy {\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n\n      if (record.previousIndex == null) {\n        const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n        view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n        operation = 1\n        /* _ViewRepeaterOperation.INSERTED */\n        ;\n      } else if (currentIndex == null) {\n        viewContainerRef.remove(adjustedPreviousIndex);\n        operation = 3\n        /* _ViewRepeaterOperation.REMOVED */\n        ;\n      } else {\n        view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        operation = 2\n        /* _ViewRepeaterOperation.MOVED */\n        ;\n      }\n\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view === null || view === void 0 ? void 0 : view.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n\n  detach() {}\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\n\n\nclass _RecycleViewRepeaterStrategy {\n  constructor() {\n    /**\n     * The size of the cache used to store unused views.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n     */\n    this.viewCacheSize = 20;\n    /**\n     * View cache that stores embedded view instances that have been previously stamped out,\n     * but don't are not currently rendered. The view repeater will reuse these views rather than\n     * creating brand new ones.\n     *\n     * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n     */\n\n    this._viewCache = [];\n  }\n  /** Apply changes to the DOM. */\n\n\n  applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n    // Rearrange the views to put them in the right location.\n    changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n      let view;\n      let operation;\n\n      if (record.previousIndex == null) {\n        // Item added.\n        const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n\n        view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = view ? 1\n        /* _ViewRepeaterOperation.INSERTED */\n        : 0\n        /* _ViewRepeaterOperation.REPLACED */\n        ;\n      } else if (currentIndex == null) {\n        // Item removed.\n        this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n\n        operation = 3\n        /* _ViewRepeaterOperation.REMOVED */\n        ;\n      } else {\n        // Item moved.\n        view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n        operation = 2\n        /* _ViewRepeaterOperation.MOVED */\n        ;\n      }\n\n      if (itemViewChanged) {\n        itemViewChanged({\n          context: view === null || view === void 0 ? void 0 : view.context,\n          operation,\n          record\n        });\n      }\n    });\n  }\n\n  detach() {\n    for (const view of this._viewCache) {\n      view.destroy();\n    }\n\n    this._viewCache = [];\n  }\n  /**\n   * Inserts a view for a new item, either from the cache or by creating a new\n   * one. Returns `undefined` if the item was inserted into a cached view.\n   */\n\n\n  _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n    const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n\n    if (cachedView) {\n      cachedView.context.$implicit = value;\n      return undefined;\n    }\n\n    const viewArgs = viewArgsFactory();\n    return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n  }\n  /** Detaches the view at the given index and inserts into the view cache. */\n\n\n  _detachAndCacheView(index, viewContainerRef) {\n    const detachedView = viewContainerRef.detach(index);\n\n    this._maybeCacheView(detachedView, viewContainerRef);\n  }\n  /** Moves view at the previous index to the current index. */\n\n\n  _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n    const view = viewContainerRef.get(adjustedPreviousIndex);\n    viewContainerRef.move(view, currentIndex);\n    view.context.$implicit = value;\n    return view;\n  }\n  /**\n   * Cache the given detached view. If the cache is full, the view will be\n   * destroyed.\n   */\n\n\n  _maybeCacheView(view, viewContainerRef) {\n    if (this._viewCache.length < this.viewCacheSize) {\n      this._viewCache.push(view);\n    } else {\n      const index = viewContainerRef.indexOf(view); // The host component could remove views from the container outside of\n      // the view repeater. It's unlikely this will occur, but just in case,\n      // destroy the view on its own, otherwise destroy it through the\n      // container to ensure that all the references are removed.\n\n      if (index === -1) {\n        view.destroy();\n      } else {\n        viewContainerRef.remove(index);\n      }\n    }\n  }\n  /** Inserts a recycled view from the cache at the given index. */\n\n\n  _insertViewFromCache(index, viewContainerRef) {\n    const cachedView = this._viewCache.pop();\n\n    if (cachedView) {\n      viewContainerRef.insert(cachedView, index);\n    }\n\n    return cachedView || null;\n  }\n\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\n\n\nclass SelectionModel {\n  constructor(_multiple = false, initiallySelectedValues, _emitChanges = true) {\n    this._multiple = _multiple;\n    this._emitChanges = _emitChanges;\n    /** Currently-selected values. */\n\n    this._selection = new Set();\n    /** Keeps track of the deselected options that haven't been emitted by the change event. */\n\n    this._deselectedToEmit = [];\n    /** Keeps track of the selected options that haven't been emitted by the change event. */\n\n    this._selectedToEmit = [];\n    /** Event emitted when the value has changed. */\n\n    this.changed = new Subject();\n\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      } // Clear the array in order to avoid firing the change event for preselected values.\n\n\n      this._selectedToEmit.length = 0;\n    }\n  }\n  /** Selected values. */\n\n\n  get selected() {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n\n    return this._selected;\n  }\n  /**\n   * Selects a value or an array of values.\n   */\n\n\n  select(...values) {\n    this._verifyValueAssignment(values);\n\n    values.forEach(value => this._markSelected(value));\n\n    this._emitChangeEvent();\n  }\n  /**\n   * Deselects a value or an array of values.\n   */\n\n\n  deselect(...values) {\n    this._verifyValueAssignment(values);\n\n    values.forEach(value => this._unmarkSelected(value));\n\n    this._emitChangeEvent();\n  }\n  /**\n   * Toggles a value between selected and deselected.\n   */\n\n\n  toggle(value) {\n    this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n  /**\n   * Clears all of the selected values.\n   */\n\n\n  clear() {\n    this._unmarkAll();\n\n    this._emitChangeEvent();\n  }\n  /**\n   * Determines whether a value is selected.\n   */\n\n\n  isSelected(value) {\n    return this._selection.has(value);\n  }\n  /**\n   * Determines whether the model does not have a value.\n   */\n\n\n  isEmpty() {\n    return this._selection.size === 0;\n  }\n  /**\n   * Determines whether the model has a value.\n   */\n\n\n  hasValue() {\n    return !this.isEmpty();\n  }\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n\n\n  sort(predicate) {\n    if (this._multiple && this.selected) {\n      this._selected.sort(predicate);\n    }\n  }\n  /**\n   * Gets whether multiple values can be selected.\n   */\n\n\n  isMultipleSelection() {\n    return this._multiple;\n  }\n  /** Emits a change event and clears the records of selected and deselected values. */\n\n\n  _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit\n      });\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n  /** Selects a value. */\n\n\n  _markSelected(value) {\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n\n      this._selection.add(value);\n\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n  /** Deselects a value. */\n\n\n  _unmarkSelected(value) {\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n  /** Clears out the selected values. */\n\n\n  _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n\n\n  _verifyValueAssignment(values) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\n\n\nfunction getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\n\n\nclass UniqueSelectionDispatcher {\n  constructor() {\n    this._listeners = [];\n  }\n  /**\n   * Notify other items that selection for the given name has been set.\n   * @param id ID of the item.\n   * @param name Name of the item.\n   */\n\n\n  notify(id, name) {\n    for (let listener of this._listeners) {\n      listener(id, name);\n    }\n  }\n  /**\n   * Listen for future changes to item selection.\n   * @return Function used to deregister listener\n   */\n\n\n  listen(listener) {\n    this._listeners.push(listener);\n\n    return () => {\n      this._listeners = this._listeners.filter(registered => {\n        return listener !== registered;\n      });\n    };\n  }\n\n  ngOnDestroy() {\n    this._listeners = [];\n  }\n\n}\n\nUniqueSelectionDispatcher.ɵfac = function UniqueSelectionDispatcher_Factory(t) {\n  return new (t || UniqueSelectionDispatcher)();\n};\n\nUniqueSelectionDispatcher.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: UniqueSelectionDispatcher,\n  factory: UniqueSelectionDispatcher.ɵfac,\n  providedIn: 'root'\n});\n\n(function () {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(UniqueSelectionDispatcher, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\n\n\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, getMultipleValuesInSingleSelectionError, isDataSource };", "map": {"version": 3, "names": ["isObservable", "of", "Subject", "i0", "Injectable", "InjectionToken", "DataSource", "isDataSource", "value", "connect", "ArrayDataSource", "constructor", "_data", "disconnect", "_DisposeViewRepeaterStrategy", "applyChanges", "changes", "viewContainerRef", "itemContextFactory", "itemValueResolver", "itemViewChanged", "forEachOperation", "record", "adjustedPreviousIndex", "currentIndex", "view", "operation", "previousIndex", "insertContext", "createEmbeddedView", "templateRef", "context", "index", "remove", "get", "move", "detach", "_RecycleViewRepeaterStrategy", "viewCacheSize", "_viewCache", "viewArgsFactory", "_insertView", "_detachAndCacheView", "_moveView", "destroy", "cachedView", "_insertViewFromCache", "$implicit", "undefined", "viewArgs", "detached<PERSON>iew", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "push", "indexOf", "pop", "insert", "SelectionModel", "_multiple", "initiallySelectedValues", "_emitChanges", "_selection", "Set", "_deselectedToEmit", "_selectedToEmit", "changed", "for<PERSON>ach", "_markSelected", "selected", "_selected", "Array", "from", "values", "select", "_verifyValueAssignment", "_emitChangeEvent", "deselect", "_unmarkSelected", "toggle", "isSelected", "clear", "_unmarkAll", "has", "isEmpty", "size", "hasValue", "sort", "predicate", "isMultipleSelection", "next", "source", "added", "removed", "add", "delete", "ngDevMode", "getMultipleValuesInSingleSelectionError", "Error", "UniqueSelectionDispatcher", "_listeners", "notify", "id", "name", "listener", "listen", "filter", "registered", "ngOnDestroy", "ɵfac", "ɵprov", "type", "args", "providedIn", "_VIEW_REPEATER_STRATEGY"], "sources": ["C:/Users/<USER>/GestionFormation/frontend/node_modules/@angular/cdk/fesm2015/collections.mjs"], "sourcesContent": ["import { isObservable, of, Subject } from 'rxjs';\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken } from '@angular/core';\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nclass DataSource {\n}\n/** Checks whether an object is a data source. */\nfunction isDataSource(value) {\n    // Check if the value is a DataSource by observing if it has a connect function. Cannot\n    // be checked as an `instanceof DataSource` since people could create their own sources\n    // that match the interface, but don't extend DataSource.\n    return value && typeof value.connect === 'function';\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** DataSource wrapper for a native array. */\nclass ArrayDataSource extends DataSource {\n    constructor(_data) {\n        super();\n        this._data = _data;\n    }\n    connect() {\n        return isObservable(this._data) ? this._data : of(this._data);\n    }\n    disconnect() { }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A repeater that destroys views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will always construct a new embedded view for each item.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _DisposeViewRepeaterStrategy {\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                const insertContext = itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = viewContainerRef.createEmbeddedView(insertContext.templateRef, insertContext.context, insertContext.index);\n                operation = 1 /* _ViewRepeaterOperation.INSERTED */;\n            }\n            else if (currentIndex == null) {\n                viewContainerRef.remove(adjustedPreviousIndex);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                view = viewContainerRef.get(adjustedPreviousIndex);\n                viewContainerRef.move(view, currentIndex);\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view === null || view === void 0 ? void 0 : view.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() { }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * A repeater that caches views when they are removed from a\n * {@link ViewContainerRef}. When new items are inserted into the container,\n * the repeater will reuse one of the cached views instead of creating a new\n * embedded view. Recycling cached views reduces the quantity of expensive DOM\n * inserts.\n *\n * @template T The type for the embedded view's $implicit property.\n * @template R The type for the item in each IterableDiffer change record.\n * @template C The type for the context passed to each embedded view.\n */\nclass _RecycleViewRepeaterStrategy {\n    constructor() {\n        /**\n         * The size of the cache used to store unused views.\n         * Setting the cache size to `0` will disable caching. Defaults to 20 views.\n         */\n        this.viewCacheSize = 20;\n        /**\n         * View cache that stores embedded view instances that have been previously stamped out,\n         * but don't are not currently rendered. The view repeater will reuse these views rather than\n         * creating brand new ones.\n         *\n         * TODO(michaeljamesparsons) Investigate whether using a linked list would improve performance.\n         */\n        this._viewCache = [];\n    }\n    /** Apply changes to the DOM. */\n    applyChanges(changes, viewContainerRef, itemContextFactory, itemValueResolver, itemViewChanged) {\n        // Rearrange the views to put them in the right location.\n        changes.forEachOperation((record, adjustedPreviousIndex, currentIndex) => {\n            let view;\n            let operation;\n            if (record.previousIndex == null) {\n                // Item added.\n                const viewArgsFactory = () => itemContextFactory(record, adjustedPreviousIndex, currentIndex);\n                view = this._insertView(viewArgsFactory, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = view ? 1 /* _ViewRepeaterOperation.INSERTED */ : 0 /* _ViewRepeaterOperation.REPLACED */;\n            }\n            else if (currentIndex == null) {\n                // Item removed.\n                this._detachAndCacheView(adjustedPreviousIndex, viewContainerRef);\n                operation = 3 /* _ViewRepeaterOperation.REMOVED */;\n            }\n            else {\n                // Item moved.\n                view = this._moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, itemValueResolver(record));\n                operation = 2 /* _ViewRepeaterOperation.MOVED */;\n            }\n            if (itemViewChanged) {\n                itemViewChanged({\n                    context: view === null || view === void 0 ? void 0 : view.context,\n                    operation,\n                    record,\n                });\n            }\n        });\n    }\n    detach() {\n        for (const view of this._viewCache) {\n            view.destroy();\n        }\n        this._viewCache = [];\n    }\n    /**\n     * Inserts a view for a new item, either from the cache or by creating a new\n     * one. Returns `undefined` if the item was inserted into a cached view.\n     */\n    _insertView(viewArgsFactory, currentIndex, viewContainerRef, value) {\n        const cachedView = this._insertViewFromCache(currentIndex, viewContainerRef);\n        if (cachedView) {\n            cachedView.context.$implicit = value;\n            return undefined;\n        }\n        const viewArgs = viewArgsFactory();\n        return viewContainerRef.createEmbeddedView(viewArgs.templateRef, viewArgs.context, viewArgs.index);\n    }\n    /** Detaches the view at the given index and inserts into the view cache. */\n    _detachAndCacheView(index, viewContainerRef) {\n        const detachedView = viewContainerRef.detach(index);\n        this._maybeCacheView(detachedView, viewContainerRef);\n    }\n    /** Moves view at the previous index to the current index. */\n    _moveView(adjustedPreviousIndex, currentIndex, viewContainerRef, value) {\n        const view = viewContainerRef.get(adjustedPreviousIndex);\n        viewContainerRef.move(view, currentIndex);\n        view.context.$implicit = value;\n        return view;\n    }\n    /**\n     * Cache the given detached view. If the cache is full, the view will be\n     * destroyed.\n     */\n    _maybeCacheView(view, viewContainerRef) {\n        if (this._viewCache.length < this.viewCacheSize) {\n            this._viewCache.push(view);\n        }\n        else {\n            const index = viewContainerRef.indexOf(view);\n            // The host component could remove views from the container outside of\n            // the view repeater. It's unlikely this will occur, but just in case,\n            // destroy the view on its own, otherwise destroy it through the\n            // container to ensure that all the references are removed.\n            if (index === -1) {\n                view.destroy();\n            }\n            else {\n                viewContainerRef.remove(index);\n            }\n        }\n    }\n    /** Inserts a recycled view from the cache at the given index. */\n    _insertViewFromCache(index, viewContainerRef) {\n        const cachedView = this._viewCache.pop();\n        if (cachedView) {\n            viewContainerRef.insert(cachedView, index);\n        }\n        return cachedView || null;\n    }\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nclass SelectionModel {\n    constructor(_multiple = false, initiallySelectedValues, _emitChanges = true) {\n        this._multiple = _multiple;\n        this._emitChanges = _emitChanges;\n        /** Currently-selected values. */\n        this._selection = new Set();\n        /** Keeps track of the deselected options that haven't been emitted by the change event. */\n        this._deselectedToEmit = [];\n        /** Keeps track of the selected options that haven't been emitted by the change event. */\n        this._selectedToEmit = [];\n        /** Event emitted when the value has changed. */\n        this.changed = new Subject();\n        if (initiallySelectedValues && initiallySelectedValues.length) {\n            if (_multiple) {\n                initiallySelectedValues.forEach(value => this._markSelected(value));\n            }\n            else {\n                this._markSelected(initiallySelectedValues[0]);\n            }\n            // Clear the array in order to avoid firing the change event for preselected values.\n            this._selectedToEmit.length = 0;\n        }\n    }\n    /** Selected values. */\n    get selected() {\n        if (!this._selected) {\n            this._selected = Array.from(this._selection.values());\n        }\n        return this._selected;\n    }\n    /**\n     * Selects a value or an array of values.\n     */\n    select(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._markSelected(value));\n        this._emitChangeEvent();\n    }\n    /**\n     * Deselects a value or an array of values.\n     */\n    deselect(...values) {\n        this._verifyValueAssignment(values);\n        values.forEach(value => this._unmarkSelected(value));\n        this._emitChangeEvent();\n    }\n    /**\n     * Toggles a value between selected and deselected.\n     */\n    toggle(value) {\n        this.isSelected(value) ? this.deselect(value) : this.select(value);\n    }\n    /**\n     * Clears all of the selected values.\n     */\n    clear() {\n        this._unmarkAll();\n        this._emitChangeEvent();\n    }\n    /**\n     * Determines whether a value is selected.\n     */\n    isSelected(value) {\n        return this._selection.has(value);\n    }\n    /**\n     * Determines whether the model does not have a value.\n     */\n    isEmpty() {\n        return this._selection.size === 0;\n    }\n    /**\n     * Determines whether the model has a value.\n     */\n    hasValue() {\n        return !this.isEmpty();\n    }\n    /**\n     * Sorts the selected values based on a predicate function.\n     */\n    sort(predicate) {\n        if (this._multiple && this.selected) {\n            this._selected.sort(predicate);\n        }\n    }\n    /**\n     * Gets whether multiple values can be selected.\n     */\n    isMultipleSelection() {\n        return this._multiple;\n    }\n    /** Emits a change event and clears the records of selected and deselected values. */\n    _emitChangeEvent() {\n        // Clear the selected values so they can be re-cached.\n        this._selected = null;\n        if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n            this.changed.next({\n                source: this,\n                added: this._selectedToEmit,\n                removed: this._deselectedToEmit,\n            });\n            this._deselectedToEmit = [];\n            this._selectedToEmit = [];\n        }\n    }\n    /** Selects a value. */\n    _markSelected(value) {\n        if (!this.isSelected(value)) {\n            if (!this._multiple) {\n                this._unmarkAll();\n            }\n            this._selection.add(value);\n            if (this._emitChanges) {\n                this._selectedToEmit.push(value);\n            }\n        }\n    }\n    /** Deselects a value. */\n    _unmarkSelected(value) {\n        if (this.isSelected(value)) {\n            this._selection.delete(value);\n            if (this._emitChanges) {\n                this._deselectedToEmit.push(value);\n            }\n        }\n    }\n    /** Clears out the selected values. */\n    _unmarkAll() {\n        if (!this.isEmpty()) {\n            this._selection.forEach(value => this._unmarkSelected(value));\n        }\n    }\n    /**\n     * Verifies the value assignment and throws an error if the specified value array is\n     * including multiple values while the selection model is not supporting multiple values.\n     */\n    _verifyValueAssignment(values) {\n        if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMultipleValuesInSingleSelectionError();\n        }\n    }\n}\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nfunction getMultipleValuesInSingleSelectionError() {\n    return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Class to coordinate unique selection based on name.\n * Intended to be consumed as an Angular service.\n * This service is needed because native radio change events are only fired on the item currently\n * being selected, and we still need to uncheck the previous selection.\n *\n * This service does not *store* any IDs and names because they may change at any time, so it is\n * less error-prone if they are simply passed through when the events occur.\n */\nclass UniqueSelectionDispatcher {\n    constructor() {\n        this._listeners = [];\n    }\n    /**\n     * Notify other items that selection for the given name has been set.\n     * @param id ID of the item.\n     * @param name Name of the item.\n     */\n    notify(id, name) {\n        for (let listener of this._listeners) {\n            listener(id, name);\n        }\n    }\n    /**\n     * Listen for future changes to item selection.\n     * @return Function used to deregister listener\n     */\n    listen(listener) {\n        this._listeners.push(listener);\n        return () => {\n            this._listeners = this._listeners.filter((registered) => {\n                return listener !== registered;\n            });\n        };\n    }\n    ngOnDestroy() {\n        this._listeners = [];\n    }\n}\nUniqueSelectionDispatcher.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: UniqueSelectionDispatcher, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\nUniqueSelectionDispatcher.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: UniqueSelectionDispatcher, providedIn: 'root' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"14.0.1\", ngImport: i0, type: UniqueSelectionDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Injection token for {@link _ViewRepeater}. This token is for use by Angular Material only.\n * @docs-private\n */\nconst _VIEW_REPEATER_STRATEGY = new InjectionToken('_ViewRepeater');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ArrayDataSource, DataSource, SelectionModel, UniqueSelectionDispatcher, _DisposeViewRepeaterStrategy, _RecycleViewRepeaterStrategy, _VIEW_REPEATER_STRATEGY, getMultipleValuesInSingleSelectionError, isDataSource };\n"], "mappings": "AAAA,SAASA,YAAT,EAAuBC,EAAvB,EAA2BC,OAA3B,QAA0C,MAA1C;AACA,OAAO,KAAKC,EAAZ,MAAoB,eAApB;AACA,SAASC,UAAT,EAAqBC,cAArB,QAA2C,eAA3C;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,UAAN,CAAiB;AAEjB;;;AACA,SAASC,YAAT,CAAsBC,KAAtB,EAA6B;EACzB;EACA;EACA;EACA,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACC,OAAb,KAAyB,UAAzC;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;;;AACA,MAAMC,eAAN,SAA8BJ,UAA9B,CAAyC;EACrCK,WAAW,CAACC,KAAD,EAAQ;IACf;IACA,KAAKA,KAAL,GAAaA,KAAb;EACH;;EACDH,OAAO,GAAG;IACN,OAAOT,YAAY,CAAC,KAAKY,KAAN,CAAZ,GAA2B,KAAKA,KAAhC,GAAwCX,EAAE,CAAC,KAAKW,KAAN,CAAjD;EACH;;EACDC,UAAU,GAAG,CAAG;;AARqB;AAWzC;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,4BAAN,CAAmC;EAC/BC,YAAY,CAACC,OAAD,EAAUC,gBAAV,EAA4BC,kBAA5B,EAAgDC,iBAAhD,EAAmEC,eAAnE,EAAoF;IAC5FJ,OAAO,CAACK,gBAAR,CAAyB,CAACC,MAAD,EAASC,qBAAT,EAAgCC,YAAhC,KAAiD;MACtE,IAAIC,IAAJ;MACA,IAAIC,SAAJ;;MACA,IAAIJ,MAAM,CAACK,aAAP,IAAwB,IAA5B,EAAkC;QAC9B,MAAMC,aAAa,GAAGV,kBAAkB,CAACI,MAAD,EAASC,qBAAT,EAAgCC,YAAhC,CAAxC;QACAC,IAAI,GAAGR,gBAAgB,CAACY,kBAAjB,CAAoCD,aAAa,CAACE,WAAlD,EAA+DF,aAAa,CAACG,OAA7E,EAAsFH,aAAa,CAACI,KAApG,CAAP;QACAN,SAAS,GAAG;QAAE;QAAd;MACH,CAJD,MAKK,IAAIF,YAAY,IAAI,IAApB,EAA0B;QAC3BP,gBAAgB,CAACgB,MAAjB,CAAwBV,qBAAxB;QACAG,SAAS,GAAG;QAAE;QAAd;MACH,CAHI,MAIA;QACDD,IAAI,GAAGR,gBAAgB,CAACiB,GAAjB,CAAqBX,qBAArB,CAAP;QACAN,gBAAgB,CAACkB,IAAjB,CAAsBV,IAAtB,EAA4BD,YAA5B;QACAE,SAAS,GAAG;QAAE;QAAd;MACH;;MACD,IAAIN,eAAJ,EAAqB;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACM,OAD9C;UAEZL,SAFY;UAGZJ;QAHY,CAAD,CAAf;MAKH;IACJ,CAxBD;EAyBH;;EACDc,MAAM,GAAG,CAAG;;AA5BmB;AA+BnC;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,4BAAN,CAAmC;EAC/B1B,WAAW,GAAG;IACV;AACR;AACA;AACA;IACQ,KAAK2B,aAAL,GAAqB,EAArB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;;IACQ,KAAKC,UAAL,GAAkB,EAAlB;EACH;EACD;;;EACAxB,YAAY,CAACC,OAAD,EAAUC,gBAAV,EAA4BC,kBAA5B,EAAgDC,iBAAhD,EAAmEC,eAAnE,EAAoF;IAC5F;IACAJ,OAAO,CAACK,gBAAR,CAAyB,CAACC,MAAD,EAASC,qBAAT,EAAgCC,YAAhC,KAAiD;MACtE,IAAIC,IAAJ;MACA,IAAIC,SAAJ;;MACA,IAAIJ,MAAM,CAACK,aAAP,IAAwB,IAA5B,EAAkC;QAC9B;QACA,MAAMa,eAAe,GAAG,MAAMtB,kBAAkB,CAACI,MAAD,EAASC,qBAAT,EAAgCC,YAAhC,CAAhD;;QACAC,IAAI,GAAG,KAAKgB,WAAL,CAAiBD,eAAjB,EAAkChB,YAAlC,EAAgDP,gBAAhD,EAAkEE,iBAAiB,CAACG,MAAD,CAAnF,CAAP;QACAI,SAAS,GAAGD,IAAI,GAAG;QAAE;QAAL,EAA6C;QAAE;QAA/D;MACH,CALD,MAMK,IAAID,YAAY,IAAI,IAApB,EAA0B;QAC3B;QACA,KAAKkB,mBAAL,CAAyBnB,qBAAzB,EAAgDN,gBAAhD;;QACAS,SAAS,GAAG;QAAE;QAAd;MACH,CAJI,MAKA;QACD;QACAD,IAAI,GAAG,KAAKkB,SAAL,CAAepB,qBAAf,EAAsCC,YAAtC,EAAoDP,gBAApD,EAAsEE,iBAAiB,CAACG,MAAD,CAAvF,CAAP;QACAI,SAAS,GAAG;QAAE;QAAd;MACH;;MACD,IAAIN,eAAJ,EAAqB;QACjBA,eAAe,CAAC;UACZW,OAAO,EAAEN,IAAI,KAAK,IAAT,IAAiBA,IAAI,KAAK,KAAK,CAA/B,GAAmC,KAAK,CAAxC,GAA4CA,IAAI,CAACM,OAD9C;UAEZL,SAFY;UAGZJ;QAHY,CAAD,CAAf;MAKH;IACJ,CA1BD;EA2BH;;EACDc,MAAM,GAAG;IACL,KAAK,MAAMX,IAAX,IAAmB,KAAKc,UAAxB,EAAoC;MAChCd,IAAI,CAACmB,OAAL;IACH;;IACD,KAAKL,UAAL,GAAkB,EAAlB;EACH;EACD;AACJ;AACA;AACA;;;EACIE,WAAW,CAACD,eAAD,EAAkBhB,YAAlB,EAAgCP,gBAAhC,EAAkDT,KAAlD,EAAyD;IAChE,MAAMqC,UAAU,GAAG,KAAKC,oBAAL,CAA0BtB,YAA1B,EAAwCP,gBAAxC,CAAnB;;IACA,IAAI4B,UAAJ,EAAgB;MACZA,UAAU,CAACd,OAAX,CAAmBgB,SAAnB,GAA+BvC,KAA/B;MACA,OAAOwC,SAAP;IACH;;IACD,MAAMC,QAAQ,GAAGT,eAAe,EAAhC;IACA,OAAOvB,gBAAgB,CAACY,kBAAjB,CAAoCoB,QAAQ,CAACnB,WAA7C,EAA0DmB,QAAQ,CAAClB,OAAnE,EAA4EkB,QAAQ,CAACjB,KAArF,CAAP;EACH;EACD;;;EACAU,mBAAmB,CAACV,KAAD,EAAQf,gBAAR,EAA0B;IACzC,MAAMiC,YAAY,GAAGjC,gBAAgB,CAACmB,MAAjB,CAAwBJ,KAAxB,CAArB;;IACA,KAAKmB,eAAL,CAAqBD,YAArB,EAAmCjC,gBAAnC;EACH;EACD;;;EACA0B,SAAS,CAACpB,qBAAD,EAAwBC,YAAxB,EAAsCP,gBAAtC,EAAwDT,KAAxD,EAA+D;IACpE,MAAMiB,IAAI,GAAGR,gBAAgB,CAACiB,GAAjB,CAAqBX,qBAArB,CAAb;IACAN,gBAAgB,CAACkB,IAAjB,CAAsBV,IAAtB,EAA4BD,YAA5B;IACAC,IAAI,CAACM,OAAL,CAAagB,SAAb,GAAyBvC,KAAzB;IACA,OAAOiB,IAAP;EACH;EACD;AACJ;AACA;AACA;;;EACI0B,eAAe,CAAC1B,IAAD,EAAOR,gBAAP,EAAyB;IACpC,IAAI,KAAKsB,UAAL,CAAgBa,MAAhB,GAAyB,KAAKd,aAAlC,EAAiD;MAC7C,KAAKC,UAAL,CAAgBc,IAAhB,CAAqB5B,IAArB;IACH,CAFD,MAGK;MACD,MAAMO,KAAK,GAAGf,gBAAgB,CAACqC,OAAjB,CAAyB7B,IAAzB,CAAd,CADC,CAED;MACA;MACA;MACA;;MACA,IAAIO,KAAK,KAAK,CAAC,CAAf,EAAkB;QACdP,IAAI,CAACmB,OAAL;MACH,CAFD,MAGK;QACD3B,gBAAgB,CAACgB,MAAjB,CAAwBD,KAAxB;MACH;IACJ;EACJ;EACD;;;EACAc,oBAAoB,CAACd,KAAD,EAAQf,gBAAR,EAA0B;IAC1C,MAAM4B,UAAU,GAAG,KAAKN,UAAL,CAAgBgB,GAAhB,EAAnB;;IACA,IAAIV,UAAJ,EAAgB;MACZ5B,gBAAgB,CAACuC,MAAjB,CAAwBX,UAAxB,EAAoCb,KAApC;IACH;;IACD,OAAOa,UAAU,IAAI,IAArB;EACH;;AA3G8B;AA8GnC;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;;;AACA,MAAMY,cAAN,CAAqB;EACjB9C,WAAW,CAAC+C,SAAS,GAAG,KAAb,EAAoBC,uBAApB,EAA6CC,YAAY,GAAG,IAA5D,EAAkE;IACzE,KAAKF,SAAL,GAAiBA,SAAjB;IACA,KAAKE,YAAL,GAAoBA,YAApB;IACA;;IACA,KAAKC,UAAL,GAAkB,IAAIC,GAAJ,EAAlB;IACA;;IACA,KAAKC,iBAAL,GAAyB,EAAzB;IACA;;IACA,KAAKC,eAAL,GAAuB,EAAvB;IACA;;IACA,KAAKC,OAAL,GAAe,IAAI/D,OAAJ,EAAf;;IACA,IAAIyD,uBAAuB,IAAIA,uBAAuB,CAACP,MAAvD,EAA+D;MAC3D,IAAIM,SAAJ,EAAe;QACXC,uBAAuB,CAACO,OAAxB,CAAgC1D,KAAK,IAAI,KAAK2D,aAAL,CAAmB3D,KAAnB,CAAzC;MACH,CAFD,MAGK;QACD,KAAK2D,aAAL,CAAmBR,uBAAuB,CAAC,CAAD,CAA1C;MACH,CAN0D,CAO3D;;;MACA,KAAKK,eAAL,CAAqBZ,MAArB,GAA8B,CAA9B;IACH;EACJ;EACD;;;EACY,IAARgB,QAAQ,GAAG;IACX,IAAI,CAAC,KAAKC,SAAV,EAAqB;MACjB,KAAKA,SAAL,GAAiBC,KAAK,CAACC,IAAN,CAAW,KAAKV,UAAL,CAAgBW,MAAhB,EAAX,CAAjB;IACH;;IACD,OAAO,KAAKH,SAAZ;EACH;EACD;AACJ;AACA;;;EACII,MAAM,CAAC,GAAGD,MAAJ,EAAY;IACd,KAAKE,sBAAL,CAA4BF,MAA5B;;IACAA,MAAM,CAACN,OAAP,CAAe1D,KAAK,IAAI,KAAK2D,aAAL,CAAmB3D,KAAnB,CAAxB;;IACA,KAAKmE,gBAAL;EACH;EACD;AACJ;AACA;;;EACIC,QAAQ,CAAC,GAAGJ,MAAJ,EAAY;IAChB,KAAKE,sBAAL,CAA4BF,MAA5B;;IACAA,MAAM,CAACN,OAAP,CAAe1D,KAAK,IAAI,KAAKqE,eAAL,CAAqBrE,KAArB,CAAxB;;IACA,KAAKmE,gBAAL;EACH;EACD;AACJ;AACA;;;EACIG,MAAM,CAACtE,KAAD,EAAQ;IACV,KAAKuE,UAAL,CAAgBvE,KAAhB,IAAyB,KAAKoE,QAAL,CAAcpE,KAAd,CAAzB,GAAgD,KAAKiE,MAAL,CAAYjE,KAAZ,CAAhD;EACH;EACD;AACJ;AACA;;;EACIwE,KAAK,GAAG;IACJ,KAAKC,UAAL;;IACA,KAAKN,gBAAL;EACH;EACD;AACJ;AACA;;;EACII,UAAU,CAACvE,KAAD,EAAQ;IACd,OAAO,KAAKqD,UAAL,CAAgBqB,GAAhB,CAAoB1E,KAApB,CAAP;EACH;EACD;AACJ;AACA;;;EACI2E,OAAO,GAAG;IACN,OAAO,KAAKtB,UAAL,CAAgBuB,IAAhB,KAAyB,CAAhC;EACH;EACD;AACJ;AACA;;;EACIC,QAAQ,GAAG;IACP,OAAO,CAAC,KAAKF,OAAL,EAAR;EACH;EACD;AACJ;AACA;;;EACIG,IAAI,CAACC,SAAD,EAAY;IACZ,IAAI,KAAK7B,SAAL,IAAkB,KAAKU,QAA3B,EAAqC;MACjC,KAAKC,SAAL,CAAeiB,IAAf,CAAoBC,SAApB;IACH;EACJ;EACD;AACJ;AACA;;;EACIC,mBAAmB,GAAG;IAClB,OAAO,KAAK9B,SAAZ;EACH;EACD;;;EACAiB,gBAAgB,GAAG;IACf;IACA,KAAKN,SAAL,GAAiB,IAAjB;;IACA,IAAI,KAAKL,eAAL,CAAqBZ,MAArB,IAA+B,KAAKW,iBAAL,CAAuBX,MAA1D,EAAkE;MAC9D,KAAKa,OAAL,CAAawB,IAAb,CAAkB;QACdC,MAAM,EAAE,IADM;QAEdC,KAAK,EAAE,KAAK3B,eAFE;QAGd4B,OAAO,EAAE,KAAK7B;MAHA,CAAlB;MAKA,KAAKA,iBAAL,GAAyB,EAAzB;MACA,KAAKC,eAAL,GAAuB,EAAvB;IACH;EACJ;EACD;;;EACAG,aAAa,CAAC3D,KAAD,EAAQ;IACjB,IAAI,CAAC,KAAKuE,UAAL,CAAgBvE,KAAhB,CAAL,EAA6B;MACzB,IAAI,CAAC,KAAKkD,SAAV,EAAqB;QACjB,KAAKuB,UAAL;MACH;;MACD,KAAKpB,UAAL,CAAgBgC,GAAhB,CAAoBrF,KAApB;;MACA,IAAI,KAAKoD,YAAT,EAAuB;QACnB,KAAKI,eAAL,CAAqBX,IAArB,CAA0B7C,KAA1B;MACH;IACJ;EACJ;EACD;;;EACAqE,eAAe,CAACrE,KAAD,EAAQ;IACnB,IAAI,KAAKuE,UAAL,CAAgBvE,KAAhB,CAAJ,EAA4B;MACxB,KAAKqD,UAAL,CAAgBiC,MAAhB,CAAuBtF,KAAvB;;MACA,IAAI,KAAKoD,YAAT,EAAuB;QACnB,KAAKG,iBAAL,CAAuBV,IAAvB,CAA4B7C,KAA5B;MACH;IACJ;EACJ;EACD;;;EACAyE,UAAU,GAAG;IACT,IAAI,CAAC,KAAKE,OAAL,EAAL,EAAqB;MACjB,KAAKtB,UAAL,CAAgBK,OAAhB,CAAwB1D,KAAK,IAAI,KAAKqE,eAAL,CAAqBrE,KAArB,CAAjC;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIkE,sBAAsB,CAACF,MAAD,EAAS;IAC3B,IAAIA,MAAM,CAACpB,MAAP,GAAgB,CAAhB,IAAqB,CAAC,KAAKM,SAA3B,KAAyC,OAAOqC,SAAP,KAAqB,WAArB,IAAoCA,SAA7E,CAAJ,EAA6F;MACzF,MAAMC,uCAAuC,EAA7C;IACH;EACJ;;AA5IgB;AA8IrB;AACA;AACA;AACA;AACA;;;AACA,SAASA,uCAAT,GAAmD;EAC/C,OAAOC,KAAK,CAAC,yEAAD,CAAZ;AACH;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,yBAAN,CAAgC;EAC5BvF,WAAW,GAAG;IACV,KAAKwF,UAAL,GAAkB,EAAlB;EACH;EACD;AACJ;AACA;AACA;AACA;;;EACIC,MAAM,CAACC,EAAD,EAAKC,IAAL,EAAW;IACb,KAAK,IAAIC,QAAT,IAAqB,KAAKJ,UAA1B,EAAsC;MAClCI,QAAQ,CAACF,EAAD,EAAKC,IAAL,CAAR;IACH;EACJ;EACD;AACJ;AACA;AACA;;;EACIE,MAAM,CAACD,QAAD,EAAW;IACb,KAAKJ,UAAL,CAAgB9C,IAAhB,CAAqBkD,QAArB;;IACA,OAAO,MAAM;MACT,KAAKJ,UAAL,GAAkB,KAAKA,UAAL,CAAgBM,MAAhB,CAAwBC,UAAD,IAAgB;QACrD,OAAOH,QAAQ,KAAKG,UAApB;MACH,CAFiB,CAAlB;IAGH,CAJD;EAKH;;EACDC,WAAW,GAAG;IACV,KAAKR,UAAL,GAAkB,EAAlB;EACH;;AA5B2B;;AA8BhCD,yBAAyB,CAACU,IAA1B;EAAA,iBAAsHV,yBAAtH;AAAA;;AACAA,yBAAyB,CAACW,KAA1B,kBAD4G1G,EAC5G;EAAA,OAA0H+F,yBAA1H;EAAA,SAA0HA,yBAA1H;EAAA,YAAiK;AAAjK;;AACA;EAAA,mDAF4G/F,EAE5G,mBAA2F+F,yBAA3F,EAAkI,CAAC;IACvHY,IAAI,EAAE1G,UADiH;IAEvH2G,IAAI,EAAE,CAAC;MAAEC,UAAU,EAAE;IAAd,CAAD;EAFiH,CAAD,CAAlI;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;;;AACA,MAAMC,uBAAuB,GAAG,IAAI5G,cAAJ,CAAmB,eAAnB,CAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAEA,SAASK,eAAT,EAA0BJ,UAA1B,EAAsCmD,cAAtC,EAAsDyC,yBAAtD,EAAiFpF,4BAAjF,EAA+GuB,4BAA/G,EAA6I4E,uBAA7I,EAAsKjB,uCAAtK,EAA+MzF,YAA/M"}, "metadata": {}, "sourceType": "module"}